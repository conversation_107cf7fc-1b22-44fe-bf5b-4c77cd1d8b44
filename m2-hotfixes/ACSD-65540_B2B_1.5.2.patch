diff --git a/vendor/magento/module-company/Setup/Patch/Data/SetCompanyForStructure.php b/vendor/magento/module-company/Setup/Patch/Data/SetCompanyForStructure.php
index 4dcb3dbcb5b3..7ba339a8703e 100644
--- a/vendor/magento/module-company/Setup/Patch/Data/SetCompanyForStructure.php
+++ b/vendor/magento/module-company/Setup/Patch/Data/SetCompanyForStructure.php
@@ -71,7 +71,7 @@ public function apply()
                     $this->moduleDataSetup->getConnection()->update(
                         $this->moduleDataSetup->getTable('company_structure'),
                         ['company_id' => $company['entity_id']],
-                        ['REGEXP_LIKE(path, ?)' =>
+                        ['path REGEXP ?' =>
                             '^' . $adminStructureIds[$company['super_user_id']]['structure_id'] . '(/.+)?$']
                     );
                 }

