<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2023 Amasty (https://www.amasty.com)
 * @package One Step Checkout Thank you Page 2 for Magento 2 (System)
 */
/**
 * @var $block Magento\Checkout\Block\Onepage\Success
 * @var $escaper Magento\Framework\Escaper
 */
?>

<div class="checkout-success">
<p><?= $escaper->escapeHtml(__('Your order number is:')) ?>
    <?php if ($block->getOrderId()):?>
        <div class="success-messages">
            <?php if ($block->getCanViewOrder()):?>
                <p><?= $escaper->escapeHtml(__('Your order number is:')) ?>
                    <a href="<?= /* @noEscape */$block->getViewOrderUrl() ?>"
                       class="order-number">
                        <strong>
                            <?= /* @noEscape */$block->getOrderId() ?>
                        </strong>
                    </a>
                </p>
            <?php  else: ?>
                <p>
                    <?= $escaper->escapeHtml(__('Your order # is:')) ?>
                    <span>
                        <?= /* @noEscape */$block->getOrderId() ?>
                    </span>
                </p>
            <?php endif;?>
            <p><?= $escaper->escapeHtml(__('We\'ll email you an order confirmation with details and dddddtracking info.')) ?></p>
        </div>
        <?= $block->getChildHtml('details') ?>
    <?php endif;?>

    <?= $block->getAdditionalInfoHtml() ?>
    <?= $block->getChildHtml('cms') ?>

    <div class="actions-toolbar">
        <div class="primary">
            <a class="action Design primary continue" href="<?= /* @noEscape */$block->getUrl() ?>">
                <span><?= $escaper->escapeHtml(__('Continue Shopping')) ?></span>
            </a>
        </div>
    </div>
</div>
