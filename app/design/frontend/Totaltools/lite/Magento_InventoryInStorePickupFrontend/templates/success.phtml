<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// Get order data
$objectManager = \Magento\Framework\App\ObjectManager::getInstance();
$checkoutSession = $objectManager->get(\Magento\Checkout\Model\Session::class);
$order = $checkoutSession->getLastRealOrder();
$storeRepository = $objectManager->get(\Totaltools\Storelocator\Model\StoreRepository::class);

// Determine if this is click & collect or delivery
$isClickAndCollect = false;
$isDelivery = false;
$storeData = null;
$customerAddress = null;
$orderData = $block->getOrderData();
if ($order && $order->getId()) {
    $shippingMethod = $order->getShippingMethod();
    $storelocatorId = $order->getData('storelocator_id');

    // Check if it's click & collect (store pickup)
    if (strpos($shippingMethod, 'shippitcc_shippitcc') !== false || $storelocatorId) {
        $isClickAndCollect = true;

        // Get store data
        if ($storelocatorId) {
            try {
                $store = $storeRepository->getById($storelocatorId);
                if ($store && $store->getId()) {
                    $storeData = $store;
                }
            } catch (\Exception $e) {
                // Store not found, continue without store data
            }
        }
    } else {
        // Regular delivery
        $isDelivery = true;
        $customerAddress = $order->getShippingAddress();
    }
}
$orderData = $block->getOrderData();

?>

<div class="checkout-success <?= $block->getOrderData() ?>">
    <?php if ($block->getOrderId()) : ?>
        <?php if ($block->getCanViewOrder()) : ?>
            <p><?= $block->escapeHtml(__('Your order number is: %1.', sprintf('<a href="%s" class="order-number"><strong>%s</strong></a>', $block->escapeUrl($block->getViewOrderUrl()), $block->getOrderId())), ['a', 'strong']) ?></p>
        <?php else : ?>
            <p><?= $block->escapeHtml(__('Your order # is: <span>%1</span>.', $block->getOrderId()), ['span']) ?></p>
        <?php endif; ?>

        <p><?= $block->escapeHtml(__('Thank you for your order. You will receive an email once your order is ready for collection at the selected store.')) ?></p>

        <?php if ($isClickAndCollect): ?>
            <p class="click-collect-message">
                <strong style="color: #4CAF50;"><?= $block->escapeHtml(__('Your Click & Collect order will be ready in: 2 Days*')) ?></strong>
                <span style="font-size: 12px; color: #666;">*Min Qty</span>
            </p>
        <?php elseif ($isDelivery): ?>
            <p class="delivery-message">
                <strong style="color: #4CAF50;"><?= $block->escapeHtml(__('Your Delivery order will arrive in: 1-3 Business Days*')) ?></strong>
                <span style="font-size: 12px; color: #666;">*Min Qty</span>
            </p>
        <?php endif; ?>
    <?php endif; ?>

    <?= $block->getAdditionalInfoHtml() ?>

    <div class="actions-toolbar">
        <div class="primary">
            <a class="action primary continue"
               href="<?= $block->escapeUrl($block->getContinueUrl()) ?>"><span><?= $block->escapeHtml(__('Continue Shopping')) ?></span></a>
        </div>
    </div>
</div>

<?php if ($order && $order->getId()): ?>
    <div class="order-details-sections" style="margin-top: 30px;">
        <div class="row" style="display: flex; gap: 30px; flex-wrap: wrap;">

            <?php if ($isClickAndCollect): ?>
                <!-- Click & Collect Store Details Section -->
                <div class="collection-store-details" style="flex: 1; min-width: 300px; border: 1px solid #ddd; padding: 20px; border-radius: 5px;">
                    <h3 style="margin-top: 0; color: #333; border-bottom: 2px solid #f0f0f0; padding-bottom: 10px;">
                        <?= $block->escapeHtml(__('Collection Store details')) ?>
                    </h3>

                    <div class="store-info" style="margin-bottom: 20px;">
                        <?php if ($storeData): ?>
                            <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">
                                <?= $block->escapeHtml($storeData->getStoreName()) ?>
                            </h4>
                            <p style="margin: 5px 0; line-height: 1.4;">
                                <?= $block->escapeHtml($storeData->getAddress()) ?><br>
                                <?= $block->escapeHtml($storeData->getCity()) ?>, <?= $block->escapeHtml($storeData->getState()) ?> <?= $block->escapeHtml($storeData->getZipcode()) ?>
                            </p>
                        <?php else: ?>
                            <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">
                                <?= $block->escapeHtml(__('Total Tools Thomastown')) ?>
                            </h4>
                            <p style="margin: 5px 0; line-height: 1.4;">
                                <?= $block->escapeHtml(__('Tenancy 8 & 9, Thomastown Homemaker Centre, 308 Settlement Road, Thomastown 3074 Victoria')) ?>
                            </p>
                        <?php endif; ?>

                        <div class="store-contact" style="margin: 15px 0;">
                            <p style="margin: 5px 0;"><strong><?= $block->escapeHtml(__('Phone')) ?></strong><br>
                            <?= $block->escapeHtml($storeData ? ($storeData->getPhone() ?: '0300304265') : '0300304265') ?></p>

                            <?php if ($storeData && $storeData->getEmail()): ?>
                            <p style="margin: 5px 0;"><strong><?= $block->escapeHtml(__('Email')) ?></strong><br>
                            <?= $block->escapeHtml($storeData->getEmail()) ?></p>
                            <?php endif; ?>
                        </div>

                        <div class="trading-hours" style="margin: 15px 0;">
                            <p style="margin: 5px 0 10px 0;"><strong><?= $block->escapeHtml(__('Trading Hours')) ?></strong></p>
                            <div style="font-size: 14px; line-height: 1.3;">
                                <div style="display: flex; justify-content: space-between; margin: 2px 0;">
                                    <span><?= $block->escapeHtml(__('Monday')) ?></span>
                                    <span>07:00 - 17:30</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 2px 0;">
                                    <span><?= $block->escapeHtml(__('Tuesday')) ?></span>
                                    <span>07:00 - 17:30</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 2px 0;">
                                    <span><?= $block->escapeHtml(__('Wednesday')) ?></span>
                                    <span>07:00 - 17:30</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 2px 0;">
                                    <span><?= $block->escapeHtml(__('Thursday')) ?></span>
                                    <span>07:00 - 17:30</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 2px 0;">
                                    <span><?= $block->escapeHtml(__('Friday')) ?></span>
                                    <span>07:00 - 17:30</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 2px 0;">
                                    <span><?= $block->escapeHtml(__('Saturday')) ?></span>
                                    <span>07:00 - 17:30</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 2px 0;">
                                    <span><?= $block->escapeHtml(__('Sunday')) ?></span>
                                    <span>07:00 - 17:30</span>
                                </div>
                                <div style="display: flex; justify-content: space-between; margin: 2px 0;">
                                    <span><?= $block->escapeHtml(__('Public Holidays')) ?></span>
                                    <span><?= $block->escapeHtml(__('Please call store')) ?></span>
                                </div>
                            </div>
                        </div>

                        <div class="store-actions" style="margin-top: 15px;">
                            <?php
                            if ($storeData) {
                                $directionsUrl = 'https://www.openstreetmap.org/directions?from=&to=' .
                                    urlencode($storeData->getAddress() . ', ' . $storeData->getCity() . ', ' . $storeData->getState() . ' ' . $storeData->getZipcode());
                            } else {
                                $directionsUrl = 'https://www.openstreetmap.org/directions?from=&to=' .
                                    urlencode('Tenancy 8 & 9, Thomastown Homemaker Centre, 308 Settlement Road, Thomastown 3074 Victoria');
                            }
                            ?>
                            <a href="<?= $block->escapeUrl($directionsUrl) ?>" target="_blank" style="color: #007bff; text-decoration: none; font-weight: bold;">
                                <?= $block->escapeHtml(__('Get Directions')) ?>
                            </a>
                        </div>
                    </div>

                    <!-- Store Map -->
                    <div class="store-map" style="margin-top: 20px;">
                        <?php if ($storeData && $storeData->getLatitude() && $storeData->getLongitude()): ?>
                            <div id="store-leaflet-map" class="leaflet-map-container"
                                 data-latitude="<?= $block->escapeHtmlAttr($storeData->getLatitude()) ?>"
                                 data-longitude="<?= $block->escapeHtmlAttr($storeData->getLongitude()) ?>"
                                 data-store-name="<?= $block->escapeHtmlAttr($storeData->getStoreName()) ?>"
                                 style="width: 100%; max-width: 300px; height: 200px; border: 1px solid #ddd; border-radius: 3px;">
                            </div>
                        <?php else: ?>
                            <div id="store-leaflet-map" class="leaflet-map-container"
                                 data-latitude="-37.6833"
                                 data-longitude="144.9667"
                                 data-store-name="<?= $block->escapeHtmlAttr(__('Total Tools Thomastown')) ?>"
                                 style="width: 100%; max-width: 300px; height: 200px; border: 1px solid #ddd; border-radius: 3px;">
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endif; ?>

            <?php if ($isDelivery && $customerAddress): ?>
                <!-- Delivery Details Section -->
                <div class="delivery-details" style="flex: 1; min-width: 300px; border: 1px solid #ddd; padding: 20px; border-radius: 5px;">
                    <h3 style="margin-top: 0; color: #333; border-bottom: 2px solid #f0f0f0; padding-bottom: 10px;">
                        <?= $block->escapeHtml(__('Delivery details')) ?>
                    </h3>

                    <div class="customer-info" style="margin-bottom: 20px;">
                        <h4 style="margin: 0 0 10px 0; color: #333; font-size: 16px;">
                            <?= $block->escapeHtml($customerAddress->getFirstname() . ' ' . $customerAddress->getLastname()) ?>
                        </h4>
                        <p style="margin: 5px 0; line-height: 1.4;">
                            <?php
                            $street = $customerAddress->getStreet();
                            if (is_array($street)) {
                                echo $block->escapeHtml(implode(', ', $street));
                            } else {
                                echo $block->escapeHtml($street);
                            }
                            ?><br>
                            <?= $block->escapeHtml($customerAddress->getCity()) ?>, <?= $block->escapeHtml($customerAddress->getRegion()) ?> <?= $block->escapeHtml($customerAddress->getPostcode()) ?>
                        </p>

                        <div class="customer-contact" style="margin: 15px 0;">
                            <?php if ($customerAddress->getTelephone()): ?>
                            <p style="margin: 5px 0;"><strong><?= $block->escapeHtml(__('Phone')) ?></strong><br>
                            <?= $block->escapeHtml($customerAddress->getTelephone()) ?></p>
                            <?php endif; ?>

                            <?php if ($order->getCustomerEmail()): ?>
                            <p style="margin: 5px 0;"><strong><?= $block->escapeHtml(__('Email')) ?></strong><br>
                            <?= $block->escapeHtml($order->getCustomerEmail()) ?></p>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Customer Address Map -->
                    <?php
                    $addressString = '';
                    if (is_array($customerAddress->getStreet())) {
                        $addressString = implode(' ', $customerAddress->getStreet());
                    } else {
                        $addressString = $customerAddress->getStreet();
                    }
                    $addressString .= ', ' . $customerAddress->getCity() . ', ' . $customerAddress->getRegion() . ' ' . $customerAddress->getPostcode();
                    ?>
                    <div class="customer-map" style="margin-top: 20px;">
                        <div id="delivery-leaflet-map" class="leaflet-map-container"
                             data-address="<?= $block->escapeHtmlAttr($addressString) ?>"
                             data-customer-name="<?= $block->escapeHtmlAttr($customerAddress->getFirstname() . ' ' . $customerAddress->getLastname()) ?>"
                             style="width: 100%; max-width: 300px; height: 200px; border: 1px solid #ddd; border-radius: 3px;">
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Sidebar Section -->
            <div class="order-success-sidebar" style="flex: 0 0 300px; min-width: 280px;">
                <div class="sidebar-content" style="border: 1px solid #ddd; padding: 20px; border-radius: 5px; background-color: #f9f9f9;">
                    <h3 style="margin-top: 0; color: #333; border-bottom: 2px solid #e0e0e0; padding-bottom: 10px;">
                        <?= $block->escapeHtml(__("WHAT'S NEXT?")) ?>
                    </h3>

                    <div class="next-steps" style="margin-bottom: 20px;">
                        <div class="step-item" style="display: flex; align-items: flex-start; margin-bottom: 15px; padding: 10px; background: white; border-radius: 5px;">
                            <div class="step-icon" style="width: 30px; height: 30px; background: #4CAF50; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; flex-shrink: 0;">
                                <span style="color: white; font-weight: bold; font-size: 14px;">✓</span>
                            </div>
                            <div class="step-content">
                                <h4 style="margin: 0 0 5px 0; font-size: 14px; color: #333;"><?= $block->escapeHtml(__('Order confirmed')) ?></h4>
                                <p style="margin: 0; font-size: 12px; color: #666; line-height: 1.3;"><?= $block->escapeHtml(__('We\'re on it')) ?></p>
                            </div>
                        </div>

                        <div class="step-item" style="display: flex; align-items: flex-start; margin-bottom: 15px; padding: 10px; background: white; border-radius: 5px;">
                            <div class="step-icon" style="width: 30px; height: 30px; background: #2196F3; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; flex-shrink: 0;">
                                <span style="color: white; font-weight: bold; font-size: 12px;">📧</span>
                            </div>
                            <div class="step-content">
                                <h4 style="margin: 0 0 5px 0; font-size: 14px; color: #333;"><?= $block->escapeHtml(__('Order updates')) ?></h4>
                                <p style="margin: 0; font-size: 12px; color: #666; line-height: 1.3;"><?= $block->escapeHtml(__('You will receive order and shipping updates via email.')) ?></p>
                            </div>
                        </div>

                        <div class="step-item" style="display: flex; align-items: flex-start; margin-bottom: 15px; padding: 10px; background: white; border-radius: 5px;">
                            <div class="step-icon" style="width: 30px; height: 30px; background: #FF9800; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 15px; flex-shrink: 0;">
                                <span style="color: white; font-weight: bold; font-size: 12px;">?</span>
                            </div>
                            <div class="step-content">
                                <h4 style="margin: 0 0 5px 0; font-size: 14px; color: #333;"><?= $block->escapeHtml(__('Need help?')) ?></h4>
                                <p style="margin: 0; font-size: 12px; color: #666; line-height: 1.3;"><?= $block->escapeHtml(__('If you need help search our help centre for FAQs or to browse topics to find the help you need.')) ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="action-buttons" style="margin-top: 20px;">
                        <div style="margin-bottom: 10px;">
                            <a href="<?= $block->escapeUrl($block->getUrl('contact')) ?>" style="display: inline-block; background: #007bff; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 12px; font-weight: bold; text-align: center; min-width: 100px;">
                                <?= $block->escapeHtml(__('Help Centre')) ?>
                            </a>
                        </div>
                        <div>
                            <a href="<?= $block->escapeUrl($block->getUrl('sales/order/track')) ?>" style="display: inline-block; background: #28a745; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 12px; font-weight: bold; text-align: center; min-width: 100px;">
                                <?= $block->escapeHtml(__('Order Tracking')) ?>
                            </a>
                        </div>
                    </div>

                    <div class="contact-info" style="margin-top: 20px; padding-top: 15px; border-top: 1px solid #e0e0e0;">
                        <p style="margin: 0; font-size: 12px; color: #666; line-height: 1.4;">
                            <?= $block->escapeHtml(__('For any product related queries contact your local store.')) ?>
                        </p>
                    </div>
                </div>
            </div>

        </div>
    </div>

    <!-- Initialize OpenStreetMap with Leaflet -->
    <script type="text/javascript">
    require([
        'jquery',
        'totaltools/map-loader'
    ], function($, mapLoader) {
        'use strict';

        try {
            mapLoader
                .done(function() {
                    // Initialize store map if exists
                    var $storeMap = $('#store-leaflet-map');
                    if ($storeMap.length) {
                        var storeLat = parseFloat($storeMap.data('latitude'));
                        var storeLng = parseFloat($storeMap.data('longitude'));
                        var storeName = $storeMap.data('store-name');

                        if (storeLat && storeLng) {
                            var storeMap = L.map('store-leaflet-map').setView([storeLat, storeLng], 15);

                            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                maxZoom: 18,
                                attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                            }).addTo(storeMap);

                            var storeIcon = L.icon({
                                iconUrl: '<?= $block->getViewFileUrl('Totaltools_Storelocator::images/map-icon.png') ?>',
                                iconSize: [60, 42],
                                iconAnchor: [30, 42],
                                popupAnchor: [5, -40]
                            });

                            L.marker([storeLat, storeLng], {icon: storeIcon})
                                .addTo(storeMap)
                                .bindPopup('<strong>' + storeName + '</strong>')
                                .openPopup();
                        }
                    }

                    // Initialize delivery map if exists
                    var $deliveryMap = $('#delivery-leaflet-map');
                    if ($deliveryMap.length) {
                        var address = $deliveryMap.data('address');
                        var customerName = $deliveryMap.data('customer-name');

                        if (address) {
                            // Use Nominatim geocoding service to get coordinates from address
                            $.ajax({
                                url: 'https://nominatim.openstreetmap.org/search',
                                data: {
                                    q: address,
                                    format: 'json',
                                    limit: 1
                                },
                                success: function(data) {
                                    if (data && data.length > 0) {
                                        var lat = parseFloat(data[0].lat);
                                        var lng = parseFloat(data[0].lon);

                                        var deliveryMap = L.map('delivery-leaflet-map').setView([lat, lng], 15);

                                        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                                            maxZoom: 18,
                                            attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
                                        }).addTo(deliveryMap);

                                        var deliveryIcon = L.icon({
                                            iconUrl: '<?= $block->getViewFileUrl('Totaltools_Storelocator::images/map-icon.png') ?>',
                                            iconSize: [60, 42],
                                            iconAnchor: [30, 42],
                                            popupAnchor: [5, -40]
                                        });

                                        L.marker([lat, lng], {icon: deliveryIcon})
                                            .addTo(deliveryMap)
                                            .bindPopup('<strong>' + customerName + '</strong><br>' + address)
                                            .openPopup();
                                    }
                                },
                                error: function() {
                                    console.warn('Could not geocode delivery address');
                                }
                            });
                        }
                    }
                })
                .fail(function(err) {
                    console.error('Failed to load map library:', err);
                });
        } catch (e) {
            console.error('Error initializing maps:', e);
        }
    });
    </script>
<?php endif; ?>
