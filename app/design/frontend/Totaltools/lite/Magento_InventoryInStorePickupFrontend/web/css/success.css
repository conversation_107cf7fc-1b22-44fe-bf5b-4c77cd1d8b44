/* Order Success Page Styles */
.order-details-sections {
    margin-top: 30px;
}

.order-details-sections .row {
    display: flex;
    gap: 30px;
    flex-wrap: wrap;
}

.collection-store-details,
.delivery-details {
    flex: 1;
    min-width: 300px;
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 5px;
    background: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.order-success-sidebar {
    flex: 0 0 300px;
    min-width: 280px;
}

.sidebar-content {
    border: 1px solid #ddd;
    padding: 20px;
    border-radius: 5px;
    background-color: #f9f9f9;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.collection-store-details h3,
.delivery-details h3,
.sidebar-content h3 {
    margin-top: 0;
    color: #333;
    border-bottom: 2px solid #f0f0f0;
    padding-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.store-info h4,
.customer-info h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
}

.store-info p,
.customer-info p {
    margin: 5px 0;
    line-height: 1.4;
    color: #555;
}

.store-contact,
.customer-contact {
    margin: 15px 0;
}

.trading-hours {
    margin: 15px 0;
}

.trading-hours > div {
    font-size: 14px;
    line-height: 1.3;
}

.trading-hours > div > div {
    display: flex;
    justify-content: space-between;
    margin: 2px 0;
    padding: 2px 0;
}

.trading-hours > div > div:nth-child(even) {
    background-color: #f8f8f8;
}

.store-actions a {
    color: #007bff;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

.store-actions a:hover {
    color: #0056b3;
    text-decoration: underline;
}

.store-map .leaflet-map-container,
.customer-map .leaflet-map-container {
    width: 100%;
    max-width: 300px;
    height: 200px;
    border: 1px solid #ddd;
    border-radius: 3px;
    transition: transform 0.3s ease;
}

.store-map .leaflet-map-container:hover,
.customer-map .leaflet-map-container:hover {
    transform: scale(1.02);
}

/* Leaflet map specific styles */
.leaflet-map-container .leaflet-container {
    border-radius: 3px;
}

.leaflet-map-container .leaflet-popup-content-wrapper {
    border-radius: 4px;
}

.leaflet-map-container .leaflet-popup-content {
    margin: 8px 12px;
    font-size: 13px;
    line-height: 1.4;
}

/* Ensure map controls are visible */
.leaflet-map-container .leaflet-control-zoom {
    border: 1px solid #ccc;
    border-radius: 4px;
}

.leaflet-map-container .leaflet-control-zoom a {
    background-color: #fff;
    border-bottom: 1px solid #ccc;
    color: #333;
    text-decoration: none;
}

.leaflet-map-container .leaflet-control-zoom a:hover {
    background-color: #f4f4f4;
}

.step-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
    padding: 10px;
    background: white;
    border-radius: 5px;
    transition: box-shadow 0.3s ease;
}

.step-item:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.step-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-content h4 {
    margin: 0 0 5px 0;
    font-size: 14px;
    color: #333;
    font-weight: 600;
}

.step-content p {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.3;
}

.action-buttons a {
    display: inline-block;
    padding: 8px 16px;
    text-decoration: none;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-align: center;
    min-width: 100px;
    transition: background-color 0.3s ease;
}

.action-buttons a:first-child {
    background: #007bff;
    color: white;
}

.action-buttons a:first-child:hover {
    background: #0056b3;
}

.action-buttons a:last-child {
    background: #28a745;
    color: white;
}

.action-buttons a:last-child:hover {
    background: #1e7e34;
}

.contact-info {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #e0e0e0;
}

.contact-info p {
    margin: 0;
    font-size: 12px;
    color: #666;
    line-height: 1.4;
}

.click-collect-message,
.delivery-message {
    background-color: #f8f9fa;
    border-left: 4px solid #4CAF50;
    padding: 15px;
    margin: 15px 0;
    border-radius: 0 4px 4px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .order-details-sections .row {
        flex-direction: column;
        gap: 20px;
    }
    
    .collection-store-details,
    .delivery-details,
    .order-success-sidebar {
        min-width: auto;
        flex: none;
    }
    
    .trading-hours > div > div {
        font-size: 12px;
    }
    
    .store-map .leaflet-map-container,
    .customer-map .leaflet-map-container {
        max-width: 100%;
    }
}

@media (max-width: 480px) {
    .collection-store-details,
    .delivery-details,
    .sidebar-content {
        padding: 15px;
    }
    
    .order-details-sections .row {
        gap: 15px;
    }
    
    .step-item {
        padding: 8px;
    }
    
    .step-icon {
        width: 25px;
        height: 25px;
        margin-right: 10px;
    }
    
    .action-buttons a {
        padding: 6px 12px;
        font-size: 11px;
        min-width: 80px;
    }
}
