<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="breadcrumbs">
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Home</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Home</item>
                    <item name="label" xsi:type="string">Home</item>
                    <item name="link" xsi:type="string">/</item>
                </argument>
            </action>
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Register</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Register</item>
                    <item name="label" xsi:type="string">Register</item>
                </argument>
            </action>
        </referenceBlock>

        <referenceContainer name="main.content">
            <container name="customer.register.content.top" htmlTag="div" htmlClass="customer-register-content-top" before="-">
                <block class="Magento\Cms\Block\Block" name="customer_register_content_top">
                    <arguments>
                        <argument name="block_id" xsi:type="helper"
                                    helper="Totaltools\Customer\Helper\Data::getCustomerCreateAccountTopBlock" ></argument>
                    </arguments>
                </block>
            </container>
        </referenceContainer>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Join Insider Rewards</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <container name="customer.register.wrapper" htmlTag="div" htmlClass="customer-register-wrapper">
                <container name="customer.register.form.wrapper" htmlTag="div" htmlClass="customer-register-form-wrapper" before="-" />
                <container name="customer.register.benefits.wrapper" htmlTag="div" htmlClass="note customer-register-benefits-wrapper">
                    <container htmlTag="div" htmlClass="block block-register-benefits">
                        <container htmlTag="div" htmlClass="block-box-wrapper">
                            <block class="Magento\Cms\Block\Block" name="customer_create_account_benefits">
                                <arguments>
                                    <argument name="block_id" xsi:type="helper"
                                    helper="Totaltools\Customer\Helper\Data::getCustomerCreateBusinessAccountTopBenefits"></argument>
                                </arguments>
                            </block>
                        </container>
                    </container>
                </container>
            </container>
            <referenceBlock name="customer_form_register">
                <block class="Totaltools\Customer\Block\Account\Template" name="customer.profile.index.address" template="Totaltools_Customer::profile/address.phtml"/>
                <block class="Magento\Framework\View\Element\Template" name="customer.profile.index.tellmore" template="Totaltools_Customer::profile/tellmore.phtml"/>
                <block class="Totaltools\Customer\Block\Account\Template" name="customer.profile.index.preferences" template="Totaltools_Customer::profile/preferences.phtml"/>
                <block class="Totaltools\Customer\Block\Account\Create" name="customer.profile.index.terms" template="Totaltools_Customer::profile/terms.phtml"/>
            </referenceBlock>
        </referenceContainer>
        
        <move element="customer_form_register" destination="customer.register.form.wrapper" before="-" />
    </body>
</page>
