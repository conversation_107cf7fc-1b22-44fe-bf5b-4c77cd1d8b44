//
// Custom styles for product stock alerts
//

& when (@media-common = true) {
    .product-stock-alert {
        margin: 15px 0;
        
        .stock-notification-container {
            display: flex;
            flex-direction: column;
            
            .stock-notification-label {
                margin-bottom: 5px;
                
                span {
                    font-weight: bold;
                    color: #e02b27;
                }
            }
            
            .stock-notification-button {
                .action.alert {
                    background: #f2f2f2;
                    border: 1px solid #d1d1d1;
                    color: #333;
                    display: inline-block;
                    padding: 7px 15px;
                    text-decoration: none;
                    border-radius: 3px;
                    
                    &:hover {
                        background: #e6e6e6;
                    }
                }
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .product-stock-alert {
        .stock-notification-container {
            flex-direction: row;
            align-items: center;
            
            .stock-notification-label {
                margin-right: 15px;
                margin-bottom: 0;
            }
        }
    }
}