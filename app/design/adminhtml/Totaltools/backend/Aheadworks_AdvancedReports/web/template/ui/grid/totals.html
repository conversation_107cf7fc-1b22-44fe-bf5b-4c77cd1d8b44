<div class="aw-arep__data-grid-top-totals-wrap">
    <ul if="atLeastOneColumnIsVisible()" class="aw-arep__top-totals-list" repeat="foreach: totals, item: '$row'">
        <li class="total-item" outerfasteach="data: elems, as: '$col'" visible="$col.visible"
            data-bind="style: {'border-color': $col.color}">
            <div class="total-title" text="$parent.getTotalLabel($col)"></div>
            <div class="total-content">
                <div class="current-total" text="$col.getLabel($row())" data-bind="style: {color: $col.color}"></div>
                <div if="$col.isDisplayTotalCompareValue()" class="aw-arep__widget-difference-value" css="getAdditionalDifferenceClasses($row())">
                    <div class="difference-value-percent"><span text="getDifferenceValueInPercent($row())"></span></div>
                    <div class="difference-value"><span text="getFormattedDifferenceValue($row())"></span></div>
                </div>
                <div if="$col.isDisplayTotalCompareValue()" class="compare-total" text="$col.getLabel($row(), $col.getCompareIndex())"></div>
            </div>
        </li>
    </ul>
</div>
