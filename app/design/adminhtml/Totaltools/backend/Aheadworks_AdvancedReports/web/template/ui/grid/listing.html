<div class="admin__data-grid-wrap aw-arep__data-grid-wrap" data-role="grid-wrapper">
    <table class="data-grid" data-role="grid">
        <thead>
            <tr each="data: getVisible(), as: '$col'" render="getHeader()"></tr>
        </thead>
        <tbody>
            <tr class="data-row" repeat="foreach: rows, item: '$row'" css="'_odd-row': $index % 2">
                <td outerfasteach="data: getVisible(), as: '$col'"
                    css="getFieldClass($row())" click="getFieldHandler($row())" template="getBody()"></td>
            </tr>
            <tr ifnot="hasData()" class="data-grid-tr-no-data">
                <td attr="colspan: countVisible()" translate="'We couldn\'t find any records.'"></td>
            </tr>
        </tbody>
        <tfoot if="isDisplayTotals()">
            <tr class="data-row" repeat="foreach: totals, item: '$row'">
                <th outerfasteach="data: getVisible(), as: '$col'" css="$col.getFieldClass()">
                    <render if="$col.totalsVisible" args="$col.getBody()"></render>
                </th>
            </tr>
        </tfoot>
    </table>
</div>
