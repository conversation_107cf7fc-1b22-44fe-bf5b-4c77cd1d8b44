<div class="data-grid-cell-content data-grid-compare-cell-content">
    <div class="cell-content">
        <div class="current-value-wrap">
            <div if="isDisplayPircentBar()" class="percent-bar-wrap">
                <div class="percent-bar">
                    <div class="percent-bar-inner"
                        data-bind="style: {width: $col.getPercentBarWidth($row()) + 'px'}"></div>
                </div>
            </div>
            <span class="current-value" text="$col.getLabel($row())"></span>
        </div>
        <if args="$col.isDisplayCompareValue($row())">
            <div class="compare-value-wrap">
                <div if="isDisplayPircentBar()" class="percent-bar-wrap">
                    <div class="percent-bar">
                        <div class="percent-bar-inner"
                            data-bind="style: {width: $col.getPercentBarWidth($row(), $col.getCompareIndex()) + 'px'}"></div>
                    </div>
                </div>
                <span class="compare-value" text="$col.getLabel($row(), $col.getCompareIndex())"></span>
            </div>
        </if>
    </div>
    <div if="$col.isDisplayCompareValue($row())" class="aw-arep__widget-difference-value-wrap">
        <div class="aw-arep__widget-difference-value" css="getAdditionalDifferenceClasses($row())">
            <div class="difference-value-percent"><span text="getDifferenceValueInPercent($row())"></span></div>
            <div class="difference-value"><span text="getFormattedDifferenceValue($row())"></span></div>
        </div>
    </div>
</div>
