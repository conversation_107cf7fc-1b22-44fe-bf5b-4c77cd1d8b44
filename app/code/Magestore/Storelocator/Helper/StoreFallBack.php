<?php

namespace Magestore\Storelocator\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\ResourceConnection;

class StoreFallBack extends AbstractHelper
{
    /**
     * @var   \Magento\Framework\App\ResourceConnection
     */
    protected $resourceConnection;
    /**
     * @var    \Magento\Backend\Block\Widget\Context
     */
    protected $context;

    public function __construct(
        Context $context,
        ResourceConnection $resourceConnection
    ) {
        $this->resourceConnection = $resourceConnection;
        parent::__construct($context);
    }

    /**
     *Fetch store fallback based on storeid
     * @param string $storeId
     * @return array|null
     */

    public function getStoreFallBack($storeId)
    {
        $connection = $this->resourceConnection->getConnection();
        $tableFallback = $connection->getTableName('magestore_storelocator_fallback');
        $tableStore = $connection->getTableName('magestore_storelocator_store');

        $selectQuery = $connection->select()
        ->from(
            ['a' => $tableFallback],
            ['a.store_id','c.store_name as store_name','b.store_name as fallback_sotre']
        )->join(
            ['b' =>$tableStore],
            'a.fallback_store_id = b.storelocator_id'
        )->joinLeft(
            ['c' => $tableStore],
            'a.store_id = c.storelocator_id'
        )->where(
            'a.store_id=?',
            $storeId
        )->order('a.row_id');
        $result = $connection->fetchAll($selectQuery);
        return $result;
    }
      /**
     *Fetch all stores
     * @return array|null
     */
    public function getAllstores()
    {
        $connection = $this->resourceConnection->getConnection();
        $tableStore = $connection->getTableName('magestore_storelocator_store');

        $selectQuery = $connection->select()
        ->from(
            ['a' => $tableStore],
            ['a.storelocator_id','a.store_name as store_name']
        );
        $result = $connection->fetchAll($selectQuery);
        return $result;
    }
}
