<?php

/**
 * Magestore.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

namespace Magestore\Storelocator\Controller\Adminhtml;

/**
 * Abstract Schedule Action.
 *
 * @category Magestore
 * @package  Magestore_Storelocator
 * @module   Storelocator
 * <AUTHOR> Developer
 */
abstract class Schedule extends \Magestore\Storelocator\Controller\Adminhtml\AbstractAction
{
    /**
     * param id for crud action : edit,delete,save.
     */
    const PARAM_CRUD_ID = 'schedule_id';

    /**
     * registry name.
     */
    const REGISTRY_NAME = 'storelocator_schedule';

    /**
     * Init page.
     *
     * @param \Magento\Backend\Model\View\Result\Page $resultPage
     *
     * @return \Magento\Backend\Model\View\Result\Page
     */
    protected function initPage($resultPage)
    {
        $resultPage->setActiveMenu('Magestore_Storelocator::storelocator')
            ->addBreadcrumb(__('Store Locator'), __('Store Locator'))
            ->addBreadcrumb(__('Manage Schedule'), __('Manage Schedule'));

        return $resultPage;
    }

    /**
     * Check the permission to run it.
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Magestore_Storelocator::schedule');
    }
}
