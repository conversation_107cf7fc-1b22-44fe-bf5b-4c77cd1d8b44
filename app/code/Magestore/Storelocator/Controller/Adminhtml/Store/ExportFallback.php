<?php

namespace  Magestore\Storelocator\Controller\Adminhtml\Store;

use Magento\Framework\App\Filesystem\DirectoryList;

class ExportFallback extends \Magento\Backend\App\Action
{
    /**
     * @var  \Magento\Framework\App\Request\Http
     */
    protected $request;
    /**
     * @var   \Magento\Framework\App\ResourceConnection
     */
    protected $resourceConnection;
    /**
    * @var  \Magestore\Storelocator\Helper\StoreFallBack
    */
    protected $storeFallBackHelper;
    /**
    * @var  directory
    */
    protected $directory;
    /**
    * @var   \Magento\Framework\App\Response\Http\FileFactory
    */
    protected $_fileFactory;


    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\App\Response\Http\FileFactory $fileFactory,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\App\Request\Http $request,
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        \Magestore\Storelocator\Helper\StoreFallBack $storeFallBackHelper
    ) {
        $this->_fileFactory = $fileFactory;
        $this->directory = $filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $this->request = $request;
        $this->resourceConnection = $resourceConnection;
        $this->storeFallBackHelper = $storeFallBackHelper;
        parent::__construct($context);
    }
    public function execute()
    {
        $name = date('m_d_Y_H_i_s');
        $filepath = 'export/custom' . $name . '.csv';
        $this->directory->create('export');
        //fetch result from db
        $result = $this->storeFallBackHelper->getAllstores();
        //end query
        /* Open file */
        $stream = $this->directory->openFile($filepath, 'w+');
        $stream->lock();
        $columns = $this->getColumnHeader();
        foreach ($columns as $column) {
            $header[] = $column;
        }
        /* Write Header */
        $stream->writeCsv($header);
        foreach ($result as $key=>$fallback) {
            //fetch fall back of each store
            $storeFallBack = $this->storeFallBackHelper->getStoreFallBack($fallback['storelocator_id']);
            $nCount=1;
            //write result on csv
            foreach($storeFallBack as $data) {
                $itemData = [];
                $itemData[] = $data['store_name'];
                $itemData[] = $nCount;
                $itemData[] = $data['fallback_sotre'];
                $stream->writeCsv($itemData);
                $nCount++;
            }
        }
        $content = [];
        $content['type'] = 'filename'; // must keep filename
        $content['value'] = $filepath;
        $content['rm'] = '1'; //remove csv from var folder

        $csvfilename = 'store-fallback.csv';
        return $this->_fileFactory->create($csvfilename, $content, DirectoryList::VAR_DIR);
    }

    /* Header Columns */
    public function getColumnHeader()
    {
        $headers = ['Main Store','Fallback Position','Fallback Store'];
        return $headers;
    }
}
