<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magestore\Storelocator\Controller\Adminhtml\Store">
        <arguments>
            <argument name="mainModelName" xsi:type="string">Magestore\Storelocator\Model\Store</argument>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Store\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Tag">
        <arguments>
            <argument name="mainModelName" xsi:type="string">Magestore\Storelocator\Model\Tag</argument>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Tag\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Holiday">
        <arguments>
            <argument name="mainModelName" xsi:type="string">Magestore\Storelocator\Model\Holiday</argument>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Holiday\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Specialday">
        <arguments>
            <argument name="mainModelName" xsi:type="string">Magestore\Storelocator\Model\Specialday</argument>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Specialday\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Schedule">
        <arguments>
            <argument name="mainModelName" xsi:type="string">Magestore\Storelocator\Model\Schedule</argument>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Schedule\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Store\InlineEdit">
        <arguments>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Store\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Tag\InlineEdit">
        <arguments>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Tag\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Holiday\InlineEdit">
        <arguments>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Holiday\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Specialday\InlineEdit">
        <arguments>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Specialday\Collection</argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Controller\Adminhtml\Schedule\InlineEdit">
        <arguments>
            <argument name="mainCollectionName" xsi:type="string">Magestore\Storelocator\Model\ResourceModel\Schedule\Collection</argument>
        </arguments>
    </type>
    <type name="Magento\Config\Model\Config\Structure\Element\Field">
        <plugin name="custom_field" type="Magestore\Storelocator\Plugin\Config\Structure\Element\Field"/>
    </type>
</config>
