<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Magestore_Storelocator::storelocator" title="Store Locator" resource="Magestore_Storelocator::storelocator" module="Magestore_Storelocator" sortOrder="20"/>
        <add id="Magestore_Storelocator::store" title="Manage Store" resource="Magestore_Storelocator::store" parent="Magestore_Storelocator::storelocator" action="storelocatoradmin/store" module="Magestore_Storelocator" sortOrder="20"/>
        <add id="Magestore_Storelocator::tag" title="Manage Tag" resource="Magestore_Storelocator::tag" parent="Magestore_Storelocator::storelocator" action="storelocatoradmin/tag" module="Magestore_Storelocator" sortOrder="20"/>
        <add id="Magestore_Storelocator::schedule" title="Manage Schedule" resource="Magestore_Storelocator::schedule" parent="Magestore_Storelocator::storelocator" action="storelocatoradmin/schedule" module="Magestore_Storelocator" sortOrder="20"/>
        <add id="Magestore_Storelocator::holiday" title="Manage Holiday" resource="Magestore_Storelocator::holiday" parent="Magestore_Storelocator::storelocator" action="storelocatoradmin/holiday" module="Magestore_Storelocator" sortOrder="20"/>
        <add id="Magestore_Storelocator::specialday" title="Manage Special day" resource="Magestore_Storelocator::specialday" parent="Magestore_Storelocator::storelocator" action="storelocatoradmin/specialday" module="Magestore_Storelocator" sortOrder="20"/>
        <add id="Magestore_Storelocator::guide" title="Guide" module="Magestore_Storelocator" parent="Magestore_Storelocator::storelocator" action="storelocatoradmin/guide" resource="Magestore_Storelocator::guide" sortOrder="20" />
        <add id="Magestore_Storelocator::settings" title="Settings" module="Magestore_Storelocator" parent="Magestore_Storelocator::storelocator" action="adminhtml/system_config/edit/section/storelocator" resource="Magestore_Storelocator::settings" sortOrder="20"/>
    </menu>
</config>
