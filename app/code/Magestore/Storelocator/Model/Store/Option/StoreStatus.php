<?php
namespace Magestore\Storelocator\Model\Store\Option;

class StoreStatus implements \Magento\Framework\Data\OptionSourceInterface, \Magestore\Storelocator\Model\Data\Option\OptionHashInterface
{
    const STORE_STATUS_OPEN = 1;
    const STORE_STATUS_CLOSE = 2;

    /**
     * Return array of options as value-label pairs.
     *
     * @return array Format: array(array('value' => '<value>', 'label' => '<label>'), ...)
     */
    public function toOptionArray()
    {
        return [
            ['label' => __('Enabled'), 'value' => self::STORE_STATUS_OPEN],
            ['label' => __('Disabled'), 'value' => self::STORE_STATUS_CLOSE],
        ];
    }

    /**
     * Return array of options as key-value pairs.
     *
     * @return array Format: array('<key>' => '<value>', '<key>' => '<value>', ...)
     */
    public function toOptionHash()
    {
        return [
            self::STORE_STATUS_OPEN => __('Enabled'),
            self::STORE_STATUS_CLOSE => __('Disabled'),
        ];
    }
}
