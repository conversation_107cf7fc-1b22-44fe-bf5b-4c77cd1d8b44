<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

 ?>
<div class="fieldset ">
    <?php echo '1. Visit the APIs Console at'?> <a href="https://code.google.com/apis/console" target="_blank"><?php echo 'https://code.google.com/apis/console' ?></a><?php echo ' and log in with your Google Account.' ?>
    <br />
    <?php echo '2. Click on the <strong>APIs</strong> link in the left menu.' ?>
    <br />
    <?php echo '3. Activate the <strong>Google Maps JavaScript API v3</strong>, <strong>Distance Matrix API</strong>, <strong>Geocoding API</strong> services.' ?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storelocator::images/guide/google-api-active.png') ?>" title="<?php echo __('Google API Active') ?>" />
    <br />
    <br />
    <?php echo '4. Click on the <strong>Credentials</strong> link in the left menu,  the "CREATE NEW KEY" in the "Public API access" section to create Google Map API Key for browser apps.' ?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storelocator::images/guide/create-key.png') ?>" title="<?php echo __('Google API Active') ?>" />
</div>
