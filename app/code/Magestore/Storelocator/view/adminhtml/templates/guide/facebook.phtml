<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

 ?>
<div class="fieldset ">
    <?php echo '1. Visit the Facebook Developer page at '?> <a href="http://developers.facebook.com/setup/" target="_blank"><?php echo 'http://developers.facebook.com/setup/' ?></a><?php echo '  and log in with your Facebook Account.' ?>
    <br />
    <?php echo '2. Click on the <strong>Create New App</strong> button in the right.' ?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storelocator::images/guide/create_new_appbutton.jpg') ?>" title="<?php echo __('Create Facebook app') ?>" />
    <br />
    <?php echo '3. Fill in the form with required information to create a new Facebook app.' ?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storelocator::images/guide/create_new_app_form.jpg') ?>" title="<?php echo __('Create Facebook app') ?>" />
    <br />
    <?php echo '4. Click on the <strong>Settings</strong> link in the left menu.' ?>
    <br />
    <?php echo '5. Insert your domain into the App Domain field.' ?>
    <br />
    <?php echo '6. Copy the App ID and paste it in your back-end(Store Locator->Settings/Facebook API key).' ?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storelocator::images/guide/app_setting_form.jpg') ?>" title="<?php echo __('Add domain') ?>" />
</div>
