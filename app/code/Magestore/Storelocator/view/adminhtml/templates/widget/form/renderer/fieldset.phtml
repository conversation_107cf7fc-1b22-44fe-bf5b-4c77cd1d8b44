<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

?>
<?php

/** @var \Magestore\Storelocator\Block\Adminhtml\Widget\Form\Renderer\Fieldset $block */

/** @var \Magento\Framework\Data\Form\Element\Fieldset $element */
$element = $block->getElement();
$containerId = $element->getFieldsetContainerId();
$id = $element->getHtmlId();
$isCollapsable = $element->getCollapsable();
$isWrapped = $containerId || $element->getHeaderBar() || $isCollapsable;
$titleActions = '<div class="actions">' . $element->getHeaderBar() . '</div>';
$isField = $element->getFieldsetType() == 'field';
$advancedAfter = $element->getAdvancedPosition() == 'after'; // To place advanced options inside or after fieldset
$advancedLabel = $element->getAdvancedLabel();
if (!isset($advancedLabel)) {
    $advancedLabel = __('Additional Settings');
}

$cssClass = ($isField) ? 'field ' . $element->getClass() : 'fieldset admin__fieldset ' . $element->getClass();

if ($isField) {
    $count = $element->getCountBasicChildren();
    $cssClass .= ($element->hasAdvanced()) ? ' complex' : '';
}
?>

<?php
/**
* @todo investigate situations, when the following is needed:
* echo $element->getHeaderBar();
* echo $element->getSubFieldsetHtml();
*/ ?>

<?php if ($isWrapped): ?>
    <div class="fieldset-wrapper <?php echo($isCollapsable) ? 'admin__collapsible-block-wrapper opened' : ''; ?>"
        id="<?php echo $containerId ? $containerId : $id . '-wrapper';?>"
        data-role="<?php echo $id ?>-wrapper">
        <?php if($element->getApplyToAll()) : ?>
            <button id="schedule-allpy-to-all" type="button" class="primary"
                    style="position: relative;left: 43%;top: 50px;z-index: 100;">Apply to all
            </button>
        <?php endif; ?>
        <div class="fieldset-wrapper-title admin__fieldset-wrapper-title active">
            <strong <?php echo($isCollapsable) ?
                'class="admin__collapsible-title" data-toggle="collapse" data-target="#' . $id . '-content"' :
                'class="title"'; ?>>
                <span><?php echo $element->getLegend() ?></span>
            </strong>
            <?php echo $titleActions; ?>
        </div>
        <div class="fieldset-wrapper-content admin__fieldset-wrapper-content<?php echo($isCollapsable) ? ' in collapse' : ''; ?>"
            id="<?php echo $id ?>-content"
            data-role="<?php echo $id ?>-content" style="height: 326px;">
<?php endif; ?>

    <?php if (!$element->getNoContainer()): ?>
        <fieldset class="<?php echo $cssClass ?>" id="<?php echo $id ?>">
        <?php if ($element->getLegend() && !$isWrapped): ?>
            <legend class="<?php echo $isField ? 'label' : 'legend'?>">
                <span><?php echo $element->getLegend() ?></span>
            </legend><br />
        <?php endif; ?>
    <?php endif; ?>


    <div class="messages">
        <?php if ($element->getComment() && !$isField): ?>
            <div class="message message-notice"><?php echo $block->escapeHtml($element->getComment()) ?></div>
        <?php endif; ?>
    </div>


    <?php echo($isField) ? '<div class="control admin__field-control">' : ''; ?>

    <?php if ($element->hasHtmlContent() && !$isField): ?>
        <?php echo $element->getHtmlContent(); ?>
    <?php else: ?>

        <?php if ($isField && $count > 1):?>
            <div class="fields-group-<?php echo $count?>">
        <?php endif; ?>

        <?php echo $element->getBasicChildrenHtml(); ?>

        <?php echo($isField && $count > 1) ? '</div>' : '' ?>

        <?php if ($element->getComment() && $isField): ?>
            <div class="note"><?php echo $block->escapeHtml($element->getComment()) ?></div>
        <?php endif; ?>

        <?php if ($element->hasAdvanced() && !$isField): ?>
            <?php echo(!$element->getNoContainer() && $advancedAfter)  ? '</fieldset>' : ''?>
            <details data-mage-init='{"details": {}}' class="details" id="details<?php echo $id ?>">
                <summary class="details-summary" id="details-summary<?php echo $id ?>">
                    <span><?php echo $advancedLabel ?></span>
                </summary>
                <div class="details-content" id="details-content<?php echo $id ?>">
                    <?php echo $element->getAdvancedChildrenHtml(); ?>
                </div>
            </details>
        <?php elseif ($element->hasAdvanced() && $isField): ?>
            <div class="nested" id="nested<?php echo $id ?>">
                <?php echo $element->getAdvancedChildrenHtml(); ?>
            </div>
        <?php endif; ?>

        <?php echo($isField) ? '</div>' : ''; ?>

    <?php endif; ?>


    <?php if (!$element->getNoContainer() && !$advancedAfter): ?>
        </fieldset>
    <?php endif; ?>

<?php if ($isWrapped): ?>
        </div>
    </div>
<?php endif; ?>
