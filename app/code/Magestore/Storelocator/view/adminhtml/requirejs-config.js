/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
var config = {
    map: {
       '*': {
            'magestore/weekdaytime' : 'Magestore_Storelocator/js/weekdaytime',
            'magestore/utilities' : 'Magestore_Storelocator/js/utilities',
            'magestore/schedule' : 'Magestore_Storelocator/js/form/schedule',
            'magestore/specialday' : 'Magestore_Storelocator/js/form/specialday',
            'magestore/map': 'Magestore_Storelocator/js/store/map',
            'magestore/baseimage': 'Magestore_Storelocator/js/gallery/base-image-uploader',
       }
    },
    paths: {
    },
};
