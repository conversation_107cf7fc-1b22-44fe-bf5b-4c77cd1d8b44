/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
.mage-boder-box {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.views-wrapper {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.views-wrapper .description {
  padding-left: 15px;
}
.views-wrapper *,
.views-wrapper:before,
.views-wrapper:after {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.views-wrapper .table-wrap {
  padding: 0;
}
.views-wrapper .table-wrap .info-detail {
  border: 1px solid #ececec;
  padding: 0;
  margin-bottom: 15px;
}
.views-wrapper .table-wrap .info-detail h2.title-store {
  color: #fff;
  font-size: 14px;
  text-transform: capitalize;
  font-weight: 600;
  padding: 8px 15px 8px 40px;
  background: #428BCA url('../images/Shop_Info.png') no-repeat 15px center;
  background-size: 18px;
  margin: 0 0 15px;
}
.views-wrapper .table-wrap .info-detail h2 p {
  color: #464646;
  font-size: 12px;
  margin-bottom: 5px;
  line-height: 23px;
  width: 100%;
  padding-left: 15px;
}
.views-wrapper .table-wrap .info-detail .get_direction {
  margin: 10px 0;
  max-height: 500px;
  border-top: 1px solid #ececec;
  padding-top: 10px;
}
.views-wrapper .table-wrap .open_hour {
  border: 1px solid #cccccc;
}
.views-wrapper .table-wrap .open_hour h2.open_hour_title {
  color: #fff;
  font-size: 14px;
  padding: 8px 0 8px;
  background-color: #428BCA;
  margin-bottom: 0px;
  font-weight: 600;
  text-transform: none;
  margin-top: 0;
}
.views-wrapper .table-wrap .open_hour h2.open_hour_title span.glyphicon {
  padding-left: 15px;
  top: 4px;
}
.views-wrapper .table-wrap .open_hour table {
  margin: 0;
}
.views-wrapper .table-wrap .tab-content {
  max-height: 300px;
  overflow: auto;
  border: 1px solid #ccc;
}
.views-wrapper .table-wrap .tab-content .open_time h3 {
  font-size: 15px;
  font-weight: 600;
  border-bottom: 1px solid #ececec;
  padding-bottom: 5px;
  margin-bottom: 0;
  text-transform: uppercase;
}
.views-wrapper .table-wrap .tab-content .open_time .content1,
.views-wrapper .table-wrap .tab-content .open_time .content2 {
  width: 50%;
  float: left;
}
.views-wrapper .table-wrap .tab-content li {
  display: block;
  float: left;
  width: 100%;
  padding: 10px 15px;
  text-align: center;
  border-bottom: 1px solid #ececec;
}
.views-wrapper .table-wrap .tab_content[class*=col-] {
  padding: 0px 0px 0px 0px ;
}
.views-wrapper .table-wrap .tab_content .tabs {
  padding: 0;
}
.views-wrapper .table-wrap .tab_content .tabs ul {
  padding: 0;
  margin: 0;
}
.views-wrapper .table-wrap .tab_content .tabs ul li {
  display: block;
  float: left;
  width: 50%;
  padding: 10px 15px;
  text-align: center;
  background: #868686;
  cursor: pointer;
  margin: 0;
  color: #fff;
}
.views-wrapper .table-wrap .tab_content .tabs ul li.active {
  background: #428BCA;
  color: #fff;
}
.views-wrapper .table-wrap .tab_content .tabs ul li.full-width {
  width: 100%;
  background: #428BCA;
}
.views-wrapper .form-information h2 {
  color: #fff;
  font-size: 14px;
  text-transform: capitalize;
  font-weight: 600;
  background-color: #428BCA;
  padding: 7px 15px;
}
.views-wrapper .form-information .magestore-map-image {
  float: left;
  padding: 7px 7px 6px;
  border: 1px solid #428BCA;
  background-color: #F5F5F5;
  border-top-left-radius: 5px;
  border-top-right-radius: 5px;
}
.views-wrapper .form-information .magestore-map-image img {
  max-height: 260px;
  min-height: 260px;
  width: 100%;
}
.views-wrapper .form-information .more-views {
  border-top: 5px solid #ccc;
  padding: 10px 0 0;
}
.views-wrapper .form-information .more-views .slider-imgs {
  max-height: 260px;
  min-height: 260px;
  overflow: auto;
}
.views-wrapper .form-information .more-views .slider-imgs ul {
  padding: 0;
  margin-left: 15px;
}
.views-wrapper .form-information .more-views .slider-imgs ul li {
  float: left;
  display: block;
  margin-right: 4px;
  padding: 4px;
  border: 1px solid #0282B7;
  border-radius: 3px;
  cursor: pointer;
}
.views-wrapper .form-information .more-views .slider-imgs ul li img {
  height: 50px;
}
.views-wrapper .form-information .more-views .slider-imgs ul li:hover {
  border: 1px solid darkred;
}
.views-wrapper .form-information .store-locator-img,
.views-wrapper .form-information .small-imgages {
  text-align: center;
  clear: both;
  color: #fff;
  padding: 2px 0;
  margin: 0;
  background-image: -webkit-gradient(linear, left top, left bottom, from(#2e9fd2), to(#21759b));
  background-image: -webkit-linear-gradient(top, #2e9fd2, #21759b);
  background-image: -moz-linear-gradient(top, #2e9fd2, #21759b);
  background-image: -ms-linear-gradient(top, #2e9fd2, #21759b);
  background-image: -o-linear-gradient(top, #2e9fd2, #21759b);
}
.views-wrapper .store-locator-column-right p > span {
  border-bottom: 2px solid #428BCA;
  color: cornflowerblue;
  font-size: 15px;
  font-weight: bold;
}
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
.page-footer {
  clear: both;
}
.magestore-resetcss button {
  background-image: none;
  background: #eee;
  border: 1px solid #ccc;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  margin: 0;
  padding: 7px 15px;
  font-size: 1.4rem;
  line-height: 1.6rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.magestore-resetcss .storelocator-wrapper button,
.magestore-resetcss a.action.primary {
  border-radius: 3px;
}
.magestore-resetcss .action.primary:hover {
  background: #006bb4;
  border: 1px solid #006bb4;
  color: #fff;
}
.magestore-resetcss .action.primary {
  background-image: none;
  background: #1979c3;
  border: 1px solid #1979c3;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  padding: 7px 15px;
  font-size: 1.4rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.magestore-resetcss button:hover {
  background: #e1e1e1;
  border: 1px solid #ccc;
  color: #333;
}
.magestore-resetcss input[type="text"],
.magestore-resetcss input[type="password"],
.magestore-resetcss input[type="url"],
.magestore-resetcss input[type="tel"],
.magestore-resetcss input[type="search"],
.magestore-resetcss input[type="number"],
.magestore-resetcss input[type="datetime"],
.magestore-resetcss input[type="email"] {
  background: #fff;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 0 9px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
}
.magestore-resetcss ._keyfocus :focus,
.magestore-resetcss input:focus:not([disabled]),
.magestore-resetcss textarea:focus:not([disabled]),
.magestore-resetcss select:focus:not([disabled]) {
  box-shadow: 0 0 3px 1px #68a8e0;
}
.magestore-resetcss select {
  background: #fff;
  background-size: 30px 60px;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 5px 10px 4px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  text-indent: .01em;
  text-overflow: '';
}
.map-box-container button,
.storelocator-wrapper button {
  background-image: none;
  background: #eee;
  border: 1px solid #ccc;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  margin: 0;
  padding: 7px 15px;
  font-size: 1.4rem;
  line-height: 1.6rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.map-box-container .storelocator-wrapper button,
.storelocator-wrapper .storelocator-wrapper button,
.map-box-container a.action.primary,
.storelocator-wrapper a.action.primary {
  border-radius: 3px;
}
.map-box-container .action.primary:hover,
.storelocator-wrapper .action.primary:hover {
  background: #006bb4;
  border: 1px solid #006bb4;
  color: #fff;
}
.map-box-container .action.primary,
.storelocator-wrapper .action.primary {
  background-image: none;
  background: #1979c3;
  border: 1px solid #1979c3;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  padding: 7px 15px;
  font-size: 1.4rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.map-box-container button:hover,
.storelocator-wrapper button:hover {
  background: #e1e1e1;
  border: 1px solid #ccc;
  color: #333;
}
.map-box-container input[type="text"],
.storelocator-wrapper input[type="text"],
.map-box-container input[type="password"],
.storelocator-wrapper input[type="password"],
.map-box-container input[type="url"],
.storelocator-wrapper input[type="url"],
.map-box-container input[type="tel"],
.storelocator-wrapper input[type="tel"],
.map-box-container input[type="search"],
.storelocator-wrapper input[type="search"],
.map-box-container input[type="number"],
.storelocator-wrapper input[type="number"],
.map-box-container input[type="datetime"],
.storelocator-wrapper input[type="datetime"],
.map-box-container input[type="email"],
.storelocator-wrapper input[type="email"] {
  background: #fff;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 0 9px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
}
.map-box-container ._keyfocus :focus,
.storelocator-wrapper ._keyfocus :focus,
.map-box-container input:focus:not([disabled]),
.storelocator-wrapper input:focus:not([disabled]),
.map-box-container textarea:focus:not([disabled]),
.storelocator-wrapper textarea:focus:not([disabled]),
.map-box-container select:focus:not([disabled]),
.storelocator-wrapper select:focus:not([disabled]) {
  box-shadow: 0 0 3px 1px #68a8e0;
}
.map-box-container select,
.storelocator-wrapper select {
  background: #fff;
  background-size: 30px 60px;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 5px 10px 4px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  text-indent: .01em;
  text-overflow: '';
}
.map-box-container,
.form-information {
  padding-right: 0 ;
  margin-bottom: 15px;
}
.map-box-container {
  height: 606px;
}
.map-box-container .googlemap {
  height: 100%;
}
.map-box-container h4 {
  margin: 5px 0 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
h2.title-page {
  color: #fff;
  font-size: 15px;
  text-transform: uppercase;
  font-weight: 600;
  padding: 8px 15px 8px 40px;
  background: #428BCA url('../images/shop.png') no-repeat 15px center;
  background-size: 18px;
  margin-bottom: 20px;
}
.custom-popup {
  left: 0 !important;
  margin-top: 5px;
}
.custom-popup .display {
  display: none;
}
.custom-popup .display.active {
  display: block;
}
.custom-popup.open {
  border: 1px solid #ececec;
}
.custom-popup ul.vertical {
  margin: 0;
  padding: 10px 0;
  border-top: 1px solid #ccc;
}
.custom-popup ul.vertical li {
  display: inline-block;
  margin: 0;
  padding: 0;
  box-shadow: none;
  background-color: #ccc;
  width: 40px;
  float: none;
  height: 20px;
  border-bottom: none;
  cursor: pointer;
}
.custom-popup ul.vertical li span {
  text-indent: -10000px;
  padding: 0px 20px 5px 13px;
  display: block;
}
.custom-popup ul.vertical li span:hover {
  border-bottom: 2px solid #428bca;
}
.custom-popup ul.vertical li.car {
  background: url('../images/sprite-icon.png') no-repeat center -40px;
}
.custom-popup ul.vertical li.car:hover,
.custom-popup ul.vertical li.car.active {
  background: url('../images/sprite-icon.png') no-repeat center -60px;
}
.custom-popup ul.vertical li.bus {
  background: url('../images/sprite-icon.png') no-repeat center -80px;
}
.custom-popup ul.vertical li.bus:hover,
.custom-popup ul.vertical li.bus.active {
  background: url('../images/sprite-icon.png') no-repeat center -100px;
}
.custom-popup ul.vertical li.walk {
  background: url('../images/sprite-icon.png') no-repeat center -120px;
}
.custom-popup ul.vertical li.walk:hover,
.custom-popup ul.vertical li.walk.active {
  background: url('../images/sprite-icon.png') no-repeat center -140px;
}
.custom-popup ul.vertical li.bicycle {
  background: url('../images/sprite-icon.png') no-repeat center -160px;
}
.custom-popup ul.vertical li.bicycle:hover,
.custom-popup ul.vertical li.bicycle.active {
  background: url('../images/sprite-icon.png') no-repeat center -180px;
}
.custom-popup .directions-tool {
  margin-bottom: 10px;
  overflow: auto;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle {
  visibility: visible ;
  position: absolute;
  height: 60px;
  width: 15px;
  padding: 8px 0 0 0;
  z-index: 4;
  transition: background-color 200ms cubic-bezier(0.52, 0, 0.48, 1);
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon {
  background: transparent url('../images/omnibox-sprite.png') no-repeat -2px -31px;
  background-size: 20px 100px;
  width: 16px;
  height: 17px;
  text-indent: -10000px;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon.waypoint-handle {
  background-position: 0 2px;
  width: 16px;
  height: 13px;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon.waypoint-bullet {
  background-position: 0 -12px;
  width: 11px;
  height: 20px;
  margin-top: 3px;
}
.custom-popup .directions-tool .form-inputs {
  padding-left: 22px;
}
.custom-popup .directions-tool .form-inputs input {
  height: 34px;
  width: 95%;
  border: none;
  box-shadow: none;
  border-radius: 0;
  outline: 0;
}
.custom-popup .directions-tool .form-inputs input.start {
  border-top: 1px solid #ccc;
}
.custom-popup .directions-tool .form-inputs input.end {
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
.custom-popup .directions-tool .widget-directions-right-overlay {
  position: absolute;
  z-index: 3;
  top: 4px;
  bottom: 0;
  right: 0;
  width: 30px;
  border: 0;
  opacity: 1;
  transition: opacity 200ms cubic-bezier(0.52, 0, 0.48, 1);
}
.custom-popup .directions-tool .widget-directions-right-overlay button {
  text-indent: -10000px;
  background: transparent url('../images/omnibox-sprite.png') no-repeat center -64px;
  width: 20px;
  border: none;
  box-shadow: none;
  margin-top: 20px;
  outline: 0;
  opacity: 0.5;
  padding: 0;
}
.custom-popup .directions-tool .widget-directions-right-overlay button:hover {
  opacity: 1;
}
.custom-popup .directions-tool .directions-panel {
  max-height: 300px;
}
.table-wrap .table > tbody > tr > td,
.table-wrap .table > tbody > tr > th,
.table-wrap .table > tfoot > tr > td,
.table-wrap .table > tfoot > tr > th,
.table-wrap .table > thead > tr > td,
.table-wrap .table > thead > tr > th {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ddd !important;
  text-align: center;
}
.col-full {
  float: left;
  width: 100%;
}
.disable-ul {
  padding: 0;
  margin: 0;
}
.location-box-view .widget-mylocation {
  width: 29px;
  height: 29px;
  cursor: pointer;
  position: relative;
  -moz-user-select: none;
  float: left;
  left: 10px;
}
.location-box-view .widget-mylocation .widget-mylocation-button {
  border-radius: 2px;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.3);
  display: block;
  width: 29px;
  height: 29px;
  overflow: hidden;
  cursor: pointer;
  padding: 0px;
  border: 0px none;
  outline: 0px none;
  font: inherit;
  vertical-align: baseline;
  background: none repeat scroll 0% 0% transparent;
  list-style: outside none none;
  background-color: #FFF;
}
.location-box-view .widget-mylocation .widget-mylocation-button:hover .widget-mylocation-cookieless {
  background-position: -36px 0px;
}
.location-box-view .widget-mylocation .widget-mylocation-button .widget-mylocation-cookieless {
  background: url('../images/geolocation_resize.png') no-repeat 0 0;
  background-attachment: scroll;
  display: block;
  height: 18px;
  left: 6px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  top: 6px;
  width: 18px;
}
@media screen and (min-width: 768px) and (max-width: 992px) {
  .views-wrapper .table-wrap > .tab_content[class*=col-] {
    padding: 0px 15px 0px 15px ;
  }
}
@media screen and (max-width: 768px) {
  .open_hour .table-responsive {
    border: none;
  }
  .map-box-container,
  .form-information {
    padding: 0;
  }
}
