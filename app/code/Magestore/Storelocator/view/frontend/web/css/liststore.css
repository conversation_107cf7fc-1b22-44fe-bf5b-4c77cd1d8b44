/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
.columns {
  display: block;
}
.mage-boder-box {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.storelocator-wrapper {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
  width: 100%;
  position: relative;
}
.storelocator-wrapper *,
.storelocator-wrapper:before,
.storelocator-wrapperafter {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}
.storelocator-wrapper h2.title-list {
  color: #fff;
  font-size: 15px;
  text-transform: uppercase;
  font-weight: 600;
  padding: 8px 15px 8px 40px;
  background: #428BCA url('../images/shop.png') no-repeat 15px center;
  background-size: 18px;
  margin-bottom: 20px;
}
.storelocator-wrapper .mgs-search-box [class*=col-] {
  padding: 0px /* nlttoan added for SC-65 */ 0px 0px 0px /**/;
}
.storelocator-wrapper .mgs-search-box .slider-range {
  border-radius: 3px;
  margin-top: 10px;
  width: 90%;
}
.storelocator-wrapper .mgs-search-box .slider-range .ui-slider-handle {
  height: 20px;
  top: -5px;
  width: 7px;
  border-radius: 3px;
}
.storelocator-wrapper .mgs-search-box .search-tab {
  padding: 8px 10px 8px 34px;
  color: #fff;
  border-right: 1px solid;
  border-top-right-radius: 8px;
  border-top-left-radius: 8px;
  cursor: pointer;
  background-color: #868686;
}
.storelocator-wrapper .mgs-search-box .search-tab.search-distance {
  background: #868686 url('../images/distance.png') no-repeat 3px 3px;
}
.storelocator-wrapper .mgs-search-box .search-tab.search-area {
  background: #868686 url('../images/area.png') no-repeat 3px 3px;
}
.storelocator-wrapper .mgs-search-box .search-tab.active {
  background-color: #1979c3;
}
.storelocator-wrapper .mgs-search-box .padding-left {
  padding-left: 15px;
}
.storelocator-wrapper .mgs-search-box .padding-right {
  padding-right: 15px;
}
.storelocator-wrapper .mgs-search-box .search-content {
  background: #f8f8f8;
  padding: 15px 10px;
  border: 1px solid #ddd;
  margin-bottom: 15px;
}
.storelocator-wrapper .mgs-search-box .search-content #list-tag-ul {
  margin: 0;
  padding: 0;
}
.storelocator-wrapper .mgs-search-box .search-content .slider-range-min {
  position: relative;
  margin-left: 10px;
}
.storelocator-wrapper .mgs-search-box .search-content .slider-range-min .show-unit {
  right: -25px;
  position: absolute;
  top: 5px;
}
.storelocator-wrapper .mgs-search-box .search-content .search-by-area .form-control {
  height: 32px;
}
.storelocator-wrapper .mgs-search-box .search-content .search-by-area select {
  -moz-appearance: -moz-win-borderless-glass;
}
.storelocator-wrapper .mgs-search-box .search-filter {
  margin-top: 10px;
}
.storelocator-wrapper .mgs-search-box .search-filter ul > li > input {
  vertical-align: text-top;
  margin-right: 5px;
}
.storelocator-wrapper .mgs-search-box .search-filter .list-inline > li {
  width: 60px;
  background-color: #fff;
  padding: 5px 0 0 0;
  margin-right: 3px;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #63ACE4;
}
.storelocator-wrapper .mgs-search-box .search-filter .list-inline > li.active,
.storelocator-wrapper .mgs-search-box .search-filter .list-inline > li:hover {
  background-color: #C8EBFD;
  border: 1px solid #1979c3;
}
.storelocator-wrapper .mgs-search-box .search-filter .list-inline > li.active p,
.storelocator-wrapper .mgs-search-box .search-filter .list-inline > li:hover p {
  background-color: #1979c3;
}
.storelocator-wrapper .mgs-search-box .search-filter .list-inline > li img {
  display: block;
  margin: 0 auto;
  width: 35px;
  height: 35px;
}
.storelocator-wrapper .mgs-search-box .search-filter .list-inline > li p {
  margin-top: 5px;
  margin-bottom: 0px;
  background-color: #63ACE4;
  color: #fff;
  font-size: 12px;
  height: 20px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.storelocator-wrapper .storelocator-wrapper .theme-blue .search-filter .list-inline > li {
  background-color: #63ACE4;
  border: 1px solid #1979c3;
}
.storelocator-wrapper .storelocator-wrapper .theme-blue .search-filter .list-inline > li p {
  background-color: #63ACE4;
  color: #fff;
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  height: 20px;
}
.storelocator-wrapper .storelocator-wrapper .theme-blue .search-filter .list-inline > li.active {
  background-color: #C8EBFD;
  border: 1px solid #1979c3;
}
.storelocator-wrapper .storelocator-wrapper .theme-blue .search-filter .list-inline > li.active p {
  background-color: #1979c3;
}
.storelocator-wrapper .list-store-box {
  padding: 0;
  margin: 0;
}
.storelocator-wrapper .list-store-box .page-title {
  padding-bottom: 10px;
  border: 1px solid #ddd;
  background-color: #f8f8f8;
  float: left;
  width: 100%;
  border-bottom: none;
}
.storelocator-wrapper .list-store-box h2 {
  font-size: 14px;
  font-weight: bold;
  background-color: #1979c3;
  margin-bottom: 10px;
  padding: 10px 15px;
  margin: 0;
  color: #fff;
}
.storelocator-wrapper .list-store-box .list-store {
  border: 1px solid #ddd;
  padding-top: 20px;
  padding-bottom: 20px;
  height: 500px;
  overflow-y: auto;
  background-color: #f8f8f8;
  border-top: none;
}
.storelocator-wrapper .list-store-box .show-tag-li {
  display: block;
  padding: 10px 0;
  float: left;
  width: 100%;
  margin-bottom: 10px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 1px 1px 5px #D6D6D6, -1px -1px 7px #D6D6D6;
  cursor: pointer;
  min-height: 97px;
  overflow: auto;
}
.storelocator-wrapper .list-store-box .show-tag-li:hover,
.storelocator-wrapper .list-store-box .show-tag-li.store-active {
  background-color: #eee;
}
.storelocator-wrapper .map-box-container .tag-content {
  padding-left: 20px;
}
.storelocator-wrapper .map-box-container .tag-store {
  padding: 0;
}
.storelocator-wrapper .list-store .store-item .top-box,
.storelocator-wrapper .map-box-container .store-item .top-box {
  margin-bottom: 10px;
}
.storelocator-wrapper .list-store .store-item .tag-store,
.storelocator-wrapper .map-box-container .store-item .tag-store {
  margin-top: 8px;
  text-align: center;
}
.storelocator-wrapper .list-store .store-item .tag-store img,
.storelocator-wrapper .map-box-container .store-item .tag-store img {
  min-width: 45px;
  display: table-cell;
  margin: 0 auto;
}
.storelocator-wrapper .list-store .store-item .title-store,
.storelocator-wrapper .map-box-container .store-item .title-store {
  color: #428BCB;
  white-space: normal;
  font-weight: bold;
  font-size: 12px;
  text-decoration: none;
}
.storelocator-wrapper .list-store .store-item .title-store:hover,
.storelocator-wrapper .map-box-container .store-item .title-store:hover {
  text-decoration: underline;
}
.storelocator-wrapper .list-store .store-item p,
.storelocator-wrapper .map-box-container .store-item p {
  font-size: 12px;
  line-height: 18px;
  margin: 0;
}
.storelocator-wrapper .list-store .store-item h4,
.storelocator-wrapper .map-box-container .store-item h4 {
  margin: 5px 0 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
.storelocator-wrapper .list-store .store-item .btn-link,
.storelocator-wrapper .map-box-container .store-item .btn-link {
  text-decoration: none;
  font-weight: bold;
  margin-right: 10px;
  color: #428bca;
  border-radius: 0;
}
.storelocator-wrapper .list-store .store-item .btn-link:hover,
.storelocator-wrapper .map-box-container .store-item .btn-link:hover {
  text-decoration: underline;
}
.storelocator-wrapper .pagination-list {
  border: 1px solid #ddd;
  border-top: none;
  margin-bottom: 15px;
}
.storelocator-wrapper .pagination-list .pagination {
  margin: 10px 0;
}
.storelocator-wrapper .form-inline .form-control {
  width: 100%;
  margin-bottom: 10px;
}
.overlay-bg {
  position: absolute;
  z-index: 100;
  background-color: #fff;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  opacity: 0.8;
  display: none;
}
.overlay-bg img {
  width: 70px;
  display: block;
  margin: 0 auto;
  position: absolute;
  top: 50%;
  bottom: 0;
  right: 0;
  left: 0;
}
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
.page-footer {
  clear: both;
}
.magestore-resetcss button {
  background-image: none;
  background: #eee;
  border: 1px solid #ccc;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  margin: 0;
  padding: 7px 15px;
  font-size: 1.4rem;
  line-height: 1.6rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.magestore-resetcss .storelocator-wrapper button,
.magestore-resetcss a.action.primary {
  border-radius: 3px;
}
.magestore-resetcss .action.primary:hover {
  background: #006bb4;
  border: 1px solid #006bb4;
  color: #fff;
}
.magestore-resetcss .action.primary {
  background-image: none;
  background: #1979c3;
  border: 1px solid #1979c3;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  padding: 7px 15px;
  font-size: 1.4rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.magestore-resetcss button:hover {
  background: #e1e1e1;
  border: 1px solid #ccc;
  color: #333;
}
.magestore-resetcss input[type="text"],
.magestore-resetcss input[type="password"],
.magestore-resetcss input[type="url"],
.magestore-resetcss input[type="tel"],
.magestore-resetcss input[type="search"],
.magestore-resetcss input[type="number"],
.magestore-resetcss input[type="datetime"],
.magestore-resetcss input[type="email"] {
  background: #fff;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 0 9px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
}
.magestore-resetcss ._keyfocus :focus,
.magestore-resetcss input:focus:not([disabled]),
.magestore-resetcss textarea:focus:not([disabled]),
.magestore-resetcss select:focus:not([disabled]) {
  box-shadow: 0 0 3px 1px #68a8e0;
}
.magestore-resetcss select {
  background: #fff;
  background-size: 30px 60px;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 5px 10px 4px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  text-indent: .01em;
  text-overflow: '';
}
.map-box-container button,
.storelocator-wrapper button {
  background-image: none;
  background: #eee;
  border: 1px solid #ccc;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  margin: 0;
  padding: 7px 15px;
  font-size: 1.4rem;
  line-height: 1.6rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.map-box-container .storelocator-wrapper button,
.storelocator-wrapper .storelocator-wrapper button,
.map-box-container a.action.primary,
.storelocator-wrapper a.action.primary {
  border-radius: 3px;
}
.map-box-container .action.primary:hover,
.storelocator-wrapper .action.primary:hover {
  background: #006bb4;
  border: 1px solid #006bb4;
  color: #fff;
}
.map-box-container .action.primary,
.storelocator-wrapper .action.primary {
  background-image: none;
  background: #1979c3;
  border: 1px solid #1979c3;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  padding: 7px 15px;
  font-size: 1.4rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.map-box-container button:hover,
.storelocator-wrapper button:hover {
  background: #e1e1e1;
  border: 1px solid #ccc;
  color: #333;
}
.map-box-container input[type="text"],
.storelocator-wrapper input[type="text"],
.map-box-container input[type="password"],
.storelocator-wrapper input[type="password"],
.map-box-container input[type="url"],
.storelocator-wrapper input[type="url"],
.map-box-container input[type="tel"],
.storelocator-wrapper input[type="tel"],
.map-box-container input[type="search"],
.storelocator-wrapper input[type="search"],
.map-box-container input[type="number"],
.storelocator-wrapper input[type="number"],
.map-box-container input[type="datetime"],
.storelocator-wrapper input[type="datetime"],
.map-box-container input[type="email"],
.storelocator-wrapper input[type="email"] {
  background: #fff;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 0 9px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
}
.map-box-container ._keyfocus :focus,
.storelocator-wrapper ._keyfocus :focus,
.map-box-container input:focus:not([disabled]),
.storelocator-wrapper input:focus:not([disabled]),
.map-box-container textarea:focus:not([disabled]),
.storelocator-wrapper textarea:focus:not([disabled]),
.map-box-container select:focus:not([disabled]),
.storelocator-wrapper select:focus:not([disabled]) {
  box-shadow: 0 0 3px 1px #68a8e0;
}
.map-box-container select,
.storelocator-wrapper select {
  background: #fff;
  background-size: 30px 60px;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 5px 10px 4px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  text-indent: .01em;
  text-overflow: '';
}
.map-box-container,
.form-information {
  padding-right: 0 ;
  margin-bottom: 15px;
}
.map-box-container {
  height: 606px;
}
.map-box-container .googlemap {
  height: 100%;
}
.map-box-container h4 {
  margin: 5px 0 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
h2.title-page {
  color: #fff;
  font-size: 15px;
  text-transform: uppercase;
  font-weight: 600;
  padding: 8px 15px 8px 40px;
  background: #428BCA url('../images/shop.png') no-repeat 15px center;
  background-size: 18px;
  margin-bottom: 20px;
}
.custom-popup {
  left: 0 !important;
  margin-top: 5px;
}
.custom-popup .display {
  display: none;
}
.custom-popup .display.active {
  display: block;
}
.custom-popup.open {
  border: 1px solid #ececec;
}
.custom-popup ul.vertical {
  margin: 0;
  padding: 10px 0;
  border-top: 1px solid #ccc;
}
.custom-popup ul.vertical li {
  display: inline-block;
  margin: 0;
  padding: 0;
  box-shadow: none;
  background-color: #ccc;
  width: 40px;
  float: none;
  height: 20px;
  border-bottom: none;
  cursor: pointer;
}
.custom-popup ul.vertical li span {
  text-indent: -10000px;
  padding: 0px 20px 5px 13px;
  display: block;
}
.custom-popup ul.vertical li span:hover {
  border-bottom: 2px solid #428bca;
}
.custom-popup ul.vertical li.car {
  background: url('../images/sprite-icon.png') no-repeat center -40px;
}
.custom-popup ul.vertical li.car:hover,
.custom-popup ul.vertical li.car.active {
  background: url('../images/sprite-icon.png') no-repeat center -60px;
}
.custom-popup ul.vertical li.bus {
  background: url('../images/sprite-icon.png') no-repeat center -80px;
}
.custom-popup ul.vertical li.bus:hover,
.custom-popup ul.vertical li.bus.active {
  background: url('../images/sprite-icon.png') no-repeat center -100px;
}
.custom-popup ul.vertical li.walk {
  background: url('../images/sprite-icon.png') no-repeat center -120px;
}
.custom-popup ul.vertical li.walk:hover,
.custom-popup ul.vertical li.walk.active {
  background: url('../images/sprite-icon.png') no-repeat center -140px;
}
.custom-popup ul.vertical li.bicycle {
  background: url('../images/sprite-icon.png') no-repeat center -160px;
}
.custom-popup ul.vertical li.bicycle:hover,
.custom-popup ul.vertical li.bicycle.active {
  background: url('../images/sprite-icon.png') no-repeat center -180px;
}
.custom-popup .directions-tool {
  margin-bottom: 10px;
  overflow: auto;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle {
  visibility: visible ;
  position: absolute;
  height: 60px;
  width: 15px;
  padding: 8px 0 0 0;
  z-index: 4;
  transition: background-color 200ms cubic-bezier(0.52, 0, 0.48, 1);
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon {
  background: transparent url('../images/omnibox-sprite.png') no-repeat -2px -31px;
  background-size: 20px 100px;
  width: 16px;
  height: 17px;
  text-indent: -10000px;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon.waypoint-handle {
  background-position: 0 2px;
  width: 16px;
  height: 13px;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon.waypoint-bullet {
  background-position: 0 -12px;
  width: 11px;
  height: 20px;
  margin-top: 3px;
}
.custom-popup .directions-tool .form-inputs {
  padding-left: 22px;
}
.custom-popup .directions-tool .form-inputs input {
  height: 34px;
  width: 95%;
  border: none;
  box-shadow: none;
  border-radius: 0;
  outline: 0;
}
.custom-popup .directions-tool .form-inputs input.start {
  border-top: 1px solid #ccc;
}
.custom-popup .directions-tool .form-inputs input.end {
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
.custom-popup .directions-tool .widget-directions-right-overlay {
  position: absolute;
  z-index: 3;
  top: 4px;
  bottom: 0;
  right: 0;
  width: 30px;
  border: 0;
  opacity: 1;
  transition: opacity 200ms cubic-bezier(0.52, 0, 0.48, 1);
}
.custom-popup .directions-tool .widget-directions-right-overlay button {
  text-indent: -10000px;
  background: transparent url('../images/omnibox-sprite.png') no-repeat center -64px;
  width: 20px;
  border: none;
  box-shadow: none;
  margin-top: 20px;
  outline: 0;
  opacity: 0.5;
  padding: 0;
}
.custom-popup .directions-tool .widget-directions-right-overlay button:hover {
  opacity: 1;
}
.custom-popup .directions-tool .directions-panel {
  max-height: 300px;
}
.table-wrap .table > tbody > tr > td,
.table-wrap .table > tbody > tr > th,
.table-wrap .table > tfoot > tr > td,
.table-wrap .table > tfoot > tr > th,
.table-wrap .table > thead > tr > td,
.table-wrap .table > thead > tr > th {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ddd !important;
  text-align: center;
}
.col-full {
  float: left;
  width: 100%;
}
.disable-ul {
  padding: 0;
  margin: 0;
}
.location-box-view .widget-mylocation {
  width: 29px;
  height: 29px;
  cursor: pointer;
  position: relative;
  -moz-user-select: none;
  float: left;
  left: 10px;
}
.location-box-view .widget-mylocation .widget-mylocation-button {
  border-radius: 2px;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.3);
  display: block;
  width: 29px;
  height: 29px;
  overflow: hidden;
  cursor: pointer;
  padding: 0px;
  border: 0px none;
  outline: 0px none;
  font: inherit;
  vertical-align: baseline;
  background: none repeat scroll 0% 0% transparent;
  list-style: outside none none;
  background-color: #FFF;
}
.location-box-view .widget-mylocation .widget-mylocation-button:hover .widget-mylocation-cookieless {
  background-position: -36px 0px;
}
.location-box-view .widget-mylocation .widget-mylocation-button .widget-mylocation-cookieless {
  background: url('../images/geolocation_resize.png') no-repeat 0 0;
  background-attachment: scroll;
  display: block;
  height: 18px;
  left: 6px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  top: 6px;
  width: 18px;
}
@media screen and (min-width: 768px) and (max-width: 992px) {
  .views-wrapper .table-wrap > .tab_content[class*=col-] {
    padding: 0px 15px 0px 15px ;
  }
}
@media screen and (max-width: 768px) {
  .open_hour .table-responsive {
    border: none;
  }
  .map-box-container,
  .form-information {
    padding: 0;
  }
}
@media screen and (max-width: 1587px) {
  .storelocator-wrapper .mgs-search-box .search-content .slider-range-min {
    margin-left: 0;
  }
  .storelocator-wrapper .mgs-search-box .search-content .slider-range-min .show-unit {
    right: 0;
  }
  .storelocator-wrapper .mgs-search-box .padding-left {
    padding-left: 0px;
  }
  .storelocator-wrapper .mgs-search-box .padding-right {
    padding-right: 0px;
  }
  .storelocator-wrapper .mgs-search-box .slider-range {
    width: 80%;
  }
  .storelocator-wrapper .list-store .tag-store img {
    max-width: 65px;
  }
}
@media screen and (max-width: 480px) {
  .storelocator-wrapper .mgs-search-box .search-tab {
    font-size: 12px;
  }
}
