/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
.page-footer {
  clear: both;
}
.magestore-resetcss button {
  background-image: none;
  background: #eee;
  border: 1px solid #ccc;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  margin: 0;
  padding: 7px 15px;
  font-size: 1.4rem;
  line-height: 1.6rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.magestore-resetcss .storelocator-wrapper button,
.magestore-resetcss a.action.primary {
  border-radius: 3px;
}
.magestore-resetcss .action.primary:hover {
  background: #006bb4;
  border: 1px solid #006bb4;
  color: #fff;
}
.magestore-resetcss .action.primary {
  background-image: none;
  background: #1979c3;
  border: 1px solid #1979c3;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  padding: 7px 15px;
  font-size: 1.4rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.magestore-resetcss button:hover {
  background: #e1e1e1;
  border: 1px solid #ccc;
  color: #333;
}
.magestore-resetcss input[type="text"],
.magestore-resetcss input[type="password"],
.magestore-resetcss input[type="url"],
.magestore-resetcss input[type="tel"],
.magestore-resetcss input[type="search"],
.magestore-resetcss input[type="number"],
.magestore-resetcss input[type="datetime"],
.magestore-resetcss input[type="email"] {
  background: #fff;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 0 9px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
}
.magestore-resetcss ._keyfocus :focus,
.magestore-resetcss input:focus:not([disabled]),
.magestore-resetcss textarea:focus:not([disabled]),
.magestore-resetcss select:focus:not([disabled]) {
  box-shadow: 0 0 3px 1px #68a8e0;
}
.magestore-resetcss select {
  background: #fff;
  background-size: 30px 60px;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 5px 10px 4px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  text-indent: .01em;
  text-overflow: '';
}
.map-box-container button,
.storelocator-wrapper button {
  background-image: none;
  background: #eee;
  border: 1px solid #ccc;
  color: #333;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  margin: 0;
  padding: 7px 15px;
  font-size: 1.4rem;
  line-height: 1.6rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.map-box-container .storelocator-wrapper button,
.storelocator-wrapper .storelocator-wrapper button,
.map-box-container a.action.primary,
.storelocator-wrapper a.action.primary {
  border-radius: 3px;
}
.map-box-container .action.primary:hover,
.storelocator-wrapper .action.primary:hover {
  background: #006bb4;
  border: 1px solid #006bb4;
  color: #fff;
}
.map-box-container .action.primary,
.storelocator-wrapper .action.primary {
  background-image: none;
  background: #1979c3;
  border: 1px solid #1979c3;
  color: #fff;
  cursor: pointer;
  display: inline-block;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-weight: 600;
  padding: 7px 15px;
  font-size: 1.4rem;
  box-sizing: border-box;
  vertical-align: middle;
}
.map-box-container button:hover,
.storelocator-wrapper button:hover {
  background: #e1e1e1;
  border: 1px solid #ccc;
  color: #333;
}
.map-box-container input[type="text"],
.storelocator-wrapper input[type="text"],
.map-box-container input[type="password"],
.storelocator-wrapper input[type="password"],
.map-box-container input[type="url"],
.storelocator-wrapper input[type="url"],
.map-box-container input[type="tel"],
.storelocator-wrapper input[type="tel"],
.map-box-container input[type="search"],
.storelocator-wrapper input[type="search"],
.map-box-container input[type="number"],
.storelocator-wrapper input[type="number"],
.map-box-container input[type="datetime"],
.storelocator-wrapper input[type="datetime"],
.map-box-container input[type="email"],
.storelocator-wrapper input[type="email"] {
  background: #fff;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 0 9px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
}
.map-box-container ._keyfocus :focus,
.storelocator-wrapper ._keyfocus :focus,
.map-box-container input:focus:not([disabled]),
.storelocator-wrapper input:focus:not([disabled]),
.map-box-container textarea:focus:not([disabled]),
.storelocator-wrapper textarea:focus:not([disabled]),
.map-box-container select:focus:not([disabled]),
.storelocator-wrapper select:focus:not([disabled]) {
  box-shadow: 0 0 3px 1px #68a8e0;
}
.map-box-container select,
.storelocator-wrapper select {
  background: #fff;
  background-size: 30px 60px;
  background-clip: padding-box;
  border: 1px solid #c2c2c2;
  border-radius: 1px;
  font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 14px;
  height: 32px;
  line-height: 1.42857143;
  padding: 5px 10px 4px;
  vertical-align: baseline;
  width: 100%;
  box-sizing: border-box;
  -webkit-appearance: none;
  -moz-appearance: none;
  -ms-appearance: none;
  appearance: none;
  text-indent: .01em;
  text-overflow: '';
}
.map-box-container,
.form-information {
  padding-right: 0 ;
  margin-bottom: 15px;
}
.map-box-container {
  height: 606px;
}
.map-box-container .googlemap {
  height: 100%;
}
.map-box-container h4 {
  margin: 5px 0 10px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
h2.title-page {
  color: #fff;
  font-size: 15px;
  text-transform: uppercase;
  font-weight: 600;
  padding: 8px 15px 8px 40px;
  background: #428BCA url('../images/shop.png') no-repeat 15px center;
  background-size: 18px;
  margin-bottom: 20px;
}
.custom-popup {
  left: 0 !important;
  margin-top: 5px;
}
.custom-popup .display {
  display: none;
}
.custom-popup .display.active {
  display: block;
}
.custom-popup.open {
  border: 1px solid #ececec;
}
.custom-popup ul.vertical {
  margin: 0;
  padding: 10px 0;
  border-top: 1px solid #ccc;
}
.custom-popup ul.vertical li {
  display: inline-block;
  margin: 0;
  padding: 0;
  box-shadow: none;
  background-color: #ccc;
  width: 40px;
  float: none;
  height: 20px;
  border-bottom: none;
  cursor: pointer;
}
.custom-popup ul.vertical li span {
  text-indent: -10000px;
  padding: 0px 20px 5px 13px;
  display: block;
}
.custom-popup ul.vertical li span:hover {
  border-bottom: 2px solid #428bca;
}
.custom-popup ul.vertical li.car {
  background: url('../images/sprite-icon.png') no-repeat center -40px;
}
.custom-popup ul.vertical li.car:hover,
.custom-popup ul.vertical li.car.active {
  background: url('../images/sprite-icon.png') no-repeat center -60px;
}
.custom-popup ul.vertical li.bus {
  background: url('../images/sprite-icon.png') no-repeat center -80px;
}
.custom-popup ul.vertical li.bus:hover,
.custom-popup ul.vertical li.bus.active {
  background: url('../images/sprite-icon.png') no-repeat center -100px;
}
.custom-popup ul.vertical li.walk {
  background: url('../images/sprite-icon.png') no-repeat center -120px;
}
.custom-popup ul.vertical li.walk:hover,
.custom-popup ul.vertical li.walk.active {
  background: url('../images/sprite-icon.png') no-repeat center -140px;
}
.custom-popup ul.vertical li.bicycle {
  background: url('../images/sprite-icon.png') no-repeat center -160px;
}
.custom-popup ul.vertical li.bicycle:hover,
.custom-popup ul.vertical li.bicycle.active {
  background: url('../images/sprite-icon.png') no-repeat center -180px;
}
.custom-popup .directions-tool {
  margin-bottom: 10px;
  overflow: auto;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle {
  visibility: visible ;
  position: absolute;
  height: 60px;
  width: 15px;
  padding: 8px 0 0 0;
  z-index: 4;
  transition: background-color 200ms cubic-bezier(0.52, 0, 0.48, 1);
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon {
  background: transparent url('../images/omnibox-sprite.png') no-repeat -2px -31px;
  background-size: 20px 100px;
  width: 16px;
  height: 17px;
  text-indent: -10000px;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon.waypoint-handle {
  background-position: 0 2px;
  width: 16px;
  height: 13px;
}
.custom-popup .directions-tool .widget-directions-searchbox-handle .widget-directions-icon.waypoint-bullet {
  background-position: 0 -12px;
  width: 11px;
  height: 20px;
  margin-top: 3px;
}
.custom-popup .directions-tool .form-inputs {
  padding-left: 22px;
}
.custom-popup .directions-tool .form-inputs input {
  height: 34px;
  width: 95%;
  border: none;
  box-shadow: none;
  border-radius: 0;
  outline: 0;
}
.custom-popup .directions-tool .form-inputs input.start {
  border-top: 1px solid #ccc;
}
.custom-popup .directions-tool .form-inputs input.end {
  border-top: 1px solid #ccc;
  border-bottom: 1px solid #ccc;
}
.custom-popup .directions-tool .widget-directions-right-overlay {
  position: absolute;
  z-index: 3;
  top: 4px;
  bottom: 0;
  right: 0;
  width: 30px;
  border: 0;
  opacity: 1;
  transition: opacity 200ms cubic-bezier(0.52, 0, 0.48, 1);
}
.custom-popup .directions-tool .widget-directions-right-overlay button {
  text-indent: -10000px;
  background: transparent url('../images/omnibox-sprite.png') no-repeat center -64px;
  width: 20px;
  border: none;
  box-shadow: none;
  margin-top: 20px;
  outline: 0;
  opacity: 0.5;
  padding: 0;
}
.custom-popup .directions-tool .widget-directions-right-overlay button:hover {
  opacity: 1;
}
.custom-popup .directions-tool .directions-panel {
  max-height: 300px;
}
.table-wrap .table > tbody > tr > td,
.table-wrap .table > tbody > tr > th,
.table-wrap .table > tfoot > tr > td,
.table-wrap .table > tfoot > tr > th,
.table-wrap .table > thead > tr > td,
.table-wrap .table > thead > tr > th {
  padding: 8px;
  line-height: 1.42857143;
  vertical-align: top;
  border-top: 1px solid #ddd !important;
  text-align: center;
}
.col-full {
  float: left;
  width: 100%;
}
.disable-ul {
  padding: 0;
  margin: 0;
}
.location-box-view .widget-mylocation {
  width: 29px;
  height: 29px;
  cursor: pointer;
  position: relative;
  -moz-user-select: none;
  float: left;
  left: 10px;
}
.location-box-view .widget-mylocation .widget-mylocation-button {
  border-radius: 2px;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.3);
  display: block;
  width: 29px;
  height: 29px;
  overflow: hidden;
  cursor: pointer;
  padding: 0px;
  border: 0px none;
  outline: 0px none;
  font: inherit;
  vertical-align: baseline;
  background: none repeat scroll 0% 0% transparent;
  list-style: outside none none;
  background-color: #FFF;
}
.location-box-view .widget-mylocation .widget-mylocation-button:hover .widget-mylocation-cookieless {
  background-position: -36px 0px;
}
.location-box-view .widget-mylocation .widget-mylocation-button .widget-mylocation-cookieless {
  background: url('../images/geolocation_resize.png') no-repeat 0 0;
  background-attachment: scroll;
  display: block;
  height: 18px;
  left: 6px;
  margin: 0px;
  padding: 0px;
  position: absolute;
  top: 6px;
  width: 18px;
}
@media screen and (min-width: 768px) and (max-width: 992px) {
  .views-wrapper .table-wrap > .tab_content[class*=col-] {
    padding: 0px 15px 0px 15px ;
  }
}
@media screen and (max-width: 768px) {
  .open_hour .table-responsive {
    border: none;
  }
  .map-box-container,
  .form-information {
    padding: 0;
  }
}
