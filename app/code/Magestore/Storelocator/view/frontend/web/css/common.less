/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
.page-footer {
    clear: both;
}
.magestore-resetcss {
    button {
        background-image: none;
        background: #eee;
        border: 1px solid #ccc;
        color: #333;
        cursor: pointer;
        display: inline-block;
        font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-weight: 600;
        margin: 0;
        padding: 7px 15px;
        font-size: 1.4rem;
        line-height: 1.6rem;
        box-sizing: border-box;
        vertical-align: middle;
    }
    .storelocator-wrapper button,
    a.action.primary {
        border-radius: 3px;
    }
    .action.primary:hover {
        background: #006bb4;
        border: 1px solid #006bb4;
        color: #fff;
    }
    .action.primary {
        background-image: none;
        background: #1979c3;
        border: 1px solid #1979c3;
        color: #fff;
        cursor: pointer;
        display: inline-block;
        font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-weight: 600;
        padding: 7px 15px;
        font-size: 1.4rem;
        box-sizing: border-box;
        vertical-align: middle;
    }
    button:hover {
        background: #e1e1e1;
        border: 1px solid #ccc;
        color: #333;
    }
    input[type="text"],
    input[type="password"],
    input[type="url"],
    input[type="tel"],
    input[type="search"],
    input[type="number"],
    input[type="datetime"],
    input[type="email"] {
        background: #fff;
        background-clip: padding-box;
        border: 1px solid #c2c2c2;
        border-radius: 1px;
        font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-size: 14px;
        height: 32px;
        line-height: 1.42857143;
        padding: 0 9px;
        vertical-align: baseline;
        width: 100%;
        box-sizing: border-box;
    }

    ._keyfocus :focus, input:focus:not([disabled]), textarea:focus:not([disabled]), select:focus:not([disabled]) {
        box-shadow: 0 0 3px 1px #68a8e0;
    }

    select {
        background: #fff;
        background-size: 30px 60px;
        background-clip: padding-box;
        border: 1px solid #c2c2c2;
        border-radius: 1px;
        font-family: 'Open Sans', 'Helvetica Neue', Helvetica, Arial, sans-serif;
        font-size: 14px;
        height: 32px;
        line-height: 1.42857143;
        padding: 5px 10px 4px;
        vertical-align: baseline;
        width: 100%;
        box-sizing: border-box;
        -webkit-appearance: none;
        -moz-appearance: none;
        -ms-appearance: none;
        appearance: none;
        text-indent: .01em;
        text-overflow: '';
    }
}

.map-box-container,
.storelocator-wrapper {
    .magestore-resetcss;
}

.map-box-container,
.form-information{
    padding-right:0 ;
    margin-bottom: 15px;
}
.map-box-container {
    height: 606px;
    .googlemap {
        height: 100%;
    }
    h4 {
        margin: 5px 0 10px 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        width: 100%;
    }
}
h2 {
    &.title-page {
        color: #fff;
        font-size: 15px;
        text-transform: uppercase;
        font-weight: 600;
        padding: 8px 15px 8px 40px;
        background: #428BCA url('../images/shop.png') no-repeat 15px center;
        background-size: 18px;
        margin-bottom: 20px;
    }
}
.custom-popup{
    left: 0 !important;
    margin-top: 5px;
    .display{
        display: none;
        &.active{
            display: block;
        }
    }
    &.open{
        border: 1px solid #ececec;
    }
    ul{
        &.vertical {
            margin: 0;
            padding: 10px 0;
            border-top: 1px solid #ccc;
            li {
                display: inline-block;
                margin: 0;
                padding: 0;
                box-shadow: none;
                background-color: #ccc;
                width: 40px;
                float: none;
                height: 20px;
                border-bottom: none;
                cursor: pointer;
                span{
                    text-indent: -10000px;
                    padding: 0px 20px 5px 13px;
                    display: block;
                }
                span{
                    &:hover{
                        border-bottom: 2px solid #428bca;

                    }
                }
                &.car{
                    background: url('../images/sprite-icon.png') no-repeat center -40px;
                    &:hover, &.active{
                        background: url('../images/sprite-icon.png') no-repeat center -60px;
                    }
                }
                &.bus{
                    background: url('../images/sprite-icon.png') no-repeat center -80px;
                    &:hover, &.active{
                        background: url('../images/sprite-icon.png') no-repeat center -100px;
                    }
                }
                &.walk{
                    background: url('../images/sprite-icon.png') no-repeat center -120px;
                    &:hover,  &.active{
                        background: url('../images/sprite-icon.png') no-repeat center -140px;
                    }
                }
                &.bicycle{
                    background: url('../images/sprite-icon.png') no-repeat center -160px;
                    &:hover, &.active{
                        background: url('../images/sprite-icon.png') no-repeat center -180px;
                    }
                }
            }
        }
    }
    .directions-tool{
        margin-bottom: 10px;
        overflow: auto;
        .widget-directions-searchbox-handle {
            visibility: visible ;
            position: absolute;
            height: 60px;
            width: 15px;
            padding: 8px 0 0 0;
            z-index: 4;
            transition: background-color 200ms cubic-bezier(0.52,0,0.48,1);
            .widget-directions-icon {
                background: transparent url('../images/omnibox-sprite.png') no-repeat -2px -31px;
                background-size: 20px 100px;
                width: 16px;
                height: 17px;
                text-indent: -10000px;
                &.waypoint-handle {
                    background-position: 0 2px;
                    width: 16px;
                    height: 13px;
                }
                &.waypoint-bullet {
                    background-position: 0 -12px;
                    width: 11px;
                    height: 20px;
                    margin-top: 3px;
                }
            }
        }
        .form-inputs{
            padding-left: 22px;
            input{
                height: 34px;
                width: 95%;
                border: none;
                box-shadow: none;
                border-radius: 0;
                outline: 0;
                &.start{
                    border-top: 1px solid #ccc;
                }
                &.end{
                    border-top: 1px solid #ccc;
                    border-bottom: 1px solid #ccc;
                }
            }
        }
        .widget-directions-right-overlay{
            position: absolute;
            z-index: 3;
            top: 4px;
            bottom: 0;
            right: 0;
            width: 30px;
            border: 0;
            opacity: 1;
            transition: opacity 200ms cubic-bezier(0.52,0,0.48,1);
            button{
                text-indent: -10000px;
                background: transparent url('../images/omnibox-sprite.png') no-repeat center -64px;
                width: 20px;
                border: none;
                box-shadow: none;
                margin-top: 20px;
                outline: 0;
                opacity: 0.5;
                padding: 0;
                &:hover{
                    opacity: 1;
                }
            }
        }
        .directions-panel{
            max-height: 300px;
        }
    }
}

.table-wrap{
    .table > tbody > tr > td,
    .table > tbody > tr > th,
    .table > tfoot > tr > td,
    .table > tfoot > tr > th,
    .table > thead > tr > td,
    .table > thead > tr > th {
        padding: 8px;
        line-height: 1.42857143;
        vertical-align: top;
        border-top: 1px solid #ddd !important;
        text-align: center;
    }
}
// Custom other by class
.col-full {
    float: left;
    width: 100%;
}
.disable-ul {
    padding: 0;
    margin: 0
}
.location-box-view{
    .widget-mylocation {
        width: 29px;
        height: 29px;
        cursor: pointer;
        position: relative;
        -moz-user-select: none;
        float: left;
        left: 10px;
        .widget-mylocation-button{
            border-radius: 2px;
            box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.3);
            display: block;
            width: 29px;
            height: 29px;
            overflow: hidden;
            cursor: pointer;
            padding: 0px;
            border: 0px none;
            outline: 0px none;
            font: inherit;
            vertical-align: baseline;
            background: none repeat scroll 0% 0% transparent;
            list-style: outside none none;
            background-color: #FFF;
            &:hover {
                .widget-mylocation-cookieless {
                    background-position: -36px 0px;
                }
            }
            .widget-mylocation-cookieless{
                background: url('../images/geolocation_resize.png') no-repeat 0 0;
                background-attachment: scroll;
                display: block;
                height: 18px;
                left: 6px;
                margin: 0px;
                padding: 0px;
                position: absolute;
                top: 6px;
                width: 18px;
            }
        }
    }
}
@media screen and (max-width: 992px) {

}
@media screen and (min-width: 768px) and (max-width: 992px) {
    .views-wrapper
    {
        .table-wrap > .tab_content[class*=col-] {
            padding: 0px 15px 0px 15px ;
        }
    }
}
@media screen and (max-width: 768px) {
    .open_hour{
        .table-responsive{
            border: none;
        }
    }
    .map-box-container,
    .form-information{
        padding: 0;
    }
}
