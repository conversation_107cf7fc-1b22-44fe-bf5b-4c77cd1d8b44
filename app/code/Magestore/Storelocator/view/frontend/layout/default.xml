<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magestore
 * 
 * NOTICE OF LICENSE
 * 
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 * 
 * DISCLAIMER
 * 
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 * 
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="top.links">
            <block class="Magestore\Storelocator\Block\Link" name="storelocator-top-link" before="my-account-link">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Store Locator</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceBlock name="header.links">
            <block class="Magestore\Storelocator\Block\Link" name="storelocator-header-link">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Store Locator</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>