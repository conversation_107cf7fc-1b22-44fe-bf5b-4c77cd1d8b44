<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Magestore_Storelocator::css/bootstrap/bootstrap.css"/>
        <css src="Magestore_Storelocator::css/bootstrap/glyphicons.css"/>
        <css src="Magestore_Storelocator::css/viewpage.css"/>
    </head>
    <body>
        <referenceContainer name="content">
            <block class="Magestore\Storelocator\Block\Store\ViewPage" name="storelocator.viewpage" template="Magestore_Storelocator::viewpage.phtml" />
        </referenceContainer>
        <referenceBlock name="page.main.title" remove="true"/>
    </body>
</page>
