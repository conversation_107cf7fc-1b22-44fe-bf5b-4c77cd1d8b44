/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
var config = {
    map: {
        '*': {
            'magestore/googlemap' : 'Magestore_Storelocator/js/googlemap',
            'magestore/markerclusterer' : 'Magestore_Storelocator/js/markerclusterer',
            'magestore/pagination' : 'Magestore_Storelocator/js/pagination',
            'magestore/tag' : 'Magestore_Storelocator/js/tag',
            'magestore/liststore' : 'Magestore_Storelocator/js/liststore',
            'magestore/direction' : 'Magestore_Storelocator/js/direction',
            'magestore/searchbox' : 'Magestore_Storelocator/js/searchbox',
            'magestore/viewpage/map' : 'Magestore_Storelocator/js/viewpage/map'
        }
    },
    paths: {
    }
};
