<?php

/**
 * Magestore.
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

namespace Magestore\Storepickup\Model;

use Magento\Store\Model\StoreManagerInterface;
use Magento\UrlRewrite\Service\V1\Data\UrlRewriteFactory;

/**
 * @category Magestore
 * @package  Magestore_Storepickup
 * @module   Storepickup
 * <AUTHOR> Developer
 */
class StoreUrlRewriteGenerator implements StoreUrlRewriteGeneratorInterface
{
    /**
     * Entity type code
     */
    const ENTITY_TYPE = 'custom';

    /** @var \Magento\UrlRewrite\Service\V1\Data\UrlRewriteFactory */
    protected $_urlRewriteFactory;

    /**
     * @var StoreUrlPathGeneratorInterface
     */
    protected $_storeUrlPathGenerator;

    /**
     * Store manager
     *
     * @var StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * @var \Magestore\Storepickup\Model\Store
     */
    protected $_store;

    /**
     * StorePageUrlRewriteGenerator constructor.
     *
     * @param UrlRewriteFactory     $urlRewriteFactory
     * @param StoreUrlPathGeneratorInterface $storeUrlPathGenerator
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        UrlRewriteFactory $urlRewriteFactory,
        StoreUrlPathGeneratorInterface $storeUrlPathGenerator,
        StoreManagerInterface $storeManager
    ) {
        $this->_urlRewriteFactory = $urlRewriteFactory;
        $this->_storeManager = $storeManager;
        $this->_storeUrlPathGenerator = $storeUrlPathGenerator;
    }

    /**
     * @param \Magestore\Storepickup\Model\Store $store
     *
     * @return \Magento\UrlRewrite\Service\V1\Data\UrlRewrite[]
     */
    public function generate(\Magestore\Storepickup\Model\Store $store)
    {
        $this->_store = $store;
        $urls = $this->_generateForAllStores();
        $this->_store = null;

        return $urls;
    }

    /**
     * Generate list of urls for default store
     *
     * @return \Magento\UrlRewrite\Service\V1\Data\UrlRewrite[]
     */
    protected function _generateForAllStores()
    {
        $urls = [];
        foreach ($this->_storeManager->getStores() as $store) {
            $urls[] = $this->_createUrlRewrite($store->getStoreId());
        }

        return $urls;
    }

    /**
     * Create url rewrite object
     *
     * @param int $storeId
     * @param int $redirectType
     * @return \Magento\UrlRewrite\Service\V1\Data\UrlRewrite
     */
    protected function _createUrlRewrite($storeId, $redirectType = 0)
    {
        return $this->_urlRewriteFactory->create()->setStoreId($storeId)
            ->setEntityType(self::ENTITY_TYPE)
            ->setEntityId($this->_store->getId())
            ->setRequestPath($this->_store->getRewriteRequestPath())
            ->setTargetPath($this->_storeUrlPathGenerator->getCanonicalUrlPath($this->_store))
            ->setIsAutogenerated(1)
            ->setRedirectType($redirectType);
    }
}
