<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_StorePickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

namespace Magestore\Storepickup\Observer;

use Magento\Framework\Event\ObserverInterface;
use Symfony\Component\Config\Definition\Exception\Exception;

/**
 * Class GiftMessageConfigObserver
 *
 * @category Magestore
 * @package  Magestore_StorePickup
 * @module   StorePickup
 * <AUTHOR> Developer
 */
class AdminhtmlSaveStorepickupDecription implements ObserverInterface
{
    /**
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @codeCoverageIgnore
     */
    protected $_checkoutSession;

    /**
     * @var \Magestore\Storepickup\Model\StoreFactory
     */
    protected $_storeCollection;
    /**
     * @var \Magento\Sales\Api\Data\OrderAddressInterface
     */
    protected $_orderAddressInterface;
    /**
     * @var \Magestore\Storepickup\Helper\Data
     */
    protected $_storepickupHelper;
    /**
     * @var \Magestore\Storepickup\Helper\Email
     */
    protected $_storepickupHelperEmail;

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    private $totaltoolsConfig;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;
    /**
     * @var backendSession
     */
    private $_backendSession;

    /**
     * AdminhtmlSaveStorepickupDecription constructor.
     * @param \Magento\Backend\Model\Session $backendSession
     * @param \Magestore\Storepickup\Model\StoreFactory $storeCollection
     * @param \Magento\Sales\Api\Data\OrderAddressInterface $orderAddressInterface
     * @param \Magestore\Storepickup\Helper\Data $storepickupHelper
     * @param \Magestore\Storepickup\Helper\Email $storepickupHelperEmail
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Backend\Model\Session $backendSession,
        \Magestore\Storepickup\Model\StoreFactory $storeCollection,
        \Magento\Sales\Api\Data\OrderAddressInterface $orderAddressInterface,
        \Magestore\Storepickup\Helper\Data $storepickupHelper,
        \Magestore\Storepickup\Helper\Email $storepickupHelperEmail,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->_backendSession = $backendSession;
        $this->_storeCollection = $storeCollection;
        $this->_orderAddressInterface = $orderAddressInterface;
        $this->_storepickupHelper = $storepickupHelper;
        $this->_storepickupHelperEmail = $storepickupHelperEmail;
        $this->totaltoolsConfig = $totalToolsConfig;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return void
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        try {
            /** @var \Magento\Sales\Model\Order $order */
            $order = $observer->getEvent()->getOrder();
            // Workaround for ticket: TOT0017-1293
            // Check shipping carrier code
            $defaultCarrierCode = $this->totaltoolsConfig->getTotalToolsConfig('cc_carrier_code');
            if ($order->getShippingMethod(true)->getCarrierCode() === $defaultCarrierCode) {
                if ($this->_backendSession->getData('storepickup')) {
                    $new = $order->getShippingDescription();
                    $storepickup_session = $this->_backendSession->getData('storepickup', true);
                    $datashipping = array();
                    $storeId = $storepickup_session['store_id'];
                    $collectionstore = $this->_storeCollection->create();
                    $store = $collectionstore->load($storeId, 'storepickup_id');
                    //set Shipping Description
                    if (isset($storepickup_session['shipping_date']) && isset($storepickup_session['shipping_time'])) {
                        $new .= '<br>' . __('Pickup date') . ' : ' . $storepickup_session['shipping_date'] . '<br>' . __('Pickup time') . ' : ' . $storepickup_session['shipping_time'] . '<br><img src="http://maps.google.com/maps/api/staticmap?center=' . $store->getData('latitude') . ',' . $store->getData('longitude') . '&zoom=15&size=200x200&markers=color:red|label:S|' . $store->getData('latitude') . ',' . $store->getData('longitude') . '&sensor=false" />';
                    } else {
                        $new .= '<br><img src="http://maps.google.com/maps/api/staticmap?center=' . $store->getData('latitude') . ',' . $store->getData('longitude') . '&zoom=15&size=200x200&markers=color:red|label:S|' . $store->getData('latitude') . ',' . $store->getData('longitude') . '&sensor=false" />';
                    }
                    $order->setShippingDescription($new);
                    //set Shipping Address
                    $datashipping['firstname'] = __('Store 2');
                    $datashipping['lastname'] = $store->getData('store_name');
                    $datashipping['street'][0] = $store->getData('address');
                    $datashipping['city'] = $store->getCity();
                    $datashipping['region'] = $store->getState();
                    $datashipping['region_id'] = $store->getStateId();
                    $datashipping['postcode'] = $store->getData('zipcode');
                    $datashipping['country_id'] = $store->getData('country_id');
                    $datashipping['company'] = '';
                    if ($store->getFax()) {
                        $datashipping['fax'] = $store->getFax();
                    } else {
                        unset($datashipping['fax']);
                    }

                    if ($store->getPhone()) {
                        $datashipping['telephone'] = $store->getPhone();
                    } else {
                        unset($datashipping['telephone']);
                    }

                    $datashipping['save_in_address_book'] = 0;

                    $order->getShippingAddress()->addData($datashipping);
                    //$order->sendNewOrderEmail();
                    //$this->_storepickupHelperEmail->sendNoticeEmailToStoreOwner($order,$store);
                    //$this->_storepickupHelperEmail->sendNoticeEmailToAdmin($order,$store);

                    $billingAddress = $order->getBillingAddress();
                    $shippingMethod = $order->getShippingMethod();

                    if (strpos(strtolower($billingAddress->getFirstname()), 'store') !== false) {
                        $this->logger->critical(
                            'Store as billing address detected for order #' . $order->getIncrementId() . ' with shipping method: ' . $shippingMethod,
                            ['backtrace' => \Magento\Framework\Debug::backtrace(true, false)]
                        );
                    }
                }
            }
        } catch (Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
