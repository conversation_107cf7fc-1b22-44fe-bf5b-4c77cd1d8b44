/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
var config = {
    map: {
        '*': {
            'Magestore_Storepickup/js/store/map-loader' : 'Magestore_Storepickup/js/store/map/map-loader',
            'magestore/region-updater' : 'Magestore_Storepickup/js/store/region-updater',
        }
    },
    paths: {
    },
};