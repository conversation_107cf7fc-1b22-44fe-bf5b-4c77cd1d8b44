<?php
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
// DISABLE function escapeHTML()e

?>
<form id="edit_form" method="post" action="<?php /* @escapeNotVerified */ echo $block->getSaveUrl() ?>">
    <?php echo $block->getBlockHtml('formkey')?>
    <?php  $_order = $block->getShipment()->getOrder() ?>
    <?php echo $block->getChildHtml('order_info') ?>
    <div class="admin__page-section">
        <div class="admin__page-section-title">
            <span class="title"><?php /* @escapeNotVerified */ echo __('Payment &amp; Shipping Method') ?></span>
        </div>
        <div class="admin__page-section-content">
            <div class="admin__page-section-item order-payment-method">
                <?php /* Billing Address */ ?>
                <div class="admin__page-section-item-title">
                    <span class="title"><?php /* @escapeNotVerified */ echo __('Payment Information') ?></span>
                </div>
                <div class="admin__page-section-item-content">
                    <div><?php echo $block->getPaymentHtml() ?></div>
                    <div class="order-payment-currency"><?php /* @escapeNotVerified */ echo __('The order was placed using %1.', $_order->getOrderCurrencyCode()) ?></div>
                </div>
            </div>
            <div class="admin__page-section-item order-shipping-address">
                <?php /* Shipping Address */ ?>
                <div class="admin__page-section-item-title">
                    <span class="title"><?php /* @escapeNotVerified */ echo __('Shipping Information') ?></span>
                </div>
                <div class="admin__page-section-item-content shipping-description-wrapper">
                    <div class="shipping-description-title"><?php echo $_order->getShippingDescription() ?></div>
                    <div class="shipping-description-content">
                        <?php /* @escapeNotVerified */ echo __('Total Shipping Charges'); ?>:

                        <?php if ($this->helper('Magento\Tax\Helper\Data')->displayShippingPriceIncludingTax()): ?>
                            <?php $_excl = $block->displayShippingPriceInclTax($_order); ?>
                        <?php else: ?>
                            <?php $_excl = $block->displayPriceAttribute('shipping_amount', false, ' '); ?>
                        <?php endif; ?>
                        <?php $_incl = $block->displayShippingPriceInclTax($_order); ?>
                        <?php /* @escapeNotVerified */ echo $_excl; ?>
                        <?php if ($this->helper('Magento\Tax\Helper\Data')->displayShippingBothPrices() && $_incl != $_excl): ?>
                        (<?php /* @escapeNotVerified */ echo __('Incl. Tax'); ?> <?php /* @escapeNotVerified */ echo $_incl; ?>)
                        <?php endif; ?>
                    </div>
                </div>
                <div><?php echo $block->getChildHtml('shipment_tracking') ?></div>
            </div>
        </div>
    </div>
    <div id="ship_items_container">
        <?php echo $block->getItemsHtml() ?>
    </div>
</form>
<script>
require([
    "jquery",
    "mage/mage",
    "prototype"
], function(jQuery){
    jQuery('#edit_form').mage('form').mage('validation');

});
</script>
<?php echo $block->getChildHtml('shipment_packaging');
