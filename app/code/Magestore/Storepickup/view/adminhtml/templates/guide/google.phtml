<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

?>
<div class="fieldset ">
    <?php echo '1. Visit the APIs Console at'?> <a href="https://console.developers.google.com/apis/library" target="_blank"><?php echo 'https://console.developers.google.com/apis/library' ?></a><?php echo ' and log in with your Google Account.' ?>
    <br />
    <?php echo '2. Click on the <strong>Create project</strong> tab in the top menu.' ?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/guide/google-create-project.png') ?>" title="<?php echo __('Google API Active') ?>" />
    <br />
    <?php echo '3. Fill project name and Create' ?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/guide/fill-name.png') ?>" title="<?php echo __('Google API Active') ?>" />
    <br />
    <br />
    <?php echo '4. Click on the <strong>Credentials</strong> tab in the left menu,  the <strong>"API key"</strong>  in the "Credentials" box to create Google Map API Key for browser apps.' ?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/guide/choose-api.png') ?>" title="<?php echo __('Google API Active') ?>" />
    <br />
    <br />
    <?php echo "Choose <strong>Browser key</strong> "?>
    <br />
    <br />
    <img src="<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/guide/choose-browser.png') ?>" title="<?php echo __('Google API Active') ?>" />
    <br />
    <br />
    <?php echo "5. Fill name and <strong>Your domain</strong>"?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/guide/fill-domain.png') ?>" title="<?php echo __('Google API Active') ?>" />
    <br />
    <br />
    <?php echo "6. Get the key and paste it in the setting page."?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/guide/get-key.png') ?>" title="<?php echo __('Google API Active') ?>" />
    <br />
    <?php echo "7. Last step, enable some APIs in <strong>Library</strong> tab"?>
    <br />
    <br />
    <img width="80%" src="<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/guide/enable-apis1.png') ?>" title="<?php echo __('Google API Active') ?>" />
    <br />
</div>