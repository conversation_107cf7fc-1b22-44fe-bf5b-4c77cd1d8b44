<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<form id="edit_form" method="post" action="<?php /* @escapeNotVerified */ echo $block->getSaveUrl() ?>">
    <?php echo $block->getBlockHtml('formkey')?>
    <?php  $_order = $block->getCreditmemo()->getOrder() ?>

    <?php echo $block->getChildHtml('order_info') ?>

    <section class="admin__page-section">
        <div class="admin__page-section-title">
            <span class="title"><?php /* @escapeNotVerified */ echo __('Payment &amp; Shipping Method') ?></span>
        </div>
        <div class="admin__page-section-content">
            <?php if (!$_order->getIsVirtual()): ?>
            <div class="admin__page-section-item order-payment-method">
                <?php else: ?>
                <div class="admin__page-section-item order-payment-method order-payment-method-virtual">
                    <?php endif; ?>

                    <?php /* Billing Address */ ?>
                    <div class="admin__page-section-item-title">
                        <span class="title"><?php /* @escapeNotVerified */ echo __('Payment Information') ?></span>
                    </div>
                    <div class="admin__page-section-item-content">
                        <div class="order-payment-method-title"><?php echo $block->getChildHtml('order_payment') ?></div>
                        <div class="order-payment-currency">
                            <?php /* @escapeNotVerified */ echo __('The order was placed using %1.', $_order->getOrderCurrencyCode()) ?>
                        </div>
                        <div class="order-payment-additional">
                            <?php echo $block->getChildHtml('order_payment_additional'); ?>
                        </div>
                    </div>
                </div>

                <?php if (!$_order->getIsVirtual()): ?>
                    <div class="admin__page-section-item order-shipping-address">
                        <?php /* Shipping Address */ ?>
                        <div class="admin__page-section-item-title">
                            <span class="title"><?php /* @escapeNotVerified */ echo __('Shipping Information') ?></span>
                        </div>
                        <div class="admin__page-section-item-content shipping-description-wrapper">
                            <div class="shipping-description-title"><?php echo $_order->getShippingDescription() ?></div>
                            <div class="shipping-description-content">
                                <?php /* @escapeNotVerified */ echo __('Total Shipping Charges'); ?>:

                                <?php if ($this->helper('Magento\Tax\Helper\Data')->displaySalesPriceInclTax($block->getSource()->getStoreId())): ?>
                                    <?php $_excl = $block->displayShippingPriceInclTax($_order); ?>
                                <?php else: ?>
                                    <?php $_excl = $block->displayPriceAttribute('shipping_amount', false, ' '); ?>
                                <?php endif; ?>
                                <?php $_incl = $block->displayShippingPriceInclTax($_order); ?>

                                <?php /* @escapeNotVerified */ echo $_excl; ?>
                                <?php if ($this->helper('Magento\Tax\Helper\Data')->displaySalesBothPrices($block->getSource()->getStoreId()) && $_incl != $_excl): ?>
                                    (<?php /* @escapeNotVerified */ echo __('Incl. Tax'); ?> <?php /* @escapeNotVerified */ echo $_incl; ?>)
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
    </section>

    <div id="creditmemo_item_container">
        <?php echo $block->getChildHtml('order_items') ?>
    </div>
</form>
