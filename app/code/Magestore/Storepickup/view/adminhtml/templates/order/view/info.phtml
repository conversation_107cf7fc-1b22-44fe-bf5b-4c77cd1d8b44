<?php
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
// DISABLE function escapeHTML() online 37
?>
<?php /** @var $block \Magento\Shipping\Block\Adminhtml\View */ ?>
<?php $order = $block->getOrder() ?>
<?php if ($order->getIsVirtual()) : return '';endif; ?>

<?php /* Shipping Method */ ?>
<div class="admin__page-section-item order-shipping-method">
    <div class="admin__page-section-item-title">
        <span class="title"><?php /* @escapeNotVerified */ echo __('Shipping &amp; Handling Information') ?></span>
    </div>
    <div class="admin__page-section-item-content">
        <?php  if ($order->getTracksCollection()->count()) : ?>
            <p><a href="#" id="linkId" onclick="popWin('<?php /* @escapeNotVerified */ echo $this->helper('Magento\Shipping\Helper\Data')->getTrackingPopupUrlBySalesModel($order) ?>','trackorder','width=800,height=600,resizable=yes,scrollbars=yes')" title="<?php /* @escapeNotVerified */ echo __('Track Order') ?>"><?php /* @escapeNotVerified */ echo __('Track Order') ?></a></p>
        <?php endif; ?>
        <?php if ($order->getShippingDescription()): ?>
            <strong><?php echo $order->getShippingDescription() ?></strong>
            <?php if ($this->helper('Magento\Tax\Helper\Data')->displayShippingPriceIncludingTax()): ?>
                <?php $_excl = $block->displayShippingPriceInclTax($order); ?>
            <?php else: ?>
                <?php $_excl = $block->displayPriceAttribute('shipping_amount', false, ' '); ?>
            <?php endif; ?>
            <?php $_incl = $block->displayShippingPriceInclTax($order); ?>

            <?php /* @escapeNotVerified */ echo $_excl; ?>
            <?php if ($this->helper('Magento\Tax\Helper\Data')->displayShippingBothPrices() && $_incl != $_excl): ?>
                (<?php /* @escapeNotVerified */ echo __('Incl. Tax'); ?> <?php /* @escapeNotVerified */ echo $_incl; ?>)
            <?php endif; ?>
        <?php else: ?>
            <?php /* @escapeNotVerified */ echo __('No shipping information available'); ?>
        <?php endif; ?>
    </div>
</div>
