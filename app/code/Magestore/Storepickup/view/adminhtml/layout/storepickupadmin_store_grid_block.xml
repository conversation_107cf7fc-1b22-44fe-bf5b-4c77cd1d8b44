<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="storepickupadmin_store.grid.container">
            <block class="Magestore\Storepickup\Block\Adminhtml\Store\Grid" name="storepickupadmin.store.grid" as="grid">
                <arguments>
                    <argument name="id" xsi:type="string">storepickupadmin_store_grid</argument>
                    <argument name="dataSource" xsi:type="object">Magestore\Storepickup\Model\ResourceModel\Store\Collection</argument>
                    <argument name="use_ajax" xsi:type="string">true</argument>
                    <argument name="default_sort" xsi:type="string">storepickup_id</argument>
                    <argument name="default_dir" xsi:type="string">ASC</argument>
                    <argument name="save_parameters_in_session" xsi:type="string">1</argument>
                    <argument name="grid_url" xsi:type="url" path="*/*/grid">
                        <param name="_current">1</param>
                    </argument>
                </arguments>
                <block class="Magento\Backend\Block\Widget\Grid\Massaction"
                       name="storepickupadmin.store.grid.massaction" as="grid.massaction">
                    <arguments>
                        <argument name="massaction_id_field" xsi:type="string">entity_id</argument>
                        <argument name="form_field_name" xsi:type="string">store</argument>
                        <argument name="use_select_all" xsi:type="string">0</argument>
                        <argument name="options" xsi:type="array">
                            <item name="enable" xsi:type="array">
                                <item name="label" xsi:type="string" translate="true">Enable</item>
                                <item name="url" xsi:type="string">storepickupadmin/store/massEnable</item>
                            </item>
                            <item name="disable" xsi:type="array">
                                <item name="label" xsi:type="string" translate="true">Disable</item>
                                <item name="url" xsi:type="string">storepickupadmin/store/massDisable</item>
                            </item>
                            <item name="delete" xsi:type="array">
                                <item name="label" xsi:type="string" translate="true">Delete</item>
                                <item name="url" xsi:type="string">storepickupadmin/store/massDelete</item>
                            </item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magento\Backend\Block\Widget\Grid\Export" name="storepickupadmin.store.grid.export"
                       as="grid.export">
                    <arguments>
                        <argument name="exportTypes" xsi:type="array">
                            <item name="csv" xsi:type="array">
                                <item name="urlPath" xsi:type="string">storepickupadmin/*/exportCsv</item>
                                <item name="label" xsi:type="string" translate="true">CSV</item>
                            </item>
                            <item name="excel" xsi:type="array">
                                <item name="urlPath" xsi:type="string">storepickupadmin/*/exportExcel</item>
                                <item name="label" xsi:type="string" translate="true">Excel XML</item>
                            </item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magento\Backend\Block\Widget\Grid\ColumnSet" as="grid.columnSet"
                       name="storepickupadmin.store.grid.columnSet">
                    <arguments>
                        <argument name="id" xsi:type="string">storepickupadmin_store_grid</argument>
                        <argument name="rowUrl" xsi:type="array">
                            <item name="path" xsi:type="string">storepickupadmin/store/edit</item>
                            <item name="extraParamsTemplate" xsi:type="array">
                                <item name="storepickup_id" xsi:type="string">getId</item>
                            </item>
                        </argument>
                    </arguments>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="storepickup_id">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Store ID</argument>
                            <argument name="type" xsi:type="string">number</argument>
                            <argument name="index" xsi:type="string">storepickup_id</argument>
                            <argument name="id" xsi:type="string">storepickup_id</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="store_name">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Store Name</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">store_name</argument>
                            <argument name="id" xsi:type="string">store_name</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="address">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Address</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">address</argument>
                            <argument name="id" xsi:type="string">address</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="city">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">City</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">city</argument>
                            <argument name="id" xsi:type="string">city</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="state">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">State</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">state</argument>
                            <argument name="id" xsi:type="string">state</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="country_id">
                        <arguments>
                            <argument name="id" xsi:type="string">country_id</argument>
                            <argument name="header" xsi:type="string" translate="true">Country</argument>
                            <argument name="type" xsi:type="string">options</argument>
                            <argument name="index" xsi:type="string">country_id</argument>
                            <argument name="options" xsi:type="options"
                                      model="Magestore\Storepickup\Model\Store\Option\Country"/>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="zipcode">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Zip Code</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">zipcode</argument>
                            <argument name="id" xsi:type="string">zipcode</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="latitude">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Latitude</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">latitude</argument>
                            <argument name="id" xsi:type="string">latitude</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="longitude">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Longitude</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">longitude</argument>
                            <argument name="id" xsi:type="string">longitude</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="status">
                        <arguments>
                            <argument name="id" xsi:type="string">status</argument>
                            <argument name="header" xsi:type="string" translate="true">Status</argument>
                            <argument name="type" xsi:type="string">options</argument>
                            <argument name="index" xsi:type="string">status</argument>
                            <argument name="options" xsi:type="options"
                                      model="Magestore\Storepickup\Model\ResourceModel\Store\Grid\StatusesArray"/>
                            <argument name="header_css_class" xsi:type="string">col-status</argument>
                            <argument name="column_css_class" xsi:type="string">col-status</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="action"
                           acl="Magento_Storepickup::store_edit">
                        <arguments>
                            <argument name="id" xsi:type="string">action</argument>
                            <argument name="header" xsi:type="string" translate="true">Action</argument>
                            <argument name="type" xsi:type="string">action</argument>
                            <argument name="getter" xsi:type="string">getId</argument>
                            <argument name="filter" xsi:type="string">0</argument>
                            <argument name="sortable" xsi:type="string">0</argument>
                            <argument name="index" xsi:type="string">storepickup_id</argument>
                            <argument name="is_system" xsi:type="string">1</argument>
                            <argument name="actions" xsi:type="array">
                                <item name="edit" xsi:type="array">
                                    <item name="caption" xsi:type="string" translate="true">Edit</item>
                                    <item name="url" xsi:type="array">
                                        <item name="base" xsi:type="string">storepickupadmin/store/edit</item>
                                    </item>
                                    <item name="field" xsi:type="string">storepickup_id</item>
                                </item>
                            </argument>
                            <argument name="header_css_class" xsi:type="string">col-actions</argument>
                            <argument name="column_css_class" xsi:type="string">col-actions</argument>
                        </arguments>
                    </block>
                </block>
            </block>
        </referenceBlock>
    </body>
</page>
