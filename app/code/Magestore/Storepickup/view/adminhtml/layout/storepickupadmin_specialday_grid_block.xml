<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="storepickupadmin_specialday.grid.container">
            <block class="Magestore\Storepickup\Block\Adminhtml\Widget\Grid" name="storepickupadmin.specialday.grid" as="grid">
                <arguments>
                    <argument name="id" xsi:type="string">storepickupadmin_specialday_grid</argument>
                    <argument name="dataSource" xsi:type="object">Magestore\Storepickup\Model\ResourceModel\Specialday\Grid\Collection</argument>
                    <argument name="use_ajax" xsi:type="string">true</argument>
                    <argument name="default_sort" xsi:type="string">specialday_id</argument>
                    <argument name="default_dir" xsi:type="string">ASC</argument>
                    <argument name="save_parameters_in_session" xsi:type="string">1</argument>
                    <argument name="grid_url" xsi:type="url" path="*/*/grid">
                        <param name="_current">1</param>
                    </argument>
                </arguments>
                <block class="Magento\Backend\Block\Widget\Grid\Massaction"
                       name="storepickupadmin.specialday.grid.massaction" as="grid.massaction">
                    <arguments>
                        <argument name="massaction_id_field" xsi:type="string">entity_id</argument>
                        <argument name="form_field_name" xsi:type="string">specialday</argument>
                        <argument name="use_select_all" xsi:type="string">0</argument>
                        <argument name="options" xsi:type="array">
                            <item name="delete" xsi:type="array">
                                <item name="label" xsi:type="string" translate="true">Delete</item>
                                <item name="url" xsi:type="string">storepickupadmin/specialday/massDelete</item>
                            </item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magento\Backend\Block\Widget\Grid\Export" name="storepickupadmin.specialday.grid.export"
                       as="grid.export">
                    <arguments>
                        <argument name="exportTypes" xsi:type="array">
                            <item name="csv" xsi:type="array">
                                <item name="urlPath" xsi:type="string">storepickupadmin/*/exportCsv</item>
                                <item name="label" xsi:type="string" translate="true">CSV</item>
                            </item>
                            <item name="excel" xsi:type="array">
                                <item name="urlPath" xsi:type="string">storepickupadmin/*/exportExcel</item>
                                <item name="label" xsi:type="string" translate="true">Excel XML</item>
                            </item>
                        </argument>
                    </arguments>
                </block>
                <block class="Magento\Backend\Block\Widget\Grid\ColumnSet" as="grid.columnSet"
                       name="storepickupadmin.specialday.grid.columnSet">
                    <arguments>
                        <argument name="id" xsi:type="string">storepickupadmin_specialday_grid</argument>
                        <argument name="rowUrl" xsi:type="array">
                            <item name="path" xsi:type="string">storepickupadmin/specialday/edit</item>
                            <item name="extraParamsTemplate" xsi:type="array">
                                <item name="specialday_id" xsi:type="string">getId</item>
                            </item>
                        </argument>
                    </arguments>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="specialday_id">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Special day ID</argument>
                            <argument name="type" xsi:type="string">number</argument>
                            <argument name="index" xsi:type="string">specialday_id</argument>
                            <argument name="id" xsi:type="string">specialday_id</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="specialday_name">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Special day Name</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">specialday_name</argument>
                            <argument name="id" xsi:type="string">specialday_name</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="date_from">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Date Start</argument>
                            <argument name="type" xsi:type="string">date</argument>
                            <argument name="index" xsi:type="string">date_from</argument>
                            <argument name="id" xsi:type="string">date_from</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="date_to">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Date End</argument>
                            <argument name="type" xsi:type="string">date</argument>
                            <argument name="index" xsi:type="string">date_to</argument>
                            <argument name="id" xsi:type="string">date_to</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="time_open">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Open Time</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">time_open</argument>
                            <argument name="id" xsi:type="string">time_open</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="time_close">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Close Time</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">time_close</argument>
                            <argument name="id" xsi:type="string">time_close</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="specialday_comment">
                        <arguments>
                            <argument name="header" xsi:type="string" translate="true">Comment</argument>
                            <argument name="type" xsi:type="string">text</argument>
                            <argument name="index" xsi:type="string">specialday_comment</argument>
                            <argument name="id" xsi:type="string">specialday_comment</argument>
                        </arguments>
                    </block>
                    <block class="Magento\Backend\Block\Widget\Grid\Column" as="action"
                           acl="Magento_Storepickup::specialday_edit">
                        <arguments>
                            <argument name="id" xsi:type="string">action</argument>
                            <argument name="header" xsi:type="string" translate="true">Action</argument>
                            <argument name="type" xsi:type="string">action</argument>
                            <argument name="getter" xsi:type="string">getId</argument>
                            <argument name="filter" xsi:type="string">0</argument>
                            <argument name="sortable" xsi:type="string">0</argument>
                            <argument name="index" xsi:type="string">specialday_id</argument>
                            <argument name="is_system" xsi:type="string">1</argument>
                            <argument name="actions" xsi:type="array">
                                <item name="edit" xsi:type="array">
                                    <item name="caption" xsi:type="string" translate="true">Edit</item>
                                    <item name="url" xsi:type="array">
                                        <item name="base" xsi:type="string">storepickupadmin/specialday/edit</item>
                                    </item>
                                    <item name="field" xsi:type="string">specialday_id</item>
                                </item>
                            </argument>
                            <argument name="header_css_class" xsi:type="string">col-actions</argument>
                            <argument name="column_css_class" xsi:type="string">col-actions</argument>
                        </arguments>
                    </block>
                </block>
            </block>
        </referenceBlock>
    </body>
</page>
