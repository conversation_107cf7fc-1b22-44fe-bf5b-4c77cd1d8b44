/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
var config = {
    map: {
        '*': {
            'magestore/weekdaytime' : 'Magestore_Storepickup/js/weekdaytime',
            'magestore/utilities' : 'Magestore_Storepickup/js/utilities',
            'magestore/schedule' : 'Magestore_Storepickup/js/form/schedule',
            'magestore/specialday' : 'Magestore_Storepickup/js/form/specialday',
            'magestore/map': 'Magestore_Storepickup/js/store/map',
            'magestore/baseimage': 'Magestore_Storepickup/js/gallery/base-image-uploader',
        }
    },
    paths: {
    },
};
