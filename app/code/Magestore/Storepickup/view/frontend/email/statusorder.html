<!--@subject {{var store.getFrontendName()}}: New Order # {{var order.increment_id}} @-->
<!--@vars
{"store url=\"\"":"Store Url",
"skin url=\"images/logo_email.gif\" _area='frontend'":"Email Logo Image",
"htmlescape var=$order.getCustomerName()":"Customer Name",
"var store.getFrontendName()":"Store Name",
"store url=\"customer/account/\"":"Customer Account Url",
"var order.increment_id":"Order Id",
"var order.getCreatedAtFormated('long')":"Order Created At (datetime)",
"var order.getBillingAddress().format('html')":"Billing Address",
"var payment_html":"Payment Details",
"var order.getShippingAddress().format('html')":"Shipping Address",
"var order.getShippingDescription()":"Shipping Description",
"layout handle=\"sales_email_order_items\" order=$order":"Order Items Grid",
"var order.getEmailCustomerNote()":"Email Order Note"}
@-->
<!--@styles
body,td { color:#2f2f2f; font:11px/1.35em Verdana, Arial, Helvetica, sans-serif; }
@-->

<div style="font:11px/1.35em Verdana, Arial, Helvetica, sans-serif;">
<table cellspacing="0" cellpadding="0" border="0" width="98%" style="margin-top:10px; font:11px/1.35em Verdana, Arial, Helvetica, sans-serif; margin-bottom:10px;">
<tr>
    <td align="center" valign="top">
        <!-- [ header starts here] -->
        <table cellspacing="0" cellpadding="0" border="0" width="650">
            <tr>
                <td valign="top"><a href="{{store url=""}}"><img src="{{skin url="images/logo_email.gif" _area='frontend'}}" alt="{{var store.getFrontendName()}}"  style="margin-bottom:10px;" border="0"/></a></td>
            </tr>
        </table>
        <!-- [ middle starts here] -->
        <table cellspacing="0" cellpadding="0" border="0" width="650">
            <tr>
                <td valign="top">
                    <p>
                        <strong>Hello {{htmlescape var=$order.getStoreOwnerName()}}</strong>,<br/>
                       There has been a customer placing an order on Main Store and selecting your store location to pick up product. That order's status has been changed. 
					   You can check it by <a href="{{store url="adminhtml/index/"}}" style="color:#1E7EC8;">logging into back-end</a>.
                    </p>
                    <p>The order information is below.</p>

                    <h3 style="border-bottom:2px solid #eee; font-size:1.05em; padding-bottom:1px; ">The Order #{{var order.increment_id}} <small>(placed on {{var order.getCreatedAtFormated('long')}})</small></h3>
                    <table cellspacing="0" cellpadding="0" border="0" width="100%">
                        <thead>
                        <tr>
                            <th align="left" width="48.5%" bgcolor="#d9e5ee" style="padding:5px 9px 6px 9px; border:1px solid #bebcb7; border-bottom:none; line-height:1em;">Billing Information:</th>
                            <th width="3%"></th>
                            <th align="left" width="48.5%" bgcolor="#d9e5ee" style="padding:5px 9px 6px 9px; border:1px solid #bebcb7; border-bottom:none; line-height:1em;">Payment Method:</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td valign="top" style="padding:7px 9px 9px 9px; border:1px solid #bebcb7; border-top:0; background:#f8f7f5;">
                                {{var order.getBillingAddress().format('html')}}<br/>
                                Order Status: {{var order.getStatus()}}
                            </td>
                            <td>&nbsp;</td>
                            <td valign="top" style="padding:7px 9px 9px 9px; border:1px solid #bebcb7; border-top:0; background:#f8f7f5;">
                                {{var payment_html}}
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <br/>
                    {{depend order.getIsNotVirtual()}}
                    <table cellspacing="0" cellpadding="0" border="0" width="100%">
                        <thead>
                        <tr>
                            <th align="left" width="48.5%" bgcolor="#d9e5ee" style="padding:5px 9px 6px 9px; border:1px solid #bebcb7; border-bottom:none; line-height:1em;">Shipping Information:</th>
                            <th width="3%"></th>
                            <th align="left" width="48.5%" bgcolor="#d9e5ee" style="padding:5px 9px 6px 9px; border:1px solid #bebcb7; border-bottom:none; line-height:1em;">Shipping Method:</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td valign="top" style="padding:7px 9px 9px 9px; border:1px solid #bebcb7; border-top:0; background:#f8f7f5;">
                                {{var order.getShippingAddress().format('html')}}
                                &nbsp;
                            </td>
                            <td>&nbsp;</td>
                            <td valign="top" style="padding:7px 9px 9px 9px; border:1px solid #bebcb7; border-top:0; background:#f8f7f5;">
                                {{var order.getShippingDescription()}}
                                &nbsp;
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <br/>
                    {{/depend}}
                    {{layout handle="sales_email_order_items" order=$order}}
                </td>
            </tr>
        </table>
    </td>
</tr>
</table>
</div>