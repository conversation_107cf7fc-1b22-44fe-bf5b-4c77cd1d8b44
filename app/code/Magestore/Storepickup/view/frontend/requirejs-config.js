/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
var config = {
    map: {
        '*': {
            'magestore/googlemap': 'Magestore_Storepickup/js/googlemap',
            'magestore/pagination': 'Magestore_Storepickup/js/pagination',
            'magestore/tag': 'Magestore_Storepickup/js/tag',
            'magestore/liststore': 'Magestore_Storepickup/js/liststore',
            'magestore/direction': 'Magestore_Storepickup/js/direction',
            'magestore/searchbox': 'Magestore_Storepickup/js/searchbox',
            'magestore/viewpage/map': 'Magestore_Storepickup/js/viewpage/map'
        }
    },
    paths: {}
};

