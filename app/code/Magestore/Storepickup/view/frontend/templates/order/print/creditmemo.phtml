<?php
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
// DISABLE function escapeHTML()
?>
<?php $_order = $block->getOrder() ?>
<?php $_creditmemo = $block->getCreditmemo() ?>
<?php if ($_creditmemo): ?>
    <?php $_creditmemos = [$_creditmemo]; ?>
<?php else: ?>
    <?php $_creditmemos = $_order->getCreditmemosCollection() ?>
<?php endif; ?>
<?php foreach ($_creditmemos as $_creditmemo): ?>
    <div class="order-details-items creditmemo">
        <div class="order-title">
            <strong><?php /* @escapeNotVerified */ echo __('Refund #%1', $_creditmemo->getIncrementId()) ?></strong>
        </div>
        <div class="table-wrapper order-items-creditmemo">
            <table class="data table table-order-items creditmemo" id="my-refund-table-<?php /* @escapeNotVerified */ echo $_creditmemo->getId(); ?>">
                <caption class="table-caption"><?php /* @escapeNotVerified */ echo __('Items Refunded') ?></caption>
                <thead>
                <tr>
                    <th class="col name"><?php /* @escapeNotVerified */ echo __('Product Name') ?></th>
                    <th class="col sku"><?php /* @escapeNotVerified */ echo __('SKU') ?></th>
                    <th class="col price"><?php /* @escapeNotVerified */ echo __('Price') ?></th>
                    <th class="col qty"><?php /* @escapeNotVerified */ echo __('Qty') ?></th>
                    <th class="col subtotal"><?php /* @escapeNotVerified */ echo __('Subtotal') ?></th>
                    <th class="col discount"><?php /* @escapeNotVerified */ echo __('Discount Amount') ?></th>
                    <th class="col rowtotal"><?php /* @escapeNotVerified */ echo __('Row Total') ?></th>
                </tr>
                </thead>
                <?php $_items = $_creditmemo->getAllItems(); ?>
                <?php $_count = count($_items); ?>
                <?php foreach ($_items as $_item): ?>
                    <?php if ($_item->getOrderItem()->getParentItem()) {
                        continue;
                    } ?>
                    <tbody>
                    <?php echo $block->getItemHtml($_item) ?>
                    </tbody>
                <?php endforeach; ?>
                <tfoot>
                <?php echo $block->getTotalsHtml($_creditmemo);?>
                </tfoot>
            </table>
        </div>
        <div class="block block-order-details-view">
            <div class="block-title">
                <strong><?php /* @escapeNotVerified */ echo __('Order Information') ?></strong>
            </div>
            <div class="block-content">
                <?php if (!$_order->getIsVirtual()): ?>
                    <div class="box box-order-shipping-address">
                        <div class="box-title">
                            <strong><?php /* @escapeNotVerified */ echo __('Shipping Address') ?></strong>
                        </div>
                        <div class="box-content">
                            <?php $_shipping = $_creditmemo->getShippingAddress() ?>
                            <address><?php /* @escapeNotVerified */ echo $block->formatAddress($_shipping, 'html') ?></address>
                        </div>
                    </div>
                    <div class="box box-order-shipping-method">
                        <div class="box-title">
                            <strong><?php /* @escapeNotVerified */ echo __('Shipping Method') ?></strong>
                        </div>
                        <div class="box-content">
                            <?php echo $_order->getShippingDescription() ?>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="box box-order-billing-address">
                    <div class="box-title">
                        <strong><?php /* @escapeNotVerified */ echo __('Billing Address') ?></strong>
                    </div>
                    <div class="box-content">
                        <?php $_billing = $_creditmemo->getbillingAddress() ?>
                        <address><?php /* @escapeNotVerified */ echo $block->formatAddress($_order->getBillingAddress(), 'html') ?></address>
                    </div>
                </div>
                <div class="box box-order-billing-method">
                    <div class="box-title">
                        <strong><?php /* @escapeNotVerified */ echo __('Payment Method') ?></strong>
                    </div>
                    <div class="box-content">
                        <?php echo $block->getPaymentInfoHtml() ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>
