<?php
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
// DISABLE function escapeHTML()

?>
<?php /** @var $block \Magento\Sales\Block\Order\Info */ ?>
<?php $_order = $block->getOrder() ?>
<div class="block block-order-details-view">
    <div class="block-title">
        <strong><?php /* @escapeNotVerified */ echo __('Order Information') ?></strong>
    </div>
    <div class="block-content">
        <?php if (!$_order->getIsVirtual()): ?>
            <div class="box box-order-shipping-address">
                <strong class="box-title"><span><?php /* @escapeNotVerified */ echo __('Shipping Address') ?></span></strong>
                <div class="box-content">
                    <address><?php /* @escapeNotVerified */ echo $block->getFormattedAddress($_order->getShippingAddress()); ?></address>
                </div>
            </div>

            <div class="box box-order-shipping-method">
                <strong class="box-title">
                    <span><?php /* @escapeNotVerified */ echo __('Shipping Method') ?></span>
                </strong>
                <div class="box-content">
                    <?php if ($_order->getShippingDescription()): ?>
                        <?php echo $_order->getShippingDescription() ?>
                    <?php else: ?>
                        <?php /* @escapeNotVerified */ echo __('No shipping information available'); ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="box box-order-billing-address">
            <strong class="box-title">
                <span><?php /* @escapeNotVerified */ echo __('Billing Address') ?></span>
            </strong>
            <div class="box-content">
                <address><?php /* @escapeNotVerified */ echo $block->getFormattedAddress($_order->getBillingAddress()); ?></address>
            </div>
        </div>
        <div class="box box-order-billing-method">
            <strong class="box-title">
                <span><?php /* @escapeNotVerified */ echo __('Payment Method') ?></span>
            </strong>
            <div class="box-content">
                <?php echo $block->getPaymentInfoHtml() ?>
            </div>
        </div>
    </div>
</div>
