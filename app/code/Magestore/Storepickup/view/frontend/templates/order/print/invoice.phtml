<?php
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
// DISABLE function escapeHTML()

?>
<?php $_order = $block->getOrder() ?>
<?php $_invoice = $block->getInvoice() ?>
<?php if ($_invoice): ?>
    <?php $_invoices = [$_invoice]; ?>
<?php else: ?>
    <?php $_invoices = $_order->getInvoiceCollection() ?>
<?php endif; ?>
<?php foreach ($_invoices as $_invoice): ?>
    <div class="order-details-items invoice">
        <div class="order-title">
            <strong><?php /* @escapeNotVerified */ echo __('Invoice #') ?><?php /* @escapeNotVerified */ echo $_invoice->getIncrementId(); ?></strong>
        </div>
        <div class="table-wrapper table-order-items invoice">
            <table class="data table table-order-items invoice" id="my-invoice-table-<?php /* @escapeNotVerified */ echo $_invoice->getId(); ?>">
                <caption class="table-caption"><?php /* @escapeNotVerified */ echo __('Items Invoiced') ?></caption>
                <thead>
                <tr>
                    <th class="col name"><?php /* @escapeNotVerified */ echo __('Product Name') ?></th>
                    <th class="col sku"><?php /* @escapeNotVerified */ echo __('SKU') ?></th>
                    <th class="col price"><?php /* @escapeNotVerified */ echo __('Price') ?></th>
                    <th class="col qty"><?php /* @escapeNotVerified */ echo __('Qty Invoiced') ?></th>
                    <th class="col subtotal"><?php /* @escapeNotVerified */ echo __('Subtotal') ?></th>
                </tr>
                </thead>
                <?php $_items = $_invoice->getItemsCollection(); ?>
                <?php $_count = $_items->count(); ?>
                <?php foreach ($_items as $_item): ?>
                    <?php if ($_item->getOrderItem()->getParentItem()) {
                        continue;
                    } ?>
                    <tbody>
                    <?php echo $block->getItemHtml($_item) ?>
                    </tbody>
                <?php endforeach; ?>
                <tfoot>
                <?php echo $block->getInvoiceTotalsHtml($_invoice)?>
                </tfoot>
            </table>
        </div>
        <div class="block block-order-details-view">
            <div class="block-title">
                <strong><?php /* @escapeNotVerified */ echo __('Order Information') ?></strong>
            </div>
            <div class="block-content">
                <?php if (!$_order->getIsVirtual()): ?>
                    <div class="box box-order-shipping-address">
                        <div class="box-title">
                            <strong><?php /* @escapeNotVerified */ echo __('Shipping Address') ?></strong>
                        </div>
                        <div class="box-content">
                            <?php $_shipping = $_invoice->getShippingAddress() ?>
                            <address><?php /* @escapeNotVerified */ echo $block->formatAddress($_shipping, 'html') ?></address>
                        </div>
                    </div>

                    <div class="box box-order-shipping-method">
                        <div class="box-title">
                            <strong><?php /* @escapeNotVerified */ echo __('Shipping Method') ?></strong>
                        </div>
                        <div class="box-content">
                            <?php if ($_order->getShippingDescription()): ?>
                                <?php echo $_order->getShippingDescription() ?>
                            <?php else: ?>
                                <?php /* @escapeNotVerified */ echo __('No shipping information available'); ?>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
                <div class="box box-order-billing-address">
                    <div class="box-title">
                        <strong><?php /* @escapeNotVerified */ echo __('Billing Address') ?></strong>
                    </div>
                    <div class="box-content">
                        <?php $_billing = $_invoice->getbillingAddress() ?>
                        <address><?php /* @escapeNotVerified */ echo $block->formatAddress($_order->getBillingAddress(), 'html') ?></address>
                    </div>
                </div>

                <div class="box box-order-billing-method">
                    <div class="box-title">
                        <strong><?php /* @escapeNotVerified */ echo __('Payment Method') ?></strong>
                    </div>
                    <div class="box-content">
                        <?php echo $block->getPaymentInfoHtml() ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; ?>
