<?php
/** @var \Magestore\Storepickup\Block\Wrapper $block */
?>
<script type="text/javascript">
    var select_store_by_map ='<div id="select_store_by_map" ><?php echo __("Select store by map") ?></div>';
    var storepickup_date_box = '<div id="shipping_date_div" class="storepickup-box field required"> Please select Pickup Date/Time to go next step!<br/>'
        + '<label for="shipping_date"><?php echo __("Pickup Date :") ?> </label>'
         + '<input name="shipping_date" style="display:none;" type="text" id="shipping_date" value="" class="input-text required-entry form-control" readonly>'
        + '</div>' +'<br />';
    var storepickup_time_box = '<div id="shipping_time_div" class="storepickup-box field required">'
        + '<label for="shipping_time"><?php echo __("Pickup Time :") ?>  </label>'
        + '<select style="display:none;" id="shipping_time" name="shipping_time" class="validate-select"></select>'
        + '</div>';
    var defaultStore= '<?php echo $block->getSystemConfig()->getDefaultStore() ?>';
    var isDisplayPickuptime= <?php echo $block->getSystemConfig()->isDisplayPickuptime() ?>;
    var storePikcuplatitude="";
    var storePikcuplongitude="";
    window.onload = function() {
        require(
            [
                'jquery',
                'Magento_Ui/js/modal/modal',
                'Magestore_Storepickup/js/checkout/storepickupcheckout'
            ],
            function(
                $,
                modal
            ) {

                var options = {
                    type: 'popup',
                    responsive: true,
                    innerScroll: true,
                    title: 'Choose store by map',
                    buttons: [{
                        text: $.mage.__('Close'),
                        class: '',
                        click: function () {
                            this.closeModal();
                        }
                    }]
                };

                var popup = modal(options, $('#popup-mpdal'));
            }
        );
    };

    window.liststoreJson = <?php echo $block->getListStore() ?>;

</script>

<div id="popup-mpdal" class="storepickup-wrapper col-full" style="display: none;">
    <?php echo $block->getChildHtml('storepickupCheckout.gmapstore') ?>
</div>
<div class="overlay-bg-checkout" style="display: none">
    <img src="<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/ajax-loader.gif') ?>" alt="overlay" />
</div>