<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

/** @var \Magestore\Storepickup\Block\ListStore\ListStoreBox $block */
?>
<div data-mage-init='{"Magestore_Storepickup/js/direction": {}}' class="option-direction custom-popup col-xs-12"
     style="display: none;padding: 0;">
    <ul class="vertical text-center">
        <li class="travel car active" data-traveling="DRIVING"><span>A</span></li>
        <li class="travel bus" data-traveling="TRANSIT"><span>A</span></li>
        <li class="travel walk" data-traveling="WALKING"><span>A</span></li>
        <li class="travel bicycle" data-traveling="BICYCLING"><span>A</span></li>
    </ul>
    <div id="directions-tool" class="col-xs-12 directions-tool">
        <div class="widget-directions-searchbox-handle">
            <div class="widget-directions-icon waypoint-handle"><label for="origin">A</label></div>
            <div class="widget-directions-icon waypoint-to"><label for="origin">C</label></div>
            <div class="widget-directions-icon waypoint-bullet"><label for="origin">B</label></div>
        </div>
        <div class="form-inputs">
            <input class="form-control origin originA start" type="text" name="originA" isStart="true"
                   autocomplete="off">
            <input class="form-control origin originB end" readonly="true" type="text" name="originB" autocomplete="off"
                   value="">
        </div>
        <div class="widget-directions-right-overlay">
            <button type="button" onclick="" class="swap-locations"
                    title="<?php echo __('Swap locations A-B') ?>"><?php echo __('Swap locations A-B') ?></button>
        </div>
        <div class="directions-panel"></div>
    </div>
    <div class="box-input col-xs-12">
        <button class="get-direction action primary pull-right" id="get_direction"
                title="<?php echo __('Get Directions') ?>"
                type="submit">
            <span><?php echo __('Go') ?></span>
        </button>
    </div>
</div>
<div class="list-store-box col-sm-4 col-xs-12 pull-left">
    <div class="page-title">
        <h2 class="title"><span class="glyphicon glyphicon-align-justify"
                                aria-hidden="true"></span><span><?php echo __('Store list') ?></span><span
                class="number-store pull-right"></span></h2>
    </div>
    <div class="list-store col-xs-12">
        <ul class="list-store-container disable-ul"
            data-mage-init='{
                "Magestore_Storepickup/js/liststore": {
                    "defaultStoreIcon": "<?php echo $block->getViewFileUrl('Magestore_Storepickup::images/map_with_pin.png') ?>",
                    "mediaUrlImage": "<?php echo $block->getMediaUrlImage() ?>",
                    "distanceUnit": "<?php echo $block->getSystemConfig()->getDistanceUnit() ?>"
                }
            }'>
        </ul>
    </div>
    <div id="pagination-list"
         class="pagination-list pagination-wrapper text-center col-full"
         data-mage-init='{
            "Magestore_Storepickup/js/pagination": {

            }
         }'
    >
    </div>
</div>
<script id="popup-store-template" class="popup-store-template" type="text/x-magento-template">
    <div class="popup-store store-item store-<%- data.storepickup_id %>"
         title="<%- data.store_name %>"
         data-store-id="<%- data.storepickup_id %>"
         data-store-index="<%- data.index %>"
         data-latitude="<%- data.latitude %>"
         data-longitude="<%- data.longitude %>"
         data-address="<%- data.address %>"
    >
        <div class="store-content">
            <div class="col-sm-3 col-xs-3 tag-store">
                <img class="img-responsive" width="70px" src="<%- data.imgSrc %>" alt="<%- data.store_name %>">
                <p class="text-center"><%- data.distanceText %></p>
            </div>
            <div class="col-sm-9 col-xs-9 tag-content">
                <h4><a target="_blank" class="title-store"
                       href="<?php echo $block->getBaseUrl() ?><%- data.rewrite_request_path %>"><%- data.store_name
                        %></a></h4>
                <p class="address-store"><%- data.address %></p>
                <p class="phone-store"><%- data.phone %></p>
                <p>
                    <span class="btn-link street-view">Street View</span>
                    <span class="btn-link direction">Direction</span>
                </p>
            </div>
        </div>
    </div>
</script>
<script id="store-template" class="store-template" type="text/x-magento-template">
    <li class="show-tag-li store-item store-<%- data.storepickup_id %>"
        title="<%- data.store_name %>"
        data-store-id="<%- data.storepickup_id %>"
        data-store-index="<%- data.index %>"
        data-latitude="<%- data.latitude %>"
        data-longitude="<%- data.longitude %>"
        data-address="<%- data.address %>"
    >
        <div class="">
            <div class="col-sm-3 col-xs-3 tag-store">
                <img style="min-height: 50px; width: auto;" src="<%- data.imgSrc %>" alt="<%- data.store_name %>">
                <p class="text-center"><%- data.distanceText %></p>
            </div>
            <div class="col-sm-9 col-xs-9 tag-content">
                <h4><a target="_blank" class="title-store"
                       href="<?php echo $block->getBaseUrl() ?><%- data.rewrite_request_path %>"><%- data.store_name
                        %></a></h4>
                <p class="address-store"><%- data.address %></p>
                <p class="phone-store"><%- data.phone %></p>
                <p>
                    <span class="btn-link street-view">Street View</span>
                    <span class="btn-link direction">Direction</span>
                </p>
            </div>
        </div>
    </li>
</script>