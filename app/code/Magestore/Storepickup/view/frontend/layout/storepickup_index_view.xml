<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Magestore_Storepickup::css/bootstrap/bootstrap.css"></css>
        <css src="Magestore_Storepickup::css/bootstrap/glyphicons.css"></css>
        <css src="Magestore_Storepickup::css/viewpage.css"></css>
    </head>
    <body>
        <referenceContainer name="content">
            <block class="Magestore\Storepickup\Block\Store\ViewPage" name="storepickup.viewpage" template="Magestore_Storepickup::viewpage.phtml" />
        </referenceContainer>
        <referenceContainer name="columns.top">
        </referenceContainer>
        <referenceBlock name="page.main.title" remove="true"/>
    </body>
</page>
