<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magestore
 * 
 * NOTICE OF LICENSE
 * 
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 * 
 * DISCLAIMER
 * 
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 * 
 * @category    Magestore
 * @package     Magestore_Storepickup
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">
                <resource id="Magestore_Storepickup::storepickup" title="Store pickup" sortOrder="10">
                    <resource id="Magestore_Storepickup::store" title="Manage Store" sortOrder="10"/>
                    <resource id="Magestore_Storepickup::tag" title="Manage Tag" sortOrder="10"/>
                    <resource id="Magestore_Storepickup::schedule" title="Manage Schedule" sortOrder="10"/>
                    <resource id="Magestore_Storepickup::holiday" title="Manage Holiday" sortOrder="10"/>
                    <resource id="Magestore_Storepickup::specialday" title="Manage Special day" sortOrder="10"/>
                    <resource id="Magestore_Storepickup::guide" title="Guide" sortOrder="10"/>
                    <resource id="Magestore_Storepickup::settings" title="Settings" sortOrder="10"/>
                </resource>
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Magestore_Storepickup::config_storepickup" title="Store pickup"  sortOrder="10" />
                        </resource>
                    </resource>
                </resource>
            </resource>
        </resources>
    </acl>
</config>