<?xml version="1.0" encoding="UTF-8"?>
<!--
 /**
  * Copyright © Magento, Inc. All rights reserved.
  * See COPYING.txt for license details.
  */
-->
<actionGroups xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:noNamespaceSchemaLocation="urn:magento:mftf:Test/etc/actionGroupSchema.xsd">
  <actionGroup name="AdminSortingWithoutRuleProductsInCategoryPageActionGroup">
    <arguments>
      <argument name="sorting" defaultValue="{{SmartCategoryAutomaticSorting.name_a_z}}" type="string"/>
    </arguments>
    <!-- Set criteria for automatic sorting -->
    <waitForElementVisible selector="{{AdminCategoryProductsSection.sortOrder}}" stepKey="seeSortingSelect"/>
    <selectOption selector="{{AdminCategoryProductsSection.sortOrder}}" userInput="{{sorting}}" stepKey="selectSort"/>
    <click selector="{{AdminCategoryProductsSection.sort}}" stepKey="clickOnSort"/>
  </actionGroup>
</actionGroups>
