<?xml version="1.0" encoding="UTF-8"?>
<!--
 /**
  * Copyright © Magento, Inc. All rights reserved.
  * See COPYING.txt for license details.
  */
-->

<tests xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:noNamespaceSchemaLocation="urn:magento:mftf:Test/etc/testSchema.xsd">
    <test name="VerifyCustomerMatchedOnlyVirtualItemInCartTest">
        <annotations>
            <features value="Email"/>
            <stories value="Verify that customer is matched only if cart has virtual item, Email Remainde"/>
            <title value="Verify that customer is matched only if cart has virtual item, Email Remainde"/>
            <description value="Verify that customer is matched only if cart has virtual item, Email Remainde"/>
            <severity value="MAJOR"></severity>
            <testCaseId value="AC-5811"/>
        </annotations>

        <before>
            <actionGroup ref="AdminLoginActionGroup" stepKey="loginAsAdmin"/>

            <!-- Customer <EMAIL> is created.-->
            <createData entity="Simple_US_Customer" stepKey="customerJohn"/>

            <!--Customer <EMAIL> is created.-->
            <createData entity="Simple_GB_Customer" stepKey="customerJane"/>

            <!--Simple product is created.-->
            <createData entity="_defaultCategory" stepKey="category"/>
            <createData entity="SimpleProduct" stepKey="product">
                <requiredEntity createDataKey="category"/>
            </createData>

            <!--Virtual product is created.-->
            <createData entity="VirtualProduct" stepKey="virtualProduct">
                <requiredEntity createDataKey="category"/>
            </createData>

            <!-- johndoe has 1 virtual prduct in cart-->
            <!-- Login as customer -->
            <actionGroup ref="LoginToStorefrontActionGroup" stepKey="loginToStorefront">
                <argument name="Customer" value="$$customerJohn$$"/>
            </actionGroup>

            <!-- Add product to shopping cart -->
            <amOnPage url="{{StorefrontProductPage.url($$virtualProduct.custom_attributes[url_key]$$)}}" stepKey="navigateToToPDP"/>
            <actionGroup ref="StorefrontAddProductToCartWithQtyActionGroup" stepKey="addVirtualProductToCart">
                <argument name="productQty" value="1"/>
            </actionGroup>
            <actionGroup ref="StorefrontCustomerLogoutActionGroup" stepKey="clickOnSignOut"/>

            <!--JaneDoe has 1 simple product in cart-->
            <!-- Login as customer -->
            <actionGroup ref="LoginToStorefrontActionGroup" stepKey="loginToStorefrontWithNewCustomer">
                <argument name="Customer" value="$$customerJane$$"/>
            </actionGroup>

            <!-- Add product to shopping cart -->
            <amOnPage url="{{StorefrontProductPage.url($$product.custom_attributes[url_key]$$)}}" stepKey="navigateToToPDPAgain"/>
            <actionGroup ref="StorefrontAddProductToCartWithQtyActionGroup" stepKey="addSimpleProductToCart">
                <argument name="productQty" value="1"/>
            </actionGroup>
        </before>

        <!-- Create new rule, condition and clicking on run now -->
        <amOnPage url="{{AdminEmailReminderPage.url}}" stepKey="navigateToEmailReminderPage" />
        <waitForPageLoad stepKey="waitForPageLoadToViewEmailReminderPage"/>
        <actionGroup ref="AdminEmailReminderRuleActionGroup" stepKey="createNewRule"/>
        <actionGroup ref="AdminEmailReminderConditionsActionGroup" stepKey="createConditions"/>
        <actionGroup ref="AdminEmailReminderRunNowActionGroup" stepKey="clickOnRunNow"/>

        <!-- Match the customer in grid/>-->
        <actionGroup ref="AdminEmailReminderMatchedCustomersActionGroup" stepKey="validateEmailId">
            <argument name="Customer" value="$$customerJohn$$"/>
        </actionGroup>

        <after>
            <actionGroup ref="AdminDeleteEmailReminderRuleActionGroup" stepKey="deleteTheRule"/>
            <deleteData createDataKey="product" stepKey="deleteProduct"/>
            <deleteData createDataKey="virtualProduct" stepKey="deleteVirtualProduct"/>
            <deleteData createDataKey="category" stepKey="deleteCategory"/>
            <deleteData createDataKey="customerJohn" stepKey="deleteCustomerJohn"/>
            <deleteData createDataKey="customerJane" stepKey="deleteCustomerJane"/>
            <actionGroup ref="AdminLogoutActionGroup" stepKey="logout"/>
        </after>
    </test>
</tests>
