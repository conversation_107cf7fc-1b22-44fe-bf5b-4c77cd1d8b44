<?xml version="1.0" encoding="UTF-8"?>
<!--
 /**
  * Copyright © Magento, Inc. All rights reserved.
  * See COPYING.txt for license details.
  */
-->

<actionGroups xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
              xsi:noNamespaceSchemaLocation="urn:magento:mftf:Test/etc/actionGroupSchema.xsd">
    <actionGroup name="AdminDeleteEmailReminderRuleActionGroup">
    <annotations>
        <description> Delete created New Rule, and validate the success message</description>
    </annotations>
        <amOnPage url="{{AdminEmailReminderPage.url}}" stepKey="navigateToDesignConfigPageAgain" />
        <waitForPageLoad stepKey="waitForPageLoadToViewReminderPage"/>
        <click selector="{{AdminDataGridHeaderSection.clearFilters}}" stepKey="resetSearchFilter"/>
        <fillField userInput="Test Rule" selector="{{AdminEmailReminderSection.searchRule}}" stepKey="fillSearchEmailField"/>
        <click selector="{{AdminDataGridHeaderSection.searchButton}}" stepKey="clickSearchButton"/>
        <click selector="{{AdminEmailReminderSection.selectFirstRow}}" stepKey="clickFirstRow"/>
        <click selector="{{AdminNewStoreViewActionsSection.delete}}" stepKey="clickDeleteStoreViewButtonOnDeleteStorePage"/>
        <click selector="{{AdminEmailReminderSection.ok}}" stepKey="clickOnOkButton"/>
        <waitForPageLoad stepKey="waitForPageToLoad"/>
        <see userInput="You deleted the reminder rule." stepKey="seeSuccessMessage"/>
    </actionGroup>
</actionGroups>
