<?xml version="1.0" encoding="UTF-8"?>
<!--
 /**
  * Copyright © Magento, Inc. All rights reserved.
  * See COPYING.txt for license details.
  */
-->

<sections xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:noNamespaceSchemaLocation="urn:magento:mftf:Page/etc/SectionObject.xsd">
    <section name="AdminEmailReminderSection">
        <element name="addNewRule" type="button" selector="//span[normalize-space()='Add New Rule']"/>
        <element name="ruleName" type="input" selector="//input[@name='name']"/>
        <element name="saveAndContinueEdit" type="button" selector="//span[normalize-space()='Save and Continue Edit']"/>
        <element name="successMessage" type="text" selector="//div[text()='You saved the reminder rule.']"/>
        <element name="conditions" type="button" selector="//a[@name='conditions_section']" timeout="30"/>
        <element name="clickAdd" type="button" selector="//img[@class='rule-param-add v-middle']" timeout="30"/>
        <element name="chooseCart" type="button" selector="//select[@name='rule[conditions][1][new_child]']//option[text()='Shopping Cart']" timeout="30"/>
        <element name="clickOnPlus" type="button" selector="//ul[@id='conditions__1--1__children']//img[@title='Add']" />
        <element name="chooseVirtualOnly" type="button" selector="//select[@name='rule[conditions][1--1][new_child]']//option[text()='Virtual Only']" timeout="30"/>
        <element name="runNow" type="button" selector="//button[@title='Run Now']"/>
        <element name="ok" type="button" selector="aside.confirm .modal-footer button.action-accept" timeout="120"/>
        <element name="matchedCustomers" type="button" selector="//a[@name='matched_customers']" timeout="30"/>
        <element name="emailCustomer" type="input" selector="//input[@name='grid_email']" timeout="30"/>
        <element name="searchRule" type="input" selector="//input[@id='reminderGrid_filter_name']" timeout="30"/>
        <element name="emailId" type="text" selector= "//td[contains(@class,'a-left col-grid_email')]//a[text()='{{email}}']" parameterized="true"/>
        <element name="selectFirstRow" type="text" selector="//td[normalize-space()='Test Rule']" timeout="30"/>
    </section>
</sections>
