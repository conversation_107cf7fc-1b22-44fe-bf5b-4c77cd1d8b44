<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="BranchesTreeState">
    <expand>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="LOCAL_ROOT" type="e8cecc67:BranchNodeDescriptor" />
      </path>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="REMOTE_ROOT" type="e8cecc67:BranchNodeDescriptor" />
      </path>
      <path>
        <item name="ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="REMOTE_ROOT" type="e8cecc67:BranchNodeDescriptor" />
        <item name="GROUP_NODE:origin" type="e8cecc67:BranchNodeDescriptor" />
      </path>
    </expand>
    <select />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9690977b-f198-4821-a05b-93e8f009ae89" name="Default Changelist" comment="Sử dụng Plugin để áp dụng cho các version &lt;BSS-EXM2-QV-33&gt;">
      <change beforePath="$PROJECT_DIR$/composer.json" beforeDir="false" afterPath="$PROJECT_DIR$/composer.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/etc/adminhtml/system.xml" beforeDir="false" afterPath="$PROJECT_DIR$/etc/adminhtml/system.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/etc/module.xml" beforeDir="false" afterPath="$PROJECT_DIR$/etc/module.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ComposerSettings" doNotAsk="true" synchronizationState="SYNCHRONIZE">
    <pharConfigPath>$PROJECT_DIR$/composer.json</pharConfigPath>
    <execution />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="ProjectId" id="1jtvxW2xMPi1HnFjgJB4P899Cjg" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="vue.rearranger.settings.migration" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$" />
    </key>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9690977b-f198-4821-a05b-93e8f009ae89" name="Default Changelist" comment="" />
      <created>1604636449568</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1604636449568</updated>
      <workItem from="1604636451506" duration="332000" />
      <workItem from="1605059227515" duration="604000" />
      <workItem from="1605061703113" duration="1838000" />
      <workItem from="1606288763533" duration="866000" />
    </task>
    <task id="LOCAL-00001" summary="Sử dụng Plugin để áp dụng cho các version &lt;BSS-EXM2-QV-33&gt;">
      <created>1604636759790</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1604636759790</updated>
    </task>
    <task id="LOCAL-00002" summary="Sử dụng Plugin để áp dụng cho các version &lt;BSS-EXM2-QV-33&gt;">
      <created>1605063617895</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1605063617895</updated>
    </task>
    <option name="localTasksCounter" value="3" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="Sử dụng Plugin để áp dụng cho các version &lt;BSS-EXM2-QV-33&gt;" />
    <option name="LAST_COMMIT_MESSAGE" value="Sử dụng Plugin để áp dụng cho các version &lt;BSS-EXM2-QV-33&gt;" />
  </component>
  <component name="WindowStateProjectService">
    <state x="801" y="271" width="374" height="562" key="#com.intellij.ide.util.MemberChooser" timestamp="1605062699465">
      <screen x="67" y="27" width="1853" height="1053" />
    </state>
    <state x="801" y="271" width="374" height="562" key="#com.intellij.ide.util.MemberChooser/67.27.1853.1053@67.27.1853.1053" timestamp="1605062699465" />
    <state x="588" y="274" width="800" height="556" key="Vcs.Push.Dialog.v2" timestamp="1605063620802">
      <screen x="67" y="27" width="1853" height="1053" />
    </state>
    <state x="588" y="274" width="800" height="556" key="Vcs.Push.Dialog.v2/67.27.1853.1053@67.27.1853.1053" timestamp="1605063620802" />
    <state x="684" y="291" width="618" height="522" key="find.popup" timestamp="1606290600542">
      <screen x="67" y="27" width="1853" height="1053" />
    </state>
    <state x="684" y="291" width="618" height="522" key="find.popup/67.27.1853.1053@67.27.1853.1053" timestamp="1606290600542" />
  </component>
</project>