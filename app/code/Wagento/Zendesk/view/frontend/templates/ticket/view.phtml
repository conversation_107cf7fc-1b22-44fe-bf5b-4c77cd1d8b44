<?php
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
// @codingStandardsIgnoreFile
/** @var \Wagento\Zendesk\Block\Ticket\View $block */
$authorId = $block->getAuthorId();
$comments = $block->getComments();
?>
<div class="panel panel-primary">
    <div class="panel-heading"></div>
    <div class="panel-body body-panel">
        <ul class="chat">
            <?php foreach ($comments as $comment) : ?>
                <?php if ($comment['public']): ?>
                    <?php $right = $comment['author_id'] == $authorId; ?>
                    <li class="bs-callout <?= $right ? 'bs-callout-primary right' : 'bs-callout-warning left'; ?>">
                        <div class="chat-body">
                            <div class="header">
                                <small class="text-muted <?= $right ? '' : 'pull-right' ?>">
                                    <?= date('Y-m-d h:i:s', strtotime($comment['created_at'])) ?>
                                </small>

                                <strong class="<?= $right ? 'pull-right' : '' ?>">
                                    <?= $block->getUserName($comment['author_id']) ?>
                                </strong>
                            </div>
                            <?= $comment['html_body']; ?>
                        </div>
                    </li>
                <?php endif; ?>
            <?php endforeach; ?>
        </ul>
    </div>

    <div class="panel-footer">
        <?php if ($block->canComment()) : ?>
            <form class="form" action="<?php echo $block->getActionUrl(); ?>" id="contact-form" method="post">
                <fieldset class="fieldset">
                    <div class="field comment required">
                        <label class="label" for="comment"><span><?= __('Message'); ?></span></label>
                        <div class="control">
                            <textarea name="comment" id="comment" title="Message" class="input-text"></textarea>
                        </div>
                    </div>
                    <input type="hidden" name="code" id="code" value="<?= $block->getTicketId(); ?>">
                </fieldset>
                <div class="actions-toolbar">
                    <div class="primary">
                        <?= $block->getBlockHtml('formkey'); ?>
                        <input type="hidden" name="hideit" id="hideit" value="">
                        <button title="Submit" class="action submit primary">
                            <span><?= __('Send') ?></span>
                        </button>
                    </div>
                </div>
            </form>
        <?php endif; ?>
    </div>
</div>
