<?php
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
// @codingStandardsIgnoreFile
/** @var \Wagento\Zendesk\Block\Ticket\Form $block */
?>
<form class="form" action="<?= $block->getActionUrl(); ?>" id="contact-form" method="post">
    <fieldset class="fieldset">

        <div class="field subject required">
            <label class="label" for="subject"><span><?= __('Subject'); ?></span></label>
            <div class="control">
                <input name="subject" id="subject" title="<?= __('Subject'); ?>" class="input-text" type="text">
            </div>
        </div>


        <div class="field order">
            <label class="label" for="order"><span><?= __('Order Number'); ?></span></label>
            <div class="control">
                <select name="order" id="order">
                    <option value="-1"><?= __('No order selected'); ?></option>
                    <?php foreach ($block->getOrders() as $order): ?>
                        <option <?= $block->getRequestedOrderId() == $order['value'] ? 'selected' : ''; ?>
                                value="<?= $order['label']; ?>">
                            <?= $order['label']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>


        <div class="field comment required">
            <label class="label" for="comment"><span><?= __('Comment'); ?></span></label>
            <div class="control">
                <textarea name="comment" id="comment" title="What’s on your mind?" class="input-text" cols="5" rows="3"
                          data-validate="{required:true}" aria-required="true"></textarea>
            </div>
        </div>
    </fieldset>

    <div class="actions-toolbar">
        <div class="primary">
            <input type="hidden" name="hideit" id="hideit" value="">
            <button type="submit" title="Submit" class="action submit primary">
                <span><?= __('Submit'); ?></span>
            </button>
        </div>
    </div>
</form>
