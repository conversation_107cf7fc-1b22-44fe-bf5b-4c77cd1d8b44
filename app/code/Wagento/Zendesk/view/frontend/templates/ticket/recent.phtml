<?php
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
// @codingStandardsIgnoreFile
/** @var \Wagento\Zendesk\Block\Tickets $block */
$tickets = $block->getRecentTickets();
?>

<div class="block block-dashboard-tickets">
    <div class="block-title ticket">
        <strong><?= __('Recent Tickets') ?></strong>
        <?php if (sizeof($tickets) > 0): ?>
            <a class="action view" href="<?= $block->getViewAllUrl() ?>">
                <span><?= __('View All') ?></span>
            </a>
        <?php endif; ?>
    </div>
    <div class="block-content">
        <?php if ($tickets): ?>
            <div class="table-wrapper">
                <table cellspacing="0" class="table">
                    <thead>
                    <tr>
                        <th><?php echo $block->escapeHtml(__('Subject')) ?></th>
                        <th><?php echo $block->escapeHtml(__('Requested')) ?></th>
                        <th><?php echo $block->escapeHtml(__('Updated')) ?></th>
                        <th><?php echo $block->escapeHtml(__('Status')) ?></th>
                        <th><?php echo $block->escapeHtml(__('Action')) ?></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($tickets as $ticket): ?>
                        <tr>
                            <td><?php echo $block->escapeHtml(__($ticket['subject'])); ?></td>
                            <td><?php echo $block->escapeHtml(__($ticket['created_at'])); ?></td>
                            <td><?php echo $block->escapeHtml(__($ticket['updated_at'])); ?></td>
                            <td><?php echo $block->escapeHtml(__($ticket['status'])); ?></td>
                            <td>
                                <a href="<?php echo $block->getViewUrl($ticket['id']) ?>">
                                    <?php echo $block->escapeHtml(__('View')); ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else : ?>
            <div class="message info empty">
                <span><?php echo $block->escapeHtml(__('You don\'t have any tickets')) ?></span>
            </div>
        <?php endif; ?>
    </div>
</div>
