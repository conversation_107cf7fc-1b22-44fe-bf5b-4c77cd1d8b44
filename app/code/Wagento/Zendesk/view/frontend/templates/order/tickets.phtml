<?php
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
// @codingStandardsIgnoreFile
/** @var \Wagento\Zendesk\Block\Tickets $block */
$tickets = $block->getOrderTickets();

// open url
$orderId = $block->getRequest()->getParam('order_id', null);
$params = isset($orderId) ? ['orderid' => $orderId] : [];
$openTicketUrl = $block->getUrl('zendesk/ticket/create', $params);
?>

<div class="block block-order-tickets">
    <div class="block-title ticket">
        <strong><?= __('Tickets') ?></strong>
    </div>
    <div class="block-content">
        <?php if ($tickets): ?>
            <div class="table-wrapper">
                <table cellspacing="0" class="table">
                    <thead>
                    <tr>
                        <th><?php echo $block->escapeHtml(__('Subject')) ?></th>
                        <th><?php echo $block->escapeHtml(__('Requested')) ?></th>
                        <th><?php echo $block->escapeHtml(__('Updated')) ?></th>
                        <th><?php echo $block->escapeHtml(__('Status')) ?></th>
                        <th><?php echo $block->escapeHtml(__('Action')) ?></th>
                    </tr>
                    </thead>
                    <tbody>
                    <?php foreach ($tickets as $ticket): ?>
                        <tr>
                            <td><?php echo $block->escapeHtml(__($ticket['subject'])); ?></td>
                            <td><?php echo $block->escapeHtml(__($ticket['created_at'])); ?></td>
                            <td><?php echo $block->escapeHtml(__($ticket['updated_at'])); ?></td>
                            <td><?php echo $block->escapeHtml(__($ticket['status'])); ?></td>
                            <td>
                                <a href="<?php echo $block->getViewUrl($ticket['id']) ?>">
                                    <?php echo $block->escapeHtml(__('View')); ?>
                                </a>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else : ?>
            <div class="message info empty">
                <span><?php echo $block->escapeHtml(__('You don\'t have any tickets')) ?></span>
            </div>
        <?php endif; ?>
        <div class="actions-toolbar">
            <div class="primary">
                <a href="<?= $openTicketUrl ?>" class="action create primary">
                    <span><?= __('Open Ticket'); ?></span>
                </a>
            </div>
        </div>
    </div>
</div>
