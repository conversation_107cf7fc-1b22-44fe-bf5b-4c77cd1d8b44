<?php
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
/** @var Magento\Sales\Block\Order\Recent $block */
?>
<div class="block block-dashboard-orders">
<?php $_orders = $block->getOrders(); ?>
    <div class="block-title order">
        <strong><?= /* @escapeNotVerified */ __('Recent Orders') ?></strong>
        <?php if (sizeof($_orders->getItems()) > 0): ?>
            <a class="action view" href="<?= /* @escapeNotVerified */ $block->getUrl('sales/order/history') ?>">
                <span><?= /* @escapeNotVerified */ __('View All') ?></span>
            </a>
        <?php endif; ?>
    </div>
    <div class="block-content">
    <?php if (sizeof($_orders->getItems()) > 0): ?>
        <div class="table-wrapper orders-recent">
            <table class="data table table-order-items recent" id="my-orders-table">
                <caption class="table-caption"><?= /* @escapeNotVerified */ __('Recent Orders') ?></caption>
                <thead>
                    <tr>
                        <th scope="col" class="col id"><?= /* @escapeNotVerified */ __('Order #') ?></th>
                        <th scope="col" class="col date"><?= /* @escapeNotVerified */ __('Date') ?></th>
                        <th scope="col" class="col shipping"><?= /* @escapeNotVerified */ __('Ship To') ?></th>
                        <th scope="col" class="col total"><?= /* @escapeNotVerified */ __('Order Total') ?></th>
                        <th scope="col" class="col status"><?= /* @escapeNotVerified */ __('Status') ?></th>
                        <th scope="col" class="col actions"><?= /* @escapeNotVerified */ __('Action') ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($_orders as $_order): ?>
                        <tr>
                            <td data-th="<?= $block->escapeHtml(__('Order #')) ?>" class="col id"><?= /* @escapeNotVerified */ $_order->getRealOrderId() ?></td>
                            <td data-th="<?= $block->escapeHtml(__('Date')) ?>" class="col date"><?= /* @escapeNotVerified */ $block->formatDate($_order->getCreatedAt()) ?></td>
                            <td data-th="<?= $block->escapeHtml(__('Ship To')) ?>" class="col shipping"><?= $_order->getShippingAddress() ? $block->escapeHtml($_order->getShippingAddress()->getName()) : '&nbsp;' ?></td>
                            <td data-th="<?= $block->escapeHtml(__('Order Total')) ?>" class="col total"><?= /* @escapeNotVerified */ $_order->formatPrice($_order->getGrandTotal()) ?></td>
                            <td data-th="<?= $block->escapeHtml(__('Status')) ?>" class="col status"><?= /* @escapeNotVerified */ $_order->getStatusLabel() ?></td>
                            <td data-th="<?= $block->escapeHtml(__('Actions')) ?>" class="col actions">
                                <a href="<?= /* @escapeNotVerified */ $block->getViewUrl($_order) ?>" class="action view">
                                    <span><?= /* @escapeNotVerified */ __('View Order') ?></span>
                                </a>
                                <?php if ($this->helper('Magento\Sales\Helper\Reorder')->canReorder($_order->getEntityId())) : ?>
                                    <a href="#" data-post='<?php /* @escapeNotVerified */ echo
                                    $this->helper(\Magento\Framework\Data\Helper\PostHelper::class)
                                        ->getPostData($block->getReorderUrl($_order))
                                    ?>' class="action order">
                                        <span><?= /* @escapeNotVerified */ __('Reorder') ?></span>
                                    </a>
                                <?php endif ?>

                                <?php // Open ticket feature ?>
                                <?= $block->getChildBlock('open.ticket')->setData('orderid', $_order->getId())->toHtml(); ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    <?php else: ?>
        <div class="message info empty"><span><?= /* @escapeNotVerified */ __('You have placed no orders.') ?></span></div>
    <?php endif; ?>
    </div>
</div>
