<?xml version="1.0"?>
<!--
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
-->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">

    <body>
        <referenceBlock name="sales.order.history">
            <action method="setTemplate" ifconfig="zendesk/ticket/frontend/customer_account_open_ticket">
                <argument name="template" xsi:type="string">Wagento_Zendesk::order/history.phtml</argument>
            </action>

            <block class="Wagento\Zendesk\Block\Ticket\Action\OpenTicket"
                   name="open_ticket" as="open.ticket"
                   template="Wagento_Zendesk::ticket/action/openticket.phtml"/>
        </referenceBlock>
    </body>
</page>
