<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
-->

<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">

    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">zendesk_ticket_form.zendesk_ticket_form_data_source</item>
            <item name="namespace" xsi:type="string">zendesk_ticket_form</item>
        </item>
        <item name="label" xsi:type="string" translate="true">Ticket Information</item>
        <item name="template" xsi:type="string">templates/form/collapsible</item>
    </argument>

    <settings>
        <buttons>
            <button name="save" class="Wagento\Zendesk\Block\Adminhtml\Ticket\Edit\CreateButton"/>
            <button name="back" class="Wagento\Zendesk\Block\Adminhtml\Ticket\Edit\BackButton"/>
        </buttons>
        <namespace>zendesk_ticket_form</namespace>
        <dataScope>data</dataScope>
        <deps>
            <dep>zendesk_ticket_form.zendesk_ticket_form_data_source</dep>
        </deps>
    </settings>

    <dataSource name="zendesk_ticket_form_data_source">
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
            </item>
        </argument>

        <settings>
            <submitUrl path="zendesk/ticket/create"/>
        </settings>

        <dataProvider class="Wagento\Zendesk\Model\Ticket\DataProvider" name="zendesk_ticket_form_data_source">
            <settings>
                <requestFieldName>id</requestFieldName>
                <primaryFieldName>id</primaryFieldName>
            </settings>
        </dataProvider>
    </dataSource>

    <fieldset name="general">
        <settings>
            <label/>
        </settings>

        <field name="requester" sortOrder="10" formElement="input">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                    <rule name="validate-email" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Requester Email</label>
                <dataScope>requester</dataScope>
            </settings>
        </field>

        <field name="requester_name" sortOrder="20" formElement="input">
            <settings>
                <dataType>text</dataType>
                <label translate="true">Requester Name</label>
                <dataScope>requester_name</dataScope>
            </settings>
        </field>

        <field name="website_id" formElement="select">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>int</dataType>
                <label translate="true">Requester Website</label>
                <dataScope>website_id</dataScope>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Magento\Store\Model\ResourceModel\Website\Collection"/>
                    </settings>
                </select>
            </formElements>
        </field>

        <field name="subject" sortOrder="30" formElement="input">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Subject</label>
                <dataScope>subject</dataScope>
            </settings>
        </field>

        <field name="status" formElement="select">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>string</dataType>
                <label translate="true">Status</label>
                <dataScope>status</dataScope>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Wagento\Zendesk\Model\Config\Source\Ticket\Status"/>
                    </settings>
                </select>
            </formElements>
        </field>

        <field name="type" formElement="select">
            <settings>
                <dataType>string</dataType>
                <label translate="true">Type</label>
                <dataScope>type</dataScope>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Wagento\Zendesk\Model\Config\Source\Ticket\Type"/>
                    </settings>
                </select>
            </formElements>
        </field>

        <field name="priority" formElement="select">
            <settings>
                <dataType>string</dataType>
                <label translate="true">Priority</label>
                <dataScope>priority</dataScope>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Wagento\Zendesk\Model\Config\Source\Ticket\Priority"/>
                    </settings>
                </select>
            </formElements>
        </field>


        <field name="description" sortOrder="70" formElement="textarea">
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">Description</label>
                <dataScope>description</dataScope>
            </settings>
        </field>
    </fieldset>

    <fieldset name="options" sortOrder="30">
        <settings>
            <collapsible>true</collapsible>
            <opened>true</opened>
            <label translate="true">Add Options</label>
        </settings>
        <container name="add_order" sortOrder="10">
            <htmlContent name="html_content">
                <block name="add_options" class="Wagento\Zendesk\Block\Adminhtml\Ticket\Edit\Tab\Options" />
            </htmlContent>
        </container>
    </fieldset>

</form>
