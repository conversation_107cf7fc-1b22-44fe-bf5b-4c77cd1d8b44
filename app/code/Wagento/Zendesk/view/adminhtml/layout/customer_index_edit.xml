<?xml version="1.0"?>
<!--
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="customer_form">
            <block class="Wagento\Zendesk\Block\Adminhtml\Customer\Tab\Tickets" name="customer_ticket_tab_view"
                   ifconfig="zendesk/ticket/backend/customer_view"
                   template="Magento_Customer::tab/view.phtml">
                <arguments>
                    <argument name="sort_order" xsi:type="number">40</argument>
                    <argument name="tab_label" xsi:type="string" translate="true">Support Tickets</argument>
                </arguments>
                <block class="Wagento\Zendesk\Block\Adminhtml\Customer\Tickets" name="customer_tickets_list"
                       template="Wagento_Zendesk::tickets.phtml" output="1"/>
            </block>
        </referenceBlock>
    </body>
</page>
