<?xml version="1.0"?>
<!--
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
-->

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="order_tab_info">
            <action method="setTemplate" ifconfig="zendesk/ticket/backend/order_view">
                <argument name="template" xsi:type="string">Wagento_Zendesk::order/view/tab/info.phtml</argument>
            </action>

            <block class="Wagento\Zendesk\Block\Adminhtml\Order\Tickets"
                   name="order_tickets_list"
                   template="Wagento_Zendesk::tickets.phtml"/>
        </referenceBlock>
    </body>
</page>
