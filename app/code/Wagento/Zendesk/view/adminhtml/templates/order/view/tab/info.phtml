<?php
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php /** @var $block \Magento\Sales\Block\Adminhtml\Order\View\Tab\Info */ ?>
<?php $_order = $block->getOrder() ?>

<div id="order-messages">
    <?= $block->getChildHtml('order_messages') ?>
</div>

<?= $block->getChildHtml('order_info') ?>
<input type="hidden" name="order_id" value="<?= /* @escapeNotVerified */ $_order->getId() ?>"/>

<section class="admin__page-section order-view-billing-shipping">
    <div class="admin__page-section-title">
        <span class="title"><?= /* @escapeNotVerified */ __('Payment &amp; Shipping Method') ?></span>
    </div>
    <div class="admin__page-section-content">
        <div class="admin__page-section-item order-payment-method<?php if ($_order->getIsVirtual()): ?> order-payment-method-virtual<?php endif; ?>">
            <?php /* Payment Method */ ?>
            <div class="admin__page-section-item-title">
                <span class="title"><?= /* @escapeNotVerified */ __('Payment Information') ?></span>
            </div>
            <div class="admin__page-section-item-content">
                <div class="order-payment-method-title"><?= $block->getPaymentHtml() ?></div>
                <div class="order-payment-currency"><?= /* @escapeNotVerified */ __('The order was placed using %1.', $_order->getOrderCurrencyCode()) ?></div>
                <div class="order-payment-additional">
                    <?= $block->getChildHtml('order_payment_additional') ?>
                    <?= $block->getChildHtml('payment_additional_info') ?>
                </div>
            </div>
        </div>
        <?= $block->getChildHtml('order_shipping_view') ?>
    </div>
</section>

<?= $block->getChildHtml('order_additional_info') ?>

<?= $block->getGiftOptionsHtml() ?>

<section class="admin__page-section">
    <?= $block->getChildHtml('order_tickets_list') ?>
</section>

<section class="admin__page-section">
    <div class="admin__page-section-title">
        <span class="title"><?= /* @escapeNotVerified */ __('Items Ordered') ?></span>
    </div>
    <?= $block->getItemsHtml() ?>
</section>

<section class="admin__page-section">
    <div class="admin__page-section-title">
        <span class="title"><?= /* @escapeNotVerified */ __('Order Total') ?></span>
    </div>
    <div class="admin__page-section-content">
        <div class="admin__page-section-item order-comments-history">
            <div class="admin__page-section-item-title">
                <span class="title"><?= /* @escapeNotVerified */ __('Notes for this Order') ?></span>
            </div>
            <?= $block->getChildHtml('order_history') ?>
        </div>

        <div class="admin__page-section-item order-totals">
            <div class="admin__page-section-item-title">
                <span class="title"><?= /* @escapeNotVerified */ __('Order Totals') ?></span>
            </div>
            <?= $block->getChildHtml('order_totals') ?>
        </div>
    </div>
</section>

<?= $block->getChildHtml('popup_window') ?>

<script>
require([
    "prototype",
    "Magento_Sales/order/giftoptions_tooltip"
], function(){

//<![CDATA[
    /**
     * Retrieve gift options tooltip content
     */
    function getGiftOptionsTooltipContent(itemId) {
        var contentLines = [];
        var headerLine = null;
        var contentLine = null;

        $$('#gift_options_data_' + itemId + ' .gift-options-tooltip-content').each(function (element) {
            if (element.down(0)) {
                headerLine = element.down(0).innerHTML;
                contentLine = element.down(0).next().innerHTML;
                if (contentLine.length > 30) {
                    contentLine = contentLine.slice(0,30) + '...';
                }
                contentLines.push(headerLine + ' ' + contentLine);
            }
        });
        return contentLines.join('<br/>');
    }
    giftOptionsTooltip.setTooltipContentLoaderFunction(getGiftOptionsTooltipContent);
    window.getGiftOptionsTooltipContent = getGiftOptionsTooltipContent;
//]]>

});
</script>
