<?php
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
// @codingStandardsIgnoreFile
/** @var \Wagento\Zendesk\Block\System\Config\Form\Oauth $block */
$scope = $block->getScopeConfig();
?>
<?php if ($block->hasToken()): ?>
    <script>
        require([
            'jquery',
            'prototype'
        ], function (jQuery) {
            jQuery(document).ready(function () {
                jQuery('#revoke_oauth').click(function () {
                    new Ajax.Request('<?php echo $block->getRevokeAjaxUrl() ?>', {
                        parameters: {
                            "scope": '<?php echo $scope['scope'] ?>',
                            "scopeId": '<?php echo $scope['scopeId'] ?>'
                        },
                        onSuccess: function (response) {
                            jQuery('#config-edit-form').submit();
                        }
                    });
                });
            });
        });
    </script>
    <?= $block->getRevokeOauthButtonHtml() ?>
<?php else: ?>
    <script>
        require([
            'jquery',
            'mage/url',
            'prototype'
        ], function (jQuery, url) {

            jQuery(document).ready(function () {
                jQuery('#oauth').click(function () {
                    const regex = /^$|\s+/;
                    var client_id = jQuery('#zendesk_config_client_id').val();
                    var subdomain = jQuery('#zendesk_config_zendesk_subdomain').val();
                    var client_secret = jQuery('#zendesk_config_secret').val();

                    if ((regex.exec(client_id)) === null && (regex.exec(subdomain)) === null && (regex.exec(client_secret)) === null) {
                        /*******************[ WINDOW ]*******************/
                        var w = 800, h = 600;
                        var left = (window.innerWidth / 2) - (w / 2), top = (window.innerHeight / 2) - (h / 2);
                        var windowConfig = 'width = ' + w + ', height = ' + h + ', top =' + top + ', left =' + left;
                        var win = window.open('', '', windowConfig);

                        new Ajax.Request('<?php echo $block->getOauthAjaxUrl() ?>', {
                            parameters: {
                                "client_id": client_id,
                                "subdomain": subdomain,
                                "client_secret": client_secret,
                                "scope": '<?php echo $scope['scope'] ?>',
                                "scopeId": '<?php echo $scope['scopeId'] ?>'
                            },
                            onSuccess: function (response) {
                                var res = response.responseJSON;

                                if (res.error) {
                                    win.close();
                                    alert(res.error);
                                } else if (res.install) {
                                    if (!win || win.closed) {
                                        alert("Oauth window was closed, try again.");
                                    } else {
                                        jQuery('#oauth').prop('disabled', true);
                                        win.focus();
                                        /*******************[ URL ]*******************/
                                        var route = 'zendesk/index/index/';
                                        url.setBaseUrl('<?php echo $block->getFrontBaseUrl();?>');
                                        var redirectUrl = url.build(route + res.hash);

                                        /*******************[ OAUTH ]*******************/
                                        var checkConnect = setInterval(function () {
                                            if (!win || !win.closed) return;
                                            clearInterval(checkConnect);
                                            jQuery('#config-edit-form').submit();
                                        }, 100);

                                        win.location.href = redirectUrl;
                                    }
                                }
                            }
                        });
                    } else {
                        alert('Please make sure that subdomain, client id and secret are not empty and have no spaces.');
                    }
                });
            });
        });
    </script>
    <?= $block->getOauthButtonHtml() ?>
<?php endif; ?>

<!-- Fix for Magento 2.0 TODO: review is this still affects-->
<input type="hidden" id="zendesk_ticket_order_field_id" value=""/>
