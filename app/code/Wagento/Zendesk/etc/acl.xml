<?xml version="1.0"?>
<!--
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
    <acl>
        <resources>
            <resource id="Magento_Backend::admin">

                <!--Stores > Configuration > Zendesk-->
                <resource id="Magento_Backend::stores">
                    <resource id="Magento_Backend::stores_settings">
                        <resource id="Magento_Config::config">
                            <resource id="Wagento_Zendesk::config_zendesk"
                                      title="Zendesk Section"
                                      translate="title"
                                      sortOrder="0"/>
                        </resource>
                    </resource>
                </resource>

                <resource id="Wagento_Zendesk::zendesk"
                          title="Zendesk"
                          translate="title"
                          sortOrder="50">
                    <resource id="Wagento_Zendesk::zendesk_config"
                              title="Configuration"
                              translate="title"
                              sortOrder="0"/>
                    <resource id="Wagento_Zendesk::zendesk_ticket"
                              title="Tickets"
                              translate="title"
                              sortOrder="10"/>
                    <resource id="Wagento_Zendesk::zendesk_api"
                              title="REST API"
                              translate="title"
                              sortOrder="20"/>
                </resource>
            </resource>
        </resources>
    </acl>
</config>
