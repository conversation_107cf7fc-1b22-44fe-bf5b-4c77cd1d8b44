<?xml version="1.0"?>
<!--
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!--  Zendesk Connection HealtCheck  -->
    <type name="Magento\Framework\Notification\MessageList">
        <arguments>
            <argument name="messages" xsi:type="array">
                <item name="zendesk_rest_connection" xsi:type="string">Wagento\Zendesk\Model\System\Message\ConfigureAccount</item>
            </argument>
        </arguments>
    </type>

</config>
