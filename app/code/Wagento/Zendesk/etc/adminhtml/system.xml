<?xml version="1.0"?>
<!--
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">

    <system>
        <!--TAB NAME-->
        <tab id="zendesk" translate="label" sortOrder="150">
            <label>Zendesk</label>
        </tab>

        <!--CONFIG SECTION-->
        <section id="zendesk" translate="label" sortOrder="0"
                 showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Zendesk Support</label>
            <tab>zendesk</tab>
            <resource>Wagento_Zendesk::config_zendesk</resource>

            <!-- GENERAL -->
            <group id="config" translate="label"
                   showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Configuration</label>

                <field id="zendesk_subdomain" translate="label comment" type="text" sortOrder="0"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zendesk Subdomain</label>
                    <comment><![CDATA[https://<strong>yoursubdomain</strong>.zendesk.com/]]></comment>
                </field>

                <field id="auth_type" translate="label comment" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Authentication Type</label>
                    <source_model>Wagento\Zendesk\Model\Config\Source\Authentication\Type</source_model>
                    <comment>
                        <![CDATA[
                        <div>
                            <strong>API token:</strong>
                            Requires your email and
                            <a href="https://support.zendesk.com/hc/en-us/articles/226022787-Generating-a-new-API-token-" target="_blank">
                            Api Token</a>
                        </div>
                        <div>
                            <strong>OAuth access token:</strong>
                            The safest option, but requires many steps, follow guide
                            <a href="https://support.zendesk.com/hc/en-us/articles/203663836-Using-OAuth-authentication-with-your-app" target="_blank">
                            Registering your application with Zendesk > To register your application</a>
                        </div>
                        ]]>
                    </comment>
                </field>

                <!-- EMAIL / TOKEN -->
                <field id="api_email" translate="label comment" type="text" sortOrder="2"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email</label>
                    <comment>Email</comment>
                    <validate>validate-email</validate>
                    <depends>
                        <field id="auth_type">api_token</field>
                    </depends>
                </field>
                <field id="api_token" translate="label comment" type="obscure" sortOrder="3"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>API Token</label>
                    <comment>API Token</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="auth_type">api_token</field>
                    </depends>
                </field>

                <!-- OAUTH -->
                <field id="oauth_info" translate="label comment" type="label" sortOrder="5" showInDefault="1">
                    <label>OAuth Info</label>
                    <comment>
                        <![CDATA[
                    <div class="message">
                        Create an Oauth app in your Zendesk account: Admin > Channels > API.
                        <br>If you don't have an account create a new one <a href='https://www.zendesk.com/register/#getstarted' target="_blank">here</a>.
                    </div>
                    ]]>
                    </comment>
                    <depends>
                        <field id="auth_type">oauth_token</field>
                    </depends>
                </field>

                <field id="m2_domain" translate="label comment" type="label" sortOrder="5" showInDefault="1">
                    <label>Redirect URLs for Zendesk</label>
                    <frontend_model>Wagento\Zendesk\Block\System\Config\Form\ZdUrlList</frontend_model>
                    <depends>
                        <field id="auth_type">oauth_token</field>
                    </depends>
                </field>

                <field id="client_id" translate="label comment" type="text" sortOrder="10" showInDefault="1">
                    <label>Client Id</label>
                    <comment>API Unique Identifier also called Unique Identifier.</comment>
                    <depends>
                        <field id="auth_type">oauth_token</field>
                    </depends>
                </field>

                <!--obscure-->
                <field id="secret" translate="label comment" type="obscure" sortOrder="20" showInDefault="1">
                    <label>Secret</label>
                    <comment>API Secret</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <depends>
                        <field id="auth_type">oauth_token</field>
                    </depends>
                </field>

                <field id="oauth_btn" type="button" sortOrder="30" showInDefault="1">
                    <frontend_model>Wagento\Zendesk\Block\System\Config\Form\Oauth</frontend_model>
                    <depends>
                        <field id="auth_type">oauth_token</field>
                    </depends>
                </field>

                <field id="token" type="label" sortOrder="40" showInDefault="1">
                    <label>Authorization Token</label>
                    <frontend_model>Wagento\Zendesk\Block\System\Config\Form\Token</frontend_model>
                    <depends>
                        <field id="auth_type">oauth_token</field>
                    </depends>
                </field>

                <field id="connection_healtcheck_admin" type="select" sortOrder="60"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Connection Heltcheck On Admin</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled, this will validate connection credentials doing a test api call every time an admin page is viewed. So this may cause performance issues. Recommende value by dafult is No.</comment>
                </field>
            </group>

            <!-- TICKETS CONFIGURATION-->
            <group id="ticket" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Ticket Configuration</label>

                <field id="order_field_id" type="label" sortOrder="0" translate="label comment"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Ticket Order Number Field Id</label>
                    <comment>
                        Used to link order in Magento with tickets in Zendesk Support.
                    </comment>
                </field>

                <field id="type" type="select" sortOrder="0" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Default Value for Type</label>
                    <source_model>Wagento\Zendesk\Model\Config\Source\Ticket\Type</source_model>
                </field>

                <field id="status" type="select" sortOrder="0" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Default Value for Status</label>
                    <source_model>Wagento\Zendesk\Model\Config\Source\Ticket\Status</source_model>
                </field>

                <field id="priority" type="select" sortOrder="0" showInDefault="1" showInWebsite="1"
                       showInStore="1">
                    <label>Default Value for Priority</label>
                    <source_model>Wagento\Zendesk\Model\Config\Source\Ticket\Priority</source_model>
                </field>

                <group id="frontend" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Frontend Options</label>
                    <field id="customer_account" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                        <label>Show Ticket List In Customer Account Menu</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="customer_account_recent" type="select" showInDefault="1" showInWebsite="1"
                           showInStore="1">
                        <label>Show Recent Ticket In Customer Dashboard</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="customer_account_order_ticket" type="select" showInDefault="1" showInWebsite="1"
                           showInStore="1">
                        <label>Show Order’s Ticket In Customer Account</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="customer_account_open_ticket" type="select" showInDefault="1" showInWebsite="1"
                           showInStore="1">
                        <label>Add “Open Ticket” Action To Order List</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                        <comment>This feature is only available if "Include Order Number" is set as yes.</comment>
                    </field>
                    <field id="customer_ticket_view_comment" type="select" showInDefault="1" showInWebsite="1"
                           showInStore="1">
                        <label>Customer Can Comment In Ticket</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="contact_us" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                        <label>Create Ticket From Contact Us</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                </group>
                <group id="backend" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Backend Options</label>
                    <field id="customer_view" type="select" showInDefault="1" showInWebsite="1"
                           showInStore="1">
                        <label>Show Support Tickets In Customer View</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="order_view" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                        <label>Show Order’s Ticket List in Order View</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                </group>
            </group>

            <!-- CUSTOMER CONFIGURATION -->
            <group id="customer" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>End-User Configuration</label>
                <comment>
                    <![CDATA[
                    <div class="message message-warning warning">
                        Don't forget to save config, before sync customers.
                    </div>
                    ]]>
                </comment>
                <field id="enable_attribute_sync" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Synchronize Customers</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Synchronize Magento Customer Accounts With Zendesk End-Users</comment>
                </field>
                <field id="attributes_to_sync" type="multiselect" showInDefault="1" advanced="true">
                    <label>Attributes To Sync</label>
                    <comment>Select Attributes you would like to sync, note Email and Name will be synced by default.
                    </comment>
                    <source_model>Wagento\Zendesk\Model\Config\Source\Customer\UserFields</source_model>
                    <depends>
                        <field id="enable_attribute_sync">1</field>
                    </depends>
                </field>
                <field id="sync_by_cron" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Cron Sync</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>
                        <![CDATA[
                        <strong>Recommended:</strong> This will sync missing customer every 15 mins
                        ]]>
                    </comment>
                    <depends>
                        <field id="enable_attribute_sync">1</field>
                    </depends>
                </field>
                <field id="sync_customer_btn" type="button" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Wagento\Zendesk\Block\System\Config\Form\Customersync</frontend_model>
                    <comment>Note this can take long time arround 2h for 300 customers.</comment>
                    <depends>
                        <field id="enable_attribute_sync">1</field>
                    </depends>
                </field>
            </group>

            <group id="help_center" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Help Center Configuration</label>
                <field id="hc_link" type="select" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Link To Zendesk Support In Magento Footer</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="include_web_widget" translate="label" type="select" sortOrder="50" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Include Web Widget</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <!--
                <field id="enable_sso" translate="label" type="select" sortOrder="50" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Enable SSO</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                -->
            </group>

            <!-- M2 APP IN ZENDESK CONFIG -->
            <group id="zendesk_m2app" showInDefault="1" showInWebsite="1" translate="label comment">
                <label>Magento 2 Connector by Wagento in Zendesk</label>

                <field id="install_app_btn" type="button" sortOrder="10" translate="label comment"
                       showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Wagento\Zendesk\Block\System\Config\Form\AppInstallation</frontend_model>
                    <comment>Install Magento 2 App</comment>
                </field>

                <field id="order_qty_limit" translate="label" type="select" sortOrder="20" showInDefault="1"
                       showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Order Number Display Limit</label>
                    <source_model>Wagento\Zendesk\Model\Config\Source\Customer\OrderNumberLimit</source_model>
                    <comment>Set number of recent orders that will be displayed in Zendesk Dashboard.</comment>
                </field>

                <field id="customer_fields" translate="label" type="multiselect" sortOrder="30" showInDefault="1"
                       showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Customer Fields</label>
                    <source_model>Wagento\Zendesk\Model\Config\Source\App\CustomerFields</source_model>
                    <comment>Selected fields will be displayed on widget.</comment>
                </field>
                <field id="order_fields" translate="label" type="multiselect" sortOrder="40" showInDefault="1"
                       showInWebsite="1" showInStore="1" canRestore="1">
                    <label>Order Fields</label>
                    <source_model>Wagento\Zendesk\Model\Config\Source\App\OrderFields</source_model>
                    <comment>Selected fields will be displayed on widget.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
