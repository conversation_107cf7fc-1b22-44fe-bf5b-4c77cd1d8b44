<?xml version="1.0"?>
<!--
/**
 * Copyright Wagento Creative LLC ©, All rights reserved.
 * See COPYING.txt for license details.
 */
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">

    <!-- Observer when creating customer -->
    <event name="customer_save_after_data_object">
        <observer name="customer_sync" instance="Wagento\Zendesk\Observer\Sync"/>
    </event>

    <!-- Observer when synced manually -->
    <event name="customer_admin_sync">
        <observer name="customer_sync" instance="Wagento\Zendesk\Observer\Sync"/>
    </event>

</config>