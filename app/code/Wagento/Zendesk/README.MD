## FOR SUPPORT PLEASE CONTACT THE FOLLOWING
<EMAIL>

## Introduction
This official Zendesk module developed by wagento.

## Support
If you are facing any issue with module installation and configuration please send an <NAME_EMAIL>

## Changelog
Based in `setup_version`
v1.0.9 (composer 220.0.9)
- Updated Zendesk APP installer

v1.0.8 (composer 220.0.8)
- Added reply ticket from admin
- Added enable disable widget fields
- Magento 2.4 support
- php 7.4 support

v1.0.7 (composer 220.0.7)
- Added multi store support, using Api Token only.
- Fixed FLAG on customer sync
- Added phone_number as User attribute

v1.0.6
- Added support for Api Token Authentication for Zendesk API

v1.0.4
- Added support for M2.3
- Fixed template issue when enabling Open Ticket on recent order list
- Added sync customer by cron
- Added callback url information for zendesk API

v1.0.3 Skipped due to issues on MarketPlace release

v1.0.2
- Fixed Customer grid from `Zendesk > Tickets > Create Ticket > Select Customer`.
- Fixed Order grid from `Zendesk > Tickets > Create Ticket > Select Order`.
- Fixed Customer can't see comment box even if "Customer Can Comment In Ticket" is enabled in `System > Zendesk > Zendesk Support > Ticket Configuration > Frontend Options > Customer Can Comment In Ticket`.
- Fixed Zendesk can't open OAuth poup when backend has custom url.
- Improved Order Ticket Field always will be created on zendesk platform and synced to magento store.
- Improved Config Cache clean strategy and clean execution when config is saved to DB.


v1.0.1
- Fixed Authorization problem when user clicks Authorize button in admin
- Improved `App Installation` algorithm, now is smarter and will always update installation infromation.
- Fixed 'Customer Sync' ajax call.
- Fixed Customer Session validation for Ticket Form when magento's cache is enabled.
- Included code improvement from pull request
    - Removed unused dependecies
    - Changed sql select query to load order by customer email


v1.0.0
- Initial module integration for Zendesk
