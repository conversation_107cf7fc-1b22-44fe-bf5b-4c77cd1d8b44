<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\ViiGraphql\Rewrite\Magento\GiftCardAccountGraphQl\Model\Resolver;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Exception\CouldNotSaveException;

use Magento\GiftCardAccount\Model\Giftcardaccount as GiftCardAccount;
use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\MaskedQuoteIdToQuoteIdInterface;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;
use Magento\GiftCardAccount\Api\GiftCardAccountManagementInterface;
use Magento\GiftCardAccount\Api\Exception\TooManyAttemptsException;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\GiftCardAccount\Api\GiftCardAccountRepositoryInterface;
use Magento\GiftCardAccount\Model\Service\GiftCardAccountManagement;
use Magento\Framework\Exception\LocalizedException;
// use Magento\GiftCardAccount\Model\Giftcardaccount as ModelGiftCardAccount;
use Magento\Quote\Api\CartTotalRepositoryInterface;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\GiftCardAccountGraphQl\Model\Money\Formatter as MoneyFormatter;
use Magento\Quote\Model\Cart\TotalSegment;

class GetAppliedGiftCardsFromCart extends \Magento\GiftCardAccountGraphQl\Model\Resolver\GetAppliedGiftCardsFromCart
{
    protected $cartRepository;
    protected $maskedQuoteIdToQuoteId;

    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    protected $quoteRepository;

    /**
     * @var \Magento\GiftCardAccount\Helper\Data
     */
    protected $giftCardHelper;

    /**
     * @var \Totaltools\Vii\Model\RewriteGiftCardAccountFactory
     */
    protected $giftCardAccountFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var GetCartForUser
     */
    private $getCartForUser;

    /**
     * @var GiftCardAccountRepositoryInterface
     */
    private $repo;

    /**
     * @var GiftCardAccountManagementInterface
     */
    private $giftCardAccountManagement;

    /**
     * @var SearchCriteriaBuilder
     */
    private $criteriaBuilder;


    /**
     * @var CartTotalRepositoryInterface
     */
    private $cartTotalRepository;

    /**
     * @var Json
     */
    private $json;

    /**
     * @var MoneyFormatter
     */
    private $moneyFormatter;

    /**
     * @var GiftCardAccountRepositoryInterface
     */
    private $giftCardAccountRepository;

    /**
     * RewriteGiftCardAccountManagement constructor.
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\GiftCardAccount\Helper\Data $giftCardHelper
     * @param \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param GetCartForUser $getCartForUser
     * @param GiftCardAccountManagementInterface $giftCardAccountManagement
     * @param GiftCardAccountRepositoryInterface|null $repo
     * @param SearchCriteriaBuilder|null $criteriaBuilder
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\GiftCardAccount\Helper\Data $giftCardHelper,
        \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        CartRepositoryInterface $cartRepository,
        GetCartForUser $getCartForUser,
        GiftCardAccountManagementInterface $giftCardAccountManagement,
        MaskedQuoteIdToQuoteIdInterface $maskedQuoteIdToQuoteId,
        CartTotalRepositoryInterface $cartTotalRepository,
        Json $json,
        GiftCardAccountRepositoryInterface $giftCardAccountRepository,
        SearchCriteriaBuilder $criteriaBuilder,
        MoneyFormatter $moneyFormatter,
        ?GiftCardAccountRepositoryInterface $repo = null
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->quoteRepository = $quoteRepository;
        $this->giftCardHelper = $giftCardHelper;
        $this->giftCardAccountFactory = $giftCardAccountFactory;
        $this->storeManager = $storeManager;
        $this->cartRepository = $cartRepository;
        $this->getCartForUser = $getCartForUser;
        $this->giftCardAccountManagement = $giftCardAccountManagement;
        $this->maskedQuoteIdToQuoteId = $maskedQuoteIdToQuoteId;
        $this->repo = $repo ?? ObjectManager::getInstance()->get(GiftCardAccountRepositoryInterface::class);
        
        $this->criteriaBuilder = $criteriaBuilder ?? ObjectManager::getInstance()->get(SearchCriteriaBuilder::class);
        $this->cartTotalRepository = $cartTotalRepository;
        $this->json = $json;
        $this->giftCardAccountRepository = $giftCardAccountRepository;
        $this->criteriaBuilder = $criteriaBuilder;
        $this->moneyFormatter = $moneyFormatter;
        parent::__construct($giftCardAccountManagement, $cartTotalRepository, $json, $giftCardAccountRepository, $criteriaBuilder,$moneyFormatter);
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($value['model'])) {
            throw new LocalizedException(__('"model" value should be specified'));
        }
        $store = $context->getExtensionAttributes()->getStore();
        $cart = $value['model'];
        $cartId = $cart->getId();
         /** @var  \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->getActive($cartId);
        if (!$quote->getItemsCount()) {
            throw new CouldNotDeleteException(__('The "%1" Cart doesn\'t contain products.', $cartId));
        }

        $pin = '';
        // find pin of gift card on quote
        $quoteCards = $this->giftCardHelper->getCards($quote);
        $giftCardAccountAppliedToCart = $this->giftCardAccountManagement->getListByQuoteId($cartId);
        $giftCardAccounts = $this->getByCodes($giftCardAccountAppliedToCart->getGiftCards());
        $cartGiftCardSegments = $this->getGiftCardSegmentsFromCart($cartId);
        $appliedGiftCards = [];
        foreach ($giftCardAccounts as $giftAccount) {
            $appliedBalance = $cartGiftCardSegments[$giftAccount->getCode()][ModelGiftCardAccount::BASE_AMOUNT] ?? 0;

            $appliedGiftCards[] = [
                'code' => $giftAccount->getCode(),
                'current_balance' => $this->moneyFormatter->formatAmountAsMoney($giftAccount->getBalance(), $store),
                'applied_balance' => $this->moneyFormatter->formatAmountAsMoney($appliedBalance, $store),
                'expiration_date' => $giftAccount->getDateExpires(),
            ];
        }
        return $appliedGiftCards;
    }

    /**
     * Get giftcard segments from the cart
     *
     * @param string $cartId
     * @return array
     * @throws NoSuchEntityException
     */
    private function getGiftCardSegmentsFromCart(string $cartId)
    {
        $cartTotal = $this->cartTotalRepository->get($cartId);
        $totalSegments = $cartTotal->getTotalSegments();
        $cartGiftCards = [];
        if (isset($totalSegments['giftcardaccount'])) {
            /** @var TotalSegment $totalSegment */
            $totalSegment = $totalSegments['giftcardaccount'];
            $extensionAttributes = $totalSegment->getExtensionAttributes();
            $giftCardsTotals = $this->json->unserialize($extensionAttributes->getGiftCards());
            if (is_array($giftCardsTotals)) {
                foreach ($giftCardsTotals as $giftCardTotal) {
                    if (isset($giftCardTotal[ModelGiftCardAccount::CODE])) {
                        $cartGiftCards[$giftCardTotal[ModelGiftCardAccount::CODE]] = $giftCardTotal;
                    }
                }
            }
        }
        return $cartGiftCards;
    }


     /**
     * Retrieve set of giftcard accounts based on the codes
     *
     * @param array $giftCardCodes
     * @return array
     */
    private function getByCodes(array $giftCardCodes): array
    {
        $found = $this->giftCardAccountRepository->getList(
            $this->criteriaBuilder->addFilter('code', $giftCardCodes, 'in')->create()
        )->getItems();
        return $found;
    }
}
