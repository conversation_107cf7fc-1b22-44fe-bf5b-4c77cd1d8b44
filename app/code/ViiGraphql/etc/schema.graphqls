
type Mutation {
    applyGiftCardToCartWithPin ( input: ApplyGiftCardInput ) : ApplyGiftCardOutput @resolver( class: "Totaltools\\ViiGraphql\\Model\\Resolver\\ApplyGiftCard")  
    removeGiftCardFromCart(input: RemoveGiftCardFromCartInput @doc(description: "An input object that specifies which gift card code to remove from the cart.")): RemoveGiftCardFromCartOutput @resolver(class: "\\Totaltools\\ViiGraphql\\Model\\Resolver\\RemoveGiftCardFromCart") @doc(description: "Removes a gift card from the cart.")
}

# type Cart {
#     applied_gift_cards: [AppliedGiftCard] @resolver(class: "\\Magento\\GiftCardAccountGraphQl\\Model\\Resolver\\GetAppliedGiftCardsFromCart") @doc(description: "An array of gift card items applied to the cart.")
# }
input ApplyGiftCardInput {
    
    cart_id: String  @doc(description: "Input cart_id.") 
    gift_card_code: String  @doc(description: "Input gift_card_code.") 
    pin: String  @doc(description: "Input pin.") 
}

type ApplyGiftCardOutput @doc(description: "Defines the possible output for the `applyGiftCardToCart` mutation.") {
    message: String @doc(description: "Describes the contents of the specified shopping cart.")
    cart: Cart! @doc(description: "The contents of the specified shopping cart.")
}

input RemoveGiftCardFromCartInput @doc(description: "Defines the input required to run the `removeGiftCardFromCart` mutation.") {
    cart_id: String! @doc(description: "The unique ID that identifies the customer's cart.")
    gift_card_code: String! @doc(description: "The gift card code to be removed to the cart.")
}

type RemoveGiftCardFromCartOutput @doc(description: "Defines the possible output for the `removeGiftCardFromCart` mutation.") {
    message: String @doc(description: "Describes the contents of the specified shopping cart.")
    cart: Cart! @doc(description: "The contents of the specified shopping cart.")
}