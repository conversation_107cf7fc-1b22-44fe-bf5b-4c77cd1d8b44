<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\ViiGraphql\Model\Resolver;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Exception\CouldNotSaveException;

use Magento\GiftCardAccount\Model\Giftcardaccount as GiftCardAccount;
use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\MaskedQuoteIdToQuoteIdInterface;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;
use Magento\GiftCardAccount\Api\GiftCardAccountManagementInterface;
use Magento\GiftCardAccount\Api\Exception\TooManyAttemptsException;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\GiftCardAccount\Api\GiftCardAccountRepositoryInterface;
use Magento\GiftCardAccount\Model\Service\GiftCardAccountManagement;

class RemoveGiftCardFromCart implements ResolverInterface
{
    protected $cartRepository;
    protected $maskedQuoteIdToQuoteId;

    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    protected $quoteRepository;

    /**
     * @var \Magento\GiftCardAccount\Helper\Data
     */
    protected $giftCardHelper;

    /**
     * @var \Totaltools\Vii\Model\RewriteGiftCardAccountFactory
     */
    protected $giftCardAccountFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var GetCartForUser
     */
    private $getCartForUser;

    /**
     * @var GiftCardAccountRepositoryInterface
     */
    private $repo;

    /**
     * @var GiftCardAccountManagementInterface
     */
    private $giftCardAccountManagement;

    /**
     * @var SearchCriteriaBuilder
     */
    private $criteriaBuilder;

    /**
     * RewriteGiftCardAccountManagement constructor.
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\GiftCardAccount\Helper\Data $giftCardHelper
     * @param \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param GetCartForUser $getCartForUser
     * @param GiftCardAccountManagementInterface $giftCardAccountManagement
     * @param GiftCardAccountRepositoryInterface|null $repo
     * @param SearchCriteriaBuilder|null $criteriaBuilder
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\GiftCardAccount\Helper\Data $giftCardHelper,
        \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        CartRepositoryInterface $cartRepository,
        GetCartForUser $getCartForUser,
        GiftCardAccountManagementInterface $giftCardAccountManagement,
        MaskedQuoteIdToQuoteIdInterface $maskedQuoteIdToQuoteId,
        ?GiftCardAccountRepositoryInterface $repo = null,
        ?SearchCriteriaBuilder $criteriaBuilder = null
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->quoteRepository = $quoteRepository;
        $this->giftCardHelper = $giftCardHelper;
        $this->giftCardAccountFactory = $giftCardAccountFactory;
        $this->storeManager = $storeManager;
        $this->cartRepository = $cartRepository;
        $this->getCartForUser = $getCartForUser;
        $this->giftCardAccountManagement = $giftCardAccountManagement;
        $this->maskedQuoteIdToQuoteId = $maskedQuoteIdToQuoteId;
        $this->repo = $repo ?? ObjectManager::getInstance()->get(GiftCardAccountRepositoryInterface::class);
        
        $this->criteriaBuilder = $criteriaBuilder ?? ObjectManager::getInstance()->get(SearchCriteriaBuilder::class);
    }

    /**
     * @inheritdoc
     */
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($args['input']['cart_id']) || empty($args['input']['cart_id'])) {
            throw new GraphQlInputException(__('Required parameter "%1" is missing', 'cart_id'));
        }
        $maskedCartId = $args['input']['cart_id'];
       
        if (!isset($args['input']['gift_card_code']) || empty($args['input']['gift_card_code'])) {
            throw new GraphQlInputException(__('Required parameter "%1" is missing', 'gift_card_code'));
        }
        $giftCardCode = $args['input']['gift_card_code'];

        $currentUserId = $context->getUserId();
        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();
        $cart = $this->getCartForUser->execute($maskedCartId, $currentUserId, $storeId);
        $cartId = $cart->getId();
        /** @var  \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->getActive($cartId);
        if (!$quote->getItemsCount()) {
            throw new CouldNotDeleteException(__('The "%1" Cart doesn\'t contain products.', $cartId));
        }

        $pin = '';
        // find pin of gift card on quote
        $quoteCards = $this->giftCardHelper->getCards($quote);
        foreach ($quoteCards as $card) {
            if ($card[ModelGiftcardaccount::CODE] == $giftCardCode) {
                $pin = $card[ModelGiftcardaccount::PIN];
            }
        }

        /** @var \Totaltools\Vii\Model\RewriteGiftcardAccount $giftCard */
        $giftCard = $this->giftCardAccountFactory->create();
        $useCache = true;
        $giftCard->loadByCode($giftCardCode, $pin, $useCache);

        try {
            $giftCard->removeFromCart(true, $quote);
        } catch (\Exception $e) {
            throw new CouldNotDeleteException(__('Could not delete gift card from quote'));
        }
        $cart = $this->getCartForUser->execute($maskedCartId, $currentUserId, $storeId);
        return [
            'message' => 'Gift card removed',
            'cart' => [
                'model' => $cart,
            ],
        ];
    }
    
}

