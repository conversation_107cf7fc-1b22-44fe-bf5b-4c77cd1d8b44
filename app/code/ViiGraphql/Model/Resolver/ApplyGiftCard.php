<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\ViiGraphql\Model\Resolver;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Framework\Exception\CouldNotSaveException;

use Magento\GiftCardAccount\Model\Giftcardaccount as GiftCardAccount;
use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\MaskedQuoteIdToQuoteIdInterface;
use Magento\QuoteGraphQl\Model\Cart\GetCartForUser;
use Magento\GiftCardAccount\Api\GiftCardAccountManagementInterface;
use Magento\GiftCardAccount\Api\Exception\TooManyAttemptsException;
use Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface;

class ApplyGiftCard implements ResolverInterface
{
    protected $cartRepository;
    protected $maskedQuoteIdToQuoteId;

    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    protected $quoteRepository;

    /**
     * @var \Magento\GiftCardAccount\Helper\Data
     */
    protected $giftCardHelper;

    /**
     * @var \Totaltools\Vii\Model\RewriteGiftCardAccountFactory
     */
    protected $giftCardAccountFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var GetCartForUser
     */
    private $getCartForUser;
    /**
     * @var GiftCardAccountManagementInterface
     */
    private $giftCardAccountManagement;

    /**
     * @var RewriteGiftCardAccountInterface
     */
    private $giftCardAccountInterface;

    /**
     * RewriteGiftCardAccountManagement constructor.
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\GiftCardAccount\Helper\Data $giftCardHelper
     * @param \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param GetCartForUser $getCartForUser
     * @param GiftCardAccountManagementInterface $giftCardAccountManagement
     * @param RewriteGiftCardAccountInterface $giftCardAccountInterface
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\GiftCardAccount\Helper\Data $giftCardHelper,
        \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        CartRepositoryInterface $cartRepository,
        GetCartForUser $getCartForUser,
        GiftCardAccountManagementInterface $giftCardAccountManagement,
        MaskedQuoteIdToQuoteIdInterface $maskedQuoteIdToQuoteId,
        RewriteGiftCardAccountInterface $giftCardAccountInterface
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->quoteRepository = $quoteRepository;
        $this->giftCardHelper = $giftCardHelper;
        $this->giftCardAccountFactory = $giftCardAccountFactory;
        $this->storeManager = $storeManager;
        $this->cartRepository = $cartRepository;
        $this->getCartForUser = $getCartForUser;
        $this->giftCardAccountManagement = $giftCardAccountManagement;
        $this->maskedQuoteIdToQuoteId = $maskedQuoteIdToQuoteId;
        $this->giftCardAccountInterface = $giftCardAccountInterface;
    }

    /**
     * @inheritdoc
     */
    // public function resolve(
    //     Field $field,
    //     $context,
    //     ResolveInfo $info,
    //     array $value = null,
    //     array $args = null
    // ) {
    //     // return implode(',', $args);
    //      /** @var  \Magento\Quote\Model\Quote $quote */
    //      if (empty($args['input']['cart_id'])) {
    //         throw new GraphQlInputException(__(implode(',',$args)));
    //     }
    //     $cartId = $this->maskedQuoteIdToQuoteId->execute($args['input']['cart_id']);
    //     // $cart = $this->cartRepository->get($cartId);
    //     $quote = $this->quoteRepository->getActive($cartId);
    //      if (!$quote->getItemsCount()) {
    //          throw new NoSuchEntityException(__('Cart %1 doesn\'t contain products', $cartId));
    //      }
    //      $cardCode = $args['input']['gift_card_code'];
    //      $cardPin = $args['input']['pin'];
    //      /** @var \Totaltools\Vii\Model\RewriteGiftCardAccount $giftCard */
    //      $giftCard = $this->giftCardAccountFactory->create();
    //      $giftCard->loadByCode($cardCode, $cardPin);
    //      try {
    //          $giftCard->addToCart(true, $quote);
    //      } catch (\Exception $e) {
    //          throw new CouldNotSaveException(__($e->getMessage()));
    //      }
    //      return ['message' => 'true'];
    // }
    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($args['input']['cart_id']) || empty($args['input']['cart_id'])) {
            throw new GraphQlInputException(__('Required parameter "%1" is missing', 'cart_id'));
        }
        $maskedCartId = $args['input']['cart_id'];

        if (!isset($args['input']['gift_card_code']) || empty($args['input']['gift_card_code'])) {
            throw new GraphQlInputException(__('Required parameter "%1" is missing', 'gift_card_code'));
        }
        $giftCardCode = $args['input']['gift_card_code'];

        $currentUserId = $context->getUserId();
        $storeId = (int)$context->getExtensionAttributes()->getStore()->getId();
        $cart = $this->getCartForUser->execute($maskedCartId, $currentUserId, $storeId);
        $cartId = $cart->getId();
        $quote = $this->quoteRepository->getActive($cartId);
        $giftCardCode = $args['input']['gift_card_code'];
        $cardPin = $args['input']['pin'];
       
        $this->giftCardAccountInterface->setGiftCards([ModelGiftcardaccount::GIFT_CARDS => [$giftCardCode]]);
        $this->giftCardAccountInterface->setGiftCardPins([ModelGiftcardaccount::PIN =>   [$cardPin]]);
        

        /** @var GiftCardAccount $giftCardAccount */
        // $giftCardAccount = $this->giftCardAccountFactory->create(['data' => $data]);
        $giftCard = $this->giftCardAccountFactory->create();
        $giftCard->loadByCode($giftCardCode, $cardPin);

        try {
            // $this->giftCardAccountManagement->saveByQuoteId($cartId, $this->giftCardAccountInterface);
            $giftCard->addToCart(true, $quote);
        } catch (TooManyAttemptsException $e) {
            throw new GraphQlInputException(__($e->getMessage()), $e);
        } catch (CouldNotSaveException $e) {
            throw new GraphQlInputException(__($e->getMessage()), $e);
        }

        $cart = $this->getCartForUser->execute($maskedCartId, $currentUserId, $storeId);
        return [
            'message' => 'Gift card applied',
            'cart' => [
                'model' => $cart,
            ],
        ];
    }
}

