<?xml version="1.0"?>
<config>
    <urapidflow>
        <data_types>
            <product_extra>
                <title>Products Extra</title>
                <row_format>fixed</row_format>
                <model>Unirgy\RapidFlowPro\Model\ResourceModel\ProductExtra</model>
                <alt_model>Unirgy\RapidFlowPro\Model\ResourceModel\ProductExtraAlt</alt_model>
                <profile>
                    <import>
                        <tabs>
                            <file>
                                <title>File Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\File</block>
                            </file>
                            <csv>
                                <title>Format Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Csv</block>
                            </csv>
                            <options>
                                <title>Import Options</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\ProductExtra\ImportOptions</block>
                            </options>
                            <upload>
                                <title>Upload Files</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Upload</block>
                            </upload>
                            <reindex>
                                <title>Reindex</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Reindex</block>
                            </reindex>
                        </tabs>
                    </import>
                    <export>
                        <tabs>
                            <file>
                                <title>File Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\File</block>
                            </file>
                            <csv>
                                <title>Format Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Csv</block>
                            </csv>
                            <options>
                                <title>Export Options</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\ProductExtra\ExportOptions</block>
                            </options>
                            <conditions>
                                <title>Export Conditions</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Export\Condition</block>
                            </conditions>
                        </tabs>
                    </export>
                </profile>
            </product_extra>

            <eav_struct>
                <title>EAV Structure</title>
                <row_format>fixed</row_format>
                <model>Unirgy\RapidFlowPro\Model\ResourceModel\Eav</model>
                <profile>
                    <import>
                        <tabs>
                            <file>
                                <title>File Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\File</block>
                            </file>
                            <csv>
                                <title>Format Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Csv</block>
                            </csv>
                            <options>
                                <title>Import Options</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\Eav\ImportOptions</block>
                            </options>
                            <upload>
                                <title>Upload Files</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Upload</block>
                            </upload>
                            <reindex>
                                <title>Reindex</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Reindex</block>
                            </reindex>
                        </tabs>
                    </import>
                    <export>
                        <tabs>
                            <file>
                                <title>File Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\File</block>
                            </file>
                            <csv>
                                <title>Format Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Csv</block>
                            </csv>
                            <options>
                                <title>Export Options</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\Eav\ExportOptions</block>
                            </options>
                        </tabs>
                    </export>
                </profile>
            </eav_struct>

            <category>
                <title>Categories</title>
                <row_format>dynamic</row_format>
                <model>Unirgy\RapidFlowPro\Model\ResourceModel\Category</model>
                <profile>
                    <import>
                        <tabs>
                            <file>
                                <title>File Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\File</block>
                            </file>
                            <csv>
                                <title>Format Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Csv</block>
                            </csv>
                            <options>
                                <title>Import Options</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\Category\ImportOptions</block>
                            </options>
                            <columns>
                                <title>Import Columns</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\Category\Columns</block>
                            </columns>
                            <upload>
                                <title>Upload Files</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Upload</block>
                            </upload>
                            <reindex>
                                <title>Reindex</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Reindex</block>
                            </reindex>
                        </tabs>
                    </import>
                    <export>
                        <tabs>
                            <file>
                                <title>File Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\File</block>
                            </file>
                            <csv>
                                <title>Format Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Csv</block>
                            </csv>
                            <columns>
                                <title>Export Columns</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\Category\Columns</block>
                            </columns>
                        </tabs>
                    </export>
                </profile>
            </category>

            <enterprise>
                <title>Enterprise Data</title>
                <row_format>fixed</row_format>
                <model>Unirgy\RapidFlowPro\Model\ResourceModel\Enterprise</model>
                <restrictions>
                    <magento_edition>ee</magento_edition>
                </restrictions>
                <profile>
                    <import>
                        <tabs>
                            <file>
                                <title>File Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\File</block>
                            </file>
                            <csv>
                                <title>Format Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Csv</block>
                            </csv>
                            <options>
                                <title>Import Options</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\Enterprise\ImportOptions</block>
                            </options>
                            <upload>
                                <title>Upload Files</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Upload</block>
                            </upload>
                        </tabs>
                    </import>
                    <export>
                        <tabs>
                            <file>
                                <title>File Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\File</block>
                            </file>
                            <csv>
                                <title>Format Options</title>
                                <block>Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Csv</block>
                            </csv>
                            <options>
                                <title>Export Options</title>
                                <block>Unirgy\RapidFlowPro\Block\Adminhtml\Profile\Enterprise\ExportOptions</block>
                            </options>
                        </tabs>
                    </export>
                </profile>
            </enterprise>

        </data_types>

        <row_types>
            <EA>
                <title>EAV Attribute</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <attribute_code><col>1</col><key>1</key></attribute_code>
                    <backend_type><col>2</col></backend_type>
                    <frontend_input><col>3</col></frontend_input>
                    <frontend_label><col>4</col></frontend_label>
                    <is_required><col>5</col></is_required>
                    <is_unique><col>6</col></is_unique>
                    <entity_type><col>7</col><key>1</key></entity_type>
                </columns>
            </EA>

            <EAL>
                <title>EAV Attribute Store Specific</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <attribute_code><col>1</col><key>1</key></attribute_code>
                    <store><col>2</col><key>1</key></store>
                    <label><col>3</col></label>
                    <entity_type><col>4</col><key>1</key></entity_type>
                </columns>
                <restrictions>
                    <magento_version>1.4</magento_version>
                </restrictions>
            </EAL>

            <EAX>
                <title>EAV Attribute Extension</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <attribute_code><col>1</col><key>1</key></attribute_code>
                    <attribute_model><col>2</col></attribute_model>
                    <backend_model><col>3</col></backend_model>
                    <backend_table><col>4</col></backend_table>
                    <frontend_model><col>5</col></frontend_model>
                    <frontend_class><col>6</col></frontend_class>
                    <source_model><col>7</col></source_model>
                    <default_value><col>8</col></default_value>
                    <note><col>9</col></note>
                    <entity_type><col>10</col><key>1</key></entity_type>
                </columns>
            </EAX>

            <EAXP>
                <title>EAV Attribute Product Extension</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <attribute_code><col>1</col><key>1</key></attribute_code>
                    <is_global><col>2</col></is_global>
                    <is_visible><col>3</col></is_visible>
                    <is_searchable><col>4</col></is_searchable>
                    <is_filterable><col>5</col></is_filterable>
                    <is_comparable><col>6</col></is_comparable>
                    <is_visible_on_front><col>7</col></is_visible_on_front>
                    <is_html_allowed_on_front><col>8</col></is_html_allowed_on_front>
                    <is_used_for_price_rules><col>9</col></is_used_for_price_rules>
                    <is_filterable_in_search><col>10</col></is_filterable_in_search>
                    <used_in_product_listing><col>11</col></used_in_product_listing>
                    <used_for_sort_by><col>12</col></used_for_sort_by>
                    <apply_to><col>13</col></apply_to>
                    <is_visible_in_advanced_search><col>14</col></is_visible_in_advanced_search>
                    <position><col>15</col></position>
                    <frontend_input_renderer><col>16</col></frontend_input_renderer>
                    <is_wysiwyg_enabled><col>17</col></is_wysiwyg_enabled>
                    <is_used_for_promo_rules><col>18</col></is_used_for_promo_rules>
                    <is_required_in_admin_store><col>19</col></is_required_in_admin_store>
                    <is_used_in_grid><col>20</col></is_used_in_grid>
                    <is_visible_in_grid><col>21</col></is_visible_in_grid>
                    <is_filterable_in_grid><col>22</col></is_filterable_in_grid>
                    <search_weight><col>23</col></search_weight>
                    <additional_data><col>24</col></additional_data>
                </columns>
            </EAXP>

            <EAS>
                <title>EAV Attribute Set</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <set_name><col>1</col><key>1</key></set_name>
                    <sort_order><col>2</col></sort_order>
                    <entity_type><col>3</col><key>1</key></entity_type>
                </columns>
            </EAS>

            <EAG>
                <title>EAV Attribute Group</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <set_name><col>1</col><key>1</key></set_name>
                    <group_name><col>2</col><key>1</key></group_name>
                    <sort_order><col>3</col></sort_order>
                    <default_id><col>4</col></default_id>
                    <attribute_group_code><col>5</col></attribute_group_code>
                    <tab_group_code><col>6</col></tab_group_code>
                    <entity_type><col>7</col><key>1</key></entity_type>
                </columns>
            </EAG>

            <EASI>
                <title>EAV Attribute Within Attribute Set</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <set_name><col>1</col><key>1</key></set_name>
                    <group_name><col>2</col><key>1</key></group_name>
                    <attribute_code><col>3</col><key>1</key></attribute_code>
                    <sort_order><col>4</col></sort_order>
                    <entity_type><col>35</col><key>1</key></entity_type>
                </columns>
            </EASI>

            <EAO>
                <title>EAV Attribute Option</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <attribute_code><col>1</col><key>1</key></attribute_code>
                    <option_name><col>2</col><key>1</key></option_name>
                    <sort_order><col>3</col></sort_order>
                    <entity_type><col>4</col><key>1</key></entity_type>
                </columns>
            </EAO>

            <EAOL>
                <title>EAV Attribute Option Store Specific</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <attribute_code><col>1</col><key>1</key></attribute_code>
                    <option_name><col>2</col><key>1</key></option_name>
                    <store><col>3</col><key>1</key></store>
                    <option_label><col>4</col></option_label>
                    <entity_type><col>5</col><key>1</key></entity_type>
                </columns>
            </EAOL>
            <EAOS>
                <title>EAV Attribute Option Swatch</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <attribute_code><col>1</col><key>1</key></attribute_code>
                    <swatch_name><col>2</col><key>1</key></swatch_name>
                    <option_name><col>3</col><key>1</key></option_name>
                    <sort_order><col>4</col></sort_order>
                    <type><col>5</col></type>
                    <entity_type><col>6</col><key>1</key></entity_type>
                </columns>
            </EAOS>

            <EAOSL>
                <title>EAV Attribute Option Swatch Store Specific</title>
                <data_type>eav_struct</data_type>
                <columns>
                    <attribute_code><col>1</col><key>1</key></attribute_code>
                    <default_swatch_name><col>2</col><key>1</key></default_swatch_name>
                    <store><col>3</col><key>1</key></store>
                    <locale_swatch_name><col>4</col></locale_swatch_name>
                    <entity_type><col>5</col><key>1</key></entity_type>
                </columns>
            </EAOSL>

            <CC>
                <title>Catalog Category</title>
                <data_type>product_extra</data_type>
                <columns>
                    <url_path><col>1</col><key>1</key></url_path>
                </columns>
            </CC>

            <CP>
                <title>Catalog Product</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                </columns>
            </CP>

            <CCP>
                <title>Catalog Category Product</title>
                <data_type>product_extra</data_type>
                <columns>
                    <url_path><col>1</col><key>1</key></url_path>
                    <sku><col>2</col><key>2</key></sku>
                    <position><col>3</col></position>
                </columns>
            </CCP>

            <CPI>
                <title>Catalog Product Image</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <image_url><col>2</col><key>1</key></image_url>
                    <label><col>3</col></label>
                    <position><col>4</col></position>
                    <disabled><col>5</col></disabled>
                    <media_type><col>6</col></media_type>
                </columns>
            </CPI>

            <CPIL>
                <title>Catalog Product Image Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <image_url><col>2</col><key>1</key></image_url>
                    <store><col>3</col><key>1</key></store>
                    <label><col>4</col></label>
                    <position><col>5</col></position>
                    <disabled><col>6</col></disabled>
                </columns>
            </CPIL>

            <CPV>
                <title>Catalog Product Video</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <url><col>2</col><key>1</key></url>
                    <screenshot_url><col>3</col><key>1</key></screenshot_url>
                    <store><col>4</col><key>1</key></store>
                    <title><col>5</col></title>
                    <provider><col>6</col></provider>
                    <description><col>7</col></description>
                    <metadata><col>8</col></metadata>
                    <position><col>9</col></position>
                    <disabled><col>10</col></disabled>
                </columns>
            </CPV>

            <CPVL>
                <title>Catalog Product Video Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <screenshot_url><col>2</col><key>1</key></screenshot_url>
                    <store><col>3</col><key>1</key></store>
                    <label><col>4</col></label>
                    <position><col>5</col></position>
                    <disabled><col>6</col></disabled>
                </columns>
            </CPVL>
            <CPCO>
                <title>Catalog Product Custom Option</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <type><col>3</col></type>
                    <is_require><col>4</col></is_require>
                    <option_sku><col>5</col></option_sku>
                    <sort_order><col>6</col></sort_order>
                    <max_characters><col>7</col></max_characters>
                    <price><col>8</col></price>
                    <price_type><col>9</col></price_type>
                    <file_extension><col>10</col></file_extension>
                    <image_size_x><col>11</col></image_size_x>
                    <image_size_y><col>12</col></image_size_y>
                </columns>
            </CPCO>

            <CPCOL>
                <title>Catalog Product Custom Option Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <store><col>3</col><key>1</key></store>
                    <title><col>4</col></title>
                    <price><col>5</col></price>
                    <price_type><col>6</col></price_type>
                </columns>
            </CPCOL>

            <CPCOS>
                <title>Catalog Product Custom Option Selection</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <selection_default_title><col>3</col></selection_default_title>
                    <selection_sku><col>4</col></selection_sku>
                    <sort_order><col>5</col></sort_order>
                    <price><col>6</col></price>
                    <price_type><col>7</col></price_type>
                </columns>
            </CPCOS>

            <CPCOSL>
                <title>Catalog Product Custom Option Selection Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <selection_default_title><col>3</col><key>1</key></selection_default_title>
                    <store><col>4</col></store>
                    <title><col>5</col></title>
                    <price><col>6</col></price>
                    <price_type><col>7</col></price_type>
                </columns>
            </CPCOSL>

            <CPBO>
                <title>Catalog Product Bundle Option</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <position><col>3</col></position>
                    <type><col>4</col></type>
                    <required><col>5</col></required>
                </columns>
            </CPBO>

            <CPBOL>
                <title>Catalog Product Bundle Option Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <store><col>3</col><key>1</key></store>
                    <title><col>4</col></title>
                </columns>
            </CPBOL>

            <CPBOS>
                <title>Catalog Product Bundle Option Selection</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <selection_sku><col>3</col><key>1</key></selection_sku>
                    <position><col>4</col></position>
                    <is_default><col>5</col></is_default>
                    <selection_price_type><col>6</col></selection_price_type>
                    <selection_price_value><col>7</col></selection_price_value>
                    <selection_qty><col>8</col></selection_qty>
                    <selection_can_change_qty><col>9</col></selection_can_change_qty>
                </columns>
            </CPBOS>
            <CPBOSL>
                <title>Catalog Product Bundle Option Selection Price</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <selection_sku><col>3</col><key>1</key></selection_sku>
                    <selection_price_type><col>4</col></selection_price_type>
                    <selection_price_value><col>5</col></selection_price_value>
                    <website><col>6</col></website>
                </columns>
            </CPBOSL>

            <CPRI>
                <title>Catalog Product Related Item</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <linked_sku><col>2</col><key>1</key></linked_sku>
                    <position><col>3</col></position>
                    <qty><col>4</col></qty>
                </columns>
            </CPRI>

            <CPUI>
                <title>Catalog Product Up Sell Item</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <linked_sku><col>2</col><key>1</key></linked_sku>
                    <position><col>3</col></position>
                    <qty><col>4</col></qty>
                </columns>
            </CPUI>

            <CPXI>
                <title>Catalog Product Cross Sell Item</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <linked_sku><col>2</col><key>1</key></linked_sku>
                    <position><col>3</col></position>
                    <qty><col>4</col></qty>
                </columns>
            </CPXI>

            <CPGI>
                <title>Catalog Product Grouped Item</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <linked_sku><col>2</col><key>1</key></linked_sku>
                    <position><col>3</col></position>
                    <qty><col>4</col></qty>
                </columns>
            </CPGI>

            <CPSA>
                <title>Catalog Product Super Configurable Attribute</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <attribute_code><col>2</col><key>1</key></attribute_code>
                    <position><col>3</col></position>
                    <label><col>4</col></label>
                </columns>
            </CPSA>

            <CPSAL>
                <title>Catalog Product Super Configurable Attribute Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <attribute_code><col>2</col><key>1</key></attribute_code>
                    <store><col>3</col></store>
                    <label><col>4</col></label>
                </columns>
            </CPSAL>
            <CPSI>
                <title>Catalog Product Super Configurable Item</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <linked_sku><col>2</col><key>1</key></linked_sku>
                </columns>
            </CPSI>

            <CPD>
                <title>Catalog Product Downloadable</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <title><col>2</col><key>1</key></title>
                    <price><col>3</col></price>
                    <max_downloads><col>4</col></max_downloads>
                    <is_shareable><col>5</col></is_shareable>
                    <sort_order><col>6</col></sort_order>
                    <link_url><col>7</col></link_url>
                    <link_file><col>8</col></link_file>
                    <link_type><col>9</col></link_type>
                    <sample_url><col>10</col></sample_url>
                    <sample_file><col>11</col></sample_file>
                    <sample_type><col>12</col></sample_type>
                </columns>
            </CPD>

            <CPDL>
                <title>Catalog Product Downloadable Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <store><col>3</col><key>1</key></store>
                    <title><col>4</col></title>
                </columns>
            </CPDL>

            <CPDP>
                <title>Catalog Product Downloadable Website Price</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <website><col>3</col><key>1</key></website>
                    <price><col>4</col></price>
                </columns>
            </CPDP>

            <CPDS>
                <title>Catalog Product Downloadable Sample</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <title><col>2</col><key>1</key></title>
                    <sort_order><col>3</col></sort_order>
                    <sample_url><col>4</col></sample_url>
                    <sample_file><col>5</col></sample_file>
                    <sample_type><col>6</col></sample_type>
                </columns>
            </CPDS>

            <CPDSL>
                <title>Catalog Product Downloadable Sample Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <default_title><col>2</col><key>1</key></default_title>
                    <store><col>3</col><key>1</key></store>
                    <title><col>4</col></title>
                </columns>
            </CPDSL>

            <CPPT>
                <title>Catalog Product Tier Prices</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <customer_group><col>2</col><key>1</key></customer_group>
                    <qty><col>3</col><key>1</key></qty>
                    <price><col>4</col></price>
                    <website><col>5</col></website>
                    <percentage_value><col>6</col></percentage_value>
                </columns>
            </CPPT>

            <GCA>
                <title>Gift Card Account</title>
                <data_type>enterprise</data_type>
                <restrictions>
                    <magento_edition>ee</magento_edition>
                </restrictions>
                <columns>
                    <code><col>1</col><key>1</key></code>
                    <balance><col>2</col><key>1</key></balance>
                    <status><col>3</col><key>1</key></status>
                    <state><col>4</col><key>1</key></state>
                    <is_redeemable><col>5</col><key>1</key></is_redeemable>
                    <date_created><col>6</col></date_created>
                    <date_expires><col>7</col></date_expires>
                    <website><col>8</col></website>
                </columns>
            </GCA>
            <GCAH>
                <title>Gift Card Account History</title>
                <data_type>enterprise</data_type>
                <restrictions>
                    <magento_edition>ee</magento_edition>
                </restrictions>
                <columns>
                    <code><col>1</col><key>1</key></code>
                    <updated_at><col>2</col><key>1</key></updated_at>
                    <action><col>3</col><key>1</key></action>
                    <balance_amount><col>4</col><key>1</key></balance_amount>
                    <balance_delta><col>5</col><key>1</key></balance_delta>
                    <additional_info><col>6</col></additional_info>
                </columns>
            </GCAH>
            <!--<CPPG>
                <title>Catalog Product Group Prices</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <customer_group><col>2</col><key>1</key></customer_group>
                    <price><col>4</col></price>
                    <website><col>5</col></website>
                </columns>
            </CPPG>-->
<!--
            <CT>
                <title>Catalog Tag</title>
                <data_type>product_extra</data_type>
                <columns>
                    <tag_name><col>1</col><key>1</key></tag_name>
                    <status><col>2</col></status>
                </columns>
            </CT>

            <CTL>
                <title>Catalog Tag Store Specific</title>
                <data_type>product_extra</data_type>
                <columns>
                    <tag_name><col>1</col><key>1</key></tag_name>
                    <store><col>2</col></store>
                    <customers><col>3</col></customers>
                    <products><col>4</col></products>
                    <uses><col>5</col></uses>
                    <historical_uses><col>6</col></historical_uses>
                    <popularity><col>7</col></popularity>
                </columns>
            </CTL>

            <CTP>
                <title>Catalog Tag Product Relation</title>
                <data_type>product_extra</data_type>
                <columns>
                    <sku><col>1</col><key>1</key></sku>
                    <tag_name><col>2</col><key>1</key></tag_name>
                    <store><col>3</col></store>
                    <active><col>4</col></active>
                    <created_at><col>5</col></created_at>
                </columns>
            </CTP>
-->
        </row_types>
    </urapidflow>
</config>
