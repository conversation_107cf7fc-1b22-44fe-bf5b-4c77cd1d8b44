<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Indexer/etc/indexer.xsd">
    <indexer id="urapidflow_urlkey_index" view_id="urapidflow_urlkey_index" class="Unirgy\RapidFlow\Model\Indexer\ProductUrlkey">
        <title translate="true">Unirgy Rapidflow Product Url Key Indexer</title>
        <description translate="true">Track product urlkey changes and update index</description>
    </indexer>
</config>
