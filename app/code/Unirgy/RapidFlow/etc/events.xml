<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="urapidflow_profile_reindex_after">
        <observer name="urapidflow" instance="Unirgy\RapidFlow\Observer\ProductUrlUpdateObserver"/>
        <observer name="urfCategoryUrlUpdate" instance="\Unirgy\RapidFlow\Observer\CategoryUrlUpdateObserver"/>
        <observer name="urfProductImageCacheFlush" instance="Unirgy\RapidFlow\Observer\ProductImageCacheFlushObserver"/>
        <observer name="urfProductCacheFlush" instance="Unirgy\RapidFlow\Observer\ProductCacheFlushObserver"/>
    </event>
    <event name="catalog_product_save_commit_after">
        <observer name="udprodupload" instance="Unirgy\RapidFlow\Observer\CatalogProductCommitAfter"/>
    </event>
    <event name="catalog_product_delete_commit_after">
        <observer name="udprodupload" instance="Unirgy\RapidFlow\Observer\CatalogProductCommitAfter"/>
    </event>
</config>
