<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="urapidflow_urlkey_index" resource="default" engine="innodb" comment="urapidflow_urlkey_index">
        <column xsi:type="varchar" name="value" length="255" nullable="true"
                comment="value"/>
        <column xsi:type="int" name="entity_id" unsigned="true" nullable="false"
                comment="entity_id"/>
        <column xsi:type="int" name="row_id" unsigned="true" nullable="false"
                comment="row_id"/>
        <column xsi:type="smallint" name="store_id" unsigned="true" nullable="false"
                comment="Store ID"/>
        <constraint xsi:type="unique" referenceId="urapidflow_URLKEY_INDEX_URLKEY_ROW_ID_STORE_ID">
            <column name="value"/>
            <column name="row_id"/>
            <column name="store_id"/>
        </constraint>
    </table>
</schema>
