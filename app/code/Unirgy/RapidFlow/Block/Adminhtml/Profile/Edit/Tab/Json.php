<?php
/**
 * Unirgy LLC
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the EULA
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://www.unirgy.com/LICENSE-M1.txt
 *
 * @category   Unirgy
 * @package    \Unirgy\RapidFlow
 * @copyright  Copyright (c) 2008-2009 Unirgy LLC (http://www.unirgy.com)
 * @license    http:///www.unirgy.com/LICENSE-M1.txt
 */

namespace Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab;

use Magento\Backend\Block\Widget\Form;
use Magento\Backend\Block\Widget\Form\Generic;
use Magento\Framework\Data\Form as DataForm;
use Unirgy\RapidFlow\Model\Source;

class Json extends Generic
{
    public function _prepareForm()
    {

        $profile = $this->_coreRegistry->registry('profile_data');

        $form = $this->_formFactory->create();
        $this->setForm($form);
        $fieldset = $form->addFieldset('json_form', ['legend' => __('Profile Configuration')]);

        if (!$profile || !$profile->getId()) {
            $fieldset->addField('json_import', 'textarea', [
                'label' => __('Import Profile Configuration'),
                'name' => 'json_import',
                'style' => 'width:500px; height:500px; font-family:Courier New;',
            ]);
        } else {
            $fieldset->addField('json_export', 'textarea', [
                'label' => __('Export Profile Configuration'),
                'name' => 'json_export',
                'readonly' => true,
                'value' => $profile->exportToJSON(),
                'style' => 'width:500px; height:500px; font-family:Courier New;',
            ]);
        }

        return parent::_prepareForm();
    }

    public function indent($json)
    {

    }
}
