<?php
namespace Unirgy\RapidFlow\Plugin;

use Magento\Catalog\Model\Product\Attribute\Repository;

class CatalogProductAction
{
    public function afterUpdateAttributes(\Magento\Catalog\Model\Product\Action $productAction, $result)
    {
        $indexerIds = [
             \Unirgy\RapidFlow\Model\Indexer\ProductUrlkey\Processor::INDEXER_ID
        ];
        $this->_hlp()->reindexPids($productAction->getProductIds(), $indexerIds);
        return $result;
    }
    /**
     * @return \Unirgy\RapidFlow\Helper\Data
     */
    protected function _hlp()
    {
        return \Magento\Framework\App\ObjectManager::getInstance()->get(\Unirgy\RapidFlow\Helper\Data::class);
    }
}
