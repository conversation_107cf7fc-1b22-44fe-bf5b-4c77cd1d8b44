/* global module, require */

module.exports = require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image')

require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image-scale')
require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image-meta')
require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image-fetch')
require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image-exif')
require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image-exif-map')
require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image-iptc')
require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image-iptc-map')
require('jquery/fileUploader/vendor/blueimp-load-image/js/load-image-orientation')
