<?php
/** @var $block \Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Columns */
$_isExport = $block->getProfile()->getProfileType() == 'export';
$_fields = $block->getColumnsFields();
$_dt = $block->getProfile()->getDataType();
?>
<div class="entry-edit-head">
    <h4 class="icon-head head-edit-form fieldset-legend"><?= __("Columns") ?></h4>
</div>

<style type="text/css">
    #columns-container .even td {
        background: #EEE !important;
    }

    #columns-container .button-container {
        float: left;
    }

    #columns-new-options {
        height: 250px;
        max-width: 70%;
    }

    #columns-new-options option.required {
        color: #A00;
        font-weight: bold;
    }

    #columns-container .action-delete {
        font-size: 1rem;
    }

    .sortable-handle {
        float: left;
        cursor: pointer;
        height: 16px;
        padding-left: 12px;
        font-weight: bold;
        text-align: right;
        background: url('<?php echo $block->getViewFileUrl('Unirgy_RapidFlow::arrow_sort_move.gif')?>') no-repeat 0 50%;
    }
</style>

<fieldset class="fieldset">
    <?php if (!$_isExport): ?>
        <div class="messages">
            <div class="message message-notice notice notice-msg">
                <div>
                    <?php echo __("For IMPORT profiles the 'Columns' section is optional, as the field information will be taken from header row in the file.") ?>
                    <br/>
                    <?php echo __("Add only fields that need a special Alias, Default Value or Format.") ?>
                    <br/>
                    <?php echo __("Order of columns is irrelevant and was implemented only for convenience.") ?>
                </div>
            </div>
        </div>
    <?php endif ?>

    <div class="grid tier form-list">
        <table id="columns-table" class="data-grid .admin__control-table" cellspacing="0" cellpadding="0">
            <thead>
            <tr class="headings">
                <th class="type-title data-grid-th" style="width:10px"><?php echo $_isExport ? '#' : '&nbsp;' ?></th>
                <th class="type-title data-grid-th" style="width:20%;"><?php echo __('Field Source') ?></th>
                <th class="type-title data-grid-th" style="width:20%;"><?php echo __('Title Alias') ?></th>
                <th class="type-title data-grid-th" style="width:20%;"><?php echo __('Default Value') ?></th>
                <th class="type-title data-grid-th" style="width:20%;"><?php echo __('Format') ?></th>
                <th class="type-title data-grid-th" style="width:20px;">&nbsp;</th>
            </tr>
            </thead>
            <tbody id="columns-container"></tbody>
            <tfoot>
            <tr>
                <td colspan="20">
                    <div class="button-container">
                        <button class="action-secondary add-select-row right" id="urf-add-columns-btn" type="button">
                            <span><?php echo __('Add New Columns') ?></span></button>
                    </div>
                    <select id="columns-new-options" multiple="multiple"
                            class="admin__control-multiselect validate-urapidflow-columns"></select>
                    <p><?php echo __('* Columns in red are required, unless all the imported entities are not new') ?></p>
                    <p><input type="checkbox" id="_allow_mutable_attributes"
                              accept="" <?php if ($block->getProfile()->getData('options/export/allow_mutable_attributes')): ?> checked="checked"<?php endif ?>
                              /> <strong><label
                                for="_allow_mutable_attributes"><?php echo __('Allow mutable attributes (same alias for different attributes). In that case first non-empty attribute value will be used') ?></label></strong>
                    </p>
                    <input type="hidden" id="allow_mutable_attributes" name="options[export][allow_mutable_attributes]"
                           value="<?php echo $block->getProfile()->getData('options/export/allow_mutable_attributes') ?>"/>
                </td>
            </tr>
            </tfoot>
        </table>
    </div>
</fieldset>

<script type="text/javascript">
    require(["jquery", "jquery/validate", "jquery/ui", "urapidflow"], function ($, Validate) {
        var columnsFields = <?php echo @json_encode($_fields) ?>;
        var isExport = <?php echo $_isExport ? 'true' : 'false' ?>;
        var requiredField = <?php echo $_dt === 'catalog_product' ? '"sku"' : ($_dt === 'catalog_category' ? '"url_path"' : 'false') ?>;

        fillColumnsLines();
        fillColumnsFieldsSelect('#columns-new-options', columnsFields);

        $("#_allow_mutable_attributes").on("click", toggleAllowMutableAttributes);
        $("#urf-add-columns-btn").on("click", addColumns);
        $("#columns-new-options").on("dblclick", addColumns);

        var $tBody = $("#columns-container");
        $tBody.sortable({
            appendTo: "parent",
            cursor: "move",
            handle: ".sortable-handle",
            opacity: 0.75,
            update: redecorateColumnsTable

        });
//        $tBody.disableSelection();
//        var sortColumns = new UnirgySortable({
//            container: '#columns-container',
//            tag: 'tr',
//            ondrop: redecorateColumnsTable
//        });

        function fillColumnsLines() {
            var columns = <?php echo @json_encode($block->getColumns()) ?>;
            for (var i = 0; i < columns.length; i++) {
                addColumnLine(columns[i]);
            }
            redecorateColumnsTable();
        }

        function fillColumnsFieldsSelect(select, columnsFields) {
            var i, j, label, optgroup, opt;
            var $select = $(select);
            for (i in columnsFields) {
                if (columnsFields.hasOwnProperty(i) == false) {
                    continue;
                }

                var $optgroup = $("<optgroup/>");
                var columnsField = columnsFields[i];
                $optgroup.attr('label', columnsField.label);
                $select.append($optgroup);
                for (j in columnsField.fields) {
                    if (columnsField.fields.hasOwnProperty(j) == false) {
                        continue;
                    }
                    var field = columnsField.fields[j];
                    label = field.frontend_label || j;
                    var $opt = $("<option/>");
                    $opt.val(j);
                    if (field.is_required == 1) {
                        $opt.addClass("required")
                    }
                    $opt.html(escapeHTML(label) + ' [' + j + ']');
                    $optgroup.append($opt);
                    $opt.on('click', function(e){
                        e.preventDefault();
                    });
                }
            }
        }

        function updatePrevHidden(e) {
            var select = e.currentTarget;
            var selected = [];

            for (i = 0; i < select.options.length; i++) {
                if (select.options[i].selected) {
                    selected.push(select.options[i].value);
                }
            }
//        $(select).previous().value = selected.join(',');
            $(select).prev().val(selected.join(','));
        }

        function toggleAllowMutableAttributes(e) {
            $('#allow_mutable_attributes').val(1*(e.target.checked));
        }
        function addColumns(e) {
            e.preventDefault();
            e.stopPropagation();
            $('#columns-new-options').find('option').each(function () {
                var el = this;
                if (el.selected) {
                    addColumnLine(el.value);
                    el.selected = false;
                }
            });
            redecorateColumnsTable();
        }

        function addColumnLine(field) {
            var format;
            var separator;
            var k = typeof field === 'string' ? field : field.field, f = {};
            if (!k) return;
            if (k.match(/\./)) {
                f = columnsFields[k.split('.')[0]].fields[k] || {};
            } else if (columnsFields['attributes'].fields[k]) {
                f = columnsFields['attributes'].fields[k];
            } else if (columnsFields['hidden'].fields[k]) {
                f = columnsFields['hidden'].fields[k];
            }
            f.attribute_code = f.attribute_code || k;
            var td, tds = '', i, j, def, label = f.frontend_label || k, v;

            tds += '<td><div class="sortable-handle"></div></td>';
            tds += '<td><input type="hidden" name="columns_post[field][]" value="' + k + '" />' + escapeHTML(label) + ' [' + k + ']</td>';

            tds += '<td><input type="text" name="columns_post[alias][]" class="input-text" value="' + (field.alias || k) + '" style="width:100px" title="<?php echo __('Title Alias') ?>"/></td>';

            def = field['default'] || '';
            if (f.attribute_code.match(/^category\./) || f.frontend_input === 'textarea') {
                tds += '<td><input type="hidden" name="columns_post[default_multiselect][]" value="1"><textarea name="columns_post[default][]" style="width:100%; height:50px" title="<?php echo __('Default Value') ?>">' + escapeHTML(def) + '</textarea></td>';
            } else if (f.frontend_input === 'select' || f.frontend_input === 'multiselect') {
                if (f.frontend_input === 'multiselect') {
                    v = def instanceof Array ? def.join(',') : def;
                    tds += '<td><input type="hidden" name="columns_post[default_multiselect][]" value="1"><input type="hidden" name="columns_post[default][]" value="' + escapeHTML(v) + '"/><select class="admin__control-multiselect default_multiselect" multiple="multiple" style="width:100%" size="8" title="<?php echo __('Default Value') ?>">';
                } else {
                    if (f.options.length === 0) {
                        tds += '<td><input type="hidden" name="columns_post[default_multiselect][]" value=""><input type="hidden" name="columns_post[default][]" value=""><select class="admin__control-select" style="width:100%" title="<?php echo __('Default Value') ?>">';
                    } else {
                        tds += '<td><input type="hidden" name="columns_post[default_multiselect][]" value=""><select class="admin__control-select" name="columns_post[default][]" style="width:100%" title="<?php echo __('Default Value') ?>">';
                    }
                }
                var defArr = def instanceof Array ? def : def.split(',');
                for (i in f.options) {
                    if (!f.options.hasOwnProperty(i)) {
                        continue;
                    }
                    if (typeof f.options[i] === 'object') {
                        tds += '<optgroup label="' + i + '">';
                        for (j in f.options[i]) {
                            if (!f.options[i].hasOwnProperty(j)) {
                                continue;
                            }
                            v = j.replace(/ $/, '');
                            tds += '<option value="' + v + '"' + (defArr.indexOf(v) !== -1 ? ' selected="selected"' : '') + '>' + escapeHTML(f.options[i][j]) + '</option>';
                        }
                        tds += '</optgroup>';
                    } else {
                        v = i.replace(/ $/, '');
                        tds += '<option value="' + v + '"' + (defArr.indexOf(v) !== -1 ? ' selected="selected"' : '') + '>' + escapeHTML(f.options[i]) + '</option>';
                    }
                }
                tds += '</select></td>';
            } else {
                tds += '<td><input type="hidden" name="columns_post[default_multiselect][]" value=""><input type="text" name="columns_post[default][]" class="input-text" value="' + escapeHTML(def) + '" style="width:95%" title="<?php echo __('Default Value') ?>"/></td>';
            }

            if (isExport && (f.frontend_input === 'media_image' || f.attribute_code === 'url_path' || f.attribute_code === 'url_key')) {
                tds += '<td><select class="admin__control-select" name="columns_post[format][]" style="width:100px" title="<?php echo __('Format') ?>">';
                tds += '<option value=""' + (!field.format ? ' selected="selected"' : '') + '><?php echo __('Raw Value') ?></option>';
                tds += '<option value="url"' + (field.format === 'url' ? ' selected="selected"' : '') + '><?php echo __('URL') ?></option>';
                tds += '</select><input type="hidden" name="columns_post[separator][]" value=""><input type="hidden" name="columns_post[delimiter][]" value=""></td>';
            } else if (isExport && f.attribute_code === 'product.configurable_parent_sku') {
                tds += '<td><span style="width: 120px"><?php echo __('Output') ?>:</span> <select class="admin__control-select" name="columns_post[format][]" style="width:100px" title="<?php echo __('Format') ?>">';
                tds += '<option value=""' + (!field.format ? ' selected="selected"' : '') + '><?php echo __('Join If Multiple Parents') ?></option>';
                tds += '<option value="url"' + (field.format === 'single' ? ' selected="selected"' : '') + '><?php echo __('First Found Parent') ?></option>';
                tds += '</select><input type="hidden" name="columns_post[delimiter][]" value=""><br/>';
                separator = field.separator || ';';
                tds += '<span style="width: 120px"><?php echo __('Separator') ?>:</span> <input type="text" name="columns_post[separator][]" class="input-text" value="' + escapeHTML(separator) + '" style="width:25px" title="<?php echo __('Separator') ?>"/></td>';
            } else if (isExport && f.backend_type === 'decimal') {
                format = field.format || '%.4f';
                tds += '<td><input type="text" name="columns_post[format][]" class="input-text" value="' + escapeHTML(format) + '" style="width:50px" title="<?php echo __('Format') ?>"/><input type="hidden" name="columns_post[separator][]" value=""><input type="hidden" name="columns_post[delimiter][]" value=""></td>';
            } else if (f.backend_type === 'datetime') {
                format = field.format || '<?php echo $block->getProfile()->getDefaultDatetimeFormat()?>';
                tds += '<td><input type="text" name="columns_post[format][]" class="input-text" value="' + escapeHTML(format) + '" style="width:100px" title="<?php echo __('Format') ?>"/><input type="hidden" name="columns_post[separator][]" value=""><input type="hidden" name="columns_post[delimiter][]" value=""></td>';
            } else if (f.frontend_input === 'multiselect') {
                separator = field.separator || ';';
                tds += '<td class="a-right nowrap"><input type="hidden" name="columns_post[format][]" value=""><?php echo __('Separator') ?>: <input type="text" name="columns_post[separator][]" class="input-text" value="' + escapeHTML(separator) + '" style="width:25px" title="<?php echo __('Separator') ?>"/>';
                if (f.attribute_code === 'category.name') {
                    var delimiter = field.delimiter || '>';
                    tds += '<br/><?php echo __('Levels') ?>: <input type="text" name="columns_post[delimiter][]" class="input-text" value="' + escapeHTML(delimiter) + '" style="width:25px" title="<?php echo __('Levels') ?>"/>';
                } else {
                    tds += '<input type="hidden" name="columns_post[delimiter][]" value="">';
                }
                tds += '</td>';
            } else {
                tds += '<td><input type="hidden" name="columns_post[format][]" value=""><input type="hidden" name="columns_post[separator][]" value=""><input type="hidden" name="columns_post[delimiter][]" value="">&nbsp;</td>';
            }

            tds += '<td><button class="action-delete delete-select-row icon-btn" type="button"><span><?php echo __('Remove') ?></span></button></td>';
            var $node = $('<tr>' + tds + '</tr>');
            $('#columns-container').append($node);


            $node.find(".delete-select-row").on("click", function () {
                deleteColumnLine(this);
            });

            $node.find(".default_multiselect").on('change', updatePrevHidden);
//            $node.find(".sortable-handle").on("mousedown", function (event) {
//                sortColumns.drag(event, this)
//            });
            $node.find(".sortable-handle").on("selectstart", function (event) {
                event.preventDefault();
                return false;
            });
        }

        function deleteColumnLine(button) {
            $(button.parentNode.parentNode).remove();
            redecorateColumnsTable();
        }

        function redecorateColumnsTable() {
            var $columnsContainer = $('#columns-container');
            var $rows = $columnsContainer.find('tr');
            $rows.each(function (idx) {
                var $tr = $(this);
                $tr.removeClass('odd even first last');
                if(idx ==  0) {
                    $tr.addClass('first');
                }

                if(idx == $rows.length - 1) {
                    $tr.addClass('last')
                }

                if(idx%2 == 0) {
                    $tr.addClass('even')
                } else {
                    $tr.addClass('odd');
                }
            });
            <?php if ($_isExport): ?>
            var colNum = 0;
            $columnsContainer.find('.sortable-handle').each(function () {
                var el = this;
                el.innerHTML = ++colNum;
            });
            <?php endif ?>
        }

        $.validator.addMethod("validateUrapidflowColumns", function (value, element) {
            //todo
            var fields = $("#columns-container").find("input"), attrsUsed = {}, aliasUsed = {}, hasRequiredFields = !requiredField, i, k, errors;

            fields.each(function () {
                var name = $(this).attr("name");
                switch (name) {
                    case 'columns_post[field][]':
                        k = $(this).val();
                        if (k == requiredField) {
                            hasRequiredFields = true;
                        }
                        attrsUsed[k] = attrsUsed[k] ? attrsUsed[k] + 1 : 1;
                        break;

                    case 'columns_post[alias][]':
                        k = $(this).val();
                        aliasUsed[k] = aliasUsed[k] ? aliasUsed[k] + 1 : 1;
                        break;
                }

            });

            errors = [];

            if (isExport) {
                if (!$('#_allow_mutable_attributes').is(":checked")) {
                    for (i in aliasUsed) {
                        if (aliasUsed[i] > 1) errors.push("Duplicate alias: " + i);
                    }
                }
                if (!hasRequiredFields) {
                    errors.push("'" + requiredField + "' attribute is required");
                }
            } else {
                for (i in attrsUsed) {
                    if (attrsUsed.hasOwnProperty(i)
                        && i != 'const.value'
                        && attrsUsed[i] > 1
                    ) {
                        errors.push("Duplicate attribute: " + i);
                    }
                }
            }
            if (errors.length) {
                alert("Errors encountered:\n\n" + errors.join("\n"));
                return false;
            }
            return true;
        }, '<?php echo __('Please correct the column issues before continuing')?>');

        $.validator.addClassRules("validate-urapidflow-columns", {validateUrapidflowColumns: true});
    });
</script>
