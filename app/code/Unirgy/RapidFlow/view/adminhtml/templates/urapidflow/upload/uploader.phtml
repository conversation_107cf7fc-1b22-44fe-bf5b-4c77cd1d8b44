<?php
/**
 * Created by pp
 * @project magento2
 */

// @codingStandardsIgnoreFile
// magento2/app/code/Unirgy/RapidFlow/view/adminhtml/templates/upload/uploader.phtml
/** @var $block \Magento\Backend\Block\Media\Uploader */
?>

<div id="<?php echo $block->getHtmlId() ?>" class="uploader">
    <span class="fileinput-button form-buttons">
        <span><?php /* @escapeNotVerified */
            echo __('Browse Files') ?></span>
        <input id="fileupload" type="file" name="<?php /* @escapeNotVerified */
        echo $block->getConfig()->getFileField() ?>"
               data-url="<?php /* @escapeNotVerified */
               echo $block->getConfig()->getUrl() ?>" multiple>
    </span>
    <div class="clear"></div>
    <script id="<?php echo $block->getHtmlId() ?>-template" type="text/x-magento-template">
        <div id="<%- data.id %>" class="file-row" style="clear: both">
            <span class="file-info"><%- data.file %> (<%- data.size %>)</span>
            <div class="progressbar-container">
                <div class="progressbar upload-progress" style="width: 0%; height: 2em; background-color: #27cc60;"></div>
            </div>
            <div class="clear"></div>
        </div>
    </script>
    <script id="<?php echo $block->getHtmlId() ?>-template-download" type="text/x-magento-template">
        <tr id="file-<%- data.id %>" class="file-row">
            <td><span class="file-success"><%- data.file %> (<%- data.size %>)</span></td>
        </tr>
    </script>
    <script id="<?php echo $block->getHtmlId() ?>-template-error" type="text/x-magento-template">
        <tr class="file-row">
            <td><span class="file-failure"><%- data.error %> (<%- data.errorcode %>)</span></td>
        </tr>
    </script>
</div>

<script>
require([
    'jquery',
    'mage/template',
    'domReady!'
], function ($, mageTemplate) {
    require([
        'jqueryFileUploader',
    ], function () {
        var $fileupload = $('#fileupload');
        $fileupload.fileupload({
            dataType: 'json',
            formData: {
                isAjax: 'true',
                form_key: FORM_KEY
            },
            sequentialUploads: true,
            acceptFileTypes: /(\.|\/)(csv|txt)$/i,
            maxFileSize: <?php /* @escapeNotVerified */ echo $block->getFileSizeService()->getMaxFileSize() ?> ,
            add: function (e, data) {
                var progressTmpl = mageTemplate('#<?php echo $block->getHtmlId(); ?>-template'),
                    fileSize,
                    tmpl;

                $.each(data.files, function (index, file) {
                    fileSize = typeof file.size == "undefined" ?
                        $.mage.__('We could not detect a size.') :
                        byteConvert(file.size);

                    data.fileId = Math.random().toString(36).substr(2, 9);

                    tmpl = progressTmpl({
                        data: {
                            name: file.name,
                            size: fileSize,
                            id: data.fileId
                        }
                    });

                    $(tmpl).appendTo('#<?php echo $block->getHtmlId() ?>');
                });

                $(this).fileupload('process', data).done(function () {
                    data.submit();
                });
            },
            done: function (e, data) {
                var progressSelector = '#' + data.fileId + ' .progressbar-container .progressbar';
                $(progressSelector).css('width', '100%');
//                console.log(data);
                var result = data.result;
                if (result && !result.hasOwnProperty('errorcode')) {
                    $(progressSelector).removeClass('upload-progress').addClass('upload-success');

                    var doneTmpl = mageTemplate('#<?php echo $block->getHtmlId(); ?>-template-download');
                    var tmpl = doneTmpl({
                        data: {
                            file: result.file,
                            name: result.name,
                            size: byteConvert(result.size),
                            id: data.fileId
                        }
                    });
                    $(tmpl).appendTo("#urf-upload-container");
                } else if (result && result.hasOwnProperty('errorcode')){
                    $(progressSelector).removeClass('upload-progress').addClass('upload-failure');
                    var errorTmpl = mageTemplate('#<?php echo $block->getHtmlId(); ?>-template-error');
                    var tmplE = errorTmpl({
                        data: {
                            error: result.error,
                            errorcode: result.errorcode
                        }
                    });
                    $(tmplE).appendTo("#urf-upload-container");
                }
                $('#' + data.fileId).remove();
            },
            progress: function (e, data) {
                var progress = parseInt(data.loaded / data.total * 100, 10);
                var progressSelector = '#' + data.fileId + ' .progressbar-container .progressbar';
                $(progressSelector).css('width', progress + '%');
            },
            fail: function (e, data) {
                var progressSelector = '#' + data.fileId + ' .progressbar-container .progressbar';
                $(progressSelector).removeClass('upload-progress').addClass('upload-failure');
            }
        });
        $fileupload.fileupload('option', {
            process: [{
                action: 'load',
                fileTypes: /^text\/(csv|txt)$/
            }, {
                action: 'save'
            }]
        });


        /**
         * Convert byte count to float KB/MB format
         *
         * @param int $bytes
         * @return string
         */
        var byteConvert = function (bytes) {
            if (isNaN(bytes)) {
                return '';
            }
            var symbols = ['bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];
            var exp = Math.floor(Math.log(bytes) / Math.log(2));
            if (exp < 1) {
                exp = 0;
            }
            var i = Math.floor(exp / 10);
            bytes = bytes / Math.pow(2, 10 * i);

            if (bytes.toString().length > bytes.toFixed(2).toString().length) {
                bytes = bytes.toFixed(2);
            }
            return bytes + ' ' + symbols[i];
        };
    });
});
</script>
