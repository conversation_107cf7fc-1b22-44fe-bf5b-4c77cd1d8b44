<?php

namespace Unirgy\RapidFlow\Observer;

use \Magento\Framework\Event\Observer;
use \Magento\Framework\Event\ObserverInterface;

class CatalogProductCommitAfter implements ObserverInterface
{
    public function execute(Observer $observer)
    {
        $indexerIds = [
             \Unirgy\RapidFlow\Model\Indexer\ProductUrlkey\Processor::INDEXER_ID
        ];
        $this->_hlp()->reindexPids([$observer->getProduct()->getId()], $indexerIds);
    }
    /**
     * @return \Unirgy\RapidFlow\Helper\Data
     */
    protected function _hlp()
    {
        return \Magento\Framework\App\ObjectManager::getInstance()->get(\Unirgy\RapidFlow\Helper\Data::class);
    }
}
