<?php

namespace Unirgy\RapidFlow\Model\ResourceModel\Indexer;

use Magento\Catalog\Model\Config;
use Magento\Framework\Indexer\Table\StrategyInterface;
use Magento\Framework\Model\ResourceModel\Db\Context;
use Magento\Indexer\Model\ResourceModel\AbstractResource;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\DB\Adapter\AdapterInterface;

class ProductUrlkey extends AbstractResource
{
    /**
     * @var Config
     */
    protected $_eavConfig;
    /**
     * @var \Unirgy\RapidFlow\Helper\Data
     */
    protected $_rfHlp;

    public function __construct(
        Config                       $eavConfig,
        \Unirgy\RapidFlow\Helper\Data $rapidflowHelper,
        Context                      $context,
        StrategyInterface            $tableStrategy
    ) {
        $this->_eavConfig = $eavConfig;
        parent::__construct($context, $tableStrategy);
        $this->_rfHlp = $rapidflowHelper;
    }

    protected $indexTbl;
    protected $productTbl;
    protected function _construct()
    {
        $this->_setResource('core');
        $this->indexTbl = $this->getTable('urapidflow_urlkey_index');
        $this->productTbl = $this->getTable('catalog_product_entity');
    }

    public function isInstalled()
    {
        /** @var \Magento\Framework\App\DeploymentConfig $deployment */
        $deployment = ObjectManager::getInstance()->get('Magento\Framework\App\DeploymentConfig');
        return $deployment->isAvailable();
    }

    public function reindexProducts($entityIds = null)
    {
        if (!$this->isInstalled()) {
            return false;
        }

        if (empty($entityIds)) {
            return false;
        }

        $conn = $this->getConnection();

        $conn->delete($this->indexTbl, ['entity_id in (?)' => $entityIds]);

        $urlkeySelect = $this->getUrlkeySelect();
        $urlkeySelect->where('e.entity_id in (?)', $entityIds);

        $urlkeyIndexInsert = $conn->insertFromSelect($urlkeySelect, $this->indexTbl, [], AdapterInterface::INSERT_IGNORE);
        $conn->query($urlkeyIndexInsert);

        return $this;
    }

    protected function getUrlkeySelect()
    {
        $conn = $this->getConnection();
        $attrId = $this->_eavConfig->getAttribute(\Magento\Catalog\Model\Product::ENTITY, 'url_key')->getAttributeId();
        $rowIdField = $this->_rfHlp->rowIdField();
        $columns = [
            'v.value',
            'e.entity_id',
            $rowIdField!='entity_id' ? 'v.'.$rowIdField : 'e.entity_id',
            'store_id'
        ];
        $select = $conn->select()->from(['v'=>$this->productTbl.'_varchar'], [])
            ->join(['e'=>$this->productTbl], "v.$rowIdField=e.$rowIdField", [])
            ->columns($columns)
            ->where('v.attribute_id=?', $attrId);
        return $select;
    }

    public function reindexAll()
    {
        if (!$this->isInstalled()) {
            return false;
        }
        $conn = $this->getConnection();
        $conn->query(sprintf('truncate %s', $this->indexTbl));
        $urlkeySelect = $this->getUrlkeySelect();
        $urlkeyIndexInsert = $conn->insertFromSelect($urlkeySelect, $this->indexTbl, [], AdapterInterface::INSERT_IGNORE);
        $conn->query($urlkeyIndexInsert);
        return $this;
    }

    public function disableTableKeys()
    {
        return $this;
    }

    public function enableTableKeys()
    {
        return $this;
    }
}
