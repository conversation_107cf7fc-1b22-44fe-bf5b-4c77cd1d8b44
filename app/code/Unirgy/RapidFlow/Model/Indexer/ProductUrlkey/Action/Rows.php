<?php

namespace Unirgy\RapidFlow\Model\Indexer\ProductUrlkey\Action;

class Rows extends \Unirgy\RapidFlow\Model\Indexer\ProductUrlkey\AbstractAction
{
    public function execute($ids)
    {
        try {
            $this->_resource->reindexProducts($ids);
        } catch (\Exception $e) {
            throw new \Magento\Framework\Exception\LocalizedException(__($e->getMessage()), $e);
        }
    }
}
