<?php

namespace Unirgy\RapidFlow\Model\Indexer\ProductUrlkey\Action;

class Full extends \Unirgy\RapidFlow\Model\Indexer\ProductUrlkey\AbstractAction
{
    public function execute($ids = null)
    {
        try {
            $this->_resource->reindexAll();
        } catch (\Exception $e) {
            throw new \Magento\Framework\Exception\LocalizedException(__($e->getMessage()), $e);
        }
    }
}
