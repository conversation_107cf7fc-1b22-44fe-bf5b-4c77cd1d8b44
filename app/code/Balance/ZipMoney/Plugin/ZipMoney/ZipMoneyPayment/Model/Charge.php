<?php
/**
 * <AUTHOR> Internet
 * @package    Balance_ZipMoney
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2018, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Balance\ZipMoney\Plugin\Zip<PERSON>oney\ZipMoneyPayment\Model;

use \Magento\Checkout\Model\Type\Onepage;

/**
 * Class Charge
 * @package Balance\ZipMoney\Plugin\ZipMoney\ZipMoneyPayment\Model
 */
class Charge extends \Zip\ZipPayment\Model\Charge
{

    /**
     * Around plugin to
     *
     * @param \Zip\ZipPayment\Model\Charge $subject
     * @param \Closure $proceed
     * @return bool
     */
    public function aroundPlaceOrder(
        \Zip\ZipPayment\Model\Charge $subject,
        \Closure $proceed
    )
    {
        $checkoutMethod = $subject->getCheckoutMethod();

        $subject->_logger->debug(
            $subject->_helper->__('Quote Grand Total:- %s Quote Customer Id:- %s Checkout Method:- %s',
                $subject->_quote->getGrandTotal(),$subject->_quote->getCustomerId(),$checkoutMethod)
        );

        $isNewCustomer = false;
        switch ($checkoutMethod) {
            case  Onepage::METHOD_GUEST:
                $subject->_prepareGuestQuote();
                break;
            case  Onepage::METHOD_REGISTER:
                $subject->_prepareNewCustomerQuote();
                $isNewCustomer = true;
                break;
            default:
                $subject->_prepareCustomerQuote();
                break;
        }

        $this->_ignoreAddressValidation($subject);
        $subject->_checkoutSession
            ->setLastQuoteId($subject->getQuote()->getId())
            ->setLastSuccessQuoteId($subject->getQuote()->getId())
            ->clearHelperData();
        $subject->_quote->collectTotals();
        $order = $subject->_quoteManagement->submit($subject->_quote);

        if ($isNewCustomer) {
            try {
                $subject->_involveNewCustomer();
            } catch (\Exception $e) {
                $subject->_logger->critical($e);
            }
        }

        $subject->_order = $order;

        if (!$order) {
            $subject->_logger->info(__('Couldnot place the order'));
            return false;
        }

        $subject->_logger->info(__('Successfull to place the order'));

        /**
         * we only want to send to customer about new order when there is no redirect to third party
         */
        $subject->_checkoutSession->start();
        // add order information to the session
        $subject->_checkoutSession
            ->setLastOrderId($order->getId())
            ->setLastRealOrderId($order->getIncrementId())
            ->setLastOrderStatus($order->getStatus());

        return $order;
    }

    /**
     * Make sure addresses will be saved without validation errors
     */
    private function _ignoreAddressValidation($subject)
    {
        $subject->_quote->getBillingAddress()->setShouldIgnoreValidation(true);
        if (!$subject->_quote->getIsVirtual()) {
            $subject->_quote->getShippingAddress()->setShouldIgnoreValidation(true);
            if (!$subject->_quote->getBillingAddress()->getEmail()) {
                $subject->_quote->getBillingAddress()->setSameAsBilling(1);
            }
        }
    }
}

