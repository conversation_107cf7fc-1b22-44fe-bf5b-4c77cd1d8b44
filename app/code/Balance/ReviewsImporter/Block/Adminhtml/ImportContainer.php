<?php
/**
 * <AUTHOR> Internet
 * @package    Balance_ReviewsImporter
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2020, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Balance\ReviewsImporter\Block\Adminhtml;

class ImportContainer extends \Magento\Backend\Block\Widget\Form\Container
{

    /**
     * Constructor
     */
    protected function _construct()
    {
        parent::_construct();
        $this->_objectId = 'entity_id';
        $this->_blockGroup = 'Balance_ReviewsImporter';
        $this->_controller = 'adminhtml';
        $this->buttonList->remove('back');
        $this->buttonList->remove('reset');
        $this->buttonList->update('save', 'label', __('Import Review'));
    }

    /**
     * Get header text
     *
     * @return \Magento\Framework\Phrase
     */
    public function getHeaderText()
    {
        return __('Reviews Importer');
    }
}