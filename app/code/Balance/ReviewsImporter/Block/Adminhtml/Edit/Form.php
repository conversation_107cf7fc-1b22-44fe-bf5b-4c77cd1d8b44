<?php
/**
 * <AUTHOR> Internet
 * @package    Balance_ReviewsImporter
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2020, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Balance\ReviewsImporter\Block\Adminhtml\Edit;

use Magento\Framework\App\Filesystem\DirectoryList;

class Form extends \Magento\Backend\Block\Widget\Form\Generic
{

    protected $appDir;

    /**
     * Form constructor.
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Data\FormFactory $formFactory
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        array $data = []
    )
    {
        parent::__construct($context, $registry, $formFactory, $data);

        $this->appDir = $context->getFilesystem()->getDirectoryRead(DirectoryList::APP)->getAbsolutePath();
    }

    /**
     * constructor
     */
    protected function _construct()
    {
        parent::_construct();
        $this->setId('reviews_importer_form');
        $this->setTitle(__('Reviews Form'));
    }

    /**
     * Build Form Elements
     * @return \Magento\Backend\Block\Widget\Form\Generic
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function _prepareForm()
    {
        /** @var \Magento\Framework\Data\Form $form */
        $form = $this->_formFactory->create(
            [
                'data' => [
                    'id' => 'edit_form',
                    'action' => $this->getUrl('balance_reviewimporter/import/upload'),
                    'method' => 'post',
                    'enctype' => 'multipart/form-data',
                ],
            ]
        );

        $fieldsets['base'] = $form->addFieldset('base_fieldset', ['legend' => __('Import CSV File')]);

        $fieldsets['base']->addField(
            'reviews_import_file',
            'file',
            [
                'name' => 'reviews_import_file',
                'label' => __('Select File to Import'),
                'title' => __('Select File to Import'),
                'required' => true,
                'class' => 'input-file'
            ]
        );

        $form->setUseContainer(true);
        $this->setForm($form);

        return parent::_prepareForm();
    }
}