<?php
/**
 * <AUTHOR> Internet
 * @package    Balance_ReviewsImporter
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2020, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Balance\ReviewsImporter\Controller\Adminhtml\Import;

use Magento\Backend\App\Response\Http\FileFactory;
use Magento\Framework\Filesystem\DirectoryList;

class Index extends \Magento\Backend\App\Action
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;

    protected $downloader;

    protected $directory;

    /**
     * Index constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param FileFactory $fileFactory
     * @param DirectoryList $directory
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        FileFactory $fileFactory,
        DirectoryList $directory
    )
    {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->downloader = $fileFactory;
        $this->directory = $directory;
    }

    /**
     * Execute
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface|\Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        $this->messageManager->addNotice('Date format on the sample CSV file is MM/DD/YYYY, For status column use 
            {1 = Approved, 2 = Pending, 3 = Not Approved}');
        return $resultPage = $this->resultPageFactory->create();
    }
}