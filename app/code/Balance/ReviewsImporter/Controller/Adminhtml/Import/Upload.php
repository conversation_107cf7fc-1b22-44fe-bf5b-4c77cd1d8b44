<?php
/**
 * <AUTHOR> Internet
 * @package    Balance_ReviewsImporter
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2020, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Balance\ReviewsImporter\Controller\Adminhtml\Import;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Controller\ResultFactory;

class Upload extends \Magento\Backend\App\Action
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;

    protected $uploaderFactory;

    protected $varDirectory;

    protected $csvProcessor;

    protected $storeId;

    protected $product;

    protected $reviewFactory;

    protected $errorArray;

    protected $reviewProductEntityId;

    protected $reviewCollectionFactory;

    protected $ratingFactory;

    protected $objectManager;

    /**
     * @var array
     */
    protected $ratings;

    private $productRepository;

    /**
     * Upload constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Magento\MediaStorage\Model\File\UploaderFactory $uploaderFactory
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Framework\File\Csv $csvProcessor
     * @param \Magento\Store\Model\StoreManager $storeManager
     * @param \Balance\ReviewsImporter\Model\ReviewFactory $reviewFactory
     * @param \Magento\Review\Model\ResourceModel\Review\CollectionFactory $reviewCollectionFactory
     * @param \Magento\Review\Model\RatingFactory $ratingFactory
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\MediaStorage\Model\File\UploaderFactory $uploaderFactory,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\File\Csv $csvProcessor,
        \Magento\Store\Model\StoreManager $storeManager,
        \Balance\ReviewsImporter\Model\ReviewFactory $reviewFactory,
        \Magento\Review\Model\ResourceModel\Review\CollectionFactory $reviewCollectionFactory,
        \Magento\Review\Model\RatingFactory $ratingFactory,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
    )
    {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->uploaderFactory = $uploaderFactory;
        $this->varDirectory = $filesystem->getDirectoryWrite(DirectoryList::VAR_DIR); // Get default 'var' directory
        $this->csvProcessor = $csvProcessor;
        $this->storeId = $storeManager->getStore()->getId();
        $this->reviewFactory = $reviewFactory;
        $this->ratingFactory = $ratingFactory;
        $this->reviewCollectionFactory = $reviewCollectionFactory;
        $this->productRepository = $productRepository;
        $this->objectManager = \Magento\Framework\App\ObjectManager::getInstance();
    }

    /**
     * Execute Method
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute()
    {
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setPath('balance_reviewimporter/import/index');

        try {
            /**
             * fileId field must be the same name as the upload_field name from the form block
             * see Balance\ReviewsImporter\Block\Adminhtml\Edit\Form
             */
            $uploader = $this->uploaderFactory->create(['fileId' => 'reviews_import_file']);
            $uploader->checkAllowedExtension('csv');
            $uploader->skipDbProcessing(true);
            $result = $uploader->save($this->getWorkingDir());

            $this->validateIfHasExtension($result);
        } catch (\Exception $e) {
            $this->messageManager->addError(__($e->getMessage()));
            return $resultRedirect;
        }

        $this->processUpload($result);

        $this->messageManager->addSuccess(__('Reviews imported'));

        return $resultRedirect;
    }

    /**
     * Validation
     * @param $result
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function validateIfHasExtension($result)
    {
        $extension = pathinfo($result['file'], PATHINFO_EXTENSION);

        $uploadedFile = $result['path'] . $result['file'];
        if (!$extension) {
            $this->varDirectory->delete($uploadedFile);
            throw new \Exception(__('The file you uploaded has no extension.'));
        }

        $sourceFile = $this->getWorkingDir() . $result['file'];
        $rows = $this->csvProcessor->getData($sourceFile);
        $header = array_shift($rows);

        if (!in_array("SKU", $header)) {
            $this->varDirectory->delete($uploadedFile);
            throw new \Exception(__('Column SKU is missing in csv file.'));
        }
        if (!in_array("Title", $header)) {
            $this->varDirectory->delete($uploadedFile);
            throw new \Exception(__('Column Title is missing in csv file.'));
        }
        if (!in_array("Nickname", $header)) {
            $this->varDirectory->delete($uploadedFile);
            throw new \Exception(__('Column Nickname is missing in csv file.'));
        }
        if (!in_array("Review", $header)) {
            $this->varDirectory->delete($uploadedFile);
            throw new \Exception(__('Column Review is missing in csv file.'));
        }
        if (!in_array("Summary_Rating", $header)) {
            $this->varDirectory->delete($uploadedFile);
            throw new \Exception(__('Column Summary_Rating is missing in csv file.'));
        }
        if (!in_array("Status", $header)) {
            $this->varDirectory->delete($uploadedFile);
            throw new \Exception(__('Column Status is missing in csv file.'));
        }
    }

    /**
     * Get Var directory
     * @return string
     */
    public function getWorkingDir()
    {
        return $this->varDirectory->getAbsolutePath('importexport/');
    }

    /**
     * @param $result
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function processUpload($result)
    {

        $sourceFile = $this->getWorkingDir() . $result['file'];

        $rows = $this->csvProcessor->getData($sourceFile);
        $header = array_shift($rows);

        // See \Magento\ReviewSampleData\Model\Review::install()
        foreach ($rows as $row) {
            $data = [];
            foreach ($row as $key => $value) {
                $data[$header[$key]] = $value;
            }
            $row = $data;
            $row['RATING_CODE'] = 'Quality'; // Fixed to "Rating" for now

            if (isset($row['SKU'])) {
                try {
                    //If product id is used as sku
                    $productId = $this->productRepository->get($row['SKU'])->getId();
                    $row['Product'] = $productId;

                    if (empty($productId)) {
                        continue;
                    }
                } catch (\Exception $e) {
                    continue;
                }

            }

            $review = $this->prepareReview($row);

            /** @var \Magento\Review\Model\ResourceModel\Review\Collection $reviewCollection */
            $reviewCollection = $this->reviewCollectionFactory->create();
            $reviewCollection->addFilter('entity_pk_value', $productId)
                ->addFilter('entity_id', $this->getReviewEntityId())
                ->addFieldToFilter('detail.title', ['eq' => $row['Title']])
                ->addFieldToFilter('detail.nickname', ['eq' => $row['Nickname']]);
            if ($reviewCollection->getSize() > 0) {
                continue;
            }

            $review->save();
            $this->setReviewRating($review, $row);
        }
    }


    /**
     * Prepare Data to import
     * @param $row
     * @return \Magento\Review\Model\Review
     */
    protected function prepareReview($row)
    {
        /** @var $review \Magento\Review\Model\Review */
        $review = $this->reviewFactory->create();

        if ($row['Status'] == 'Approved') {
            $statusId = \Magento\Review\Model\Review::STATUS_APPROVED;
        } elseif ($row['Status'] == 'Not Approved') {
            $statusId = \Magento\Review\Model\Review::STATUS_NOT_APPROVED;
        } else {
            $statusId = \Magento\Review\Model\Review::STATUS_PENDING;
        }
        $review->setEntityId(
            $review->getEntityIdByCode(\Magento\Review\Model\Review::ENTITY_PRODUCT_CODE)
        )->setEntityPkValue(
            $row['Product']
        )->setNickname(
            $row['Nickname']
        )->setTitle(
            $row['Title']
        )->setDetail(
            $row['Review']
        )->setStatusId(
            $statusId
        )->setStoreId(
            $this->storeId
        )->setStores(
            [$this->storeId]
        )->setCreatedAt(
            $this->convertDate($row['Created'])
        );
        return $review;
    }

    /**
     * Converts date to mysql formatted date
     *
     * @param string $date
     * @return string convertedDate
     */
    public function convertDate($date)
    {
        $timestamp = strtotime($date);
        return date("Y-m-d H:i:s", $timestamp);
    }

    /**
     * Get Review Entity Id
     * @return bool|int
     */
    protected function getReviewEntityId()
    {
        if (!$this->reviewProductEntityId) {
            /** @var $review \Magento\Review\Model\Review */
            $review = $this->reviewFactory->create();
            $this->reviewProductEntityId = $review->getEntityIdByCode(
                \Magento\Review\Model\Review::ENTITY_PRODUCT_CODE
            );
        }
        return $this->reviewProductEntityId;
    }

    /**
     * Set Review Rating
     * @param \Magento\Review\Model\Review $review
     * @param $row
     */
    protected function setReviewRating(\Magento\Review\Model\Review $review, $row)
    {
        $rating = $this->getRating($row['RATING_CODE']);
        foreach ($rating->getOptions() as $option) {
            $optionId = $option->getOptionId();
            if (($option->getValue() == $row['Summary_Rating']) && !empty($optionId)) {
                $rating->setReviewId($review->getId())->addOptionVote(
                    $optionId,
                    $row['Product']
                );
            }
        }
        $review->aggregate();
    }

    /**
     * Get Ragtings
     * @param $rating
     * @return mixed
     */
    protected function getRating($rating)
    {
        $ratingCollection = $this->ratingFactory->create()->getResourceCollection();
        if (!$this->ratings[$rating]) {
            $this->ratings[$rating] = $ratingCollection->addFieldToFilter('rating_code', $rating)->getFirstItem();
        }
        return $this->ratings[$rating];
    }

}