!function(t){var h=Array.prototype.slice;function e(){}function i(a){if(a){var l="undefined"==typeof console?e:function(t){console.error(t)};return a.bridget=function(t,e){var i,s,r;(i=e).prototype.option||(i.prototype.option=function(t){a.isPlainObject(t)&&(this.options=a.extend(!0,this.options,t))}),s=t,r=e,a.fn[s]=function(e){if("string"!=typeof e)return this.each(function(){var t=a.data(this,s);t?(t.option(e),t._init()):(t=new r(this,e),a.data(this,s,t))});for(var t=h.call(arguments,1),i=0,n=this.length;i<n;i++){var o=this[i],o=a.data(o,s);if(o)if(a.isFunction(o[e])&&"_"!==e.charAt(0)){o=o[e].apply(o,t);if(void 0!==o)return o}else l("no such method '"+e+"' for "+s+" instance");else l("cannot call methods on "+s+" prior to initialization; attempted to call '"+e+"'")}return this}},a.bridget}}"function"==typeof define&&define.amd?define("jquery-bridget/jquery.bridget",["jquery"],i):"object"==typeof exports?i(require("jquery")):i(t.jQuery)}(window),function(t){function i(t){return new RegExp("(^|\\s+)"+t+"(\\s+|$)")}var n,o,s;function e(t,e){(n(t,e)?s:o)(t,e)}s="classList"in document.documentElement?(n=function(t,e){return t.classList.contains(e)},o=function(t,e){t.classList.add(e)},function(t,e){t.classList.remove(e)}):(n=function(t,e){return i(e).test(t.className)},o=function(t,e){n(t,e)||(t.className=t.className+" "+e)},function(t,e){t.className=t.className.replace(i(e)," ")});var r={hasClass:n,addClass:o,removeClass:s,toggleClass:e,has:n,add:o,remove:s,toggle:e};"function"==typeof define&&define.amd?define("classie/classie",function(){return r}):"object"==typeof exports?module.exports=r:t.classie=r}(window),function(){"use strict";function t(){}var e=t.prototype,i=this,n=i.EventEmitter;function s(t,e){for(var i=t.length;i--;)if(t[i].listener===e)return i;return-1}function o(t){return function(){return this[t].apply(this,arguments)}}e.getListeners=function(t){var e,i,n=this._getEvents();if(t instanceof RegExp)for(i in e={},n)n.hasOwnProperty(i)&&t.test(i)&&(e[i]=n[i]);else e=n[t]||(n[t]=[]);return e},e.flattenListeners=function(t){for(var e=[],i=0;i<t.length;i+=1)e.push(t[i].listener);return e},e.getListenersAsObject=function(t){var e,i=this.getListeners(t);return i instanceof Array&&((e={})[t]=i),e||i},e.addListener=function(t,e){var i,n=this.getListenersAsObject(t),o="object"==typeof e;for(i in n)n.hasOwnProperty(i)&&-1===s(n[i],e)&&n[i].push(o?e:{listener:e,once:!1});return this},e.on=o("addListener"),e.addOnceListener=function(t,e){return this.addListener(t,{listener:e,once:!0})},e.once=o("addOnceListener"),e.defineEvent=function(t){return this.getListeners(t),this},e.defineEvents=function(t){for(var e=0;e<t.length;e+=1)this.defineEvent(t[e]);return this},e.removeListener=function(t,e){var i,n,o=this.getListenersAsObject(t);for(n in o)o.hasOwnProperty(n)&&-1!==(i=s(o[n],e))&&o[n].splice(i,1);return this},e.off=o("removeListener"),e.addListeners=function(t,e){return this.manipulateListeners(!1,t,e)},e.removeListeners=function(t,e){return this.manipulateListeners(!0,t,e)},e.manipulateListeners=function(t,e,i){var n,o,s=t?this.removeListener:this.addListener,r=t?this.removeListeners:this.addListeners;if("object"!=typeof e||e instanceof RegExp)for(n=i.length;n--;)s.call(this,e,i[n]);else for(n in e)e.hasOwnProperty(n)&&(o=e[n])&&("function"==typeof o?s:r).call(this,n,o);return this},e.removeEvent=function(t){var e,i=typeof t,n=this._getEvents();if("string"==i)delete n[t];else if(t instanceof RegExp)for(e in n)n.hasOwnProperty(e)&&t.test(e)&&delete n[e];else delete this._events;return this},e.removeAllListeners=o("removeEvent"),e.emitEvent=function(t,e){var i,n,o,s=this.getListenersAsObject(t);for(o in s)if(s.hasOwnProperty(o))for(n=s[o].length;n--;)!0===(i=s[o][n]).once&&this.removeListener(t,i.listener),i.listener.apply(this,e||[])===this._getOnceReturnValue()&&this.removeListener(t,i.listener);return this},e.trigger=o("emitEvent"),e.emit=function(t){var e=Array.prototype.slice.call(arguments,1);return this.emitEvent(t,e)},e.setOnceReturnValue=function(t){return this._onceReturnValue=t,this},e._getOnceReturnValue=function(){return!this.hasOwnProperty("_onceReturnValue")||this._onceReturnValue},e._getEvents=function(){return this._events||(this._events={})},t.noConflict=function(){return i.EventEmitter=n,t},"function"==typeof define&&define.amd?define("eventEmitter/EventEmitter",[],function(){return t}):"object"==typeof module&&module.exports?module.exports=t:i.EventEmitter=t}.call(this),function(i){var t=document.documentElement,e=function(){};function n(t){var e=i.event;return e.target=e.target||e.srcElement||t,e}t.addEventListener?e=function(t,e,i){t.addEventListener(e,i,!1)}:t.attachEvent&&(e=function(e,t,i){e[t+i]=i.handleEvent?function(){var t=n(e);i.handleEvent.call(i,t)}:function(){var t=n(e);i.call(e,t)},e.attachEvent("on"+t,e[t+i])});var o=function(){};t.removeEventListener?o=function(t,e,i){t.removeEventListener(e,i,!1)}:t.detachEvent&&(o=function(e,i,n){e.detachEvent("on"+i,e[i+n]);try{delete e[i+n]}catch(t){e[i+n]=void 0}});var s={bind:e,unbind:o};"function"==typeof define&&define.amd?define("eventie/eventie",function(){return s}):"object"==typeof exports?module.exports=s:i.eventie=s}(window),function(t){var o="Webkit Moz ms Ms O".split(" "),s=document.documentElement.style;function e(t){if(t){if("string"==typeof s[t])return t;var e;t=t.charAt(0).toUpperCase()+t.slice(1);for(var i=0,n=o.length;i<n;i++)if(e=o[i]+t,"string"==typeof s[e])return e}}"function"==typeof define&&define.amd?define("get-style-property/get-style-property",[],function(){return e}):"object"==typeof exports?module.exports=e:t.getStyleProperty=e}(window),function(C){function w(t){var e=parseFloat(t);return-1===t.indexOf("%")&&!isNaN(e)&&e}var E="undefined"==typeof console?function(){}:function(t){console.error(t)},P=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"];function t(v){var m,b,x,S=!1;return function(t){var e,i;if(S||(S=!0,e=C.getComputedStyle,i=e?function(t){return e(t,null)}:function(t){return t.currentStyle},m=function(t){t=i(t);return t||E("Style returned "+t+". Are you running this code in a hidden iframe on Firefox? See http://bit.ly/getsizebug1"),t},(b=v("boxSizing"))&&((g=document.createElement("div")).style.width="200px",g.style.padding="1px 2px 3px 4px",g.style.borderStyle="solid",g.style.borderWidth="1px 2px 3px 4px",g.style[b]="border-box",(y=document.body||document.documentElement).appendChild(g),f=m(g),x=200===w(f.width),y.removeChild(g))),"string"==typeof t&&(t=document.querySelector(t)),t&&"object"==typeof t&&t.nodeType){var n=m(t);if("none"===n.display)return function(){for(var t={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},e=0,i=P.length;e<i;e++)t[P[e]]=0;return t}();var o={};o.width=t.offsetWidth,o.height=t.offsetHeight;for(var s=o.isBorderBox=!(!b||!n[b]||"border-box"!==n[b]),r=0,a=P.length;r<a;r++){var l=P[r],h=function(t,e){if(C.getComputedStyle||-1===e.indexOf("%"))return e;var i=t.style,n=i.left,o=t.runtimeStyle,s=o&&o.left;s&&(o.left=t.currentStyle.left);i.left=e,e=i.pixelLeft,i.left=n,s&&(o.left=s);return e}(t,h=n[l]),h=parseFloat(h);o[l]=isNaN(h)?0:h}var c=o.paddingLeft+o.paddingRight,p=o.paddingTop+o.paddingBottom,d=o.marginLeft+o.marginRight,u=o.marginTop+o.marginBottom,f=o.borderLeftWidth+o.borderRightWidth,y=o.borderTopWidth+o.borderBottomWidth,g=s&&x,s=w(n.width);!1!==s&&(o.width=s+(g?0:c+f));s=w(n.height);return!1!==s&&(o.height=s+(g?0:p+y)),o.innerWidth=o.width-(c+f),o.innerHeight=o.height-(p+y),o.outerWidth=o.width+d,o.outerHeight=o.height+u,o}}}"function"==typeof define&&define.amd?define("get-size/get-size",["get-style-property/get-style-property"],t):"object"==typeof exports?module.exports=t(require("desandro-get-style-property")):C.getSize=t(C.getStyleProperty)}(window),function(e){var i=e.document,n=[];function o(t){"function"==typeof t&&(o.isReady?t():n.push(t))}function s(t){t="readystatechange"===t.type&&"complete"!==i.readyState;o.isReady||t||r()}function r(){o.isReady=!0;for(var t=0,e=n.length;t<e;t++)(0,n[t])()}function t(t){return"complete"===i.readyState?r():(t.bind(i,"DOMContentLoaded",s),t.bind(i,"readystatechange",s),t.bind(e,"load",s)),o}o.isReady=!1,"function"==typeof define&&define.amd?define("doc-ready/doc-ready",["eventie/eventie"],t):"object"==typeof exports?module.exports=t(require("eventie")):e.docReady=t(e.eventie)}(window),function(o){"use strict";var t,i=function(){if(o.matches)return"matches";if(o.matchesSelector)return"matchesSelector";for(var t=["webkit","moz","ms","o"],e=0,i=t.length;e<i;e++){var n=t[e]+"MatchesSelector";if(o[n])return n}}();function n(t,e){return t[i](e)}function s(t){t.parentNode||document.createDocumentFragment().appendChild(t)}t=i?n(document.createElement("div"),"div")?n:function(t,e){return s(t),n(t,e)}:function(t,e){s(t);for(var i=t.parentNode.querySelectorAll(e),n=0,o=i.length;n<o;n++)if(i[n]===t)return!0;return!1},"function"==typeof define&&define.amd?define("matches-selector/matches-selector",[],function(){return t}):"object"==typeof exports?module.exports=t:window.matchesSelector=t}(Element.prototype),function(i,n){"use strict";"function"==typeof define&&define.amd?define("fizzy-ui-utils/utils",["doc-ready/doc-ready","matches-selector/matches-selector"],function(t,e){return n(i,t,e)}):"object"==typeof exports?module.exports=n(i,require("doc-ready"),require("desandro-matches-selector")):i.fizzyUIUtils=n(i,i.docReady,i.matchesSelector)}(window,function(d,t,h){var i,u={extend:function(t,e){for(var i in e)t[i]=e[i];return t},modulo:function(t,e){return(t%e+e)%e}},e=Object.prototype.toString;u.isArray=function(t){return"[object Array]"==e.call(t)},u.makeArray=function(t){var e=[];if(u.isArray(t))e=t;else if(t&&"number"==typeof t.length)for(var i=0,n=t.length;i<n;i++)e.push(t[i]);else e.push(t);return e},u.indexOf=Array.prototype.indexOf?function(t,e){return t.indexOf(e)}:function(t,e){for(var i=0,n=t.length;i<n;i++)if(t[i]===e)return i;return-1},u.removeFrom=function(t,e){e=u.indexOf(t,e);-1!=e&&t.splice(e,1)},u.isElement="function"==typeof HTMLElement||"object"==typeof HTMLElement?function(t){return t instanceof HTMLElement}:function(t){return t&&"object"==typeof t&&1==t.nodeType&&"string"==typeof t.nodeName},u.setText=function(t,e){t[i=i||(void 0!==document.documentElement.textContent?"textContent":"innerText")]=e},u.getParent=function(t,e){for(;t!=document.body;)if(t=t.parentNode,h(t,e))return t},u.getQueryElement=function(t){return"string"==typeof t?document.querySelector(t):t},u.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},u.filterFindElements=function(t,e){for(var i=[],n=0,o=(t=u.makeArray(t)).length;n<o;n++){var s=t[n];if(u.isElement(s))if(e){h(s,e)&&i.push(s);for(var r=s.querySelectorAll(e),a=0,l=r.length;a<l;a++)i.push(r[a])}else i.push(s)}return i},u.debounceMethod=function(t,e,n){var o=t.prototype[e],s=e+"Timeout";t.prototype[e]=function(){var t=this[s];t&&clearTimeout(t);var e=arguments,i=this;this[s]=setTimeout(function(){o.apply(i,e),delete i[s]},n||100)}},u.toDashed=function(t){return t.replace(/(.)([A-Z])/g,function(t,e,i){return e+"-"+i}).toLowerCase()};var f=d.console;return u.htmlInit=function(c,p){t(function(){for(var t=u.toDashed(p),e=document.querySelectorAll(".js-"+t),i="data-"+t+"-options",n=0,o=e.length;n<o;n++){var s,r=e[n],a=r.getAttribute(i);try{s=a&&JSON.parse(a)}catch(t){f&&f.error("Error parsing "+i+" on "+r.nodeName.toLowerCase()+(r.id?"#"+r.id:"")+": "+t);continue}var l=new c(r,s),h=d.jQuery;h&&h.data(r,p,l)}})},u}),function(e,i){"use strict";"function"==typeof define&&define.amd?define("flickity/js/cell",["get-size/get-size"],function(t){return i(e,t)}):"object"==typeof exports?module.exports=i(e,require("get-size")):(e.Flickity=e.Flickity||{},e.Flickity.Cell=i(e,e.getSize))}(window,function(t,e){function i(t,e){this.element=t,this.parent=e,this.create()}var n="attachEvent"in t;return i.prototype.create=function(){this.element.style.position="absolute",n&&this.element.setAttribute("unselectable","on"),this.x=0,this.shift=0},i.prototype.destroy=function(){this.element.style.position="";var t=this.parent.originSide;this.element.style[t]=""},i.prototype.getSize=function(){this.size=e(this.element)},i.prototype.setPosition=function(t){this.x=t,this.setDefaultTarget(),this.renderPosition(t)},i.prototype.setDefaultTarget=function(){var t="left"==this.parent.originSide?"marginLeft":"marginRight";this.target=this.x+this.size[t]+this.size.width*this.parent.cellAlign},i.prototype.renderPosition=function(t){var e=this.parent.originSide;this.element.style[e]=this.parent.getPositionValue(t)},i.prototype.wrapShift=function(t){this.shift=t,this.renderPosition(this.x+this.parent.slideableWidth*t)},i.prototype.remove=function(){this.element.parentNode.removeChild(this.element)},i}),function(i,n){"use strict";"function"==typeof define&&define.amd?define("flickity/js/animate",["get-style-property/get-style-property","fizzy-ui-utils/utils"],function(t,e){return n(i,t,e)}):"object"==typeof exports?module.exports=n(i,require("desandro-get-style-property"),require("fizzy-ui-utils")):(i.Flickity=i.Flickity||{},i.Flickity.animatePrototype=n(i,i.getStyleProperty,i.fizzyUIUtils))}(window,function(o,t,e){for(var i,s=0,n="webkit moz ms o".split(" "),r=o.requestAnimationFrame,a=o.cancelAnimationFrame,l=0;l<n.length&&(!r||!a);l++)i=n[l],r=r||o[i+"RequestAnimationFrame"],a=a||o[i+"CancelAnimationFrame"]||o[i+"CancelRequestAnimationFrame"];r&&a||(r=function(t){var e=(new Date).getTime(),i=Math.max(0,16-(e-s)),n=o.setTimeout(function(){t(e+i)},i);return s=e+i,n},a=function(t){o.clearTimeout(t)});var h={startAnimation:function(){this.isAnimating||(this.isAnimating=!0,this.restingFrames=0,this.animate())},animate:function(){this.applyDragForce(),this.applySelectedAttraction();var t,e=this.x;this.integratePhysics(),this.positionSlider(),this.settle(e),this.isAnimating&&(t=this,r(function(){t.animate()}))}},c=t("transform"),p=!!t("perspective");return h.positionSlider=function(){var t=this.x;this.options.wrapAround&&1<this.cells.length&&(t=e.modulo(t,this.slideableWidth),t-=this.slideableWidth,this.shiftWrapCells(t)),t+=this.cursorPosition,t=this.options.rightToLeft&&c?-t:t;t=this.getPositionValue(t);c?this.slider.style[c]=p&&this.isAnimating?"translate3d("+t+",0,0)":"translateX("+t+")":this.slider.style[this.originSide]=t},h.positionSliderAtSelected=function(){var t;this.cells.length&&(t=this.cells[this.selectedIndex],this.x=-t.target,this.positionSlider())},h.getPositionValue=function(t){return this.options.percentPosition?.01*Math.round(t/this.size.innerWidth*1e4)+"%":Math.round(t)+"px"},h.settle=function(t){this.isPointerDown||Math.round(100*this.x)!=Math.round(100*t)||this.restingFrames++,2<this.restingFrames&&(this.isAnimating=!1,delete this.isFreeScrolling,p&&this.positionSlider(),this.dispatchEvent("settle"))},h.shiftWrapCells=function(t){var e=this.cursorPosition+t;this._shiftCells(this.beforeShiftCells,e,-1);t=this.size.innerWidth-(t+this.slideableWidth+this.cursorPosition);this._shiftCells(this.afterShiftCells,t,1)},h._shiftCells=function(t,e,i){for(var n=0,o=t.length;n<o;n++){var s=t[n],r=0<e?i:0;s.wrapShift(r),e-=s.size.outerWidth}},h._unshiftCells=function(t){if(t&&t.length)for(var e=0,i=t.length;e<i;e++)t[e].wrapShift(0)},h.integratePhysics=function(){this.velocity+=this.accel,this.x+=this.velocity,this.velocity*=this.getFrictionFactor(),this.accel=0},h.applyForce=function(t){this.accel+=t},h.getFrictionFactor=function(){return 1-this.options[this.isFreeScrolling?"freeScrollFriction":"friction"]},h.getRestingPosition=function(){return this.x+this.velocity/(1-this.getFrictionFactor())},h.applyDragForce=function(){var t;this.isPointerDown&&(t=this.dragX-this.x-this.velocity,this.applyForce(t))},h.applySelectedAttraction=function(){var t,e=this.cells.length;this.isPointerDown||this.isFreeScrolling||!e||(t=this.cells[this.selectedIndex],e=this.options.wrapAround&&1<e?this.slideableWidth*Math.floor(this.selectedIndex/e):0,e=(-1*(t.target+e)-this.x)*this.options.selectedAttraction,this.applyForce(e))},h}),function(a,l){"use strict";var t;"function"==typeof define&&define.amd?define("flickity/js/flickity",["classie/classie","eventEmitter/EventEmitter","eventie/eventie","get-size/get-size","fizzy-ui-utils/utils","./cell","./animate"],function(t,e,i,n,o,s,r){return l(a,t,e,i,n,o,s,r)}):"object"==typeof exports?module.exports=l(a,require("desandro-classie"),require("wolfy87-eventemitter"),require("eventie"),require("get-size"),require("fizzy-ui-utils"),require("./cell"),require("./animate")):(t=a.Flickity,a.Flickity=l(a,a.classie,a.EventEmitter,a.eventie,a.getSize,a.fizzyUIUtils,t.Cell,t.animatePrototype))}(window,function(n,i,t,o,e,r,a,s){var l=n.jQuery,h=n.getComputedStyle,c=n.console;function p(t,e){for(t=r.makeArray(t);t.length;)e.appendChild(t.shift())}var d=0,u={};function f(t,e){var i=r.getQueryElement(t);i?(this.element=i,l&&(this.$element=l(this.element)),this.options=r.extend({},this.constructor.defaults),this.option(e),this._create()):c&&c.error("Bad element for Flickity: "+(i||t))}f.defaults={accessibility:!0,cellAlign:"center",freeScrollFriction:.075,friction:.28,percentPosition:!0,resize:!0,selectedAttraction:.025,setGallerySize:!0},f.createMethods=[],r.extend(f.prototype,t.prototype),f.prototype._create=function(){var t=this.guid=++d;this.element.flickityGUID=t,(u[t]=this).selectedIndex=0,this.restingFrames=0,this.x=0,this.velocity=0,this.accel=0,this.originSide=this.options.rightToLeft?"right":"left",this.viewport=document.createElement("div"),this.viewport.className="flickity-viewport",f.setUnselectable(this.viewport),this._createSlider(),(this.options.resize||this.options.watchCSS)&&(o.bind(n,"resize",this),this.isResizeBound=!0);for(var e=0,i=f.createMethods.length;e<i;e++)this[f.createMethods[e]]();this.options.watchCSS?this.watchCSS():this.activate()},f.prototype.option=function(t){r.extend(this.options,t)},f.prototype.activate=function(){var t;this.isActive||(this.isActive=!0,i.add(this.element,"flickity-enabled"),this.options.rightToLeft&&i.add(this.element,"flickity-rtl"),this.getSize(),p(this._filterFindCellElements(this.element.children),this.slider),this.viewport.appendChild(this.slider),this.element.appendChild(this.viewport),this.reloadCells(),this.options.accessibility&&(this.element.tabIndex=0,o.bind(this.element,"keydown",this)),this.emit("activate"),t=this.options.initialIndex,t=this.isInitActivated?this.selectedIndex:void 0!==t&&this.cells[t]?t:0,this.select(t,!1,!0),this.isInitActivated=!0)},f.prototype._createSlider=function(){var t=document.createElement("div");t.className="flickity-slider",t.style[this.originSide]=0,this.slider=t},f.prototype._filterFindCellElements=function(t){return r.filterFindElements(t,this.options.cellSelector)},f.prototype.reloadCells=function(){this.cells=this._makeCells(this.slider.children),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize()},f.prototype._makeCells=function(t){for(var e=this._filterFindCellElements(t),i=[],n=0,o=e.length;n<o;n++){var s=e[n],s=new a(s,this);i.push(s)}return i},f.prototype.getLastCell=function(){return this.cells[this.cells.length-1]},f.prototype.positionCells=function(){this._sizeCells(this.cells),this._positionCells(0)},f.prototype._positionCells=function(t){t=t||0,this.maxCellHeight=t&&this.maxCellHeight||0;var e,i,n=0;0<t&&(n=(e=this.cells[t-1]).x+e.size.outerWidth);for(var o=this.cells.length,s=t;s<o;s++)(i=this.cells[s]).setPosition(n),n+=i.size.outerWidth,this.maxCellHeight=Math.max(i.size.outerHeight,this.maxCellHeight);this.slideableWidth=n,this._containCells()},f.prototype._sizeCells=function(t){for(var e=0,i=t.length;e<i;e++)t[e].getSize()},f.prototype._init=f.prototype.reposition=function(){this.positionCells(),this.positionSliderAtSelected()},f.prototype.getSize=function(){this.size=e(this.element),this.setCellAlign(),this.cursorPosition=this.size.innerWidth*this.cellAlign};var y={center:{left:.5,right:.5},left:{left:0,right:1},right:{right:0,left:1}};f.prototype.setCellAlign=function(){var t=y[this.options.cellAlign];this.cellAlign=t?t[this.originSide]:this.options.cellAlign},f.prototype.setGallerySize=function(){this.options.setGallerySize&&(this.viewport.style.height=this.maxCellHeight+"px")},f.prototype._getWrapShiftCells=function(){var t,e;this.options.wrapAround&&(this._unshiftCells(this.beforeShiftCells),this._unshiftCells(this.afterShiftCells),t=this.cursorPosition,e=this.cells.length-1,this.beforeShiftCells=this._getGapCells(t,e,-1),t=this.size.innerWidth-this.cursorPosition,this.afterShiftCells=this._getGapCells(t,0,1))},f.prototype._getGapCells=function(t,e,i){for(var n=[];0<t;){var o=this.cells[e];if(!o)break;n.push(o),e+=i,t-=o.size.outerWidth}return n},f.prototype._containCells=function(){if(this.options.contain&&!this.options.wrapAround&&this.cells.length)for(var t=this.options.rightToLeft?"marginRight":"marginLeft",e=this.options.rightToLeft?"marginLeft":"marginRight",i=this.cells[0].size[t],t=this.getLastCell(),n=this.slideableWidth-t.size[e],o=n-this.size.innerWidth*(1-this.cellAlign),s=n<this.size.innerWidth,r=0,a=this.cells.length;r<a;r++){var l=this.cells[r];l.setDefaultTarget(),s?l.target=n*this.cellAlign:(l.target=Math.max(l.target,this.cursorPosition+i),l.target=Math.min(l.target,o))}},f.prototype.dispatchEvent=function(t,e,i){var n=[e].concat(i);this.emitEvent(t,n),l&&this.$element&&(e?((e=l.Event(e)).type=t,this.$element.trigger(e,i)):this.$element.trigger(t,i))},f.prototype.select=function(t,e,i){var n;this.isActive&&(t=parseInt(t,10),n=this.cells.length,this.options.wrapAround&&1<n&&(t<0?this.x-=this.slideableWidth:n<=t&&(this.x+=this.slideableWidth)),(this.options.wrapAround||e)&&(t=r.modulo(t,n)),this.cells[t]&&(this.selectedIndex=t,this.setSelectedCell(),i?this.positionSliderAtSelected():this.startAnimation(),this.dispatchEvent("cellSelect")))},f.prototype.previous=function(t){this.select(this.selectedIndex-1,t)},f.prototype.next=function(t){this.select(this.selectedIndex+1,t)},f.prototype.setSelectedCell=function(){this._removeSelectedCellClass(),this.selectedCell=this.cells[this.selectedIndex],this.selectedElement=this.selectedCell.element,i.add(this.selectedElement,"is-selected")},f.prototype._removeSelectedCellClass=function(){this.selectedCell&&i.remove(this.selectedCell.element,"is-selected")},f.prototype.getCell=function(t){for(var e=0,i=this.cells.length;e<i;e++){var n=this.cells[e];if(n.element==t)return n}},f.prototype.getCells=function(t){for(var e=[],i=0,n=(t=r.makeArray(t)).length;i<n;i++){var o=t[i],o=this.getCell(o);o&&e.push(o)}return e},f.prototype.getCellElements=function(){for(var t=[],e=0,i=this.cells.length;e<i;e++)t.push(this.cells[e].element);return t},f.prototype.getParentCell=function(t){var e=this.getCell(t);return e||(t=r.getParent(t,".flickity-slider > *"),this.getCell(t))},f.prototype.getAdjacentCellElements=function(t,e){if(!t)return[this.selectedElement];e=void 0===e?this.selectedIndex:e;var i=this.cells.length;if(i<=1+2*t)return this.getCellElements();for(var n=[],o=e-t;o<=e+t;o++){var s=this.options.wrapAround?r.modulo(o,i):o,s=this.cells[s];s&&n.push(s.element)}return n},f.prototype.uiChange=function(){this.emit("uiChange")},f.prototype.childUIPointerDown=function(t){this.emitEvent("childUIPointerDown",[t])},f.prototype.onresize=function(){this.watchCSS(),this.resize()},r.debounceMethod(f,"onresize",150),f.prototype.resize=function(){this.isActive&&(this.getSize(),this.options.wrapAround&&(this.x=r.modulo(this.x,this.slideableWidth)),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize(),this.positionSliderAtSelected())};var g,v=f.supportsConditionalCSS=function(){if(void 0!==g)return g;if(h){var t=document.createElement("style"),e=document.createTextNode('body:after { content: "foo"; display: none; }');t.appendChild(e),document.head.appendChild(t);e=h(document.body,":after").content;return g=-1!=e.indexOf("foo"),document.head.removeChild(t),g}g=!1};f.prototype.watchCSS=function(){var t=this.options.watchCSS;t&&(v()?-1!=h(this.element,":after").content.indexOf("flickity")?this.activate():this.deactivate():this["fallbackOn"==t?"activate":"deactivate"]())},f.prototype.onkeydown=function(t){var e;!this.options.accessibility||document.activeElement&&document.activeElement!=this.element||(37==t.keyCode?(e=this.options.rightToLeft?"next":"previous",this.uiChange(),this[e]()):39==t.keyCode&&(t=this.options.rightToLeft?"previous":"next",this.uiChange(),this[t]()))},f.prototype.deactivate=function(){if(this.isActive){i.remove(this.element,"flickity-enabled"),i.remove(this.element,"flickity-rtl");for(var t=0,e=this.cells.length;t<e;t++)this.cells[t].destroy();this._removeSelectedCellClass(),this.element.removeChild(this.viewport),p(this.slider.children,this.element),this.options.accessibility&&(this.element.removeAttribute("tabIndex"),o.unbind(this.element,"keydown",this)),this.isActive=!1,this.emit("deactivate")}},f.prototype.destroy=function(){this.deactivate(),this.isResizeBound&&o.unbind(n,"resize",this),this.emit("destroy"),l&&this.$element&&l.removeData(this.element,"flickity"),delete this.element.flickityGUID,delete u[this.guid]},r.extend(f.prototype,s);var m="attachEvent"in n;return f.setUnselectable=function(t){m&&t.setAttribute("unselectable","on")},f.data=function(t){t=(t=r.getQueryElement(t))&&t.flickityGUID;return t&&u[t]},r.htmlInit(f,"flickity"),l&&l.bridget&&l.bridget("flickity",f),f.Cell=a,f}),function(i,n){"use strict";"function"==typeof define&&define.amd?define("unipointer/unipointer",["eventEmitter/EventEmitter","eventie/eventie"],function(t,e){return n(i,t,e)}):"object"==typeof exports?module.exports=n(i,require("wolfy87-eventemitter"),require("eventie")):i.Unipointer=n(i,i.EventEmitter,i.eventie)}(window,function(r,t,a){function e(){}(e.prototype=new t).bindStartEvent=function(t){this._bindStartEvent(t,!0)},e.prototype.unbindStartEvent=function(t){this._bindStartEvent(t,!1)},e.prototype._bindStartEvent=function(t,e){e=(e=void 0===e||!!e)?"bind":"unbind";r.navigator.pointerEnabled?a[e](t,"pointerdown",this):r.navigator.msPointerEnabled?a[e](t,"MSPointerDown",this):(a[e](t,"mousedown",this),a[e](t,"touchstart",this))},e.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},e.prototype.getTouch=function(t){for(var e=0,i=t.length;e<i;e++){var n=t[e];if(n.identifier==this.pointerIdentifier)return n}},e.prototype.onmousedown=function(t){var e=t.button;e&&0!==e&&1!==e||this._pointerDown(t,t)},e.prototype.ontouchstart=function(t){this._pointerDown(t,t.changedTouches[0])},e.prototype.onMSPointerDown=e.prototype.onpointerdown=function(t){this._pointerDown(t,t)},e.prototype._pointerDown=function(t,e){this.isPointerDown||(this.isPointerDown=!0,this.pointerIdentifier=void 0!==e.pointerId?e.pointerId:e.identifier,this.pointerDown(t,e))},e.prototype.pointerDown=function(t,e){this._bindPostStartEvents(t),this.emitEvent("pointerDown",[t,e])};var l={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"],MSPointerDown:["MSPointerMove","MSPointerUp","MSPointerCancel"]};return e.prototype._bindPostStartEvents=function(t){if(t){for(var e=l[t.type],i=t.preventDefault?r:document,n=0,o=e.length;n<o;n++){var s=e[n];a.bind(i,s,this)}this._boundPointerEvents={events:e,node:i}}},e.prototype._unbindPostStartEvents=function(){var t=this._boundPointerEvents;if(t&&t.events){for(var e=0,i=t.events.length;e<i;e++){var n=t.events[e];a.unbind(t.node,n,this)}delete this._boundPointerEvents}},e.prototype.onmousemove=function(t){this._pointerMove(t,t)},e.prototype.onMSPointerMove=e.prototype.onpointermove=function(t){t.pointerId==this.pointerIdentifier&&this._pointerMove(t,t)},e.prototype.ontouchmove=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerMove(t,e)},e.prototype._pointerMove=function(t,e){this.pointerMove(t,e)},e.prototype.pointerMove=function(t,e){this.emitEvent("pointerMove",[t,e])},e.prototype.onmouseup=function(t){this._pointerUp(t,t)},e.prototype.onMSPointerUp=e.prototype.onpointerup=function(t){t.pointerId==this.pointerIdentifier&&this._pointerUp(t,t)},e.prototype.ontouchend=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerUp(t,e)},e.prototype._pointerUp=function(t,e){this._pointerDone(),this.pointerUp(t,e)},e.prototype.pointerUp=function(t,e){this.emitEvent("pointerUp",[t,e])},e.prototype._pointerDone=function(){this.isPointerDown=!1,delete this.pointerIdentifier,this._unbindPostStartEvents(),this.pointerDone()},e.prototype.pointerDone=function(){},e.prototype.onMSPointerCancel=e.prototype.onpointercancel=function(t){t.pointerId==this.pointerIdentifier&&this._pointerCancel(t,t)},e.prototype.ontouchcancel=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerCancel(t,e)},e.prototype._pointerCancel=function(t,e){this._pointerDone(),this.pointerCancel(t,e)},e.prototype.pointerCancel=function(t,e){this.emitEvent("pointerCancel",[t,e])},e.getPointerPoint=function(t){return{x:void 0!==t.pageX?t.pageX:t.clientX,y:void 0!==t.pageY?t.pageY:t.clientY}},e}),function(i,n){"use strict";"function"==typeof define&&define.amd?define("unidragger/unidragger",["eventie/eventie","unipointer/unipointer"],function(t,e){return n(i,t,e)}):"object"==typeof exports?module.exports=n(i,require("eventie"),require("unipointer")):i.Unidragger=n(i,i.eventie,i.Unipointer)}(window,function(n,r,o){function s(t){t.preventDefault?t.preventDefault():t.returnValue=!1}function a(){}(a.prototype=new o).bindHandles=function(){this._bindHandles(!0)},a.prototype.unbindHandles=function(){this._bindHandles(!1)};var l=n.navigator;function h(){return!1}a.prototype._bindHandles=function(e){var t;e=void 0===e||!!e,t=l.pointerEnabled?function(t){t.style.touchAction=e?"none":""}:l.msPointerEnabled?function(t){t.style.msTouchAction=e?"none":""}:function(){e&&c(s)};for(var i=e?"bind":"unbind",n=0,o=this.handles.length;n<o;n++){var s=this.handles[n];this._bindStartEvent(s,e),t(s),r[i](s,"click",this)}};var c="attachEvent"in document.documentElement?function(t){"IMG"==t.nodeName&&(t.ondragstart=h);for(var e=t.querySelectorAll("img"),i=0,n=e.length;i<n;i++)e[i].ondragstart=h}:function(){};a.prototype.pointerDown=function(t,e){if("INPUT"==t.target.nodeName&&"range"==t.target.type)return this.isPointerDown=!1,void delete this.pointerIdentifier;this._dragPointerDown(t,e);var i=document.activeElement;i&&i.blur&&i.blur(),this._bindPostStartEvents(t),this.pointerDownScroll=a.getScrollPosition(),r.bind(n,"scroll",this),this.emitEvent("pointerDown",[t,e])},a.prototype._dragPointerDown=function(t,e){this.pointerDownPoint=o.getPointerPoint(e);var i="touchstart"==t.type,e=t.target.nodeName;i||"SELECT"==e||s(t)},a.prototype.pointerMove=function(t,e){var i=this._dragPointerMove(t,e);this.emitEvent("pointerMove",[t,e,i]),this._dragMove(t,e,i)},a.prototype._dragPointerMove=function(t,e){var i=o.getPointerPoint(e),i={x:i.x-this.pointerDownPoint.x,y:i.y-this.pointerDownPoint.y};return!this.isDragging&&this.hasDragStarted(i)&&this._dragStart(t,e),i},a.prototype.hasDragStarted=function(t){return 3<Math.abs(t.x)||3<Math.abs(t.y)},a.prototype.pointerUp=function(t,e){this.emitEvent("pointerUp",[t,e]),this._dragPointerUp(t,e)},a.prototype._dragPointerUp=function(t,e){this.isDragging?this._dragEnd(t,e):this._staticClick(t,e)},a.prototype.pointerDone=function(){r.unbind(n,"scroll",this)},a.prototype._dragStart=function(t,e){this.isDragging=!0,this.dragStartPoint=a.getPointerPoint(e),this.isPreventingClicks=!0,this.dragStart(t,e)},a.prototype.dragStart=function(t,e){this.emitEvent("dragStart",[t,e])},a.prototype._dragMove=function(t,e,i){this.isDragging&&this.dragMove(t,e,i)},a.prototype.dragMove=function(t,e,i){s(t),this.emitEvent("dragMove",[t,e,i])},a.prototype._dragEnd=function(t,e){this.isDragging=!1;var i=this;setTimeout(function(){delete i.isPreventingClicks}),this.dragEnd(t,e)},a.prototype.dragEnd=function(t,e){this.emitEvent("dragEnd",[t,e])},a.prototype.pointerDone=function(){r.unbind(n,"scroll",this),delete this.pointerDownScroll},a.prototype.onclick=function(t){this.isPreventingClicks&&s(t)},a.prototype._staticClick=function(t,e){var i,n;this.isIgnoringMouseUp&&"mouseup"==t.type||("INPUT"!=(i=t.target.nodeName)&&"TEXTAREA"!=i||t.target.focus(),this.staticClick(t,e),"mouseup"!=t.type&&(this.isIgnoringMouseUp=!0,n=this,setTimeout(function(){delete n.isIgnoringMouseUp},400)))},a.prototype.staticClick=function(t,e){this.emitEvent("staticClick",[t,e])},a.prototype.onscroll=function(){var t=a.getScrollPosition(),e=this.pointerDownScroll.x-t.x,t=this.pointerDownScroll.y-t.y;(3<Math.abs(e)||3<Math.abs(t))&&this._pointerDone()},a.getPointerPoint=function(t){return{x:void 0!==t.pageX?t.pageX:t.clientX,y:void 0!==t.pageY?t.pageY:t.clientY}};var t=void 0!==n.pageYOffset;return a.getScrollPosition=function(){return{x:t?n.pageXOffset:document.body.scrollLeft,y:t?n.pageYOffset:document.body.scrollTop}},a.getPointerPoint=o.getPointerPoint,a}),function(s,r){"use strict";"function"==typeof define&&define.amd?define("flickity/js/drag",["classie/classie","eventie/eventie","./flickity","unidragger/unidragger","fizzy-ui-utils/utils"],function(t,e,i,n,o){return r(s,t,e,i,n,o)}):"object"==typeof exports?module.exports=r(s,require("desandro-classie"),require("eventie"),require("./flickity"),require("unidragger"),require("fizzy-ui-utils")):s.Flickity=r(s,s.classie,s.eventie,s.Flickity,s.Unidragger,s.fizzyUIUtils)}(window,function(n,o,s,t,r,a){function l(t){t.preventDefault?t.preventDefault():t.returnValue=!1}a.extend(t.defaults,{draggable:!0}),t.createMethods.push("_createDrag"),a.extend(t.prototype,r.prototype),t.prototype._createDrag=function(){this.on("activate",this.bindDrag),this.on("uiChange",this._uiChangeDrag),this.on("childUIPointerDown",this._childUIPointerDownDrag),this.on("deactivate",this.unbindDrag)},t.prototype.bindDrag=function(){this.options.draggable&&!this.isDragBound&&(o.add(this.element,"is-draggable"),this.handles=[this.viewport],this.bindHandles(),this.isDragBound=!0)},t.prototype.unbindDrag=function(){this.isDragBound&&(o.remove(this.element,"is-draggable"),this.unbindHandles(),delete this.isDragBound)},t.prototype._uiChangeDrag=function(){delete this.isFreeScrolling},t.prototype._childUIPointerDownDrag=function(t){l(t),this.pointerDownFocus(t)},t.prototype.pointerDown=function(t,e){if("INPUT"==t.target.nodeName&&"range"==t.target.type)return this.isPointerDown=!1,void delete this.pointerIdentifier;this._dragPointerDown(t,e);var i=document.activeElement;i&&i.blur&&i!=this.element&&i!=document.body&&i.blur(),this.pointerDownFocus(t),this.dragX=this.x,o.add(this.viewport,"is-pointer-down"),this._bindPostStartEvents(t),this.pointerDownScroll=r.getScrollPosition(),s.bind(n,"scroll",this),this.dispatchEvent("pointerDown",t,[e])};var e={touchstart:!0,MSPointerDown:!0},i={INPUT:!0,SELECT:!0};return t.prototype.pointerDownFocus=function(t){!this.options.accessibility||e[t.type]||i[t.target.nodeName]||(t=n.pageYOffset,this.element.focus(),n.pageYOffset!=t&&n.scrollTo(n.pageXOffset,t))},t.prototype.hasDragStarted=function(t){return 3<Math.abs(t.x)},t.prototype.pointerUp=function(t,e){o.remove(this.viewport,"is-pointer-down"),this.dispatchEvent("pointerUp",t,[e]),this._dragPointerUp(t,e)},t.prototype.pointerDone=function(){s.unbind(n,"scroll",this),delete this.pointerDownScroll},t.prototype.dragStart=function(t,e){this.dragStartPosition=this.x,this.startAnimation(),this.dispatchEvent("dragStart",t,[e])},t.prototype.dragMove=function(t,e,i){l(t),this.previousDragX=this.dragX;var n=this.options.rightToLeft?-1:1,o=this.dragStartPosition+i.x*n;!this.options.wrapAround&&this.cells.length&&(o=(o=(n=Math.max(-this.cells[0].target,this.dragStartPosition))<o?.5*(o+n):o)<(n=Math.min(-this.getLastCell().target,this.dragStartPosition))?.5*(o+n):o),this.dragX=o,this.dragMoveTime=new Date,this.dispatchEvent("dragMove",t,[e,i])},t.prototype.dragEnd=function(t,e){this.options.freeScroll&&(this.isFreeScrolling=!0);var i,n=this.dragEndRestingSelect();this.options.freeScroll&&!this.options.wrapAround?(i=this.getRestingPosition(),this.isFreeScrolling=-i>this.cells[0].target&&-i<this.getLastCell().target):this.options.freeScroll||n!=this.selectedIndex||(n+=this.dragEndBoostSelect()),delete this.previousDragX,this.select(n),this.dispatchEvent("dragEnd",t,[e])},t.prototype.dragEndRestingSelect=function(){var t=this.getRestingPosition(),e=Math.abs(this.getCellDistance(-t,this.selectedIndex)),i=this._getClosestResting(t,e,1),e=this._getClosestResting(t,e,-1);return(i.distance<e.distance?i:e).index},t.prototype._getClosestResting=function(t,e,i){for(var n=this.selectedIndex,o=1/0,s=this.options.contain&&!this.options.wrapAround?function(t,e){return t<=e}:function(t,e){return t<e};s(e,o)&&(n+=i,o=e,null!==(e=this.getCellDistance(-t,n)));)e=Math.abs(e);return{distance:o,index:n-i}},t.prototype.getCellDistance=function(t,e){var i=this.cells.length,n=this.options.wrapAround&&1<i,o=n?a.modulo(e,i):e,o=this.cells[o];if(!o)return null;i=n?this.slideableWidth*Math.floor(e/i):0;return t-(o.target+i)},t.prototype.dragEndBoostSelect=function(){if(void 0===this.previousDragX||!this.dragMoveTime||100<new Date-this.dragMoveTime)return 0;var t=this.getCellDistance(-this.dragX,this.selectedIndex),e=this.previousDragX-this.dragX;return 0<t&&0<e?1:t<0&&e<0?-1:0},t.prototype.staticClick=function(t,e){var i=this.getParentCell(t.target),n=i&&i.element,i=i&&a.indexOf(this.cells,i);this.dispatchEvent("staticClick",t,[e,n,i])},t}),function(e,i){"function"==typeof define&&define.amd?define("tap-listener/tap-listener",["unipointer/unipointer"],function(t){return i(e,t)}):"object"==typeof exports?module.exports=i(e,require("unipointer")):e.TapListener=i(e,e.Unipointer)}(window,function(r,a){function t(t){this.bindTap(t)}(t.prototype=new a).bindTap=function(t){t&&(this.unbindTap(),this.tapElement=t,this._bindStartEvent(t,!0))},t.prototype.unbindTap=function(){this.tapElement&&(this._bindStartEvent(this.tapElement,!0),delete this.tapElement)};var l=void 0!==r.pageYOffset;return t.prototype.pointerUp=function(t,e){var i,n,o,s;this.isIgnoringMouseUp&&"mouseup"==t.type||(i=a.getPointerPoint(e),n=this.tapElement.getBoundingClientRect(),o=l?r.pageXOffset:document.body.scrollLeft,s=l?r.pageYOffset:document.body.scrollTop,i.x>=n.left+o&&i.x<=n.right+o&&i.y>=n.top+s&&i.y<=n.bottom+s&&this.emitEvent("tap",[t,e]),"mouseup"!=t.type&&(this.isIgnoringMouseUp=!0,setTimeout(function(){delete this.isIgnoringMouseUp}.bind(this),320)))},t.prototype.destroy=function(){this.pointerDone(),this.unbindTap()},t}),function(t,o){"use strict";"function"==typeof define&&define.amd?define("flickity/js/prev-next-button",["eventie/eventie","./flickity","tap-listener/tap-listener","fizzy-ui-utils/utils"],function(t,e,i,n){return o(0,t,e,i,n)}):"object"==typeof exports?module.exports=o(0,require("eventie"),require("./flickity"),require("tap-listener"),require("fizzy-ui-utils")):o(0,t.eventie,t.Flickity,t.TapListener,t.fizzyUIUtils)}(window,function(t,e,n,i,o){var s,r="http://www.w3.org/2000/svg",a=function(){if(void 0!==s)return s;var t=document.createElement("div");return t.innerHTML="<svg/>",s=(t.firstChild&&t.firstChild.namespaceURI)==r};function l(t,e){this.direction=t,this.parent=e,this._create()}return(l.prototype=new i)._create=function(){this.isEnabled=!0,this.isPrevious=-1==this.direction;var t=this.parent.options.rightToLeft?1:-1;this.isLeft=this.direction==t;var e=this.element=document.createElement("button");e.className="flickity-prev-next-button",e.className+=this.isPrevious?" previous":" next",e.setAttribute("type","button"),this.disable(),e.setAttribute("aria-label",this.isPrevious?"previous":"next"),n.setUnselectable(e),a()?(t=this.createSVG(),e.appendChild(t)):(this.setArrowText(),e.className+=" no-svg");var i=this;this.onCellSelect=function(){i.update()},this.parent.on("cellSelect",this.onCellSelect),this.on("tap",this.onTap),this.on("pointerDown",function(t,e){i.parent.childUIPointerDown(e)})},l.prototype.activate=function(){this.bindTap(this.element),e.bind(this.element,"click",this),this.parent.element.appendChild(this.element)},l.prototype.deactivate=function(){this.parent.element.removeChild(this.element),i.prototype.destroy.call(this),e.unbind(this.element,"click",this)},l.prototype.createSVG=function(){var t=document.createElementNS(r,"svg");t.setAttribute("viewBox","0 0 100 100");var e,i=document.createElementNS(r,"path"),e="string"!=typeof(e=this.parent.options.arrowShape)?"M "+e.x0+",50 L "+e.x1+","+(e.y1+50)+" L "+e.x2+","+(e.y2+50)+" L "+e.x3+",50  L "+e.x2+","+(50-e.y2)+" L "+e.x1+","+(50-e.y1)+" Z":e;return i.setAttribute("d",e),i.setAttribute("class","arrow"),this.isLeft||i.setAttribute("transform","translate(100, 100) rotate(180) "),t.appendChild(i),t},l.prototype.setArrowText=function(){var t=this.parent.options,t=this.isLeft?t.leftArrowText:t.rightArrowText;o.setText(this.element,t)},l.prototype.onTap=function(){var t;this.isEnabled&&(this.parent.uiChange(),t=this.isPrevious?"previous":"next",this.parent[t]())},l.prototype.handleEvent=o.handleEvent,l.prototype.onclick=function(){var t=document.activeElement;t&&t==this.element&&this.onTap()},l.prototype.enable=function(){this.isEnabled||(this.element.disabled=!1,this.isEnabled=!0)},l.prototype.disable=function(){this.isEnabled&&(this.element.disabled=!0,this.isEnabled=!1)},l.prototype.update=function(){var t=this.parent.cells;this.parent.options.wrapAround&&1<t.length?this.enable():(t=t.length?t.length-1:0,t=this.isPrevious?0:t,this[this.parent.selectedIndex==t?"disable":"enable"]())},l.prototype.destroy=function(){this.deactivate()},o.extend(n.defaults,{prevNextButtons:!0,leftArrowText:"‹",rightArrowText:"›",arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:40,x3:30}}),n.createMethods.push("_createPrevNextButtons"),n.prototype._createPrevNextButtons=function(){this.options.prevNextButtons&&(this.prevButton=new l(-1,this),this.nextButton=new l(1,this),this.on("activate",this.activatePrevNextButtons))},n.prototype.activatePrevNextButtons=function(){this.prevButton.activate(),this.nextButton.activate(),this.on("deactivate",this.deactivatePrevNextButtons)},n.prototype.deactivatePrevNextButtons=function(){this.prevButton.deactivate(),this.nextButton.deactivate(),this.off("deactivate",this.deactivatePrevNextButtons)},n.PrevNextButton=l,n}),function(t,o){"use strict";"function"==typeof define&&define.amd?define("flickity/js/page-dots",["eventie/eventie","./flickity","tap-listener/tap-listener","fizzy-ui-utils/utils"],function(t,e,i,n){return o(0,0,e,i,n)}):"object"==typeof exports?module.exports=o(0,require("eventie"),require("./flickity"),require("tap-listener"),require("fizzy-ui-utils")):o(0,t.eventie,t.Flickity,t.TapListener,t.fizzyUIUtils)}(window,function(t,e,n,i,o){function s(t){this.parent=t,this._create()}return(s.prototype=new i)._create=function(){this.holder=document.createElement("ol"),this.holder.className="flickity-page-dots",n.setUnselectable(this.holder),this.dots=[];var i=this;this.onCellSelect=function(){i.updateSelected()},this.parent.on("cellSelect",this.onCellSelect),this.on("tap",this.onTap),this.on("pointerDown",function(t,e){i.parent.childUIPointerDown(e)})},s.prototype.activate=function(){this.setDots(),this.bindTap(this.holder),this.parent.element.appendChild(this.holder)},s.prototype.deactivate=function(){this.parent.element.removeChild(this.holder),i.prototype.destroy.call(this)},s.prototype.setDots=function(){var t=this.parent.cells.length-this.dots.length;0<t?this.addDots(t):t<0&&this.removeDots(-t)},s.prototype.addDots=function(t){for(var e=document.createDocumentFragment(),i=[];t;){var n=document.createElement("li");n.className="dot",e.appendChild(n),i.push(n),t--}this.holder.appendChild(e),this.dots=this.dots.concat(i)},s.prototype.removeDots=function(t){for(var e=this.dots.splice(this.dots.length-t,t),i=0,n=e.length;i<n;i++){var o=e[i];this.holder.removeChild(o)}},s.prototype.updateSelected=function(){this.selectedDot&&(this.selectedDot.className="dot"),this.dots.length&&(this.selectedDot=this.dots[this.parent.selectedIndex],this.selectedDot.className="dot is-selected")},s.prototype.onTap=function(t){t=t.target;"LI"==t.nodeName&&(this.parent.uiChange(),t=o.indexOf(this.dots,t),this.parent.select(t))},s.prototype.destroy=function(){this.deactivate()},n.PageDots=s,o.extend(n.defaults,{pageDots:!0}),n.createMethods.push("_createPageDots"),n.prototype._createPageDots=function(){this.options.pageDots&&(this.pageDots=new s(this),this.on("activate",this.activatePageDots),this.on("cellAddedRemoved",this.onCellAddedRemovedPageDots),this.on("deactivate",this.deactivatePageDots))},n.prototype.activatePageDots=function(){this.pageDots.activate()},n.prototype.onCellAddedRemovedPageDots=function(){this.pageDots.setDots()},n.prototype.deactivatePageDots=function(){this.pageDots.deactivate()},n.PageDots=s,n}),function(t,e){"use strict";"function"==typeof define&&define.amd?define("flickity/js/player",["eventEmitter/EventEmitter","eventie/eventie","fizzy-ui-utils/utils","./flickity"],e):"object"==typeof exports?module.exports=e(require("wolfy87-eventemitter"),require("eventie"),require("fizzy-ui-utils"),require("./flickity")):e(t.EventEmitter,t.eventie,t.fizzyUIUtils,t.Flickity)}(window,function(t,e,i,n){var o,s;function r(t){var e;this.parent=t,this.state="stopped",s&&((e=this).onVisibilityChange=function(){e.visibilityChange()})}return"hidden"in document?(o="hidden",s="visibilitychange"):"webkitHidden"in document&&(o="webkitHidden",s="webkitvisibilitychange"),(r.prototype=new t).play=function(){"playing"!=this.state&&(this.state="playing",s&&document.addEventListener(s,this.onVisibilityChange,!1),this.tick())},r.prototype.tick=function(){var t,e;"playing"==this.state&&(t="number"==typeof(t=this.parent.options.autoPlay)?t:3e3,(e=this).clear(),this.timeout=setTimeout(function(){e.parent.next(!0),e.tick()},t))},r.prototype.stop=function(){this.state="stopped",this.clear(),s&&document.removeEventListener(s,this.onVisibilityChange,!1)},r.prototype.clear=function(){clearTimeout(this.timeout)},r.prototype.pause=function(){"playing"==this.state&&(this.state="paused",this.clear())},r.prototype.unpause=function(){"paused"==this.state&&this.play()},r.prototype.visibilityChange=function(){this[document[o]?"pause":"unpause"]()},i.extend(n.defaults,{pauseAutoPlayOnHover:!0}),n.createMethods.push("_createPlayer"),n.prototype._createPlayer=function(){this.player=new r(this),this.on("activate",this.activatePlayer),this.on("uiChange",this.stopPlayer),this.on("pointerDown",this.stopPlayer),this.on("deactivate",this.deactivatePlayer)},n.prototype.activatePlayer=function(){this.options.autoPlay&&(this.player.play(),e.bind(this.element,"mouseenter",this),this.isMouseenterBound=!0)},n.prototype.playPlayer=function(){this.player.play()},n.prototype.stopPlayer=function(){this.player.stop()},n.prototype.pausePlayer=function(){this.player.pause()},n.prototype.unpausePlayer=function(){this.player.unpause()},n.prototype.deactivatePlayer=function(){this.player.stop(),this.isMouseenterBound&&(e.unbind(this.element,"mouseenter",this),delete this.isMouseenterBound)},n.prototype.onmouseenter=function(){this.options.pauseAutoPlayOnHover&&(this.player.pause(),e.bind(this.element,"mouseleave",this))},n.prototype.onmouseleave=function(){this.player.unpause(),e.unbind(this.element,"mouseleave",this)},n.Player=r,n}),function(t,i){"use strict";"function"==typeof define&&define.amd?define("flickity/js/add-remove-cell",["./flickity","fizzy-ui-utils/utils"],function(t,e){return i(0,t,e)}):"object"==typeof exports?module.exports=i(0,require("./flickity"),require("fizzy-ui-utils")):i(0,t.Flickity,t.fizzyUIUtils)}(window,function(t,e,r){return e.prototype.insert=function(t,e){var i,n,o,s=this._makeCells(t);s&&s.length&&(o=this.cells.length,e=void 0===e?o:e,i=function(t){for(var e=document.createDocumentFragment(),i=0,n=t.length;i<n;i++){var o=t[i];e.appendChild(o.element)}return e}(s),(n=e==o)?this.slider.appendChild(i):(t=this.cells[e].element,this.slider.insertBefore(i,t)),0===e?this.cells=s.concat(this.cells):n?this.cells=this.cells.concat(s):(o=this.cells.splice(e,o-e),this.cells=this.cells.concat(s).concat(o)),this._sizeCells(s),s=e>this.selectedIndex?0:s.length,this._cellAddedRemoved(e,s))},e.prototype.append=function(t){this.insert(t,this.cells.length)},e.prototype.prepend=function(t){this.insert(t,0)},e.prototype.remove=function(t){for(var e=this.getCells(t),i=0,n=0,o=e.length;n<o;n++){var s=e[n];i-=r.indexOf(this.cells,s)<this.selectedIndex?1:0}for(n=0,o=e.length;n<o;n++)(s=e[n]).remove(),r.removeFrom(this.cells,s);e.length&&this._cellAddedRemoved(0,i)},e.prototype._cellAddedRemoved=function(t,e){e=e||0,this.selectedIndex+=e,this.selectedIndex=Math.max(0,Math.min(this.cells.length-1,this.selectedIndex)),this.emitEvent("cellAddedRemoved",[t,e]),this.cellChange(t,!0)},e.prototype.cellSizeChange=function(t){t=this.getCell(t);t&&(t.getSize(),t=r.indexOf(this.cells,t),this.cellChange(t))},e.prototype.cellChange=function(t,e){var i=this.slideableWidth;this._positionCells(t),this._getWrapShiftCells(),this.setGallerySize(),this.options.freeScroll?(i=i-this.slideableWidth,this.x+=i*this.cellAlign,this.positionSlider()):(e&&this.positionSliderAtSelected(),this.select(this.selectedIndex))},e}),function(t,o){"use strict";"function"==typeof define&&define.amd?define("flickity/js/lazyload",["classie/classie","eventie/eventie","./flickity","fizzy-ui-utils/utils"],function(t,e,i,n){return o(0,t,e,i,n)}):"object"==typeof exports?module.exports=o(0,require("desandro-classie"),require("eventie"),require("./flickity"),require("fizzy-ui-utils")):o(0,t.classie,t.eventie,t.Flickity,t.fizzyUIUtils)}(window,function(t,n,o,e,r){"use strict";function a(t,e){this.img=t,this.flickity=e,this.load()}return e.createMethods.push("_createLazyload"),e.prototype._createLazyload=function(){this.on("cellSelect",this.lazyLoad)},e.prototype.lazyLoad=function(){var t=this.options.lazyLoad;if(t){for(var t="number"==typeof t?t:0,e=this.getAdjacentCellElements(t),i=[],n=0,o=e.length;n<o;n++)var s=function(t){if("IMG"==t.nodeName&&t.getAttribute("data-flickity-lazyload"))return[t];t=t.querySelectorAll("img[data-flickity-lazyload]");return r.makeArray(t)}(e[n]),i=i.concat(s);for(n=0,o=i.length;n<o;n++)new a(i[n],this)}},a.prototype.handleEvent=r.handleEvent,a.prototype.load=function(){o.bind(this.img,"load",this),o.bind(this.img,"error",this),this.img.src=this.img.getAttribute("data-flickity-lazyload"),this.img.removeAttribute("data-flickity-lazyload")},a.prototype.onload=function(t){this.complete(t,"flickity-lazyloaded")},a.prototype.onerror=function(t){this.complete(t,"flickity-lazyerror")},a.prototype.complete=function(t,e){o.unbind(this.img,"load",this),o.unbind(this.img,"error",this);var i=this.flickity.getParentCell(this.img),i=i&&i.element;this.flickity.cellSizeChange(i),n.add(this.img,e),this.flickity.dispatchEvent("lazyLoad",t,i)},e.LazyLoader=a,e}),function(t){"use strict";"function"==typeof define&&define.amd?define("flickity/js/index",["./flickity","./drag","./prev-next-button","./page-dots","./player","./add-remove-cell","./lazyload"],t):"object"==typeof exports&&(module.exports=t(require("./flickity"),require("./drag"),require("./prev-next-button"),require("./page-dots"),require("./player"),require("./add-remove-cell"),require("./lazyload")))}((window,function(t){return t})),function(t,n){"use strict";"function"==typeof define&&define.amd?define("flickity-as-nav-for/as-nav-for",["classie/classie","flickity/js/index","fizzy-ui-utils/utils"],function(t,e,i){return n(0,t,e,i)}):"object"==typeof exports?module.exports=n(0,require("desandro-classie"),require("flickity"),require("fizzy-ui-utils")):t.Flickity=n(0,t.classie,t.Flickity,t.fizzyUIUtils)}(window,function(t,e,i,n){return i.createMethods.push("_createAsNavFor"),i.prototype._createAsNavFor=function(){this.on("activate",this.activateAsNavFor),this.on("deactivate",this.deactivateAsNavFor),this.on("destroy",this.destroyAsNavFor);var t,e=this.options.asNavFor;e&&(t=this,setTimeout(function(){t.setNavCompanion(e)}))},i.prototype.setNavCompanion=function(t){t=n.getQueryElement(t);var e,t=i.data(t);t&&t!=this&&(this.navCompanion=t,(e=this).onNavCompanionSelect=function(){e.navCompanionSelect()},t.on("cellSelect",this.onNavCompanionSelect),this.on("staticClick",this.onNavStaticClick),this.navCompanionSelect())},i.prototype.navCompanionSelect=function(){var t;this.navCompanion&&(t=this.navCompanion.selectedIndex,this.select(t),this.removeNavSelectedElement(),this.selectedIndex==t&&(this.navSelectedElement=this.cells[t].element,e.add(this.navSelectedElement,"is-nav-selected")))},i.prototype.activateAsNavFor=function(){this.navCompanionSelect()},i.prototype.removeNavSelectedElement=function(){this.navSelectedElement&&(e.remove(this.navSelectedElement,"is-nav-selected"),delete this.navSelectedElement)},i.prototype.onNavStaticClick=function(t,e,i,n){"number"==typeof n&&this.navCompanion.select(n)},i.prototype.deactivateAsNavFor=function(){this.removeNavSelectedElement()},i.prototype.destroyAsNavFor=function(){this.navCompanion&&(this.navCompanion.off("cellSelect",this.onNavCompanionSelect),this.off("staticClick",this.onNavStaticClick),delete this.navCompanion)},i}),function(i,n){"use strict";"function"==typeof define&&define.amd?define("imagesloaded/imagesloaded",["eventEmitter/EventEmitter","eventie/eventie"],function(t,e){return n(i,t,e)}):"object"==typeof module&&module.exports?module.exports=n(i,require("wolfy87-eventemitter"),require("eventie")):i.imagesLoaded=n(i,i.EventEmitter,i.eventie)}(window,function(e,t,i){var o=e.jQuery,n=e.console;function s(t,e){for(var i in e)t[i]=e[i];return t}var r=Object.prototype.toString;function a(t){var e,i=[];if(e=t,"[object Array]"==r.call(e))i=t;else if("number"==typeof t.length)for(var n=0;n<t.length;n++)i.push(t[n]);else i.push(t);return i}function l(t,e,i){if(!(this instanceof l))return new l(t,e,i);"string"==typeof t&&(t=document.querySelectorAll(t)),this.elements=a(t),this.options=s({},this.options),"function"==typeof e?i=e:s(this.options,e),i&&this.on("always",i),this.getImages(),o&&(this.jqDeferred=new o.Deferred);var n=this;setTimeout(function(){n.check()})}(l.prototype=new t).options={},l.prototype.getImages=function(){this.images=[];for(var t=0;t<this.elements.length;t++){var e=this.elements[t];this.addElementImages(e)}},l.prototype.addElementImages=function(t){"IMG"==t.nodeName&&this.addImage(t),!0===this.options.background&&this.addElementBackgroundImages(t);var e=t.nodeType;if(e&&h[e]){for(var i=t.querySelectorAll("img"),n=0;n<i.length;n++){var o=i[n];this.addImage(o)}if("string"==typeof this.options.background)for(var s=t.querySelectorAll(this.options.background),n=0;n<s.length;n++){var r=s[n];this.addElementBackgroundImages(r)}}};var h={1:!0,9:!0,11:!0};l.prototype.addElementBackgroundImages=function(t){for(var e=c(t),i=/url\(['"]*([^'"\)]+)['"]*\)/gi,n=i.exec(e.backgroundImage);null!==n;){var o=n&&n[1];o&&this.addBackground(o,t),n=i.exec(e.backgroundImage)}};var c=e.getComputedStyle||function(t){return t.currentStyle};function p(t){this.img=t}function d(t,e){this.url=t,this.element=e,this.img=new Image}return l.prototype.addImage=function(t){t=new p(t);this.images.push(t)},l.prototype.addBackground=function(t,e){e=new d(t,e);this.images.push(e)},l.prototype.check=function(){var n=this;if(this.progressedCount=0,this.hasAnyBroken=!1,this.images.length)for(var t=0;t<this.images.length;t++){var e=this.images[t];e.once("progress",i),e.check()}else this.complete();function i(t,e,i){setTimeout(function(){n.progress(t,e,i)})}},l.prototype.progress=function(t,e,i){this.progressedCount++,this.hasAnyBroken=this.hasAnyBroken||!t.isLoaded,this.emit("progress",this,t,e),this.jqDeferred&&this.jqDeferred.notify&&this.jqDeferred.notify(this,t),this.progressedCount==this.images.length&&this.complete(),this.options.debug&&n&&n.log("progress: "+i,t,e)},l.prototype.complete=function(){var t=this.hasAnyBroken?"fail":"done";this.isComplete=!0,this.emit(t,this),this.emit("always",this),this.jqDeferred&&(t=this.hasAnyBroken?"reject":"resolve",this.jqDeferred[t](this))},(p.prototype=new t).check=function(){this.getIsImageComplete()?this.confirm(0!==this.img.naturalWidth,"naturalWidth"):(this.proxyImage=new Image,i.bind(this.proxyImage,"load",this),i.bind(this.proxyImage,"error",this),i.bind(this.img,"load",this),i.bind(this.img,"error",this),this.proxyImage.src=this.img.src)},p.prototype.getIsImageComplete=function(){return this.img.complete&&void 0!==this.img.naturalWidth},p.prototype.confirm=function(t,e){this.isLoaded=t,this.emit("progress",this,this.img,e)},p.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},p.prototype.onload=function(){this.confirm(!0,"onload"),this.unbindEvents()},p.prototype.onerror=function(){this.confirm(!1,"onerror"),this.unbindEvents()},p.prototype.unbindEvents=function(){i.unbind(this.proxyImage,"load",this),i.unbind(this.proxyImage,"error",this),i.unbind(this.img,"load",this),i.unbind(this.img,"error",this)},(d.prototype=new p).check=function(){i.bind(this.img,"load",this),i.bind(this.img,"error",this),this.img.src=this.url,this.getIsImageComplete()&&(this.confirm(0!==this.img.naturalWidth,"naturalWidth"),this.unbindEvents())},d.prototype.unbindEvents=function(){i.unbind(this.img,"load",this),i.unbind(this.img,"error",this)},d.prototype.confirm=function(t,e){this.isLoaded=t,this.emit("progress",this,this.element,e)},(l.makeJQueryPlugin=function(t){(t=t||e.jQuery)&&((o=t).fn.imagesLoaded=function(t,e){return new l(this,t,e).jqDeferred.promise(o(this))})})(),l}),function(t,i){"use strict";"function"==typeof define&&define.amd?define(["flickity/js/index","imagesloaded/imagesloaded"],function(t,e){return i(0,t,e)}):"object"==typeof exports?module.exports=i(0,require("flickity"),require("imagesloaded")):t.Flickity=i(0,t.Flickity,t.imagesLoaded)}(window,function(t,e,n){"use strict";return e.createMethods.push("_createImagesLoaded"),e.prototype._createImagesLoaded=function(){this.on("activate",this.imagesLoaded)},e.prototype.imagesLoaded=function(){var i;this.options.imagesLoaded&&n((i=this).slider).on("progress",function(t,e){e=i.getParentCell(e.img),i.cellSizeChange(e&&e.element),i.options.freeScroll||i.positionSliderAtSelected()})},e});