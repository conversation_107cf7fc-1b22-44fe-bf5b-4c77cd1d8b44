<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Balance
 * @package   Balance_B2b
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Balance\B2b\Helper;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    const XML_B2B_STORE_TEMPLATE    = 'balanceb2b/general/email_template_store';

    const XML_B2B_CUSTOMER_TEMPLATE = 'balanceb2b/general/email_template';

    const XML_B2B_ENABLE            = 'balanceb2b/general/enabled';

    /**
     * @return mixed
     */
    public function getStoreTemplate()
    {
        return $this->scopeConfig->getValue(self::XML_B2B_STORE_TEMPLATE);
    }

    /**
     * @return mixed
     */
    public function getCustomerTemplate()
    {
        return $this->scopeConfig->getValue(self::XML_B2B_CUSTOMER_TEMPLATE);
    }


    /**
     * @return mixed
     */
    public function getEnabled()
    {
        return $this->scopeConfig->getValue(self::XML_B2B_ENABLE);
    }

}