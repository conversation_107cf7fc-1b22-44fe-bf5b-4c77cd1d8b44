<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Amasty
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Balance\Amasty\Block;

class Label extends \Magento\Framework\View\Element\Template
{
    const DISPLAY_PRODUCT  = 'display/product';
    const DISPLAY_CATEGORY = 'display/category';

    protected $randomKey = '';

    /**
     * @var \Amasty\Label\Helper\Config
     */
    private $helper;

    /**
     * @var \Amasty\Label\Model\Labels
     */
    protected $_product;

    /**
     * Label constructor.
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Amasty\Label\Helper\Config $helper
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Amasty\Label\Helper\Config $helper,
        array $data = []
    ) {
        $this->randomKey = rand();
        parent::__construct($context, $data);

        $this->helper = $helper;
        $this->setTemplate('Balance_Amasty::label.phtml');
        if ($this->getData('label')) {
            $this->setLabel($this->getData('label'));

            $id  = $this->getProduct()->getId();
            $labelId = $id.'_amasty_label_new';
            $this->addData([
                'cache_lifetime' => 86400,
                'cache_tags' => [
                    \Magento\Catalog\Model\Product::CACHE_TAG . '_' . $id,
                    \Amasty\Label\Model\Labels::CACHE_TAG . '_' . $labelId,
                ],
            ]);
        }
    }

    /**
     * @return int|string
     */
    public function getRandomKey()
    {
        return $this->randomKey;
    }

    /**
     * @return array
     */
    public function getCacheKeyInfo()
    {
        return [
            'AMASTY_LABEL_BLOCK',
            $this->randomKey,
            $this->_storeManager->getStore()->getId(),
            $this->_design->getDesignTheme()->getId(),
            $this->getProduct()->getId().'_new',
            'cat',
            $this->getProduct()->getId()
        ];
    }

    /**
     * @param $product
     * @return $this
     */
    public function setProduct($product)
    {
        $this->_product = $product;
        return $this;
    }

    /**
     * @return \Magento\Catalog\Model\Product
     */
    public function getProduct()
    {
        return $this->_product;
    }

    /**
     * Get container path from module settings
     *
     * @return string
     */
    public function getContainerPath()
    {
        return $path= $this->helper->getModuleConfig(self::DISPLAY_CATEGORY);
    }
}
