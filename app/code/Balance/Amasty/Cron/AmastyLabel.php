<?php
/**
 * @category  Balance
 * @package   Balance_Amasty
 * <AUTHOR> Internet
 * @copyright Copyright (c) 2018 Balance Internet (http://www.balanceinternet.com.au)
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License
 */

namespace Balance\Amasty\Cron;

class AmastyLabel
{

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @var \Magento\Indexer\Model\IndexerFactory
     */
    protected $indexer;

    /**
     * Constructor
     *
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Psr\Log\LoggerInterface $logger,
        \Magento\Indexer\Model\IndexerFactory $indexer
    ){
        $this->logger  = $logger;
        $this->indexer = $indexer;
    }

    /**
     * Execute the cron
     *
     * @return void
     */
    public function execute()
    {
        try {
            $indexerIds = array(
                'amasty_label',
                'amasty_label_main',
                'aw_arep_statistics',
            );
            foreach ($indexerIds as $indexerId) {
                echo " create index: ".$indexerId."\n";
                $this->logger->addInfo("Cronjob AmastyLabel is executed.".$indexerId.' re-indexed');
                $indexer = $this->indexer->create();
                $indexer->load($indexerId);
                $indexer->reindexAll();
            }
        } catch (\Exception $e) {
            $this->logger->addInfo($e->getMessage());
        }

    }
}
