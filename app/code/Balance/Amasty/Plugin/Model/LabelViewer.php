<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Amasty
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Balance\Amasty\Plugin\Model;

use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\View\LayoutFactory;

class LabelViewer
{
    /**
     * @var TimezoneInterface
     */
    protected $localeDate;

    /**
     * @var \Magento\Framework\View\LayoutFactory
     */
    private $layoutFactory;


    public function __construct(
        TimezoneInterface $localeDate,
        LayoutFactory $layoutFactory
    ) {
        $this->localeDate = $localeDate;
        $this->layoutFactory = $layoutFactory;
    }

    /**
     * @param \Amasty\Label\Model\LabelViewer $subject
     * @param \Closure $proceed
     * @param $product
     * @param string $mode
     * @param bool $shouldMove
     * @return mixed|string
     */
    public function aroundRenderProductLabel(\Amasty\Label\Model\LabelViewer $subject,
                                             \Closure $proceed,
                                            $product,
                                            $mode = 'category',
                                            $shouldMove = false)
    {

        $isNewProduct = $this->isNewProduct($product);
        $newContainer = '';
        if($isNewProduct) {
            $newContainer = $this->generateHtml($product);
        }
        $product->setIsNew($isNewProduct);
        $originalResult = $proceed($product,$mode,$shouldMove);

        if($isNewProduct) {
            $originalResult = $newContainer.$originalResult;
        }
        return $originalResult;
    }

    /**
     * @param $product
     * @return bool
     */
    private function isNewProduct($product)
    {
        $newsFromDate = $product->getNewsFromDate();
        $newsToDate = $product->getNewsToDate();
        if (!$newsFromDate && !$newsToDate) {
            return false;
        }

        return $this->localeDate->isScopeDateInInterval(
            $product->getStore(),
            $newsFromDate,
            $newsToDate
        );
    }

    /*
     * generate block with label configuration
     * @param \Amasty\Label\Model\Labels $label
     * @return string
     */
    private function generateHtml($product)
    {
        $layout = $this->layoutFactory->create();
        $block = $layout->createBlock(
            \Balance\Amasty\Block\Label::class,
            'amasty.label',
            ['data' => ['prodcut' => $product]]
        );
        $html = $block->setProduct($product)->toHtml();

        return $html;
    }

}