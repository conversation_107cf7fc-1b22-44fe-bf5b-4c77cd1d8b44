<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Amasty
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */
?>
<?php
/** @var \Balance\Amasty\Block\Labels  $label */
$product = $this->getProduct();
$keyNew = $product->getId() . '-' . $product->getId() . '-' . rand(1000, 9999).'-new';
$text = '';
$nativeText = strip_tags($text);
?>
<div class="amasty-label-conatiner" id="amasty-label-conatiner-<?php echo $keyNew;?>" style="display: none;">
    <div class="amasty-label-text">
        <?php echo $text;?>
    </div>
    <img class="amasty-label-image"
         id="amasty-label-image-<?php echo $keyNew;?>"
         src="<?php echo $block->getViewFileUrl('images/new_label.png'); ?>"
         style="opacity: 1 !important;"
         title="<?php echo $nativeText?>"
         alt="<?php echo $nativeText?>"
    />
</div>
<script>
    require([
        'jquery',
        'Amasty_Label/js/label',
        'domReady!'
    ], function ($) {
        var element = $('#amasty-label-conatiner-<?php echo $keyNew;?>');
        <?php $params = $this->getRequest()->getParams(); ?>
        <?php $productListMode = isset($params['product_list_mode']) ? $params['product_list_mode'] : ''?>
        <?php if ($productListMode == 'list'){?>
        element.amShowLabel({
            position: "top-left",
            size: "",
            path: ".more-info",
            mode: "cat",
            parent: ".product-item-info"
        });
        <?php } else {?>
        element.amShowLabel({
            position: "top-left",
            size: "",
            path: "<?php echo $this->getContainerPath()?>",
            mode: "cat"
        });
        <?php }?>
    });
</script>
