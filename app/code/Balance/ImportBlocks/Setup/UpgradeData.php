<?php
/**
 * Created by <PERSON>
 * Copyright (c) 2017 Balance Internet (http://www.balanceinternet.com.au/)
 * Date: 10/08/2017
 */
namespace Balance\ImportBlocks\Setup;

use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Cms\Model\BlockFactory;

class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var \Balance\ImportBlocks\Model\Page
     */
    private $page;

    /**
     * @var \Balance\ImportBlocks\Model\Block
     */
    private $block;

    /**
     * Block factory
     *
     * @var BlockFactory
     */
    private $__blockFactory;

    /**
     * @param \Balance\ImportBlocks\Model\Page $page
     * @param \Balance\ImportBlocks\Model\Block $block
     * @param BlockFactory $blockFactory
     */
    public function __construct(
        \Balance\ImportBlocks\Model\Page $page,
        \Balance\ImportBlocks\Model\Block $block,
        BlockFactory $blockFactory
    ) {
        $this->page  = $page;
        $this->block = $block;
        $this->__blockFactory = $blockFactory;

    }

    /**
     * Upgrades data for a module
     *
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface   $context
     * @return void
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        if (version_compare($context->getVersion(), '1.0.1') < 0) {
            /* Upgrade to 1.0.1 */
            $_block = $this->createBlock()->load('brand_content');
            $_data = array(
                'title' => 'Brand Content',
                'identifier' => 'brand_content',
                'is_active' => 1,
                'stores' => 0,
                'content' => '<div class="page-info-box">
                                    <div class="brands-page-title-wrapper">
                                    <h1 id="page-title-heading" class="page-title"><span class="base" data-ui-id="page-title-wrapper">Brands</span></h1>
                                    </div>
                                    <div class="page-short-description">
                                    <p>Lorem ipsum dolor sit amet, consectetur adipisicing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
                                    <p>Ut enim ad minim nisi ut aliquip ex ea commodo consequat. <a class="brand photo brand-item-photo" href="#">more...</a></p>
                                    </div>
                                    <div class="page-background-image"><img src="{{view url="images/brands/brand-title-bg.png"}}" alt="Brand Title Background" /></div>
                                </div>'
            );
            if ($_block->getId()) {
                try {
                    $_block->setContent($_data['content'])->save();
                } catch (Exception $e) {
                }
            } else {
                try {
                    $_block->setData($_data)->save();
                } catch (Exception $e) {
                }
            }
        }
        if (version_compare($context->getVersion(), '1.0.2') < 0) {
            /* Upgrade to 1.0.1 */
            $_block = $this->createBlock()->load('manual_verification_required_block');
            $_data = array(
                'title' => 'Manual Verification Required Block',
                'identifier' => 'manual_verification_required_block',
                'is_active' => 1,
                'stores' => 0,
                'content' => '<div>
                                    <p>Please contact our customer service of your closest store to start using points and see you order history.</p>
                                </div>'
            );
            if ($_block->getId()) {
                try {
                    $_block->setContent($_data['content'])->save();
                } catch (Exception $e) {
                }
            } else {
                try {
                    $_block->setData($_data)->save();
                } catch (Exception $e) {
                }
            }
        }

        $setup->endSetup();
    }

    /**
     * Create block
     *
     * @return Block
     */
    public function createBlock()
    {
        return $this->__blockFactory->create();
    }
}
