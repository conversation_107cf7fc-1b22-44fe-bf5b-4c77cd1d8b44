<?php
/**
 * Created by <PERSON>
 * Copyright (c) 2017 Balance Internet (http://www.balanceinternet.com.au/)
 * Date: 10/08/2017
 */
namespace Balance\ImportBlocks\Setup;

use Magento\Framework\Setup;

class Installer implements Setup\SampleData\InstallerInterface
{
    /**
     * @var \Balance\ImportBlocks\Model\Page
     */
    private $page;

    /**
     * @var \Balance\ImportBlocks\Model\Block
     */
    private $block;

    /**
     * @param \Balance\ImportBlocks\Model\Page  $page
     * @param \Balance\ImportBlocks\Model\Block $block
     */
    public function __construct(
        \Balance\ImportBlocks\Model\Page $page,
        \Balance\ImportBlocks\Model\Block $block
    ) {
        $this->page  = $page;
        $this->block = $block;
    }

    /**
     * {@inheritdoc}
     */
    public function install()
    {
        $version = '1.0.0';

        $this->block->install(['Balance_ImportBlocks::fixtures/blocks/' . $version . '/blocks.csv'], $version);

        $this->page->install(['Balance_ImportBlocks::fixtures/pages/' . $version . '/pages.csv'], $version);
    }
}