<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Balance\ImportBlocks\Helper;

use Magento\Framework\App\Helper\Context;
use Magento\Framework\Filesystem\Driver\File;
use Magento\Framework\Module\ModuleListInterface;
use Magento\Framework\ObjectManagerInterface;
use Magento\Framework\Setup\SampleData\Context as SampleDataContext;
use Magento\Framework\Stdlib\StringUtils;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\Helper\AbstractHelper;

class Theme extends AbstractHelper
{

    /**#@+
     * Constants defined for keys of data array
     */
    const MODULE_NAME = 'Balance_ImportBlocks';
    /**
     * @var \Magento\Framework\ObjectManagerInterface
     */
    protected $objectManager;
    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;
    /**
     * @var \Magento\Framework\Stdlib\StringUtils
     */
    protected $stringUtils;
    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $defaultLogger;
    /**
     * @var string
     */
    protected $channelName;
    /**
     * @var string
     */
    protected $logFile;
    /**
     * @var \Magento\Framework\Setup\SampleData\FixtureManager
     */
    protected $fixtureManager;
    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvReader;
    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    protected $file;

    /**
     * @var \Magento\Framework\Module\ModuleListInterface
     */
    protected $moduleList;

    /**
     * Theme helper constructor.
     *
     * @param Context                $context
     * @param ObjectManagerInterface $objectManager
     * @param StoreManagerInterface  $storeManager
     * @param StringUtils            $stringUtils
     * @param SampleDataContext      $sampleDataContext
     * @param ModuleListInterface    $moduleList
     * @param File                   $file
     */
    public function __construct(
        Context $context,
        ObjectManagerInterface $objectManager,
        StoreManagerInterface $storeManager,
        StringUtils $stringUtils,
        ModuleListInterface $moduleList,
        SampleDataContext $sampleDataContext,
        File $file
    ) {
        $this->storeManager   = $storeManager;
        $this->objectManager  = $objectManager;
        $this->stringUtils    = $stringUtils;
        $this->moduleList     = $moduleList;
        $this->defaultLogger  = $context->getLogger();
        $this->channelName    = 'balance-debug';
        $this->logFile        = BP . '/var/log/balance.log';
        $this->fixtureManager = $sampleDataContext->getFixtureManager();
        $this->csvReader      = $sampleDataContext->getCsvReader();
        $this->file           = $file;
        parent::__construct($context);
    }

    /**
     * Retrieve file id content from fixture
     *
     * @param string $fixture
     *
     * @return string|bool
     */
    public function getFixtureContent($fixture)
    {
        $fileContent = false;
        $fileName    = $this->fixtureManager->getFixture($fixture);
        if ($this->file->isExists($fileName)) {
            $fileContent = $this->file->fileGetContents($fileName);
        }

        return $fileContent;
    }

    /**
     * Retrieve module version
     *
     * @return string
     */
    public function getVersion()
    {
        return $this->moduleList->getOne(static::MODULE_NAME)['setup_version'];
    }

    /**
     * Check if any previous versions content exists
     *
     * @param string $version
     *
     * @return bool
     */
    protected function checkPreviousVersionContent($version)
    {
        //@TODO: Check if content of preivous version exists
        return ($version) ? false : false;
    }
}