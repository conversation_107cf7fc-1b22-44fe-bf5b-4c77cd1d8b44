<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Balance\ImportBlocks\Model;

use Balance\ImportBlocks\Helper\Theme as ThemeHelper;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Cms\Model\BlockFactory;
use Magento\Framework\Setup\SampleData\Context as SampleDataContext;

/**
 * Class Block
 */
class Block
{
    /**
     * @var \Magento\Framework\Setup\SampleData\FixtureManager
     */
    private $fixtureManager;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvReader;

    /**
     * @var \Magento\Cms\Model\BlockFactory
     */
    protected $blockFactory;

    /**
     * @var Block\Converter
     */
    protected $converter;

    /**
     * @var \Magento\Catalog\Api\CategoryRepositoryInterface
     */
    protected $categoryRepository;

    /**
     * @var ThemeHelper
     */
    protected $theme;

    /**
     * @param SampleDataContext           $sampleDataContext
     * @param BlockFactory                $blockFactory
     * @param Block\Converter             $converter
     * @param ThemeHelper                 $theme
     * @param CategoryRepositoryInterface $categoryRepository
     */
    public function __construct(
        SampleDataContext $sampleDataContext,
        BlockFactory $blockFactory,
        Block\Converter $converter,
        ThemeHelper $theme,
        CategoryRepositoryInterface $categoryRepository
    ) {
        $this->fixtureManager     = $sampleDataContext->getFixtureManager();
        $this->csvReader          = $sampleDataContext->getCsvReader();
        $this->blockFactory       = $blockFactory;
        $this->converter          = $converter;
        $this->theme              = $theme;
        $this->categoryRepository = $categoryRepository;
    }

    /**
     * @param array  $fixtures
     * @param string $version
     * @throws \Exception
     */
    public function install(array $fixtures, $version)
    {
        foreach ($fixtures as $fileName) {
            $fileName = $this->fixtureManager->getFixture($fileName);
            if (!file_exists($fileName)) {
                continue;
            }

            $rows   = $this->csvReader->getData($fileName);
            $header = array_shift($rows);

            foreach ($rows as $row) {
                $data = [];
                foreach ($row as $key => $value) {
                    $data[$header[$key]] = $value;
                }
                $row  = $data;
                $data = $this->converter->convertRow($row);
                unset($data['block']['content_file']);
                unset($data['block']['delete']);
                unset($data['block']['update']);
                if (!empty($row['content_file'])) {
                    $data['block']['content'] = $this->theme->getFixtureContent('Balance_ImportBlocks::fixtures/blocks/' . $version . '/content/'
                        . $row['content_file']);
                }

                if (!empty($row['delete'])) {
                    $this->deleteCmsBlock($data['block']);
                } else if (!empty($row['update'])) {
                    $cmsBlock = $this->saveCmsBlock($data['block'], true);
                } else {
                    $cmsBlock = $this->saveCmsBlock($data['block']);
                }

                $cmsBlock->unsetData();
            }
        }
    }

    /**
     * @param array $data
     *
     * @return \Magento\Cms\Model\Block
     */
    protected function deleteCmsBlock($data)
    {
        try {
            $cmsBlock = $this->blockFactory->create();
            $cmsBlock->getResource()->load($cmsBlock, $data['identifier']);
            $cmsBlock->delete();
        } catch (\Exception $e) {
            echo $e->getMessage();

            return false;
        }

    }

    /**
     * @param array $data
     *
     * @return \Magento\Cms\Model\Block
     */
    protected function saveCmsBlock($data)
    {
        $storeId = $data['storeviews'];
        unset($data['storeviews']);

        try {
            $cmsBlock = $this->blockFactory->create();
            $cmsBlock->setStoreId($storeId)->load($data['identifier']);
        } catch (\Exception $e) {
            echo $e->getMessage();

            return false;
        }
        if (!$cmsBlock->getData('identifier')) {
            $cmsBlock->setData($data);
        } else {
            $cmsBlock->addData($data);
        }
        $cmsBlock->setIsActive(1);
        $cmsBlock->setStores($storeId);
        $cmsBlock->save();

        return $cmsBlock;
    }

    /**
     * @param string $blockId
     * @param string $categoryId
     *
     * @return void
     */
    protected function setCategoryLandingPage($blockId, $categoryId)
    {
        $categoryCms = [
            'landing_page' => $blockId,
            'display_mode' => 'PRODUCTS_AND_PAGE',
        ];
        if (!empty($categoryId)) {
            $category = $this->categoryRepository->get($categoryId);
            $category->setData($categoryCms);
            $this->categoryRepository->save($categoryId);
        }
    }
}
