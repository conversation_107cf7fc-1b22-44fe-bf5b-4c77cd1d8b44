<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="balance_validatedata_command_configurableproducts" xsi:type="object">Balance\ValidateData\Command\ConfigurableProducts</item>
                <item name="balance_validatedata_command_urls" xsi:type="object">Balance\ValidateData\Command\Urls</item>
            </argument>
        </arguments>
    </type>
</config>