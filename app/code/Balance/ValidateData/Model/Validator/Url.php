<?php
/**
 * This class is also horrible and should be refactored
 *  - Danny
 */

namespace Balance\ValidateData\Model\Validator;

use Magento\Framework\App\ResourceConnection;
use Symfony\Component\Console\Output\OutputInterface;

class Url
{

    /**
     * Is developer output
     * @var bool
     */
    private $isDevOutput = false;

    /**
     * Product info map by entity ID
     * @var mixed[]
     */
    private $productMapByEntityId = [];

    /**
     * Product info map by row ID
     * @var mixed[]
     */
    private $productMapByRowId = [];

    /**
     * Product info map by SKU
     * @var mixed[]
     */
    private $productMapBySku = [];

    /**
     * Output interface
     * @var OutputInterface
     */
    private $output;

    /**
     * DB connection
     * @var \Magento\Framework\DB\Adapter\AdapterInterface
     */
    private $connection;

    /**
     * Product constructor
     *
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        ResourceConnection $resourceConnection
    ) {
        $this->connection = $resourceConnection->getConnection();
    }

    /**
     * Set the output interface
     *
     * @param OutputInterface $output
     * @return $this
     */
    public function setOutputInterface (OutputInterface $output)
    {
        $this->output = $output;
        return $this;
    }

    /**
     * Set developer output
     *
     * @param bool $on
     * @return $this
     */
    public function setDevOutput ($on)
    {
        $this->isDevOutput = $on;
        return $this;
    }

    /**
     * Validate products
     *
     * @return void
     */
    public function validate ()
    {
        $this->validateDuplicateProductUrls();
    }

    /**
     * Get product info by entity or row ID
     *
     * @param string $field
     * @param int $value
     * @return mixed|null
     */
    private function getProductInfo ($field, $value)
    {
        if (
            !$this->productMapByEntityId
            || !$this->productMapByRowId
            || !$this->productMapBySku
        ) {
            $sql = 'SELECT
                `row_id`,
                `entity_id`,
                `sku`
            FROM `catalog_product_entity`
            ORDER BY `row_id` ASC';
            $products = $this->connection->query($sql)->fetchAll();

            foreach ($products as $product) {
                $this->productMapByEntityId[(int) $product['entity_id']] = $product;
                $this->productMapByRowId[(int) $product['row_id']] = $product;
                $this->productMapBySku[$product['sku']] = $product;
            }
        }

        $info = null;
        switch ($field) {
            case 'entity_id':
                if (isset($this->productMapByEntityId[(int) $value])) {
                    $info = $this->productMapByEntityId[(int) $value];
                }
                break;
            case 'row_id':
                if (isset($this->productMapByRowId[(int) $value])) {
                    $info = $this->productMapByRowId[(int) $value];
                }
                break;
            case 'sku':
                if (isset($this->productMapBySku[$value])) {
                    $info = $this->productMapBySku[$value];
                }
                break;
        }
        return $info;
    }

    /**
     * Get product info message by entity or row ID
     * @param string $field
     * @param int $value
     * @return string
     */
    private function getProductInfoMessage ($field, $value)
    {
        $message = '[NOT_FOUND]';

        $format = '%s';
        if ($this->isDevOutput) {
            $format = '[SKU: %s, Entity: %s, Row: %s]';
        }

        $info = $this->getProductInfo($field, $value);
        if ($info) {
            $message = sprintf($format, $info['sku'], $info['entity_id'], $info['row_id']);
        }

        return $message;
    }

    /**
     * Get products info message by list of entity or row IDs
     * @param string $field
     * @param int[] $values
     * @return string
     */
    private function getProductsInfoMessage($field, $values)
    {
        $messages = [];
        foreach ($values as $value) {
            $messages[] = '<comment>' . $this->getProductInfoMessage($field, $value) . '</comment>';
        }
        return implode(', ', $messages);
    }

    /**
     * Validate configurable products are not a child of other products
     *
     * @return void
     */
    private function validateDuplicateProductUrls ()
    {
        $errors = [];

        $sql = 'SELECT `value`
        FROM `catalog_product_entity_varchar`
        WHERE
            `store_id` = 0
            AND `attribute_id` IN (
                SELECT `attribute_id`
                FROM `eav_attribute`
                WHERE `attribute_code` = "url_key"
            )
        GROUP BY `value`
        HAVING count(`value`) > 1
        ORDER BY `value` ASC';
        $duplicateUrls = $this->connection->query($sql)->fetchAll(\Zend_Db::FETCH_COLUMN);

        foreach ($duplicateUrls as $duplicateUrl) {
            $sql      = 'SELECT `row_id`
            FROM `catalog_product_entity_varchar`
            WHERE
                `store_id` = 0
                AND `value` = "' . $duplicateUrl . '"';
            $rowIds   = $this->connection->query($sql)->fetchAll(\Zend_Db::FETCH_COLUMN);
            $errors[] = [
                'row_ids' => $rowIds,
                'url_key' => $duplicateUrl,
            ];
        }

        if ($errors) {
            $message = '

    Found products with duplicate URL keys. Consider:    
     - Deleting duplicate products
     - Changing URL key
';
            $this->output->writeln(sprintf('<error>%s</error>', $message));
            $this->output->writeln('');

            $message = 'URL key <comment>%s</comment> is shared by products: %s';
            foreach ($errors as $error) {
                $products = $this->getProductsInfoMessage('row_id', $error['row_ids']);
                $this->output->writeln(sprintf($message, $error['url_key'], $products));
            }
        }
    }

}