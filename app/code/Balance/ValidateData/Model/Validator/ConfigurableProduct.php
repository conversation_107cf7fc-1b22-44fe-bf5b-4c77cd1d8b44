<?php
/**
 * This class is horrible and should be refactored
 *  - Danny
 */

namespace Balance\ValidateData\Model\Validator;

use Magento\Framework\App\ResourceConnection;
use Symfony\Component\Console\Output\OutputInterface;

class ConfigurableProduct
{

    /**
     * Is developer output
     * @var bool
     */
    private $isDevOutput = false;

    /**
     * Product info map by entity ID
     * @var mixed[]
     */
    private $productMapByEntityId = [];

    /**
     * Product info map by row ID
     * @var mixed[]
     */
    private $productMapByRowId = [];

    /**
     * Product info map by SKU
     * @var mixed[]
     */
    private $productMapBySku = [];

    /**
     * Attribute info map by ID
     * @var mixed[]
     */
    private $attributeMap = [];

    /**
     * DB connection
     * @var \Magento\Framework\DB\Adapter\AdapterInterface
     */
    private $connection;

    /**
     * Output interface
     * @var OutputInterface
     */
    private $output;

    /**
     * Product constructor
     *
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        ResourceConnection $resourceConnection
    ) {
        $this->connection = $resourceConnection->getConnection();
    }

    /**
     * Get product info by entity or row ID
     *
     * @param string $field
     * @param int $value
     * @return mixed|null
     */
    private function getProductInfo ($field, $value)
    {
        if (
            !$this->productMapByEntityId
            || !$this->productMapByRowId
            || !$this->productMapBySku
        ) {
            $sql = 'SELECT
                `row_id`,
                `entity_id`,
                `sku`
            FROM `catalog_product_entity`
            ORDER BY `row_id` ASC';
            $products = $this->connection->query($sql)->fetchAll();

            foreach ($products as $product) {
                $this->productMapByEntityId[(int) $product['entity_id']] = $product;
                $this->productMapByRowId[(int) $product['row_id']] = $product;
                $this->productMapBySku[$product['sku']] = $product;
            }
        }

        $info = null;
        switch ($field) {
            case 'entity_id':
                if (isset($this->productMapByEntityId[(int) $value])) {
                    $info = $this->productMapByEntityId[(int) $value];
                }
                break;
            case 'row_id':
                if (isset($this->productMapByRowId[(int) $value])) {
                    $info = $this->productMapByRowId[(int) $value];
                }
                break;
            case 'sku':
                if (isset($this->productMapBySku[$value])) {
                    $info = $this->productMapBySku[$value];
                }
                break;
        }
        return $info;
    }

    /**
     * Get attribute info by ID
     *
     * @param int $attributeId
     * @return mixed|null
     */
    private function getAttributeInfo ($attributeId)
    {
        if (!$this->attributeMap) {
            $sql = 'SELECT
                `attribute_id`,
                `attribute_code`,
                `backend_type`
            FROM `eav_attribute`
            ORDER BY `attribute_id` ASC';
            $attributes = $this->connection->query($sql)->fetchAll();
            foreach ($attributes as $attribute) {
                $this->attributeMap[(int) $attribute['attribute_id']] = $attribute;
            }
        }

        $info = null;
        if (isset($this->attributeMap[(int) $attributeId])) {
            $info = $this->attributeMap[(int) $attributeId];
        }
        return $info;
    }

    /**
     * Get product info message by entity or row ID
     * @param string $field
     * @param int $value
     * @return string
     */
    private function getProductInfoMessage ($field, $value)
    {
        $message = '[NOT_FOUND]';

        $format = '%s';
        if ($this->isDevOutput) {
            $format = '[SKU: %s, Entity: %s, Row: %s]';
        }

        $info = $this->getProductInfo($field, $value);
        if ($info) {
            $message = sprintf($format, $info['sku'], $info['entity_id'], $info['row_id']);
        }

        return $message;
    }

    /**
     * Get attribute info message by ID
     *
     * @param int $attributeId
     * @return string
     */
    private function getAttributeInfoMessage ($attributeId)
    {
        $message = '[NOT_FOUND]';

        $format = '%s';
        if ($this->isDevOutput) {
            $format = '[Code: %s, ID: %s, Type: %s]';
        }

        $info = $this->getAttributeInfo($attributeId);
        if ($info) {
            $message = sprintf($format, $info['attribute_code'], $info['attribute_id'], $info['backend_type']);
        }

        return $message;
    }

    /**
     * Validate child products are not their own parent
     *
     * @return void
     */
    private function validateParentIsNotChild ()
    {
        $sql = 'SELECT
            `parent`.`sku`,
            CONCAT("DELETE FROM `catalog_product_relation` WHERE `parent_id` = ", `link`.`parent_id`, " AND `child_id` = ", `link`.`child_id`, ";") AS `delete_query`
        FROM `catalog_product_relation` AS `link`
        LEFT JOIN `catalog_product_entity` AS `parent` ON `parent`.`row_id` = `link`.`parent_id`
        LEFT JOIN `catalog_product_entity` AS `child` ON `child`.`entity_id` = `link`.`child_id`
        WHERE `parent`.`sku` = `child`.`sku`
        ORDER BY `parent`.`row_id` ASC';
        $relationResults = $this->connection->query($sql)->fetchAll();

        $sql = 'SELECT
            `parent`.`sku`,
            CONCAT("DELETE FROM `catalog_product_super_link` WHERE `link_id` = ", `link`.`link_id`, ";") AS `delete_query`
        FROM `catalog_product_super_link` AS `link`
        LEFT JOIN `catalog_product_entity` AS `parent` ON `parent`.`row_id` = `link`.`parent_id`
        LEFT JOIN `catalog_product_entity` AS `child` ON `child`.`entity_id` = `link`.`product_id`
        WHERE `parent`.`sku` = `child`.`sku`
        ORDER BY `parent`.`row_id` ASC';
        $superResults = $this->connection->query($sql)->fetchAll();

        $results = array_merge($relationResults, $superResults);

        if (count($results)) {
            $message = '

    Found child products that are their own parents. Consider:
     - Deleting the product
     - Deleting the link via SQL
';
            $this->output->writeln(sprintf('<error>%s</error>', $message));
            $this->output->writeln('');

            $message = '<comment>%s</comment> is it\'s own parent%s';
            foreach ($results as $result) {
                $product = $this->getProductInfoMessage('sku', $result['sku']);
                $devOutput = ($this->isDevOutput) ?
                    ', delete query: <comment>' . $result['delete_query'] . '</comment>' : '';
                $this->output->writeln(sprintf($message, $product, $devOutput));
            }
        }
    }

    /**
     * Validate configurable products are not a child of other products
     *
     * @return void
     */
    private function validateConfigurableIsNotChild ()
    {
        $sql = 'SELECT
            `link_id`,
            `product_id` AS `child_entity_id`,
            `parent_id` AS `parent_row_id`
        FROM `catalog_product_super_link`
        WHERE `product_id` IN (
            SELECT `entity_id`
            FROM `catalog_product_entity`
            WHERE `type_id` = "configurable"
        )
        ORDER BY
            `product_id` ASC,
            `parent_id` ASC';
        $superResults = $this->connection->query($sql)->fetchAll();

        $sql = 'SELECT
            CONCAT("child_id = ", `child_id`, " AND parent_id = ", `parent_id`) AS `link_id`,
            `child_id` AS `child_entity_id`,
            `parent_id` AS `parent_row_id`
        FROM `catalog_product_relation`
        WHERE `child_id` IN (
                SELECT `entity_id`
            FROM `catalog_product_entity`
            WHERE `type_id` = "configurable"
        )
        ORDER BY
            `child_id` ASC,
            `parent_id` ASC';
        $relationResults = $this->connection->query($sql)->fetchAll();

        $results = array_merge($superResults, $relationResults);

        if (count($results)) {
            $message = '

    Found configurable products that are children. Consider:    
     - Changing to simple product
     - Deleting the link
';
            $this->output->writeln(sprintf('<error>%s</error>', $message));
            $this->output->writeln('');

            $message = '<comment>%s</comment> is a configurable, but is a child of <comment>%s</comment>';
            if ($this->isDevOutput) {
                $message .= ' (link_id %s)';
            }
            foreach ($results as $result) {
                $child  = $this->getProductInfoMessage('entity_id', $result['child_entity_id']);
                $parent = $this->getProductInfoMessage('row_id', $result['parent_row_id']);
                $this->output->writeln(sprintf($message, $child, $parent, $result['link_id']));
            }
        }
    }

    /**
     * Validate configurable products have at least one child
     *
     * @return void
     */
    private function validateConfigurableHasChildren ()
    {
        $sql = 'SELECT `entity_id`
        FROM `catalog_product_entity`
        WHERE
            `type_id` = "configurable"
            AND `row_id` NOT IN (
                SELECT `parent_id`
                FROM `catalog_product_super_link`
            )
        ORDER BY `entity_id` ASC';
        $superResults = $this->connection->query($sql)->fetchAll(\Zend_Db::FETCH_COLUMN);

        $sql = 'SELECT `entity_id`
        FROM `catalog_product_entity`
        WHERE
            `type_id` = "configurable"
            AND `row_id` NOT IN (
        SELECT `parent_id`
                FROM `catalog_product_relation`
            )
        ORDER BY `entity_id` ASC';
        $relationResults = $this->connection->query($sql)->fetchAll(\Zend_Db::FETCH_COLUMN);

        $results = array_merge($superResults, $relationResults);
        $results = array_unique($results);

        if (count($results)) {
            $message = '

    Found configurable products without children. Consider:    
     - Changing to simple product
     - Assigning children
';
            $this->output->writeln(sprintf('<error>%s</error>', $message));
            $this->output->writeln('');

            $message = '<comment>%s</comment> is a configurable, but has no children';
            foreach ($results as $result) {
                $product = $this->getProductInfoMessage('entity_id', $result);
                $this->output->writeln(sprintf($message, $product));
            }
        }
    }

    /**
     * Validate configurable products have a configurable attribute assigned
     *
     * @return void
     */
    private function validateConfigurableHasSuperAttribute ()
    {
        $sql = 'SELECT `entity_id`
        FROM `catalog_product_entity`
        WHERE
            `type_id` = "configurable"
            AND `row_id` NOT IN (
                SELECT `product_id`
                FROM `catalog_product_super_attribute`
            )
        ORDER BY `entity_id` ASC';
        $results = $this->connection->query($sql)->fetchAll();

        if (count($results)) {
            $message = '

    Found configurable products with no attributes assigned. Consider:    
     - Changing to simple product
     - Assigning a configurable attribute (CPSA)
';
            $this->output->writeln(sprintf('<error>%s</error>', $message));
            $this->output->writeln('');

            $message = '<comment>%s</comment> is a configurable, but has no configurable attributes assigned';
            foreach ($results as $result) {
                $product = $this->getProductInfoMessage('entity_id', $result['entity_id']);
                $this->output->writeln(sprintf($message, $product));
            }
        }
    }

    /**
     * Validate configurable products have only one configurable attribute assigned
     *
     * @return void
     */
    private function validateConfigurableHasOneSuperAttribute ()
    {
        $sql = 'SELECT `product_id` AS `product_row_id`
        FROM `catalog_product_super_attribute`
        GROUP BY `product_id`
        HAVING count(`product_id`) > 1
        ORDER BY `product_id` ASC';
        $results = $this->connection->query($sql)->fetchAll();

        if (count($results)) {
            $message = '

    Found configurable products with more than one configurable attribute. Consider:    
     - This could be correct
     - Remove configurable attribute assignments
';
            $this->output->writeln(sprintf('<error>%s</error>', $message));
            $this->output->writeln('');

            $message = '<comment>%s</comment> is a configurable, and has more than one configurable attribute assigned';
            foreach ($results as $result) {
                $product = $this->getProductInfoMessage('row_id', $result['product_row_id']);
                $this->output->writeln(sprintf($message, $product));
            }
        }
    }

    /**
     * Validate child products have values for the attributes they are configured by
     *
     * @return void
     */
    private function validateChildrenHaveSuperAttributeData ()
    {
        $sql = 'SELECT
            DISTINCT `catalog_product_super_attribute`.`attribute_id`,
            `eav_attribute`.`backend_type`
        FROM `catalog_product_super_attribute`
        LEFT JOIN `eav_attribute`
            ON `catalog_product_super_attribute`.`attribute_id` = `eav_attribute`.`attribute_id`
        ORDER BY `catalog_product_super_attribute`.`attribute_id` ASC';
        $configurableAttributes = $this->connection->query($sql)->fetchAll();
        $configurableAttributeTypesMap = [];
        foreach ($configurableAttributes as $attribute) {
            $configurableAttributeTypesMap[(int) $attribute['attribute_id']] = $attribute['backend_type'];
        }

        $sql = 'SELECT
            `product_id` AS `parent_row_id`,
            `attribute_id`
        FROM `catalog_product_super_attribute`
        ORDER BY
            `product_id` ASC,
            `position` ASC';
        $parents = $this->connection->query($sql)->fetchAll();
        $parentsMap = [];
        foreach ($parents as $parent) {
            $parentsMap[(int) $parent['parent_row_id']] = (int) $parent['attribute_id'];
        }

        $sql = 'SELECT
            `catalog_product_entity`.`row_id` AS `child_row_id`,
            `parent_id` AS `parent_row_id`
        FROM `catalog_product_super_link`
        LEFT JOIN `catalog_product_entity`
            ON `catalog_product_super_link`.`product_id` = `catalog_product_entity`.`entity_id`
        ORDER BY `product_id` ASC';
        $superChildren = $this->connection->query($sql)->fetchAll();

        $sql = 'SELECT
            `catalog_product_entity`.`row_id` AS `child_row_id`,
            `parent_id` AS `parent_row_id`
        FROM `catalog_product_relation`
        LEFT JOIN `catalog_product_entity`
            ON `catalog_product_relation`.`child_id` = `catalog_product_entity`.`entity_id`
        ORDER BY `parent_id` ASC';
        $relationChildren = $this->connection->query($sql)->fetchAll();

        $exists = [];
        $children = [];
        foreach (array_merge($superChildren, $relationChildren) as $result) {
            $key = (int) $result['parent_row_id'] . '_' . (int) $result['child_row_id'];
            if (in_array($key, $exists)) {
                continue;
            }
            $children[] = $result;
            $exists[] = $key;
        }

        $errors = [];
        foreach ($children as $child) {
            $parentRowId = (int) $child['parent_row_id'];
            $childRowId  = (int) $child['child_row_id'];
            if (!isset($parentsMap[$parentRowId])) {
                continue;
            }
            $attributeId = $parentsMap[$parentRowId];
            if (!isset($configurableAttributeTypesMap[$attributeId])) {
                continue;
            }
            $type = $configurableAttributeTypesMap[$attributeId];

            $sql        = 'SELECT count(`value_id`)
            FROM `catalog_product_entity_' . $type . '`
            WHERE
                `row_id` = ' . $childRowId . '
                AND `attribute_id` = ' . $attributeId;
            $valueCount = (int) $this->connection->query($sql)->fetch(\Zend_Db::FETCH_COLUMN);

            if (!$valueCount) {
                $errors[] = [
                    'child_row_id' => $childRowId,
                    'attribute_id' => $attributeId,
                ];
            }
        }

        if ($errors) {
            $message = '

    Found child products with not attribute data for their configurable attributes. Consider:    
     - Adding value for the attribute
     - Removing this child from the parent
     - Changing the parents configurable attribute
';
            $this->output->writeln(sprintf('<error>%s</error>', $message));
            $this->output->writeln('');

            foreach ($errors as $error) {
                $message = '<comment>%s</comment> should have value for <comment>%s</comment>';
                $product = $this->getProductInfoMessage('row_id', $error['child_row_id']);
                $attribute = $this->getAttributeInfoMessage($error['attribute_id']);
                $this->output->writeln(sprintf($message, $product, $attribute));
            }
        }
    }

    /**
     * Set the output interface
     *
     * @param OutputInterface $output
     * @return $this
     */
    public function setOutputInterface (OutputInterface $output)
    {
        $this->output = $output;
        return $this;
    }

    /**
     * Set developer output
     *
     * @param bool $on
     * @return $this
     */
    public function setDevOutput ($on)
    {
        $this->isDevOutput = $on;
        return $this;
    }

    /**
     * Validate products
     *
     * @return void
     */
    public function validate()
    {
        $this->validateParentIsNotChild();
        $this->validateConfigurableIsNotChild();
        $this->validateConfigurableHasChildren();
        $this->validateConfigurableHasSuperAttribute();
        $this->validateConfigurableHasOneSuperAttribute();
        $this->validateChildrenHaveSuperAttributeData();
    }

}