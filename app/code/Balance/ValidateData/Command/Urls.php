<?php

namespace Balance\ValidateData\Command;

use Balance\ValidateData\Model\Validator\Url as UrlValidator;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class Urls extends Command
{

    const NAME = 'balance:validate-data:urls';
    const DESC = 'Validates URL data for potential integrity errors';

    const OPT_DEV_OUTPUT_NAME = 'dev-output';
    const OPT_DEV_OUTPUT_DESC = 'Developer output';

    /**
     * Url validator
     * @var UrlValidator
     */
    private $urlValidator;

    /**
     * Urls constructor
     *
     * @param UrlValidator $urlValidator
     */
    public function __construct(
        UrlValidator $urlValidator
    ) {
        parent::__construct();
        $this->urlValidator = $urlValidator;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this
            ->setName(self::NAME)
            ->setDescription(self::DESC)
            ->addOption(
                self::OPT_DEV_OUTPUT_NAME,
                null,
                InputOption::VALUE_NONE,
                self::OPT_DEV_OUTPUT_DESC
            );
        parent::configure();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $this->urlValidator
            ->setOutputInterface($output)
            ->setDevOutput($input->getOption(self::OPT_DEV_OUTPUT_NAME))
            ->validate();
    }

}