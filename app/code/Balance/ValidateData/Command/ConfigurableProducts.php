<?php

namespace Balance\ValidateData\Command;

use Balance\ValidateData\Model\Validator\ConfigurableProduct as ConfigurableProductValidator;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Input\InputOption;
use Symfony\Component\Console\Output\OutputInterface;

class ConfigurableProducts extends Command
{

    const NAME = 'balance:validate-data:configurable-products';
    const DESC = 'Validates configurable product data for potential integrity errors';

    const OPT_DEV_OUTPUT_NAME = 'dev-output';
    const OPT_DEV_OUTPUT_DESC = 'Developer output';

    /**
     * Product validator
     * @var ConfigurableProductValidator
     */
    private $productValidator;

    /**
     * Products constructor
     *
     * @param ConfigurableProductValidator $productValidator
     */
    public function __construct(
        ConfigurableProductValidator $productValidator
    ) {
        parent::__construct();
        $this->productValidator = $productValidator;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this
            ->setName(self::NAME)
            ->setDescription(self::DESC)
            ->addOption(
                self::OPT_DEV_OUTPUT_NAME,
                null,
                InputOption::VALUE_NONE,
                self::OPT_DEV_OUTPUT_DESC
            );
        parent::configure();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    ) {
        $this->productValidator
            ->setOutputInterface($output)
            ->setDevOutput($input->getOption(self::OPT_DEV_OUTPUT_NAME))
            ->validate();
    }

}