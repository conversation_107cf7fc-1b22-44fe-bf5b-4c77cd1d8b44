<?php
/**
 * Copyright © 2019 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Balance\Signifyd\Plugin\Magento\Sales\Model\Order\Email\Sender;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\ResourceModel\Order as OrderResource;

class OrderSender
{

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $_storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\ShippingZoneRepository
     */
    protected $_shippingZoneRepository;

    /**
     * @var \Magento\Quote\Model\Quote\AddressFactory
     */
    private $quoteAddress;

    /**
     * @var \Totaltools\Storelocator\Model\StoreAdminEmail
     */
    private $storeAdminEmail;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @var OrderResource
     */
    protected $orderResource;

    /**
     * OrderSender constructor.
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\StoreAdminEmail $storeAdminEmail
     * @param \Totaltools\Storelocator\Model\ShippingZoneRepository $shippingZoneRepository
     * @param \Magento\Quote\Model\Quote\AddressFactory $quoteAddressFactory
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreAdminEmail $storeAdminEmail,
        \Totaltools\Storelocator\Model\ShippingZoneRepository $shippingZoneRepository,
        \Magento\Quote\Model\Quote\AddressFactory $quoteAddressFactory,
        \Psr\Log\LoggerInterface $logger,
        OrderResource $orderResource
    ) {
        $this->_storeRepository = $storeRepository;
        $this->_shippingZoneRepository = $shippingZoneRepository;
        $this->quoteAddress = $quoteAddressFactory;
        $this->storeAdminEmail = $storeAdminEmail;
        $this->logger = $logger;
        $this->orderResource = $orderResource;
    }

    /**
     * @param \Magento\Sales\Model\Order\Email\Sender\OrderSender $subject
     * @param \Closure $proceed
     * @param bool $forceSyncMode
     * @param $order
     * @return bool|mixed
     */
    public function aroundSend(
        \Magento\Sales\Model\Order\Email\Sender\OrderSender $subject,
        \Closure $proceed,
        Order $order,
        $forceSyncMode = false
    ) {
        $code = $order->getPayment()->getMethodInstance()->getCode();
        if(($code == 'braintree' || $code == 'braintree_cc_vault' || $code == 'braintree_applepay' || $code == 'braintree_googlepay')
            && $forceSyncMode === false) {
            $order->setRecaptchaBypass(1);
            $this->orderResource->saveAttribute($order, 'recaptcha_bypass');
            return false;
        } else {
            if($forceSyncMode) {
                try {
                    $storeId = (int)$order->getStorelocatorId();
                    /** @var \Totaltools\Storelocator\Model\Store $store */
                    $store = $this->_storeRepository->getById($storeId);
                    $this->storeAdminEmail->sendMail($store, $order);
                } catch (\Exception $e) {
                    $this->logger->critical($e->__toString(), array('entity' => $order));
                }
            }
            $result = $proceed($order, $forceSyncMode);
            return $result;
        }
    }
}
