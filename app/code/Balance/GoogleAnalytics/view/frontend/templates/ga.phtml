<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php
/** @var \Balance\GoogleAnalytics\Block\Ga $block */
if (!$block->isUserNotAllowSaveCookie()) {
    $accountType = $block->getConfig(\Magento\GoogleTagManager\Helper\Data::XML_PATH_TYPE);
    $gtmAccountId = $block->getConfig(\Magento\GoogleTagManager\Helper\Data::XML_PATH_CONTAINER_ID);
    $gapAccountId = $block->getConfig(\Magento\GoogleTagManager\Helper\Data::XML_PATH_ACCOUNT);
    ?>
    <!-- CLIENT SNIPPED -->
    <style>.async-hide {
            opacity: 0 !important
        }

    </style>
    <script>(function (a, s, y, n, c, h, i, d, e) {
            s.className += ' ' + y;
            h.start = 1 * new Date;
            h.end = i = function () {
                s.className = s.className.replace(RegExp(' ?' + y), '')
            }

            ;
            (a[n] = a[n] || []).hide = h;
            setTimeout(function () {
                    i();
                    h.end = null
                }

                , c);
            h.timeout = c;
        })(window, document.documentElement, 'async-hide', 'dataLayer', 4000,
            {'<?php /* @escapeNotVerified */ echo $block->getPageRequireId();?>': true}
        );</script>

    <!-- END CLIENT SNIPPED -->

    <?php switch ($accountType) {
        case \Magento\GoogleTagManager\Helper\Data::TYPE_TAG_MANAGER:
            if (!empty($gtmAccountId)) {
                ?>
                <!-- GOOGLE TAG MANAGER -->
                <noscript>
                    <iframe src="//www.googletagmanager.com/ns.html?id=<?php /* @escapeNotVerified */
                    echo $gtmAccountId ?>" height="0" width="0"
                            style="display:none;visibility:hidden"></iframe>
                </noscript>
                <script>
                    //<![CDATA[
                    (function (w, d, s, l, i) {
                        w[l] = w[l] || [];
                        w[l].push({'gtm.start': new Date().getTime(), event: 'gtm.js'});
                        var f = d.getElementsByTagName(s)[0];
                        var j = d.createElement(s);
                        var dl = l != 'dataLayer' ? '&l=' + l : '';
                        j.async = true;
                        j.src = '//www.googletagmanager.com/gtm.js?id=' + i + dl;
                        f.parentNode.insertBefore(j, f);
                    })(window, document, 'script', 'dataLayer', '<?php /* @escapeNotVerified */ echo $gtmAccountId ?>');

                    var dlCurrencyCode = '<?php /* @escapeNotVerified */ echo $block->getStoreCurrencyCode() ?>';
                    <?php /* @escapeNotVerified */ echo $block->getOrdersData() ?>
                    //]]>
                </script>
                <!-- END GOOGLE TAG MANAGER -->
                <?php
            }
            break;
        case \Magento\GoogleTagManager\Helper\Data::TYPE_UNIVERSAL:
            if (!empty($gapAccountId)) {
                ?>
                <!-- BEGIN GOOGLE UNIVERSAL ANALYTICS CODE -->
                <script>
                    //<![CDATA[
                    (function (i, s, o, g, r, a, m) {
                        i['GoogleAnalyticsObject'] = r;
                        i[r] = i[r] || function () {
                                (i[r].q = i[r].q || []).push(arguments)
                            }, i[r].l = 1 * new Date();
                        a = s.createElement(o),
                            m = s.getElementsByTagName(o)[0];
                        a.async = 1;
                        a.src = g;
                        m.parentNode.insertBefore(a, m)
                    })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

                    <?php /* @escapeNotVerified */ echo $block->getPageTrackingCode($gapAccountId) ?>

                    <?php
                    $successBlock = $block->getLayout()->getBlock('checkout.success');
                    if (!empty($successBlock)) {
                        $orderIds = array();
                        $orderIds = $block->getOrderIds();
                        if (empty($orderIds)) {
                            $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
                            $session = $objectManager->get('\Magento\Checkout\Model\Session');
                            $order=$session->getLastRealOrder();
                            $orderId = $order->getId();
                            if (!empty($orderId)) $orderIds[] = $orderId;
                            $block->setOrderIds($orderIds);
                        }
                    }
                    /* @escapeNotVerified */ echo $block->getOrdersTrackingCode();
                    ?>
                    //]]>
                </script>
                <!-- END GOOGLE UNIVERSAL ANALYTICS CODE -->
                <?php
            }
            break;
    }
}