<?php
/**
 * Balance Internet
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category
 * @package
 * @copyright   Copyright (c) 2017 Balance Internet. (https://www.balanceinternet.com.au)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

namespace Balance\GoogleAnalytics\Block;

class Ga extends \Magento\GoogleTagManager\Block\Ga {


    /**
     * Render regular page tracking javascript code
     * The custom "page name" may be set from layout or somewhere else. It must start from slash.
     *
     * @param string $accountId
     * @return string
     * @link https://developers.google.com/analytics/devguides/collection/analyticsjs/method-reference#set
     * @link https://developers.google.com/analytics/devguides/collection/analyticsjs/method-reference#gaObjectMethods
     */
    public function getPageTrackingCode($accountId)
    {
        $pageName = trim($this->getPageName());
        $optPageURL = '';
        if ($pageName && substr($pageName, 0, 1) == '/' && strlen($pageName) > 1) {
            $optPageURL = ", '{$this->escapeJsQuote($pageName)}'";
        }

        $requireId = $this->getConfig(\Balance\GoogleAnalytics\Helper\Data::XML_PATH_REQUIRE_ID);

        return "\nga('create', '{$this->escapeJsQuote(
            $accountId
        )}', 'auto');\nga('require', '{$this->escapeJsQuote($requireId)}');\nga('send', 'pageview'{$optPageURL});\n";
    }

    public function getPageRequireId() {
        return $this->escapeJsQuote($this->getConfig(\Balance\GoogleAnalytics\Helper\Data::XML_PATH_REQUIRE_ID));
    }


}