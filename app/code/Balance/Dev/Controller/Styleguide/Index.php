<?php
namespace Balance\Dev\Controller\Styleguide;

class Index
    extends \Magento\Framework\App\Action\Action
{


    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;


    /**
     * @var \Magento\Framework\View\Page\Config
     */
    protected $pageConfig;


    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Magento\Framework\View\Page\Config $pageConfig
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context
        , \Magento\Framework\View\Result\PageFactory $resultPageFactory
        , \Magento\Framework\View\Page\Config $pageConfig
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->pageConfig = $pageConfig;
    }



    public function execute () {
        $this->pageConfig->getTitle()->set('Styleguide');
        return $this->resultPageFactory->create();
    }


}
