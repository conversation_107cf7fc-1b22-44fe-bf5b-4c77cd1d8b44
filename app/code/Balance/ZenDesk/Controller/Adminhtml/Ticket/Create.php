<?php
/**
 * Balance Internet
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category
 * @package
 * @copyright   Copyright (c) 2018 Balance Internet. (https://www.balanceinternet.com.au)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

namespace Balance\ZenDesk\Controller\Adminhtml\Ticket;

use Magento\Backend\App\Action;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultFactory;

class Create extends \Wagento\Zendesk\Controller\Adminhtml\Ticket\Create
{
    /**
     * @var \Wagento\Zendesk\Helper\Api\Ticket
     */
    private $ticket;

    /**
     * @var \Magento\Sales\Api\OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * Create constructor.
     * @param Action\Context $context
     * @param \Wagento\Zendesk\Helper\Api\Ticket $ticket
     */
    public function __construct(
        Action\Context $context,
        \Wagento\Zendesk\Helper\Api\Ticket $ticket,
        \Wagento\Zendesk\Helper\Api\User $userApi,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Wagento\Zendesk\Helper\Data $helper,
        \Magento\Backend\Model\Auth\Session $authSession,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
    ) {

        parent::__construct(
            $context,
            $ticket,
            $userApi,
            $customerFactory,
            $helper,
            $authSession);
        $this->ticket = $ticket;
        $this->orderRepository = $orderRepository;
    }

    /**
     * Execute action based on request and return result
     *
     * Note: Request will be added as operation argument in future
     *
     * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        if (!$this->_formKeyValidator->validate($this->getRequest())) {
            $this->messageManager->addErrorMessage('Can\' create ticket please try again');
            return $resultRedirect->setPath('*/*/edit');
        }

        $data = $this->getRequest()->getParams();

        /** Fix Magento 2.0 */
        if(isset($data["general"])){
            $data = array_merge($data, $data["general"]);
            unset($data["general"]);
        }

        $requester = trim($data["requester"]);
        if (!empty($data["customer_email"])) {
            $requester = $data["customer_email"];
        }
        $requesterName = trim($data["requester_name"]);
        $websiteId = trim($data["website_id"]);
        /** Create the Request Id */
        $requestId = $this->createRequest($requester, $requesterName, $websiteId);
        $submitterId = $this->getSubmitterId() ? $this->getSubmitterId() : "";
        $ticket = [
            'requester_id' => $requestId,
            'submitter_id' => $submitterId,
            'subject' => $data['subject'],
            'status' => $data['status'],
            'priority' => $data['priority'],
            'comment' => [
                'value' => $data['description']
            ]
        ];
        /** Add additional options  */
        if (isset($data['type']) && strlen(trim($data['type'])) > 0) {
            $ticket['type'] = $data['type'];
        }

        if ($fieldId = $this->helper->getOrderField() && isset($data['order_id']) && strlen(trim($data['order_id'])) > 0) {
            $order = $this->orderRepository->get($data['order_id']);
            $orderIncrementId = $order->getIncrementId();
            $ticket['fields'] = [
                'id' => $this->helper->getOrderField(),
                'value' => $orderIncrementId
            ];
        }

        $ticketId = $this->ticket->create($ticket);
        if (isset($ticketId)) {
            $this->messageManager->addSuccessMessage(sprintf('Ticket Created successfully, the id is %s.', $ticketId));
            return $resultRedirect->setPath('*/*');
        }

        $this->messageManager->addErrorMessage("Error while creating new ticket, Make sure that connection was configured correctly.");
        return $resultRedirect->setPath('*/*/edit');
    }
}
