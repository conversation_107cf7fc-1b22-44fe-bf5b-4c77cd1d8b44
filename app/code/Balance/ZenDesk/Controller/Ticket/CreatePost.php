<?php
/**
 * Balance Internet
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category
 * @package
 * @copyright   Copyright (c) 2018 Balance Internet. (https://www.balanceinternet.com.au)
 * @license     http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

namespace Balance\ZenDesk\Controller\Ticket\CreatePost;

use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultFactory;
use Wagento\Zendesk\Controller\AbstractUserAuthentication;

class CreatePost extends \Wagento\Zendesk\Controller\Ticket\CreatePost
{
    /**
     * @var \Wagento\Zendesk\Helper\Api\Ticket
     */
    private $ticket;
    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var \Magento\Sales\Api\OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * CreatePost constructor.
     * @param Context $context
     * @param \Wagento\Zendesk\Helper\Api\Ticket $ticket
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        Context $context,
        \Wagento\Zendesk\Helper\Api\Ticket $ticket,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
    ) {
    
        parent::__construct(
            $context,
            $ticket,
            $customerSession,
            $scopeConfig
        );
        $this->ticket = $ticket;
        $this->customerSession = $customerSession;
        $this->scopeConfig = $scopeConfig;
        $this->orderRepository = $orderRepository;
    }

    /**
     * Execute action based on request and return result
     *
     * Note: Request will be added as operation argument in future
     *
     * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        $customer = $this->customerSession->getCustomerDataObject();
        $customerAttribute = $customer->getCustomAttribute('zd_user_id');

        $backParams = [];
        if ($customerAttribute) {
            $endUserId = $customerAttribute->getValue();
            $data = $this->getRequest()->getParams();

            $params = [
                'requester_id' => $endUserId,
                'submitter_id' => $endUserId,
                'subject' => $data['subject'],
                'comment' => [
                    'body' => $data['comment']
                ],
            ];

            // Add extra attributes validation
            $status = $this->scopeConfig->getValue('zendesk/ticket/priority');
            if ($status) {
                $params['status'] = $status;
            }
            $type = $this->scopeConfig->getValue('zendesk/ticket/status');
            if ($type) {
                $params['type'] = $type;
            }
            $priority = $this->scopeConfig->getValue('zendesk/ticket/type');
            if ($priority) {
                $params['priority'] = $priority;
            }

            // Verify order number send
            $sendOrderNumber = $this->scopeConfig->getValue('zendesk/ticket/add_order_number');
            if ($sendOrderNumber && isset($data['order']) && $data['order'] != -1) {
                $ticketFieldId = $this->scopeConfig->getValue('zendesk/ticket/order_field_id');
                $order = $this->orderRepository->get($data['order_id']);
                $orderIncrementId = $order->getIncrementId();
                $params['custom_fields'] = [
                    [
                        'id' => $ticketFieldId,
                        'value' => $orderIncrementId
                    ]
                ];
                // assign order id param in case something went wrong
                $backParams['orderid'] = $data['order'];
            }

            $response = $this->ticket->create($params);
            if (is_numeric($response)) {
                $this->messageManager->addSuccessMessage('Ticket create successfully.');
                return $resultRedirect->setPath('*/customer/tickets');
            }
        }

        $this->messageManager->addErrorMessage('Try again, if problem persist contact with store support.');
        return $resultRedirect->setPath('*/*/create', $backParams);
    }
}
