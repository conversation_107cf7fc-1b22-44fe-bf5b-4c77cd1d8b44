<?php
/**
 * Copyright © 2020 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Balance\ClearMedia\Console\Command;

use Symfony\Component\Console\Command\Command;
use Magento\Framework\App\State;
use Magento\Framework\App\ResourceConnection;
use Magento\Catalog\Model\Product\Media\Config;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use \Magento\Framework\Filesystem\Driver\File;

class ClearMedia extends Command
{

    /**
     * @var State
     */
    protected $state;

    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;

    /**
     * @var Config
     */
    protected $config;

    /**
     * @var Filesystem
     */
    protected $filesystem;

    /**
     * @var File
     */
    protected $file;

    /**
     * ClearMedia constructor.
     * @param State $state
     * @param ResourceConnection $resourceConnection
     * @param Config $config
     * @param Filesystem $filesystem
     * @param File $file
     */
    public function __construct(
        State $state,
        ResourceConnection $resourceConnection,
        Config $config,
        Filesystem $filesystem,
        File $file
    )
    {
        $this->state = $state;
        $this->resourceConnection = $resourceConnection;
        $this->config = $config;
        $this->filesystem = $filesystem;
        $this->file = $file;
        parent::__construct();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void|null
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    )
    {
        $this->state->setAreaCode('adminhtml');
        $mediaDir = $this->filesystem->getDirectoryWrite(DirectoryList::MEDIA);

        $connection = $this->resourceConnection->getConnection('core_write');
        $tableName = $this->resourceConnection->getTableName('catalog_product_entity_media_gallery');
        $result = $connection->fetchAll('SELECT * FROM '.$tableName.' WHERE media_type = "image"');
        $count = 1;
        foreach ($result as $item) {
            $imagePath = $mediaDir->getAbsolutePath('/catalog/product'.$item['value']);
            if (!$this->file->isExists($imagePath)) {
                $sql = "DELETE FROM " . $tableName . " WHERE value_id = " . $item['value_id'];
                $connection->query($sql);
                $count=$count+1;
            }
        }

        $output->writeln($count ."Not existing image records are deleted.");
    }

    /**
     * configure function
     */
    protected function configure()
    {
        $this->setName("balance:clearmedia");
        $this->setDescription("Clear Media Images");
        parent::configure();
    }
}
