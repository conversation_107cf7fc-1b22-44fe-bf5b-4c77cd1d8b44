<?xml version="1.0"?>
<!--

-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <signifyd>
            <general>
                <enabled>0</enabled>
                <!--The device fingerprint enable configuration must be kept only at the default and database with no UI-->
                <!--option to change it at the admin (MAG1-55)-->
                <enable_device_fingerprint>1</enable_device_fingerprint>

                <restrict_states_default>pending_payment,payment_review,canceled,closed,complete</restrict_states_default>
                <restrict_states_create>holded,pending_payment,payment_review,canceled,closed,complete</restrict_states_create>
                <restrict_payment_methods>checkmo,cashondelivery,banktransfer,purchaseorder,zippayment</restrict_payment_methods>
                <payment_methods_config>{"CREDIT_CARD":["payflow_link", "payflow_advanced", "authorizenet_acceptjs", "adyen_cc", "braintree", "cybersource", "stripe_payments", "anet_creditcard", "authorizenet_directpost"],"CHECK":["checkmo"]}</payment_methods_config>
                <shipper_config>{"FEDEX":["fedex"],"DHL":["dhl"],"SHIPWIRE":[],"USPS":["usps"],"UPS":["ups"]}</shipper_config>
                <shipping_method_config>{"EXPRESS":["FEDEX_EXPRESS_SAVER", "7", "B", "C", "D", "U", "K", "L", "I", "N", "T", "X", "INT_4", "INT_5", "INT_6", "INT_7", "54", "07"],"ELECTRONIC":[],"FIRST_CLASS":["0_FCLE", "0_FCL", "0_FCP", "0_FCPC", "15", "53", "61", "INT_13", "INT_14", "INT_15", "INT_21"],"FIRST_CLASS_INTERNATIONAL":[],"FREE":["freeshipping"],"FREIGHT":["FEDEX_1_DAY_FREIGHT", "FEDEX_2_DAY_FREIGHT", "FEDEX_3_DAY_FREIGHT", "INTERNATIONAL_ECONOMY_FREIGHT", "INTERNATIONAL_PRIORITY_FREIGHT", "FEDEX_FREIGHT", "FEDEX_NATIONAL_FREIGHT"],"GROUND":["FEDEX_GROUND", "GROUND_HOME_DELIVERY", "INTERNATIONAL_GROUND", "4", "03"],"INTERNATIONAL":["INTERNATIONAL_ECONOMY", "INTERNATIONAL_FIRST"],"OVERNIGHT":["FIRST_OVERNIGHT", "PRIORITY_OVERNIGHT", "STANDARD_OVERNIGHT"],"PRIORITY":["1", "2", "3", "13", "16", "17", "22", "23", "25", "27", "28", "29", "30", "31", "32", "33", "34", "35", "36", "37", "38", "39", "40", "41", "42", "43", "44", "45", "46", "47", "48", "49", "50", "57", "58", "59", "62", "63", "64", "INT_1", "INT_2", "INT_8", "INT_9", "INT_10", "INT_11", "INT_16", "INT_17", "INT_18", "INT_19", "INT_20", "INT_22", "INT_23", "INT_24", "INT_25", "INT_27"],"PRIORITY_INTERNATIONAL":["EUROPE_FIRST_INTERNATIONAL_PRIORITY", "INTERNATIONAL_PRIORITY"],"PICKUP":["pickup"],"STANDARD":["11"],"STORE_TO_STORE":[],"TWO_DAY":["FEDEX_2_DAY", "FEDEX_2_DAY_AM", "59", "02"]}</shipping_method_config>
                <async_payment_methods>cybersource</async_payment_methods>
            </general>
            <advanced>
                <guarantee_negative_action>nothing</guarantee_negative_action>
                <guarantee_positive_action>nothing</guarantee_positive_action>
            </advanced>
            <logs>
                <log>1</log>
            </logs>

            <gateway_integration>
                <anet_creditcard>{"gateway":"\\Signifyd\\Models\\Payment\\Authorizenet","params":{"name":{"type":"path","path":"authorize_net/anet_core/login_id"},"transactionKey":{"type":"path","path":"authorize_net/anet_core/trans_key"},"environment":{"type":"path","path":"payment/authorizenet_acceptjs/environment"}}}</anet_creditcard>
                <braintree>{"gateway":"\\Signifyd\\Models\\Payment\\Braintree","params":{"publicKeySandbox":{"type":"path","path":"payment/braintree/sandbox_public_key"},"privateKeySandbox":{"type":"path","path":"payment/braintree/sandbox_private_key"},"publicKey":{"type":"path","path":"payment/braintree/public_key"},"privateKey":{"type":"path","path":"payment/braintree/private_key"},"environment":{"type":"path","path":"payment/braintree/environment"}}}</braintree>
            </gateway_integration>

            <payment>
                <adyen_cc>
                    <signifyd_avs_ems_adapter>Signifyd\Connect\Model\Payment\AdyenCc\AvsEmsCodeMapper</signifyd_avs_ems_adapter>
                    <signifyd_bin_adapter>Signifyd\Connect\Model\Payment\AdyenCc\BinMapper</signifyd_bin_adapter>
                    <signifyd_cvv_ems_adapter>Signifyd\Connect\Model\Payment\AdyenCc\CvvEmsCodeMapper</signifyd_cvv_ems_adapter>
                    <signifyd_exp_month_adapter>Signifyd\Connect\Model\Payment\AdyenCc\ExpMonthMapper</signifyd_exp_month_adapter>
                    <signifyd_exp_year_adapter>Signifyd\Connect\Model\Payment\AdyenCc\ExpYearMapper</signifyd_exp_year_adapter>
                    <signifyd_last4_adapter>Signifyd\Connect\Model\Payment\AdyenCc\Last4Mapper</signifyd_last4_adapter>
                    <signifyd_transaction_id_adapter>Signifyd\Connect\Model\Payment\AdyenCc\TransactionIdMapper</signifyd_transaction_id_adapter>
                </adyen_cc>

                <authorizenet_directpost>
                    <signifyd_cvv_ems_adapter>Signifyd\Connect\Model\Payment\Authorizenet\CvvEmsCodeMapper</signifyd_cvv_ems_adapter>
                    <signifyd_last4_adapter>Signifyd\Connect\Model\Payment\Authorizenet\Last4Mapper</signifyd_last4_adapter>
                    <signifyd_exp_month_adapter>Signifyd\Connect\Model\Payment\Authorizenet\ExpMonthMapper</signifyd_exp_month_adapter>
                    <signifyd_exp_year_adapter>Signifyd\Connect\Model\Payment\Authorizenet\ExpYearMapper</signifyd_exp_year_adapter>
                    <signifyd_transaction_id_adapter>Signifyd\Connect\Model\Payment\Authorizenet\TransactionIdMapper</signifyd_transaction_id_adapter>
                </authorizenet_directpost>

                <braintree>
                    <signifyd_avs_ems_adapter>Signifyd\Connect\Model\Payment\Braintree\AvsEmsCodeMapper</signifyd_avs_ems_adapter>
                    <signifyd_cvv_ems_adapter>Signifyd\Connect\Model\Payment\Braintree\CvvEmsCodeMapper</signifyd_cvv_ems_adapter>
                </braintree>

                <cybersource>
                    <signifyd_avs_ems_adapter>Signifyd\Connect\Model\Payment\Cybersource\AvsEmsCodeMapper</signifyd_avs_ems_adapter>
                    <signifyd_bin_adapter>Signifyd\Connect\Model\Payment\Cybersource\BinMapper</signifyd_bin_adapter>
                    <signifyd_cvv_ems_adapter>Signifyd\Connect\Model\Payment\Cybersource\CvvEmsCodeMapper</signifyd_cvv_ems_adapter>
                    <signifyd_exp_month_adapter>Signifyd\Connect\Model\Payment\Cybersource\ExpMonthMapper</signifyd_exp_month_adapter>
                    <signifyd_exp_year_adapter>Signifyd\Connect\Model\Payment\Cybersource\ExpYearMapper</signifyd_exp_year_adapter>
                    <signifyd_last4_adapter>Signifyd\Connect\Model\Payment\Cybersource\Last4Mapper</signifyd_last4_adapter>
                    <signifyd_transaction_id_adapter>Signifyd\Connect\Model\Payment\Cybersource\TransactionIdMapper</signifyd_transaction_id_adapter>
                </cybersource>

                <payflowpro>
                    <signifyd_avs_ems_adapter>Signifyd\Connect\Model\Payment\Payflow\Pro\AvsEmsCodeMapper</signifyd_avs_ems_adapter>
                    <signifyd_cvv_ems_adapter>Signifyd\Connect\Model\Payment\Payflow\Pro\CvvEmsCodeMapper</signifyd_cvv_ems_adapter>
                </payflowpro>

                <payflow_link>
                    <signifyd_avs_ems_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\AvsEmsCodeMapper</signifyd_avs_ems_adapter>
                    <signifyd_cvv_ems_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\CvvEmsCodeMapper</signifyd_cvv_ems_adapter>
                    <signifyd_last4_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\Last4Mapper</signifyd_last4_adapter>
                    <signifyd_exp_month_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\ExpMonthMapper</signifyd_exp_month_adapter>
                    <signifyd_exp_year_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\ExpYearMapper</signifyd_exp_year_adapter>
                </payflow_link>

                <payflow_advanced>
                    <signifyd_avs_ems_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\AvsEmsCodeMapper</signifyd_avs_ems_adapter>
                    <signifyd_cvv_ems_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\CvvEmsCodeMapper</signifyd_cvv_ems_adapter>
                    <signifyd_last4_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\Last4Mapper</signifyd_last4_adapter>
                    <signifyd_exp_month_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\ExpMonthMapper</signifyd_exp_month_adapter>
                    <signifyd_exp_year_adapter>Signifyd\Connect\Model\Payment\Payflow\Link\ExpYearMapper</signifyd_exp_year_adapter>
                </payflow_advanced>

                <stripe_payments>
                    <signifyd_avs_ems_adapter>Signifyd\Connect\Model\Payment\Stripe\Payments\AvsEmsCodeMapper</signifyd_avs_ems_adapter>
                    <signifyd_cvv_ems_adapter>Signifyd\Connect\Model\Payment\Stripe\Payments\CvvEmsCodeMapper</signifyd_cvv_ems_adapter>
                    <signifyd_last4_adapter>Signifyd\Connect\Model\Payment\Stripe\Payments\Last4Mapper</signifyd_last4_adapter>
                    <signifyd_exp_month_adapter>Signifyd\Connect\Model\Payment\Stripe\Payments\ExpMonthMapper</signifyd_exp_month_adapter>
                    <signifyd_exp_year_adapter>Signifyd\Connect\Model\Payment\Stripe\Payments\ExpYearMapper</signifyd_exp_year_adapter>
                </stripe_payments>
            </payment>
        </signifyd>
    </default>
</config>
