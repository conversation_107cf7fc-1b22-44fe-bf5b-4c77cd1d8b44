<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label" sortOrder="0">
            <label>Total Tools</label>
        </tab>
        <section id="totaltools_vii" translate="label" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Vii</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Vii::config_vii</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>General</label>
                <field id="staging" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Staging Mode</label>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
                <field id="debug" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Debug</label>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
            </group>
            <group id="authentication" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Authentication</label>
                <group id="staging" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Staging</label>
                    <field id="redemption_url" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Redemption URL</label>
                    </field>
                    <field id="order_url" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Order URL</label>
                    </field>
                    <field id="username" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Username</label>
                    </field>
                    <field id="password" translate="label" type="password" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Password</label>
                        <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    </field>
                </group>
                <group id="production" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Production</label>
                    <field id="redemption_url" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Redemption URL</label>
                    </field>
                    <field id="order_url" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Order URL</label>
                    </field>
                    <field id="username" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Username</label>
                    </field>
                    <field id="password" translate="label" type="password" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Password</label>
                        <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    </field>
                </group>
            </group>
            <group id="code" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Codes</label>
                <field id="store_id" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>StoreId</label>
                </field>
                <field id="stored_value_card_order_source_id" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>StoredValueCardOrderSourceId</label>
                </field>
                <field id="physical_card_design" translate="label" type="text" sortOrder="25" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Physical Gift CardDesign</label>
                </field>
                <field id="virtual_card_design" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Virtual Gift CardDesign</label>
                </field>
                <field id="bin" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>BIN</label>
                </field>
                <field id="gift_card_program_id" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>GiftCardProgramId</label>
                </field>
            </group>
        </section>
    </system>
</config>
