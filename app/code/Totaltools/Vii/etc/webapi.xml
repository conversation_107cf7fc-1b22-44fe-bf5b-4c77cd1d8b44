<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <!--add gift cart account for customer-->
    <route url="/V1/integration/mine/giftCards" method="POST">
        <service class="Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface" method="saveByQuoteId"/>
        <resources>
            <resource ref="self" />
        </resources>
        <data>
            <parameter name="cartId" force="true">%cart_id%</parameter>
        </data>
    </route>
    <!--add gift cart account for guest-->
    <route url="/V1/integration/guest-carts/:cartId/giftCards" method="POST">
        <service class="Totaltools\Vii\Api\RewriteGuestGiftCardAccountManagementInterface" method="addGiftCard"/>
        <resources>
            <resource ref="anonymous" />
        </resources>
    </route>
    <!--check gift cart account for guest-->
    <route url="/V1/integration/guest-carts/:cartId/checkGiftCard/:giftCardCode/:giftCardPin" method="GET">
        <service class="Totaltools\Vii\Api\RewriteGuestGiftCardAccountManagementInterface" method="checkGiftCard"/>
        <resources>
            <resource ref="anonymous" />
        </resources>
    </route>
    <!--check gift cart account for customer-->
    <route url="/V1/integration/mine/checkGiftCard/:giftCardCode/:giftCardPin" method="GET">
        <service class="Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface" method="checkGiftCard"/>
        <resources>
            <resource ref="self" />
        </resources>
        <data>
            <parameter name="cartId" force="true">%cart_id%</parameter>
        </data>
    </route>

    <route url="/V1/integration/:cartId/giftCards/:giftCardCode" method="DELETE">
        <service class="Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface" method="deleteByQuoteId"/>
        <resources>
            <resource ref="Magento_GiftCardAccount::customer_giftcardaccount" />
        </resources>
    </route>
    <!--delete gift cart account for guest-->
    <route url="/V1/integration/guest-carts/:cartId/giftCards/:giftCardCode" method="DELETE">
        <service class="Totaltools\Vii\Api\RewriteGuestGiftCardAccountManagementInterface" method="deleteByQuoteId"/>
        <resources>
            <resource ref="anonymous" />
        </resources>
    </route>
    <!--delete gift cart account for customer-->
    <route url="/V1/integration/mine/giftCards/:giftCardCode" method="DELETE">
        <service class="Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface" method="deleteByQuoteId"/>
        <resources>
            <resource ref="self" />
        </resources>
        <data>
            <parameter name="cartId" force="true">%cart_id%</parameter>
        </data>
    </route>
</routes>
