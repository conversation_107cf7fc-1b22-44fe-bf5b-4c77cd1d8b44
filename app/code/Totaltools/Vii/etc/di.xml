<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\GiftCardAccount\Model\Giftcardaccount" type="Totaltools\Vii\Model\RewriteGiftCardAccount" />
    <preference for="Totaltools\Vii\Api\RewriteGuestGiftCardAccountManagementInterface" type="Totaltools\Vii\Model\GuestCart\RewriteGiftCardAccountManagement"/>
    <preference for="Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface" type="Totaltools\Vii\Model\Service\RewriteGiftCardAccountManagement"/>
    <preference for="Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface" type="Totaltools\Vii\Model\RewriteGiftCardAccount"/>
    <preference for="Magento\GiftCardAccount\Model\Total\Quote\Giftcardaccount" type="Totaltools\Vii\Model\Plugin\GiftcardaccountRewrite"/>
    <preference for="Magento\GiftCardAccount\Observer\ProcessOrderPlace" type="Totaltools\Vii\Observer\RewriteProcessOrderPlace"/>
    <preference for="Magento\GiftCardAccount\Observer\PaymentDataImport" type="Totaltools\Vii\Observer\RewritePaymentDataImport"/>
    <preference for="Totaltools\Vii\Api\QueueRepositoryInterface" type="Totaltools\Vii\Model\ResourceModel\QueueRepository"/>
    <preference for="Totaltools\Vii\Api\Data\QueueInterface" type="Totaltools\Vii\Model\Queue"/>
    <type name="Magento\GiftCard\Observer\GenerateGiftCardAccountsOrder">
        <plugin name="total_tools_observer_generate_gift_card_accounts_order" type="Totaltools\Vii\Plugin\Observer\GenerateGiftCardAccountsOrder" />
    </type>
    <type name="Magento\GiftCard\Observer\GenerateGiftCardAccountsInvoice">
        <plugin name="total_tools_observer_generate_gift_card_accounts_invoice" type="Totaltools\Vii\Plugin\Observer\GenerateGiftCardAccountsInvoice" />
    </type>
</config>
