<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */

namespace Totaltools\Vii\Helper;

class Config
    extends \Magento\Framework\App\Helper\AbstractHelper
{

    const PATH_DEBUG   = 'totaltools_vii/general/debug';
    const PATH_STAGING = 'totaltools_vii/general/staging';

    const PATH_AUTH_STAGING_REDEMPTION_URL = 'totaltools_vii/authentication/staging/redemption_url';
    const PATH_AUTH_STAGING_ORDER_URL      = 'totaltools_vii/authentication/staging/order_url';
    const PATH_AUTH_STAGING_USERNAME       = 'totaltools_vii/authentication/staging/username';
    const PATH_AUTH_STAGING_PASSWORD       = 'totaltools_vii/authentication/staging/password';

    const PATH_AUTH_PRODUCTION_REDEMPTION_URL = 'totaltools_vii/authentication/production/redemption_url';
    const PATH_AUTH_PRODUCTION_ORDER_URL      = 'totaltools_vii/authentication/production/order_url';
    const PATH_AUTH_PRODUCTION_USERNAME       = 'totaltools_vii/authentication/production/username';
    const PATH_AUTH_PRODUCTION_PASSWORD       = 'totaltools_vii/authentication/production/password';

    const PATH_CODE_STORE_ID                          = 'totaltools_vii/code/store_id';
    const PATH_CODE_STORED_VALUE_CARD_ORDER_SOURCE_ID = 'totaltools_vii/code/stored_value_card_order_source_id';
    const PATH_CODE_PHYSICAL_CARD_DESIGN              = 'totaltools_vii/code/physical_card_design';
    const PATH_CODE_VIRTUAL_CARD_DESIGN               = 'totaltools_vii/code/virtual_card_design';
    const PATH_CODE_BIN                               = 'totaltools_vii/code/bin';
    const PATH_CODE_GIFT_CARD_PROGRAM_ID              = 'totaltools_vii/code/gift_card_program_id';


    /**
     * @var  boolean
     */
    protected $debug;

    /**
     * @var  boolean
     */
    protected $staging;

    /**
     * @var  string
     */
    protected $apiRedemptionUrl;

    /**
     * @var  string
     */
    protected $apiOrderUrl;

    /**
     * @var  string
     */
    protected $apiUsername;

    /**
     * @var  string
     */
    protected $apiPassword;

    /**
     * @var  string
     */
    protected $codeStoreId;

    /**
     * @var  string
     */
    protected $codeStoredValueCardOrderSourceId;

    /**
     * @var  string
     */
    protected $codePhysicalCardDesign;

    /**
     * @var  string
     */
    protected $codeVirtualCardDesign;

    /**
     * @var  string
     */
    protected $codeBin;

    /**
     * @var  string
     */
    protected $codeGiftCardProgramId;


    /**
     * @var  \Magento\Framework\Encryption\EncryptorInterface
     */
    protected $encryptor;


    /**
     * @param  \Magento\Framework\App\Helper\Context $context
     * @param  \Magento\Framework\Encryption\EncryptorInterface $encryptor
     */
    public function __construct (
        \Magento\Framework\App\Helper\Context $context
        , \Magento\Framework\Encryption\EncryptorInterface $encryptor
    ) {
        parent::__construct($context);
        $this->encryptor = $encryptor;
    }


    /**
     * @return  boolean
     */
    public function isDebug () {
        if (!$this->debug) {
            $this->debug = $this->scopeConfig->isSetFlag(self::PATH_DEBUG);
        }
        return $this->debug;
    }


    /**
     * @return  boolean
     */
    public function isStaging () {
        if (!$this->staging) {
            $this->staging = $this->scopeConfig->isSetFlag(self::PATH_STAGING);
        }
        return $this->staging;
    }


    /**
     * @return  string
     */
    public function getApiRedemptionUrl () {
        if (!$this->apiRedemptionUrl) {
            $path = ($this->isStaging()) ?
                self::PATH_AUTH_STAGING_REDEMPTION_URL :
                self::PATH_AUTH_PRODUCTION_REDEMPTION_URL
            ;
            $this->apiRedemptionUrl = $this->scopeConfig->getValue($path);
        }
        return $this->apiRedemptionUrl;
    }


    /**
     * @return  string
     */
    public function getApiOrderUrl () {
        if (!$this->apiOrderUrl) {
            $path = ($this->isStaging()) ?
                self::PATH_AUTH_STAGING_ORDER_URL :
                self::PATH_AUTH_PRODUCTION_ORDER_URL
            ;
            $this->apiOrderUrl = $this->scopeConfig->getValue($path);
        }
        return $this->apiOrderUrl;
    }


    /**
     * @return  string
     */
    public function getApiUsername () {
        if (!$this->apiUsername) {
            $path = ($this->isStaging()) ?
                self::PATH_AUTH_STAGING_USERNAME :
                self::PATH_AUTH_PRODUCTION_USERNAME
            ;
            $this->apiUsername = $this->scopeConfig->getValue($path);
        }
        return $this->apiUsername;
    }


    /**
     * @return  string
     */
    public function getApiPassword () {
        if (!$this->apiPassword) {
            $path = ($this->isStaging()) ?
                self::PATH_AUTH_STAGING_PASSWORD :
                self::PATH_AUTH_PRODUCTION_PASSWORD
            ;
            $this->apiPassword = $this->encryptor->decrypt($this->scopeConfig->getValue($path));
        }
        return $this->apiPassword;
    }


    /**
     * @return  string
     */
    public function getCodeStoreId () {
        if (!$this->codeStoreId) {
            $this->codeStoreId = $this->scopeConfig->getValue(self::PATH_CODE_STORE_ID);
        }
        return $this->codeStoreId;
    }


    /**
     * @return  string
     */
    public function getCodeStoredValueCardOrderSourceId () {
        if (!$this->codeStoredValueCardOrderSourceId) {
            $this->codeStoredValueCardOrderSourceId = $this->scopeConfig->getValue(self::PATH_CODE_STORED_VALUE_CARD_ORDER_SOURCE_ID);
        }
        return $this->codeStoredValueCardOrderSourceId;
    }


    /**
     * @return  string
     */
    public function getCodePhysicalCardDesign () {
        if (!$this->codePhysicalCardDesign) {
            $this->codePhysicalCardDesign = $this->scopeConfig->getValue(self::PATH_CODE_PHYSICAL_CARD_DESIGN);
        }
        return $this->codePhysicalCardDesign;
    }


    /**
     * @return  string
     */
    public function getCodeVirtualCardDesign () {
        if (!$this->codeVirtualCardDesign) {
            $this->codeVirtualCardDesign = $this->scopeConfig->getValue(self::PATH_CODE_VIRTUAL_CARD_DESIGN);
        }
        return $this->codeVirtualCardDesign;
    }


    /**
     * @return  string
     */
    public function getCodeBin () {
        if (!$this->codeBin) {
            $this->codeBin = $this->scopeConfig->getValue(self::PATH_CODE_BIN);
        }
        return $this->codeBin;
    }


    /**
     * @return  string
     */
    public function getCodeGiftCardProgramId () {
        if (!$this->codeGiftCardProgramId) {
            $this->codeGiftCardProgramId = $this->scopeConfig->getValue(self::PATH_CODE_GIFT_CARD_PROGRAM_ID);
        }
        return $this->codeGiftCardProgramId;
    }

}
