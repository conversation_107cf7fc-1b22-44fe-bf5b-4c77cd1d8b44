<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */

namespace Totaltools\Vii\Helper;

class Utility
    extends \Magento\Framework\App\Helper\AbstractHelper
{

    /**
     * Convert array data to XML (root node passed by reference)
     *
     * @param  array $array
     * @param  \SimpleXMLElement &$xml
     * @return void
     */
    public static function arrayToXml (
        array $array,
        \SimpleXMLElement &$xml
    ) {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                if (preg_match('/^[0-9]+[a-zA-Z]+/', $key)) {
                    $key = preg_replace('/^[0-9]+/', '', $key);
                } elseif (is_numeric($key)) {
                    $key = 'i' . $key;
                }
                $subnode = $xml->addChild($key);
                self::arrayToXml($value, $subnode);
            } else {
                $xml->addChild("$key", htmlspecialchars("$value"));
            }
        }
    }

}
