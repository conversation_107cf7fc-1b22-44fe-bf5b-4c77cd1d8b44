<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Vii\Api;

/**
 * Interface GiftCardAccountManagementInterface
 */
interface RewriteGiftCardAccountManagementInterface
{
    /**
     * Remove GiftCard Account entity
     *
     * @param int $cartId
     * @param string $giftCardCode
     * @return bool
     */
    public function deleteByQuoteId($cartId, $giftCardCode);

    /**
     * Return GiftCard Account cards
     *
     * @param int $quoteId
     * @return \Magento\GiftCardAccount\Api\Data\GiftCardAccountInterface
     */
    public function getListByQuoteId($quoteId);

    /**
     * @param int $cartId
     * @param \Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface $giftCardAccountData
     * @return bool
     */
    public function saveByQuoteId(
        $cartId,
        \Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface $giftCardAccountData
    );

    /**
     * @param int $cartId
     * @param string $giftCardCode
     * @param string $giftCardPin
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @return float
     */
    public function checkGiftCard($cartId, $giftCardCode, $giftCardPin);
}
