<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Vii\Api;

interface RewriteGuestGiftCardAccountManagementInterface
{
    /**
     * @param string $cartId
     * @param \Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface $giftCardAccountData
     * @return bool
     */
    public function addGiftCard(
        $cartId,
        \Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface $giftCardAccountData
    );

    /**
     * @param string $cartId
     * @param string $giftCardCode
     * @param string $giftCardPin
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @return float
     */
    public function checkGiftCard($cartId, $giftCardCode, $giftCardPin);


    /**
     * Remove GiftCard Account entity
     *
     * @param string $cartId
     * @param string $giftCardCode
     * @return bool
     */
    public function deleteByQuoteId($cartId, $giftCardCode);
}
