<?php

namespace Totaltools\Vii\Api\Data;

interface QueueInterface
{

    const QUEUE_ID   = 'queue_id';
    const ACTION     = 'action';
    const DATA       = 'data';
    const STATUS     = 'status';
    const RETRIES    = 'retries';
    const CREATED_AT = 'created_at';

    /**
     * Get Queue Item ID
     *
     * @return int|null
     */
    public function getId ();

    /**
     * Get Queue Item action
     *
     * @return string|null
     */
    public function getAction ();

    /**
     * Get Queue Item data
     *
     * @return mixed[]|null
     */
    public function getQueueData ();

    /**
     * Get Queue Item status
     *
     * @return string|null
     */
    public function getStatus ();

    /**
     * Get Queue Item retry count
     *
     * @return int
     */
    public function getRetries ();

    /**
     * Get Queue Item created at timestamp
     *
     * @return string|null
     */
    public function getCreatedAt ();

    /**
     * Set Queue Item action
     *
     * @param string $action
     * @return QueueInterface
     */
    public function setAction ($action);

    /**
     * Set Queue Item data
     *
     * @param mixed[] $data
     * @return QueueInterface
     */
    public function setQueueData (array $data);

    /**
     * Set Queue Item status
     *
     * @param string $status
     * @return QueueInterface
     */
    public function setStatus ($status);

    /**
     * Set Queue Item retry count
     *
     * @param int $retries
     * @return QueueInterface
     */
    public function setRetries ($retries);

}