<?php

namespace Totaltools\Vii\Api;

use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Vii\Api\Data\QueueInterface;

interface QueueRepositoryInterface
{

    /**
     * Create or update a queue item
     *
     * @param QueueInterface $queue
     * @return QueueInterface
     * @throws InputException If bad input is provided
     * @throws LocalizedException
     */
    public function save(QueueInterface $queue);

    /**
     * Retrieve queue item
     *
     * @param int $id
     * @return QueueInterface
     * @throws NoSuchEntityException If queue item with the specified ID does not exist
     * @throws LocalizedException
     */
    public function get($id);

    /**
     * Retrieve queue items which match a specified criteria
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria);

    /**
     * Delete queue item
     *
     * @param QueueInterface $queue
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(QueueInterface $queue);

}