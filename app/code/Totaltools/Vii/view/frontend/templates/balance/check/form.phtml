<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */
/**
 * @var $block Totaltools\Vii\Block\Balance\Check\Form
 */

$balance = $block->getBalance();
$expiry  = $block->getExpiryDate();

?>

<?php if ($balance) : ?>
    <div class="giftcards-check-balance-results">
        <h2 class="giftcards-check-balance-results-heading"><?php echo __('Card Balance'); ?></h2>

        <dl class="giftcards-check-balance-results-list">
            <dt class="giftcards-check-balance-results-label"><?php echo __('Available Balance'); ?></dt>
            <dd class="giftcards-check-balance-results-value"><?php echo $block->escapeHtml($balance); ?></dd>
            <?php if ($expiry) : ?>
                <dt class="giftcards-check-balance-results-label"><?php echo __('Expiry Date'); ?></dt>
                <dd class="giftcards-check-balance-results-value"><?php echo $block->escapeHtml($expiry); ?></dd>
            <?php endif; ?>
        </dl>
    </div>
<?php endif; ?>

<form class="form giftcards-check-balance"
      action="<?php echo $block->escapeUrl($block->getFormAction()); ?>"
      id="giftcards-check-balance-form"
      method="post"
      data-hasrequired="<?php echo __('* Required Fields'); ?>"
      data-mage-init='{"validation":{}}'>
    <fieldset class="fieldset">
        <div class="field card-number required">
            <label class="label" for="card-number"><span><?php echo __('Card Number'); ?></span></label>
            <div class="control">
                <input
                        name="card-number"
                        id="card-number"
                        title="<?php echo __('Card Number'); ?>"
                        class="input-text"
                        type="number"
                        data-validate="{required:true}"
                />
            </div>
        </div>
        <div class="field card-pin required">
            <label class="label" for="card-pin"><span><?php echo __('Card PIN'); ?></span></label>
            <div class="control">
                <input
                        name="card-pin"
                        id="card-pin"
                        title="<?php echo __('Card PIN'); ?>"
                        class="input-text"
                        type="number"
                        data-validate="{required:true}"
                />
            </div>
        </div>
    </fieldset>
    <div class="actions-toolbar">
        <div class="primary">
            <input type="hidden" name="hideit" id="hideit" value="" />
            <button type="submit" title="<?php echo __('Check Balance'); ?>" class="action submit primary">
                <span><?php echo __('Check Balance'); ?></span>
            </button>
        </div>
    </div>
</form>