<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
/**
 * @var \Magento\GiftCardAccount\Block\Account\Redeem $block
 * @see \Magento\GiftCardAccount\Block\Account\Redeem
 */
?>
<div class="giftcard-account">
    <span id="gc-please-wait" class="please-wait load indicator"
          data-text="<?php echo $block->escapeHtml(__('This won\'t take long . . .')); ?>" style="display: none;">
        <span><?php echo $block->escapeHtml(__('This won\'t take long . . .')); ?></span>
    </span>
    <form class="form form-giftcard-redeem" id="giftcard-form" action="<?php echo $block->escapeUrl($block->getUrl('*/*/*')); ?>" method="post"
        data-mage-init='{
            "giftCard": {"giftCardCodeSelector": "#giftcard-code", "giftCardPinSelector": "#giftcard-pin", "checkStatus": ".action.check",
                "giftCardStatusUrl": "<?php echo $block->escapeUrl($block->getUrl('magento_giftcardaccount/cart/quickCheck',  ['_secure' => true])); ?>",
                "giftCardStatusId": "#giftcard-balance-lookup", "giftCardSpinnerId": "#gc-please-wait", "messages": ".page.messages .messages"
            }
        }'>
        <div id="giftcard-balance-lookup" class="giftcard ballance"></div>
        <fieldset class="fieldset">
            <div class="field giftcard required">
                <label class="label" for="giftcard-code">
                    <span><?php echo $block->escapeHtml(__('Enter gift card code')); ?></span>
                </label>
                <div class="control">
                    <input class="input-text" id="giftcard-code" type="text" name="giftcard_code"
                           data-validate="{required:true}"
                           value="<?php /* @noEscape */ echo $block->getCurrentGiftcard(); ?>" />
                </div>
            </div>
            <div class="field pin required">
                <label class="label" for="giftcard-pin">
                    <span><?php echo $block->escapeHtml(__('Pin')); ?></span>
                </label>
                <div class="control">
                    <input class="input-text" id="giftcard-pin" type="text" name="giftcard_pin"
                           data-validate="{required:true, 'validate-number':true}"
                           value="<?php /* @noEscape */ echo $block->getCurrentGiftcardPin(); ?>" />
                </div>
            </div>
        </fieldset>
        <div class="actions-toolbar">
            <?php if ($block->canRedeem()): ?>
                <div class="primary">
                    <button class="action redeem primary" type="submit" value="<?php echo $block->escapeHtml(__('Redeem Gift Card')); ?>">
                        <span><?php echo $block->escapeHtml(__('Redeem Gift Card')); ?></span>
                    </button>
                </div>
            <?php endif; ?>
            <div class="secondary">
                <button class="action check" id="gca_balance_button" type="button"
                        value="<?php echo $block->escapeHtml(__('Check status and balance')); ?>">
                    <span><?php echo $block->escapeHtml(__('Check status and balance')); ?></span>
                </button>
            </div>
        </div>
    </form>
</div>
