<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**
 *
 * @var \Magento\GiftCardAccount\Block\Checkout\Cart\Giftcardaccount $block
 * @see \Magento\GiftCardAccount\Block\Checkout\Cart\Giftcardaccount
 */
?>
<?php
    $helper = $this->helper(\Totaltools\Checkout\Helper\Data::class);
    if (!$helper->isB2BCustomer()) :
?>
<div class="block giftcard" id="block-giftcard" data-mage-init='{"collapsible":{"openedState": "active", "saveState": false}}'>
    <div data-role="title" class="title">
        <strong id="block-giftcard-heading" role="heading" aria-level="2">
            <?php echo $block->escapeHtml(__('I have a Gift Card')); ?>
        </strong>
    </div>
    <div data-role="content" class="content" aria-labelledby="block-giftcard-heading">
        <div id="giftcard-balance-lookup"></div>
        <span id="gc-please-wait" class="please-wait load indicator" style="display: none;"
              data-text="<?php echo $block->escapeHtml(__('This won\'t take long . . .')); ?>">
            <span><?php echo $block->escapeHtml(__('This won\'t take long . . .')); ?></span>
        </span>
        <form class="form giftcard add" id="giftcard-form"
              action="<?php echo $block->escapeUrl($block->getUrl('magento_giftcardaccount/cart/add')); ?>" method="post"
              data-mage-init='{"validation": {},"giftCard": {
                "giftCardCodeSelector": "#giftcard-code",
                "giftCardPinSelector": "#giftcard-pin",
                "checkStatus": ".action.check",
                "giftCardStatusUrl": "<?php echo $block->escapeHtml($block->getUrl('magento_giftcardaccount/cart/quickCheck',  ['_secure' => true])); ?>",
                "giftCardStatusId": "#giftcard-balance-lookup",
                "giftCardSpinnerId": "#gc-please-wait",
                "messages": ".page.messages .messages"
              }}'>
            <div class="fieldset">
                <div class="field">
                    <label class="label" for="giftcard-code">
                        <span><?php echo $block->escapeHtml(__('Enter the gift card code')); ?></span>
                    </label>
                    <div class="control">
                        <input class="input-text" type="text" id="giftcard-code" name="giftcard_code"
                               data-validate="{'required-entry':true}" value=""
                               placeholder="<?php echo $block->escapeHtml(__('Enter the gift card code'));?>" />
                    </div>
                </div>
                <div class="field card-pin">
                    <div class="control">
                        <input class="input-text" type="text" id="giftcard-pin" name="giftcard_pin"
                               data-validate="{'required-entry':true, 'validate-number':true}" value=""
                               placeholder="<?php echo $block->escapeHtml(__('Enter the gift card pin'));?>" />
                    </div>
                    <div class="actions-toolbar">
                        <div class="primary">
                            <button class="action add primary" type="submit" value="<?php echo $block->escapeHtml(__('Add Gift Card')); ?>">
                                <span><?php echo $block->escapeHtml(__('Apply')); ?></span>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="actions-toolbar">
                    <div class="secondary">
                        <button class="action check" type="button" value="<?php echo $block->escapeHtml(__('Check Gift Card status and balance')); ?>">
                            <span><?php echo $block->escapeHtml(__('Check Gift Card status and balance')); ?></span>
                        </button>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>
<?php endif; ?>
