/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
define(
    [
        'ko',
        'Magento_Checkout/js/model/url-builder',
        'mage/storage',
        'Magento_GiftCardAccount/js/model/payment/gift-card-messages',
        'Totaltools_Vii/js/model/gift-card',
        'Magento_Customer/js/model/customer',
        'Magento_Checkout/js/model/quote',
        'Magento_Checkout/js/model/error-processor'
    ],
    function (ko, urlBuilder, storage, messageList, giftCardAccount, customer, quote, errorProcessor) {
        'use strict';

        return {
            isLoading: ko.observable(false),
            check: function (giftCardCode, giftCardPin) {
                var self = this,
                    serviceUrl;
                this.isLoading(true);
                if (!customer.isLoggedIn()) {
                    serviceUrl = urlBuilder.createUrl('/integration/guest-carts/:cartId/checkGiftCard/:giftCardCode/:giftCardPin', {
                        cartId: quote.getQuoteId(),
                        giftCardCode: giftCardCode,
                        giftCardPin: giftCardPin
                    });
                } else {
                    serviceUrl = urlBuilder.createUrl('/integration/mine/checkGiftCard/:giftCardCode/:giftCardPin', {
                        giftCardCode: giftCardCode,
                        giftCardPin: giftCardPin
                    });

                }
                messageList.clear();
                storage.get(
                    serviceUrl, false
                ).done(
                    function (response) {
                        giftCardAccount.isChecked(true);
                        giftCardAccount.code(giftCardCode);
                        giftCardAccount.pin(giftCardPin);
                        giftCardAccount.amount(response);
                        giftCardAccount.isValid(true);
                    }
                ).fail(
                    function (response) {
                        giftCardAccount.isValid(false);
                        errorProcessor.process(response, messageList);
                    }
                ).always(
                    function () {
                        self.isLoading(false);
                    }
                )
            }
        };
    }
);
