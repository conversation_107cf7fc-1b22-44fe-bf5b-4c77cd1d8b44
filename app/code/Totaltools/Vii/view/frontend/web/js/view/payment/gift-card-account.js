/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
/*jshint browser:true jquery:true*/
/*global alert*/
define(
    [
        'jquery',
        'ko',
        'uiComponent',
        'Totaltools_Vii/js/action/set-gift-card-information',
        'Totaltools_Vii/js/action/get-gift-card-information',
        'Magento_Checkout/js/model/totals',
        'Totaltools_Vii/js/model/gift-card',
        'Magento_Checkout/js/model/quote',
        'Magento_Catalog/js/price-utils',
        "mage/validation"

    ],
    function (
        $,
        ko,
        Component,
        setGiftCardAction,
        getGiftCardAction,
        totals,
        giftCardAccount,
        quote,
        priceUtils
    ) {
        "use strict";
        return Component.extend({
            defaults: {
                template: 'Totaltools_Vii/payment/gift-card-account',
                giftCartCode: '',
                giftCartPin: ''
            },
            isLoading: getGiftCardAction.isLoading,
            giftCardAccount: giftCardAccount,
            initObservable: function () {
                this._super().observe(['giftCartCode', 'giftCartPin']);
                return this;
            },
            setGiftCard: function () {
                if (this.validate()) {
                    setGiftCardAction([this.giftCartCode()],[this.giftCartPin()])
                }
            },
            checkBalance: function () {
                if (this.validate()) {
                    getGiftCardAction.check(this.giftCartCode(), this.giftCartPin())
                }
            },
            getAmount: function (price) {
                return priceUtils.formatPrice(price, quote.getPriceFormat());
            },
            validate: function () {
                var form = '#giftcard-form';
                return $(form).validation() && $(form).validation('isValid');
            },
            isVisible: !quote.isVirtual() && !window.isB2BCustomer
        })
    }
);
