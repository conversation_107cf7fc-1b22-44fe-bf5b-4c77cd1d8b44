<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="opc-payment-additional giftcardaccount" id="giftcardaccount-placer" data-bind="visible: isVisible">
    <div class="payment-option-title field choice" data-role="title">
        <span class="action action-toggle" id="block-giftcard-heading" role="heading" aria-level="2">
            <!-- ko i18n: 'Redeem a Gift Card'--><!-- /ko -->
        </span>
    </div>
    <div class="payment-option-content" data-role="content">
        <!-- ko foreach: getRegion('messages') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <form class="form form-giftcard-account" id="giftcard-form" method="post" data-bind="blockLoader: isLoading">
            <div class="payment-option-inner">
                <div class="field">
                    <label class="label" for="giftcard-code">
                        <span data-bind="i18n: 'Enter the gift card code'"></span>
                    </label>
                    <div class="control">
                        <input class="input-text"
                               type="text"
                               id="giftcard-code"
                               name="giftcard_code"
                               data-validate="{'required-entry':true}"
                               data-bind="value: giftCartCode, attr:{placeholder: $t('Gift card code')} "/>
                    </div>
                </div>
                <div class="field">
                    <label class="label" for="giftcard-pin">
                        <span data-bind="i18n: 'Enter the gift card pin'"></span>
                    </label>
                    <div class="control">
                        <input class="input-text"
                               type="text"
                               id="giftcard-pin"
                               name="giftcard_pin"
                               data-validate="{'required-entry':true, 'validate-number':true}"
                               data-bind="value: giftCartPin, attr:{placeholder: $t('Card PIN')} "/>
                    </div>
                </div>
                <!-- ko if: giftCardAccount.isChecked-->
                    <!-- ko if: giftCardAccount.isValid-->
                    <div class="giftcard-account-info">
                        <div class="giftcard-account-number">
                            <!-- ko i18n: 'Gift Card:'--><!-- /ko -->
                            <span class="giftcard-number">
                                <!-- ko i18n: giftCardAccount.code--><!-- /ko -->
                            </span>
                        </div>

                        <div class="giftcard-account-pin">
                            <!-- ko i18n: 'Pin:'--><!-- /ko -->
                            <span class="giftcard-pin">
                                <!-- ko i18n: giftCardAccount.pin--><!-- /ko -->
                            </span>
                        </div>

                        <div class="giftcard-account-balance">
                            <!-- ko i18n: 'Current Balance: '--><!-- /ko -->
                            <span class="price">
                                 <!-- ko text: getAmount(giftCardAccount.amount())--><!-- /ko -->
                            </span>
                        </div>
                    </div>
                    <!-- /ko -->
                <!-- /ko -->
            </div>
            <div class="actions-toolbar">
                <div class="primary">
                    <button class="action action-add primary" type="submit" data-bind="'value': $t('Apply'), click: setGiftCard">
                        <span><!-- ko i18n: 'Apply'--><!-- /ko --></span>
                    </button>
                </div>
                <div class="secondary">
                    <button class="action action-check" type="button" data-bind="'value': $t('See Balance'), click: checkBalance">
                        <span><!-- ko i18n: 'See Balance'--><!-- /ko --></span>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
