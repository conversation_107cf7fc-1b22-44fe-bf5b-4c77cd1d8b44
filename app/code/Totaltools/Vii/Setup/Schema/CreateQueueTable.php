<?php

namespace Totaltools\Vii\Setup\Schema;

use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Setup\SchemaSetupInterface;
use Totaltools\Vii\Api\Data\QueueInterface;
use Totaltools\Vii\Model\ResourceModel\Queue as QueueResource;

class CreateQueueTable
{

    /**
     * Create the Vii queue table
     *
     * @param SchemaSetupInterface $setup
     * @return void
     */
    public function create (
        SchemaSetupInterface $setup
    ) {
        $table = $setup->getConnection()->newTable(
            $setup->getTable(QueueResource::TABLE_NAME)
        )->addColumn(
            QueueInterface::QUEUE_ID,
            Table::TYPE_INTEGER,
            null,
            [
                'identity' => true,
                'unsigned' => true,
                'nullable' => false,
                'primary'  => true,
            ],
            'Queue Item ID'
        )->addColumn(
            QueueInterface::ACTION,
            Table::TYPE_TEXT,
            '255',
            [
                'nullable' => false,
            ],
            'Queue Item Action'
        )->addColumn(
            QueueInterface::DATA,
            Table::TYPE_TEXT,
            '2M',
            [
                'nullable' => true,
            ],
            'Queue Item Data'
        )->addColumn(
            QueueInterface::STATUS,
            Table::TYPE_TEXT,
            '255',
            [
                'nullable' => true,
            ],
            'Queue Item Status'
        )->addColumn(
            QueueInterface::RETRIES,
            Table::TYPE_INTEGER,
            null,
            [
                'default'  => 0,
                'unsigned' => true,
                'nullable' => false,
            ],
            'Queue Retires Count'
        )->addColumn(
            QueueInterface::CREATED_AT,
            Table::TYPE_TIMESTAMP,
            null,
            [
                'default'  => Table::TIMESTAMP_INIT,
                'nullable' => false
            ],
            'Queue Item Created At'
        )->setComment(
            'Totaltools Vii Queue'
        );
        $setup->getConnection()->createTable($table);
    }

}