<?php
/**
 * <AUTHOR> Nguyen (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Vii\Setup;

use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Totaltools\Vii\Setup\Schema\CreateQueueTable;

/**
 * upgrade schema
 */
class UpgradeSchema implements UpgradeSchemaInterface
{

    /**
     * Create Queue Table
     * @var CreateQueueTable
     */
    private $createQueueTable;

    /**
     * InstallSchema constructor
     *
     * @param CreateQueueTable $createQueueTable
     */
    public function __construct(
        CreateQueueTable $createQueueTable
    ) {
        $this->createQueueTable = $createQueueTable;
    }

    /**
     * {@inheritdoc}
     */
    public function upgrade(
        SchemaSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $installer = $setup;
        $installer->startSetup();

        $version = $context->getVersion();

        if ($context->getVersion() && version_compare($version, '0.1.1') < 0) {
            //add column to order
            $installer->getConnection()->addColumn(
                $installer->getTable('sales_order'),
                'order_number_vii',
                [
                    'type'     => Table::TYPE_TEXT,
                    'length'   => '255',
                    'nullable' => true,
                    'comment'  => 'Order Number on Vii system',
                ]
            );
            $installer->getConnection()->addColumn(
                $installer->getTable('sales_order'),
                'activation_pin',
                [
                    'type'     => Table::TYPE_TEXT,
                    'length'   => '50',
                    'nullable' => true,
                    'comment'  => 'Activation Pin',
                ]
            );
        }

        if ($version && version_compare($version, '0.2.1') < 0) {
            $this->createQueueTable->create($installer);
        }

        $installer->endSetup();
    }
}