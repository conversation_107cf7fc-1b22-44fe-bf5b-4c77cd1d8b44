<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Vii\Setup;

use Magento\Framework\DB\Ddl\Table;
use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Totaltools\Vii\Setup\Schema\CreateQueueTable;

/**
 * install schema for Pronto
 */
class InstallSchema implements InstallSchemaInterface
{

    /**
     * Create Queue Table
     * @var CreateQueueTable
     */
    private $createQueueTable;

    /**
     * InstallSchema constructor
     *
     * @param CreateQueueTable $createQueueTable
     */
    public function __construct(
        CreateQueueTable $createQueueTable
    ) {
        $this->createQueueTable = $createQueueTable;
    }

    /**
     * Install schema
     *
     * @param SchemaSetupInterface $setup
     * @param ModuleContextInterface $context
     * @return void
     */
    public function install(
        SchemaSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $installer = $setup;
        $installer->startSetup();

        //add column to order
        $installer->getConnection()->addColumn(
            $installer->getTable('sales_order'),
            'order_number_vii',
            [
                'type'     => Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Order Number on Vii system',
            ]
        )->addColumn(
            $installer->getTable('sales_order'),
            'activation_pin',
            [
                'type'     => Table::TYPE_TEXT,
                'length'   => '50',
                'nullable' => true,
                'comment'  => 'Activation Pin',
            ]
        );

        $this->createQueueTable->create($installer);

        $installer->endSetup();
    }
}