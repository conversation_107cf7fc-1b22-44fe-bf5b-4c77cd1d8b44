<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Vii\Model\GuestCart;

use Magento\GiftCardAccount\Api\GuestGiftCardAccountManagementInterface;
use Magento\GiftCardAccount\Api\GiftCardAccountManagementInterface;
use Magento\Quote\Model\QuoteIdMaskFactory;

/**
 * Class GiftCardAccountManagement
 */
class RewriteGiftCardAccountManagement implements \Totaltools\Vii\Api\RewriteGuestGiftCardAccountManagementInterface
{
    /**
     * @var \Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface
     */
    protected $giftCartAccountManagement;

    /**
     * @var QuoteIdMaskFactory
     */
    protected $quoteIdMaskFactory;

    /**
     * @param \Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface $giftCartAccountManagement
     * @param QuoteIdMaskFactory $quoteIdMaskFactory
     */
    public function __construct(
        \Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface $giftCartAccountManagement,
        QuoteIdMaskFactory $quoteIdMaskFactory
    ) {
        $this->giftCartAccountManagement = $giftCartAccountManagement;
        $this->quoteIdMaskFactory = $quoteIdMaskFactory;
    }

    /**
     * {@inheritDoc}
     */
    public function addGiftCard(
        $cartId,
        \Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface $giftCardAccountData
    ) {
        $quoteIdMask = $this->quoteIdMaskFactory->create()->load($cartId, 'masked_id');
        return $this->giftCartAccountManagement->saveByQuoteId($quoteIdMask->getQuoteId(), $giftCardAccountData);
    }

    /**
     * {@inheritDoc}
     */
    public function checkGiftCard($cartId, $giftCardCode, $giftCardPin)
    {
        $quoteIdMask = $this->quoteIdMaskFactory->create()->load($cartId, 'masked_id');
        return $this->giftCartAccountManagement->checkGiftCard($quoteIdMask->getQuoteId(), $giftCardCode, $giftCardPin);
    }

    /**
     * Remove GiftCard Account entity
     *
     * @param string $cartId
     * @param string $giftCardCode
     *
     * @return bool
     */
    public function deleteByQuoteId($cartId, $giftCardCode)
    {
        /** @var \Magento\Quote\Model\QuoteIdMask $quoteIdMask */
        $quoteIdMask = $this->quoteIdMaskFactory->create()->load($cartId, 'masked_id');
        return $this->giftCartAccountManagement->deleteByQuoteId($quoteIdMask->getQuoteId(), $giftCardCode);
    }
}
