<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Vii\Model\Plugin;

use Magento\Framework\Registry;
use Magento\Quote\Model\Quote;
use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;
use Magento\Framework\Pricing\PriceCurrencyInterface;

class RewriteTotalsCollector extends \Magento\GiftCardAccount\Model\Plugin\TotalsCollector
{
    /**
     * @var \Totaltools\Vii\Model\Api\GiftCard
     */
    protected $giftCardApi;

    /**
     * Chrckout Session
     *
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession = null;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    protected $_date;

    /**
     * Request registry
     * @var Registry
     */
    protected $registry;

    /**
     * @var \Totaltools\Vii\Model\RewriteGiftCardAccountFactory
     */
    private $giftCardAccountFactory;

    /**
     * RewriteTotalsCollector constructor.
     * @param \Magento\GiftCardAccount\Helper\Data $giftCardAccountData
     * @param \Magento\GiftCardAccount\Model\GiftcardaccountFactory $giftCAFactory
     * @param PriceCurrencyInterface $priceCurrency
     * @param \Totaltools\Vii\Model\Api\GiftCard $api
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $date
     * @param Registry $registry
     * @param \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory
     */
    public function __construct(
        \Magento\GiftCardAccount\Helper\Data $giftCardAccountData,
        \Magento\GiftCardAccount\Model\GiftcardaccountFactory $giftCAFactory,
        PriceCurrencyInterface $priceCurrency,
        \Totaltools\Vii\Model\Api\GiftCard $api,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        Registry $registry,
        \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory
    ) {
        $this->giftCAFactory = $giftCAFactory;
        $this->giftCardAccountData = $giftCardAccountData;
        $this->priceCurrency = $priceCurrency;
        $this->giftCardApi = $api;
        $this->_checkoutSession = $checkoutSession;
        $this->_date = $date;
        $this->registry = $registry;
        $this->giftCardAccountFactory = $giftCardAccountFactory;
    }

    /**
     * Reset quote reward point amount
     *
     * @param \Magento\Quote\Model\Quote\TotalsCollector $subject
     * @param Quote $quote
     *
     * @return void
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function beforeCollect(
        \Magento\Quote\Model\Quote\TotalsCollector $subject,
        Quote $quote
    ) {

        if (is_null($quote)) {
            $quote = $this->_checkoutSession->getQuote();
        }

        $quote->setBaseGiftCardsAmount(0);
        $quote->setGiftCardsAmount(0);

        $quote->setBaseGiftCardsAmountUsed(0);
        $quote->setGiftCardsAmountUsed(0);

        $baseAmount = 0;
        $amount = 0;
        $giftCardBalnceCard = 0;
        $cards = $this->giftCardAccountData->getCards($quote);

        $canceled = array();
        $isNotAvailableBalance = false;

        $useCache = !!$this->registry->registry(ModelGiftcardaccount::GC_CACHE_PREFIX . '_SAVED');
        foreach ($cards as $k => &$card) {
            $model = $this->giftCardAccountFactory->create()->loadByCode(
                $card[ModelGiftcardaccount::ID],
                $card[ModelGiftcardaccount::PIN],
                $useCache
            );

            if ($model->isExpired() || $model->getBalance() == 0 || $model->getCurrentBalance() == 0) {
                if ($model->getAvailableBalance() == 0) {
                    $isNotAvailableBalance = true;
                }
                $canceled[] = $cards[$k];
                unset($cards[$k]);
            } else {
                $card[ModelGiftcardaccount::AMOUNT] = $this->priceCurrency->round(
                    $this->priceCurrency->convert(
                        $card[ModelGiftcardaccount::BASE_AMOUNT],
                        $quote->getStore()
                    )
                );
                $giftCardBalnceCard = $card['current_ba'];
                $baseAmount += $card[ModelGiftcardaccount::BASE_AMOUNT];
                $amount += $card[ModelGiftcardaccount::AMOUNT];
            }
        }
        $subTotal = $quote->getSubtotal();
        $shippingAmount =  $quote->getShippingAddress()->getShippingAmount();
        //subTotal +shpping +tax
        $totals = $quote->getTotals();
        $aprTax = (isset($totals['tax'])) ? $totals['tax']->getValue() : 0;
        //final amount of quote
        $finalAmt = $subTotal + $shippingAmount + $aprTax;
        //incase giftcard applied on checkout;
        if ($quote->getGiftCards() != null) {
            $giftCardData = json_decode($quote->getGiftCards());
            //giftcard total balance more then quote amount
            if ($giftCardBalnceCard >= $finalAmt) {
                $baseAmount = $finalAmt;
                $amount = $finalAmt;
                //set updated value of giftcard into quote
                if (isset($giftCardData[0]->a)) {
                    $giftCardData[0]->a = $baseAmount;
                    $giftCardData[0]->ba = $amount;
                    $quote->setGiftCards(json_encode($giftCardData));
                }
            }
            //giftcard total balance less then quote amount
            if ($giftCardBalnceCard <= $finalAmt) {
                $baseAmount = $giftCardBalnceCard;
                $amount = $giftCardBalnceCard;
                // set updated value of giftcard into quote
                if (isset($giftCardData[0]->a)) {
                    $giftCardData[0]->a = $baseAmount;
                    $giftCardData[0]->ba = $amount;
                    $quote->setGiftCards(json_encode($giftCardData));
                }
            }
        }
        $quote->setBaseGiftCardsAmount($baseAmount);
        $quote->setGiftCardsAmount($amount);

        if ($isNotAvailableBalance) {
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Your available balance is equal 0 now.')
            );
        }

    }
}
