<?php

/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Vii\Model\Plugin;

use Magento\Framework\Registry;
use Magento\Quote\Model\Quote;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\GiftCardAccount\Model\Total\Quote\Giftcardaccount;
use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote\Address\Total;
use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;

class GiftcardaccountRewrite extends Giftcardaccount
{
    /**
     * @var \Totaltools\Vii\Model\Api\GiftCard
     */
    protected $giftCardApi;

    /**
     * Chrckout Session
     *
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession = null;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    protected $_date;

    /**
     * Request registry
     * @var Registry
     */
    protected $registry;

    /**
     * @var \Totaltools\Vii\Model\RewriteGiftCardAccountFactory
     */
    private $giftCardAccountFactory;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    protected $_code = 'giftcardaccount';

    /**
     * RewriteTotalsCollector constructor.
     * @param \Magento\GiftCardAccount\Helper\Data $giftCardAccountData
     * @param \Magento\GiftCardAccount\Model\GiftcardaccountFactory $giftCAFactory
     * @param PriceCurrencyInterface $priceCurrency
     * @param \Totaltools\Vii\Model\Api\GiftCard $api
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $date
     * @param Registry $registry
     * @param \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\GiftCardAccount\Helper\Data $giftCardAccountData,
        \Magento\GiftCardAccount\Model\GiftcardaccountFactory $giftCAFactory,
        PriceCurrencyInterface $priceCurrency,
        \Totaltools\Vii\Model\Api\GiftCard $api,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        Registry $registry,
        \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::__construct($giftCardAccountData, $giftCAFactory, $priceCurrency);

        $this->giftCardApi = $api;
        $this->_checkoutSession = $checkoutSession;
        $this->_date = $date;
        $this->registry = $registry;
        $this->giftCardAccountFactory = $giftCardAccountFactory;
        $this->logger = $logger;
    }

    /**
     * @inheritdoc
     */
    public function collect(
        Quote $quote,
        ShippingAssignmentInterface $shippingAssignment,
        Total $total
    ) {
        parent::collect($quote, $shippingAssignment, $total);

        $quote->setBaseGiftCardsAmount(0);
        $quote->setGiftCardsAmount(0);

        $remainingGrandTotal = $total->getBaseGrandTotal();

        $baseCardsAmount = 0;
        $cardsAmount = 0;
        $giftCardsBalance = 0;

        $cards = $this->_giftCardAccountData->getCards($quote);
        $canceled = [];
        $useCache = (bool)$this->registry->registry(ModelGiftcardaccount::GC_CACHE_PREFIX . '_SAVED');

        foreach ($cards as $k => &$card) {
            $cardId = $card[ModelGiftcardaccount::ID];
            $cardPin = $card[ModelGiftcardaccount::PIN];

            $model = $this->giftCardAccountFactory->create()->loadByCode(
                $cardId,
                $cardPin,
                $useCache
            );

            if ($model->isExpired() || $model->getBalance() == 0 || $model->getCurrentBalance() == 0) {
                $canceled[] = $cards[$k];
                unset($cards[$k]);
                continue;
            }

            $cardBalance = floatval($model->getCurrentBalance());
            $giftCardsBalance += $cardBalance;

            if ($cardBalance >= $remainingGrandTotal) {
                $baseAmount = $remainingGrandTotal;
                $amount = $remainingGrandTotal;
                $remainingGrandTotal = 0;
            } else {
                $baseAmount = $cardBalance;
                $amount = $cardBalance;
                $remainingGrandTotal -= $cardBalance;
            }

            $baseCardsAmount += $baseAmount;
            $cardsAmount += $amount;

            $card[ModelGiftcardaccount::BASE_AMOUNT] = round($baseAmount, 4);
            $card[ModelGiftcardaccount::AMOUNT] = round($amount, 4);

            if ($remainingGrandTotal <= 0) {
                break;
            }
        }

        if ($baseCardsAmount > 0) {
            $this->_giftCardAccountData->setCards($quote, $cards);
            $this->_giftCardAccountData->setCards($total, $cards);

            $quote->setBaseGiftCardsAmount($baseCardsAmount);
            $quote->setGiftCardsAmount($cardsAmount);

            $total->setBaseGiftCardsAmount($baseCardsAmount);
            $total->setGiftCardsAmount($cardsAmount);

            $quote->setBaseGiftCardsAmountUsed($baseCardsAmount);
            $quote->setGiftCardsAmountUsed($cardsAmount);

            $total->setBaseGrandTotal(max(0, $total->getBaseGrandTotal() - $baseCardsAmount));
            $total->setGrandTotal(max(0, $total->getGrandTotal() - $cardsAmount));
        }

        return $this;
    }


    /**
     * Retrieve total code name
     *
     * @return string
     */
    public function getCode()
    {
        return $this->_code;
    }

    protected function getGiftCardCacheKey($code, $pin)
    {
        return ModelGiftcardaccount::GC_CACHE_PREFIX . 'CODE_' . $code . '_PIN_' . $pin;
    }
}
