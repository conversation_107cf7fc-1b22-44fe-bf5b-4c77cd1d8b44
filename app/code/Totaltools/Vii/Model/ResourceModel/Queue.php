<?php

namespace Totaltools\Vii\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\AbstractDb as AbstractDbResource;
use Totaltools\Vii\Model\Queue as QueueModel;
use Totaltools\Vii\Setup\Schema\CreateQueueTable;

class Queue extends AbstractDbResource
{

    const TABLE_NAME = 'totaltools_vii_queue';

    /**
     * Resource initialization
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            self::TABLE_NAME,
            QueueModel::QUEUE_ID
        );
    }


}