<?php

namespace Totaltools\Vii\Model\ResourceModel;

use Magento\Framework\Api\Search\SearchResultFactory;
use Magento\Framework\Api\Search\SearchResultInterface;
use Magento\Framework\Api\SearchCriteriaInterface;
use Magento\Framework\Api\SearchResultsInterface;
use Magento\Framework\Api\SortOrder;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Vii\Api\Data\QueueInterface;
use Totaltools\Vii\Api\QueueRepositoryInterface;
use Totaltools\Vii\Model\Queue as QueueModel;
use Totaltools\Vii\Model\QueueFactory;
use Totaltools\Vii\Model\ResourceModel\Queue\Collection as QueueCollection;
use Totaltools\Vii\Model\ResourceModel\Queue\CollectionFactory as QueueCollectionFactory;

class QueueRepository implements QueueRepositoryInterface
{

    /**
     * Queue model factory
     * @var QueueFactory
     */
    private $queueFactory;

    /**
     * Queue collection factory
     * @var QueueCollectionFactory
     */
    private $queueCollectionFactory;

    /**
     * SearchResult factory
     * @var SearchResultFactory
     */
    private $searchResultFactory;

    /**
     * QueueRepository constructor
     *
     * @param QueueFactory $queueFactory
     * @param QueueCollectionFactory $queueCollectionFactory
     * @param SearchResultFactory $searchResultFactory
     */
    public function __construct (
        QueueFactory $queueFactory,
        QueueCollectionFactory $queueCollectionFactory,
        SearchResultFactory $searchResultFactory
    ) {
        $this->queueFactory = $queueFactory;
        $this->queueCollectionFactory = $queueCollectionFactory;
        $this->searchResultFactory = $searchResultFactory;
    }

    /**
     * Create or update a queue item
     *
     * @param QueueInterface $queue
     * @return QueueInterface
     * @throws InputException If bad input is provided
     * @throws LocalizedException
     */
    public function save(QueueInterface $queue)
    {
        $queue->getResource()->save($queue);
        return $queue;
    }

    /**
     * Retrieve queue item
     *
     * @param int $id
     * @return QueueInterface
     * @throws NoSuchEntityException If queue item with the specified ID does not exist
     * @throws LocalizedException
     */
    public function get($id)
    {
        /** @var QueueModel $queue */
        $queue = $this->queueFactory->create();
        $queue->getResource()->load($queue, $id);
        if (!$queue->getId()) {
            throw new NoSuchEntityException(__('Unable to find Queue item with ID "%1"', $id));
        }
        return $queue;
    }

    /**
     * Retrieve queue items which match a specified criteria
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @return SearchResultsInterface
     * @throws LocalizedException
     */
    public function getList(SearchCriteriaInterface $searchCriteria)
    {
        /** @var QueueCollection $collection */
        $collection = $this->queueCollectionFactory->create();

        $this
            ->addFiltersToCollection($searchCriteria, $collection)
            ->addSortOrdersToCollection($searchCriteria, $collection)
            ->addPagingToCollection($searchCriteria, $collection);

        $collection->load();

        return $this->buildSearchResult($searchCriteria, $collection);
    }

    /**
     * Delete queue item
     *
     * @param QueueInterface $queue
     * @return bool true on success
     * @throws LocalizedException
     */
    public function delete(QueueInterface $queue)
    {
        $queue->getResource()->delete($queue);
        return true;
    }

    /**
     * Add filters to queue collection
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @param QueueCollection $collection
     * @return $this
     */
    private function addFiltersToCollection (
        SearchCriteriaInterface $searchCriteria,
        QueueCollection $collection
    ) {
        foreach ($searchCriteria->getFilterGroups() as $filterGroup) {
            $fields = $conditions = [];
            foreach ($filterGroup->getFilters() as $filter) {
                $fields[] = $filter->getField();
                $conditions[] = [$filter->getConditionType() => $filter->getValue()];
            }
            $collection->addFieldToFilter($fields, $conditions);
        }
        return $this;
    }

    /**
     * Add sort orders to queue collection
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @param QueueCollection $collection
     * @return $this
     */
    private function addSortOrdersToCollection (
        SearchCriteriaInterface $searchCriteria,
        QueueCollection $collection
    ) {
        foreach ((array) $searchCriteria->getSortOrders() as $sortOrder) {
            $direction = $sortOrder->getDirection() == SortOrder::SORT_ASC ? 'asc' : 'desc';
            $collection->addOrder($sortOrder->getField(), $direction);
        }
        return $this;
    }

    /**
     * Add paging to queue collection
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @param QueueCollection $collection
     * @return $this
     */
    private function addPagingToCollection (
        SearchCriteriaInterface $searchCriteria,
        QueueCollection $collection
    ) {
        $collection->setPageSize($searchCriteria->getPageSize());
        $collection->setCurPage($searchCriteria->getCurrentPage());
        return $this;
    }

    /**
     * Build queue collection search result
     *
     * @param SearchCriteriaInterface $searchCriteria
     * @param QueueCollection $collection
     * @return SearchResultInterface
     */
    private function buildSearchResult (
        SearchCriteriaInterface $searchCriteria,
        QueueCollection $collection
    ) {
        $searchResults = $this->searchResultFactory->create();

        $searchResults->setSearchCriteria($searchCriteria);
        $searchResults->setItems($collection->getItems());
        $searchResults->setTotalCount($collection->getSize());

        return $searchResults;
    }

}