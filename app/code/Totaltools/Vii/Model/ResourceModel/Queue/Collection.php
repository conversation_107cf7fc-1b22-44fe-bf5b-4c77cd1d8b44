<?php

namespace Totaltools\Vii\Model\ResourceModel\Queue;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection as AbstractCollectionResource;
use Totaltools\Vii\Model\Queue;
use Totaltools\Vii\Model\ResourceModel\Queue as QueueResource;

class Collection extends AbstractCollectionResource
{

    /**
     * ID field name
     * @var string
     */
    protected $_idFieldName = Queue::QUEUE_ID;

    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct ()
    {
        $this->_init(
            Queue::class,
            QueueResource::class
        );
    }

}