<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */

namespace Totaltools\Vii\Model;

use Magento\Framework\Data\Collection\AbstractDb as AbstractDbCollection;
use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\Context;
use Magento\Framework\Model\ResourceModel\AbstractResource;
use Magento\Framework\Registry;
use Ramsey\Uuid\Uuid;
use Totaltools\Vii\Api\Data\QueueInterface;
use Totaltools\Vii\Api\Data\QueueInterfaceFactory;
use Totaltools\Vii\Api\QueueRepositoryInterface;
use Totaltools\Vii\Helper\Config as ConfigHelper;
use Totaltools\Vii\Model\Queue\UndoAction as QueueUndoAction;

class Api extends AbstractModel
{

    const REQUEST_USERAGENT = 'Total Tools Magento 2 Application';
    const REQUEST_TIMEOUT   = 30;


    // Uses Order API URL
    const REQUEST_TYPE_NEW_ORDER                = 'NewOrder';

    // Uses Redemption API URL
    const REQUEST_TYPE_CHECK_BALANCE            = 'CheckBalance';
    const REQUEST_TYPE_LOAD                     = 'Load';
    const REQUEST_TYPE_LOAD_SEND_EMAIL          = 'LoadSendEmail';
    const REQUEST_TYPE_CANCEL_LOAD              = 'CancelLoad';
    const REQUEST_TYPE_REDEMPTION               = 'Redemption';
    const REQUEST_TYPE_UNDO                     = 'Undo';
    const REQUEST_TYPE_PRE_AUTH_REQUEST         = 'PreAuthRequest';
    const REQUEST_TYPE_PRE_AUTH_CANCELLATION    = 'PreAuthCancellation';
    const REQUEST_TYPE_GET_TRANSACTION_HISTORY  = 'GetTransactionHistory';
    const REQUEST_TYPE_GET_EOD_TOTALS           = 'GetEODTotals';
    const REQUEST_TYPE_ORDER_ACTIVATION         = 'OrderActivation';
    const REQUEST_TYPE_ORDER_PARTIAL_ACTIVATION = 'OrderPartialActivation';
    const REQUEST_TYPE_VERIFY_STORE             = 'VerifyStore';
    const REQUEST_TYPE_ORDER_LOAD               = 'OrderLoad';
    const REQUEST_TYPE_ORDER_PARTIAL_LOAD       = 'OrderPartialLoad';

    /**
     * @var  string[]  Valid request types
     */
    protected $validRequestTypes = [
        self::REQUEST_TYPE_NEW_ORDER,
        self::REQUEST_TYPE_CHECK_BALANCE,
        self::REQUEST_TYPE_LOAD,
        self::REQUEST_TYPE_LOAD_SEND_EMAIL,
        self::REQUEST_TYPE_CANCEL_LOAD,
        self::REQUEST_TYPE_REDEMPTION,
        self::REQUEST_TYPE_UNDO,
        self::REQUEST_TYPE_PRE_AUTH_REQUEST,
        self::REQUEST_TYPE_PRE_AUTH_CANCELLATION,
        self::REQUEST_TYPE_GET_TRANSACTION_HISTORY,
        self::REQUEST_TYPE_GET_EOD_TOTALS,
        self::REQUEST_TYPE_ORDER_ACTIVATION,
        self::REQUEST_TYPE_ORDER_PARTIAL_ACTIVATION,
        self::REQUEST_TYPE_VERIFY_STORE,
        self::REQUEST_TYPE_ORDER_LOAD,
        self::REQUEST_TYPE_ORDER_PARTIAL_LOAD,
    ];


    const RESPONSE_FORMAT_XML       = 'xml';
    const RESPONSE_FORMAT_SIMPLEXML = 'simplexml';
    const RESPONSE_FORMAT_JSON      = 'json';
    const RESPONSE_FORMAT_ARRAY     = 'array';
    const RESPONSE_FORMAT_OBJECT    = 'object';

    /**
     * @var  string[]  Valid response formats
     */
    protected $validResponseFormats = [
        self::RESPONSE_FORMAT_XML,
        self::RESPONSE_FORMAT_SIMPLEXML,
        self::RESPONSE_FORMAT_JSON,
        self::RESPONSE_FORMAT_ARRAY,
        self::RESPONSE_FORMAT_OBJECT,
    ];

    const RESPONSE_CODE_UNDO_SUCCESS = 0;


    /**
     * @var  string  Root XML node for request
     */
    protected $rootNode = 'Request';


    /**
     * @var  string  Request type
     */
    protected $requestType;

    /**
     * @var  string[]  POST body data
     */
    protected $body;

    /**
     * @var  string  Response format
     */
    protected $responseFormat;

    /**
     * @var  string  Last transaction's UUID
     */
    protected $lastTranId;


    /**
     * @var  ConfigHelper
     */
    protected $configHelper;

    /**
     * Queue repository
     * @var QueueRepositoryInterface
     */
    protected $queueRepository;

    /**
     * Queue factory
     * @var QueueInterfaceFactory
     */
    protected $queueFactory;


    /**
     * Api constructor
     *
     * @param ConfigHelper $configHelper
     * @param QueueRepositoryInterface $queueRepository
     * @param QueueInterfaceFactory $queueFactory
     * @param Context $context
     * @param Registry $registry
     * @param AbstractResource $resource
     * @param AbstractDbCollection $resourceCollection
     * @param array $data
     */
    public function __construct (
        ConfigHelper $configHelper,
        QueueRepositoryInterface $queueRepository,
        QueueInterfaceFactory $queueFactory,
        Context $context,
        Registry $registry,
        AbstractResource $resource = null,
        AbstractDbCollection $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
        $this->configHelper = $configHelper;
        $this->queueRepository = $queueRepository;
        $this->queueFactory = $queueFactory;
    }


    /**
     * @return  string  Root XML node
     */
    protected function getRootNode () {
        return $this->rootNode;
    }


    protected function getTranId () {
        $tranId = Uuid::uuid4()->toString();
        $this->lastTranId = $tranId;
        return $tranId;
    }


    /**
     * @return  string|false  XML string
     */
    protected function getBodyXml () {
        $standardBody = [
            'RequestType' => $this->getRequestType(),
            'viiUserName' => $this->configHelper->getApiUsername(),
            'viiPassword' => $this->configHelper->getApiPassword(),
            'viiTranId'   => $this->getTranId(),
            'StoreId'     => $this->configHelper->getCodeStoreId(),
        ];
        $body = array_merge($standardBody, $this->body);

        $xml = new \SimpleXMLElement('<' . $this->getRootNode() . '/>');
        \Totaltools\Vii\Helper\Utility::arrayToXml($body, $xml);

        return $xml->asXML();
    }


    protected function getUrl () {
        switch ($this->getRequestType()) {
            case self::REQUEST_TYPE_NEW_ORDER:
                return $this->configHelper->getApiOrderUrl();
                break;
            default:
                return $this->configHelper->getApiRedemptionUrl();
                break;
        }
    }


    /**
     * @param   string $bodyXml
     * @return  string|false
     */
    protected function curl ($bodyXml) {
        $c = curl_init();
        $isDebug = $this->configHelper->isDebug();

        $headers = [
            'Content-type: application/xml',
            'Content-Length: ' . strlen($bodyXml),
        ];

        curl_setopt_array($c, [
            CURLOPT_FAILONERROR    => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST           => true,
            CURLOPT_URL            => $this->getUrl(),
            CURLOPT_USERAGENT      => self::REQUEST_USERAGENT,
            CURLOPT_TIMEOUT        => self::REQUEST_TIMEOUT,
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_POSTFIELDS     => $bodyXml,
        ]);

        if ($isDebug) {
            curl_setopt($c, CURLINFO_HEADER_OUT, true);
        }

        $result = curl_exec($c);

        if ($isDebug) {
            $this->_logger->info('Totaltools_Vii cURL info: ' . var_export(curl_getinfo($c), true));
            $this->_logger->info('Totaltools_Vii cURL request POST data: ' . var_export($bodyXml, true));
            $this->_logger->info('Totaltools_Vii cURL response data: ' . var_export($result, true));
        }

        curl_close($c);

        return $result;
    }


    /**
     * @return  string|false
     */
    protected function cancelRequest () {
        /** @var QueueInterface $queueItem */
        $queueItem = $this->queueFactory->create();
        $queueItem
            ->setAction(Queue::ACTION_UNDO)
            ->setQueueData([QueueUndoAction::KEY_TRANSACTION_ID => $this->lastTranId])
            ->setStatus(Queue::STATUS_PENDING);

        $this->queueRepository->save($queueItem);
    }


    /**
     * @return  string|\SimpleXMLElement|string[]|stdClass  Response in requested format
     */
    protected function request () {
        $bodyXml = $this->getBodyXml();

        $result = $this->curl($bodyXml);

        $dontCancel = [
            self::REQUEST_TYPE_UNDO,
            self::REQUEST_TYPE_CHECK_BALANCE,
            self::REQUEST_TYPE_NEW_ORDER,
        ];
        if ($result === false && !in_array($this->getRequestType(), $dontCancel)) {
            $this->cancelRequest();
        } else {
            // Turn XML response into requested format
            switch ($this->getResponseFormat()) {
                case self::RESPONSE_FORMAT_XML:
                    // $result is already an XML string
                    break;
                case self::RESPONSE_FORMAT_SIMPLEXML:
                    $result = simplexml_load_string($result);
                    break;
                case self::RESPONSE_FORMAT_JSON:
                    $result = simplexml_load_string($result);
                    $result = json_encode($result);
                    break;
                case self::RESPONSE_FORMAT_ARRAY:
                    $result = simplexml_load_string($result);
                    $result = json_encode($result);
                    $result = json_decode($result, true);
                    break;
                case self::RESPONSE_FORMAT_OBJECT:
                default:
                    $result = simplexml_load_string($result);
                    $result = json_encode($result);
                    $result = json_decode($result);
                    break;
            }
        }

        return $result;
    }


    /**
     * @param   string $requestType  Request type, one of class consts
     * @return  self
     */
    public function setRequestType ($requestType) {
        if (!in_array($requestType, $this->validRequestTypes)) {
            throw new \Exception('Unknown RequestType: ' . $requestType);
        }
        $this->requestType = $requestType;
        return $this;
    }


    /**
     * @return  string  Request type
     */
    public function getRequestType () {
        return $this->requestType;
    }


    /**
     * @param   string[] $body  POST body data
     * @return  self
     */
    public function setBody (array $body) {
        $this->body = $body;
        return $this;
    }


    /**
     * @return  string[]  POST body data
     */
    public function getBody () {
        return $this->body;
    }


    /**
     * @param   string $responseFormat  Response format, one of class consts
     * @return  self
     */
    public function setResponseFormat ($responseFormat) {
        if (!in_array($responseFormat, $this->validResponseFormats)) {
            throw new \Exception('Unknown response format: ' . $responseFormat);
        }
        $this->responseFormat = $responseFormat;
        return $this;
    }


    /**
     * @return  string  Response format
     */
    public function getResponseFormat () {
        return $this->responseFormat;
    }


    protected function validate () {
        if (!$this->getRequestType()) {
            throw new \Exception('No RequestType specified');
        }
        if (!$this->getBody() || !is_array($this->getBody())) {
            throw new \Exception('No request Body specified');
        }
        if (!$this->getResponseFormat()) {
            throw new \Exception('No ResponseFormat specified');
        }
    }


    /**
     * @return  string|\SimpleXMLElement|string[]|stdClass  Response in requested format
     */
    public function call () {
        $this->validate();
        return $this->request();
    }

}
