<?php

namespace Totaltools\Vii\Model\Queue;

use Magento\Sales\Api\OrderRepositoryInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Vii\Api\Data\QueueActionInterface;
use Totaltools\Vii\Model\Api;
use Totaltools\Vii\Model\Api\Order as OrderApi;

class OrderAction implements QueueActionInterface
{

    const KEY_ORDER_ID = 'order_id';
    const KEY_BODY = 'body';

    /**
     * API
     * @var Api
     */
    private $api;

    /**
     * Order repository
     * @var OrderRepositoryInterface
     */
    private $orderRepository;

    /**
     * Logger
     * @var LoggerInterface
     */
    private $logger;

    /**
     * UndoAction constructor
     *
     * @param Api $api
     * @param OrderRepositoryInterface $orderRepository
     * @param LoggerInterface $logger
     */
    public function __construct (
        Api $api,
        OrderRepositoryInterface $orderRepository,
        LoggerInterface $logger
    ) {
        $this->api = $api;
        $this->orderRepository = $orderRepository;
        $this->logger = $logger;
    }

    /**
     * Perform queue action
     *
     * @param mixed[] $data
     * @return boolean success
     */
    public function action(array $data)
    {
        $success = false;
        if (!isset($data[self::KEY_BODY])) {
            return $success;
        }

        $body = $data[self::KEY_BODY];

        $this->api
            ->setRequestType(Api::REQUEST_TYPE_NEW_ORDER)
            ->setBody($body)
            ->setResponseFormat(Api::RESPONSE_FORMAT_ARRAY);

        $result = $this->api->call();

        if (
            isset($result['ResponseCode'])
            && (int) $result['ResponseCode'] === OrderApi::VII_RESPONSE_CODE_SUCCESS
        ) {
            $success = true;

            if (isset($data[self::KEY_ORDER_ID]) && $data[self::KEY_ORDER_ID]) {
                try {
                    $order = $this->orderRepository->get($data[self::KEY_ORDER_ID]);
                    $order
                        ->setOrderNumberVii($result['ViiOrderNumber'])
                        ->setActivationPin($result['ActivationPin']);
                    $this->orderRepository->save($order);
                } catch (\Exception $e) {
                    $errorMessage = 'Error saving Vii order information onto Magento order: %s';
                    $this->logger->warning(sprintf($errorMessage, $e->getMessage()));
                }
            }
        }

        return $success;
    }
}