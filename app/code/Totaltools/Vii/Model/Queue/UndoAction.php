<?php

namespace Totaltools\Vii\Model\Queue;

use Totaltools\Vii\Api\Data\QueueActionInterface;
use Totaltools\Vii\Model\Api;

class UndoAction implements QueueActionInterface
{

    const KEY_TRANSACTION_ID = 'transaction_id';

    /**
     * API
     * @var Api
     */
    private $api;

    /**
     * UndoAction constructor
     *
     * @param Api $api
     */
    public function __construct (
        Api $api
    ) {
        $this->api = $api;
    }

    /**
     * Undo previous request
     *
     * @param mixed[] $data
     * @return boolean success
     */
    public function action (array $data)
    {
        $success = false;
        if (!isset($data[self::KEY_TRANSACTION_ID])) {
            return $success;
        }

        $transactionId = $data[self::KEY_TRANSACTION_ID];
        $result = $this->api
            ->setRequestType(Api::REQUEST_TYPE_UNDO)
            ->setBody(['viiTranIdToUndo' => $transactionId])
            ->setResponseFormat(Api::RESPONSE_FORMAT_ARRAY)
            ->call();

        if (
            isset($result['ResponseCode'])
            && (int) $result['ResponseCode'] === Api::RESPONSE_CODE_UNDO_SUCCESS
        ) {
            $success = true;
        }

        return $success;
    }

}