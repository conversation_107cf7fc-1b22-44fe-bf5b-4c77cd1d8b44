<?php

namespace Totaltools\Vii\Model;

use Magento\Framework\Model\AbstractModel;
use Totaltools\Vii\Api\Data\QueueInterface;
use Totaltools\Vii\Model\ResourceModel\Queue as QueueResource;

class Queue extends AbstractModel implements QueueInterface
{

    const STATUS_PENDING  = 'pending';
    const STATUS_RUNNING  = 'running';
    const STATUS_COMPLETE = 'complete';
    const STATUS_ERROR    = 'error';

    const ACTION_UNDO = 'undo';
    const ACTION_ORDER = 'order';

    /**
     * Prefix of model events names
     *
     * @var string
     */
    protected $_eventPrefix = 'totaltools_vii_queue';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct ()
    {
        $this->_init(QueueResource::class);
    }

    /**
     * Get Queue Item ID
     *
     * @return int|null
     */
    public function getId ()
    {
        return $this->getData(self::QUEUE_ID);
    }

    /**
     * Get Queue Item action
     *
     * @return string|null
     */
    public function getAction ()
    {
        return $this->getData(self::ACTION);
    }

    /**
     * Get Queue Item data
     *
     * @return mixed[]|null
     */
    public function getQueueData ()
    {
        return json_decode($this->getData(self::DATA), true);
    }

    /**
     * Get Queue Item status
     *
     * @return string|null
     */
    public function getStatus ()
    {
        return $this->getData(self::STATUS);
    }

    /**
     * Prepare queue items's statuses
     *
     * @return string[]
     */
    public function getAvailableStatuses ()
    {
        return [
            self::STATUS_PENDING,
            self::STATUS_RUNNING,
            self::STATUS_COMPLETE,
            self::STATUS_ERROR,
        ];
    }

    /**
     * Get Queue Item retry count
     *
     * @return int|null
     */
    public function getRetries ()
    {
        return $this->getData(self::RETRIES);
    }

    /**
     * Get Queue Item created at timestamp
     *
     * @return string|null
     */
    public function getCreatedAt ()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set Queue Item action
     *
     * @param string $action
     * @return QueueInterface
     */
    public function setAction ($action)
    {
        $this->setData(self::ACTION, $action);
        return $this;
    }

    /**
     * Set Queue Item data
     *
     * @param mixed[] $data
     * @return QueueInterface
     */
    public function setQueueData (array $data)
    {
        $this->setData(self::DATA, json_encode($data));
        return $this;
    }

    /**
     * Set Queue Item status
     *
     * @param string $status
     * @return QueueInterface
     */
    public function setStatus ($status)
    {
        $this->setData(self::STATUS, $status);
        return $this;
    }

    /**
     * Set Queue Item retry count
     *
     * @param int $retries
     * @return QueueInterface
     */
    public function setRetries ($retries)
    {
        $this->setData(self::RETRIES, $retries);
        return $this;
    }

}