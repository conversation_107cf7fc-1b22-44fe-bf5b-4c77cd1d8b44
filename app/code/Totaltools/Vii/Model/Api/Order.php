<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */

namespace Totaltools\Vii\Model\Api;

use Magento\Framework\Data\Collection\AbstractDb as AbstractDbCollection;
use Magento\Framework\Model\Context;
use Magento\Framework\Model\ResourceModel\AbstractResource;
use Magento\Framework\Registry;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Totaltools\Vii\Api\Data\QueueInterface;
use Totaltools\Vii\Api\Data\QueueInterfaceFactory;
use Totaltools\Vii\Api\QueueRepositoryInterface;
use Totaltools\Vii\Helper\Config as ConfigHelper;
use Totaltools\Vii\Model\Queue as ViiQueue;
use Totaltools\Vii\Model\Queue\OrderAction as QueueOrderAction;

class Order
    extends \Totaltools\Vii\Model\Api
{

    const VII_STORED_VALUE_CARD_ORDER_STATUS_ID_NEW               = 10;
    const VII_STORED_VALUE_CARD_ORDER_STATUS_ID_AWAITING_APPROVAL = 15;
    const VII_STORED_VALUE_CARD_ORDER_STATUS_ID_APPROVED          = 20;
    const VII_STORED_VALUE_CARD_ORDER_STATUS_ID_DECLINED          = 30;

    const VII_PAYMENT_STATUS_NEW      = 10;
    const VII_PAYMENT_STATUS_APPROVED = 20;
    const VII_PAYMENT_STATUS_DECLINED = 30;

    const VII_PAYMENT_TYPE = 10;

    const VII_DELIVERY_METHOD = 'Email';

    const VII_SETTLEMENT_DATE_FORMAT  = 'd/m/Y';
    const VII_TRANSACTION_DATE_FORMAT = 'd/m/Y h:i:s A';

    const VII_RESPONSE_CODE_SUCCESS = 0;


    /**
     * @var  string[]
     */
    protected $orderDataKeys = [
        'OrderId',
        'Amount',
        'CustomerSalutation',
        'CustomerFirstName',
        'CustomerLastName',
        'CustomerEmail',
    ];
    /**
     * @var bool
     */
    protected $isProcessing = false;

    /**
     * @var  mixed[]
     */
    protected $orderData = [
        'Order' => [
            'StoredValueCardOrderSourceId' => null,
            'StoredValueCardOrderStatusId' => self::VII_STORED_VALUE_CARD_ORDER_STATUS_ID_APPROVED,
            'PaymentStatus'                => self::VII_PAYMENT_STATUS_APPROVED,
            'SourceReferenceId'            => null,
            'TotalAmount'                  => null,
            'PaymentType'                  => self::VII_PAYMENT_TYPE,
            'SettlementDate'               => null,
            'TransactionDate'              => null,
            'OrderPerson' => [
                'Salutation' => null,
                'FirstName'  => null,
                'LastName'   => null,
                'Email'      => null,
            ],
            'Packages' => [],
            'Payments' => '', // Must exist, but be blank...
        ],
    ];


    /**
     * @var  \Magento\Framework\Stdlib\DateTime\Timezone
     */
    protected $timezone;

    /**
     * Order constructor
     *
     * @param TimezoneInterface $timezone
     * @param ConfigHelper $configHelper
     * @param QueueRepositoryInterface $queueRepository
     * @param QueueInterfaceFactory $queueFactory
     * @param Context $context
     * @param Registry $registry
     * @param AbstractResource|null $resource
     * @param AbstractDbCollection|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        TimezoneInterface $timezone,
        ConfigHelper $configHelper,
        QueueRepositoryInterface $queueRepository,
        QueueInterfaceFactory $queueFactory,
        Context $context,
        Registry $registry,
        AbstractResource $resource = null,
        AbstractDbCollection $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($configHelper, $queueRepository, $queueFactory, $context, $registry, $resource, $resourceCollection, $data);
        $this->timezone = $timezone;

        $this->orderData['Order']['StoredValueCardOrderSourceId'] = $this->configHelper->getCodeStoredValueCardOrderSourceId();
    }

    /**
     * @param   array $data
     * @return  self
     */
    public function setOrderData (array $data) {
        if (!$data) return;
        foreach ($this->orderDataKeys as $key) {
            if (isset($data[$key])) {
                $method = "set$key";
                if (method_exists(self, $method)) {
                    $this->$method($data[$key]);
                }
            }
        }
        return $this;
    }


    /**
     * @param   string $orderId
     * @return  self
     */
    public function setOrderId ($orderId) {
        $this->orderData['Order']['SourceReferenceId'] = $orderId;
        return $this;
    }


    /**
     * @param   float|int $amount  Card value (maximum 1000)
     * @return  self
     */
    public function setAmount ($amount) {
        $this->orderData['Order']['TotalAmount'] = floatval($amount);
        return $this;
    }


    /**
     * @param   string $customerSalutation
     * @return  self
     */
    public function setCustomerSalutation ($customerSalutation) {
        $this->orderData['Order']['OrderPerson']['Salutation'] = $customerSalutation;
        return $this;
    }


    /**
     * @param   string $customerFirstName
     * @return  self
     */
    public function setCustomerFirstName ($customerFirstName) {
        $this->orderData['Order']['OrderPerson']['FirstName'] = $customerFirstName;
        return $this;
    }


    /**
     * @param   string $customerLastName
     * @return  self
     */
    public function setCustomerLastName ($customerLastName) {
        $this->orderData['Order']['OrderPerson']['LastName'] = $customerLastName;
        return $this;
    }


    /**
     * @param   string $customerEmail
     * @return  self
     */
    public function setCustomerEmail ($customerEmail) {
        $this->orderData['Order']['OrderPerson']['Email'] = $customerEmail;
        return $this;
    }


    /**
     * Add virtual gift card to order
     *
     * @param string $to
     * @param string $from
     * @param string $message
     * @param string $email
     * @param float|int $value
     * @param int $qty
     * @return self
     */
    public function addPackage ($to, $from, $message, $email, $value, $qty = 1)
    {
        $i = 0;
        if (is_array($this->orderData['Order']['Packages'])) {
            $i = count($this->orderData['Order']['Packages']);
        }
        $this->orderData['Order']['Packages'][$i.'Package'] = [
            'DeliveryMethod' => self::VII_DELIVERY_METHOD,
            'Salutation'     => '',
            'FirstName'      => $to,
            'LastName'       => '',
            'Email'          => $email,
            'OrderItems'     => [
                'OrderDetail' => [
                    'Quantity'          => $qty,
                    'CardDesign'        => $this->configHelper->getCodeVirtualCardDesign(),
                    'CarrierCode'       => '', // Must exist, but be blank...
                    'CardFaceValue'     => floatval($value),
                    'Bin'               => $this->configHelper->getCodeBin(),
                    'GiftCardProgramId' => $this->configHelper->getCodeGiftCardProgramId(),
                    'ToName'            => $to,
                    'CarrierMessage'    => $message,
                    'FromName'          => $from,
                ]
            ],
        ];
        return $this;
    }

    /**
     * @return $this
     */
    public function clearPackages()
    {
        $this->orderData['Order']['Packages'] = [];
        return $this;
    }

    /**
     * @return $this
     */
    public function start()
    {
        $this->isProcessing = true;
        $this->clearPackages();
        return $this;
    }
    public function isProcessing()
    {
        return $this->isProcessing;
    }
    /**
     * @param   string $requestType  Request type, one of class consts
     * @return  self
     */
    public function setRequestType ($requestType) {
        throw new \Exception('Cannot set RequestType manually on Order model');
    }


    /**
     * @param   string[] $body  POST body data
     * @return  self
     */
    public function setBody (array $body) {
        throw new \Exception('Cannot set Body manually on Order model');
    }


    /**
     * Set order dates to now
     *
     * @return $this
     */
    protected function setDates () {
        $now = $this->timezone->date();
        $this->orderData['Order']['SettlementDate']  = $now->format(self::VII_SETTLEMENT_DATE_FORMAT);
        $this->orderData['Order']['TransactionDate'] = $now->format(self::VII_TRANSACTION_DATE_FORMAT);
        return $this;
    }


    /**
     * Validate order data
     *
     * @throws \Exception
     */
    protected function validate () {
        if (!$this->orderData['Order']['StoredValueCardOrderSourceId']) {
            throw new \Exception('No StoredValueCardOrderSourceId specified in system configuration');
        }
        if (!$this->orderData['Order']['SourceReferenceId']) {
            throw new \Exception('No OrderId specified');
        }
        if (!$this->orderData['Order']['TotalAmount']) {
            throw new \Exception('No Amount specified');
        }
        if (!$this->orderData['Order']['OrderPerson']['FirstName']) {
            throw new \Exception('No CustomerFirstName specified');
        }
        if (!$this->orderData['Order']['OrderPerson']['LastName']) {
            throw new \Exception('No CustomerLastName specified');
        }
        if (!$this->orderData['Order']['OrderPerson']['Email']) {
            throw new \Exception('No CustomerEmail specified');
        }
        if (!count($this->orderData['Order']['Packages'])) {
            throw new \Exception('No Packages added to order');
        }

        parent::validate();
    }


    /**
     * Add order to queue
     *
     * @param int|null $magentoOrderId
     * @return void
     */
    public function addToQueue ($magentoOrderId = null)
    {
        $this
            ->setDates()
            ->setResponseFormat(self::RESPONSE_FORMAT_ARRAY);

        parent::setRequestType(self::REQUEST_TYPE_NEW_ORDER);
        parent::setBody($this->orderData);

        $this->validate();

        $body = $this->getBody();
        $body['viiTranId'] = $this->getTranId();

        /** @var QueueInterface $queueItem */
        $queueItem = $this->queueFactory->create();
        $queueItem
            ->setAction(ViiQueue::ACTION_ORDER)
            ->setQueueData([
                QueueOrderAction::KEY_ORDER_ID => $magentoOrderId,
                QueueOrderAction::KEY_BODY => $body
           ])
            ->setStatus(ViiQueue::STATUS_PENDING);

        $this->queueRepository->save($queueItem);
    }


    /**
     * @return  string|\SimpleXMLElement|string[]|stdClass  Response in requested format
     */
    public function call () {
        $this->setDates();

        parent::setRequestType(self::REQUEST_TYPE_NEW_ORDER);
        parent::setBody($this->orderData);

        return parent::call();
    }

}
