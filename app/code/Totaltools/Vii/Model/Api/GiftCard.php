<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */

namespace Totaltools\Vii\Model\Api;

use Magento\Framework\Data\Collection\AbstractDb as AbstractDbCollection;
use Magento\Framework\Model\Context;
use Magento\Framework\Model\ResourceModel\AbstractResource;
use Magento\Framework\Registry;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Totaltools\Vii\Api\Data\QueueInterfaceFactory;
use Totaltools\Vii\Api\QueueRepositoryInterface;
use Totaltools\Vii\Helper\Config as ConfigHelper;

class GiftCard
    extends \Totaltools\Vii\Model\Api
{

    const RESPONSE_CODE_CHECK_BALANCE_SUCCESS = 0;
    const RESPONSE_CODE_PRE_AUTH_REQUEST_SUCCESS = 0;
    const RESPONSE_CODE_REDEMPTION_SUCCESS = 0;

    /**
     * @var  \Magento\Framework\Stdlib\DateTime\Timezone
     */
    protected $timezone;

    /**
     * GiftCard constructor.
     * @param TimezoneInterface $timezone
     * @param ConfigHelper $configHelper
     * @param QueueRepositoryInterface $queueRepository
     * @param QueueInterfaceFactory $queueFactory
     * @param Context $context
     * @param Registry $registry
     * @param AbstractResource|null $resource
     * @param AbstractDbCollection|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        TimezoneInterface $timezone,
        ConfigHelper $configHelper,
        QueueRepositoryInterface $queueRepository,
        QueueInterfaceFactory $queueFactory,
        Context $context,
        Registry $registry,
        AbstractResource $resource = null,
        AbstractDbCollection $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct($configHelper, $queueRepository, $queueFactory, $context, $registry, $resource, $resourceCollection, $data);
        $this->timezone = $timezone;
    }

    /**
     * call check Balance Api
     * @param string $code
     * @param string $pin
     * @return array
     * */
    public function checkBalance($code, $pin)
    {
        if (!$code || !$pin) {
            return false;
        }
        $this->setRequestType(self::REQUEST_TYPE_CHECK_BALANCE)
            ->setBody([
                'CardNumber'        => $code,
                'PIN'               => $pin,
            ])
        ;

        $result = $this->setResponseFormat(self::RESPONSE_FORMAT_ARRAY)
            ->call()
        ;
        return $result;
    }

    /**
     * call pre auth request api
     * @param string $code
     * @param string $pin
     * @param float $amount
     * @param string $externalReference
     * @return array
     * */
    public function preAuthRequest($code, $pin, $amount, $externalReference = null)
    {
        if (!$code || !$pin) {
            return false;
        }
        $this->setRequestType(self::REQUEST_TYPE_PRE_AUTH_REQUEST)
            ->setBody([
                'CardNumber'        => $code,
                'PIN'               => $pin,
                'Amount'            => $amount,
                'ExternalReference' => $externalReference
            ])
        ;

        $result = $this->setResponseFormat(self::RESPONSE_FORMAT_ARRAY)
            ->call()
        ;
        return $result;
    }

    /**
     * call pre auth cancellation api
     * @param string $code
     * @param string $preAuthCode
     * @return array
     * */
    public function preAuthCancellation($code, $preAuthCode)
    {
        if (!$code || !$preAuthCode) {
            return false;
        }
        $this->setRequestType(self::REQUEST_TYPE_PRE_AUTH_CANCELLATION)
            ->setBody([
                'CardNumber'        => $code,
                'PreAuthCode'               => $preAuthCode,
            ])
        ;

        $result = $this->setResponseFormat(self::RESPONSE_FORMAT_ARRAY)
            ->call()
        ;
        return $result;
    }


    /**
     * Call redemption API to charge gift card
     *
     * @param string $code
     * @param string $pin
     * @param float $amount
     * @param null $externalReference
     * @param string $preAuthCode
     * @return \string[]
     * @throws \Exception
     */
    public function redemption($code, $pin, $amount, $externalReference = null, $preAuthCode = null)
    {
        if (!$code) {
            throw new \Exception('Gift Card code not set');
        }
        if (!$pin) {
            throw new \Exception('Gift Card PIN not set');
        }
        if (!$amount) {
            throw new \Exception('Amount not set');
        }

        $body = [
            'CardNumber' => $code,
            'PIN'        => $pin,
            'Amount'     => $amount,
        ];
        if ($externalReference) {
            $body['ExternalReference'] = $externalReference;
        }
        if ($preAuthCode) {
            $body['PreAuthCode'] = $preAuthCode;
        }

        $this
            ->setRequestType(self::REQUEST_TYPE_REDEMPTION)
            ->setBody($body)
            ->setResponseFormat(self::RESPONSE_FORMAT_ARRAY);

        $result = $this->call();
        return $result;
    }

}
