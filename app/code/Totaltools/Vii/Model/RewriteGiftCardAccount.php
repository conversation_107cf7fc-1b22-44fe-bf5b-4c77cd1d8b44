<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

namespace Totaltools\Vii\Model;

use Magento\Framework\Cache\FrontendInterface as CacheFrontendInterface;
use Magento\GiftCardAccount\Model\Giftcardaccount as OriginalGiftcardaccount;
use Totaltools\Vii\Model\Api\GiftCard as GiftCardApi;
use Totaltools\Vii\Model\Api\GiftCard as ViiApiGiftCard;

/**
 * @method string getPin()
 * @method \Totaltools\Vii\Model\RewriteGiftCardAccount setPin(string $number)
 * @method float getCurrentBalance()
 * @method \Totaltools\Vii\Model\RewriteGiftCardAccount setCurrentBalance(float $number)
  * @method string getViiTranId()
 * @method \Totaltools\Vii\Model\RewriteGiftCardAccount setViiTranId(string $number)
 * @method string getPreAuthCode()
 * @method \Totaltools\Vii\Model\RewriteGiftCardAccount setPreAuthCode(string $number)
 * @method float getAvailableBalance()
 * @method \Magento\GiftCardAccount\Model\Giftcardaccount setAvailableBalance(float $value)
 * @method string getExternalReference()
 * @method \Totaltools\Vii\Model\RewriteGiftCardAccount setExternalReference(string $number)
 */

class RewriteGiftCardAccount
    extends OriginalGiftcardaccount
    implements \Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface
{
    /**
     * Gift card Pin cart key
     *
     * @var string
     */
    const PIN = 'pin';

    const CURRENT_BALANCE = 'current_ba';

    const PREVIOUS_BASE_AMOUNT = 'previous_ba';

    const VII_TRAN_ID = 'vii_tran_id';

    const EXTERNAL_REFERENCE = 'ExternalReference';

    const PRE_AUTH_CODE = 'PreAuthCode';

    const PRE_AUTH_CODE_EXPIRED = 'PreAuthCodeExpired';

    const GIFT_CARD_PINS = 'gift_card_pins';

    const GC_CACHE_PREFIX = 'TOTALTOOLS_VII_GIFTCARD_';
    const GC_CACHE_TAG = 'TOTALTOOLS_VII_GIFTCARD';
    const GC_CACHE_LIFETIME = 14400; // 4 hours

    /**
     * Giftcard code that was requested for load
     *
     * @var bool|string
     */
    protected $_requestedPin = null;

    /**
     * Config helper
     *
     * @var \Totaltools\Vii\Helper\Config
     */
    protected $configHelper;

    /**
     * Config helper
     *
     * @var \Totaltools\Vii\Model\Api
     */
    protected $api;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    protected $_localeDate;

    /**
     * @var CacheFrontendInterface
     */
    protected $cacheFrontend;

    /**
     * RewriteGiftCardAccount constructor.
     *
     * @param \Totaltools\Vii\Helper\Config                                $configHelper
     * @param GiftCardApi                                                  $api
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface         $localeDate
     * @param \Magento\Framework\Model\Context                             $context
     * @param \Magento\Framework\Registry                                  $registry
     * @param \Magento\Framework\Api\ExtensionAttributesFactory            $extensionFactory
     * @param \Magento\Framework\Api\AttributeValueFactory                 $customAttributeFactory
     * @param \Magento\GiftCardAccount\Helper\Data                         $giftCardAccountData
     * @param \Magento\Framework\App\Config\ScopeConfigInterface           $scopeConfig
     * @param \Magento\Framework\Mail\Template\TransportBuilder            $transportBuilder
     * @param \Magento\CustomerBalance\Model\Balance                       $customerBalance
     * @param \Magento\Framework\Locale\CurrencyInterface                  $localeCurrency
     * @param \Magento\Store\Model\StoreManagerInterface                   $storeManager
     * @param \Magento\Checkout\Model\Session                              $checkoutSession
     * @param \Magento\Customer\Model\Session                              $customerSession
     * @param \Magento\GiftCardAccount\Model\PoolFactory                   $poolFactory
     * @param \Magento\Quote\Api\CartRepositoryInterface                   $quoteRepository
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null           $resourceCollection
     * @param array                                                        $data
     */
    public function __construct(
        \Totaltools\Vii\Helper\Config $configHelper,
        GiftCardApi $api,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate,
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Api\ExtensionAttributesFactory $extensionFactory,
        \Magento\Framework\Api\AttributeValueFactory $customAttributeFactory,
        \Magento\GiftCardAccount\Helper\Data $giftCardAccountData,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Magento\CustomerBalance\Model\Balance $customerBalance,
        \Magento\Framework\Locale\CurrencyInterface $localeCurrency,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\GiftCardAccount\Model\PoolFactory $poolFactory,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {

        parent::__construct(
            $context,
            $registry,
            $extensionFactory,
            $customAttributeFactory,
            $giftCardAccountData,
            $scopeConfig,
            $transportBuilder,
            $customerBalance,
            $localeCurrency,
            $storeManager,
            $checkoutSession,
            $customerSession,
            $poolFactory,
            $localeDate,
            $quoteRepository,
            $resource,
            $resourceCollection,
            $data
        );
        $this->configHelper = $configHelper;
        $this->api = $api;
        $this->_localeDate = $localeDate;
        $this->cacheFrontend = $context->getCacheManager()->getFrontend();
    }

    /**
     * @codeCoverageIgnoreStart
     * {@inheritdoc}
     */
    public function getGiftCardPins()
    {
        return $this->getData(self::GIFT_CARD_PINS);
    }

    /**
     * @codeCoverageIgnoreStart
     * {@inheritdoc}
     */
    public function setGiftCardPins(array $pins)
    {
        return $this->setData(self::GIFT_CARD_PINS, $pins);
    }


    public function getApi()
    {
        $this->api;
    }

    /**
     * Load gift card account model using specified code
     *
     * @param string $code
     * @param string|null $pin
     * @param bool $useCache
     * @return $this
     */
    public function loadByCode($code, $pin = null, $useCache = false)
    {
        $this->_requestedCode = $code;
        $this->_requestedPin = $pin;

        $cardData = null;
        $success = false;
        $cacheKey = $this->getGiftCardCacheKey($code, $pin);
        if ($useCache) {
            $cardData = json_decode($this->cacheFrontend->load($cacheKey), true);
            if ($cardData) {
                $success = true;
            }
        }
        if (!$useCache || !$cardData) {
            $cardData = $this->api->checkBalance($code, $pin);
            $success = (int) $cardData['ResponseCode'] === ViiApiGiftCard::RESPONSE_CODE_CHECK_BALANCE_SUCCESS;
            if ($success) {
                $this->cacheFrontend->save(
                    json_encode($cardData),
                    $cacheKey,
                    [self::GC_CACHE_TAG],
                    self::GC_CACHE_LIFETIME
                );
                $this->_registry->register(self::GC_CACHE_PREFIX . '_SAVED', true, true);
            }
        }

        if ($success) {
            $this->setId($cardData['CardNumber']);
            $this->setCode($cardData['CardNumber']);
            $this->setBalance($cardData['AvailableBalance']);
            $this->setCurrentBalance($cardData['CurrentBalance']);
            $this->setPin($pin);
            $this->setViiTranId($cardData['viiTranId']);
            $expiryDate = \DateTime::createFromFormat('d/m/Y h:i:s A', $cardData['ExpiryDate'])->getTimestamp();
            $expiryDate = date('Y-m-d H:i:s', $expiryDate);
            $this->setDateExpires($expiryDate);
            $this->setStatus($cardData['CardStatusId']);
            $this->setWebsiteId($this->_storeManager->getStore()->getWebsiteId());
        }
        return $this;
    }

    /**
     * Get cache key for card data
     * @param string $code
     * @param string $pin
     * @return string
     */
    protected function getGiftCardCacheKey ($code, $pin) {
        return self::GC_CACHE_PREFIX . 'CODE_' . $code . '_PIN_' . $pin;
    }

    protected function mappingStatus($cardStatusId)
    {
        switch ($cardStatusId) {
            case 0 :
                $status = self::STATUS_DISABLED;
                break;
            case 1:
                $status = self::STATUS_ENABLED;
                break;
            case 2:
                $status = self::STATE_EXPIRED;
                break;
            case 3:
                $status = self::STATE_USED;
                break;
            case 4:
                $status = self::STATE_REDEEMED;
                break;
        }
        return $status;
    }


    /**
     * Check all the gift card validity attributes
     *
     * @param bool $expirationCheck
     * @param bool $statusCheck
     * @param mixed $websiteCheck
     * @param mixed $balanceCheck
     * @return bool
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function isValid($expirationCheck = true, $statusCheck = true, $websiteCheck = false, $balanceCheck = true)
    {
        if (!$this->getCode()) {
            $this->_throwException(
                __('Please correct the gift card account ID. Requested code: "%1"', $this->_requestedCode)
            );
        }

        if ($websiteCheck) {
            if ($websiteCheck === true) {
                $websiteCheck = null;
            }
            $website = $this->_storeManager->getWebsite($websiteCheck)->getId();
            if ($this->getWebsiteId() != $website) {
                $this->_throwException(__('Please correct the gift card account website: %1.', $this->getWebsiteId()));
            }
        }

        if ($statusCheck && $this->getStatus() != self::STATUS_ENABLED) {
            $this->_throwException(__('Gift card account %1 is not enabled.', $this->getId()));
        }

        if ($expirationCheck && $this->isExpired()) {
            $this->_throwException(__('Gift card account %1 is expired.', $this->getId()));
        }

        if ($balanceCheck) {
            if ($this->getBalance() <= 0) {
                $this->_throwException(__('Gift card account %1 has a zero balance.', $this->getId()));
            }
            if ($balanceCheck !== true && is_numeric($balanceCheck)) {
                if ($this->getBalance() < $balanceCheck) {
                    $this->_throwException(
                        __('Gift card account %1 balance is lower than the charged amount.', $this->getId())
                    );
                }
            }
        }

        return true;
    }

    /**
     * Check if this gift card is expired at the moment
     *
     * @return bool
     */
    public function isExpired()
    {
        if (!$this->getDateExpires()) {
            return false;
        }
        $timezone = $this->_localeDate->getConfigTimezone(
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $this->_storeManager->getWebsite($this->getWebsiteId())->getDefaultStore()->getId()
        );

        $expirationDate = (new \DateTime($this->getDateExpires(), new \DateTimeZone($timezone)))->setTime(0, 0, 0);
        $currentDate = (new \DateTime('now', new \DateTimeZone($timezone)))->setTime(0, 0, 0);

        if ($expirationDate < $currentDate) {
            return true;
        }

        return false;
    }

    /**
     * Add gift card to quote gift card storage
     *
     * @param bool $saveQuote
     * @param \Magento\Quote\Model\Quote|null $quote
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function addToCart($saveQuote = true, $quote = null)
    {
        if (is_null($quote)) {
            $quote = $this->_checkoutSession->getQuote();
        }
        /*$website = $this->_storeManager->getStore($quote->getStoreId())->getWebsite();*/

        if ($this->isValid(true, true, false)) {
            $cards = $this->_giftCardAccountData->getCards($quote);
            if (!$cards) {
                $cards = [];
            } else {
                foreach ($cards as $one) {
                    if ($one[self::ID] == $this->getId()) {
                        throw new \Magento\Framework\Exception\LocalizedException(
                            __('This gift card account is already in the quote.')
                        );
                    }
                }
            }
            $amount = $this->getBalance();
            $grandTotal= $quote->getGrandTotal();
            if ($grandTotal <= $amount) {
                $amount = $grandTotal;
            }
            $cards[] = [
                self::ID => $this->getId(),
                self::CODE => $this->getCode(),
                self::PIN => $this->getPin(),
                self::CURRENT_BALANCE => $this->getCurrentBalance(),
                self::VII_TRAN_ID => $this->getViiTranId(),
                self::AMOUNT => $amount,
                self::BASE_AMOUNT => $amount,
            ];
            $this->_giftCardAccountData->setCards($quote, $cards);

            if ($saveQuote) {
                $quote->collectTotals();
                $this->quoteRepository->save($quote);
            }
        }

        return $this;
    }


    /**
     * Remove gift card from quote gift card storage
     *
     * @param bool $saveQuote
     * @param \Magento\Quote\Model\Quote|null $quote
     * @return $this|void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function removeFromCart($saveQuote = true, $quote = null)
    {
        if (!$this->getId()) {
            $this->_throwException(__('Please correct the gift card account code: "%1".', $this->_requestedCode));
        }
        if (is_null($quote)) {
            $quote = $this->_checkoutSession->getQuote();
        }

        $cards = $this->_giftCardAccountData->getCards($quote);
        if ($cards) {
            foreach ($cards as $k => $one) {
                if ($one[self::ID] == $this->getId()) {
                    unset($cards[$k]);
                    $this->_giftCardAccountData->setCards($quote, array_values($cards));
                    if ($saveQuote) {
                        $quote->collectTotals();
                        $this->quoteRepository->save($quote);
                    }
                    return $this;
                }
            }
        }

        $this->_throwException(__('This gift card account wasn\'t found in the quote.'));
    }

    /**
     * Reduce Gift Card Account balance by specified amount
     *
     * @param float $amount
     * @param string|null $preAuthCode
     * @return false|string[]
     * @throws \Exception
     */
    public function charge($amount, $preAuthCode = null)
    {
        if ($preAuthCode) {
            $isValid = $this->isValid(false, false, false, false);
        } else {
            $isValid = $this->isValid(false, false, false, $amount);
        }
        if ($isValid) {
            //calling API Redemption instead of charge balance in Magento core
            $result = $this->api->redemption(
                $this->getCode(),
                $this->getPin(),
                $amount,
                $this->getExternalReference(),
                $preAuthCode
            );

            if (
                isset($result['ResponseCode'])
                && (int)$result['ResponseCode'] === GiftCardApi::RESPONSE_CODE_REDEMPTION_SUCCESS
            ) {
                return $result;
            } else {
                throw new \Exception('Couldn\'t redeem amount on Gift Card');
            }
        }
        return false;
    }

    /**
     * Hold Gift Card Account balance by specified amount for preAuthRequest
     *
     * @param float $amount
     * @return false|string
     * @throws \Exception
     */
    public function hold($amount)
    {
        if ($this->isValid(false, false, false, $amount)) {
            //calling API Redemption instead of charge balance in Magento core
            $result = $this->api->preAuthRequest(
                $this->getCode(),
                $this->getPin(),
                $amount,
                $this->getExternalReference()
            );

            if (
                isset($result['ResponseCode'])
                && (int)$result['ResponseCode'] === GiftCardApi::RESPONSE_CODE_PRE_AUTH_REQUEST_SUCCESS
                && !empty($result['PreAuthCode'])
            ) {
                return $result['PreAuthCode'];
            } else {
                throw new \Exception('Couldn\'t hold preAuthRequest amount on Gift Card');
            }
        }
        return false;
    }

    /**
     * Refund Gift Card Account balance for preAuthCancellation
     *
     * @param string $preAuthCode
     * @return false|string[]
     * @throws \Exception
     */
    public function refund($preAuthCode)
    {
        $result = $this->api->preAuthCancellation(
            $this->getCode(),
            $preAuthCode
        );

        if (
            isset($result['ResponseCode'])
            && (int)$result['ResponseCode'] === GiftCardApi::RESPONSE_CODE_PRE_AUTH_REQUEST_SUCCESS
        ) {
            return $result;
        } else {
            throw new \Exception('Couldn\'t refund preAuthCancellation amount on Gift Card');
        }
        return false;
    }

    /**
     * Revert amount to gift card balance if order was not placed
     *
     * @param   float $amount
     * @return  $this
     */
    public function revert($amount)
    {
        $amount = (double)$amount;

        if ($amount > 0 && $this->isValid(true, true, false, false)) {
            $this->setBalanceDelta(
                $amount
            )->setBalance(
                $this->getBalance() + $amount
            )->setHistoryAction(
                \Magento\GiftCardAccount\Model\History::ACTION_UPDATED
            );
        }

        return $this;
    }

    /**
     * Redeem gift card (-gca balance, +cb balance)
     *
     * @param int $customerId
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    public function redeem($customerId = null)
    {
        if ($this->isValid(true, true, false, true)) {
            if ($this->getIsRedeemable() != self::REDEEMABLE) {
                $this->_throwException(sprintf('Gift card account %s is not redeemable.', $this->getId()));
            }
            if (is_null($customerId)) {
                $customerId = $this->_customerSession->getCustomerId();
            }
            if (!$customerId) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Please enter a valid customer ID.'));
            }
        }
        return $this;
    }
}
