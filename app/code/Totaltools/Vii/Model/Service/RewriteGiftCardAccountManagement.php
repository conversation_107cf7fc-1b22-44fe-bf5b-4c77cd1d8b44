<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Vii\Model\Service;

use Magento\GiftCardAccount\Model\Giftcardaccount as GiftCardAccount;
use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\CouldNotSaveException;

/**
 * Class GiftCardAccountManagement
 */
class RewriteGiftCardAccountManagement implements \Totaltools\Vii\Api\RewriteGiftCardAccountManagementInterface
{
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    protected $quoteRepository;

    /**
     * @var \Magento\GiftCardAccount\Helper\Data
     */
    protected $giftCardHelper;

    /**
     * @var \Totaltools\Vii\Model\RewriteGiftCardAccountFactory
     */
    protected $giftCardAccountFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * RewriteGiftCardAccountManagement constructor.
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\GiftCardAccount\Helper\Data $giftCardHelper
     * @param \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\GiftCardAccount\Helper\Data $giftCardHelper,
        \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCardAccountFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->quoteRepository = $quoteRepository;
        $this->giftCardHelper = $giftCardHelper;
        $this->giftCardAccountFactory = $giftCardAccountFactory;
        $this->storeManager = $storeManager;
    }

    /**
     * {@inheritdoc}
     */
    public function deleteByQuoteId($quoteId, $giftCardCode)
    {
        /** @var  \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->getActive($quoteId);
        if (!$quote->getItemsCount()) {
            throw new NoSuchEntityException(__('Cart %1 doesn\'t contain products', $quoteId));
        }

        $pin = '';
        // find pin of gift card on quote
        $quoteCards = $this->giftCardHelper->getCards($quote);
        foreach ($quoteCards as $card) {
            if ($card[ModelGiftcardaccount::CODE] == $giftCardCode) {
                $pin = $card[ModelGiftcardaccount::PIN];
            }
        }

        /** @var \Totaltools\Vii\Model\RewriteGiftcardAccount $giftCard */
        $giftCard = $this->giftCardAccountFactory->create();
        $useCache = true;
        $giftCard->loadByCode($giftCardCode, $pin, $useCache);

        try {
            $giftCard->removeFromCart(true, $quote);
        } catch (\Exception $e) {
            throw new CouldNotDeleteException(__('Could not delete gift card from quote'));
        }
        return true;
    }


    /**
     * {@inheritdoc}
     */
    public function getListByQuoteId($cartId)
    {
        /** @var  \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->getActive($cartId);
        $giftCards = $this->giftCardHelper->getCards($quote);
        $cards = [];
        foreach ($giftCards as $giftCard) {
            $cards[] = $giftCard[GiftCardAccount::CODE];
        }
        $data = [
            GiftCardAccount::GIFT_CARDS => $cards,
            GiftCardAccount::GIFT_CARDS_AMOUNT => $quote->getGiftCardsAmount(),
            GiftCardAccount::BASE_GIFT_CARDS_AMOUNT => $quote->getBaseGiftCardsAmount(),
            GiftCardAccount::GIFT_CARDS_AMOUNT_USED => $quote->getGiftCardsAmountUsed(),
            GiftCardAccount::BASE_GIFT_CARDS_AMOUNT_USED => $quote->getBaseGiftCardsAmountUsed(),
        ];

        /** @var \Magento\GiftCardAccount\Model\Giftcardaccount $giftCardAccount */
        $giftCardAccount = $this->giftCardAccountFactory->create(['data' => $data]);
        return $giftCardAccount;
    }

    /**
     * {@inheritdoc}
     */
    public function saveByQuoteId(
        $cartId,
        \Totaltools\Vii\Api\Data\RewriteGiftCardAccountInterface $giftCardAccountData
    ) {
        /** @var  \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->getActive($cartId);
        if (!$quote->getItemsCount()) {
            throw new NoSuchEntityException(__('Cart %1 doesn\'t contain products', $cartId));
        }
        $cardCode = $giftCardAccountData->getGiftCards();
        $cardPin = $giftCardAccountData->getGiftCardPins();
        /** @var \Totaltools\Vii\Model\RewriteGiftCardAccount $giftCard */
        $giftCard = $this->giftCardAccountFactory->create();
        $giftCard->loadByCode(array_shift($cardCode), array_shift($cardPin));
        try {
            $giftCard->addToCart(true, $quote);
        } catch (\Exception $e) {
            throw new CouldNotSaveException(__('Could not add gift card code'));
        }
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function checkGiftCard($cartId, $giftCardCode, $giftCardPin)
    {
        if (!$giftCardPin) {
            throw new CouldNotSaveException(__('Please correct the wrong or expired Gift Card Pin.'));
        }
        $quote = $this->quoteRepository->getActive($cartId);
        $giftCard = $this->giftCardAccountFactory->create();
        $giftCard->loadByCode($giftCardCode, $giftCardPin);

        try {
            $giftCard->isValid(true, true, false, false);
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            throw new CouldNotSaveException(__('Please correct the wrong or expired Gift Card Code.'));
        }
        /** @var \Magento\Directory\Model\Currency $currency */
        $currency = $this->storeManager->getStore()->getBaseCurrency();
        return $currency->convert($giftCard->getBalance(), $quote->getQuoteCurrencyCode());
    }
}
