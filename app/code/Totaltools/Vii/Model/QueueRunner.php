<?php

namespace Totaltools\Vii\Model;

use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrderBuilder;
use Psr\Log\LoggerInterface;
use Totaltools\Vii\Api\Data\QueueActionInterface;
use Totaltools\Vii\Api\Data\QueueInterface as QueueData;
use Totaltools\Vii\Api\QueueRepositoryInterface;
use Totaltools\Vii\Model\Queue\OrderAction;
use Totaltools\Vii\Model\Queue\UndoAction;

class QueueRunner
{

    const QUEUE_SIZE = 20;
    const MAX_RETRIES = 240; // (4 hours of per minute retries)

    const DAYS_KEEP_QUEUE = 30;

    /**
     * Queue repository
     * @var QueueRepositoryInterface
     */
    private $queueRepository;

    /**
     * Search criteria builder
     * @var SearchCriteriaBuilder
     */
    private $searchCriteriaBuilder;

    /**
     * Sort order builder
     * @var SortOrderBuilder
     */
    private $sortOrderBuilder;

    /**
     * Undo action
     * @var UndoAction
     */
    private $undoAction;

    /**
     * Order action
     * @var OrderAction
     */
    private $orderAction;

    /**
     * Logger
     * @var LoggerInterface
     */
    private $logger;

    /**
     * QueueRunner constructor
     *
     * @param QueueRepositoryInterface $queueRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param SortOrderBuilder $sortOrderBuilder
     * @param UndoAction $undoAction
     * @param OrderAction $orderAction
     * @param LoggerInterface $logger
     */
    public function __construct (
        QueueRepositoryInterface $queueRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        SortOrderBuilder $sortOrderBuilder,
        UndoAction $undoAction,
        OrderAction $orderAction,
        LoggerInterface $logger
    ) {
        $this->queueRepository = $queueRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->sortOrderBuilder = $sortOrderBuilder;
        $this->undoAction = $undoAction;
        $this->orderAction = $orderAction;
        $this->logger = $logger;
    }

    /**
     * Run queue
     *
     * @return void
     */
    public function run ()
    {
        $this->actionQueue();
        $this->cleanQueue();
    }

    /**
     * Action queue
     *
     * @return void
     */
    private function actionQueue()
    {
        /** @var QueueData $queueItem */
        foreach ($this->getQueueItems() as $queueItem) {
            switch ($queueItem->getAction()) {
                case Queue::ACTION_UNDO:
                    $status = $this->actionItem($this->undoAction, $queueItem);
                    break;
                case Queue::ACTION_ORDER:
                    $status = $this->actionItem($this->orderAction, $queueItem);
                    break;
                default:
                    $status = Queue::STATUS_ERROR;
                    $this->logger->warning('Unknown Vii queue action: ' . $queueItem->getAction());
                    break;
            }

            $queueItem
                ->setStatus($status)
                ->setRetries($queueItem->getRetries() + 1);

            $this->queueRepository->save($queueItem);
        }
    }

    /**
     * Remove old queued jobs
     *
     * @return void
     */
    private function cleanQueue ()
    {
        $interval = 'P' . self::DAYS_KEEP_QUEUE . 'D';
        $oldDate = (new \DateTime())->sub(new \DateInterval($interval))->format('Y-m-d');

        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(Queue::STATUS, Queue::STATUS_COMPLETE)
            ->addFilter(Queue::CREATED_AT, $oldDate, 'lt')
            ->create()
        ;

        $oldItems = $this->queueRepository->getList($searchCriteria)->getItems();
        /** @var QueueData $oldItem */
        foreach ($oldItems as $oldItem) {
            $this->queueRepository->delete($oldItem);
        }
    }

    /**
     * Get Queue items
     *
     * @return QueueData[]
     */
    private function getQueueItems ()
    {
        $sortOrder = $this->sortOrderBuilder
            ->setField(Queue::CREATED_AT)
            ->setAscendingDirection()
            ->create()
        ;

        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter(Queue::STATUS, Queue::STATUS_PENDING)
            ->addFilter(Queue::RETRIES, self::MAX_RETRIES, 'lt')
            ->addSortOrder($sortOrder)
            ->setPageSize(self::QUEUE_SIZE)
            ->setCurrentPage(1)
            ->create()
        ;

        /** @var QueueData[] $items */
        $items = $this->queueRepository->getList($searchCriteria)->getItems();
        return $items;
    }

    /**
     * Action queue item
     *
     * @param QueueActionInterface $action
     * @param QueueData $queueItem
     * @return null|string
     */
    private function actionItem (
        QueueActionInterface $action,
        QueueData $queueItem
    ) {
        $status = Queue::STATUS_PENDING;
        $data = $queueItem->getQueueData();
        if (is_array($data)) {
            $complete = $action->action($data);
            if ($complete) {
                $status = Queue::STATUS_COMPLETE;
            }
        } else {
            $status = Queue::STATUS_ERROR;
            $this->logger->warning('Vii queue item has no data: ' . $queueItem->getId());
        }
        return $status;
    }

}
