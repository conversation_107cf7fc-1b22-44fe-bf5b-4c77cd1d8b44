<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Vii\Observer;

use Magento\Framework\Event\ObserverInterface;

use Totaltools\Vii\Model\Api\GiftCard as GiftCardApi;
use \Totaltools\Vii\Model\RewriteGiftCardAccount as Giftcardaccount;
use Magento\Framework\Serialize\Serializer\Json;

class RewriteProcessOrderPlace implements ObserverInterface
{
    /**
     * Gift card account data
     *
     * @var \Magento\GiftCardAccount\Helper\Data
     */
    protected $giftCAHelper;

    /**
     * @var \Totaltools\Vii\Model\RewriteGiftCardAccountFactory
     */
    protected $giftCAFactory;

    /**
     * Gift Card API
     * @var GiftCardApi
     */
    private $giftCardApi;

    /**
     * Instance of serializer.
     *
     * @var Json
     */
    private $serializer;

    /**
     * RewriteProcessOrderPlace constructor.
     * @param \Magento\GiftCardAccount\Helper\Data $giftCAHelper
     * @param \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCAFactory
     * @param GiftCardApi $giftCardApi
     * @param Json $serializer
     */
    public function __construct(
        \Magento\GiftCardAccount\Helper\Data $giftCAHelper,
        \Totaltools\Vii\Model\RewriteGiftCardAccountFactory $giftCAFactory,
        GiftCardApi $giftCardApi,
        Json $serializer
    ) {
        $this->giftCAHelper = $giftCAHelper;
        $this->giftCAFactory = $giftCAFactory;
        $this->giftCardApi = $giftCardApi;
        $this->serializer = $serializer;
    }

    /**
     * Charge all gift cards applied to the order
     * used for event: sales_order_place_after
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return $this
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /** @var \Magento\Sales\Model\Order $order */
        $order = $observer->getEvent()->getOrder();
        $code = $order->getPayment()->getMethodInstance()->getCode();
        /** @var \Magento\Quote\Model\Quote\Address $address */
        $address = $observer->getEvent()->getAddress();
                
        if (!$address) {
            // Single address checkout.
            /** @var \Magento\Quote\Model\Quote $quote */
            $quote = $observer->getEvent()->getQuote();
            $address = $quote->isVirtual() ? $quote->getBillingAddress() : $quote->getShippingAddress();
        }

        $order->setGiftCardsAmount($address->getGiftCardsAmount());
        $order->setBaseGiftCardsAmount($address->getBaseGiftCardsAmount());
        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $observer->getEvent()->getQuote();
        $cards = $this->giftCAHelper->getCards($quote);
        if (!empty($cards) && is_array($cards)) {
            foreach ($cards as &$card) {
                /** @var \Totaltools\Vii\Model\RewriteGiftCardAccount $giftCard */
                $giftCard = $this->giftCAFactory->create();
                $giftCard->loadByCode($card[Giftcardaccount::ID], $card[Giftcardaccount::PIN]);
                if ($giftCard->getId()) {
                    $giftCard->setExternalReference($order->getIncrementId());
                    if(($code == 'braintree' || $code == 'braintree_cc_vault' || 
                        $code == 'braintree_applepay' || $code == 'braintree_googlepay')) {
                        $preAuthCode = $giftCard->hold($card[Giftcardaccount::BASE_AMOUNT]);
                        $card[Giftcardaccount::PRE_AUTH_CODE] = $preAuthCode;
                        $giftCardMessage = "holded";
                    } else {
                        $giftCard->charge($card[Giftcardaccount::BASE_AMOUNT]);
                        $giftCardMessage = "redeem";
                    }
                    $card[Giftcardaccount::AUTHORIZED] = $card[Giftcardaccount::BASE_AMOUNT];
                }
            }
            $order->setGiftCards($this->serializer->serialize($cards));
            if (!empty($giftCardMessage) && ($giftCardMessage === 'holded' || $giftCardMessage === 'redeem')) {
                $comment = $giftCardMessage === 'redeem' ? "Gift Cards Redeemed" : "Gift Cards Holded - PreAuthRequest";
                $status = $giftCardMessage === 'redeem' ? 'processing' : 'pending';
                $order->addCommentToStatusHistory($comment, $status);
            }
        }

        return $this;
    }
}
