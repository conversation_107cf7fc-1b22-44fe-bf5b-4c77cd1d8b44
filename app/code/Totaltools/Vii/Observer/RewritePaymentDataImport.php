<?php

namespace Totaltools\Vii\Observer;

use Magento\GiftCardAccount\Observer\PaymentDataImport as GiftCAPaymentDataImport;

class RewritePaymentDataImport extends GiftCAPaymentDataImport
{

    /**
     * Set flag that giftcard applied on payment step in checkout process
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return $this
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /* @var $quote \Magento\Quote\Model\Quote */
        $quote = $observer->getEvent()->getPayment()->getQuote();
        if (!$quote || !$quote->getCustomerId()) {
            return $this;
        }
        /* Gift cards validation */
        $cards = $this->giftCAHelper->getCards($quote);
        $website = $this->storeManager->getStore($quote->getStoreId())->getWebsite();
        foreach ($cards as $one) {
            /** @var \Totaltools\Vii\Model\RewriteGiftCardAccount $giftCard */
            $giftCard = $this->giftCAFactory->create();
            $giftCard
                ->loadByCode(
                    $one[\Magento\GiftCardAccount\Model\Giftcardaccount::CODE],
                    $one[\Totaltools\Vii\Model\RewriteGiftCardAccount::PIN]
                )->isValid(true, true, $website);
        }

        if ((double)$quote->getBaseGiftCardsAmountUsed()) {
            $quote->setGiftCardAccountApplied(true);
            $input = $observer->getEvent()->getInput();
            if (!$input->getMethod()) {
                $input->setMethod('free');
            }
        }
        return $this;
    }

}