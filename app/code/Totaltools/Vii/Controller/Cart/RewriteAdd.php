<?php
/**
 *
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Vii\Controller\Cart;

class RewriteAdd extends \Magento\GiftCardAccount\Controller\Cart\Add
{
    /**
     * Add Gift Card to current quote
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        if (isset($data['giftcard_code']) && isset($data['giftcard_pin'])) {
            $code = $data['giftcard_code'];
            $pin = $data['giftcard_pin'];
            try {
                /*if (strlen($code) > \Magento\GiftCardAccount\Helper\Data::GIFT_CARD_CODE_MAX_LENGTH) {
                    throw new \Magento\Framework\Exception\LocalizedException(__('Please correct the gift card code.'));
                }*/
                $this->_objectManager->create(
                    'Magento\GiftCardAccount\Model\Giftcardaccount'
                )->loadByCode(
                    $code,
                    $pin
                )->addToCart();
                $this->messageManager->addSuccess(
                    __(
                        'Gift Card "%1" was added.',
                        $this->_objectManager->get('Magento\Framework\Escaper')->escapeHtml($code)
                    )
                );
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $this->messageManager->addError($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addException($e, __('We cannot apply this gift card.'));
            }
        }

        return $this->_goBack();
    }
}
