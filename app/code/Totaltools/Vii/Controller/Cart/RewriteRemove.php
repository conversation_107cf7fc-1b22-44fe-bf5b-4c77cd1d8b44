<?php
/**
 *
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Vii\Controller\Cart;

use Magento\GiftCardAccount\Block\Checkout\Cart\Giftcardaccount;

use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;

class RewriteRemove extends \Magento\GiftCardAccount\Controller\Cart\Remove
{
    /**
     * @return \Magento\Framework\Controller\Result\Redirect|void
     */
    public function execute()
    {
        $code = $this->getRequest()->getParam('code');
        /*$pin = $this->getRequest()->getParam('pin');*/
        if ($code) {
            $pin = '';
            // find pin of gift card on quote
            $giftCardData = $this->_objectManager->create('Magento\GiftCardAccount\Helper\Data');
            $quoteCards = $giftCardData->getCards($this->_checkoutSession->getQuote());
            foreach ($quoteCards as $card) {
                if ($card[ModelGiftcardaccount::CODE] == $code) {
                    $pin = $card[ModelGiftcardaccount::PIN];
                }
            }
            if ($pin) {
                try {
                    $this->_objectManager->create(
                        'Magento\GiftCardAccount\Model\Giftcardaccount'
                    )->loadByCode(
                        $code,
                        $pin
                    )->removeFromCart();
                    $this->messageManager->addSuccess(
                        __(
                            'Gift Card "%1" was removed.',
                            $this->_objectManager->get('Magento\Framework\Escaper')->escapeHtml($code)
                        )
                    );
                } catch (\Magento\Framework\Exception\LocalizedException $e) {
                    $this->messageManager->addError($e->getMessage());
                } catch (\Exception $e) {
                    $this->messageManager->addException($e, __('You can\'t remove this gift card.'));
                }
                return $this->_goBack();
            } else {
                $this->_forward('noroute');
            }

        } else {
            $this->_forward('noroute');
        }
    }
}
