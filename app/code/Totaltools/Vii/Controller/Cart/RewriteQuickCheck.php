<?php
/**
 *
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Vii\Controller\Cart;

class RewriteQuickCheck extends \Magento\GiftCardAccount\Controller\Cart\QuickCheck
{
    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry
    ) {
        parent::__construct($context, $coreRegistry);
    }

    /**
     * Check a gift card account availability
     *
     * @return void
     */
    public function execute()
    {
        /* @var $card \Magento\GiftCardAccount\Model\Giftcardaccount */
        $card = $this->_objectManager->create(
            'Magento\GiftCardAccount\Model\Giftcardaccount'
        )->loadByCode(
            $this->getRequest()->getParam('giftcard_code', ''),
            $this->getRequest()->getParam('giftcard_pin', '')
        );
        $this->_coreRegistry->register('current_giftcardaccount', $card);
        try {
            $card->isValid(true, true, false, false);
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $card->unsetData();
        }

        $this->_view->loadLayout();
        $this->_view->renderLayout();
    }
}
