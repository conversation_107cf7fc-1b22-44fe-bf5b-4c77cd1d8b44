<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */
namespace Totaltools\Vii\Controller\Balance;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Vii\Block\Balance\Check\Form as BalanceCheckFormBlock;
use Totaltools\Vii\Model\Api\GiftCard;

class Post
    extends Action
{

    /**
     * Check action path
     * @var string
     */
    protected $checkAction = 'totaltools_vii_giftcards/balance/check';

    /**
     * Gift Card API model
     * @var GiftCard
     */
    protected $giftCardApi;

    /**
     * Store Manager model
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * Data Persistor
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * POST data
     * @var string[]
     */
    protected $post;


    /**
     * Post constructor
     *
     * @param GiftCard $giftCardApi
     * @param StoreManagerInterface $storeManager
     * @param DataPersistorInterface $dataPersistor
     * @param Context $context
     */
    public function __construct (
        GiftCard $giftCardApi,
        StoreManagerInterface $storeManager,
        DataPersistorInterface $dataPersistor,
        Context $context
    ) {
        parent::__construct($context);
        $this->giftCardApi = $giftCardApi;
        $this->storeManager = $storeManager;
        $this->dataPersistor = $dataPersistor;
    }

    /**
     * Validate POST data
     *
     * @throws \Exception
     * @return void
     */
    protected function validate ()
    {
        $validEmpty = new \Laminas\Validator\NotEmpty();
        if (
            !$validEmpty->isValid(trim($this->post['card-number']))
            ||!$validEmpty->isValid(trim($this->post['card-pin']))
            || $validEmpty->isValid(trim($this->post['hideit']))
        ) {
            throw new \Exception();
        }
    }

    /**
     * {@inheritdoc}
     */
    public function execute()
    {
        $this->post = $this->getRequest()->getPostValue();
        if (!$this->post) {
            $this->_redirect($this->checkAction);
            return;
        }

        try {
            $this->validate();

            $result = $this->giftCardApi->checkBalance(
                trim($this->post['card-number']),
                trim($this->post['card-pin'])
            );

            if (
                isset($result['ResponseCode'])
                && (int) $result['ResponseCode'] !== GiftCard::RESPONSE_CODE_CHECK_BALANCE_SUCCESS
            ) {
                $message = 'Something went wrong with your request. Please check your card details and try again.';
                $this->messageManager->addErrorMessage(__($message));
                $this->_redirect($this->checkAction);
                return;
            }

            if (isset($result['ExpiryDate'])) {
                $this->dataPersistor->set(
                    BalanceCheckFormBlock::DATA_KEY_EXPIRY,
                    $result['ExpiryDate']
                );
            }

            if (isset($result['AvailableBalance'])) {
                $currency = $this->storeManager->getStore()->getBaseCurrency();
                $balance = $currency->convert($result['AvailableBalance'], 'AUD');
                $this->dataPersistor->set(
                    BalanceCheckFormBlock::DATA_KEY_BALANCE,
                    $balance
                );
            }

            $this->_redirect($this->checkAction);
            return;
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(
                __('Please enter your card number & PIN')
            );
            $this->_redirect($this->checkAction);
            return;
        }
    }

}
