<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */

namespace Totaltools\Vii\Controller\Test;

use \Totaltools\Vii\Model\Api as ApiModel;
use \Totaltools\Vii\Model\Api\Order as OrderApi;

class Index
    extends \Magento\Framework\App\Action\Action
{

    /**
     * @var  ApiModel
     */
    protected $api;

    /**
     * @var  OrderApi
     */
    protected $orderApi;

    /**
     * @var  \Totaltools\Vii\Helper\Utility
     */
    protected $utilityHelper;


    /**
     * @param  \Magento\Framework\App\Action\Context $context
     * @param  ApiModel $api
     * @param  OrderApi $api
     * @param  \Totaltools\Vii\Helper\Utility $utilityHelper
    */
    public function __construct (
        \Magento\Framework\App\Action\Context $context
        , ApiModel $api
        , OrderApi $orderApi
        , \Totaltools\Vii\Helper\Utility $utilityHelper
    ) {
        parent::__construct($context);
        $this->api = $api;
        $this->orderApi = $orderApi;
        $this->utilityHelper = $utilityHelper;
    }


    protected function testPreAuthRequest ($transId) {
        $this->api
            ->setRequestType(ApiModel::REQUEST_TYPE_PRE_AUTH_REQUEST)
            ->setBody([
                'CardNumber'        => '502904177000030302',
                'PIN'               => '7031',
                'Amount'            => '10',
                'ExternalReference' => $transId,
            ])
        ;

        $result = $this->api
            ->setResponseFormat(ApiModel::RESPONSE_FORMAT_ARRAY)
            ->call()
        ;

        /*header('Content-Type: text/xml');*/
        \Zend_Debug::dump($result);
        die;
    }


    protected function checkBalance () {
        $this->api
            ->setRequestType(ApiModel::REQUEST_TYPE_CHECK_BALANCE)
            ->setBody([
                'CardNumber'        => '502904177000030302',
                'PIN'               => '7031',
            ])
        ;

        $result = $this->api
            ->setResponseFormat(ApiModel::RESPONSE_FORMAT_ARRAY)
            ->call()
        ;

        /*header('Content-Type: text/xml');*/
        \Zend_Debug::dump($result);
        return $result;
    }


    protected function testNewOrder () {
        $this->orderApi
            ->setOrderId('abc789')
            ->setAmount(999.00)
        ;

        $this->orderApi
            ->setCustomerSalutation('Mr')
            ->setCustomerFirstName('Danny')
            ->setCustomerLastName('Nimmo')
            ->setCustomerEmail('<EMAIL>')
        ;

        $this->orderApi
            ->setToSalutation('Mrs')
            ->setToFirstName('Danielle')
            ->setToLastName('Nim')
            ->setToEmail('<EMAIL>')
        ;

        $this->orderApi
            ->setCardTo('John')
            ->setCardMessage("Congrats\nMate")
            ->setCardFrom('Jim')
        ;

        /* Alternative syntax

        $this->orderApi->setOrderData([
            'OrderId' => 'abc789',
            'Amount'  => 1000.00,
            'CustomerSalutation' => 'Mr',
            'CustomerFirstName'  => 'Danny',
            'CustomerLastName'   => 'Nimmo',
            'CustomerEmail'      => '<EMAIL>',
            'ToSalutation' => 'Mrs',
            'ToFirstName'  => 'Danielle',
            'ToLastName'   => 'Nim',
            'ToEmail'      => '<EMAIL>',
            'CardTo'      => 'John',
            'CardMessage' => "Congrats\nMate",
            'CardFrom'    => 'Jim',
        ]);
        */


        $result = $this->orderApi
            ->setResponseFormat(OrderApi::RESPONSE_FORMAT_XML)
            ->call()
        ;

        header('Content-Type: text/xml');
        echo $result;
        die;
    }


    public function execute () {
        $result = $this->checkBalance();
        $transId = $result['SerialNumber'];
        $this->testPreAuthRequest($transId);
        // $this->testNewOrder();
    }

}
