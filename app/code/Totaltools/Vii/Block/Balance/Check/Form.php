<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Vii
 */
namespace Totaltools\Vii\Block\Balance\Check;

use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\CurrencyInterface;
use Magento\Framework\Stdlib\DateTime\DateTimeFormatterInterface;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

class Form
    extends Template
{

    const DATA_KEY_BALANCE = 'totaltools_vii_giftcards_balance';
    const DATA_KEY_EXPIRY  = 'totaltools_vii_giftcards_expiry';

    // http://www.icu-project.org/apiref/icu4c/classSimpleDateFormat.html#details
    const EXPIRY_DATE_FORMAT = 'dd/MM/YYYY';

    /**
     * POST action path
     * @var string
     */
    protected $postAction = 'totaltools_vii_giftcards/balance/post';

    /**
     * Data Persistor
     * @var DataPersistorInterface
     */
    protected $dataPersistor;

    /**
     * Currency
     * @var CurrencyInterface
     */
    protected $currency;

    /**
     * DateTimeFormatter
     * @var DateTimeFormatterInterface
     */
    protected $dateTimeFormatter;

    /**
     * Form constructor
     *
     * @param DataPersistorInterface $dataPersistor
     * @param CurrencyInterface $currency
     * @param DateTimeFormatterInterface $dateTimeFormatter
     * @param Context $context
     * @param array $data
     */
    public function __construct (
        DataPersistorInterface $dataPersistor,
        CurrencyInterface $currency,
        DateTimeFormatterInterface $dateTimeFormatter,
        Context $context,
        array $data
    ) {
        parent::__construct($context, $data);
        $this->dataPersistor = $dataPersistor;
        $this->currency = $currency;
        $this->dateTimeFormatter = $dateTimeFormatter;
    }

    /**
     * Get form action
     *
     * @return string
     */
    public function getFormAction ()
    {
        return $this->getUrl(
            $this->postAction,
            [
                '_secure' => true,
            ]
        );
    }

    /**
     * Get Gift Card balance
     *
     * @return string|null
     */
    public function getBalance ()
    {
        $balance = $this->dataPersistor->get(self::DATA_KEY_BALANCE);
        $this->dataPersistor->clear(self::DATA_KEY_BALANCE);

        if ($balance === null) {
            return null;
        }

        return $this->currency->toCurrency(
            $balance
        );
    }

    /**
     * Get Gift Card expiry date
     *
     * @return string|null
     */
    public function getExpiryDate ()
    {
        $date = $this->dataPersistor->get(self::DATA_KEY_EXPIRY);
        $this->dataPersistor->clear(self::DATA_KEY_EXPIRY);

        if ($date === null) {
            return null;
        }

        // Replace "/" with "-" so PHP assumes non-US date format (http://php.net/manual/en/datetime.formats.php)
        $date = str_replace('/', '-', $date);

        return $this->dateTimeFormatter->formatObject(
            new \DateTime($date),
            self::EXPIRY_DATE_FORMAT
        );
    }

}
