<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Vii\Block\Checkout\LayoutProcessor;

use Totaltools\Checkout\Helper\Data;

/**
 * layout processor checkout page plugin
 */
class Plugin
{
    /**
     * @var Data
     */
    protected $_helper;

    /**
     * Plugin constructor.
     * @param Data $data
     */
    public function __construct(
      Data $data
    ) {
        $this->_helper = $data;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        array  $jsLayout
    ) {
        if (isset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']
            ['payment']['children']['afterMethods']['children']['giftCardAccount'])) {

            $jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']
            ['payment']['children']['afterMethods']['children']['giftCardAccount']['component'] =
                'Totaltools_Vii/js/view/payment/gift-card-account';
        }

        if (isset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']
            ['totals']['children']['giftCardAccount'])) {

            $jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']
            ['totals']['children']['giftCardAccount']['component'] = 'Totaltools_Vii/js/view/summary/gift-card-account';
        }

        if ($this->_helper->isB2BCustomer()) {
            if (isset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['giftCardAccount'])) {
                unset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['giftCardAccount']);
            }

            if (isset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['giftCardAccount'])) {
                unset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['giftCardAccount']);
            }

            if (isset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']['totals']['children']['giftCardAccount'])) {
                unset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']['totals']['children']['giftCardAccount']);
            }

            if (isset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']['giftCardAccount'])) {
                unset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']['giftCardAccount']);
            }

            if (isset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']['discount'])) {
                unset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']['discount']);
            }

            if (isset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']['totals']['children']['discount'])) {
                unset($jsLayout['components']['checkout']['children']['sidebar']['children']['summary']['children']['totals']['children']['discount']);
            }

            if (isset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['discount'])) {
                unset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['discount']);
            }
        }

        return $jsLayout;
    }
}