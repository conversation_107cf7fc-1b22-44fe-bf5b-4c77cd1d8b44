<?php

namespace Totaltools\Vii\Cron;

use Totaltools\Vii\Model\QueueRunner as QueueRunnerModel;

class QueueRunner
{

    /**
     * Queue runner
     *
     * @var QueueRunnerModel
     */
    private $queueRunner;

    /**
     * QueueRunner constructor
     *
     * @param QueueRunnerModel $queueRunner
     */
    public function __construct (
        QueueRunnerModel $queueRunner
    ) {
        $this->queueRunner = $queueRunner;
    }

    /**
     * Execute queue
     *
     * @return void
     */
    public function execute ()
    {
        try {
            $this->queueRunner->run();
        } catch (\Exception $e) {
            echo "\n";
            echo $e->getMessage();
            echo "\n";
        }
    }

}