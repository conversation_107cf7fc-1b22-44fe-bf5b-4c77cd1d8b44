<?php


namespace Totaltools\Ec\Helper;


class Data extends \Anowave\Ec\Helper\Data
{
    /**
     * Get loaded product collection from product list block
     *
     * @param \Magento\Catalog\Block\Product\ListProduct $list
     */
    protected function getLoadedCollection(\Magento\Catalog\Block\Product\ListProduct $list)
    {
        $collection = $list->getLayer()->getProductCollection();

        /**
         * Get toolbar
         */
        $toolbar = $list->getToolbarBlock();

        if ($toolbar)
        {
            $orders = $list->getAvailableOrders();

            if ($orders)
            {
                $toolbar->setAvailableOrders($orders);
            }

            $sort = $list->getSortBy();

            if ($sort)
            {
                $toolbar->setDefaultOrder($sort);
            }

            $dir = $list->getDefaultDirection();

            if ($dir)
            {
                $toolbar->setDefaultDirection($dir);
            }

            $modes = $list->getModes();

            if ($modes)
            {
                $toolbar->setModes($modes);
            }

            $collection->setCurPage($toolbar->getCurrentPage());

            $limit = (int) $toolbar->getLimit();

            if ($limit)
            {
                $collection->setPageSize($limit);
            }

            if ($toolbar->getCurrentOrder())
            {
                $collection->setOrder($toolbar->getCurrentOrder(), $toolbar->getCurrentDirection());
            }
        }

        return $collection;
    }
}
