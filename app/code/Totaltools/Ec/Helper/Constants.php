<?php

namespace Totaltools\Ec\Helper;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class Constants
{
    /**
     * Category path extension attribute name
     * 
     * @var string
     */
    const CATEGORY_PATH = 'category_path';

    /**
     * Seperator used for categories
     * 
     * @var string
     */
    const CATEGORY_SEPERATOR = '/';

    /**
     * Seperator used for categories in Emarsys
     * 
     * @var string
     */
    const EMARSYS_CATEGORY_SEPERATOR = ' > ';

    /**
     * Homepage Category name
     * 
     * @var string
     */
    const HOME_CATEGORY = 'Home';

    /**
     * Cart Category name
     * 
     * @var string
     */
    const CART_CATEGORY = 'Cart';

    /**
     * Checkout Category name
     * 
     * @var string
     */
    const CHECKOUT_CATEGORY = 'Checkout';

    /**
     * Product Category name
     * 
     * @var string
     */
    const PRODUCT_CATEGORY = 'Product';

    /**
     * Search Results Category name
     * 
     * @var string
     */
    const SEARCH_CATEGORY = 'Search Results';

    /**
     * @var string
     */
    const LIST_RELATED = 'Frequently Purchased Together';

    /**
     * @var string
     */
    const LIST_UPSELL = 'You May Also Like';

    /**
     * @var string
     */
    const UPSELL_ADD_TO_CART_SELECTOR = '//div/div/div/div/button[contains(@class,"tocart")]';

    /**
     * @var string
     */
    const PARAM_RELATED_ITEMS = 'related_product';

    /**
     * @var string
     */
    const PARAM_COMBO_ITEMS = 'combo-item-ids';
}
