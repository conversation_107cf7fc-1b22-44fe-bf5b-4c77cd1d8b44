<?php

namespace Totaltools\Ec\Helper\Emarsys;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @var string
     */
    const EMARSYS_PRODUCTS_ADD_TO_CART = 'Emarsys_products_addtocart';

    /**
     * @var string
     */
    const EMARSYS_PRODUCTS_TO_REMOVE = 'Emarsys_products_to_remove';

    /**
     * @var string
     */
    const EMARSYS_CART_CONTENT_COOKIE = 'emarsys_cart_contents';

    /**
     * @var string
     */
    const EMARSYS_REMOVE_CART_CONTENT_COOKIE = 'emarsys_remove_cart_contents';

    /**
     * @var string
     */
    const EMARSYS_USER_INFO = 'emarsys_user_info';

    /**
     * @var \Magento\Framework\Stdlib\CookieManagerInterface
     */
    protected $cookieManager;

    /**
     * @var \Magento\Framework\Stdlib\Cookie\CookieMetadataFactory
     */
    protected $cookieMetadataFactory;

    /**
     * @var \Magento\Framework\Session\SessionManagerInterface
     */
    protected $sessionManager;

    /**
     * @var \Magento\Checkout\Model\Cart
     */
    protected $cart;

    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    protected $jsonHelper;

    /**
     * @var \Magento\Checkout\Helper\Data
     */
    protected $checkoutHelper;

    /**
     * @param \Magento\Framework\Stdlib\CookieManagerInterface $cookieManager,
     * @param \Magento\Framework\Stdlib\Cookie\CookieMetadataFactory $cookieMetadataFactory,
     * @param \Magento\Framework\Session\SessionManagerInterface $sessionManager,
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Magento\Checkout\Model\Cart $cart
     * @param \Magento\Checkout\Helper\Data $checkoutHelper
     */
    public function __construct(
        \Magento\Framework\Stdlib\CookieManagerInterface $cookieManager,
        \Magento\Framework\Stdlib\Cookie\CookieMetadataFactory $cookieMetadataFactory,
        \Magento\Framework\Session\SessionManagerInterface $sessionManager,
        \Magento\Checkout\Model\Cart $cart,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\Checkout\Helper\Data $checkoutHelper,
        \Magento\Framework\App\Helper\Context $context
    ) {
        $this->cookieManager = $cookieManager;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
        $this->sessionManager = $sessionManager;
        $this->cart = $cart;
        $this->jsonHelper = $jsonHelper;
        $this->checkoutHelper = $checkoutHelper;
        parent::__construct($context);
    }

    /**
     * @param string $cookieName
     * @param mixed $data
     * @param int $duration
     * @param bool $deleteExisting
     */
    public function setCookie(string $cookieName, $data, int $duration = 3600, bool $deleteExisting = true)
    {
        if ($deleteExisting) {
            $this->deleteCookie($cookieName);
        }

        $publicCookieMetadata = $this->cookieMetadataFactory->createPublicCookieMetadata()
            ->setDuration($this->sessionManager->getCookieLifetime() ?: $duration)
            ->setPath($this->sessionManager->getCookiePath())
            ->setDomain($this->sessionManager->getCookieDomain())
            ->setHttpOnly(false);

        $this->cookieManager->setPublicCookie(
            $cookieName,
            $this->jsonHelper->jsonEncode($data),
            $publicCookieMetadata
        );

        return $this;
    }

    /**
     * @param $cookieName
     * @return void
     */
    public function deleteCookie(string $cookieName)
    {
        if ($this->cookieManager->getCookie($cookieName)) {
            $publicCookieMetadata = $this->cookieMetadataFactory->createPublicCookieMetadata()
                ->setDuration(time() - 3600)
                ->setPath($this->sessionManager->getCookiePath())
                ->setDomain($this->sessionManager->getCookieDomain())
                ->setHttpOnly(false);

            $this->cookieManager->deleteCookie($cookieName, $publicCookieMetadata);
        }

        return $this;
    }

    /**
     * @param \Magento\Quote\Model\Quote $quote
     * @return array|bool
     */
    protected function getQuoteItems(\Magento\Quote\Model\Quote $quote = null)
    {
        $cartContents = [];
        $quoteItems = [];

        if ($quote instanceof \Magento\Quote\Model\Quote) {
            $quoteItems = $quote->getAllVisibleItems();
        } else {
            $quoteItems = $this->cart->getQuote()->getAllVisibleItems();
        }

        if (is_array($quoteItems) && sizeof($quoteItems)) {
            foreach ($quoteItems as $item) {
                $cartContents[] = [
                    'item' => $item->getSku(),
                    'price' => $this->checkoutHelper->convertPrice($this->checkoutHelper->getPriceInclTax($item), false),
                    'quantity' => $item->getQty()
                ];
            }

            return $cartContents;
        }

        return (bool) sizeof($cartContents);
    }

    /**
     * @param \Magento\Quote\Model\Quote $quote
     * @return void
     */
    public function setCartContents(\Magento\Quote\Model\Quote $quote = null)
    {
        try {
            $cartContents = $this->getQuoteItems($quote);

            if ($cartContents) {
                $this->setCookie(self::EMARSYS_CART_CONTENT_COOKIE, $cartContents);
            }
        } catch (\Exception $e) { }
    }

    /**
     * @return void
     */
    public function unsetCartContents()
    {
        try {
            $this->deleteCookie(self::EMARSYS_CART_CONTENT_COOKIE);
        } catch (\Exception $e) { }
    }

    /**
     * @param string $email
     * @return void
     */
    public function setCustomerInfo(string $email)
    {
        try {
            $this->setCookie(self::EMARSYS_USER_INFO, $email);
        } catch (\Exception $e) { }
    }

    /**
     * @return void
     */
    public function unsetCustomerInfo()
    {
        try {
            $this->deleteCookie(self::EMARSYS_USER_INFO);
        } catch (\Exception $e) { }
    }

    /**
     * @return Boolean
     */
    public function isWebExtendEnabled() {
        return (bool) $this->scopeConfig->isSetFlag('totaltools_emarsys/web_extend/enabled', \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }
}
