<?php

namespace Totaltools\Ec\Helper;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class Path extends \Anowave\Package\Helper\Package
{
    /**
     * @var \Magento\Framework\App\Helper\Context
     */
    protected $context;

    /**
     * @var \Magento\Catalog\Helper\Data
     */
    protected $catalogHelper;

    /**
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Catalog\Helper\Data $catalogHelper
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Catalog\Helper\Data $catalogHelper
    ) {
        parent::__construct($context);

        $this->context = $context;
        $this->catalogHelper = $catalogHelper;
    }

    /**
     * @return string|bool
     */
    public function getPath()
    {
        switch ($this->getPageType()) {
            case 'cms_index_index':
                return \Totaltools\Ec\Helper\Constants::HOME_CATEGORY;
                break;
            case 'checkout_index_index':
                return \Totaltools\Ec\Helper\Constants::CHECKOUT_CATEGORY;
                break;
            case 'catalogsearch_result_index';
                return \Totaltools\Ec\Helper\Constants::SEARCH_CATEGORY;
                break;
            default:
                return $this->getExtractedPath();
                break;
        }
    }

    /**
     * @return string
     */
    protected function getExtractedPath()
    {
        $breadcrumbs = $this->catalogHelper->getBreadcrumbPath();

        if (sizeof($breadcrumbs)) {
            return $this->extractPath($breadcrumbs);
        }

        return false;
    }

    /**
     * @param array $breadcrumbs
     * @return string
     */
    protected function extractPath($breadcrumbs)
    {
        $result = [];

        if (is_array($breadcrumbs) && sizeof($breadcrumbs)) {
            foreach ($breadcrumbs as $key => $crumb) {
                $result[] = trim($crumb['label']);
            }
        } else {
            return false;
        }

        if ($this->getPageType() === 'catalog_product_view') {
            $size = sizeof($result);

            if ($size == 1) {
                return \Totaltools\Ec\Helper\Constants::PRODUCT_CATEGORY;
            } elseif ($size > 1) {
                array_pop($result);
            }
        }

        return implode(\Totaltools\Ec\Helper\Constants::CATEGORY_SEPERATOR, $result);
    }

    /**
     * @return string
     */
    protected function getPageType()
    {
        return (string) $this->context->getRequest()->getFullActionName();
    }
}
