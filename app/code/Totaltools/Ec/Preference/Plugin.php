<?php

namespace Totaltools\Ec\Preference;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class Plugin extends \Anowave\Ec\Block\Plugin
{
    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $categoryFactory;

    /**
     * @var \Totaltools\Ec\Helper\Path
     */
    protected $categoryPath;

    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $request;

    /**
     * @var \Anowave\Ec\Model\Cache
     */
    protected $cache = null;

    /**
     * @var \Anowave\Ec\Helper\Datalayer
     */
    protected $dataLayer = null;

    /**
     * @var \Magento\Checkout\Model\Cart|null
     */
    protected $cart = null;

    /**
     * Plugin constructor.
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $coreConfig
     * @param \Magento\Framework\Registry $registry
     * @param \Anowave\Ec\Helper\Data $helper
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     * @param \Magento\Catalog\Model\CategoryRepository $categoryRepository
     * @param \Anowave\Ec\Model\Apply $apply
     * @param \Anowave\Ec\Model\Cache $cache
     * @param \Anowave\Ec\Helper\Datalayer $dataLayer
     * @param \Anowave\Ec\Helper\Attributes $attributes
     * @param \Anowave\Ec\Helper\Bridge $bridge
     * @param \Magento\CatalogInventory\Model\Stock\StockItemRepository $stockItemRepository
     * @param \Anowave\Ec\Model\SwatchAttributeType $swatchTypeChecker
     * @param \Magento\Catalog\Model\CategoryFactory $categoryFactory
     * @param \Totaltools\Ec\Helper\Path $categoryPath
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magento\Checkout\Model\Cart $cart
     */
    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $coreConfig,
        \Magento\Framework\Registry $registry,
        \Anowave\Ec\Helper\Data $helper,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        \Magento\Catalog\Model\CategoryRepository $categoryRepository,
        \Anowave\Ec\Model\Apply $apply,
        \Anowave\Ec\Model\Cache $cache,
        \Anowave\Ec\Helper\Datalayer $dataLayer,
        \Anowave\Ec\Helper\Attributes $attributes,
        \Anowave\Ec\Helper\Bridge $bridge,
        \Magento\CatalogInventory\Model\Stock\StockItemRepository $stockItemRepository,
        \Anowave\Ec\Model\SwatchAttributeType $swatchTypeChecker,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Totaltools\Ec\Helper\Path $categoryPath,
        \Magento\Framework\App\RequestInterface $request,
        \Magento\Checkout\Model\Cart $cart
    ) {
        parent::__construct(
            $coreConfig,
            $registry,
            $helper,
            $productRepository,
            $categoryRepository,
            $apply,
            $cache,
            $dataLayer,
            $attributes,
            $bridge,
            $stockItemRepository,
            $swatchTypeChecker
        );

        /**
         * @var \Anowave\Ec\Model\Cache
         */
        $this->cache = $cache;

        /**
         * Set dataLayer
         *
         * @var \Anowave\Ec\Helper\Datalayer
         */
        $this->dataLayer = $dataLayer;

        /**
         * @var \Magento\Catalog\Model\CategoryFactory
         */
        $this->categoryFactory = $categoryFactory;

        /**
         * @var \Totaltools\Ec\Block\CategoryPath
         */
        $this->categoryPath = $categoryPath;

        /**
         * @var \Magento\Framework\App\RequestInterface $request
         */
        $this->request = $request;

        /**
         * @var \Magento\Checkout\Model\Cart $cart
         */
        $this->cart = $cart;
    }

    /**
     * Block output modifier
     *
     * @param \Magento\Framework\View\Element\Template $block
     * @param string $html
     *
     * @return string
     */
    public function afterToHtml($block, $content)
    {
        $content = parent::afterToHtml($block, $content);

        if ($this->_helper->isActive()) {
            switch ($block->getNameInLayout()) {
                case 'popular.products.list':
                    return $this->augmentListPopularBlock($block, $content);
                default:
                    break;
            }
        }

        return $content;
    }

    /**
     * Modify categories listing output
     *
     * @param AbstractBlock $block
     * @param string $content
     */
    public function augmentListBlock($block, $content)
    {

        /**
         * Load cache
         *
         * @var string
         */
        $cache = $this->cache->load(\Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        if ($cache) {
            return $cache;
        }

        /**
         * Retrieve list of impression product(s)
         *
         * @var array
         */
        $products = [];

        foreach ($block->getLoadedProductCollection() as $product) {
            $products[] = $product;
        }

        if ($this->_helper->usePlaceholders()) {
            $placeholders = $this->applyPlaceholders($content);
        }

        /**
         * Append tracking
         */
        $position = 1;
        list($doc, $dom) = $this->getDom();
		@$dom->loadHTML($content);
        $query = new \DOMXPath($dom);
        foreach ($query->query( (string) $this->_helper->getListSelector()) as $key => $element) {
            if (isset($products[$key])) {
                /**
                 * Get current category
                 *
                 * @var object
                 */
                $category = $this->_coreRegistry->registry('current_category');

                /**
                 * Add data-* attributes used for tracking dynamic values
                 */
                foreach ($query->query( (string) $this->_helper->getListClickSelector(), $element) as $a) {
                    $click = $a->getAttribute('onclick');

                    $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                    $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                    $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                    $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                    $a->setAttribute('data-list',        $this->_helper->escapeDataArgument($this->_helper->getCategoryList($category)));
                    $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                    $a->setAttribute('data-quantity',     1);
                    $a->setAttribute('data-click', $click);

                    /**
                     * Set stock status attribute
                     */
                    $a->setAttribute("data-{$this->_helper->getStockDimensionIndex(true)}", $this->_helper->getStock($products[$key]));

                    $a->setAttribute('data-store',        $this->_helper->getStoreName());
                    $a->setAttribute('data-position',    $position);
                    $a->setAttribute('data-event',        'productClick');
                    $a->setAttribute('onclick', $click);

                    /**
                     * Create transport object
                     *
                     * @var \Magento\Framework\DataObject $transport
                     */
                    $transport = new \Magento\Framework\DataObject(
                            [
                                'attributes' => $this->attributes->getAttributes()
                            ]
                        );

                    /**
                     * Notify others
                     */
                    $this->_helper->getEventManager()->dispatch('ec_get_click_attributes', ['transport' => $transport]);

                    /**
                     * Get response
                     */
                    $attributes = $transport->getAttributes();

                    $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                }

                /**
                 * Apply direct "Add to cart" tracking for listings
                 */
                if ('' !== $selector = $this->_helper->getCartCategorySelector()) {
                    /**
                     * Skip tracking for configurable and grouped products from listings
                     */
                    if (!in_array(
                        $products[$key]->getTypeId(),
                        [
                            \Magento\GroupedProduct\Model\Product\Type\Grouped::TYPE_CODE
                        ]
                    )) {
                        foreach (@$query->query($selector, $element) as $a) {
                            $click = $a->getAttribute('onclick');

                            $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                            $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                            $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                            $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                            $a->setAttribute('data-list',        $this->_helper->escapeDataArgument($this->_helper->getCategoryList($category)));
                            $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                            $a->setAttribute('data-quantity',     1);
                            $a->setAttribute('data-click',        $click);
                            $a->setAttribute('data-position',     $position);
                            $a->setAttribute('data-store',        $this->_helper->getStoreName());

                            if (\Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE === $products[$key]->getTypeId()) {
                                try {
                                    /**
                                     * Presume swatch only by default
                                     *
                                     * @var bool $swatch
                                     */
                                    $swatch = true;

                                    $swatch_attributes = [];

                                    $configurableAttributes = $products[$key]->getTypeInstance()->getConfigurableAttributes($products[$key]);

                                    foreach ($configurableAttributes as $attribute) {
                                        if (!$this->swatchTypeChecker->isSwatchAttribute($attribute->getProductAttribute())) {
                                            $swatch = false;
                                        } else {
                                            $swatch_attributes[] =
                                                [
                                                    'attribute_id'         => $attribute->getProductAttribute()->getAttributeId(),
                                                    'attribute_code'     => $attribute->getProductAttribute()->getAttributeCode(),
                                                    'attribute_label'     => $this->_helper->useDefaultValues() ? $attribute->getProductAttribute()->getFrontendLabel() : $attribute->getProductAttribute()->getStoreLabel()
                                                ];
                                        }
                                    }

                                    if ($swatch) {
                                        $a->setAttribute('data-event', 'addToCartSwatch');

                                        /**
                                         * Product has swatch attributes only
                                         */
                                        $a->setAttribute('data-swatch', $this->_helper->getJsonHelper()->encode($swatch_attributes));
                                    }
                                } catch (\Exception $e) {
                                }
                            } else {
                                $a->setAttribute('data-event', 'addToCart');
                            }

                            $a->setAttribute('onclick', $click);

                            /**
                             * Create transport object
                             *
                             * @var \Magento\Framework\DataObject $transport
                             */
                            $transport = new \Magento\Framework\DataObject(
                                    [
                                        'attributes' => $this->attributes->getAttributes()
                                    ]
                                );

                            /**
                             * Notify others
                             */
                            $this->_helper->getEventManager()->dispatch('ec_get_add_list_attributes', ['transport' => $transport]);

                            /**
                             * Get response
                             */
                            $attributes = $transport->getAttributes();

                            $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                        }
                    }
                }
            }

            $position++;
        }

        $content = $this->getDOMContent($dom, $doc);

        if ($this->_helper->usePlaceholders()) {
            if (isset($placeholders)) {
                $content = $this->restorePlaceholders($content, $placeholders);
            }
        }

        /**
         * Save cache
         */
        $this->cache->save($content, \Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        return $content;
    }

    /**
     * Modify categories listing output
     *
     * @param AbstractBlock $block
     * @param string $content
     */
    public function augmentListUpsellBlock($block, $content)
    {
        $content = trim($content);

        if (!strlen($content)) {
            return $content;
        }

        /**
         * Load cache
         *
         * @var string
         */
        $cache = $this->cache->load(\Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        if ($cache) {
            return $cache;
        }

        /**
         * Retrieve list of impression product(s)
         *
         * @var array
         */
        $products = [];

        if ($block->getItems()) {
            foreach ($block->getItems() as $product) {
                $products[] = $product;
            }
        }

        /**
         * Append tracking
         */
        list($doc, $dom) = $this->getDom();
		@$dom->loadHTML($content);
        $query = new \DOMXPath($dom);

        $position = 1;

        foreach ($query->query($this->_helper->getListSelector()) as $key => $element) {
            if (isset($products[$key])) {
                /**
                 * Get all product categories
                 */
                $categories = $this->_helper->getCurrentStoreProductCategories($products[$key]);

                if (!$categories) {
                    if (null !== $root = $this->_helper->getStoreRootDefaultCategoryId()) {
                        $categories[] = $root;
                    }
                }

                if ($categories) {
                    /**
                     * Load last category
                     */
                    $category = $this->categoryRepository->get(
                            end($categories)
                        );
                } else {
                    $category = null;
                }

                /**
                 * Add data-* attributes used for tracking dynamic values
                 */
                foreach ($query->query($this->_helper->getListClickSelector(), $element) as $a) {
                    $click = $a->getAttribute('onclick');

                    $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                    $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                    $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                    $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                    $a->setAttribute('data-list',        $this->_helper->escapeDataArgument($this->_helper->getCategoryList($category)));
                    $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                    $a->setAttribute('data-quantity',     1);
                    $a->setAttribute('data-click',        $click);
                    $a->setAttribute('data-store',        $this->_helper->getStoreName());
                    $a->setAttribute('data-position',    $position);
                    $a->setAttribute('data-event',        'productClick');
                    $a->setAttribute('data-block',        $block->getNameInLayout());
                    $a->setAttribute('onclick', $click);

                    $productCategoryPath = false;

                    if ($products[$key] instanceof \Magento\Quote\Api\Data\CartItemInterface) {
                        $productCategoryPath = $products[$key]->getCategoryPath();
                    }

                    if ($category) {
                        $element->setAttribute('data-category', $productCategoryPath ?: $this->_helper->getCategoryDetailList($products[$key], $category));
                    }

                    /**
                     * Create transport object
                     *
                     * @var \Magento\Framework\DataObject $transport
                     */
                    $transport = new \Magento\Framework\DataObject(
                            [
                                'attributes' => $this->attributes->getAttributes()
                            ]
                        );

                    /**
                     * Notify others
                     */
                    $this->_helper->getEventManager()->dispatch('ec_get_click_list_attributes', ['transport' => $transport]);

                    /**
                     * Get response
                     */
                    $attributes = $transport->getAttributes();

                    $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                }

                /**
                 * Track "Add to cart" from Related products
                 */
                if ('' !== $selector = $this->_helper->getCartCategorySelector()) {
                    foreach (@$query->query($selector, $element) as $a) {
                        $click = $a->getAttribute('onclick');

                        $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                        $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                        $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                        $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                        $a->setAttribute('data-list',        $this->_helper->escapeDataArgument($this->_helper->getCategoryList($category)));
                        $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                        $a->setAttribute('data-quantity',     1);
                        $a->setAttribute('data-click',        $click);
                        $a->setAttribute('data-position',     $position);
                        $a->setAttribute('data-store',        $this->_helper->getStoreName());
                        $a->setAttribute('data-event',        'addToCart');
                        $a->setAttribute('data-block',        $block->getNameInLayout());
                        $a->setAttribute('onclick', $click);

                        /**
                         * Create transport object
                         *
                         * @var \Magento\Framework\DataObject $transport
                         */
                        $transport = new \Magento\Framework\DataObject(
                                [
                                    'attributes' => $this->attributes->getAttributes()
                                ]
                            );

                        /**
                         * Notify others
                         */
                        $this->_helper->getEventManager()->dispatch('ec_get_add_list_attributes', ['transport' => $transport]);

                        /**
                         * Get response
                         */
                        $attributes = $transport->getAttributes();

                        $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                    }
                }
            }

            $position++;
        }

        $content = $this->getDOMContent($dom, $doc);

        /**
         * Save cache
         */
        $this->cache->save($content, \Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        return $content;
    }

    /**
     * Modify add to cart output
     *
     * @param AbstractBlock $block
     * @param string $content
     *
     * @return string
     */
    public function augmentAddCartBlock($block, $content)
    {
        list($doc, $dom) = $this->getDom();
		@$dom->loadHTML($content);
        $query = new \DOMXPath($dom);

        foreach ($query->query($this->_helper->getCartSelector()) as $element) {
            $category = $this->_coreRegistry->registry('current_category');

            if (!$category) {
                /**
                 * Get all product categories
                 */
                $categories = $this->_helper->getCurrentStoreProductCategories($block->getProduct());

                /**
                 * Cases when product does not exist in any category
                 */
                if (!$categories) {
                    $categories[] = $this->_helper->getStoreRootDefaultCategoryId();
                }

                /**
                 * Load last category
                 */
                $category = $this->categoryRepository->get(
                        end($categories)
                    );
            }

            /**
             * Get existing onclick attribute
             *
             * @var string
             */
            $click = $element->getAttribute('onclick');

            $element->setAttribute('onclick', $click);
            $element->setAttribute('data-id',             $this->_helper->escapeDataArgument($block->getProduct()->getSku()));
            $element->setAttribute('data-simple-id',     $this->_helper->escapeDataArgument($block->getProduct()->getSku()));
            $element->setAttribute('data-name',         $this->_helper->escapeDataArgument($block->getProduct()->getName()));
            $element->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($block->getProduct())));
            $element->setAttribute('data-category',     $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
            $element->setAttribute('data-list',         $this->_helper->getCategoryDetailList($block->getProduct(), $category));
            $element->setAttribute('data-brand',         $this->_helper->getBrand($block->getProduct()));
            $element->setAttribute('data-click',         $click);
            $element->setAttribute('data-use-simple',     $this->_helper->useSimples() ? 1 : 0);

            try {
                /**
                 * Get current stock level
                 *
                 * @var int $max
                 */
                $max = (int) $this->stockItemRepository->get($block->getProduct()->getId())->getQty();

                $element->setAttribute('data-quantity-max', $max);
            } catch (\Exception $e) {
            }

            /**
             * Set data event
             */
            $element->setAttribute('data-event', 'addToCart');

            if ('grouped' == $block->getProduct()->getTypeId()) {
                $element->setAttribute('data-grouped', 1);
            }

            if ('configurable' == $block->getProduct()->getTypeId()) {
                $element->setAttribute('data-configurable', 1);
            }

            /**
             * Set current store
             */
            $element->setAttribute('data-currentstore', $this->_helper->getStoreName());

            /**
             * Create transport object
             *
             * @var \Magento\Framework\DataObject $transport
             */
            $transport = new \Magento\Framework\DataObject(
                    [
                        'attributes' => $this->attributes->getAttributes(),
                        'product'    => $block->getProduct()
                    ]
                );

            /**
             * Notify others
             */
            $this->_helper->getEventManager()->dispatch('ec_get_add_attributes', ['transport' => $transport]);

            /**
             * Get response
             */
            $attributes = $transport->getAttributes();

            $element->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
        }

        return $this->getDOMContent($dom, $doc);
    }

    /**
     * Modify popular block output
     *
     * @param AbstractBlock $block
     * @param string $content
     */
    protected function augmentListPopularBlock($block, $content)
    {
        /**
         * Load cache
         *
         * @var string
         */
        $cache = $this->cache->load(\Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        if ($cache) {
            return $cache;
        }

        /**
         * Retrieve list of impression product(s)
         *
         * @var array
         */
        $products = [];

        foreach ($block->getLoadedProductCollection() as $product) {
            $products[] = $product;
        }

        /**
         * Append tracking
         */
        list($doc, $dom) = $this->getDom();
		@$dom->loadHTML($content);
        $query = new \DOMXPath($dom);

        $position = 1;

        $categoryFactory = $this->categoryFactory->create();
        $category = $categoryFactory->loadByAttribute('url_key', $block->getCategoryId());

        foreach ($query->query($this->_helper->getListSelector()) as $key => $element) {
            if (isset($products[$key])) {

                /**
                 * Add data-* attributes used for tracking dynamic values
                 */
                foreach ($query->query($this->_helper->getListClickSelector(), $element) as $a) {
                    $click = $a->getAttribute('onclick');

                    $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                    $a->setAttribute('data-name',       $this->_helper->escapeDataArgument($products[$key]->getName()));
                    $a->setAttribute('data-price',      $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                    $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                    $a->setAttribute('data-list',       $this->_helper->escapeDataArgument($this->_helper->getCategoryList($category)));
                    $a->setAttribute('data-brand',      $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                    $a->setAttribute('data-quantity',   1);
                    $a->setAttribute('data-click', $click);

                    $a->setAttribute('data-store',        $this->_helper->getStoreName());
                    $a->setAttribute('data-position',    $position);
                    $a->setAttribute('data-event',        'productClick');
                    $a->setAttribute('onclick', $click);

                    /**
                     * Create transport object
                     *
                     * @var \Magento\Framework\DataObject $transport
                     */
                    $transport = new \Magento\Framework\DataObject(
                        [
                            'attributes' => $this->attributes->getAttributes()
                        ]
                    );

                    /**
                     * Notify others
                     */
                    $this->_helper->getEventManager()->dispatch('ec_get_click_attributes', ['transport' => $transport]);

                    /**
                     * Get response
                     */
                    $attributes = $transport->getAttributes();

                    $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                }

                /**
                 * Apply direct "Add to cart" tracking for listings
                 */
                if ('' !== $selector = $this->_helper->getCartCategorySelector()) {
                    /**
                     * Skip tracking for configurable and grouped products from listings
                     */
                    if (!in_array(
                        $products[$key]->getTypeId(),
                        [
                            \Magento\GroupedProduct\Model\Product\Type\Grouped::TYPE_CODE
                        ]
                    )) {
                        foreach (@$query->query($selector, $element) as $a) {
                            $click = $a->getAttribute('onclick');

                            $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                            $a->setAttribute('data-name',       $this->_helper->escapeDataArgument($products[$key]->getName()));
                            $a->setAttribute('data-price',      $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                            $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                            $a->setAttribute('data-list',       $this->_helper->escapeDataArgument($this->_helper->getCategoryList($category)));
                            $a->setAttribute('data-brand',      $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                            $a->setAttribute('data-quantity',   1);
                            $a->setAttribute('data-click',      $click);
                            $a->setAttribute('data-position',   $position);
                            $a->setAttribute('data-store',      $this->_helper->getStoreName());

                            if (\Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE === $products[$key]->getTypeId()) {
                                try {
                                    /**
                                     * Presume swatch only by default
                                     *
                                     * @var bool $swatch
                                     */
                                    $swatch = true;

                                    $swatch_attributes = [];

                                    $configurableAttributes = $products[$key]->getTypeInstance()->getConfigurableAttributes($products[$key]);

                                    foreach ($configurableAttributes as $attribute) {
                                        if (!$this->swatchTypeChecker->isSwatchAttribute($attribute->getProductAttribute())) {
                                            $swatch = false;
                                        } else {
                                            $swatch_attributes[] =
                                                [
                                                    'attribute_id'         => $attribute->getProductAttribute()->getAttributeId(),
                                                    'attribute_code'     => $attribute->getProductAttribute()->getAttributeCode(),
                                                    'attribute_label'     => $this->_helper->useDefaultValues() ? $attribute->getProductAttribute()->getFrontendLabel() : $attribute->getProductAttribute()->getStoreLabel()
                                                ];
                                        }
                                    }

                                    if ($swatch) {
                                        $a->setAttribute('data-event', 'addToCartSwatch');

                                        /**
                                         * Product has swatch attributes only
                                         */
                                        $a->setAttribute('data-swatch', $this->_helper->getJsonHelper()->encode($swatch_attributes));
                                    }
                                } catch (\Exception $e) {
                                }
                            } else {
                                $a->setAttribute('data-event', 'addToCart');
                            }

                            $a->setAttribute('onclick', $click);

                            /**
                             * Create transport object
                             *
                             * @var \Magento\Framework\DataObject $transport
                             */
                            $transport = new \Magento\Framework\DataObject(
                                [
                                    'attributes' => $this->attributes->getAttributes()
                                ]
                            );

                            /**
                             * Notify others
                             */
                            $this->_helper->getEventManager()->dispatch('ec_get_add_list_attributes', ['transport' => $transport]);

                            /**
                             * Get response
                             */
                            $attributes = $transport->getAttributes();

                            $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                        }
                    }
                }
            }

            $position++;
        }

        $content = $this->getDOMContent($dom, $doc);

        /**
         * Save cache
         */
        $this->cache->save($content, \Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        return $content;
    }

    /**
     * Augment cross sells
     *
     * @param unknown $block
     * @param unknown $content
     * @return string|unknown
     */
    public function augmentListCrossSellBlock($block, $content)
    {
        /**
         * Remove empty spaces
         */
        $content = trim($content);

        if (!strlen($content)) {
            return $content;
        }

        /**
         * Load cache
         *
         * @var string
         */
        $cache = $this->cache->load(\Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        if ($cache) {
            return $cache;
        }

        /**
         * Retrieve list of impression product(s)
         *
         * @var array
         */
        $products = [];

        if (!empty($block->getItems())) {
            foreach ($block->getItems() as $product) {
                $products[] = $product;
            }
        }


        /**
         * Append tracking
         */
        list($doc, $dom) = $this->getDom();
		@$dom->loadHTML($content);
        $query = new \DOMXPath($dom);

        $position = 1;

        foreach ($query->query($this->_helper->getListCrossSellSelector()) as $key => $element) {
            if (isset($products[$key])) {
                /**
                 * Get all product categories
                 */
                $categories = $this->_helper->getCurrentStoreProductCategories($products[$key]);

                if (!$categories) {
                    if (null !== $root = $this->_helper->getStoreRootDefaultCategoryId()) {
                        $categories[] = $root;
                    }
                }

                if ($categories) {
                    /**
                     * Load last category
                     */

                    $category = $this->categoryRepository->get(
                            end($categories)
                        );
                } else {
                    $category = null;
                }

                /**
                 * Add data-* attributes used for tracking dynamic values
                 */
                foreach ($query->query($this->_helper->getListClickSelector(), $element) as $a) {
                    $click = $a->getAttribute('onclick');

                    $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                    $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                    $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                    $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                    $a->setAttribute('data-list',        \Anowave\Ec\Helper\Constants::LIST_CROSS_SELL);
                    $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                    $a->setAttribute('data-quantity',     1);
                    $a->setAttribute('data-click',        $click);
                    $a->setAttribute('data-store',        $this->_helper->getStoreName());
                    $a->setAttribute('data-position',    $position);
                    $a->setAttribute('data-event',        'productClick');
                    $a->setAttribute('data-block',        $block->getNameInLayout());
                    $a->setAttribute('onclick', $click);

                    $productCategoryPath = false;

                    if ($products[$key] instanceof \Magento\Quote\Api\Data\CartItemInterface) {
                        $productCategoryPath = $products[$key]->getCategoryPath();
                    }

                    if ($category) {
                        $element->setAttribute('data-category', $productCategoryPath ?: $this->_helper->getCategoryDetailList($products[$key], $category));
                    }

                    /**
                     * Create transport object
                     *
                     * @var \Magento\Framework\DataObject $transport
                     */
                    $transport = new \Magento\Framework\DataObject(
                            [
                                'attributes' => $this->attributes->getAttributes()
                            ]
                        );

                    /**
                     * Notify others
                     */
                    $this->_helper->getEventManager()->dispatch('ec_get_click_list_attributes', ['transport' => $transport]);

                    /**
                     * Get response
                     */
                    $attributes = $transport->getAttributes();

                    $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                }

                /**
                 * Track "Add to cart" from Related products
                 */
                if ('' !== $selector = $this->_helper->getCartCategorySelector()) {
                    foreach (@$query->query($selector, $element) as $a) {
                        $click = $a->getAttribute('onclick');

                        $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                        $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                        $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                        $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                        $a->setAttribute('data-list',        $this->_helper->escapeDataArgument($this->_helper->getCategoryList($category)));
                        $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                        $a->setAttribute('data-quantity',     1);
                        $a->setAttribute('data-click',        $click);
                        $a->setAttribute('data-position',     $position);
                        $a->setAttribute('data-store',        $this->_helper->getStoreName());
                        $a->setAttribute('data-event',        'addToCart');
                        $a->setAttribute('data-block',        $block->getNameInLayout());
                        $a->setAttribute('onclick', $click);

                        /**
                         * Create transport object
                         *
                         * @var \Magento\Framework\DataObject $transport
                         */
                        $transport = new \Magento\Framework\DataObject(
                                [
                                    'attributes' => $this->attributes->getAttributes()
                                ]
                            );

                        /**
                         * Notify others
                         */
                        $this->_helper->getEventManager()->dispatch('ec_get_add_list_attributes', ['transport' => $transport]);

                        /**
                         * Get response
                         */
                        $attributes = $transport->getAttributes();

                        $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                    }
                }
            }

            $position++;
        }

        $content = $this->getDOMContent($dom, $doc);

        /**
         * Save cache
         */
        $this->cache->save($content, \Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        return $content;
    }

    /**
     * Modify categories listing output
     *
     * @param AbstractBlock $block
     * @param string $content
     */
    public function augmentListRelatedBlock($block, $content)
    {
        /**
         * Remove empty spaces
         */
        $content = trim($content);

        if (!strlen($content)) {
            return $content;
        }

        /**
         * Load cache
         *
         * @var string
         */
        $cache = $this->cache->load(\Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        if ($cache) {
            return $cache;
        }

        /**
         * Retrieve list of impression product(s)
         *
         * @var array
         */
        $products = [];

        if (null != $items = $this->bridge->getLoadedItems($block)) {
            foreach ($items as $product) {
                $products[] = $product;
            }
        }

        /**
         * Append tracking
         */
        list($doc, $dom) = $this->getDom();
		@$dom->loadHTML($content);
        $query = new \DOMXPath($dom);

        $position = 1;

        foreach ($query->query($this->_helper->getListSelector()) as $key => $element) {
            if (isset($products[$key])) {
                /**
                 * Get all product categories
                 */
                $categories = $this->_helper->getCurrentStoreProductCategories($products[$key]);

                if (!$categories) {
                    if (null !== $root = $this->_helper->getStoreRootDefaultCategoryId()) {
                        $categories[] = $root;
                    }
                }

                if ($categories) {
                    /**
                     * Load last category
                     */

                    $category = $this->categoryRepository->get(
                            end($categories)
                        );
                } else {
                    $category = null;
                }

                /**
                 * Add data-* attributes used for tracking dynamic values
                 */
                foreach ($query->query($this->_helper->getListClickSelector(), $element) as $a) {
                    $click = $a->getAttribute('onclick');

                    $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                    $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                    $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                    $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                    $a->setAttribute('data-list',        \Anowave\Ec\Helper\Constants::LIST_RELATED);
                    $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                    $a->setAttribute('data-quantity',     1);
                    $a->setAttribute('data-click',        $click);
                    $a->setAttribute('data-store',        $this->_helper->getStoreName());
                    $a->setAttribute('data-position',    $position);
                    $a->setAttribute('data-event',        'productClick');
                    $a->setAttribute('data-block',        $block->getNameInLayout());
                    $a->setAttribute('onclick', $click);

                    // custom code to add category path
                    $productCategoryPath = false;

                    if ($products[$key] instanceof \Magento\Quote\Api\Data\CartItemInterface) {
                        $productCategoryPath = $products[$key]->getCategoryPath();
                    }

                    if ($category) {
                        $element->setAttribute('data-category', $productCategoryPath ?: $this->_helper->getCategoryDetailList($products[$key], $category));
                    }

                    /**
                     * Create transport object
                     *
                     * @var \Magento\Framework\DataObject $transport
                     */
                    $transport = new \Magento\Framework\DataObject(
                            [
                                'attributes' => $this->attributes->getAttributes()
                            ]
                        );

                    /**
                     * Notify others
                     */
                    $this->_helper->getEventManager()->dispatch('ec_get_click_list_attributes', ['transport' => $transport]);

                    /**
                     * Get response
                     */
                    $attributes = $transport->getAttributes();

                    $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                }

                /**
                 * Track "Add to cart" from Related products
                 */
                if ('' !== $selector = $this->_helper->getCartCategorySelector()) {
                    foreach (@$query->query($selector, $element) as $a) {
                        $click = $a->getAttribute('onclick');

                        $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                        $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                        $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                        $a->setAttribute('data-category',   $this->_helper->escapeDataArgument($this->categoryPath->getPath()));
                        $a->setAttribute('data-list',        $this->_helper->escapeDataArgument($this->_helper->getCategoryList($category)));
                        $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                        $a->setAttribute('data-quantity',     1);
                        $a->setAttribute('data-click',        $click);
                        $a->setAttribute('data-position',     $position);
                        $a->setAttribute('data-store',        $this->_helper->getStoreName());
                        $a->setAttribute('data-event',        'addToCart');
                        $a->setAttribute('data-block',        $block->getNameInLayout());
                        $a->setAttribute('onclick', $click);

                        /**
                         * Create transport object
                         *
                         * @var \Magento\Framework\DataObject $transport
                         */
                        $transport = new \Magento\Framework\DataObject(
                                [
                                    'attributes' => $this->attributes->getAttributes()
                                ]
                            );

                        /**
                         * Notify others
                         */
                        $this->_helper->getEventManager()->dispatch('ec_get_add_list_attributes', ['transport' => $transport]);

                        /**
                         * Get response
                         */
                        $attributes = $transport->getAttributes();

                        $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                    }
                }
            }

            $position++;
        }

        $content = $this->getDOMContent($dom, $doc);

        /**
         * Save cache
         */
        $this->cache->save($content, \Anowave\Ec\Model\Cache::CACHE_LISTING . $block->getNameInLayout());

        return $content;
    }

    /**
     * Modify remove from cart output
     *
     * @param AbstractBlock $block
     * @param string $content
     *
     * @return string
     */

    public function augmentRemoveCartBlock($block, $content)
    {
        /**
         * Get quote item
         *
         * @var \Magento\Quote\Model\Quote\Item $item
         */
        $item = $this->cart->getQuote()->getItemById((int) $this->request->getParam('item_id'));
		/**
		 * Append tracking
		 */
	    list($doc, $dom) = $this->getDom();
		@$dom->loadHTML($content);
		/**
		 * Modify DOM
		 */
		$x = new \DOMXPath($dom);

        foreach ($x->query($this->_helper->getDeleteSelector()) as $element) {
            /**
             * Get all product categories
             */
            $categories = $this->_helper->getCurrentStoreProductCategories($block->getItem()->getProduct());

            if (!$categories) {
                if (null !== $root = $this->_helper->getStoreRootDefaultCategoryId()) {
                    $categories[] = $root;
                }
            }

            if (!$this->_helper->useSimples()) {
                $element->setAttribute('data-id', $this->_helper->escapeDataArgument($block->getItem()->getProduct()->getSku()));
            } else {
                $element->setAttribute('data-id', $this->_helper->escapeDataArgument($block->getItem()->getSku()));
            }

            $element->setAttribute('data-name',           $this->_helper->escapeDataArgument($block->getItem()->getProduct()->getName()));
            $element->setAttribute('data-price',           $this->_helper->escapeDataArgument($this->_helper->getPrice($block->getItem()->getProduct())));
            $element->setAttribute('data-brand',           $this->_helper->escapeDataArgument($this->_helper->getBrand($block->getItem()->getProduct())));
            $element->setAttribute('data-quantity', (int) $block->getItem()->getQty());
            $element->setAttribute('data-event',           'removeFromCart');

            if ($element->getAttribute('data-post') && $this->_helper->getUseRemoveConfirm()) {
                /**
                 * Get current data post
                 *
                 * @var string $post
                 */
                $post = $element->getAttribute('data-post');

                /**
                 * Remove standard data-post
                 */
                $element->removeAttribute('data-post');

                /**
                 * Create new data-post
                 */
                $element->setAttribute('data-post-action', $post);
            }

            if ($categories) {
                /**
                 * Load last category
                 */
                $category = $this->categoryRepository->get(
                        end($categories)
                    );

                // custom code to add category path
                $itemCategoryPath = false;

                if ($item instanceof \Magento\Quote\Api\Data\CartItemInterface) {
                    $itemCategoryPath = $item->getCategoryPath();
                }

                $element->setAttribute('data-category',     $itemCategoryPath ?: $this->_helper->getCategoryDetailList($block->getItem()->getProduct(), $category));
                $element->setAttribute('data-list',         $this->_helper->getCategoryDetailList($block->getItem()->getProduct(), $category));
            }

            /**
             * Create transport object
             *
             * @var \Magento\Framework\DataObject $transport
             */
            $transport = new \Magento\Framework\DataObject(
                    [
                        'attributes' => $this->attributes->getAttributes()
                    ]
                );

            /**
             * Notify others
             */
            $this->_helper->getEventManager()->dispatch('ec_get_remove_attributes', ['transport' => $transport]);

            /**
             * Get response
             */
            $attributes = $transport->getAttributes();

            $element->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
        }


        return $this->getDOMContent($dom, $doc);
    }

    /**
     * @inheritdoc
     */
    public function augmentCartBlock($block, $content)
    {
        return $content .= $block->getLayout()->createBlock(static::BLOCK)->setTemplate('cart.phtml')->setData(
                [
                    'checkout_push' => $this->_helper->getCheckoutPush($block, $this->_coreRegistry)
                ]
            )
            ->toHtml();
    }

    /**
     * Modify Widget List widget
     *
     * @param \Magento\Catalog\Block\Product\Widget\NewWidget $block
     * @param string $content
     * @return string
     */
    public function augmentWidgetListBlock(\Magento\CatalogWidget\Block\Product\ProductsList $block, $content)
    {
        /**
         * Load cache
         *
         * @var string
         */
        $cache = $this->cache->load(\Anowave\Ec\Model\Cache::CACHE_LISTING_PRODUCT_WIDGET . $block->getNameInLayout());

        if ($cache) {
            return $cache;
        }

        /**
         * Retrieve list of impression product(s)
         *
         * @var array
         */
        $products = [];

        $collection = $block->getProductCollection();

        if (!$collection) {
            return $content;
        }

        foreach ($collection as $product) {
            $products[] = $product;
        }

        list($doc, $dom) = $this->getDom();
		@$dom->loadHTML($content);
        $query = new \DOMXPath($dom);

        /**
         * Default starting position
         *
         * @var integer $position
         */
        $position = 1;

        /**
         * @var string $category
         */
        $category = (string) $block->getGtmCategory();

        /**
         * @var string $list
         */
        $list = (string) $block->getGtmList();

        /**
         * Impression push
         *
         * @var array $impressions
         */
        $impressions =
            [
                'event'     => $block->getGtmEvent() ?? 'widgetViewNonInteractive',
                'ecommerce' =>
                [
                    'currencyCode'     => $this->_helper->getCurrency(),
                    'actionField'    =>
                    [
                        'list' => $list
                    ],
                    'impressions'     => []
                ]
            ];

        foreach ($query->query($this->_helper->getListWidgetSelector()) as $key => $element) {
            if (isset($products[$key])) {
                foreach ($query->query($this->_helper->getListWidgetClickSelector(), $element) as $a) {
                    $click = $a->getAttribute('onclick');

                    $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                    $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                    $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                    $a->setAttribute('data-category',   $category);
                    $a->setAttribute('data-list',        $list);
                    $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                    $a->setAttribute('data-quantity',     1);
                    $a->setAttribute('data-count', (int) $block->getMaxHeight());
                    $a->setAttribute('data-click', $click);

                    $a->setAttribute('data-store',        $this->_helper->getStoreName());
                    $a->setAttribute('data-position',    $position);
                    $a->setAttribute('data-event',        'productClick');
                    $a->setAttribute('onclick', $click);
                    $a->setAttribute('data-widget',     $block->getCacheKey());

                    /**
                     * Create transport object
                     *
                     * @var \Magento\Framework\DataObject $transport
                     */
                    $transport = new \Magento\Framework\DataObject(
                            [
                                'attributes' => $this->attributes->getAttributes()
                            ]
                        );

                    /**
                     * Notify others
                     */
                    $this->_helper->getEventManager()->dispatch('ec_get_widget_click_attributes', ['transport' => $transport]);

                    /**
                     * Get response
                     */
                    $attributes = $transport->getAttributes();

                    $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));

                    $impressions['ecommerce']['impressions'][] =
                        [
                            'id'             => $products[$key]->getSku(),
                            'name'             => $products[$key]->getName(),
                            'price'         => $this->_helper->getPrice($products[$key]),
                            'category'         => $category,
                            'list'             => $list,
                            'brand'         => $this->_helper->getBrand($products[$key]),
                            'quantity'         => 1,
                            'position'         => $position,
                            'store'         => $this->_helper->getStoreName()
                        ];
                }

                /**
                 * Apply direct "Add to cart" tracking for listings
                 */
                if ('' !== $selector = $this->_helper->getListWidgetCartCategorySelector()) {
                    if (!in_array($products[$key]->getTypeId(), [\Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE, \Magento\GroupedProduct\Model\Product\Type\Grouped::TYPE_CODE])) {
                        foreach (@$query->query($selector, $element) as $a) {
                            $click = $a->getAttribute('onclick');

                            $a->setAttribute('data-id',         $this->_helper->escapeDataArgument($products[$key]->getSku()));
                            $a->setAttribute('data-name',         $this->_helper->escapeDataArgument($products[$key]->getName()));
                            $a->setAttribute('data-price',         $this->_helper->escapeDataArgument($this->_helper->getPrice($products[$key])));
                            $a->setAttribute('data-category',   $category);
                            $a->setAttribute('data-list',        $list);
                            $a->setAttribute('data-brand',        $this->_helper->escapeDataArgument($this->_helper->getBrand($products[$key])));
                            $a->setAttribute('data-quantity',     1);
                            $a->setAttribute('data-click',        $click);
                            $a->setAttribute('data-position',     $position);
                            $a->setAttribute('data-store',        $this->_helper->getStoreName());
                            $a->setAttribute('data-event',        'addToCart');
                            $a->setAttribute('onclick', $click);

                            /**
                             * Create transport object
                             *
                             * @var \Magento\Framework\DataObject $transport
                             */
                            $transport = new \Magento\Framework\DataObject(
                                    [
                                        'attributes' => $this->attributes->getAttributes()
                                    ]
                                );

                            /**
                             * Notify others
                             */
                            $this->_helper->getEventManager()->dispatch('ec_get_widget_add_list_attributes', ['transport' => $transport]);

                            /**
                             * Get response
                             */
                            $attributes = $transport->getAttributes();

                            $a->setAttribute('data-attributes', $this->_helper->getJsonHelper()->encode($attributes));
                        }
                    }
                }
            }

            $position++;
        }

        $content = $this->getDOMContent($dom, $doc);

        $content .= $block->getLayout()->createBlock('Anowave\Ec\Block\Track')->setTemplate('widgets.phtml')->setData(
                [
                    'impressions' => $this->_helper->getJsonHelper()->encode($impressions, JSON_PRETTY_PRINT)
                ]
            )->toHtml();

        /**
         * Save cache
         */
        $this->cache->save($content, \Anowave\Ec\Model\Cache::CACHE_LISTING_PRODUCT_WIDGET . $block->getNameInLayout());

        return $content;
    }
    /**
	 * Get DOM wrappers
	 *
	 * @return array
	 */
	public function getDom() : array
	{
	    return
	    [
	        new \Anowave\Ec\Model\Dom('1.0','utf-8'),
	        new \Anowave\Ec\Model\Dom('1.0','utf-8')
	    ];
	}
}