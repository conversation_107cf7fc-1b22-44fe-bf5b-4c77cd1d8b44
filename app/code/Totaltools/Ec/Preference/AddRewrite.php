<?php

namespace Totaltools\Ec\Preference;

use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;
use Magento\Framework\Escaper;
/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class AddRewrite extends \Totaltools\Bundle\Controller\Cart\AddRewrite
{

    /**
     * @var \Magento\Framework\Registry
     */
    private $coreRegistry;

    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    protected $jsonHelper;

    /**
     * @var \Magento\Checkout\Helper\Data
     */
    protected $checkoutHelper;

    /**
     * @var \Anowave\Ec\Helper\Data
     */
    protected $dataHelper;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Framework\Data\Form\FormKey\Validator $formKeyValidator
     * @param \Magento\Checkout\Model\Cart $cart
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
     * @param \Magento\Customer\CustomerData\Section\Identifier $sectionIdentifier,
     * @param \Magento\Customer\CustomerData\SectionPoolInterface $sectionPool
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Magento\Checkout\Helper\Data $checkoutHelper
     * @param \Anowave\Ec\Helper\Data $dataHelper
     * @param Escaper $escaper
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Data\Form\FormKey\Validator $formKeyValidator,
        \Magento\Checkout\Model\Cart $cart,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        \Magento\Customer\CustomerData\Section\Identifier $sectionIdentifier,
        \Magento\Customer\CustomerData\SectionPoolInterface $sectionPool,
        \Magento\Framework\Registry $coreRegistry,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\Checkout\Helper\Data $checkoutHelper,
        \Anowave\Ec\Helper\Data $dataHelper,
        \Magento\GiftCardAccount\Helper\Data $giftCardHelper,
        ModelGiftcardaccount $giftCardAccount,
        Escaper $escaper
    ) {
        parent::__construct(
            $context,
            $scopeConfig,
            $checkoutSession,
            $storeManager,
            $formKeyValidator,
            $cart,
            $productRepository,
            $sectionIdentifier,
            $sectionPool,
            $giftCardHelper,
            $giftCardAccount,
            $escaper
        );

        $this->coreRegistry = $coreRegistry;
        $this->jsonHelper = $jsonHelper;
        $this->checkoutHelper = $checkoutHelper;
        $this->dataHelper = $dataHelper;
    }

    /**
     * @inheritdoc
     */
    protected function goBack($backUrl = null, $product = null)
    {
        if (!$this->getRequest()->isAjax()) {
            return parent::_goBack($backUrl);
        }

        $result = [];

        if ($backUrl || $backUrl = $this->getBackUrl()) {
            $result['backUrl'] = $backUrl;
        } else {
            if ($product && !$product->getIsSalable()) {
                $result['product'] = [
                    'statusText' => __('Out of stock')
                ];
            }
        }

        /*TOT0001-551: cart cart info into response*/
        $sectionNames = 'cart';
        $sectionNames = $sectionNames ? array_unique(\explode(',', $sectionNames)) : null;
        $cartUpdate = $this->sectionPool->getSectionsData($sectionNames, true);

        if (isset($cartUpdate['cart'])) {
            $result['cart'] = $cartUpdate;
        }
        /*TOT0001-551: cart cart info into response*/

        /**
         * @var \Magento\Quote\Model\Quote\Item[] $promoItems
         */
        $promoItems = $this->coreRegistry->registry(\Totaltools\Ec\Plugin\Model\CartInterfacePlugin::PROMO_ITEMS_KEY);

        if ($promoItems && count($promoItems)) {

            $products = [];

            foreach ($promoItems as $promoItem) {
                if ($promoItem instanceof \Magento\Quote\Model\Quote\Item) {

                    $products[] = [
                        'id'        => $promoItem->getSku(),
                        'name'      => $promoItem->getName(),
                        'quantity'  => $promoItem->getQty(),
                        'price'     => $this->checkoutHelper->convertPrice($this->checkoutHelper->getPriceInclTax($promoItem), false),
                        'brand'     => $this->dataHelper->getBrand($product),
                        'category'  => $this->getRequest()->getParam('category_path') ?: ''
                    ];
                }
            }

            $data = [
                'event' => 'addToCart',
                'ecommerce' => [
                    'add' => [
                        'products' => $products
                    ]
                ]
            ];

            $result['dataLayer'] = $data;

            $this->coreRegistry->unregister(\Totaltools\Ec\Plugin\Model\CartInterfacePlugin::PROMO_ITEMS_KEY);
        }

        $this->getResponse()->representJson(
            $this->jsonHelper->jsonEncode($result)
        );
    }
}
