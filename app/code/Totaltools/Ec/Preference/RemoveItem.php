<?php

namespace Totaltools\Ec\Preference;

use Magento\Framework\Exception\LocalizedException;

/**
 * Class preference for \Anowave\Ec\Preference\RemoveItem:
 * 1) Convert removed item price to GST included
 * 2) Rather than fetching item category, fetch category_path from \Magento\Quote\Api\Data\CartItemInterface
 *
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class RemoveItem extends \Magento\Checkout\Controller\Sidebar\RemoveItem
{
	/**
	 * @var \Magento\Checkout\Helper\Data
	 */
	protected $checkoutHelper;

    /**
     * RemoveItem constructor.
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magento\Framework\Controller\Result\JsonFactory $jsonFactory
     * @param \Magento\Framework\Controller\Result\RedirectFactory $resultRedirectFactory
     * @param \Magento\Framework\Data\Form\FormKey\Validator $formKeyValidator
     * @param \Magento\Checkout\Model\Sidebar $sidebar
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Checkout\Helper\Data $checkoutHelper
     */
	public function __construct
	(
		\Magento\Framework\App\Action\Context $context,
        \Magento\Framework\App\RequestInterface $request,
        \Magento\Framework\Controller\Result\JsonFactory $jsonFactory,
        \Magento\Framework\Controller\Result\RedirectFactory $resultRedirectFactory,
        \Magento\Framework\Data\Form\FormKey\Validator $formKeyValidator,
        \Magento\Checkout\Model\Sidebar $sidebar,
		\Psr\Log\LoggerInterface $logger,
		\Magento\Checkout\Helper\Data $checkoutHelper
	)
	{
        parent::__construct($context, $request, $jsonFactory, $resultRedirectFactory, $sidebar, $formKeyValidator, $logger);

        /**
         * @var \Magento\Checkout\Helper\Data
         */
		$this->checkoutHelper = $checkoutHelper;
	}

    /**
     * @inheritDoc
     */
    public function execute()
    {
        $resultJson = parent::execute();

        $response = $resultJson->getData();

        $item = $this->cart->getQuote()->getItemById((int) $this->getRequest()->getParam('item_id'));

        if ($item instanceof \Magento\Quote\Api\Data\CartItemInterface)
        {
            /**
             * Load product
             *
             * @var \Magento\Catalog\Api\Data\ProductInterface $product
             */
            $product = $this->productRepository->getById
            (
                $item->getProductId()
            );

            $data =
                [
                    'event' 	=> 'removeFromCart',
                    'ecommerce' =>
                        [
                            'remove' =>
                                [
                                    'products' =>
                                        [
                                            [
                                                'id'  		=> ($this->dataHelper->useSimples() ? $item->getSku() : $product->getSku()),
                                                'name' 		=> $item->getName(),
                                                'quantity' 	=> $item->getQty(),
                                                'price'		=> $this->checkoutHelper->convertPrice($this->checkoutHelper->getPriceInclTax($item), false),
                                                'brand'		=> $this->dataHelper->getBrand($product)
                                            ]
                                        ]
                                ]
                        ]
                ];

            /**
             * Get all product categories
             */
            $categories = $this->dataHelper->getCurrentStoreProductCategories($product);

            if ($categories)
            {
                /**
                 * Load last category
                 */
                $category = $this->categoryRepository->get
                (
                    end($categories)
                );

                /**
                 * Set category name
                 */
                $data['ecommerce']['remove']['products'][0]['category'] = $item->getCategoryPath();

                /**
                 * Set action field
                 */
                $data['ecommerce']['remove']['actionField'] =
                    [
                        'list' => $this->dataHelper->getCategoryList($category)
                    ];
            }

            /**
             * Set response push
             */
            $response['dataLayer'] = $data;
        }


        $resultJson->setData($response);
        return $resultJson;
    }

	/**
     * Compile JSON response
     *
     * @param string $error
     * @return \Magento\Framework\App\Response\Http
     */
    protected function jsonResponse($error = '')
    {
        $response = $this->sidebar->getResponseData($error);

        $item = $this->cart->getQuote()->getItemById((int) $this->getRequest()->getParam('item_id'));

        if ($item instanceof \Magento\Quote\Api\Data\CartItemInterface)
        {
        	/**
        	 * Load product
        	 *
        	 * @var \Magento\Catalog\Api\Data\ProductInterface $product
        	 */
        	$product = $this->productRepository->getById
        	(
        		$item->getProductId()
        	);

        	$data =
        	[
        		'event' 	=> 'removeFromCart',
        		'ecommerce' =>
        		[
        			'remove' =>
        			[
        				'products' =>
        				[
        					[
        						'id'  		=> ($this->dataHelper->useSimples() ? $item->getSku() : $product->getSku()),
        						'name' 		=> $item->getName(),
        						'quantity' 	=> $item->getQty(),
        						'price'		=> $this->checkoutHelper->convertPrice($this->checkoutHelper->getPriceInclTax($item), false),
								'brand'		=> $this->dataHelper->getBrand($product)
        					]
        				]
        			]
        		]
        	];

        	/**
        	 * Get all product categories
        	 */
        	$categories = $this->dataHelper->getCurrentStoreProductCategories($product);

        	if ($categories)
        	{
        		/**
        		 * Load last category
        		 */
        		$category = $this->categoryRepository->get
        		(
        			end($categories)
        		);

        		/**
        		 * Set category name
        		 */
        		$data['ecommerce']['remove']['products'][0]['category'] = $item->getCategoryPath();

        		/**
        		 * Set action field
        		 */
        		$data['ecommerce']['remove']['actionField'] =
        		[
        			'list' => $this->dataHelper->getCategoryList($category)
        		];
        	}

        	/**
        	 * Set response push
        	 */
        	$response['dataLayer'] = $data;
        }

        return $this->getResponse()->representJson($this->jsonHelper->jsonEncode($response));
    }
}
