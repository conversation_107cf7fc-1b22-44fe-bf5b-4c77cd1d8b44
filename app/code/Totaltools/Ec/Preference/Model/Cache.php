<?php
/**
 * Cache
 *
 * @category  Totaltools
 * @package   Totaltools_Ec
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Ec\Preference\Model;

use Magento\Customer\Model\Session as CustomerSession;

class Cache extends \Anowave\Ec\Model\Cache
{

    /**
     * @var CustomerSession
     */
    protected $customerSession;


    /**
     * @param \Magento\Framework\App\Cache\Type\FrontendPool $cacheFrontendPool
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Framework\HTTP\Header $headerService
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Customer\Api\CustomerRepositoryInterfaceFactory $customerRepositoryFactory
     * @param \Magento\Framework\App\Cache\StateInterface $cacheState
     * @param CustomerSession $customerSession
     */
    public function __construct(
            \Magento\Framework\App\Cache\Type\FrontendPool $cacheFrontendPool,
            \Magento\Store\Model\StoreManagerInterface $storeManager,
            \Magento\Framework\HTTP\Header $headerService,
            \Magento\Framework\Registry $registry,
            \Magento\Customer\Api\CustomerRepositoryInterfaceFactory $customerRepositoryFactory,
            \Magento\Framework\App\Cache\StateInterface $cacheState,
            CustomerSession $customerSession
        ) {
            parent::__construct($cacheFrontendPool, $storeManager, $headerService, $registry, $customerRepositoryFactory, $cacheState);
            $this->customerSession = $customerSession;
        }

    /**
	 * Generate unique cache id
	 *
	 * @param string $prefix
	 */
	protected function generateCacheId($prefix)
	{
		/**
		 * Push current store to make cache store specific
		 *
		 * @var int
		 */
		$p[] = $this->storeManager->getStore()->getId();

		/**
		 * Add website id
		 */
		$p[] = $this->storeManager->getStore()->getWebsiteId();

		/**
		 * Add currency
		 */
		$p[] = $this->storeManager->getStore()->getCurrentCurrencyCode();

		/**
		 * Check for mobile users
		 */
		if (version_compare(phpversion(), '8.0.0', '<')) 
		{
		    $p[] = \Zend_Http_UserAgent_Mobile::match($this->headerService->getHttpUserAgent(),$_SERVER);
		}
		else 
		{
		    if (!(php_sapi_name() == 'cli'))
		    {
		        if(!empty($_SERVER['HTTP_USER_AGENT']))
		        {
		            $p[] = preg_match("/(android|avantgo|blackberry|bolt|boost|cricket|docomo|fone|hiptop|mini|mobi|palm|phone|pie|tablet|up\.browser|up\.link|webos|wos)/i", $_SERVER["HTTP_USER_AGENT"]);
		        };
		    }
		    else 
		    {
		        $p[] = php_sapi_name();
		    }
		}

		/**
		 * Push request URI
		 *
		 * @var string
		 */
		$p[] =
		[
			$_SERVER['REQUEST_URI']
		];

		foreach (array($_GET, $_POST, $_FILES) as $request)
		{
			if ($request)
			{
				$p[] = $request;
			}
		}
        if ($this->customerSession->isLoggedIn()) {
            if ($this->registry->registry('cache_session_customer_id') > 0)
            {
                $customer = $this->customerRepositoryFactory->create()->getById($this->registry->registry('cache_session_customer_id'));

                /**
                 * Add customer group to key
                 */
                $p[] = $customer->getGroupId();
            }
        }


		$p = md5(serialize($p));

		/**
		 * Merge
		 */
		return "{$prefix}_{$p}";
	}
}

