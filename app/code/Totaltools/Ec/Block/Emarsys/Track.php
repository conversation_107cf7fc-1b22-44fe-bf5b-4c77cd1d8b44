<?php

namespace Totaltools\Ec\Block\Emarsys;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class Track extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Totaltools\Ec\Helper\Path
     */
    protected $pathHelper;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Totaltools\Ec\Helper\Path $pathHelper
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Totaltools\Ec\Helper\Path $pathHelper,
        array $data = []
    ) {
        /**
         * Set path helper
         * 
         * @var \Totaltools\Ec\Helper\Path
         */
        $this->pathHelper = $pathHelper;

        /**
         * Parent constructor
         */
        parent::__construct($context, $data);
    }

    /**
     * @return string
     */
    public function getPageCategory()
    {
        return str_replace(
            \Totaltools\Ec\Helper\Constants::CATEGORY_SEPERATOR,
            \Totaltools\Ec\Helper\Constants::EMARSYS_CATEGORY_SEPERATOR,
            $this->pathHelper->getPath()
        );
    }

    /**
     * @return Boolean
     */
    public function isEnabled()
    {
        return (bool) $this->_scopeConfig->getValue('totaltools_emarsys/web_extend/enabled', \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }
}
