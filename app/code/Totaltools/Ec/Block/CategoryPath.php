<?php

namespace Totaltools\Ec\Block;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class CategoryPath extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Totaltools\Ec\Helper\Path
     */
    protected $helper;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param array $data
     * @param \Totaltools\Ec\Helper\Path $helper
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Totaltools\Ec\Helper\Path $helper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->helper = $helper;
    }

    /**
     * @return string
     */
    public function getPath()
    {
        return $this->helper->getPath();
    }
}
