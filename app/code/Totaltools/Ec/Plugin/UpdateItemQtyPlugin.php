<?php

namespace Totaltools\Ec\Plugin;

use Magento\Framework\App\Response\Http;
use Magento\Framework\App\RequestInterface;
use Magento\Checkout\Model\Cart;
use Magento\Checkout\Helper\Data as CheckoutHelper;
use Anowave\Ec\Preference\UpdateItemQty;

class UpdateItemQtyPlugin
{
    /**
     * @var RequestInterface
     */
    protected RequestInterface $request;

    /**
     * @var CheckoutHelper
     */
    protected CheckoutHelper $checkoutHelper;

    /**
     * @var Cart
     */
    protected Cart $cart;

    /**
     * @param RequestInterface $request
     * @param CheckoutHelper $checkoutHelper
     * @param Cart $cart
     */
    public function __construct(RequestInterface $request, CheckoutHelper $checkoutHelper, Cart $cart)
    {
        $this->request = $request;
        $this->checkoutHelper = $checkoutHelper;
        $this->cart = $cart;
    }

    /**
     * @param UpdateItemQty $subject
     * @param Http $response
     * @return Http
     */
    public function afterExecute(UpdateItemQty $subject, Http $response)
    {
        $content = \json_decode($response->getContent(), 1);
        $type = isset($content['dataLayer']['ecommerce']['add']) ? 'add' : 'remove';

        $itemId = (int) $this->request->getParam('item_id');
        $cartItem = $this->cart->getQuote()->getItemById($itemId);

        if ($cartItem && !empty($content) && isset($content['dataLayer']['ecommerce'][$type]['products'])) {
            $products = &$content['dataLayer']['ecommerce'][$type]['products'];

            foreach ($products as &$product) {
                $priceInclTax = $this->checkoutHelper->getPriceInclTax($cartItem);

                $product['price'] = $this->checkoutHelper->convertPrice($priceInclTax, 0);
                $product['category'] = $cartItem->getCategoryPath() ?? $product['category'];
            }

            $response->setContent(\json_encode($content));
        }

        return $response;
    }
}
