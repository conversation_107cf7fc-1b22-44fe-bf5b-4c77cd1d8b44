<?php

namespace Totaltools\Ec\Plugin\Emarsys\Model;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class CheckoutSessionPlugin
{
    /**
     * @var \Totaltools\Ec\Helper\Emarsys\Data 
     */
    protected $helper;

    /**     
     * @param \Totaltools\Ec\Helper\Emarsys\Data $helper
     */
    public function __construct(\Totaltools\Ec\Helper\Emarsys\Data $helper)
    {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Checkout\Model\Session $subject
     * @param \Magento\Checkout\Model\Session $result
     */
    public function afterClearQuote($subject, $result)
    {
        if ($result instanceof \Magento\Checkout\Model\Session) {
            $this->helper->unsetCartContents();
        }

        return $result;
    }
}
