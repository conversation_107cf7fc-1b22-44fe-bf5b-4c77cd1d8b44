<?php

namespace Totaltools\Ec\Plugin\Emarsys\Model;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class QuotePlugin
{
    /**
     * @var \Totaltools\Ec\Helper\Emarsys\Data 
     */
    protected $helper;

    /**     
     * @param \Totaltools\Ec\Helper\Emarsys\Data $helper
     */
    public function __construct(\Totaltools\Ec\Helper\Emarsys\Data $helper)
    {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Quote\Model\Quote $subject
     * @param \Magento\Quote\Api\Data\CartItemInterface|string $quoteItem
     * @param \Magento\Catalog\Model\Product $product
     * @return \Magento\Quote\Api\Data\CartItemInterface|null
     */
    public function afterAddProduct(
        \Magento\Quote\Model\Quote $subject,
        $quoteItem = null
    ) {
        if ($quoteItem instanceof \Magento\Quote\Api\Data\CartItemInterface) {
            $this->helper->setCartContents($subject);
        }

        return $quoteItem;
    }

    /**
     * @param \Magento\Quote\Model\Quote $subject
     * @param \Magento\Quote\Api\Data\CartItemInterface $quoteItem
     * @return \Magento\Quote\Api\Data\CartItemInterface|null
     */
    public function afterUpdateItem(
        \Magento\Quote\Model\Quote $subject,
        \Magento\Quote\Api\Data\CartItemInterface $quoteItem = null
    ) {
        if ($quoteItem instanceof \Magento\Quote\Api\Data\CartItemInterface) {
            $this->helper->setCartContents($subject);
        }

        return $quoteItem;
    }

    /**
     * @param \Magento\Quote\Model\Quote $subject 
     * @param \Magento\Quote\Model\Quote $result 
     */
    public function afterRemoveItem(
        \Magento\Quote\Model\Quote $subject,
        \Magento\Quote\Model\Quote $result
    ) {
        $this->helper->setCartContents($result);
    }
}
