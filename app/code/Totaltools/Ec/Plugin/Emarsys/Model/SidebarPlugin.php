<?php

namespace Totaltools\Ec\Plugin\Emarsys\Model;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class SidebarPlugin
{
    /**
     * @var \Totaltools\Ec\Helper\Emarsys\Data 
     */
    protected $helper;

    /**     
     * @param \Totaltools\Ec\Helper\Emarsys\Data $helper
     */
    public function __construct(\Totaltools\Ec\Helper\Emarsys\Data $helper)
    {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Checkout\Model\Sidebar $subject
     * @param \Magento\Checkout\Model\Sidebar $result
     * @param \Magento\Checkout\Model\Sidebar $result
     */
    public function afterUpdateQuoteItem($subject, $result)
    {
        if ($result instanceof \Magento\Checkout\Model\Sidebar) {
            $this->helper->setCartContents($result->cart->getQuote());
        }

        return $result;
    }

    /**
     * @param \Magento\Checkout\Model\Sidebar $subject
     * @param \Magento\Checkout\Model\Sidebar $result
     * @param \Magento\Checkout\Model\Sidebar $result
     */
    public function afterRemoveQuoteItem($subject, $result)
    {
        if ($result instanceof \Magento\Checkout\Model\Sidebar) {
            $this->helper->setCartContents($result->cart->getQuote());
        }

        return $result;
    }
}
