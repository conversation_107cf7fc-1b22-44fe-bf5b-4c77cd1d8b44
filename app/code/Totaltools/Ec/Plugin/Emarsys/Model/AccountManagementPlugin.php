<?php

namespace Totaltools\Ec\Plugin\Emarsys\Model;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class AccountManagementPlugin
{
    /**
     * @var \Totaltools\Ec\Helper\Emarsys\Data 
     */
    protected $helper;

    /**
     * @param \Totaltools\Ec\Helper\Emarsys\Data $helper
     */
    public function __construct(
        \Totaltools\Ec\Helper\Emarsys\Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Customer\Model\AccountManagement $subject
     * @param bool $result
     * @param string $customerEmail
     */
    public function afterIsEmailAvailable(
        \Magento\Customer\Api\AccountManagementInterface $subject,
        bool $result,
        string $customerEmail = null
    ) {
        if ($customerEmail) {
            $this->helper->setCustomerInfo(json_encode(['email' => $customerEmail]));
        }

        return $result;
    }
}
