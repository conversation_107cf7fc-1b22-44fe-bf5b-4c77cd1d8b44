<?php

namespace Totaltools\Ec\Plugin\Helper;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class Data
{
    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $categoryFactory;

    /**
     * @var \Anowave\Ec\Helper\Attributes
     */
    protected $attributes;

    /**
     * @var \Totaltools\Ec\Helper\Path
     */
    protected $pathHelper;

    /**
     * @var \Totaltools\Pronto\Helper\Data
     */
    protected $prontoHelper;

    /**
     * @param \Magento\Catalog\Model\CategoryFactory $categoryFactory
     * @param \Anowave\Ec\Helper\Attributes $attributes,
     * @param \Totaltools\Ec\Helper\Path $pathHelper,
     * @param \Totaltools\Pronto\Helper\Data $prontoHelper
     */
    public function __construct(
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Anowave\Ec\Helper\Attributes $attributes,
        \Totaltools\Ec\Helper\Path $pathHelper,
        \Totaltools\Pronto\Helper\Data $prontoHelper
    ) {
        /**
         * set category factory
         *
         * @var \Magento\Catalog\Model\CategoryFactory
         */
        $this->categoryFactory = $categoryFactory;

        /**
         * set attributes
         *
         * @var \Anowave\Ec\Helper\Attributes
         */
        $this->attributes = $attributes;

        /**
         * set path helper
         *
         * @var \Totaltools\Ec\Helper\Path
         */
        $this->pathHelper = $pathHelper;

        $this->prontoHelper = $prontoHelper;
    }

    /**
     * @param \Anowave\Helper\Data $subject
     * @param bool|mixed $result
     * @param \Magento\Framework\View\Element\Template $block
     * @return bool|mixed
     */
    public function afterGetImpressionPushForward($subject, $result, $block)
    {
        if ($result === false) {
            $result = $this->getPopularBlockImpressions($subject, $block);
        }

        return $result;
    }

    /**
     * @param \Anowave\Helper\Data $subject
     * @param \Magento\Framework\View\Element\Template $block
     * @return mixed
     */
    protected function getPopularBlockImpressions($subject, $block)
    {
        try {
            $list = $block->getLayout()->getBlock('popular.products.list');

            if ($list) {

                $categoryFactory = $this->categoryFactory->create();
                $category = $categoryFactory->loadByAttribute('url_key', 'featured');

                if (!$category) {
                    return false;
                }

                $response =
                    [
                        'ecommerce' =>
                        [
                            'currencyCode' => $subject->getStore()->getCurrentCurrencyCode(),
                            'actionField' =>
                            [
                                'list' => $category->getName()
                            ],
                            'impressions' => []
                        ]
                    ];

                /**
                 * Get loaded collection
                 *
                 * @var \Magento\Eav\Model\Entity\Collection\AbstractCollection $collection
                 */
                $collection = $list->getLoadedProductCollection();

                /**
                 * Set default position
                 *
                 * @var integer $position
                 */
                $position = 1;

                /**
                 * Consider pagination
                 *
                 * @var int $p
                 */
                $p = (int) $collection->getCurPage();

                if ($p > 1) {
                    $position += (($p - 1) * (int) $collection->getPageSize());
                }

                /**
                 * Push data
                 *
                 * @var []
                 */
                $data = [];

                $taxonomy = (object) [
                    'list' => $category->getName(),
                    'name' => \Totaltools\Ec\Helper\Constants::HOME_CATEGORY
                ];

                foreach ($collection as $product) {

                    $entity =
                        [
                            'list'          => $taxonomy->list,
                            'category'      => $taxonomy->name,
                            'id'            => $product->getSku(),
                            'name'          => $product->getName(),
                            'brand'         => $subject->getBrand(
                                $product
                            ),
                            'price'         => $subject->getPrice($product),
                            'position'      => $position++
                        ];

                    /**
                     * Create transport object
                     *
                     * @var \Magento\Framework\DataObject $transport
                     */
                    $transport = new \Magento\Framework\DataObject(
                        [
                            'attributes' => $this->attributes->getAttributes(),
                            'entity'     => $entity
                        ]
                    );

                    /**
                     * Get response
                     */
                    $attributes = $transport->getAttributes();

                    /**
                     * Add entity to impression array
                     */
                    $response['ecommerce']['impressions'][] = array_merge($entity, $attributes);
                }

                $response['currentStore'] = $subject->getStoreName();
            } else {
                /**
                 * @todo: Handle non-existent block
                 */
            }

            /**
             * Create transport object
             *
             * @var \Magento\Framework\DataObject $transport
             */
            $transport = new \Magento\Framework\DataObject(
                [
                    'response' => $response
                ]
            );

            /**
             * Get response
             */
            $response = $transport->getResponse();

            /**
             * Facebook data
             *
             * @var []
             */

            $content_name =  $taxonomy->name;

            $fbq =
                [
                    'content_name'        => $content_name,
                    'content_category'     => $content_name,
                    'content_type'         => 'product',
                    'content_ids'         => array_map(
                        function ($entity) {
                            return $entity['id'];
                        },
                        $response['ecommerce']['impressions']
                    )
                ];

            return (object) [
                'push'               => $subject->getJsonHelper()->encode($response),
                'google_tag_params'  => array(
                    'ecomm_pagetype' => 'home',
                    'ecomm_category' => 'home'
                ),
                'fbq' => $subject->getJsonHelper()->encode($fbq)
            ];
        } catch (\Exception $e) {
        }

        return false;
    }

    /**
     * @param \Anowave\Helper\Data $subject
     * @param string $result
     * @param \Magento\Catalog\Model\Category $category
     * @return string
     */
    public function afterGetCategory(
        $subject,
        $result,
        \Magento\Catalog\Model\Category $category
    ) {
        if ($this->pathHelper->getPath()) {
            return $this->pathHelper->getPath();
        }

        return $result;
    }

    /**
     * @param \Anowave\Helper\Data $subject
     * @param string $result
     * @param \Magento\Framework\View\Element\AbstractBlock $block
     * @return string
     */
    public function afterGetVisitorPush($subject, $result, $block = null)
    {
        if ($subject->isLogged()) {
            $data = (array) json_decode($result, true);
            $extAttrs = $subject->getCustomer()->getExtensionAttributes();

            if (isset($data['visitorId']) && $extAttrs->getLoyaltyId()) {
                $data['visitorId'] = $extAttrs->getLoyaltyId();
                $data['visitorTier'] = $this->getLoyaltyTier($subject->getCustomer()->getId());
            }

            $result = $subject->getJsonHelper()->encode($data);
        }

        return $result;
    }

    /**
     * @param int|string $customerId
     * @return string
     */
    public function getLoyaltyTier($customerId)
    {
        $loyaltyData = $this->prontoHelper->getCustomerLoyaltyData($customerId);

        return $loyaltyData ? $loyaltyData->getData('customerLoyaltyLevelText') : '';
    }
}
