<?php

namespace Totaltools\Ec\Plugin\Model;

/**
 * @see https://github.com/magento/community-features/issues/152
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class CartInterfacePlugin
{
    /**
     * Promo item registry key
     */
    const PROMO_ITEMS_KEY = 'am_promo_items';

    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $request;

    /**
     * @var \Magento\Framework\Registry
     */
    private $coreRegistry;

    /**
     * @var \Amasty\Promo\Helper\Item
     */
    protected $promoItemHelper;

    /**
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magento\Framework\Registry $coreRegistry
     * @param \Amasty\Promo\Helper\Item $promoItemHelper
     */
    public function __construct(
        \Magento\Framework\App\RequestInterface $request,
        \Magento\Framework\Registry $coreRegistry,
        \Amasty\Promo\Helper\Item $promoItemHelper
    ) {
        $this->request = $request;
        $this->coreRegistry = $coreRegistry;
        $this->promoItemHelper = $promoItemHelper;
    }

    /**
     * @param \Magento\Quote\Api\Data\CartInterface $subject
     * @param null|string|\Magento\Quote\Model\Quote\Item $quoteItem
     * @param \Magento\Catalog\Model\Product $product
     * @param null|float|\Magento\Framework\DataObject $request
     * @return \Magento\Quote\Api\Data\CartItemInterface|\Magento\Quote\Model\Quote\Item|mixed
     */
    public function afterAddProduct(
        \Magento\Quote\Api\Data\CartInterface $subject,
        $quoteItem,
        \Magento\Catalog\Model\Product $product,
        $request = null
    ) {
        if ($quoteItem instanceof \Magento\Quote\Api\Data\CartItemInterface) {
            $categoryPath = filter_var($this->getCategoryPath(), FILTER_SANITIZE_SPECIAL_CHARS);
            $param = (int) $this->request->getParam('product');
            $pid = (int) $product->getId();

            if ($categoryPath && ($pid === $param || in_array($pid, $this->getRelatedItemsIds()) || in_array($pid, $this->getComboItemsIds()))) {
                $quoteItem->setData(\Totaltools\Ec\Helper\Constants::CATEGORY_PATH, $categoryPath);
            }
        }

        if ($quoteItem instanceof \Magento\Quote\Model\Quote\Item && $this->promoItemHelper->isPromoItem($quoteItem)) {
            $promoItems = $this->coreRegistry->registry(self::PROMO_ITEMS_KEY);

            if ($promoItems && count($promoItems)) {
                array_push($promoItems, $quoteItem);
                $this->coreRegistry->unregister(self::PROMO_ITEMS_KEY);
            } else {
                $promoItems = [$quoteItem];
            }

            $this->coreRegistry->register(self::PROMO_ITEMS_KEY, $promoItems, true);
        }

        return $quoteItem;
    }

    /**
     * @return string
     */
    protected function getCategoryPath()
    {
        return $this->request->getParam(\Totaltools\Ec\Helper\Constants::CATEGORY_PATH);
    }

    /**
     * @return array
     */
    protected function getRelatedItemsIds()
    {
        return explode(',', (string) $this->request->getParam(\Totaltools\Ec\Helper\Constants::PARAM_RELATED_ITEMS));
    }

    /**
     * @return array
     */
    protected function getComboItemsIds()
    {
        return explode(',', (string) $this->request->getParam(\Totaltools\Ec\Helper\Constants::PARAM_RELATED_ITEMS));
    }
}
