<?php

namespace Totaltools\Ec\Observer;

/**
 * @category    Totaltools
 * @package     Totaltools_Ec
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 */

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;

class AddCustomProductAttributes implements ObserverInterface
{
    /**
     * @inheritdoc
     */
    public function execute(Observer $observer)
    {
        /**
         * @var \Magento\Framework\DataObject $transport
         */
        $transport = $observer->getTransport();

        /**
         * @var \Magento\Sales\Model\Order\Item $quoteItem
         */
        $quoteItem = $transport->getQuoteItem();

        /**
         * @var \Magento\Framework\DataObject $productData
         */
        $productData = $transport->getProduct();

        /**
         * @var \Magento\Catalog\Model\Product $product
         */
        $product = $quoteItem->getProduct();

        if ($product->getId() != null) {
            $productData->setData('mpn', $product->getData('part_no'));
        }
    }
}
