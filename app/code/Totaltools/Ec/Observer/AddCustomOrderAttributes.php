<?php

namespace Totaltools\Ec\Observer;

/**
 * @category    Totaltools
 * @package     Totaltools_Ec
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 */

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\Request\Http;
use Magento\Framework\Event\Observer;
use Magento\Sales\Model\Order;

class AddCustomOrderAttributes implements ObserverInterface
{
    /**
     * @var Order
     */
    protected $orderModel;

    /**
     * @var Http
     */
    protected $request;

    /**
     * AddCustomOrderAttributes constructor
     *
     * @param Order $orderModel
     * @param Http $request
     */
    public function __construct(Order $orderModel, Http $request)
    {
        $this->orderModel = $orderModel;
        $this->request = $request;
    }

    /**
     * @inheritdoc
     */
    public function execute(Observer $observer)
    {
        /** @var \Magento\Framework\DataObject $transport */
        $transport = $observer->getTransport();

        /**  @var array $response */
        $response = $transport->getResponse();

        if (isset($response['ecommerce']['purchase']['actionField']['id'])) {
            /** @var string $orderIncrementId */
            $orderIncrementId = $response['ecommerce']['purchase']['actionField']['id'];

            /** @var Order $order */
            $order = $this->orderModel->loadByIncrementId($orderIncrementId);

            /** @var string $checkoutSource */
            $checkoutSource = $this->request->getParam('source');

            if ($order->getId() != null) {
                $data = [
                    'ecommerce' => [
                        'purchase' => [
                            'actionField' => [
                                'subtotal' => (float) $order->getSubtotal()
                            ]
                        ]
                    ]
                ];

                if ($checkoutSource) {
                    $data['ecommerce']['purchase']['actionField']['source'] = $checkoutSource;
                }

                $transport->setData('response', array_merge_recursive($response, $data));
            }
        }
    }
}
