<?php

namespace Totaltools\Ec\Observer\Emarsys;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class CheckoutCartUpdateItemsAfterObserver implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @var \Totaltools\Ec\Helper\Emarsys\Data 
     */
    protected $helper;

    /**
     * @param \Totaltools\Ec\Helper\Emarsys\Data $helper
     */
    public function __construct(
        \Totaltools\Ec\Helper\Emarsys\Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /**
         * @var \Magento\Checkout\Model\Cart
         */
        $cart = $observer->getCart();

        if ($cart instanceof \Magento\Checkout\Model\Cart) {
            $this->helper->setCartContents($cart->getQuote());
        }

        return $this;
    }
}
