<?php

namespace Totaltools\Ec\Observer\Emarsys;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class CustomerLogoutObserver implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @var \Totaltools\Ec\Helper\Emarsys\Data 
     */
    protected $helper;

    /**
     * @param \Totaltools\Ec\Helper\Emarsys\Data $helper
     */
    public function __construct(
        \Totaltools\Ec\Helper\Emarsys\Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $this->helper->unsetCustomerInfo();
        $this->helper->setCartContents();

        return $this;
    }
}
