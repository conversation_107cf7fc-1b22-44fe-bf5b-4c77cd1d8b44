<?php

namespace Totaltools\Ec\Observer\Emarsys;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class CustomerLoginObserver implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $checkoutSession;

    /**
     * @var \Totaltools\Ec\Helper\Emarsys\Data
     */
    protected $helper;

    /**
     * @param \Magento\Checkout\Model\Session $customerSession
     * @param \Totaltools\Ec\Helper\Emarsys\Data $helper
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Totaltools\Ec\Helper\Emarsys\Data $helper
    ) {
        $this->checkoutSession = $checkoutSession;
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $this->setCartContents();
        $this->setUserInfo($observer->getCustomer());

        return $this;
    }

    /**
     * @return void
     */
    protected function setCartContents()
    {
        try {
            $this->helper->setCartContents($this->checkoutSession->getQuote());
        } catch (\Exception $e) { }
    }

    /**
     * @return void
     */
    protected function setUserInfo(\Magento\Customer\Model\Customer $customer)
    {
        $customerEmail = $customer->getEmail();
        $customerId = $customer->getLoyaltyId();

        if ($customerEmail) {
            $customerInfo = ['id' => $customerId, 'email' => $customerEmail];
            $this->helper->setCustomerInfo(json_encode($customerInfo));
        }
    }
}
