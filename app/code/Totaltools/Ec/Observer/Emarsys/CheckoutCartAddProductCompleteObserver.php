<?php

namespace Totaltools\Ec\Observer\Emarsys;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class CheckoutCartAddProductCompleteObserver implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @var \Totaltools\Ec\Helper\Emarsys\Data 
     */
    protected $helper;

    /**
     * @var \Magento\Checkout\Model\Cart
     */
    protected $cart;

    /**
     * @param \Totaltools\Ec\Helper\Emarsys\Data $helper
     * @param \Magento\Checkout\Model\Cart $cart
     */
    public function __construct(
        \Totaltools\Ec\Helper\Emarsys\Data $helper,
        \Magento\Checkout\Model\Cart $cart
    ) {
        $this->helper = $helper;
        $this->cart = $cart;
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        if ($this->cart instanceof \Magento\Checkout\Model\Cart) {
            $this->helper->setCartContents($this->cart->getQuote());
        }

        return $this;
    }
}
