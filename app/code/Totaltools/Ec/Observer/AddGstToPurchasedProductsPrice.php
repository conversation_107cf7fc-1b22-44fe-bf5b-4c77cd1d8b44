<?php

namespace Totaltools\Ec\Observer;

/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

class AddGstToPurchasedProductsPrice implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /** 
         * @var \Magento\Framework\DataObject $transport 
         */
        $transport = $observer->getTransport();

        /** @var \Magento\Framework\DataObject $product */
        $product = $transport->getProduct();

        /** @var \Magento\Sales\Model\Order\Item $quoteItem */
        $quoteItem = $transport->getQuoteItem();

        if ($quoteItem instanceof \Magento\Sales\Model\Order\Item) {
            $product->setPriceInGst((float) $quoteItem->getPriceInclTax());
        }

        return $this;
    }
}
