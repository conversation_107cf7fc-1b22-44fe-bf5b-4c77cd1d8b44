<?php
/**
 * @var \Totaltools\Ec\Block\Emarsys\Track $block
 * @var \Magento\Framework\Escaper $escaper
 */

 $searchQuery = $block->getRequest()->getParam('q', null);

if ($block->isEnabled() && $searchQuery): ?>
<script>
    (function(w){
        w.ScarabQueue = w.ScarabQueue || [];
        ScarabQueue.push(['searchTerm', '<?= strip_tags(($searchQuery)); ?>']);
        !window.preventGoCommand && !ScarabQueue.includes('go') && ScarabQueue.push(['go']);
    })(window);
</script>
<?php endif; ?>
