<?php
/**
 * @var \Magento\Catalog\Block\Product\View $block
 */

$product = $block->getProduct();
$helper = $this->helper(\Totaltools\Ec\Helper\Emarsys\Data::class);

if ($helper->isWebExtendEnabled() && $product->getSku()): ?>
<script>
    (function(w) {
        if (!window.preventGoCommand) {
            w.ScarabQueue = w.ScarabQueue || [];
            ScarabQueue.push(['view', '<?= /** @noEscape */ $product->getSku(); ?>']);
        }
    })(window);
</script>
<?php endif; ?>