<?php
/**
 * @var \Anowave\Ec\Block\Track $block
 */
$helper = $this->helper(\Totaltools\Ec\Helper\Emarsys\Data::class);
$pruchaseData = $block->getHelper()->getPurchasePayloadCollection($block);

$helper->unsetCartContents();

if ($helper->isWebExtendEnabled() && !empty($pruchaseData)): ?>
<script>
    (function(w) {
        w.ScarabQueue = w.ScarabQueue || [];
        var data = <?= /** @noEscape */ $pruchaseData; ?>;

        if (Array.isArray(data[0]?.ecommerce?.purchase?.products)) {
            let purchase = data[0].ecommerce.purchase,
                orderData = { orderId: purchase.actionField.id };

            orderData.items = purchase.products.map(function(prod) {
                return {
                    item: prod.id,
                    quantity: prod.quantity,
                    price: prod.price_in_gst,
                };
            });

            ScarabQueue.push(['purchase', orderData]);
        }
    })(window);
</script>
<?php endif; ?>
