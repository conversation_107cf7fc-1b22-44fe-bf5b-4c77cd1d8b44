<?php
/**
 * @var \Totaltools\Ec\Block\Emarsys\Track $block
 */

if ($block->isEnabled()): ?>
<script type="text/javascript">
    <?php
    /****************************
     * Emarsys Tags
     ***************************/ ?>
    (function(w) {
        /**
         * Define ScarabQueue object & append script to page.
         */
        w.ScarabQueue = w.ScarabQueue || [];

        (function(id) {
            if (document.getElementById(id)) return;
            var js = document.createElement('script');
            js.id = id;
            js.src = '//cdn.scarabresearch.com/js/1DA9B74C6CFBAE7B/scarab-v2.js';
            var fs = document.getElementsByTagName('script')[0];
            fs.parentNode.insertBefore(js, fs);
        })('scarab-js-api');

        var Cookie = {
            get: function (name) {
                var start = document.cookie.indexOf(name + "="), len = start + name.length + 1;

                if (!start && name != document.cookie.substring(0, name.length)) return null;
                if (start == -1) return null;

                var end = document.cookie.indexOf(String.fromCharCode(59), len);
                if (end == -1) end = document.cookie.length;

                return decodeURIComponent(document.cookie.substring(len, end));
            },
            parse: function(json) {
                var json = decodeURIComponent(json.replace(/\+/g, ' '));
                return JSON.parse(json);
            }
        }

        /**
         * Push cart
         */
        var cartContents = Cookie.get("<?= /* @escapeNotVerified */ \Totaltools\Ec\Helper\Emarsys\Data::EMARSYS_CART_CONTENT_COOKIE; ?>");

        ScarabQueue.push(['cart', cartContents ? (Cookie.parse(cartContents)) : []]);

        /**
         * Push user email
         */
        var userInfo = Cookie.get("<?= /* @escapeNotVerified */ \Totaltools\Ec\Helper\Emarsys\Data::EMARSYS_USER_INFO; ?>");

        if (userInfo) {
            let userEmail = Cookie.parse(userInfo);
            ScarabQueue.push(['setEmail', userEmail]);
        }

        /**
         * Push page category
         */
        <?php if ($block->getPageCategory()): ?>
        ScarabQueue.push(['category', '<?= /* @noEscape */ $block->getPageCategory(); ?>']);
        <?php endif; ?>

        if (!window.preventGoCommand) {
            ScarabQueue.push(['go']);
        }
    })(window);
</script>
<?php endif; ?>
