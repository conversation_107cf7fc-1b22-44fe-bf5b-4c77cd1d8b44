<script type="text/javascript">
    <?php
    /****************************
     * Emarsys Tags
     ***************************/ ?>
        (function(w, AEC) {
            /**
             * Define global dataLayer[] object
             */
            w.dataLayer = w.dataLayer || [];

            /**
             * Push cartContents
             */
            var cartContents = AEC.Cookie.get("<?= /* @escapeNotVerified */ \Totaltools\Ec\Helper\Emarsys\Data::EMARSYS_CART_CONTENT_COOKIE; ?>");

            if (!cartContents) {
                cartContents = false;
            } else {
                cartContents = AEC.Cookie.parse(cartContents);
            }

            AEC.Cookie.impressions({
                'cartContents': cartContents,
                'event': 'cart_contents'
            }).push(dataLayer, false);

            /**
             * Push userEmail
             */
            var userInfo = AEC.Cookie.get("<?= /* @escapeNotVerified */ \Totaltools\Ec\Helper\Emarsys\Data::EMARSYS_USER_INFO; ?>");

            if (userInfo) {
                var userInfo = AEC.parseJSON(AEC.parseJSON(userInfo));

                AEC.Cookie.impressions({
                    'userId' : `${userInfo?.id}`,
                    'event' : 'user_id'
                }).push(dataLayer, false);

                AEC.Cookie.impressions({
                    'userEmail': userInfo?.email,
                    'event': 'user_email'
                }).push(dataLayer, false);
            }

            /**
             * Push pageCateogry
             */
            <?php $pageCategory = $block->getPageCategory(); ?>
            AEC.Cookie.impressions({
                'pageCategory': '<?php /** @escapeNotVerified */ echo $pageCategory ?: ''; ?>',
                'event': 'page_category'
            }).push(dataLayer, false);
        })(window, AEC);
</script>
<script type="text/x-magento-init">
{
    "*": {
        "bonusProductEventHandler": {}
    }
}
</script>