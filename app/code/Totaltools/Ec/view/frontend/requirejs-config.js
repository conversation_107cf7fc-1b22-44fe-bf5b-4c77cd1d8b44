var config = {
    config: {
        mixins: {
            "Totaltools_Checkout/js/view/cart-step/item/details": {
                "Totaltools_Ec/js/view/cart-step/item/details": false,
            },
            "Magento_Checkout/js/action/select-shipping-method": {
                "Totaltools_Ec/js/action/select-shipping-method": true,
            },
            "Magento_Checkout/js/action/place-order": {
                "Totaltools_Ec/js/action/place-order": true,
            },
            "Magento_Checkout/js/view/payment": {
                "Totaltools_Ec/js/view/payment": true,
            },
            "Amasty_CheckoutCore/js/view/checkout/summary/item/details": {
                "Totaltools_Ec/js/view/checkout/summary/item/details": true,
            },
        },
    },
    map: {
        "*": {
            bonusProductEventHandler:
                "Totaltools_Ec/js/bonus-product-event-handler",
        },
    },
};
