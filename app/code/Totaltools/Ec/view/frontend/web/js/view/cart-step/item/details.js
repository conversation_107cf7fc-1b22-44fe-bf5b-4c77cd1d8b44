/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */

define(function() {
    'use strict';

    return function(Component) {

        return Component.extend({
            /**
             * @private
             */
            _updateItemQtyAfter: function(response) {
                this._super();

                if (response.hasOwnProperty('dataLayer') && 'undefined' !== typeof dataLayer) {
                    if (AEC.Const.COOKIE_DIRECTIVE) {
                        if (AEC.Const.COOKIE_DIRECTIVE_CONSENT_GRANTED) {
                            dataLayer.push(response.dataLayer);
                        }
                    } else {
                        dataLayer.push(response.dataLayer);
                    }
                }
            },

            /**
             * @private
             */
            _removeItemAfter: function(response) {
                this._super();

                if (response.hasOwnProperty('dataLayer') && 'undefined' !== typeof dataLayer) {
                    if (AEC.Const.COOKIE_DIRECTIVE) {
                        if (AEC.Const.COOKIE_DIRECTIVE_CONSENT_GRANTED) {
                            dataLayer.push(response.dataLayer);
                        }
                    } else {
                        dataLayer.push(response.dataLayer);
                    }
                }
            }
        });
    };
});
