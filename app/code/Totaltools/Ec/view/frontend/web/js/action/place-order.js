/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define(['mage/utils/wrapper'], function (wrapper) {
    'use strict';

    return function (placeOrderAction) {
        return wrapper.wrap(
            placeOrderAction,
            function (original, paymentData, messageContainer) {
                if (
                    'undefined' !== typeof data &&
                    'undefined' !== typeof AEC.VPV &&
                    'undefined' !== typeof AEC.Const.CHECKOUT_STEP_ORDER_TITLE
                ) {
                    AEC.VPV.push(
                        AEC.Const.CHECKOUT_STEP_ORDER_TITLE,
                        AEC.Const.CHECKOUT_STEP_ORDER_URL
                    );

                    var dataLayer = window.dataLayer || [];
                    dataLayer.push({ event: 'placeOrder' });
                }

                return original(paymentData, messageContainer);
            }
        );
    };
});
