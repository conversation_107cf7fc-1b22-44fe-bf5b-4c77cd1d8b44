/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define(['Magento_Customer/js/customer-data'], function (customerData) {
    'use strict';

    return function (itemDetails) {
        return itemDetails.extend({
            /**
             * @inheritdoc
             */
            initObservable: function () {
                this._super();

                customerData.get('cart')
                    .subscribe(this.updateTrackingData);

                return this;
            },

            /**
             *
             * @param {Object} data
             */
            updateTrackingData: function (data) {
                var updatedItems = data.items || [],
                    gtmProducts =
                        window.data && window.data.ecommerce
                            ? window.data.ecommerce.checkout.products
                            : [];

                if (updatedItems.length && gtmProducts.length) {
                    gtmProducts.forEach(function (p, idx) {
                        let match = updatedItems.find(function (i) {
                            return (
                                i.product_name == p.name ||
                                i.product_sku == p.id
                            );
                        });

                        if (match && gtmProducts[idx].quantity != match.qty) {
                            gtmProducts[idx].quantity = match.qty;
                        }

                        if (!match) {
                            gtmProducts.splice(idx, 1);
                        }
                    });
                }
            },
        });
    };
});
