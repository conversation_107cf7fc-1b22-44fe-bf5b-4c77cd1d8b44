/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'underscore',
    'Magento_Checkout/js/model/quote',
    'Amasty_CheckoutCore/js/model/events',
    '../model/analytics',
], function (_, quote, events, analytics) {
    'use strict';

    return function (Payment) {
        return Payment.extend({
            /**
             * @inheritdoc
             */
            initObservable: function () {
                this._super();

                var self = this;

                events.onAfterShippingSave(_.debounce(function () {
                    self.fireVirtualView();
                    self.fireStepEcEvent();
                }, 50));

                if (quote.isVirtual()) {
                    quote.billingAddress.subscribe(_.debounce(function (addr) {
                        self.fireVirtualView();
                        self.fireStepEcEvent();
                    }, 50));
                }

                return this;
            },

            fireVirtualView: function () {
                if (
                    (analytics.paymentProcessed() === false,
                    'undefined' !== typeof data &&
                        'undefined' !== typeof AEC.VPV &&
                        'undefined' !==
                            typeof AEC.Const.CHECKOUT_STEP_PAYMENT_TITLE)
                ) {
                    AEC.VPV.push(
                        AEC.Const.CHECKOUT_STEP_PAYMENT_TITLE,
                        AEC.Const.CHECKOUT_STEP_PAYMENT_URL
                    );
                    analytics.paymentProcessed(true);
                }
            },

            fireStepEcEvent: function () {
                if (
                    'undefined' !== typeof data &&
                    'undefined' !== typeof AEC.Const.CHECKOUT_STEP_PAYMENT
                ) {
                    AEC.Checkout.step(
                        AEC.Const.CHECKOUT_STEP_SHIPPING,
                        AEC.Const.CHECKOUT_STEP_PAYMENT - 1,
                        'payment'
                    );

                    let paymentMethod = quote.paymentMethod()
                        ? quote.paymentMethod()['title']
                        : '';

                    paymentMethod &&
                        AEC.Checkout.stepOption(
                            AEC.Const.CHECKOUT_STEP_PAYMENT,
                            quote.paymentMethod()['title'] || ''
                        );
                }
            },
        });
    };
});
