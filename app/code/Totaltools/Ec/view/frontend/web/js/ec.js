/**
 * @category    Totaltools
 * @package     Totaltools_Ec
 * <AUTHOR>
 * @copyright   Copyright (c) 2019-21 Totaltools. <https://totaltools.com.au>
 */

(function (AEC) {
    /**
     * Overwrite default AEC.ajax method to add support for related products.
     */
    AEC.ajax = function (context, dataLayer, customEvent) {
        var element = jQuery(context),
            qty = jQuery(':radio[name=qty]:checked, [name=qty]').eq(0).val(),
            variant = [],
            variant_attribute_option = [],
            products = [];

        /**
         * Cast quantity to integer
         */
        qty = Math.abs(qty);

        if (isNaN(qty)) {
            qty = 1;
        }

        /**
         * Validate "Add to cart" before firing an event
         */
        var form = jQuery(context).closest('form');

        if (form.length) {
            if (!form.valid()) {
                return true;
            }
        }

        if (!AEC.gtm()) {
            /**
             * Invoke original click event(s)
             */
            if (element.data('click')) {
                /**
                 * Track time
                 */
                AEC.Time.track(
                    dataLayer,
                    AEC.Const.TIMING_CATEGORY_ADD_TO_CART,
                    element.data('name'),
                    element.data('category')
                );

                eval(element.data('click'));
            }

            return true;
        }

        if (element.data('configurable')) {
            var attributes = jQuery('[name^="super_attribute"]'),
                variants = [];

            jQuery.each(attributes, function (index, attribute) {
                if (jQuery(attribute).is('select')) {
                    var name = jQuery(attribute).attr('name'),
                        id = name.substring(
                            name.indexOf('[') + 1,
                            name.lastIndexOf(']')
                        );

                    var option = jQuery(attribute).find('option:selected');

                    if (0 < parseInt(option.val())) {
                        variants.push({
                            id: id,
                            option: option.val(),
                            text: option.text(),
                        });
                    }
                }
            });

            /**
             * Colour Swatch support
             */
            if (!variants.length) {
                jQuery.each(AEC.SUPER, function (index, attribute) {
                    var swatch = jQuery(
                        'div[attribute-code="' + attribute.code + '"]'
                    );

                    if (swatch.length) {
                        var variant = {
                            id: attribute.id,
                            text: '',
                            option: null,
                        };

                        var select = swatch.find('select');

                        if (select.length) {
                            var option = swatch
                                .find('select')
                                .find(':selected');

                            if (option.length) {
                                variant.text = option.text();
                                variant.option = option.val();
                            }
                        } else {
                            var span = swatch.find(
                                'span.swatch-attribute-selected-option'
                            );

                            if (span.length) {
                                variant.text = span.text();
                                variant.option = span
                                    .parent()
                                    .attr('option-selected');
                            }
                        }

                        variants.push(variant);
                    }
                });
            }

            var SUPER_SELECTED = [];

            if (true) {
                for (i = 0, l = variants.length; i < l; i++) {
                    for (a = 0, b = AEC.SUPER.length; a < b; a++) {
                        if (AEC.SUPER[a].id == variants[i].id) {
                            var text = variants[i].text;

                            if (AEC.useDefaultValues) {
                                jQuery.each(
                                    AEC.SUPER[a].options,
                                    function (index, option) {
                                        if (
                                            parseInt(option.value_index) ==
                                            parseInt(variants[i].option)
                                        ) {
                                            text = option.admin_label;
                                        }
                                    }
                                );
                            }

                            variant.push(
                                [AEC.SUPER[a].label, text].join(
                                    AEC.Const.VARIANT_DELIMITER_ATT
                                )
                            );

                            /**
                             * Push selected options
                             */
                            variant_attribute_option.push({
                                attribute: variants[i].id,
                                option: variants[i].option,
                            });
                        }
                    }
                }
            }

            if (!variant.length) {
                /**
                 * Invoke original click event(s)
                 */
                if (element.data('click')) {
                    /**
                     * Track time
                     */
                    AEC.Time.track(
                        dataLayer,
                        AEC.Const.TIMING_CATEGORY_ADD_TO_CART,
                        element.data('name'),
                        element.data('category')
                    );

                    eval(element.data('click'));
                }

                return true;
            }
        }

        if (element.data('grouped')) {
            for (u = 0, y = window.G.length; u < y; u++) {
                var qty = Math.abs(
                    jQuery('[name="super_group[' + window.G[u].id + ']"]').val()
                );

                if (qty) {
                    products.push({
                        name: window.G[u].name,
                        id: window.G[u].sku,
                        price: window.G[u].price,
                        category: window.G[u].category,
                        brand: window.G[u].brand,
                        quantity: qty,
                    });
                }
            }
        } else {
            products.push({
                name: element.data('name'),
                id: element.data('id'),
                price: element.data('price'),
                category: element.data('category'),
                brand: element.data('brand'),
                variant: variant.join(AEC.Const.VARIANT_DELIMITER),
                quantity: qty,
            });
        }

        /**
         * Affiliation attributes
         */
        for (i = 0, l = products.length; i < l; i++) {
            (function (product) {
                jQuery.each(
                    AEC.parseJSON(element.data('attributes')),
                    function (key, value) {
                        product[key] = value;
                    }
                );
            })(products[i]);
        }

        var data = {
            event: customEvent || 'addToCart',
            eventLabel: element.data('name'),
            ecommerce: {
                currencyCode: AEC.currencyCode,
                add: {
                    actionField: {
                        list: element.data('list'),
                    },
                    products: products,
                },
                options: variant_attribute_option,
            },
            eventCallback: function () {
                if (AEC.eventCallback) {
                    if (element.data('event')) {
                        element.trigger(element.data('event'));
                    }

                    if (element.data('click')) {
                        eval(element.data('click'));
                    }
                }
            },
            currentStore: element.data('currentstore'),
        };

        if (AEC.useDefaultValues) {
            data['currentstore'] = AEC.storeName;
        }

        /**
         * Track event
         */
        AEC.Cookie.add(data).push(dataLayer);

        /**
         * Save backreference
         */
        if (AEC.localStorage) {
            (function (products) {
                for (var i = 0, l = products.length; i < l; i++) {
                    AEC.Storage.reference().set({
                        id: products[i].id,
                        category: products[i].category,
                    });
                }
            })(products);
        }

        /**
         * Track time
         */
        if (!customEvent) {
            AEC.Time.track(
                dataLayer,
                AEC.Const.TIMING_CATEGORY_ADD_TO_CART,
                element.data('name'),
                element.data('category')
            );
        }

        if (AEC.facebook) {
            if ('undefined' !== typeof fbq) {
                (function (product, products, fbq) {
                    var content_ids = [],
                        price = 0;

                    for (i = 0, l = products.length; i < l; i++) {
                        content_ids.push(products[i].id);

                        price += parseFloat(products[i].price);
                    }

                    (function (callback) {
                        if (AEC.Const.COOKIE_DIRECTIVE) {
                            AEC.CookieConsent.queue(callback).process();
                        } else {
                            callback.apply(window, []);
                        }
                    })(
                        (function (product, content_ids, price) {
                            return function () {
                                fbq('track', 'AddToCart', {
                                    content_name: product,
                                    content_ids: content_ids,
                                    content_type: 'product',
                                    value: price,
                                    currency: AEC.currencyCode,
                                });
                            };
                        })(product, content_ids, price)
                    );
                })(element.data('name'), products, fbq);
            }
        }

        /**
         * Invoke original click event(s)
         */
        if (element.data('click')) {
            eval(element.data('click'));
        }

        /**
         * Related products
         */
        var related = jQuery('#related-products-field', form);

        if (related.length && related.val()) {
            var selected_ids = related.val().split(',');

            for (var i = 0; i < selected_ids.length; i++) {
                var product = jQuery('#related-checkbox' + selected_ids[i])
                    .parents('.product-item-detail')
                    .find('.product-item-link');

                AEC.ajaxRelated(product, dataLayer);
            }
        }

        return true;
    };

    /**
     * Add method to push related products
     */
    AEC.ajaxRelated = function (context, dataLayer) {
        var element = jQuery(context),
            qty = element.data('qty'),
            variant_attribute_option = [],
            products = [];

        /**
         * Cast quantity to integer
         */
        qty = Math.abs(qty);

        if (isNaN(qty)) {
            qty = 1;
        }

        if (!AEC.gtm()) {
            /**
             * Invoke original click event(s)
             */
            if (element.data('click')) {
                /**
                 * Track time
                 */
                AEC.Time.track(
                    dataLayer,
                    AEC.Const.TIMING_CATEGORY_ADD_TO_CART,
                    element.data('name'),
                    element.data('category')
                );

                eval(element.data('click'));
            }

            return true;
        }

        products.push({
            name: element.data('name'),
            id: element.data('id'),
            price: element.data('price'),
            category: element.data('category'),
            brand: element.data('brand'),
            variant: '',
            quantity: qty,
        });

        /**
         * Affiliation attributes
         */
        for (i = 0, l = products.length; i < l; i++) {
            (function (product) {
                jQuery.each(
                    AEC.parseJSON(element.data('attributes')),
                    function (key, value) {
                        product[key] = value;
                    }
                );
            })(products[i]);
        }

        var data = {
            event: 'addToCart',
            eventLabel: element.data('name'),
            ecommerce: {
                currencyCode: AEC.currencyCode,
                add: {
                    actionField: {
                        list: element.data('list'),
                    },
                    products: products,
                },
                options: variant_attribute_option,
            },
            eventCallback: function () {
                if (AEC.eventCallback) {
                    if (element.data('event')) {
                        element.trigger(element.data('event'));
                    }

                    if (element.data('click')) {
                        eval(element.data('click'));
                    }
                }
            },
            currentStore: element.data('currentstore'),
        };

        if (AEC.useDefaultValues) {
            data['currentstore'] = AEC.storeName;
        }

        /**
         * Track event
         */
        AEC.Cookie.add(data).push(dataLayer);

        /**
         * Save backreference
         */
        if (AEC.localStorage) {
            (function (products) {
                for (var i = 0, l = products.length; i < l; i++) {
                    AEC.Storage.reference().set({
                        id: products[i].id,
                        category: products[i].category,
                    });
                }
            })(products);
        }

        /**
         * Track time
         */
        AEC.Time.track(
            dataLayer,
            AEC.Const.TIMING_CATEGORY_ADD_TO_CART,
            element.data('name'),
            element.data('category')
        );

        if (AEC.facebook) {
            if ('undefined' !== typeof fbq) {
                (function (product, products, fbq) {
                    var content_ids = [],
                        price = 0;

                    for (i = 0, l = products.length; i < l; i++) {
                        content_ids.push(products[i].id);

                        price += parseFloat(products[i].price);
                    }

                    (function (callback) {
                        if (AEC.Const.COOKIE_DIRECTIVE) {
                            AEC.CookieConsent.queue(callback).process();
                        } else {
                            callback.apply(window, []);
                        }
                    })(
                        (function (product, content_ids, price) {
                            return function () {
                                fbq('track', 'AddToCart', {
                                    content_name: product,
                                    content_ids: content_ids,
                                    content_type: 'product',
                                    value: price,
                                    currency: AEC.currencyCode,
                                });
                            };
                        })(product, content_ids, price)
                    );
                })(element.data('name'), products, fbq);
            }
        }

        /**
         * Invoke original click event(s)
         */
        if (element.data('click')) {
            eval(element.data('click'));
        }

        return true;
    };

    AEC.VPV = (function () {
        return {
            /**
             * @param {String} title
             * @param {String} url
             * @returns {void}
             */
            push: function (title, url) {
                if (!AEC.gtm() || !title || !url) {
                    return;
                }

                var dataLayer = dataLayer || [],
                    data = {
                        event: 'VirtualPageView',
                        virtualPageURL: url,
                        virtualPageTitle: title,
                    };

                dataLayer.push(data);
            },
        };
    })();

    return AEC;
})(AEC);
