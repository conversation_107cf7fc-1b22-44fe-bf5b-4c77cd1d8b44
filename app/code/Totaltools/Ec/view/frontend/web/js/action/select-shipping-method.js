/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'mage/utils/wrapper',
    'Totaltools_Ec/js/model/analytics'
], function (wrapper, analytics) {
    'use strict';

    return function (shippingMethod) {
        return wrapper.wrap(shippingMethod, function (original, method) {
            if (
                analytics.shippingProcessed() === false &&
                'undefined' !== typeof AEC &&
                false !== AEC.gtm() &&
                'undefined' !== typeof dataLayer &&
                'undefined' !== typeof AEC.VPV &&
                'undefined' !== typeof AEC.Const.CHECKOUT_STEP_SHIPPING_TITLE
            ) {
                AEC.VPV.push(
                    AEC.Const.CHECKOUT_STEP_SHIPPING_TITLE,
                    AEC.Const.CHECKOUT_STEP_SHIPPING_URL
                );

                analytics.shippingProcessed(true);
            }

            return original(method);
        });
    };
});
