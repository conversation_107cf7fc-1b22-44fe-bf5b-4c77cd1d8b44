/**
 * @description This mixins add addtional attributes to widget directive converter in products element of type grid.
 * @category    Totaltools
 * @package     Totaltools_Ec
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 */

define([
    'mage/utils/wrapper',
    'Magento_PageBuilder/js/mass-converter/widget-directive-abstract',
    'Magento_PageBuilder/js/utils/object'
], function (wrapper, _widgetDirectiveAbstract, _object) {
    'use strict';

    return function (widgetDirective) {
        var _proto = widgetDirective.prototype;

        var _fromDomWrapper = wrapper.wrap(_proto.fromDom, function (fromDomOrig, data, config) {
            var attributes = _widgetDirectiveAbstract.prototype.fromDom.call(this, data, config),
                result = fromDomOrig(data, config),
                data = Object.assign({}, data, result);

            if (attributes['gtm_event']) {
                data.gtm_event = attributes.gtm_event;
            }

            if (attributes['gtm_list']) {
                data.gtm_list = attributes.gtm_list;
            }

            if (attributes['gtm_category']) {
                data.gtm_category = attributes.gtm_category;
            }

            return data;
        });

        var _toDomWrapper = wrapper.wrap(_proto.toDom, function (toDomOrig, data, config) {
            var attributes = {
                type: "Magento\\CatalogWidget\\Block\\Product\\ProductsList",
                template: "Magento_CatalogWidget::product/widget/content/grid.phtml",
                anchor_text: "",
                id_path: "",
                show_pager: 0,
                products_count: data.products_count,
                condition_option: data.condition_option,
                condition_option_value: "",
                type_name: "Catalog Products List",
                conditions_encoded: this.encodeWysiwygCharacters(data.conditions_encoded || "")
              };
        
              if (data.sort_order) {
                attributes.sort_order = data.sort_order;
              }
        
              if (typeof data[data.condition_option] === "string") {
                attributes.condition_option_value = this.encodeWysiwygCharacters(data[data.condition_option]);
              }
        
              if (attributes.conditions_encoded.length === 0) {
                return data;
              }

              if (data['gtm_event']) {
                attributes.gtm_event = data.gtm_event;
            }

            if (data['gtm_list']) {
                attributes.gtm_list = data.gtm_list;
            }

            if (data['gtm_category']) {
                attributes.gtm_category = data.gtm_category;
            }
        
            (0, _object.set)(data, config.html_variable, this.buildDirective(attributes));

            return data;
        });

        _proto.fromDom = _fromDomWrapper;
        _proto.toDom = _toDomWrapper;

        return widgetDirective;
    };
});
