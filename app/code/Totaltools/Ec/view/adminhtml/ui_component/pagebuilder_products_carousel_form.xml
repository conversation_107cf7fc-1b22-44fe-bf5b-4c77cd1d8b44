<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * @category    Totaltools
 * @package     Totaltools_Ec
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 */ 
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="gtm_fieldset" sortOrder="30" component="Magento_PageBuilder/js/form/element/dependent-fieldset">
        <settings>
            <label translate="true">GTM Settings</label>
            <additionalClasses>
                <class name="admin__fieldset-visual-select-large">true</class>
            </additionalClasses>
            <collapsible>true</collapsible>
            <opened>true</opened>
        </settings>
        <field name="gtm_event" sortOrder="100" formElement="input">
            <settings>
                <label translate="true">GTM Event Name</label>
                <additionalClasses>
                    <class name="admin__field-small">true</class>
                </additionalClasses>
                <dataType>text</dataType>
                <dataScope>gtm_event</dataScope>
            </settings>
        </field>
        <field name="gtm_list" sortOrder="110" formElement="input">
            <settings>
                <label translate="true">GTM List Name</label>
                <additionalClasses>
                    <class name="admin__field-small">true</class>
                </additionalClasses>
                <dataType>text</dataType>
                <dataScope>gtm_list</dataScope>
            </settings>
        </field>
        <field name="gtm_category" sortOrder="120" formElement="input">
            <settings>
                <label translate="true">GTM Category Name</label>
                <additionalClasses>
                    <class name="admin__field-small">true</class>
                </additionalClasses>
                <dataType>text</dataType>
                <dataScope>gtm_category</dataScope>
            </settings>
        </field>
    </fieldset>
</form>
