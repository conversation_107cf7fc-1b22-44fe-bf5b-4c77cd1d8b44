<?xml version="1.0"?>
<!--
/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */    
-->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="customer_login">
        <observer name="totaltools_ec_customer_login" instance="Totaltools\Ec\Observer\Emarsys\CustomerLoginObserver" />
    </event>
    <event name="customer_logout">
        <observer name="totaltools_ec_customer_logout" instance="Totaltools\Ec\Observer\Emarsys\CustomerLogoutObserver" />
    </event>
    <event name="checkout_cart_add_product_complete">
        <observer name="totaltools_ec_checkout_cart_add_product_complete" instance="Totaltools\Ec\Observer\Emarsys\CheckoutCartAddProductCompleteObserver" />
    </event>
    <event name="checkout_cart_update_items_after">
        <observer name="totaltools_ec_checkout_cart_update_items_after" instance="Totaltools\Ec\Observer\Emarsys\CheckoutCartUpdateItemsAfterObserver" />
    </event>
    <event name="ec_order_products_product_get_after">
        <observer name="totaltools_ec_add_gst_price_purchased_products" instance="Totaltools\Ec\Observer\AddGstToPurchasedProductsPrice" />
        <observer name="totaltools_ec_add_custom_product_attributes" instance="Totaltools\Ec\Observer\AddCustomProductAttributes" />
    </event>
    <event name="ec_get_purchase_push_after">
        <observer name="totaltools_ec_add_custom_order_attributes" instance="Totaltools\Ec\Observer\AddCustomOrderAttributes" />
    </event>
</config>