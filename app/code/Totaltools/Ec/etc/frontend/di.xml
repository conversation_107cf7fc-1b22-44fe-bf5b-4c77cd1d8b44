<?xml version="1.0"?>
<!--
/**
 * @category Totaltools
 * @package Totaltools_Ec
 * <AUTHOR> Dev
 * @copyright Copyright (c) 2019 Totaltools <https://www.totaltools.com.au>
 */
 -->

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Class Overrides-->
    <preference for="Anowave\Ec\Block\Plugin" type="Totaltools\Ec\Preference\Plugin" />
    <preference for="Totaltools\Bundle\Controller\Cart\AddRewrite" type="Totaltools\Ec\Preference\AddRewrite" />
    <preference for="Anowave\Ec\Helper\Data" type="Totaltools\Ec\Helper\Data" />
    <preference for="Anowave\Ec\Model\Cache" type="Totaltools\Ec\Preference\Model\Cache"/>

    <!-- Plugins -->
    <type name="Anowave\Ec\Helper\Data">
        <plugin name="totaltools_ec_helper_data" type="Totaltools\Ec\Plugin\Helper\Data" sortOrder="10"/>
    </type>
    <type name="Magento\Quote\Api\Data\CartInterface">
        <plugin name="totaltools_ec_emarsys_cart_interface_plugin" type="Totaltools\Ec\Plugin\Model\CartInterfacePlugin" sortOrder="10"/>
    </type>
    <type name="Magento\Quote\Model\Quote">
        <plugin name="totaltools_ec_emarsys_quote_plugin" type="Totaltools\Ec\Plugin\Emarsys\Model\QuotePlugin" sortOrder="20"/>
    </type>
    <type name="Magento\Checkout\Model\Session">
        <plugin name="totaltools_ec_emarsys_session_plugin" type="Totaltools\Ec\Plugin\Emarsys\Model\CheckoutSessionPlugin" sortOrder="10" />
    </type>
    <type name="Anowave\Ec\Preference\UpdateItemQty">
        <plugin name="Totaltools_Ec::UpdateItemQtyPlugin" type="Totaltools\Ec\Plugin\UpdateItemQtyPlugin" sortOrder="10" />
    </type>
</config>
