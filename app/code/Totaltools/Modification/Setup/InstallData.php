<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Data
 */


namespace Totaltools\Modification\Setup;

use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Eav\Setup\EavSetupFactory;
use Totaltools\Data\Setup\ProductAttribute;

class InstallData implements InstallDataInterface
{
    /**
     * EAV setup factory
     *
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var \Totaltools\Data\Setup\ProductAttribute
     */
    protected $productAttribute;


    /**
     * @var \Totaltools\Data\Setup\Attributes\Combo
     */
    protected $comboAttributes;

    /**
     * @var \Magento\Config\Model\ResourceModel\Config
     */
    protected $resourceConfig;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Init
     *
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        \Magento\Framework\Logger\Monolog $logger,
        \Magento\Config\Model\ResourceModel\Config $resourceConfig
    )
    {
        $this->logger = $logger;
        $this->resourceConfig = $resourceConfig;
    }

    public function install(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $installer = $setup;

        $installer->startSetup();

        $this->updateWelcomeText();

        $installer->endSetup();
    }

    public function updateWelcomeText()
    {
        $this->resourceConfig->saveConfig('design/header/welcome', 'Welcome to Total Tools!', 'default', 0);
    }
}
