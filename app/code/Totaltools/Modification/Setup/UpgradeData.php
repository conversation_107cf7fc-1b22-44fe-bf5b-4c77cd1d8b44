<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Modification\Setup;

use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Eav\Setup\EavSetupFactory;
use Totaltools\Data\Setup\ProductAttribute;
use Totaltools\Data\Setup\AttributeSet;
use Magento\Framework\ObjectManagerInterface;
use Magento\Framework\App\State;
use Magento\Backend\App\Area\FrontNameResolver;

/**
 * Upgrade Data script
 * @codeCoverageIgnore
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var \Magento\Config\Model\ResourceModel\Config
     */
    protected $resourceConfig;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Init
     *
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        \Magento\Framework\Logger\Monolog $logger,
        \Magento\Config\Model\ResourceModel\Config $resourceConfig)
    {
        $this->logger = $logger;
        $this->resourceConfig = $resourceConfig;
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        $version = $context->getVersion();

        $this->showCaptchaOnLogin();

        $setup->endSetup();
    }

    public function showCaptchaOnLogin()
    {
        $this->resourceConfig->saveConfig('customer/captcha/forms', 'user_create,user_login,user_forgotpassword,guest_checkout,register_during_checkout,contact_us', 'default', 0);
        $this->resourceConfig->saveConfig('customer/captcha/mode', 'always', 'default', 0);
    }
}
