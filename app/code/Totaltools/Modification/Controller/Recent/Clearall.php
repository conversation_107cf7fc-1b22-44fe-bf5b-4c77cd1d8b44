<?php
namespace Totaltools\Modification\Controller\Recent;

use Magento\Framework\App\Action\Context;
use Symfony\Component\Config\Definition\Exception\Exception;
use Magento\Framework\Controller\ResultFactory;
/**
 * clear all visitor logs
 * */
class Clearall extends \Magento\Framework\App\Action\Action
{
    /**
     * Store manage model
     *
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * viewed factory model
     * @var \Magento\Reports\Model\Product\Index\ViewedFactory
     * */
    protected $viewedProduct;

    /**
     * customer session model
     *
     * @var \Magento\Customer\Model\Session
     */
    protected $customerSession;

    /**
     * visitor model
     *
     * @var \Magento\Customer\Model\Visitor
     */
    protected $visitor;

    /**
     * Action constructor.
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Reports\Model\Product\Index\ViewedFactory $viewed
     * @param \Magento\Customer\Model\Session $session
     * @param \Magento\Customer\Model\Visitor $visitor
     */
    public function __construct(Context $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Reports\Model\Product\Index\ViewedFactory $viewed,
        \Magento\Customer\Model\Session $session,
        \Magento\Customer\Model\Visitor $visitor
    )
    {
        $this->storeManager = $storeManager;
        $this->viewedProduct = $viewed;
        $this->customerSession = $session;
        $this->visitor = $visitor;
        return parent::__construct($context);
    }

    /**
     * execute action.
     *
     */
    public function execute()
    {
        $storeManager = $this->storeManager;
        $customerSession = $this->customerSession;
        $customerVisitor = $this->visitor;

        $productId = $this->getRequest()->getParam('productId');

        $storeId  = $storeManager->getStore()->getId();
        $orWhere = [];
        $where = [];

        if ($productId) {
            $where[] = 'product_id = '.$productId;
        }

        if ($customerSession->isLoggedIn()) {
            $customerId = $customerSession->getCustomerId();
            $orWhere[] = 'customer_id = '.$customerId;
        } else {
            $visitorId = $customerVisitor->getId();
            $orWhere[] = 'visitor_id = '.$visitorId;
        }

        $orWhere = implode($orWhere, ' OR ');
        if ($orWhere) {
            $where[] = $orWhere;
        }

        $where[] = 'store_id = '. $storeId;
        $where = implode($where, ' AND ');

        $redirect = $this->getRequest()->get('redirect');

        if ($redirect) {
            $redirect = base64_decode($redirect);
        }

        try {
            $viewedProduct = $this->viewedProduct->create();
            $viewedConnection = $viewedProduct->getResourceCollection()->getConnection();
            $tableName =  $viewedConnection->getTableName('report_viewed_product_index');

            $viewedConnection->delete($tableName, $where);

            $this->messageManager->addSuccess(__('You have clean the recently products.'));
        } catch(Exception $e) {
            $this->messageManager->addError($e->getMessage());
        }

        if ($redirect) {
            return $this->resultRedirectFactory->create()->setUrl($redirect);
        } else {
            $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
            return $resultPage;
        }
    }
}