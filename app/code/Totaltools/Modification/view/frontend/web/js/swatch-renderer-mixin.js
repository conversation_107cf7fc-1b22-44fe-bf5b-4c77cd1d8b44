/**
 *
 * A mixin to override default behavior of swatch renderer
 *
 */
define([
    "jquery",
    "jquery-ui-modules/widget",
    "mage/mage"
], function($) {
    "use strict";

    return function(widget) {
        $.widget("mage.SwatchRenderer", widget, {
            /**
             * Get default options values settings with either URL query parameters
             * @private
             */
            _getSelectedAttributes: function () {
                var hashIndex = window.location.href.indexOf('#'),
                    selectedAttributes = {},
                    params;
                if (hashIndex !== -1) {
                    params = $.parseQuery(window.location.href.substr(hashIndex + 1));

                    selectedAttributes = _.invert(_.mapObject(_.invert(params), function (attributeId) {
                        var attribute = false;
                        if (this.options.jsonConfig.mappedAttributes !== undefined && this.options.jsonConfig.mappedAttributes.length != 0) {
                            attribute = this.options.jsonConfig.mappedAttributes[attributeId];
                        }

                        return attribute ? attribute.code : attributeId;
                    }.bind(this)));
                }

                return selectedAttributes;
            }
        });

        return $.mage.SwatchRenderer;
    };
});

