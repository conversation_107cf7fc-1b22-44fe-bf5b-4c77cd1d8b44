<?php
/**
 * <AUTHOR> Team
 * @copyright Copyright (c) 2018 Amasty (https://www.amasty.com)
 * @package Amasty_Label
 */


namespace Totaltools\Modification\Model;

use Amasty\Label\Api\Data\LabelInterface;
use Amasty\Label\Model\Labels;
use Amasty\Label\Model\LabelsDataProvider;
use Amasty\Label\Model\ResourceModel\Labels\CollectionFactory;
use Magento\Catalog\Model\Product;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;
use Magento\Customer\Model\Session;
use Magento\Framework\Profiler;
use Magento\GroupedProduct\Model\Product\Type\Grouped;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

class LabelViewer extends \Amasty\Label\Model\LabelViewer
{
    /**
     * @var TimezoneInterface
     */
    protected $localeDate;

    /**
     * @var \Magento\Framework\View\LayoutFactory
     */
    private $layoutFactory;

    /**
     * @var Configurable
     */
    private $productTypeConfigurable;

    /**
     * @var CollectionFactory
     */
    private $labelCollectionFactory;

    /**
     * @var \Amasty\Base\Model\Serializer
     */
    private $serializer;

    /**
     * @var Session
     */
    private $customerSession;

    /**
     * @var \Amasty\Label\Model\ResourceModel\Index
     */
    private $labelIndex;

    /**
     * @var \Amasty\Label\Helper\Config
     */
    private $config;

    /**
     * @var \Magento\Framework\View\Asset\Repository
     */
    private $assetRepo;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    protected $store;
    /**
     * LabelViewer constructor.
     *
     * @param \Magento\Framework\View\LayoutInterface $layoutFactory
     * @param Configurable $catalogProductTypeConfigurable
     * @param CollectionFactory $labelCollectionFactory
     * @param \Amasty\Base\Model\Serializer $serializer
     * @param Session $customerSession
     * @param \Amasty\Label\Model\ResourceModel\Index $labelIndex
     * @param \Amasty\Label\Helper\Config $config
     * @param TimezoneInterface $localeDate
     * @param \Magento\Framework\View\Asset\Repository $assetRepo
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Framework\View\LayoutInterface $layoutFactory,
        Configurable $catalogProductTypeConfigurable,
        CollectionFactory $labelCollectionFactory,
        \Amasty\Base\Model\Serializer $serializer,
        Session $customerSession,
        \Amasty\Label\Model\ResourceModel\Index $labelIndex,
        \Amasty\Label\Helper\Config $config,
        TimezoneInterface $localeDate,
        \Magento\Framework\View\Asset\Repository $assetRepo,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Store\Model\StoreManagerInterface $store,
        LabelsDataProvider $labelsProvider
    ) {
        parent::__construct(
            $layoutFactory,
            $catalogProductTypeConfigurable,
            $serializer,
            $customerSession,
            $config,
            $labelsProvider
            );
        $this->layoutFactory = $layoutFactory;
        $this->productTypeConfigurable = $catalogProductTypeConfigurable;
        $this->labelCollectionFactory = $labelCollectionFactory;
        $this->serializer = $serializer;
        $this->customerSession = $customerSession;
        $this->labelIndex = $labelIndex;
        $this->config = $config;
        $this->localeDate = $localeDate;
        $this->assetRepo = $assetRepo;
        $this->logger = $logger;
        $this->store = $store;
    }

    /**
     * @param Product $product
     * @param string $mode
     * @param bool $shouldMove
     *
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function renderProductLabel(Product $product, $mode = 'category', $shouldMove = false)
    {
        $item = [];
        $labelItems = [];
        $labelNewItems = [];
        $appliedLabelIds = [];
        $amastyLabels = [];
        $amastySearchResultsTop = [];
        $amastySearchResultsBottom = [];
        $applied = false;

        try {
            $isNewProduct = $this->isNewProduct($product);
            if ($isNewProduct) {
                $mediaUrl = $this->store->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
                $newImage = $mediaUrl.'/amasty/default/new_label.png';
                $item['label_image'] = $newImage;
                $item['label_pos'] = 'cat';
                $item['label_style'] = '';
                $item['new_flag'] = 1;
                $labelNewItems[] = $item;
            }

            foreach ($this->getCollection($product->getId(), $product->getStoreId()) as $label) {
                if ($this->validateNonProductDependConditions($label, $applied)) {
                    continue;
                }

                $applied = true;
                $label->setShouldMove($shouldMove);
                $label->init($product, $mode);
                $status = (int)$label->getSyncToAlgolia();
                $newStatus = (int)$label->getIsNewLabel();
                if($status === 1) {
                    if ($newStatus !== 1) {
                        $categoryPosition = $label->getCatPos();
                        $item['label_image'] = $this->config->getImageUrl($label->getCatImg());
                        $item['label_pos'] = $label->getCatPos();
                        $item['label_style'] = $label->getCatStyle();
                        $item['new_flag'] = (int)$label->getIsNewLabel();
                        $appliedLabelIds[] = $label->getId();
                        $labelItems[] = $item;
                        if ($categoryPosition === 0) {
                            $amastySearchResultsTop[] = $item;
                        } else {
                            $amastySearchResultsBottom[] = $item;
                        }

                    } else {
                        $categoryPosition = $label->getCatPos();
                        $item['label_image'] = $this->config->getImageUrl($label->getCatImg());
                        $item['label_pos'] = $label->getCatPos();
                        $item['label_style'] = $label->getCatStyle();
                        $item['new_flag'] = (int)$label->getIsNewLabel();
                        $appliedLabelIds[] = $label->getId();
                        if ($categoryPosition === 0) {
                            $amastySearchResultsTop[] = $item;
                        } else {
                            $amastySearchResultsBottom[] = $item;
                        }
                    }
                }
            }

            /* apply label from child products*/
            if (in_array($product->getTypeId(), [Grouped::TYPE_CODE, Configurable::TYPE_CODE])
                && $this->isLabelForParentEnabled($product->getStoreId())
            ) {
                $usedProds = $this->getUsedProducts($product);
                foreach ($usedProds as $child) {
                    foreach ($this->getCollection($child->getId(), $child->getStoreId()) as $label) {
                        /** @var Labels $label */
                        if (!$label->getUseForParent()
                            || $this->validateNonProductDependConditions($label, $applied)
                        ) {
                            continue;
                        }
                        /* apply label only one time(remove duplicated for child products */
                        if (in_array($label->getId(), $appliedLabelIds)) {
                            continue;
                        }

                        $applied = true;
                        $label->setShouldMove($shouldMove);
                        $label->init($child, $mode, $product);
                        $status = (int)$label->getSyncToAlgolia();
                        $newStatus = (int)$label->getIsNewLabel();
                        if($status === 1) {
                            if ($newStatus !== 1) {
                                if ($label->getCatStyle()) {
                                    $item['label_image'] = $this->config->getImageUrl($label->getCatImg());
                                }
                                $item['label_pos'] = $label->getCatPos();
                                $categoryPosition = $label->getCatPos();
                                $item['label_style'] = $label->getCatStyle();
                                $item['new_flag'] = (int)$label->getIsNewLabel();
                                $appliedLabelIds[] = $label->getId();
                                $labelItems[] = $item;
                                if ($categoryPosition === 0) {
                                    $amastySearchResultsTop[] = $item;
                                } else {
                                    $amastySearchResultsBottom[] = $item;
                                }
                            } else {
                                $item['label_image'] = $this->config->getImageUrl($label->getCatImg());
                                $item['label_pos'] = $label->getCatPos();
                                $categoryPosition = $label->getCatPos();
                                $item['label_style'] = $label->getCatStyle();
                                $item['new_flag'] = (int)$label->getIsNewLabel();
                                $appliedLabelIds[] = $label->getId();
                                if ($categoryPosition === 0) {
                                    $amastySearchResultsTop[] = $item;
                                } else {
                                    $amastySearchResultsBottom[] = $item;
                                }
                            }
                        }
                    }
                }
            }
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage());
            $this->logger->error($exception->getTraceAsString());
        }
        $amastyLabels['amasty_other_labels']  = $labelItems;
        $amastyLabels['amasty_new_labels']    = $labelNewItems;
        $amastyLabels['amasty_labels_top']    = $amastySearchResultsTop;
        $amastyLabels['amasty_labels_bottom'] = $amastySearchResultsBottom;

        return $amastyLabels;
    }

    /**
     * @param $product
     * @return bool
     */
    private function isNewProduct($product)
    {
        /** @var \Magento\Catalog\Model\ResourceModel\Product $resource */
        $resource = $product->getResource();
        $newsFromDate = $resource->getAttributeRawValue($product->getId(), 'news_from_date', $product->getStoreId());
        $newsToDate   = $resource->getAttributeRawValue($product->getId(), 'news_to_date', $product->getStoreId());

        if (empty($newsFromDate)) {
            $newsFromDate = null;
        }
        if (empty($newsToDate)) {
            $newsToDate = null;
        }
        if (!$newsFromDate && !$newsToDate) {
            return false;
        }
        if (!$newsFromDate && !$newsToDate) {
            return false;
        }

        return $this->localeDate->isScopeDateInInterval(
            $product->getStore(),
            $newsFromDate,
            $newsToDate
        );
    }

    /**
     * @param \Amasty\Label\Model\Labels $label
     * @param bool $applied
     * @return bool
     */
    private function validateNonProductDependConditions(Labels $label, &$applied)
    {
        if ($label->getIsSingle() === '1' && $applied) {
            return true;
        }

        // need this condition, because in_array returns true for NOT LOGGED IN customers
        if ($label->getCustomerGroupEnabled()
            && !$this->checkCustomerGroupCondition($label)
        ) {
            return true;
        }

        if (!$label->checkDateRange()) {
            return true;
        }

        return false;
    }

    /**
     * if anyone label has setting - UseForParent - check all
     * @param int $storeId
     * @return bool
     */
    private function isLabelForParentEnabled($storeId)
    {
        $collection = $this->labelCollectionFactory->create()
            ->addActiveFilter()
            ->addFieldToFilter('stores', ['like' => '%'.$storeId.'%'])
            ->addFieldToFilter(LabelInterface::USE_FOR_PARENT, 1);

        return $collection->getSize() ? true : false;
    }

    /**
     * @param Labels $label
     * @return bool
     */
    private function checkCustomerGroupCondition(Labels $label)
    {
        $groups = $label->getData('customer_group_ids');
        if ($groups === '') {
            return true;
        }
        $groups = $this->serializer->unserialize($groups);

        return in_array(
            (int)$this->customerSession->getCustomerGroupId(),
            $groups
        );
    }

    /*
     * generate block with label configuration
     * @param \Amasty\Label\Model\Labels $label
     * @return string
     */
    private function generateHtml(Labels $label)
    {
        $layout = $this->layoutFactory->create();
        $block = $layout->createBlock(
            \Amasty\Label\Block\Label::class,
            'amasty.label',
            ['data' => ['label' => $label]]
        );
        $html = $block->setLabel($label)->toHtml();

        return $html;
    }

    /**
     * @param int $productId
     * @param int $storeId
     * @return $mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function getCollection($productId, $storeId)
    {
        $labelIds = $this->labelIndex->getIdsFromIndex($productId, $storeId);
        if (!count($labelIds)) {
            return [];
        }

        $collection = $this->labelCollectionFactory->create()
            ->addActiveFilter()
            ->addFieldToFilter(LabelInterface::LABEL_ID, $labelIds)
            ->setOrder('pos', 'asc');

        return $collection;
    }

    /**
     * @param Product $product
     * @return array|\Magento\Catalog\Api\Data\ProductInterface[]
     */
    private function getUsedProducts(Product $product)
    {
        if ($product->getTypeId() === Configurable::TYPE_CODE) {
            return $this->productTypeConfigurable->getUsedProducts($product);
        } else { // product is grouped
            return $product->getTypeInstance(true)->getAssociatedProducts($product);
        }
    }

    /**
     * @param Product $product
     * @param string $mode
     * @param bool $shouldMove
     * @return string
     */
    public function renderProductLabelWithoutIndex(Product $product, $mode = 'category', $shouldMove = false)
    {
        $html = '';
        $appliedLabelIds = [];
        $applied = false;

        Profiler::start('__RenderAmastyProductLabel__');
        foreach ($this->getNonIndexCollection($product->getStoreId()) as $label) {
            /** @var Labels $label */
            if ($this->validateNonProductDependConditions($label, $applied)) {
                continue;
            }

            $label->setShouldMove($shouldMove);
            $label->init($product, $mode);

            if ($label->isApplicable()) {
                $applied = true;
                $appliedLabelIds[] = $label->getId();
                $html .= $this->generateHtml($label);
            } elseif ($label->getUseForParent()
                && ($product->getTypeId() === 'configurable' || $product->getTypeId() === 'grouped')
            ) {
                $usedProds = $this->getUsedProducts($product);
                foreach ($usedProds as $child) {
                    $label->init($child, $mode, $product);
                    if ($label->isApplicable()) {
                        $applied = true;
                        $html .= $this->generateHtml($label);
                        break;
                    }
                }
            }
        }
        Profiler::stop('__RenderAmastyProductLabel__');

        return $html;
    }

    /**
     * @param int $storeId
     * @return $this
     */
    private function getNonIndexCollection($storeId)
    {
        $collection = $this->labelCollectionFactory->create()
            ->addActiveFilter()
            ->addFieldToFilter('stores', ['like' => '%'.$storeId.'%'])
            ->setOrder('pos', 'asc');

        return $collection;
    }
}
