<?php
/**
 * InsiderMember
 *
 * @category  Totaltools
 * @package   Totaltools_LoyaltyGraphQL
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\LoyaltyGraphQL\Model\Resolver;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Totaltools\Loyalty\Model\InstoreOrder as InstoreOrderModel;
use Magento\CustomerGraphQl\Model\Customer\GetCustomer;
use Magento\Framework\GraphQl\Exception\GraphQlAuthorizationException;
use Magento\GraphQl\Model\Query\ContextInterface;


class InstoreOrderDetails implements ResolverInterface
{
    /**
     * @var InstoreOrderModel
     */
    private $instoreOrderModel;

     /**
     * @var GetCustomer
     */
    private $getCustomer;

    /**
     * @param GetCustomer $getCustomer
     */
    public function __construct(
        InstoreOrderModel $instoreOrderModel,
        GetCustomer $getCustomer
    ) {
        $this->instoreOrderModel = $instoreOrderModel;
        $this->getCustomer = $getCustomer;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        /** @var ContextInterface $context */
        if (false === $context->getExtensionAttributes()->getIsCustomer()) {
            throw new GraphQlAuthorizationException(__('The current customer isn\'t authorized.'));
        }
        $customer = $this->getCustomer->execute($context);
        $order_id = $args['order_id']??[];
        $instorOrderData = $this->instoreOrderModel->getCustomerInstoreOrders($customer, $order_id);         
        return  $instorOrderData;
    }
}

