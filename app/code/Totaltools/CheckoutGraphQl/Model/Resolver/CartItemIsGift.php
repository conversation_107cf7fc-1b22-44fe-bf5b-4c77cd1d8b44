<?php

namespace Totaltools\CheckoutGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\Quote\Model\Quote\ItemFactory;

class CartItemIsGift implements ResolverInterface
{
    /**
     * @var ItemFactory
     */
    private $itemFactory;

    /**
     * @param ItemFactory $itemFactory
     */
    public function __construct(ItemFactory $itemFactory)
    {
        $this->itemFactory = $itemFactory;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        \Magento\Framework\GraphQl\Config\Element\Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        if (isset($value['model']) && $value['model'] instanceof \Magento\Quote\Model\Quote\Item) {
            $item = $value['model'];

            // Custom logic to determine if the product is a gift product
            $isGift = false;
            
            if ($item->getProduct()->getCustomAttribute('is_gift') && $item->getProduct()->getCustomAttribute('is_gift')->getValue()) {
                $isGift = true;
            }

            return $isGift;
        }

        return false;
    }
}
