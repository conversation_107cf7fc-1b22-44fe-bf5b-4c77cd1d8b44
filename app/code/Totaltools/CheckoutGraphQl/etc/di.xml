<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <type name="Totaltools\CheckoutGraphQl\Model\Resolver\Cart\PayLaterBlock">
        <arguments>
            <argument name="scopeConfig" xsi:type="object">Magento\Framework\App\Config\ScopeConfigInterface</argument>

            <argument name="storeManager" xsi:type="object">Magento\Store\Model\StoreManagerInterface</argument>

            <argument name="designInterface" xsi:type="object">Magento\Framework\View\DesignInterface</argument>

            <argument name="view" xsi:type="object">Magento\Framework\App\ViewInterface</argument>

            <argument name="layout" xsi:type="object">Magento\Framework\View\LayoutInterface</argument>

            <argument name="cartRepository" xsi:type="object">Magento\Quote\Api\CartRepositoryInterface</argument>

            <argument name="checkoutSession" xsi:type="object">Magento\Checkout\Model\Session</argument>

            <argument name="maskedQuoteIdToQuoteId" xsi:type="object">Magento\Quote\Model\MaskedQuoteIdToQuoteIdInterface</argument>

            <argument name="viewModel" xsi:type="object">Afterpay\Afterpay\ViewModel\Container\Cta\Cta</argument>
        </arguments>
    </type>

</config>
