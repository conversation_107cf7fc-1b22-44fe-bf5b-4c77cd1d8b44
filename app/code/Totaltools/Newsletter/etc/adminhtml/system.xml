<?xml version="1.0"  ?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="../../../Backend/etc/system_file.xsd">
    <system>
        <section id="newsletter">
            <group id="subscription">
                <field id="disable_newsletter_success" translate="label" type="select" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Disable Newsletter Confirmation Success Email</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="disable_newsletter_unsubscribe" translate="label" type="select" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Disable Newsletter Unsubscription Confirmation Email</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[If set to yes, Customer will not receive unsubscription from newsletter email]]></comment>
                </field>
            </group>
        </section>
    </system>
</config>