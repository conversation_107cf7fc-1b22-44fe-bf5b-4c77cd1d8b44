<?php
/**
 * @package     Totaltools_Newsletter
 * <AUTHOR> <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Newsletter\Plugin\Newsletter\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;

class Subscriber
{
    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * Subscriber constructor.
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param \Magento\Newsletter\Model\Subscriber $subject
     */
    public function beforeSendConfirmationSuccessEmail(\Magento\Newsletter\Model\Subscriber $subject)
    {
        if ($this->scopeConfig->getValue(
            'newsletter/subscription/disable_newsletter_success',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        )) {
            $subject->setImportMode(true);
        }
    }

    /**
     * @param \Magento\Newsletter\Model\Subscriber $subject
     */
    public function beforeSendUnsubscriptionEmail(\Magento\Newsletter\Model\Subscriber $subject)
    {
        if ($this->scopeConfig->getValue(
            'newsletter/subscription/disable_newsletter_unsubscribe',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        )) {
            $subject->setImportMode(true);
        }
    }
}
