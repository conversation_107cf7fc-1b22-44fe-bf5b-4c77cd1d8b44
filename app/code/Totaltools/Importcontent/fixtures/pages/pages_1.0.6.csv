"title","page_layout","meta_keywords","meta_description","identifier","content_heading","content","is_active","sort_order","layout_update_xml","custom_theme","custom_root_template","custom_layout_update_xml","custom_theme_from","custom_theme_to"
"404 Not Found","1column",,,"no-route","opps! we can’t seem to find the page you are looking for","<p><strong>Error code: 404</strong></p>
<p>Here are some helpful links instead:</p>
<ul>
<li><a href=""{{store url=""""}}"">Home</a></li>
<li><a href=""{{store url=""customer/account/login""}}"">Login</a> <span>/</span> <a href=""/customer/account/create"">Create Account</a></li>
<li><a href=""{{store url=""franchise-opportunities""}}"">Franchise Opportunities</a></li>
<li><a href=""{{store url=""about-us""}}"">About Us</a></li>
<li><a href=""{{store url=""faq""}}"">FAQ&rsquo;s</a></li>
</ul>",1,,,,,,,
"franchise-opportunities","2columns-left",,,"franchise-opportunities",,"<div class=""wrapper wrap-cms"">
<h1 class=""title title-cms"">FRANCHISE OPPORTUNITIES</h1>
<div class=""banner"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_banner""}}</div>
<div class=""content"">
<div class=""content-main"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_content""}}</div>
<div class=""content-right"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_sidebar""}}</div>
</div>
</div>",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
