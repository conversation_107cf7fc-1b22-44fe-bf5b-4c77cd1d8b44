title,page_layout,meta_keywords,meta_description,identifier,content_heading,content,is_active,sort_order,layout_update_xml,custom_theme,custom_root_template,custom_layout_update_xml,custom_theme_from,custom_theme_to
franchise-opportunies,2columns-left,,,franchise-opportunies,,"<div class=""wrapper wrap-cms"">
<h1 class=""title title-cms"">FRANCHISE OPPORTUNITIES</h1>
<div class=""banner"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_banner""}}</div>
<div class=""content"">
<div class=""content-main"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_content""}}</div>
<div class=""content-right"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_sidebar""}}</div>
</div>
</div>
",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\\Cms\\Block\\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
apprentice,2columns-left,,,apprentice,,"<div class=""wrapper wrap-cms"">
<h1 class=""title title-cms"">APPRENTICE</h1>
<div class=""banner"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_banner""}}</div>
<div class=""content"">
<div class=""content-main"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_content""}}</div>
<div class=""content-right"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_sidebar""}}</div>
</div>
</div>
",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\\Cms\\Block\\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
