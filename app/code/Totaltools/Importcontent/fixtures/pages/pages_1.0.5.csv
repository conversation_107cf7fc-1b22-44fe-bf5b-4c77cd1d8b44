"title","page_layout","meta_keywords","meta_description","identifier","content_heading","content","is_active","sort_order","layout_update_xml","custom_theme","custom_root_template","custom_layout_update_xml","custom_theme_from","custom_theme_to"
"Delivery Infomation","2columns-left",,,"delivery-information","Delivery Infomation","Delivery Infomation",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Returns Policy","2columns-left",,,"returns-exchanges-policy","Returns Policy","Returns Policy",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"FAQ","2columns-left",,,"faq","FAQ","FAQ",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Terms & Conditions","2columns-left",,,"terms-conditions","Terms & Conditions","Terms & Conditions",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Franchising","2columns-left",,,"franchising","Franchising","<div class=""wrapper wrap-cms"">
<h1 class=""title title-cms"">FRANCHISE OPPORTUNITIES</h1>
<div class=""banner"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_banner""}}</div>
<div class=""content"">
<div class=""content-main"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_content""}}</div>
<div class=""content-right"">{{block class=""Magento\\Cms\\Block\\Block"" block_id=""franchise_sidebar""}}</div>
</div>
</div>",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Privacy Policy","2columns-left",,,"privacy-policy","Privacy Policy","Privacy Policy",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Buy Gift Cards","2columns-left",,,"gift-cards","Buy Gift Cards","Buy Gift Cards",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"About Us","2columns-left",,,"about-us","About Us","About Us",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Careers","2columns-left",,,"careers","Careers","Careers",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Warranty & Returns","2columns-left",,,"warranty-returns","Warranty & Returns","Warranty & Returns",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Customer Service","2columns-left",,,"customer-service","Customer Service","Customer Service",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
"Sponsorships & Community","2columns-left",,,"sponsorships-community","Sponsorships & Community","Sponsorships & Community",1,,"<referenceContainer name=""catalog.compare.sidebar"" remove=""true""/>
<referenceContainer name=""wishlist_sidebar"" remove=""true""/>

<referenceContainer name=""sidebar.main"">
    <block class=""Magento\Cms\Block\Block"" name=""cms_nav"">
        <arguments>
            <argument name=""block_id"" xsi:type=""string"">cms_nav</argument>
        </arguments>
    </block>
</referenceContainer>
",,,,,
