<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Importcontent\Setup;

use Magento\Framework\Setup;

class Installer implements InstallerInterface
{
    /**
     * @var \Totaltools\Importcontent\Model\Page
     */
    private $page;

    /**
     * @var \Totaltools\Importcontent\Model\Block
     */
    private $block;
	
	/**
     * @var \Totaltools\Importcontent\Model\Box
     */
    private $box;

    /**
     * @param \Totaltools\Importcontent\Model\Page $page
     * @param \Totaltools\Importcontent\Model\Block $block
     * @param \Totaltools\Importcontent\Model\Box $block
     */
    public function __construct(
        \Totaltools\Importcontent\Model\Page $page,
        \Totaltools\Importcontent\Model\Block $block,
        \Totaltools\Importcontent\Model\Box $box
    ) {
        $this->page = $page;
        $this->block = $block;
        $this->box = $box;
    }

    /**
     * {@inheritdoc}
     */
    public function install()
    {
        $this->page->install(['Totaltools_Importcontent::fixtures/pages/pages.csv']);
        $this->block->install(['Totaltools_Importcontent::fixtures/blocks/static_blocks.csv']);
        $this->box->install(['Totaltools_Importcontent::fixtures/box/box.csv']);
    }
}