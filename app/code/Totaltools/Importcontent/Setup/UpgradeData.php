<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Importcontent\Setup;

use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

/**
 * Upgrade Data script
 * @codeCoverageIgnore
 */
class UpgradeData implements UpgradeDataInterface
{
	/**
     * @var \Totaltools\Importcontent\Model\Page
     */
    private $page;

    /**
     * @var \Totaltools\Importcontent\Model\Block
     */
    private $block;
	
	/**
     * @var \Totaltools\Importcontent\Model\Box
     */
    private $box;
	
    /**
     * Init
     *
     * @param CategorySetupFactory $categorySetupFactory
     */
    public function __construct(
		\Totaltools\Importcontent\Model\Page $page,
        \Totaltools\Importcontent\Model\Block $block,
        \Totaltools\Importcontent\Model\Box $box
	){
        $this->page = $page;
        $this->block = $block;
        $this->box = $box;
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
		
		// Update for version 1.0.1
        if ($context->getVersion() && (version_compare($context->getVersion(), '1.0.2') < 0)) {
			// Import cms static block
			$this->block->install(['Totaltools_Importcontent::fixtures/blocks/static_blocks_1.0.1.csv']);
        }

        // Update for version 1.0.2
        if ($context->getVersion() && (version_compare($context->getVersion(), '1.0.3') < 0)) {
            // Import box
            $this->box->install(['Totaltools_Importcontent::fixtures/box/box_1.0.2.csv']);
        }
        // Update for version 1.0.3
        if ($context->getVersion() && (version_compare($context->getVersion(), '1.0.4') < 0)) {
            // Import cms static block
            $this->block->install(['Totaltools_Importcontent::fixtures/blocks/static_blocks_1.0.3.csv']);
        }

        // Update for version 1.0.4; Update static block
        if ($context->getVersion() && (version_compare($context->getVersion(), '1.0.5') < 0)) {
            // Import cms static block
            $this->block->install(['Totaltools_Importcontent::fixtures/blocks/static_blocks_1.0.4.csv']);
            // Import Cms page
            $this->page->install(['Totaltools_Importcontent::fixtures/pages/pages_1.0.4.csv']);
        }

        // Update for version 1.0.5
        if ($context->getVersion() && (version_compare($context->getVersion(), '1.0.6') < 0)) {
            // Import Cms page
            $this->page->install(['Totaltools_Importcontent::fixtures/pages/pages_1.0.5.csv']);
        }

        // Update for version 1.0.6
        if ($context->getVersion() && (version_compare($context->getVersion(), '1.0.7') < 0)) {
            // Import Cms page
            $this->page->install(['Totaltools_Importcontent::fixtures/pages/pages_1.0.6.csv']);

            // Import cms static block
            $this->block->install(['Totaltools_Importcontent::fixtures/blocks/static_blocks_1.0.6.csv']);

            // Import box
            $this->box->install(['Totaltools_Importcontent::fixtures/box/box_1.0.6.csv']);
        }

        // Update for version 1.0.7
        if ($context->getVersion() && (version_compare($context->getVersion(), '1.0.8') < 0)) {
            // Import cms static block
            $this->block->install(['Totaltools_Importcontent::fixtures/blocks/static_blocks_1.0.7.csv']);
        }

        // Update for version 1.0.7
        if ($context->getVersion() && (version_compare($context->getVersion(), '1.0.9') < 0)) {
            // Import cms static block
            $this->block->install(['Totaltools_Importcontent::fixtures/blocks/static_blocks_1.0.8.csv']);
        }

        $setup->endSetup();
    }
}
