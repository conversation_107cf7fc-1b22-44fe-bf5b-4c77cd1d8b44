<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Importcontent\Model;

/**
 * Class Box
 */
class Box
{
    /**
     * @var \Magento\Framework\Setup\SampleData\FixtureManager
     */
    private $fixtureManager;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvReader;

    /**
     * @var \Balance\Box\Model\BoxFactory
     */
    protected $boxFactory;


    /**
     * @param \Balance\Box\Model\BoxFactory $boxFactory
     * @param Block\Converter $converter
     */
    public function __construct(
        \Totaltools\Importcontent\Setup\Context $dataContext,
        \Balance\Box\Model\BoxFactory $boxFactory
    ) {
        $this->fixtureManager = $dataContext->getFixtureManager();
        $this->csvReader = $dataContext->getCsvReader();
        $this->boxFactory = $boxFactory;
    }

    /**
     * @param array $fixtures
     * @throws \Exception
     */
    public function install(array $fixtures)
    {
        foreach ($fixtures as $fileName) {
            $fileName = $this->fixtureManager->getFixture($fileName);
            if (!file_exists($fileName)) {
                continue;
            }

            $rows = $this->csvReader->getData($fileName);
            $header = array_shift($rows);

            foreach ($rows as $row) {
                $data = [];
                foreach ($row as $key => $value) {
                    $data[$header[$key]] = $value;
                }
                $row = $data;

                $this->boxFactory->create()
                    ->load($row['identifier'], 'identifier')
                    ->addData($row)
                    ->save();
            }
        }
    }
}
