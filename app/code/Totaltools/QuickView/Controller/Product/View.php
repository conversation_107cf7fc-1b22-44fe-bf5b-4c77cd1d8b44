<?php
namespace Totaltools\QuickView\Controller\Product;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Catalog\Helper\Product;

class View extends Action
{
    protected $resultPageFactory;
    protected $resultJsonFactory;
    protected $productHelper;

    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        JsonFactory $resultJsonFactory,
        Product $productHelper
    ) {
        parent::__construct($context);
        $this->resultPageFactory = $resultPageFactory;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->productHelper = $productHelper;
    }

    public function execute()
    {
        $productId = (int)$this->getRequest()->getParam('id');
        $result = $this->resultJsonFactory->create();

        if (!$productId) {
            return $result->setData(['error' => __('Product ID is missing')]);
        }

        try {
            $page = $this->resultPageFactory->create();
            $block = $page->getLayout()
                ->createBlock('Totaltools\QuickView\Block\Product\View')
                ->setTemplate('Totaltools_QuickView::product/quickview.phtml')
                ->setProductId($productId);

            return $result->setData(['content' => $block->toHtml()]);
        } catch (\Exception $e) {
            return $result->setData(['error' => $e->getMessage()]);
        }
    }
}