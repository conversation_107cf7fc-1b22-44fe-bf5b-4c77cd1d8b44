
& when (@media-target = 'mobile'), (@media-target = 'all') {
    .media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
        // Hide quickview button on mobile
        .bss-bt-quickview {
            display: none !important;
        }

        // Hide magnific popup on mobile
        .mfp-wrap.bss-quickview {
            display: none !important;
        }
    }
}

// Desktop styles
.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
    .bss-quickview {
        display: block;
    }
}


.product-upsell-container{
    display: none;
}