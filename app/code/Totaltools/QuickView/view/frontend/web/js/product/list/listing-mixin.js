define([
    'jquery',
    'underscore'
], function ($, _) {
    'use strict';

    return function (widget) {
        $.widget('mage.productListingQuickView', widget, {
            _create: function () {
                this._super();
                this._initQuickView();
            },

            _initQuickView: function () {
                var self = this;
                
                // Add quick view button to all product items
                $('.product-item-info').each(function () {
                    var productId = $(this).find('[data-product-id]').attr('data-product-id');
                    if (productId && !$(this).find('.bss-quickview').length) {
                        var quickViewButton = $('<a>', {
                            'href': '#',
                            'class': 'bss-quickview',
                            'data-product-id': productId,
                            'text': 'Quick View'
                        });
                        $(this).find('.product-item-photo').append(quickViewButton);
                    }
                });
            }
        });

        return $.mage.productListingQuickView;
    };
});