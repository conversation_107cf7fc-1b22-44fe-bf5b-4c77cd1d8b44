<?php
declare(strict_types=1);

namespace Totaltools\OtpLogin\Test\Unit\Block\Login;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Magento\Framework\View\Element\Template\Context;
use Totaltools\OtpLogin\Helper\Data as OtpHelper;
use Totaltools\OtpLogin\Block\Login\Scripts;

/**
 * Unit test for OTP Login Scripts Block class
 */
class ScriptsTest extends TestCase
{
    /**
     * @var Scripts
     */
    private $block;

    /**
     * @var MockObject|OtpHelper
     */
    private $otpHelperMock;

    /**
     * @var MockObject|Context
     */
    private $contextMock;

    /**
     * Set up test dependencies
     */
    protected function setUp(): void
    {
        $this->otpHelperMock = $this->createMock(OtpHelper::class);
        $this->contextMock = $this->createMock(Context::class);

        $this->block = new Scripts(
            $this->contextMock,
            $this->otpHelperMock
        );
    }

    /**
     * Test isOtpLoginEnabled returns true when helper returns true
     */
    public function testIsOtpLoginEnabledReturnsTrue(): void
    {
        $this->otpHelperMock->expects($this->once())
            ->method('isOtpLoginEnabled')
            ->willReturn(true);

        $result = $this->block->isOtpLoginEnabled();
        $this->assertTrue($result);
    }

    /**
     * Test isOtpLoginEnabled returns false when helper returns false
     */
    public function testIsOtpLoginEnabledReturnsFalse(): void
    {
        $this->otpHelperMock->expects($this->once())
            ->method('isOtpLoginEnabled')
            ->willReturn(false);

        $result = $this->block->isOtpLoginEnabled();
        $this->assertFalse($result);
    }

    /**
     * Test _toHtml returns empty string when OTP is disabled
     */
    public function testToHtmlReturnsEmptyWhenOtpDisabled(): void
    {
        $this->otpHelperMock->expects($this->once())
            ->method('isOtpLoginEnabled')
            ->willReturn(false);

        // Use reflection to call protected method
        $reflection = new \ReflectionClass($this->block);
        $method = $reflection->getMethod('_toHtml');
        $method->setAccessible(true);

        $result = $method->invoke($this->block);
        $this->assertEquals('', $result);
    }
}
