<?php
declare(strict_types=1);

namespace Totaltools\OtpLogin\Test\Unit\Helper;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Store\Model\ScopeInterface;
use Totaltools\OtpLogin\Helper\Data;

/**
 * Unit test for OTP Login Helper Data class
 */
class DataTest extends TestCase
{
    /**
     * @var Data
     */
    private $helper;

    /**
     * @var MockObject|ScopeConfigInterface
     */
    private $scopeConfigMock;

    /**
     * @var MockObject|CustomerRepositoryInterface
     */
    private $customerRepositoryMock;

    /**
     * @var MockObject|Context
     */
    private $contextMock;

    /**
     * Set up test dependencies
     */
    protected function setUp(): void
    {
        $this->scopeConfigMock = $this->createMock(ScopeConfigInterface::class);
        $this->customerRepositoryMock = $this->createMock(CustomerRepositoryInterface::class);
        $this->contextMock = $this->createMock(Context::class);
        
        $this->contextMock->method('getScopeConfig')
            ->willReturn($this->scopeConfigMock);

        $this->helper = new Data(
            $this->contextMock,
            $this->customerRepositoryMock
        );
    }

    /**
     * Test isOtpLoginEnabled returns true when enabled
     */
    public function testIsOtpLoginEnabledReturnsTrue(): void
    {
        $this->scopeConfigMock->expects($this->once())
            ->method('isSetFlag')
            ->with(
                Data::XML_PATH_OTP_ENABLED,
                ScopeInterface::SCOPE_STORE,
                null
            )
            ->willReturn(true);

        $result = $this->helper->isOtpLoginEnabled();
        $this->assertTrue($result);
    }

    /**
     * Test isOtpLoginEnabled returns false when disabled
     */
    public function testIsOtpLoginEnabledReturnsFalse(): void
    {
        $this->scopeConfigMock->expects($this->once())
            ->method('isSetFlag')
            ->with(
                Data::XML_PATH_OTP_ENABLED,
                ScopeInterface::SCOPE_STORE,
                null
            )
            ->willReturn(false);

        $result = $this->helper->isOtpLoginEnabled();
        $this->assertFalse($result);
    }

    /**
     * Test isOtpLoginEnabled with specific store ID
     */
    public function testIsOtpLoginEnabledWithStoreId(): void
    {
        $storeId = 1;
        
        $this->scopeConfigMock->expects($this->once())
            ->method('isSetFlag')
            ->with(
                Data::XML_PATH_OTP_ENABLED,
                ScopeInterface::SCOPE_STORE,
                $storeId
            )
            ->willReturn(true);

        $result = $this->helper->isOtpLoginEnabled($storeId);
        $this->assertTrue($result);
    }
}
