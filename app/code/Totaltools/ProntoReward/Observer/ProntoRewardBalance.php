<?php
/**
 * Totaltools ProntoReward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\ProntoReward\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Totaltools\ProntoReward\Api\ProntoRewardManagementInterface;
use Totaltools\ProntoReward\Helper\Data;

/**
 * Class ProntoRewardBalance
 * @package Totaltools\ProntoReward\Observer
 */
class ProntoRewardBalance implements ObserverInterface
{
    /**
     * @var ProntoRewardManagementInterface
     */
    private $prontoRewardManagement;

    /**
     * helper
     *
     * @var Data
     */
    protected $helper;

    /**
     * ProntoRewardBalance constructor.
     *
     * @param ProntoRewardManagementInterface $prontoRewardManagement
     * @param Data $helper
     * @SuppressWarnings(PHPMD.LongVariable)
     */
    public function __construct(
        ProntoRewardManagementInterface $prontoRewardManagement,
        Data $helper
    ) {
        $this->prontoRewardManagement = $prontoRewardManagement;
        $this->helper = $helper;
    }

    /**
     * Get Reward information from Pronto service.
     *
     * @param Observer $observer
     * @return $this|void
     */
    public function execute(Observer $observer)
    {
        $event = $observer->getEvent();
        $loyaltyId = $event->getData('loyalty_id');
        $amount = $event->getData('amount');

        /** @var \Totaltools\Reward\Api\Data\RewardInformationInterface $reward */
        $reward = $event->getData('reward');
        if ($reward->isValid() === true) {
            return $this;
        }
        if($this->helper->isRewardsSyncReady()) {
            $this->helper->setRewardsSyncTimestamp(time());
            $this->prontoRewardManagement->retrieveRewardBalance($reward, $loyaltyId, $amount);
        }

        return $this;
    }
}
