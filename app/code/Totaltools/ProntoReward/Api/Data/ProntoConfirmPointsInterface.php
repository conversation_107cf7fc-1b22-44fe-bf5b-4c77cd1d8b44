<?php
/**
 * Totaltools ProntoReward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\ProntoReward\Api\Data;

/**
 * @api
 */
interface ProntoConfirmPointsInterface
{
    const PRONTO_RES_BALANCE = 'Balance';
    const PRONTO_RES_RESPONSE = 'Response';
    const PRONTO_RES_STATUS = 'Status';
    const PRONTO_RES_CUSTOMER_STATUS = 'CustomerStatus';

    /**
     * Get Balance Value.
     *
     * @return string
     */
    public function getBalance();

    /**
     * Set Balance Value.
     *
     * @param string $balance
     *
     * @return \Totaltools\ProntoReward\Api\Data\ProntoConfirmPointsInterface
     */
    public function setBalance($balance);

    /**
     * Get Response Value.
     *
     * @return string
     */
    public function getResponse();

    /**
     * Set Response Value.
     *
     * @param string $response
     *
     * @return \Totaltools\ProntoReward\Api\Data\ProntoConfirmPointsInterface
     */
    public function setResponse($response);

    /**
     * Get Response Status Code.
     *
     * @return string
     */
    public function getStatus();

    /**
     * Set Response Status Code.
     *
     * @param $status
     *
     * @return \Totaltools\ProntoReward\Api\Data\ProntoConfirmPointsInterface
     */
    public function setStatus($status);

    /**
     * Get Response CustomerStatus.
     *
     * @return string
     */
    public function getCustomerStatus();

    /**
     * Set Response CustomerStatus.
     *
     * @param $customerStatus
     *
     * @return \Totaltools\ProntoReward\Api\Data\ProntoConfirmPointsInterface
     */
    public function setCustomerStatus($customerStatus);
}
