<?php
/**
 * Totaltools ProntoReward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
declare(strict_types=1);

namespace Totaltools\ProntoReward\Api;

use Totaltools\Reward\Api\Data\RewardInformationInterface;

interface ProntoRewardManagementInterface
{
    const PRONTO_REQ_LOYALTY_ID = 'LoyaltyID';
    const PRONTO_REQ_AMOUNT = 'Amount';
    const PRONTO_REQ_MAGENTO_ORDER_NO = 'MagentoOrderNumber';

    /**
     * Retrieve reward balance from Pronto.
     *
     * @param RewardInformationInterface $reward
     * @param string $loyaltyId
     * @param string $amount
     *
     * @return \Totaltools\ProntoReward\Api\ProntoRewardManagementInterface
     */
    public function retrieveRewardBalance(RewardInformationInterface $reward, string $loyaltyId, string $amount);

    /**
     * Lock points in Pronto after reward purchased.
     *
     * @param RewardInformationInterface $reward
     * @param string $loyaltyId
     * @param string $amount
     * @param string $magentoOrderNumber
     *
     * @return \Totaltools\ProntoReward\Api\ProntoRewardManagementInterface
     */
    public function lockRewardPoints(RewardInformationInterface $reward, string $loyaltyId, string $amount, string $magentoOrderNumber);
}
