<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Totaltools ProntoReward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Totaltools\ProntoReward\Api\Data\ProntoConfirmPointsInterface"
                type="Totaltools\ProntoReward\Model\ProntoConfirmPoints"/>
    <preference for="Totaltools\ProntoReward\Api\ProntoRewardManagementInterface"
                type="Totaltools\ProntoReward\Model\ProntoRewardManagement"/>
    <virtualType name="Totaltools\ProntoReward\Model\Logger\VirtualDebug" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/totaltools/pronto_reward.log</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\ProntoReward\Model\Logger\VirtualLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="name" xsi:type="string">TOT</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Totaltools\ProntoReward\Model\Logger\VirtualDebug</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Totaltools\ProntoReward\Model\ProntoRewardManagement">
        <arguments>
            <argument name="logger" xsi:type="object">Totaltools\ProntoReward\Model\Logger\VirtualLogger</argument>
        </arguments>
    </type>
</config>