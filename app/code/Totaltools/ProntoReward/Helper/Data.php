<?php

/**
 * Copyright © 2016 Balance Internet. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\ProntoReward\Helper;

use Magento\Customer\Model\Session;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Psr\Log\LoggerInterface;

/**
 * Helper class
 *
 * @category  Totaltools
 * @package   Totaltools_ProntoReward
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
class Data extends AbstractHelper
{ 

    /**
     * Timeout configuration for Pronto to Magento Rewards sync
     *
     * @var int
     */
    const PRONTO_REWARDS_SYNC_TIMEOUT = "totaltools_loyalty/sync/rewards_sync_timeout";

    /**
     * Session key name for rewards sync timestamp
     *
     * @var int
     */
    const REWARDS_SYNC_TIMESTAMP = 'rewards_sync_timeout';

    /**
     * LoggerInterface
     *
     * @var LoggerInterface
     */
    protected $logger;
    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Data constructor.
     *
     * @param Context                     $context
     * @param Session                     $customerSession
     * @param LoggerInterface             $logger
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
        $this->session = $customerSession;
        parent::__construct($context);
    }

    /**
     * @return bool
     */
    public function isRewardsSyncReady()
    {
        $this->logger->debug('isRewardsSyncReady');
        return time() - $this->getRewardsSyncTimestamp() > $this->getRewardsSyncTimeout();
    }

    /**
     * @param int $timestamp
     * @return void
     */
    public function setRewardsSyncTimestamp($timestamp)
    {
        $this->logger->debug('setrewardssyn');
        $this->session->setData(self::REWARDS_SYNC_TIMESTAMP, $timestamp);
    }

    /**
     * @return int|null
     */
    public function getRewardsSyncTimestamp()
    {
        return (int) $this->session->getData(self::REWARDS_SYNC_TIMESTAMP);
    }

     /**
     * Timeout in seconds for Pronto to Magento sync of customer data
     *
     * @return int
     */
    public function getRewardsSyncTimeout()
    {
        return ((int)$this->scopeConfig->getValue(self::PRONTO_REWARDS_SYNC_TIMEOUT)) * 60;
    }
}
