<?php

namespace Totaltools\ProntoReward\Controller\Adminhtml\Order;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Framework\Controller\Result\JsonFactory;
use Totaltools\ProntoReward\Api\ProntoRewardManagementInterface;
use Totaltools\Reward\Model\RewardInformationFactory;

class LockPoints extends Action
{
    protected $orderRepository;
    protected $prontoRewardManagement;
    protected $jsonFactory;

     /**
     * @var RewardInformationFactory
     */
    private $rewardInformationFactory;
     /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    private $customerFactory;

    public function __construct(
        Context $context,
        OrderRepositoryInterface $orderRepository,
        ProntoRewardManagementInterface $prontoRewardManagement,
        RewardInformationFactory $rewardInformationFactory,
        \Magento\Customer\Model\CustomerFactory $customerFactory
    ) {
        parent::__construct($context);
        $this->orderRepository = $orderRepository;
        $this->prontoRewardManagement = $prontoRewardManagement;
        $this->rewardInformationFactory = $rewardInformationFactory;

        $this->customerFactory = $customerFactory;
    }

    public function execute()
    {
        $orderId = $this->getRequest()->getParam('order_id');

        if (!$orderId) {
            return $this->messageManager->addErrorMessage(__('Order ID is missing.'));
        }

        try {
            $order = $this->orderRepository->get($orderId);
            /** @var \Magento\Customer\Model\Customer $customer */
            $customer = $this->customerFactory->create()->load($order->getCustomerId());
            $loyaltyId = $customer->getLoyaltyId(); 
            $amount = $order->getBaseRewardCurrencyAmount();
            /** @var \Totaltools\Reward\Model\RewardInformation $reward */
            $reward = $this->rewardInformationFactory->create();
            if (!$loyaltyId || !$amount) {
                return $this->messageManager->addErrorMessage(__('No points redemption found.'));
            }

            // Call the Lock Points API
            $this->prontoRewardManagement->lockRewardPoints($reward, $loyaltyId, $amount, $order->getIncrementId());
            if ($reward->getIsValid()) {
                $this->messageManager->addSuccessMessage(__('Points locked successfully.'));
            } else {
                $this->messageManager->addErrorMessage($reward->getErrorMessages()[0]);
            }

        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__('An error occurred while locking points.'));
        }

        return $this->_redirect('sales/order/view', ['order_id' => $orderId]);
    }
}
