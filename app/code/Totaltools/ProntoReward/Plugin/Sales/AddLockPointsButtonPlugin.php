<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @package    Totaltools_ProntoReward
 * @copyright  Copyright (c) 2025, Totaltools  (http://www.totaltools.com.au/)
 */

namespace Totaltools\ProntoReward\Plugin\Sales;

use \Magento\Framework\App\Action\Context;
use \Magento\Backend\Model\UrlInterface;
use \Magento\Sales\Model\OrderRepository;

class AddLockPointsButtonPlugin
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var UrlInterface
     */
    protected $url;

    /**
     * @var OrderRepository
     */
    protected $orderRepository;

    /**
     * @var \Magento\Framework\Registry
     */
    protected $registry;

    /**
     * @var \Magento\Framework\AuthorizationInterface
     */
    protected $_authorization;

     /**
     * @var \Magento\Reward\Helper\Data
     */
    private $rewardData;

    /**
     * @param Context $context
     * @param UrlInterface $url
     * @param OrderRepository $orderRepository
     * @param \Magento\Framework\AuthorizationInterface $authorization
     */
    public function __construct(
        Context $context,
        UrlInterface $url,
        OrderRepository $orderRepository,
        \Magento\Framework\Registry $registry,
        \Magento\Reward\Helper\Data $rewardData,
        \Magento\Framework\AuthorizationInterface $authorization
    ) {
        $this->context = $context;
        $this->url = $url;
        $this->orderRepository = $orderRepository;
        $this->registry = $registry;
        $this->rewardData = $rewardData;
        $this->_authorization = $authorization;
    }

    /**
     * Retrieve order model object
     *
     * @param \Magento\Backend\Block\Widget\Context $subject
     * @param UrlInterface $buttonList
     * @throws \Exception
     *
     * @return $buttonList
     */
    public function afterGetButtonList(
        \Magento\Backend\Block\Widget\Context $subject,
        $buttonList
    ) {
        $orderId = $this->getOrderId();
        $order = $this->registry->registry('current_order');
        if(
            $order &&
            $this->context->getRequest()->getFullActionName() === 'sales_order_view'
        ){
            
            $amount = $order->getBaseRewardCurrencyAmount();
            if ($order->getCustomerIsGuest() || !$this->rewardData->isEnabledOnFront($order->getStore()->getWebsiteId()) ||
                null === $amount
            ) {
                return $buttonList;
            }
           
                $buttonList->add(
                    'lock_points',
                    [
                        'label' => __('Lock Points'),
                        'onclick' => 'setLocation(\'' . $this->getLockPointsUrl($orderId) . '\')',
                        'class' => 'lock_points'
                    ],
                    101
                );
            

            
        }
        return $buttonList;
    }

    protected function getOrderId()
    {
        return $this->context->getRequest()->getParam('order_id');
    }

    private function getLockPointsUrl($orderId)
    {
        return  $this->url->getUrl(
            'prontoreward/order/lockpoints',
            ['order_id' => $this->getOrderId()]
        );
        
    }

    /**
     * Check permission for passed action
     *
     * @param string $resourceId
     * @return bool
     */
    protected function _isAllowedAction($resourceId)
    {
        return $this->_authorization->isAllowed($resourceId);
    }



}