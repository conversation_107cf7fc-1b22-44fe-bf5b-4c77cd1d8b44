<?php
/**
 * Totaltools ProntoReward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\ProntoReward\Model;

use Magento\Framework\Model\AbstractModel;
use Totaltools\ProntoReward\Api\Data\ProntoConfirmPointsInterface;

/**
 * @api
 */
class ProntoConfirmPoints extends AbstractModel implements ProntoConfirmPointsInterface
{
    /**
     * {@inheritdoc}
     */
    public function getBalance()
    {
        return $this->getData(self::PRONTO_RES_BALANCE);
    }

    /**
     * @inheritDoc
     */
    public function setBalance($balance)
    {
        return $this->setData(self::PRONTO_RES_BALANCE, $balance);
    }

    /**
     * @inheritDoc
     */
    public function getResponse()
    {
        return $this->getData(self::PRONTO_RES_RESPONSE);
    }

    /**
     * @inheritDoc
     */
    public function setResponse($response)
    {
        return $this->setData(self::PRONTO_RES_RESPONSE, $response);
    }

    /**
     * @inheritDoc
     */
    public function getStatus()
    {
        return $this->getData(self::PRONTO_RES_STATUS);
    }

    /**
     * @inheritDoc
     */
    public function setStatus($status)
    {
        return $this->setData(self::PRONTO_RES_STATUS, $status);
    }

    /**
     * @inheritDoc
     */
    public function getCustomerStatus()
    {
        return $this->getData(self::PRONTO_RES_CUSTOMER_STATUS);
    }

    /**
     * @inheritDoc
     */
    public function setCustomerStatus($customerStatus)
    {
        return $this->setData(self::PRONTO_RES_CUSTOMER_STATUS, $customerStatus);
    }
}
