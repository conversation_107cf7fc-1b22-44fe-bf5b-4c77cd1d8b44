<?php
/**
 * Totaltools ProntoReward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
declare(strict_types=1);

namespace Totaltools\ProntoReward\Model;

use Totaltools\Pronto\Helper\Config;
use Totaltools\Pronto\Helper\Data as ProntoHelper;
use Totaltools\Reward\Api\Data\RewardInformationInterface;
use Totaltools\ProntoReward\Model\ProntoConfirmPointsFactory;
use Totaltools\ProntoReward\Api\ProntoRewardManagementInterface;

/**
 * @api
 */
class ProntoRewardManagement implements ProntoRewardManagementInterface
{
    /**
     * @var \Totaltools\Pronto\Model\Api\Connect
     */
    private $apiClient;

    /**
     * @var ProntoConfirmPointsFactory
     */
    private $prontoConfirmPointsFactory;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var Config
     */
    private $config;

    /**
     * @var ProntoHelper
     */
    private $prontoHelper;

    /**
     * ProntoRewardManagement constructor.
     * @param \Totaltools\Pronto\Model\Api\Connect $apiClient
     * @param \Psr\Log\LoggerInterface $logger
     * @param Config $config
     * @param \Totaltools\ProntoReward\Model\ProntoConfirmPointsFactory $prontoConfirmPointsFactory
     * @param ProntoHelper $prontoHelper
     */
    public function __construct(
        \Totaltools\Pronto\Model\Api\Connect $apiClient,
        \Psr\Log\LoggerInterface $logger,
        Config $config,
        ProntoConfirmPointsFactory $prontoConfirmPointsFactory,
        ProntoHelper $prontoHelper
    ) {
        $this->apiClient = $apiClient;
        $this->logger = $logger;
        $this->config = $config;
        $this->prontoConfirmPointsFactory = $prontoConfirmPointsFactory;
        $this->prontoHelper = $prontoHelper;
    }

    /**
     * @inheritDoc
     */
    public function retrieveRewardBalance(RewardInformationInterface $reward, string $loyaltyId, string $amount)
    {
        $params = [
            self::PRONTO_REQ_LOYALTY_ID => $loyaltyId,
            self::PRONTO_REQ_AMOUNT => $amount,
            self::PRONTO_REQ_MAGENTO_ORDER_NO => null,
        ];

        $this->apiClient
            ->setPath('ConfirmPoints')
            ->setRootNode('ConfirmPointsRequest')
            ->setBody([
                'Parameters' => $params
            ]);
        try {
            $this->logger->info('Start Balance Request');
            $response = $this->apiClient->call('array');
            $this->logger->info('End Balance Request');
            /** @var ProntoConfirmPoints $prontoConfirmPoints */
            $prontoConfirmPoints = $this->prontoConfirmPointsFactory->create();
            $prontoConfirmPoints->setData($response);
            $reward = $this->processProntoResponse($prontoConfirmPoints, $reward);
        } catch (\Exception $e) {
            $reward->setIsValid(false);
            // @codingStandardsIgnoreStart
            if ($this->config->isDebug()) {
                $this->logger->critical($e);
            }
            // @codingStandardsIgnoreEnd
            return $this;
        }

        return $this;
    }

    /**
     * @inheritDoc
     */
    public function lockRewardPoints(RewardInformationInterface $reward, string $loyaltyId, string $amount, string $magentoOrderNumber)
    {
        $params = [
            self::PRONTO_REQ_LOYALTY_ID => $loyaltyId,
            self::PRONTO_REQ_AMOUNT => $amount,
            self::PRONTO_REQ_MAGENTO_ORDER_NO => $magentoOrderNumber,
        ];

        $this->apiClient
            ->setPath('ConfirmPoints')
            ->setRootNode('ConfirmPointsRequest')
            ->setBody([
                'Parameters' => $params
            ]);

        try {
            $this->logger->info('Start Lock Request');
            $response = $this->apiClient->call('array');
            $this->logger->info('End Lock Request');
            /** @var ProntoConfirmPoints $prontoConfirmPoints */
            $prontoConfirmPoints = $this->prontoConfirmPointsFactory->create();
            $prontoConfirmPoints->setData($response);
            $reward = $this->processProntoResponse($prontoConfirmPoints, $reward);
        } catch (\Exception $e) {
            $reward->setIsValid(false);
            // @codingStandardsIgnoreStart
            if ($this->config->isDebug()) {
                $this->logger->critical($e);
            }
            // @codingStandardsIgnoreEnd
            return $this;
        }

        return $this;
    }

    /**
     * @param ProntoConfirmPoints $prontoConfirmPoints
     * @param RewardInformationInterface $reward
     * @return RewardInformationInterface
     */
    private function processProntoResponse(ProntoConfirmPoints $prontoConfirmPoints,RewardInformationInterface $reward)
    {
        $reward->setIsValid(false);
        $status = (int)$prontoConfirmPoints->getStatus();
        if ($status === 200) {
            $reward->setIsValid(true);
            if ($prontoConfirmPoints->getBalance() !== 0) {
                $reward->setBalance($prontoConfirmPoints->getBalance());
            }
        }
        if ($status === 400) {
            $reward->addErrorMessage([$prontoConfirmPoints->getResponse()]);
        }

        $customerStatus = $this->prontoHelper->getLoyaltyStatus($prontoConfirmPoints->getCustomerStatus());
        $reward->setLoyaltyStatus($customerStatus);

        return $reward;
    }
}
