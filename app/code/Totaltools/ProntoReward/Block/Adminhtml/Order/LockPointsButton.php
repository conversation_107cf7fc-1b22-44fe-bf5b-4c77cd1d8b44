<?php

namespace Totaltools\ProntoReward\Block\Adminhtml\Order;

use Magento\Backend\Block\Widget\Context;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;

class LockPointsButton implements ButtonProviderInterface
{
    protected $context;
    protected $orderRepository;

    /**
     * @var \Magento\Reward\Helper\Data
     */
    private $rewardData;

    public function __construct(
        Context $context,

        \Magento\Reward\Helper\Data $rewardData,
        OrderRepositoryInterface $orderRepository
    ) {
        $this->context = $context;
        $this->rewardData = $rewardData;
        $this->orderRepository = $orderRepository;
    }

    public function getButtonData()
    {
        $orderId = $this->context->getRequest()->getParam('order_id');
        if (!$orderId) {
            return [];
        }

        $order = $this->orderRepository->get($orderId);
        $amount = $order->getBaseRewardCurrencyAmount();
        if ($order->getCustomerIsGuest() || $order->getIsProntoLocked() ||
            !$this->rewardData->isEnabledOnFront($order->getStore()->getWebsiteId()) ||
            null === $amount
        ) {
            return [];
        }

       

        return [
            'label' => __('Lock Points'),
            'class' => 'primary',
            'on_click' => sprintf("location.href = '%s';", $this->getLockPointsUrl($orderId)),
            'sort_order' => 100,
        ];
    }

    private function hasPointsRedemption($order)
    {
        $pointsUsed = $order->getData('pronto_reward_points_used'); // Replace with the correct attribute
        return $pointsUsed && $pointsUsed > 0;
    }

    private function getLockPointsUrl($orderId)
    {
        return $this->context->getUrl('prontoreward/order/lockpoints', ['order_id' => $orderId]);
    }
}
