<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Totaltools\Commercial\Api\AccountRepositoryInterface" type="Totaltools\Commercial\Model\AccountRepository"/>
	<preference for="Totaltools\Commercial\Api\Data\AccountInterface" type="Totaltools\Commercial\Model\Account"/>
	<preference for="Totaltools\Commercial\Api\Data\AccountSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<virtualType name="Totaltools\Commercial\Model\ResourceModel\Account\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">totaltools_commercial_account</argument>
			<argument name="resourceModel" xsi:type="string">Totaltools\Commercial\Model\ResourceModel\Account\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="totaltools_commercial_account_listing_data_source" xsi:type="string">Totaltools\Commercial\Model\ResourceModel\Account\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
</config>
