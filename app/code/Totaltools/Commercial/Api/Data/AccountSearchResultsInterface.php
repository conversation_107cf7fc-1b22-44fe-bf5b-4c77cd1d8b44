<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Commercial\Api\Data;

interface AccountSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Account list.
     * @return \Totaltools\Commercial\Api\Data\AccountInterface[]
     */
    public function getItems();

    /**
     * Set customer_name list.
     * @param \Totaltools\Commercial\Api\Data\AccountInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

