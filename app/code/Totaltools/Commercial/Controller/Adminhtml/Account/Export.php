<?php

namespace Totaltools\Commercial\Controller\Adminhtml\Account;

use Totaltools\Commercial\Model\AccountFactory as CommercialAccountFactory;
use Magento\Framework\App\Filesystem\DirectoryList;

class Export extends \Magento\Backend\App\Action
{
    /** @var \Magento\Framework\View\Result\PageFactory */
    protected $resultPageFactory;
    
    /** @var CommercialAccountFactory */
    protected $commercialAccountFactory;

    /**
     * @var \Magento\Framework\App\Response\Http\FileFactory
     */
    protected $fileFactory;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvProcessor;

    /**
     * @var \Magento\Framework\App\Filesystem\DirectoryList
     */
    protected $directoryList;

    /**
     * Constructor
     *
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param CommercialAccountFactory $commercialAccountFactory
     * @param \Magento\Framework\App\Response\Http\FileFactory $fileFactory
     * @param \Magento\Framework\File\Csv                      $csvProcessor
     * @param \Magento\Framework\App\Filesystem\DirectoryList  $directoryList
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        CommercialAccountFactory $commercialAccountFactory,
        \Magento\Framework\App\Response\Http\FileFactory $fileFactory,
        \Magento\Framework\File\Csv $csvProcessor,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->commercialAccountFactory = $commercialAccountFactory;
        $this->fileFactory = $fileFactory;
        $this->csvProcessor = $csvProcessor;
        $this->directoryList = $directoryList;
        parent::__construct($context);
    }

    /**
     * Commercial Accounts Export To CSV
     *
     * @return \Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function execute()
    {
        $content[] = [
            'Customer Name',
            'Email',
            'Phone',
            'ABN',
            'Company Name',
            'Estimated Monthly Spend',
            'Post Code',
            'Submission Date'
        ];
        $resultPage = $this->commercialAccountFactory->create();
        $collection = $resultPage->getCollection();
        $fileName = 'commercial_accounts_export.csv';
        $filePath =  $this->directoryList->getPath(DirectoryList::MEDIA) . "/" . $fileName;
        while ($commercialAccount = $collection->fetchItem()) {
            $content[] = [
                $commercialAccount->getData('customer_name'),
                $commercialAccount->getData('email'),
                $commercialAccount->getData('phone'),
                $commercialAccount->getData('abn'),
                $commercialAccount->getData('company_name'),
                $commercialAccount->getData('estimated_monthly_spend'),
                $commercialAccount->getData('postcode'),
                $commercialAccount->getData('submission_date')
            ];
        }
        $this->csvProcessor->setEnclosure('"')->setDelimiter(',')->saveData($filePath, $content);
        return $this->fileFactory->create(
            $fileName,
            [
                'type'  => "filename",
                'value' => $fileName,
                'rm'    => true,
            ],
            DirectoryList::MEDIA,
            'text/csv',
            null
        );
    }
}
