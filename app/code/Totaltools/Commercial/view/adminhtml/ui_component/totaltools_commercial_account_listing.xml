<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">totaltools_commercial_account_listing.totaltools_commercial_account_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>totaltools_commercial_account_columns</spinner>
		<deps>
			<dep>totaltools_commercial_account_listing.totaltools_commercial_account_listing_data_source</dep>
		</deps>
		<buttons>
			<button name="export">
				<url path="*/*/export"/>
				<class>primary</class>
				<label translate="true">Export Commercial Accounts</label>
			</button>
		</buttons>
	</settings>
	<dataSource name="totaltools_commercial_account_listing_data_source" component="Magento_Ui/js/grid/provider">
		<settings>
			<storageConfig>
				<param name="indexField" xsi:type="string">account_id</param>
			</storageConfig>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>Totaltools_Commercial::Account</aclResource>
		<dataProvider name="totaltools_commercial_account_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>account_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="totaltools_commercial_account_columns">
		<column name="customer_name">
			<settings>
				<filter>text</filter>
				<label translate="true">Customer Name</label>
			</settings>
		</column>
		<column name="email">
			<settings>
				<filter>text</filter>
				<label translate="true">Email</label>
			</settings>
		</column>
		<column name="phone">
			<settings>
				<filter>text</filter>
				<label translate="true">Phone</label>
			</settings>
		</column>
		<column name="abn">
			<settings>
				<filter>text</filter>
				<label translate="true">ABN</label>
			</settings>
		</column>
		<column name="company_name">
			<settings>
				<filter>text</filter>
				<label translate="true">Company Name</label>
			</settings>
		</column>
		<column name="estimated_monthly_spend">
			<settings>
				<filter>text</filter>
				<label translate="true">Estimated Monthly Spend</label>
			</settings>
		</column>
		<column name="selected_stores">
			<settings>
				<filter>text</filter>
				<label translate="true">Selected Stores</label>
			</settings>
		</column>
		<column name="postcode">
			<settings>
				<filter>text</filter>
				<label translate="true">Postcode</label>
			</settings>
		</column>
		<column name="submission_date" class="Magento\Ui\Component\Listing\Columns\Date" component="Magento_Ui/js/grid/columns/date">
			<settings>
				<filter>dateRange</filter>
				<dataType>date</dataType>
				<label translate="true">Submission Date</label>
			</settings>
		</column>
	</columns>
</listing>
