<?php

/**
 * Totaltools Shippit.
 *
 * @category  Totaltools_Shippit
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 totaltools Pty Ltd
 */

namespace Totaltools\Shippit\Helper;

use Totaltools\Storelocator\Model\Registry;
use Magento\Customer\Model\Session;
use Magento\Sales\Api\Data\OrderInterface;

class Api
{
    /**
     * @var \Magento\Framework\Registry
     */
    private $registry;

    /**
     * @var \Totaltools\Storelocator\Model\Registry
     */
    private $storeRegistry;

    /**
     * @var \Totaltools\Storelocator\Helper\Checkout\Data
     */
    private $checkoutHelper;

    /**
     * @var OrderInterface
     */
    private $orderModel;

    /**
     * @var  \Psr\Log\LoggerInterface
     */
    private  $logger;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $checkoutSession;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $customerSession;

    /**
     * @var \Shippit\Shipping\Helper\Data
     */
    protected $helper;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;
    /**
     * @var \Magento\Backend\Model\Session
     */
    protected $adminSession;
    /**
     * @var  \Magento\Framework\UrlInterface
     */
    protected $urlInterface;
    /**
     * @var  \Magento\Framework\App\RequestInterface
     */
    protected $request;
    /**
     * @param \Magento\Framework\Registry $registry
     * @param Registry $storeRegistry
     * @param Data $checkoutHelper
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Magento\Customer\Model\Session $customerSession
     */
    public function __construct(
        \Magento\Framework\Registry $registry,
        \Totaltools\Storelocator\Model\Registry $storeRegistry,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Psr\Log\LoggerInterface $logger,
        Session $customerSession,
        \Magento\Backend\Model\Session $adminSession,
        \Shippit\Shipping\Helper\Data $helper,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\UrlInterface $urlInterface,
        \Magento\Framework\App\RequestInterface $request,
        OrderInterface $orderModel
    ) {
        $this->registry = $registry;
        $this->storeRegistry = $storeRegistry;
        $this->checkoutHelper = $checkoutHelper;
        $this->checkoutSession = $checkoutSession;
        $this->storeRepository = $storeRepository;
        $this->logger = $logger;
        $this->customerSession = $customerSession;
        $this->helper = $helper;
        $this->scopeConfig = $scopeConfig;
        $this->adminSession = $adminSession;
        $this->urlInterface = $urlInterface;
        $this->request = $request;
        $this->orderModel = $orderModel;
    }

    /**
     * @param $postcode
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface|\Totaltools\Storelocator\Model\Store
     */
    protected function getPostCodeFromRequestData($postcode = "")
    {
        if (!empty($postcode)) {
            $store = $this->storeRepository->getByPostcodeInZone($postcode);
            if ($store && !empty($store->getShippitApiKey())) {
                $logMessage = 'Quote generated ' . $postcode . ' : ' . $store->getStoreName() . ' : ' . $store->getShippitApiKey();
                $this->logger->info($logMessage);
                return $store;
            }
        }

        return false;
    }

    /**
     * @param $postcode
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface|\Totaltools\Storelocator\Model\Store
     */
    protected function getStoreFromDefaultShippingAddressPostCode($postcode)
    {
        // Get store from post code
        $store = $this->storeRepository->getByPostcodeInZone($postcode);
        if ($store && !empty($store->getShippitApiKey())) {
            $logMessage = 'Quote generated from default shipping address ' . $postcode . ' : ' . $store->getStoreName() . ' : ' . $store->getShippitApiKey();
            $this->logger->info($logMessage);
            return $store;
        }
        return false;
    }

    /**
     * @param $data
     * @return mixed|string|apikey|null
     */
    public function getUpdatedApiKey($requestData = null)
    {
        $postcodeStore = "";
        $storeApiKey = "";

        if (is_object($requestData) && $requestData->getData('dropoff_postcode') !== null) {
            $postCode = $requestData->getData('dropoff_postcode');
            $postcodeStore = $this->getPostCodeFromRequestData($postCode);
        }

        $postcode = '';
        $uriPath = $this->customerSession->getUriPath();
        $path = '';
        $isEstimateCostPath = $this->urlInterface->getCurrentUrl();
        //for estimate shpping and instant purchase product detial page
        if (strpos($isEstimateCostPath, 'getquote') !== false || strpos($isEstimateCostPath, 'availabledeliverymethod') !== false) {
            if ($postcodeStore) {
                $storeApiKey = $postcodeStore->getShippitApiKey();
                $this->logger->info("FinalStoreApiKey availabledeliverymethod".$storeApiKey);
                return $storeApiKey;
            }
        }

        //end estimate shipping product detial page
        if (isset($uriPath)) {
            $path = $uriPath->getPath();
        }

        if (strpos($path, 'quotes') !== false) {
            if ($postcodeStore) {
                $storeApiKey = $postcodeStore->getShippitApiKey();
                $this->logger->info("FinalStoreApiKey Quotes".$storeApiKey);
                return $storeApiKey;
            } elseif ($this->customerSession->isLoggedIn()) {
                $defaultShippingAddress = $this->customerSession->getCustomer()->getDefaultShippingAddress();
                if ($defaultShippingAddress) {
                    $postcode = $defaultShippingAddress->getPostcode();
                    if ($postcode) {
                        $postcodeStore = $this->getStoreFromDefaultShippingAddressPostCode($postcode);
                        if ($postcodeStore) {
                            $storeApiKey = $postcodeStore->getShippitApiKey();
                            $this->logger->info("FinalStoreApiKey Quotes Logged In else".$storeApiKey);
                            return  $storeApiKey;
                        }
                    }
                }
            }
        }

        if (is_object($requestData) && $requestData->getData('storelocator_id') !== null) {
            $storelocatorId = $requestData->getData('storelocator_id');
            $storeApiKey =  $this->getApiKeyByStoreId($storelocatorId);
            $this->logger->info("FinalStoreApiKey Orders".$storeApiKey);
            return $storeApiKey;
        }

        if (!$storeApiKey) {
            $storeApiKey = $this->checkoutHelper->getStorelocatorApiKey();
        }
        //in case not in checkout and admin session
        if (!$storeApiKey) {
            $storeApiKey = $this->getDefaultApiKey();
        }
        $this->logger->info("FinalStoreApiKey Default".$storeApiKey);
        return $storeApiKey;
    }

     /**
     *
     * @return apikey
     */
    protected function getDefaultApiKey()
    {
        $storeApiKey = $this->scopeConfig->getValue(
            'shippit/general/api_key',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
         );
         return $storeApiKey;
    }
    /**
     * @param $storeId
     * @return @return apikey
     */
    protected function getApiKeyByStoreId($storeId)
    {
        // Get store from storeid
        $store = $this->storeRepository->getById($storeId);
        if ($store && !empty($store->getShippitApiKey())) {
            return $store->getShippitApiKey();
        }
    }

    public function getShippitApiKeyFromAssignedStore($incrementId)
    {
        $order = $this->orderModel->loadByIncrementId($incrementId);
        if ($order && $order->getData('storelocator_id')) {
            $storeId = $order->getData('storelocator_id');
            return $this->getApiKeyByStoreId($storeId);
        }

        return false;
    }
}
