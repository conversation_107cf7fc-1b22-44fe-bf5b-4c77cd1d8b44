<?php

namespace Totaltools\Shippit\Helper;

use Magento\Sales\Model\Order;
use Shippit\Shipping\Model\Sync\Order as SyncOrderModel;
use Shippit\Shipping\Model\Config\Source\Shippit\Sync\Order\Mode;
use Magento\Framework\App\Helper\Context;

/**
 * Class Data
 * @package Totaltools\Shippit\Helper
 */
class SyncOrder extends \Magento\Framework\App\Helper\AbstractHelper
{
    const STATUS_CANCEL_PENDING = 3;
    const STATUS_CANCEL_PENDING_TEXT = 'Cancel Pending';

    const STATUS_CANCELED = 4;
    const STATUS_CANCELED_TEXT = 'Canceled';

    const STATUS_CANCEL_FAILED = 5;
    const STATUS_CANCEL_FAILED_TEXT = 'Cancel Failed';

    const XML_CANCEL_EMAIL_TEMPLATE_PATH = 'cancel_sync_email';
    const XML_CANCEL_EMAIL_IDENTIFIER_PATH = 'cancel_sync_email_identity';

    const ORDER_RELLOCATING_STATUS = 'reallocating';
    const ORDER_RELLOCATING_STATUS_LABEL = 'Reallocating';
    const ORDER_RELLOCATED_STATUS = 'reallocated';
    const ORDER_RELLOCATED_STATUS_LABEL = 'Reallocated';

    /**
     * @var \Totaltools\Shippit\Model\Api\Order\Cancel
     */
    private $apiCancel;

    /**
     * @var \Shippit\Shipping\Helper\Sync\Order
     */
    private $syncHelper;

    /**
     * @var \Shippit\Shipping\Model\ResourceModel\Sync\Order\CollectionFactory
     */
    private $syncOrderFactory;

    private $syncResult = [];

    /**
     * SyncOrder constructor.
     * @param Context $context
     * @param \Totaltools\Shippit\Model\Api\Order\Cancel $apiCancel
     * @param \Shippit\Shipping\Helper\Sync\Order $syncHelper
     * @param \Shippit\Shipping\Model\ResourceModel\Sync\Order\CollectionFactory $syncOrderCollectionFactory
     */
    public function __construct(
        Context $context,
        \Totaltools\Shippit\Model\Api\Order\Cancel $apiCancel,
        \Shippit\Shipping\Helper\Sync\Order $syncHelper,
        \Shippit\Shipping\Model\Sync\OrderFactory $syncOrderFactory
    )
    {
        $this->apiCancel = $apiCancel;
        $this->syncHelper = $syncHelper;
        $this->syncOrderFactory = $syncOrderFactory;
        parent::__construct($context);
    }

    /**
     * @param $statusFilter
     * @param $orderId
     * @param $toUpdateStatus
     * @param bool $sync
     */
    private function updateSyncQueue($statusFilter, $orderId, $toUpdateStatus, $sync = false)
    {
        $existInShippit = $this->syncOrderFactory->create()
            ->getCollection()
            ->addFieldToFilter('order_id', $orderId)
            ->addFieldToFilter('status', $statusFilter);
        foreach ($existInShippit as $exist) {
            $exist->setStatus($toUpdateStatus)->save();
            if ($sync) {
                $this->_syncCancel($exist);
            }
            else {
                $this->syncResult[$exist->getTrackingNumber()] = [
                    'status' => true,
                    'message' => __('The Tracking number %s (was not sent to Shippit) has just been updated to "Canceled"', $exist->getTrackingNumber())
                ];
            }
        }
    }

    /**
     * @param Order $order
     * @param bool $displayMessage
     * @return array
     */
    public function checkAndCancelOrderInShippit(Order $order, $displayMessage = false)
    {
        // First check if there is any pending calcel request
        $historyCollection = $this->syncOrderFactory->create()
            ->getCollection()
            ->addFieldToFilter('order_id', $order->getId())
            ->addFieldToFilter('status', ['in' => [self::STATUS_CANCEL_PENDING, self::STATUS_CANCEL_FAILED]])
            ->toArray();
        $cancelPending = array_filter($historyCollection['items'], function($item) {
           return @$item['status'] == self::STATUS_CANCEL_PENDING;
        });
        $cancelFailed = array_filter($historyCollection['items'], function($item) {
           return @$item['status'] == self::STATUS_CANCEL_FAILED;
        });
        if (count($cancelPending)) {
            $this->syncResult[$order->getIncrementId()] = [
                'status' => false,
                'message' => __('The Order Can\'t be canceled because there is a pending cancel request')
            ];
        }
        elseif (count($cancelFailed)) {
            $this->syncResult[$order->getIncrementId()] = [
                'status' => false,
                'message' => __('The Order failed to cancel previously')
            ];
        }
        else {
            // Then check if order is synced to Shippit then cancel them all
            $this->updateSyncQueue(
                SyncOrderModel::STATUS_SYNCED,
                $order->getId(),
                self::STATUS_CANCEL_PENDING,
                true,
                $displayMessage
            );
            // check and update pending request to cancel
            $this->updateSyncQueue(
                SyncOrderModel::STATUS_PENDING,
                $order->getId(),
                self::STATUS_CANCELED
            );
            $this->apiCancel->sendEmailErrors(
                $order->getStore()->getId(),
                $order->getStore()->getName()
            );
        }
        return $this->syncResult;
    }

    /**
     * @param SyncOrderModel $syncOrder
     */
    private function _syncCancel(SyncOrderModel $syncOrder)
    {
        if (!$syncOrder->getProcessing()) {
            $syncOrder->setProcessing(true);
            // attempt the sync
            $this->syncResult[$syncOrder->getTrackingNumber()] = $this->apiCancel->sync($syncOrder);
        }
    }
}
