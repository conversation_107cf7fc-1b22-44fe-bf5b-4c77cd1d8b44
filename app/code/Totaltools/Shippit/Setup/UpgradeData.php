<?php
/**
 *  Developed by Balanceinternet
 *  Do not edit or add to this file if you wish to upgrade This to newer
 *  versions in the future.
 *  <AUTHOR>
 *
 */

namespace Totaltools\Shippit\Setup;

use Magento\Cms\Model\BlockFactory;
use Magento\Cms\Model\BlockRepository;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;
use Totaltools\Shippit\Helper\SyncOrder;

/**
 * Data upgrade script
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var BlockFactory
     */
    private $blockFactory;
    /**
     * @var BlockRepository
     */
    private $blockRepository;

    public function __construct(
        BlockFactory $blockFactory,
        BlockRepository $blockRepository
    ) {
        $this->blockFactory = $blockFactory;
        $this->blockRepository = $blockRepository;
    }

    /**
     * {@inheritdoc}
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        if (version_compare($context->getVersion(), '1.1.0', '<')) {
            $this->addRellocationOrderStatus($setup);
        }

        if (version_compare($context->getVersion(), '1.1.1', '<')) {
            $this->addShippitStaticBlock();
        }
        if (version_compare($context->getVersion(), '1.2.0', '<')) {
            $this->addReallocatedOrderStatus($setup);
        }
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    private function addRellocationOrderStatus(ModuleDataSetupInterface $setup)
    {
        $existingSelect = $setup->getConnection()
            ->select()
            ->from(
                ['order_status' => $setup->getTable('sales_order_status')]
            )
            ->columns('status')
            ->where('status = ?', SyncOrder::ORDER_RELLOCATING_STATUS);
        if (!$setup->getConnection()->fetchOne($existingSelect)) {
            /**
             * Install order statuses from config
             */
            $data = [
                ['status' => SyncOrder::ORDER_RELLOCATING_STATUS, 'label' => __(SyncOrder::ORDER_RELLOCATING_STATUS_LABEL)]
            ];
            $setup->getConnection()->insertArray($setup->getTable('sales_order_status'), ['status', 'label'], $data);

            /**
             * Install order states from config
             */
            $data = [
                [
                    'status' => SyncOrder::ORDER_RELLOCATING_STATUS,
                    'state' => 'processing',
                    'is_default' => 0,
                ]
            ];
            $setup->getConnection()->insertArray(
                $setup->getTable('sales_order_status_state'),
                ['status', 'state', 'is_default'],
                $data
            );
        }
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    private function addReallocatedOrderStatus(ModuleDataSetupInterface $setup)
    {
        $existingSelect = $setup->getConnection()
            ->select()
            ->from(
                ['order_status' => $setup->getTable('sales_order_status')]
            )
            ->columns('status')
            ->where('status = ?', SyncOrder::ORDER_RELLOCATED_STATUS);
        if (!$setup->getConnection()->fetchOne($existingSelect)) {
            /**
             * Install order statuses from config
             */
            $data = [
                ['status' => SyncOrder::ORDER_RELLOCATED_STATUS, 'label' => __(SyncOrder::ORDER_RELLOCATED_STATUS_LABEL)]
            ];
            $setup->getConnection()->insertArray($setup->getTable('sales_order_status'), ['status', 'label'], $data);

            /**
             * Install order states from config
             */
            $data = [
                [
                    'status' => SyncOrder::ORDER_RELLOCATED_STATUS,
                    'state' => 'processing',
                    'is_default' => 0,
                ]
            ];
            $setup->getConnection()->insertArray(
                $setup->getTable('sales_order_status_state'),
                ['status', 'state', 'is_default'],
                $data
            );
        }
    }

    /**
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    private function addShippitStaticBlock()
    {
        try {
            $topBlock = [
                'title' => 'Shippit Tracking Top Text',
                'identifier' => 'shippit_tracking_top_text',
                'stores' => ['0', '1'],
                'is_active' => 1,
                'content' => "Please enter the order number which is mentioned in the order confirmation email."
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);

            $sideBarBlock = [
                'title' => 'Shippit Tracking Sidebar',
                'identifier' => 'shippit_tracking_sidebar',
                'stores' => ['0', '1'],
                'is_active' => 1,
                'content' => ""
            ];
            $block = $this->blockFactory->create(['data' => $sideBarBlock]);
            $this->blockRepository->save($block);
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }
}
