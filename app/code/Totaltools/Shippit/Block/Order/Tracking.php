<?php

namespace Totaltools\Shippit\Block\Order;


use Magento\Customer\Model\Session;
use Magento\Framework\View\Element\Template;

class Tracking extends \Magento\Framework\View\Element\Template
{
    private Session $session;

    /**
     * @param Template\Context $context
     * @param Session $session
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        Session $session,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->session = $session;
    }

    /**
     * @return string
     */
    public function getPostActionUrl()
    {
        return $this->getUrl("shippit/order/track");
    }

    /**
     * @return string
     */
    public function getCustomerEmail()
    {
        if ($this->session->isLoggedIn()) {
            return $this->session->getCustomer()->getEmail();
        }

        return "";
    }
}
