<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="../../../../../../../lib/internal/Magento/Framework/View/Layout/etc/page_configuration.xsd">
	<head>
        <title>Online Order Tracking</title>
    </head>
    <body>
        <attribute name="class" value="customer-account-login"/>
        <attribute name="class" value="shippit-order-tracking"/>
        <referenceContainer name="content">
            <container  label="Online Order Tracking" htmlTag="div" htmlClass="login-container">
                <block class="Totaltools\Shippit\Block\Order\Tracking" name="shippit_tracking_index"
                       template="Totaltools_Shippit::shippit_tracking.phtml" cacheable="false">
                    <container name="form.additional.info" label="Form Additional Info">
                        <block class="Magento\ReCaptchaUi\Block\ReCaptcha" name="recaptcha" after="-" template="Magento_ReCaptchaFrontendUi::recaptcha.phtml" ifconfig="recaptcha_frontend/type_for/shippit_order_tracking_captcha">
                            <arguments>
                                <argument name="recaptcha_for" xsi:type="string">shippit_order_tracking_captcha</argument>
                                <argument name="jsLayout" xsi:type="array">
                                    <item name="components" xsi:type="array">
                                        <item name="recaptcha" xsi:type="array">
                                            <item name="component" xsi:type="string">Magento_ReCaptchaFrontendUi/js/reCaptcha</item>
                                        </item>
                                    </item>
                                </argument>
                            </arguments>
                        </block>
                    </container>
                </block>
            </container>
        </referenceContainer>
    </body>
</page>