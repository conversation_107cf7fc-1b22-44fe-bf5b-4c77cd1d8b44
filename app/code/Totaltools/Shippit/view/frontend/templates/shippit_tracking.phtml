<?php

/** @var \Totaltools\Shippit\Block\Order\Tracking $block */
?>
<?php
/**
 * Shippit Order Tracking Form
 *
 * @see \Totaltools\Shippit\Block\Order\Tracking
 * @var $block \Totaltools\Shippit\Block\Order\Tracking
 */
?>

<div class="block block-customer-login">
    <div class="block-box-wrapper">
        <div class="block-title">
            <strong id="block-customer-login-heading" role="heading" aria-level="2"><?php /* @escapeNotVerified */ echo __('Track Your Order') ?></strong>
        </div>
        <div class="block-content" >
            <form class="form form-login"
                  action="<?php /* @escapeNotVerified */  echo $block->getPostActionUrl() ?>"
                  method="post"  target="_blank"
                  id="track_order-form"
                  data-mage-init='{"validation":{}}'>
                <?php echo $block->getBlockHtml('formkey'); ?>
                <fieldset class="fieldset login" data-hasrequired="<?php /* @escapeNotVerified */ echo __('* Required Fields') ?>">
                    <div class="field note">
                        <?php
                        echo $this->getLayout()
                            ->createBlock('Magento\Cms\Block\Block')
                            ->setBlockId('shippit_tracking_top_text')
                            ->toHtml();
                        ?>
                    </div>
                    <div class="field email required">
                        <label class="label" for="email"><span><?php /* @escapeNotVerified */ echo __('Email') ?></span></label>
                        <div class="control">
                            <input
                                name="email"
                                value="<?php echo $block->getCustomerEmail();?>"
                                autocomplete="off"
                                id="email"
                                type="email"
                                class="input-text"
                                title="<?php /* @escapeNotVerified */ echo __('Email') ?>"
                                data-validate="{required:true, 'validate-email':true}"
                                placeholder="<?php echo __('Enter your email address'); ?>">
                        </div>
                    </div>
                    <div class="field order_id required">
                        <label for="order_id" class="label"><span><?php /* @escapeNotVerified */ echo __('Order Id') ?></span></label>
                        <div class="control">
                            <input name="order_id"  type="text" autocomplete="off" class="input-text" id="order_id" title="<?php /* @escapeNotVerified */ echo __('Order ID') ?>" data-validate="{required:true}" placeholder="<?php echo __('Enter Order Id'); ?>">
                            <p class="note"><?php echo __('Please enter the order number which is mentioned in the order confirmation email.')?> </p>
                        </div>
                    </div>

                    <?php echo $this->getChildHtml('form.additional.info'); ?>
                </fieldset>
                <div class="actions-toolbar">
                    <div class="primary"><button type="submit" class="action button-primary-3 login" name="send" id="register"><span><?php /* @escapeNotVerified */ echo __('Track Order') ?></span></button></div>
                </div>
                <p class="required-note"><?php echo __('* Required fields'); ?></p>
            </form>
        </div>
    </div>
</div>
<div class="block block-new-customer">
    <div class="block-box-wrapper">
    <?php
        echo $this->getLayout()
            ->createBlock('Magento\Cms\Block\Block')
            ->setBlockId('shippit_tracking_sidebar')
            ->toHtml();
        ?>
    </div>
</div>
