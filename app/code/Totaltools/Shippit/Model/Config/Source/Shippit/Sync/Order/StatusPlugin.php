<?php
/**
 *  Developed by Balanceinternet
 *  Do not edit or add to this file if you wish to upgrade This to newer
 *  versions in the future.
 *  <AUTHOR>
 *
 */

namespace Totaltools\Shippit\Model\Config\Source\Shippit\Sync\Order;

use Totaltools\Shippit\Helper\SyncOrder;
use Shippit\Shipping\Model\Config\Source\Shippit\Sync\Order\Status;

class StatusPlugin
{
    /**
     * Options getter
     *
     * @return array
     */
    public function afterToOptionArray(Status $subject, $result)
    {
        $result[] = [
            'label' => SyncOrder::STATUS_CANCEL_PENDING_TEXT,
            'value' => SyncOrder::STATUS_CANCEL_PENDING
        ];
        $result[] = [
            'label' => SyncOrder::STATUS_CANCELED_TEXT,
            'value' => SyncOrder::STATUS_CANCELED
        ];
        $result[] = [
            'label' => SyncOrder::STATUS_CANCEL_FAILED_TEXT,
            'value' => SyncOrder::STATUS_CANCEL_FAILED
        ];
        return $result;
    }

    /**
     * Get options in "key-value" format
     *
     * @return array
     */
    public function afterToArray(Status $subject, $result)
    {
        $result[SyncOrder::STATUS_CANCEL_PENDING] = SyncOrder::STATUS_CANCEL_PENDING_TEXT;
        $result[SyncOrder::STATUS_CANCELED] = SyncOrder::STATUS_CANCELED_TEXT;
        $result[SyncOrder::STATUS_CANCEL_FAILED] = SyncOrder::STATUS_CANCEL_FAILED_TEXT;

        return $result;
    }
}
