<?php

/**
 * Shippit Pty Ltd
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the terms
 * that is available through the world-wide-web at this URL:
 * http://www.shippit.com/terms
 *
 * @category   Shippit
 * @copyright  Copyright (c) by Shippit Pty Ltd (http://www.shippit.com)
 * <AUTHOR> <<EMAIL>>
 * @license    http://www.shippit.com/terms
 */

namespace Totaltools\Shippit\Model\Api\Order;

use Exception;
use Magento\Framework\App\Area as AppArea;
use Totaltools\Shippit\Helper\SyncOrder as SyncOrderHelper;
use Shippit\Shipping\Model\Sync\Order as SyncOrder;
use Magento\Store\Model\Store;
use function PHPUnit\Framework\isJson;

class Cancel extends \Shippit\Shipping\Model\Api\Order
{
    /**
     * @var array
     */
    private $syncErrors = [];

    /**
     * @var \Magento\Sales\Model\Order\Email\Container\Template
     */
    protected $templateContainer;

    /**
     * @var \Magento\Framework\Mail\Template\TransportBuilder
     */
    protected $transportBuilder;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;
    /**
     * @var \Magento\Backend\Model\Session
     */
    protected $adminSession;
    /**
     * @var \Shippit\Shipping\Helper\Sync\Shipping
     */
    protected $_helper;
    /**
     * @var  \Magento\Store\Model\StoreManagerInterface
     */
    protected $_storeManagerInterface;
    /**
     * @var \Shippit\Shipping\Model\Sync\OrderFactory
     */
    protected $_syncOrderFactory;
    /**
     * @var   \Shippit\Shipping\Logger\Logger
     */
    protected $_logger;
    /**
     * @var  \Shippit\Shipping\Helper\Api
     */
    protected $api;

    /**
     * @var  \Magento\Store\Model\App\Emulation
     */
    protected $appEmulation;

    /**
     * @var  \Magento\Store\Model\App\Emulation
     */
    protected $date;

    public function __construct(
        \Shippit\Shipping\Helper\Sync\Order $helper,
        \Shippit\Shipping\Helper\Api $api,
        \Shippit\Shipping\Model\Request\OrderFactory $requestOrderFactory,
        \Shippit\Shipping\Model\Sync\OrderFactory $syncOrderFactory,
        \Shippit\Shipping\Logger\Logger $logger,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        \Magento\Store\Model\StoreManagerInterface $storeManagerInterface,
        \Magento\Store\Model\App\Emulation $appEmulation,
        \Magento\Backend\Model\Session $adminSession,
        \Shippit\Shipping\Helper\Sync\Shipping $_helper,
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Sales\Model\Order\Email\Container\Template $templateContainer,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        $this->adminSession = $adminSession;
        $this->_helper = $_helper;
        $this->_storeManagerInterface = $storeManagerInterface;
        $this->_syncOrderFactory = $syncOrderFactory;
        $this->_logger = $logger;
        $this->templateContainer = $templateContainer;
        $this->transportBuilder = $transportBuilder;
        $this->storeRepository = $storeRepository;
        $this->registry = $registry;
        $this->api = $api;
        $this->_appEmulation = $appEmulation;
        $this->_date = $date;
        parent::__construct(
            $helper,
            $api,
            $requestOrderFactory,
            $syncOrderFactory,
            $logger,
            $messageManager,
            $date,
            $storeManagerInterface,
            $appEmulation,
            $context,
            $registry,
            $resource,
            $resourceCollection,
            $data
        );
    }
    /**
     * @return $this
     */
    public function run()
    {
        if (!$this->_helper->isActive()) {
            return $this;
        }

        // get all stores, as we will emulate each storefront for integration run
        $stores = $this->_storeManagerInterface->getStores();

        foreach ($stores as $store) {
            $storeId = $store->getStoreId();

            // Start Store Emulation
            $this->_appEmulation->startEnvironmentEmulation(
                $storeId,
                AppArea::AREA_ADMINHTML
            );

            $syncOrders = $this->getSyncOrders($storeId);

            foreach ($syncOrders as $syncOrder) {
                $this->sync($syncOrder);
            }
            $this->sendEmailErrors($storeId);
            // Stop Store Emulation
            $this->_appEmulation->stopEnvironmentEmulation();
        }
    }

    /**
     * Get a list of sync orders pending cancel sync
     * @return [type] [description]
     */
    public function getSyncOrders($storeId)
    {
        $collection = $this->_syncOrderFactory->create()
            ->getCollection();

        return $collection
            ->join(
                ['order' => $collection->getTable('sales_order')],
                'order.entity_id = main_table.order_id',
                [],
                null,
                'left'
            )
            ->addFieldToFilter(
                'main_table.status',
                SyncOrderHelper::STATUS_CANCEL_PENDING
            )
            ->addFieldToFilter(
                'main_table.attempt_count',
                ['lteq' => SyncOrder::SYNC_MAX_ATTEMPTS]
            )
            ->addFieldToFilter(
                'order.store_id',
                ['eq' => $storeId]
            );
    }

    /**
     * @param $syncOrder
     * @param bool $displayNotifications
     * @return bool
     */
    public function sync($syncOrder, $displayNotifications = false)
    {
        $result = [
            'status' => false,
            'message' => ''
        ];
        /**
         * process cancel only if sync order is active and order was successfully synced(track_number exists)
         */
        if (!$this->_helper->isActive() || !$syncOrder->getTrackingNumber()) {
            $result['message'] = $this->_helper->isActive() ? __('The order sync is not activated') : __('No Tracking number so can\'t be canceled');
            return $result;
        }


        try {
            // increase the attempt count by 1
            $syncOrder->setAttemptCount($syncOrder->getAttemptCount() + 1)->save();
            $order = $syncOrder->getOrder();
            $apiKey = $this->processApiKey($syncOrder);
            //cancel order
            $apiResponse = $this->api->cancelOrderShippit("orders/{$syncOrder->getTrackingNumber()}", $apiKey);
            // an error will response if order can not be cancel in Netsuite
            if (property_exists($apiResponse, 'error')) {
                $state = @$apiResponse->state ? ', Order State In Shippit:' . @$apiResponse->state : '';
                $comment = __('Can not cancel order in Shippit: ' . $apiResponse->error_description . $state);
                $message =  __(
                    'Order %1 can not be canceled in Shippit: %2%3',
                    $order->getIncrementId(),
                    $apiResponse->error_description,
                    $state
                );
                // update status to failed to prevent sending cancel request again
                $syncOrder->setStatus(SyncOrderHelper::STATUS_CANCEL_FAILED);
                $this->syncErrors[] = "<b>{$order->getIncrementId()}<b>: {$apiResponse->error_description}\n";
            } else {
                $comment = __('Successfully cancel Synced Order in Shippit - ' . $apiResponse->tracking_number);
                $message = __('Order ' . $order->getIncrementId()
                    . ' has just been successfully canceled in Shippit - '
                    . $apiResponse->tracking_number);
                $syncOrder->setStatus(SyncOrderHelper::STATUS_CANCELED);
                $result['status'] = true;
            }
            // Add note to the order comment and save
            $order->addStatusHistoryComment($comment)
                ->setIsVisibleOnFront(false)
                ->save();
            // Update the order to be marked as canceled
            $syncOrder->setSyncedAt($this->_date->gmtDate())->save();
            $result['message'] = $message;
        } catch (LocalizedException $e) {
            $this->_logger->addError('API - Order Cancel Request Failed - ' . $e->getMessage());
            $this->syncErrors[] = "<b>{$order->getIncrementId()}<b>: {$e->getMessage()}\n";
            // Fail the sync item if it's breached the max attempts
            if ($syncOrder->getAttemptCount() > SyncOrder::SYNC_MAX_ATTEMPTS) {
                $syncOrder->setStatus(SyncOrderHelper::STATUS_CANCEL_FAILED);
            }

            // save the sync item attempt count
            $syncOrder->save();

            $result['message'] = implode(',', $this->syncErrors);

            $result['status'] = false;
        }

        return $result;
    }

    /**
     * @param $syncOrder
     */
    protected function processApiKey($syncOrder)
    {
        $apiKey = $syncOrder->getApiKey();
        $storelocatorId = (int) $syncOrder->getOrder()->getStorelocatorId();
        if ($storelocatorId && $storelocatorId > 0) {
            $type = (int) $syncOrder->getOrder()->getStorelocatorApiType();
            /** @var \Totaltools\Storelocator\Model\Store $store */
            $store = $this->storeRepository->getById($storelocatorId);
            if ($store->getData()) {
                $apiKey = $type === 1 ? $store->getShippitApiKey() : $store->getShippitApiKeySecond();
            }
        }
        $this->registry->unregister('current_store_order_api_key');
        $this->registry->register('current_store_order_api_key', $apiKey);
        //storing api key in session
        $this->adminSession->unsCurrentStoreOrderApiKey();
        $this->adminSession->setCurrentStoreOrderApiKey($apiKey);
        return $apiKey;
    }

    public function isCancelled($syncOrder)
    {
        $apiKey = $this->processApiKey($syncOrder);
        $apiResponse = $this->api->getOrderLabel("orders/{$syncOrder->getTrackingNumber()}/label", $apiKey );
        $result = false;
        if (!property_exists($apiResponse, 'error')) {
            if ($apiResponse->order->state == 'cancelled') {
                $result = true;
            }
        }
        return $result;
    }

    /**
     * return sync details
     * @return array
     */
    public function getSyncErrors()
    {
        return $this->syncErrors;
    }

    public function sendEmailErrors($storeId = null, $storeName = '', array $message = [])
    {
        $message = $message ?: $this->syncErrors;
        if (!empty($message)) {
            try {
                $storeId = $storeId ?: Store::DEFAULT_STORE_ID;
                $recipients = explode(',', $this->_helper->getValue(SyncOrderHelper::XML_CANCEL_EMAIL_TEMPLATE_PATH));
                $transport = [
                    'error_messages' => implode("\n", $this->syncErrors),
                    'store_name' => $storeName
                ];
                $transportObject = new \Magento\Framework\DataObject($transport);
                $this->templateContainer->setTemplateVars($transportObject->getData());
                $mailer = $this->transportBuilder
                    ->setTemplateIdentifier('shippit_cancel_email_template')
                    ->setTemplateOptions([
                        'area' => AppArea::AREA_FRONTEND,
                        'store' => $storeId,
                    ])
                    ->setTemplateVars($transport)
                    ->setFrom($this->_helper->getValue(SyncOrderHelper::XML_CANCEL_EMAIL_IDENTIFIER_PATH));
                foreach ($recipients as $recipient) {
                    $mailer->addTo($recipient, 'Totaltools');
                }
                $transport = $mailer->getTransport();
                $transport->sendMessage();
            } catch (Exception $e) {
                $this->_logger->addError('Notify - Order Cancel Request Failed Notify - ' . $e->getMessage());
            }
        }
    }
}
