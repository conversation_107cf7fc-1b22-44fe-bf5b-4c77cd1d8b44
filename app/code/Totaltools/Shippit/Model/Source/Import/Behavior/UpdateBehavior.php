<?php
/**
 * Totaltools Shippit.
 *
 * @category   Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright  2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Shippit\Model\Source\Import\Behavior;

/**
 * Class UpdateBehavior
 * @package Totaltools\Shippit\Model\Source\Import\Behavior
 */
class UpdateBehavior extends \Magento\ImportExport\Model\Source\Import\AbstractBehavior
{
    /**
     * {@inheritdoc}
     */
    public function toArray()
    {
        return [
            \Magento\ImportExport\Model\Import::BEHAVIOR_APPEND => __('Update'),
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getCode()
    {
        return 'update';
    }

    /**
     * {@inheritdoc}
     */
    public function getNotes($entityCode)
    {
        return [];
    }
}
