<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Shippit
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="checkout_submit_all_after">
        <observer name="totaltools_shippit_checkout_submit_all_after"
                  instance="Totaltools\Shippit\Observer\SendOrdersToShippitObserver" />
    </event>
    <event name="shippit_add_order">
        <observer name="shippit_add_order_comment_add" instance="Totaltools\Shippit\Observer\Shippit" />
    </event>
    <event name="order_cancel_after">
        <observer name="totaltools_shippit_add_order_cancel_request_to_queue"
                  instance="Totaltools\Shippit\Observer\AddCancelOrderToQueue" />
    </event>
    <!--<event name="sales_order_creditmemo_refund">
        <observer name="totaltools_shippit_add_order_refund_request_to_queue"
                  instance="Totaltools\Shippit\Observer\AddRefundOrderToQueue" />
    </event>-->
</config>
