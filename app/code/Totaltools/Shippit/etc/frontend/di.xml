<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <!-- Module Form ReCaptcha Totaltools Shippit -->
    <virtualType name="Totaltools\Shippit\Model\Provider\Failure\RedirectUrl\Tracking"
                 type="MSP\ReCaptcha\Model\Provider\Failure\RedirectUrl\SimpleUrlProvider">
        <arguments>
            <argument name="urlPath" xsi:type="string">shippit/order/tracking</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\Shippit\Model\Provider\Failure\OrderTrackingForm"
                 type="MSP\ReCaptcha\Model\Provider\Failure\ObserverRedirectFailure">
        <arguments>
            <argument name="redirectUrlProvider"
                      xsi:type="object">Totaltools\Shippit\Model\Provider\Failure\RedirectUrl\Tracking</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\Shippit\Model\Provider\IsCheckRequired\Frontend\Tracking"
                 type="MSP\ReCaptcha\Model\IsCheckRequired">
        <arguments>
            <argument name="enableConfigFlag"
                      xsi:type="string">msp_securitysuite_recaptcha/frontend/shippit_tracking_captcha</argument>
            <argument name="area" xsi:type="string">frontend</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\Shippit\Observer\Frontend\OrderTrackingForm"
                 type="MSP\ReCaptcha\Observer\ReCaptchaObserver">
        <arguments>
            <argument name="isCheckRequired"
                      xsi:type="object">Totaltools\Shippit\Model\Provider\IsCheckRequired\Frontend\Tracking</argument>
            <argument name="failureProvider"
                      xsi:type="object">Totaltools\Shippit\Model\Provider\Failure\OrderTrackingForm</argument>
        </arguments>
    </virtualType>
</config>