<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="shippit">
            <group id="ccsettings" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Click and Collect Settings</label>
                <field id="order_status" translate="label" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="0">
                    <label>Order Status</label>
                    <source_model>Magento\Sales\Model\Config\Source\Order\Status</source_model>
                </field>
            </group>
            <group id="sync_order">
                <field id="cancel_sync_email" translate="label" type="text" sortOrder="70" showInDefault="1"
                       showInWebsite="1" showInStore="0">
                    <label>Canceled Order Sync Error emails</label>
                    <comment>Separated by comma</comment>
                </field>
                <field id="cancel_sync_email_identity" translate="label" type="select" sortOrder="80" showInDefault="1"
                       showInWebsite="1" showInStore="0">
                    <label>Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                </field>
            </group>
            <group id="general">
                <field id="tracking_url" translate="label" type="text" sortOrder="300" showInDefault="1"
                       showInWebsite="1" showInStore="0">
                    <label>Tracking URL</label>
                </field>
            </group>
        </section>
        <section id="recaptcha_frontend">
            <group id="type_for">
                <field id="shippit_order_tracking_captcha" translate="label" type="select" sortOrder="170" showInDefault="1"
                       showInWebsite="1" showInStore="0" canRestore="1">
                    <label>Enable for Online Order Tracking</label>
                    <source_model>Magento\ReCaptchaAdminUi\Model\OptionSource\Type</source_model>
                </field>
               </group>
        </section>
    </system>
</config>
