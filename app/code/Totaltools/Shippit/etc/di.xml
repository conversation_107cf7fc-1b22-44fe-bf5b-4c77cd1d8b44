<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Shippit
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Shippit\Shipping\Model\Request\Order">
        <plugin name="totaltools-shippit-request-order"
                type="Totaltools\Shippit\Plugin\Model\Request\Order" />
    </type>
    <type name="Magento\Sales\Api\OrderManagementInterface">
        <plugin name="shippit_add_order_to_sync_queue" disabled="true"/>
    </type>
    <type name="Shippit\Shipping\Model\Request\SyncOrder">
        <plugin name="totaltools-shippit-request-sync-order"
                type="Totaltools\Shippit\Plugin\Model\Request\SyncOrder" />
    </type>
    <type name="Shippit\Shipping\Api\Data\SyncShipmentInterface">
        <plugin name="totaltools-shippit-order-status-changed-as-completed"
                type="Totaltools\Shippit\Plugin\Api\Data\SyncShipmentInterface" />
    </type>
    <type name="Shippit\Shipping\Controller\Order\Update">
        <plugin disabled="false" name="totaltools-shippit-cc-order-status-update-latest"
                type="Totaltools\Shippit\Plugin\Shippit\Shipping\Controller\Order\Update"/>
    </type>
    <type name="Magento\Quote\Model\Cart\ShippingMethodConverter">
        <plugin name="totaltools-shippit-update-shipping-method-title"
                type="Totaltools\Shippit\Plugin\Shippit\Shipping\ShippingMethodConverterPlugin" />
    </type>
    <type name="Shippit\Shipping\Model\Carrier\Shippit">
        <plugin name="totaltools-shippit-update-uber-quote-price"
                type="Totaltools\Shippit\Plugin\Shippit\Shipping\CollectRatesPlugin" />
    </type>
    <type name="Totaltools\Shippit\Plugin\Helper\Api">
        <arguments>
            <argument name="logger" xsi:type="object">Totaltools\Checkout\Model\Logger\VirtualLogger</argument>
        </arguments>
    </type>
    <preference for="Shippit\Shipping\Logger\Logger" type="Totaltools\Shippit\Logger\Logger"/>
</config>
