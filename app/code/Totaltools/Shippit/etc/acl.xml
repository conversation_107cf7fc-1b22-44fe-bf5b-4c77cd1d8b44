<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
	<acl>
		<resources>
			<resource id="Magento_Backend::admin">
				<resource id="Magento_Backend::stores">
					<resource id="Magento_Backend::stores_settings">
						<resource id="Magento_Config::config">
							<resource id="Totaltools_Shippit::config_totaltools_shippit" title="shippit"/>
						</resource>
					</resource>
				</resource>
				<resource id="Magento_Sales::sales">
					<resource id="Magento_Sales::sales_operation">
						<resource id="Shippit_Shipping::sync_order" title="Shippit Order Sync" sortOrder="11">
							<resource id="Shippit_Shipping::sync_order_view" title="Order Sync view" sortOrder="30" />
						</resource>
						<resource id="Shippit_Shipping::sync_shipment" title="Shippit Shipment Sync" sortOrder="12">
							<resource id="Shippit_Shipping::sync_shipment_view" title="Schedule Shipment Sync view" sortOrder="10" />
						</resource>
						<resource id="Magento_Sales::sales_order">
							<resource id="Magento_Sales::actions">
								<resource id="Shippit_Shipping::order_sync" title="Sync with Shippit" sortOrder="160" />
							</resource>
						</resource>
					</resource>
				</resource>
			</resource>
		</resources>
	</acl>
</config>
