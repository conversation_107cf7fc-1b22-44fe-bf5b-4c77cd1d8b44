<?php
/**
 * Totaltools Shippit.
 *
 * @category   Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright  2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Shippit\Plugin\Sales;
use Magento\Company\Model\ResourceModel\Customer;
/**
 * Class AddSendToShippitButtonPlugin
 *
 * @package Totaltools\Shippit\Plugin\Sales
 */
class AddSendToShippitButtonPlugin
{
    /**
     * @var \Magento\Framework\App\Action\Context
     */
    private $context;

    /**
     * @var \Magento\Backend\Model\UrlInterface
     */
    private $url;

    /**
     * @var \Magento\Sales\Model\OrderRepository
     */
    private $orderRepository;

    /**
     * @var \Magento\Company\Model\CustomerFactory
     */
    private $companyCustomerFactory;

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;
    /**
     * AddSendToShippitButtonPlugin constructor.
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Sales\Model\OrderRepository $orderRepository
     * @param \Magento\Company\Model\CustomerFactory $companyCustomerFactory
     * @param \Magento\Backend\Model\UrlInterface $url
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Sales\Model\OrderRepository $orderRepository,
        \Magento\Company\Model\CustomerFactory $companyCustomerFactory,
        \Magento\Backend\Model\UrlInterface $url,
        \Magento\Framework\Registry $registry,
        Customer  $companyCustomer
    ) {
        $this->context = $context;
        $this->orderRepository = $orderRepository;
        $this->companyCustomerFactory = $companyCustomerFactory;
        $this->url = $url;
        $this->_coreRegistry = $registry;
        $this->companyCustomer = $companyCustomer;
    }

    /**
     * @param \Magento\Backend\Block\Widget\Context $subject
     * @param $buttonList
     *
     * @return mixed
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function afterGetButtonList(
        \Magento\Backend\Block\Widget\Context $subject,
        $buttonList
    ) {
        $request = $this->context->getRequest();
        $order = $this->getOrder();

        if (($request->getFullActionName() == 'sales_order_view') &&
            (($order->getState() == \Magento\Sales\Model\Order::STATE_PROCESSING) ||
                ($order->getState() == \Magento\Sales\Model\Order::STATE_COMPLETE))) {
            $orderId = $request->getParam('order_id');
            $order = $this->orderRepository->get($orderId);

            if ($customerId = $order->getCustomerId()) {

                $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId($customerId);
                $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
                $b2bCustomer =  array_diff($currentCompanyIds, [0]);
                if (!empty($b2bCustomer)) {
                    return $buttonList;
                }

            }
            $message = __('Are you sure you want to send this order to Shippit?');
            $buttonList->add(
                'shippit_send_order',
                [
                    'label' => __('Send to Shippit'),
                    'onclick' => "confirmSetLocation('{$message}', '{$this->getShippitOrderSyncUrl($request)}')",
                    'class' => 'ship'
                ],
                100
            );
        }

        return $buttonList;
    }

    public function getShippitOrderSyncUrl($request)
    {
        $orderId = $request->getParam('order_id');

        return $this->url->getUrl(
            'shippit/order/sync',
            [
                'order_id' => $orderId
            ]
        );
    }


    /**
     * Retrieve order model object
     *
     * @return \Magento\Sales\Model\Order
     */
    public function getOrder()
    {
        return $this->_coreRegistry->registry('sales_order');
    }
}
