<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Shippit
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Shippit\Plugin\Model\Request;

/**
 * Class Order.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Order
{
    /**
     * @var \Magento\Framework\Registry
     */
    private $_registry;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;


    /**
     * The order object
     * @var Magento\Sales\Model\Order
     */
    protected $_order;
    /**
     * @var \Magento\Backend\Model\Session
     */
    protected $adminSession;

    /**
     * Order constructor.
     * @param \Magento\Framework\Registry $registry
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     */
    public function __construct(
        \Magento\Framework\Registry $registry,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Backend\Model\Session $adminSession
    ) {
        $this->_registry = $registry;
        $this->storeRepository = $storeRepository;
        $this->adminSession = $adminSession;
    }

    /**
     * @param \Shippit\Shipping\Model\Request\Order $subject
     * @param \Shippit\Shipping\Model\Sync\Order $syncOrder
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function beforeProcessSyncOrder(
        \Shippit\Shipping\Model\Request\Order $subject,
        \Shippit\Shipping\Model\Sync\Order $syncOrder
    ) {
        $apiKey = $syncOrder->getApiKey();
        $storelocatorId = (int) $syncOrder->getOrder()->getStorelocatorId();
        if ($storelocatorId && $storelocatorId > 0) {
            $type = (int) $syncOrder->getOrder()->getStorelocatorApiType();
            /** @var \Totaltools\Storelocator\Model\Store $store */
            $store = $this->storeRepository->getById($storelocatorId);
            if ($store->getData())
            {
                $apiKey = $type === 1 ? $store->getShippitApiKey() : $store->getShippitApiKeySecond();
            }
        }
        $this->_registry->unregister('current_store_order_api_key');
        $this->_registry->register('current_store_order_api_key', $apiKey);
        //storing api key in session
        $apiKey = $syncOrder->setApiKey($apiKey);
        $this->adminSession->unsCurrentStoreOrderApiKey();
        $this->adminSession->setCurrentStoreOrderApiKey($apiKey);
    }

    /**
     * Set the order to be sent to the api  request
     *
     * @param object $order The Order Request
     */
    public function afterSetOrder(
        \Shippit\Shipping\Model\Request\Order $subject,
        $result,
        $order
    ) {

        if ($order instanceof \Magento\Sales\Model\Order) {
            $this->_order = $order;
        }
        else {
            $this->_order = $subject->load($order);
        }

        $billingAddress = $this->_order->getBillingAddress();
        $shippingMethod = $order->getShippingMethod();

        if($shippingMethod == 'shippitcc_shippitcc') {
            $subject
            ->setReceiverName($billingAddress->getName())
                ->setReceiverContactNumber($billingAddress->getTelephone())
                ->setDeliveryCompany($billingAddress->getCompany())
                ->setDeliveryAddress(implode(' ', $billingAddress->getStreet()))
                ->setDeliverySuburb($billingAddress->getCity())
                ->setDeliveryPostcode($billingAddress->getPostcode())
                ->setDeliveryState($billingAddress->getRegionCode())
                ->setDeliveryCountry($billingAddress->getCountryId());
        }

        return $result;
    }

}
