<?php

namespace Totaltools\Shippit\Plugin\Model\Request;

use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;

/**
 * Class SyncOrder
 * @package Totaltools\Shippit\Plugin\Model\Request
 */
class SyncOrder
{
    /**
     * @var \Magento\Sales\Api\Data\OrderItemInterface
     */
    private $orderItemInterface;

    /**
     * @var \Shippit\Shipping\Helper\Sync\Order\Items
     */
    private $itemsHelper;

    /**
     * @var \Magento\Directory\Helper\Data
     */
    private $directoryHelper;

    /**
	 * @var LoggerInterface
	 */
	private $logger;

    /**
     * SyncOrder constructor.
     * @param \Magento\Sales\Api\Data\OrderItemInterface $orderItemInterface
     * @param \Shippit\Shipping\Helper\Sync\Order\Items $itemsHelper
     * @param \Magento\Directory\Helper\Data $directoryHelper
     * @param LoggerInterface $logger
     */
    public function __construct(
        \Magento\Sales\Api\Data\OrderItemInterface $orderItemInterface,
        \Shippit\Shipping\Helper\Sync\Order\Items $itemsHelper,
        \Magento\Directory\Helper\Data $directoryHelper,
        LoggerInterface $logger
    ) {
        $this->orderItemInterface = $orderItemInterface;
        $this->itemsHelper = $itemsHelper;
        $this->directoryHelper = $directoryHelper;
        $this->logger = $logger;
    }

    /**
     * @param \Shippit\Shipping\Model\Request\SyncOrder $subject
     * @param \Closure $proceed
     * @param array $items
     * @return \Shippit\Shipping\Model\Request\SyncOrder
     * @throws LocalizedException
     */
    public function aroundSetItems(
        \Shippit\Shipping\Model\Request\SyncOrder $subject,
        \Closure $proceed,
        $items = []
    ) {
        $itemsCollection = $this->orderItemInterface
            ->getCollection()
            ->addFieldToFilter('order_id', $subject->getOrderId());

        if (!empty($items)) {
            $itemsSkus = $this->itemsHelper->getSkus($items);

            if (!empty($itemsSkus)) {
                $itemsCollection = $itemsCollection->addFieldToFilter(
                    'sku',
                    [
                        'in' => $itemsSkus
                    ]
                );
            }
        }

        if ($itemsCollection->getSize() < 1) {
            $this->logger->info('$itemsCollection count is zero for order_id: ' . $subject->getOrderId());
        }

        $itemsAdded = 0;

        foreach ($itemsCollection as $item) {
            if ($item->isDummy(true) || $item->getIsVirtual()) {
                $this->logger->info('order_id: ' . $subject->getOrderId() . ' item: ' . $item->getId() .' is dummy or virtual sku: ' . $item->getSku());
                continue;
            }

            $itemQty = $this->getItemQty($items, $item);

            if ($itemQty <= 0) {
                $this->logger->info('order_id: ' . $subject->getOrderId() . ' item: ' . $item->getId() .' has qty 0 or less sku: ' . $item->getSku());
                continue;
            }

            $subject->addItem(
                $item->getSku(),
                $this->getItemName($item),
                $itemQty,
                $this->getBasicItemPrice($item),
                $item->getWeight(),
                $this->getItemLength($item),
                $this->getItemWidth($item),
                $this->getItemDepth($item),
                $this->getItemLocation($item),
                $this->getItemTariffCode($item),
                $this->getOriginCountryCode($item)
            );

            $itemsAdded++;
        }

        if ($itemsAdded == 0) {
            $this->logger->info('$itemsAdded is zero for order_id: ' . $subject->getOrderId());
            throw new LocalizedException(
                __(\Shippit\Shipping\Model\Request\SyncOrder::ERROR_NO_ITEMS_AVAILABLE_FOR_SHIPPING)
            );
        }

        return $subject;
    }

    /**
     * Get Price Item
     *
     * @param $item
     * @return float
     */
    private function getBasicItemPrice($item)
    {
        $rowTotalAfterDiscounts = $item->getRowTotalInclTax() - $item->getDiscountAmount();
        $itemPrice = $rowTotalAfterDiscounts / $item->getQtyOrdered();

        return round($itemPrice, 2);
    }

    /**
     * @param $items
     * @param $item
     * @return int|null
     */
    private function getItemQty($items, $item)
    {
        $requestedQty = $this->getRequestedQuantity($items, 'item_id', $item->getItemId(), 'qty');

        return $this->itemsHelper->getQtyToShip($item, $requestedQty);
    }

    /**
     * @param $items
     * @param $itemKey
     * @param $itemSku
     * @param $itemDataKey
     * @return bool
     */
    private function getRequestedQuantity($items, $itemKey, $itemSku, $itemDataKey)
    {
        return $this->itemsHelper->getItemData($items, $itemKey, $itemSku, $itemDataKey);
    }

    /**
     * @param $item
     * @return string|null
     */
    private function getItemName($item)
    {
        $childItem = $this->getChildItem($item);

        return $childItem->getName();
    }

    /**
     * @param $item
     * @return object
     */
    private function getChildItem($item)
    {
        if ($item->getHasChildren()) {
            $rootItem = $this->getRootItem($item);
            if ($rootItem->getProductType() == 'bundle') {
                // if we are sending the bundle together
                if ($rootItem->getId() == $item->getId()) {
                    return $rootItem;
                } else {
                    $items = $item->getChildrenItems();

                    return reset($items);
                }
            } else {
                $items = $item->getChildrenItems();

                return reset($items);
            }
        } else {
            return $item;
        }
    }

    /**
     * @param $item
     * @return object
     */
    private function getRootItem($item)
    {
        if ($item->getParentItem()) {
            return $item->getParentItem();
        } else {
            return $item;
        }
    }

    /**
     * @param $item
     * @return float|void|null
     */
    private function getItemLength($item)
    {
        if (!$this->itemsHelper->isProductDimensionActive()) {
            return null;
        }

        $childItem = $this->getChildItem($item);

        return $this->itemsHelper->getLength($childItem);
    }

    /**
     * @param $item
     * @return float|void|null
     */
    private function getItemWidth($item)
    {
        if (!$this->itemsHelper->isProductDimensionActive()) {
            return null;
        }

        $childItem = $this->getChildItem($item);

        return $this->itemsHelper->getWidth($childItem);
    }

    /**
     * @param $item
     * @return float|void|null
     */
    private function getItemDepth($item)
    {
        if (!$this->itemsHelper->isProductDimensionActive()) {
            return null;
        }

        $childItem = $this->getChildItem($item);

        return $this->itemsHelper->getDepth($childItem);
    }

    /**
     * @param $item
     * @return string|void|null
     */
    private function getItemLocation($item)
    {
        if (!$this->itemsHelper->isProductLocationActive()) {
            return null;
        }

        $childItem = $this->getChildItem($item);

        return $this->itemsHelper->getLocation($childItem);
    }

    /**
     * @param $item
     * @return string|void
     */
    private function getItemTariffCode($item)
    {
        if (!$this->itemsHelper->isProductTariffCodeActive()) {
            return null;
        }

        $rootItem = $this->getRootItem($item);
        $childItem = $this->getChildItem($item);

        $tariffCode = $this->itemsHelper->getTariffCode($childItem);

        if (
            $rootItem != $childItem
            && empty($tariffCode)
        ) {
            $tariffCode = $this->itemsHelper->getTariffCode($rootItem);
        }

        return $tariffCode;
    }

    /**
     * @param $item
     * @return string|void
     */
    private function getOriginCountryCode($item)
    {
        if (!$this->itemsHelper->isProductOriginCountryCodeActive()) {
            return;
        }

        $rootItem = $this->getRootItem($item);
        $childItem = $this->getChildItem($item);
        $originCountryCode = $this->itemsHelper->getOriginCountryCode($childItem);

        if (
            $rootItem != $childItem
            && empty($originCountryCode)
        ) {
            $originCountryCode = $this->itemsHelper->getOriginCountryCode($rootItem);
        }

        if (strlen($originCountryCode) > 2) {
            $countryCollection = $this->directoryHelper->getCountryCollection();
            $countryData = [];

            foreach ($countryCollection as $country) {
                $countryData[] = [
                    'name' => $country->getName(),
                    'iso2_code' => $country->getData('iso2_code'),
                    'iso3_code' => $country->getData('iso3_code'),
                ];
            }

            // Attempt to lookup using the name or iso3 code
            $countriesFound = array_filter($countryData, function ($country) use ($originCountryCode) {
                return (
                    $country['iso3_code'] == $originCountryCode
                    || $country['name'] == $originCountryCode
                );
            });

            // If we have at least 1 country match, set this as the origin country code
            if (!empty($countriesFound)) {
                $originCountryCode = reset($countriesFound)['iso2_code'];
            }
        }

        return $originCountryCode;
    }
}
