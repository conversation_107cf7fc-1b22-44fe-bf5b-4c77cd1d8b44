<?php
namespace Totaltools\Shippit\Plugin\Shippit\Shipping;

class ShippingMethodConverterPlugin
{
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    public function afterModelToDataObject(
        \Magento\Quote\Model\Cart\ShippingMethodConverter  $subject,
        $result
    ) {
        if($result->getMethodCode()  ==  'ondemand') {
            $methodTitle = !empty($this->getUberOnDemandMethodTitle()) ? $this->getUberOnDemandMethodTitle() : $result->getMethodTitle();
            $result->setMethodTitle($methodTitle);
        }
        return  $result;
    }

    /**
     * @return mixed
     */
    public function getUberOnDemandMethodTitle()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/title', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }
}
