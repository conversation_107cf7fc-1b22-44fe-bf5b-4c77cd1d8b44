<?php

namespace Totaltools\Shippit\Plugin\Shippit\Shipping;

class CollectRatesPlugin
{
    /**
     * The default value for Uber on-demand custom quote price configuration.
     */
    const UBER_ONDEMAND_CUSTOM_QUOTE_PRICE = 999;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var \Shippit\Shipping\Helper\Carrier\Shippit
     */
    protected $_helper;

    /**
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Shippit\Shipping\Helper\Carrier\Shippit $helper
     */
    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Shippit\Shipping\Helper\Carrier\Shippit           $helper
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->_helper = $helper;
    }

    /**
     * @param \Shippit\Shipping\Model\Carrier\Shippit $subject
     * @param $result
     * @return mixed
     */
    public function afterCollectRates(
        \Shippit\Shipping\Model\Carrier\Shippit $subject,
        $result
    ) {
        if ($result && $result instanceof \Magento\Shipping\Model\Rate\Result) {
            foreach ($result->getAllRates() as $rate) {
                if ($rate->getMethod() === 'ondemand') {
                    $price = floatval($this->getUberCustomQuotePrice());
                    if ($price != self::UBER_ONDEMAND_CUSTOM_QUOTE_PRICE) {
                        $rate->setPrice($price);
                        $rate->setCost($this->_getQuotePrice($price));
                    }
                }
            }
        }

        return $result;
    }

    /**
     * @return mixed
     */
    public function getUberCustomQuotePrice()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_ondemand_custom_quote_price', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * Get the quote price, including the margin amount if enabled
     * @param float $quotePrice The quote amount
     * @return float             The quote amount, with margin if applicable
     */
    protected function _getQuotePrice($quotePrice)
    {
        switch ($this->_helper->getMargin()) {
            case 'fixed':
                $quotePrice += (float)$this->_helper->getMarginAmount();
                break;
            case 'percentage':
                $quotePrice *= (1 + ((float)$this->_helper->getMarginAmount() / 100));
                break;
        }

        // ensure we get the lowest price, but not below 0.
        $quotePrice = max(0, $quotePrice);

        return $quotePrice;
    }
}
