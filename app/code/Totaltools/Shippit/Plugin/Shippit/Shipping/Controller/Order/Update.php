<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Shippit
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Shippit\Plugin\Shippit\Shipping\Controller\Order;
use \Magento\Sales\Model\OrderFactory;
use \Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Update extends \Shippit\Shipping\Controller\Order\Update
{

    /**
     * order status xml path
     */
    const XML_PATH_ORDER_STATUS = 'shippit/ccsettings/order_status';

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var \Magento\Framework\Json\Helper\Data
     */
    protected $_jsonHelper;

    /**
     * @var \Shippit\Shipping\Model\Sync\OrderFactory
     */
    protected $syncOrderFactory;

    /**
     * @var \Totaltools\Shippit\Model\Api\Order\Cancel
     */
    protected $apiCancel;

    /**
     * @var \Shippit\Shipping\Logger\Logger
     */
    protected $logger;

    /**
     * @var OrderFactory
     */
    protected $orderFactory;

    /**
     * Update constructor.
     * @param OrderFactory $orderFactory
     * @param ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Shippit\Shipping\Model\Sync\OrderFactory $syncOrderFactory
     * @param \Totaltools\Shippit\Model\Api\Order\Cancel $apiCancel
     */
    public function __construct(
        OrderFactory $orderFactory,
        ScopeConfigInterface $scopeConfig,
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Shippit\Shipping\Model\Sync\OrderFactory $syncOrderFactory,
        \Totaltools\Shippit\Model\Api\Order\Cancel $apiCancel,
        \Shippit\Shipping\Logger\Logger $logger

    ) {
        parent::__construct($context, $resultPageFactory, $storeManager);
        $this->orderFactory = $orderFactory;
        $this->scopeConfig = $scopeConfig;
        $this->syncOrderFactory = $syncOrderFactory;
        $this->apiCancel = $apiCancel;
        $this->logger = $logger;
    }

    /**
     * Update CC order status
     *
     * @param \Shippit\Shipping\Controller\Order\Update $subject
     * @param callable $proceed
     * @return mixedUpdate CC order status
     */
    public function aroundExecute(
        \Shippit\Shipping\Controller\Order\Update $subject,
        callable $proceed
    ) {

        try {
            $request = $this->_getRequest();
            $orderIncrement = $this->_getOrderIncrement($request);
            $trackingNumber = $this->_getTrackingNumber($request);
            $courierName = $this->_getCourierName($request);
            $currentState = !empty($request['current_state']) ? $request['current_state'] : '';
            $apiKey = $this->getRequest()->getParam('api_key');
            $logMessage = "apiKey: " . $apiKey . " orderNumber: " . $orderIncrement . " trackingNumber: " . $trackingNumber . " courierName: " . $courierName . " currentState: " . $currentState;
            $this->logger->info("shippit webhook logs: ". $logMessage);
            if ($request['current_state'] == 'ready_for_pickup'
                && $request['courier_name'] == 'Click & Collect') {
                $shippitSyncOrder = $this->syncOrderFactory->create()->load($this->_getTrackingNumber($request), 'tracking_number');

                $isCancelled = $this->apiCancel->isCancelled($shippitSyncOrder);
                if (!$isCancelled) {
                    // attempt to retrieve request data values for the shipment
                    $order = $this->orderFactory->create()->load($orderIncrement, 'increment_id');
                    $currentOrderStatus = $order->getStatus();
                    $restrictedStatus = ['closed', 'pending', 'canceled', 'complete'];
                    if (!in_array($currentOrderStatus, $restrictedStatus)) {
                        $orderStatus = $this->scopeConfig->getValue(
                            self::XML_PATH_ORDER_STATUS, ScopeInterface::SCOPE_STORE, $order->getStoreId()
                        );
                        $comment = 'Order status change from the shipping webhook.';
                        $order->setStatus($orderStatus);
                        $order->addStatusToHistory($order->getStatus(), $comment);
                        $order->save();
                    }
                }
            }
            $result = $proceed();
            return $result;
        }  catch (\Exception $e) {
            $this->logger->info("exception_ready_for_pickup:".$e->getMessage());
        }
    }
}
