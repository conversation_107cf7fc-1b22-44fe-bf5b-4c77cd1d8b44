<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Shippit
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Shippit\Plugin\Api\Data;

use Magento\Sales\Model\OrderFactory;
use Totaltools\Shippit\Helper\SyncOrder;

class SyncShipmentInterface
{
    /**
     * order status xml path
     */
    const XML_PATH_ORDER_STATUS = 'shippit/ccsettings/order_status';

    /**
     * @var OrderFactory
     */
    private $orderFactory;

    /**
     * SyncShipmentInterface constructor.
     * @param OrderFactory $orderFactory
     */
    public function __construct(
        OrderFactory $orderFactory
    ) {
        $this->orderFactory = $orderFactory;
    }

    /**
     * @param \Shippit\Shipping\Api\Data\SyncShipmentInterface $subject
     * @param $result
     */
    public function afterSave(
        \Shippit\Shipping\Api\Data\SyncShipmentInterface $subject,
        $result
    ){
        $orderIncrement = $result->getOrderIncrement();
        $order = $this->orderFactory->create()->load($orderIncrement, 'increment_id');

        if ($order->getStatus() == SyncOrder::ORDER_RELLOCATING_STATUS) {
            $comment = 'Webhook: created shipping but keep status reallocating';
            $order->addStatusToHistory($order->getStatus(), $comment);
        }
        else {
            if ($order->getStatus() != \Magento\Sales\Model\Order::STATE_COMPLETE) {
                $comment = 'Order status changed as completed from the shipping webhook.';
                $order->setStatus(\Magento\Sales\Model\Order::STATE_COMPLETE);
                $order->addStatusToHistory($order->getStatus(), $comment);
            }
        }
        $order->save();
    }
}