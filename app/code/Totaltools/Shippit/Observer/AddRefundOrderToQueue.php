<?php

namespace Totaltools\Shippit\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;
use Shippit\Shipping\Model\Sync\Order as SyncOrder;
use Totaltools\Shippit\Helper\SyncOrder as SyncOrderHelper;
use Shippit\Shipping\Model\Config\Source\Shippit\Sync\Order\Mode;
use Totaltools\Storelocator\Model\StoreShippitService;
use Totaltools\Storelocator\Model\StoreRepository;
use Magento\Framework\Message\ManagerInterface;

/**
 * Class AddCancelOrderToQueue
 * @package Totaltools\Shippit\Observer
 */
class AddRefundOrderToQueue implements ObserverInterface
{
    /**
     * @var SyncOrderHelper
     */
    private $syncOrderHelper;
    /**
     * @var \Psr\Log\LoggerInterface|\Shippit\Shipping\Logger\Logger
     */
    private $logger;

    /**
     * @var StoreShippitService
     */
    private $shippitService;

    /**
     * @var StoreRepository
     */
    private $storeRepository;

    /**
     * @var ManagerInterface
     */
    private $messgeManager;

    /**
     * AddCancelOrderToQueue constructor.
     * @param SyncOrderHelper $syncOrderHelper
     * @param \Shippit\Shipping\Model\ResourceModel\Sync\Order\CollectionFactory $syncOrderCollectionFactory
     * @param \Shippit\Shipping\Logger\Logger $logger
     */
    public function __construct(
        SyncOrderHelper $syncOrderHelper,
        \Shippit\Shipping\Logger\Logger $logger,
        StoreShippitService $shippitService,
        StoreRepository $storeRepository,
        ManagerInterface $messgeManager
    ) {
        $this->syncOrderHelper = $syncOrderHelper;
        $this->logger = $logger;
        $this->shippitService = $shippitService;
        $this->storeRepository = $storeRepository;
        $this->messgeManager = $messgeManager;
    }

    /**
     * Send cancel request only if order was sent to Shippit(sync order exist and status is not STATUS_FAILED and status is not STATUS_PENDING)
     * @param \Magento\Framework\Event\Observer $observer
     *
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @event sales_order_creditmemo_refund
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        try {
            /** @var Order $order */
            $order = $observer->getEvent()->getCreditmemo()->getOrder();
            $this->syncOrderHelper->checkAndCancelOrderInShippit($order);
            $storeLocator = $this->storeRepository->getById($order->getStorelocatorId());
            $result = $this->shippitService->sendOrder($order, $storeLocator);
            $message = __('Order was cancelled in Shippit');
            if ($result) {
                $message .= __(', left items were resynced to Shippit');
            }
            $this->messgeManager->addSuccessMessage($message);
        }
        catch (\Exception $e) {
            $this->logger->addError('API - Order Refund - Cancel Request Failed - ' . $e->getMessage());
            $this->messgeManager->addErrorMessage($e->getMessage());
        }
    }
}
