<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Shippit
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Shippit\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;
use Shippit\Shipping\Model\Sync\Order as SyncOrder;
use Totaltools\Shippit\Helper\SyncOrder as SyncOrderHelper;
use Shippit\Shipping\Model\Config\Source\Shippit\Sync\Order\Mode;
/**
 * Class AddCancelOrderToQueue
 * @package Totaltools\Shippit\Observer
 */
class AddCancelOrderToQueue implements ObserverInterface
{
    /**
     * @var SyncOrderHelper
     */
    private $syncOrderHelper;
    /**
     * @var \Psr\Log\LoggerInterface|\Shippit\Shipping\Logger\Logger
     */
    private $logger;

    /**
     * AddCancelOrderToQueue constructor.
     * @param SyncOrderHelper $syncOrderHelper
     * @param \Shippit\Shipping\Model\ResourceModel\Sync\Order\CollectionFactory $syncOrderCollectionFactory
     * @param \Shippit\Shipping\Logger\Logger $logger
     */
    public function __construct(
        SyncOrderHelper $syncOrderHelper,
        \Shippit\Shipping\Logger\Logger $logger
    ) {
        $this->syncOrderHelper = $syncOrderHelper;
        $this->logger = $logger;
    }

    /**
     * Send cancel request only if order was sent to Shippit(sync order exist and status is not STATUS_FAILED and status is not STATUS_PENDING)
     * There might be a timer issue when order is posting to Shipping and cancel in Magento, in this case cancel order won't be updated to Shippit
     * This should be monitored and introduce order lock if order is posting to Shippit
     * @param \Magento\Framework\Event\Observer $observer
     *
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @event order_cancel_after
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /** @var Order $order */
        $order = $observer->getOrder();
        try {
            $this->syncOrderHelper->checkAndCancelOrderInShippit($order);
        }
        catch (\Exception $e) {
            $this->logger->addError('API - Order Cancel Request Failed - ' . $e->getMessage());
        }
    }
}
