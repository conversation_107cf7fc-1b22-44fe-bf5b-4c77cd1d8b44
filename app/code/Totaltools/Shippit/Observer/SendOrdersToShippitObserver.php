<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Shippit
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Shippit\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;
use Magento\Company\Model\ResourceModel\Customer;

/**
 * Class SendOrderToShippitObserver.
 *
 * @category  Totaltools
 * @package   Totaltools_Shippit
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class SendOrdersToShippitObserver implements ObserverInterface
{
    /**
     * @var \Totaltools\Storelocator\Model\StoreShippitService
     */
    private $_shippitService;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $_storeRepository;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $_logger;

    /**
     * @var \Totaltools\Storelocator\Helper\Checkout\Data
     */
    protected $_checkoutHelper;

    /**
     * @var \Totaltools\Storelocator\Model\StoreInventoryService
     */
    private $storeInventoryService;

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    private $config;

    /**
     * @var \Magento\Quote\Model\Quote\AddressFactory
     */
    private $quoteAddress;

    /**
     * @var \Magento\Company\Model\CustomerFactory
     */
    private $companyCustomerFactory;
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;
    /**
     * SendOrderToShippitObserver constructor.
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\StoreShippitService $shippitService
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper
     * @param \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig
     * @param \Magento\Company\Model\CustomerFactory $companyCustomerFactory
     * @param \Magento\Quote\Model\Quote\AddressFactory $quoteAddressFactory
     */
    public function __construct(
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreShippitService $shippitService,
        \Psr\Log\LoggerInterface $logger,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper,
        \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        \Magento\Company\Model\CustomerFactory $companyCustomerFactory,
        \Magento\Quote\Model\Quote\AddressFactory $quoteAddressFactory,
        Customer  $companyCustomer
    ) {
        $this->_storeRepository = $storeRepository;
        $this->_shippitService = $shippitService;
        $this->_logger = $logger;
        $this->_checkoutHelper = $checkoutHelper;
        $this->storeInventoryService = $storeInventoryService;
        $this->config = $totalToolsConfig;
        $this->companyCustomerFactory = $companyCustomerFactory;
        $this->quoteAddress = $quoteAddressFactory;
        $this->companyCustomer = $companyCustomer;
    }

    /**
     * Execute method.
     *
     * @param \Magento\Framework\Event\Observer $observer
     *
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @event checkout_submit_all_after
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /** @var Order $order */
        $order = $observer->getOrder();
        $customerId = $order->getCustomerId();
        $this->_logger->info(__('SHIPPIT OBSERVER CALLED:'));
        if($customerId == null){
            $this->_sendOrderToShippit($order);
        }else{
            $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId($customerId);
            $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
            $b2bCustomer =  array_diff($currentCompanyIds, [0]);
            if (empty($b2bCustomer)) {
                $this->_sendOrderToShippit($order);
            }
        }

    }

    /**
     * Send order to Shippit account.
     *
     * @param Order $order
     *
     * @return void
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function _sendOrderToShippit(Order $order)
    {

        if ($order) {

            $storeId = $order->getStorelocatorId();

            if (!empty($storeId)) {
                /** @var \Totaltools\Storelocator\Model\Store $store */
                $store = $this->_storeRepository->getById($storeId);

                try {
                    /** @var \Shippit\Shipping\Model\Shippit $syncOrderObject */
                    $syncOrderObject = $this->_shippitService->sendOrder($order, $store);
                    $this->_logger->info(print_r($syncOrderObject->getData(), true));
                    $this->_logger->info(__('SHIPPIT OBSERVER: Order# ' . $order->getIncrementId() . ' has been sent to Shippit account successfully. Store ID: ' . $storeId));
                } catch (\Magento\Framework\Exception\NotFoundException $exception) {
                    $this->_logger->error($exception->getMessage());
                }
            } else {

                $this->_logger->info(__('SHIPPIT OBSERVER: Unable to sync Order# ' . $order->getIncrementId() . ' to Shippit account.'));
            }
        }
    }

    /**
     * @param Order $order
     * @param $store
     * @return bool
     */
    private function checkInventoryItem(Order $order, $store)
    {
        $items = $order->getItems();
        $productIds = [];
        foreach ($items as $item) {
            $productIds[$item->getSku()] = $item->getQtyOrdered();
        }
        $hasInventory = $this->storeInventoryService->checkStoreInventory($store, $productIds);

        if ($hasInventory) {
            return true;
        }

        return false;
    }
}
