<?php

namespace Totaltools\ReviewGraphQl\Model\Resolver;

use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;
use Magento\CatalogGraphQl\Model\Resolver\Product\ProductFieldsSelector;
use Magento\CatalogGraphQl\Model\Resolver\Products\DataProvider\Deferred\Product as ProductDataProvider;
use Magento\Framework\GraphQl\Query\Resolver\ValueFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;


class GetProductReviews implements ResolverInterface
{
     /**
     * @var ProductDataProvider
     */
    private $productDataProvider;

    /**
     * @var ValueFactory
     */
    private $valueFactory;

    /**
     * @var ProductFieldsSelector
     */
    private $productFieldsSelector;

    /**
	 * @var ProductRepositoryInterface
	 */
	protected $productRepository = null;

    /**
     * Product 
     *
     * @var Product
     */
    public $product;

    public function __construct(
        ProductDataProvider $productDataProvider,
        ProductRepositoryInterface $productRepository,
        ValueFactory $valueFactory,
        ProductFieldsSelector $productFieldsSelector
    ) {
        $this->productDataProvider = $productDataProvider;
        $this->valueFactory = $valueFactory;
        $this->productRepository = $productRepository;
        $this->productFieldsSelector = $productFieldsSelector;
    }

    public function resolve(Field $field, $context, ResolveInfo $info, array $value = null, array $args = null)
    {
        if (!isset($args['sku'])) {
            throw new GraphQlInputException(__('No sku found for product link.'));
        }
        $this->productDataProvider->addProductSku($args['sku']);
        $fields = $this->productFieldsSelector->getProductFieldsFromInfo($info);
        $this->productDataProvider->addEavAttributes($fields);
        $this->product =  $this->productRepository->get($args['sku']);
        $result = function () use ($args) {
            $data = $this->productRepository->get($args['sku']);
            if (empty($data)) {
                return null;
            }
            if (!isset($data['model'])) {
                throw new LocalizedException(__('"model" value should be specified'));
            }
            $productModel = $data['model'];
            /** @var \Magento\Catalog\Model\Product $productModel */
            $data = $productModel->getData();
            $data['model'] = $productModel;

            if (!empty($productModel->getCustomAttributes())) {
                foreach ($productModel->getCustomAttributes() as $customAttribute) {
                    if (!isset($data[$customAttribute->getAttributeCode()])) {
                        $data[$customAttribute->getAttributeCode()] = $customAttribute->getValue();
                    }
                }
            }
            return array_replace($args, $data);
        };
        $productData = $this->valueFactory->create($result);
        return $productData;
    }
}