<?php
namespace Totaltools\ReviewGraphQl\Model\DataProvider;

use Magento\Review\Model\ResourceModel\Review\CollectionFactory as ReviewCollectionFactory;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ProductRepository;

class ProductReviewProvider
{
    protected $reviewCollectionFactory;
    protected $productRepository;

    public function __construct(
        ReviewCollectionFactory $reviewCollectionFactory,
        ProductRepository $productRepository
    ) {
        $this->reviewCollectionFactory = $reviewCollectionFactory;
        $this->productRepository = $productRepository;
    }

    public function getReviewsByUrlKey(string $sku, int $page = 1, int $limit = 10)
    {
        try {
            $product = $this->productRepository->get($sku, false, null, true);
        } catch (\Magento\Framework\Exception\NoSuchEntityException $e) {
            return ['items' => [], 'total_count' => 0];
        }

        $collection = $this->reviewCollectionFactory->create()
            ->addStatusFilter(\Magento\Review\Model\Review::STATUS_APPROVED)
            ->addEntityFilter('product', $product->getId())
            ->addRateVotes()
            ->setPageSize($limit)
            ->setCurPage($page);

        $items = [];
        foreach ($collection as $review) {
            $ratings = [];
            foreach ($review->getRatingVotes() as $vote) {
                $ratings[] = [
                    'vote_id' => $vote->getVoteId(),
                    'rating_name' => $vote->getRatingCode(),
                    'value' => $vote->getPercent(),
                ];
            }

            $items[] = [
                'title' => $review->getTitle(),
                'detail' => $review->getDetail(),
                'nickname' => $review->getNickname(),
                'ratings' => $ratings,
                'created_at' => $review->getCreatedAt(),
            ];
        }
        $pageInfo = [
            'page_size' => $limit,
            'current_page' => $page,
            'total_pages' => (integer) ceil((double)count($items)/$limit),
        ];
        return ['items' => $items, 'total_count' => count($items), 'page_info' => $pageInfo, 'model' => $product];
    }
}
