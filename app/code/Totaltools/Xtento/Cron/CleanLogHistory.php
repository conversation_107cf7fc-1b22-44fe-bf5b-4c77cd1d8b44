<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Xtento
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2022, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Xtento\Cron;

use Totaltools\Xtento\Helper\Data;
use Totaltools\Xtento\Logger\Logger;

class CleanLogHistory
{
    /**
     * @var Data
     */
    protected $helper;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * CleanLogHistory constructor.
     * @param Data $helper
     * @param Logger $logger
     */
    public function __construct(
        Data $helper,
        Logger $logger
    ) {
        $this->helper = $helper;
        $this->logger = $logger;
    }

    /**
     * Clear logs
     *
     * @param $schedule
     */
    public function execute($schedule)
    {
        try {
            $this->logger->notice(__('Starting log cleaning process from cron job'));
            $this->helper->cleanProductExport();
            $this->helper->cleanOrderExport();
            $this->logger->notice(__('Cleaning process finished from cron job.'));
        } catch (\Exception $e) {
            $this->logger->critical(__('Cronjob exception: %1'), $e->getMessage());
        }
    }
}
