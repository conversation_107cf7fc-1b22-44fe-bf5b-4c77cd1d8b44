<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Xtento
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2022, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Xtento\Logger;

class Handler extends \Magento\Framework\Logger\Handler\Base
{
    /**
     * Logging level
     * @var int
     */
    protected $loggerType = Logger::INFO;

    /**
     * File name
     * @var string
     */
    protected $fileName = '/var/log/xtento_clean.log';
}
