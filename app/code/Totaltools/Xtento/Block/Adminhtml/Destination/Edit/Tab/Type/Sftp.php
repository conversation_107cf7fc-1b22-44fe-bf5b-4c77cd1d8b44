<?php
/**
 * @category  Totaltools
 * @package   Totaltools_Xtento
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Xtento\Block\Adminhtml\Destination\Edit\Tab\Type;

class Sftp extends \Xtento\ProductExport\Block\Adminhtml\Destination\Edit\Tab\Type\Sftp
{
    /**
     * @param \Magento\Framework\Data\Form $form
     * @param string $type
     */
    public function getFields(\Magento\Framework\Data\Form $form, $type = 'SFTP')
    {
        if ($type == 'SFTP') {
            parent::getFields($form, $type);

            $model = $this->registry->registry('productexport_destination');

            $fieldset = $form->getElement('config_fieldset');
            $fieldset->addField('new_private_key', 'file', [
                'label' => __('Private key'),
                'name' => 'new_private_key',
                'required' => false,
                'note' => __(!empty($model->getPrivateKey()) ? '<div class="message message-success success">Key installed for this destination.</div>' : 'Allowed extensions: PEM, PPK, DER, PKCS'),
            ]);
        }
    }
}
