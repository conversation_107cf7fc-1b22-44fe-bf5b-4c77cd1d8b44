<?php
/**
 * @category  Totaltools
 * @package   Totaltools_Xtento
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Xtento\Model\Product\Destination;

use Xtento\ProductExport\Model\Destination\Sftp;
use phpseclib3\Crypt\PublicKeyLoader;

class Ssh extends Sftp
{
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $_scopeConfig;

    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    protected $_encryptor;

    /**
     * Ssh constructor.
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\ObjectManagerInterface $objectManager
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $dateTime
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param \Xtento\ProductExport\Model\DestinationFactory $destinationFactory
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Stdlib\DateTime\DateTime $dateTime,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Xtento\ProductExport\Model\DestinationFactory $destinationFactory,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $registry,
            $objectManager,
            $scopeConfig,
            $dateTime,
            $filesystem,
            $encryptor,
            $destinationFactory,
            $resource,
            $resourceCollection,
            $data
        );
        $this->_scopeConfig = $scopeConfig;
        $this->_encryptor = $encryptor;
    }

    public function initConnection()
    {
        $this->setDestination($this->destinationFactory->create()->load($this->getDestination()->getId()));
        $testResult = new \Magento\Framework\DataObject();
        $this->setTestResult($testResult);

        if (class_exists('\phpseclib3\Net\SFTP')) { // Magento 2.4.4
            $this->connection = new \phpseclib3\Net\SFTP(
                $this->getDestination()->getHostname(),
                $this->getDestination()->getPort(),
                $this->getDestination()->getTimeout()
            );
        } elseif (class_exists('\phpseclib\Net\SFTP')) { // Magento 2.1
            $this->connection = new \phpseclib\Net\SFTP(
                $this->getDestination()->getHostname(),
                $this->getDestination()->getPort(),
                $this->getDestination()->getTimeout()
            );
        } elseif (class_exists('\Net_SFTP')) { // Magento 2.0
            $this->connection = new \Net_SFTP(
                $this->getDestination()->getHostname(),
                $this->getDestination()->getPort(),
                $this->getDestination()->getTimeout()
            );
        } else {
            $this->getTestResult()->setSuccess(false)->setMessage(
                __('No SFTP functions found. The Net_SFTP class is missing.')
            );
            return false;
        }

        if (!$this->connection) {
            $this->getTestResult()
                ->setSuccess(false)
                ->setMessage(__('Could not connect to SFTP server. Please make sure that there is no firewall blocking the outgoing connection to the SFTP server and that the timeout is set to a high enough value. If this error keeps occurring, please get in touch with your server hoster / server administrator AND with the server hoster / server administrator of the remote SFTP server. A firewall is probably blocking ingoing/outgoing SFTP connections.'));
            return false;
        }

        // Pub/Private key support - make sure to use adjust the loadKey function with the right key format: http://phpseclib.sourceforge.net/documentation/misc_crypt.html WARNING: Magento version of phpseclib actually only implements CRYPT_RSA_PRIVATE_FORMAT_PKCS1 (and apparently PUTTY format since recently) (and apparently PUTTY format since recently)

        $password =  $this->encryptor->decrypt($this->getDestination()->getPassword());
        $privateKeyPath = $this->getDestination()->getPrivateKey();

        if (!empty($privateKeyPath) && (empty($password) || $password == '******')) {
            $password = false;
        }

        $privateKey = !empty($privateKeyPath) ? PublicKeyLoader::load(\file_get_contents($this->encryptor->decrypt($privateKeyPath)), $password) : $password;

        $warning = '';
        $loginResult = false;

        try {
            $loginResult = $this->connection->login($this->getDestination()->getUsername(), $privateKey);
        } catch (\Exception $e) {
            $warning = '(' . __('Detailed Error') . ': ' . substr($e->getMessage(), 0, strrpos($e->getMessage(), ' in ')) . ')';
        }
        if (!$loginResult) {
            $this->getTestResult()
                ->setSuccess(false)
                ->setMessage(__('Connection to SFTP server failed (make sure no firewall is blocking the connection). This error could also be caused by a wrong login for the SFTP server. %1', $warning));
            return false;
        }

        $warning = '';
        $chdirResult = false;
        try {
            $chdirResult = $this->connection->chdir($this->getDestination()->getPath());
        } catch (\Exception $e) {
            $warning = '(' . __('Detailed Error') . ': ' . substr($e->getMessage(), 0, strrpos($e->getMessage(), ' in ')) . ')';
        }
        if (!$chdirResult) {
            $this->getTestResult()
                ->setSuccess(false)
                ->setMessage(__('Could not change directory on SFTP server to import directory. Please make sure the directory exists and that we have rights to read in the directory. %1', $warning));
            return false;
        }

        $this->getTestResult()
            ->setSuccess(true)
            ->setMessage(__('Connection with SFTP server tested successfully.'));
        return true;
    }
}
