<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * @category  Totaltools
 * @package   Totaltools_Xtento
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Xtento\ProductExport\Model\Destination\Sftp"
                type="Totaltools\Xtento\Model\Product\Destination\Ssh" />
    <preference for="Xtento\ProductExport\Block\Adminhtml\Destination\Edit\Tab\Type\Sftp"
                type="Totaltools\Xtento\Block\Adminhtml\Destination\Edit\Tab\Type\Sftp" />
    <type name="Xtento\ProductExport\Controller\Adminhtml\Destination\Save">
        <plugin name="totaltools_xtento_product_destination_save"
            type="Totaltools\Xtento\Plugin\Block\Adminhtml\Destination\Save" />
    </type>
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="CleanXtento" xsi:type="object">Totaltools\Xtento\Console\Command\CleanXtentoCommand</item>
            </argument>
        </arguments>
    </type>
    <type name="Totaltools\Xtento\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Totaltools\Xtento\Logger\Logger">
        <arguments>
            <argument name="name" xsi:type="string">xtentoCleanLogger</argument>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">Totaltools\Xtento\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
</config>
