<?php

/**
 * @category  Totaltools
 * @package   Totaltools_Xtento
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Xtento\Plugin\Block\Adminhtml\Destination;

use Magento\Framework\Filesystem;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem\Directory\WriteInterface;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\MediaStorage\Model\File\UploaderFactory;

class Save
{
    const PRIVATE_KEY = 'private_key';

    const NEW_PRIVATE_KEY = 'new_private_key';

    /**
     * @var UploaderFactory
     */
    protected $fileUploaderFactory;

    /**
     * @var WriteInterface
     */
    protected $uploadDirectory;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var EncryptorInterface
     */
    protected $encryptor;

    /**
     * @var array
     */
    protected $allowedExtensions = ['pem', 'ppk', 'der', 'pkcs'];

    /**
     * @var string
     */
    protected $basePath;

    /**
     * Save constructor.
     *
     * @param Filesystem $filesystem
     * @param UploaderFactory $fileUploaderFactory
     * @param ManagerInterface $messageManager
     * @param EncryptorInterface $encryptor
     */
    public function __construct(
        Filesystem $filesystem,
        UploaderFactory $fileUploaderFactory,
        ManagerInterface $messageManager,
        EncryptorInterface $encryptor
    ) {
        $this->fileUploaderFactory = $fileUploaderFactory;
        $this->messageManager = $messageManager;
        $this->encryptor = $encryptor;
        $this->uploadDirectory = $filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
    }

    /**
     * @param \Xtento\ProductExport\Controller\Adminhtml\Destination\Save $subject
     */
    public function beforeExecute(\Xtento\ProductExport\Controller\Adminhtml\Destination\Save $subject)
    {
        /** @var \Magento\Framework\App\Request\Http $request */
        $request = $subject->getRequest();
        $this->basePath = '/' . $request->getModuleName();
        $newPrivateKey = $request->getFiles(self::NEW_PRIVATE_KEY);

        if (is_object($request) && !empty($newPrivateKey['size'])) {
            $filePath = $this->_uploadFileAndGetPath();

            if (!empty($filePath)) {
                $request->setPostValue(self::PRIVATE_KEY, $this->encryptor->encrypt($filePath));
            }
        }
    }

    /**
     * Upload the file and return complete file path.
     *
     * @return string
     */
    private function _uploadFileAndGetPath()
    {
        try {
            /** @var \Magento\Framework\Api\Uploader $fileUploader */
            $fileUploader = $this->fileUploaderFactory->create(
                ['fileId' => self::NEW_PRIVATE_KEY]
            )
                ->setAllowedExtensions($this->allowedExtensions)
                ->setAllowRenameFiles(true)
                ->setAllowCreateFolders(true);

            $result = $fileUploader->save($this->getDestination());

            return $result['path'] . '/' . $result['file'];
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__($e->getMessage()));
        }

        return '';
    }

    /**
     * @return string
     */
    protected function getDestination()
    {
        return $this->uploadDirectory->getAbsolutePath($this->basePath);
    }
}
