<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Xtento
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2022, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Xtento\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\App\State;
use Magento\Framework\App\Area;
use Totaltools\Xtento\Helper\Data;

class CleanXtentoCommand extends Command
{

    const NAME = 'totaltools:xtento:clean';
    const DESCRIPTION = 'Clean Old Xtento Files.';

    /**
     * @var State
     */
    private $state;

    /**
     * @var ProductExportLog
     */
    protected $helper;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * CleanXtentoCommand constructor.
     * @param State $state
     * @param Data $helper
     */
    public function __construct(
        State $state,
        Data $helper
    )
    {
        parent::__construct(static::NAME);
        $this->state = $state;
        $this->helper = $helper;
    }

    /**
     * clean command
     */
    protected function configure()
    {
        $this
            ->setName(static::NAME)
            ->setDescription(static::DESCRIPTION);

        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->state->setAreaCode(Area::AREA_ADMINHTML);
        $this->helper->cleanProductExport();
        $this->helper->cleanOrderExport();
    }
}
