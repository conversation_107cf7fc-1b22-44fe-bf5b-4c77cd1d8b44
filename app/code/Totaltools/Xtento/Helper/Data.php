<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Xtento
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2022, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Xtento\Helper;

use Xtento\ProductExport\Model\Log as ProductExportLog;
use Xtento\OrderExport\Model\Log as OrderExportLog;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Totaltools\Xtento\Logger\Logger;
use Xtento\ProductExport\Model\LogFactory as ProductLogFactory;
use Xtento\OrderExport\Model\LogFactory as OrderLogFactory;
use Xtento\ProductExport\Helper\Module as ProductModuleHelper;
use Xtento\OrderExport\Helper\Module as OrderModuleHelper;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var ProductExportLog
     */
    protected $productExportLog;

    /**
     * @var OrderExportLog
     */
    protected $orderExportLog;

    /**
     * @var DateTime
     */
    protected $dateTime;

    /**
     * @var ProductLogFactory
     */
    protected $productLogFactory;

    /**
     * @var ProductModuleHelper
     */
    protected $productModuleHelper;

    /**
     * @var OrderLogFactory
     */
    protected $orderLogFactory;

    /**
     * @var OrderModuleHelper
     */
    protected $orderModuleHelper;

    /**
     * Data constructor.
     * @param \Magento\Framework\App\Helper\Context $context
     * @param ProductExportLog $productExportLog
     * @param OrderExportLog $orderExportLog
     * @param DateTime $dateTime
     * @param ProductLogFactory $productLogFactory
     * @param ProductModuleHelper $productModuleHelper
     * @param OrderLogFactory $orderLogFactory
     * @param OrderModuleHelper $orderModuleHelper
     * @param Logger $logger
     */
    public function __construct (
        \Magento\Framework\App\Helper\Context $context,
        ProductExportLog $productExportLog,
        OrderExportLog $orderExportLog,
        DateTime $dateTime,
        ProductLogFactory $productLogFactory,
        ProductModuleHelper $productModuleHelper,
        OrderLogFactory $orderLogFactory,
        OrderModuleHelper $orderModuleHelper,
        Logger $logger

    ) {
        parent::__construct($context);
        $this->productExportLog = $productExportLog;
        $this->orderExportLog = $orderExportLog;
        $this->dateTime = $dateTime;
        $this->logger = $logger;
        $this->productLogFactory = $productLogFactory;
        $this->productModuleHelper = $productModuleHelper;
        $this->orderModuleHelper = $orderModuleHelper;
        $this->orderLogFactory = $orderLogFactory;
    }

    /**
     * clear product export history
     */
    public function cleanProductExport()
    {
        $this->logger->notice(__('Starting Product History clean process.'));
        $currentDate = $this->dateTime->gmtDate();
        $minusOneMonth = date('Y-m-d', strtotime($currentDate . "-1 months"));
        $collection = $this->productExportLog->getCollection()
            ->addFieldToFilter('created_at', array('to' => $minusOneMonth))->load();

        foreach ($collection as $item) {
            $model = $this->productLogFactory->create();
            $backupDir = $this->productModuleHelper->getExportBkpDir();
            $this->deleteFilesForLogId($item->getLogId(), $model, $backupDir);
            $this->logger->notice(__('This product log entry (ID: %1) deleted.', $item->getLogId()));
            $item->delete();

        }
        $this->logger->notice(__('END Product History clean process.'));
    }

    /**
     * clear order export history
     */
    public function cleanOrderExport()
    {
        $this->logger->notice(__('Starting Order History clean process.'));
        $currentDate = $this->dateTime->gmtDate();
        $minusOneMonth = date('Y-m-d', strtotime($currentDate . "-1 months"));
        $collection = $this->orderExportLog->getCollection()
            ->addFieldToFilter('created_at', array('to' => $minusOneMonth))->load();

        foreach ($collection as $item) {
            $model = $this->orderLogFactory->create();
            $backupDir = $this->orderModuleHelper->getExportBkpDir();
            $this->deleteFilesForLogId($item->getLogId(), $model, $backupDir);
            $this->logger->notice(__('This order log entry (ID: %1) deleted.', $item->getLogId()));
            $item->delete();

        }
        $this->logger->notice(__('END Order History clean process.'));
    }

    /**
     * Delete filets
     * @param $logId
     * @param $model
     * @param $backupDir
     * @param bool $massDownload
     * @return bool
     */
    protected function deleteFilesForLogId($logId, $model, $backupDir, $massDownload = false)
    {
        $model->load($logId);

        if (!$model->getId()) {
            if (!$massDownload) {
                $this->logger->notice(__('This log entry (ID: %1) does not exist anymore.', $logId));
            }
            return false;
        }

        $savedFiles = $model->getFiles();
        if (empty($savedFiles)) {
            if (!$massDownload) {
                $this->logger->notice(
                    __('There is no file to delete. No files have been saved with this export, or you disabled "Save local copies of exports" in the profile. (Log ID: %1)', $logId)
                );
            }
            return false;
        }
        $savedFiles = explode("|", $savedFiles);

        $baseFilenames = [];
        foreach ($savedFiles as $filePath) {
            array_push($baseFilenames, basename($filePath));
        }
        $baseFilenames = array_unique($baseFilenames);

        foreach ($baseFilenames as $filename) {
            $filePath = $backupDir . $logId . '_' . $filename;
            $data = false;


            if (file_exists($filePath)) {
                try {
                    if (!unlink($filePath)) {
                        $this->logger->notice(
                            __('File could not delete may due to permission issue directory: %1 (Log ID: %2)', $filePath, $logId)
                        );
                    }
                    else {
                        $this->logger->notice(
                            __('File deleted: %1 (Log ID: %2)', $filePath, $logId)
                        );
                    }

                } catch (\Exception $e) {}
            } else {
                $this->logger->notice(
                    __('File not found in local backup directory: %1 (Log ID: %2)', $filePath, $logId)
                );
            }
        }
        return true;
    }
}
