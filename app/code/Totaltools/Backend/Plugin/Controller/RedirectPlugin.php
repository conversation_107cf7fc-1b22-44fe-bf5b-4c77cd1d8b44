<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2022 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Backend\Plugin\Controller;

use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\App\Config\ScopeConfigInterface;

/** @class RedirectPlugin */
class RedirectPlugin
{
    /**
     * @var string
     */
    const XML_PATH_NON_ADMIN_SCOPE_URLS = 'web/totaltools_backend/non_admin_scope_urls';

    /**
     * Admin store id.
     * @var int
     */
    private const ADMIN_STORE_ID = 0;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Checks for given group of url paths to match against $path param.
     * If matached, admin scope is set manually for the path which is lost
     * during email template processing.
     *
     * @param Redirect $subject
     * @param string $path
     * @param array $params
     * @return array
     */
    public function beforeSetPath($subject, $path, $params = [])
    {
        $config = (string) $this->scopeConfig->getValue(self::XML_PATH_NON_ADMIN_SCOPE_URLS);
        $paths = preg_split("/\r\n|\n\r|\r|\n/", $config);

        if (in_array($path, $paths) && empty($params['_scope'])) {
            $params['_scope'] = self::ADMIN_STORE_ID;
        }

        return [$path, $params];
    }
}
