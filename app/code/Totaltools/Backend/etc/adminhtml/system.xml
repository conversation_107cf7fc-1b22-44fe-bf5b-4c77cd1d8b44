<?xml version="1.0"?>
<!--
 /**
 * @category    Totaltools
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2022 (c) Totaltools. <https://totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="web">
            <group id="totaltools_backend" translate="label" type="text" sortOrder="250" showInDefault="1" showInWebsite="0">
                <label>URLs &amp; Redirects</label>
                <field id="non_admin_scope_urls" translate="label" type="textarea" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Non Admin Scope URLs</label>
                    <comment>URLs that are redirected to 404 page after email is sent from controller e.g sales/order/view</comment>
                </field>
            </group>
        </section>
    </system>
</config>
