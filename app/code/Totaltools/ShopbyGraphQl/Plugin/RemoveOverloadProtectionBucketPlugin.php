<?php
namespace Totaltools\ShopbyGraphQl\Plugin;

class RemoveOverloadProtectionBucketPlugin
{
    public function afterResolve(
        \Magento\CatalogGraphQl\Model\Resolver\Products $subject,
        array $result
    ) {
        $data = $result;
        // Check if aggregations exist and remove 'overload_protection_bucket'
        // foreach ($result['aggregations'] as $key => $aggregation) {
        //     if (isset($aggregation['attribute_code']) && $aggregation['attribute_code'] === 'overload_protection_bucket') {
        //         unset($result['aggregations'][$key]);
        //     }
        // }

        // // Re-index array to maintain structure
        // $result['aggregations'] = array_values($result['aggregations']);

        return $result;
    }
}
