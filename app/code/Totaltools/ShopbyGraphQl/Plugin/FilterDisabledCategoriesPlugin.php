<?php
namespace Totaltools\ShopbyGraphQl\Plugin;

class FilterDisabledCategoriesPlugin
{
    public function afterResolve(
        \Magento\CatalogGraphQl\Model\Resolver\CategoryList $subject,
        array $result
    ) {
        // foreach ($result['items'] as $key => $category) {
        //     if ($category['is_active'] === false || $category['product_count'] === 0) {
        //         unset($result['items'][$key]);
        //     }
        // }

        // Re-index the items array
        // $result['items'] = array_values($result['items']);

        return $result;
    }
}
