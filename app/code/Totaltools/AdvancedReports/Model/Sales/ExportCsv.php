<?php

namespace Totaltools\AdvancedReports\Model\Sales;


use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Filesystem;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\Locale\ResolverInterface;

/**
 * Class ExportCsv
 * @package Totaltools\AdvancedReports\Model\Sales
 */
class ExportCsv
{
    /**
     * Sales fields collection
     * @var array
     */
    private $salesColumnMetadata = ['order_increment_id', 'order_status', 'order_date', 'product_name', 'sku', 'manufacturer',
        'customer_email', 'customer_name', 'customer_group', 'country', 'region', 'city', 'zip_code', 'address',
        'phone', 'coupon_code', 'qty_ordered', 'qty_invoiced', 'qty_shipped', 'qty_refunded', 'item_price',
        'item_cost', 'subtotal', 'discount', 'tax', 'total', 'total_incl_tax', 'invoiced', 'tax_invoiced',
        'invoiced_incl_tax', 'refunded', 'tax_refunded', 'refunded_incl_tax'];

    /**
     * Header Value
     * @var array
     */
    private $csvHeaders = ['Order #', 'Order Status', 'Order Date', 'Product Name', 'SKU', 'Manufacturer', 'Customer Email',
        'Customer Name', 'Customer Group', 'Country', 'Region', 'City', 'Zip Code', 'Address', 'Phone',
        'Coupon_code', 'Qty Ordered', 'Qty Invoiced', 'Qty Shipped', 'Qty Refunded', 'Item Price', 'Item Cost',
        'Subtotal', 'Discount', 'Tax', 'Total', 'Total Incl Tax', 'Invoiced', 'Tax Invoiced', 'Invoiced Incl Tax',
        'Refunded', 'Tax Refunded', 'Refunded Incl Tax'];

    /**
     * @var \Magento\Framework\App\Response\Http\FileFactory
     */
    private $fileFactory;

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $resourceConnection;

    /**
     * @var Filesystem\Directory\WriteInterface
     */
    private $directory;

    /**
     * @var TimezoneInterface
     */
    private $localeDate;

    /**
     * @var string
     */
    private $locale;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * ExportCsv constructor.
     * @param \Magento\Framework\App\Response\Http\FileFactory $fileFactory
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     * @param Filesystem $filesystem
     * @param TimezoneInterface $localeDate
     * @param ResolverInterface $localeResolver
     * @param \Psr\Log\LoggerInterface $logger
     * @throws FileSystemException
     */
    public function __construct(
        \Magento\Framework\App\Response\Http\FileFactory $fileFactory,
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        Filesystem $filesystem,
        TimezoneInterface $localeDate,
        ResolverInterface $localeResolver,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->fileFactory = $fileFactory;
        $this->resourceConnection = $resourceConnection;
        $this->directory = $filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $this->localeDate = $localeDate;
        $this->locale = $localeResolver->getLocale();
        $this->logger = $logger;
    }

    /**
     * Export csv file
     */
    public function export()
    {
        $name = md5(microtime());
        $file = 'Sales_Reports/Report-'. $name . '-' . date('d-m-Y') . '.csv';
        try {
            $stream = $this->directory->openFile($file, 'w+');
            $stream->lock();
            $stream->writeCsv($this->csvHeaders, '|', '"');
            $endDay = date('Y-m-d');
            $startDay = date('Y-m-d', strtotime('-7 day',strtotime(date('Y-m-d'))));;
            $startDate = $startDay . ' 00:00:01';
            $endDate = $endDay . ' 11:59:59';
            $connection = $this->resourceConnection->getConnection();
            $select = $connection->select()
                ->from($connection->getTableName('aw_arep_sales_detailed'), $this->salesColumnMetadata)
                ->where('order_date < ?', $endDate)
                ->where('order_date >= ?', $startDate);
            $items = $connection->fetchAll($select);
            if (is_array($items) && count($items) > 0) {
                foreach ($items as $item) {
                    $this->convertOrderDate($item);
                    $stream->writeCsv($item, '|', '"');
                }
            }
            $stream->unlock();
            $stream->close();
        } catch (\Exception $e) {
            $this->logger->info($e);
        }

        return;
    }

    /**
     * @param $document
     * @throws \Exception
     */
    private function convertOrderDate($document)
    {
        foreach ($document as $key => &$value) {
            if ($key !== 'order_date') {
                continue;
            }
            $convertedDate = $this->localeDate->date(
                new \DateTime($value, new \DateTimeZone('AEST')),
                $this->locale,
                true
            );
            $dateFormat = 'Y-m-d h:i:s';
            $document[$key] = $convertedDate->format($dateFormat);
        }
    }
}
