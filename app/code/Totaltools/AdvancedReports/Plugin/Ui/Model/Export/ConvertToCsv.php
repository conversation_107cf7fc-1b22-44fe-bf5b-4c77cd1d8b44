<?php

namespace Totaltools\AdvancedReports\Plugin\Ui\Model\Export;

use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Exception\FileSystemException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Filesystem;
use Magento\Ui\Component\MassAction\Filter;
use Magento\Ui\Model\Export\MetadataProvider;

/**
 * Class ConvertToCsv
 * @package Totaltools_AdvancedReports
 */
class ConvertToCsv
{
    /**
     * @var DirectoryList
     */
    private $directory;

    /**
     * @var MetadataProvider
     */
    private $metadataProvider;

    /**
     * @var int|null
     */
    private $pageSize = null;

    /**
     * @var Filter
     */
    private $filter;

    /**
     * ConvertToCsv constructor.
     * @param Filesystem $filesystem
     * @param Filter $filter
     * @param MetadataProvider $metadataProvider
     * @param int $pageSize
     * @throws FileSystemException
     */
    public function __construct(
        Filesystem $filesystem,
        Filter $filter,
        MetadataProvider $metadataProvider,
        $pageSize = 200
    ) {
        $this->filter = $filter;
        $this->directory = $filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $this->metadataProvider = $metadataProvider;
        $this->pageSize = $pageSize;
    }

    /**
     * @param \Magento\Ui\Model\Export\ConvertToCsv $subject
     * @param \Closure $proceed
     * @return array
     * @throws FileSystemException
     * @throws LocalizedException
     */
    public function aroundGetCsvFile(
        \Magento\Ui\Model\Export\ConvertToCsv $subject,
        \Closure $proceed
    ) {
        $component = $this->filter->getComponent();
        if ($component->getName() !== 'aw_arep_salesdetailed_grid') {
            return $proceed();
        }
        $name = md5(microtime());
        $date = date('d-m-Y').'-'.date('H:i:s');
        $file = 'export/'. $component->getName() . $name. '-' .$date . '.csv';

        $this->filter->prepareComponent($component);
        $this->filter->applySelectionOnTargetProvider();
        $dataProvider = $component->getContext()->getDataProvider();
        $fields = $this->metadataProvider->getFields($component);
        $options = $this->metadataProvider->getOptions();

        $this->directory->create('export');
        $stream = $this->directory->openFile($file, 'w+');
        $stream->lock();
        $stream->writeCsv($this->metadataProvider->getHeaders($component), '|', '"');
        $i = 1;
        $searchCriteria = $dataProvider->getSearchCriteria()
            ->setCurrentPage($i)
            ->setPageSize($this->pageSize);
        $totalCount = (int) $dataProvider->getSearchResult()->getTotalCount();
        while ($totalCount > 0) {
            $items = $dataProvider->getSearchResult()->getItems();
            foreach ($items as $item) {
                $this->metadataProvider->convertDate($item, $component->getName());
                $stream->writeCsv($this->metadataProvider->getRowData($item, $fields, $options), '|', '"');
            }
            $searchCriteria->setCurrentPage(++$i);
            $totalCount = $totalCount - $this->pageSize;
        }
        $stream->unlock();
        $stream->close();

        return [
            'type' => 'filename',
            'value' => $file,
            'rm' => false  // can delete file after use
        ];
    }
}
