<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_AdvancedReports
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Ui\Model\Export\ConvertToCsv">

        <plugin name="Totaltools\AdvancedReports\Plugin\Ui\Model\Export\ConvertToCsv"
                type="Totaltools\AdvancedReports\Plugin\Ui\Model\Export\ConvertToCsv"/>
    </type>
    <type name="Aheadworks\AdvancedReports\Model\Config">
        <plugin name="totaltools_advancedreports_model_config" type="Totaltools\AdvancedReports\Plugin\Model\Config"/>
    </type>
</config>