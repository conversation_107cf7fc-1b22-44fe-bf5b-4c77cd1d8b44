<?php

namespace Totaltools\AdvancedReports\Cron;

/**
 * Class ExportSalesDetails
 * @package Totaltools\AdvancedReports\Cron
 */
class ExportSalesDetails
{
    /**
     * @var \Totaltools\AdvancedReports\Model\Sales\ExportCsv
     */
    private $export;

    /**
     * ExportSalesDetails constructor.
     * @param \Totaltools\AdvancedReports\Model\Sales\ExportCsv $exportCsv
     */
    public function __construct(
        \Totaltools\AdvancedReports\Model\Sales\ExportCsv $exportCsv
    ) {
        $this->export = $exportCsv;
    }

    /**
     * Run cron
     * @return $this
     */
    public function execute()
    {
        $day = date('D');
        if ($day === 'Fri') {
            $this->export->export();
        }

        return $this;
    }
}
