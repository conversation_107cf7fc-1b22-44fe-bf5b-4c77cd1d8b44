<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Setup;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Model\Customer;
use Magento\Customer\Setup\CustomerSetup;
use Magento\Eav\Model\Entity\Attribute\SetFactory as AttributeSetFactory;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Store\Model\WebsiteFactory;

/**
 * Class UpgradeData.
 * Sets the data on module upgrade.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @const string OCI website url.
     */
    const OCI_WEBSITE_URL = 'https://dev04-oci-totaltools.balancenet.com.au/';
    /**
     * @const string Name postfix for new categories.
     */
    const CATEGORY_NAME_POSTFIX = 'default category';

    /**
     * @var string $_setupVersion Current setup version.
     */
    private $_setupVersion;
    /**
     * @var \Magento\Framework\App\Config\ConfigResource\ConfigInterface $_config Config resource model.
     */
    protected $_config;
    /**
     * @var \Magento\Store\Api\WebsiteRepositoryInterface $_websiteRepository Website repository.
     */
    protected $_websiteRepository;
    /**
     * @var \Magento\Store\Api\StoreRepositoryInterface $_storeRepository Store repository.
     */
    protected $_storeRepository;
    /**
     * @var \Totaltools\OCI\Helper\Data $_helper Helper instance.
     */
    protected $_helper;
    /**
     * @var \Magento\Customer\Setup\CustomerSetupFactory
     */
    private $_customerSetupFactory;
    /**
     * @var AttributeSetFactory
     */
    private $_attributeSetFactory;
    /**
     * @var WebsiteFactory
     */
    private $_websiteFactory;
    /**
     * @var StoreManagerInterface
     */
    private $_storeManager;
    /**
     * @var \Magento\Store\Model\GroupFactory
     */
    private $_groupFactory;
    /**
     * @var \Magento\Store\Model\StoreFactory
     */
    private $_storeFactory;
    /**
     * @var \Magento\Framework\Event\ManagerInterface
     */
    private $_eventManager;
    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    private $_categoryFactory;
    /**
     * @var \Magento\Catalog\Api\CategoryRepositoryInterface
     */
    private $_categoryRepository;

    /**
     * UpgradeData constructor.
     *
     * @param \Magento\Framework\App\Config\ConfigResource\ConfigInterface $config
     * @param \Magento\Store\Api\WebsiteRepositoryInterface                $websiteRepository
     * @param \Totaltools\OCI\Helper\Data                                  $helper
     * @param \Magento\Customer\Setup\CustomerSetupFactory                 $customerSetupFactory
     * @param WebsiteFactory                                               $websiteFactory
     * @param StoreManagerInterface                   $storeManager
     *
     * @internal param AttributeSetFactory $attributeSetFactory
     */
    public function __construct(
        \Magento\Framework\App\Config\ConfigResource\ConfigInterface $config,
        \Magento\Store\Api\WebsiteRepositoryInterface $websiteRepository,
        \Magento\Store\Api\StoreRepositoryInterface $storeRepository,
        \Totaltools\OCI\Helper\Data $helper,
        \Magento\Customer\Setup\CustomerSetupFactory $customerSetupFactory,
        WebsiteFactory $websiteFactory,
        StoreManagerInterface $storeManager,
        \Magento\Store\Model\GroupFactory $groupFactory,
        \Magento\Store\Model\StoreFactory $storeFactory,
        \Magento\Framework\Event\ManagerInterface $eventManager,
        AttributeSetFactory $attributeSetFactory,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Magento\Catalog\Api\CategoryRepositoryInterface $categoryRepository
    ) {
        $this->_config = $config;
        $this->_helper = $helper;
        $this->_websiteRepository = $websiteRepository;
        $this->_storeRepository = $storeRepository;
        $this->_customerSetupFactory = $customerSetupFactory;
        $this->_websiteFactory = $websiteFactory;
        $this->_storeManager = $storeManager;
        $this->_groupFactory = $groupFactory;
        $this->_storeFactory = $storeFactory;
        $this->_eventManager = $eventManager;
        $this->_attributeSetFactory = $attributeSetFactory;
        $this->_categoryFactory = $categoryFactory;
        $this->_categoryRepository = $categoryRepository;
    }

    /**
     * Upgrades the data.
     *
     * @param ModuleDataSetupInterface $setup   Setup model.
     * @param ModuleContextInterface   $context Context model.
     *
     * @return void
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        // Share current setup version with all class functionality.
        $this->_setupVersion = $context->getVersion();

        $setup->startSetup();

        $this->_setBaseWebsiteCookieLifetime();
        $this->_addOCICustomerAttribute($setup);
        $this->_createOCIWebsite();
        $this->_createOCIStore();
        $this->_setOCIWebsiteUrl();
        $this->_createRootCategories();

        $setup->endSetup();
    }

    /**
     * Sets session lifetime to 0 for Base website.
     *
     * @return void
     */
    private function _setBaseWebsiteCookieLifetime()
    {
        if (version_compare($this->_setupVersion, '1.0.1') < 0) {
            $website = $this->_websiteRepository->get($this->_helper->getOCIWebsiteCode());

            $this->_config->saveConfig(
                'web/cookie/cookie_lifetime',
                0,
                ScopeInterface::SCOPE_WEBSITES,
                $website->getId()
            );
        }
    }

    /**
     * Add oci_customer customer attribute.
     *
     * @param ModuleDataSetupInterface $setup   Setup model.
     *
     * @return void
     */
    private function _addOCICustomerAttribute($setup)
    {
        if (version_compare($this->_setupVersion, '1.2.1') < 0) {
            /** @var CustomerSetup $customerSetup */
            $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);

            $customerEntity = $customerSetup->getEavConfig()->getEntityType('customer');
            $attributeSetId = $customerEntity->getDefaultAttributeSetId();

            /** @var \Magento\Eav\Model\ResourceModel\Entity\Attribute\Set $attributeSet */
            $attributeSet = $this->_attributeSetFactory->create();
            $attributeGroupId = $attributeSet->getDefaultGroupId($attributeSetId);

            $attributeCode = 'oci_customer';
            $customerSetup->removeAttribute(Customer::ENTITY, $attributeCode);
            $customerSetup->addAttribute(
                Customer::ENTITY,
                $attributeCode,
                [
                    'label'        => 'OCI Customer',
                    'type'         => 'int',
                    'source'       => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                    'input'        => 'boolean',
                    'visible'      => true,
                    'required'     => false,
                    'system'       => 0,
                    'default'      => 0,
                    'user_defined' => true,
                    'position'     => 200,
                    'sort_order'   => 200,
                ]
            );

            $magentoUsernameAttribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, $attributeCode);
            $magentoUsernameAttribute->addData([
                'attribute_set_id'   => $attributeSetId,
                'attribute_group_id' => $attributeGroupId,
                'used_in_forms'      => ['adminhtml_customer'],
            ]);

            $magentoUsernameAttribute->save();

            $customerSetup->updateAttribute(Customer::ENTITY, $attributeCode, 'is_used_for_customer_segment', '1');
        }
    }

    /**
     * Create OCI website.
     *
     * @return void
     */
    private function _createOCIWebsite()
    {
        if (version_compare($this->_setupVersion, '1.2.2') < 0) {
            $websiteCode = 'oci';
            $websiteName = 'OCI Website';

            if (!$this->_isWebsiteExists($websiteCode)) {
                /** @var \Magento\Store\Model\Website $website */
                $website = $this->_websiteFactory->create();
                $website->setCode($websiteCode);
                $website->setName($websiteName);

                $website->save();
            }
        }
    }

    /**
     * Create OCI store.
     *
     * @return void
     */
    private function _createOCIStore()
    {
        if (version_compare($this->_setupVersion, '1.2.3') < 0) {
            $stores = [
                [
                    'website_code' => 'oci',
                    'store_groups' => [
                        [
                            'group_code' => 'oci',
                            'group_name' => 'OCI Store',
                            'is_default' => true,
                            'stores'     => [
                                [
                                    'code'       => 'oci',
                                    'name'       => 'OCI Store View',
                                    'is_default' => true
                                ]
                            ]
                        ]
                    ]
                ]
            ];

            foreach ($stores as $storeGroups) {
                /** @var \Magento\Store\Model\Website $website */
                $website = $this->_storeManager->getWebsite($storeGroups['website_code']);
                $this->_processStoreGroups($website, $storeGroups['store_groups']);
            }
        }
    }

    /**
     * Determines whether website already exists.
     *
     * @param string $websiteCode
     *
     * @return bool
     */
    private function _isWebsiteExists($websiteCode)
    {
        try {
            /** @var \Magento\Store\Model\Website $website */
            $website = $this->_storeManager->getWebsite($websiteCode);
        } catch (NoSuchEntityException $exception) {
            return false;
        }

        return $website->hasWebsiteId();
    }

    /**
     * Processes store groups.
     *
     * @param \Magento\Store\Model\Website $website
     * @param array                        $storeGroups
     *
     * @return void
     */
    private function _processStoreGroups($website, $storeGroups)
    {
        foreach ($storeGroups as $storeGroupData) {
            /** @var \Magento\Store\Model\Group $storeGroup */
            $storeGroup = $this->_getStoreGroup($website->getId(), $storeGroupData);

            if (!empty($storeGroupData['is_default'])) {
                $website->setDefaultGroupId($storeGroup->getId());
                $website->save();
            }

            $this->_processStores($website, $storeGroup, $storeGroupData['stores']);
        }
    }

    /**
     * Returns existing store group, otherwise creates a new one.
     *
     * @param int   $websiteId
     * @param array $storeGroupData
     *
     * @return \Magento\Store\Model\Group
     *
     * @throws \Exception
     */
    private function _getStoreGroup($websiteId, $storeGroupData)
    {
        if (empty($storeGroupData['group_code'])
            || empty($storeGroupData['group_name'])
        ) {
            throw new \Exception('Invalid store group data.');
        }

        /** @var \Magento\Store\Model\Group $storeGroup */
        foreach ($this->_storeManager->getGroups() as $storeGroup) {
            if ($storeGroup->getCode() == $storeGroupData['group_code']) {
                return $storeGroup;
            }
        }

        /** @var \Magento\Store\Model\Group $storeGroup */
        $storeGroup = $this->_groupFactory->create();
        $storeGroup->setWebsiteId($websiteId);
        $storeGroup->setCode($storeGroupData['group_code']);
        $storeGroup->setName($storeGroupData['group_name']);
        $storeGroup->save();

        if (is_null($storeGroup->getId())) {
            throw new \Exception('Store group has not been saved properly.');
        }

        $this->_eventManager->dispatch('store_group_save', ['group' => $storeGroup]);

        return $storeGroup;
    }

    /**
     * Processes stores.
     *
     * @param \Magento\Store\Model\Website $website
     * @param \Magento\Store\Model\Group   $storeGroup
     * @param array                        $stores
     *
     * @return void
     *
     * @throws \Exception
     */
    private function _processStores($website, $storeGroup, $stores)
    {
        foreach ($stores as $storeData) {
            if ($this->_isStoreExists($storeData['code'])) {
                continue;
            }

            /** @var \Magento\Store\Model\Store $store */
            $store = $this->_storeFactory->create();
            $store->setCode($storeData['code']);
            $store->setName($storeData['name']);
            $store->setGroup($storeGroup);
            $store->setWebsite($website);
            $store->setIsActive(true);
            $store->save();

            if (is_null($store->getId())) {
                throw new \Exception('Store has not been saved properly.');
            }

            $this->_eventManager->dispatch('store_add', ['store' => $store]);

            if (!empty($storeData['is_default'])) {
                $storeGroup->setDefaultStoreId($store->getId());
                $storeGroup->save();

                $this->_eventManager->dispatch('store_group_save', ['group' => $storeGroup]);
            }
        }
    }

    /**
     * Determines whether store already exists.
     *
     * @param string $storeCode Store code.
     *
     * @return bool
     */
    private function _isStoreExists($storeCode)
    {
        try {
            /** @var \Magento\Store\Model\Store $store */
            $store = $this->_storeManager->getStore($storeCode);
        } catch (NoSuchEntityException $exception) {
            return false;
        }

        return !is_null($store->getId());
    }

    /**
     * Set base urls for OCI website.
     *
     * @return void
     */
    private function _setOCIWebsiteUrl()
    {
        if (version_compare($this->_setupVersion, '1.2.4') < 0) {
            $staticMainUrl = self::OCI_WEBSITE_URL . 'static/';
            $mediaMainUrl = self::OCI_WEBSITE_URL . 'media/';
            $website = $this->_websiteRepository->get($this->_helper->getOCIWebsiteCode());
            $store = $this->_storeRepository->get($this->_helper->getOCIWebsiteCode());

            $urls = [
                'web/secure/base_url'          => self::OCI_WEBSITE_URL,
                'web/unsecure/base_url'        => self::OCI_WEBSITE_URL,
                'web/secure/base_link_url'     => self::OCI_WEBSITE_URL,
                'web/unsecure/base_link_url'   => self::OCI_WEBSITE_URL,
                'web/secure/base_static_url'   => $staticMainUrl,
                'web/unsecure/base_static_url' => $staticMainUrl,
                'web/secure/base_media_url'    => $mediaMainUrl,
                'web/unsecure/base_media_url'  => $mediaMainUrl,
            ];

            foreach ($urls as $path => $value) {
                $this->_config->saveConfig($path, $value, ScopeInterface::SCOPE_WEBSITES, $website->getId());
                $this->_config->saveConfig($path, $value, ScopeInterface::SCOPE_STORES, $store->getId());
            }
        }
    }

    /**
     * Creates root categories.
     *
     * @return void
     */
    private function _createRootCategories()
    {
        if (version_compare($this->_setupVersion, '1.2.5') < 0) {
            $websites = ['oci'];

            foreach ($websites as $websiteCode) {
                /** @var \Magento\Store\Model\Website $website */
                $website = $this->_storeManager->getWebsite($websiteCode);

                /** @var \Magento\Store\Model\Group $storeGroup */
                $storeGroup = $website->getDefaultGroup();
                if ($storeGroup === false) {
                    continue;
                }

                if (empty($storeGroup->getRootCategoryId())) {
                    $this->_createStoreGroupDefaultCategory($storeGroup);
                }
            }
        }
    }

    /**
     * Creates default category for store group.
     *
     * @param \Magento\Store\Model\Group $storeGroup Store group.
     *
     * @return void
     */
    private function _createStoreGroupDefaultCategory($storeGroup)
    {
        /** @var \Magento\Catalog\Model\Category $category */
        $category = $this->_categoryFactory->create();
        $category->setParentId($this->_getRootCategory()->getId());
        $category->setName($this->_getDefaultCategoryName($storeGroup));
        $category->setIsActive(1);
        $category->setLevel(1);
        $category->setData('is_anchor', 0);
        $category->setData('display_mode', \Magento\Catalog\Model\Category::DM_PAGE);
        $this->_categoryRepository->save($category);

        // Assign created category to a store group.
        $storeGroup->setRootCategoryId($category->getId());
        $storeGroup->save();
    }

    /**
     * Returns root category.
     *
     * @return \Magento\Catalog\Api\Data\CategoryInterface
     */
    public function _getRootCategory()
    {
        return $this->_categoryRepository->get(\Magento\Catalog\Model\Category::TREE_ROOT_ID, 0);
    }

    /**
     * Returns default category name.
     *
     * @param \Magento\Store\Model\Group $storeGroup Store group.
     *
     * @return string
     */
    private function _getDefaultCategoryName($storeGroup)
    {
        return sprintf(
            '%s %s',
            $storeGroup->getWebsite()->getName(),
            self::CATEGORY_NAME_POSTFIX
        );
    }
}