<?php

namespace Totaltools\OCI\Setup;

use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\UpgradeSchemaInterface;

/**
 * Upgrade Schema script
 */
class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * Upgrade schema action
     * @param SchemaSetupInterface $setup
     * @param ModuleContextInterface $context
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        if (version_compare($context->getVersion(), '1.2.0') < 0) {
            /**
             * Alter quote table with oci_customer_token
             */
            $setup->getConnection()->addColumn(
                $setup->getTable('quote'),
                'oci_customer_token',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length'   => 50,
                    'comment' => 'OCI Customer Token'
                ]
            );
        }
    }
}