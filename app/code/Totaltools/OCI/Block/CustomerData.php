<?php

namespace Totaltools\OCI\Block;

class CustomerData extends \Magento\Framework\View\Element\Template
{
    /**
     * Get reload customer data flag.
     * Invalidate the flag if is it active.
     *
     * @return string
     */
    public function getCheckCustomerDataUrl()
    {
        return $this->_urlBuilder->getUrl(
            'oci/index/checkCustomerData',
            ['_secure' => $this->getRequest()->isSecure()]
        );
    }

    /**
     * Get Reload Customer cookie name.
     *
     * @return string
     */
    public function getReloadCustomerCookieName()
    {
        return \Totaltools\OCI\Model\Session::RELOAD_CUSTOMER_DATA_PARAMETER_NAME;
    }
}