<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Block\Checkout\Cart;

/**
 * Class Form.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Form extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Totaltools\OCI\Model\Session $_ociSession
     */
    protected $_ociSession;
    /**
     * @var \Totaltools\OCI\Model\Quote\Translator
     */
    protected $_translator;
    /**
     * @var \Totaltools\OCI\Helper\Data $_helper Helper instance.
     */
    protected $_helper;

    /**
     * Form constructor.
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Totaltools\OCI\Model\Session               $ociSession
     * @param \Totaltools\OCI\Model\Quote\Translator      $translator
     * @param array                                            $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Totaltools\OCI\Model\Session $ociSession,
        \Totaltools\OCI\Model\Quote\Translator $translator,
        \Totaltools\OCI\Helper\Data $helper,
        array $data = []
    )
    {
        $this->_ociSession = $ociSession;
        $this->_translator = $translator;
        $this->_helper = $helper;

        parent::__construct($context, $data);
    }

    /**
     * Determines whether the form is enabled for the store.
     *
     * @param string $storeCode
     *
     * @return bool
     */
    public function isEnabled($storeCode = null)
    {
        if (!$storeCode) {
            $storeCode = $this->_helper->getOCIWebsiteCode();
        }

        return $this->_storeManager->getWebsite()->getCode() == $storeCode;
    }

    /**
     * Returns form action (the data is sent to hook URL).
     *
     * @return string
     */
    public function getFormAction()
    {
        return $this->_ociSession->getHookUrl();
    }

    /*
     * Returns form data to be rendered.
     *
     * @return array
     */
    public function getFormData()
    {
        return $this->_translator->getQuoteData();
    }

    /**
     * Returns check data URL.
     *
     * @return string
     */
    public function getCheckDataUrl()
    {
        return $this->_urlBuilder->getUrl(
            'oci/index/checkData',
            ['_secure' => $this->getRequest()->isSecure()]
        );
    }
}