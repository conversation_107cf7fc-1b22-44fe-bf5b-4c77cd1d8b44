<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Helper;

/**
 * Class Data.
 * Base helper class.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @const string XML path to enabled flag value.
     */
    const XML_PATH_OCI_ENABLED = 'oci/general/enabled';
    /**
     * @const string XML path to username parameter name.
     */
    const XML_PATH_OCI_USERNAME_PARAM_NAME = 'oci/general/username_param_name';
    /**
     * @const string XML path to username parameter value.
     */
    const XML_PATH_OCI_USERNAME_PARAM_VALUE = 'oci/general/username_param_value';
    /**
     * @const string XML path to password parameter name.
     */
    const XML_PATH_OCI_PASSWORD_PARAM_NAME = 'oci/general/password_param_name';
    /**
     * @const string XML path to password parameter value.
     */
    const XML_PATH_OCI_PASSWORD_PARAM_VALUE = 'oci/general/password_param_value';
    /**
     * @const string XML path to hook url parameter name.
     */
    const XML_PATH_OCI_HOOK_URL_PARAM_NAME = 'oci/general/hook_url_param_name';
    /**
     * @const string XML path to failure url value.
     */
    const XML_PATH_OCI_FAILURE_URL = 'oci/general/failure_url';
    /**
     * @const string XML path to success url value.
     */
    const XML_PATH_OCI_SUCCESS_URL = 'oci/general/success_url';
    /**
     * @const string XML path to debug logging setting.
     */
    const XML_PATH_OCI_DEBUG_LOGGING = 'oci/general/debug_logging';
    /**
     * @const string XML path to OCI website code
     */
    const XML_PATH_OCI_WEBSITE_CODE = 'oci/general/website_code';

    /**
     * Determines whether OCI website restriction functionality is enabled.
     *
     * @return bool
     */
    public function isRestrictionEnabled()
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_OCI_ENABLED);
    }

    /**
     * Returns username parameter name from system configuration.
     *
     * @return string
     */
    public function getUsernameParamConfigName()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_OCI_USERNAME_PARAM_NAME);
    }

    /**
     * Returns username parameter value from system configuration.
     *
     * @return string
     */
    public function getUsernameParamConfigValue()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_OCI_USERNAME_PARAM_VALUE);
    }

    /**
     * Returns password parameter name from system configuration.
     *
     * @return string
     */
    public function getPasswordParamConfigName()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_OCI_PASSWORD_PARAM_NAME);
    }

    /**
     * Returns password parameter value from system configuration.
     *
     * @return string
     */
    public function getPasswordParamConfigValue()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_OCI_PASSWORD_PARAM_VALUE);
    }

    /**
     * Returns hook url parameter name from system configuration.
     *
     * @return string
     */
    public function getHookUrlParamConfigName()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_OCI_HOOK_URL_PARAM_NAME);
    }

    /**
     * Returns failure url value from system configuration.
     *
     * @return string
     */
    public function getFailureUrlConfigValue()
    {
        $failureUrl = $this->scopeConfig->getValue(self::XML_PATH_OCI_FAILURE_URL);

        return empty($failureUrl)
            ? $this->_urlBuilder->getUrl('cms/noroute/index')
            : $failureUrl;
    }

    /**
     * Returns success url value from system configuration.
     *
     * @return string
     */
    public function getSuccessUrlConfigValue()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_OCI_SUCCESS_URL);
    }

    /**
     * Determines whether debug logging is enabled.
     *
     * @return bool
     */
    public function isDebugLoggingEnabled()
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_OCI_DEBUG_LOGGING);
    }

    /**
     * Get website code allowed for OCI.
     *
     * @return string
     */
    public function getOCIWebsiteCode()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_OCI_WEBSITE_CODE);
    }
}
