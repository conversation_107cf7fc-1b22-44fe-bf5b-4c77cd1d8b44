<?php

namespace Totaltools\OCI\Observer;


class CustomerLogout implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @var \Totaltools\OCI\Model\Session
     */
    protected $_ociSession;
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * CustomerLogout constructor.
     *
     * @param \Totaltools\OCI\Model\Session $ociSession
     */
    public function __construct(
        \Totaltools\OCI\Model\Session $ociSession,
        \Magento\Checkout\Model\Session $checkoutSession
    )
    {
        $this->_ociSession = $ociSession;
        $this->_checkoutSession = $checkoutSession;
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $this->_ociSession->unsetToken();
        $this->_checkoutSession->clearQuote();
    }
}