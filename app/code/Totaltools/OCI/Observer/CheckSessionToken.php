<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Observer;

/**
 * Class CheckSessionToken.
 * Observer model that checks generated token in session for "OCI" website.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class CheckSessionToken implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @var \Totaltools\OCI\Model\Session $_ociSession OCI website session.
     */
    protected $_ociSession;
    /**
     * @var \Magento\Store\Model\StoreManagerInterface $_storeManager Store manager.
     */
    protected $_storeManager;
    /**
     * @var \Magento\Framework\App\Response\RedirectInterface $_redirect Redirect model.
     */
    protected $_redirect;
    /**
     * @var \Totaltools\OCI\Helper\Data $_helper Helper instance.
     */
    protected $_helper;

    /**
     * @var array
     */
    protected $_restrictedActions = [
        'oci_index_login',
        'cms_noroute_index'
    ];

    /**
     * CheckSessionToken constructor.
     *
     * @param \Totaltools\OCI\Model\Session                $ociSession
     * @param \Magento\Store\Model\StoreManagerInterface        $storeManager
     * @param \Magento\Framework\App\Response\RedirectInterface $redirect
     * @param \Totaltools\OCI\Helper\Data                  $helper
     */
    function __construct(
        \Totaltools\OCI\Model\Session $ociSession,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\App\Response\RedirectInterface $redirect,
        \Totaltools\OCI\Helper\Data $helper
    ) {
        $this->_ociSession = $ociSession;
        $this->_storeManager = $storeManager;
        $this->_redirect = $redirect;
        $this->_helper = $helper;
    }

    /**
     * Checks generated token in session.
     *
     * @param \Magento\Framework\Event\Observer $observer Observer instance.
     *
     * @return void
     *
     * @event controller_action_predispatch
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        /** @var \Magento\Framework\App\Action\Action $controller */
        $controllerAction = $observer->getData('controller_action');
        if (is_null($controllerAction)) {
            return;
        }

        /** @var \Magento\Framework\App\Request\Http $request */
        $request = $observer->getData('request');
        if (is_null($request)) {
            return;
        }

        /** @var \Magento\Store\Api\Data\WebsiteInterface $currentWebsite */
        $currentWebsite = $this->_storeManager->getWebsite();

        if ($currentWebsite->getCode() != $this->_helper->getOCIWebsiteCode()
            || $this->_ociSession->isTokenSet()
            || in_array($request->getFullActionName(), $this->_restrictedActions)
        ) {
            return;
        }

        $redirectUrl = $this->_ociSession->isRedirectUrlSet()
            ? $this->_ociSession->getRedirectUrl()
            : $this->_helper->getFailureUrlConfigValue();

        // Redirect should be done only once, after order is successfully placed.
        $this->_ociSession->unsetRedirectUrl();

        $this->_redirect->redirect($controllerAction->getResponse(), $redirectUrl);

        /** @var \Magento\Framework\App\ActionFlag $actionFlag */
        $actionFlag = $controllerAction->getActionFlag();
        $actionFlag->set('', \Magento\Framework\App\ActionInterface::FLAG_NO_DISPATCH, true);
    }
}