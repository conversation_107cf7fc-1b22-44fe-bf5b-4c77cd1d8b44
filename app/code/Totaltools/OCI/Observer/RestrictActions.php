<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Observer;

/**
 * Class RestrictActions.
 * Observer model that checks that current action is allowed for OCI website.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class RestrictActions implements \Magento\Framework\Event\ObserverInterface
{
    /**
     * @var \Magento\Store\Model\StoreManagerInterface $_storeManager Store manager.
     */
    protected $_storeManager;
    /**
     * @var \Magento\Framework\App\Response\RedirectInterface $_redirect Redirect model.
     */
    protected $_redirect;
    /**
     * @var array $_restrictedActions Restricted action list.
     */
    protected $_restrictedActions;
    /**
     * @var \Totaltools\OCI\Helper\Data $_helper Helper instance.
     */
    protected $_helper;

    /**
     * RestrictActions constructor.
     *
     * @param \Magento\Store\Model\StoreManagerInterface        $storeManager      Store manager.
     * @param \Magento\Framework\App\Response\RedirectInterface $redirect          Redirect model.
     * @param \Totaltools\OCI\Helper\Data                       $helper            Helper.
     * @param array                                             $restrictedActions Restricted action list.
     */
    function __construct(
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\App\Response\RedirectInterface $redirect,
        \Totaltools\OCI\Helper\Data $helper,
        array $restrictedActions = []
    )
    {
        $this->_storeManager = $storeManager;
        $this->_redirect = $redirect;
        $this->_restrictedActions = $restrictedActions;
        $this->_helper = $helper;
    }

    /**
     * Checks that current action is allowed for OCI website.
     *
     * @param \Magento\Framework\Event\Observer $observer Observer instance.
     *
     * @return void
     *
     * @event controller_action_predispatch
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        if (!$this->_isOciWebsite()) {
            return;
        }

        /** @var \Magento\Framework\App\Action\Action $controller */
        $controllerAction = $observer->getData('controller_action');
        if (is_null($controllerAction)) {
            return;
        }

        /** @var \Magento\Framework\App\Request\Http $request */
        $request = $observer->getData('request');
        if (is_null($request)) {
            return;
        }

        if (!in_array($request->getFullActionName(), $this->_restrictedActions)) {
            return;
        }

        $this->_redirect->redirect($controllerAction->getResponse(), $this->_helper->getFailureUrlConfigValue());

        /** @var \Magento\Framework\App\ActionFlag $actionFlag */
        $actionFlag = $controllerAction->getActionFlag();
        $actionFlag->set('', \Magento\Framework\App\ActionInterface::FLAG_NO_DISPATCH, true);
    }

    /**
     * Determines whether current website is OCI website.
     *
     * @return bool
     */
    protected function _isOciWebsite()
    {
        /** @var \Magento\Store\Api\Data\WebsiteInterface $currentWebsite */
        $currentWebsite = $this->_storeManager->getWebsite();

        return $currentWebsite->getCode() == $this->_helper->getOCIWebsiteCode();
    }
}