<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Controller\Index;

/**
 * Class CheckData.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class CheckData extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Psr\Log\LoggerInterface $_logger Logger.
     */
    protected $_logger;
    /**
     * @var \Totaltools\OCI\Model\Session
     */
    protected $_ociSession;
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * CheckData constructor.
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Psr\Log\LoggerInterface              $logger
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Psr\Log\LoggerInterface $logger,
        \Totaltools\OCI\Model\Session $ociSession,
        \Magento\Checkout\Model\Session $checkoutSession
    )
    {
        parent::__construct($context);

        $this->_logger = $logger;
        $this->_ociSession = $ociSession;
        $this->_checkoutSession = $checkoutSession;
    }

    /**
     * Execute method.
     *
     * @return void
     */
    public function execute()
    {
        try {
            // Additional logging to a separate file.
            $this->_logger->debug(
                sprintf(
                    '%s %s %s %s',
                    PHP_EOL . 'CHECK DATA REQUEST:',
                    PHP_EOL . $this->getRequest()->renderRequestLine(),
                    PHP_EOL . 'REQUEST PARAMS:',
                    PHP_EOL . print_r($this->getRequest()->getParams(), true)
                )
            );
        } catch (\Exception $exception) {
            // Additional logging to a separate file.
            $this->_logger->debug(
                sprintf(
                    '%s %s',
                    PHP_EOL . 'AN ERROR HAS OCCURRED',
                    PHP_EOL . $exception->__toString()
                )
            );

            $this->_logger->error($exception->getMessage());
        }

        $this->_clearQuote();
        $this->_clearToken();
    }

    /**
     * Clears quote data.
     *
     * @return void
     */
    protected function _clearQuote()
    {
        $this->_checkoutSession->clearQuote();
    }

    /**
     * Clears quote data.
     *
     * @return void
     */
    protected function _clearToken()
    {
        $this->_ociSession->unsetToken();
    }
}