<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Controller\Index;

use Magento\Framework\Exception\AuthorizationException;
use Totaltools\OCI\Exception\NotOCICustomerException;
use Magento\Store\Model\StoreManagerInterface;

/**
 * Class Login.
 * Index controller login action class.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Login extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Totaltools\OCI\Helper\Data $_helper Helper instance.
     */
    protected $_helper;
    /**
     * @var \Totaltools\OCI\Model\Session $_ociSession Default website session.
     */
    protected $_ociSession;
    /**
     * @var \Magento\Customer\Model\Session $_customerSession
     */
    protected $_customerSession;
    /**
     * @var \Psr\Log\LoggerInterface $_logger Logger.
     */
    protected $_logger;
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;
    /**
     * @var \Magento\Customer\Api\AccountManagementInterface
     */
    protected $_customerAccountManagement;
    /**
     * @var \Magento\Framework\Controller\Result\RawFactory
     */
    protected $_resultRawFactory;
    /**
     * @var StoreManagerInterface
     */
    protected $_storeManager;
    /**
     * @var \Magento\Store\Api\StoreRepositoryInterface $_storeRepository Store repository.
     */
    protected $_storeRepository;

    /**
     * Login constructor.
     *
     * @param \Magento\Framework\App\Action\Context            $context
     * @param \Totaltools\OCI\Helper\Data                      $helper
     * @param \Totaltools\OCI\Model\Session                    $ociSession
     * @param \Magento\Customer\Model\Session                  $session
     * @param \Psr\Log\LoggerInterface                         $logger
     * @param \Magento\Checkout\Model\Session                  $checkoutSession
     * @param \Magento\Framework\Controller\Result\RawFactory  $resultRawFactory
     * @param \Magento\Customer\Api\AccountManagementInterface $customerAccountManagement
     */
    function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Totaltools\OCI\Helper\Data $helper,
        \Totaltools\OCI\Model\Session $ociSession,
        \Magento\Customer\Model\Session $session,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Framework\Controller\Result\RawFactory $resultRawFactory,
        \Magento\Customer\Api\AccountManagementInterface $customerAccountManagement,
        StoreManagerInterface $storeManager,
        \Magento\Store\Api\StoreRepositoryInterface $storeRepository
    )
    {
        parent::__construct($context);

        $this->_helper = $helper;
        $this->_ociSession = $ociSession;
        $this->_customerSession = $session;
        $this->_logger = $logger;
        $this->_checkoutSession = $checkoutSession;
        $this->_resultRawFactory = $resultRawFactory;
        $this->_customerAccountManagement = $customerAccountManagement;
        $this->_storeManager = $storeManager;
        $this->_storeRepository = $storeRepository;
    }

    /**
     * Execute method.
     *
     * @return \Magento\Framework\Controller\ResultInterface|\Magento\Framework\App\ResponseInterface
     */
    public function execute()
    {
        try {
            // Additional logging to a separate file.
            $this->_logger->debug(
                sprintf(
                    '%s %s %s %s',
                    PHP_EOL . 'LOGIN REQUEST:',
                    PHP_EOL . $this->getRequest()->renderRequestLine(),
                    PHP_EOL . 'REQUEST PARAMS:',
                    PHP_EOL . print_r($this->getRequest()->getParams(), true)
                )
            );

            $this->_authorizeCustomer();
            $this->_storeHookUrl();
            $this->_clearQuote();
        } catch (AuthorizationException $exception) {
            // Additional logging to a separate file.
            $this->_logger->debug(
                sprintf(
                    '%s %s',
                    PHP_EOL . 'AN ERROR HAS OCCURRED',
                    PHP_EOL . $exception->__toString()
                )
            );

            $this->_logger->info($exception->getMessage());

            echo $exception->getMessage();
            /** @var \Magento\Framework\Controller\Result\Raw $resultRaw */
            $resultRaw = $this->_resultRawFactory->create();
            $resultRaw->setHttpResponseCode(401);

            return $resultRaw;
        } catch (NotOCICustomerException $exception) {
            // Additional logging to a separate file.
            $this->_logger->debug(
                sprintf(
                    '%s %s',
                    PHP_EOL . 'AN ERROR HAS OCCURRED',
                    PHP_EOL . $exception->__toString()
                )
            );

            $this->_logger->info($exception->getMessage());

            echo $exception->getMessage();
            /** @var \Magento\Framework\Controller\Result\Raw $resultRaw */
            $resultRaw = $this->_resultRawFactory->create();
            $resultRaw->setHttpResponseCode(401);

            return $resultRaw;
        } catch (\Exception $exception) {
            $this->_logger->debug(
                sprintf(
                    '%s %s',
                    PHP_EOL . 'AN ERROR HAS OCCURRED',
                    PHP_EOL . $exception->__toString()
                )
            );

            $this->_logger->error($exception->getMessage());

            echo $exception->getMessage();
            /** @var \Magento\Framework\Controller\Result\Raw $resultRaw */
            $resultRaw = $this->_resultRawFactory->create();
            $resultRaw->setHttpResponseCode(400);

            return $resultRaw;
        }

        $this->_logger->debug(
            sprintf('%s', PHP_EOL . 'SUCCESS' . PHP_EOL)
        );

        $ociStore = $this->_storeRepository->get($this->_helper->getOCIWebsiteCode());
        $ociUrl = $ociStore->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_LINK);

        return $this->_redirect($ociUrl);
    }

    /**
     * Validates request.
     *
     * @return void
     *
     * @throws AuthorizationException
     */
    protected function _validateRequest()
    {
        /** @var \Magento\Framework\App\Request\Http $request */
        $request = $this->getRequest();

        $usernameParam = $request->getParam($this->_helper->getUsernameParamConfigName());
        $passwordParam = $request->getParam($this->_helper->getPasswordParamConfigName());

        if (is_null($usernameParam) || is_null($passwordParam)) {
            throw new AuthorizationException(__('Username and/or password parameter is missed.'));
        }
    }

    /**
     * Authorize customer by parameters.
     *
     * @throws AuthorizationException
     */
    protected function _authorizeCustomer()
    {
        $this->_validateRequest();

        /** @var \Magento\Framework\App\Request\Http $request */
        $request = $this->getRequest();

        $usernameParam = $request->getParam($this->_helper->getUsernameParamConfigName());
        $passwordParam = $request->getParam($this->_helper->getPasswordParamConfigName());

        try {
            $customer = $this->_customerAccountManagement->authenticate($usernameParam, $passwordParam);
        } catch (\Exception $e) {
            throw new AuthorizationException(__('Username and/or password parameter is wrong.'));
        }

        /** @var \Magento\Customer\Model\Data\Customer $customer */
        $ociCustomer = $customer->getCustomAttribute('oci_customer');
        if (is_null($ociCustomer) || empty($ociCustomer->getValue())) {
            throw new NotOCICustomerException(__('This customer does not have permissions to access.'));
        }


        $this->_setLoginToken();
        $this->_customerSession->setCustomerDataAsLoggedIn($customer);
        $this->_customerSession->regenerateId();

        $hookUrlParam = $request->getParam($this->_helper->getHookUrlParamConfigName());
        if (is_null($hookUrlParam)) {
            throw new AuthorizationException(__('Hook url parameter is missed.'));
        }
    }

    /**
     * Sets login token into the session.
     *
     * @return void
     */
    protected function _setLoginToken()
    {
        $this->_ociSession->generateToken();
    }

    /**
     * Stores hook url into the session.
     *
     * @return void
     */
    protected function _storeHookUrl()
    {
        $hookUrl = $this->getRequest()->getParam($this->_helper->getHookUrlParamConfigName());
        $this->_ociSession->setHookUrl($hookUrl);
    }

    /**
     * Clears quote data.
     *
     * @return void
     */
    protected function _clearQuote()
    {
        $this->_checkoutSession->clearQuote();
    }

    /**
     *  Performs redirect to failure URL.
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    protected function _redirectToFailureUrl()
    {
        $result = $this->resultRedirectFactory->create();
        $result->setUrl(
            $this->_helper->getFailureUrlConfigValue()
        );

        return $result;
    }
}