<?php

namespace Totaltools\OCI\Plugin\Magento\Framework\View\Page\Config;


class Renderer
{
    const OCI_CSS_CLASS = 'oci-customer';

    /**
     * @var \Totaltools\OCI\Model\Session
     */
    protected $_ociSession;

    /**
     * Renderer constructor.
     * @param \Totaltools\OCI\Model\Session $ociSession
     */
    public function __construct(\Totaltools\OCI\Model\Session $ociSession)
    {
        $this->_ociSession = $ociSession;
    }

    /**
     * Add a custom css class to body tag for OCI customers.
     *
     * @param \Magento\Framework\View\Page\Config\Renderer $subject
     * @param \Closure $proceed
     * @param string $elementType
     *
     * @return string
     */
    public function aroundRenderElementAttributes(
        \Magento\Framework\View\Page\Config\Renderer $subject,
        \Closure $proceed,
        string $elementType
    )
    {
        $result = $proceed($elementType);

        if ($elementType === \Magento\Framework\View\Page\Config::ELEMENT_TYPE_BODY) {
            // Prepend CSS class:
            if ($this->_ociSession->isTokenSet()) {
                $result = str_replace(
                    'class="',
                    'class="' . self::OCI_CSS_CLASS . ' ',
                    $result
                );
            } else {
                $result = str_replace(
                    self::OCI_CSS_CLASS,
                    '',
                    $result
                );
            }
        }

        return $result;
    }
}