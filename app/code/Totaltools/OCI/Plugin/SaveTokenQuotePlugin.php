<?php


namespace Totaltools\OCI\Plugin;

class SaveTokenQuotePlugin
{
    /**
     * @var \Magento\Quote\Model\Quote
     */
    protected $_quote;
    /**
     * @var \Totaltools\OCI\Model\Session
     */
    protected $_ociSession;
    /**
     * @var \Magento\Store\Model\StoreManagerInterface;
     */
    protected $storeManager;
    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * SetSessionTokenToQuote constructor.
     *
     * @param \Totaltools\OCI\Model\Session              $ociSession
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     */
    public function __construct(
        \Totaltools\OCI\Model\Session $ociSession,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    )
    {
        $this->_ociSession = $ociSession;
        $this->_storeManager = $storeManager;
    }

    /**
     * @param \Magento\Quote\Api\CartRepositoryInterface $subject
     * @param \Magento\Quote\Api\Data\CartInterface $quote
     * @return void
     *
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function beforeSave(
        \Magento\Quote\Api\CartRepositoryInterface $subject,
        \Magento\Quote\Api\Data\CartInterface $quote
    ) {
        if ($quote->hasData('oci_customer_token') === false) {
            $quote->setData('oci_customer_token', $this->_ociSession->getToken());
        }
    }
}