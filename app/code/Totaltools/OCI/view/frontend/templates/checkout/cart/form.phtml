<?php
/** @var Totaltools\OCI\Block\Checkout\Cart\Form $block */
?>
<?php if (!$block->isEnabled()) { return; } ?>

<form action="<?php echo $block->getFormAction() ?>" method="POST"
      data-mage-init='{"checkOrderData": {"checkDataUrl": "<?= $block->getCheckDataUrl() ?>"}}'>
    <?php foreach ($block->getFormData() as $formElementName => $formElementData): ?>
        <?php if (is_array($formElementData)): ?>
            <?php foreach ($formElementData as $index => $formElementValue): ?>
                <input type="hidden"
                       name="<?php echo $formElementName ?>[<?= $index ?>]"
                       value="<?php echo $formElementValue ?>"/>
            <?php endforeach ?>
        <?php else: ?>
            <input type="hidden"
                   name="<?php echo $formElementName ?>"
                   value="<?php echo $formElementData ?>" />
        <?php endif; ?>
    <?php endforeach ?>

    <?php echo $block->getChildHtml('checkout.cart.methods.oci.form.button') ?>
</form>
