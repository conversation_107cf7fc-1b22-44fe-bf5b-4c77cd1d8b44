define([
    'jquery',
    'Magento_Customer/js/customer-data',
    'jquery-ui-modules/widget'
], function($, customerData) {

    /**
     * Check OCI order data widget.
     */
    $.widget('Totaltools.checkOrderData', {
        options: {
            checkDataUrl: ''
        },

        /**
         * Create action.
         *
         * @returns void
         */
        _create: function () {
            this._initSubmitHandler();
        },

        /**
         * Init form submit handler.
         *
         * @returns void
         */
        _initSubmitHandler: function() {
            var self = this;
            var form = this.element;

            if (self.options.checkDataUrl == '') {
                return;
            }

            form.on('submit', function (event) {
                event.preventDefault();

                $.ajax({
                    url: self.options.checkDataUrl,
                    data: form.serialize(),
                    type: 'POST',
                    dataType: 'json',
                    showLoader: true
                }).always(function () {
                    customerData.invalidate(['cart']);
                    form.off('submit').submit();
                });
            });
        }
    });

    return $.Totaltools.checkOrderData;
});