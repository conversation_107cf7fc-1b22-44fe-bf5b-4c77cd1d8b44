define([
    'jquery',
    'Magento_Customer/js/customer-data',
    'jquery/jquery.cookie',
    'jquery-ui-modules/widget'
], function($, customerData) {

    /**
     * Check OCI order data widget.
     */
    $.widget('Totaltools.checkCustomerData', {
        options: {
            checkCustomerDataUrl: '',
            reloadCustomerCookieName: ''
        },

        /**
         * Create action.
         *
         * @returns void
         */
        _create: function () {
            this._initRequestHandler();
        },

        /**
         * Init form submit handler.
         *
         * @returns void
         */
        _initRequestHandler: function() {
            var self = this;
            var form = this.element;

            if (self.options.checkCustomerDataUrl == '') {
                return;
            }

            if (self._getTokenFromCookie() !== null) {
                customerData.reload(['cart'], true);
                self._invalidateToken();
            }
        },

        _getTokenFromCookie: function () {
            return $.cookie(this.options.reloadCustomerCookieName);
        },

        _invalidateToken: function () {
            var date = new Date();
            var minutes = 60;
            date.setTime(date.getTime() + (minutes * 60 * 1000));
            $.cookie(this.options.reloadCustomerCookieName, '', {path: '/', expires: -1}); // Expire Cookie
        }
    });

    return $.Totaltools.checkCustomerData;
});