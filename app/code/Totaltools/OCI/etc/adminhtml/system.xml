<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="oci" translate="label" type="text" sortOrder="200"
                 showInDefault="1"
                 showInWebsite="1"
                 showInStore="1">
            <label>OCI</label>
            <tab>totaltools</tab>
            <resource>Totaltools_OCI::config_oci</resource>
            <group id="general" translate="label" type="text" sortOrder="1"
                   showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>General configuration</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Enabled</label>
                    <comment><![CDATA[]]></comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="website_code" translate="label comment" type="select" sortOrder="20"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>OCI Website</label>
                    <comment><![CDATA[]]></comment>
                    <source_model>Totaltools\OCI\Model\Adminhtml\System\Config\Source\Website</source_model>
                    <can_be_empty>0</can_be_empty>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="username_param_name" translate="label comment" type="text" sortOrder="30"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Username parameter name</label>
                    <comment><![CDATA[The name of username parameter we expect to receive.]]></comment>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="username_param_value" translate="label comment" type="text" sortOrder="30"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Username parameter value</label>
                    <comment><![CDATA[The value of username parameter we expect to receive.]]></comment>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="password_param_name" translate="label comment" type="text" sortOrder="30"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Password parameter name</label>
                    <comment><![CDATA[The name of password parameter we expect to receive.]]></comment>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="password_param_value" translate="label comment" type="text" sortOrder="30"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Password parameter value</label>
                    <comment><![CDATA[The value of password parameter we expect to receive.]]></comment>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="hook_url_param_name" translate="label comment" type="text" sortOrder="40"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Hook url parameter name</label>
                    <comment><![CDATA[The name of hook url parameter we expect to receive.]]></comment>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="failure_url" translate="label comment" type="text" sortOrder="50"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Failure url</label>
                    <comment><![CDATA[The user is redirected to specified url once the login is failed.]]></comment>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="success_url" translate="label comment" type="text" sortOrder="50"
                       showInDefault="0"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Success url</label>
                    <comment><![CDATA[The user is redirected to specified url after order is placed.]]></comment>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
                <field id="debug_logging" translate="label comment" type="select" sortOrder="60"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1"
                       canRestore="1">
                    <label>Debug Logging</label>
                    <comment><![CDATA[]]></comment>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                    <depends>
                        <field id="enable">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
