<?php
namespace Totaltools\OCI\Model\ResourceModel;

use Magento\Framework\Model\ResourceModel\Db\VersionControl\RelationComposite;
use Magento\Framework\Model\ResourceModel\Db\VersionControl\Snapshot;
use Magento\SalesSequence\Model\Manager;

class Quote extends \Magento\Quote\Model\ResourceModel\Quote
{
    /**
     * @var \Totaltools\OCI\Helper\Data
     */
    private $_ociHelper;

    /**
     * Core store config
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $_scopeConfig;

    /**
     * @var \Totaltools\OCI\Model\Session $_ociSession Default website session.
     */
    protected $_ociSession;

    /**
     * @param \Magento\Framework\Model\ResourceModel\Db\Context $context
     * @param Snapshot $entitySnapshot,
     * @param RelationComposite $entityRelationComposite,
     * @param \Magento\SalesSequence\Model\Manager $sequenceManager
     * @param string $connectionName
     */
    public function __construct(
        \Magento\Framework\Model\ResourceModel\Db\Context $context,
        Snapshot $entitySnapshot,
        RelationComposite $entityRelationComposite,
        Manager $sequenceManager,
        \Totaltools\OCI\Model\Session $ociSession,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Totaltools\OCI\Helper\Data $ociHelper,
        $connectionName = null
    ) {
        parent::__construct(
            $context,
            $entitySnapshot,
            $entityRelationComposite,
            $sequenceManager,
            $connectionName
        );

        $this->_ociSession = $ociSession;
        $this->_scopeConfig = $scopeConfig;
        $this->_ociHelper = $ociHelper;
    }

    /**
     * Load quote data by customer identifier
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @param int $customerId
     * @return $this
     */
    public function loadByCustomerId($quote, $customerId)
    {
        $connection = $this->getConnection();
        $select = $this->_getLoadSelect(
            'customer_id',
            $customerId,
            $quote
        )->where(
            'is_active = ?',
            1
        )->order(
            'updated_at ' . \Magento\Framework\DB\Select::SQL_DESC
        )->limit(
            1
        );

        $token = $this->_ociSession->getToken();

        if ($this->_ociHelper->isRestrictionEnabled()) {
            $select->where(
                'oci_customer_token = ?',
                $token
            );
        }

        $data = $connection->fetchRow($select);

        if ($data) {
            $quote->setData($data);
        }

        $this->_afterLoad($quote);

        return $this;
    }
}