<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Model;

/**
 * Class Session.
 * OCI website session model.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Session extends \Magento\Framework\Session\SessionManager
{
    /**
     * @const string Token parameter name.
     */
    const TOKEN_PARAMETER_NAME = 'token';
    /**
     * @const string Hook url parameter name.
     */
    const HOOK_URL_PARAMETER_NAME = 'hook_url';
    /**
     * @const string Redirect url parameter name.
     */
    const REDIRECT_URL_PARAMETER_NAME = 'redirect_url';
    /**
     * @const string Reload customer data parameter name.
     */
    const RELOAD_CUSTOMER_DATA_PARAMETER_NAME = 'reload_customer_data';

    /**
     * @var \Magento\Framework\Math\Random $_randomDataGenerator Random data generator.
     */
    protected $_randomDataGenerator;

    /**
     * Session constructor.
     *
     * @param \Magento\Framework\App\Request\Http                    $request               Request model.
     * @param \Magento\Framework\Session\SidResolverInterface        $sidResolver           SID resolver.
     * @param \Magento\Framework\Session\Config\ConfigInterface      $sessionConfig         Session config.
     * @param \Magento\Framework\Session\SaveHandlerInterface        $saveHandler           Save handler.
     * @param \Magento\Framework\Session\ValidatorInterface          $validator             Session validator.
     * @param \Magento\Framework\Session\StorageInterface            $storage               Session storage.
     * @param \Magento\Framework\Stdlib\CookieManagerInterface       $cookieManager         Cookie manager.
     * @param \Magento\Framework\Stdlib\Cookie\CookieMetadataFactory $cookieMetadataFactory Cookie metadata factory.
     * @param \Magento\Framework\App\State                           $appState              App state.
     * @param \Magento\Framework\Math\Random                         $randomDataGenerator   Random dada generator.
     */
    public function __construct(
        \Magento\Framework\App\Request\Http $request,
        \Magento\Framework\Session\SidResolverInterface $sidResolver,
        \Magento\Framework\Session\Config\ConfigInterface $sessionConfig,
        \Magento\Framework\Session\SaveHandlerInterface $saveHandler,
        \Magento\Framework\Session\ValidatorInterface $validator,
        \Magento\Framework\Session\StorageInterface $storage,
        \Magento\Framework\Stdlib\CookieManagerInterface $cookieManager,
        \Magento\Framework\Stdlib\Cookie\CookieMetadataFactory $cookieMetadataFactory,
        \Magento\Framework\App\State $appState,
        \Magento\Framework\Math\Random $randomDataGenerator
    )
    {
        parent::__construct(
            $request,
            $sidResolver,
            $sessionConfig,
            $saveHandler,
            $validator,
            $storage,
            $cookieManager,
            $cookieMetadataFactory,
            $appState
        );

        $this->_randomDataGenerator = $randomDataGenerator;
    }

    /**
     * Determines whether token is set.
     *
     * @return bool
     */
    public function isTokenSet()
    {
        return $this->storage->hasData(self::TOKEN_PARAMETER_NAME)
            && !is_null($this->storage->getData(self::TOKEN_PARAMETER_NAME));
    }

    /**
     * Get token.
     *
     * @return string
     */
    public function getToken()
    {
        return $this->storage->getData(self::TOKEN_PARAMETER_NAME);
    }

    /**
     * Generates token and stores its value.
     * NOTE: it seems we don't need unique hash for now, simple flag will be enough.
     *
     * @return void
     */
    public function generateToken()
    {
        $this->storage->setData(
            self::TOKEN_PARAMETER_NAME,
            $this->_randomDataGenerator->getUniqueHash()
        );
    }

    /**
     * Unset token value.
     *
     * @return void
     */
    public function unsetToken()
    {
        $this->storage->setData(self::TOKEN_PARAMETER_NAME, null);
    }

    /**
     * Sets hook url.
     *
     * @param string $value Hook url.
     *
     * @return void
     */
    public function setHookUrl($value)
    {
        $this->storage->setData(self::HOOK_URL_PARAMETER_NAME, $value);
    }

    /**
     * Returns hook url.
     *
     * @return string
     */
    public function getHookUrl()
    {
        return $this->storage->getData(self::HOOK_URL_PARAMETER_NAME);
    }

    /**
     * Unset hook url.
     *
     * @return void
     */
    public function unsetHookUrl()
    {
        $this->storage->unsetData(self::HOOK_URL_PARAMETER_NAME);
    }

    /**
     * Sets redirect url.
     *
     * @param string $value Redirect url.
     *
     * @return void
     */
    public function setRedirectUrl($value)
    {
        $this->storage->setData(self::REDIRECT_URL_PARAMETER_NAME, $value);
    }

    /**
     * Returns redirect url.
     *
     * @return string
     */
    public function getRedirectUrl()
    {
        return $this->storage->getData(self::REDIRECT_URL_PARAMETER_NAME);
    }

    /**
     * Unset redirect url.
     *
     * @return void
     */
    public function unsetRedirectUrl()
    {
        $this->storage->unsetData(self::REDIRECT_URL_PARAMETER_NAME);
    }

    /**
     * Determines whether redirect url is set.
     *
     * @return bool
     */
    public function isRedirectUrlSet()
    {
        $redirectUrl = $this->storage->getData(self::REDIRECT_URL_PARAMETER_NAME);

        return !empty($redirectUrl);
    }
}