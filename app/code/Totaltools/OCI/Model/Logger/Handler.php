<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Model\Logger;

/**
 * Class Handler
 * @package Totaltools\OCI\Model\Logger
 */
class Handler extends \Magento\Framework\Logger\Handler\Base
{
    /**
     * @var string
     */
    protected $fileName = '/var/log/oci.log';
    /**
     * @var int
     */
    protected $loggerType = \Monolog\Logger::DEBUG;
    /**
     * @var \Totaltools\OCI\Helper\Data
     */
    protected $baseHelper;

    /**
     * Debug constructor.
     *
     * @param \Magento\Framework\Filesystem\DriverInterface $filesystem
     * @param \Totaltools\OCI\Helper\Data              $baseHelper
     * @param null|string                                   $filePath
     */
    public function __construct(
        \Magento\Framework\Filesystem\DriverInterface $filesystem,
        \Totaltools\OCI\Helper\Data $baseHelper,
        $filePath = null
    )
    {
        $this->baseHelper = $baseHelper;
        parent::__construct($filesystem, $filePath);
    }

    /**
     * Determines whether specified record should be handled.
     *
     * @param array $record
     *
     * @return bool
     */
    public function isHandling(array $record)
    {
        if (!isset($record['level'])) {
            return false;
        }

        switch ($record['level']) {
            case \Monolog\Logger::DEBUG:
                return $this->baseHelper->isDebugLoggingEnabled();
                break;
            default:
                return parent::isHandling($record);
                break;
        }
    }
}