<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\OCI\Model\Quote;

/**
 * Class Translator.
 * Quote translator. Translates quote data to appropriate format.
 *
 * @category  Totaltools
 * @package   Totaltools_OCI
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Translator
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;
    /**
     * @var \Magento\Catalog\Model\ProductRepository
     */
    protected $_productRepository;
    /**
     * Filter manager
     *
     * @var \Magento\Framework\Filter\FilterManager
     */
    protected $_filterManager;

    /**
     * Translator constructor.
     *
     * @param \Magento\Checkout\Model\Session $checkoutSession
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Catalog\Model\ProductRepository $productRepository,
        \Magento\Framework\Filter\FilterManager $filterManager
    )
    {
        $this->_checkoutSession = $checkoutSession;
        $this->_productRepository = $productRepository;
        $this->_filterManager = $filterManager;
    }

    /**
     * Returns translated quote data.
     *
     * @return array
     */
    public function getQuoteData()
    {
        $quoteData = [];
        $index = 1;
        /*** @var \Magento\Quote\Model\Quote\Item $quoteItem */
        foreach ($this->_checkoutSession->getQuote()->getItems() as $quoteItem) {
            $product = $this->_productRepository->getById($quoteItem->getProduct()->getId());
            $brand = $product->getResource()->getAttribute('brand')->getFrontend()->getValue($product);

            $quoteData['NEW_ITEM-DESCRIPTION'][$index] = $quoteItem->getName();
            $quoteData['NEW_ITEM-MATNR'][$index] = '';
            $quoteData['NEW_ITEM-QUANTITY'][$index] = $quoteItem->getQty();
            $quoteData['NEW_ITEM-UNIT'][$index] = 'PC';
            $quoteData['NEW_ITEM-PRICE'][$index] = $quoteItem->getPrice();
            $quoteData['NEW_ITEM-CURRENCY'][$index] = 'AUD';
            $quoteData['NEW_ITEM-PRICEUNIT'][$index] = '1';
            $quoteData['NEW_ITEM-LEADTIME'][$index] = '';
            $quoteData['NEW_ITEM-LONGTEXT_' . $index . ':132'] = trim($this->stripTags($product->getDescription()));
            $quoteData['NEW_ITEM-VENDOR'][$index] = '';
            $quoteData['NEW_ITEM-VENDORMAT'][$index] = $product->getSku();
            $quoteData['NEW_ITEM-MANUFACTCODE'][$index] = $brand;
            $quoteData['NEW_ITEM-MANUFACTMAT'][$index] = $product->getData('part_no');
            $quoteData['NEW_ITEM-MATGROUP'][$index] = $product->getData('oci_code');
            $quoteData['NEW_ITEM-CONTRACT'][$index] = '';
            $quoteData['NEW_ITEM-CONTRACT_ITEM'][$index] = '';

            $index++;
        }

        return $quoteData;
    }

    /**
     * Wrapper for standard strip_tags() function with extra functionality for html entities.
     *
     * @param string $data
     * @param string|null $allowableTags
     * @param bool $allowHtmlEntities
     *
     * @return string
     */
    public function stripTags($data, $allowableTags = null, $allowHtmlEntities = false)
    {
        return $this->_filterManager->stripTags(
            $data,
            ['allowableTags' => $allowableTags, 'escape' => $allowHtmlEntities]
        );
    }
}