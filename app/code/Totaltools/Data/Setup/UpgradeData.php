<?php
/**
 * @package Totaltools_Data
 */

namespace Totaltools\Data\Setup;

use Magento\Framework\Filter\FilterManager;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\ObjectManagerInterface;
use Magento\Framework\App\State;
use Magento\Backend\App\Area\FrontNameResolver;
use Totaltools\Data\Setup\Attributes\Combo;
use Totaltools\Data\Setup\Attributes\Document;
use Totaltools\Data\Setup\Attributes\GoogleShoppingCategory;

/**
 * Upgrade Total Tools data
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * EAV setup factory
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * Product attributes
     * @var ProductAttribute
     */
    protected $productAttribute;

    /**
     * Australian states data
     * @var StatesAustralian
     */
    protected $statesAustralian;

    /**
     * Combo attributes
     * @var Combo
     */
    protected $comboAttributes;

    /**
     * Document attributes
     * @var Document
     */
    protected $documentAttributes;

    /**
     * Google Shopping category attribute
     * @var GoogleShoppingCategory
     */
    protected $googleShoppingCategory;

    /**
     * Attribute Set
     * @var AttributeSet
     */
    protected $attributeSet;

    /**
     * Object manager interface
     * @var ObjectManagerInterface
     */
    protected $objectManager;

    /**
     * Filter manager
     * @var FilterManager
     */
    protected $filterManager;

    /**
     * State
     * @var string
     */
    private $state;

    /**
     * UpgradeData constructor
     *
     * @param ObjectManagerInterface $objectManager
     * @param ProductAttribute $productAttribute
     * @param AttributeSet $attributeSet
     * @param StatesAustralian $statesAustralian
     * @param Combo $comboAttributes
     * @param EavSetupFactory $eavSetupFactory
     * @param FilterManager $filterManager
     * @param Document $documentAttributes
     * @param GoogleShoppingCategory $googleShoppingCategory
     * @param State $state
     */
    public function __construct(
        ObjectManagerInterface $objectManager,
        ProductAttribute $productAttribute,
        AttributeSet $attributeSet,
        StatesAustralian $statesAustralian,
        Combo $comboAttributes,
        EavSetupFactory $eavSetupFactory,
        FilterManager $filterManager,
        Document $documentAttributes,
        GoogleShoppingCategory $googleShoppingCategory,
        State $state
    ) {
        $this->objectManager = $objectManager;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->productAttribute = $productAttribute;
        $this->attributeSet = $attributeSet;
        $this->statesAustralian = $statesAustralian;
        $this->comboAttributes = $comboAttributes;
        $this->filterManager = $filterManager;
        $this->documentAttributes = $documentAttributes;
        $this->googleShoppingCategory = $googleShoppingCategory;
        $this->state = $state;
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        $version = $context->getVersion();

        if ($context->getVersion() && version_compare($version, '1.0.1') < 0) {
            $this->updateProductAttribute($setup, $version);
        }

        if ($context->getVersion() && version_compare($version, '1.0.2') < 0) {
            $this->updateProductAttribute($setup, $version);
        }

        if ($context->getVersion() && version_compare($version, '1.1.1') < 0) {
            $this->installAttributeSets($setup, $version);
        }

        if (version_compare($version, '1.1.2') < 0) {
            $this->updateProductTypeAttribute($setup, $version);
        }

        // install Combo attributes
        if (version_compare($version, '1.1.3') < 0) {
            $this->comboAttributes->install($setup);
        }

        if (version_compare($version, '1.1.4') < 0) {
            $this->documentAttributes->install($setup);
        }

        if (version_compare($version, '1.1.5') < 0) {
            $this->updateProductTypeAttribute2($setup, $version);
        }

        if (version_compare($version, '1.1.6') < 0) {
            $this->updateBrandAttribute($setup);
        }

        if (version_compare($version, '1.1.7') < 0) {
            $this->updateIncludeOnHomepage($setup);
        }

        if (version_compare($version, '1.1.8') < 0) {
            $this->googleShoppingCategory->install($setup);
        }

        if (version_compare($version, '1.1.9') < 0) {
            $this->attributeSet->updateAttributeSets($setup);
            // Always check Australian states are installed
            $this->statesAustralian->install($setup);
        }

        // ... more version in the case we need update product attribute by another csv


        if (version_compare($version, '1.2.0') < 0) {
            $this->statesAustralian->update($setup);
        }

        if (version_compare($version, '1.2.1') < 0) {
            $this->updateOptionStockAvailabilityCodeAttribute($setup);
        }


        $setup->endSetup();
    }

    /**
     * update product attribute 'brand'
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function updateIncludeOnHomepage($setup)
    {
        // add attributes
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $params = array(
            'type' => 'int',
            'label' => 'Include On Homepage',
            'input' => 'select',
            'source_model' => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
            'required' => false,
            'sort_order' => 5,
            'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
            'group' => 'General Information',
        );
        $eavSetup->removeAttribute(\Magento\Catalog\Model\Category::ENTITY, 'include_on_homepage');
        $eavSetup->addAttribute(\Magento\Catalog\Model\Category::ENTITY, 'include_on_homepage', $params);
    }

    /**
     * update product attribute 'brand'
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function updateBrandAttribute($setup)
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $attribute = $eavSetup->getAttribute(\Magento\Catalog\Model\Product::ENTITY, 'brand');
        $entityTypeId = $eavSetup->getEntityTypeId(\Magento\Catalog\Model\Product::ENTITY);
        if (isset($attribute['attribute_id'])) {
            $eavSetup->updateAttribute($entityTypeId, $attribute['attribute_id'], 'used_in_product_listing', '1');
        }
    }

    /**
     * update product attribute
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */

    public function updateProductAttribute($setup, $version) {
        // add attributes
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $attributes = $this->productAttribute->initialProductAttributes($version);

        foreach ($attributes as $attributeCode => $params) {
            $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode);
            $eavSetup->addAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode, $params);
        }
    }

    public function updateProductTypeAttribute2($setup, $version)
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $attribute = $eavSetup->getAttribute(\Magento\Catalog\Model\Product::ENTITY, 'product_type');
        $entityTypeId = $eavSetup->getEntityTypeId(\Magento\Catalog\Model\Product::ENTITY);
        if (isset($attribute['attribute_id'])) {
            $eavSetup->updateAttribute($entityTypeId, $attribute['attribute_id'], 'attribute_code', 'stock_availability_code');
        }
    }

    public function updateProductTypeAttribute($setup, $version) {
        // add attributes
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $productTypeParams = array(
            'group' => 'Product Details',
            'sort_order' => 1005,
            'type'  => 'varchar',
            'backend'   => '',
            'frontend'  => '',
            'label'     => 'Product Type',
            'input'     => 'select',
            'class'     => '',
            'source'    => 'Magento\Eav\Model\Entity\Attribute\Source\Table',
            'global'    => \Magento\Catalog\Model\ResourceModel\Eav\Attribute::SCOPE_GLOBAL,
            'visible'   => true,
            'required'  => true,
            'user_defined'  => 1,
            'searchable'=> 0,
            'filterable'=> 0,
            'comparable'=> 0,
            'visible_on_front' => false,
            'used_in_product_listing'   => true,
            'used_for_promo_rules'   => 0,
            'is_html_allowed_on_front'  => 0,
            'filterable_in_search'   => 0,
            'used_for_sort_by'          => 0,
            'apply_to'  => ''
        );
        $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, 'product_type');
        $eavSetup->addAttribute(\Magento\Catalog\Model\Product::ENTITY, 'product_type', $productTypeParams);

        // add options
        $attribute = $this->objectManager->create('\Magento\Eav\Model\Entity\Attribute');
        $attributeData = $eavSetup->getAttribute(\Magento\Catalog\Model\Product::ENTITY, 'product_type');
        $attributeId = $attributeData['attribute_id'];
        $attribute->load($attributeId);
        $values = array('OL', 'OD', 'OC', 'SP');
        foreach ($values as $value) {
            $option['value']['option_1'][0] = $value;
            //$option['value']['option_1'][1] = $value;
            $attribute->addData(array('option'=>$option));
            $attribute->save();
        }
    }

    /**
     * @param $setup
     */
    private function updateOptionStockAvailabilityCodeAttribute($setup)
    {
        // add attributes
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        // add options
        $attribute = $this->objectManager->create('\Magento\Eav\Model\Entity\Attribute');
        $attributeData = $eavSetup->getAttribute(\Magento\Catalog\Model\Product::ENTITY, 'stock_availability_code');
        $attributeId = $attributeData['attribute_id'];
        $attribute->load($attributeId);
        $option['value']['option_1'][0] = 'OX';
        $attribute->addData(['option' => $option]);
        $attribute->save();
    }

    /**
     * install attribute sets, set product attribute to attribute sets
     */
    public function installAttributeSets($setup, $version)
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $attributeSets = $this->attributeSet->initialAttributeSets($version);

        // set Area Code to fix bug "Area is not set ...."
        $this->state->setAreaCode(FrontNameResolver::AREA_CODE);

        $defaultId = $eavSetup->getDefaultAttributeSetId(\Magento\Catalog\Model\Product::ENTITY);
        foreach ($attributeSets as $params) {
            if (isset($params['attribute_set_name'])) {
                $attributeSetName = $params['attribute_set_name'];
                // add attribute sets
                $model = $this->objectManager
                    ->create('Magento\Eav\Api\Data\AttributeSetInterface')
                    ->setId(null)
                    ->setEntityTypeId(4)
                    ->setAttributeSetName($attributeSetName);

                $this->objectManager
                    ->create('Magento\Eav\Api\AttributeSetManagementInterface')
                    ->create(\Magento\Catalog\Model\Product::ENTITY, $model, $defaultId)
                    ->save();

                /*$eavSetup->addAttributeSet(\Magento\Catalog\Model\Product::ENTITY, $attributeSetName);*/
                $attributeSet = $eavSetup->getAttributeSet(\Magento\Catalog\Model\Product::ENTITY, $attributeSetName);
                $attributeSetId = $attributeSet['attribute_set_id'];

                // add attribute to attribute sets
                $attributeCodes = $params['attribute_codes'];
                $attributeCodes = explode(',', $attributeCodes);
                foreach ($attributeCodes as $attributeCode) {
                    $defaultGroupId = $eavSetup->getDefaultAttributeGroupId(
                        \Magento\Catalog\Model\Product::ENTITY,
                        $attributeSetId
                    );
                    //addAttributeToSet($entityTypeId, $setId, $groupId, $attributeId, $sortOrder = null)
                    $eavSetup->addAttributeToSet(
                        \Magento\Catalog\Model\Product::ENTITY,
                        $attributeSetId,
                        $defaultGroupId,
                        $attributeCode
                    );
                }

                // creating rapidflow profile importing product for the attribute sets
                /*$profile = $this->objectManager->create('Unirgy\RapidFlow\Model\Profile');
                $profile->addData(
                    array(
                        'title' => 'Import products - '.$attributeSetName,
                        'profile_status'    => 'enabled',
                        'profile_type'      => 'import',
                        'data_type'         => 'product',
                        'store_id'          => 0,
                        'base_dir'          => '',
                        'filename'          => $this->processFileNameForAttributeSetFile($attributeSetName),
                        'json_import'       => '',
                        'columns_post'      => [],
                        'options'           => array(
                            'log'   => array('min_level' => 'SUCCESS'),
                            'debug' => '0'
                        ),
                        'created_time'      => date('Y-m-d H:i:s'),
                        'update_time'       => date('Y-m-d H:i:s'),

                    )
                );
                $profile->save();
                */
            }
        }
    }

    /*public function processFileNameForAttributeSetFile($value)
    {
        $fileName = $this->filterManager->translitUrl($value);
        return $fileName.'csv';
    }*/

}
