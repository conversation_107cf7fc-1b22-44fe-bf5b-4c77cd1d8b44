<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Data
 */

namespace Totaltools\Data\Setup;

class ProductAttribute
{
    /**
     * @var \Magento\Framework\Filesystem
     */
    protected $fileSystem;

    /**
     * @var \Magento\Framework\Filesystem\DirectoryList
     */
    protected $directoryList;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Init
     *
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        \Magento\Framework\Filesystem $fileSystem,
        \Magento\Framework\Logger\Monolog $logger,
        \Magento\Framework\Filesystem\DirectoryList $directoryList
    )
    {
        $this->fileSystem = $fileSystem;
        $this->logger = $logger;
        $this->directoryList = $directoryList;
    }

    public function initialProductAttributes($version)
    {
        $ds = DIRECTORY_SEPARATOR;
        $csvFile = 'app'.$ds.'code'.$ds.'Totaltools'.$ds.'Data'.$ds.'data'.$ds.'product_attributes_'.$version.'.csv';
        //$path = $this->directoryList->getDefaultConfig();
        $tmpDirectory = $this->fileSystem->getDirectoryRead('base');
        $path = $tmpDirectory->getRelativePath($csvFile);
        $stream = $tmpDirectory->openFile($path);

        $headers = $stream->readCsv();
        if ($headers === false) {
            $stream->close();
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Please use the correct file format for product_attribute.csv.')
            );
        }
        $productAttributes = array();
        try {
            $rowNumber = 1;
            while (false !== ($csvLine = $stream->readCsv())) {
                $rowNumber++;

                if (empty($csvLine)) {
                    continue;
                }

                $attributeInfo = array();
                $attributeCode = '';
                foreach ($this->matchingAttributes() as $key=>$attribute) {
                    if ($attribute == 'attribute_code') {
                        $attributeCode = $csvLine[$key];
                    } else {
                        if (isset($csvLine[$key])) {
                            $attributeInfo[$attribute] = $csvLine[$key];
                        }
                    }
                }
                if ($attributeCode) {
                    $productAttributes[$attributeCode] = $attributeInfo;
                }
            }
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $stream->close();
            throw new \Magento\Framework\Exception\LocalizedException(__($e->getMessage()));
        } catch (\Exception $e) {
            $stream->close();
            $this->logger->critical($e);
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Something went wrong while importing csv.')
            );
        }
        return $productAttributes;
    }

    public function inititalProductAttributeSets($version)
    {
        $csvFile = 'attribute_sets_'.$version;
        $attributeSets = array();

        return $attributeSets;
    }

    public function matchingAttributes()
    {
        return array(
            'attribute_code',
            'label',
            'type',
            'input',
            'source',
            'global',
            'visible',
            'required',
            'user_defined',
            'searchable',
            'filterable',
            'comparable',
            'visible_on_front',
            'used_in_product_listing',
            'used_for_promo_rules',
            'is_html_allowed_on_front',
            'filterable_in_search',
            'used_for_sort_by',
            'apply_to',
            'sort_order'
        );
    }


}