<?php
namespace Totaltools\Data\Setup\Attributes;

class Document
{


    protected $attributes;

    /**
     * @var \Magento\Eav\Setup\EavSetupFactory
     */
    protected $eavSetupFactory;


    /**
     * @param $eavSetupFactory \Magento\Eav\Setup\EavSetupFactory
     */
    public function __construct (
        \Magento\Eav\Setup\EavSetupFactory $eavSetupFactory
    ) {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->setAttributeData();
    }

    protected  function setAttributeData()
    {
        $this->attributes = [
            'attachment_title_1' => array(101, 'Attachment Title 1'),
            'attachment_1' => array(102, 'Attachment 1'),
            'attachment_title_2' => array(103, 'Attachment Title 2'),
            'attachment_2' => array(104, 'Attachment 2'),
            'attachment_title_3' => array(105, 'Attachment Title 3'),
            'attachment_3' => array(106, 'Attachment 3'),
            'attachment_title_4' => array(107, 'Attachment Title 4'),
            'attachment_4' => array(108, 'Attachment 4'),
            'attachment_title_5' => array(109, 'Attachment Title 5'),
            'attachment_5' => array(110, 'Attachment 5'),
        ];
    }

    protected function attributeParams($sortOrder, $label) {
         return [
            'group'      => 'Document',
            'sort_order' => $sortOrder,
            'backend'        => null,
            'type'           => 'text',
            'table'          => null,
            'frontend'       => null,
            'input'          => 'text',
            'label'          => $label,
            'frontend_class' => null,
            'required'       => 0,
            'user_defined'   => 1,
            'default'        => null,
            'unique'         => 0,
            'note'           => null,
            'global'         => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL,
            'input_renderer'             => null,
            'visible'                    => 1,
            'searchable'                 => 0,
            'filterable'                 => 0,
            'comparable'                 => 0,
            'visible_on_front'           => 0,
            'wysiwyg_enabled'            => 0,
            'is_html_allowed_on_front'   => 0,
            'visible_in_advanced_search' => 0,
            'filterable_in_search'       => 0,
            'used_in_product_listing'    => 1,
            'used_for_sort_by'           => 0,
            'apply_to'                   => implode(',', [
                \Magento\Catalog\Model\Product\Type::TYPE_SIMPLE,
                \Magento\Catalog\Model\Product\Type::TYPE_BUNDLE,
                \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE,
            ]),
            'position'              => 0,
            'used_for_promo_rules'  => 0,
            'is_used_in_grid'       => 0,
            'is_visible_in_grid'    => 0,
            'is_filterable_in_grid' => 0,
        ];
    }


    public function install ($setup) {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

        foreach ($this->attributes as $attributeCode => $config) {
            // Check if already exists
            //if ($eavSetup->getAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode)) continue;
            $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode);
            $eavSetup->addAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode,
                $this->attributeParams($config[0], $config[1]));
        }
    }


}
