<?php
namespace Totaltools\Data\Setup\Attributes;

class Combo
{


    protected $attributes;


    /**
     * @var \Magento\Eav\Setup\EavSetupFactory
     */
    protected $eavSetupFactory;


    /**
     * @param $eavSetupFactory \Magento\Eav\Setup\EavSetupFactory
     */
    public function __construct (
        \Magento\Eav\Setup\EavSetupFactory $eavSetupFactory
    ) {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->setAttributeData();
    }


    protected function setAttributeData () {
        $this->attributes = [
            'combo_link' => [
                'group'      => 'Product Details',
                'sort_order' => 91,

                'option' => [
                    'values' => [
                        0 => '-',
                    ],
                ],

                'backend'        => null,
                'type'           => 'int',
                'table'          => null,
                'frontend'       => null,
                'input'          => 'select',
                'label'          => 'Combo Link',
                'frontend_class' => null,
                'source'         => 'Magento\Eav\Model\Entity\Attribute\Source\Table',
                'required'       => 0,
                'user_defined'   => 1,
                'default'        => null,
                'unique'         => 0,
                'note'           => null,
                'global'         => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL,

                'input_renderer'             => null,
                'visible'                    => 1,
                'searchable'                 => 0,
                'filterable'                 => 0,
                'comparable'                 => 0,
                'visible_on_front'           => 0,
                'wysiwyg_enabled'            => 0,
                'is_html_allowed_on_front'   => 0,
                'visible_in_advanced_search' => 0,
                'filterable_in_search'       => 0,
                'used_in_product_listing'    => 0,
                'used_for_sort_by'           => 0,
                'apply_to'                   => implode(',', [
                    \Magento\Catalog\Model\Product\Type::TYPE_SIMPLE,
                    \Magento\Catalog\Model\Product\Type::TYPE_BUNDLE,
                    \Magento\Catalog\Model\Product\Type::TYPE_VIRTUAL,
                    \Magento\Downloadable\Model\Product\Type::TYPE_DOWNLOADABLE,
                    \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD,
                    \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE,
                ]),
                'position'              => 0,
                'used_for_promo_rules'  => 1,
                'is_used_in_grid'       => 1,
                'is_visible_in_grid'    => 0,
                'is_filterable_in_grid' => 1,
            ],

            'combo_base' => [
                'group'      => 'Product Details',
                'sort_order' => 91,

                'backend'        => null,
                'type'           => 'int',
                'table'          => null,
                'frontend'       => null,
                'input'          => 'select',
                'label'          => 'Combo Base',
                'frontend_class' => null,
                'source'         => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                'required'       => 0,
                'user_defined'   => 1,
                'default'        => 0,
                'unique'         => 0,
                'note'           => null,
                'global'         => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_GLOBAL,

                'input_renderer'             => null,
                'visible'                    => 1,
                'searchable'                 => 0,
                'filterable'                 => 0,
                'comparable'                 => 0,
                'visible_on_front'           => 0,
                'wysiwyg_enabled'            => 0,
                'is_html_allowed_on_front'   => 0,
                'visible_in_advanced_search' => 0,
                'filterable_in_search'       => 0,
                'used_in_product_listing'    => 0,
                'used_for_sort_by'           => 0,
                'apply_to'                   => implode(',', [
                    \Magento\Catalog\Model\Product\Type::TYPE_SIMPLE,
                    \Magento\Catalog\Model\Product\Type::TYPE_BUNDLE,
                    \Magento\Catalog\Model\Product\Type::TYPE_VIRTUAL,
                    \Magento\Downloadable\Model\Product\Type::TYPE_DOWNLOADABLE,
                    \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD,
                    \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE,
                ]),
                'position'              => 0,
                'used_for_promo_rules'  => 1,
                'is_used_in_grid'       => 1,
                'is_visible_in_grid'    => 0,
                'is_filterable_in_grid' => 1,
            ],
        ];
    }


    public function install ($setup) {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

        foreach ($this->attributes as $attributeCode => $attributeConfig) {
            // Check if already exists
            if ($eavSetup->getAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode)) continue;

            $eavSetup->addAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode, $attributeConfig);
        }
    }


}
