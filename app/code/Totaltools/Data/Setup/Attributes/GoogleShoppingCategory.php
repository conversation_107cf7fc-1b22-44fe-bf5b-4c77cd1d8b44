<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Data
 */

namespace Totaltools\Data\Setup\Attributes;

use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableProductType;
use Magento\Downloadable\Model\Product\Type as DownloadableProductType;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Model\Entity\Attribute\Source\Table as TableAttributeSource;
use Magento\Eav\Setup\EavSetup;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\GiftCard\Model\Catalog\Product\Type\Giftcard as GiftcardProductType;

class GoogleShoppingCategory
{

    /**
     * Attribute code
     * @var string
     */
    private $attributeCode   = 'google_shopping_category';

    /**
     * Attribute config
     * @var string[]
     */
    private $attributeConfig = [
        'group'          => 'Product Details',
        'sort_order'     => 17,
        'backend'        => null,
        'type'           => 'text',
        'table'          => null,
        'frontend'       => null,
        'input'          => 'textarea',
        'label'          => 'Google Shopping Category',
        'frontend_class' => null,
        'source'         => null,
        'required'       => 0,
        'user_defined'   => 1,
        'default'        => null,
        'unique'         => 0,
        'note'           => null,
        'global'         => ScopedAttributeInterface::SCOPE_GLOBAL,

        'input_renderer'             => null,
        'visible'                    => 1,
        'searchable'                 => 0,
        'filterable'                 => 0,
        'comparable'                 => 0,
        'visible_on_front'           => 0,
        'wysiwyg_enabled'            => 0,
        'is_html_allowed_on_front'   => 0,
        'visible_in_advanced_search' => 0,
        'filterable_in_search'       => 0,
        'used_in_product_listing'    => 0,
        'used_for_sort_by'           => 0,
        'apply_to'                   =>
            ProductType::TYPE_SIMPLE . ',' .
            ProductType::TYPE_BUNDLE . ',' .
            ProductType::TYPE_VIRTUAL . ',' .
            DownloadableProductType::TYPE_DOWNLOADABLE . ',' .
            GiftcardProductType::TYPE_GIFTCARD . ',' .
            ConfigurableProductType::TYPE_CODE,
        'position'              => 0,
        'used_for_promo_rules'  => 0,
        'is_used_in_grid'       => 0,
        'is_visible_in_grid'    => 0,
        'is_filterable_in_grid' => 0,
    ];

    /**
     * EAV setup factory
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * GoogleShoppingCategory constructor
     *
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        EavSetupFactory $eavSetupFactory
    ) {
        $this->eavSetupFactory = $eavSetupFactory;
    }

    /**
     * Get attribute code
     *
     * @return string
     */
    public function getAttributeCode()
    {
        return $this->attributeCode;
    }

    /**
     * Get attribute config
     *
     * @return string[]
     */
    public function getAttributeConfig()
    {
        return $this->attributeConfig;
    }

    /**
     * Install Google Shopping category attribute
     *
     * @param ModuleDataSetupInterface $setup
     * @return void
     */
    public function install(
        ModuleDataSetupInterface $setup
    ) {
        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

        $attributeCode   = $this->getAttributeCode();
        $attributeConfig = $this->getAttributeConfig();

        $eavSetup->removeAttribute(Product::ENTITY, $attributeCode);
        if (!$eavSetup->getAttribute(Product::ENTITY, $attributeCode)) {
            $eavSetup->addAttribute(Product::ENTITY, $attributeCode, $attributeConfig);
        }
    }
}