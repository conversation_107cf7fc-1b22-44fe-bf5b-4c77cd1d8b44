<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Data
 */

namespace Totaltools\Data\Setup;

class AttributeSet
{
    /**
     * @var \Magento\Framework\Filesystem
     */
    protected $fileSystem;

    /**
     * @var \Magento\Framework\Filesystem\DirectoryList
     */
    protected $directoryList;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * EAV setup factory
     * @var EavSetupFactory
     */
    private $eavSetupFactory;


    /**
     * Init
     *
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        \Magento\Eav\Setup\EavSetupFactory $eavSetupFactory,
        \Magento\Framework\Filesystem $fileSystem,
        \Magento\Framework\Logger\Monolog $logger,
        \Magento\Framework\Filesystem\DirectoryList $directoryList
    )
    {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->fileSystem = $fileSystem;
        $this->logger = $logger;
        $this->directoryList = $directoryList;
    }

    public function initialAttributeSets($version)
    {
        $ds = DIRECTORY_SEPARATOR;
        $csvFile = 'app'.$ds.'code'.$ds.'Totaltools'.$ds.'Data'.$ds.'data'.$ds.'attribute_sets_'.$version.'.csv';
        //$path = $this->directoryList->getDefaultConfig();
        $tmpDirectory = $this->fileSystem->getDirectoryRead('base');
        $path = $tmpDirectory->getRelativePath($csvFile);
        $stream = $tmpDirectory->openFile($path);

        $headers = $stream->readCsv();
        if ($headers === false) {
            $stream->close();
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Please use the correct file format for attribute_sets.csv.')
            );
        }
        $attributeSets = array();
        try {
            $rowNumber = 1;
            while (false !== ($csvLine = $stream->readCsv())) {
                $rowNumber++;

                if (empty($csvLine)) {
                    continue;
                }

                $attributeInfo = array();
                foreach ($this->matchingAttributes() as $key=>$attribute) {
                    $attributeInfo[$attribute] = $csvLine[$key];
                }
                $attributeSets[] = $attributeInfo;
            }
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $stream->close();
            throw new \Magento\Framework\Exception\LocalizedException(__($e->getMessage()));
        } catch (\Exception $e) {
            $stream->close();
            $this->logger->critical($e);
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Something went wrong while importing csv.')
            );
        }
        return $attributeSets;
    }

    public function matchingAttributes()
    {
        return array(
            'attribute_set_name',
            'attribute_codes',
        );
    }

    /**
     * update attribute sets to make it the same "Default" set
     */
    public function updateAttributeSets($setup)
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

        $entityTypeId = $eavSetup->getEntityTypeId(\Magento\Catalog\Model\Product::ENTITY);
        $defaultAttributeSetId = $eavSetup->getDefaultAttributeSetId(\Magento\Catalog\Model\Product::ENTITY);

        $attributeSetIds = $eavSetup->getAllAttributeSetIds($entityTypeId);

        // regroup attributes on attribute sets
        foreach ($attributeSetIds as $attributeSetId) {

            if ($defaultAttributeSetId != $attributeSetId) {

                $attributeGroups = $this->attributeGroups();

                foreach ($attributeGroups as $groupName=>$attributes) {
                    $groupCode = $eavSetup->convertToAttributeGroupCode($groupName);
                    $attributeGroup = $eavSetup->getAttributeGroupByCode($entityTypeId, $attributeSetId, $groupCode);

                    $attributeGroupId = '';
                    if (!isset($attributeGroup['attribute_group_id'])) {
                        // create attribute group and add attribute into this group
                        $eavSetup->addAttributeGroup($entityTypeId, $attributeSetId, $groupName);
                        $group = $eavSetup->getAttributeGroupByCode($entityTypeId, $attributeSetId, $groupCode);
                        if ($group['attribute_group_id']) {
                            $attributeGroupId = $group['attribute_group_id'];
                        }
                    } else {
                        $attributeGroupId = $attributeGroup['attribute_group_id'];
                    }

                    if ($attributeGroupId) {
                        //save attributes into this group
                        foreach ($attributes as $attributeCode) {
                            $attribute = $eavSetup->getAttribute($entityTypeId, $attributeCode);
                            if(isset($attribute['attribute_id'])) {
                                $eavSetup->addAttributeToGroup(
                                    \Magento\Catalog\Model\Product::ENTITY,
                                    $attributeSetId,
                                    $attributeGroupId,
                                    $attribute['attribute_id']
                                );
                            }
                        }
                    }

                }
            }

        }
    }

    protected function attributeGroups()
    {
        return [
            'Product Details' => [
                'country_of_manufacture',
                'is_returnable',
                'visibility',
                'news_from_date',
                'news_to_date'
            ],
            'Content' => [
                'description',
                'short_description'
            ],
            'Bundle Items' => [
                'shipment_type'
            ],
            'Image Management' => [
                'image',
                'swatch_image'
            ],
            'Gift Options' => [
                'gift_message_available',
                'gift_wrapping_available',
                'gift_wrapping_price'
            ]
        ];
    }

}
