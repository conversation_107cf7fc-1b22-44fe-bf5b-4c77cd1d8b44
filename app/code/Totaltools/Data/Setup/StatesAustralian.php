<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Data
 */

namespace Totaltools\Data\Setup;

class StatesAustralian
{

    /**
     * Australian states
     */
    const STATES = [
        ['AU', 'ACT', 'Australian Capital Territory'],
        ['AU', 'NSW', 'New South Wales'],
        ['AU', 'NT',  'Northern Territory'],
        ['AU', 'QLD', 'Queensland'],
        ['AU', 'SA',  'South Australia'],
        ['AU', 'TAS', 'Tasmania'],
        ['AU', 'VIC', 'Victoria'],
        ['AU', 'WA',  'Western Australia'],
    ];

    /**
     * Install states
     *
     * @param ModuleDataSetupInterface $setup
     */
    public function install(
        \Magento\Framework\Setup\ModuleDataSetupInterface $setup
    )
    {
        foreach (self::STATES as $row) {
            // Check if already exists
            $select = $setup->getConnection()->select()
                ->from($setup->getTable('directory_country_region'))
                ->where('country_id = ?', $row[0])
                ->where('code = ?', $row[1])
            ;
            if ($select->query()->rowCount()) continue;

            // Insert into regions table
            $bind = ['country_id' => $row[0], 'code' => $row[1], 'default_name' => $row[2]];
            $setup->getConnection()->insert($setup->getTable('directory_country_region'), $bind);
            $regionId = $setup->getConnection()->lastInsertId($setup->getTable('directory_country_region'));

            // Insert into region name table
            $bind = ['locale' => 'en_AU', 'region_id' => $regionId, 'name' => $row[2]];
            $setup->getConnection()->insert($setup->getTable('directory_country_region_name'), $bind);
        }
    }
    /**
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $setup
     * @throws \Zend_Db_Statement_Exception
     */
    public function update(
        \Magento\Framework\Setup\ModuleDataSetupInterface $setup
    ) {
        $setup->getConnection()->delete(
            $setup->getTable('directory_country_region_name'),
            "locale = 'en_US' AND name IN ('Australian Capital Territory,New South Wales,Northern Territory,Queensland,South Australia,Tasmania,Victoria,Western Australia')"
        );
        $setup->getConnection()->delete(
            $setup->getTable('directory_country_region_name'),
            "locale = 'en_AU'"
        );
        foreach (self::STATES as $row) {
            // Check if already exists
            $select = $setup->getConnection()->select()
                ->from($setup->getTable('directory_country_region'))
                ->where('country_id = ?', $row[0])
                ->where('code = ?', $row[1])
            ;
            $count = (int) $select->query()->rowCount();
            switch($count) {
                case 0:
                    // Insert into regions table
                    $bind = ['country_id' => $row[0], 'code' => $row[1], 'default_name' => $row[2]];
                    $setup->getConnection()->insert($setup->getTable('directory_country_region'), $bind);
                    $regionId = $setup->getConnection()->lastInsertId($setup->getTable('directory_country_region'));

                    // Insert into region name table
                    $bind = ['locale' => 'en_AU', 'region_id' => $regionId, 'name' => $row[2]];
                    $setup->getConnection()->insert($setup->getTable('directory_country_region_name'), $bind);
                    break;
                default :
                    $regions = $select->query()->fetchAll();
                    $i = 0;
                    foreach ($regions as $region) {
                        if ($i === 0) {
                            // Insert into region name table
                            $bind = ['locale' => 'en_AU', 'region_id' => $region['region_id'], 'name' => $region['default_name']];
                            $setup->getConnection()->insert($setup->getTable('directory_country_region_name'), $bind);
                        } else {
                            $setup->getConnection()->delete(
                                $setup->getTable('directory_country_region'),
                                'region_id = '.$region['region_id']
                            );
                        }
                        $i++;
                    }
                    break;
            }
        }
    }
}
