<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Data
 */

namespace Totaltools\Data\Setup;

use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Eav\Setup\EavSetupFactory;
use Totaltools\Data\Setup\Attributes\Combo;
use Totaltools\Data\Setup\Attributes\GoogleShoppingCategory;

/**
 * Install Total Tools data
 */
class InstallData implements InstallDataInterface
{
    /**
     * EAV setup factory
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * Product attributes
     * @var ProductAttribute
     */
    protected $productAttribute;

    /**
     * Australian States data
     * @var StatesAustralian
     */
    protected $statesAustralian;

    /**
     * Combo attributes
     * @var Combo
     */
    protected $comboAttributes;

    /**
     * Google Shopping category attribute
     * @var GoogleShoppingCategory
     */
    protected $googleShoppingCategory;

    /**
     * InstallData constructor
     *
     * @param ProductAttribute $productAttribute
     * @param StatesAustralian $statesAustralian
     * @param Combo $comboAttributes
     * @param GoogleShoppingCategory $googleShoppingCategory
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        ProductAttribute $productAttribute,
        StatesAustralian $statesAustralian,
        Combo $comboAttributes,
        GoogleShoppingCategory $googleShoppingCategory,
        EavSetupFactory $eavSetupFactory
    ) {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->productAttribute = $productAttribute;
        $this->statesAustralian = $statesAustralian;
        $this->comboAttributes = $comboAttributes;
        $this->googleShoppingCategory = $googleShoppingCategory;
    }

    /**
     * Install data
     *
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface $context
     * @return void
     */
    public function install(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $installer = $setup;

        $installer->startSetup();

        // add attributes
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $attributes = $this->productAttribute->initialProductAttributes('1.0.0');

        foreach ($attributes as $attributeCode => $params) {
            $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode);
            $eavSetup->addAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode, $params);
        }

        // install Australian states
        $this->statesAustralian->install($setup);

        // install Combo attributes
        $this->comboAttributes->install($setup);

        // install Google Shopping category attribute
        $this->googleShoppingCategory->install($setup);

        $installer->endSetup();
    }
}
