attribute_code,label,type,input,source,global,visible,required,user_defined,searchable,filterable,comparable,visible_on_front,used_in_product_listing,used_for_promo_rules,is_html_allowed_on_front,filterable_in_search,used_for_sort_by,apply_to,sort_order
accuracy,Accuracy,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
air_comsumption,Air Consumption,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
air_inlet,Air Inlet,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
air_pressure,Air Pressure,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
air_volume,Air Volume,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
barcode,Barcode,varchar,text,,1,1,1,1,1,1,1,1,0,1,0,1,0,,100
batteries_supplied,Batteries Supplied,int,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
battery_capacity,Battery Capacity,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
battery_inc,Battery Included,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,0,1,0,0,1,1,0,0,0,0,0,,
battery_life,Battery Life,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
battery_type,Battery Type,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
battery_manual,Battery / Manual,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
blade_size_mm,Blade size (mm),varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
blade_diameter,Blade diameter,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
blows_per_min,Blows per minute,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
bore,Bore,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
brand,Brand,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,1,1,1,1,0,1,0,1,0,,102
bulb_type,Bulb Type,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,1,1,1,1,0,0,0,1,0,,
capacity_stands,Capacity,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
capacity,Capacity,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
catridge_size,Cartridge Size,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
catridge_bulk,Catridge / Bulk,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
charging_capacity,Charging Capacity,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,0,1,0,0,1,1,0,0,0,0,0,,
comp_sealent_size,Compatible Sealent Size,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
collect_capacity,Collet Capacity,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,0,1,0,0,0,0,0,,
continuous_amps,Continuous Amps,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
corded_cordless,Corded / Cordless,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
cord_length,Cord length,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
cutting_capacity,Cutting Capacity,varchar,text,,1,1,1,1,0,0,0,1,0,0,0,0,0,,
cutting_depth,Cutting Depth (mm),varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
cutting_depth_alum,Cutting Depth Aluminium,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
cutting_depth_metal,Cutting Depth Metal,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
cycle_rate,Cycle Rate,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
dc_output,DC Output,int,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
decibel_rating,Decibel Rating,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
delay,Delay,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,115
din_rating,DIN rating,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
disk_size,Disc Size,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
drive_size,Drive Size,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
drive_type,Drive Type,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
duty_cycle,Duty Cycle,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,113
effort_to_lift,Effort To Lift Full Load,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
engine,Engine,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
engine_displacement,Engine Displacement,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
fad_lpm,FAD (LPM),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
falls_of_chain,Falls of Chain,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
fuel,Fuel,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
fuel_cell_life,Fuel Cell Life,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
fuel_tank_capacity,Fuel Tank Capacity,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
generator_size_req,Generator Size Required,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,114
handles,Handles,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,0,1,0,0,1,1,0,0,0,0,0,,
headroom,Headroom,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
hose_length,Hose Length,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
hose_included,Hose Included,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,1,1,0,0,1,1,0,0,0,0,0,,
hose_reel,Hose Reel,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,1,1,0,0,1,1,0,0,0,0,0,,
joules,Joules,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
impact_rate_bpm,Impact Rate (BPM),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
ip_rating,IP Rating,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
ladder_height,Ladder Height,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
laser_class,Laser Class,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
lift_range,Lift Range,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
load_rating,Load Rating,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
loan_chain_diameter,Load Chain Diameter,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
lowered_height,Lowered Height,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
machine_speed,Machine Speed,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
mag_dead_lift,Mag Dead Lift,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
magazine_capacity,Magazine Capacity,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
material,Material,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,105
max_cutting_cap_at_90,Maximum Cutting Capacity at 90 Degrees,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_cutting_cap_mild_steel,Max Cutting Capacity - Mild Steel,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_cutting_cap_stainless,Max Cutting Capacity - Stainless Steel,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_cutting_depth,Max Cutting Depth,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_cut_depth_mm,Max Cut Depth (mm),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_cutting_diameter,Max Cutting Diameter,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_drill_cap_wood,Max Drill Capacity Wood (mm),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_drill_cap_masonary,Max Drill Capacity Masonary (mm),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_drill_cap_steel,Max Drill Capacity Steel (mm),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_drill_cap_concrete,Max Drill Capacity Concrete (mm),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_rpm,Max RPM,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
max_power,Max Power,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
max_pressure,Max Pressure,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
max_pushing_force,Max Pushing Force,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
max_flow_rate,Maximum Flow Rate,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
metric_af,Metric/AF,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
net_weight,Net Weight,varchar,text,,1,1,0,1,0,0,1,1,0,0,0,0,0,,112
no_load_speed_rpm,No Load Speed (RPM),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
no_of_sensors,No of Sensors,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
no_of_modes,Number of Modes,int,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
on_off_control,On Off Control,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
operating_pressure,Operating Pressure,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
operating_range,Operating Range,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
oscillation_angle,Oscillation Angle,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
oscillation_rate_opm,Oscillation Rate (OPM),varchar,text,,1,1,0,1,0,0,1,1,0,0,0,0,0,,
output_range,Output Range,varchar,text,,1,1,0,1,0,0,1,1,0,0,0,0,0,,115
overload_protection,Overload Protection,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,0,1,0,0,1,1,0,0,0,0,0,,116
pack_size,Pack Size,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
pad_diameter,Pad Diameter,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
part_no,Part No,varchar,text,,1,1,1,1,1,1,1,1,0,1,0,1,0,,101
petrol_electric,Petrol / Electric,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
phase,Phase (Amp),varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,116
planning_depth,Planing Depth,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
planning_width,Planing Width,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
plug,Plug,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
plunge_capacity,Plunge Capacity,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
pressure,Pressure,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
pressure_cycle_psi,Pressure Cycle (PSI),varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
pressure_range,Pressure Range,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
process,Process,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,116
product_length,Product Length,varchar,text,,1,1,0,1,0,0,1,1,0,0,0,0,0,,106
product_width,Product Width,varchar,text,,1,1,0,1,0,0,1,1,0,0,0,0,0,,107
product_height,Product Height,varchar,text,,1,1,0,1,0,0,1,1,0,0,0,0,0,,108
profile,Profile,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
pronto_id_sku,Pronto ID (SKU),varchar,text,,1,1,1,1,1,0,0,1,1,1,0,0,0,,
power_hp,Power HP,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
power_supply,Power Supply,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
power_output,Power Output,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
power_rating,Power Rating,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
rated_power,Rated Power,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
raised_height,Raised Height,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
rcd,RCD,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,0,1,0,0,0,1,0,0,0,0,0,,
rebate_depth,Rebate Depth,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
recomm_bolt_range,Recommended Bolt Range,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
reversible,Reversible,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,0,1,0,0,1,1,0,0,0,0,0,,
run_time,Run Time,varchar,text,,1,1,1,1,0,0,1,1,0,0,0,0,0,,
sensitivity,Sensitivity,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,113
shade,Shade,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,112
shank_diameter,Shank Diameter,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
shank_length,Shank Length,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
shipping_length,Shipping Length,varchar,text,,1,1,1,1,0,0,1,1,0,0,0,0,0,,109
shipping_width,Shipping Width,varchar,text,,1,1,1,1,0,0,1,1,0,0,0,0,0,,110
shipping_height,Shipping Height,varchar,text,,1,1,1,1,0,0,1,1,0,0,0,0,0,,111
sockets,Sockets,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
sprindle_thread,Spindle Thread,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
standard_lift,Standard Lift,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
starting_system,Starting System,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
stroke_length_mm,Stroke Length (mm),varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
strokes_per_m,Strokes Per Minute,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
suitability,Suitability,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
supply_plug,Supply Plug,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
tank_size,Tank Size,varchar,text,,1,1,0,1,0,0,1,1,0,0,0,0,0,,
test_load,Test Load,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
teeth_per_inch,Teeth per inch,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
thd_5,THD <5%,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,0,1,0,0,1,1,0,0,0,0,0,,
torque,Torque (NM),varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
traction_power,Traction Power,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
type,Type,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,1,1,1,1,0,1,0,1,0,,104
viewing_area_dim,Viewing Area Dimension,varchar,text,,1,1,0,1,0,0,0,1,0,0,0,0,0,,
voltage,Voltage,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
warranty,Warranty,varchar,text,,1,1,1,1,1,1,1,1,0,0,0,1,0,,103
water_flow_rate,Water flow rate,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
wet_dry,Wet / Dry,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
wheel_type,Wheel Type,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,
wheels,Wheels,int,select,Magento\Eav\Model\Entity\Attribute\Source\Boolean,1,1,0,1,0,0,1,1,0,0,0,0,0,,
wire_feeder,Wire Feeder,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,0,1,0,0,1,1,0,0,0,0,0,,117
working_range,Working range,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
working_pressure,Working Pressure,varchar,select,Magento\Eav\Model\Entity\Attribute\Source\Table,1,1,1,1,0,0,1,1,0,0,0,0,0,,
