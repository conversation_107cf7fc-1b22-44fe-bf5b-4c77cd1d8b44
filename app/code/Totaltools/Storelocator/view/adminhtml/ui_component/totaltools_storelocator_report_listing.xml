<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">
                totaltools_storelocator_report_listing.totaltools_storelocator_report_listing_data_source
            </item>
            <item name="deps" xsi:type="string">
                totaltools_storelocator_report_listing.totaltools_storelocator_report_listing_data_source
            </item>
        </item>
        <item name="spinner" xsi:type="string">totaltools_storelocator_report_columns</item>
    </argument>
    <dataSource name="totaltools_storelocator_report_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">
                Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider
            </argument>
            <argument name="name" xsi:type="string">totaltools_storelocator_report_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">entity_id</argument>
            <argument name="requestFieldName" xsi:type="string">entity_id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <paging name="listing_paging"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                </item>
            </argument>
        </filters>
    </listingToolbar>
    <columns name="totaltools_storelocator_report_columns" sortOrder="10">
        <column name="entity_id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sorting" xsi:type="string">desc</item>
                    <item name="filter" xsi:type="string">textRange</item>
                    <item name="label" xsi:type="string" translate="true">ID</item>
                    <item name="resizeDefaultWidth" xsi:type="string">25</item>
                </item>
            </argument>
        </column>
        <column name="filename" sortOrder="20">
            <settings>
                <filter>text</filter>
                <label translate="true">Filename</label>
            </settings>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date" sortOrder="30">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Created</label>
            </settings>
        </column>
        <column name="updated_at" class="Magento\Ui\Component\Listing\Columns\Date"
                component="Magento_Ui/js/grid/columns/date" sortOrder="40">
            <settings>
                <filter>dateRange</filter>
                <dataType>date</dataType>
                <label translate="true">Updated</label>
            </settings>
        </column>
        <column name="status" sortOrder="50">
            <settings>
                <filter>text</filter>
                <label translate="true">Status</label>
            </settings>
        </column>
        <column name="comment" sortOrder="60">
            <settings>
                <filter>text</filter>
                <label translate="true">Details</label>
            </settings>
        </column>
    </columns>
</listing>
