<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing_data_source</item>
            <item name="deps" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">totaltoolsadmin_zone_listing_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="add" xsi:type="array">
                <item name="name" xsi:type="string">add</item>
                <item name="label" xsi:type="string" translate="true">Add New Zone</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/new</item>
            </item>
            <item name="export" xsi:type="array">
                <item name="name" xsi:type="string">export</item>
                <item name="label" xsi:type="string" translate="true">Export</item>
                <item name="class" xsi:type="string">secondary</item>
                <item name="url" xsi:type="string">*/*/export</item>
                <item name="sortOrder" xsi:type="number">30</item>
            </item>
        </item>
    </argument>

    <dataSource name="totaltoolsadmin_zone_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="class" xsi:type="string">ZoneGridDataProvider</argument>
            <argument name="name" xsi:type="string">totaltoolsadmin_zone_listing_data_source</argument>
            <argument name="primaryFieldName" xsi:type="string">zone_id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">zone_id</item>
                    </item>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>

    <container name="zone_listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="template" xsi:type="string">ui/grid/toolbar</item>
            </item>
        </argument>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="columnsProvider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing_columns</item>
                    <item name="dataScope" xsi:type="string">filters</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.filters</item>
                    </item>
                    <item name="childDefaults" xsi:type="array">
                        <item name="provider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.listing_top.listing_filters</item>
                        <item name="imports" xsi:type="array">
                            <item name="visible" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing_columns.${ $.index }:visible</item>
                        </item>
                    </item>
                </item>
                <item name="observers" xsi:type="array">
                    <item name="column" xsi:type="string">column</item>
                </item>
            </argument>           
        </filters>
        <paging name="listing_paging">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.listing_top.bookmarks</item>
                        <item name="namespace" xsi:type="string">current.paging</item>
                    </item>
                    <item name="sizesConfig" xsi:type="array">
                        <item name="value" xsi:type="number">200</item>
                    </item>
                    <item name="selectProvider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing_columns.ids</item>
                    <item name="displayArea" xsi:type="string">bottom</item>
                </item>
            </argument>
        </paging>
    </container>

    <columns name="totaltoolsadmin_zone_listing_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="storageConfig" xsi:type="array">
                    <item name="provider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing_top.bookmarks</item>
                    <item name="namespace" xsi:type="string">current</item>
                </item>
                <item name="childDefaults" xsi:type="array">
                    <item name="fieldAction" xsi:type="array">
                        <item name="provider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing_columns.actions</item>
                        <item name="target" xsi:type="string">applyAction</item>
                        <item name="params" xsi:type="array">
                            <item name="0" xsi:type="string">edit</item>
                            <item name="1" xsi:type="string">${ $.$data.rowIndex }</item>
                        </item>
                    </item>
                    <item name="controlVisibility" xsi:type="boolean">true</item>
                    <item name="storageConfig" xsi:type="array">
                        <item name="provider" xsi:type="string">totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing.totaltoolsadmin_zone_listing_top.bookmarks</item>
                        <item name="root" xsi:type="string">columns.${ $.index }</item>
                        <item name="namespace" xsi:type="string">current.${ $.storageConfig.root}</item>
                    </item>
                </item>
            </item>
        </argument>
        <column name="name">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Name</item>
                </item>
            </argument>
        </column>
        <column name="ranges">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">false</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Ranges(s)</item>
                </item>
            </argument>
        </column>
        <column name="country_code">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">text</item>
                    <item name="editor" xsi:type="array">
                        <item name="editorType" xsi:type="string">text</item>
                        <item name="validation" xsi:type="array">
                            <item name="required-entry" xsi:type="boolean">true</item>
                        </item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Country</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="actions" class="Totaltools\Storelocator\Ui\Component\Listing\Column\ZoneActions">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">107</item>
                    <item name="indexField" xsi:type="string">zone_id</item>
                </item>
            </argument>
        </actionsColumn>
    </columns>
</listing>