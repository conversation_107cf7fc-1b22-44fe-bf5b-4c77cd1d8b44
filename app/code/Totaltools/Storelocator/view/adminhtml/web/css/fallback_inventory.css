
.store-fallback-container {
    margin-bottom: 20px;
    border: 1px solid #dcdcdc;
    padding: 15px;
    background-color: #f7f7f7;
    border-radius: 4px;
}
.store-fallback-container label {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}


.store-select-container select {
    width: 80%;
    padding: 0px 10px;
    margin-right: 10px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
}

.store-select-container button {
    padding: 8px 16px;
    background-color: #3a7d34;
    color: #fff;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.store-select-container button:hover {
    background-color: #4caf50;
}


.sortable-list {
    list-style: none;
    padding: 0;
    margin: 10px 0;
    border: 1px solid #dcdcdc;
    min-height: 50px;
    background-color: #fff;
    border-radius: 4px;
}

.sortable-list li {
    padding: 10px;
    margin: 5px 0;
    background-color: #f1f1f1;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: move;
}

.sortable-list li:hover {
    background-color: #e9e9e9;
}

.sortable-list .remove-store {
    background-color: #e53935;
    color: #fff;
    border: none;
    padding: 5px 8px;
    border-radius: 4px;
    cursor: pointer;
}

.sortable-list .remove-store:hover {
    background-color: #d32f2f;
}

.super-fallback-store-container {
    margin-top: 20px;
    margin-bottom: 20px;
}

.super-fallback-store-container select {
    width: 100%;
    padding: 8px;
    font-size: 14px;
    border: 1px solid #ccc;
    border-radius: 4px;
    background-color: #fff;
}

.super-fallback-store-container label {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}


.inventory-section {
    margin-top: 20px;
    margin-bottom: 20px;
}

.inventory-section textarea {
    width: 100%;
    height: 100px;
    padding: 10px;
    font-size: 14px;
    background-color: #f9f9f9;
    border: 1px solid #ccc;
    border-radius: 4px;
    resize: none;
}

.inventory-section label {
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab {
    display: flex;
    flex-wrap: wrap;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-stores {
    width: 50%;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-stores .sortable-stores-list-heading {
    font-size: 18px;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-stores .store-select-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

#sortable-stores-list.sortable-list li.store-fallback-bold,
#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide #stores_distance .data-grid td.store-fallback-bold {
    font-weight: 700;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide {
    width: 50%;
    padding-left: 25px;
    box-sizing: border-box;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide .fallback-guide-multiselect {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide .fallback-guide-multiselect > .admin__field {
    width: 26%;   
    margin-top: 0;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide .fallback-guide-multiselect > .admin__field:first-child {
    width: 44%;   
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide .label.admin__field-label {
    width: 25%;
    padding-left: 0;
    text-align: left;
    padding-right: 15px;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide .admin__field-control select {
    width: 100%;
    min-width: unset;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide #stores_distance,
#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide #stores_distance .data-grid {
    width: 100%;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide #stores_distance .data-grid th {
    text-align: center;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide #stores_distance .data-grid th,
#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide #stores_distance .data-grid td {
    padding: 10px;
}

#store_tabs_fallback_inventory_section_content .store-fallback-tab .fallback-guide #stores_distance .data-grid tr:nth-child(even) td {
    background-color: #f5f5f5;
}