require(['jquery', 'jquery/ui'], function($) {
    $(document).ready(function () {

        $('#store_guide_submit_button').on('click', function() {
            var states = $('#store_state').val();
            var locations = $('#locations').val();
            var structures = $('#structures').val();
            var storelocator_id = $('#current_storelocator_id').val();

            var ajaxUrl = $(this).data("url");
            $('.store-guide-table-loading').show();
            $.ajax({
                url: ajaxUrl,
                type: 'POST',
                data: {
                    states: states,
                    locations: locations,
                    structures: structures,
                    storelocator_id: storelocator_id,
                    isAjax: true
                },
                success: function(response) {
                    $('#stores_distance').html(response.htmlTable);
                    $('.store-guide-table-loading').hide();
                },
                error: function(xhr, status, error) {
                    console.log(error); 
                    $('.store-guide-table-loading').hide();
                }
            });
        });

        function updateFallbackStoresOrder() {
            let fallbackStoresOrder = [];
            $("#sortable-stores-list li").each(function() {
                fallbackStoresOrder.push($(this).data("id"));
            });
            $("#fallback-stores-order").val(fallbackStoresOrder.join(','));
        }

        
        $("#sortable-stores-list").sortable({
            placeholder: "sortable-placeholder",
            update: function (event, ui) {
                updateFallbackStoresOrder();
                console.log("New order:", $(this).sortable("toArray", { attribute: "data-id" }));
            }
        });

        
        $("#add-store-btn").on("click", function (event) {
            event.preventDefault();
            event.stopPropagation();
            let storeId = $("#store-dropdown").val();
            let storeLabel = $("#store-dropdown option:selected").text();
            if (storeId && storeLabel) {
                if ($("#sortable-stores-list li[data-id='" + storeId + "']").length === 0) {
                    let newStore = "<li class='store-fallback-bold ui-sortable-handle' data-id='" + storeId + "'>" + storeLabel + "<button class='remove-store'>×</button></li>";
                    $("#sortable-stores-list").append(newStore);
                    updateFallbackStoresOrder();
                }
            }
        });

        
        $(document).on("click", ".remove-store", function () {
            $(this).closest("li").remove();
            updateFallbackStoresOrder();
        });
        updateFallbackStoresOrder();
    });
});