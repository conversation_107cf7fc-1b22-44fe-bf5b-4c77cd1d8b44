<?php $current_storelocator_id = $block->getRegistryModel() ? $block->getRegistryModel()->getId() : 0; ?>
<div style="display: none;" class="store-guide-table-loading loading-mask">
    <div class="popup popup-loading">
        <div class="popup-inner">
            <img alt="<?php echo __('Loading...'); ?>" src="<?php echo $block->getViewFileUrl('Magestore_Storelocator::images/loader.gif'); ?>" />
            <?php echo __('Please wait...'); ?>
        </div>
    </div>
</div>
<div class="store-fallback-tab">
    <div class="fallback-stores">
        <div class="store-fallback-container">
            <label for="sortable-stores-list" class="sortable-stores-list-heading">
                <?php echo __('Fallback Stores'); ?>
            </label>
            <p>Drag and drop the items in the list below to change the priority of the fallback stores. You can also use the form on the right side to retrieve the nearest stores of a specific type by distance.</p>
            <ul id="sortable-stores-list" class="sortable-list">
                <?php foreach ($block->getFallbackStores() as $store): ?>
                    <li class="store-fallback-bold" data-id="<?php echo $store['value']; ?>">
                        <?php echo $store['label']; ?>
                            <button class="remove-store">×</button>
                    </li>
                    <?php endforeach; ?>
            </ul>
        </div>
        <div class="store-fallback-container">
            <div class="store-select-container">
                <select id="store-dropdown">
                    <?php foreach ($block->getStores() as $store): ?>
                        <option value="<?php echo $store['value']; ?>">
                            <?php echo $store['label']; ?>
                        </option>
                        <?php endforeach; ?>
                </select>
                <button id="add-store-btn">
                    <?php echo __('Add to fallback stores'); ?>
                </button>
            </div>
        </div>
        <input type="hidden" id="fallback-stores-order" name="fallback_stores_order" value="" />
        <div class="super-fallback-store-container">
            <label for="super_store_id">
                <?php echo __('Super Fallback Store'); ?>
            </label>
            <select name="super_store_id" id="super_store_id">
                <?php foreach ($block->getStores() as $store): ?>
                    <option value="<?php echo $store['value']; ?>" <?php if ($block->getSelectedSuperStoreId() == $store['value']): ?> selected="selected"
                        <?php endif; ?>
                            >
                            <?php echo $store['label']; ?>
                    </option>
                    <?php endforeach; ?>
            </select>
        </div>
    </div>
    <div class="fallback-guide">
    <?php if ($current_storelocator_id) : ?>
        <input type="hidden" id="current_storelocator_id" value="<?=$current_storelocator_id?>" />
        <div class="fallback-guide-multiselect">
            <div class="admin__field field field-state">
                <div class="admin__field-control control">
                    <select id="store_state" name="states[]" title="State" size="3" class="select multiselect admin__control-multiselect" multiple="multiple">
                        <?php foreach ($block->getStates() as $state): ?>
                            <option value="<?= $state['name'] ?>"><?= $state['name'] ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>

            <div class="admin__field field field-location">
                <div class="admin__field-control control">
                    <select id="locations" name="locations[]" title="Location" size="3" class="select multiselect admin__control-multiselect" multiple="multiple">
                        <option value="capital">Capital</option>
                        <option value="metro">Metro</option>
                        <option value="remote">Remote</option>
                    </select>
                </div>
            </div>

            <div class="admin__field field field-structure">
                <div class="admin__field-control control">
                    <select id="structures" name="structures[]" title="Structure" size="3" class="select multiselect admin__control-multiselect" multiple="multiple">
                        <option value="franchise">Franchise</option>
                        <option value="jv">JV</option>
                    </select>
                </div>
            </div>
        </div>                
        <div class="admin__field field field-submit_button">
            <div class="admin__field-control control">
                <div class="admin__field store-guide-submit-button">
                    <input id="store_guide_submit_button" name="store_guide_submit_button" value="Get Stores" class="action-default scalable action-secondary" type="button" data-url="<?php echo $block->getAjaxSubmitUrl() ?>">
                </div>
            </div>
        </div>

        <div id="stores_distance" class="control-value admin__field-value">

        </div>
    <?php endif; ?>
    </div>
</div>
<div class="inventory-section">
    <label for="inventory">
        <?php echo __('Inventory'); ?>
    </label>
    <textarea id="inventory" name="inventory" readonly>
        <?php echo $block->getInventory(); ?>
    </textarea>
</div>