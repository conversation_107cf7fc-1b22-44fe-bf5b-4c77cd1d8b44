<div class="mgs-search-box" data-default-raidus="">
    <div class="search-content col-xs-12">
        <div class="container-search">
            <!--      Search by distance tab      -->
            <div
                class="inline col-md-7 col-xs-12 search-by-distance"
                id="form-search-distance"
            >
                <div class="form-inline col-full padding-right padding-left">
                    <label
                        class="col-md-5 col-sm-4 col-xs-12"
                        for="input-search-distance"
                        >Find your nearest Total Tools</label
                    >
                    <div
                        class="col-md-7 col-sm-8 col-xs-12 input-group"
                        style="position: relative"
                        data-bind="afterRender: bindListeners"
                    >
                        <input
                            type="text"
                            name="input-search-distance"
                            class="input-search-distance input-sm"
                            placeholder="Enter a suburb or postcode"
                            id="input-search-distance"
                            data-bind="textInput: postCode, css: { 'is-searching': isSearching }"
                        />
                        <if args="isSearching">
                            <img class="search-loader" data-bind="attr: { src: require.toUrl('') + 'images/loader-1.gif' }" alt="Searching..." />
                        </if>
                        <div class="search-result">
                            <ul
                                class="search-result-list"
                                each="data: suburbs, as: 'suburb'"
                            >
                                <li
                                    data-bind="click: $parent.fetchStores.bind($parent)"
                                >
                                    <a href="#">
                                        <span
                                            html="locality.toLowerCase() + ', ' + postcode + ', <span>' + state + '</span>'"
                                        ></span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>

                    <div
                        class="form-group col-md-4 col-xs-12 slider-range-min hide"
                    >
                        <div
                            id="slider-range-min"
                            class="slider-range-bar slider-range"
                        ></div>
                        <span class="show-unit slider-range-amount">
                            100 Km
                        </span>
                    </div>
                </div>
            </div>

            <div class="col-md-1 col-xs-12 or">- OR -</div>
            <div
                class="col-md-4 col-xs-12 search-by-area"
                id="form-search-area"
            >
                <ul id="au_states" each="data: regions, as: 'region'">
                    <li>
                        <a
                            class="state"
                            text="code"
                            href="#"
                            data-bind="attr: {title: name}, click: $parent.navigateState.bind($parent)"
                        ></a>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>
