<div class="list-store-box col-sm-4 col-xs-12 pull-left">
    <div class="page-title">
        <h2 class="title">
            <span
                class="glyphicon glyphicon-align-justify"
                aria-hidden="true"
            ></span>
            <span>Store list</span>
            <span
                class="number-store pull-right"
                text="stores().length + ' Stores'">
            </span>
        </h2>
    </div>
    <div class="list-store col-xs-12">
        <ul
            class="list-store-container disable-ul"
            each="data: stores, as: 'store'"
        >
            <li
                class="show-tag-li store-item"
                data-bind="attr: {title: store_name}, css: {'store-active' : $parent.selectedStore() == store}"
            >
                <div class="">
                    <div class="col-sm-1 col-xs-1 tag-store">
                        <span></span>
                    </div>
                    <div class="col-sm-11 col-xs-11 tag-content">
                        <div data-bind="click: $parent.navigateStore.bind($parent)">
                            <h4 text="store_name"></h4>
                            <p
                                class="address-store"
                                html="address + '<br />' + city + ', ' + zipcode + ', ' + state"
                            ></p>
                            <p class="phone-store">
                                Phone:
                                <a
                                    data-bind="text: phone, attr: { href: 'tel:' + phone }, click: $parent.gtm.handleStorePhone.bind($parent)"
                                ></a>
                            </p>
                        </div>
                        <a
                            target="_self"
                            class="btn-link2"
                            data-bind="attr: { href: '/' + rewrite_request_path }"
                            ><span>View store details</span>
                        </a>
                    </div>
                </div>
            </li>
        </ul>
        <div if="!stores().length" class="no-result">
            <p class="text-center" translate="'No stores found!'"></p>
        </div>
    </div>
    <div
        id="pagination-list"
        class="pagination-list pagination-wrapper text-center col-full"
        style="display: none"
    ></div>
</div>
