define(['jquery', 'mage/url'], function ($, url) {
    'use strict';

    return {
        xhr: null,
        sku: $('.product-info-stock-sku .sku .value').html(),

        /**
         * @returns {String}
         */
        _getChangestoreUrl: function () {
            return url.build('totaltools_storelocator/checkout/changestore');
        },

        /**
         * @returns {String}
         */
        _getStockcheckUrl: function () {
            return url.build('totaltools_storelocator/checkout/stockcheck');
        },

        /**
         * @param {ko.observableArray} stores
         * @param {String} postcode
         * @param {Boolean} hasLoader
         * @returns {Promise}
         */
        fetchStores: async function (stores, postcode, hasLoader, limit) {
            return this.fetchAvailableStores(postcode, hasLoader, limit)
                .then(function (data) {
                    $.each(data?.stores || [], (idx, store) =>
                        stores.push(store)
                    );
                })
                .catch(function (err) {
                    console.error(err);
                });
        },

        /**
         * @param {String} postcode
         * @param {Boolean} showLoader
         * @returns {jQuery.ajax}
         */
        fetchAvailableStores: async function (postcode, showLoader, limit) {
            var that = this,
                stockCheckUrl = that._getStockcheckUrl();

            if (that.xhr && that.xhr.readystate != 4) {
                that.xhr.abort();
            }

            var configurableSku = $.trim(
                $('.product-info-stock-sku .configurable-sku .value').html()
            );

            if (configurableSku) {
                that.sku = configurableSku;
            }

            return (that.xhr = $.ajax({
                showLoader: showLoader,
                url: stockCheckUrl,
                data: {
                    postcode: postcode,
                    sku: that.sku,
                    limit: limit
                },
                type: 'POST',
                dataType: 'json',
            }));
        },

        /**
         * @param {Object} store
         * @returns {jQuery.ajax}
         */
        changeStore: function (store) {
            var storeChangeUrl = this._getChangestoreUrl();

            return $.ajax({
                type: 'POST',
                dataType: 'text',
                showLoader: true,
                url: storeChangeUrl,
                data: {
                    store_id: store.storelocator_id,
                },
            });
        },
    };
});
