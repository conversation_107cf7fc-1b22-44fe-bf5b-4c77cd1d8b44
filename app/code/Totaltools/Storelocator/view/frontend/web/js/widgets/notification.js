/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */
define(['jquery', 'mage/template', 'jquery-ui-modules/widget'], function ($, mageTemplate) {
    'use strict';
    
    $.widget('totaltools.storenotification', {
        options: {
            templateId: '#selected-store-notification-template',
            notificationsWrapper: null,
            canAutoHide: true,
            hasCloseBtn: true,
            position: 'top-right',
            holdup: 300000,
            fadeTime: 5000,
            data: null,
            title: 'Great choice!',
            btnText: 'View Store Details',
            changeStoreBtnText: 'Change Store',
        },
        notification: null,

        /**
         * @inheritdoc
         */
        _create: function () {
            this.init();
        },

        /**
         * @public
         */
        init: function () {
            this._createNotification()
                ._renderNotification();
        },

        /**
         * @private
         */
        _createNotification: function () {
            var self = this;
            var opts = this.options;

            if ('object' === typeof opts.data) {
                let notificationTemplate = mageTemplate(
                    $(opts.templateId).html()
                );

                this.notification = $(
                    notificationTemplate({
                        data: opts.data,
                        opts: opts,
                    })
                ).delegate('.close', 'click', function (ev) {
                    self._destroy();
                });
            }

            return this;
        },

        /**
         * @private
         */
        _renderNotification: function () {
            var opts = this.options;

            if (this.notification && $(opts?.notificationsWrapper).length) {
                $(opts.notificationsWrapper).addClass(opts.position);

                this.notification
                    .hide()
                    .appendTo(opts.notificationsWrapper)
                    .fadeIn(opts.fadeTime);

                this._autoHideNotification();
            }

            return this;
        },

        /**
         * @private
         */
        _autoHideNotification: function () {
            var opts = this.options;

            if (opts.canAutoHide) {
                setTimeout(this._destroy.bind(this), opts.holdup);
            }
        },

        /**
         * @private
         */
        _destroy: function () {
            if (this.notification) {
                this.notification.fadeOut(
                    this.options.fadeTime,
                    function () {
                        this.notification.remove();
                        this.notification = null;
                    }.bind(this)
                );
            }
        },
    });

    return $.totaltools.storenotification;
});
