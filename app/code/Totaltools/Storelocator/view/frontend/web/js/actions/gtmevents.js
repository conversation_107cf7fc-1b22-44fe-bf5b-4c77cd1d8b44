/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> Iqbal <<EMAIL>>
 */

define(['underscore'], function (_) {
    'use strict';

    var dataLayer = window.dataLayer || [];

    return {
        /**
         * @param {Object} data
         * @param {jQuery.event} ev
         */
        handleStorePhone: function (data, ev) {
            if (data['store_name']) {
                dataLayer.push({
                    event: 'calledStore',
                    store: data.store_name,
                });
            }

            if (data['phone']) {
                window.location.href = 'tel:' + data.phone;
            }
        },

        /**
         * @param {Object} data
         * @param {jQuery.event} ev
         */
        handleStoreDirections: function (data, ev) {
            if (data['store']) {
                dataLayer.push({
                    event: 'directionStore',
                    store: data.store,
                });
            }
        },
    };
});
