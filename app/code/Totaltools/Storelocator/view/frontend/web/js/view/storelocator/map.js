/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> Iqbal <<EMAIL>>
 */

define([
    'jquery',
    'ko',
    'underscore',
    'uiComponent',
    'mage/url',
    'mage/template',
    'totaltools/map-loader',
    '../../model/locator-store',
    'Magento_Customer/js/customer-data'
], function (
    $,
    ko,
    _,
    Component,
    urlBuilder,
    mageTemplate,
    mapLoader,
    locatorStore,
    customerData
) {
    'use strict';

    var fulfillmentData = customerData.get('fulfilment-data');
    var isStorelocatorIndex = ['/storelocator/index/index/', '/storelocator/'].includes(location.pathname);

    return Component.extend({
        defaults: {
            template: 'Totaltools_Storelocator/view/storelocator/map',
            links: {
                isLoading: '${ $.provider }:isLoading',
            },
            tracks: {
                isLoading: true,
            },
        },
        markers: {},
        scriptLoaded: false,

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super()._loadMapScript()._registerSubscriptions();

            return this;
        },

        /**
         * Loads LeafletJs map script from remote source.
         */
        _loadMapScript: function () {
            var self = this;

            try {
                mapLoader
                    .done(function () {
                        self.scriptLoaded = true;
                    })
                    .fail(function (err) {
                        console.error(err);
                    });
            } catch (e) {
                console.trace();
            }

            return this;
        },

        /**
         * Register all subscribers
         */
        _registerSubscriptions: function () {
            locatorStore.stores.subscribe(this.storesSubscription.bind(this));
            locatorStore.store.subscribe(this.storeSubscription.bind(this));
            locatorStore.state.subscribe(this.stateSubscription.bind(this));

            return this;
        },

        /**
         * @param {Array} data
         */
        storesSubscription: function (data) {
            var self = this,
                bounds = [];

            if (!_.isEmpty(data)) {
                _.each(self.markers, function (marker) {
                    self.map.removeLayer(marker);
                });

                self.markers = {};

                _.each(data, function (store) {
                    let coord = [
                            parseFloat(store.latitude),
                            parseFloat(store.longitude),
                        ],
                        marker = self._createMarker(coord, store);

                    self.markers[store.storelocator_id] = marker;
                    bounds.push(coord);
                });

                self.map.fitBounds(bounds);
            }
        },

        /**
         * @param {Object} newStore
         */
        storeSubscription: function (newStore) {
            var self = this;

            if (!_.isEmpty(newStore) && self.map) {
                let newCoords = [newStore.latitude, newStore.longitude];

                self.map.fitBounds([newCoords]);
                self.markers[newStore.storelocator_id] &&
                    self.markers[newStore.storelocator_id].openPopup();
            }
        },

        /**
         * @param {Object} newState
         */
        stateSubscription: function (newState) {
            var self = this;

            if (!_.isEmpty(newState)) {
                self.loadStores({
                    state: newState.name,
                });
            }
        },

        /**
         * Initialize map with default params.
         *
         * @param {HTMLElement} elem
         */
        initMap: function (elem) {
            if (!this.scriptLoaded) {
                setTimeout(this.initMap.bind(this, elem), 200);
                return;
            }

            if (this.map) {
                console.warn('Map object already initialized!');
                return;
            }

            this.isLoading = true;

            var opts = this.options,
                coords = opts.map.coords,
                mapService = opts.map.service_url + opts.map.access_token,
                params = { curPage: 1 };

            this.map = L.map(elem, { center: coords, zoom: opts.map.zoom });
            this.mapIcon = L.icon(opts.icon);
            this.tileLayer = L.tileLayer(mapService, opts.tile).addTo(this.map);
            this.popupTemplate = mageTemplate($('#popup-store-template').html());

            if (this.options['state']) {
                params['state'] = this.options.state;
            }

            if (this.options['stores'] && this.options['stores'].length) {
                locatorStore.setStores(this.options.stores);
            } else {
                this.loadStores(params);
            }

            this.isLoading = false;

            return this;
        },

        /**
         * Create map marker with giveen co-ordinates.
         *
         * @param {Array} coord
         * @param {Object} data
         * @returns {L.marker}
         */
        _createMarker: function (coord, data) {
            var template = this.popupTemplate({ data: data });

            return L.marker(coord, { icon: this.mapIcon })
                .addTo(this.map)
                .bindPopup(template);
        },

        /**
         * @param {Object} params
         * @param {Function} callback
         */
        loadStores: function (params, callback) {
            var self = this;

            let userLocation = fulfillmentData()?.location;
            if (
                isStorelocatorIndex &&
                !params.state &&
                userLocation?.postcode &&
                userLocation?.country_id == "AU"
            ) {
                return;
            }

            self.isLoading = true;

            $.ajax({
                url: urlBuilder.build(this.loadStoreUrl),
                type: 'GET',
                dataType: 'json',
                data: params,
            })
                .done(function (res) {
                    locatorStore.setStores(res.storesjson || []);
                    'function' === typeof callback && callback(res, params);
                    self.isLoading = false;
                })
                .fail(function (err) {
                    self.isLoading = false;
                    console.error(err);
                });
        },
    });
});
