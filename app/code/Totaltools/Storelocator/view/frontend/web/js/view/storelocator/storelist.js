/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> Iqbal <<EMAIL>>
 */

define([
    'jquery',
    'ko',
    'underscore',
    'uiComponent',
    '../../model/locator-store',
    '../../actions/gtmevents'
], function ($, ko, _, Component, locatorStore, gtmEvents) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Storelocator/view/storelocator/storelist',
            links: {
                isLoading: '${ $.provider }:isLoading',
            },
            tracks: {
                isLoading: true,
            },
        },
        stores: locatorStore.getStores(),
        selectedStore: locatorStore.getStore(),
        gtm: gtmEvents,

        /**
         * @param {Object} store
         * @param {jQuery.event} ev
         */
        navigateStore: function (store, ev) {
            if (!_.isEmpty(store)) {
                locatorStore.setStore(store);
                this.selectedStore(store);

                if ($(window).width() <= 768) {
                    $('html, body').animate(
                        { scrollTop: ($('.list-store-box').offset().top + 100) || 200 },
                        'slow'
                    );
                }
            }
        },
    });
});
