/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2024 Totaltools <https://totaltools.com.au>
 * <AUTHOR> Iqbal <<EMAIL>>
 */

define([
    'jquery',
    'Totaltools_Storelocator/js/checkout/stockcheck',
    'Magento_Customer/js/customer-data'
], function ($, stockCheckAction, customerData) {
    'use strict';

    return function (store, element) {
        // Show the button when script initializes
        $(element).fadeIn();

        // Add click event listener to set the store
        $(element).on('click', function (ev) {
            ev.preventDefault();

            $('body').trigger('processStart');

            stockCheckAction.changeStore(store)
                .done(function (data) {
                    customerData.reload(['fulfilment-data']);
                    $('body').trigger('processStop');
                })
                .fail(function() {
                    $('body').trigger('processStop');
                });
        });
    };
});
