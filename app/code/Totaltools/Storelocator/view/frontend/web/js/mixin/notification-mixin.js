/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */
define([
    'jquery',
    'mage/url',
    'Magento_Customer/js/customer-data'
], function ($, url, customerData) {
    'use strict';

    return function (widget) {
        $.widget(
            'totaltools.storenotification',
            $.totaltools.storenotification,
            {
                /**
                 * @inheritdoc
                 */
                _create: function () {
                    var customer = customerData.get('customer');

                    if (!customer().firstname) {
                        this.options.btnUrl = url.build('checkout');
                    }

                    this._super();
                },
            }
        );

        return $.totaltools.storenotification;
    };
});
