define([
    'jquery',
    'underscore',
    'Magento_Ui/js/form/form',
    'ko',
    'mage/url',
], function ($, _, Component, ko, url) {
    'use strict';

    return {
        xhr: null,

        _getChangestoreUrl: function () {
            return url.build('totaltools_storelocator/checkout/changestore');
        },

        _getStockcheckUrl: function () {
            return url.build('totaltools_storelocator/checkout/stockcheck/');
        },

        fetchStores: function (stores, postcode, showLoader) {
            var self = this;
            $.when(self.fetchAvailableStores(postcode, showLoader)).done(
                function (result) {
                    if (typeof result.stores !== 'undefined') {
                        $.each(result.stores, function (index, store) {
                            stores.push(store);
                        });
                    }
                }
            );
        },

        fetchAvailableStores: function (postcode, showLoader) {
            var _self = this;

            if (_self.xhr && _self.xhr.readystate != 4) {
                _self.xhr.abort();
            }

            return (_self.xhr = $.ajax({
                showLoader: showLoader,
                url: _self._getStockcheckUrl(),
                data: {
                    postcode: postcode,
                },
                type: 'POST',
                dataType: 'json',
            }));
        },

        changeStore: function (store) {
            var _self = this;

            return $.ajax({
                type: 'POST',
                dataType: 'text',
                url: _self._getChangestoreUrl(),
                data: {
                    store_id: store.storelocator_id,
                },
            });
        },
    };
});
