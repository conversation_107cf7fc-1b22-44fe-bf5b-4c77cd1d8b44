define(
    [
        'j<PERSON>y',
        'mage/url',
        'ko'
    ],
    function ($, url, ko) {
        'use strict';

        var store = ko.observable(null);
        var xhr = null;

        return {
            /**
             * Explicitly subscribe to location
             * in dependant components to get any changes
             */
            store: store,

            getStore: function () {
                return this.store;
            },

            setStore: function (data) {
                return this.store(data);
            },

            fetch: function (postcode) {
                if (xhr && xhr.readystate != 4) {
                    xhr.abort();
                }

                if(postcode) {
                    xhr = $.ajax({
                        url: url.build('rest/V1/storelocator/stores/' + postcode),
                        type: "GET",
                        dataType: 'json'
                    }).done(function (data) {
                        store(JSON.parse(data));
                    });
                }
            }
        };
    }
);
