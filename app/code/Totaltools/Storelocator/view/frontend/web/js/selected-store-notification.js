/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */
define(['jquery', 'underscore', 'storenotification'], function ($, _, storenotification) {
    'use strict';

    var storeNotifications = {
        options: {
            notificationsWrapper: '#add-cart-notifications',
        },
// selected-store-notification
        /**
         * @public
         */
        init: function () {
            if (arguments[0] && 'object' === typeof arguments[0]) {
                this.options = $.extend(this.options, arguments[0]);
            }

            this._bindEventListener();
        },

        /**
         * Adds event listener for the custom AJAX event `ajax:nearest`.
         *
         * @private
         */
        _bindEventListener: function () {
            $(document).on(
                'ajax:nearest',
                function (ev, data) {
                    if (Object.keys(data.store).length > 0) {
                        this._createWrapper()
                            ._createStoreNotification(data);
                    }
                }.bind(this)
            );
               // Event listener for change store button
            $(document).on('click', '.change-store-btn', function () {
                this._openChangeStoreModal(); // Call method to open the modal
            }.bind(this));
        },

        /**
         * @private
         * @param {Object} storeData
         */
        _createStoreNotification: function (storeData) {
            var opts = this.options;
            $('<div/>').storenotification({
                data: storeData,
                ...opts,
            });

            return this;
        },

        _createWrapper: function () {
            var wrapper = this.options.notificationsWrapper;
            if (!$(wrapper).length) {
                $('body').append('<div id="' + wrapper.replace('#', '') + '"/>');
            }

            return this;
        },
        /**
         * Opens the Change Store modal when the Change Store button is clicked.
         * 
         * @private
         */
        _openChangeStoreModal: function () {
            $(document).trigger('openChangeStoreModal');
        },
    };

    return storeNotifications;
});
