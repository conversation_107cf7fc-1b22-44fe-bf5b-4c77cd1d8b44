/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> Iqbal <<EMAIL>>
 */

define([
    'jquery',
    'ko',
    'underscore',
    'uiComponent',
    'mage/url',
    '../../model/locator-store',
    'Totaltools_Postcodes/js/fetch-suburbs',
    'Magento_Customer/js/customer-data'
], function ($, ko, _, Component, urlBuilder, locatorStore, fetchSuburbs, customerData) {
    'use strict';

    var fulfillmentData = customerData.get('fulfilment-data');
    var isStorelocatorIndex = ['/storelocator/index/index/', '/storelocator/'].includes(location.pathname);

    return Component.extend({
        defaults: {
            template: 'Totaltools_Storelocator/view/storelocator/searchbar',
            links: {
                isLoading: '${ $.provider }:isLoading',
            },
            tracks: {
                isLoading: true,
            },
        },
        isSearching: ko.observable(false),
        postCode: ko.observable(''),
        suburbs: ko.observableArray([]),
        regions: ko.observableArray([]),
        disableSearch: ko.observable(true),

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super()
                ._populateRegions();

            this.postCode.subscribe(function(val) {
                this.fetchSuburbs(val);
                this.disableSearch(!(val && val.length > 2));
            }.bind(this));

            // Fetch stores on page load if data is available
            var data = fulfillmentData();
            if (
                isStorelocatorIndex &&
                data.hasOwnProperty('location') &&
                data.location.postcode
            ) {
                this.loadStoresOnDataChange(data.location.postcode);
            }
           

            return this;
        },

        loadStoresOnDataChange: function (postcode) {
            this.isLoading = true;
            this.fetchAvailableStores(postcode)
                .done(function(res) {
                    var stores = [];
                    if (!_.isEmpty(res.stores)) {
                        stores = Object.keys(res.stores).map(function (key) {
                            return res.stores[key];
                        });
                    }
                    locatorStore.setStores(stores);
                    $('#input-search-distance').val(postcode);
                })
                .fail(function(err) {
                    console.error(err);
                })
                .always(function() {
                    this.isLoading = false;
                }.bind(this));
        },

        /**
         * @param {HTMLElement} element
         */
        bindListeners: function (element) {
            var self = this;

            document.addEventListener(
                'mousedown',
                function (e) {
                    if (element && !element.contains(e.target)) {
                        self.suburbs.removeAll();
                    }
                },
                false
            );

            return this;
        },

        /**
         * @param {String} searchInput
         */
        fetchSuburbs: function (searchInput) {
            var self = this;

            if ('function' === typeof searchInput) {
                searchInput = searchInput();
            }

            if (searchInput.length > 2) {
                self.isSearching(true);

                fetchSuburbs
                    .fetchByPostcode(searchInput)
                    .done(function (res) {
                        self.suburbs(res.items || []);
                        self.isSearching(false);
                    })
                    .fail(function (err) {
                        self.isSearching(false);
                        console.error(err);
                    });
            } else {
                self.suburbs.removeAll();
            }
        },

        /**
         * @param {Object} suburb
         * @param {jQuery.event} ev
         */
        fetchStores: function (suburb, ev) {
            ev.preventDefault();

            if (!suburb.postcode) {
                console.warn('Postcode is missing for given suburb.');
                return;
            }

            if (this.options['redirect']) {
                $('body').trigger('processStart');
                var stateUrl = urlBuilder.build(
                    'storelocator/index/index/suburb/'
                );
                window.location = stateUrl + suburb.postcode;

                return;
            }

            var self = this;

            this.isLoading = true;
            this.suburbs.removeAll();

            this.fetchAvailableStores(suburb.postcode)
                .done(function (res) {
                    var stores = [];

                    if (!_.isEmpty(res.stores)) {
                        stores = Object.keys(res.stores).map(function (key) {
                            return res.stores[key];
                        });
                    }

                    locatorStore.setStores(stores);
                    self.isLoading = false;
                })
                .fail(function (err) {
                    console.error(err);
                    self.isLoading = false;
                });
        },

        /**
         * Get regions and pouplate searchbar.
         */
        _populateRegions: function () {
            var self = this,
                regions = this.options.states;

            _.each(regions, function (region) {
                self.regions.push(region);
            });

            return this;
        },

        /**
         * @param {String} postcode
         * @returns {jQuery.ajax}
         */
        fetchAvailableStores: function (postcode) {
            if (this.xhr && this.xhr.readystate != 4) {
                this.xhr.abort();
            }

            this.xhr = $.ajax({
                url: urlBuilder.build('totaltools_storelocator/index/stores'),
                data: {
                    postcode: postcode,
                },
                type: 'POST',
                dataType: 'json',
            });

            return this.xhr;
        },

        /**
         * @param {Object} state
         * @param {jQuery.event} ev
         */
        navigateState: function (state, ev) {
            ev.preventDefault();

            locatorStore.setState(state);

            if (this.options['redirect']) {
                $('body').trigger('processStart');
                var stateUrl = urlBuilder.build(
                    'storelocator/index/index/state/'
                );
                window.location = stateUrl + state.name;

                return;
            }
        },
    });
});
