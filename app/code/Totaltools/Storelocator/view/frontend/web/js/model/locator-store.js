/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> Iqbal <<EMAIL>>
 */

define(['ko'], function (ko) {
    'use strict';

    var stores = ko.observableArray([]),
        state = ko.observable({}),
        store = ko.observable({});

    return {
        stores: stores,
        state: state,
        store: store,

        getStores: function () {
            return this.stores;
        },

        setStores: function (stores) {
            this.stores(stores);
            return this;
        },

        getState: function () {
            return this.state;
        },

        setState: function (state) {
            this.state(state);
            return this;
        },

        getStore: function () {
            return this.store;
        },

        setStore: function (store) {
            this.store(store);
            return this;
        },
    };
});
