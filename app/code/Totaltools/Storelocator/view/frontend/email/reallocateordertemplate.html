<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!--@subject {{trans "Reallocation: Your %store_name order reallocation confirmation from %original_store" store_name=$order_store.getFrontendName() original_store=$original_store}} @-->
<!--@vars {
"var formattedBillingAddress|raw":"Billing Address",
"var order.getEmailCustomerNote()":"Email Order Note",
"var order.increment_id":"Order Id",
"layout handle=\"sales_email_order_items\" order=$order area=\"frontend\"":"Order Items Grid",
"var payment_html|raw":"Payment Details",
"var formattedShippingAddress|raw":"Shipping Address",
"var order.getShippingDescription()":"Shipping Description",
"var shipping_msg":"Shipping message"
} @-->

{{template config_path="design/email/header_template_new"}}

<tr>
    <td class="main-content main-content-custom">
    <!-- Begin Content -->
    
<div class="email-inner-content">
<table>
    <tr class="email-intro">
        <td>
<p class="greeting">{{trans "G’day %tt_store_name," tt_store_name=$store_name}}
            </p>            
            <p>
                {{trans "A new online order %increment_id has been reallocated to you from: <span class='greeting'>%original_store</span>." increment_id=$order.increment_id original_store=$original_store |raw}}
            </p>
            <p>
                {{trans "Please log in to your Shippit account to process the order." }}
            </p>
        </td>
    </tr>

    <tr>
        <td class="notification">
            <span style="float: left;">Delivery Method: <br/>
                <strong>
                    {{if is_store_pickup}}
                        {{trans "Shippit Click & Collect"}}
                    {{else}}
                        {{trans "Shippit Delivery"}}
                    {{/if}}
                </strong>
            </span>
            {{if is_store_pickup}}
                {{depend order.getThirdPartyPickup()}}
                    <span style="float: right;">{{trans "Third Party Pickup"}}: <br /> <strong>{{var order.getThirdPartyName()|escape|nl2br}}</strong></span>
                {{/depend}}
            {{else}}
                
                    <span style="float: right;">{{trans "Authority to leave"}}: <br /> <strong>
                        {{var authToLeaveText|raw}}
                    </strong></span>
            {{/if}}
        </td>
    </tr>

    <tr class="email-summary">
        <td>
            <h2 >{{trans 'Order Confirmation: <span class="no-link">#%increment_id</span>' increment_id=$order.increment_id |raw}}
                <br />{{trans 'Order Date: <span class="no-link">%created_at</span>' created_at=$order.getCreatedAtFormatted(10) |raw}}
            </h2>
            
        </td>
    </tr>
    <tr class="email-information">
        <td>
            {{depend order.getEmailCustomerNote()}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var order.getEmailCustomerNote()|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}
            <table class="order-details">
                <tr>
                    <td class="address-details">
                        <h3>{{trans "Billing Info"}}</h3>
                        <p>{{var formattedBillingAddress|raw}}
<br/>{{trans 'E: <a href="mailto:%customer_email_address">%customer_email_address</a>' customer_email_address=$order.getCustomerEmail() |raw}}
</p>
                    </td>
                    {{if !is_store_pickup}}
                        {{depend order.getIsNotVirtual()}}
                        <td class="address-details">
                            <h3>{{trans "Shipping Info"}}</h3>
                            <p>{{var formattedShippingAddress|raw}}</p>
                        </td>
                        {{/depend}}
                    {{/if}}
                    <td class="method-info">
                        <h3>{{trans "Payment Method"}}</h3>
                        {{var payment_html|raw}}
                    </td>
                    
                </tr>
            </table>
            {{layout handle="sales_email_order_items"  showBarcode=1 order=$order area="frontend"}}
        </td>
    </tr>
    {{if is_store_pickup}}
    <tr>
        <td class="method-info">
            <h2>STORE USE ONLY - PICK UP IN STORE CHECKLIST</h3>
        </td>
    </tr>
        <tr>
        <td>
            <table style="width: 100%;">
                <tr>
                    <td>Credit Card Check:</td>
                    <td>Photo ID Check:</td>
                    <td>Customer name:</td>
                </tr>
                <tr>
                    <td>Signature:</td>
                    <td>Date of collection:</td>
                    <td>TT staff name:</td>
                </tr>
            </table>
    </td>
    </tr>
    {{/if}}
</table>
</div>

{{template config_path="design/email/footer_template_new"}}
