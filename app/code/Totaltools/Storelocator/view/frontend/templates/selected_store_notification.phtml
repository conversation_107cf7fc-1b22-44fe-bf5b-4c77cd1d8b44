<?php

/**
 * @var \Totaltools\Storelocator\Block\SelectedStoreNotification $block
 */
?>
<div id="add-cart-notifications"></div>
<?php if ($block->isEnabled()): ?>
<script type="text/x-magento-template" id="selected-store-notification-template">
    <div class="notification">
        <% if (opts.hasCloseBtn) { %><span class="close"><span>x</span></span><% } %>
        <div class="notification-wrapper">
            <div class="notification-content locate-store">
              
                <div class="store-name">
                    <a href="<%- data.store.store_url %>"><%= data.store.store_name %></a> <span>has been selected as your nearest store.</span>
                </div>
                <div class="store-link"><a class="action primary" href="<%- data.store.store_url %>"><%- opts.btnText %></a></div>
                <!-- Change Store Button -->
                    <button class="action change-store-btn"><%- opts.changeStoreBtnText %></button>
            </div>
        </div>
    </div>
</script>

<script>
    require(['selectedstorenotification'], function(notifications) {
        notifications.init(<?= /* @noEscape */ $block->getJsonConfig(); ?>);
    });
</script>
<?php endif; ?>
