<?php

/**
 * Magestore
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Magestore.com license that is
 * available through the world-wide-web at this URL:
 * http://www.magestore.com/license-agreement.html
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade this extension to newer
 * version in the future.
 *
 * @category    Magestore
 * @package     Magestore_Storelocator
 * @copyright   Copyright (c) 2012 Magestore (http://www.magestore.com/)
 * @license     http://www.magestore.com/license-agreement.html
 */

/** @var \Magestore\Storelocator\Block\Wrapper $block */
?>
<script type="text/javascript">
    var initGoogleMap = function() {
        require(['jquery', 'Magestore_Storelocator/js/googlemap', 'Magestore_Storelocator/js/markerclusterer'], function($){
            $(document).ready(function ($) {
                window.MarkerClusterer.IMAGE_PATH = 'https://cdn.rawgit.com/googlemaps/js-marker-clusterer/gh-pages/images/m';
                $('.googlemap').GoogleMap({
                    urlLoadStore: '<?php echo $block->getUrl('totaltools_storelocator/index/loadStoreRewrite') ?>',
                    paginationWrapper: '.pagination-wrapper',
                    liststoreContainer: '.list-store-container',
                    storePopupTemplate: '.popup-store-template',
                    listTag: '.list-tag-ul',
                    searchBox: '.mgs-search-box',
                    loader: '.overlay-bg',
                    defaultRaidus: '<?php echo $block->getSystemConfig()->getDefaultRadius() ?>',
                    distanceUnit: '<?php echo $block->getSystemConfig()->getDistanceUnit() ?>',
                    circleCenterIcon: '<?php echo $block->getViewFileUrl('circlecenter.png') ?>',
                    mediaUrlImage: '<?php echo $block->getMediaUrlImage() ?>',
                    maxZoom: 17,
                    initialSearch: <?php echo json_encode($this->getSearchParams()); ?>
                });
            });
        });
    }
</script>
<script src="https://maps.googleapis.com/maps/api/js?key=<?php echo $block->getGoolgeApiKey(); ?>&amp;callback=initGoogleMap&amp;libraries=places,geometry"></script>
<div class="storelocator-wrapper col-full">
    <h2 class="title-page"><?php echo __('Store Locator') ?></h2>
    <?php echo $block->getChildHtml('storelocator.searchbox'); ?>
    <div class="boxes-content">
        <?php echo $block->getChildHtml('storelocator.mapbox'); ?>
        <?php echo $block->getChildHtml('storelocator.liststorebox'); ?>
    </div>
    <div class="overlay-bg">
        <img src="<?php echo $block->getViewFileUrl('Magestore_Storelocator::images/ajax-loader.gif') ?>" alt="overlay" />
    </div>
</div>
