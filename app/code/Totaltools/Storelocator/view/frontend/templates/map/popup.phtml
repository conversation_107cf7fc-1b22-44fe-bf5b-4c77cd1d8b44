<script id="popup-store-template" class="popup-store-template" type="text/x-magento-template">
    <div class="popup-store store-item store-<%= data.storelocator_id %>"
        title="<%= data.store_name %>"
        data-store-id="<%= data.storelocator_id %>"
        data-latitude="<%= data.latitude %>"
        data-longitude="<%= data.longitude %>"
        data-address="<%= data.address %>"

        data-phone="<%= data.phone %>"
        data-city="<%= data.city %>"
        data-zipcode="<%= data.zipcode %>"
        data-state="<%= data.state %>"
        >
        <div class="store-content">
            <div class="tag-content">
                <h4><a class="title-store" href="<?php echo $block->getBaseUrl() ?><%= data.rewrite_request_path %>"><%= data.store_name %></a></h4>
                <p class="address-store"><%= data.address %><br/><%= data.city %>, <%= data.zipcode %>, <%= data.state %></p>
                <p class="phone-store"><?php echo __('Phone:'); ?> <a class="store-phone" href="tel:<%- data.phone %>" data-store_name="<%= data.store_name %>"><%- data.phone %></a></p>
                <p>
                    <a target="_blank" data-store="<%= data.store_name %>" href="<%= data.store_direction_url %>" class="btn-link direction">Get Directions</a>
                </p>
            </div>
        </div>
    </div>
</script>

<script type="text/javascript">
require([
        'jquery',
        'Totaltools_Storelocator/js/actions/gtmevents'
    ], function ($, gtmEvents) {
        /**
         * GTM click events for store phone and directions.
         */
        $('body').on('click', 'a.store-phone', function (e) {
            gtmEvents.handleStorePhone($(this).data(), e);
        });

        $('body').on('click', 'a.direction', function (e) {
            gtmEvents.handleStoreDirections($(this).data(), e);
        });
    }
);
</script>
