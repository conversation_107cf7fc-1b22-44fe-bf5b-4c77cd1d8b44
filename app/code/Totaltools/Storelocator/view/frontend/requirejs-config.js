/**
 * <AUTHOR> Internet
 * @package  Totaltools_Storelocator
 */

var config = {
    map: {
        '*': {
            'Magestore_Storelocator/js/googlemap': 'Totaltools_Storelocator/js/googlemap',
            'totaltools/map-loader': 'Totaltools_Storelocator/js/map/map-loader',
            'selectedstorenotification': 'Totaltools_Storelocator/js/selected-store-notification',
            'storenotification': 'Totaltools_Storelocator/js/widgets/notification',
        },
    },
    config: {
        mixins: {
          
            'Totaltools_Storelocator/js/widgets/notification': {
                'Totaltools_Storelocator/js/mixin/notification-mixin': true
            }
        },
    },
};
