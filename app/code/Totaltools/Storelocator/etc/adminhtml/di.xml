<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tabs"
                type="Totaltools\Storelocator\Block\Adminhtml\Store\Edit\Tabs" />

    <preference for="Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GeneralTab"
                type="Totaltools\Storelocator\Block\Adminhtml\Store\Edit\Tab\GeneralTab" />
    
    <preference for="Magestore\Storelocator\Block\Adminhtml\Store\Edit"
                type="Totaltools\Storelocator\Block\Adminhtml\Store\Edit" />

    <type name="Magento\Backend\Block\Widget\Context">
        <plugin name="add_send_email_store_button_sales_view" type="Totaltools\Storelocator\Plugin\Sales\AddSendToStoreButtonPlugin" sortOrder="100" />
    </type>
</config>