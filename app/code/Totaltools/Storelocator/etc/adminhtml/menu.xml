<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
    <menu>
        <add id="Totaltools_Storelocator::shippingzones" title="Shipping Zones"
             module="Totaltools_Storelocator"
             parent="Magestore_Storelocator::storelocator"
             action="totaltoolsadmin/zone"
             resource="Totaltools_Storelocator::shippingzones"
             sortOrder="20"/>
        <add id="Totaltools_Storelocator::soh" title="SOH"
             translate="title"
             module="Totaltools_Storelocator"
             sortOrder="110"
             parent="Magento_Reports::report"
             resource="Magento_Reports::soh"/>
        <add id="Totaltools_Storelocator::inventoryimportreport" title="Inventory Import Report"
             translate="title"
             module="Totaltools_Storelocator"
             sortOrder="10"
             parent="Totaltools_Storelocator::soh"
             action="totaltools_storelocator/report/index"
             resource="Totaltools_Storelocator::inventoryimportreport"/>
    </menu>
</config>
