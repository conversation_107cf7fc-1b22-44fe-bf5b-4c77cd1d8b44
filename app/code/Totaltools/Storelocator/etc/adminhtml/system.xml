<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="storelocator">
            <group id="inventory" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Inventory Import</label>
                <field id="directory" translate="label comment" type="text" sortOrder="10"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Inventory import directory</label>
                    <comment><![CDATA[Use absolute file system directory path without a trailing slash]]></comment>
                </field>
                <field id="days" translate="label comment" type="text" sortOrder="20"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Keep processed files for how many days</label>
                    <comment><![CDATA[Enter a whole number (default value is 7).  Can be increased during times of debugging or decreased to minimise disk usage.]]></comment>
                </field>
                <field id="enable_csv_soh" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable CSV Inventory Import</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="logs" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Inventory Import log</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[Log debug data to inventory-import.log.  Only enable this feature during debugging.]]></comment>
                </field>
                <field id="inventoryimport_clear" translate="label comment" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Inventory Import Clear Expressions</label>
                    <comment><![CDATA[Expressions to clear tmp table.]]></comment>
                </field>
                <field id="email_receiver" translate="label comment" type="text" sortOrder="60"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Notify Email To</label>
                    <comment><![CDATA[Send email when importing error.]]></comment>
                </field>
                <field id="inventoryimport_last_run_boundary" translate="label comment" type="text" sortOrder="70"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Last Import Notification Boundary (in minutes)</label>
                    <comment><![CDATA[Notify the email receiver ('Send Notify Email To' field above) if the last full import has not run in (x) MINUTES. Minimum value 60 minutes]]></comment>
                </field>
                <field id="max_partial_import_size_kb" translate="Maximum Partial File Size (in kb)" type="text" sortOrder="80"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Maximum Partial File Size (in kb)</label>
                    <comment><![CDATA[Maximum Partial File Size (in kb). If over this limit, the file will not be imported]]></comment>
                </field>
                <field id="max_full_import_deviation_percentage" translate="Maximum Full File Discrepancy (percentage)" type="text" sortOrder="90"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Maximum Full File Discrepancy (percentage)</label>
                    <comment><![CDATA[This value defines the percentage threshold for import validation. It utilises the full-import-file item count and the DB item count to find any full-import-files above (or below) this threshold entered. If the threshold is met, the import is aborted]]></comment>
                </field>
                <field id="soh_api_email_recipient" translate="label" type="text" sortOrder="100"  showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>SOH API Email Recipient</label>
                </field>
                <field id="api_directory" translate="label comment" type="text" sortOrder="110"  showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>SOH API Logs Directory</label>
                    <comment><![CDATA[Use absolute file system directory path without a trailing slash]]></comment>
                </field>
            </group>
            <group id="service" >
                <field id="service_url" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Map Service URL</label>
                    <comment><![CDATA[MAP service Url other than Google Maps]]></comment>
                </field>
                <field id="access_token" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Map Service Access Token</label>
                    <comment><![CDATA[Leave blank if not applicable]]></comment>
                </field>
                <field id="tile_max_zoom" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tile Max Zoom</label>
                </field>
                <field id="tile_size" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tile Size</label>
                </field>
                <field id="tile_zoom_offset" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zoom Offset</label>
                </field>
                <field id="tile_service_id" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Tile Service ID</label>
                </field>
            </group>
            <group id="send_mail" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Email</label>
                <field id="notify_merchant_email_template" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Notify merchant of new order template</label>
                    <source_model>Totaltools\Storelocator\Model\Config\Source\Email\Template</source_model>
                </field>
                 <field id="notify_merchant_email_sender" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Notify Merchant of new order Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                </field>
                <field id="email_copy_to" translate="label comment" type="text" sortOrder="20"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Notify Email Copy To</label>
                    <comment><![CDATA[Comma-separated]]></comment>
                </field>
                <field id="method" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Notify Email Copy Method</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Method</source_model>
                </field>
                <field id="reallocation_merchant_email_template" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Notify merchant of new order reallocated template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="reallocation_store_email_sender" translate="label" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Order Reallocation Original Store Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                </field>
                <field id="reallocation_customer_email_sender" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Order Reallocation Customer Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                </field>
                 <field id="original_store_reallocate" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Notify merchant of order reallocated to another store template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                 <field id="customer_order_reallocate" translate="label" type="select" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Notify customer of order reallocated to another store template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="cc_notify_email_to" translate="label" type="text" sortOrder="100"  showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Click and Collect issue notify email To</label>
                </field>
                <field id="cc_notify_email_template" translate="label" type="select" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Click and Collect issue notify email template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>

                <field id="commercial_account_request_email_sender" translate="label" type="select" sortOrder="115" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Commercial Account Request Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                </field>
                <field id="commercial_account_request_email_to" translate="label" type="text" sortOrder="120"  showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Commercial Account Request email To</label>
                </field>
                 <field id="commercial_account_request_email_cc_to" translate="label" type="text" sortOrder="125"  showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Commercial Account Request email cc To</label>
                </field>
                <field id="commercial_account_request" translate="label" type="select" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Commercial Account Request email template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="store_account_email_cc_to" translate="label" type="text" sortOrder="140"  showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Store account form email cc to</label>
                </field>
                <field id="store_account_email_template" translate="label" type="select" sortOrder="150" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Store account form email template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="customer_store_account_email_template" translate="label" type="select" sortOrder="160" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer Store account form email template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="customer_commercial_account_email_template" translate="label" type="select" sortOrder="170" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer Commercial account form email template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="customer_insider_to_traderewards_email_template" translate="label" type="select" sortOrder="180" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer insider signs up to trade rewards email template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>
            <group id="trade_reward" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Trade Rewards Setting</label>
                <field id="store_account_form_path" translate="label comment" type="text" sortOrder="10"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Store Account Form Path</label>
                    <comment><![CDATA[Use absolute file system file path without a trailing slash]]></comment>
                </field>
                 <field id="commercial_account_form_path" translate="label comment" type="text" sortOrder="20"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Commercial Account Form Path</label>
                    <comment><![CDATA[Use absolute file system file path without a trailing slash]]></comment>
                </field>
            </group>
              <group id="notifications" translate="label" type="text" sortOrder="1100" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Store Selected Notification</label>
                <field id="enabled" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="1" translate="label" type="select">
                    <label>Enabled</label>
                    <comment>Enable Notifications</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="auto_hide" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="2" translate="label" type="select">
                    <label>Can Auto Hide</label>
                    <comment>Notification should auto hide after certain time</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="holdup" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="3" translate="label" type="text">
                    <label>Notification Hold up</label>
                    <comment>Time in seconds before notification dissappears</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="fade_time" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="4" translate="label" type="text">
                    <label>Fade Delay</label>
                    <comment>Time in seconds for fade in/out animations</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="close_btn" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="5" translate="label" type="select">
                    <label>Show Close Button</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
              
                <field id="title" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="7" translate="label" type="text">
                    <label>Notification Title</label>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="button_text" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="8" translate="label" type="text">
                    <label>Button Title</label>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="promo_text" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="9" translate="label" type="text">
                    <label>Promotional Message</label>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
        <section id="sales_email">
            <group id="order" >
                <label>General</label>
                <field id="store_email_sender" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>New Order Store Confirmation Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
