<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="inventory_import_report">
        <column xsi:type="int" name="entity_id" padding="10" unsigned="true" nullable="false" identity="true"
                comment="Entity Id"/>
        <column xsi:type="varchar" name="filename" nullable="false" length="255" comment="Filename"/>
        <column xsi:type="varchar" name="import_type" nullable="false" length="16" comment="Import Type"/>
        <column xsi:type="timestamp" name="created_at" nullable="false" default="CURRENT_TIMESTAMP"
                comment="Created At"/>
        <column xsi:type="timestamp" name="updated_at" nullable="false" default="CURRENT_TIMESTAMP"
                on_update="true" comment="Updated At"/>
        <column xsi:type="varchar" name="status"  nullable="false" length="16" comment="Status"/>
        <column xsi:type="text" name="comment" nullable="true"
                comment="Comment"/>
        <column xsi:type="int" name="row_count_in_file" padding="6" unsigned="true" nullable="false"
                identity="false" default="0" comment="Line count in File"/>
        <constraint xsi:type="primary" referenceId="PRIMARY">
            <column name="entity_id"/>
        </constraint>
    </table>
</schema>
