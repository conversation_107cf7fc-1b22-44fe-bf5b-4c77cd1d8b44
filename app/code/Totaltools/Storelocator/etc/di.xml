<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Storelocator
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magestore\Storelocator\Block\Wrapper"
                type="Totaltools\Storelocator\Block\Wrapper" />

    <preference for="Magestore\Storelocator\Block\ListStore\Pagination"
                type="Totaltools\Storelocator\Block\ListStore\Pagination" />

    <preference for="Magestore\Storelocator\Block\ListStore\SearchBox"
                type="Totaltools\Storelocator\Block\ListStore\SearchBoxRewrite" />

    <preference for="Magestore\Storelocator\Block\Store\ViewPage"
                type="Totaltools\Storelocator\Block\Store\ViewPageRewrite" />

    <preference for="Magestore\Storelocator\Model\ResourceModel\Store\Collection"
                type="Totaltools\Storelocator\Model\ResourceModel\Store\Collection" />

    <preference for="Totaltools\Storelocator\Api\ListStoresInterface"
                type="Totaltools\Storelocator\Model\ListStoresModel"/>

    <preference for="Totaltools\Storelocator\Api\StoreLocatorInterface"
                type="Totaltools\Storelocator\Model\Rest\Store" />

    <preference for="Totaltools\Storelocator\Api\Data\StoreInventoryInterface"
                type="Totaltools\Storelocator\Model\StoreInventory" />

    <preference for="Totaltools\Storelocator\Api\StoreInventorySearchResultsInterface"
                type="Magento\Framework\Api\SearchResults" />

    <preference for="Totaltools\Storelocator\Api\StoreSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults" />

    <preference for="Totaltools\Storelocator\Api\Data\StoreFallbackInterface"
                type="Totaltools\Storelocator\Model\StoreFallback" />

    <preference for="Totaltools\Storelocator\Api\StoreFallbackSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults" />

    <preference for="Magestore\Storelocator\Model\Store"
                type="Totaltools\Storelocator\Model\Store" />

    <preference for="Totaltools\Storelocator\Parser\ItemProcessorInterface"
                type="Totaltools\Storelocator\Parser\ItemProcessor" />

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="totaltoolsadmin_zone_listing_data_source"
                      xsi:type="string">Totaltools\Storelocator\Model\ResourceModel\Zone\Grid\Collection</item>
            </argument>
        </arguments>
    </type>

    <!-- ZONE -->
    <preference for="Totaltools\Storelocator\Api\Data\ZoneInterface" type="Totaltools\Storelocator\Model\Zone" />
    <virtualType name="ZoneGridDataProvider" type="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Totaltools\Storelocator\Model\Resource\Zone\Grid\Collection</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\Storelocator\Model\ResourceModel\Zone\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">magestore_storelocator_zone</argument>
            <argument name="resourceModel" xsi:type="string">Totaltools\Storelocator\Model\ResourceModel\Zone</argument>
        </arguments>
    </virtualType>

    <type name="Totaltools\Storelocator\Model\Resource\Zone\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">magestore_storelocator_zone</argument>
            <argument name="eventPrefix" xsi:type="string">magestore_storelocator_zone</argument>
            <argument name="eventObject" xsi:type="string">magestore_storelocator_zone</argument>
            <argument name="resourceModel" xsi:type="string">Totaltools\Storelocator\Model\ResourceModel\Zone</argument>
        </arguments>
    </type>

    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="migrate_fallback_stores"
                      xsi:type="object">Totaltools\Storelocator\Console\Command\StoreFallbackCommand</item>
                <item name="migrate_temando"
                      xsi:type="object">Totaltools\Storelocator\Console\Command\TemandoMigrationCommand</item>
                <item name="totalToolsUpdateStoreLocatorUrlRewrite"
                      xsi:type="object">Totaltools\Storelocator\Console\Command\UrlWrite</item>
                <item name="stockOnHand"
                      xsi:type="object">Totaltools\Storelocator\Console\Command\StockOnHand</item>
                <item name="stockOnHandApi"
                      xsi:type="object">Totaltools\Storelocator\Console\Command\StockOnHandApi</item>
                <item name="stockOnHandDeltaApi"
                      xsi:type="object">Totaltools\Storelocator\Console\Command\StockOnHandDeltaApi</item>

            </argument>
        </arguments>
    </type>

    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="totaltools_storelocator_add_order_extension_attribute"
                type="Totaltools\Storelocator\Plugin\OrderRepositoryPlugin" sortOrder="1"/>
    </type>
    <!-- Configure the console command processor dependencies as proxies to prevent urlRewriteProcessor::__construct() during setup -->
    <type name="Totaltools\Storelocator\Console\Command\UrlWrite">
        <arguments>
            <argument name="urlRewriteProcessor" xsi:type="object">Totaltools\Storelocator\Model\UrlRewriteProcessor\Proxy</argument>
        </arguments>
    </type>
    <type name="Magento\Backend\Block\Widget\Grid\Column\Renderer\Date">
        <plugin name="totaltools_storelocator_widget_grid_column_renderer_date"
                type="Totaltools\Storelocator\Plugin\Block\Widget\Grid\Column\Renderer\Date" />
    </type>

    <!-- Custom log file for TotalTools Inventory Import Filesystem -->
    <virtualType name="Totaltools\Storelocator\Model\Logger\FsDebug" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/inventory_import_fs.log</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\Storelocator\Model\Logger\FileLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="name" xsi:type="string">TOT</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Totaltools\Storelocator\Model\Logger\FsDebug</item>
            </argument>
        </arguments>
    </virtualType>

    <!-- Custom log file for TotalTools Inventory Import -->
    <virtualType name="Totaltools\Storelocator\Model\Logger\ImportDebug" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/inventory_import.log</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\Storelocator\Model\Logger\ImportLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="name" xsi:type="string">TOT</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Totaltools\Storelocator\Model\Logger\ImportDebug</item>
            </argument>
        </arguments>
    </virtualType>

    <type name="Totaltools\Storelocator\Parser\File">
        <arguments>
            <argument name="logger" xsi:type="object">Totaltools\Storelocator\Model\Logger\FileLogger</argument>
        </arguments>
    </type>
    <type name="Totaltools\Storelocator\Parser\ItemProcessor">
        <arguments>
            <argument name="logger" xsi:type="object">Totaltools\Storelocator\Model\Logger\ImportLogger</argument>
        </arguments>
    </type>
    <type name="Totaltools\Storelocator\Model\InventoryProcessor">
        <arguments>
            <argument name="logger" xsi:type="object">Totaltools\Storelocator\Model\Logger\ImportLogger</argument>
        </arguments>
    </type>
    <type name="Totaltools\Storelocator\Model\Registry">
        <arguments>
            <argument name="resourceSession" xsi:type="object">Magento\Checkout\Model\Session\Proxy</argument>
        </arguments>
    </type>
    <type name="Magento\Sales\Model\Service\OrderService">
        <plugin name="balance_storelocatore_value_set_plugin" type="Totaltools\Storelocator\Plugin\Model\Service\OrderService" sortOrder="1" />
    </type>
    <type name="Totaltools\Storelocator\Model\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Totaltools\Storelocator\Model\Logger">
        <arguments>
            <argument name="name" xsi:type="string">storeDetailsLogger</argument>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">Totaltools\Storelocator\Model\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
    <type name="Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GmapTab">
        <plugin name="gmap_tab_plugin" type="Totaltools\Storelocator\Plugin\Block\Adminhtml\Store\Edit\Tab\GmapTab" sortOrder="1"/>
    </type>
    <preference for="Totaltools\Storelocator\Api\InventoryimportreportRepositoryInterface" type="Totaltools\Storelocator\Model\InventoryimportreportRepository"/>
    <preference for="Totaltools\Storelocator\Api\Data\InventoryimportreportInterface" type="Totaltools\Storelocator\Model\Inventoryimportreport"/>
    <preference for="Totaltools\Storelocator\Api\Data\InventoryimportreportSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
    <virtualType name="Totaltools\Storelocator\Model\ResourceModel\Inventoryimportreport\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">inventory_import_report</argument>
            <argument name="resourceModel" xsi:type="string">Totaltools\Storelocator\Model\ResourceModel\Inventoryimportreport\Collection
            </argument>
        </arguments>
    </virtualType>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="totaltools_storelocator_report_listing_data_source"
                      xsi:type="string">Totaltools\Storelocator\Model\ResourceModel\Inventoryimportreport\Grid\Collection
                </item>
            </argument>
        </arguments>
    </type>
</config>
