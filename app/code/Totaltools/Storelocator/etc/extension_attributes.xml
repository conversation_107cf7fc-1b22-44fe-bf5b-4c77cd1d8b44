<?xml version="1.0"?>
<!-- File: app/code/Atwix/OrderFeedback/etc/extension_attributes.xml -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Sales\Api\Data\OrderInterface">
        <attribute code="warehouse" type="string" />
        <attribute code="store" type="string" />
        <attribute code="storelocator_id" type="int" />
        <attribute code="storelocator_api_type" type="int" />
    </extension_attributes>
    <extension_attributes for="Magento\Quote\Api\Data\AddressInterface">
        <attribute code="storelocator_id" type="int" />
    </extension_attributes>
    <extension_attributes for="Magento\Checkout\Api\Data\ShippingInformationInterface">
        <attribute code="storelocator_id" type="int" />
    </extension_attributes>
    <extension_attributes for="Magento\Quote\Api\Data\TotalsItemInterface">
        <attribute code="shipping_label" type="string" />
    </extension_attributes>
</config>