<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/storelocator/list" method="GET">
        <service class="Totaltools\Storelocator\Api\ListStoresInterface" method="listStores"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/storelocator/stores/:postCode" method="GET">
        <service class="Totaltools\Storelocator\Api\StoreLocatorInterface" method="getByZipCode"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
</routes>