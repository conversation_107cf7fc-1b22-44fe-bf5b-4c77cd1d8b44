<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="storelocator_inventory_import">
        <job name="storelocator_inventoryimport" instance="Totaltools\Storelocator\Cron\Inventory" method="execute">
            <schedule>* * * * *</schedule>
        </job>
        <job name="storelocator_inventoryimport_clear" instance="Totaltools\Storelocator\Cron\CleanupTemporaryTables" method="execute">
            <config_path>storelocator/inventory/inventoryimport_clear</config_path>
        </job>
        <!-- <job name="storelocator_inventoryimport_fullsoh_perstore" instance="Totaltools\Storelocator\Cron\FullSoh" method="execute">
            <schedule>0 3 * * *</schedule>
        </job>
        <job name="storelocator_inventoryimport_deltasoh_perstore" instance="Totaltools\Storelocator\Cron\DeltaSoh" method="execute">
            <schedule>*/30 * * * *</schedule>
        </job> -->
    </group>
    <group id="storelocator_inventory_import_report">
        <job name="storelocator_inventoryimport_status_checker" instance="Totaltools\Storelocator\Cron\InventoryImportStatusChecker" method="execute">
            <schedule>15 * * * *</schedule>
        </job>
    </group>
</config>
