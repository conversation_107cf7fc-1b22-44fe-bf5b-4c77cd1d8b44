<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<logging xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Logging:etc/logging.xsd">
    <groups>
        <group name="store_admin_email_log">
            <label translate="true">Store Admin Email Send</label>
            <expected_models>
                <expected_model class="Magento\Sales\Model\Order">
                    <additional_fields>
                        <field name="increment_id" />
                    </additional_fields>
                </expected_model>
            </expected_models>
            <events>
                <event controller_action="totaltoolsadmin_store_email" action_alias="send" >
                    <expected_models merge_group="true">
                        <expected_model class="Magento\Sales\Model\Order\Status\History" />
                    </expected_models>
                </event>
            </events>
        </group>
    </groups>
</logging>