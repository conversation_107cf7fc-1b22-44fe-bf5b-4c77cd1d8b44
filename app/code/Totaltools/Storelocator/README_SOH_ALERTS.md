# SOH API Import Failure Alerts

## Overview

This enhancement adds email alert functionality to the SOH (Stock on Hand) API import process. When the `totaltools:sohapi:import` command runs and encounters failures, it will automatically send email alerts to configured recipients.

## Features

### Failure Detection
The system now detects and tracks the following failure scenarios:
- **Empty API Response**: When the API returns no data
- **Missing API Endpoint**: When a store has no configured API endpoint
- **No Items Found**: When the API response contains no inventory items
- **No Matching Data**: When no inventory data matches the store's ERP ID
- **API Exceptions**: When API requests fail due to network or server errors

### Email Alerts
- **Automatic Alerts**: Failure emails are sent automatically when failures are detected
- **Detailed Information**: Each alert includes store names, ERP IDs, error messages, and timestamps
- **HTML Formatting**: Professional email template with clear formatting and action items
- **Configurable Recipients**: Uses the existing "SOH API Email Recipient" configuration

### Enhanced Logging
- **Detailed Logs**: All failures are logged with specific error messages
- **Success Tracking**: Successful imports are also logged for audit purposes
- **Console Output**: Command line execution shows real-time progress and results

## Configuration

### Email Recipients
Configure email recipients in the Magento Admin:
1. Go to **Stores > Configuration > Store Locator > Inventory**
2. Set the **SOH API Email Recipient** field
3. Use comma-separated email addresses for multiple recipients

Example: `<EMAIL>,<EMAIL>,<EMAIL>`

### Email Template
The failure alert uses the template: `import_inventory_api_failure_email`
- Template file: `view/adminhtml/email/import_api_failure_report.html`
- Subject: "SOH API Import Failure Alert - Action Required"

## Usage

### Console Command
```bash
php bin/magento totaltools:sohapi:import
```

**Enhanced Output:**
```
Pronto API SOH Import started

Processing 5 stores...

Processing store: Store A (ERP ID: 001)... SUCCESS
Processing store: Store B (ERP ID: 002)... FAILED
Processing store: Store C (ERP ID: 003)... SUCCESS
Processing store: Store D (ERP ID: 004)... FAILED
Processing store: Store E (ERP ID: 005)... SUCCESS

Failure alert email sent for 2 failed stores.

Pronto API SOH Import finished
Summary: 3 successful, 2 failed
```

### Cron Job
The cron job (`totaltools_storelocator_full_soh`) also includes the same failure tracking and alerting.

### Admin Panel
Manual imports via the admin panel will show warning messages when failures occur and send alert emails.

## Email Template Content

The failure alert email includes:
- **Header**: Clear alert styling with failure indication
- **Store Details Table**: ERP ID, Store Name, Error Message, Timestamp
- **Action Items**: Suggested next steps for resolution
- **Summary**: Total count of failed stores

## Troubleshooting

### Common Failure Scenarios

1. **"No API endpoint configured"**
   - Check store configuration in admin panel
   - Ensure `full_soh_api_endpoint` is set for each store

2. **"Empty response from API"**
   - Verify API endpoint is accessible
   - Check network connectivity
   - Verify API credentials

3. **"No items found in API response"**
   - Check if the API is returning data
   - Verify API response format
   - Check if store has inventory

4. **"No matching inventory data found for store"**
   - Verify ERP ID matches between store config and API response
   - Check if API is returning data for the correct warehouse

### Logs
Check the following log files for detailed information:
- `var/log/system.log` - General system logs
- `var/log/debug.log` - Debug information (if enabled)

## Technical Implementation

### Key Classes Modified
- `Totaltools\Storelocator\Model\SohProcessor` - Enhanced with failure tracking
- `Totaltools\Storelocator\Model\ImportInventory\Email` - Added failure email method
- `Totaltools\Storelocator\Console\Command\StockOnHandApi` - Enhanced console output
- `Totaltools\Storelocator\Cron\FullSoh` - Added failure alerting to cron

### New Methods
- `SohProcessor::addFailedStore()` - Track individual store failures
- `SohProcessor::getFailedStores()` - Retrieve failed stores list
- `SohProcessor::hasFailures()` - Check if any failures occurred
- `SohProcessor::resetFailureTracking()` - Reset failure state
- `SohProcessor::sendFailureAlert()` - Send failure email
- `Email::sendSohApiFailureEmail()` - Send formatted failure email

### Return Codes
The console command now returns appropriate exit codes:
- `0` (SUCCESS) - All stores imported successfully
- `1` (FAILURE) - One or more stores failed to import

This allows for proper monitoring in automated environments and CI/CD pipelines.
