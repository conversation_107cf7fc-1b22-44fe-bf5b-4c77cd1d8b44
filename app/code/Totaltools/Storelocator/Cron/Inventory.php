<?php
/**
 * Total Tools Store Locator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Cron;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Class Inventory
 * @package Totaltools\Storelocator\Cron
 */
class Inventory
{
    /**
     * Inventory Csv SOH Enabled
     */
    const XML_PATH_INVENTORY_CSV_SOH_ENABLED = 'storelocator/inventory/enable_csv_soh';

    /**
     * @var \Totaltools\Storelocator\Model\InventoryProcessor
     */
    private $inventoryProcessor;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * Inventory constructor.
     * @param \Totaltools\Storelocator\Model\InventoryProcessor $inventoryProcessor
     * @param \Psr\Log\LoggerInterface $logger
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        \Totaltools\Storelocator\Model\InventoryProcessor $inventoryProcessor,
        \Psr\Log\LoggerInterface $logger,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->inventoryProcessor = $inventoryProcessor;
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @return $this|bool
     * @throws \Exception
     */
    public function execute()
    {
        if ($this->isInventoryCsvSohEnabled()) {
            try {
                $this->inventoryProcessor->execute();
            } catch (\Exception $e) {
                $this->logger->critical(__('Something went wrong with the cronjob.\\n %1', $e->getMessage()));
            }
        } else {
            $this->logger->critical(__('CSV SOH Import is disabled.'));
        }

        return $this;
    }

    /**
     * @inheritdoc
     */
    public function isInventoryCsvSohEnabled() : ?bool
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_INVENTORY_CSV_SOH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }
}
