<?php
/**
 * Total Tools Store Locator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Cron;

/**
 * Class CleanupTemporaryTables
 * @package Totaltools\Storelocator\Cron
 */
class CleanupTemporaryTables
{
    /**
     * @var \Totaltools\Storelocator\Model\ResourceModel\InventoryProcessor
     */
    private $resource;

    /**
     * DropInventory constructor.
     * @param \Totaltools\Storelocator\Model\ResourceModel\InventoryProcessor $resource
     */
    public function __construct(
        \Totaltools\Storelocator\Model\ResourceModel\InventoryProcessor $resource
    ) {
        $this->resource = $resource;
    }

    /**
     * @return $this
     */
    public function execute()
    {
        $this->resource->cleanupTemporaryTable();

        return $this;
    }
}
