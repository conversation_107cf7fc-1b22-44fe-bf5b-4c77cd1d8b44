<?php
/**
 * Total Tools Store Locator Import Checker.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Cron;

/**
 * Class ImportStatusChecker
 * @package Totaltools\Storelocator\Cron
 */
class InventoryImportStatusChecker
{

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;
    /**
     * @var \Totaltools\Storelocator\Model\InventoryImportStatusChecker
     */
    private $resource;

    /**
     * DropInventory constructor.
     * @param \Totaltools\Storelocator\Model\InventoryImportStatusChecker $resource
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Totaltools\Storelocator\Model\InventoryImportStatusChecker $resource,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->resource = $resource;
        $this->logger = $logger;
    }

    /**
     * @return $this
     */
    public function execute()
    {
        try {
            $this->resource->execute();
        } catch (\Exception $e) {
            $this->logger->critical(__('Something went wrong with the cronjob.\\n %1', $e->getMessage()));
        }

        return $this;
    }
}
