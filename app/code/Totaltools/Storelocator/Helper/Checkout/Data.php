<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Helper\Checkout;

use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Postcodes\Model\PostCodeFactory as PostCodeFactory;

/**
 * Class Data
 * @package Totaltools\Storelocator\Helper\Checkout
 */
class Data
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $checkoutSession;

     /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * @var \Totaltools\Storelocator\Helper\Data
     */
    protected $_helper;

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    private $totaltoolsConfig;

    /**
     * @var PostCodeFactory
     */
    protected $postCodeFactory;

    /**
     * Logging instance
     * @var \Totaltools\Postcodes\Model\Logger
     */
    protected $logger;
    /**
     * Data constructor.
     * @param \Magento\Checkout\Model\Session $checkoutSession
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\ShippingZoneRepository $shippingZoneRepository,
        \Magento\Customer\Model\Session $customerSession,
        \Totaltools\Storelocator\Helper\Data $helper,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        PostCodeFactory $postCodeFactory,
        \Totaltools\Postcodes\Model\Logger $logger
    ) {
        $this->checkoutSession = $checkoutSession;
        $this->_storeRepository = $storeRepository;
        $this->_customerSession = $customerSession;
        $this->_helper = $helper;
        $this->totaltoolsConfig = $totalToolsConfig;
        $this->postCodeFactory = $postCodeFactory;
        $this->logger = $logger;
    }

    /**
     * @param int $storeId
     */
    public function setSessionStoreId(?int $storeId)
    {
        $this->checkoutSession->setStorelocatorStore($storeId);
    }

    /**
     * @return int
     */
    public function getSessionStoreId()
    {
        return $this->checkoutSession->getStorelocatorStore();
    }

    /**
     * @param int $value
     */
    public function setSessionOdItemExisted(int $value)
    {
        $this->checkoutSession->setOdItemExisted($value);
    }

    /**
     * @return int
     */
    public function getSessionOdItemExisted()
    {
        return $this->checkoutSession->getOdItemExisted();
    }

    /**
     * @param string $key
     */
    public function setStorelocatorApiKey(string $key)
    {
        $this->checkoutSession->setStorelocatorApiKey($key);
    }

    /**
     * @return string|null
     */
    public function getStorelocatorApiKey()
    {
        return $this->checkoutSession->getStorelocatorApiKey();
    }

    /**
     * @param int $value
     */
    public function setSessionOxItemExisted(int $value)
    {
        $this->checkoutSession->setOxItemExisted($value);
    }

    /**
     * @return int
     */
    public function getSessionOxItemExisted()
    {
        return $this->checkoutSession->getOxItemExisted();
    }

    /**
     * @param int $value
     */
    public function setSessionOeItemExisted(int $value)
    {
        $this->checkoutSession->setOeItemExisted($value);
    }

    /**
     * @return int
     */
    public function getSessionOeItemExisted()
    {
        return $this->checkoutSession->getOeItemExisted();
    }

    public function stockCheck($param)
    {
        $response = $stores = $fallbackStoresCollection = [];
        $postcode = (int) $param['postcode'];

        $defaultShippingMethod = $this->totaltoolsConfig->getDefaultShippingMethod();
        $method = $param['method'] ?? $defaultShippingMethod;

        $sku = $param['sku'] ?? "";

        $index = 0;

        $resultStore = $this->getByPostcode($postcode);
        if( empty($resultStore->getLatitude()) || empty($resultStore->getLongitude()) )
        {
            $resultStore = $this->_storeRepository->getByPostcodeInZone($postcode);
            $this->logger->info('Postcode exists in Database but lat long is empty: ' . $postcode);
        }
        if ($resultStore) {
            $latitude = $resultStore->getLatitude();
            $longitude = $resultStore->getLongitude();
            $nearestStoresCollection = $this->_storeRepository->getNearestStores($latitude, $longitude)->getItems();
            foreach ($nearestStoresCollection as $nearestStore) {
                $index++;
                /** @var \Totaltools\Storelocator\Model\Store $nearestStore */
                if (empty($stores[$nearestStore->getId()])) {
                    $stores[$index] = $nearestStore->getData();
                    $stores[$index]['stockMessage'] = $this->_getInventoryMessage($nearestStore->getId(), $method, $sku);
                }
            }

            $response['stores'] = $stores;
        }


        return $response;
    }

    /**
     * @param int $storeId
     * @param string $method
     * @param string $sku
     * @return mixed
     * @throws NoSuchEntityException
     * @throws \Zend_Mail_Exception
     */
    private function _getInventoryMessage(int $storeId, string $method, string $sku)
    {
        $messages = $this->_helper->getStockAvailabilityMessage($storeId, $method, $sku);

        $maxMessageCode = -1;
        $returnMessage = null;

        foreach ($messages as $message) {
            if (isset($message["code"]) && $message["code"] > $maxMessageCode) {
                $returnMessage = $message;
                $maxMessageCode = $message["code"];
            }
        }

        return $returnMessage;
    }

    /**
     * Get customer preferred store
     *
     * @return bool|Store
     */
    protected function _getCustomerPreferredStore()
    {
        $preferredStore = $this->_customerSession->getCustomerDataObject()->getCustomAttribute('preferred_store');

        if ($preferredStore) {
            try {
                $store = $this->_storeRepository->getByErpId($preferredStore->getValue());
            } catch (NoSuchEntityException $exception) {
                return false;
            }

            return $store;
        }

        return false;
    }

    /**
     * Return post code data
     *
     * @param string $postcode
     *
     * @return \Magento\Framework\DataObject|null
     */
    protected function getByPostcode($postcode)
    { 
        $collection = $this->postCodeFactory->create()->getCollection();
        $collection->addFieldToFilter('postcode', ['eq' => $postcode]);
        if($collection)
        {
            return $collection->getFirstItem();
        }   
        return false;
    }
}