<?php
/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> <<EMAIL>>
 */

namespace Totaltools\Storelocator\Helper\Shippit;

use Magento\Framework\Exception\LocalizedException;

class Api extends \Shippit\Shipping\Helper\Api
{

    public function getShippitApiUri($path, $authToken = null)
    {

        $this->_api->setHeaders(
            'Authorization',
            sprintf(
                'Bearer %s',
                $authToken
            )
        );

        return $this->getApiEndpoint() . '/' . $path;
    }

    public function callApi(
        $path,
        $requestData,
        $storeApiKey,
        $method = \Laminas\Http\Request::METHOD_POST,
        $exceptionOnResponseError = true
    ) {
        $uri = $this->getShippitApiUri($path, $storeApiKey);
        $jsonRequestData = json_encode($requestData);
        $this->log($uri, $requestData);

        $apiRequest = $this->_api
            ->setMethod($method)
            ->setUri($uri);

        if ($requestData !== null) {
            $apiRequest->setRawData($jsonRequestData);
        }

        try {
            $apiResponse = $apiRequest->request($method);
        } catch (\Exception $e) {
            if (!isset($apiResponse)) {
                $apiResponse = null;
            }

            $this->log($uri, $requestData, $apiResponse, false, 'API Request Error');

            throw new LocalizedException(
                __('Shippit_Shipping - An API Communication Error Occurred')
            );
        }

        if ($exceptionOnResponseError && $apiResponse->isError()) {
            $message = 'API Response Error' . "\n";
            $message .= 'Response: ' . $apiResponse->getStatus() . ' - ' . $apiResponse->getMessage() . "\n";

            $this->log($uri, $requestData, $apiResponse, false, $message);

            throw new LocalizedException(
                __('Shippit_Shipping - '. $message)
            );
        }

        $this->log($uri, $requestData, $apiResponse);
        $apiResponseBody = json_decode($apiResponse->getBody());

        return $apiResponseBody;
    }



    public function getShippitQuote($requestData,  $storeApiKey)
    {
        $requestData = [
            'quote' => $requestData->toArray()
        ];

        return $this->callApi('quotes', $requestData,  $storeApiKey)
            ->response;
    }

}
