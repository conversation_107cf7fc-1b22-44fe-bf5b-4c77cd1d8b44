<?php

/**
 * CommercialEmail
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */

namespace Totaltools\Storelocator\Helper;

use Magento\Framework\App\Helper\Context;
use Totaltools\Storelocator\Model\Mail\TransportBuilder;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Storelocator\Model\Store;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\Convert\Excel;
use Magento\Framework\App\Filesystem\DirectoryList;
use PhpOffice\PhpSpreadsheet\IOFactory;
use Magento\Framework\Filesystem\Io\File;
use Totaltools\Commercial\Model\AccountFactory;
use Totaltools\StoreAccount\Model\AccountFactory as StoreAccountFactory;
use Totaltools\Storelocator\Model\Zone as Zone;

/**
 * Class CommercialEmail
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Ahmed <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */

class CommercialEmail extends AbstractHelper
{
    /**
     * Configuration paths constants
     */
    const XML_PATH_STORELOCATOR_SEND_MAIL_COMMERCIAL_ACCOUNT_REQUEST_TEMPLATE = 'storelocator/send_mail/commercial_account_request';
    const XML_PATH_STORELOCATOR_SEND_MAIL_COMMERCIAL_ACCOUNT_REQUEST_TEMPLATE_CUSTOMER = 'storelocator/send_mail/customer_commercial_account_email_template';
    const XML_PATH_STORELOCATOR_SEND_MAIL_CUSTOMER_INSIDER_TO_TRADEREWARDS_EMAIL_TEMPLATE = 'storelocator/send_mail/customer_insider_to_traderewards_email_template';
    const XML_PATH_STORELOCATOR_SEND_MAIL_STORE_ACCOUNT_EMAIL_TEMPLATE = 'storelocator/send_mail/store_account_email_template';
    const XML_PATH_STORELOCATOR_SEND_MAIL_CUSTOMER_STORE_ACCOUNT_EMAIL_TEMPLATE = 'storelocator/send_mail/customer_store_account_email_template';
    const XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_TO = 'storelocator/send_mail/commercial_account_request_email_to';
    const XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_CC_TO = 'storelocator/send_mail/commercial_account_request_email_cc_to';
    const XML_PATH_STORE_ACCOUNT_EMAIL_CC_TO = 'storelocator/send_mail/store_account_email_cc_to';
    const XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_SENDER = 'storelocator/send_mail/commercial_account_request_email_sender';
    const CONFIG_TRADE_REWARD_STORE_ACCOUNT_FORM_PATH = 'storelocator/trade_reward/store_account_form_path';
    const CONFIG_TRADE_REWARD_COMMERCIAL_ACCOUNT_FORM_PATH = 'storelocator/trade_reward/commercial_account_form_path';
    const DOCTYPE = 'commercial';


    protected $transportBuilder;
    protected $storeManager;
    protected $inlineTranslation;

    /**
     * @var MessageManagerInterface
     */
    protected $messageManager;

    /**
     * @var Excel
     */
    protected $excel;

     /**
     * Additional path to folder
     *
     * @var string
     */
    protected $_path = 'export';

     /**
     * @var \Magento\Framework\Filesystem
     */
    /**
     * @var [type]
     */
    protected $_filesystem;

     /**
     * @var \Magento\Framework\Filesystem\DirectoryList
     */
    protected $directoryList;

    /**
     * @var \Magento\Framework\Filesystem\Directory\WriteInterface
     */
    protected $_directory;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    protected $file;

    protected $commercialAcountFactory;
    protected $storeAcountFactory;
    protected $countryFactory;

    /**
     * __construct
     *
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     *
     * @return void
     */
    public function __construct(
        Context $context,
        TransportBuilder $transportBuilder,
        StoreManagerInterface $storeManager,
        StateInterface $state,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\Filesystem\DirectoryList $directoryList,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        ManagerInterface $messageManager,
        File $file,
        AccountFactory $commercialAcountFactory,
        StoreAccountFactory $storeAcountFactory,
        \Magento\Directory\Model\CountryFactory $countryFactory
    ){
        $this->transportBuilder = $transportBuilder;
        $this->storeManager = $storeManager;
        $this->inlineTranslation = $state;
        $this->_filesystem = $filesystem;
        $this->messageManager = $messageManager;
        $this->_directory = $this->_filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $this->directoryList = $directoryList;
        $this->storeRepository = $storeRepository;
        $this->file = $file;
        $this->commercialAcountFactory = $commercialAcountFactory;
        $this->storeAcountFactory = $storeAcountFactory;
        $this->countryFactory = $countryFactory;
        parent::__construct($context);
    }

    public function sendEmail($post)
    {
        $templateId = $this->scopeConfig->getValue(self::XML_PATH_STORELOCATOR_SEND_MAIL_COMMERCIAL_ACCOUNT_REQUEST_TEMPLATE) ;
        $sender  = $this->scopeConfig->getValue(self::XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_SENDER);
        $toEmail = $this->scopeConfig->getValue(self::XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_TO);
        $ccTo = $this->scopeConfig->getValue(self::XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_CC_TO);
        $filePath = $this->scopeConfig->getValue(self::CONFIG_TRADE_REWARD_COMMERCIAL_ACCOUNT_FORM_PATH);
        $fileName = basename($filePath);
        $filenameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);
        $attachment = str_replace($filenameWithoutExtension, $filenameWithoutExtension . '-' . $post['abn'], $filePath);
        $this->file->cp($filePath, $attachment);
        $postcode = $post['postcode'];
        $store = $this->storeRepository->getByPostcodeInZone($postcode);
        if ($store && $store->getId() !== null) {
            $post['preferred_store'] =  $store->getData('store_name');
        } else {
            $store = $this->storeRepository->getByPostcodeInZone(Zone::DEFAULT_ZONE_POSTCODE);
            $post['preferred_store'] =  $store->getData('store_name');
        }

        $storeData = $store->getData();
        $post['store_data'] = $storeData;
        $this->editSpreadsheet($attachment, $post, self::DOCTYPE);
        $this->saveCommercialAccountData($post);
        $updatedFileName = $filenameWithoutExtension . '-' . $post['abn'] . '.xlsx';
        $templateVars = [
            'data' => $post
        ];

        $storeId = $this->storeManager->getStore()->getId();
        $this->inlineTranslation->suspend();
        $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
        $templateOptions = [
            'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
            'store' => $storeId
        ];
        $this->sendAccountFormEmailToCustomer($post, self::DOCTYPE);
        if (!empty($toEmail)) {
            $transport= $this->transportBuilder->setTemplateIdentifier($templateId, $storeScope)
                ->setTemplateOptions($templateOptions)
                ->setTemplateVars($templateVars)
                ->setFrom($sender)
                ->addTo($toEmail)
                ->addBcc($ccTo)
                ->addAttachment(file_get_contents($attachment), $updatedFileName, 'xlsx')
                ->getTransport();

            try {
                $transport->sendMessage();
                $this->inlineTranslation->resume();
                if ($this->file->fileExists($attachment)) {
                    $this->file->rm($attachment);
                }
                $this->messageManager->addSuccessMessage(__("Thank you for registering your interest for ‘National Commercial Account’. Your application has been sent to the relevant department and someone will get in touch with you shortly."));
            } catch (\Exception $e) {
                $this->_logger->info($e->getMessage());
            }
        }
    }

    public function sendStoreAccountFormEmail($post)
    {
        $templateId = $this->scopeConfig->getValue(self::XML_PATH_STORELOCATOR_SEND_MAIL_STORE_ACCOUNT_EMAIL_TEMPLATE) ;
        $sender  = $this->scopeConfig->getValue(self::XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_SENDER);
        $toEmail = "";
        $ccTo = $this->scopeConfig->getValue(self::XML_PATH_STORE_ACCOUNT_EMAIL_CC_TO);
        $filePath = $this->scopeConfig->getValue(self::CONFIG_TRADE_REWARD_STORE_ACCOUNT_FORM_PATH);
        $fileName = basename($filePath);
        $filenameWithoutExtension = pathinfo($fileName, PATHINFO_FILENAME);
        $attachment = str_replace($filenameWithoutExtension, $filenameWithoutExtension . '-' . $post['abn'], $filePath);
        $this->file->cp($filePath, $attachment);
        $preferredStore = '';
        $postcode = $post['postcode'];
        $store = $this->storeRepository->getByPostcodeInZone($postcode);
        if ($store && $store->getId() !== null) {
            $storeData = $store->getData();
            $preferredStore = $storeData['store_name'];
            $toEmail = $store->getData('store_escalations_email');
        } else {
            $store = $this->storeRepository->getByPostcodeInZone(Zone::DEFAULT_ZONE_POSTCODE);
            $storeData = $store->getData();
            $preferredStore = $storeData['store_name'];
            $toEmail = $store->getData('store_escalations_email');
        }
        $post['preferred_store'] = $preferredStore;
        $post['store_data'] = $storeData;
        $this->editSpreadsheet($attachment, $post);
        $this->saveStoreAccountData($post);
        $updatedFileName = $filenameWithoutExtension . '-' . $post['abn'] . '.xlsx';
        $templateVars = [
            'data' => $post
        ];
        $storeId = $this->storeManager->getStore()->getId();
        $this->inlineTranslation->suspend();
        $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
        $templateOptions = [
            'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
            'store' => $storeId
        ];
        $this->sendAccountFormEmailToCustomer($post, 'store');
        if (!empty($toEmail)) {
            $transport= $this->transportBuilder->setTemplateIdentifier($templateId, $storeScope)
            ->setTemplateOptions($templateOptions)
            ->setTemplateVars($templateVars)
            ->setFrom($sender)
            ->addTo($toEmail)
            ->addBcc($ccTo)
            ->addAttachment(file_get_contents($attachment), $updatedFileName, 'xlsx')
            ->getTransport();
            try {
                $transport->sendMessage();
                $this->inlineTranslation->resume();
                if ($this->file->fileExists($attachment)) {
                    $this->file->rm($attachment);
                }
                $this->messageManager->addSuccessMessage(__("Thank you for registering your interest for ‘Store Account’. Your application has been sent to the relevant department and someone will get in touch with you shortly."));
            } catch (\Exception $e) {
                $this->_logger->info($e->getMessage());
            }
        }
    }

    public function sendAccountFormEmailToCustomer($post, $formType)
    {
        if ($formType == 'store') {
            $templateId = $this->scopeConfig->getValue(self::XML_PATH_STORELOCATOR_SEND_MAIL_CUSTOMER_STORE_ACCOUNT_EMAIL_TEMPLATE);
        } elseif ($formType == 'commercial') {
            $templateId = $this->scopeConfig->getValue(self::XML_PATH_STORELOCATOR_SEND_MAIL_COMMERCIAL_ACCOUNT_REQUEST_TEMPLATE_CUSTOMER);
        }

        $sender  = $this->scopeConfig->getValue(self::XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_SENDER);
        $toEmail = $post["email"];

        $templateVars = [
            'customer_name' => $post["firstname"] . " " . $post["lastname"],
            'store_name'    => $post["store_data"]['store_name'],
            'address'    => $post["store_data"]['address'],
            'city'    => $post["store_data"]['city'],
            'state'    => $post["store_data"]['state'],
            'zipcode'    => $post["store_data"]['zipcode'],
            'phone'    => $post["store_data"]['phone'],
            'country'    => !empty($post["store_data"]['country_id']) ? $this->getCountryNameByCode($post["store_data"]['country_id']) : 'Australia'
        ];
        $storeId = $this->storeManager->getStore()->getId();
        $this->inlineTranslation->suspend();
        $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
        $templateOptions = [
            'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
            'store' => $storeId
        ];
        if (!empty($toEmail)) {
            $transport= $this->transportBuilder->setTemplateIdentifier($templateId, $storeScope)
            ->setTemplateOptions($templateOptions)
            ->setTemplateVars($templateVars)
            ->setFrom($sender)
            ->addTo($toEmail)
            ->getTransport();
            try {
                $transport->sendMessage();
                $this->inlineTranslation->resume();
            } catch (\Exception $e) {
                $this->_logger->info($e->getMessage());
            }
        }
    }

    public function getExcelFile($post = [])
    {
        $name = md5(microtime());
        $file = $this->_path . '/' . $name . '.xls';
        $convert = new \Magento\Framework\Convert\Excel(
            new \ArrayIterator($post)
        );
        $this->_directory->create($this->_path);
        $stream = $this->_directory->openFile($file, 'w+');
        $stream->lock();
        $headers = [
            'First Name' ,
            'Last Name'  ,
            'Email' ,
            'Mobile Number'  ,
            'ABN'  ,
            'Customer Company' ,
            'Estimated Spending' ,
            'Preferred Store' ,
        ];
        $convert->setDataHeader($headers);

        $convert->write($stream, 'Details');
        $stream->unlock();
        $stream->close();

        return $file;
    }

    public function editSpreadsheet($filePath, $post, $type = 'store')
    {
        // Load the spreadsheet from the given file path
        $spreadsheet = IOFactory::load($filePath);

        // Get the active sheet
        $sheet = $spreadsheet->getActiveSheet();

        // Perform your desired edits on the spreadsheet
        $sheet->setCellValue('C4', $post['firstname']);
        $sheet->setCellValue('E4', $post['lastname']);
        $sheet->setCellValue('C5', $post['email']);
        $sheet->setCellValue('E5', $post['mobile_number']);
        if ($type == self::DOCTYPE) {
            $sheet->setCellValue('C8', $post['postcode']);
        } else {
            $sheet->setCellValue('C8', $post['preferred_store']);
        }

        $sheet->setCellValue('E8', $post['customer_company']);
        $sheet->setCellValue('C9', $post['abn']);
        $sheet->setCellValue('E9', $post['estimated_spending']);

        // Define font style array
        $fontStyleArray = [
            'font' => [
                'name' => 'Arial',
                'size' => 10,
                'bold' => false,
                'italic' => false,
            ],
        ];

        // List of cells to style
        $cellsToStyle = ['C4', 'E4', 'C5', 'E5', 'C8', 'E8', 'C9', 'E9'];

        // Apply font style to each cell in the list
        foreach ($cellsToStyle as $cell) {
            $sheet->getStyle($cell)->applyFromArray($fontStyleArray);
        }

        // Save the modified spreadsheet
        $writer = IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save($filePath);
    }

    public function saveStoreAccountData($post)
    {
        $data = [
            'customer_name' => $post['firstname'] . " " . $post['lastname'],
            'email' => $post['email'],
            'phone' => $post['mobile_number'],
            'abn' => $post['abn'],
            'company_name' => $post['customer_company'],
            'estimated_monthly_spend' => $post['estimated_spending'],
            'postcode' => $post['postcode'],
            'selected_stores' => $post['preferred_store']
        ];
        $storeAcountModel = $this->storeAcountFactory->create();
        $storeAcountModel->setData($data);
        $storeAcountModel->save();
    }

    public function saveCommercialAccountData($post)
    {
        $data = [
            'customer_name' => $post['firstname'] . " " . $post['lastname'],
            'email' => $post['email'],
            'phone' => $post['mobile_number'],
            'abn' => $post['abn'],
            'company_name' => $post['customer_company'],
            'estimated_monthly_spend' => $post['estimated_spending'],
            'selected_stores' => $post['preferred_store'],
            'postcode' => $post['postcode']
        ];
        $commercialAcountModel = $this->commercialAcountFactory->create();
        $commercialAcountModel->setData($data);
        $commercialAcountModel->save();
    }

    public function getCountryNameByCode($countryCode)
    {
        $country = $this->countryFactory->create()->loadByCode($countryCode);
        return $country->getName();
    }

    public function processTradeRewardsEmail($customer)
    {
        $templateId = $this->scopeConfig->getValue(self::XML_PATH_STORELOCATOR_SEND_MAIL_CUSTOMER_INSIDER_TO_TRADEREWARDS_EMAIL_TEMPLATE);
        $sender  = $this->scopeConfig->getValue(self::XML_PATH_COMMERCIAL_ACCOUNT_REQUEST_EMAIL_SENDER);
        $toEmail = $customer->getEmail();

        $templateVars = [
            'customer_name' => $customer->getFirstname() . " " . $customer->getLastname(),
            'customer_email' => $customer->getEmail()
        ];

        $storeId = $this->storeManager->getStore()->getId();
        $this->inlineTranslation->suspend();
        $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
        $templateOptions = [
            'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
            'store' => $storeId
        ];
        if (!empty($toEmail)) {
            $transport= $this->transportBuilder->setTemplateIdentifier($templateId, $storeScope)
            ->setTemplateOptions($templateOptions)
            ->setTemplateVars($templateVars)
            ->setFrom($sender)
            ->addTo($toEmail)
            ->getTransport();
            try {
                $transport->sendMessage();
                $this->inlineTranslation->resume();
            } catch (\Exception $e) {
                $this->_logger->info($e->getMessage());
            }
        }
    }
}
