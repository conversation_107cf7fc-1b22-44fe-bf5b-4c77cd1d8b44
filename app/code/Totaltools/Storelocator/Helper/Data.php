<?php

/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Helper;

use Magento\Store\Model\ScopeInterface;
use Totaltools\Storelocator\Api\Message\MessageInterface;
use Totaltools\Storelocator\Model\Store;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;

/**
 * Class Data.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * Configuration paths constants
     */
    const XML_PATH_SERVICE_URL = 'storelocator/service/service_url';
    const XML_PATH_SERVICE_ACCESS_TOKEN = 'storelocator/service/access_token';
    const XML_PATH_TILE_MAX_ZOOM = 'storelocator/service/tile_max_zoom';
    const XML_PATH_TILE_SIZE = 'storelocator/service/tile_size';
    const XML_PATH_TILE_ZOOM_OFFSET = 'storelocator/service/tile_zoom_offset';
    const XML_PATH_TILE_SERVICE_ID = 'storelocator/service/tile_service_id';
    const SHIPPITCC_SHIPPITCC = 'shippitcc_shippitcc';
    const SHIPPIT_EXPRESS = 'shippit_Express';
    const SHIPPIT_ONDEMAND = 'shippit_ondemand';
    const OUTOFSTOCK_CART_MESSAGE_PREFIX = '<span class="atleast-one-item">At least one item in your cart is: <span>';

    /**
     * User Collection.
     *
     * @var \Magento\User\Model\ResourceModel\User\Collection
     */
    protected $_userCollection;

    /**
     * Zone Collection.
     *
     * @var \Totaltools\Storelocator\Model\ResourceModel\Zone\Collection
     */
    protected $_zoneCollection;

    /**
     * @var \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory
     */
    protected $_storeCollection;

    /**
     * Temando Zone.
     *
     * @var \Totaltools\Storelocator\Model\Zone
     */
    protected $_zone;

    /**
     * Magento Catalog Product.
     *
     * @var \Magento\Catalog\Model\Product
     */
    protected $_product;

    /**
     * Scope Config Interface.
     *
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $_scopeConfig;

    /**
     * Authorization Interface
     *
     * @var \Magento\Framework\AuthorizationInterface
     */
    protected $_authorization;

    /**
     * Country Helper.
     *
     * @var \Magento\Directory\Model\Config\Source\Country
     */
    protected $_countryHelper;

    /**
     * Logger.
     *
     * @var \Psr\Log\LoggerInterface
     */
    protected $_logger;

    /**
     * Price.
     *
     * @var \Magento\Framework\Pricing\PriceCurrencyInterface
     */
    protected $_priceCurrency;

    /**
     * Temando unit of weight measurement.
     *
     * @var \Totaltools\Storelocator\Model\System\Config\Source\Unit\Weight
     */
    protected $_weight;

    /**
     * Temando unit of distance measurement.
     *
     * @var \Totaltools\Storelocator\Model\System\Config\Source\Unit\Measure
     */
    protected $_measure;

    /**
     * Resource Connection.
     *
     * @var \Magento\Framework\App\ResourceConnection
     */
    protected $_resourceConnection;

    /**
     * Deployment Config.
     *
     * @var \Magento\Framework\App\DeploymentConfig
     */
    protected $_config;

    /**
     * Product Repository.
     *
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    protected $_productRepositoryInterface;

    /**
     * Directory List
     *
     * @var \Magento\Framework\App\Filesystem\DirectoryList
     */
    protected $_directoryList;

    /**
     * Object Manager
     *
     * @var \Magento\Framework\App\ObjectManager
     */
    protected $_objectManager;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var \Totaltools\Storelocator\Model\StoreInventoryService
     */
    protected $_storeInventoryService;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var \Magento\Eav\Api\AttributeRepositoryInterface
     */
    protected $_eavAttributeRepository;

    /**
     * @var TimezoneInterface
     */
    protected $timezoneInterface;

    protected $redirect;

    /**
     * Default currency code
     */
    const DEFAULT_CURRENCY_CODE = 'AUD';

    /**
     * Default country id
     */
    const DEFAULT_COUNTRY_ID = 'AU';

    const DEFAULT_STOCK_CHECK_RESULT = [
        'message' => "",
        'store_message' => "",
        'cc_message' => "",
        'cart_message' => "",
        'code' => 0,
        'shipping_dangerous' => 0,
        'pre_order_item' => 0,
        'dangerous_item' => 0,
        'thirty_minute_click_and_collect_available' => 0
    ];

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    private $totaltoolsConfig;

    /**
     * Data constructor.
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Totaltools\Storelocator\Model\ResourceModel\Zone\Collection $zoneCollection
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\Collection $storeCollection
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     * @param \Magento\Checkout\Model\Session $_checkoutSession
     * @param \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Magento\Eav\Api\AttributeRepositoryInterface $eavAttributeRepository
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig
     * @param TimezoneInterface $timezoneInterface
     * @param \Magento\Framework\App\Response\RedirectInterface $redirect
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Totaltools\Storelocator\Model\ResourceModel\Zone\Collection $zoneCollection,
        \Magestore\Storelocator\Model\ResourceModel\Store\Collection $storeCollection,
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        \Magento\Checkout\Model\Session $_checkoutSession,
        \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Eav\Api\AttributeRepositoryInterface $eavAttributeRepository,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        TimezoneInterface $timezoneInterface,
        \Magento\Framework\App\Response\RedirectInterface $redirect
    ) {
        $this->_zoneCollection = $zoneCollection;
        $this->_storeCollection = $storeCollection;
        $this->_resourceConnection = $resourceConnection;
        $this->_checkoutSession = $_checkoutSession;
        $this->_storeInventoryService = $storeInventoryService;
        $this->_productRepositoryInterface = $productRepository;
        $this->_storeRepository = $storeRepository;
        $this->_eavAttributeRepository = $eavAttributeRepository;
        $this->totaltoolsConfig = $totalToolsConfig;
        $this->timezoneInterface = $timezoneInterface;
        $this->redirect = $redirect;
        parent::__construct($context);
    }
    /**
     * Get zones Options Array
     *
     * @return array
     */
    public function getZonesOptionArray()
    {
        $zones = array();
        $zoneItems = $this->_zoneCollection->load();
        foreach ($zoneItems as $zone) {
            $zones[$zone->getId()] = $zone->getData('name');
        }
        return $zones;
    }

    /**
     * Get stores Options Array
     *
     * @return array
     */
    public function getStoreOptionArray()
    {
        $stores = array();
        $storeItems = $this->_storeCollection->load();

        foreach ($storeItems as $origin) {
            $stores[$origin->getId()] = $origin->getData('city');
        }

        return $stores;
    }

    /**
     * Function to check if cart has an OP or OX line
     * @return bool
     */
    public function cartHasOPOXLines()
    {
        $items = $this->_checkoutSession->getQuote()->getAllVisibleItems();

        foreach ($items as $item) {

            $product = $item->getProduct() ?: $item;

            if (!$product) {
                continue;
            }

            $isBackorderable = (bool) $product->getData(Store::IS_BACKORDERABLE);

            if (!$isBackorderable) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param $storeId
     * @param $method
     * @param $productSku
     * @param $postcode
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStockAvailabilityMessage($storeId, $method, $productSku, $postcode = "")
    {
        $showArriveByEstimate = true;
        $showExpressMessage = true;
        if (!$this->isArriveByEstimateEnabled()) {
            $showArriveByEstimate = false;
        }
        if (!$this->isExpressCheckoutOptionEnabled()) {
            $showExpressMessage = false;
        }

        $isClickAndCollectOrder = ($method == self::SHIPPITCC_SHIPPITCC) ? true : false;
        $isDeliveryOrder = !$isClickAndCollectOrder;
        $isExpressOrder = ($method == self::SHIPPIT_EXPRESS) ? true :false;
        $isOnDemandOrder = ($method == self::SHIPPIT_ONDEMAND) ? true :false;
        $isPriorityOrder = (bool) (str_contains($method ?? '', 'Priority'));

        $expressSurplusQtyBuffer = $this->getExpressSurplusQtyBuffer();
        $deliveryQtyBuffer = $this->getArriveByEstimateQtyBuffer();

        $errorReturn = [0 => ["message" => $this->getNotBackorderableProductOutOfStockDeliveryMessage(), "code" => 5, "productmessage" => $this->getNotBackorderableProductOutOfStockDeliveryMessage()]];

        $store = null;

        if ($storeId && $isClickAndCollectOrder) {
            // Store should be loaded by storeId only for click & collect. For delivery we will get the first store which can fulfil the complete order.
            $store = $this->_storeRepository->getById($storeId);
        }

        if (!$store) {
            if (!($store = $this->_storeRepository->getByPostcodeInZone($postcode))) {
                return $errorReturn;
            }
        }

        $items = $this->_checkoutSession->getQuote()->getAllVisibleItems();
        if ($productSku) {
            $item = $this->_productRepositoryInterface->get($productSku);
            $items = [$item];
        }

        if (empty($store->getErpId())) {
            $this->_logger->debug(date('Y/m/d H:i:s') . ' ' . get_class($this) . ' this origin (' . $store['name'] . ') does not have any ERP ID');
            return $errorReturn;
        }

        $erpId = $store->getErpId();

        $return = [];

        if ($isDeliveryOrder) {

            // For Delivery method we first need to find out the store which should fulfill the order.

            // Compile a list of all required quantities, delivery and express quantities
            $requiredProductQuantities = [];
            $deliveryRequiredProductQuantities = [];
            $expressRequiredProductQuantities = [];

            // First compile the required quantities.
            foreach ($items as $item) {

                $sku = $item->getSku();

                if (empty($sku)) {
                    continue;
                }

                $qty = 1;

                if ($item instanceof \Magento\Quote\Model\Quote\Item) {

                    $qty = $item->getQty();
                } else if ($item instanceof \Magento\Sales\Model\Order\Item) {

                    $qty = $item->getQtyOrdered();
                }

                $requiredProductQuantities[$sku] = $qty;
                $deliveryRequiredProductQuantities[$sku] = $qty + (int)$deliveryQtyBuffer;
                $expressRequiredProductQuantities[$sku] = $qty + (int)$expressSurplusQtyBuffer;
            }

            $store = $this->_storeInventoryService->getStoreWhichShouldFulfillTheOrder($store, $requiredProductQuantities);
            $deliveryStore = $this->_storeInventoryService->getStoreWhichShouldFulfillTheOrder($store, $deliveryRequiredProductQuantities);
            $expressStore = $this->_storeInventoryService->getStoreWhichShouldFulfillTheOrder($store, $expressRequiredProductQuantities);
        }

        // Find out the stock level for each item individually based on the allocated store.
        foreach ($items as $item) {

            $sku = $item->getSku();

            if (empty($sku)) {
                continue;
            }

            $qty = 1;

            if ($item instanceof \Magento\Quote\Model\Quote\Item) {

                $qty = $item->getQty();
            } else if ($item instanceof \Magento\Sales\Model\Order\Item) {

                $qty = $item->getQtyOrdered();
            }

            $result = self::DEFAULT_STOCK_CHECK_RESULT;

            $product = $item->getProduct() ?: $item;

            $result['url'] = $product->getProductUrl();

            if (!$product) {
                continue;
            }

            if ($showArriveByEstimate == true && $product->getAttributeText('stock_availability_code') != 'OL') {
                $showArriveByEstimate = false;
            }

            $isPreOrderItem = (bool) $product->getData(Store::PRE_ORDER_KEY);

            // For pre-order item we do not need to check the stock. Standard messages should appear.
            if ($isPreOrderItem) {

                $result['message'] = $result['productmessage'] = $isClickAndCollectOrder ? $this->getPreOrderCCMessage() : $this->getPreOrderDeliveryMessage();
                $result['cart_message'] = self::OUTOFSTOCK_CART_MESSAGE_PREFIX . $result['message'];
                $result['pre_order_item'] = 1;
                $result['code'] = 1;

                $return[$item->getId()] = $result;

                continue;
            }

            $isDangerousItem = (bool) $product->getData(Store::SHIPPING_DANGEROUS_KEY);

            // For dangerous item we do not allow delivery so no need to check the stock for delivery method.
            if ($isDangerousItem && $isDeliveryOrder) {

                $result['message'] = $result['productmessage'] = __(MessageInterface::DANGEROUS_DELIVERY_SHORT_MESSAGE);
                $result['cart_message'] = __(MessageInterface::DANGEROUS_DELIVERY_CART_MESSAGE);
                $result['dangerous_item'] = 1;
                $result['code'] = 2;

                $return[$item->getId()] = $result;

                continue;
            }

            $isKnifeCompliant = (bool) $product->getData(Store::KNIFE_COMPLIANCE_KEY);
            if ($isDeliveryOrder && $isKnifeCompliant) {
                $result['message'] = $result['productmessage'] = __(MessageInterface::KNIFE_COMPLIANCE_SHORT_MESSAGE);
                $result['cart_message'] = __(MessageInterface::KNIFE_COMPLIANCE_CART_MESSAGE);                
                $result['code'] = 6;

                $return[$item->getId()] = $result;

                continue;
            }

            $isEB = (bool) $product->getData(Store::EXCLUSIVE_PRODUCT_KEY);

            $isSpecialOrder = (bool) $product->getData(Store::IS_SPECIAL_ORDER);

            $isBackorderable = (bool) $product->getData(Store::IS_BACKORDERABLE);

            $stockExist = $this->_storeInventoryService->checkStoreInventoryForSku($store, $sku, $qty);

            if ($isClickAndCollectOrder || $isExpressOrder || $isPriorityOrder || $isOnDemandOrder || !$stockExist) {
                $showArriveByEstimate = false;
            }

            if ($stockExist && $showArriveByEstimate == true) {
                $arriveByEstimateQty = (int)$deliveryQtyBuffer + $qty;
                $arriveByEstimateStockExist = $this->_storeInventoryService->checkStoreInventoryForSku($deliveryStore, $sku, $arriveByEstimateQty);
                if (!$arriveByEstimateStockExist) {
                    $showArriveByEstimate = false;
                }
            }

            $result['product_page_express_message'] = "";
            $result['product_page_standard_message'] = "";
            if ($stockExist && $showExpressMessage == true && $isDeliveryOrder) {
                $expressQty = (int)$expressSurplusQtyBuffer + $qty;
                $expressStockExist = $this->_storeInventoryService->checkStoreInventoryForSku($expressStore, $sku, $expressQty);
                if (!$expressStockExist) {
                    $showExpressMessage = false;
                } else {
                    $result['product_page_express_message'] = $this->getExpressDeliveryMessage();
                }
            }

            if (!$isExpressOrder) {
                $showExpressMessage = false;
            }

            if ($isClickAndCollectOrder && $stockExist) {

                $result['message'] = $result['cart_message'] = $result['store_message'] = $result['productmessage'] = $this->getInStockCCMessage();
                $result['cc_message'] = __(MessageInterface::THIRTY_MINUTE_CLICK_AND_COLLECT_AVAILABLE);
                $result['thirty_minute_click_and_collect'] = 1;
                $result['code'] = 0;
            } else if ($isClickAndCollectOrder && !$stockExist) {

                $result = $this->getOutOfStockMessage($isEB, $isSpecialOrder, false, $item->getName(), $isBackorderable);
                $result['url'] = $product->getProductUrl();
            } else if (!$isClickAndCollectOrder && $stockExist) {

                $result['message'] = $result['productmessage'] = $result['cart_message'] = $result['store_message'] = $this->getInStockDeliveryMessage();
                $result['product_page_standard_message'] = $this->getStandardDeliveryMessage();
                $result['code'] = 0;
            } else {

                $result = $this->getOutOfStockMessage($isEB, $isSpecialOrder, true, $item->getName(), $isBackorderable);
                $result['url'] = $product->getProductUrl();

            }

            // Mark item dangerous/knife compliant if it is, regardless of shipping method
            $result['dangerous_item'] = $isDangerousItem ? 1 : 0;
            $result['knife_compliance_item'] = $isKnifeCompliant ? 1 : 0;
            $result['name'] = $item->getName();

            $return[$item->getId()] = $result;
        }

        if (strpos($this->redirect->getRedirectUrl(), '/checkout') !== false) {
            $arriveByEstimate = '';
            if ($showArriveByEstimate == true) {
                $arriveByEstimateDate = $this->getArriveByEstimateDate();
                $arriveByEstimate = '<span id="arrive-by-estimate">Order should arrive by '. $arriveByEstimateDate .'</span>';
                $return['arrive_by_estimate'] = $arriveByEstimate;
            } else {
                $return['arrive_by_estimate'] = $arriveByEstimate;
            }

            $expressMessage = '';
            if ($showExpressMessage == true) {
                $expressMessage = '<span id="checkout-express-message">'. $this->getCheckoutExpressMessage() .'</span>';
                $return['checkout_express_message'] = $expressMessage;
            } else {
                $return['checkout_express_message'] = $expressMessage;
            }
        }

        return $return;
    }

    /**
     * @param $isEB
     * @param $forDelivery
     * @param $productName
     * @param $isBackorderable
     * @return array
     */
    private function getOutOfStockMessage($isEB, $isSpecialOrder, $forDelivery = true, $productName = '', $isBackorderable = true)
    {
        $result = self::DEFAULT_STOCK_CHECK_RESULT;
        if (!$isBackorderable) {
            // For Not Backorderable
            $result['message'] = $forDelivery ? $this->getNotBackorderableProductOutOfStockDeliveryMessage() : $this->getNotBackorderableProductOutOfStockCCMessage();
            $result['cart_message'] = $this->getCheckoutMessage($forDelivery, $productName);
            $result['code'] = 5;
            // On checkout order is not allowed if there is an OP || OX item which is out of stock on all fallback stores.
        } elseif ($isSpecialOrder) {
            // For Special order products
            $result['message'] = $forDelivery ? $this->getSpecialOrderProductOutOfStockDeliveryMessage() : $this->getSpecialOrderProductOutOfStockCCMessage();
            $result['cart_message'] = self::OUTOFSTOCK_CART_MESSAGE_PREFIX . $result['message'];
            $result['code'] = 3;
        } elseif ($isEB) {
            // Backorderable exclusive brand
            $result['message'] = $forDelivery ? $this->getBackorderableEbProductOutOfStockDeliveryMessage() : $this->getBackorderableEbProductOutOfStockCCMessage();
            $result['cart_message'] = self::OUTOFSTOCK_CART_MESSAGE_PREFIX . $result['message'];
            $result['code'] = 2;
        } elseif (!$isEB) {
            // Backorderable Normal brands
            $result['message'] = $forDelivery ? $this->getBackorderableNormalProductOutOfStockDeliveryMessage() : $this->getBackorderableNormalProductOutOfStockCCMessage();
            $result['cart_message'] = self::OUTOFSTOCK_CART_MESSAGE_PREFIX . $result['message'];
            $result['code'] = 1;
        } else {
            $result['message'] = $forDelivery ? __(MessageInterface::UNKNOWN_STOCK_DELIVERY_SHORT_MESSAGE) : __(MessageInterface::UNKNOWN_STOCK_CC_SHORT_MESSAGE);
            $result['cart_message'] = $forDelivery ? __(MessageInterface::UNKNOWN_STOCK_DELIVERY_CART_MESSAGE) : __(MessageInterface::UNKNOWN_STOCK_CC_CART_MESSAGE);
            $result['code'] = 4;
        }

        $result['productmessage'] = $forDelivery ? $this->getNotBackorderableProductOutOfStockDeliveryMessage() : $this->getNotBackorderableProductOutOfStockCCMessage();

        return $result;
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStockAvailabilityAttributeValues()
    {
        $result = [];
        $stockAvailabilityAttribute = $this->_eavAttributeRepository->get(
            \Magento\Catalog\Api\Data\ProductAttributeInterface::ENTITY_TYPE_CODE,
            Store::STOCK_AVAILABILITY_KEY
        );
        $stockAvailabilityValues = $stockAvailabilityAttribute->getSource()->getAllOptions(false);

        foreach ($stockAvailabilityValues as $index => $stockAvailabilityValue) {
            $result[$stockAvailabilityValue['value']] = $stockAvailabilityValue['label'];
        }

        return $result;
    }

    /**
     * @return mixed
     */
    public function getNotifyMerchantTemplate()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/notify_merchant_email_template');
    }

    /**
     * @return mixed
     */
    public function getNotifyMerchantReallocateTemplate()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/reallocation_merchant_email_template');
    }

    /**
     * @return mixed
     */
    public function getEmailCopyTo()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/email_copy_to');
    }

    /**
     * @return mixed
     */
    public function getEmailCopyMethod()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/method');
    }

    /**
     * @return mixed
     */
    public function getEmailSender()
    {
        return $this->scopeConfig->getValue('sales_email/order/store_email_sender');
    }

    /**
     * @return mixed
     */
    public function getNotifyMerchantEmailSender()
    {
        return $this->scopeConfig->getValue('storelocator/send_mail/notify_merchant_email_sender');
    }

    /**
     * @return mixed
     */
    public function isAuthorityToLeaveActive()
    {
        return $this->scopeConfig->getValue('checkout/total_tools/authority_leave_active');
    }

    /**
     * @return mixed
     */
    public function getConfig($configPath)
    {
        return $this->scopeConfig->getValue($configPath);
    }

    /**
     * @return mixed
     */
    public function isArriveByEstimateEnabled()
    {
        return $this->scopeConfig->getValue('checkout/total_arrive_estimate/enabled');
    }

    /**
     * @return mixed
     */
    public function isExpressCheckoutOptionEnabled()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/enabled', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getArriveByEstimateQtyBuffer()
    {
        return $this->scopeConfig->getValue('checkout/total_arrive_estimate/minimum_qty_buffer');
    }

    /**
     * @return mixed
     */
    public function getExpressSurplusQtyBuffer()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/express_surplus_qty_buffer', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getUberSurplusQtyBuffer()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_surplus_qty_buffer', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getArriveByEstimateDays()
    {
        return $this->scopeConfig->getValue('checkout/total_arrive_estimate/arrive_by_days');
    }

    /**
     * @return mixed
     */
    public function getExpressHourOfDay()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/hour_of_day', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    public function getExpressArriveByTodayMessage()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/arrive_by_today_message', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    public function getExpressArriveByTomorrowMessage()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/arrive_by_tomorrow_message', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getExpressDeliveryMessage()
    {
        return $this->scopeConfig->getValue('delivery/message/express', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getStandardDeliveryMessage()
    {
        return $this->scopeConfig->getValue('delivery/message/standard', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return string
     */
    public function getArriveByEstimateDate()
    {
        $arriveByEstimateDays = (int)$this->getArriveByEstimateDays();

        // Get current TimeStamp according to time zone
        $currentTime = $this->timezoneInterface->scopeTimeStamp();

        if ( ((int) date('H', $currentTime)) >= 12 ) {
            $arriveByEstimateDays++;
        }

        $arriveByEstimateTimeStamp = $currentTime + (86400 * $arriveByEstimateDays);

        $arriveByEstimateDate = date('d', $arriveByEstimateTimeStamp) . " " . date('M', $arriveByEstimateTimeStamp);

        return $arriveByEstimateDate;
    }

    /**
     * @return string
     */
    public function getCheckoutExpressMessage()
    {
        $expressHour = (int)$this->getExpressHourOfDay();

        // Get current TimeStamp according to time zone
        $currentTime = $this->timezoneInterface->scopeTimeStamp();

        if ( ((int) date('G', $currentTime)) >= $expressHour ) {
            return $this->getExpressArriveByTomorrowMessage();
        } else {
            return $this->getExpressArriveByTodayMessage();
        }
    }

    public function getCheckoutMessage($forDelivery, $productName)
    {
        if ($forDelivery) {
            return __("Sorry mate, " . $productName . " is out of stock.");
        } else {
            return __("Sorry mate, " . $productName . " is out of stock at your selected store - try another below! Or swap to fast delivery for assured availability.");
        }
    }

    /**
     * @return mixed
     */
    public function getInStockCCMessage()
    {
        return $this->scopeConfig->getValue('delivery/cc_message/instock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getInStockDeliveryMessage()
    {
        return $this->scopeConfig->getValue('delivery/message/instock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getPreOrderCCMessage()
    {
        return $this->scopeConfig->getValue('delivery/cc_message/preorder', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getPreOrderDeliveryMessage()
    {
        return $this->scopeConfig->getValue('delivery/message/preorder', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

     /**
     * @return mixed
     */
    public function getSpecialOrderProductOutOfStockCCMessage()
    {
        return $this->scopeConfig->getValue('delivery/cc_message/special_order_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getSpecialOrderProductOutOfStockDeliveryMessage()
    {
        return $this->scopeConfig->getValue('delivery/message/special_order_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getBackorderableNormalProductOutOfStockCCMessage()
    {
        return $this->scopeConfig->getValue('delivery/cc_message/backorderable_normal_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getBackorderableNormalProductOutOfStockDeliveryMessage()
    {
        return $this->scopeConfig->getValue('delivery/message/backorderable_normal_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getBackorderableEbProductOutOfStockCCMessage()
    {
        return $this->scopeConfig->getValue('delivery/cc_message/backorderable_eb_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }
   
    /**
     * @return mixed
     */
    public function getBackorderableEbProductOutOfStockDeliveryMessage()
    {
        return $this->scopeConfig->getValue('delivery/message/backorderable_eb_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

     /**
     * @return mixed
     */
    public function getNotBackorderableProductOutOfStockCCMessage()
    {
        return $this->scopeConfig->getValue('delivery/cc_message/not_backorderable_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getNotBackorderableProductOutOfStockDeliveryMessage()
    {
        return $this->scopeConfig->getValue('delivery/message/not_backorderable_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    public function getGiftcardWarehouseCode()
    {
        return $this->scopeConfig->getValue('totaltools_pronto/general/giftcard_warehouse_code', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    public function getGiftcardStoreCode()
    {
        return $this->scopeConfig->getValue('totaltools_pronto/general/giftcard_store_code', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return boolean
     */
    public function getFlag($configPath, $scope = ScopeInterface::SCOPE_WEBSITE)
    {
        return $this->scopeConfig->isSetFlag($configPath, $scope);
    }
}
