<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

class StoreFallbackCommand extends Command
{
    const STORELOCATOR_ID_FIELD = 'storelocator_id';
    const SUPPORTING_ORIGINS_FIELD = 'supporting_origins';

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $_resourceConnection;

    /**
     * @var array
     */
    private $_storeIdsByOriginId = [];

    /**
     * @var \Totaltools\Storelocator\Model\StoreFallbackFactory
     */
    private $_fallbackFactory;

    /**
     * @var \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory
     */
    private $_storeCollectionFactory;

    public function __construct(
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        \Totaltools\Storelocator\Model\StoreFallbackFactory $fallbackFactory,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        string $name = null
    )
    {
        parent::__construct($name);

        $this->_resourceConnection = $resourceConnection;
        $this->_fallbackFactory = $fallbackFactory;
        $this->_storeCollectionFactory = $storeCollectionFactory;
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName('totaltools:migration:migrate-fallback-stores');
        $this->setDescription("Migrate fallback stores");
    }

    /**
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln("Fallback Stores migration is started.\n");
        $storeCollection = $this->_storeCollectionFactory->create()
            ->addFieldToFilter('supporting_origins', ['neq' => 'NULL'])
            ->addFieldToFilter('temando_origin_id', ['neq' => 0]);

        if ($storeCollection->getSize()) {
            $this->_initFallbackIdsByOriginId($storeCollection);

            foreach ($storeCollection->getData() as $data) {
                $fallbackStores = explode(',', $data[self::SUPPORTING_ORIGINS_FIELD]);

                foreach ($fallbackStores as $index => $fallbackStore) {

                    if (empty($this->_storeIdsByOriginId [$fallbackStore])) {
                        continue;
                    }

                    $fallbackModel = $this->_fallbackFactory->create();
                    $fallbackModel->setData([
                        'store_id' => $data[self::STORELOCATOR_ID_FIELD],
                        'fallback_store_id' => $this->_storeIdsByOriginId [$fallbackStore],
                    ]);
                    $fallbackModel->save();
                }
            }
        }

        $this->_resourceConnection->getConnection()->dropColumn('magestore_storelocator_store', 'temando_origin_id');
        $output->writeln("Fallback Stores migration is finished.\n");
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }

    /**
     * Get new store id by origin id
     *
     * @param $storeCollection
     */
    private function _initFallbackIdsByOriginId($storeCollection)
    {
        foreach ($storeCollection->getData() as $index => $data) {
            $this->_storeIdsByOriginId [$data['temando_origin_id']] = $data['storelocator_id'];
        }
    }
}