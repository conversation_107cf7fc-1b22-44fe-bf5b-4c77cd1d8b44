<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class TemandoMigrationCommand
 * @package Totaltools\Storelocator\Console\Command
 */
class TemandoMigrationCommand extends Command
{
    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $resourceConnection;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;

    /**
     * TemandoMigrationCommand constructor.
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param string|null $name
     */
    public function __construct(
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        string $name = null
    )
    {
        parent::__construct($name);
        $this->resourceConnection = $resourceConnection;
        $this->storeRepository = $storeRepository;
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName('totaltools:migration:migrate-temando');
        $this->setDescription("Migrate Temando");
    }

    /**
     * @inheritdoc
     */
    public function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln("Temando migration is started.\n");
        $this->_migrateZones();
        $this->_migrateInventory();
        $this->_migrateStores();
        $output->writeln("Temando migration is finished.\n");
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }

    /**
     * Temando zones migration
     *
     * Required table temando_zone
     */
    private function _migrateZones()
    {
        $this->resourceConnection->getConnection()->query('INSERT INTO magestore_storelocator_zone SELECT * FROM temando_zone');
    }

    /**
     * Inventory migration
     *
     * Required table temando_origin_inventory
     */
    private function _migrateInventory()
    {
        $this->resourceConnection->getConnection()->query('INSERT INTO magestore_storelocator_store_inventory (erp_id, sku, units)
                      SELECT erp_id, sku, units FROM temando_origin_inventory;');
    }

    /**
     * Temando stores migration
     *
     * Required table temando origin
     */
    private function _migrateStores()
    {
        $connection = $this->resourceConnection->getConnection();
        $connection->addColumn(
            \Magestore\Storelocator\Setup\InstallSchema::SCHEMA_STORE,
            'temando_origin_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER
        );
        $connection->query('INSERT INTO magestore_storelocator_store (address, baseimage_id, city, country_id, description,
                                          email, fax, link, latitude, longitude, marker_icon, meta_description, meta_keywords, meta_title,
                                          phone, rewrite_request_path, schedule_id, sort_order, state, status, store_name, zipcode, zoom_level,
                                          facebook_link, instagram_link, allow_store_collection, erp_code, erp_id, warehouse_code, zone_id,
                                          supporting_origins, state_id, temando_origin_id) 
              SELECT tem.street AS `address`, NULL AS `baseimage_id`, LOWER(tem.city) AS `city`, tem.country AS `country_id`, tem.description, tem.contact_email AS `email`,
                  IFNULL(tem.contact_fax , "") AS `fax`, NULL AS `link`, tem.latitude, tem.longitude, "" AS `marker_icon`,
                  "" AS `meta_description`, "" AS `meta_keywords`, "" AS `meta_title`, IFNULL(tem.contact_phone_1, tem.contact_phone_2) AS `phone`,
                CONCAT(LOWER(REPLACE(tem.name, "", "-")), "-", tem.origin_id) AS rewrite_request_path, NULL AS `schedule_id`, NULL AS `sort_order`,
                (SELECT dcr.default_name FROM directory_country_region dcr WHERE dcr.country_id = "AU" AND dcr.code LIKE tem.region LIMIT 1) AS `state`,
                IF(tem.is_active = 1, 1, 2) AS `status`, tem.name AS `store_name`, tem.postcode AS `zipcode`, tem.zoom_level,
                NULL AS `facebook_link`, NULL AS `instagram_link`, tem.allow_store_collection, tem.erp_code, tem.erp_id, tem.erp_code AS `warehouse_code`,
                tem.zone_id, tem.supporting_origins, 
                (SELECT dcr.region_id FROM directory_country_region dcr WHERE dcr.country_id = "AU" AND dcr.code LIKE tem.region LIMIT 1) AS `state_id`,
                tem.origin_id AS `temando_origin_id`
              FROM temando_origin tem;');

        $this->_updateStoreNames();
    }

    /**
     * Update Store city action
     */
    private function _updateStoreNames()
    {
        $storesCollection = $this->storeRepository->getCollection();

        foreach ($storesCollection->getItems() as $store) {
            $this->resourceConnection->getConnection()->query('UPDATE magestore_storelocator_store SET city = :city WHERE storelocator_id = :storelocator_id',
                [
                    'city' => ucwords($store->getCity(), "' "),
                    'storelocator_id' => $store->getId()
                ]);
        }
    }
}