<?php

namespace Totaltools\Storelocator\Console\Command;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\Storelocator\Model\SohProcessor;

class StockOnHandDeltaApi extends Command
{
    /**
     * API SOH Enabled
     */
    const XML_PATH_API_SOH_ENABLED = 'totaltools_pronto/general/enable_api_soh';

    /**
     * @var SohProcessor
     */
    private $sohProcessor;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory
     */
    private $storeCollectionFactory;

    /**
     * StockOnHand constructor.
     * @param \SohProcessor $sohProcessor
     * @param ScopeConfigInterface $scopeConfig
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory
     * @param string|null $name
     */
    public function __construct(
        SohProcessor $sohProcessor,
        ScopeConfigInterface $scopeConfig,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        $name = null
    ) {
        parent::__construct($name);
        $this->sohProcessor = $sohProcessor;
        $this->scopeConfig = $scopeConfig;
        $this->storeCollectionFactory = $storeCollectionFactory;
    }

    /**
     * configure
     *
     * @return void
     */
    protected function configure()
    {
        $this->setName('totaltools:sohapidelta:import');
        $this->setDescription("Import stock on hand from pronto API");
        parent::configure();
    }

    /**
     * execute
     *
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if ($this->isApiCronSohEnabled()) {
            $output->writeln("Pronto API SOH Import started\n");
            try {
                $storeCollection = $this->storeCollectionFactory->create();
                if ($storeCollection->getSize()) {
                    $stores = [];
                    foreach ($storeCollection->getData() as $store) {
                        $updatedRecordsCount = $this->sohProcessor->updateInventory($store);
                        if ($updatedRecordsCount > 0) {
                            $stores[] = [
                                $store['erp_id'],
                                $store['store_name'],
                                $updatedRecordsCount
                            ];
                        }
                    } 
                }
            } catch (\Exception $exception) {
                $output->writeln($exception->getMessage());
            }
            $output->writeln("\nPronto API SOH Import finished");
            return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
        } else {
            $output->writeln("Pronto API SOH Import is disabled");
            return \Magento\Framework\Console\Cli::RETURN_FAILURE;
        }
    }

    /**
     * @inheritdoc
     */
    public function isApiCronSohEnabled() : ?bool
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_API_SOH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }
}
