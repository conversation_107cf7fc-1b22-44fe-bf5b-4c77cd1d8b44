<?php
/**
 * StockOnHand
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
namespace Totaltools\Storelocator\Console\Command;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\Storelocator\Model\InventoryProcessor;

class StockOnHand extends Command
{
    /**
     * Inventory Csv SOH Enabled
     */
    const XML_PATH_INVENTORY_CSV_SOH_ENABLED = 'storelocator/inventory/enable_csv_soh';

    /**
     * @var InventoryProcessor
     */
    private $inventoryProcessor;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * StockOnHand constructor.
     * @param \InventoryProcessor $inventoryProcessor
     * @param ScopeConfigInterface $scopeConfig
     * @param string|null $name
     */
    public function __construct(
        InventoryProcessor $inventoryProcessor,
        ScopeConfigInterface $scopeConfig,
        $name = null
    ) {
        parent::__construct($name);
        $this->inventoryProcessor = $inventoryProcessor;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * configure
     *
     * @return void
     */
    protected function configure()
    {
        $this->setName('totaltools:soh:import');
        $this->setDescription("Import stock on hand");
        parent::configure();
    }

    /**
     * execute
     *
     * @inheritdoc
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if ($this->isInventoryCsvSohEnabled()) {
            $output->writeln("SOH Import started\n");
            try {
                $this->inventoryProcessor->execute();
            } catch (\Exception $exception) {
                $output->writeln($exception->getMessage());
            }
            $output->writeln("\nSOH Import finished");
            return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
        } else {
            $output->writeln("CSV SOH Import is disabled");
            return \Magento\Framework\Console\Cli::RETURN_FAILURE;
        }
    }

    /**
     * @inheritdoc
     */
    public function isInventoryCsvSohEnabled() : ?bool
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_INVENTORY_CSV_SOH_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }
}
