<?php

namespace Totaltools\Storelocator\Console\Command;

use Magento\Framework\Console\Cli;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class UrlWrite
 * @package Totaltools\Storelocator\Console\Command
 */
class UrlWrite extends Command
{
    /**
     * @var \Totaltools\Storelocator\Model\UrlRewriteProcessor
     */
    private $processor;

    /**
     * @var \Magento\Framework\App\State
     */
    private $appState;

    /**
     * Check constructor.
     *
     * @param \Totaltools\Storelocator\Model\UrlRewriteProcessor $urlRewriteProcessor
     * @param \Magento\Framework\App\State                 $state
     */
    public function __construct(
        \Totaltools\Storelocator\Model\UrlRewriteProcessor $urlRewriteProcessor,
        \Magento\Framework\App\State $state
    ) {
        $this->processor = $urlRewriteProcessor;
        $this->appState = $state;
        parent::__construct();
    }

    /**
     * Setup configure.
     */
    protected function configure()
    {
        $this->setName('totaltools:storelocator:urlrewrite');
        parent::configure();
    }

    /**
     * {@inheritdoc}
     *
     * @throws \Exception
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        return $this->appState->emulateAreaCode(
            \Magento\Framework\App\Area::AREA_ADMINHTML,
            [$this, 'process'],
            [$input, $output]
        );
    }

    /**
     * @param InputInterface  $input
     * @param OutputInterface $output
     *
     * @return int
     */
    public function process(InputInterface $input, OutputInterface $output)
    {
        try {
            $this->processor->execute($input, $output);
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());

            return Cli::RETURN_FAILURE;
        }
        return Cli::RETURN_SUCCESS;
    }
}
