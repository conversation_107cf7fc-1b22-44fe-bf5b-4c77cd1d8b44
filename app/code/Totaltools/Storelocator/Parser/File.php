<?php
/**
 * Total Tools Store Locator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Parser;

use Exception;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Totaltools\Storelocator\Model\ImportInventory\Email;
use Totaltools\Storelocator\Model\ResourceModel\InventoryProcessor;
use Magento\Framework\File\Csv;

/**
 * Class File
 * @package Totaltools\Storelocator\Parser
 */
class File
{
    const IMPORT_FILE_PATH = '%s/%s';
    const PROCESSING_DIR = 'processing';
    const PROCESSED_DIR = 'processed';
    const IMPORT_LOCK_FILENAME = 'import.lock';
    const ALLOWED_FORMATS = [
        'csv'
    ];

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var \Magento\Framework\Filesystem\Io\File
     */
    private $ioFile;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var string
     */
    private $importFilePath;

    /**
     * @var string
     */
    private $importFileMaxPartialSize;

    /**
     * @var string
     */
    private $importFileMaxFullPercentage;

    /**
     * @var array
     */
    private $processingFiles;

    /**
     * @var TimezoneInterface
     */
    private $localeDate;

    /**
     * @var Email
     */
    private $transport;

    /**
     * @var InventoryProcessor
     */
    private $resource;

    /**
     * @var Csv
     */
    private $csvProcessor;

    /**
     * @var array
     */
    public array $errorMessages;

    /**
     * @var bool
     */
    public bool $fileUploadInProgress;

    /**
     * File constructor.
     * @param \Magento\Framework\Filesystem\Io\File $ioFile
     * @param ScopeConfigInterface $scopeConfig
     * @param \Psr\Log\LoggerInterface $logger
     * @param TimezoneInterface $localeDate
     * @param Email $email
     * @param InventoryProcessor $resource
     * @param Csv $csvProcessor
     */
    public function __construct(
        \Magento\Framework\Filesystem\Io\File $ioFile,
        ScopeConfigInterface $scopeConfig,
        \Psr\Log\LoggerInterface $logger,
        TimezoneInterface $localeDate,
        Email $email,
        InventoryProcessor $resource,
        Csv $csvProcessor
    ) {
        $this->ioFile = $ioFile;
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
        $this->localeDate = $localeDate;
        $this->transport = $email;
        $this->resource =$resource;
        $this->csvProcessor = $csvProcessor;
        $this->errorMessages = [];
    }

    /**
     * Returns absolute file path.
     *
     * @param array $fileParts
     * @return string
     */
    public function getFilePath($fileParts)
    {
        return sprintf(
            self::IMPORT_FILE_PATH,
            $fileParts[0],
            $fileParts[1]
        );
    }

    /**
     * Get processed directory file path.
     *
     * @return string
     */
    public function getProcessedDir()
    {
        return sprintf(
            self::IMPORT_FILE_PATH,
            $this->importFilePath,
            self::PROCESSED_DIR
        );
    }

    /**
     * Get processing directory file path.
     *
     * @return string
     */
    public function getProcessingDir()
    {
        return sprintf(
            self::IMPORT_FILE_PATH,
            $this->importFilePath,
            self::PROCESSING_DIR
        );
    }

    /**
     * Returns processing grep files.
     *
     * @return array|bool
     */
    public function getProcessingFiles()
    {
        $this->importFilePath = $this->scopeConfig->getValue(
            \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_DIRECTORY,
            ScopeConfigInterface::SCOPE_TYPE_DEFAULT
        );

        if (!$this->isImportFilePathExists()) {
            $this->errorMessages[] = 'Import Filepath does not exist in core_config.';
            return false;
        }

        $this->ioFile->open(['path' => $this->importFilePath]);
        $lockFile = self::IMPORT_LOCK_FILENAME;

        if ($this->ioFile->fileExists($lockFile)) {
            $this->logger->warning(__('Import process is locked. Please remove the lock file then try again.'));
            $this->errorMessages[] = 'Import process is locked. Please remove the lock file then try again.';
            return false;
        }

        try {
            $this->createFolders();
            $this->deleteOldImports();
            $lockMessage = __('Storelocator inventory import started at %1', [date('Y/m/d H:i:s')]);
            $this->ioFile->write(self::IMPORT_LOCK_FILENAME, $lockMessage->__toString(), 0755);
            $this->moveFilesToProcessingDir();
            $this->ioFile->cd($this->getProcessingDir());
            $this->processingFiles = $this->ioFile->ls();
            $this->ioFile->cd($this->importFilePath);
        } catch (Exception $e) {
            $this->logger->warning($e->getMessage());
            $this->errorMessages[] = 'Error on getProcessingFiles: '.$e->getMessage();
            return false;
        }

        return $this->processingFiles;
    }

    /**
     * Create folders action.
     */
    public function createFolders()
    {
        $this->ioFile->mkdir(self::PROCESSING_DIR);
        $this->ioFile->mkdir(self::PROCESSED_DIR);

        return $this;
    }

    /**
     * Validate the file being processed based off of the rules in TOT0000-2972.
     *
     * return bool
     */
    public function validateFile($file)
    {
        $filepath = $this->importFilePath.'/'.self::PROCESSING_DIR.'/'.$file;
        $this->importFileMaxPartialSize = $this->scopeConfig->getValue(
            \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_MAX_PARTIAL_SIZE,
            ScopeConfigInterface::SCOPE_TYPE_DEFAULT
        );
        $this->importFileMaxFullPercentage = $this->scopeConfig->getValue(
            \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_MAX_FULL_PERCENTAGE,
            ScopeConfigInterface::SCOPE_TYPE_DEFAULT
        );
        if(strpos($filepath, 'full') === false){
            if ($this->_getFileSizeInKilobytes($filepath) > $this->importFileMaxPartialSize) {
                $this->errorMessages[] = 'Import Partial: filesize ('.$this->_getFileSizeInKilobytes($filepath).' kb) is greater than the Maximum Partial filesize ('.$this->importFileMaxPartialSize.' kb) allowed. Please fix and re-upload.';
                $this->moveProcessedFile($filepath);
                return false;
            }
        } else {
            //adding a processing buffer of 60 sec to the full file.
            // Trying to stop files which are still being uploaded from being processed by checking the modified time
            $timeDiff = (time() - filemtime($filepath));
            $this->fileUploadInProgress = false;
            if($timeDiff < 60) {
                $this->fileUploadInProgress = true;
                $this->errorMessages[] = 'Import Full: '.$this->importFileMaxFullPercentage.'% Disparity threshold reached. File is currently uploading.';
                return false;
            }
            //standard validation
            if (!$this->_isFilesizePercentageBoundaryValid($filepath)) {
                $this->errorMessages[] = 'Import Full: '.$this->importFileMaxFullPercentage.'% Disparity threshold reached. line count ('.$this->_getLines($filepath).') and DB count ('.$this->resource->getInventoryRowCount().') have diverged more than '.$this->importFileMaxFullPercentage.'%. Please fix and re-upload.';
                $this->moveProcessedFile($filepath);
                return false;
            }
        }
        return true;
    }

    /**
     * Get File Size In Kilobytes
     *
     * @return int
     */
    private function _getFileSizeInKilobytes($filepath)
    {
        return (int) round(filesize($filepath) / 1024, 2);
    }

    /**
     * return validation result of Filesize Boundary, Based On Percentage
     *
     * @return bool
     */
    private function _isFilesizePercentageBoundaryValid($filepath)
    {
        $numberOfEntriesInFullFile = $this->_getLines($filepath);
        $numberOfEntriesInTable = $this->resource->getInventoryRowCount();

        if($numberOfEntriesInFullFile == $numberOfEntriesInTable) {
            return true;
        } elseif($numberOfEntriesInFullFile > $numberOfEntriesInTable) {
            $lowerBoundary = $numberOfEntriesInTable;
            $upperBoundary = $numberOfEntriesInFullFile;
        } else {
            $upperBoundary = $numberOfEntriesInTable;
            $lowerBoundary = $numberOfEntriesInFullFile;
        }
        $currentDifference = number_format($this->_getPercentageDifference($upperBoundary, $lowerBoundary), 2, '.', '');

        if ( $currentDifference > $this->importFileMaxFullPercentage) {
            return false;
        }
        return true;
    }

    /**
     * return Percentage difference
     *
     * @return int
     */
    private function _getPercentageDifference($maximum, $value, $minimum = 0)
    {
        $tempMax = $maximum - $minimum;
        $tempValue = $value - $minimum;

        return 100 - (($tempValue / $tempMax) * 100);
    }

    /**
     * get file line count
     *
     * @return int
     */
    private function _getLines($file)
    {
        $f = fopen($file, 'rb');
        $lines = 0;
        while (!feof($f)) {
            $lines += substr_count(fread($f, 8192), "\n");
        }
        fclose($f);

        return (int)$lines;
    }

    /**
     * Check if the partial import file path exists
     *
     * @return bool
     */
    private function isPartialFileValid()
    {
        return $this->ioFile->fileExists($this->importFilePath, false);
    }

    /**
     * @throws Exception
     */
    public function deleteOldImports()
    {
        $this->ioFile->cd( $this->getProcessedDir());
        $files = $this->ioFile->ls(\Magento\Framework\Filesystem\Io\File::GREP_FILES);
        $keepFilesConfigValue = $this->scopeConfig->getValue(
            \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_DAYS,
            ScopeConfigInterface::SCOPE_TYPE_DEFAULT
        );

        $oldFileTime = 3600 * 24 * $keepFilesConfigValue;

        foreach ($files as $file) {
            if (!$this->isValidFormat($file)) {
                if ($this->isDebug()) {
                    $this->logger->info(__('%1 is not a csv file', $file['text']));
                }
            }

            $isOld = time() - strtotime($file['mod_date']) >= $oldFileTime;

            if ($isOld) {
                if ($this->isDebug()) {
                    $this->logger->info(__('%1 is deleted because it is old enough', $file['text']));
                }
                $this->ioFile->rm($file['text']);
            }
        }

        $this->ioFile->cd($this->importFilePath);

        return $this;
    }

    /**
     * Move processed file to processed directory.
     *
     * @param string $fileName
     * @return $this
     */
    public function moveProcessedFile(string $fileName)
    {
        $destinationFilePath = $this->getFilePath([$this->getProcessedDir(), $fileName]);
        $sourceFilePath = $this->getFilePath([$this->getProcessingDir(), $fileName]);
        $this->ioFile->mv($sourceFilePath, $destinationFilePath);

        return $this;
    }

    /**
     * @return $this
     * @throws Exception
     */
    public function moveFilesToProcessingDir()
    {
        $startTime = $this->localeDate->date()->format('Y-m-d H:i:s');

        $inventoryImportFiles = $this->ioFile->ls(\Magento\Framework\Filesystem\Io\File::GREP_FILES);

        foreach ($inventoryImportFiles as $index => $inventoryImportFile) {
            if ($this->isValidFormat($inventoryImportFile)) {
                $destinationFile = $this->getFilePath([$this->getProcessingDir(), $inventoryImportFile['text']]);
                $sourceFilePath = $this->getFilePath([$this->importFilePath, $inventoryImportFile['text']]);
                try {
                    $this->ioFile->mv($sourceFilePath, $destinationFile);
                    if (!$this->ioFile->fileExists($destinationFile)) {
                        $this->transport->sendEmail('Error when moving file to Processing directory', $sourceFilePath);
                        $this->errorMessages[] = 'Error moving file to Processing directory.';
                    }
                } catch (Exception $exception) {
                    $this->logger->error($exception->getMessage());
                    $this->transport->sendEmail($exception->getMessage(), $sourceFilePath);
                    $this->errorMessages[] = 'Error moving file to Processing directory.';
                }
            }
        }

        return $this;
    }

    /**
     * Check if import file path exists
     *
     * @return bool
     */
    private function isImportFilePathExists()
    {
        return $this->ioFile->fileExists($this->importFilePath, false);
    }

    /**
     * Check if files is in valid format.
     *
     * @param array $fileData
     * @return bool
     */
    private function isValidFormat($fileData)
    {
        if (!empty($fileData['filetype'])
            && (in_array($fileData['filetype'] , self::ALLOWED_FORMATS)
                || (strpos($fileData['text'], '.csv') !== false
                && strpos($fileData['text'], '.filepart') === false))
        ) {
            return true;
        }

        return false;
    }

    /**
     * Check if debug enabled.
     *
     * @return mixed
     */
    public function isDebug()
    {
        return $this->scopeConfig->getValue(
        \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_LOG,
        ScopeConfigInterface::SCOPE_TYPE_DEFAULT
        );
    }

    /**
     * Delete lock file
     *
     * @return bool
     */
    public function deleteLockFile()
    {
        return $this->ioFile->rm(self::IMPORT_LOCK_FILENAME);
    }

    /**
     * Logs API response for SOH in CSV format.
     *
     * @param string $erpId
     * @param string $apiRequestType
     * @param array|string $response
     * @return void
     */
    public function createSohApiResponseLogs($erpId, $apiRequestType, $response)
    {
        $sohApiLogDir = $this->scopeConfig->getValue(
            \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_API_LOG_DIRECTORY,
            ScopeConfigInterface::SCOPE_TYPE_DEFAULT
        );

        $currentDate = date('d-m-Y');
        $sohApiLogDir = $sohApiLogDir . DIRECTORY_SEPARATOR . $currentDate;
        if (!$this->ioFile->fileExists($sohApiLogDir, false)) {
            $this->ioFile->mkdir($sohApiLogDir);
        }

        $filename = sprintf(
            '%s_%s_%s.csv',
            date("d-m-y-h-i-s"),
            ($apiRequestType == 'full_soh') ? 'fullsoh' : 'delta',
            $erpId
        );
        
        $filePath = $sohApiLogDir . DIRECTORY_SEPARATOR . $filename;

        if ($this->ioFile->fileExists($sohApiLogDir, false)) {
            $data = is_array($response) ? $this->getSohApiResonseItems($erpId, $response) : [[$response]];
            $this->csvProcessor->saveData($filePath, $data);
        }
    }

    /**
     * Extracts items from API response.
     *
     * @param string $erpId
     * @param array $response
     * @return array
     */
    private function getSohApiResonseItems($erpId, $response)
    {
        $data = [];
        if (!empty($response['Items']['Item'])) {
            $items = $response['Items']['Item'];
            // check for single result
            $items = isset($items['ItemCode']) ? [$items] : $items;
            foreach ($items as $item) {
                $prontoErpId = $item['Warehouse'];
                if ($prontoErpId == $erpId) {
                    $data[] = [$item['ItemCode'], $item['SOH']];
                }
            }
        } 
        return $data;
    }
}
