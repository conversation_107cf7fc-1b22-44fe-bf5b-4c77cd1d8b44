<?php
/**
 * Total Tools Store Locator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Parser;

use Goodby\CSV\Import\Standard\Interpreter;
use Goodby\CSV\Import\Standard\LexerConfig;
use Goodby\CSV\Import\Standard\LexerFactory;
use Totaltools\Storelocator\Model\ResourceModel\InventoryProcessor;
use Zend_Db_Exception;

/**
 * Class Parser
 * @package Totaltools\Storelocator\Parser
 */
class Parser
{
    /**
     * @var LexerFactory
     */
    private LexerFactory $lexerFactory;

    /**
     * @var LexerConfig
     */
    private LexerConfig $lexerConfig;

    /**
     * @var Interpreter
     */
    private Interpreter $interpreter;

    /**
     * @var ItemProcessorInterface
     */
    private ItemProcessorInterface $itemProcessor;

    /**
     * @var InventoryProcessor
     */
    private InventoryProcessor $resource;

    /**
     * @var boolean
     */
    private bool $isFull;

    /**
     * @var array
     */
    public array $errorMessages;

    /**
     * @var array
     */
    public array $successCountGroupedByErpId;

    /**
     * Parser constructor.
     * @param LexerFactory $lexerFactory
     * @param LexerConfig $lexerConfig
     * @param Interpreter $interpreter
     * @param ItemProcessorInterface $itemProcessor
     * @param InventoryProcessor $resource
     */
    public function __construct(
        LexerFactory $lexerFactory,
        LexerConfig $lexerConfig,
        Interpreter $interpreter,
        ItemProcessorInterface $itemProcessor,
        InventoryProcessor $resource
    ) {
        $this->lexerFactory = $lexerFactory;
        $this->lexerConfig = $lexerConfig;
        $this->interpreter = $interpreter;
        $this->itemProcessor = $itemProcessor;
        $this->resource =$resource;
        $this->errorMessages = [];
        $this->successCountGroupedByErpId = [];
    }

    /**
     * @param bool $value
     * @return $this
     */
    public function setIsFull($value)
    {
        $this->isFull = $value;

        return $this;
    }

    /**
     * @return boolean
     */
    public function getIsFull()
    {
        return $this->isFull;
    }

    /**
     * Parse the CSV file and process line by line.
     *
     * @param string $filePath
     * @return $this
     * @throws Zend_Db_Exception
     */
    public function parse($filePath)
    {
        $this->errorMessages = [];
        if ($this->isFull === true) {
            $this->successCountGroupedByErpId = [];
            $this->resource->createTemporaryTables();
            if($this->resource->insertAllInventoryData($filePath) === false) {
                $this->errorMessages[] = 'LOAD DATA LOCAL INFILE has failed for the full import process.';
            }
            $this->successCountGroupedByErpId = $this->resource->getTmpInventoryRowCountByErp();
        }

        if ($this->isFull === false) {
            $inventoryData = array();
            /** @var \Goodby\CSV\Import\Standard\Lexer $lexer */
            $lexer = $this->lexerFactory->create([$this->lexerConfig]);
            $this->interpreter->addObserver(function(array $row) use (&$inventoryData) {

                $stock = $this->itemProcessor->process($row);
                if($stock && is_array($stock) && count($stock) > 0) {
                    $inventoryData[] = $stock;
                }
            });

            $lexer->parse($filePath, $this->interpreter);
            $res = $this->resource->insertInventoryData($inventoryData);
            if($res == false)
                $this->errorMessages[] = 'InsertOnDuplicate has failed for partial import. No data found.';
        }

        if ($this->isFull === true && count($this->successCountGroupedByErpId) > 0) {
            $this->resource->renameTables();
        }

        return $this;
    }
}
