<?php
/**
 * Total Tools Store Locator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Parser;

/**
 * Class ItemProcessor
 * @package Totaltools\Storelocator\Parser
 */
class ItemProcessor implements ItemProcessorInterface
{
    /**
     * @var File
     */
    private $csvFile;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var \Totaltools\Storelocator\Model\ResourceModel\InventoryProcessor
     */
    private $resource;

    /**
     * ItemProcessor constructor.
     * @param File $csvFile
     * @param \Totaltools\Storelocator\Model\ResourceModel\InventoryProcessor $resource
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        File $csvFile,
        \Totaltools\Storelocator\Model\ResourceModel\InventoryProcessor $resource,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->csvFile = $csvFile;
        $this->resource = $resource;
        $this->logger = $logger;
    }

    /**
     * {@inheritdoc}
     */
    public function process(array $data)
    {
        $erpId = !empty(trim($data[0])) ? trim($data[0]) : false;
        $sku = !empty(trim($data[1])) ? trim($data[1]) : false;

        if (!$erpId || !$sku) {
            return [];
        }

        $inventoryData = [
            'erp_id' => $data[0],
            'sku' => $data[1],
            'units' => !empty($data[2]) ? $data[2] : 0,
        ];

        if ($this->csvFile->isDebug()) {
            $this->logger->info(__('%1 inventory updated.', $sku));
        }

        return $inventoryData;
    }
}
