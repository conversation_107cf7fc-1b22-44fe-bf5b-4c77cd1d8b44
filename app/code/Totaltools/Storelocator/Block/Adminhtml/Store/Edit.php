<?php

namespace Totaltools\Storelocator\Block\Adminhtml\Store;


class Edit extends \Magestore\Storelocator\Block\Adminhtml\Store\Edit
{
    
    protected function _construct()
    {
        parent::_construct();
        $storelocator_id = $this->getRequest()->getParam('storelocator_id');
        $this->buttonList->add(
            'import-full-soh',
            [
                'label' => __('Import Full SOH'),
                'class' => 'fullsoh',
                'onclick' => 'window.location.href=\'' . $this->getUrl('totaltoolsadmin/store/fullsoh', ['storelocator_id' => $storelocator_id]) . '\'',
            ]
        );

        $this->buttonList->add(
            'import-delta-soh',
            [
                'label' => __('Import Delta SOH'),
                'class' => 'deltasoh',
                'onclick' => 'window.location.href=\'' . $this->getUrl('totaltoolsadmin/store/deltasoh', ['storelocator_id' => $storelocator_id]) . '\'',
            ]
        );

    }
}
