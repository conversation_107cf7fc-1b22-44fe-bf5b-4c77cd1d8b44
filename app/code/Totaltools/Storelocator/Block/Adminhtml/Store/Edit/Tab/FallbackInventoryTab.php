<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Block\Adminhtml\Store\Edit\Tab;

/**
 * Class FallbackInventoryTab.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

class FallbackInventoryTab extends \Magento\Backend\Block\Widget\Form\Generic implements \Magento\Backend\Block\Widget\Tab\TabInterface
{
    /**
     * @var \Totaltools\Storelocator\Model\Config\Source\Store
     */
    protected $_storeConfig;

    /**
     * @var \Totaltools\Storelocator\Model\StoreInventoryService
     */
    protected $_inventoryService;

    /**
     * @var \Totaltools\Storelocator\Model\StoreFallbackRepository
     */
    protected $_fallbackRepository;

    /**
     * @var \Magento\Backend\Model\UrlInterface
     */
    protected $_backendUrl;


    /**
     * FallbackInventoryTab constructor.
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Data\FormFactory $formFactory
     * @param \Totaltools\Storelocator\Model\StoreFallbackRepository $fallbackRepository
     * @param \Totaltools\Storelocator\Model\StoreInventoryService $inventoryService
     * @param \Totaltools\Storelocator\Model\Config\Source\Store $storeConfig
     * @param array $data
     */
    public function __construct(

        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Totaltools\Storelocator\Model\StoreFallbackRepository $fallbackRepository,
        \Totaltools\Storelocator\Model\StoreInventoryService $inventoryService,
        \Totaltools\Storelocator\Model\Config\Source\Store $storeConfig,
        \Magento\Backend\Model\UrlFactory $backendUrlFactory,
        array $data = []
    ) {
        $this->_storeConfig = $storeConfig;
        $this->_inventoryService = $inventoryService;
        $this->_fallbackRepository = $fallbackRepository;
        $this->_backendUrl = $backendUrlFactory->create();
        parent::__construct($context, $registry, $formFactory, $data);
    }

    protected function _prepareLayout()
    {
        $this->setTemplate('Totaltools_Storelocator::store/edit/tab/fallback_inventory.phtml');
        return parent::_prepareLayout();
    }

    // Fetch stores for dropdown
    public function getStores()
    {
        return $this->_storeConfig->getAllOptions(true, true);
    }

    // Fetch fallback stores
    public function getFallbackStores()
    {
        $model = $this->getRegistryModel();
        $selectedStores = [];

        if ($model->getId()) {
            $fallbackStores = $this->_fallbackRepository->getFallbackStores($model->getId());
            $stores = $this->getStores();
            foreach ($fallbackStores as $store) {
                $fallbackStoreId = $store->getFallbackStoreId();
                $key = array_search($fallbackStoreId, array_column($stores, 'value'));
                $selectedStores[]=['value' => $fallbackStoreId, 'label' => $stores[$key]['label'] ];
            }
        }
        return $selectedStores;
    }

    public function getStates()
    {
        return $this->_storeConfig->getAllRegions("AU");
    }
    

    /**
     * Fetch selected super fallback store
     *
     * @return \Magestore\Storelocator\Model\Store
     */
    public function getSelectedSuperStoreId()
    {
        return $this->getRegistryModel()->getSuperStoreId();
    }

     /**
     * Get registry model.
     *
     * @return \Magestore\Storelocator\Model\Store
     */
    public function getRegistryModel()
    {
        return $this->_coreRegistry->registry('storelocator_store');
    }

    /**
     * Return Tab label
     *
     * @return string
     * @api
     */
    public function getTabLabel()
    {
        return __('Fallback Stores & Inventory');
    }

    /**
     * Return Tab title
     *
     * @return string
     * @api
     */
    public function getTabTitle()
    {
        return __('Fallback Stores & Inventory');
    }

     /**
     * Can show tab in tabs
     *
     * @return boolean
     * @api
     */
    public function canShowTab()
    {
        return true;
    }

    /**
     * Tab is hidden
     *
     * @return boolean
     * @api
     */
    public function isHidden()
    {
        return false;
    }

    public function getInventory()
    {
        $model = $this->getRegistryModel();
        $inventoryStr = [];
        if ($model->hasData('erp_id')) {
            $inventoryList = $this->_inventoryService->getStoreInventoryByErpIds($model->getErpId());
            /*** @var \Totaltools\Storelocator\Api\Data\StoreInventoryInterface $_inventory */
            foreach ($inventoryList->getItems() as $_inventory) {
                $inventoryStr[] = $_inventory->getSKU() . ',' . $_inventory->getUnits();
            }
            return implode("\n", $inventoryStr);
        }

        
        return '';
    }

    public function getAjaxSubmitUrl()
    {
        return $this->_backendUrl->getUrl('totaltoolsadmin/store/distance');
    }
}
    