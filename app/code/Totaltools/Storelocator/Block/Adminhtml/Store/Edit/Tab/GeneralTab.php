<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Block\Adminhtml\Store\Edit\Tab;

use Totaltools\Storelocator\Model\Config\Source\IsActive;

/**
 * Class GeneralTab.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class GeneralTab extends \Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GeneralTab
{
    /**
     * Is Active Source.
     *
     * @var IsActive
     */
    protected $_isActive;
    /**
     * @var \Totaltools\Storelocator\Helper\Data
     */
    protected $_helper;

    /**
     * GeneralTab constructor.
     *
     * @param \Magento\Backend\Block\Template\Context              $context
     * @param \Magento\Framework\Registry                          $registry
     * @param \Magento\Framework\Data\FormFactory                  $formFactory
     * @param IsActive $isActive
     * @param \Totaltools\Storelocator\Helper\Data                  $helper
     * @param array                                                $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        IsActive $isActive,
        \Totaltools\Storelocator\Helper\Data $helper,
        array $data = []
    ) {
        $this->_isActive = $isActive;
        $this->_helper = $helper;

        parent::__construct($context, $registry, $formFactory, $data);
    }

    /**
     * Prepare form.
     *
     * @return \Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GeneralTab
     */
    protected function _prepareForm()
    {
        $formResult = parent::_prepareForm();

        $model = $this->getRegistryModel();
        $form = $this->getForm();

        $fieldset = $form->getElement('general_fieldset');

        $fieldset->addField(
            'allow_store_collection',
            'select',
            [
                'name' => 'allow_store_collection',
                'label' => __('Allow Store Collection'),
                'title' => __('Allow Store Collection'),
                'required' => true,
                'options' => $this->_isActive->toOptionArray(true)
            ],
            'status'
        );

        $fieldset->addField(
            'allow_delivery_orders',
            'select',
            [
                'name' => 'allow_delivery_orders',
                'label' => __('Allow Delivery Orders'),
                'title' => __('Allow Delivery Orders'),
                'required' => true,
                'options' => $this->_isActive->toOptionArray(true)
            ],
            'allow_store_collection'
        );

        $fieldset->addField(
            'allow_uber_orders',
            'select',
            [
                'name' => 'allow_uber_orders',
                'label' => __('Allow Uber Orders'),
                'title' => __('Allow Uber Orders'),
                'required' => true,
                'options' => $this->_isActive->toOptionArray(true)
            ],
            'allow_delivery_orders'
        );

        $fieldset->addField(
            'is_visible',
            'select',
            [
                'name' => 'is_visible',
                'label' => __('Is Visible'),
                'title' => __('Is Visible'),
                'required' => true,
                'options' => $this->_isActive->toOptionArray(true)
            ],
            'allow_uber_orders'
        );

        $fieldset->addField(
            'erp_id',
            'text',
            [
                'name' => 'erp_id',
                'label' => __('ERP ID'),
                'title' => __('ERP ID'),
                'required' => true,
                'note' => __('Used for synchronising inventory data from third party system.')
            ]
        );
        $fieldset->addField(
            'erp_code',
            'text',
            [
                'name' => 'erp_code',
                'label' => __('ERP Code'),
                'title' => __('ERP Code'),
                'required' => true,
                'note' => __('Passed into third party system.')
            ]
        );
        $fieldset->addField(
            'warehouse_code',
            'text',
            [
                'name' => 'warehouse_code',
                'label' => __('Warehouse Code'),
                'title' => __('Warehouse Code'),
                'required' => true
            ]
        );
        $fieldset->addField(
            'zone_id',
            'select',
            [
                'name' => 'zone_id',
                'label' => __('Zone'),
                'title' => __('Zone'),
                'required' => false,
                'options' => $this->_helper->getZonesOptionArray()
            ]
        );
        $fieldset->addField(
            'full_soh_api_endpoint',
            'text',
            [
                'name' => 'full_soh_api_endpoint',
                'label' => __('Full SOH API Endpoint'),
                'title' => __('Full SOH API Endpoint'),
                'required' => false
            ]
        );
        $fieldset->addField(
            'delta_soh_api_endpoint',
            'text',
            [
                'name' => 'delta_soh_api_endpoint',
                'label' => __('Delta SOH API Endpoint'),
                'title' => __('Delta SOH API Endpoint'),
                'required' => false
            ]
        );

        $fieldset->addField(
            'structure',
            'select',
            [
                'name' => 'structure',
                'label' => __('Structure'),
                'title' => __('Structure'),
                'required' => false,
                'options' => [
                    'franchise' => 'Franchise',
                    'jv'        => 'JV'
                ]
            ]
        );

        $fieldset->addField(
            'franchisee_group',
            'text',
            [
                'name' => 'franchisee_group',
                'label' => __('Franchisee Group'),
                'title' => __('Franchisee Group'),
                'required' => false
            ]
        );

        $fieldset->addField(
            'location',
            'select',
            [
                'name' => 'location',
                'label' => __('Location'),
                'title' => __('Location'),
                'required' => false,
                'options' => [
                    'capital'   => 'Capital',
                    'metro'     => 'Metro',
                    'remote'    => 'Remote'
                ]
            ]
        );

        $fielsetContact = $form->getElement('contact_fieldset');
        $fielsetContact->addField(
            'store_admin_email',
            'text',
            [
                'name' => 'store_admin_email',
                'label' => __('Store Admin Email'),
                'title' => __('Store Admin Email'),
                'required' => true
            ]
        );

        $fielsetContact->addField(
            'commercial_store_admin_email',
            'text',
            [
                'name' => 'commercial_store_admin_email',
                'label' => __('B2B Store Admin Email'),
                'title' => __('B2B Store Admin Email'),
                'required' => true
            ]
        );

        $fielsetContact->addField(
            'store_escalations_email',
            'text',
            [
                'name' => 'store_escalations_email',
                'label' => __('Store Escalations Email'),
                'title' => __('Store Escalations Email'),
                'required' => true
            ]
        );

        /*
         * Uber Information Field Set
         */
        $fieldset = $form->addFieldset(
            'uber_fieldset',
            [
                'legend' => __('Uber Information'),
                'collapsable' => true,
            ]
        );

        $fieldset->addField(
            'uber_start_at',
            'text',
            [
                'name' => 'uber_start_at',
                'label' => __('Uber Time Slot Start At'),
                'title' => __('Uber Time Slot Start At'),
                'required' => true,
                'note' => __('Hour of the day. 0 to 23')
            ]
        );

        $fieldset->addField(
            'uber_end_at',
            'text',
            [
                'name' => 'uber_end_at',
                'label' => __('Uber Time Slot End At'),
                'title' => __('Uber Time Slot End At'),
                'required' => true,
                'note' => __('Hour of the day. 0 to 23')
            ]
        );

        $fieldset->addField(
            'uber_available_days',
            'text',
            [
                'name' => 'uber_available_days',
                'label' => __('Uber Available on Days of the Week'),
                'title' => __('Uber Available on Days of the Week'),
                'required' => true,
                'note' => __('Comma-separated numbers representing days of the week, starting from 0 (Sunday) to 6 (Saturday).')
            ]
        );

        $form->setValues($model->getData());
        $this->setForm($form);

        return $formResult;
    }
}