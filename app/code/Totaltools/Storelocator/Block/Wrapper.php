<?php

/**
 * <AUTHOR> Internet
 * @package  Totaltools_Storelocator
 */

namespace Totaltools\Storelocator\Block;

use Totaltools\Storelocator\Helper\Data as Helper;

class Wrapper extends \Magestore\Storelocator\Block\Wrapper
{
    /**
     * @var  string
     */
    protected $_template = 'Totaltools_Storelocator::wrapper.phtml';

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var \Totaltools\Storelocator\Helper\Data
     */
    protected $_helper;

    /**
     * @var  \Magento\Framework\App\Request\Http
     */
    protected $request;

    /**
     * @param \Magestore\Storelocator\Block\Context $context
     * @param \Totaltools\Storelocator\Model\StoreRepository $_storeRepository
     * @param \Totaltools\Storelocator\Helper\Data $_helper
     * @param array $data
     */
    public function __construct(
        \Magestore\Storelocator\Block\Context $context,
        \Totaltools\Storelocator\Model\StoreRepository $_storeRepository,
        \Totaltools\Storelocator\Helper\Data $_helper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->request = $context->getRequest();
        $this->_storeRepository = $_storeRepository;
        $this->_helper = $_helper;
    }

    /**
     * @return  string[]
     */
    public function getSearchParams()
    {
        $params = [];

        if ($state = $this->request->getParam('state')) {
            $params['state'] = strtoupper($state);
        }

        if ($suburb = $this->request->getParam('suburb')) {
            $params['zipcode'] = $suburb;
        }

        if ($this->request->getParam('latitude')) {
            $params['latitude'] = $this->request->getParam('latitude');
        }

        if ($this->request->getParam('longitude')) {
            $params['longitude'] = $this->request->getParam('longitude');
        }

        if ($this->request->getParam('radius')) {
            $params['radius'] = $this->request->getParam('radius');
        }

        return $params;
    }

    /**
     * @inheritdoc
     */
    public function getJsLayout()
    {
        $jsLayout = $this->augmentJsLayout($this->jsLayout);

        return json_encode($jsLayout, JSON_NUMERIC_CHECK);
    }

    /**
     * @param array $jsLayout
     * @return array
     */
    protected function augmentJsLayout($jsLayout)
    {
        $params = $this->getSearchParams();
        $coords = [];
        $zoom = 5;

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_SERVICE_URL))) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['map']['service_url'] = $this->_helper->getConfig(Helper::XML_PATH_SERVICE_URL);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_SERVICE_ACCESS_TOKEN))) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['map']['access_token'] = $this->_helper->getConfig(Helper::XML_PATH_SERVICE_ACCESS_TOKEN);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_TILE_MAX_ZOOM))) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['map']['maxZoom'] = (int) $this->_helper->getConfig(Helper::XML_PATH_TILE_MAX_ZOOM);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_TILE_SIZE))) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['tile']['tileSize'] = (int) $this->_helper->getConfig(Helper::XML_PATH_TILE_SIZE);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_TILE_ZOOM_OFFSET))) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['tile']['zoomOffset'] = (int) $this->_helper->getConfig(Helper::XML_PATH_TILE_ZOOM_OFFSET);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_TILE_SERVICE_ID))) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['tile']['id'] =  $this->_helper->getConfig(Helper::XML_PATH_TILE_SERVICE_ID);
        }

        if (isset($jsLayout['components']['storelocations']['children']['map']['config']['options']['icon']['iconUrl'])) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['icon']['iconUrl'] = $this->getViewFileUrl('Totaltools_Storelocator::images/map-icon.png');
        }

        if (isset($params['state'])) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['state'] = $params['state'];
        }

        if (isset($params['zipcode'])) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['stores'] = $this->getStores($params['zipcode']);
        }

        if (isset($params['latitude']) && isset($params['longitude'])) {
            $coords = [$params['latitude'], $params['longitude']];
            $zoom = 18;
        }

        if (count($coords)) {
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['map']['coords'] = $coords;
            $jsLayout['components']['storelocations']['children']['map']['config']['options']['map']['zoom'] = $zoom;
        }

        return $jsLayout;
    }

    /**
     * @param string $postcode
     * @return array
     */
    protected function getStores($postcode)
    {
        $stores = [];
        $resultStore = $this->_storeRepository->getByPostcodeInZone($postcode);

        if ($resultStore) {
            $latitude = $resultStore->getLatitude();
            $longitude = $resultStore->getLongitude();
            $nearestStoresCollection = $this->_storeRepository
                    ->getNearestStoresCollection($latitude, $longitude)
                    ->getItems();

            foreach ($nearestStoresCollection as $nearestStore) {
                /** @var \Totaltools\Storelocator\Model\Store $nearestStore */
                if (empty($stores[$nearestStore->getId()])) {
                    $stores[] = $nearestStore->getData();
                }
            }
        }

        return $stores;
    }
}
