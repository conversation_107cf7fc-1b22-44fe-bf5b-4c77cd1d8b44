<?php

/**
 * <AUTHOR> Internet
 * @package  Totaltools_Storelocator
 */

namespace Totaltools\Storelocator\Block\Store;

/**
 * @category Totaltools
 * @package  Totaltools_Storelocator
 * @module   Storelocator
 */
class ViewPageRewrite extends \Magestore\Storelocator\Block\Store\ViewPage
{

    protected function _prepareLayout(){

        if ($breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb(
                'home',
                [
                    'label' => __('Home'),
                    'title' => __('Go to Home Page'),
                    'link' => $this->_storeManager->getStore()->getBaseUrl()
                ]
            );

            $breadcrumbsBlock->addCrumb(
                'storelocations',
                [
                    'label' => __('Store Locations'),
                    'title' => __('Store Locations'),
                    'link' => $this->getUrl('storelocator/index/index')
                ]
            );

            if ($this->getStore()) {
                $breadcrumbsBlock->addCrumb('storelocator',
                    [
                        'label' => $this->getStore()->getStoreName(),
                        'title' => $this->getStore()->getStoreName()
                    ]
                );
            }
        }
        return $this;
    }
}
