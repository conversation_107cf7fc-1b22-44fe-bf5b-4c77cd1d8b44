<?php

/**
 * <AUTHOR> Internet
 * @package  Totaltools_Storelocator
 */

namespace Totaltools\Storelocator\Block\ListStore;

/**
 * @category Blanceinternet
 * @package  Totaltools_Storelocator
 * @module   Storelocator
 * <AUTHOR> Developer
 */
class Pagination extends \Magestore\Storelocator\Block\ListStore\Pagination
{
    /**
     * Get params request
     */
    public function getParamsInJson()
    {
        $params = $this->getRequest()->getParams();
        return json_encode($params);
    }
}
