<?php
/**
 * <AUTHOR> Internet
 * @package  Totaltools_Storelocator
 */

namespace Totaltools\Storelocator\Block\ListStore;

/**
 * @category Totaltools
 * @package  Totaltools_Storelocator
 * @module   Storelocator
 */
class SearchBoxRewrite extends \Magestore\Storelocator\Block\ListStore\SearchBox
{
    /**
     * @var  \Magento\Framework\App\Request\Http
     */
    protected $request;


    /**
     * @param  \Magestore\Storelocator\Block\Context $context
     * @param  \Magento\Directory\Helper\Data $directoryHelper
     * @param  \Magento\Config\Model\Config\Source\Locale\Country $localCountry
     * @param  array $data
     */
    public function __construct(
        \Magestore\Storelocator\Block\Context $context,
        \Magento\Directory\Helper\Data $directoryHelper,
        \Magento\Config\Model\Config\Source\Locale\Country $localCountry,
        array $data = []
    ) {
        parent::__construct($context, $directoryHelper, $localCountry, $data);
        $this->request = $context->getRequest();
    }

    /**
     * @return  string
     */
    public function getInputSearchDistance()
    {
        if ($value = $this->request->getParam('input-search-distance')) {
            return $value;
        }

        return '';
    }
}
