<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Storelocator\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\Serialize\Serializer\Json;
use Totaltools\Storelocator\Helper\Data;

class SelectedStoreNotification extends Template
{
    /**
     * @return string
     */
    const XML_CONFIG_PATH = 'storelocator/notifications/';
 
    /**
     * @inheritdoc
     */
    protected $_template = 'Totaltools_Storelocator::selected_store_notification.phtml';

    /**
     * @var Json
     */
    protected $serializer;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * CartNotifications constructor
     *
     * @param Template\Context $context
     * @param Json $serializer
     * @param Data $helper
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        Json $serializer,
        Data $helper,
        array $data = []
    ) {
        $this->serializer = $serializer;
        $this->helper = $helper;

        parent::__construct($context, $data);
    }

    /**
     * @return string
     */
    public function getJsonConfig()
    {
        $config = [
            'canAutoHide' => $this->helper->getFlag(self::XML_CONFIG_PATH.'auto_hide'),
            'holdup' => (int) ($this->helper->getConfig(self::XML_CONFIG_PATH.'holdup') * 1000),
            'fadeTime' => (int) ($this->helper->getConfig(self::XML_CONFIG_PATH.'fade_time') * 1000),
            'hasCloseBtn' => $this->helper->getFlag(self::XML_CONFIG_PATH.'close_btn'),
            'title' => $this->helper->getConfig(self::XML_CONFIG_PATH.'title'),
            'btnText' => $this->helper->getConfig(self::XML_CONFIG_PATH.'button_text'),
            'promoText' => $this->helper->getConfig(self::XML_CONFIG_PATH.'promo_text'),
        ];

        return $this->serializer->serialize($config);
    }

    /**
     * @return boolean
     */
    public function isEnabled()
    {
        return $this->helper->getFlag(self::XML_CONFIG_PATH.'enabled');
    }
}
