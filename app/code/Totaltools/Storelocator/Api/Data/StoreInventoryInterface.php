<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Api\Data;

/**
 * Interface StoreInventoryInterface.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
interface StoreInventoryInterface
{
    const INVENTORY_ID = 'inventory_id';
    const ERP_ID = 'erp_id';
    const SKU = 'sku';
    const UNITS = 'units';

    /**
     * Get ID
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get ERP Id
     *
     * @return string|null
     */
    public function getERPId();

    /**
     * Get SKU
     *
     * @return string|null
     */
    public function getSKU();

    /**
     * Get Units
     *
     * @return string|null
     */
    public function getUnits();

    /**
     * Set ID
     *
     * @param int $id
     *
     * @return int|null
     */
    public function setId($id);

    /**
     * Set ERP Id
     *
     * @param string $erpId
     *
     * @return null|string
     */
    public function setERPId($erpId);

    /**
     * Set SKU
     *
     * @param string $sku
     *
     * @return null|string
     */
    public function setSKU($sku);

    /**
     * Set units
     *
     * @param string $units
     *
     * @return null|string
     */
    public function setUnits($units);

}