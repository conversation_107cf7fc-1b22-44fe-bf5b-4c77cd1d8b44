<?php

namespace Totaltools\Storelocator\Api\Data;

/**
 * @package Totaltools_Storelocatorreport
 * <AUTHOR> Dev Team
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */

interface InventoryimportreportSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get inventoryimportreport list.
     * @return \Totaltools\Storelocator\Api\Data\InventoryimportreportInterface[]
     */
    public function getItems();

    /**
     * Set inventoryimportreport list.
     * @param \Totaltools\Storelocator\Api\Data\InventoryimportreportInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
