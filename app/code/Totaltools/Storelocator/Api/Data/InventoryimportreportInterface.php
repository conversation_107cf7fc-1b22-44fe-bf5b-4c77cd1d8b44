<?php

namespace Totaltools\Storelocator\Api\Data;

use Totaltools\Storelocator\Api\varchar;

interface InventoryimportreportInterface
{
    /**
     * Table field ENTITY_ID.
     */
    const ENTITY_ID = 'entity_id';
    /**
     * Table field FILENAME.
     */
    const FILENAME = 'filename';
    /**
     * Table field IMPORT_TYPE.
     */
    const IMPORT_TYPE = 'import_type';
    /**
     * Table field CREATED_AT.
     */
    const CREATED_AT = 'created_at';
    /**
     * Table field UPDATE_AT.
     */
    const UPDATED_AT = 'updated_at';
    /**
     * Table field STATUS.
     */
    const STATUS = 'status';
    /**
     * Table field COMMENT.
     */
    const COMMENT = 'comment';
    /**
     * Table field ROW_COUNT_IN_FILE.
     */
    const ROW_COUNT_IN_FILE = 'row_count_in_file';

    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getEntityId();

    /**
     * Set EntityId.
     */
    public function setEntityId($entityId);

    /**
     * Get Filename.
     *
     * @return varchar
     */
    public function getFilename();

    /**
     * Set Filename.
     */
    public function setFilename($filename);

    /**
     * Get ImportType.
     *
     * @return varchar
     */
    public function getImportType();

    /**
     * Set ImportType.
     */
    public function setImportType($importType);

    /**
     * Get CreatedAt.
     *
     * @return varchar
     */
    public function getCreatedAt();

    /**
     * Set CreatedAt.
     */
    public function setCreatedAt($createdAt);

    /**
     * Get UpdatedAt.
     *
     * @return varchar
     */
    public function getUpdatedAt();

    /**
     * Set UpdatedAt.
     */
    public function setUpdatedAt($updatedAt);

    /**
     * Get Status.
     *
     * @return varchar
     */
    public function getStatus();

    /**
     * Set Status.
     */
    public function setStatus($status);

    /**
     * Get getComment.
     *
     * @return varchar
     */
    public function getComment();

    /**
     * Set Comment.
     */
    public function setComment($comment);

    /**
     * Get rowCountInFile.
     *
     * @return varchar
     */
    public function getRowCountInFile();

    /**
     * Set rowCountInFile.
     */
    public function setRowCountInFile($rowCountInFile);
}
