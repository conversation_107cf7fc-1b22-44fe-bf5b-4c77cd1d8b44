<?php
/**
 * Created by PhpStorm.
 * User: andrey
 * Date: 25.01.19
 * Time: 10:34
 */

namespace Totaltools\Storelocator\Api\Data;


interface StoreFallbackInterface
{
    const ROW_ID = 'row_id';
    const STORE_ID = 'store_id';
    const FALLBACK_STORE_ID = 'fallback_store_id';

    /**
     * Get Row ID
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get Store ID
     *
     * @return int|null
     */
    public function getStoreId();


    /**
     * Get Store Fallback ID
     *
     * @return int|null
     */
    public function getFallbackStoreId();

    /**
     * Set Row ID
     *
     * @param int $id
     *
     * @return $this
     */
    public function setId($id);

    /**
     * Set Store ID
     *
     * @param int $id
     *
     * @return $this
     */
    public function setStoreId($storeId);


    /**
     * Set Fallback Store ID
     *
     * @return $this
     */
    public function setFallbackStoreId($falbackStoreId);
}