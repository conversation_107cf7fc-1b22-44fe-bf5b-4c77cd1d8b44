<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Api\Data;

/**
 * Interface ZoneInterface.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
interface ZoneInterface
{
    /**
     * Constants for keys of data array. Identical to the name of the getter in snake case
     */
    const ZONE_ID = 'zone_id';
    const NAME = 'name';
    const COUNTRY_CODE = 'country_code';
    const RANGES = 'ranges';

    /**
     * Get ID
     *
     * @return int|null
     */
    public function getId();

    /**
     * Get name
     *
     * @return string|null
     */
    public function getName();

    /**
     * Get Country Code
     *
     * @return string
     */
    public function getCountryCode();

    /**
     * Get Ranges
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function getRanges();

    /**
     * Set ID
     *
     * @param int $id
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function setId($id);

    /**
     * Set title.
     *
     * @param string $title
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function setName($title);

    /**
     * Set Country Code.
     *
     * @param string $country_code
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function setCountryCode($country_code);

    /**
     * Set Ranges.
     *
     * @param string $ranges
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function setRanges($ranges);
}
