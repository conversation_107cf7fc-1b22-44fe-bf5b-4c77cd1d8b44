<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Api\Message;

/**
 * Interface MessageInterface
 * @package Totaltools\Storelocator\Api\Message
 */
interface MessageInterface
{

    /**
     * Pre order related messages
     */
    const PRE_ORDER_DELIVERY_SHORT_MESSAGE = 'Pre-order item';
    const PRE_ORDER_DELIVERY_CART_MESSAGE = 'At least one item in your cart is a pre-order item, Order will be dispatched once pre-order item is available';
    const PRE_ORDER_CC_SHORT_MESSAGE = 'Pre-order item';
    const PRE_ORDER_CC_CART_MESSAGE = 'At least one item in your cart is a pre-order item, Order will be available for collection once pre-order item is available';

    /**
     * Dangerous goods related messages
     */
    const DANGEROUS_DELIVERY_SHORT_MESSAGE = 'This item is not available for delivery as it is classed as a dangerous good or is too bulky';
    const DANGEROUS_DELIVERY_CART_MESSAGE = 'At least one item in your cart is a not available for delivery as it is classed as a dangerous good or is too bulky.';

    /**
     * Knife compliance related messages
     */
    const KNIFE_COMPLIANCE_SHORT_MESSAGE = 'This item is not available for delivery as it requires a knife compliance check';
    const KNIFE_COMPLIANCE_CART_MESSAGE = 'At least one item in your cart is a not available for delivery as it requires a knife compliance check.';

    /**
     * In stock Messages
     */
    const IN_STOCK_CC_MESSAGE = "In Stock - Available for Click & Collect";
    const IN_STOCK_DELIVERY_MESSAGE = "Available for Delivery";

    /**
     * Out of stock messages for Click & Collect
     */

    //OD
    const OUT_OF_STOCK_CC_OD_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 3 months before collection. Special order conditions apply. Alternatively select another store.";
    const OUT_OF_STOCK_CC_OD_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 3 months before collection.";
    const OUT_OF_STOCK_CC_OD_NON_EB_SHORT_MESSAGE = "Special order. Please allow up to 14 business days before collection. Special order conditions apply. Alternatively select another store.";
    const OUT_OF_STOCK_CC_OD_NON_EB_CART_MESSAGE = "At least one item in your cart is a special order. Please allow up to 14 business days before collection.";

    //OL
    const OUT_OF_STOCK_CC_OL_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 4 weeks before collection. Special order conditions apply. Alternatively select another store.";
    const OUT_OF_STOCK_CC_OL_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 4 weeks before collection.";
    const OUT_OF_STOCK_CC_OL_NON_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 14 business days before collection. Special order conditions apply. Alternatively select another store.";
    const OUT_OF_STOCK_CC_OL_NON_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 14 business days before collection.";

    //OC
    const OUT_OF_STOCK_CC_OC_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 4 weeks before collection. Special order conditions apply. Alternatively select another store.";
    const OUT_OF_STOCK_CC_OC_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 4 weeks before collection.";
    const OUT_OF_STOCK_CC_OC_NON_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 5 to 14 days before collection. Special order conditions apply. Alternatively select another store.";
    const OUT_OF_STOCK_CC_OC_NON_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 5 to 14 days before collection.";

    //EB
    const OUT_OF_STOCK_CC_OE_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 3 months before collection. Special order conditions apply. Alternatively select another store.";
    const OUT_OF_STOCK_CC_OE_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 3 months before collection.";
    const OUT_OF_STOCK_CC_OE_NON_EB_SHORT_MESSAGE = "Out of Stock. Please allow up to 5 to 14 days before collection. Special order conditions apply. Alternatively select another store.";
    const OUT_OF_STOCK_CC_OE_NON_EB_CART_MESSAGE = "At least one item in your cart is a special order. Please allow up to 5 to 14 days before collection.";

    //OX
    const OUT_OF_STOCK_CC_OX_SHORT_MESSAGE = 'Clearance product out of stock at your selected store. Please select another store.';
    const OUT_OF_STOCK_CC_OX_CART_MESSAGE = 'At least one item in your cart is a clearance item out of stock at selected store. Please select another store or remove item from the cart.';

    //OP
    const OUT_OF_STOCK_CC_OP_SHORT_MESSAGE = 'Product out of stock at your selected store. Please select another store.';
    const OUT_OF_STOCK_CC_OP_CART_MESSAGE = 'At least one item in your cart is out of stock at selected store. Please select another store or remove item from the cart.';

    /**
     * Out of stock messages for Delivery
     */

    //OD
    const OUT_OF_STOCK_DELIVERY_OD_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 3 months before dispatch. Special order conditions apply.";
    const OUT_OF_STOCK_DELIVERY_OD_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 3 months before dispatch.";
    const OUT_OF_STOCK_DELIVERY_OD_NON_EB_SHORT_MESSAGE = "Special order. Please allow up to 14 business days before dispatch. Special order conditions apply.";
    const OUT_OF_STOCK_DELIVERY_OD_NON_EB_CART_MESSAGE = "At least one item in your cart is a special order. Please allow up to 14 business days before dispatch.";

    //OL
    const OUT_OF_STOCK_DELIVERY_OL_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 4 weeks before dispatch. Special order conditions apply.";
    const OUT_OF_STOCK_DELIVERY_OL_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 4 weeks before dispatch.";
    const OUT_OF_STOCK_DELIVERY_OL_NON_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 14 business days before dispatch. Special order conditions apply.";
    const OUT_OF_STOCK_DELIVERY_OL_NON_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 14 business days before dispatch.";

    //OC
    const OUT_OF_STOCK_DELIVERY_OC_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 4 weeks before dispatch. Special order conditions apply.";
    const OUT_OF_STOCK_DELIVERY_OC_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 4 weeks before dispatch.";
    const OUT_OF_STOCK_DELIVERY_OC_NON_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow 5 to 14 days before dispatch. Special order conditions apply.";
    const OUT_OF_STOCK_DELIVERY_OC_NON_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow 5 to 14 days before dispatch.";

    //EB
    const OUT_OF_STOCK_DELIVERY_OE_EB_SHORT_MESSAGE = "Temporarily out of stock. Please allow up to 3 months before dispatch. Special order conditions apply.";
    const OUT_OF_STOCK_DELIVERY_OE_EB_CART_MESSAGE = "At least one item in your cart is temporarily out of stock. Please allow up to 3 months before dispatch.";
    const OUT_OF_STOCK_DELIVERY_OE_NON_EB_SHORT_MESSAGE = "Special order. Please allow 5 to 14 days before dispatch. Special order conditions apply.";
    const OUT_OF_STOCK_DELIVERY_OE_NON_EB_CART_MESSAGE = "At least one item in your cart is a special order. Please allow 5 to 14 days before dispatch.";

    //OX
    const OUT_OF_STOCK_DELIVERY_OX_SHORT_MESSAGE = 'Clearance product out of stock.';
    const OUT_OF_STOCK_DELIVERY_OX_CART_MESSAGE = 'At least one item in your cart is a clearance item and out of stock.';

    //OP
    const OUT_OF_STOCK_DELIVERY_OP_SHORT_MESSAGE = 'Product out of stock.';
    const OUT_OF_STOCK_DELIVERY_OP_CART_MESSAGE = 'At least one item in your cart is out of stock.';

    /**
     * Unknown stock messages
     */
    const UNKNOWN_STOCK_CC_SHORT_MESSAGE = 'Unable to find stock level for this product at your selected store. Please select another store.';
    const UNKNOWN_STOCK_CC_CART_MESSAGE = 'Unable to find stock level for at least one item in your cart at your selected store. Please select another store or remove item from the cart.';
    const UNKNOWN_STOCK_DELIVERY_SHORT_MESSAGE = 'Unable to find delivery estimates for this product.';
    const UNKNOWN_STOCK_DELIVERY_CART_MESSAGE = 'Unable to find delivery estimates for at least one item in your cart.';

    const THIRTY_MINUTE_CLICK_AND_COLLECT_AVAILABLE = "30 minute click & collect";

}