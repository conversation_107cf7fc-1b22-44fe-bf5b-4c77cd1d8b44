<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Controller\Adminhtml\Zone;

use Magento\Backend\App\Action;
use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Storelocator\Model\ShippingZoneRepository;

/**
 * Class Edit.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Edit extends \Magento\Backend\App\Action
{
    /**
     * Core registry.
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * Result Page Factory.
     *
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var ShippingZoneRepository
     */
    protected $_shippingZoneRepository;

    /**
     * @var \Totaltools\Storelocator\Model\ZoneFactory
     */
    protected $_zoneFactory;

    /**
     * Constructor.
     *
     * @param Action\Context                             $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Magento\Framework\Registry                $registry
     */
    public function __construct(
        Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        ShippingZoneRepository $shippingZoneRepository,
        \Totaltools\Storelocator\Model\ZoneFactory $zoneFactory,
        \Magento\Framework\Registry $registry
    )
    {
        $this->resultPageFactory = $resultPageFactory;
        $this->_coreRegistry = $registry;
        $this->_zoneFactory = $zoneFactory;
        $this->_shippingZoneRepository = $shippingZoneRepository;

        parent::__construct($context);
    }

    /**
     * {@inheritdoc}
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Totaltools_Storelocator::shippingzones_locations_edit_zone');
    }

    /**
     * Init actions
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    protected function _initAction()
    {
        // load layout, set active menu and breadcrumbs
        $resultPage = $this->resultPageFactory->create();
        $resultPage->setActiveMenu('Magestore_Storelocator::Storelocator')
            ->addBreadcrumb(__('Shipping Zones'), __('Shipping Zones'))
            ->addBreadcrumb(__('Manage Zones'), __('Manage Zones'));

        return $resultPage;
    }

    /**
     * Get shipping zone entity by id.
     *
     * @param $zoneId
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    protected function getZoneById($zoneId)
    {
        try {
            return $this->_shippingZoneRepository->getById($zoneId);
        } catch (NoSuchEntityException $entityException) {
            $this->messageManager->addErrorMessage($entityException->getMessage());
        }

        return null;
    }

    /**
     * Edit Zone
     *
     * @return \Magento\Framework\Controller\ResultInterface|\Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        $zoneId = $this->getRequest()->getParam('zone_id');

        if ($zoneId) {
            $zoneModel = $this->getZoneById($zoneId);
            if (!$zoneModel->getId()) {
                $this->messageManager->addErrorMessage(__('This shipping zone no longer exists.'));
                $resultRedirect = $this->resultRedirectFactory->create();

                return $resultRedirect->setPath('*/*/');
            }
        } else {
            $zoneModel = $this->_zoneFactory->create();
        }

        $data = $this->_getSession()->getFormData(true);
        if (!empty($data)) {
            $zoneModel->setData($data);
        }

        $this->_coreRegistry->register('shippingzones_zone', $zoneModel);

        $resultPage = $this->_initAction();
        $resultPage->addBreadcrumb(
            $zoneId ? __('Edit Shipping Zone') : __('Edit Shipping Zone'),
            $zoneId ? __('Edit Shipping Zone') : __('New Shipping Zone')
        );
        $resultPage->getConfig()->getTitle()->prepend(__('Shipping Zones'));
        $resultPage->getConfig()->getTitle()->prepend($zoneModel->getId()
            ? $zoneModel->getName()
            : __('New Shipping Zone')
        );

        return $resultPage;
    }
}
