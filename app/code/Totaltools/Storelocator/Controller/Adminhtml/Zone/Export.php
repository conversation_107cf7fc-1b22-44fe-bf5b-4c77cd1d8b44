<?php


namespace Totaltools\Storelocator\Controller\Adminhtml\Zone;

use Magento\Backend\App\Action;
use Totaltools\Storelocator\Model\Export\ShippingZone;
use Magento\Framework\App\Response\Http\FileFactory;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Filesystem\DirectoryList;

class Export extends Action
{
    /** @var $shippingZone ShippingZone */
    protected $shippingZone;

    /** @var FileFactory $fileFactory */
    protected $fileFactory;

    /** @var Filesystem $fileSystem */
    protected $fileSystem;

    /**
     * Export constructor.
     * @param Action\Context $context
     * @param ShippingZone $shippingZone
     * @param FileFactory $fileFactory
     * @param Filesystem $fileSystem
     */
    public function __construct(
        Action\Context $context,
        ShippingZone $shippingZone,
        FileFactory $fileFactory,
        Filesystem $fileSystem
    ) {
        parent::__construct($context);
        $this->shippingZone = $shippingZone;
        $this->fileFactory = $fileFactory;
        $this->fileSystem = $fileSystem;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     * @throws \Exception
     */
    public function execute()
    {
        return $this->fileFactory->create(
            md5(microtime()). '-' . date('d-m-Y') . '.csv',
            $this->shippingZone->export(), //return file content
            DirectoryList::VAR_DIR
        );
    }
}