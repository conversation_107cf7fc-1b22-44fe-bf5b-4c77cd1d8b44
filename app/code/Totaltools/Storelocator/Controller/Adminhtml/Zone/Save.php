<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Controller\Adminhtml\Zone;

use Magento\Backend\App\Action;
use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Storelocator\Model\ShippingZoneRepository;

/**
 * Class Save.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Save extends Action
{
    /**
     * @var ShippingZoneRepository
     */
    protected $_shippingZoneRepository;

    /**
     * @var \Totaltools\Storelocator\Model\ZoneFactory
     */
    protected $_zoneFactory;

    /**
     * Constructor.
     *
     * @param Action\Context                             $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param \Magento\Framework\Registry                $registry
     */
    public function __construct(
        Action\Context $context,
        ShippingZoneRepository $shippingZoneRepository,
        \Totaltools\Storelocator\Model\ZoneFactory $zoneFactory
    )
    {
        $this->_zoneFactory = $zoneFactory;
        $this->_shippingZoneRepository = $shippingZoneRepository;

        parent::__construct($context);
    }

    /**
     * {@inheritdoc}
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Totaltools_Storelocator::shippingzones_locations_save_zone');
    }

    /**
     * Get shipping zone entity by id.
     *
     * @param $zoneId
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    protected function getZoneById($zoneId)
    {
        try {
            return $this->_shippingZoneRepository->getById($zoneId);
        } catch (NoSuchEntityException $entityException) {
            $this->messageManager->addErrorMessage($entityException->getMessage());
        }

        return null;
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        $resultRedirect = $this->resultRedirectFactory->create();

        if ($data) {
            $zoneId = $this->getRequest()->getParam('zone_id');
            if ($zoneId) {
                $zoneModel = $this->getZoneById($zoneId);
            } else {
                $zoneModel = $this->_zoneFactory->create();
            }

            $zoneModel->setData($data);

            $this->_eventManager->dispatch(
                'shippingzones_zone_prepare_save',
                ['zone' => $zoneModel, 'request' => $this->getRequest()]
            );

            try {
                $zoneModel->save();
                $this->messageManager->addSuccessMessage(__('You saved this Shipping Zone.'));

                $this->_getSession()->setFormData(false);

                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['zone_id' => $zoneModel->getId(), '_current' => true]);
                }

                return $resultRedirect->setPath('*/*/');
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\RuntimeException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('Something went wrong while saving the zone.'));
            }

            $this->_getSession()->setFormData($data);

            return $resultRedirect->setPath('*/*/edit', ['zone_id' => $this->getRequest()->getParam('zone_id')]);
        }

        return $resultRedirect->setPath('*/*/');
    }
}
