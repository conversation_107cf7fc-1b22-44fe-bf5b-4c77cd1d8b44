<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Controller\Adminhtml\Zone;

use Magento\Backend\App\Action;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\TestFramework\ErrorLog\Logger;
use Totaltools\Storelocator\Model\ShippingZoneRepository;

/**
 * Class Delete.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Delete extends \Magento\Backend\App\Action
{
    /**
     * @var ShippingZoneRepository
     */
    protected $_shippingZoneRepository;

    /**
     * Constructor.
     *
     * @param Action\Context         $context
     * @param ShippingZoneRepository $shippingZoneRepository
     */
    public function __construct(
        Action\Context $context,
        ShippingZoneRepository $shippingZoneRepository
    )
    {
        $this->_shippingZoneRepository = $shippingZoneRepository;

        parent::__construct($context);
    }

    /**
     * {@inheritdoc}
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Totaltools_Storelocator::shippingzones_locations_delete_zone');
    }

    /**
     * Delete action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $zoneId = $this->getRequest()->getParam('zone_id');
        $resultRedirect = $this->resultRedirectFactory->create();

        if ($zoneId) {
            try {
                $zoneModel = $this->_shippingZoneRepository->getById($zoneId);
                if (!$zoneModel->getId()) {
                    $this->messageManager->addErrorMessage(__('This shipping zone no longer exists.'));
                    $resultRedirect = $this->resultRedirectFactory->create();

                    return $resultRedirect->setPath('*/*/');
                }

                $this->_shippingZoneRepository->delete($zoneModel);
                $this->messageManager->addSuccessMessage(__('The shipping zone has been deleted.'));

                return $resultRedirect->setPath('*/*/');
            } catch (NoSuchEntityException $entityException) {
                $this->messageManager->addErrorMessage($entityException->getMessage());
                return $resultRedirect->setPath('*/*/edit', ['zone_id' => $zoneId]);
            }
        }

        $this->messageManager->addErrorMessage(__('We can\'t find a shipping zone to delete.'));

        return $resultRedirect->setPath('*/*/');
    }
}
