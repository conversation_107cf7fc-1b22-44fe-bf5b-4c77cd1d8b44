<?php

namespace Totaltools\Storelocator\Controller\Adminhtml\Store;

use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Action\Action;
use Magento\Framework\Controller\Result\JsonFactory;
use Totaltools\Storelocator\Model\Config\Source\Store;
use Totaltools\Storelocator\Model\StoreRepository;

class Distance extends Action
{
    protected $resultJsonFactory;

    protected $storeRepository;

    protected $storeConfig;

    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        StoreRepository $storeRepository,
        Store $storeConfig
    ) {
        $this->resultJsonFactory = $resultJsonFactory;
        $this->storeRepository = $storeRepository;
        $this->storeConfig = $storeConfig;
        parent::__construct($context);
    }

    public function execute()
    {
        $resultJson = $this->resultJsonFactory->create();
        
        $states = $this->getRequest()->getParam('states');
        $locations = $this->getRequest()->getParam('locations');
        $structures = $this->getRequest()->getParam('structures');
        $storeId = $this->getRequest()->getParam('storelocator_id');
        $currentStore = $this->storeRepository->getById($storeId);
        $stores = [];

        if ($currentStore->hasData('erp_id')) {
            $erpId = $currentStore->getErpId();
            $latitudeFrom = $currentStore->getLatitude();
            $longitudeFrom = $currentStore->getLongitude();
            $stores = $this->storeConfig->getStoresByFilters($erpId, $latitudeFrom, $longitudeFrom, $states, $locations, $structures);    
        }

        $storeDistanceTableHtml = $this->getStoreDistanceTableHtml($stores);

        $response = [
            'success' => true,
            'htmlTable' => $storeDistanceTableHtml
        ];

        return $resultJson->setData($response);
    }

    protected function getStoreDistanceTableHtml($stores)
    {
        $html = '<table class="data-grid">';
        $html .= '<thead>';
        $html .= '<tr>';
        $html .= '<th>Store Name</th>';
        $html .= '<th>Location</th>';
        $html .= '<th>Structure</th>';
        $html .= '<th>Franchisee Group</th>';
        $html .= '<th>Distance</th>';
        $html .= '</tr>';
        $html .= '</thead>';
        $html .= '<tbody>';
        if ($stores) {
            foreach ($stores as $store) {
                $html .= '<tr>';
                $html .= '<td class="store-fallback-bold">' . $store['name'] . '</td>';
                $html .= '<td>' . ucfirst($store['location'] ?? '') . '</td>';
                $html .= '<td>' . ucfirst($store['structure'] ?? '') . '</td>';
                $html .= '<td>' . $store['franchisee_group'] . '</td>';
                $html .= '<td class="store-fallback-bold">' . number_format($store['distance'], 2) . ' km' . '</td>';
                $html .= '</tr>';
            }
        } else {
            $html .= '<tr><td colspan="5">No store was found.</td></tr>';
        }
        

        $html .= '</tbody>';
        $html .= '</table>';

        return $html;
    }
}
