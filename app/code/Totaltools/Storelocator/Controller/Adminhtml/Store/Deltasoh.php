<?php

namespace Totaltools\Storelocator\Controller\Adminhtml\Store;

class Deltasoh extends \Magento\Backend\App\Action
{
    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\SohProcessor
     */
    private $sohProcessor;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Fullsoh constructor.
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\SohProcessor $sohProcessor
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\SohProcessor $sohProcessor,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->storeRepository = $storeRepository;
        $this->sohProcessor = $sohProcessor;
        $this->logger = $logger;
    }

    
    public function execute()
    {
        $storeId = $this->getRequest()->getParam('storelocator_id');
        $store = $this->storeRepository->getById($storeId);
        $this->sohProcessor->updateInventory($store->getData());
        $this->messageManager->addSuccess(__('The delta SOH import for this store has been completed.'));

        return $this->resultRedirectFactory->create()->setPath(
            'storelocatoradmin/store/edit',
            [
                'storelocator_id' => $storeId
            ]
        );
    }    
}
