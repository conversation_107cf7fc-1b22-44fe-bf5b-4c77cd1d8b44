<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Storelocator
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */
namespace Totaltools\Storelocator\Controller\Adminhtml\Store;

class Email extends \Magento\Backend\App\Action
{
    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $_storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\StoreAdminEmail
     */
    private $storeAdminEmail;

    /**
     * @var \Magento\Sales\Api\OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Store email constructor.
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\StoreAdminEmail $storeAdminEmail
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreAdminEmail $storeAdminEmail,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository,
        \Psr\Log\LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->_storeRepository = $storeRepository;
        $this->storeAdminEmail = $storeAdminEmail;
        $this->orderRepository = $orderRepository;
        $this->logger = $logger;
    }

    /**
     * Reallocate the selected Store form
     */
    public function execute()
    {
        $id = $this->getRequest()->getParam('order_id');
        $order = $order = $this->orderRepository->get($id);
        if ($order) {
            try {
                $this->sendEmail($order);
                $order->addStatusHistoryComment('Order Email Sent to Store Manually.');
                $order->save();
                $this->messageManager->addSuccess(__('You sent the order email to store.'));
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $this->messageManager->addError($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addError(__('We can\'t send the email order store right now.'));
                $this->logger->critical($e);
            }
            return $this->resultRedirectFactory->create()->setPath(
                'sales/order/view',
                [
                    'order_id' => $order->getEntityId()
                ]
            );
        }
        return $this->resultRedirectFactory->create()->setPath('sales/*/');
    }

    /**
     * @param $order
     * @return bool|mixed
     */
    public function sendEmail(\Magento\Sales\Model\Order $order)
    {
        try {
            $storeId = (int)$order->getStorelocatorId();
            /** @var \Totaltools\Storelocator\Model\Store $store */
            $store = $this->_storeRepository->getById($storeId);
            $this->storeAdminEmail->sendMail($store, $order);
        } catch (\Exception $e) {
            $this->logger->critical($e->__toString(), array('entity' => $order));
        }
    }
}
