<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Controller\Product;

use Totaltools\Storelocator\Helper\Data;
use Magento\Framework\App\Action\Context;
use Magento\Checkout\Model\Session;

/**
 * Class StockAvailabilityMessage
 * @package Totaltools\Storelocator\Controller\Product
 */
class StockAvailabilityMessage extends \Magento\Framework\App\Action\Action
{
    
    /**
     * Helper
     *
     * @var Data
     */
    protected $_helper;

    /**
     * Checkout Session.
     *
     * @param \Magento\Checkout\Model\Session $checkoutSession
     *
     * @codeCoverageIgnore
     */
    protected $_checkoutSession;

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    private $totaltoolsConfig;

    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    private $_jsonResultFactory;

    /**
     * StockAvailabilityMessage constructor.
     * @param Context $context
     * @param Data $helper
     * @param Session $checkoutSession
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig
     * @param \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory
     */
    public function __construct(
        Context $context,
        Data $helper,
        Session $checkoutSession,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory
    ) {
        parent::__construct($context);
        $this->_helper = $helper;
        $this->_checkoutSession = $checkoutSession;
        $this->totaltoolsConfig = $totalToolsConfig;
        $this->_jsonResultFactory = $jsonResultFactory;
    }

    /**
     * Read params and find stock levels
     */
    public function execute()
    {
        $result = $this->_jsonResultFactory->create();
        $storeId = $this->getRequest()->getParam('storelocator_id', 0);
        $method = $this->getRequest()->getParam('method');
        $sku = $this->getRequest()->getParam('sku', '');
        $postcode = $this->getRequest()->getParam('postcode');
        $defaultShippingMethod = $this->totaltoolsConfig->getDefaultShippingMethod();
        if ($method === $defaultShippingMethod && $storeId != '') {
            $originalData['store_id'] = $storeId;
            $this->_checkoutSession->setStorepickupSession($originalData);
        }

        $messages = $this->_helper->getStockAvailabilityMessage($storeId, $method, $sku, $postcode);
        $result->setData($messages);
        return $result;
    }
}
