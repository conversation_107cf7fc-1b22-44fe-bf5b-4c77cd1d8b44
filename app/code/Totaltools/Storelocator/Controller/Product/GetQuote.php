<?php
/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> <<EMAIL>>
 */

namespace Totaltools\Storelocator\Controller\Product;

use Magento\Framework\App\Action\Context;

/**
 * Class StockAvailabilityMessage
 * @package Totaltools\Storelocator\Controller\Product
 */
class GetQuote extends \Magento\Framework\App\Action\Action
{

    /**
     * @var \Totaltools\Storelocator\Model\Rewrite\Carrier\ShippitQuote
     */
    private $shippitQuote;

    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    private $_jsonResultFactory;

    /**
     * GetQuote constructor.
     * @param Context $context
     * @param \Totaltools\Storelocator\Model\Rewrite\Carrier\ShippitQuote $shippitQuote
     * @param \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory
     */
    public function __construct(
        Context $context,
        \Totaltools\Storelocator\Model\Rewrite\Carrier\ShippitQuote $shippitQuote,
        \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory
    ) {
        parent::__construct($context);
        $this->shippitQuote = $shippitQuote;
        $this->_jsonResultFactory = $jsonResultFactory;        
    }

    /**
     * Read params and find stock levels
     */
    public function execute()
    {
        $request = $this->getRequest();
        $result = $this->_jsonResultFactory->create();
        $shippingQuotes = [];

        try {
            // Call the api and retrieve the quote
            $shippingQuotes = $this->shippitQuote->collectShippingRates($request);
        }
        catch (\Exception $e) {
           
            return false;
        }
       
        $result->setData($shippingQuotes);
        return $result;
    }

}