<?php

/**
 * <AUTHOR> Internet
 * @package Totaltools_Storelocator
 */

namespace Totaltools\Storelocator\Controller\Index;

use Magento\Framework\Controller\ResultFactory;
use Magestore\Storelocator\Model\Config\Source\OrderTypeStore;

/**
 * @category Magestore
 * @package  Magestore_Storelocator
 * @module   Storelocator
 * <AUTHOR> Developer
 */
class LoadStoreRewrite extends \Magestore\Storelocator\Controller\Index\Loadstore
{
    /**
     * @var \Magento\Directory\Helper\Data
     */
    protected $_directoryHelper;

    /**
     * Action constructor.
     *
     * @param \Magento\Framework\App\Action\Context $context
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magestore\Storelocator\Model\SystemConfig $systemConfig,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        \Magento\Framework\Registry $coreRegistry,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\Directory\Helper\Data $directoryHelper
    ) {
        $this->_directoryHelper = $directoryHelper;
        parent::__construct($context, $systemConfig, $storeCollectionFactory, $coreRegistry, $jsonHelper);
    }

    /**
     * filter store.
     *
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\Collection $collection
     *
     * @return \Magestore\Storelocator\Model\ResourceModel\Store\Collection
     */
    protected function _filterStoreCollection(
        \Magestore\Storelocator\Model\ResourceModel\Store\Collection $collection
    ) {
        $filterParams = $this->getRequest()->getParams();
        /*$filterParamsInJson = '';
        foreach ($params as $key=>$param) {
            $filterParamsInJson = $key;
        }
        $filterParams = $this->_jsonHelper->jsonDecode($filterParamsInJson);*/

        $collection->addFieldToSelect([
            'store_name',
            'phone',
            'address',
            'city',
            'state',
            'zipcode',
            'latitude',
            'longitude',
            'marker_icon',
            'zoom_level',
            'rewrite_request_path',
            'is_visible',
            'store_direction_url'
        ]);

        //$this->getRequest()->getParam('curPage', self::DEFAULT_CURRENT_PAGINATION);
        $curPage = isset($filterParams['curPage']) ? $filterParams['curPage'] : self::DEFAULT_CURRENT_PAGINATION;
        $collection->setPageSize($this->_systemConfig->getPainationSize())->setCurPage($curPage);

        /*
         * Filter store enabled
         */
        $collection->addFieldToFilter('status', \Magestore\Storelocator\Model\Status::STATUS_ENABLED);
        $collection->addFieldToFilter('is_visible', \Magestore\Storelocator\Model\Status::STATUS_ENABLED);

        /*
         * filter by radius
         */
        $radius = isset($filterParams['radius']) ? $filterParams['radius'] : '';
        if ($radius) {
            $latitude = isset($filterParams['latitude']) ? floatval($filterParams['latitude']): '';
            $longitude = isset($filterParams['longitude']) ? floatval($filterParams['longitude']): '';
            $collection->addLatLngToFilterDistance($latitude, $longitude, $radius);
        }

        /*
         * filter by tags
         */
        $tagIds = $this->getRequest()->getParam('tagIds');
        if (!empty($tagIds)) {
            $collection->addTagsToFilter($tagIds);
        }

        /*
         * filter by store information
         */
        $countryId = isset($filterParams['country_id']) ? $filterParams['country_id'] : '';
        if ($countryId) {
            $collection->addFieldToFilter('country_id', $countryId);
        }

        $storeName = isset($filterParams['store_name']) ? $filterParams['store_name'] : '';
        if ($storeName) {
            $collection->addFieldToFilter('store_name', ['like' => "%$storeName%"]);
        }

        $state = isset($filterParams['state']) ? $filterParams['state'] : '';
        if ($state) {
            $collection->addFieldToFilter('state', ['like' => "%$state%"]);
        }

        $city = isset($filterParams['city']) ? $filterParams['city'] : '';
        if ($city) {
            $collection->addFieldToFilter('city', ['like' => "%$city%"]);
        }

        $zipcode = isset($filterParams['zipcode']) ? $filterParams['zipcode'] : '';
        if ($zipcode) {
            $collection->addFieldToFilter('zipcode', ['like' => "%$zipcode%"]);
        }

        // Set sort type for list store
        switch ($this->_systemConfig->getSortStoreType()) {
            case OrderTypeStore::SORT_BY_ALPHABETICAL:
                $collection->setOrder('store_name', \Magento\Framework\Data\Collection\AbstractDb::SORT_ORDER_ASC);
                break;

            case OrderTypeStore::SORT_BY_DISTANCE:
                if ($radius) {
                    $collection->setOrder('distance', \Magento\Framework\Data\Collection\AbstractDb::SORT_ORDER_ASC);
                } else {
                    $collection->setOrder('store_name', \Magento\Framework\Data\Collection\AbstractDb::SORT_ORDER_ASC);
                }
                break;
            default:
                $collection->setOrder('sort_order', \Magento\Framework\Data\Collection\AbstractDb::SORT_ORDER_ASC);
        }

        // Allow load base image for each store
        $collection->setLoadBaseImage(true);

        return $collection;
    }
}
