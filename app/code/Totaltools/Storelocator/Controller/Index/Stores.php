<?php

namespace Totaltools\Storelocator\Controller\Index;

/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> Iqbal <<EMAIL>>
 */

use Exception;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\NotFoundException;

class Stores extends \Magento\Framework\App\Action\Action
{
    const STORE_LIST_LIMIT = 10;
    
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    protected $_jsonResultFactory;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository
    ) {
        parent::__construct($context);

        $this->_storeRepository = $storeRepository;
        $this->_jsonResultFactory = $jsonResultFactory;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Json|\Magento\Framework\Controller\ResultInterface
     * @throws NoSuchEntityException
     */
    public function execute()
    {
        $result = $this->_jsonResultFactory->create();

        try {
            $this->_validateRequest();
        } catch (NotFoundException $notFoundException) {
            return $result->setData($notFoundException->getMessage());
        }

        $response = $stores = $fallbackStoresCollection = [];
        $postcode = (int) $this->getRequest()->getParam('postcode');

        $index = 0;
        $resultStore = null;
        try {
            $resultStore = $this->_storeRepository->getByZipCode($postcode);
        } catch (Exception $e) {
            $resultStore = $this->_storeRepository->getByPostcodeInZone($postcode);
        }

        if ($resultStore) {
            $latitude = $resultStore->getLatitude();
            $longitude = $resultStore->getLongitude();

            $nearestStoresCollection = $this->_storeRepository
                ->getNearestStoresCollection($latitude, $longitude, self::STORE_LIST_LIMIT)
                ->getItems();

            foreach ($nearestStoresCollection as $nearestStore) {
                $index++;
                /** @var \Totaltools\Storelocator\Model\Store $nearestStore */
                if (empty($stores[$nearestStore->getId()])) {
                    $stores[$index] = $nearestStore->getData();
                }
            }

            $response['stores'] = $stores;
        }

        $result->setData($response);

        return $result;
    }

    /**
     * Validates request.
     *
     * @return void
     *
     * @throws NotFoundException
     */
    protected function _validateRequest()
    {
        if (!$this->getRequest()->isAjax() || !$this->getRequest()->isPost()) {
            throw new NotFoundException(__('Request type is incorrect'));
        }
    }
}
