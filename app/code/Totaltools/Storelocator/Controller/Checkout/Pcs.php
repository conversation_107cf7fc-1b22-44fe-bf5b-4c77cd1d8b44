<?php
/**
 * Pcs (AVS) Controller
 */

namespace Totaltools\Storelocator\Controller\Checkout;

use Magento\Framework\Escaper;

class Pcs extends \Magento\Framework\App\Action\Action
{

    protected $_objectManager;

    protected $_jsonEncoder;

    protected $_logger;

    protected $_scopeConfig;

    /**
     * Escaper.
     *
     * @var Escaper
     */
    protected $_escaper;

    private $_result = array (
            'query' => '',
            'suggestions' => array(),
            'data' => array(
                0 => array (
                    0 => array(
                            'city' => '',
                            'region_id' => '',
                            'postcode' => ''
                        )
                    )
            )
        );

    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Framework\Json\Encoder $encoder,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Escaper $escaper
    ) {
        parent::__construct($context);
        $this->_logger = $logger;
        $this->_jsonEncoder = $encoder;
        $this->_scopeConfig = $scopeConfig;
        $this->_escaper = $escaper;
    }

    
    /**
     * Autocomplete suggestion of post code and suburb
     */
    public function execute()
    {
        $query = $this->getRequest()->getParam('query');
        $country = $this->getRequest()->getParam(
            'country',
            $this->_scopeConfig->getValue(
                'general/country/default',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE
            )
        );
        echo $this->_makeAutocomplete($query, $country);
        exit;
    }
    /**
     * Make the autocomplete
     *
     * @param string $query
     * @param string $country
     *
     * @return json
     */
    protected function _makeAutocomplete($query, $country = null)
    {
        //TODO: Replace this with Storelocator version
        return [];
    }

    protected function _getValidator()
    {

        return $this;
    }
}
