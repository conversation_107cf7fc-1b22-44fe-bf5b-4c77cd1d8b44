<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Controller\Checkout;

use Magento\Checkout\Model\Session;
use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Storelocator\Api\Message\MessageInterface;
use \Totaltools\Storelocator\Model\Store;
use Magento\Framework\Exception\NotFoundException;
use Totaltools\Postcodes\Model\PostCodeFactory as PostCodeFactory;

/**
 * Class Stockcheck
 * @package Totaltools\Storelocator\Controller\Checkout
 */
class Stockcheck extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    protected $_jsonResultFactory;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var Session
     */
    protected $_checkoutSession;

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * @var \Totaltools\Storelocator\Helper\Data
     */
    protected $_helper;

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    private $totaltoolsConfig;

    /**
     * @var PostCodeFactory
     */
    protected $postCodeFactory;

    /**
     * Logging instance
     * @var \Totaltools\Postcodes\Model\Logger
     */
    protected $logger;

    /**
     * Stockcheck constructor.
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\ShippingZoneRepository $shippingZoneRepository
     * @param \Magento\Customer\Model\Session $customerSession
     * @param Session $checkoutSession
     * @param \Totaltools\Storelocator\Helper\Data $helper
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig
     * @param PostCodeFactory $postCodeFactory
     * @param \Totaltools\Postcodes\Model\Logger $logger
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\ShippingZoneRepository $shippingZoneRepository,
        \Magento\Customer\Model\Session $customerSession,
        Session $checkoutSession,
        \Totaltools\Storelocator\Helper\Data $helper,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        PostCodeFactory $postCodeFactory,
        \Totaltools\Postcodes\Model\Logger $logger
    ) {
        parent::__construct($context);

        $this->_storeRepository = $storeRepository;
        $this->_checkoutSession = $checkoutSession;
        $this->_jsonResultFactory = $jsonResultFactory;
        $this->_customerSession = $customerSession;
        $this->_helper = $helper;
        $this->totaltoolsConfig = $totalToolsConfig;
        $this->postCodeFactory = $postCodeFactory;
        $this->logger = $logger;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Json|\Magento\Framework\Controller\ResultInterface
     * @throws NoSuchEntityException
     */
    public function execute()
    {
        $result = $this->_jsonResultFactory->create();

        try {
            $this->_validateRequest();
        } catch (NotFoundException $notFoundException) {
            return $result->setData($notFoundException->getMessage());
        }

        $response = $stores = $fallbackStoresCollection = [];
        $postcode = (int) $this->getRequest()->getParam('postcode');

        $defaultShippingMethod = $this->totaltoolsConfig->getDefaultShippingMethod();
        $method = $this->getRequest()->getParam('method', $defaultShippingMethod);

        $sku = $this->getRequest()->getParam('sku', "");
        $limit = $this->getRequest()->getParam('limit', 5);

        $index = 0;

        $resultStore = $this->getByPostcode($postcode);
        if( empty($resultStore->getLatitude()) || empty($resultStore->getLongitude()) )
        {
            $resultStore = $this->_storeRepository->getByPostcodeInZone($postcode);
            $this->logger->info('Postcode exists in Database but lat long is empty: ' . $postcode);
        }
        if ($resultStore) {
            $latitude = $resultStore->getLatitude();
            $longitude = $resultStore->getLongitude();
            $nearestStoresCollection = $this->_storeRepository->getNearestStores($latitude, $longitude, $limit)->getItems();
            foreach ($nearestStoresCollection as $nearestStore) {
                $index++;
                /** @var \Totaltools\Storelocator\Model\Store $nearestStore */
                if (empty($stores[$nearestStore->getId()])) {
                    $stores[$index] = $nearestStore->getData();
                    $stores[$index]['stockMessage'] = $this->_getInventoryMessage($nearestStore->getId(), $method, $sku);
                }
            }

            $response['stores'] = $stores;
        }

        $result->setData($response);

        return $result;
    }

    /**
     * @param int $storeId
     * @param string $method
     * @param string $sku
     * @return mixed
     * @throws NoSuchEntityException
     * @throws \Zend_Mail_Exception
     */
    private function _getInventoryMessage(int $storeId, string $method, string $sku)
    {
        $messages = $this->_helper->getStockAvailabilityMessage($storeId, $method, $sku);

        $maxMessageCode = -1;
        $returnMessage = null;

        foreach ($messages as $message) {
            if (isset($message["code"]) && $message["code"] > $maxMessageCode) {
                $returnMessage = $message;
                $maxMessageCode = $message["code"];
            }
        }

        return $returnMessage;
    }

    /**
     * Get customer preferred store
     *
     * @return bool|Store
     */
    protected function _getCustomerPreferredStore()
    {
        $preferredStore = $this->_customerSession->getCustomerDataObject()->getCustomAttribute('preferred_store');

        if ($preferredStore) {
            try {
                $store = $this->_storeRepository->getByErpId($preferredStore->getValue());
            } catch (NoSuchEntityException $exception) {
                return false;
            }

            return $store;
        }

        return false;
    }

    /**
     * Validates request.
     *
     * @return void
     *
     * @throws NotFoundException
     */
    protected function _validateRequest()
    {
        if (!$this->getRequest()->isAjax()
            || !$this->getRequest()->isPost()
        ) {
            throw new NotFoundException(__('Request type is incorrect'));
        }
    }

    /**
     * Return post code data
     *
     * @param string $postcode
     *
     * @return \Magento\Framework\DataObject|null
     */
    protected function getByPostcode($postcode)
    { 
        $collection = $this->postCodeFactory->create()->getCollection();
        $collection->addFieldToFilter('postcode', ['eq' => $postcode]);
        if($collection)
        {
            return $collection->getFirstItem();
        }   
        return false;
    }
}