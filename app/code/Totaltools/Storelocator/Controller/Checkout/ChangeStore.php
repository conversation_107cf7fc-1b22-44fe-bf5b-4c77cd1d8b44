<?php

namespace Totaltools\Storelocator\Controller\Checkout;

use Psr\Log\LoggerInterface;

class ChangeStore extends \Magento\Framework\App\Action\Action
{
    /**
     * JSON Factory.
     *
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    protected $_resultJsonFactory;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var \Totaltools\Storelocator\Helper\Checkout\Data
     */
    protected $_checkoutHelper;

    /**
     * ChangeStore constructor.
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper
    )
    {
        parent::__construct($context);
        $this->_resultJsonFactory = $resultJsonFactory;
        $this->_checkoutSession = $checkoutSession;
        $this->_storeRepository = $storeRepository;
        $this->_checkoutHelper = $checkoutHelper;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute()
    {
        $storepickup_session = ['store_id' => null];
        $storeId = $this->getRequest()->getParam('store_id');

        if (in_array($storeId, [208, 407, 316, 501])) {
            /** @var \Psr\Log\LoggerInterface $logger */
            $logger = $this->_objectManager->create(\Psr\Log\LoggerInterface::class);
            $logger->warning('Erp ID found in Change Store Request: ' . $storeId, ['referer' => $this->_redirect->getRefererUrl()]);
        }

        if (!empty($storeId)) {
            $this->_checkoutHelper->setSessionStoreId($storeId);
            $storepickup_session['store_id'] = $storeId;
            $this->_checkoutSession->setData('storepickup_session', $storepickup_session);
        }

        return $this->getResponse()->setBody(\Laminas\Json\Json::encode($storepickup_session));
    }
}
