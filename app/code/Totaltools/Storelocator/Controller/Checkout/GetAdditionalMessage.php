<?php

namespace Totaltools\Storelocator\Controller\Checkout;

class GetAdditionalMessage extends \Magento\Framework\App\Action\Action
{

    public function __construct(
        \Magento\Framework\App\Action\Context $context
    ) {
        parent::__construct($context);

    }

    public function execute()
    {

        return $this->getResponse()->setBody(\Laminas\Json\Json::encode("Dummy message"));
    }
}
