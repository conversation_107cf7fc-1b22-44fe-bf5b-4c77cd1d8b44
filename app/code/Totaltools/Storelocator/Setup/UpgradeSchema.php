<?php
/**
 * Created by PhpStorm.
 * User: andrey
 * Date: 29.01.19
 * Time: 11:06
 */

namespace Totaltools\Storelocator\Setup;

use Magento\Framework\DB\Ddl\Table as Table;
use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magestore\Storelocator\Setup\InstallSchema as StorelocatorSchema;
use Totaltools\Storelocator\Api\Data\StoreFallbackInterface;

class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * @var string
     */
    private $_setupVersion;

    const SCHEMA_ZONE = 'magestore_storelocator_zone';

    const SCHEMA_STORE_STORE_INVENTORY = 'magestore_storelocator_store_inventory';

    const SCHEMA_STORE = 'magestore_storelocator_store';

    const FALLBACK_TABLE_NAME = 'magestore_storelocator_fallback';

    /**
     * Installs the data.
     *
     * @param SchemaSetupInterface $setup Setup model.
     * @param ModuleContextInterface $context Context model.
     *
     * @return void
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $this->_setupVersion = $context->getVersion();
        unset($context);

        $setup->startSetup();

        if (version_compare($this->_setupVersion, '1.0.2', '<')) {
            $this->_addAllowStoreCollectionAttribute($setup);
            $this->_addErpCodeAttribute($setup);
            $this->_addErpIdAttribute($setup);
            $this->_addWarehouseCodeAttribute($setup);
            $this->_addPostcodeZonesAttribute($setup);
            $this->_addShippitProfile($setup);
            $this->_addFallbackStores($setup);
            $this->_addStoreInventory($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.3', '<')) {
            $this->_addPickupStoreFallbackTable($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.4', '<')) {
            $this->_addIndexToInventoryTable($setup);
            $this->_updateShippitProfile($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.7', '<')) {
            $this->_addUniqueIndexToInventoryTable($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.9', '<')) {
            $this->addStateIdColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.10', '<')) {
            $this->_addWarehouseOrderColumn($setup);
            $this->_addStoreOrderColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.12', '<')) {
            $this->addStorelocatorIdColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.13', '<')) {
            $this->addShippitApiKeySecondColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.14', '<')) {
            $this->addStorelocatorApiTypeColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.15', '<')) {
            $this->addIsVisibleColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.16', '<')) {
            $this->updateStoreOrderColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.17', '<')) {
            $this->addStoreAdminEmail($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.18', '<')) {
            $this->addSuperStoreColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.19', '<')) {
            $this->addB2bStoreAdminEmail($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.20', '<')) {
            $this->addStoreDirectionUrl($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.22', '<')) {
            $this->modifyErpIdColumn($setup);
        }

        // Allow Delivery Orders 
        if (version_compare($this->_setupVersion, '1.0.23', '<')) {
            $this->addAllowDeliveryOrders($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.24', '<')) {
            $this->addSohApiEndpoints($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.25', '<')) {
            $this->addStoreEscalationsEmail($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.26', '<')) {
            $this->addAllowUberOrders($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.27', '<')) {
            $this->addUberTimeSlotColumns($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.28', '<')) {
            $this->addNewStoreColumns($setup);
        }

        $setup->endSetup();
    }

    /**
     * Created uniaue index
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addUniqueIndexToInventoryTable(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addIndex(
            $setup->getTable(self::SCHEMA_STORE_STORE_INVENTORY),
            $setup->getIdxName($setup->getTable(self::SCHEMA_STORE_STORE_INVENTORY), ['erp_id', 'sku']),
            ['erp_id', 'sku'],
            \Magento\Framework\DB\Adapter\AdapterInterface::INDEX_TYPE_UNIQUE
        );
    }

    /**
     * Add index to inventory table
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addIndexToInventoryTable(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addIndex(
            $setup->getTable(self::SCHEMA_STORE_STORE_INVENTORY),
            $setup->getIdxName(self::SCHEMA_STORE_STORE_INVENTORY, ['erp_id', 'sku']),
            ['erp_id', 'sku']
        );
    }

    /**
     * Allow store collection.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addAllowStoreCollectionAttribute(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'allow_store_collection',
            [
                'type' => Table::TYPE_BOOLEAN,
                'length' => '12,8',
                'nullable' => false,
                'default' => true,
                'comment' => 'Allow Store Pickup',
            ]
        );
    }

    /**
     * Add state id.
     *
     * @param SchemaSetupInterface $setup
     */
    private function addStateIdColumn(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'state_id',
            [
                'type' => Table::TYPE_INTEGER,
                'length' => '11',
                'nullable' => true,
                'comment' => 'State Id',
            ]
        );
    }

    /**
     * Add Store Admin Email.
     *
     * @param SchemaSetupInterface $setup
     */
    private function addStoreAdminEmail(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'store_admin_email',
            [
                'type' => Table::TYPE_TEXT,
                'length' => '255',
                'nullable' => false,
                'comment' => 'Store Admin Email',
            ]
        );
    }

    /**
     * Add Store Escalations Email.
     *
     * @param SchemaSetupInterface $setup
     */
    private function addStoreEscalationsEmail(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'store_escalations_email',
            [
                'type' => Table::TYPE_TEXT,
                'length' => '255',
                'nullable' => false,
                'comment' => 'Store Escalations Email',
            ]
        );
    }

    /**
     * ERP ID.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addErpIdAttribute(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'erp_id',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 15,
                'nullable' => true,
                'comment' => 'ERP ID',
            ]
        );
    }

    /**
     * ERP code.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addErpCodeAttribute(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'erp_code',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 127,
                'nullable' => true,
                'comment' => 'ERP CODE',
            ]
        );
    }

    /**
     * Warehouse code.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addWarehouseCodeAttribute(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'warehouse_code',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Warehouse Code',
            ]
        );
    }

    /**
     * SOH API Endpoint
     *
     * @param SchemaSetupInterface $setup
     */
    private function addSohApiEndpoints(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'full_soh_api_endpoint',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Full SOH API Endpoint',
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'delta_soh_api_endpoint',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Delta SOH API Endpoint',
            ]
        );
    }

    /**
     * Uber Time Slot Columns
     *
     * @param SchemaSetupInterface $setup
     */
    private function addUberTimeSlotColumns(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'uber_start_at',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => false,
                'default' => '9',
                'comment' => 'Uber Time Slot Start At',
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'uber_end_at',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => false,
                'default' => '15',
                'comment' => 'Uber Time Slot End At',
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'uber_available_days',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => false,
                'default' => '1,2,3,4,5',
                'comment' => 'Uber Available on Days of the Week',
            ]
        );
    }

    /**
     * Postcode zones.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addPostcodeZonesAttribute(SchemaSetupInterface $setup)
    {
        /**
         * Create table 'magestore_Storelocator_zone'
         */
        $zoneTable = $setup->getConnection()->newTable(
            $setup->getTable(self::SCHEMA_ZONE)
        )->addColumn(
            'zone_id',
            Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'Id'
        )->addColumn(
            'name',
            Table::TYPE_TEXT,
            null,
            ['nullable' => false, 'unique' => true, 'length' => 50],
            'Name'
        )->addColumn(
            'country_code',
            Table::TYPE_TEXT,
            null,
            ['nullable' => false, 'length' => 2],
            'Country Code'
        )->addColumn(
            'ranges',
            Table::TYPE_TEXT,
            null,
            ['nullable' => true],
            'ranges'
        )->setComment(
            'Magestore Storelocator Zone'
        );

        $setup->getConnection()->createTable($zoneTable);

        /**
         * Add column zone_id to magestore_Storelocator_store
         */
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'zone_id',
            [
                'type' => Table::TYPE_INTEGER,
                'unsigned' => true,
                'nullable' => true,
                'default' => null,
                'comment' => 'Zone ID',
            ]
        );
        $setup->getConnection()->addIndex(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            $setup->getIdxName(StorelocatorSchema::SCHEMA_STORE, ['zone_id']),
            ['zone_id']
        );
        $setup->getConnection()->addForeignKey(
            $setup->getFkName(StorelocatorSchema::SCHEMA_STORE, 'zone_id', self::SCHEMA_ZONE, 'zone_id'),
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'zone_id',
            $setup->getTable(self::SCHEMA_ZONE),
            'zone_id',
            Table::ACTION_SET_NULL
        );
    }

    /**
     * Shippit Profile & Authorization.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addShippitProfile(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_mode',
            [
                'type' => Table::TYPE_BOOLEAN,
                'nullable' => false,
                'default' => true,
                'comment' => 'Account Mode'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_sandbox',
            [
                'type' => Table::TYPE_BOOLEAN,
                'nullable' => false,
                'default' => true,
                'comment' => 'Account Sandbox'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_username',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Account Username'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_password',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Account Password'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_clientid',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Client ID'
            ]
        );
    }

    /**
     * Replace columns for Shippit profile per store.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _updateShippitProfile(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->dropColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_mode'
        );
        $setup->getConnection()->dropColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_sandbox'
        );
        $setup->getConnection()->dropColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_username'
        );
        $setup->getConnection()->dropColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_password'
        );
        $setup->getConnection()->dropColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'account_clientid'
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'shippit_api_key',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Shippit Api Key'
            ]
        );
    }

    /**
     * Fallback stores.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addFallbackStores(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'supporting_origins',
            [
                'type' => Table::TYPE_TEXT,
                'nullable' => true,
                'comment' => 'Fallback Stores'
            ]
        );
    }

    /**
     * Inventory.
     *
     * @param SchemaSetupInterface $setup
     */
    private function _addStoreInventory(SchemaSetupInterface $setup)
    {
        /**
         * Create table 'magestore_Storelocator_store_inventory'
         */
        $inventoryTable = $setup->getConnection()->newTable(
            $setup->getTable(self::SCHEMA_STORE_STORE_INVENTORY)
        )->addColumn(
            'inventory_id',
            Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'Id'
        )->addColumn(
            'erp_id',
            Table::TYPE_INTEGER,
            null,
            ['unsigned' => true, 'nullable' => false],
            'ERP Id'
        )->addColumn(
            'sku',
            Table::TYPE_TEXT,
            64,
            ['unsigned' => true, 'nullable' => false, 'length' => 64],
            'Product SKU'
        )->addColumn(
            'units',
            Table::TYPE_INTEGER,
            null,
            ['nullable' => false, 'length' => 50],
            'Units'
        )->setComment(
            'Magestore Storelocatory Store Inventory'
        );

        $setup->getConnection()->createTable($inventoryTable);
    }

    private function _addPickupStoreFallbackTable(SchemaSetupInterface $setup)
    {
        $fallBackTable = $setup->getConnection()->newTable(
            $setup->getTable(self::FALLBACK_TABLE_NAME)
        )->addColumn(
            StoreFallbackInterface::ROW_ID,
            Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'Primary Id'
        )->addColumn(
            StoreFallbackInterface::STORE_ID,
            Table::TYPE_INTEGER,
            null,
            ['unsigned' => true, 'nullable' => false, 'primary' => false],
            'Pickup Store Id'
        )->addColumn(
            StoreFallbackInterface::FALLBACK_STORE_ID,
            Table::TYPE_INTEGER,
            null,
            ['unsigned' => true, 'nullable' => false, 'primary' => false],
            'Fallback Store Id'
        )->addForeignKey(
            $setup->getFkName(
                self::FALLBACK_TABLE_NAME,
                StoreFallbackInterface::STORE_ID,
                self::SCHEMA_STORE,
                'storelocator_id'
            ),
            StoreFallbackInterface::STORE_ID,
            $setup->getTable(self::SCHEMA_STORE),
            'storelocator_id',
            Table::ACTION_CASCADE
        )->addForeignKey(
            $setup->getFkName(
                self::FALLBACK_TABLE_NAME,
                StoreFallbackInterface::FALLBACK_STORE_ID,
                self::SCHEMA_STORE,
                'storelocator_id'
            ),
            StoreFallbackInterface::FALLBACK_STORE_ID,
            $setup->getTable(self::SCHEMA_STORE),
            'storelocator_id',
            Table::ACTION_CASCADE
        )->addIndex(
            StoreFallbackInterface::STORE_ID . '_' . StoreFallbackInterface::FALLBACK_STORE_ID,
            [StoreFallbackInterface::STORE_ID, StoreFallbackInterface::FALLBACK_STORE_ID]
        )->setComment(
            'Magestore Storelocator Fallback'
        );

        $setup->getConnection()->createTable($fallBackTable);
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function _addWarehouseOrderColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable('sales_order'), 'warehouse')) {
            $setup->getConnection()->modifyColumn(
                $setup->getTable('sales_order'),
                'warehouse',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 3,
                    'nullable' => true,
                    'comment' => 'ERP Code Storelocator Store value'
                ]
            );
        } else {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'warehouse',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 3,
                    'nullable' => true,
                    'comment' => 'ERP Code Storelocator Store value'
                ]
            );
        }
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function _addStoreOrderColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable('sales_order'), 'store')) {
            $setup->getConnection()->modifyColumn(
                $setup->getTable('sales_order'),
                'store',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_SMALLINT,
                    'length' => 3,
                    'nullable' => true,
                    'comment' => 'ERP ID Storelocator Store value'
                ]
            );
        } else {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'store',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_SMALLINT,
                    'length' => 3,
                    'nullable' => true,
                    'comment' => 'ERP ID Storelocator Store value'
                ]
            );
        }
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function updateStoreOrderColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable('sales_order'), 'store')) {
            $setup->getConnection()->modifyColumn(
                $setup->getTable('sales_order'),
                'store',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 255,
                    'nullable' => true,
                    'comment' => 'ERP Code Storelocator Store value'
                ]
            );
        } else {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'store',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 255,
                    'nullable' => true,
                    'comment' => 'ERP Code Storelocator Store value'
                ]
            );
        }
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function addStorelocatorIdColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable('sales_order'), 'storelocator_id')) {
            $setup->getConnection()->modifyColumn(
                $setup->getTable('sales_order'),
                'storelocator_id',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                    'length' => 5,
                    'nullable' => true,
                    'comment' => 'Storelocator ID'
                ]
            );
        } else {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'storelocator_id',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                    'length' => 5,
                    'nullable' => true,
                    'comment' => 'Storelocator ID'
                ]
            );
        }
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function addShippitApiKeySecondColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable(StorelocatorSchema::SCHEMA_STORE), 'shippit_api_key_second') === false) {
            $setup->getConnection()->addColumn(
                $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
                'shippit_api_key_second',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 255,
                    'nullable' => true,
                    'comment' => 'Shippit Api Key Second'
                ]
            );
        }
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function addStorelocatorApiTypeColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable('sales_order'), 'storelocator_api_type')) {
            $setup->getConnection()->modifyColumn(
                $setup->getTable('sales_order'),
                'storelocator_api_type',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                    'length' => 5,
                    'nullable' => true,
                    'default' => 1,
                    'comment' => 'Storelocator Api Type'
                ]
            );
        } else {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'storelocator_api_type',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                    'length' => 5,
                    'nullable' => true,
                    'default' => 1,
                    'comment' => 'Storelocator Api Type'
                ]
            );
        }
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function addIsVisibleColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable(StorelocatorSchema::SCHEMA_STORE), 'is_visible') === false) {
            $setup->getConnection()->addColumn(
                $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
                'is_visible',
                [
                    'type' => Table::TYPE_BOOLEAN,
                    'nullable' => false,
                    'default' => true,
                    'comment' => 'Is Visible'
                ]
            );
        }
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function addSuperStoreColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable('magestore_storelocator_store'), 'super_store_id')) {
            $setup->getConnection()->modifyColumn(
                $setup->getTable('magestore_storelocator_store'),
                'super_store_id',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                    'length' => 5,
                    'nullable' => true,
                    'comment' => 'Super Store Id'
                ]
            );
        } else {
            $setup->getConnection()->addColumn(
                $setup->getTable('magestore_storelocator_store'),
                'super_store_id',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                    'length' => 5,
                    'nullable' => true,
                    'comment' => 'Super Store Id'
                ]
            );
        }
    }

    /**
     * Add Store Admin Email.
     *
     * @param SchemaSetupInterface $setup
     */
    private function addB2bStoreAdminEmail(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'commercial_store_admin_email',
            [
                'type' => Table::TYPE_TEXT,
                'length' => '255',
                'nullable' => true,
                'comment' => 'B2B Store Admin Email',
            ]
        );
    }

    /**
     * Add Store Direction URL.
     *
     * @param SchemaSetupInterface $setup
     */
    private function addStoreDirectionUrl(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'store_direction_url',
            [
                'type' => Table::TYPE_TEXT,
                'nullable' => true,
                'comment' => 'Store Direction URL',
            ]
        );
    }

    private function modifyErpIdColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists(self::SCHEMA_STORE_STORE_INVENTORY, 'erp_id')) {
            $setup->getConnection()->modifyColumn(
                self::SCHEMA_STORE_STORE_INVENTORY,
                'erp_id',
                [
                    'type' => Table::TYPE_TEXT,
                    'length' => 4,
                    'nullable' => false,
                    'comment' => 'ERP ID'
                ]
            );
        }
    }

    /**
     * Allow Delivery Orders.
     *
     * @param SchemaSetupInterface $setup
     */
    private function addAllowDeliveryOrders(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'allow_delivery_orders',
            [
                'type' => Table::TYPE_BOOLEAN,
                'nullable' => false,
                'default' => true,
                'comment' => 'Allow Delivery Orders',
            ]
        );
    }

    /**
     * Allow Uber Orders
     *
     * @param SchemaSetupInterface $setup
     */
    private function addAllowUberOrders(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'allow_uber_orders',
            [
                'type' => Table::TYPE_BOOLEAN,
                'nullable' => false,
                'default' => true,
                'comment' => 'Allow Uber Orders',
            ]
        );
    }

    /**
     * Store New Columns
     *
     * @param SchemaSetupInterface $setup
     */
    private function addNewStoreColumns(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'structure',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Structure'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'franchisee_group',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Franchisee Group'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable(StorelocatorSchema::SCHEMA_STORE),
            'location',
            [
                'type' => Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Location',
            ]
        );
    }
}
