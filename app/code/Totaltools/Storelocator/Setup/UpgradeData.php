<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Setup;

use Magento\Customer\Model\Customer;
use Magento\Customer\Setup\CustomerSetup;
use Magento\Framework\App\Config\ConfigResource\ConfigInterface as ConfigInterfaceAlias;
use Magento\Framework\App\Config\ScopeConfigInterface as ScopeConfig;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Eav\Model\Entity\Attribute\SetFactory as AttributeSetFactory;
use Totaltools\Storelocator\Model\StoreRepository;
use Magestore\Storelocator\Setup\InstallSchema;

/**
 * Class UpgradeData
 * @package Totaltools\Storelocator\Setup
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var string $_setupVersion Current setup version.
     */
    private $_setupVersion;
    /**
     * @var \Magento\Customer\Setup\CustomerSetupFactory
     */
    private $customerSetupFactory;
    /**
     * @var AttributeSetFactory
     */
    private $attributeSetFactory;
    /**
     * @var ConfigInterfaceAlias
     */
    private $_config;

    /**
     * @var StoreRepository
     */
    private $_storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\TemandoMigration
     */
    private $temando;

    /**
     * @var ResourceConnection
     */
    private $_resourceConnection;
    /**
     * UpgradeData constructor.
     * @param \Magento\Customer\Setup\CustomerSetupFactory $customerSetupFactory
     * @param AttributeSetFactory $attributeSetFactory
     * @param ConfigInterfaceAlias $config
     * @param \Totaltools\Storelocator\Model\TemandoMigration $temandoMigration
     * @param StoreRepository $storeRepository
     */
    public function __construct(
        \Magento\Customer\Setup\CustomerSetupFactory $customerSetupFactory,
        AttributeSetFactory $attributeSetFactory,
        ConfigInterfaceAlias $config,
        \Totaltools\Storelocator\Model\TemandoMigration $temandoMigration,
        StoreRepository $storeRepository,
        ResourceConnection $resourceConnection
    ) {
        $this->customerSetupFactory = $customerSetupFactory;
        $this->attributeSetFactory = $attributeSetFactory;
        $this->_config = $config;
        $this->temando = $temandoMigration;
        $this->_storeRepository = $storeRepository;
        $this->_resourceConnection = $resourceConnection;
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface   $context
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $this->_setupVersion = $context->getVersion();
        $setup->startSetup();

        if (version_compare($this->_setupVersion, '1.0.6') < 0) {
            $this->_createCustomerPreferedStoreAttribute($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.18') < 0) {
            $this->_updateSuperStoreId($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.19') < 0) {
            $this->_updateB2bStoreAdminEmail($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.21') < 0) {
            $this->_updateStoreDirectionUrls($setup);
        }

        $this->_configureShippitSettings();

        $setup->endSetup();
    }

    /**
     * Update Shippit configuration.
     *
     * @return void
     */
    private function _configureShippitSettings()
    {
        if (version_compare($this->_setupVersion, '1.0.11') < 0) {
            $this->_config->saveConfig('shippit/sync_order/mode', 'custom', ScopeConfig::SCOPE_TYPE_DEFAULT, 0);
        }
    }

    /**
     * Create preferred store attribute type
     *
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _createCustomerPreferedStoreAttribute($setup)
    {
        $this->temando->migrate();
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->customerSetupFactory->create(['setup' => $setup]);
        $customerSetup->removeAttribute(Customer::ENTITY, 'customer_preferred_store');
        $this->_createCustomerAttribute(
            $setup,
            'preferred_store',
            Customer::ENTITY,
            [
                'label' => 'Preferred Store',
                'type' => 'int',
                'source' => \Totaltools\Storelocator\Model\Config\Source\Store::class,
                'input' => 'select',
                'visible' => true,
                'required' => false,
                'system' => 0,
                'user_defined' => false,
                'position' => 1070,
            ],
            [
                'adminhtml_customer',
            ]
        );
    }

    /**
     * Create customer attribute by parameters.
     *
     * @param ModuleDataSetupInterface $setup
     * @param $entityType
     * @param string $attributeCode
     * @param array $attributeParams
     * @param array $usedInForms
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _createCustomerAttribute($setup, $attributeCode, $entityType, $attributeParams, $usedInForms)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->customerSetupFactory->create(['setup' => $setup]);

        $customerEntity = $customerSetup->getEavConfig()->getEntityType($entityType);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $attributeSetId = $customerEntity->getDefaultAttributeSetId();

        $attributeSet = $this->attributeSetFactory->create();
        $attributeGroupId = $attributeSet->getDefaultGroupId($attributeSetId);

        $customerSetup->removeAttribute($entityTypeId, $attributeCode);
        $customerSetup->addAttribute(
            $entityTypeId,
            $attributeCode,
            $attributeParams
        );

        $magentoAttribute = $customerSetup->getEavConfig()->getAttribute($entityType, $attributeCode);
        $magentoAttribute->addData([
            'attribute_set_id'   => $attributeSetId,
            'attribute_group_id' => $attributeGroupId,
            'used_in_forms'      => $usedInForms,
        ]);

        $magentoAttribute->save();

        $customerSetup->updateAttribute($entityTypeId, $attributeCode, 'is_used_for_customer_segment', '1');
    }

    /**
     * @param ModuleDataSetupInterface $setup
     */
    private function _updateSuperStoreId(ModuleDataSetupInterface $setup) {
        $storeCollection = $this->_storeRepository->getCollection();
        foreach ($storeCollection->getItems() as $store) {
            $this->_resourceConnection->getConnection()->query('UPDATE magestore_storelocator_store SET super_store_id = :super_store_id WHERE storelocator_id = :storelocator_id',
                [
                    'super_store_id' => $store->getId(),
                    'storelocator_id' => $store->getId()
                ]);
        }
    }

    /**
     * @param ModuleDataSetupInterface $setup
     */
    private function _updateB2bStoreAdminEmail(ModuleDataSetupInterface $setup) {
        $storeCollection = $this->_storeRepository->getCollection();
        foreach ($storeCollection->getItems() as $store) {
            $this->_resourceConnection->getConnection()->query('UPDATE magestore_storelocator_store SET commercial_store_admin_email = :commercial_store_admin_email WHERE storelocator_id = :storelocator_id',
                [
                    'commercial_store_admin_email' => $store->getStoreAdminEmail(),
                    'storelocator_id' => $store->getId()
                ]);
        }
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @return void
     */
    private function _updateStoreDirectionUrls(ModuleDataSetupInterface $setup)
    {
        $conn = $setup->getConnection();
        $table = $setup->getTable(InstallSchema::SCHEMA_STORE);
        $data = [
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Auburn/@-33.8390829,151.029783,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x1e9bbebf98ac62d6!8m2!3d-33.8390829!4d151.0319717', 'storelocator_id' => 597],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Bendigo/@-36.8070091,144.2395313,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xbf902a619fffbf4a!8m2!3d-36.8070091!4d144.24172', 'storelocator_id' => 598],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Bunbury/@-33.3427184,115.6519952,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xa7e0888d2838534c!8m2!3d-33.3427184!4d115.6541839', 'storelocator_id' => 599],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Burleigh+Waters/@-28.0993864,153.4205437,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x5a3617c6d98c8d13!8m2!3d-28.0993864!4d153.4227324', 'storelocator_id' => 600],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Canning+Vale/@-32.0595928,115.9072497,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xbd02d129c5c81ee3!8m2!3d-32.0595928!4d115.9094384', 'storelocator_id' => 601],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Darwin/@-12.4271942,130.8859272,16.25z/data=!4m5!3m4!1s0x0:0x32369ac3ba1b3170!8m2!3d-12.4260594!4d130.8896666', 'storelocator_id' => 602],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+East+Brisbane/@-27.4859463,153.0381919,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x625a629736e7a402!8m2!3d-27.4859463!4d153.0403806', 'storelocator_id' => 603],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Elizabeth/@-34.7207828,138.6687725,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xfac7d9b85bf8557c!8m2!3d-34.7207828!4d138.6709612', 'storelocator_id' => 604],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Fyshwick/@-35.3221611,149.1785164,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x8db9adc2fb28dcdb!8m2!3d-35.3221611!4d149.1807051', 'storelocator_id' => 605],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Joondalup/@-31.74289,115.7600713,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x6447a8f56aa73f29!8m2!3d-31.74289!4d115.76226', 'storelocator_id' => 606],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Mildura/@-34.1962855,142.1673132,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x155f966c471210c0!8m2!3d-34.1962855!4d142.1695019', 'storelocator_id' => 607],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Midland/@-31.8930041,116.015279,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xd3d5a10142b19936!8m2!3d-31.8930041!4d116.0174677', 'storelocator_id' => 608],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Mitcham/@-37.8167119,145.1985773,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x4f482fe5cfb8a73c!8m2!3d-37.8167119!4d145.200766', 'storelocator_id' => 609],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Melton/@-37.6849093,144.5929567,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x236e0416fe8662a5!8m2!3d-37.6849093!4d144.5951454', 'storelocator_id' => 610],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Mt+Waverley/@-37.898677,145.1241916,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x1c77eaa06c46fdb6!8m2!3d-37.898677!4d145.1263803', 'storelocator_id' => 611],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Gregory+Hills/@-34.0249989,150.7594833,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xe6a14028a1f6cedb!8m2!3d-34.0249989!4d150.761672', 'storelocator_id' => 612],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+O\'Connor/@-32.054148,115.7906309,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x475274eac54c2bf1!8m2!3d-32.054148!4d115.7928196', 'storelocator_id' => 613],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Rockingham/@-32.2765235,115.766488,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x204950ea7f4a6755!8m2!3d-32.2765235!4d115.7686767', 'storelocator_id' => 614],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Shepparton/@-36.385392,145.4153614,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x971a4949c0cf69c2!8m2!3d-36.385392!4d145.4175501', 'storelocator_id' => 615],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Springwood/@-27.6278805,153.1268916,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x9ce2c36f01611300!8m2!3d-27.6278805!4d153.1290803', 'storelocator_id' => 616],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Sunbury/@-37.5888994,144.713969,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xa0dee268ee60434!8m2!3d-37.5888994!4d144.7161577', 'storelocator_id' => 617],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Taren+Point/@-34.0219341,151.1195246,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x24dae6910c30f42f!8m2!3d-34.0219341!4d151.1217133', 'storelocator_id' => 618],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Brendale/@-27.3188044,152.9849829,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xd12d24f6b7288a12!8m2!3d-27.3188044!4d152.9871716', 'storelocator_id' => 619],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Ballarat/@-37.5720249,143.8222822,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xb9e527d3d57c47e3!8m2!3d-37.5720249!4d143.8244709', 'storelocator_id' => 620],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Brooklyn/@-37.8214688,144.8286509,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x1fdae137ed23ba85!8m2!3d-37.8214688!4d144.8308396', 'storelocator_id' => 621],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Braeside/@-37.9939436,145.1036349,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x197a5a5ea8f02bdc!8m2!3d-37.9939436!4d145.1058236', 'storelocator_id' => 622],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Biggera+Waters/@-27.9324455,153.3824133,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xf8376f976efb0904!8m2!3d-27.9324455!4d153.384602', 'storelocator_id' => 623],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Carrum+Downs/@-38.1002804,145.1685536,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xf3d8676d0daacb62!8m2!3d-38.1002804!4d145.1707423', 'storelocator_id' => 624],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Fountain+Gate/@-38.025053,145.3063593,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x789c4a2cfd1992fc!8m2!3d-38.025053!4d145.308548', 'storelocator_id' => 625],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Dandenong/@-38.0119712,145.194722,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xb960240178b86ce3!8m2!3d-38.0119712!4d145.1969107', 'storelocator_id' => 626],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Ferntree+Gully/@-37.8810414,145.2685653,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xc81444af62229e2c!8m2!3d-37.8810414!4d145.270754', 'storelocator_id' => 627],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Geelong/@-38.1095967,144.3421212,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x8146b36341d926a6!8m2!3d-38.1095967!4d144.3443099', 'storelocator_id' => 628],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Hoppers+Crossing/@-37.8720586,144.721688,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x27d833f6b64374da!8m2!3d-37.8720586!4d144.7238767', 'storelocator_id' => 629],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Kewdale/@-31.9791588,115.957334,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xc9b74120bc11c06e!8m2!3d-31.9791588!4d115.9595227', 'storelocator_id' => 630],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Kilsyth/@-37.8212694,145.3059225,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x2e7f76025652ebd2!8m2!3d-37.8212694!4d145.3081112', 'storelocator_id' => 631],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Lonsdale/@-35.1059795,138.4982341,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x1c1ef6f142afb517!8m2!3d-35.1059795!4d138.5004228', 'storelocator_id' => 632],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Lansvale/@-33.8950298,150.9553697,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x95c0ad5fafca1259!8m2!3d-33.8950298!4d150.9575584', 'storelocator_id' => 633],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Maroochydore/@-26.6639817,153.0422582,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xff5dc706ed6e8a57!8m2!3d-26.6639817!4d153.0444469', 'storelocator_id' => 634],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Mackay/@-21.1673027,149.1523857,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x24d9a9d532c811be!8m2!3d-21.1673027!4d149.1545744', 'storelocator_id' => 635],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Mornington/@-38.2365264,145.058086,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x273c1a3b4b47b2ab!8m2!3d-38.2365264!4d145.0602747', 'storelocator_id' => 636],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Moorabbin/@-37.9488255,145.0765062,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x4101089da87340dd!8m2!3d-37.9488255!4d145.0786949', 'storelocator_id' => 637],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Pakenham/@-38.0987286,145.4877512,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x8ac8dabc91c3ea24!8m2!3d-38.0987286!4d145.4899399', 'storelocator_id' => 638],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Penrith/@-33.7439121,150.6955564,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x597595fd11b00ac4!8m2!3d-33.7439121!4d150.6977451', 'storelocator_id' => 639],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Preston/@-37.7469108,145.0235517,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xeec4da3a3244a68e!8m2!3d-37.7469108!4d145.0257404', 'storelocator_id' => 640],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Townsville/@-19.2756969,146.7547012,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x6eeebef0b42934a2!8m2!3d-19.2756969!4d146.7568899', 'storelocator_id' => 641],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Albury/@-36.0755278,146.9444891,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x789a876b6f42d613!8m2!3d-36.0755278!4d146.9466778', 'storelocator_id' => 642],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Thebarton/@-34.9146231,138.5750977,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xe20b6145ef47c20!8m2!3d-34.9146231!4d138.5772864', 'storelocator_id' => 643],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Epping/@-37.6484894,145.0007433,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xfb335841e26f2a94!8m2!3d-37.6484894!4d145.002932', 'storelocator_id' => 644],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Coopers+Plains/@-27.5695398,153.0248902,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x56bb8a03654872db!8m2!3d-27.5695398!4d153.0270789', 'storelocator_id' => 645],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Traralgon/@-38.1919345,146.5572431,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xfb6765b10dad6eed!8m2!3d-38.1919345!4d146.5594318', 'storelocator_id' => 646],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Tullamarine/@-37.6919286,144.8785834,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x4ac5f63163827883!8m2!3d-37.6919286!4d144.8807721', 'storelocator_id' => 647],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Toowoomba/@-27.5514292,151.907374,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xb5ba6a32ce84d459!8m2!3d-27.5514292!4d151.9095627', 'storelocator_id' => 648],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Wagga/@-35.1181612,147.3459611,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xd5dbabc349b6e321!8m2!3d-35.1181612!4d147.3481498', 'storelocator_id' => 649],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Warners+Bay/@-32.9654147,151.6524383,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xbd1295787564d9c8!8m2!3d-32.9654147!4d151.654627', 'storelocator_id' => 650],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Wingfield/@-34.8494469,138.5581809,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xf4e8fa94ff1aa043!8m2!3d-34.8494469!4d138.5603696', 'storelocator_id' => 651],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Albion+Park/@-34.571869,150.8146559,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x4bc8d3e9dba3bed8!8m2!3d-34.571869!4d150.8168446', 'storelocator_id' => 652],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Ipswich/@-27.607294,152.7585534,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x4886bf073b718a9c!8m2!3d-27.607294!4d152.7607421', 'storelocator_id' => 653],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Mandurah/@-32.50882,115.7372969,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x11a1e856336afa0d!8m2!3d-32.50882!4d115.7394856', 'storelocator_id' => 654],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Cairns/@-16.9404366,145.7649467,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xdbd311baf2f90c44!8m2!3d-16.9404366!4d145.7671354', 'storelocator_id' => 655],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Virginia/@-27.367942,153.0589293,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x6618cf1b597a8e12!8m2!3d-27.367942!4d153.061118', 'storelocator_id' => 656],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Noosaville/@-26.409925,153.0448722,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x2739ac3ee7ad0499!8m2!3d-26.409925!4d153.0470609', 'storelocator_id' => 657],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Port+Macquarie/@-31.4485922,152.8881951,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xba9ecf2dc99c5802!8m2!3d-31.4485922!4d152.8903838', 'storelocator_id' => 658],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Brookvale/@-33.7633027,151.2689007,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xc6a7bddfa1d3d091!8m2!3d-33.7633027!4d151.2710894', 'storelocator_id' => 659],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Tamworth/@-31.0873203,150.9065942,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x8cd680ede1d08769!8m2!3d-31.0873203!4d150.9087829', 'storelocator_id' => 660],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Malaga/@-31.8558151,115.8901187,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x6adf6c6179c17f93!8m2!3d-31.8558151!4d115.8923074', 'storelocator_id' => 661],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Gosford/@-33.4160864,151.3282311,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x94388236d7da9953!8m2!3d-33.4160864!4d151.3304198', 'storelocator_id' => 662],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Hobart/@-42.8355866,147.2888974,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x7c3bcaed9f68b09!8m2!3d-42.8355866!4d147.2910861', 'storelocator_id' => 663],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Sandgate/@-32.874197,151.7105493,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xe18fb642f1afe3fc!8m2!3d-32.874197!4d151.712738', 'storelocator_id' => 664],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Launceston/@-41.4109755,147.1300347,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x3fd1879767259430!8m2!3d-41.4109755!4d147.1322234', 'storelocator_id' => 665],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Bankstown/@-33.9345658,151.0265876,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x958835a2e4e7cc3d!8m2!3d-33.9345658!4d151.0287763', 'storelocator_id' => 666],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Morayfield/@-27.1119899,152.9494104,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xd68ece13edad7c6e!8m2!3d-27.1119899!4d152.9515991', 'storelocator_id' => 667],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Castle+Hill/@-33.7290179,150.9734974,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0xb992451d178851c2!8m2!3d-33.7290179!4d150.9756861', 'storelocator_id' => 668],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+North+Lakes/@-27.2156258,152.9971202,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x2d2b0cb4c0bde366!8m2!3d-27.2156258!4d152.9993089', 'storelocator_id' => 669],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Beenleigh/@-27.7132524,153.1820798,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x2a1072eb79aac91e!8m2!3d-27.7132524!4d153.1842685', 'storelocator_id' => 670],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Thomastown/@-37.6889712,145.0234925,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x27a7bb5c6f61a4ad!8m2!3d-37.6889712!4d145.0256812', 'storelocator_id' => 671],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Smithfield/@-33.8443949,150.9520664,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x62b73fd9d5bf4652!8m2!3d-33.8443949!4d150.9542551', 'storelocator_id' => 672],
            ['store_direction_url' => 'https://www.google.com/maps/place/Total+Tools+Mount+Barker/@-35.052942,138.8429577,17z/data=!3m1!4b1!4m5!3m4!1s0x0:0x40145d7fc9aa682!8m2!3d-35.052942!4d138.8451464', 'storelocator_id' => 673]
        ];

        if ($conn->isTableExists($table)) {
            foreach ($data as $storeData) {
                $storelocatorId = $storeData['storelocator_id'];
                unset($storeData['storelocator_id']);

                $conn->update($table, $storeData, 'storelocator_id = ' . $storelocatorId);
            }
        }
    }
}