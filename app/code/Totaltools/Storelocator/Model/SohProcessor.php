<?php

namespace Totaltools\Storelocator\Model;

use Exception;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Storelocator\Model\ImportInventory\Email;
use Magento\Framework\App\ResourceConnection;
use Totaltools\Pronto\Model\Api\SohApi;
use Zend_Db_Exception;

/**
 * @api
 */
class SohProcessor
{
    /**
     * @var TimezoneInterface
     */
    private $localeDate;

    /**
     * @var Email
     */
    private $transport;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var SohApi
     */
    private $sohApi;

    /**
     * SohProcessor constructor.
     * @param TimezoneInterface $localeDate
     * @param Email $email
     * @param LoggerInterface $logger
     * @param ResourceConnection $resource
     * @param SohApi $sohApi
     */
    public function __construct(
        TimezoneInterface $localeDate,
        Email $email,
        LoggerInterface $logger,
        ResourceConnection $resource,
        SohApi $sohApi
    ) {
        
        $this->localeDate = $localeDate;
        $this->transport = $email;
        $this->logger = $logger;
        $this->resource = $resource;
        $this->sohApi = $sohApi;
    }

    public function importInventory($store)
    {
        $storeFullSohApiEndpoint = $store['full_soh_api_endpoint'];
        $erpId = $store['erp_id'];
        if (!empty($storeFullSohApiEndpoint)) {
            $response = $this->sohApi->sendApiSohRequest($storeFullSohApiEndpoint, $erpId, 'full_soh');

            if (!empty($response['Items']['Item'])) {
                $items = $response['Items']['Item'];
                // check for single result
                $items = isset($items['ItemCode']) ? [$items] : $items;
                $this->cleanupInventoryForStore($erpId);
                $data = [];
                foreach ($items as $item) {
                    $prontoErpId = $item['Warehouse'];
                    if ($prontoErpId == $erpId) {
                        $data[] = [
                            'erp_id'    => $erpId,
                            'sku'       => $item['ItemCode'],
                            'units'     => $item['SOH']
                        ];
                    }
                }

                if (!empty($data)) {
                    $this->importSohInventoryDataForStore($data);
                }
            }
        }
    }

    public function updateInventory($store)
    {
        $updatedRecordsCount = 0;
        $storeDeltaSohApiEndpoint = $store['delta_soh_api_endpoint'];
        $erpId = $store['erp_id'];
        if (!empty($storeDeltaSohApiEndpoint)) {
            $response = $this->sohApi->sendApiSohRequest($storeDeltaSohApiEndpoint, $erpId, 'delta_soh');
            if (!empty($response['Items']['Item'])) {
                $items = $response['Items']['Item'];
                // check for single result
                $items = isset($items['ItemCode']) ? [$items] : $items;
                foreach ($items as $item) {
                    $prontoErpId = $item['Warehouse'];
                    if ($prontoErpId == $erpId) {
                        $data = [
                            'erp_id'    => $erpId,
                            'sku'       => $item['ItemCode'],
                            'units'     => $item['SOH']
                        ];
                        $updatedRecordsCount++;
                        $this->importDelteSohInventoryDataForStore($data);
                    }
                }
            }
        }

        return $updatedRecordsCount;
    }

    public function cleanupInventoryTable()
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('magestore_storelocator_store_inventory');
        $connection->truncateTable($tableName);
    }

    public function cleanupInventoryForStore($erpId)
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('magestore_storelocator_store_inventory');
        $connection->delete($tableName, ['erp_id = ?' => $erpId]);
    }

    public function importSohInventoryDataForStore($data)
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('magestore_storelocator_store_inventory');
        if (empty($data)) {
            return;
        }
        
        $connection->insertMultiple($tableName, $data);
    }

    public function importDelteSohInventoryDataForStore($data)
    {
        $connection = $this->resource->getConnection();
        $tableName = $this->resource->getTableName('magestore_storelocator_store_inventory');
        $connection->insertOnDuplicate($tableName, $data);
    }

    public function getSummaryOfImportedRecords()
    {
        $connection = $this->resource->getConnection();
        $query = "SELECT i.erp_id, s.store_name as store_name, COUNT(i.erp_id) AS total FROM magestore_storelocator_store_inventory i INNER JOIN magestore_storelocator_store s 
        ON i.erp_id = s.erp_id GROUP BY i.erp_id, s.store_name ORDER BY i.erp_id";
        $result = $connection->fetchAll($query);
        return $result;
    }

    public function prepareSummaryOfImportedRecordsEmail()
    {
        $records = $this->getSummaryOfImportedRecords();
        $this->transport->sendSummaryOfImportedRecordsEmail($records);
    }
}
