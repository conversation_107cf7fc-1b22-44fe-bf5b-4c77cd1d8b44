<?php

namespace Totaltools\Storelocator\Model;

use Totaltools\Storelocator\Console\Command\StoreFallbackCommand;
/**
 * Class TemandoMigration
 * @package Totaltools\Storelocator\Model
 */
class TemandoMigration
{
    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $resourceConnection;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;

    /**
     * @var StoreFallbackFactory
     */
    private $fallbackFactory;

    /**
     * @var \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory
     */
    private $storeCollectionFactory;

    /**
     * @var array
     */
    private $storeIdsByOriginId = [];

    /**
     * TemandoMigration constructor.
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     * @param StoreRepository $storeRepository
     * @param StoreFallbackFactory $fallbackFactory
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory
     */
    public function __construct(
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreFallbackFactory $fallbackFactory,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory
    ) {
        $this->resourceConnection = $resourceConnection;
        $this->storeRepository = $storeRepository;
        $this->fallbackFactory = $fallbackFactory;
        $this->storeCollectionFactory = $storeCollectionFactory;
    }

    /**
     * Migrate store locator
     */
    public function migrate()
    {
        $this->resourceConnection->getConnection()->query('DELETE FROM magestore_storelocator_fallback;');
        $this->resourceConnection->getConnection()->query('DELETE FROM magestore_storelocator_store_inventory;');
        $this->resourceConnection->getConnection()->query('DELETE FROM magestore_storelocator_zone;');
        $this->resourceConnection->getConnection()->query('DELETE FROM magestore_storelocator_store;');
        $this->migrateZones();
        $this->migrateInventory();
        $this->migrateStores();
        $this->storeFallBack();
        return $this;
    }

    /**
     * Migrate store fallback
     */
    private function storeFallBack()
    {
        $storeCollection = $this->storeCollectionFactory->create()
            ->addFieldToFilter('supporting_origins', ['neq' => 'NULL'])
            ->addFieldToFilter('temando_origin_id', ['neq' => 0]);

        if ($storeCollection->getSize()) {
            $this->initFallbackIdsByOriginId($storeCollection);

            foreach ($storeCollection->getData() as $data) {
                $fallbackStores = explode(',', $data[StoreFallbackCommand::SUPPORTING_ORIGINS_FIELD]);

                foreach ($fallbackStores as $index => $fallbackStore) {

                    if (empty($this->storeIdsByOriginId [$fallbackStore])) {
                        continue;
                    }

                    $fallbackModel = $this->fallbackFactory->create();
                    $fallbackModel->setData([
                        'store_id' => $data[StoreFallbackCommand::STORELOCATOR_ID_FIELD],
                        'fallback_store_id' => $this->storeIdsByOriginId [$fallbackStore],
                    ]);
                    $fallbackModel->save();
                }
            }
        }

        $this->resourceConnection->getConnection()->dropColumn('magestore_storelocator_store', 'temando_origin_id');
    }

    /**
     * Get new store id by origin id
     *
     * @param $storeCollection
     */
    private function initFallbackIdsByOriginId($storeCollection)
    {
        foreach ($storeCollection->getData() as $index => $data) {
            $this->storeIdsByOriginId [$data['temando_origin_id']] = $data['storelocator_id'];
        }
    }


    /**
     * Migrate zones
     */
    private function migrateZones()
    {
        $this->resourceConnection->getConnection()->query('INSERT INTO magestore_storelocator_zone SELECT * FROM temando_zone');
    }

    /**
     * Inventory migration
     *
     * Required table temando_origin_inventory
     */
    private function migrateInventory()
    {
        $this->resourceConnection->getConnection()->query('INSERT INTO magestore_storelocator_store_inventory (erp_id, sku, units)
                      SELECT erp_id, sku, units FROM temando_origin_inventory;');
    }

    /**
     * Temando stores migration
     *
     * Required table temando origin
     */
    private function migrateStores()
    {
        $connection = $this->resourceConnection->getConnection();
        $connection->addColumn(
            \Magestore\Storelocator\Setup\InstallSchema::SCHEMA_STORE,
            'temando_origin_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER
        );
        $connection->query('INSERT INTO magestore_storelocator_store (address, baseimage_id, city, country_id, description,
                                          email, fax, link, latitude, longitude, marker_icon, meta_description, meta_keywords, meta_title,
                                          phone, rewrite_request_path, schedule_id, sort_order, state, status, store_name, zipcode, zoom_level,
                                          facebook_link, instagram_link, allow_store_collection, erp_code, erp_id, warehouse_code, zone_id,
                                          supporting_origins, state_id, temando_origin_id) 
              SELECT tem.street AS `address`, NULL AS `baseimage_id`, LOWER(tem.city) AS `city`, tem.country AS `country_id`, tem.description, tem.contact_email AS `email`,
                  IFNULL(tem.contact_fax , "") AS `fax`, NULL AS `link`, tem.latitude, tem.longitude, "" AS `marker_icon`,
                  "" AS `meta_description`, "" AS `meta_keywords`, "" AS `meta_title`, IFNULL(tem.contact_phone_1, tem.contact_phone_2) AS `phone`,
                CONCAT(LOWER(REPLACE(tem.name, "", "-")), "-", tem.origin_id) AS rewrite_request_path, NULL AS `schedule_id`, NULL AS `sort_order`,
                (SELECT dcr.default_name FROM directory_country_region dcr WHERE dcr.country_id = "AU" AND dcr.code LIKE tem.region LIMIT 1) AS `state`,
                IF(tem.is_active = 1, 1, 2) AS `status`, tem.name AS `store_name`, tem.postcode AS `zipcode`, tem.zoom_level,
                NULL AS `facebook_link`, NULL AS `instagram_link`, tem.allow_store_collection, tem.erp_code, tem.erp_id, tem.erp_code AS `warehouse_code`,
                tem.zone_id, tem.supporting_origins, 
                (SELECT dcr.region_id FROM directory_country_region dcr WHERE dcr.country_id = "AU" AND dcr.code LIKE tem.region LIMIT 1) AS `state_id`,
                tem.origin_id AS `temando_origin_id`
              FROM temando_origin tem;');

        $this->updateStoreNames();
    }

    /**
     * Update Store city action
     */
    private function updateStoreNames()
    {
        $storesCollection = $this->storeRepository->getCollection();

        foreach ($storesCollection->getItems() as $store) {
            $this->resourceConnection->getConnection()->query('UPDATE magestore_storelocator_store SET city = :city WHERE storelocator_id = :storelocator_id',
                [
                    'city' => ucwords($store->getCity(), "' "),
                    'storelocator_id' => $store->getId()
                ]);
        }
    }
}
