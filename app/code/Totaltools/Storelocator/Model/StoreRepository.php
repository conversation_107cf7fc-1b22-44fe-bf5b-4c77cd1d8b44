<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model;

use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Exception\NoSuchEntityException;
use Magestore\Storelocator\Model\ResourceModel\Store as StoreResource;
use Totaltools\Storelocator\Model\ShippingZoneRepository as ShippingZoneRepository;

/**
 * Class StoreRepository
 *
 * @package Totaltools\Storelocator\Model
 */
class StoreRepository extends BaseRepository
{
    /**
     * @var StoreInventoryCollectionFactory
     */
    protected $_storeCollectionFactory;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $_searchCriteriaBuilder;

    /**
     * @var StoreFactory
     */
    protected $_storeFactory;

    /**
     * @var StoreResource
     */
    protected $_storeResource;

    /**
     * @var \Totaltools\Storelocator\Model\ShippingZoneRepository
     */
    protected $_shippingZoneRepository;

    /**
     * StoreRepository constructor.
     *
     * @param ResourceModel\Store\CollectionFactory                           $storeCollectionFactory
     * @param \Totaltools\Storelocator\Api\StoreSearchResultsInterfaceFactory $searchResultsFactory
     * @param SearchCriteriaBuilder                                           $searchCriteriaBuilder
     * @param StoreFactory                                                    $storeFactory
     * @param StoreResource                                                   $storeResource
     * @param ShippingZoneRepository                                          $shippingZoneRepository
     * @param CollectionProcessorInterface|null                               $collectionProcessor
     */
    public function __construct(
        \Totaltools\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        \Totaltools\Storelocator\Api\StoreSearchResultsInterfaceFactory $searchResultsFactory,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        \Totaltools\Storelocator\Model\StoreFactory $storeFactory,
        StoreResource $storeResource,
        ShippingZoneRepository $shippingZoneRepository,
        CollectionProcessorInterface $collectionProcessor = null
    )
    {
        $this->_searchResultsFactory = $searchResultsFactory;
        $this->_storeCollectionFactory = $storeCollectionFactory;
        $this->_searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->_storeFactory = $storeFactory;
        $this->_storeResource = $storeResource;
        $this->_shippingZoneRepository = $shippingZoneRepository;

        parent::__construct($collectionProcessor);
    }

    /**
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection()
    {
        return $this->_storeCollectionFactory->create();
    }

    /**
     * Get Store by ID.
     *
     * @param $storeId
     *
     * @return Store
     * @throws NoSuchEntityException
     * @api
     */
    public function getById($storeId)
    {
        /*** @var \Totaltools\Storelocator\Model\Store $store */
        $store = $this->_storeFactory->create();

        $this->_storeResource->load($store, $storeId);

        if (!$store->getId()) {
            throw new NoSuchEntityException(__('Store with ID "%1" does not exist.', $storeId));
        }

        return $store;
    }

    /**
     * Get store by id if its is active and enabled for click & collect
     *
     * @param $storeId
     *
     * @return Store
     * @throws NoSuchEntityException
     */
    public function getValidStoreById($storeId)
    {
        $store = $this->getStore('storelocator_id', $storeId);

        if (!$store || !$store->getId()) {
            throw new NoSuchEntityException(__('Store with ID "%1" does not exist.', $storeId));
        }

        return $store;
    }

    /**
     * Get store by ERP ID.
     *
     * @param int $erpId
     *
     * @return Store
     * @throws NoSuchEntityException
     */
    public function getByErpId(int $erpId)
    {
        $store = $this->getStore('erp_id', $erpId);

        if (!$store || !$store->getId()) {
            throw new NoSuchEntityException(__('Store with ERP ID "%1" does not exist.', $erpId));
        }

        return $store;
    }

    /**
     * Get store by condition.
     *
     * @param string $field
     * @param        $value
     *
     * @return bool|Store
     */
    public function getStore(string $field, $value)
    {
        $storeCollection = $this->getCollection()
            ->addFieldToFilter($field, $value);

        $storeCollection->getSelect()
            ->joinLeft(
                ['table_schedule' => $storeCollection->getTable(\Magestore\Storelocator\Setup\InstallSchema::SCHEMA_SCHEDULE)],
                'table_schedule.schedule_id = main_table.schedule_id',
                [
                    'monday_open',
                    'tuesday_open',
                    'wednesday_open',
                    'thursday_open',
                    'friday_open',
                    'saturday_open',
                    'sunday_open',
                    'monday_close',
                    'tuesday_close',
                    'wednesday_close',
                    'thursday_close',
                    'friday_close',
                    'saturday_close',
                    'sunday_close'
                ]
            );

        foreach (Store::DEFAULT_FILTER as $filterField => $filterValue) {
            $storeCollection->addFieldToFilter($filterField, $filterValue);
        }

        return $storeCollection->getFirstItem();
    }

    /**
     * Get store by condition.
     *
     * @param string $field
     * @param        $value
     *
     * @return bool|Store
     */
    protected function getStoreForZone(string $field, $value)
    {
        $storeCollection = $this->getCollection()
            ->addFieldToFilter($field, $value);

        $storeCollection->getSelect()
            ->joinLeft(
                ['table_schedule' => $storeCollection->getTable(\Magestore\Storelocator\Setup\InstallSchema::SCHEMA_SCHEDULE)],
                'table_schedule.schedule_id = main_table.schedule_id',
                [
                    'monday_open',
                    'tuesday_open',
                    'wednesday_open',
                    'thursday_open',
                    'friday_open',
                    'saturday_open',
                    'sunday_open',
                    'monday_close',
                    'tuesday_close',
                    'wednesday_close',
                    'thursday_close',
                    'friday_close',
                    'saturday_close',
                    'sunday_close'
                ]
            );

        foreach (Store::ZONE_FILTER as $filterField => $filterValue) {
            $storeCollection->addFieldToFilter($filterField, $filterValue);
        }

        return $storeCollection->getFirstItem();
    }

    /**
     * @param string $city
     * @return bool|Store
     */
    public function getByCity(string $city)
    {
        $store = $this->getStore('city', $city);

        if (!$store || !$store->getId()) {
            return false;
        }

        return $store;
    }

    /**
     * Find store by zone ID.
     *
     * @param $zoneId
     *
     * @return bool|Store
     */
    public function findByZone($zoneId)
    {
        $store = $this->getStoreForZone('zone_id', $zoneId);

        if (!$store || !$store->getId()) {
            return false;
        }

        return $store;
    }

    /**
     * Find click and collect store by zone id
     *
     * @param $zoneId
     *
     * @return bool|Store
     */
    public function findClickAndCollectStoreByZone($zoneId)
    {
        $store = $this->getStore('zone_id', $zoneId);

        if (!$store || !$store->getId() || !$store->getAllowStoreCollection()) {
            return false;
        }

        return $store;
    }

    /**
     * Get store by zip code.
     *
     * @param string $zipCode
     *
     * @return Store
     * @throws NoSuchEntityException
     * @api
     */
    public function getByZipCode($zipCode)
    {
        $collection = $this->getCollection();
        $collection->getSelect()
            ->joinLeft(
                ['sched' => $collection->getTable(\Magestore\Storelocator\Setup\InstallSchema::SCHEMA_SCHEDULE)],
                'sched.schedule_id = main_table.schedule_id',
                [
                    'monday_open',
                    'tuesday_open',
                    'wednesday_open',
                    'thursday_open',
                    'friday_open',
                    'saturday_open',
                    'sunday_open',
                    'monday_close',
                    'tuesday_close',
                    'wednesday_close',
                    'thursday_close',
                    'friday_close',
                    'saturday_close',
                    'sunday_close'
                ]
            )->joinLeft(
                ['region' => $collection->getTable('directory_country_region')],
                'region.default_name = main_table.state AND region.country_id = \'AU\'',
                ['state_code' => 'region_id']
            )->where('main_table.zipcode = ?', $zipCode);

        /** @var Store $store */
        $store = $collection->getFirstItem();

        if (!$store->getId()) {
            throw new NoSuchEntityException(__('Store with zipcode "%1" does not exist.', $zipCode));
        }

        return $store;
    }

    /**
     * Get fallback related to store.
     *
     * @param int $storeId
     *
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getFallbackStores(int $storeId)
    {
        $collection = $this->getCollection();
        $collection->getSelect()
            ->join(
                ['sf' => $collection->getTable(\Totaltools\Storelocator\Setup\UpgradeSchema::FALLBACK_TABLE_NAME)],
                'sf.fallback_store_id = main_table.storelocator_id',
                ''
            )            
            ->joinLeft(
                ['sched' => $collection->getTable(\Magestore\Storelocator\Setup\InstallSchema::SCHEMA_SCHEDULE)],
                'sched.schedule_id = main_table.schedule_id',
                [
                    'monday_open',
                    'tuesday_open',
                    'wednesday_open',
                    'thursday_open',
                    'friday_open',
                    'saturday_open',
                    'sunday_open',
                    'monday_close',
                    'tuesday_close',
                    'wednesday_close',
                    'thursday_close',
                    'friday_close',
                    'saturday_close',
                    'sunday_close'
                ]
            )->where('sf.store_id = ?', $storeId)->where("allow_delivery_orders = 1")->where("status = 1")->order('row_id')->group('main_table.storelocator_id');

        return $collection;
    }

    /**
     * @param float $latitude
     * @param float $longitude
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getNearestStores(float $latitude, float $longitude, $limit = 5)
    {
        $collection = $this->getCollection();
        $collection->getSelect()->columns('
                    SQRT(POW(69.1 * (latitude - ('.$latitude.')), 2)+POW(69.1 * ('.$longitude.' - longitude) * COS(latitude / 57.3), 2)) AS distance
                ')
            ->joinLeft(
                ['sched' => $collection->getTable(\Magestore\Storelocator\Setup\InstallSchema::SCHEMA_SCHEDULE)],
                'sched.schedule_id = main_table.schedule_id',
                [
                    'monday_open',
                    'tuesday_open',
                    'wednesday_open',
                    'thursday_open',
                    'friday_open',
                    'saturday_open',
                    'sunday_open',
                    'monday_close',
                    'tuesday_close',
                    'wednesday_close',
                    'thursday_close',
                    'friday_close',
                    'saturday_close',
                    'sunday_close'
                ]
            )->where("allow_store_collection = 1")
            ->where("status = 1")
            ->where("is_visible = 1")
            ->having("distance >= 0")
            ->order('distance asc')
            ->limit($limit);
        return $collection;
    }

    /**
     * @param float $latitude
     * @param float $longitude
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getNearestStoresCollection(float $latitude, float $longitude, $limit = 20)
    {
        $collection = $this->getCollection();
        $collection->getSelect()->columns('
                SQRT(POW(69.1 * (latitude - (' . $latitude . ')), 2)+POW(69.1 * (' . $longitude . ' - longitude) * COS(latitude / 57.3), 2)) AS distance
            ')
            ->where("status=1")
            ->where("is_visible=1")
            ->having("distance >= 0")
            ->order('distance asc')
            ->limit($limit);

        return $collection;
    }

    /**
     * Get store by postcode.
     *
     * @param $postcode
     *
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface
     */
    public function getByPostcodeInZone($postcode)
    {
        /**
         * @var \Totaltools\Storelocator\Api\Data\ZoneInterface $zone
         */
        $zone = $this->_shippingZoneRepository->getZoneByPostCode($postcode);

        if (!$zone || !$zone->getId()) {
            return false;
        }

        return $this->findByZone($zone->getId());
    }

    /**
     * Get click and collect store by postcode.
     *
     * @param $postcode
     *
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface
     */
    public function getClickAndCollectStoreInZone($postcode)
    {
        /**
         * @var \Totaltools\Storelocator\Api\Data\ZoneInterface $zone
         */
        $zone = $this->_shippingZoneRepository->getZoneByPostCode($postcode);

        if (!$zone || !$zone->getId()) {
            return false;
        }

        return $this->findClickAndCollectStoreByZone($zone->getId());
    }

    /**
     * Get store by store name.
     *
     * @param $storeName
     * @return bool|Store
     */
    public function getStoreByStoreName($storeName)
    {
        $store = $this->getStore('store_name',$storeName);

        if (!$store || !$store->getId()) {
            return false;
        }

        return $store;
    }
}