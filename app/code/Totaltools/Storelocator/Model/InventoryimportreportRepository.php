<?php


namespace Totaltools\Storelocator\Model;

/**
 * @package Totaltools_Storelocatorreport
 * <AUTHOR> Dev Team
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */

use Totaltools\Storelocator\Api\Data\InventoryimportreportInterface;
use Totaltools\Storelocator\Api\InventoryimportreportRepositoryInterface;
use Totaltools\Storelocator\Model\Config\Source\Inventory as InventoryConfig;
use Totaltools\Storelocator\Model\ResourceModel\Inventoryimportreport\CollectionFactory as InventoryimportreportCollectionFactory;

class InventoryimportreportRepository implements InventoryimportreportRepositoryInterface
{
    /**
     * @var InventoryimportreportCollectionFactory
     */
    protected $inventoryimportreportCollectionFactory;
    /**
     * @var InventoryConfig
     */
    private $inventoryConfig;

    /**
     * Logging instance
     * @var Logger
     */
    protected $logger;

    /**
     * @param InventoryimportreportCollectionFactory $inventoryimportreportCollectionFactory
     * @param InventoryConfig $inventoryConfig
     * @param Logger $logger
     */
    public function __construct(
        InventoryConfig $inventoryConfig,
        InventoryimportreportCollectionFactory $inventoryimportreportCollectionFactory,
        Logger $logger
    ) {
        $this->inventoryConfig = $inventoryConfig;
        $this->inventoryimportreportCollectionFactory = $inventoryimportreportCollectionFactory;
        $this->logger = $logger;
    }

    /**
     * Retrieve boundary notification value in minutes
     * @return string|null
     */
    public function getNotificationBoundaryInMinutes()
    {
        return $this->inventoryConfig->getDefaultValue(
            \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_MAX_LAST_RUN_BOUNDARY,
            \Magento\Framework\App\Config\ScopeConfigInterface::SCOPE_TYPE_DEFAULT
        );
    }

    /**
     * Retrieve collection of full imports
     * @param bool $includeConfigTimeFrom
     * @return ResourceModel\Inventoryimportreport\Collection
     */
    public function getLastFullImport(bool $includeConfigTimeFrom=false)
    {
        $collection = $this->inventoryimportreportCollectionFactory->create();
        $collection
            ->addFieldToFilter(InventoryimportreportInterface::IMPORT_TYPE, 'full');

        if($includeConfigTimeFrom === true){
            $minutesAgo = $this->getNotificationBoundaryInMinutes();
            $collection->addFieldToFilter('updated_at', array(
                'from'     => strtotime('-'.$minutesAgo.' minutes', time()),
                'to'       => time(),
                'datetime' => true
            ));
        }
        $collection->setOrder(InventoryimportreportInterface::ENTITY_ID,'DESC')
            ->setPageSize(10)
            ->setCurPage(1);

        return $collection;
    }
}
