<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model;

use Magento\Framework\DataObject\IdentityInterface;
use Magento\Framework\Model\AbstractModel;
use Totaltools\Storelocator\Api\Data\StoreInventoryInterface;

/**
 * Class StoreInventory.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class StoreInventory extends AbstractModel implements StoreInventoryInterface, IdentityInterface
{
    /**
     * CMS page cache tag
     */
    const CACHE_TAG = 'magestore_Storelocator_zone';

    /**
     * Cache tag.
     *
     * @var string
     */
    protected $_cacheTag = 'magestore_Storelocator_zone';

    /**
     * Prefix of model events names
     *
     * @var string
     */
    protected $_eventPrefix = 'magestore_Storelocator_zone';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('Totaltools\Storelocator\Model\ResourceModel\StoreInventory');
    }

    /**
     * Return unique ID(s) for each object in system
     *
     * @return array
     */
    public function getIdentities()
    {
        return [self::CACHE_TAG . '_' . $this->getId()];
    }

    /**
     * Get ID
     *
     * @return int|null
     */
    public function getId()
    {
        return $this->getData(self::INVENTORY_ID);
    }

    /**
     * Get ERP Id
     *
     * @return string|null
     */
    public function getERPId()
    {
        return $this->getData(self::ERP_ID);
    }

    /**
     * Get SKU
     *
     * @return string|null
     */
    public function getSKU()
    {
        return $this->getData(self::SKU);
    }

    /**
     * Get Units
     *
     * @return string|null
     */
    public function getUnits()
    {
        return $this->getData(self::UNITS);
    }

    /**
     * Set ID
     *
     * @param int $id
     *
     * @return \Totaltools\Storelocator\Api\Data\StoreInventoryInterface
     */
    public function setId($id)
    {
        return $this->setData(self::INVENTORY_ID, $id);
    }

    /**
     * Set ERP Id
     *
     * @param string $erpId
     *
     * @return null|string
     */
    public function setERPId($erpId)
    {
        return $this->setData(self::ERP_ID, $erpId);
    }

    /**
     * Set SKU
     *
     * @param string $sku
     *
     * @return null|string
     */
    public function setSKU($sku)
    {
        return $this->setData(self::SKU, $sku);
    }

    /**
     * Set units
     *
     * @param string $units
     *
     * @return null|string
     */
    public function setUnits($units)
    {
        return $this->setData(self::UNITS, $units);
    }
}