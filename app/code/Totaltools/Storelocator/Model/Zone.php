<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model;

use Magento\Framework\DataObject\IdentityInterface;
use Magento\Framework\Exception\LocalizedException as CoreException;
use Totaltools\Storelocator\Api\Data\ZoneInterface;

/**
 * Class Zone.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Zone extends \Magento\Framework\Model\AbstractModel implements ZoneInterface, IdentityInterface
{
    /**
     * Origin's Statuses
     */
    const STATUS_ENABLED = 1;
    const STATUS_DISABLED = 0;

    /**
     * Head office postode
     */
    const DEFAULT_ZONE_POSTCODE = '0000';

    /**
     * CMS page cache tag
     */
    const CACHE_TAG = 'magestore_storelocator_zone';

    /**
     * Cache tag.
     *
     * @var string
     */
    protected $_cacheTag = 'magestore_storelocator_zone';

    /**
     * Prefix of model events names
     *
     * @var string
     */
    protected $_eventPrefix = 'magestore_storelocator_zone';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('Totaltools\Storelocator\Model\ResourceModel\Zone');
    }

    /**
     * Prepare origin's statuses.
     * Available event temando_origin_get_available_statuses to customize statuses.
     *
     * @return array
     */
    public function getAvailableStatuses()
    {
        return [self::STATUS_ENABLED => __('Enabled'), self::STATUS_DISABLED => __('Disabled')];
    }

    /**
     * Return unique ID(s) for each object in system
     *
     * @return array
     */
    public function getIdentities()
    {
        return [self::CACHE_TAG . '_' . $this->getId()];
    }

    /**
     * Get ID
     *
     * @return int|null
     */
    public function getId()
    {
        return $this->getData(self::ZONE_ID);
    }

    /**
     * Get title
     *
     * @return string|null
     */
    public function getName()
    {
        return $this->getData(self::NAME);
    }

    /**
     * Get Country Code
     *
     * @return string
     */
    public function getCountryCode()
    {
        return $this->getData(self::COUNTRY_CODE);
    }

    /**
     * Get Ranges
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function getRanges()
    {
        return $this->getData(self::RANGES);
    }

    /**
     * Set ID
     *
     * @param int $id
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function setId($id)
    {
        return $this->setData(self::ZONE_ID, $id);
    }

    /**
     * Set name.
     *
     * @param string $title
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function setName($title)
    {
        return $this->setData(self::NAME, $title);
    }

    /**
     * Set country code
     *
     * @param $country_code
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function setCountryCode($countryCode)
    {
        return $this->setData(self::COUNTRY_CODE, $countryCode);
    }

    /**
     * Set Ranges.
     *
     * @param $ranges
     *
     * @return \Totaltools\Storelocator\Api\Data\ZoneInterface
     */
    public function setRanges($ranges)
    {
        return $this->setData(self::RANGES, $ranges);
    }
}
