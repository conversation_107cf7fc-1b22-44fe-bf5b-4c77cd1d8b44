<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model;

use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Storelocator\Api\Data\ZoneInterface;
use Totaltools\Storelocator\Api\ShippingZoneRepositoryInterface;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Totaltools\Storelocator\Model\ResourceModel\Zone\CollectionFactory as ShippingZoneCollectionFactory;
use Totaltools\Storelocator\Model\Zone as Zone;

/**
 * Class ShippingZoneRepository.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class ShippingZoneRepository extends BaseRepository implements ShippingZoneRepositoryInterface
{
    /**
     * @var ZoneFactory
     */
    protected $_zoneFactory;

    /**
     * @var ResourceModel\Zone
     */
    protected $_resource;

    /**
     * @var ShippingZoneCollectionFactory
     */
    protected $_shippingZoneCollection;

    /**
     * @param ShippingZoneCollectionFactory                                      $shippingZoneCollection
     * @param \Totaltools\Storelocator\Api\ShippingZoneRepositoryInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface|null                                  $collectionProcessor
     */
    public function __construct(
        ShippingZoneCollectionFactory $shippingZoneCollection,
        \Totaltools\Storelocator\Api\StoreInventorySearchResultsInterfaceFactory $searchResultsFactory,
        \Totaltools\Storelocator\Model\ZoneFactory $zoneFactory,
        \Totaltools\Storelocator\Model\ResourceModel\Zone $resourceModel,
        CollectionProcessorInterface $collectionProcessor = null
    )
    {
        $this->_shippingZoneCollection = $shippingZoneCollection;
        $this->_searchResultsFactory = $searchResultsFactory;
        $this->_zoneFactory = $zoneFactory;
        $this->_resource = $resourceModel;

        parent::__construct($collectionProcessor);
    }

    /**
     * Get collection entity.
     *
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection()
    {
        return $this->_shippingZoneCollection->create();
    }

    /**
     * Retrieve shipping zone.
     *
     * @param int $zoneId
     *
     * @return ZoneInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($zoneId)
    {
        $zone = $this->_zoneFactory->create();
        $zone->load($zoneId);
        if (!$zone->getId()) {
            throw new NoSuchEntityException(__('Shipping zone with id "%1" does not exist.', $zoneId));
        }

        return $zone;
    }

    /**
     * Return zone by post code.
     *
     * @param string $postcode
     *
     * @return \Magento\Framework\DataObject|null
     */
    public function getZoneByPostCode($postcode)
    {
        /*** @var Zone $zone */
        foreach ($this->getCollection() as $zone) {
            if ($ranges = $zone->getRanges()) {
                if ($ranges === Zone::DEFAULT_ZONE_POSTCODE && $postcode !== Zone::DEFAULT_ZONE_POSTCODE) {
                    continue;
                }
                $ranges = array_filter(array_map('trim', explode(',', $ranges)));
                foreach ($ranges as $range) {
                    if (strpos($range, ':') !== false) {
                        $rangeZones = explode(':', $range);
                        if ($postcode >= $rangeZones[0] && $postcode <= $rangeZones[1]) {
                            return $zone;
                        }
                    } elseif ($postcode == $range) {
                        return $zone;
                    }
                }
            }
        }

        return null;
    }

    /**
     * Delete shipping zone.
     *
     * @param ZoneInterface $shippingZone
     *
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(ZoneInterface $shippingZone)
    {
        try {
            $this->_resource->delete($shippingZone);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the shipping zone: %1',
                $exception->getMessage()
            ));
        }

        return true;
    }
}