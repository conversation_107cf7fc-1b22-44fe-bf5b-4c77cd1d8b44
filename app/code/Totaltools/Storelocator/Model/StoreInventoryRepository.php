<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model;

use Totaltools\Storelocator\Api\StoreInventoryRepositoryInterface;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use \Totaltools\Storelocator\Model\ResourceModel\StoreInventory\CollectionFactory as StoreInventoryCollectionFactory;

/**
 * Class StoreInventoryRepository.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class StoreInventoryRepository extends BaseRepository implements StoreInventoryRepositoryInterface
{
    /**
     * @var StoreInventoryCollectionFactory
     */
    protected $_inventoryCollectionFactory;

    /**
     * @param StoreInventoryCollectionFactory                                         $storeInventoryCollectionFactory
     * @param \Totaltools\Storelocator\Api\StoreInventorySearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface|null                                       $collectionProcessor
     */
    public function __construct(
        StoreInventoryCollectionFactory $storeInventoryCollectionFactory,
        \Totaltools\Storelocator\Api\StoreInventorySearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor = null
    ) {
        $this->_searchResultsFactory = $searchResultsFactory;
        $this->_inventoryCollectionFactory = $storeInventoryCollectionFactory;

        parent::__construct($collectionProcessor);
    }

    /**
     * Get collection entity.
     *
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection()
    {
        return $this->_inventoryCollectionFactory->create();
    }
}