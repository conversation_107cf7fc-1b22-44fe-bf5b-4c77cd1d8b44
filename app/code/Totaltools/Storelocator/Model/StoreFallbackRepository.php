<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Model;

use Magento\Framework\Api\SearchCriteriaInterface;
use Totaltools\Storelocator\Api\StoreFallbackRepositoryInterface;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use \Totaltools\Storelocator\Model\ResourceModel\StoreFallback\CollectionFactory as StoreFallbackCollectionFactory;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\SortOrder;
use Totaltools\Storelocator\Api\Data\StoreFallbackInterface;

/**
 * Class StoreFallbackRepository
 *
 * @package Totaltools\Storelocator\Model
 */
class StoreFallbackRepository extends BaseRepository implements StoreFallbackRepositoryInterface
{
    /**
     * @var SearchCriteriaBuilder
     */
    protected $_searchCriteriaBuilder;

    /**
     * @var  \Magento\Framework\Api\SortOrderBuilder
     */
    protected $_sortOrderBuilder;

    /**
     * @var StoreFallbackCollectionFactory
     */
    protected $_fallbackCollectionFactory;

    /**
     * StoreFallbackRepository constructor.
     * @param StoreFallbackCollectionFactory $storeFallbackCollectionFactory
     * @param \Totaltools\Storelocator\Api\StoreFallbackSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface|null $collectionProcessor
     */
    public function __construct(
        StoreFallbackCollectionFactory $storeFallbackCollectionFactory,
        \Totaltools\Storelocator\Api\StoreFallbackSearchResultsInterfaceFactory $searchResultsFactory,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        \Magento\Framework\Api\SortOrderBuilder $sortOrderBuilder,
        CollectionProcessorInterface $collectionProcessor = null
    ) {
        $this->_searchResultsFactory = $searchResultsFactory;
        $this->_fallbackCollectionFactory = $storeFallbackCollectionFactory;
        $this->_searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->_sortOrderBuilder = $sortOrderBuilder;

        parent::__construct($collectionProcessor);
    }

    /**
     * @return \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
     */
    public function getCollection()
    {
        return $this->_fallbackCollectionFactory->create();
    }

    /**
     * @param int $storeId
     * @return \Magento\Framework\Api\ExtensibleDataInterface[]
     */
    public function getFallbackStores(int $storeId)
    {
        $sortOrder = $this->_sortOrderBuilder->setField(StoreFallbackInterface::ROW_ID)
            ->setDirection(SortOrder::SORT_ASC)->create();
        $fallbackSearchCriteria = $this->_searchCriteriaBuilder;
        $fallbackSearchCriteria->addFilter(StoreFallbackInterface::STORE_ID, $storeId, 'eq')
            ->addSortOrder($sortOrder);

        $fallbackStores = $this->getList($fallbackSearchCriteria->create());

        return $fallbackStores->getItems();
    }
}