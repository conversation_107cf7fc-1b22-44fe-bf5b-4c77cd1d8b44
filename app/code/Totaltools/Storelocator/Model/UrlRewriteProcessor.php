<?php

namespace Totaltools\Storelocator\Model;

use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class UrlRewriteProcessor
 * @package Totaltools\Storelocator\Model
 */
class UrlRewriteProcessor
{
    const XML_URL_REWRITE_TABLE = 'url_rewrite';

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $resources;

    /**
     * @var ResourceModel\Store\CollectionFactory
     */
    private $storeCollection;

    /**
     * @var \Magestore\Storelocator\Model\StoreUrlRewriteGeneratorInterface
     */
    private $storeUrlRewriteGenerator;

    /**
     * @var \Magento\UrlRewrite\Model\UrlPersistInterface
     */
    private $urlPersist;

    /**
     * UrlRewriteProcessor constructor.
     * @param \Magento\Framework\App\ResourceConnection $resource
     * @param ResourceModel\Store\CollectionFactory $collectionFactory
     * @param \Magestore\Storelocator\Model\StoreUrlRewriteGeneratorInterface $storeUrlRewriteGenerator
     * @param \Magento\UrlRewrite\Model\UrlPersistInterface $urlPersist
     */
    public function __construct(
        \Magento\Framework\App\ResourceConnection $resource,
        ResourceModel\Store\CollectionFactory $collectionFactory,
        \Magestore\Storelocator\Model\StoreUrlRewriteGeneratorInterface $storeUrlRewriteGenerator,
        \Magento\UrlRewrite\Model\UrlPersistInterface $urlPersist
    ) {
        $this->resources = $resource;
        $this->storeCollection = $collectionFactory;
        $this->storeUrlRewriteGenerator = $storeUrlRewriteGenerator;
        $this->urlPersist = $urlPersist;
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     *
     * @throws \Exception
     */
    public function execute($input, $output)
    {
        $this->generateUrl($output);
    }

    /**
     * @param OutputInterface $output
     * @throws \Magento\UrlRewrite\Model\Exception\UrlAlreadyExistsException
     */
    private function generateUrl(OutputInterface $output)
    {
        $this->deleteOldUrl();
        $this->regenerate($output);
        $output->writeln('Success !');
    }

    /**
     * @param $output
     * @throws \Magento\UrlRewrite\Model\Exception\UrlAlreadyExistsException
     */
    private function regenerate($output)
    {
        $collection = $this->storeCollection->create();
        $count = 0;
        foreach ($collection as $store) {
            $urls = $this->storeUrlRewriteGenerator->generate($store);
            $count += count($urls);
            $this->urlPersist->replace($urls);
        }
        $output->writeln('Generated ' . $count . ' rows');
    }

    private function deleteOldUrl()
    {
        $targetPath = 'storelocator/index/view/storelocator_id/';
        $connection = $this->resources->getConnection();
        $where = [
            'target_path LIKE ?' => '%' . $targetPath . '%',
        ];
        $tableName = $connection->getTableName(self::XML_URL_REWRITE_TABLE);

        if ($tableName) {
            $connection->delete(
                $tableName,
                $where
            );
        }
    }
}
