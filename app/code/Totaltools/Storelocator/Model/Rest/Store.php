<?php

namespace Totaltools\Storelocator\Model\Rest;

use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Storelocator\Api\StoreLocatorInterface;

class Store implements StoreLocatorInterface
{
    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;
    /**
     * @var \Magento\Framework\Serialize\Serializer\Json
     */
    protected $_jsonEncoder;

    /**
     * Store constructor.
     *
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Magento\Framework\Serialize\Serializer\Json   $jsonEncoder
     */
    public function __construct(
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Framework\Serialize\Serializer\Json $jsonEncoder
    )
    {
        $this->_storeRepository = $storeRepository;
        $this->_jsonEncoder = $jsonEncoder;
    }

    /**
     * Get store by zip code.
     *
     * @param $zipCode
     *
     * @api
     * @return string Json encoded data of store.
     */
    public function getByZipCode($zipCode)
    {
        try {
            $store = $this->_storeRepository->getByZipCode($zipCode);
            $response['store'] = $this->_jsonEncoder->serialize($store->getData());
        } catch (NoSuchEntityException $exception) {
            $response['message'] = $exception->getMessage();
        }

        return $response;
    }
}