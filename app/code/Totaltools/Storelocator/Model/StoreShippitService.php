<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model;

use Magento\Framework\Exception\NotFoundException;
use Magento\Sales\Model\Order;

/**
 * Class StoreShippitService.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class StoreShippitService
{
    /**
     * @var \Shippit\Shipping\Model\Shippit
     */
    protected $_shippit;
    protected $_helper;

    /**
     * StoreShippitService constructor.
     *
     * @param \Shippit\Shipping\Model\Shippit $shippit
     */
    public function __construct(
        \Shippit\Shipping\Model\Shippit $shippit,
        \Shippit\Shipping\Helper\Sync\Order $helper
    ){
        $this->_shippit = $shippit;
        $this->_helper = $helper;
    }

    /**
     * Prepare order items for sending to Shippit.
     *
     * @param Order $order
     *
     * @return array
     */
    protected function _getOrderItems(Order $order)
    {
        $orderItems = [];
        foreach ($order->getItems() as $item) {
            if ($item->getQtyOrdered() - $item->getQtyRefunded() <= 0) {
                continue;
            }
            /*** @var \Magento\Sales\Api\Data\OrderItemInterface $item */
            $orderItems[] = [
                'sku' => $item->getSku(),
                'qty' => $item->getQtyOrdered() - $item->getQtyRefunded(),
                'weight' => $item->getWeight(),
            ];
        }

        return $orderItems;
    }

    /**
     * Send order to Shippit account.
     * Credentials to Shippit are provided by Store.
     *
     * @param Order  $order
     * @param Store  $store
     *
     * @return $this|bool
     * @throws NotFoundException
     */
    public function sendOrder(Order $order, Store $store)
    {
        $apiKey = $store->getShippitApiKey();
        if ((int) $order->getStorelocatorApiType() === 2) {
            $apiKey = $store->getShippitApiKeySecond();
        }
        if (empty($apiKey)) {
            throw new NotFoundException(
                __('Store with ID %1 does not contain API key for access to Shippit account.', $store->getId())
            );
        }
        $items = $this->_getOrderItems($order);
        if (!empty($items)) {
            $shippitShippingMethod = $this->_helper->getShippitShippingMethod($order->getShippingMethod());

            $syncOrderObject = $this->_shippit->addOrder(
                $order,
                $this->_getOrderItems($order),
                $shippitShippingMethod,
                $apiKey
            );

            return $syncOrderObject;
        }
        return false;
    }
}