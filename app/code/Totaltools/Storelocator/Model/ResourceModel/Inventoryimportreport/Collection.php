<?php

namespace Totaltools\Storelocator\Model\ResourceModel\Inventoryimportreport;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

/**
 * Class Collection
 *
 * @package Dhs\Storelocatorreport\Model\ResourceModel\Inventoryimportreport
 */
class Collection extends AbstractCollection
{
    /**
     * Define resource model.
     */
    protected function _construct()
    {
        $this->_init(
            \Totaltools\Storelocator\Model\Inventoryimportreport::class,
            \Totaltools\Storelocator\Model\ResourceModel\Inventoryimportreport::class
        );
    }
}
