<?php
/**
 * Totaltools Storelocator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Model\ResourceModel;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Totaltools\Storelocator\Api\Data\StoreInventoryInterface;
use Zend_Db_Exception;

/**
 * Class InventoryProcessor
 * @package Totaltools\Storelocator\Model\ResourceModel
 */
class InventoryProcessor
{
    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var AdapterInterface
     */
    private AdapterInterface $connection;

    /**
     * InventoryProcessor constructor.
     * @param ResourceConnection $resource
     */
    public function __construct(
        ResourceConnection $resource
    ) {
        $this->resource = $resource;
        $this->connection = $resource->getConnection();
    }

    /**
     * @return AdapterInterface
     */
    public function getConnection()
    {
        return $this->connection;
    }

    /**
     * Create new temporary tables after dropped the current ones to make sure there is no data inside it.
     *
     * @throws Zend_Db_Exception
     */
    public function createTemporaryTables()
    {
        $this->connection->dropTable($this->getTempTableName('magestore_storelocator_store_inventory'));
        $this->createBatchLikeTables([
            [
                'origin' => $this->connection->getTableName('magestore_storelocator_store_inventory'),
                'temporary' => $this->getTempTableName('magestore_storelocator_store_inventory'),
            ]
        ]);
    }

    /**
     * Drop the original table, then rename all temporary tables to the original ones.
     *
     * @throws Zend_Db_Exception
     */
    public function renameTables()
    {
        $this->connection->renameTablesBatch([
            [
                'oldName' => $this->connection->getTableName('magestore_storelocator_store_inventory'),
                'newName' => $this->connection->getTableName('magestore_storelocator_store_inventory_tmp_' . time()),
            ],

            [
                'oldName' => $this->getTempTableName('magestore_storelocator_store_inventory'),
                'newName' => $this->connection->getTableName('magestore_storelocator_store_inventory'),
            ]

        ]);
    }

    /**
     * Create batch tables copied from the others.
     *
     * @param array $tablePairs array('origin' => 'tableName1','temporary' => 'tableName2')
     * @return bool
     * @throws Zend_Db_Exception
     */
    private function createBatchLikeTables(array $tablePairs)
    {
        if (count($tablePairs) == 0) {
            throw new Zend_Db_Exception('Please provide tables for creating');
        }

        $createsList = [];
        $tablesList = [];
        foreach ($tablePairs as $pair) {
            $originTableName = $pair['origin'];
            $temporaryTableName = $pair['temporary'];
            //@codingStandardsIgnoreStart
            $createsList[] = sprintf('CREATE TABLE %s LIKE %s', $temporaryTableName, $originTableName);
            //@codingStandardsIgnoreEnd

            $tablesList[$originTableName] = $originTableName;
            $tablesList[$temporaryTableName] = $temporaryTableName;
        }

        foreach ($createsList as $query) {
            $this->connection->query($query);
        }

        foreach ($tablesList as $table) {
            $this->connection->resetDdlCache($table);
        }

        return true;
    }

    /**
     * Get temporary table name.
     *
     * @param string $tableName
     *
     * @return string
     */
    private function getTempTableName($tableName)
    {
        return $this->connection->getTableName(sprintf('%s_tmp', $tableName));
    }

    /**
     * Cleanup Temporary Table
     */
    public function cleanupTemporaryTable()
    {
        $results = $this->connection->getTables('%magestore_storelocator_store_inventory_tmp_%');
        foreach ($results as $tableName) {
            $this->connection->dropTable($this->connection->getTableName($tableName));
        }
    }

    /**
     * Insert row to magestore_storelocator_store_inventory table
     *
     * @param array $data
     * @return bool
     */
    public function insertInventoryData(array $data)
    {
        if (!empty($data)) {
            $this->getConnection()->insertOnDuplicate(
                $this->getConnection()->getTableName('magestore_storelocator_store_inventory'),
                $data,
                [
                    StoreInventoryInterface::ERP_ID,
                    StoreInventoryInterface::SKU,
                    StoreInventoryInterface::UNITS,
                ]
            );
            return true;
        }
        return false;
    }

    /**
     * Insert all row to magestore_storelocator_store_inventory table
     *
     * @param $path
     * @return bool
     */
    public function insertAllInventoryData($path)
    {
        $table = $this->getTempTableName('magestore_storelocator_store_inventory');
        $query = sprintf(
            'LOAD DATA LOCAL INFILE "%s" INTO TABLE %s FIELDS TERMINATED BY "," LINES TERMINATED BY "\n" (erp_id,sku,units);',
            $path,
            $table
        );
        $this->getConnection()->query($query);
        //let's check if the import was successful
        if($this->getTmpInventoryRowCount() > 0){
            return true;
        }
        return false;
    }

    /**
     * count rows in magestore_storelocator_store_inventory_tmp table
     *
     * return int
     */
    public function getTmpInventoryRowCount()
    {
        $table = $this->getTempTableName('magestore_storelocator_store_inventory');
        $query = sprintf(
            'SELECT COUNT(*) as count FROM %s;',
            $table
        );
        $res = $this->getConnection()->fetchAll($query);

        if($res[0] && is_numeric($res[0]['count'])){
            return (int)$res[0]['count'];
        }
        return 0;
    }

    /**
     * count rows in magestore_storelocator_store_inventory table
     *
     * return int
     */
    public function getInventoryRowCount()
    {
        $table = $this->connection->getTableName('magestore_storelocator_store_inventory');
        $query = sprintf(
            'SELECT COUNT(*) as count FROM %s;',
            $table
        );
        $res = $this->getConnection()->fetchAll($query);

        if($res[0] && is_numeric($res[0]['count'])){
            return (int)$res[0]['count'];
        }
        return 0;
    }

    /**
     * count rows in magestore_storelocator_store_inventory_tmp table grouping by erp_id
     *
     * return array
     */
    public function getTmpInventoryRowCountByErp()
    {
        $table = $this->getTempTableName('magestore_storelocator_store_inventory');
        $query = sprintf(
            'SELECT '.StoreInventoryInterface::ERP_ID.', COUNT(*) as count
            FROM %s GROUP BY '.StoreInventoryInterface::ERP_ID.';',
            $table
        );
        return $this->getConnection()->fetchAll($query);
    }
}
