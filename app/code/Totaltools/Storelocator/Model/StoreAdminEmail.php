<?php
/**
 * Totaltools Storelocator.
 *
 * @category   Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright  2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Model;

use Balance\B2b\Helper\Data;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Area;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Address\Renderer;
use Psr\Log\LoggerInterface;
use Magento\Payment\Helper\Data as PaymentHelper;
use Totaltools\Storelocator\Helper\Data as DataHelper;
use Totaltools\Storelocator\Model\Store;
use Totaltools\Pronto\Helper\Data as ProntoHelper;

/**
 * Class StoreAdminEmail
 * @package Totaltools\Storelocator\Model
 */
class StoreAdminEmail
{
    /**
     * Click and collect method name
     */
    const STORE_PICKUP_METHOD = 'shippitcc_shippitcc';

    /**
     * Express Delivery method name
     */
    const EXPRESS_DELIVERY = 'shippit_Express';

    /**
     * OnDemand Delivery method name
     */
    const ONDEMAND_DELIVERY = 'shippit_ondemand';

    /**
     * Free Delivery method name
     */
    const FREE_DELIVERY = 'freeshipping';

    /**
     * Standard Delivery method name
     */
    const STANDARD_DELIVERY = 'shippit_Standard';

    /**
     * @var \Totaltools\Storelocator\Helper\Data
     */
    private $dataHelper;

    /**
     * @var \Magento\Payment\Helper\Data
     */
    private $paymentHelper;

    /**
     * @var Renderer
     */
    private $addressRenderer;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var Data
     */
    private $balanceHelper;

    protected $orderRepository;

    /**
     * @var Context
     */
    private $context;
    /**
     * @var ProntoHelper
     */
    private $prontoHelper;
    /**
     * StoreAdminEmail constructor.
     * @param LoggerInterface $logger
     * @param Renderer $addressRenderer
     * @param PaymentHelper $paymentHelper
     * @param \Totaltools\Storelocator\Helper\Data $dataHelper
     * @param TransportBuilder $transportBuilder
     * @param Data $balanceHelper
     * @param CustomerRepositoryInterface $customerRepository
     * @param OrderRepositoryInterface $orderRepository
     * @param ProntoHelper  $prontoHelper
     * @param Context $context
     */
    public function __construct(
        LoggerInterface $logger,
        Renderer $addressRenderer,
        PaymentHelper $paymentHelper,
        DataHelper $dataHelper,
        TransportBuilder $transportBuilder,
        Data $balanceHelper,
        CustomerRepositoryInterface $customerRepository,
        OrderRepositoryInterface $orderRepository,
        ProntoHelper $prontoHelper,
        Context $context
    ) {
        $this->dataHelper = $dataHelper;
        $this->transportBuilder = $transportBuilder;
        $this->addressRenderer = $addressRenderer;
        $this->paymentHelper = $paymentHelper;
        $this->logger = $logger;
        $this->balanceHelper = $balanceHelper;
        $this->customerRepository = $customerRepository;
        $this->orderRepository = $orderRepository;
        $this->prontoHelper = $prontoHelper;
        $this->context = $context;
    }

    /**
     * @param $order
     * @return |null
     */
    private function getFormattedShippingAddress($order)
    {
        return $order->getIsVirtual()
            ? null
            : $this->addressRenderer->format($order->getShippingAddress(), 'html');
    }

    /**
     * @param $order
     * @return mixed
     */
    private function getFormattedBillingAddress($order)
    {
        return $this->addressRenderer->format($order->getBillingAddress(), 'html');
    }

    /**
     * @param $order
     * @return string
     * @throws \Exception
     */
    private function getPaymentHtml($order)
    {
        return $this->paymentHelper->getInfoBlockHtml(
            $order->getPayment(),
            $order->getStore()->getStoreId()
        );
    }

    /**
     * @param Store $store
     * @param Order $order
     * @return bool
     * @throws \Exception
     */
    public function sendMail($store, $order, array $options = [])
    {
        $request = $this->context->getRequest();
        if ($request->getFullActionName() == 'sales_order_email') {
            return false;
        }

        $sender = $this->dataHelper->getNotifyMerchantEmailSender();
        $originalStore = '';

        if ($this->isB2bCustomer($order)) {
            $template = $this->balanceHelper->getStoreTemplate();
            $sendTo = [
                [
                    'email' => $store->getCommercialStoreAdminEmail(),
                    'name' => $store->getStoreName(),
                ],
            ];
        } else {
            if (!empty($options) && isset($options['reallocate'])) {
                $template = $this->dataHelper->getNotifyMerchantReallocateTemplate();
                $originalStore = $options['originalStore'] ?? '';
            } else {
                $template = $this->dataHelper->getNotifyMerchantTemplate();
            }

            $sendTo = [
                [
                    'email' => $store->getStoreAdminEmail(),
                    'name' => $store->getStoreName(),
                ],
            ];
        }
        $customerLoyaltyData = '';
        if($order->getId()) {
            $guestCustomer = $order->getCustomerIsGuest();
            if(!$guestCustomer) {
                $customerId = $order->getCustomerId();
                $customerLoyaltyData = $this->prontoHelper->getCustomerLoyaltyData($customerId);
            }
        }

        $copyTo = $this->dataHelper->getEmailCopyTo() ? explode(',', $this->dataHelper->getEmailCopyTo()) : [];
        $copyMethod = $this->dataHelper->getEmailCopyMethod();
        $bcc = [];

        if (!empty($copyTo)) {
            switch ($copyMethod) {
                case 'bcc':
                    $bcc = $copyTo;
                    break;
                case 'copy':
                    foreach ($copyTo as $email) {
                        $sendTo[] = ['email' => $email, 'name' => null];
                    }
                    break;
            }
        }

        foreach ($sendTo as $recipient) {

            if ((int)$order->getThirdPartyPickup() != 1) {
                $order->setThirdPartyPickup(NULL);
            }

            if ($order->getShippitAuthorityToLeave()) {
                $authToLeaveText = 'Yes';
            } else {
                $authToLeaveText = 'No';
            } 

            $shippingMethod = $order->getShippingMethod();
            $isStorePickup = (bool) ($shippingMethod == self::STORE_PICKUP_METHOD);
            $isExpressDelivery = (bool) ($shippingMethod == self::EXPRESS_DELIVERY);
            $isOnDemandDelivery = (bool) ($shippingMethod == self::ONDEMAND_DELIVERY);
            $isPriorityDelivery = (bool) (str_contains($shippingMethod, 'Priority'));
            $isStandardDelivery = (bool) ($shippingMethod == self::STANDARD_DELIVERY);
            $isFreeDelivery = (bool) (str_contains($shippingMethod, self::FREE_DELIVERY));
            $isAuthorityToLeaveActive = (bool) $this->dataHelper->isAuthorityToLeaveActive();

            /** @var Order $order */
            $templateVars = [
                'order' => $order,
                'order_id' => $order->getId(),
                'billing' => $order->getBillingAddress(),
                'payment_html' => $this->getPaymentHtml($order),
                'store' => $store,
                'order_store' => $order->getStore(),
                'store_name' => $store->getStoreName(),
                'created_at_formatted' => $order->getCreatedAtFormatted(10),
                'original_store' => $originalStore,
                'customer_name' => $order->getCustomerName(),
                'customerLoyaltyData' => $customerLoyaltyData,
                'formattedShippingAddress' => $this->getFormattedShippingAddress($order),
                'formattedBillingAddress' => $this->getFormattedBillingAddress($order),
                'authToLeaveText' => $authToLeaveText,
                'authToLeaveActive' => (!$isStorePickup && $isAuthorityToLeaveActive) ? true : false,
                'is_store_pickup' => $isStorePickup,
                'is_express_delivery' => $isExpressDelivery,
                'is_ondemand_delivery' => $isOnDemandDelivery,
                'is_priority_delivery' => $isPriorityDelivery,
                'is_express' => $isExpressDelivery,
                'is_ondemand' => $isOnDemandDelivery,
                'is_priority' => $isPriorityDelivery,
                'is_ed' => $isExpressDelivery,
                'is_od' => $isOnDemandDelivery,
                'is_pd' => $isPriorityDelivery,
                'is_standard_delivery' => $isStandardDelivery,
                'is_free_delivery' => $isFreeDelivery,
                'order_data' => [
                    'customer_name' => $order->getCustomerName(),
                    'is_not_virtual' => $order->getIsNotVirtual(),
                    'email_customer_note' => $order->getEmailCustomerNote(),
                    'frontend_status_label' => $order->getFrontendStatusLabel()
                ]
            ];

            try {
                $mailer = $this->transportBuilder
                    ->setTemplateIdentifier($template)
                    ->setTemplateOptions([
                        'area' => Area::AREA_FRONTEND,
                        'store' => $order->getStoreId(),
                    ])
                    ->setTemplateVars($templateVars)
                    ->setFrom($sender)
                    ->addBcc($bcc)
                    ->addTo($recipient['email'], $recipient['name']);

                $transport = $mailer->getTransport();
                $transport->sendMessage();

            } catch (\Exception $e) {
                $this->logger->critical($e->getMessage());
                $this->logger->critical($e->getTraceAsString());
            }
        }
    }

    /**
     * @param $order
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function isB2bCustomer($order)
    {
        $status = $this->balanceHelper->getEnabled();
        if (!$order->getCustomerIsGuest() && $status) {
            $orderLoad = $this->orderRepository->get($order->getId());
            if ($orderLoad->getExtensionAttributes()->getCompanyOrderAttributes()) {
                $orderCompanyId = $orderLoad->getExtensionAttributes()->getCompanyOrderAttributes()->getCompanyId();
                if ($orderCompanyId) {
                    return true;
                }
            }
        }
        return false;
    }
}
