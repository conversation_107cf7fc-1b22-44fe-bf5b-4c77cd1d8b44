<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Model;

use Magento\UrlRewrite\Model\UrlPersistInterface;
use Totaltools\Storelocator\Api\Data\StoreShippitInterface;

/**
 * Class Store
 * @package Totaltools\Storelocator\Model
 */
class Store extends \Magestore\Storelocator\Model\Store implements StoreShippitInterface
{
    /**
     * Define stock_availability_code
     */
    const STOCK_ON_DEMAND = 'OD';
    const STOCK_ON_CATALOG = 'OC';
    const STOCK_ONLINE = 'OL';
    const TYPE_OE = 'OE';
    const STOCK_SPECIAL_PRODUCT = 'SP';
    const STOCK_CLEARANCE_PRODUCT = 'OX';
    const OP_PRODUCT = 'OP';
    const SHIPPING_DANGEROUS_KEY = 'shipping_dangerous';
    const KNIFE_COMPLIANCE_KEY = 'knife_compliance';
    const PRE_ORDER_KEY = 'pre_order';
    const EXCLUSIVE_PRODUCT_KEY = 'eb_product';
    const IS_SPECIAL_ORDER = 'is_a_special_order';
    const STOCK_AVAILABILITY_KEY = 'stock_availability_code';

    const IS_BACKORDERABLE = 'is_backorderable';


    const STOCKCHECK_REQUIRED_PRODUCT_TYPES = [
        self::STOCK_ON_CATALOG,
        self::STOCK_ONLINE,
        self::TYPE_OE,
        self::STOCK_CLEARANCE_PRODUCT,
        self::OP_PRODUCT,
    ];

    const UNSHIPPABLE_PRODUCT_TYPES = [
        self::STOCK_SPECIAL_PRODUCT,
    ];

    /**
     * Define filters for store search
     */
    const ALLOW_STORE_COLLECTION_ENABLED = 1;
    const DEFAULT_FILTER = [
        'status'=> \Magestore\Storelocator\Model\Status::STATUS_ENABLED,
        'allow_store_collection' => self::ALLOW_STORE_COLLECTION_ENABLED,
    ];
    const ZONE_FILTER = [
        'status'=> \Magestore\Storelocator\Model\Status::STATUS_ENABLED,
    ];

    /**
     * @var StoreFallbackFactory
     */
    protected $_fallbackStoreFactory;

    /**
     * @var \Magento\Framework\App\Request\Http
     */
    protected $_request;

    /**
     * @var StoreFallbackRepository
     */
    protected $_storeFallbackRepository;

    /**
     * Store constructor.
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Directory\Model\RegionFactory $regionFactory
     * @param \Magento\Directory\Model\CountryFactory $countryFactory
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory
     * @param \Magestore\Storelocator\Model\ResourceModel\Specialday\CollectionFactory $specialdayCollectionFactory
     * @param \Magestore\Storelocator\Model\ResourceModel\Holiday\CollectionFactory $holidayCollectionFactory
     * @param \Magestore\Storelocator\Model\ResourceModel\Image\CollectionFactory $imageCollectionFactory
     * @param \Magento\UrlRewrite\Model\ResourceModel\UrlRewriteCollectionFactory $urlRewriteCollectionFactory
     * @param \Magestore\Storelocator\Model\SystemConfig $systemConfig
     * @param \Magestore\Storelocator\Model\StoreUrlPathGeneratorInterface $storeUrlPathGenerator
     * @param \Magestore\Storelocator\Model\StoreUrlRewriteGeneratorInterface $storeUrlRewriteGenerator
     * @param UrlPersistInterface $urlPersist
     * @param StoreFallbackFactory $fallBackStoreFactory
     * @param \Magento\Framework\App\Request\Http $_request
     * @param StoreFallbackRepository $storeFallbackRepository
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|NULL $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|NULL $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Directory\Model\RegionFactory $regionFactory,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        \Magestore\Storelocator\Model\ResourceModel\Specialday\CollectionFactory $specialdayCollectionFactory,
        \Magestore\Storelocator\Model\ResourceModel\Holiday\CollectionFactory $holidayCollectionFactory,
        \Magestore\Storelocator\Model\ResourceModel\Image\CollectionFactory $imageCollectionFactory,
        \Magento\UrlRewrite\Model\ResourceModel\UrlRewriteCollectionFactory $urlRewriteCollectionFactory,
        \Magestore\Storelocator\Model\SystemConfig $systemConfig,
        \Magestore\Storelocator\Model\StoreUrlPathGeneratorInterface $storeUrlPathGenerator,
        \Magestore\Storelocator\Model\StoreUrlRewriteGeneratorInterface $storeUrlRewriteGenerator,
        UrlPersistInterface $urlPersist,
        \Totaltools\Storelocator\Model\StoreFallbackFactory $fallBackStoreFactory,
        \Magento\Framework\App\Request\Http $_request,
        \Totaltools\Storelocator\Model\StoreFallbackRepository $storeFallbackRepository,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = NULL,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = NULL,
        array $data = []
    )
    {
        parent::__construct($context, $registry, $regionFactory, $countryFactory, $jsonHelper, $storeCollectionFactory,
            $specialdayCollectionFactory, $holidayCollectionFactory, $imageCollectionFactory, $urlRewriteCollectionFactory,
            $systemConfig, $storeUrlPathGenerator, $storeUrlRewriteGenerator, $urlPersist, $resource, $resourceCollection, $data);

        $this->_fallbackStoreFactory = $fallBackStoreFactory;
        $this->_request = $_request;
        $this->_storeFallbackRepository = $storeFallbackRepository;
    }

    /**
     * After save action.
     *
     * @return $this
     */
    public function afterSave()
    {
        parent::afterSave();

        $this->_saveFallbackStores();

        return $this;
    }

    /**
     * @return void
     * @throws \Exception
     */
    protected function _saveFallbackStores()
    {
        $fallbackStoresOrder = $this->_request->getPostValue('fallback_stores_order');
        if (!$fallbackStoresOrder) {
            return;
        }
        $newFallbackStoreIds = array_filter(explode(',', $fallbackStoresOrder));
        $existingFallbackStores = $this->_storeFallbackRepository->getFallbackStores($this->getId());
        foreach ($existingFallbackStores as $fallback) {
            $fallback->delete();
        }
        foreach ($newFallbackStoreIds as $order => $storeId) {
            $fallback = $this->_fallbackStoreFactory->create();
            $fallback->setStoreId($this->getId())
                    ->setFallbackStoreId($storeId)
                    ->save();
        }
    }

    /**
     * Get Shippit Api key.
     *
     * @return string
     */
    public function getShippitApiKey()
    {
        return $this->getData(StoreShippitInterface::SHIPPIT_API_KEY);
    }

    /**
     * Get Shippit Api Key Second.
     *
     * @return string
     */
    public function getShippitApiKeySecond()
    {
        return $this->getData(StoreShippitInterface::SHIPPIT_API_KEY_SECOND);
    }

    /**
     * @inheritdoc
     */
    protected function _filterDays(\Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection $collection)
    {
        $collection = parent::_filterDays($collection);
        $collection->setOrder('date_from', 'ASC');

        return $collection;
    }
}