<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model;

use Magento\Framework\Api\SearchCriteriaBuilder;
use Totaltools\Storelocator\Api\Data\StoreInventoryInterface;
use Totaltools\Customer\Helper\AttributeData;
use Totaltools\Storelocator\Model\ResourceModel\StoreInventory as StoreInventoryResource;

/**
 * Class StoreInventoryService.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class StoreInventoryService
{
    /**
     * @var SearchCriteriaBuilder
     */
    protected $_searchCriteriaBuilder;

    /**
     * @var StoreInventoryRepository
     */
    protected $_storeInventoryRepository;

    /**
     * @var StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var AttributeData
     */
    protected $_customerAttributeData;

    /**
     * @var StoreInventoryResource
     */
    protected $storeInventoryResource;

    /**
     * CSVImport constructor.
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param StoreInventoryRepository $inventoryRepository
     * @param StoreRepository $storeRepository
     * @param AttributeData $attributeData
     * @param StoreInventoryResource $storeInventoryResource
     */
    public function __construct(
        SearchCriteriaBuilder $searchCriteriaBuilder,
        \Totaltools\Storelocator\Model\StoreInventoryRepository $inventoryRepository,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        AttributeData $attributeData,
        StoreInventoryResource $storeInventoryResource
    ){
        $this->_searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->_storeInventoryRepository = $inventoryRepository;
        $this->_storeRepository = $storeRepository;
        $this->_customerAttributeData = $attributeData;
        $this->storeInventoryResource = $storeInventoryResource;
    }

    /**
     * Get store inventory by ERP IDs.
     * The list can be filtered by products list.
     *
     * @param array      $erpIds
     * @param array|null $products
     *
     * @return \Magento\Framework\Api\SearchResultsInterface
     */
    public function getStoreInventoryByErpIds($erpIds, $products = null)
    {
        $searchCriteria = $this->_searchCriteriaBuilder;

        if (is_array($erpIds)) {
            $searchCriteria->addFilter(StoreInventoryInterface::ERP_ID, $erpIds, 'in');
        } else {
            $searchCriteria->addFilter(StoreInventoryInterface::ERP_ID, $erpIds, 'eq');
        }

        if ($products != null) {
            $searchCriteria->addFilter(StoreInventoryInterface::SKU, array_keys($products), 'in');
        }

        return $this->_storeInventoryRepository->getList($searchCriteria->create());
    }

    /**
     * @param Store $store
     * @param $products
     * @return bool
     */
    public function checkStoreInventory(\Totaltools\Storelocator\Model\Store $store, $products)
    {
        if (empty($products)) {
            return true;
        }

        $stockInventory = $this->getStoreInventoryByErpIds($store->getErpId(), $products);

        $stockInventoryItems = $stockInventory->getItems();

        if (!count($stockInventoryItems)) {
            return false;
        }

        if (count($stockInventoryItems) < count($products)) {
            return false;
        }

        foreach ($stockInventoryItems as $inventory) {
            $hasInventory = $products[$inventory->getSku()] <= $inventory->getUnits();

            if (!$hasInventory) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if store has required qty for the sku.
     *
     * @param Store $store
     * @param $sku
     * @param $qty
     * @return bool
     */
    public function checkStoreInventoryForSku(\Totaltools\Storelocator\Model\Store $store, $sku, $qty)
    {

        $stockInventory = $this->getStoreInventoryByErpIds($store->getErpId(), [$sku => $qty]);

        $hasInventory = false;

        foreach ($stockInventory->getItems() as $inventory) {

            if ($qty <= $inventory->getUnits()) {

                $hasInventory = true;
                break;
            }
        }

        return $hasInventory;
    }


    /**
     * Find out the store which should fulfil the order. It can either be store itself or a fallback store which has the stock.
     * ---- If Store has all stock then it should fulfill the order
     * ---- If Store does not have the stock then find the fallback store that has all the stock and that store should fulfil the order.
     * ---- If no fallback store has stock then the main store should filfull the stock as it owns the postcode.
     *
     * @param Store $store
     * @param $products
     * @return Store
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getStoreWhichShouldFulfillTheOrder(\Totaltools\Storelocator\Model\Store $store, $products)
    {

        // For B2B Customers, only the owner store should fulfill the order.
        $isB2bCustomer = $this->_customerAttributeData->isB2bCustomer();
        if ($isB2bCustomer) {

            return $store;
        }

        $mainStoreErpId = $store->getErpId();

        $erpIds = [$mainStoreErpId];

        $fallbackStoresCollection = $this->_storeRepository->getFallbackStores($store->getId());

        foreach ($fallbackStoresCollection->getItems() as $fallbackStore) {

            $erpIds[] = $fallbackStore->getErpId();
        }

        $stockInventory = $this->getStoreInventoryByErpIds($erpIds, $products);

        $availablePerStore = [];

        foreach ($stockInventory->getItems() as $inventory) {

            if(!isset($products[$inventory->getSku()])) {

                continue;
            }

            if($products[$inventory->getSku()] <= $inventory->getUnits()) {

                $availablePerStore[$inventory->getErpId()][] = $inventory->getSku();
            }
        }

        if(isset($availablePerStore[$mainStoreErpId]) && sizeof(array_unique($availablePerStore[$mainStoreErpId])) >= sizeof($products)) {

            return $store;
        }

        foreach ($fallbackStoresCollection->getItems() as $fallbackStore) {

            $fallbackStoreErpId = $fallbackStore->getErpId();

            if(isset($availablePerStore[$fallbackStoreErpId]) && sizeof(array_unique($availablePerStore[$fallbackStoreErpId])) >= sizeof($products) ) {

                $fallbackStore = $this->_storeRepository->getByErpId($fallbackStoreErpId);

                if($fallbackStore) {
                    return $fallbackStore;
                }
            }

        }

        if ($superStoreId = $store->getSuperStoreId()) {
            if ( $superStoreId != $store->getId()) {
                $superStore = $this->_storeRepository->getById($superStoreId);
                if ($superStore) {
                    return $superStore;
                }
            }
        }

        return $store;
    }

    public function updateStoreInventory($erpId, $products = null)
    {
        if (!empty($products) && !empty($erpId)) {
            $stockInventory = $this->getStoreInventoryByErpIds($erpId, $products);
            $stockInventoryItems = $stockInventory->getItems();
            foreach ($stockInventoryItems as $inventory) {
                $sku = $inventory->getSku();
                $orderedQty = $products[$sku] ?? 0;
                $newUnits = $inventory->getUnits() - $orderedQty;
                $inventory->setUnits($newUnits);
                $this->storeInventoryResource->save($inventory);
            }
        }
    }
}
