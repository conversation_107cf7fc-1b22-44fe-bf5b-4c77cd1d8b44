<?php
namespace Totaltools\Storelocator\Model;

use Magento\Framework\Model\AbstractModel;
use Totaltools\Storelocator\Api\Data\InventoryimportreportInterface;

class Inventoryimportreport extends AbstractModel implements InventoryimportreportInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Totaltools\Storelocator\Model\ResourceModel\Inventoryimportreport::class);
    }


    /**
     * Get EntityId.
     *
     * @return int
     */
    public function getEntityId()
    {
        return $this->getData(self::ENTITY_ID);
    }

    /**
     * Set EntityId.
     */
    public function setEntityId($entityId)
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * Get Filename.
     *
     * @return varchar
     */
    public function getFilename()
    {
        return $this->getData(self::FILENAME);
    }

    /**
     * Set Filename.
     */
    public function setFilename($filename)
    {
        return $this->setData(self::FILENAME, $filename);
    }

    /**
     * Get ImportType.
     *
     * @return varchar
     */
    public function getImportType()
    {
        return $this->getData(self::IMPORT_TYPE);
    }

    /**
     * Set ImportType.
     */
    public function setImportType($importType)
    {
        return $this->setData(self::IMPORT_TYPE, $importType);
    }

    /**
     * Get CreatedAt.
     *
     * @return varchar
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * Set CreatedAt.
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * Get UpdatedAt.
     *
     * @return varchar
     */
    public function getUpdatedAt()
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * Set UpdatedAt.
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }

    /**
     * Get Status.
     *
     * @return varchar
     */
    public function getStatus()
    {
        return $this->getData(self::STATUS);
    }

    /**
     * Set Status.
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get getComment.
     *
     * @return varchar
     */
    public function getComment()
    {
        return $this->getData(self::COMMENT);
    }

    /**
     * Set Comment.
     */
    public function setComment($comment)
    {
        return $this->setData(self::COMMENT, $comment);
    }

    /**
     * Get rowCountInFile.
     *
     * @return varchar
     */
    public function getRowCountInFile()
    {
        return $this->getData(self::ROW_COUNT_IN_FILE);
    }

    /**
     * Set rowCountInFile.
     */
    public function setRowCountInFile($rowCountInFile)
    {
        return $this->setData(self::ROW_COUNT_IN_FILE, $rowCountInFile);
    }
}
