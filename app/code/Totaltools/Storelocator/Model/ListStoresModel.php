<?php

namespace Totaltools\Storelocator\Model;

use Totaltools\Storelocator\Api\ListStoresInterface;
use Magento\Framework\App\ResourceConnection;

class ListStoresModel implements ListStoresInterface
{

    protected $_resourceConnection;

    protected $_connection;

    public function __construct(ResourceConnection $resourceConnection)
    {
        $this->_resourceConnection = $resourceConnection;
    }

    /**
     * Returns json containing all stores
     *
     * @api
     * @return string json containing all stores
     */
    public function listStores()
    {

        $this->_connection = $this->_resourceConnection->getConnection();


        $collection = $this->_connection->fetchAll("SELECT state, store_name FROM magestore_storelocator_store WHERE status = 1 ORDER BY state, store_name ASC");

        $stores = [];

        foreach ($collection as $item) {

            $stores[$item["state"]][] = $item["store_name"];
        }

        return json_encode($stores);
    }
}