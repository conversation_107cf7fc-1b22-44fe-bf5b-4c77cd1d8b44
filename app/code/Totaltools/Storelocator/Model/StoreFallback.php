<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Model;

use Magento\Framework\DataObject\IdentityInterface;
use Magento\Framework\Model\AbstractModel;
use Totaltools\Storelocator\Api\Data\StoreFallbackInterface;

/**
 * Class StoreFallback
 *
 * @package Totaltools\Storelocator\Model
 */
class StoreFallback extends AbstractModel implements StoreFallbackInterface, IdentityInterface
{
    /**
     * CMS page cache tag
     */
    const CACHE_TAG = 'magestore_storelocator_fallback';

    /**
     * Cache tag.
     *
     * @var string
     */
    protected $_cacheTag = 'magestore_storelocator_fallback';

    /**
     * Prefix of model events names
     *
     * @var string
     */
    protected $_eventPrefix = 'magestore_storelocator_fallback';

    /**
     * Initialize resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('Totaltools\Storelocator\Model\ResourceModel\StoreFallback');
    }

    /**
     * Return unique ID(s) for each object in system
     *
     * @return array
     */
    public function getIdentities()
    {
        return [self::CACHE_TAG . '_' . $this->getId()];
    }

    /**
     * Get Row ID
     *
     * @return int|null
     */
    public function getId()
    {
        return $this->getData(self::ROW_ID);
    }

    /**
     * Get Store ID
     *
     * @return int|null
     */
    public function getStoreId()
    {
        return $this->getData(self::STORE_ID);
    }


    /**
     * Get Store Fallback ID
     *
     * @return int|null
     */
    public function getFallbackStoreId()
    {
        return $this->getData(self::FALLBACK_STORE_ID);
    }

    /**
     * Set Row ID
     *
     * @param int $id
     *
     * @return $this
     */
    public function setId($id)
    {
        $this->setData(self::ROW_ID, $id);

        return $this;
    }

    /**
     * Set Store ID
     *
     * @param int $id
     *
     * @return $this
     */
    public function setStoreId($storeId)
    {
        $this->setData(self::STORE_ID, $storeId);

        return $this;
    }


    /**
     * Set Fallback Store ID
     *
     * @return $this
     */
    public function setFallbackStoreId($fallbackStoreId)
    {
        $this->setData(self::FALLBACK_STORE_ID, $fallbackStoreId);

        return $this;
    }
}