<?php
/**
 * Totaltools Storelocator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Model;

/**
 * @api
 */
class Registry extends \Magento\Framework\DataObject
{
    const STORELOCATOR_STORE_ID = 'session_store_id';
    const STORELOCATOR_API_KEY = 'storelocator_api_key';
    const DELIVERY_LOCATION = 'delivery_location_session';
    const GUEST_LOCATION = 'guest_location_session';
    const OD_ITEM_EXISTED = 'session_od_item_existed';
    const OE_ITEM_EXISTED = 'session_oe_item_existed';
    const OX_ITEM_EXISTED = 'session_ox_item_existed';

    /**
     * @var \Magento\Checkout\Model\Session
     */
    private $checkoutSession;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var bool
     */
    private $hasStoresData = false;

    /**
     * @var bool
     */
    private $locked = false;

    /**
     * @var array
     */
    private $originStoreData = [
        self::STORELOCATOR_API_KEY => null,
        self::STORELOCATOR_STORE_ID => null,
        self::OD_ITEM_EXISTED => null,
        self::OE_ITEM_EXISTED => null,
        self::OX_ITEM_EXISTED => null,
        self::DELIVERY_LOCATION => [],
        self::GUEST_LOCATION => []
    ];

    /**
     * Registry constructor.
     *
     * @param \Magento\Framework\Session\SessionManagerInterface $resourceSession
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Session\SessionManagerInterface $resourceSession,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        array $data = []
    ) {
        $this->checkoutSession = $resourceSession;
        $this->scopeConfig = $scopeConfig;

        parent::__construct($data);
    }

    /**
     * Retrieve quote from checkout session.
     *
     * @return \Magento\Quote\Model\Quote
     */
    public function getQuote()
    {
        return $this->checkoutSession->getQuote();
    }

    /**
     * Refresh the session data.
     *
     * @return void
     */
    public function reset()
    {
        if ($this->hasStoresData === true) {
            $this->locked = true;

            return;
        }

        $this->checkoutSession->setTtStoreData($this->originStoreData);
    }

    /**
     * Add storelocator data then add it to checkout session.
     *
     * @param array $storeData
     *
     * @return \Totaltools\Storelocator\Model\Registry
     */
    public function addStoreData($storeData)
    {
        if ($this->locked) {
            return $this;
        }

        if ($this->hasStoresData === false) {
            $this->reset();
        }

        $this->hasStoresData = true;
        $currentStoreData = $this->getStoresData();

        $storeData = array_merge($currentStoreData, $storeData);

        $this->checkoutSession->setTtStoreData($storeData);
        $this->setFallbackData($storeData);

        return $this;
    }

    /**
     * Override the store data in the checkout session.
     *
     * @param array $data
     *
     * @return \Totaltools\Storelocator\Model\Registry
     */
    public function setStoresData($data)
    {
        $this->checkoutSession->setTtStoreData($data);

        return $this;
    }

    /**
     * Get storelocator data from the checkout session.
     *
     * @return array
     */
    public function getStoresData()
    {
        $storesData = $this->checkoutSession->getTtStoreData();

        if (empty($storesData) === true) {
            return $this->originStoreData;
        }

        return $storesData;
    }

    /**
     * Get storelocator store id from checkout session.
     *
     * @return int
     */
    public function getStoreId()
    {
        $storesData = $this->checkoutSession->getTtStoreData();

        if (empty($storesData) === true) {
            return 0;
        }

        return $storesData[self::STORELOCATOR_STORE_ID];
    }

    /**
     * Fallback store data for old APIs.
     *
     * @param array $storeData
     *
     * @return \Totaltools\Storelocator\Model\Registry
     */
    public function setFallbackData($storeData)
    {
        if (empty($storeData) === true) {
            $storeData = $this->checkoutSession->getTtStoreData();
        }

        /** Fallback session data */
        $this->checkoutSession->setGuestLocationSession($storeData[self::GUEST_LOCATION] ?: []);
        $this->checkoutSession->setDeliveryLocationSession($storeData[self::DELIVERY_LOCATION] ?: []);
        $this->checkoutSession->setStorelocatorStore($storeData[self::STORELOCATOR_STORE_ID] ?: null);
        $this->checkoutSession->setStorelocatorApiKey($storeData[self::STORELOCATOR_API_KEY] ?: null);
        $this->checkoutSession->setOdItemExisted($storeData[self::OD_ITEM_EXISTED] ?: null);
        $this->checkoutSession->setOxItemExisted($storeData[self::OX_ITEM_EXISTED] ?: null);
        $this->checkoutSession->setOeItemExisted($storeData[self::OE_ITEM_EXISTED] ?: null);

        return $this;
    }
}
