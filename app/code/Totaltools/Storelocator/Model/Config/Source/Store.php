<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Model\Config\Source;

use Magento\Directory\Model\ResourceModel\Region\CollectionFactory as RegionCollectionFactory;
use Magento\Framework\Exception\LocalizedException;
use Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory as StoreCollectionFactory;

/**
 * Class Store
 *
 * @package Totaltools\Storelocator\Model\Config\Source
 */
class Store extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    /**
     * @var StoreCollectionFactory
     */
    protected $storeCollectionFactory;

    /**
     * @var RegionCollectionFactory
     */
    protected $regionCollectionFactory;

    /**
     * @var \Magento\Framework\App\State
     */
    private $state;

    /**
     * Store constructor.
     * @param StoreCollectionFactory $storeCollectionFactory
     * @param RegionCollectionFactory $regionCollectionFactory
     * @param \Magento\Framework\App\State $state
     */
    public function __construct(
        StoreCollectionFactory $storeCollectionFactory,
        RegionCollectionFactory $regionCollectionFactory,
        \Magento\Framework\App\State $state
    ) {
        $this->state = $state;
        $this->storeCollectionFactory = $storeCollectionFactory;
        $this->regionCollectionFactory = $regionCollectionFactory;
    }

    /**
     * @return \Magestore\Storelocator\Model\ResourceModel\Store\Collection
     */
    public function getStorelocatorCollection()
    {
        /** @var \Magestore\Storelocator\Model\ResourceModel\Store\Collection $storeCollection */
        $storeCollection = $this->storeCollectionFactory->create();

        try {
            if ($this->state->getAreaCode() !== \Magento\Framework\App\Area::AREA_ADMINHTML) {
                $storeCollection->addFieldToFilter('allow_store_collection', 1);
            }

            $storeCollection->addFieldToSelect(['storelocator_id', 'erp_id', 'store_name']);
            $storeCollection->addOrder('store_name', 'ASC');
        } catch (LocalizedException $exception) {
        }

        return $storeCollection;
    }

    /**
     * @param bool $withEmpty
     * @param bool $defaultValues
     *
     * @return array
     */
    public function getAllOptions($withEmpty = true, $defaultValues = false)
    {
        $storeCollection = $this->getStorelocatorCollection();
        $options = [];
        foreach ($storeCollection as $index => $store) {
            $options[] = [
                'value' => $defaultValues ? $store->getId() : $store->getErpId(),
                'label' => $store->getStoreName(),
            ];
        }

        if ($withEmpty) {
            $options = $this->addEmptyOption($options);
        }

        return $options;
    }

    /**
     * Add an empty option to the array
     *
     * @param array $options
     *
     * @return array
     */
    private function addEmptyOption(array $options)
    {
        array_unshift($options, ['label' => __('Please select a preferred store'), 'value' => '']);

        return $options;
    }

    public function getAllRegions($country_id)
    {
        $regions = [];
        $collection = $this->regionCollectionFactory->create();
        $collection->addCountryFilter($country_id);
        if (sizeof($collection) > 0) {
            foreach ($collection as $region) {
                $regions[] = [
                    'id' => $region->getId(),
                    'name' => $region->getName()
                ];
            }
        }

        return $regions;
    }

    public function getStoresByFilters($erpId, $latitudeFrom, $longitudeFrom, $states, $locations, $structures)
    {
        /** @var \Magestore\Storelocator\Model\ResourceModel\Store\Collection $storeCollection */
        $storeCollection = $this->storeCollectionFactory->create();
        $storeCollection->addFieldToFilter('allow_store_collection', 1)
        ->addFieldToFilter('status', 1)
        ->addFieldToFilter('is_visible', 1);

        if ($states) {
            $storeCollection->addFieldToFilter('state', ['in' => $states]);
        }

        if ($locations) {
            $storeCollection->addFieldToFilter('location', ['in' => $locations]);
        }

        if ($structures) {
            $storeCollection->addFieldToFilter('structure', ['in' => $structures]);
        }

        $storeCollection->addFieldToFilter('erp_id', ['neq' => $erpId]);
        $storeCollection->addFieldToSelect(['store_name', 'location', 'structure', 'franchisee_group', 'latitude', 'longitude']);
        $storeCollection->addOrder('store_name', 'ASC');
        $stores = [];
        foreach ($storeCollection as $store) {
            $latitudeTo = $store['latitude'];
            $longitudeTo = $store['longitude'];
            $distance = $this->calculateDistance($latitudeFrom, $longitudeFrom, $latitudeTo, $longitudeTo);
            $stores[] = [
                'name' => $store['store_name'],
                'location' =>  $store['location'],
                'structure' => $store['structure'],
                'franchisee_group' => $store['franchisee_group'],
                'distance' => $distance
            ];
        }

        if ($stores) {
            usort($stores, fn ($a, $b) => $a['distance'] <=> $b['distance']);
        }

        return $stores;
    }

    public function calculateDistance(
        float $latitudeFrom,
        float $longitudeFrom,
        float $latitudeTo,
        float $longitudeTo
    ): float {
        $earthRadius = 6371; // in kilometers

        $dLat = deg2rad($latitudeTo - $latitudeFrom);
        $dLon = deg2rad($longitudeTo - $longitudeFrom);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($latitudeFrom)) * cos(deg2rad($latitudeTo)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }
}
