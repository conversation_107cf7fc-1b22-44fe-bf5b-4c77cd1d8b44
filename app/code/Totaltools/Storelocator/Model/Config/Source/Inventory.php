<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Storelocator\Model\Config\Source;

/**
 * Class Inventory
 * @package Totaltools\Storelocator\Model\Config\Source
 */
class Inventory
{
    const CONFIG_INVENTORY_DIRECTORY = 'storelocator/inventory/directory';
    const CONFIG_INVENTORY_API_LOG_DIRECTORY = 'storelocator/inventory/api_directory';
    const CONFIG_INVENTORY_DAYS = 'storelocator/inventory/days';
    const CONFIG_INVENTORY_LOG = 'storelocator/inventory/logs';
    const CONFIG_INVENTORY_EMAIL_RECEIVER = 'storelocator/inventory/email_receiver';
    const CONFIG_INVENTORY_API_EMAIL_RECIPIENT = 'storelocator/inventory/soh_api_email_recipient';
    const CONFIG_INVENTORY_MAX_LAST_RUN_BOUNDARY = 'storelocator/inventory/inventoryimport_last_run_boundary';
    const CONFIG_INVENTORY_MAX_PARTIAL_SIZE = 'storelocator/inventory/max_partial_import_size_kb';
    const CONFIG_INVENTORY_MAX_FULL_PERCENTAGE = 'storelocator/inventory/max_full_import_deviation_percentage';

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    public $scopeConfig;

    /**
     * @var \Magento\Framework\Serialize\Serializer\Json
     */
    private $serialize;

    /**
     * Inventory constructor.
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\Serialize\Serializer\Json $serialize
     */
    public function __construct(
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Serialize\Serializer\Json $serialize
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->serialize = $serialize;
    }

    /**
     * @param $path
     * @return string|null
     */
    public function getDefaultValue($path)
    {
        return $this->scopeConfig->getValue(
            $path,
            \Magento\Framework\App\Config\ScopeConfigInterface::SCOPE_TYPE_DEFAULT
        );
    }
}
