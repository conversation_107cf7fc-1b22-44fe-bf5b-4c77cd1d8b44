<?php
/**
 * Totaltools Storelocator.
 *
 * @category   Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright  2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace TotalTools\Storelocator\Model\Config\Source\Email;

/**
 * Class Template
 * @package TotalTools\Storelocator\Model\Config\Source\Email
 */
class Template extends \Magento\Config\Model\Config\Source\Email\Template
{
    /**
     * Generate list of email templates
     *
     * @return array
     */
    public function toOptionArray()
    {
        $collection = $this->_templatesFactory->create();
        $collection->load();

        return $collection->toOptionArray();
    }
}
