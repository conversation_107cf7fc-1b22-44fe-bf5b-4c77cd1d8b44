<?php
/**
 * Total Tools Store Locator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Model;

use Exception;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Storelocator\Model\ImportInventory\Email;
use Totaltools\Storelocator\Parser\File;
use Totaltools\Storelocator\Parser\Parser;
use Zend_Db_Exception;

/**
 * @api
 */
class InventoryProcessor
{
    /**
     * @var File
     */
    private $fileParser;

    /**
     * @var Parser
     */
    private $csvParser;

    /**
     * @var TimezoneInterface
     */
    private $localeDate;

    /**
     * @var InventoryimportreportFactory
     */
    private $inventoryImportReportFactory;

    /**
     * @var Email
     */
    private $transport;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var string
     */
    private string $fullRunErrorMessages;

    /**
     * InventoryProcessor constructor.
     * @param File $fileParser
     * @param Parser $csvParser
     * @param TimezoneInterface $localeDate
     * @param InventoryimportreportFactory $inventoryImportReportFactory
     * @param Email $email
     * @param LoggerInterface $logger
     */
    public function __construct(
        File $fileParser,
        Parser $csvParser,
        TimezoneInterface $localeDate,
        InventoryimportreportFactory $inventoryImportReportFactory,
        Email $email,
        LoggerInterface $logger
    ) {
        $this->fileParser = $fileParser;
        $this->csvParser = $csvParser;
        $this->localeDate = $localeDate;
        $this->inventoryImportReportFactory = $inventoryImportReportFactory;
        $this->transport = $email;
        $this->logger = $logger;
        $this->fullRunErrorMessages = '';
    }

    /**
     * Inventory processing.
     *
     * @return $this
     * @throws Zend_Db_Exception
     * @throws Exception
     */
    public function execute()
    {
        $processingFiles = $this->fileParser->getProcessingFiles();
        if (is_array($processingFiles)) {
            $startTime = $this->localeDate->date();
            if ($this->fileParser->isDebug()) {
                $this->logger->info(sprintf('Start importing inventory: %s.', $startTime->format('Y-m-d H:i:s')));
            }
            $full = [
                'exists' => false,
                'model' => false
            ];
            foreach ($processingFiles as $importFile) {
                $importModel = $this->inventoryImportReportFactory->create();
                if(strpos($importFile['text'], 'full') !== false){
                    $full['model'] = $importModel;
                    $full['exists'] = true;
                    $isFull = true;
                } else {
                    $isFull = false;
                }
                $this->csvParser->setIsFull($isFull);
                $filePath = $this->fileParser->getFilePath([
                    $this->fileParser->getProcessingDir(),
                    $importFile['text']
                ]);
                //start DB log
                $sTime = $this->localeDate->date();
                $importModel
                    ->addData([
                        'filename' => $importFile['text'],
                        'import_type' => $isFull === true ? 'full' : 'partial',
                        'updated_at' => $sTime->format('Y-m-d H:i:s'),
                        'status' => 'processing',
                        'row_count_in_file' => $this->_getLines($filePath)])
                    ->save();
                if ($this->fileParser->isDebug()) {
                    $this->logger->info(sprintf('Processing file: %s contains %s lines', $importFile['text'], $this->_getLines($filePath)));
                }
                //validate single run
                if(!$this->fileParser->validateFile($importFile['text'])) {
                    $this->manageErrors($importModel, $importFile['text']);
                    continue;
                }
                //do import
                $this->csvParser->parse($filePath);
                $this->fileParser->moveProcessedFile($importFile['text']);
                //finish DB log
                $eTime = $this->localeDate->date();
                if(count($this->csvParser->errorMessages) > 0 || count($this->fileParser->errorMessages) > 0) {
                    $this->manageErrors($importModel, $importFile['text']);
                } else {
                    $importModel
                        ->setStatus('complete')
                        ->setUpdatedAt($eTime->format('Y-m-d H:i:s'))
                        ->save();
                    if ($this->fileParser->isDebug()) {
                        $this->logger->info(sprintf('Processing file: %s successfully imported', $importFile['text']));
                    }
                }
            }
            $finishTime = $this->localeDate->date();
            if ($this->fileParser->isDebug()) {
                $this->logger->info(sprintf('Inventory: It took %s minutes and %s seconds to import.', $finishTime->diff($startTime)->i, $finishTime->diff($startTime)->s));
            }
            //validate full run
            if($full['exists'] === true && count($this->csvParser->successCountGroupedByErpId) > 0) {
                $this->transport->sendFullImportSuccessEmail($full['model'], $this->csvParser->successCountGroupedByErpId);
                $comment = sprintf('Processing ERP count results: %s', json_encode($this->csvParser->successCountGroupedByErpId));
                $full['model']
                    ->setUpdatedAt($finishTime->format('Y-m-d H:i:s'))
                    ->setComment($comment)
                    ->save();
                if ($this->fileParser->isDebug()) {
                    $this->logger->info($comment);
                    $this->logger->info('Inventory: Success Email sent');
                }
            } elseif($full['exists'] === true) {
                if($this->fileParser->fileUploadInProgress === false){
                    $this->transport->sendFullImportErrorEmail($importModel, $this->fullRunErrorMessages);
                }
                if ($this->fileParser->isDebug()) {
                    $this->logger->info('Inventory: Error Email sent');
                }
            } elseif(strlen($this->fullRunErrorMessages) > 0) {
                $this->transport->sendPartialImportErrorEmail($importModel, $this->fullRunErrorMessages);
                if ($this->fileParser->isDebug()) {
                    $this->logger->info('Inventory: Partial Error Email sent');
                }
            }
        } else {
            $messages = implode(PHP_EOL, $this->fileParser->errorMessages);
            $this->transport->sendErrorEmail(sprintf('Inventory: File retrieval process contains errors: %s', $messages));
            if ($this->fileParser->isDebug()) {
                $this->logger->info(sprintf('Inventory: File retrieval process contains errors: %s', $messages));
            }
        }
        $this->fileParser->deleteLockFile();
        if ($this->fileParser->isDebug()) {
            $this->logger->info(__('Delete lock file'));
        }

        return $this;
    }

    /**
     * Get number of lines in file
     *
     * @param string $file
     * @return int
     */
    private function _getLines(string $file)
    {
        $f = fopen($file, 'rb');
        $lines = 0;
        while (!feof($f)) {
            $lines += substr_count(fread($f, 8192), "\n");
        }
        fclose($f);

        return $lines;
    }

    /**
     * Get Error Messages
     *
     * @return string
     */
    private function getErrorMessages()
    {
        return implode(PHP_EOL, array_merge(
            $this->csvParser->errorMessages,
            $this->fileParser->errorMessages
        ));
    }

    /**
     * Manage Errors
     *
     * @param Inventoryimportreport $importModel
     * @param string $filename
     * @return void
     */
    private function manageErrors(Inventoryimportreport $importModel, string $filename)
    {
        $messages = $this->getErrorMessages();
        $this->fullRunErrorMessages .= $messages.PHP_EOL;
        $importModel
            ->setStatus('error')
            ->setComment($messages)
            ->setUpdatedAt($this->localeDate->date()->format('Y-m-d H:i:s'))
            ->save();
        if ($this->fileParser->isDebug()) {
            $this->logger->info(sprintf(
                'Processing file: %s contains errors: %s',
                $filename,
                $messages
            ));
        }
    }
}
