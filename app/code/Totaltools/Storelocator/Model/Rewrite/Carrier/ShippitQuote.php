<?php
/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> <<EMAIL>>
 */

namespace Totaltools\Storelocator\Model\Rewrite\Carrier;

use Magento\Framework\DataObject;
use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\Catalog\Model\Product\Type\AbstractType as ProductTypeAbstract;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable as ConfigurableProductType;
use Magento\GroupedProduct\Model\Product\Type\Grouped as GroupedProductType;
use Magento\Quote\Model\Quote\Address\RateRequest;
use Magento\Shipping\Model\Carrier\AbstractCarrierOnline;
use Magento\Shipping\Model\Carrier\CarrierInterface;
use Magento\Shipping\Model\Rate\Result;
use Totaltools\Storelocator\Model\Store;
use Totaltools\Storelocator\Helper\Shippit\Api;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Totaltools\Checkout\Helper\Data as CheckoutHelperData;

class ShippitQuote extends \Shippit\Shipping\Model\Carrier\Shippit
{
    const NOTICE_MODULE_DISABLED = 'Skipping Live Quote - The Module is not enabled';
    const NOTICE_NOMETHODS_SELECTED = 'Skipping Live Quote - No Shipping Methods are selected';
    const NOTICE_PRODUCTS_NOT_ELIGIBLE = 'Skipping Live Quote - The cart contains items not eligable for shipping';
    const STANDARD_DELIVERY_METHOD = 'Standard';
    const EXPRESS_DELIVERY_METHOD = 'Express';
    const UBER_DELIVERY_METHOD = 'ondemand';
    const UBER_ONDEMAND_CUSTOM_QUOTE_PRICE = 999;


    /**
     * @var string
     */
    protected $_code = \Shippit\Shipping\Helper\Data::CARRIER_CODE;

    /**
     * @var \Shippit\Shipping\Helper\Data
     */
    protected $_helper;
    /**
    * @var \Shippit\Shipping\Api\Request\QuoteInterface
    */
   protected $_quote;

    protected $scopeConfig;

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    private $pricingHelper;

    /**
     * @var TimezoneInterface
     */
    protected $timezoneInterface;

    /**
     * @var CheckoutHelperData
     */
    protected $checkoutHelperData;

    /**
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory $rateErrorFactory
     * @param \Magento\Framework\Logger\Monolog $logger
     * @param \Magento\Framework\Xml\Security $xmlSecurity
     * @param \Magento\Shipping\Model\Simplexml\ElementFactory $xmlElFactory
     * @param \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory $rateMethodFactory
     * @param \Magento\Shipping\Model\Tracking\ResultFactory $trackFactory
     * @param \Magento\Shipping\Model\Tracking\Result\ErrorFactory $trackErrorFactory
     * @param \Magento\Shipping\Model\Tracking\Result\StatusFactory $trackStatusFactory
     * @param \Magento\Directory\Model\RegionFactory $regionFactory
     * @param \Magento\Directory\Model\CountryFactory $countryFactory
     * @param \Magento\Directory\Model\CurrencyFactory $currencyFactory
     * @param \Magento\Directory\Helper\Data $directoryData
     * @param \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry
     * @param \Shippit\Shipping\Helper\Carrier\Shippit $helper
     * @param \Shippit\Shipping\Helper\Api $api
     * @param \Shippit\Shipping\Model\Config\Source\Shippit\Shipping\QuoteMethods $methods
     * @param \Shippit\Shipping\Api\Request\QuoteInterface $quote
     * @param \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory
     * @param \Totaltools\Storelocator\Helper\Shippit\Api $shippitApi
     * @param \Shippit\Shipping\Helper\Data $shippitHelper
     * @param \Magento\Framework\Pricing\Helper\Data $pricingHelper
     * @param TimezoneInterface $timezoneInterface
     * @param CheckoutHelperData $checkoutHelperData
     * @param array $data
     */
    public function __construct(
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService,
        RateRequest $rateRequest,
        \Magento\Quote\Model\Quote\Item $item,
        \Totaltools\Storelocator\Model\Registry $storeRegistry,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper,
        \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Quote\Model\Quote\Address\RateResult\ErrorFactory $rateErrorFactory,
        \Magento\Framework\Logger\Monolog $logger,
        \Magento\Framework\Xml\Security $xmlSecurity,
        \Magento\Shipping\Model\Simplexml\ElementFactory $xmlElFactory,
        \Magento\Shipping\Model\Rate\ResultFactory $rateFactory,
        \Magento\Quote\Model\Quote\Address\RateResult\MethodFactory $rateMethodFactory,
        \Magento\Shipping\Model\Tracking\ResultFactory $trackFactory,
        \Magento\Shipping\Model\Tracking\Result\ErrorFactory $trackErrorFactory,
        \Magento\Shipping\Model\Tracking\Result\StatusFactory $trackStatusFactory,
        \Magento\Directory\Model\RegionFactory $regionFactory,
        \Magento\Directory\Model\CountryFactory $countryFactory,
        \Magento\Directory\Model\CurrencyFactory $currencyFactory,
        \Magento\Directory\Helper\Data $directoryData,
        \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry,
        \Shippit\Shipping\Helper\Carrier\Shippit $helper,
        \Shippit\Shipping\Helper\Api $api,
        \Shippit\Shipping\Helper\Sync\Order\Items $itemsHelper,
        \Shippit\Shipping\Model\Config\Source\Shippit\Shipping\QuoteMethods $methods,
        \Shippit\Shipping\Api\Request\QuoteInterface $quote,
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Catalog\Model\Product\Attribute\Repository $productAttributeRepository,
        Api $shippitApi,
        \Shippit\Shipping\Helper\Data $shippitHelper,
        \Magento\Framework\Pricing\Helper\Data $pricingHelper,
        TimezoneInterface $timezoneInterface,
        CheckoutHelperData $checkoutHelperData,
        array $data = []
    ) {
        $this->_productRepositoryInterface = $productRepository;
        $this->_storeRepository = $storeRepository;
        $this->_jsonResultFactory = $jsonResultFactory;
        $this->rateRequest = $rateRequest;
        $this->item = $item;
        $this->storeRegistry = $storeRegistry;
        $this->checkoutHelper = $checkoutHelper;
        $this->_storeInventoryService = $storeInventoryService;
        $this->_productCollectionFactory = $productCollectionFactory;
        $this->_productAttributeRepository = $productAttributeRepository;
        $this->shippitApi = $shippitApi;
        $this->_helper = $shippitHelper;
        $this->_quote = $quote;
        $this->api = $api;
        $this->scopeConfig = $scopeConfig;
        $this->pricingHelper = $pricingHelper;
        $this->timezoneInterface = $timezoneInterface;
        $this->checkoutHelperData = $checkoutHelperData;

        parent::__construct(
            $scopeConfig,
            $rateErrorFactory,
            $logger,
            $xmlSecurity,
            $xmlElFactory,
            $rateFactory,
            $rateMethodFactory,
            $trackFactory,
            $trackErrorFactory,
            $trackStatusFactory,
            $regionFactory,
            $countryFactory,
            $currencyFactory,
            $directoryData,
            $stockRegistry,
            $helper,
            $api,
            $itemsHelper,
            $methods,
            $quote,
            $productCollectionFactory,
            $productAttributeRepository,
            $data
        );
    }

    

    /**
     * @param $request
     * @return bool|Result
     */
    public function collectShippingRates($request)
    {
        $storeId = $request->getParam('storelocator_id', 0);
        $messages = [];
        $sku = $request->getParam('sku', '');
        $city = $request->getParam('city', '');
        $region = $request->getParam('region', '');
        $country_id = $request->getParam('country_id', '');
        $postcode = $request->getParam('postcode');

        $product = $this->_productRepositoryInterface->get($sku);

        // Shipping Label
        $_shippingLabel = $product->getResource()->getAttribute('shipping_label');
        $_shippingLabelValue = $_shippingLabel->getFrontend()->getValue($product);

        $errorReturn = [0 => ["message" => __("Not available"), "code" => 2]];

        if ($postcode) {
            $store = $this->_storeRepository->getByPostcodeInZone($postcode);
        }
        
        if ($storeId && !$store) {
            // Store should be loaded by storeId only for click & collect. For delivery we will get the first store which can fulfil the complete order.
            $store = $this->_storeRepository->getById($storeId);
        }

        if (!$store) {
            return $errorReturn;
        }
        
        $apiKey = $store->getShippitApiKey();

        if (empty($apiKey) || $apiKey === null) {
            $apiKey = $this->_helper->getApiKey();
        }
      
        $this->item->setProduct($product);
        $this->item->setQty(1);
        $rateRequest = $this->rateRequest;
        $rateRequest->setAllItems([$this->item]);
        $rateRequest->setParcelAttributes($this->_getParcelAttributes($rateRequest));

        // check the products are eligible for shippit shipping
        if (!$this->_canShipProducts($rateRequest)) {
            $this->_logger->addDebug(self::NOTICE_PRODUCTS_NOT_ELIGIBLE);
            return false;
        }

        $quoteRequest = $this->_quote;

        // Get the first available dates based on the customer's shippit profile settings
        $quoteRequest->setOrderDate('');
        $quoteRequest->setDropoffCountryCode($country_id);
        $quoteRequest->setDropoffStreet('');
        $quoteRequest->setDropoffAddress('');
        

        $quoteRequest->setDropoffPostcode($postcode);
        $quoteRequest->setDropoffState(strtoupper($region));
        $quoteRequest->setDropoffSuburb($city);
        $quoteRequest->setParcelAttributes($this->_getParcelAttributes($rateRequest));

        try {
            // Call the api and retrieve the quote
            $shippingQuotes = $this->api->createQuote($quoteRequest);
        }
        catch (\Exception $e) {
            $this->_logger->addError('Quote Request Error - ' . $e->getMessage());

            return false;
        }

        /** @var \Magento\Shipping\Model\Rate\Result $rateResult */
        $rateResult = $this->_rateFactory->create();

        $this->_processShippingQuotes($rateResult, $shippingQuotes);
        $output = 0;
        if (!empty($rateResult)) {
            $deliveryMethodsMessages = $this->getProductPageDeliveryMethodsMessages();
            foreach ($rateResult->getAllRates() as $carrierRates) {
                if (in_array($carrierRates->getMethod(), [self::STANDARD_DELIVERY_METHOD, self::EXPRESS_DELIVERY_METHOD, self::UBER_DELIVERY_METHOD])) {
                    $shipping_price = $this->formatPrice($carrierRates->getPrice());
                    $shipping_method = $carrierRates->getMethod();
                    if ($carrierRates->getMethod() == self::STANDARD_DELIVERY_METHOD && $_shippingLabelValue && preg_match('/free/i', $_shippingLabelValue)) {
                        $shipping_price = 'FREE';
                    }
                    if ($carrierRates->getMethod() == self::UBER_DELIVERY_METHOD) {
                        $customPrice = floatval($this->getUberCustomQuotePrice());
                        if ($customPrice != self::UBER_ONDEMAND_CUSTOM_QUOTE_PRICE) {
                            $shipping_price = $this->formatPrice($customPrice);
                        }
                    }
                    $shipping_name = ($shipping_method == self::STANDARD_DELIVERY_METHOD) ? $deliveryMethodsMessages['standard_title'] : (($shipping_method == self::EXPRESS_DELIVERY_METHOD) ? $deliveryMethodsMessages['express_title'] : (($shipping_method == self::UBER_DELIVERY_METHOD) ? $deliveryMethodsMessages['uber_title'] : ''));
                    $shipping_message = ($shipping_method == self::STANDARD_DELIVERY_METHOD) ? $deliveryMethodsMessages['standard_message'] : (($shipping_method == self::EXPRESS_DELIVERY_METHOD) ? $deliveryMethodsMessages['express_message'] : (($shipping_method == self::UBER_DELIVERY_METHOD) ? $deliveryMethodsMessages['uber_message'] : ''));
                    $isAllowedDeliveryMethod = $this->isAllowedDeliveryMethod($shipping_method, $product, $store);
                    if ($isAllowedDeliveryMethod) {
                        $messages[] = [
                            'shipping_name'    =>  $shipping_name,
                            'shipping_price'   => $shipping_price,
                            'shipping_class'   => strtolower($shipping_method),
                            'product_price'    => $product->getPriceInfo()->getPrice('final_price')->getValue(),
                            'shipping_message' => $this->getShippingMessage($shipping_message, $isAllowedDeliveryMethod, $deliveryMethodsMessages)
                        ];
                    }
                }
            }

            if ($messages) {
                $messages = $this->getSortedShippingMethods($messages, $deliveryMethodsMessages);
            }
        }
        return $messages;
    }

    public function getProductPageDeliveryMethodsMessages()
    {
        $standard_title = $this->scopeConfig->getValue('delivery/title/standard', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $express_title = $this->scopeConfig->getValue('delivery/title/express', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $uber_title = $this->scopeConfig->getValue('delivery/title/uber', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $standard_message = $this->scopeConfig->getValue('delivery/message/standard', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $express_message = $this->scopeConfig->getValue('delivery/message/express', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $uber_message = $this->scopeConfig->getValue('delivery/message/uber', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $preorder_message = $this->scopeConfig->getValue('delivery/message/preorder', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $backorderable_normal_product_out_of_stock = $this->scopeConfig->getValue('delivery/message/backorderable_normal_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $special_order_product_out_of_stock = $this->scopeConfig->getValue('delivery/message/special_order_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $backorderable_eb_product_out_of_stock = $this->scopeConfig->getValue('delivery/message/backorderable_eb_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        $not_backorderable_product_out_of_stock = $this->scopeConfig->getValue('delivery/message/not_backorderable_product_out_of_stock', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);

        $deliveryMethodsMessages = [
            'standard_title'    => $standard_title,
            'express_title'     => $express_title,
            'uber_title'        => $uber_title,
            'standard_message'  => $standard_message,
            'express_message'   => $express_message,
            'uber_message'      => $uber_message,
            'preorder_message'      => $preorder_message,
            'backorderable_normal_product_out_of_stock' => $backorderable_normal_product_out_of_stock,
            'special_order_product_out_of_stock' => $special_order_product_out_of_stock,
            'backorderable_eb_product_out_of_stock' => $backorderable_eb_product_out_of_stock,
            'not_backorderable_product_out_of_stock' => $not_backorderable_product_out_of_stock
        ];
        return $deliveryMethodsMessages;
    }

    private function getShippingMessage($shipping_message, $isAllowedDeliveryMethod, $deliveryMethodsMessages)
    {
        if ($isAllowedDeliveryMethod === Store::PRE_ORDER_KEY) {
            return $deliveryMethodsMessages['preorder_message'];
        } elseif ($isAllowedDeliveryMethod === Store::IS_SPECIAL_ORDER) {
            return $deliveryMethodsMessages['special_order_product_out_of_stock'];
        } elseif ($isAllowedDeliveryMethod === Store::EXCLUSIVE_PRODUCT_KEY) {
            return $deliveryMethodsMessages['backorderable_eb_product_out_of_stock'];
        } elseif ($isAllowedDeliveryMethod === 'N-EB') {
            return $deliveryMethodsMessages['backorderable_normal_product_out_of_stock'];
        } else {
            return $shipping_message;
        }
    }

    /**
     * @param $messages
     * @param $deliveryMethodsMessages
     * @return array
     */
    private function getSortedShippingMethods($messages, $deliveryMethodsMessages)
    {
        $sorted = [];
        foreach ($messages as $message) {
            if ($message['shipping_name'] == $deliveryMethodsMessages['uber_title']) {
                $sorted[0] = $message;
            }
            if ($message['shipping_name'] == $deliveryMethodsMessages['standard_title']) {
                $sorted[1] = $message;
            }
            if ($message['shipping_name'] == $deliveryMethodsMessages['express_title']) {
                $sorted[2] = $message;
            }
        }
        ksort($sorted);
        $sorted = array_values($sorted);
        return $sorted;
    }

    private function isAllowedDeliveryMethod($shipping_method, $product, $store)
    {
        $isDangerousItem = (bool) $product->getData(Store::SHIPPING_DANGEROUS_KEY);
        $isPreOrderItem = (bool) $product->getData(Store::PRE_ORDER_KEY);
        $isSpecialOrder = (bool) $product->getData(Store::IS_SPECIAL_ORDER);
        $isEB = (bool) $product->getData(Store::EXCLUSIVE_PRODUCT_KEY);
        $isBackorderable = (bool) $product->getData(Store::IS_BACKORDERABLE);
        $isKnifeCompliant = (bool) $product->getData(Store::KNIFE_COMPLIANCE_KEY);

        // For knife compliant products, we'll handle them differently
        // We'll return true for click and collect in the collectShippingRates method
        if ($isKnifeCompliant) {
            // Only allow standard delivery for knife compliant products
            // Click and collect will be handled separately
            return false;
        }

        if ($isDangerousItem) {
            return false;
        }

        if ($isPreOrderItem) {
            if ($shipping_method == self::STANDARD_DELIVERY_METHOD) {
                return Store::PRE_ORDER_KEY;
            } else {
                return false;
            }
        }

        if (in_array($shipping_method, [self::EXPRESS_DELIVERY_METHOD, self::UBER_DELIVERY_METHOD]) && $this->checkoutHelperData->isB2BCustomer()) {
            return false;
        }

        $sku = $product->getSku();
        $qty = 1;

        if ($shipping_method == self::UBER_DELIVERY_METHOD) {
            if ($this->productPriceIsGreaterThanMaxAllowed($product)) {
                return false;
            }

            if ($this->productPriceIsLessThanMinAllowed($product)) {
                return false;
            }
            
            if (!$store || !$store->getAllowUberOrders()) {
                return false;
            }

            if ($this->uberOnDemandNotAllowed($product, $store)) {
                return false;
            }

            $surplusQtyBuffer = $this->getUberSurplusQtyBuffer();
            $qty = $qty + $surplusQtyBuffer;
        }

        if ($shipping_method == self::EXPRESS_DELIVERY_METHOD) {
            $surplusQtyBuffer = $this->getExpressSurplusQtyBuffer();
            $qty = $qty + $surplusQtyBuffer;
            $requiredProductQuantities[$sku] = $qty;
            $store = $this->_storeInventoryService->getStoreWhichShouldFulfillTheOrder($store, $requiredProductQuantities);
        }

        if ($shipping_method == self::STANDARD_DELIVERY_METHOD) {
            $requiredProductQuantities[$sku] = $qty;
            $store = $this->_storeInventoryService->getStoreWhichShouldFulfillTheOrder($store, $requiredProductQuantities);
        }

        $stockExist = $this->_storeInventoryService->checkStoreInventoryForSku($store, $sku, $qty);

        if (!$stockExist && $shipping_method == self::STANDARD_DELIVERY_METHOD) {

            if (!$isBackorderable) {
                return false;
            } elseif ($isSpecialOrder) {
                return Store::IS_SPECIAL_ORDER;
            } elseif ($isEB) {
                return Store::EXCLUSIVE_PRODUCT_KEY;
            } elseif (!$isEB) {
                return "N-EB";
            } else {
                return false;
            }
        }

        if (!$stockExist) {
            return false;
        }

        return true;
    }

    /**
     * @return mixed
     */
    public function getUberSurplusQtyBuffer()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_surplus_qty_buffer', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getExpressSurplusQtyBuffer()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/express_surplus_qty_buffer', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * Format price
     * @param  float $price
     * @return string
     */
    public function formatPrice($price)
    {
        return $this->pricingHelper->currency($price, true, false);
    }

    /**
     * @return int
     */
    public function getUberMaxAllowedWeight()
    {
        return (int) $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_ondemand_max_weight', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return int
     */
    public function getUberMaxAllowedDimension()
    {
        return (int) $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_ondemand_max_dimension', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getUberCustomQuotePrice()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_ondemand_custom_quote_price', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * Checks if Uber on-demand is not allowed for a given product.
     *
     * @param $product The product to check.
     * @param $store
     * @return bool Returns true if Uber on-demand is not allowed, false otherwise.
     */
    public function uberOnDemandNotAllowed($product, $store)
    {
        $uberNotAllowed = false;
        if ($product->getWeight() !== null && floatval($product->getWeight()) > $this->getUberMaxAllowedWeight()) {
            $uberNotAllowed = true;
        }
        if ($product->getData('shipping_height') !== null && floatval($product->getData('shipping_height')) > $this->getUberMaxAllowedDimension()) {
            $uberNotAllowed = true;
        }
        if ($product->getData('shipping_length') !== null && floatval($product->getData('shipping_length')) > $this->getUberMaxAllowedDimension()) {
            $uberNotAllowed = true;
        }
        if ($product->getData('shipping_width') !== null && floatval($product->getData('shipping_width')) > $this->getUberMaxAllowedDimension()) {
            $uberNotAllowed = true;
        }

        if ($this->excludeUberBasedOnCurrentHourAndDay($store)) {
            $uberNotAllowed = true;
        }

        return $uberNotAllowed;
    }

    /**
     * @param $store
     * @return bool
     */
    private function excludeUberBasedOnCurrentHourAndDay($store)
    {
        $uberDays =  $this->getStoreUberAvailableDays($store->getUberAvailableDays());
        if (!in_array(date('w'), $uberDays)) {
            return true;
        }
        if (!is_numeric($store->getUberStartAt()) || !is_numeric($store->getUberEndAt())) {
            return true;
        }
        $currentDateTime = $this->timezoneInterface->date();
        $currentHour = (int)$currentDateTime->format('G');
        $uberTimeSlotStartAt = (int)$store->getUberStartAt();
        $uberTimeSlotEndAt = (int)$store->getUberEndAt();
        if ($currentHour >= $uberTimeSlotStartAt && $currentHour <= $uberTimeSlotEndAt) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @return mixed
     */
    public function getUberTimeSlotStartAt()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/hour_of_day_uber_start_at', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }
    /**
     * @return mixed
     */
    public function getUberTimeSlotEndAt()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/hour_of_day_uber_end_at', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return array
     */
    public function getUberAvailableDays()
    {
        $uberDays = [];
        $uberConfigDays = $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_available_days', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        if (!empty($uberConfigDays)) {
            $uberDays = explode(",", $uberConfigDays);
        }
        return $uberDays;
    }

    /**
     * @param $uberStoreDays
     * @return array
     */
    public function getStoreUberAvailableDays($uberStoreDays)
    {
        $uberDays = [];
        if (!empty($uberStoreDays)) {
            $uberDays = explode(",", $uberStoreDays);
        }

        return $uberDays;
    }

    /**
     * @param $product
     * @return bool
     */
    public function productPriceIsGreaterThanMaxAllowed($product)
    {
        $maxAllowedUberOrderTotal = $this->scopeConfig->getValue('checkout/uber_ondemand_settings/max_order_value', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);

        if ((float)$product->getPriceInfo()->getPrice('final_price')->getValue() > (float)$maxAllowedUberOrderTotal) {
            return true;
        }

        return false;
    }

    /**
     * @param $product
     * @return bool
     */
    public function productPriceIsLessThanMinAllowed($product)
    {
        $minAllowedUberOrderTotal = $this->scopeConfig->getValue('checkout/uber_ondemand_settings/min_order_value', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);

        if ((float)$product->getPriceInfo()->getPrice('final_price')->getValue() < (float)$minAllowedUberOrderTotal) {
            return true;
        }

        return false;
    }
}
