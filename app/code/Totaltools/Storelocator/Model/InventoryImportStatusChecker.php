<?php
/**
 * Total Tools Store Locator.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Storelocator\Model;

use Exception;
use Totaltools\Storelocator\Model\ImportInventory\Email;
use Totaltools\Storelocator\Model\Inventoryimportreport;
use Totaltools\Storelocator\Model\InventoryimportreportRepository;
use Psr\Log\LoggerInterface;
use Zend_Db_Exception;
use Totaltools\Storelocator\Parser\File;

/**
 * @api
 */
class InventoryImportStatusChecker
{
    /**
     * @var InventoryimportreportRepository
     */
    private $inventoryImportReportRepository;

    /**
     * @var Email
     */
    private $transport;

    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * @var File
     */
    private $fileParser;

    /**
     * InventoryProcessor constructor.
     * @param InventoryimportreportRepository $inventoryImportReportRepository
     * @param Email $email
     * @param LoggerInterface $logger
     * @param File $fileParser
     */
    public function __construct(
        InventoryimportreportRepository $inventoryImportReportRepository,
        Email $email,
        LoggerInterface $logger,
        File $fileParser
    ) {
        $this->inventoryImportReportRepository = $inventoryImportReportRepository;
        $this->transport = $email;
        $this->logger = $logger;
        $this->fileParser = $fileParser;
    }

    /**
     * Inventory Import Status Checker.
     *
     * @return $this
     * @throws Zend_Db_Exception
     * @throws Exception
     */
    public function execute()
    {
        //load using time restriction
        $importCollection = $this->inventoryImportReportRepository->getLastFullImport(true);
        if(count($importCollection) === 0) {
            //we have no run data, so there's an issue. Load again without the time filter
            $importCollection = $this->inventoryImportReportRepository->getLastFullImport();
            /** @var Inventoryimportreport $import */
            if($import = $importCollection->getFirstItem()) {
                $minutes = $this->inventoryImportReportRepository->getNotificationBoundaryInMinutes();
                $message = 'ERROR: last full import has not run for '.$minutes.' minutes.<br><br>Last import:<br>';
                ($import->getComment() !== null) ? $message .= $import->getComment() : $message .= 'No notes on last import';
                $this->transport->sendFullImportErrorEmail($import, $message);
                if ($this->fileParser->isDebug()) {
                    $this->logger->info($message);
                }
            }
        }

        return $this;
    }
}
