<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model\System\Config\Source;

/**
 * Class Unit.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
abstract class Unit extends \Totaltools\Storelocator\Model\System\Config\Source
{
    /**
     * The array of options in the configuration item.
     *
     * This array's keys are the values used in the database etc. and the
     * values of this array are used as labels on the frontend.
     *
     * @var array
     */
    protected $_brief_options;

    /**
     * Unit constructor.
     */
    public function __construct()
    {
        parent::__construct();
        $this->_setupBriefOptions();
    }

    /**
     * Sets up the $_brief_options array with the correct values.
     *
     * This function is called in the constructor.
     */
    protected abstract function _setupBriefOptions();
    
    /**
     * Looks up an option by key and gets the label.
     *
     * @param string $value
     *
     * @return string
     */
    public function getBriefOptionLabel($value)
    {
        if (array_key_exists($value, $this->_brief_options)) {
            return $this->_brief_options[$value];
        }

        return null;
    }

    /**
     * @return array
     */
    public function toBriefOptionArray()
    {
        return $this->_toOptionArray($this->_brief_options);
    }

    /**
     * @param $value
     *
     * @return false|int|string
     */
    public function getOptionValue($value)
    {
        return array_search($value, array_flip($this->_brief_options));
    }
}
