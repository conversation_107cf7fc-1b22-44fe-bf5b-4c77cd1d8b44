<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model\System\Config\Source;

/**
 * Class Pricing.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Pricing extends \Totaltools\Storelocator\Model\System\Config\Source
{
    /**
     * {@inheritdoc}
     *
     * @codeCoverageIgnore
     */
    const FREE                         = 'free';
    const FLAT_RATE                    = 'flat';
    const DYNAMIC                      = 'dynamic';
    const DYNAMIC_FASTEST              = 'dynamicfast';
    const DYNAMIC_CHEAPEST             = 'dynamiccheap';
    const DYNAMIC_FASTEST_AND_CHEAPEST = 'dynamicfastcheap';

    /**
     * Init options
     */
    protected function _setupOptions()
    {
        $this->_options = [
            self::FREE                         => __('Free Shipping'),
            self::FLAT_RATE                    => __('Fixed Price / Flat Rate'),
            self::DYNAMIC                      => __('Dynamic Pricing (All)'),
            self::DYNAMIC_CHEAPEST             => __('Dynamic Pricing (Cheapest only)'),
            self::DYNAMIC_FASTEST              => __('Dynamic Pricing (Fastest only)'),
            self::DYNAMIC_FASTEST_AND_CHEAPEST => __('Dynamic Pricing (Cheapest and Fastest only)'),
        ];
    }
}
