<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Model\System\Config\Source\Unit;

/**
 * Class Weight.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Weight extends \Totaltools\Storelocator\Model\System\Config\Source\Unit
{
    /**
     * {@inheritdoc}
     *
     * @codeCoverageIgnore
     */
    const GRAMS     = 'Grams';
    const KILOGRAMS = 'Kilograms';
    const OUNCES    = 'Ounces';
    const POUNDS    = 'Pounds';

    /**
     * Init options
     */
    protected function _setupOptions()
    {
        $this->_options = [
            self::GRAMS     => 'Grams',
            self::KILOGRAMS => 'Kilograms',
            self::OUNCES    => 'Ounces',
            self::POUNDS    => 'Pounds',
        ];
    }

    /**
     * Init brief options
     */
    protected function _setupBriefOptions()
    {
        $this->_brief_options = [
            self::GRAMS     => 'g',
            self::KILOGRAMS => 'kg',
            self::OUNCES    => 'oz.',
            self::POUNDS    => 'lb.',
        ];
    }
}
