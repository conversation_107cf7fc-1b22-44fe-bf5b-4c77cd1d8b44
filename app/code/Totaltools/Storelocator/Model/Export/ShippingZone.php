<?php


namespace Totaltools\Storelocator\Model\Export;

use Magento\ImportExport\Model\Export\Adapter\Csv;
use Totaltools\Storelocator\Model\ResourceModel\Zone\Collection;
use Totaltools\Storelocator\Model\ResourceModel\Zone\CollectionFactory;
use Totaltools\Storelocator\Model\Zone;

class ShippingZone
{
    /**
     * Permanent column names
     */
    const COL_NAME = 'name';
    const COL_ZIPCODE = 'code';

    /**
     * Product collection
     *
     * @var CollectionFactory
     */
    protected $_entityCollectionFactory;

    /**
     * @var Collection
     */
    protected $_entityCollection;

    /** @var Csv  */
    protected $writer;

    /**
     * Items per page for collection limitation
     *
     * @var int|null
     */
    protected $_itemsPerPage = null;

    /**
     * ShippingZone constructor.
     * @param Csv $writer
     * @param CollectionFactory $_entityCollectionFactory
     */
    public function __construct(
        Csv $writer,
        CollectionFactory $_entityCollectionFactory
    ) {
        $this->writer = $writer;
        $this->_entityCollectionFactory = $_entityCollectionFactory;
    }

    /**
     * @return string
     * @throws \Exception
     */
    public function export()
    {
        //Execution time may be very long
        set_time_limit(0);
        $page = 0;
        while (true) {
            ++$page;
            $entityCollection = $this->_getEntityCollection(true);
            //$this->_prepareEntityCollection($entityCollection);
            $this->paginateCollection($page, $this->getItemsPerPage());
            if ($entityCollection->count() == 0) {
                break;
            }
            $exportData = $this->getExportData();
            foreach ($exportData as $dataRow) {
                $this->writer->writeRow($dataRow);
            }
            if ($entityCollection->getCurPage() >= $entityCollection->getLastPageNumber()) {
                break;
            }
        }
        return $this->writer->getContents();
    }

    /**
     * Get items per page
     *
     * @return int
     */
    protected function getItemsPerPage()
    {
        if ($this->_itemsPerPage === null) {
            $memoryLimitConfigValue = trim(ini_get('memory_limit'));
            $lastMemoryLimitLetter = strtolower($memoryLimitConfigValue[strlen($memoryLimitConfigValue) - 1]);
            $memoryLimit = (int) $memoryLimitConfigValue;
            switch ($lastMemoryLimitLetter) {
                case 'g':
                case 'm':
                case 'k':
                    $memoryLimit *= 1024;
                    break;
                default:
                    // minimum memory required by Magento
                    $memoryLimit = 250000000;
            }

            // Tested one product to have up to such size
            $memoryPerProduct = 500000;
            // Decrease memory limit to have supply
            $memoryUsagePercent = 0.8;
            // Minimum Products limit
            $minProductsLimit = 500;
            // Maximal Products limit
            $maxProductsLimit = 5000;

            $this->_itemsPerPage = (int)
                ($memoryLimit * $memoryUsagePercent - memory_get_usage(true)) / $memoryPerProduct;
            if ($this->_itemsPerPage < $minProductsLimit) {
                $this->_itemsPerPage = $minProductsLimit;
            }
            if ($this->_itemsPerPage > $maxProductsLimit) {
                $this->_itemsPerPage = $maxProductsLimit;
            }
        }
        return $this->_itemsPerPage;
    }

    /**
     * Set page and page size to collection
     *
     * @param int $page
     * @param int $pageSize
     * @return void
     */
    protected function paginateCollection($page, $pageSize)
    {
        $this->_getEntityCollection()->setCurPage($page)->setPageSize($pageSize);
    }

    /**
     * @param bool $resetCollection
     * @return Collection
     */
    protected function _getEntityCollection($resetCollection = false)
    {
        if ($resetCollection || empty($this->_entityCollection)) {
            $this->_entityCollection = $this->_entityCollectionFactory->create();
        }
        return $this->_entityCollection;
    }

    /**
     * @return array
     */
    protected function getExportData()
    {
        $exportData = [];
        try {
            /** @var Zone $zone */
            foreach ($this->_getEntityCollection() as $zone) {
                foreach (explode(',', $zone->getRanges() ?? '') as $range) {
                    $exportData[] = [
                        self::COL_NAME => $zone->getName(),
                        self::COL_ZIPCODE => $range,
                    ];
                }
            }

        } catch (\Exception $e) {
            return [];
        }
        return $exportData;
    }
}
