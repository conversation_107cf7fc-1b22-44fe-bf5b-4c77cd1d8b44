<?php

namespace Totaltools\Storelocator\Plugin\Model;

/**
 * Class SystemConfig
 * @package Totaltools\Storelocator\Plugin\Model
 */
class SystemConfig
{
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    private $request;

    /**
     * @var \Magestore\Storelocator\Model\StoreFactory
     */
    private $store;

    /**
     * SystemConfig constructor.
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Magestore\Storelocator\Model\StoreFactory $store
     */
    public function __construct(
        \Magento\Framework\App\RequestInterface $request,
        \Magestore\Storelocator\Model\StoreFactory $store
    ) {
        $this->request = $request;
        $this->store = $store;
    }

    /**
     *
     * @param \Magestore\Storelocator\Model\SystemConfig $subject
     * @param $result
     * @return bool
     */
    public function afterIsEnableFrontend(
        \Magestore\Storelocator\Model\SystemConfig $subject,
        $result
    ) {
        $storelocatorId = $this->request->getParam('storelocator_id');
        $storeVisisble = true;
        if ($storelocatorId) {
            /** @var \Magestore\Storelocator\Model\Store $store */
            $store = $this->store->create()->load($storelocatorId);
            if ((int)$store->getIsVisible() === 0) {
                $storeVisisble = false;
            }
        }

        return $result && $storeVisisble;
    }
}
