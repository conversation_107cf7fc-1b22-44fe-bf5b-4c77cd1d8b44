<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  total-tools
 * @package   total-tools_OrderService
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2020 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Plugin\Model\Service;

use Magento\Framework\Event\ObserverInterface;
use Magento\Quote\Model\Quote\AddressFactory;
use Magento\Sales\Model\Order;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Debug;
use Psr\Log\LoggerInterface;
use Totaltools\Checkout\Model\CheckoutApiModel;
use Totaltools\Storelocator\Helper\Checkout\Data;
use Totaltools\Storelocator\Helper\Data as Helper;
use Totaltools\Storelocator\Model\ShippingZoneRepository;
use Totaltools\Storelocator\Model\Store;
use Totaltools\Storelocator\Model\StoreAdminEmail;
use Totaltools\Storelocator\Model\StoreInventoryService;
use Totaltools\Storelocator\Model\StoreRepository;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Company\Model\CustomerFactory;
use Magento\Company\Model\ResourceModel\Customer;
use Magento\Quote\Model\QuoteRepository;

class OrderService
{
    /**
     * @var StoreRepository
     */
    private StoreRepository $_storeRepository;

    /**
     * @var Data
     */
    protected Data $_checkoutHelper;

    /**
     * @var ShippingZoneRepository
     */
    protected ShippingZoneRepository $_shippingZoneRepository;

    /**
     * @var AddressFactory
     */
    private AddressFactory $_quoteAddress;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $_logger;

    /**
     * @var CheckoutApiModel
     */
    private CheckoutApiModel $_checkoutApiModel;

    /**
     * @var StoreInventoryService
     */
    private StoreInventoryService $_storeInventoryService;

    /**
     * @var array
     */
    protected static array $_requiredProducts = [];

    /**
     * @var array
     */
    protected static array $_orderedProducts = [];

    /**
     * @var Helper
     */
    private Helper $_storelocatorHepler;

    /**
     * @var CustomerRepositoryInterface
     */
    private CustomerRepositoryInterface $_customerRepository;

    /**
     * @var CustomerFactory
     */
    private CustomerFactory $companyCustomerFactory;
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;

    /**
     * @var QuoteRepository
     */
    private QuoteRepository $quoteRepository;

    /**
     * SaveOrderExternalAttributesObserver constructor.
     * @param StoreRepository $storeRepository
     * @param Data $checkoutHelper
     * @param Helper $storeLocatorHelper
     * @param StoreAdminEmail $storeAdminEmail
     * @param ShippingZoneRepository $shippingZoneRepository
     * @param AddressFactory $quoteAddressFactory
     * @param CheckoutApiModel $checkoutApiModel
     * @param StoreInventoryService $storeInventoryService
     * @param CustomerRepositoryInterface $customerRepository
     * @param CustomerFactory $companyCustomerFactory
     * @param QuoteRepository $quoteRepository
     */
    public function __construct(
        StoreRepository        $storeRepository,
        Data                   $checkoutHelper,
        Helper                 $storeLocatorHelper,
        ShippingZoneRepository $shippingZoneRepository,
        AddressFactory         $quoteAddressFactory,
        LoggerInterface        $logger,
        CheckoutApiModel       $checkoutApiModel,
        StoreInventoryService  $storeInventoryService,
        CustomerRepositoryInterface $customerRepository,
        CustomerFactory $companyCustomerFactory,
        Customer  $companyCustomer,
        QuoteRepository $quoteRepository
    )
    {
        $this->_storeRepository = $storeRepository;
        $this->_checkoutHelper = $checkoutHelper;
        $this->_storelocatorHepler = $storeLocatorHelper;
        $this->_shippingZoneRepository = $shippingZoneRepository;
        $this->_quoteAddress = $quoteAddressFactory;
        $this->_logger = $logger;
        $this->_checkoutApiModel = $checkoutApiModel;
        $this->_storeInventoryService = $storeInventoryService;
        $this->_customerRepository = $customerRepository;
        $this->companyCustomerFactory = $companyCustomerFactory;
        $this->companyCustomer = $companyCustomer;
        $this->quoteRepository = $quoteRepository;
    }

    /**
     * After place plugin
     *
     * @param \Magento\Sales\Model\Service\OrderService $context
     * @param \Magento\Sales\Model\Order $order
     * @return \Magento\Sales\Model\Order
     */
    public function afterPlace(\Magento\Sales\Model\Service\OrderService $context, \Magento\Sales\Model\Order $order)
    {
        return $this->assignStore($order);
    }

    public function assignStore($order)
    {
        $isVirtualOrder = $order->getIsVirtual();
        if ($isVirtualOrder) {
            // No allocation logic required for virtual order. Assign the order to TTO and return.
            $this->_logger->info("ORDER SERVICE: " . $order->getIncrementId() . ' is Gift Card Order');
            $this->_setAdditionalAttributesToOrder($order, 0, false);
            return $order;
        }

        $shippingAddress = $order->getShippingAddress();
        $billingAddress = $order->getBillingAddress();
        $postcode = $shippingAddress ? $shippingAddress->getPostcode() : "";

        if (!$shippingAddress || !$billingAddress || ($isVirtualOrder && !$postcode) || str_contains(strtolower($shippingAddress->getFirstname() ?? ""), 'store') || str_contains(strtolower($billingAddress->getFirstname() ?? ""), 'store')) {
            $this->_logger->info('ORDER SERVICE: FAULT: Order with potentially corrupt address #' . $order->getIncrementId(), ['shippingAddress' => $shippingAddress, 'billingAddress' => $billingAddress, 'postcode' => $postcode]);
        }

        $storeId = 0;
        $shippingMethod = $order->getShippingMethod();

        $items = $order->getAllVisibleItems();
        $expressSurplusQtyBuffer = $this->_storelocatorHepler->getExpressSurplusQtyBuffer();

        foreach ($items as $item) {
            self::$_requiredProducts[$item->getSku()] = (str_contains(strtolower($shippingMethod), 'express') || str_contains(strtolower($shippingMethod), 'priority')) ? $item->getQtyOrdered() + (int)$expressSurplusQtyBuffer : $item->getQtyOrdered();
            self::$_orderedProducts[$item->getSku()] = $item->getQtyOrdered();
        }

        $this->_logger->info('ORDER SERVICE: Shipping & Items Data:', [
            'shippingMethod' => $shippingMethod,
            'shippingAddress' => $shippingAddress ? $shippingAddress->getData() : 'null',
            'postcode' => $postcode
        ]);

        if (!empty($shippingMethod) && !empty($postcode)) {

            if ($shippingMethod === 'shippitcc_shippitcc') {
                // First try: Get from shipping address
                if ((int) $shippingAddress->getStorelocatorId() > 0) {
                    $storeId = (int) $shippingAddress->getStorelocatorId();
                    $this->_logger->info('ORDER SERVICE: Assigning store based on click and collect -> address store. Order: ' . $order->getIncrementId());
                } 
                // Second try: Get from order extension attributes
                elseif ($order->getExtensionAttributes() && $order->getExtensionAttributes()->getStorelocatorId()) {
                    $storeId = (int) $order->getExtensionAttributes()->getStorelocatorId();
                    $this->_logger->info('ORDER SERVICE: Assigning store based on click and collect -> order extension attributes. Order: ' . $order->getIncrementId());
                }
                // Third try: Get from session
                elseif ($this->_checkoutHelper->getSessionStoreId()) {
                    $storeId = $this->_checkoutHelper->getSessionStoreId();
                    $this->_logger->info('ORDER SERVICE: Assigning store based on click and collect -> session. Order: ' . $order->getIncrementId());
                }
                // Fourth try: Check custom order attribute if extension attributes failed
                elseif ($order->getStorelocatorId()) {
                    $storeId = (int) $order->getStorelocatorId();
                    $this->_logger->info('ORDER SERVICE: Assigning store based on click and collect -> order attribute. Order: ' . $order->getIncrementId());
                }
                // Fifth try: Check quote data if available
                elseif ($order->getQuoteId()) {
                    try {
                        $quote = $this->quoteRepository->get($order->getQuoteId());
                        if ($quote->getShippingAddress() && (int)$quote->getShippingAddress()->getStorelocatorId() > 0) {
                            $storeId = (int)$quote->getShippingAddress()->getStorelocatorId();
                            $this->_logger->info('ORDER SERVICE: Assigning store based on click and collect -> quote shipping address. Order: ' . $order->getIncrementId());
                        }
                    } catch (\Exception $e) {
                        $this->_logger->error('ORDER SERVICE: Failed to retrieve quote for order: ' . $order->getIncrementId() . ' Error: ' . $e->getMessage());
                    }
                }
                // Last resort: Use postcode to find nearest store
                else {
                    $this->_logger->warning('ORDER SERVICE: FAULT: Click & Collect order missing store ID, using postcode fallback for order: ' . $order->getIncrementId());
                    $store = $this->_getStoreByPostcode($postcode);
                    if ($store) {
                        $storeId = $store->getId();
                        
                        $this->_logger->info('ORDER SERVICE: Assigning store based on click and collect -> postcode.', [
                            'order_id' => $order->getIncrementId(),
                            'postcode' => $postcode,
                            'store_id' => $storeId,
                            'store_name' => $store->getStoreName()
                        ]);
                    } else {                        
                        // Additional information to help diagnose the issue
                        $this->_logger->info('ORDER SERVICE: DIAGNOSTIC: Click & Collect missing store ID details', [
                            'order_id' => $order->getIncrementId(),
                            'customer_id' => $order->getCustomerId(),
                            'shipping_method' => $shippingMethod,
                            'postcode' => $postcode,
                            'session_store_id' => $this->_checkoutHelper->getSessionStoreId(),
                            'shipping_address_store_id' => $shippingAddress->getStorelocatorId(),
                            'order_store_id' => $order->getStorelocatorId()
                        ]);
                        
                    }
                }
            } else {
                /**
                 * Re-run the allocation logic to resolve bugs related to session and stock issues.
                 */
                $allocatedStore = $this->getAllocatedStore($order, $postcode);

                if ($allocatedStore) {
                    $storeId = $allocatedStore->getId();
                    $this->_logger->info('ORDER SERVICE: Assigning store based on delivery logic -> re-calculation. Order: ' . $order->getIncrementId());
                } elseif ($this->_checkoutHelper->getSessionStoreId()) {
                    $storeId = $this->_checkoutHelper->getSessionStoreId();
                    $this->_logger->info('ORDER SERVICE: FAULT: Assigning store based on delivery logic -> session. Order: ' . $order->getIncrementId());
                } else {
                    $this->_logger->info('ORDER SERVICE: FAULT: Assigning store based on default postcode owner for order: ' . $order->getIncrementId());
                    $store = $this->_getStoreByPostcode($postcode);
                    if ($store) {
                        $storeId = $store->getId();
                    }
                }
            }

        } else {
            $this->_logger->info('ORDER SERVICE: FAULT: Store allocation logic was not applied for order:' . $order->getIncrementId());
        }

        $this->_setAdditionalAttributesToOrder($order, $storeId, $shippingAddress);
        
        return $order;
    }

    private function getAllocatedStore($order, $postcode)
    {

        // Set store to default
        $allocatedStore = $this->_storeRepository->getByPostcodeInZone(\Totaltools\Storelocator\Model\Zone::DEFAULT_ZONE_POSTCODE);
        // Get the postcode owner
        $postcodeOwnerStore = $this->_storeRepository->getByPostcodeInZone($postcode);

        $shippingMethod = $order->getShippingMethod();

        // For B2B Customers and Uber orders the postcode owner store will always fulfill the order
        if ($this->isB2BCustomer($order->getCustomerId()) || $shippingMethod === 'shippit_ondemand') {
            return $postcodeOwnerStore ? $postcodeOwnerStore : $allocatedStore;
        }

        // If postcode owner found then run allocation lock based on stock
        if ($postcodeOwnerStore) {
            $allocatedStore = $this->allocateStoreByStockAvailability($postcodeOwnerStore);
        }

        return $allocatedStore;
    }

    /**
     * @param $customerId
     * @return bool
     */
    protected function isB2BCustomer($customerId): bool
    {
        if ($customerId) {
            $customer = $this->_customerRepository->getById($customerId);
        } else {
            $customer = null;
        }

        if ($customer) {
            $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId($customer->getId());
            $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
            $b2bCustomer =  array_diff($currentCompanyIds, [0]);
            if (!empty($b2bCustomer)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param Store $postcodeOwnerStore
     * @return bool|Store
     */
    protected function allocateStoreByStockAvailability(Store $postcodeOwnerStore)
    {
        // Check that postcode owner store allows delivery type orders
        $allowedDelivery = $postcodeOwnerStore->getAllowDeliveryOrders();
        if ($allowedDelivery == 1) {

            $hasInventory = $this->_storeInventoryService->checkStoreInventory($postcodeOwnerStore, self::$_requiredProducts);
            if ($hasInventory) {
                return $postcodeOwnerStore;
            }
        }

        $fallbackStoresCollection = $this->_storeRepository->getFallbackStores($postcodeOwnerStore->getId());
        foreach ($fallbackStoresCollection->getItems() as $fallbackStore) {
            /** @var Store $fallbackStore */

            // Check that this fallback store allows delivery type orders
            $allowedDelivery = $fallbackStore->getAllowDeliveryOrders();
            if ($allowedDelivery == 1) {

                $hasInventory = $this->_storeInventoryService->checkStoreInventory($fallbackStore, self::$_requiredProducts);
                if ($hasInventory) {
                    return $fallbackStore;
                }
            }


        }

        $superStoreId = $postcodeOwnerStore->getSuperStoreId();
        try {
            $superStore = $this->_storeRepository->getById($superStoreId);
            if ($superStore) {
                return $superStore;
            }
        } catch (NoSuchEntityException $e) {
            $this->_logger->info('ORDER SERVICE: FAULT: Unable to find store with id:' . $superStoreId);
        }


        // Return the first store if non of the fallback stores have stock
        return $postcodeOwnerStore;


    }

    /**
     * Set additional attributes to order.
     *
     * @param Order $order
     * @param int $storeId
     * @param storeAddress $shippingAddress
     *
     * @return void
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function _setAdditionalAttributesToOrder(Order $order, $storeId, $shippingAddress = false)
    {

        $this->_logger->info('ORDER SERVICE: HIT _setAdditionalAttributesToOrder to update store details for order ' . $order->getIncrementId() . ' store id-' . $storeId);

        /* If order contains a virtual item, then do not create shipment or pickup */
        if ($storeId === 0) {
            $giftcardWarehouseCode = $this->_storelocatorHepler->getGiftcardWarehouseCode();
            $giftcardStoreCode = $this->_storelocatorHepler->getGiftcardStoreCode();
            $order->setWarehouse($giftcardWarehouseCode);
            $order->setStore($giftcardStoreCode);
            $order->addStatusHistoryComment('Assigned Store: 0 ( Support Office Store )');
            $order->save();
        }

        if (!empty($storeId)) {
            try {

                /** @var Store $store */
                $store = $this->_storeRepository->getById($storeId);
                /** @var \Magento\Sales\Model\Order $order */
                $order->setWarehouse($store->getErpId());
                $order->setStore($store->getErpCode());
                $order->setStorelocatorId($store->getId());
                $this->_logger->info('ORDER SERVICE: Store detail updated for order: ' . $order->getIncrementId());
                $order->addStatusHistoryComment('Assigned Store: ' . $store->getId() . ' (' . $store->getStoreName() . ')');
                $order->save();
            } catch (NoSuchEntityException $e) {
                $this->_logger->info('ORDER SERVICE: HIT _setAdditionalAttributesToOrder Catch Block for order: ' . $order->getIncrementId());
                if ($shippingAddress) {
                    $store = $this->_getStoreByPostcode($shippingAddress->getPostcode());
                    if ($store) {
                        $storeId = $store->getId();
                    }
                    if (!empty($storeId)) {
                        /** @var Store $store */
                        $store = $this->_storeRepository->getById($storeId);
                        /** @var \Magento\Sales\Model\Order $order */
                        $order->setWarehouse($store->getErpId());
                        $order->setStore($store->getErpCode());
                        $order->setStorelocatorId($store->getId());
                        $this->_logger->info('ORDER SERVICE: Store detail updated for order: ' . $order->getIncrementId() . " using postcode" . $shippingAddress->getPostcode());
                        $order->addStatusHistoryComment('Assigned Store: ' . $store->getId() . ' (' . $store->getStoreName() . ')');
                        $order->save();
                    }
                }
            }
            if ($store && $store->getErpId()) {
                $this->_storeInventoryService->updateStoreInventory($store->getErpId(), self::$_orderedProducts);
            }
        }
        $order->save();
    }

    /**
     * Get store by postcode.
     *
     * @param $postcode
     *
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface
     */
    protected function _getStoreByPostcode($postcode)
    {
        /**
         * @var \Totaltools\Storelocator\Api\Data\ZoneInterface $zone
         */
        $zone = $this->_shippingZoneRepository->getZoneByPostCode($postcode);

        if (!$zone || !$zone->getId()) {
            return false;
        }

        return $this->_storeRepository->findByZone($zone->getId());
    }
}
