<?php

namespace Totaltools\Storelocator\Plugin\Model\Quote;

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

use Magento\Checkout\Api\Data\ShippingInformationInterface;
use Magento\Checkout\Model\ShippingInformationManagement;
use Psr\Log\LoggerInterface;

class AddressStoreLocator
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param LoggerInterface $logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }

    /**
     * @param ShippingInformationManagement $subject
     * @param $cartId
     * @param ShippingInformationInterface $addressInformation
     */
    public function beforeSaveAddressInformation(
        ShippingInformationManagement $subject,
        $cartId,
        ShippingInformationInterface $addressInformation
    ) {

        $this->logger->info(sprintf('[%s] storelocator_id plugin started', get_class($this)));

        $extensionAttributes = $addressInformation->getExtensionAttributes();
        $shippingAddress = $addressInformation->getShippingAddress();

        if (empty($extensionAttributes)) {
            return;
        }

        $storeLocatorId = $extensionAttributes->getStorelocatorId();

        if (isset($storeLocatorId)) {
            $shippingAddress->setStorelocatorId($storeLocatorId);
        }

        $this->logger->info(sprintf('[%s] storelocator_id assigned to address id: [%d]', $storeLocatorId, $shippingAddress->getId()));

        return [$cartId, $addressInformation];
    }
}