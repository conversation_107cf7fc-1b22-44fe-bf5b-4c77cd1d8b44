<?php


namespace Totaltools\Storelocator\Plugin;

use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderExtensionInterface;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderSearchResultInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class OrderRepositoryPlugin
{
    /**
     * Order feedback field name
     */
    const WAREHOUSE_FIELD = 'warehouse';
    const STORE_FIELD = 'store';
    const XML_STORE_LOCATOR_ID = 'storelocator_id';
    const XML_STORE_LOCATOR_API_TYPE = 'storelocator_api_type';

    /**
     * Order Extension Attributes Factory
     *
     * @var OrderExtensionFactory
     */
    protected $extensionFactory;

    /**
     * OrderRepositoryPlugin constructor
     *
     * @param OrderExtensionFactory $extensionFactory
     */
    public function __construct(OrderExtensionFactory $extensionFactory)
    {
        $this->extensionFactory = $extensionFactory;
    }

    /**
     * Add "warehouse" and "store" extension attributes to order data object to make it accessible in API data
     *
     * @param OrderRepositoryInterface $subject
     * @param OrderInterface $order
     *
     * @return OrderInterface
     */
    public function afterGet(OrderRepositoryInterface $subject, OrderInterface $order)
    {
        $warehouse = $order->getData(self::WAREHOUSE_FIELD);
        $store = $order->getData(self::STORE_FIELD);
        $storelocatorId = $order->getData(self::XML_STORE_LOCATOR_ID);
        $storelocatorApiType = $order->getData(self::XML_STORE_LOCATOR_API_TYPE);

        $extensionAttributes = $order->getExtensionAttributes();
        $extensionAttributes = $extensionAttributes ? $extensionAttributes : $this->extensionFactory->create();

        $extensionAttributes->setWarehouse($warehouse);
        $extensionAttributes->setStore($store);
        $extensionAttributes->setStorelocatorId($storelocatorId);
        $extensionAttributes->setStorelocatorApiType($storelocatorApiType);

        $order->setExtensionAttributes($extensionAttributes);

        return $order;
    }

    /**
     * Add "warehouse" and "store" extension attributes to order data object to make it accessible in API data
     *
     * @param OrderRepositoryInterface $subject
     * @param OrderSearchResultInterface $searchResult
     *
     * @return OrderSearchResultInterface
     */
    public function afterGetList(OrderRepositoryInterface $subject, OrderSearchResultInterface $searchResult)
    {
        $orders = $searchResult->getItems();

        foreach ($orders as &$order) {
            /*** @var \Magento\Sales\Model\Order $order */
            $warehouse = $order->getData(self::WAREHOUSE_FIELD);
            $store = $order->getData(self::STORE_FIELD);
            $storelocatorId = $order->getData(self::XML_STORE_LOCATOR_ID);
            $storelocatorApiType = $order->getData(self::XML_STORE_LOCATOR_API_TYPE);

            $extensionAttributes = $order->getExtensionAttributes();
            $extensionAttributes = $extensionAttributes ? $extensionAttributes : $this->extensionFactory->create();

            $extensionAttributes->setWarehouse($warehouse);
            $extensionAttributes->setStore($store);
            $extensionAttributes->setStorelocatorId($storelocatorId);
            $extensionAttributes->setStorelocatorApiType($storelocatorApiType);

            $order->setExtensionAttributes($extensionAttributes);
        }

        return $searchResult;
    }
}