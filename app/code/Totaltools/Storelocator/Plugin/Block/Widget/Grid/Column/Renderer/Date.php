<?php

namespace Totaltools\Storelocator\Plugin\Block\Widget\Grid\Column\Renderer;

/**
 * Class Date
 * @package Totaltools\Storelocator\Plugin\Block\Widget\Grid\Column\Renderer
 */
class Date
{
    /**
     * @param \Magento\Backend\Block\Widget\Grid\Column\Renderer\Date $subject
     * @param \Magento\Framework\DataObject $row
     */
    public function beforeRender(
        \Magento\Backend\Block\Widget\Grid\Column\Renderer\Date $subject,
        \Magento\Framework\DataObject $row
    ) {
        $format = $subject->getColumn()->getFormat();
        if ($format && is_string($format)) {
            $subject->getColumn()->setFormat(null);
        }
    }
}
