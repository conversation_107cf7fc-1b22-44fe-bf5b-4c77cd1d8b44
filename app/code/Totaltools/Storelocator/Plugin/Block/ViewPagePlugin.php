<?php

namespace Totaltools\Storelocator\Plugin\Block;

/**
 * @category    Totaltools
 * @package     Totaltools_Storelocator
 * @copyright   Copyright (c) 2021 Totaltools <https://totaltools.com.au>
 * <AUTHOR> Iqbal <<EMAIL>>
 */

use Totaltools\Storelocator\Helper\Data as Helper;

class ViewPagePlugin
{
    /**
     * @var Helper
     */
    protected $_helper;

    /**
     * Viewpage class constructor
     *
     * @param Helper
     */
    public function __construct(Helper $_helper)
    {
        $this->_helper = $_helper;
    }

    /**
     * @param \Magestore\Storelocator\Block\Store\ViewPage $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterGetJsLayout($subject, $jsLayout)
    {
        $jsLayout = json_decode($jsLayout, true);

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_SERVICE_URL))) {
            $jsLayout['map']['service_url'] = $this->_helper->getConfig(Helper::XML_PATH_SERVICE_URL);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_SERVICE_ACCESS_TOKEN))) {
            $jsLayout['map']['access_token'] = $this->_helper->getConfig(Helper::XML_PATH_SERVICE_ACCESS_TOKEN);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_TILE_MAX_ZOOM))) {
            $jsLayout['map']['maxZoom'] = $this->_helper->getConfig(Helper::XML_PATH_TILE_MAX_ZOOM);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_TILE_SIZE))) {
            $jsLayout['tile']['tileSize'] = $this->_helper->getConfig(Helper::XML_PATH_TILE_SIZE);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_TILE_ZOOM_OFFSET))) {
            $jsLayout['tile']['zoomOffset'] = $this->_helper->getConfig(Helper::XML_PATH_TILE_ZOOM_OFFSET);
        }

        if (!empty($this->_helper->getConfig(Helper::XML_PATH_TILE_SERVICE_ID))) {
            $jsLayout['tile']['id'] = $this->_helper->getConfig(Helper::XML_PATH_TILE_SERVICE_ID);
        }

        return json_encode($jsLayout, JSON_NUMERIC_CHECK);
    }
}
