<?php
/**
 * GmapTab
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
namespace Totaltools\Storelocator\Plugin\Block\Adminhtml\Store\Edit\Tab;

class GmapTab
{

    /**
     * @param \Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GmapTab $subject
     * @param \Closure $proceed
     * @return mixed
     */
    public function aroundGetFormHtml(
        \Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GmapTab $subject,
        \Closure $proceed
    )
    {
        $form = $subject->getForm();
        $model = $subject->getRegistryModel();
        if (is_object($form)) {
            $fieldset = $form->getElement('gmap_fieldset');
            $fieldset->removeField('latitude');
            $fieldset->removeField('longitude');
            $fieldset->removeField('zoom_level');
            $fieldset->removeField('marker_icon');
            $fieldset->removeField('googlemap');
            $fieldset->addField(
                'latitude',
                'text',
                [
                    'name' => 'latitude',
                    'label' => __('Latitude'),
                    'title' => __('Latitude'),
                    'required' => true,
                ]
            );

            $fieldset->addField(
                'longitude',
                'text',
                [
                    'name' => 'longitude',
                    'label' => __('Longitude'),
                    'title' => __('Longitude'),
                    'required' => true,
                ]
            );

            $fieldset->addField(
                'zoom_level',
                'text',
                [
                    'name' => 'zoom_level',
                    'label' => __('Zoom Level'),
                    'title' => __('Zoom Level'),
                    'required' => true,
                ]
            );

            $fieldset->addField(
                'store_direction_url',
                'text',
                [
                    'name' => 'store_direction_url',
                    'label' => __('Store Direction URL'),
                    'title' => __('Store Direction URL'),
                    'required' => true,
                ]
            );


            $form->setValues($model->getData());
            $subject->setForm($form);
        }

        return $proceed();
    }

    /**
     * @param \Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GmapTab $subject
     * @param $title
     * @return \Magento\Framework\Phrase
     */
    public function afterGetTabLabel(\Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GmapTab $subject, $title)
    {
        return __('Location Information');
    }


    /**
     * @param \Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GmapTab $subject
     * @param $title
     * @return \Magento\Framework\Phrase
     */
    public function afterGetTabTitle(\Magestore\Storelocator\Block\Adminhtml\Store\Edit\Tab\GmapTab $subject, $title)
    {
        return __('Location Information');
    }
}
