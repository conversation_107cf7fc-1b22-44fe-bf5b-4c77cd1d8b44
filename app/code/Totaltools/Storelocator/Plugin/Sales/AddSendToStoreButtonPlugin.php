<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Storelocator
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */
namespace Totaltools\Storelocator\Plugin\Sales;

class AddSendToStoreButtonPlugin
{
    protected $context;
    protected $url;

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Backend\Model\UrlInterface $url,
        \Magento\Framework\Registry $registry
    ) {
        $this->context = $context;
        $this->url = $url;
        $this->_coreRegistry = $registry;
    }

    public function afterGetButtonList(
        \Magento\Backend\Block\Widget\Context $subject,
        $buttonList
    ) {
        $request = $this->context->getRequest();

        $order = $this->getOrder();

        if (($request->getFullActionName() == 'sales_order_view') &&
            (($order->getState() == \Magento\Sales\Model\Order::STATE_PROCESSING) ||
                ($order->getState() == \Magento\Sales\Model\Order::STATE_COMPLETE))) {
            $message = __('Are you sure you want to send Email to Store?');
            $buttonList->add(
                'store_send_order',
                [
                    'label' => __('Send Email to Store'),
                    'onclick' => 'setLocation(\'' . $this->getStoreLocatorEmailUrl($request) . '\')',
                    'onclick' => "confirmSetLocation('{$message}', '{$this->getStoreLocatorEmailUrl($request)}')",
                    'class' => 'ship'
                ],
                10
            );
        }

        return $buttonList;
    }

    public function getStoreLocatorEmailUrl($request)
    {
        $orderId = $request->getParam('order_id');

        return $this->url->getUrl(
            'totaltoolsadmin/store/email',
            [
                'order_id' => $orderId
            ]
        );
    }

    /**
     * Retrieve order model object
     *
     * @return \Magento\Sales\Model\Order
     */
    public function getOrder()
    {
        return $this->_coreRegistry->registry('sales_order');
    }
}
