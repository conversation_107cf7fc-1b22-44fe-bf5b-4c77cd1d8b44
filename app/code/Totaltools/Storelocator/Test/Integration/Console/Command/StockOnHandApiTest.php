<?php
declare(strict_types=1);

namespace Totaltools\Storelocator\Test\Integration\Console\Command;

use PHPUnit\Framework\TestCase;
use Magento\TestFramework\Helper\Bootstrap;
use Symfony\Component\Console\Tester\CommandTester;
use Totaltools\Storelocator\Console\Command\StockOnHandApi;

/**
 * Integration test for StockOnHandApi command
 */
class StockOnHandApiTest extends TestCase
{
    /**
     * @var StockOnHandApi
     */
    private $command;

    /**
     * @var CommandTester
     */
    private $commandTester;

    /**
     * Set up test dependencies
     */
    protected function setUp(): void
    {
        $objectManager = Bootstrap::getObjectManager();
        $this->command = $objectManager->get(StockOnHandApi::class);
        $this->commandTester = new CommandTester($this->command);
    }

    /**
     * Test command execution when disabled
     */
    public function testExecuteWhenDisabled(): void
    {
        // Mock the configuration to return false for enabled check
        $this->commandTester->execute([]);
        
        $output = $this->commandTester->getDisplay();
        $this->assertStringContains('Pronto API SOH Import is disabled', $output);
        $this->assertEquals(\Magento\Framework\Console\Cli::RETURN_FAILURE, $this->commandTester->getStatusCode());
    }

    /**
     * Test command provides helpful output
     */
    public function testCommandProvidesOutput(): void
    {
        $this->commandTester->execute([]);
        
        $output = $this->commandTester->getDisplay();
        $this->assertNotEmpty($output);
        
        // Should contain either success or failure message
        $this->assertTrue(
            strpos($output, 'Import started') !== false ||
            strpos($output, 'Import is disabled') !== false
        );
    }
}
