<?php
declare(strict_types=1);

namespace Totaltools\Storelocator\Test\Unit\Model;

use PHPUnit\Framework\TestCase;
use PHPUnit\Framework\MockObject\MockObject;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Storelocator\Model\ImportInventory\Email;
use Magento\Framework\App\ResourceConnection;
use Totaltools\Pronto\Model\Api\SohApi;
use Totaltools\Storelocator\Model\SohProcessor;

/**
 * Unit test for SohProcessor class
 */
class SohProcessorTest extends TestCase
{
    /**
     * @var SohProcessor
     */
    private $sohProcessor;

    /**
     * @var MockObject|TimezoneInterface
     */
    private $localeDateMock;

    /**
     * @var MockObject|Email
     */
    private $emailMock;

    /**
     * @var MockObject|LoggerInterface
     */
    private $loggerMock;

    /**
     * @var MockObject|ResourceConnection
     */
    private $resourceMock;

    /**
     * @var MockObject|SohApi
     */
    private $sohApiMock;

    /**
     * Set up test dependencies
     */
    protected function setUp(): void
    {
        $this->localeDateMock = $this->createMock(TimezoneInterface::class);
        $this->emailMock = $this->createMock(Email::class);
        $this->loggerMock = $this->createMock(LoggerInterface::class);
        $this->resourceMock = $this->createMock(ResourceConnection::class);
        $this->sohApiMock = $this->createMock(SohApi::class);

        $this->sohProcessor = new SohProcessor(
            $this->localeDateMock,
            $this->emailMock,
            $this->loggerMock,
            $this->resourceMock,
            $this->sohApiMock
        );
    }

    /**
     * Test that failure tracking works correctly
     */
    public function testFailureTracking(): void
    {
        // Initially should have no failures
        $this->assertFalse($this->sohProcessor->hasFailures());
        $this->assertEmpty($this->sohProcessor->getFailedStores());

        // Test import with missing API endpoint
        $store = [
            'erp_id' => 'TEST001',
            'store_name' => 'Test Store',
            'full_soh_api_endpoint' => ''
        ];

        $result = $this->sohProcessor->importInventory($store);

        // Should return false and track the failure
        $this->assertFalse($result);
        $this->assertTrue($this->sohProcessor->hasFailures());
        
        $failedStores = $this->sohProcessor->getFailedStores();
        $this->assertCount(1, $failedStores);
        $this->assertEquals('TEST001', $failedStores[0]['erp_id']);
        $this->assertEquals('Test Store', $failedStores[0]['store_name']);
        $this->assertStringContains('No API endpoint configured', $failedStores[0]['error']);
    }

    /**
     * Test reset failure tracking
     */
    public function testResetFailureTracking(): void
    {
        // Add a failure first
        $store = [
            'erp_id' => 'TEST001',
            'store_name' => 'Test Store',
            'full_soh_api_endpoint' => ''
        ];

        $this->sohProcessor->importInventory($store);
        $this->assertTrue($this->sohProcessor->hasFailures());

        // Reset and verify
        $this->sohProcessor->resetFailureTracking();
        $this->assertFalse($this->sohProcessor->hasFailures());
        $this->assertEmpty($this->sohProcessor->getFailedStores());
    }

    /**
     * Test send failure alert
     */
    public function testSendFailureAlert(): void
    {
        // Add a failure first
        $store = [
            'erp_id' => 'TEST001',
            'store_name' => 'Test Store',
            'full_soh_api_endpoint' => ''
        ];

        $this->sohProcessor->importInventory($store);

        // Expect email to be sent
        $this->emailMock->expects($this->once())
            ->method('sendSohApiFailureEmail')
            ->with($this->isType('array'));

        $this->sohProcessor->sendFailureAlert();
    }

    /**
     * Test send failure alert with no failures
     */
    public function testSendFailureAlertWithNoFailures(): void
    {
        // Should not send email when there are no failures
        $this->emailMock->expects($this->never())
            ->method('sendSohApiFailureEmail');

        $this->sohProcessor->sendFailureAlert();
    }
}
