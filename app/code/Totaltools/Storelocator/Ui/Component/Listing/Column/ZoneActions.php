<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storelocator\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Magento\Framework\UrlInterface;

/**
 * Class ZoneAction.
 *
 * @category  Totaltools
 * @package   Totaltools_Storelocator
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class ZoneActions extends Column
{
    /**
     * Edit Zone URL.
     */
    const Storelocator_ZONE_URL_PATH_EDIT = 'totaltoolsadmin/zone/edit';

    /**
     * Delete Zone URL.
     */
    const Storelocator_ZONE_URL_PATH_DELETE = 'totaltoolsadmin/zone/delete';

    /**
     * URL Builder.
     *
     * @var UrlInterface
     */
    protected $_urlBuilder;

    /**
     * URL to the Zone edit form.
     *
     * @var string
     */
    private $_editUrl;

    /**
     * ZoneActions constructor.
     *
     * @param ContextInterface   $context
     * @param UiComponentFactory $uiComponentFactory
     * @param UrlInterface       $urlBuilder
     * @param array              $components
     * @param array              $data
     * @param string             $editUrl
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        UrlInterface $urlBuilder,
        array $components = [],
        array $data = [],
        $editUrl = self::Storelocator_ZONE_URL_PATH_EDIT
    )
    {
        $this->_urlBuilder = $urlBuilder;
        $this->_editUrl = $editUrl;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     *
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as & $item) {
                $name = $this->getData('name');
                $title =  $item['name'];
                if (isset($item['zone_id'])) {
                    $item[$name]['edit'] = [
                        'href'  => $this->_urlBuilder->getUrl($this->_editUrl, ['zone_id' => $item['zone_id']]),
                        'label' => __('Edit')
                    ];
                    $item[$name]['delete'] = [
                        'href'    => $this->_urlBuilder->getUrl(
                            self::Storelocator_ZONE_URL_PATH_DELETE,
                            ['zone_id' => $item['zone_id']]
                        ),
                        'label'   => __('Delete'),
                        'confirm' => [
                            'title'   => __("Delete $title"),
                            'message' => __("Are you sure you want to delete a $title record?")
                        ]
                    ];
                }
            }
        }

        return $dataSource;
    }
}