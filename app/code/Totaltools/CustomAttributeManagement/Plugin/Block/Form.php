<?php

namespace Totaltools\CustomAttributeManagement\Plugin\Block;

/**
 * Class Form
 * @package Totaltools\CustomAttributeManagement\Plugin\Block
 */
class Form
{
    /**
     * Format for HTML elements id attribute
     *
     * @var string
     */
    private $_fieldIdFormat = '%1$s';

    /**
     * Format for HTML elements name attribute
     *
     * @var string
     */
    private $_fieldNameFormat = '%1$s';

    /**
     * @param \Magento\CustomAttributeManagement\Block\Form $subject
     * @param \Closure $proceed
     * @param \Magento\Eav\Model\Attribute $attribute
     * @return bool|string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function aroundGetAttributeHtml(
        \Magento\CustomAttributeManagement\Block\Form $subject,
        \Closure $proceed,
        \Magento\Eav\Model\Attribute $attribute
    ) {
        $type = $attribute->getFrontendInput();
        $block = $subject->getRenderer($type);
        if ($block) {
            $block->setAttributeObject(
                $attribute
            )->setEntity(
                $subject->getEntity()
            )->setFieldIdFormat(
                $this->_fieldIdFormat
            )->setFieldNameFormat(
                $this->_fieldNameFormat
            );

            $area = $subject->getForm()->getFormCode();
            if ($area === 'customer_account_create') {
                $parentBlock = $subject->getParentBlock();
                $formData = $parentBlock->getFormData();
                if ($formData) {
                    $block->setData('current_area', $area);
                    $block->setData($attribute->getAttributeCode(), $formData->getData($attribute->getAttributeCode()));
                }
            }

            return $block->toHtml();
        }

        return false;
    }
}
