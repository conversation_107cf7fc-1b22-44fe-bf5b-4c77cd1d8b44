<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Plugin\Block\Sales\Order;

/**
 * Class Total
 * @package Totaltools\Reward\Plugin\Block\Sales\Order
 */
class Total
{
    /**
     * @param \Magento\Reward\Block\Sales\Order\Total $subject
     * @param \Closure $proceed
     * @return \Magento\Reward\Block\Sales\Order\Total
     */
    public function aroundInitTotals(
        \Magento\Reward\Block\Sales\Order\Total $subject,
        \Closure $proceed
    ) {
        if ((double)$subject->getOrder()->getBaseRewardCurrencyAmount()) {
            $source = $subject->getSource();
            $value = -$source->getRewardCurrencyAmount();

            $subject->getParentBlock()->addTotal(
                new \Magento\Framework\DataObject(
                    [
                        'code' => 'reward_points',
                        'strong' => false,
                        'label' => __('Insider Dollars Redeemed'),
                        'value' => $source instanceof \Magento\Sales\Model\Order\Creditmemo ? -$value : $value,
                    ]
                )
            );
        }

        return $subject;
    }
}
