<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Plugin\Api;

use Totaltools\Reward\Api\RewardValidatorInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Company\Api\CompanyManagementInterface;
use Psr\Log\LoggerInterface as Logger;

class AccountManagement
{
    /**
     * @var CustomerFactory
     */
    private $customerFactory;

    /**
     * @var RewardValidatorInterface
     */
    private $rewardValidator;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @var CompanyManagementInterface
     */
    private $companyManagementInterface;

    /**
     * CustomerLogin constructor.
     * @param RewardValidatorInterface $rewardValidator
     */
    public function __construct(
        RewardValidatorInterface $rewardValidator,
        CustomerFactory $customerFactory,
        CompanyManagementInterface $companyManagementInterface,
        Logger $logger
    ) {
        $this->rewardValidator = $rewardValidator;
        $this->customerFactory = $customerFactory;
        $this->companyManagementInterface = $companyManagementInterface;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Customer\Api\AccountManagementInterface $subject
     * @param \Magento\Customer\Model\Customer $result
     * @return mixed
     */
    public function afterAuthenticate(
        \Magento\Customer\Api\AccountManagementInterface $subject,
        $result
    ) {
        try {
            /** @var \Magento\Customer\Model\Customer $customerModel */
            $customerModel = $this->customerFactory->create()->updateData($result);
            if ($this->companyManagementInterface->getByCustomerId($customerModel->getId())) {
                return $result;
            }

            $this->rewardValidator->setCustomer($customerModel)->validate($customerModel->getLoyaltyId(), 0);
        } catch (\Exception $exception) {
            $this->logger->error($exception);
        }

        return $result;
    }
}