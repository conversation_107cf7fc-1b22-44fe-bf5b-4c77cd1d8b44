<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Plugin\Controller\Cart;

use Magento\Checkout\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Response\RedirectInterface;
use Magento\Framework\App\ResponseInterface;
use Magento\Reward\Helper\Data;

class Remove
{
    /**
     * @var RedirectInterface
     */
    protected $_redirect;

    /**
     * @var Data
     */
    private $rewardHelper;

    /**
     * @var Session
     */
    private $checkoutSession;

    /**
     * @param Context $context
     * @param Data $rewardHelper
     * @param Session $checkoutSession
     */
    public function __construct(
        Context $context,
        Data $rewardHelper,
        Session $checkoutSession
    ) {
        $this->_redirect = $context->getRedirect();
        $this->rewardHelper = $rewardHelper;
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * Remove Reward Points payment from current quote
     *
     * @param \Magento\Reward\Controller\Cart\Remove $subject
     * @param \Closure $proceed
     * @return void|ResponseInterface
     */
    public function aroundExecute(\Magento\Reward\Controller\Cart\Remove $subject, \Closure $proceed)
    {
        if (!$this->rewardHelper->isEnabledOnFront() || !$this->rewardHelper->getHasRates()) {
            return $this->_redirect($subject, 'customer/account/');
        }

        $quote = $this->checkoutSession->getQuote();

        if ($quote->getUseRewardPoints()) {
            $quote->setUseRewardPoints(false)->collectTotals()->save();
        }

        $referer = $subject->getRequest()->getParam('_referer');

        if ($referer === 'payment') {
            return $this->_redirect($subject, 'checkout', ['_fragment' => 'payment']);
        }

        return $this->_redirect($subject, 'checkout/cart');
    }

    /**
     * Set redirect into response
     *
     * @param $subject
     * @param string $path
     * @param array $arguments
     * @return  ResponseInterface
     */
    private function _redirect($subject, $path, $arguments = [])
    {
        $this->_redirect->redirect($subject->getResponse(), $path, $arguments);
        return $subject->getResponse();
    }
}
