<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Plugin\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Totaltools\Pronto\Model\Source\LoyaltyStatus;
use Totaltools\Reward\Api\RewardValidatorInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Session\SessionManagerInterface;

/**
 * Class PaymentDataImporter
 * @package Totaltools\Reward\Plugin\Model
 */
class PaymentDataImporter
{
    /**
     * @var RewardValidatorInterface
     */
    private $rewardValidator;

    /**
     * @var RequestInterface
     */
    private $request;

    /**
	 * @var SessionManagerInterface
	 */
	protected $session;

    /**
     * PaymentDataImporter constructor.
     * @param RewardValidatorInterface $rewardValidator
     * @param RequestInterface $request
     * @param SessionManagerInterface $session
     */
    public function __construct(
        RewardValidatorInterface $rewardValidator,
        RequestInterface $request,
        SessionManagerInterface $session
    ) {
        $this->rewardValidator = $rewardValidator;
        $this->request = $request;
        $this->session = $session;
    }

    /**
     * @param \Magento\Reward\Model\PaymentDataImporter $subject
     * @param \Closure $proceed
     * @param \Magento\Quote\Model\Quote $quote
     * @param $payment
     * @param $useRewardPoints
     * @return \Magento\Reward\Model\PaymentDataImporter|mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function aroundImport(
        \Magento\Reward\Model\PaymentDataImporter $subject,
        \Closure $proceed,
        \Magento\Quote\Model\Quote $quote,
        $payment,
        $useRewardPoints
    ) {
        $customer = $quote->getCustomer();
        $requestParams = $this->request->getBodyParams();
        $sessionRequest = $this->session->getRequest();
        if($this->session->getGraphqlRequest()) {
            $requestParams = $this->session->getRequestParams();
        }
        $this->session->setGraphqlRequest(false);
        $loyaltyIdAttribute = $customer->getCustomAttribute('loyalty_id');
        $loyaltyId = $loyaltyIdAttribute ? $loyaltyIdAttribute->getValue() : '';
        $loyaltyStatusAttribute = $customer->getCustomAttribute('loyalty_status');
        $loyaltyStatus = $loyaltyStatusAttribute ? $loyaltyStatusAttribute->getValue() : LoyaltyStatus::ACTIVE;
        if ((int)$loyaltyStatus === LoyaltyStatus::FROZEN) {
            return [];
        }
        if (false === empty($requestParams['reward_points_amount'])
            || (int)$requestParams['reward_points_amount'] === 0) {
            if (false === $this->rewardValidator->validate(
                $loyaltyId,
                $requestParams['reward_points_amount'],
                !empty($requestParams['use_maximum_points']))
            ) {
                return $subject;
            }
        }

        if (!empty($requestParams['use_maximum_points'])) {
            $quote->setCustomRewardPointsBalance(0);

            return $proceed($quote, $payment, $useRewardPoints);
        }

        if (!$quote ||
            !$quote->getCustomerId() ||
            $quote->getBaseGrandTotal() + $quote->getBaseRewardCurrencyAmount() <= 0
        ) {
            return $subject;
        }

        if ($useRewardPoints) {
            $pointsAmount = $requestParams['reward_points_amount'];
            $quote->setCustomRewardPointsBalance($pointsAmount);
        }

        return $proceed($quote, $payment, $useRewardPoints);
    }
}
