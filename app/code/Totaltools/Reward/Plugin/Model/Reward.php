<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Plugin\Model;

/**
 * Class Reward
 * @package Totaltools\Reward\Plugin\Model
 */
class Reward
{

    /**
     * @param \Magento\Reward\Model\Reward $subject
     * @param \Closure $proceed
     * @return \Magento\Reward\Model\Reward
     */
    public function aroundSendBalanceUpdateNotification(
        \Magento\Reward\Model\Reward $subject,
        \Closure $proceed
    ) {
        return $subject;
    }

    /**
     * @param \Magento\Reward\Model\Reward $subject
     * @param \Closure $proceed
     * @param $item
     * @param $websiteId
     * @return \Magento\Reward\Model\Reward
     */
    public function aroundSendBalanceWarningNotification(
        \Magento\Reward\Model\Reward $subject,
        \Closure $proceed,
        $item,
        $websiteId
    ) {
        return $subject;
    }
}
