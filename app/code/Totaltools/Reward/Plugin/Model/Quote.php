<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Plugin\Model;

/**
 * Class Quote
 * @package Totaltools\Reward\Plugin\Model
 */
class Quote
{

    /**
     * @param \Magento\Quote\Model\Quote $subject
     * @param $result
     * @return array
     */
    public function afterGetAllAddresses(\Magento\Quote\Model\Quote $subject, $result)
    {
        $addresses = [];
        foreach ($result as $address) {
            $addresses[$address->getAddressType()] = $address;
        }
        krsort($addresses);
        return $addresses;
    }
}
