<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Plugin\Model\Total\Quote;

use Magento\Reward\Model\Total\Quote\Reward as MagentoReward;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Quote\Api\Data\ShippingAssignmentInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Address\Total;
use Magento\Reward\Helper\Data;
use Magento\Reward\Model\RewardFactory;

/**
 * Class Reward
 * @package Totaltools\Reward\Plugin\Model\Total\Quote
 */
class Reward
{

    /**
     * Reward data
     *
     * @var Data
     */
    private $rewardData = null;

    /**
     * Reward factory
     *
     * @var RewardFactory
     */
    private $rewardFactory;

    /**
     * @var PriceCurrencyInterface
     */
    private $priceCurrency;

    /**
     * Reward constructor.
     * @param PriceCurrencyInterface $priceCurrency
     * @param Data $rewardData
     * @param RewardFactory $rewardFactory
     */
    public function __construct(
        PriceCurrencyInterface $priceCurrency,
        Data $rewardData,
        RewardFactory $rewardFactory
    ) {
        $this->priceCurrency = $priceCurrency;
        $this->rewardData = $rewardData;
        $this->rewardFactory = $rewardFactory;
    }

    /**
     * @param MagentoReward $magentoReward
     * @param \Closure $proceed
     * @param Quote $quote
     * @param ShippingAssignmentInterface $shippingAssignment
     * @param Total $total
     * @return MagentoReward
     */
    public function aroundCollect(
        MagentoReward $magentoReward,
        \Closure $proceed,
        Quote $quote,
        ShippingAssignmentInterface $shippingAssignment,
        Total $total
    ) {
        $this->resetRewardPointAmountItem($quote);
        if (!$this->rewardData->isEnabledOnFront($quote->getStore()->getWebsiteId())) {
            return $magentoReward;
        }

        $total->setRewardPointsBalance(0)->setRewardCurrencyAmount(0)->setBaseRewardCurrencyAmount(0);

        if ($total->getBaseGrandTotal() >= 0 && $quote->getCustomer()->getId() && $quote->getUseRewardPoints()) {
            /* @var $reward \Magento\Reward\Model\Reward */
            $reward = $quote->getRewardInstance();
            if (!$reward || !$reward->getId()) {
                $customer = $quote->getCustomer();
                $reward = $this->rewardFactory->create()->setCustomer($customer);
                $reward->setCustomerId($quote->getCustomer()->getId());
                $reward->setWebsiteId($quote->getStore()->getWebsiteId());
                $reward->loadByCustomer();
            }

            $customPointsAmount = $quote->getCustomRewardPointsBalance();

            if ($customPointsAmount > 0) {
                $customPointsAmount = $customPointsAmount < $reward->getPointsBalance() ? $customPointsAmount : $reward->getPointsBalance();
                $reward->setPointsBalance($customPointsAmount);
            }
            $amountExclude = $this->getAmountIgnore($total, $quote);
            $baseAmountIgnore = $amountExclude['base'];
            $amountIgnore = $amountExclude['amount'];

            $pointsLeft = $reward->getPointsBalance() - $quote->getRewardPointsBalance();
            $rewardCurrencyAmountLeft = $this->priceCurrency->convert(
                $reward->getCurrencyAmount(),
                $quote->getStore()
            ) - $quote->getRewardCurrencyAmount();
            $baseRewardCurrencyAmountLeft = $reward->getCurrencyAmount() - $quote->getBaseRewardCurrencyAmount();
            $baseAmount = $total->getBaseGrandTotal() - $baseAmountIgnore;
            if ($baseAmount > 0 && $baseAmount < 0.001) {
                $baseAmount = 0;
            }
            if ($baseRewardCurrencyAmountLeft >= $baseAmount) {
                $pointsBalanceUsed = $reward->getPointsEquivalent($baseAmount);
                $pointsCurrencyAmountUsed = $total->getGrandTotal() - $amountIgnore;
                $basePointsCurrencyAmountUsed = $baseAmount;

                $total->setGrandTotal($amountIgnore);
                $total->setBaseGrandTotal($baseAmountIgnore);
            } else {
                $pointsBalanceUsed = $reward->getPointsEquivalent($baseRewardCurrencyAmountLeft);
                if ($pointsBalanceUsed > $pointsLeft) {
                    $pointsBalanceUsed = $pointsLeft;
                }
                $pointsCurrencyAmountUsed = $rewardCurrencyAmountLeft;
                $basePointsCurrencyAmountUsed = $baseRewardCurrencyAmountLeft;

                $grandTotal = $total->getGrandTotal() - $pointsCurrencyAmountUsed;
                $baseGrandTotal = $total->getBaseGrandTotal() - $basePointsCurrencyAmountUsed;
                if (($grandTotal > 0 && $grandTotal < 0.001) || ($baseGrandTotal > 0 && $baseGrandTotal < 0.001)) {
                    $grandTotal = 0;
                    $baseGrandTotal = 0;
                }
                $total->setGrandTotal($grandTotal);
                $total->setBaseGrandTotal($baseGrandTotal);
            }
            if ($pointsBalanceUsed === 0) {
                $pointsCurrencyAmountUsed = 0;
                $basePointsCurrencyAmountUsed = 0;
            }
            $quote->setRewardPointsBalance(round($quote->getRewardPointsBalance() + $pointsBalanceUsed));
            $quote->setCustomRewardPointsBalance($quote->getRewardPointsBalance());
            $rewardCurrencyAmount = $quote->getRewardCurrencyAmount() + $pointsCurrencyAmountUsed;
            $baseRewardCurrencyAmount = $quote->getBaseRewardCurrencyAmount() + $basePointsCurrencyAmountUsed;
            $quote->setRewardCurrencyAmount($rewardCurrencyAmount);
            $quote->setBaseRewardCurrencyAmount($baseRewardCurrencyAmount);

            $this->setRewardPointAmountItem($quote, $baseRewardCurrencyAmount, $rewardCurrencyAmount);

            $total->setRewardPointsBalance(round($pointsBalanceUsed));
            $total->setRewardCurrencyAmount($pointsCurrencyAmountUsed);
            $total->setBaseRewardCurrencyAmount($basePointsCurrencyAmountUsed);
        }
        return $magentoReward;
    }

    /**
     * @param $total
     * @param $quote
     * @return array
     */
    private function getAmountIgnore($total, $quote)
    {
        $baseAmountIgnore = $total->getBaseShippingInclTax();
        $amountIgnore = $total->getShippingInclTax();
        $items = $quote->getAllVisibleItems();
        foreach ($items as $item) {
            $product = $item->getProduct();
            if ((bool)$product->getIsUsedForReward() === false) {
                $baseAmountIgnore += $item->getBaseRowTotalInclTax() - $item->getBaseDiscountAmount();
                $amountIgnore += $item->getRowTotalInclTax() - $item->getDiscountAmount() ;
            }
        }
        $giftCardAmountUsed = $quote->getGiftCardsAmountUsed();
        $baseGiftCardAmountUsed = $quote->getBaseGiftCardsAmountUsed();
        if ($baseGiftCardAmountUsed > 0) {
            $baseAmountIgnore = $baseAmountIgnore - $baseGiftCardAmountUsed;
            $amountIgnore = $amountIgnore - $giftCardAmountUsed;
            if ($baseAmountIgnore < 0 || $amountIgnore < 0) {
                $baseAmountIgnore = 0;
                $amountIgnore = 0;
            }
        }
        return ['base' => $baseAmountIgnore, 'amount' => $amountIgnore];
    }

    /**
     * @param $quote
     */
    private function resetRewardPointAmountItem($quote)
    {
        $items = $quote->getAllVisibleItems();
        foreach ($items as $item) {
            $item->setRewardPointAmount(0);
            $item->setBaseRewardPointAmount(0);
        }
    }

    /**
     * @param $quote
     * @param $baseRewardPointAmount
     * @param $rewardPointAmount
     */
    private function setRewardPointAmountItem($quote, $baseRewardPointAmount, $rewardPointAmount)
    {
        $items = $quote->getAllVisibleItems();
        foreach ($items as $item) {
            $product = $item->getProduct();
            if ((bool)$product->getIsUsedForReward() === true && $baseRewardPointAmount > 0) {
                $baseTaxableAmount = $item->getBaseRowTotalInclTax() - $item->getBaseDiscountAmount();
                $taxableAmount = $item->getRowTotalInclTax() - $item->getDiscountAmount();
                if ($baseRewardPointAmount <= $baseTaxableAmount) {
                    $item->setRewardPointAmount($rewardPointAmount);
                    $item->setBaseRewardPointAmount($baseRewardPointAmount);
                    $baseRewardPointAmount = 0;
                    $rewardPointAmount = 0;
                } else {
                    $item->setRewardPointAmount($taxableAmount);
                    $item->setBaseRewardPointAmount($baseTaxableAmount);
                    $baseRewardPointAmount -= $baseTaxableAmount;
                    $rewardPointAmount -= $taxableAmount;
                }
            }
        }
        $this->clearQuoteItemsCache($quote);
    }

    /**
     * @param $quote
     */
    private function clearQuoteItemsCache($quote)
    {
        $allAddress = $quote->getAllAddresses();
        foreach ($allAddress as $address) {
            $address->unsetData('cached_items_all');
            $address->unsetData('cached_items_nominal');
            $address->unsetData('cached_items_nonnominal');
        }
    }
}
