<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Plugin\Model;

use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Reward\Model\PaymentDataImporter;

/**
 * Class RewardManagement
 * @package Totaltools\Reward\Plugin\Model
 */
class RewardManagement
{
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private $quoteRepository;

    /**
     * Reward helper
     *
     * @var \Magento\Reward\Helper\Data
     */
    private $rewardData;

    /**
     * @var \Magento\Reward\Model\PaymentDataImporter
     */
    private $importer;

    /**
     * RewardManagement constructor.
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\Reward\Helper\Data $rewardData
     * @param PaymentDataImporter $importer
     */
    public function __construct(
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\Reward\Helper\Data $rewardData,
        \Magento\Reward\Model\PaymentDataImporter $importer
    ) {
        $this->quoteRepository = $quoteRepository;
        $this->rewardData = $rewardData;
        $this->importer = $importer;
    }

    /**
     * @param \Magento\Reward\Model\RewardManagement $subject
     * @param \Closure $proceed
     * @param $cartId
     * @return bool|string
     */
    public function aroundSet(
        \Magento\Reward\Model\RewardManagement $subject,
        \Closure $proceed,
        $cartId
    ) {
        if ($this->rewardData->isEnabledOnFront()) {
            try {
                /* @var $quote \Magento\Quote\Model\Quote */
                $quote = $this->quoteRepository->getActive($cartId);
                $dataImport = $this->importer->import($quote, $quote->getPayment(), true);
                if (is_array($dataImport) && count($dataImport) === 0) {
                    return ['status' => 'FROZEN'];
                }
                $quote->collectTotals();
                $this->quoteRepository->save($quote);
                $quote->setTotalsCollectedFlag(false);
                $quote->collectTotals()->save();
                if ($quote->getRewardPointsBalance() > 0) {
                    return true;
                }
            } catch (\Exception $exception) {
                return $exception->getMessage();
            }
        }

        return false;
    }
}
