<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Plugin\Observer;

use Totaltools\Reward\Api\RewardManagementInterface;

/**
 * Class OrderCompleted
 * @package Totaltools\Reward\Plugin\Observer
 */
class OrderCompleted
{
    /**
     * @var RewardManagementInterface
     */
    private $rewardManagement;

    /**
     * @var \Magento\Reward\Helper\Data
     */
    private $rewardData;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    private $customerFactory;

    /**
     * OrderCompleted constructor.
     * @param \Magento\Reward\Helper\Data $rewardData
     * @param RewardManagementInterface $rewardManagement
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Reward\Helper\Data $rewardData,
        RewardManagementInterface $rewardManagement,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->rewardManagement = $rewardManagement;
        $this->rewardData = $rewardData;
        $this->customerFactory = $customerFactory;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Reward\Observer\OrderCompleted $subject
     * @param $result
     * @param \Magento\Framework\Event\Observer $observer
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function afterExecute(
        \Magento\Reward\Observer\OrderCompleted $subject,
        $result,
        \Magento\Framework\Event\Observer $observer
    ) {
        /* @var $order \Magento\Sales\Model\Order */
        $order = $observer->getEvent()->getOrder();
        $orderResource = $order->getResource();
        try {
            $amount = $order->getBaseRewardCurrencyAmount();
            if ($order->getCustomerIsGuest() || $order->getIsProntoLocked() ||
                !$this->rewardData->isEnabledOnFront($order->getStore()->getWebsiteId()) ||
                null === $amount
            ) {
                return $result;
            }
            /** @var \Magento\Customer\Model\Customer $customer */
            $customer = $this->customerFactory->create()->load($order->getCustomerId());
            $this->rewardManagement->lock($customer->getLoyaltyId(), $amount, $order->getIncrementId());
            $order->setIsProntoLocked(true);
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage());
            $order->setIsProntoLocked(false);
            $order->setProntoLockRetry(0);
            $orderResource->saveAttribute($order, RewardManagementInterface::PRONTO_LOCK_RETRY);
        }
        $orderResource->saveAttribute($order, RewardManagementInterface::IS_PRONTO_LOCKED);

        return $result;
    }
}
