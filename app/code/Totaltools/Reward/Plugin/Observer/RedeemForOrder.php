<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Plugin\Observer;

use Magento\Framework\Event\Observer;
use Totaltools\Reward\Api\RewardValidatorInterface;
use Totaltools\Reward\Exception\CouldNotRedeemException;

/**
 * Class RedeemForOrder
 * @package Totaltools\Reward\Plugin\Observer
 */
class RedeemForOrder
{
    /**
     * Reward factory
     *
     * @var \Magento\Reward\Model\RewardFactory
     */
    private $modelFactory;

    /**
     * Core model store manager interface
     *
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var RewardValidatorInterface
     */
    private $rewardValidator;

    /**
     * RedeemForOrder constructor.
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Reward\Model\RewardFactory $modelFactory
     * @param RewardValidatorInterface $rewardValidator
     */
    public function __construct(
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Reward\Model\RewardFactory $modelFactory,
        RewardValidatorInterface $rewardValidator
    ) {
        $this->storeManager = $storeManager;
        $this->modelFactory = $modelFactory;
        $this->rewardValidator = $rewardValidator;
    }

    /**
     * @param \Magento\Reward\Observer\RedeemForOrder $subject
     * @param Observer $observer
     * @return Observer[]
     */
    public function beforeExecute(
        \Magento\Reward\Observer\RedeemForOrder $subject,
        Observer $observer
    ) {
        $event = $observer->getEvent();
        /* @var $order \Magento\Sales\Model\Order */
        $order = $event->getOrder();
        /** @var $quote \Magento\Quote\Model\Quote $quote */
        $quote = $event->getQuote();
        if ($quote->getRewardPointsBalance() > 0) {
            $customer = $quote->getCustomer();
            if ($customer &&
                $customer->getCustomAttribute('loyalty_id') &&
                $loyaltyId = $customer->getCustomAttribute('loyalty_id')->getValue()
            ) {
                $websiteId = $this->storeManager->getStore($order->getStoreId())->getWebsiteId();
                /* @var $reward \Magento\Reward\Model\Reward */
                $reward = $this->modelFactory->create();
                $reward->setCustomerId($order->getCustomerId());
                $reward->setWebsiteId($websiteId);
                $reward->loadByCustomer();

                $this->rewardValidator->validate($loyaltyId, $quote->getRewardPointsBalance());
            } else {
                throw new CouldNotRedeemException(
                    __('Your account is not qualified to use the reward points.')
                );
            }
        }

        return [$observer];
    }
}
