<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Model;

use Totaltools\Reward\Api\GuestRewardManagementInterface;

/**
 * @api
 */
class GuestRewardManagement implements GuestRewardManagementInterface
{
    /**
     * @inheritDoc
     */
    public function balance(string $loyaltyId, string $amount)
    {
        throw new \Magento\Framework\Exception\LocalizedException(__('This action is not allowed for guest.'));
    }

    /**
     * @inheritDoc
     */
    public function lock(string $loyaltyId, string $amount, string $magentoOrderNumber)
    {
        throw new \Magento\Framework\Exception\LocalizedException(__('This action is not allowed for guest.'));
    }
}
