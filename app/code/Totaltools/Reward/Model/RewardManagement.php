<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Model;

use Magento\Framework\Event\ManagerInterface;
use Totaltools\Reward\Api\RewardManagementInterface;
use Totaltools\Reward\Exception\CouldNotLockException;

/**
 * @api
 */
class RewardManagement implements RewardManagementInterface
{
    /**
     * @var RewardInformationFactory
     */
    private $rewardInformationFactory;

    /**
     * @var ManagerInterface
     */
    private $eventManager;

    /**
     * RewardManagement constructor.
     *
     * @param RewardInformationFactory $rewardInformationFactory
     * @param ManagerInterface $eventManager
     */
    public function __construct(
        RewardInformationFactory $rewardInformationFactory,
        ManagerInterface $eventManager
    ) {
        $this->rewardInformationFactory = $rewardInformationFactory;
        $this->eventManager = $eventManager;
    }

    /**
     * @inheritDoc
     */
    public function balance(string $loyaltyId, string $amount, bool $useMaxPoints = false)
    {
        if ($loyaltyId === '' || ($useMaxPoints === false && $amount === '')) {
            throw new \Magento\Framework\Exception\InputException(__('Something went wrong with your request.'));
        }

        /** @var \Totaltools\Reward\Model\RewardInformation $reward */
        $reward = $this->rewardInformationFactory->create();
        $this->eventManager->dispatch(
            'totaltools_reward_balance',
            [
                'loyalty_id' => $loyaltyId,
                'amount' => $amount,
                'reward' => $reward,
            ]
        );

        if ($reward->isValid() === false) {
            $message = 'Something went wrong with your request.';
            if (empty($reward->getErrorMessages()) === false) {
                $message = implode("\n", $reward->getErrorMessages());
            }
            $reward->addErrorMessage([$message]);
        }

        return $reward;
    }

    /**
     * @inheritDoc
     */
    public function lock(string $loyaltyId, string $amount, string $magentoOrderNumber)
    {
        if ($loyaltyId === '' || $amount === '' || $magentoOrderNumber === '') {
            throw new \Magento\Framework\Exception\InputException(__('Something went wrong with your request.'));
        }

        /** @var \Totaltools\Reward\Model\RewardInformation $reward */
        $reward = $this->rewardInformationFactory->create();
        $this->eventManager->dispatch(
            'totaltools_reward_lock',
            [
                'loyalty_id' => $loyaltyId,
                'amount' => $amount,
                'reward' => $reward,
                'order_number' => $magentoOrderNumber
            ]
        );

        if ($reward->isValid() !== true) { // process only with 200 response
            $message = 'Something went wrong with your request.';
            if (empty($reward->getErrorMessages()) === false) {
                $message = implode("\n", $reward->getErrorMessages());
            }
            throw new CouldNotLockException(
                __("Lock error:" . $message)
            );
        }

        return $reward;
    }
}
