<?php
/**
 * @package     Totaltools_Reward
 * <AUTHOR> <<EMAIL>>
 * @since       0.1.2
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Reward\Model;

use Magento\Reward\Model\ConfigProvider as BaseConfigProvider;
/**
 * @api
 */
class ConfigProvider extends BaseConfigProvider
{
    /**
     * Retrieve reward label
     *
     * @return \Magento\Framework\Phrase
     */
    protected function getRewardLabel()
    {
        $format = '%s.00';
        $points = sprintf($format, $this->getRewardModel()->getPointsBalance());
        if (null !== $this->getRewardModel()->getCurrencyAmount() && $this->rewardHelper->getHasRates()) {
            $amount = sprintf(
                $format,
                $this->rewardHelper->formatAmount($this->getRewardModel()->getCurrencyAmount(), true, null)
            );
            return __('%1 store reward points available (%2)', $points, $amount);
        }
        return __('%1 store reward points available', $points);
    }

// @codeCoverageIgnoreEnd
}
