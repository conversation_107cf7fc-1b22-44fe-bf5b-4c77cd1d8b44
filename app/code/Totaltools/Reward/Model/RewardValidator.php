<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Model;

use Totaltools\Reward\Api\RewardManagementInterface;
use Totaltools\Reward\Api\RewardValidatorInterface;
use Totaltools\Reward\Exception\RewardValidationException;
use Totaltools\Reward\Model\Validation\InsufficientPointsToRedeemValidator;
use Totaltools\Reward\Model\Validation\IsProntoCustomerValidator;
use Magento\Framework\DataObjectFactory;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Reward\Helper\RewardHelper;

/**
 * @api
 */
class RewardValidator implements RewardValidatorInterface
{
    /**
     * @var int
     */
    private $storeId;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var RewardManagementInterface
     */
    private $rewardManagement;

    /**
     * @var DataObjectFactory
     */
    private $dataObjectFactory;

    /**
     * @var ExternalRewardValidator
     */
    private $externalRewardValidator;

    /**
     * @var \Totaltools\Reward\Helper\RewardHelper
     */
    private $totRewardHelper;

    /**
     * @var \Magento\Customer\Model\Customer
     */
    private $customer = null;

    /**
     * RewardValidator constructor.
     *
     * @param StoreManagerInterface $storeManager
     * @param DataObjectFactory $dataObjectFactory
     * @param RewardManagementInterface $rewardManagement
     * @param ExternalRewardValidator $externalGiftCardValidator
     * @param RewardHelper $totRewardHelper
     */
    public function __construct(
        StoreManagerInterface $storeManager,
        DataObjectFactory $dataObjectFactory,
        RewardManagementInterface $rewardManagement,
        ExternalRewardValidator $externalGiftCardValidator,
        RewardHelper $totRewardHelper
    ) {
        $this->storeManager = $storeManager;
        $this->dataObjectFactory = $dataObjectFactory;
        $this->rewardManagement = $rewardManagement;
        $this->externalRewardValidator = $externalGiftCardValidator;
        $this->totRewardHelper = $totRewardHelper;
    }

    /**
     * {@inheritdoc}
     */
    public function setCustomer(\Magento\Customer\Model\Customer $customer)
    {
        $this->customer = $customer;

        return $this;
    }

    /**
     * {@inheritdoc}
     */
    public function setStoreId($storeId)
    {
        $this->storeId = $storeId;

        return $this;
    }

    /**
     * {@inheritdoc}
     */
    public function validate(string $loyaltyId, string $amount, bool $useMaxPoints = false)
    {
        $externalRewardInfo = $this->rewardManagement->balance($loyaltyId, $amount, $useMaxPoints);
        $externalRewardValidationResult = $this->externalRewardValidator->validate(
            $externalRewardInfo,
            [
                IsProntoCustomerValidator::class,
                InsufficientPointsToRedeemValidator::class,
            ]
        );

        if ($externalRewardValidationResult->hasMessages()) {
            throw new RewardValidationException(
                __($externalRewardValidationResult->getMessagesAsString())
            );
        }

        if ($externalRewardInfo->isValid()) {
            /** @var \Magento\Framework\DataObject $data */
            $data = $this->dataObjectFactory->create();
            $websiteId = $this->storeManager->getStore($this->storeId)->getWebsiteId();
            $data->setWebsiteId($websiteId)->setAction(\Magento\Reward\Model\Reward::REWARD_ACTION_ADMIN);
            if ($this->customer) {
                $this->totRewardHelper->setCustomer($this->customer);
            }
            $this->totRewardHelper->updateRewardBalance($data, $externalRewardInfo);

            return true;
        }

        return false;
        // @codeCoverageIgnoreStart
    }

// @codeCoverageIgnoreEnd
}
