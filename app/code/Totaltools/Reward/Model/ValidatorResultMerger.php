<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Model;

use Totaltools\Reward\Api\ValidatorResultInterface;
use Totaltools\Reward\Api\ValidatorResultInterfaceFactory;

class ValidatorResultMerger
{
    /**
     * @var ValidatorResultInterfaceFactory
     */
    private $validatorResultInterfaceFactory;

    /**
     * ValidatorResultMerger constructor.
     *
     * @param ValidatorResultInterfaceFactory $validatorResultInterfaceFactory
     * @SuppressWarnings(PHPMD.LongVariable)
     */
    public function __construct(ValidatorResultInterfaceFactory $validatorResultInterfaceFactory)
    {
        $this->validatorResultInterfaceFactory = $validatorResultInterfaceFactory;
    }

    /**
     * Merge two validator results and additional messages.
     *
     * @param ValidatorR<PERSON><PERSON>Interface $first
     * @param ValidatorResultInterface $second
     * @param \string[]                $validatorMessages
     *
     * @return ValidatorResultInterface
     */
    public function merge(ValidatorResultInterface $first, ValidatorResultInterface $second, ...$validatorMessages)
    {
        $messages = array_merge($first->getMessages(), $second->getMessages(), ...$validatorMessages);

        /** @var ValidatorResultInterface $result */
        $result = $this->validatorResultInterfaceFactory->create();
        foreach ($messages as $message) {
            $result->addMessage($message);
        }

        return $result;
    }
}
