<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Model;

use Totaltools\Reward\Api\ValidatorResultInterface;

class ValidatorResult implements ValidatorResultInterface
{
    /**
     * @var \string[]
     */
    private $messages = [];

    /**
     * {@inheritdoc}
     */
    public function addMessage($message)
    {
        $this->messages[] = (string) $message;
    }

    /**
     * {@inheritdoc}
     */
    public function hasMessages()
    {
        return empty($this->messages) === false;
    }

    /**
     * {@inheritdoc}
     */
    public function getMessages()
    {
        return $this->messages;
    }

    /**
     * {@inheritdoc}
     */
    public function getMessagesAsString()
    {
        return implode('<br/>', $this->getMessages());
    }
}
