<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Model;

use Totaltools\Reward\Api\ValidatorInterface;
use Totaltools\Reward\Api\ValidatorResultInterface;
use Totaltools\Reward\Api\ValidatorResultInterfaceFactory;
use Totaltools\Reward\Exception\GiftCardValidationException;
use Magento\Framework\Exception\ConfigurationMismatchException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\ObjectManagerInterface;

class Validator
{
    /**
     * @var ObjectManagerInterface
     */
    private $objectManager;

    /**
     * @var ValidatorResultInterfaceFactory
     */
    private $validatorResultFactory;

    /**
     * Validator constructor.
     *
     * @param ObjectManagerInterface          $objectManager
     * @param ValidatorResultInterfaceFactory $validatorResult
     */
    public function __construct(
        ObjectManagerInterface $objectManager,
        ValidatorResultInterfaceFactory $validatorResult
    ) {
        $this->objectManager = $objectManager;
        $this->validatorResultFactory = $validatorResult;
    }

    /**
     * @param object               $entity
     * @param ValidatorInterface[] $validators
     * @param object|null          $context
     *
     * @return ValidatorResultInterface
     *
     * @throws ConfigurationMismatchException
     * @throws GiftCardValidationException
     * @throws LocalizedException
     */
    public function validate($entity, array $validators, $context = null)
    {
        $messages = [];
        $validatorArguments = [];
        if ($context !== null) {
            $validatorArguments['context'] = $context;
        }

        foreach ($validators as $validatorName) {
            $validatorMessages= [];
            $validator = $this->objectManager->create($validatorName, $validatorArguments); //@codingStandardsIgnoreLine
            if (!$validator instanceof ValidatorInterface) {
                throw new ConfigurationMismatchException(
                    __(
                        sprintf('Validator %s is not instance of general validator interface', $validatorName)
                    )
                );
            }
            $validatorMessage = $validator->validate($entity);
            if(!empty($validatorMessage)) {
                $validatorMessages = $validatorMessage;
            }
            $messages = array_merge($messages, $validatorMessages);
        }

        /** @var ValidatorResultInterface $validationResult */
        $validationResult = $this->validatorResultFactory->create();
        foreach ($messages as $message) {
            $validationResult->addMessage($message);
        }

        return $validationResult;
    }
}
