<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
declare(strict_types=1);

namespace Totaltools\Reward\Model;

use Magento\Framework\Model\AbstractModel;
use Totaltools\Reward\Api\Data\RewardInformationInterface;

/**
 * @api
 */
class RewardInformation extends AbstractModel implements RewardInformationInterface
{
    /**
     * @inheritDoc
     */
    public function isValid()
    {
        return $this->getData(self::REWARD_IS_VALID);
    }

    /**
     * @inheritDoc
     */
    public function setIsValid($isValid)
    {
        return $this->setData(self::REWARD_IS_VALID, $isValid);
    }

    /**
     * @inheritDoc
     */
    public function getBalance()
    {
        return $this->getData(self::REWARD_BALANCE);

    }

    /**
     * @inheritDoc
     */
    public function setBalance($balance)
    {
        return $this->setData(self::REWARD_BALANCE, $balance);
    }

    /**
     * @inheritDoc
     */
    public function addErrorMessage($message)
    {
        return $this->setData(self::REWARD_MESSAGE, $message);
    }

    /**
     * @inheritDoc
     */
    public function getErrorMessages()
    {
        return $this->getData(self::REWARD_MESSAGE);
    }

    /**
     * @inheritDoc
     */
    public function getLoyaltyStatus()
    {
        return $this->getData(self::LOYALTY_STATUS);

    }

    /**
     * @inheritDoc
     */
    public function setLoyaltyStatus($status)
    {
        return $this->setData(self::LOYALTY_STATUS, $status);
    }
}
