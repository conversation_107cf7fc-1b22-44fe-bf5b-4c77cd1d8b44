<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Model;

use Totaltools\Reward\Api\Data\RewardInformationInterface;
use Totaltools\Reward\Api\ExternalRewardValidatorInterface;

class ExternalRewardValidator implements ExternalRewardValidatorInterface
{
    /**
     * @var \Totaltools\Reward\Model\Validator
     */
    private $validator;

    /**
     * ExternalGiftCardValidator constructor.
     *
     * @param Validator $validator
     */
    public function __construct(\Totaltools\Reward\Model\Validator $validator)
    {
        $this->validator = $validator;
    }

    /**
     * {@inheritdoc}
     */
    public function validate(RewardInformationInterface $reward, array $validators)
    {
        return $this->validator->validate($reward, $validators);
    }
}
