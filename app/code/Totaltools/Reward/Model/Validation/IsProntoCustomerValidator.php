<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Model\Validation;

use Totaltools\Reward\Api\ValidatorInterface;
use Totaltools\Reward\Helper\RewardHelper;

/**
 * Class IsProntoCustomerValidator
 * @package Totaltools\Reward\Model\Validation
 */
class IsProntoCustomerValidator implements ValidatorInterface
{
    /**
     * @var RewardHelper
     */
    private $rewardHelper;

    /**
     * IsProntoCustomerValidator constructor.
     *
     * @param RewardHelper $rewardHelper
     */
    public function __construct(
        RewardHelper $rewardHelper
    ) {
        $this->rewardHelper = $rewardHelper;
    }

    /**
     * {@inheritdoc}
     */
    public function validate($entity)
    {
        $messages = [];
        $customer = $this->rewardHelper->getCurrentCustomer();
        if ($customer &&
            $customer->getCustomAttribute('unverified_loyalty_id') &&
            $customer->getCustomAttribute('unverified_loyalty_id')->getValue()
        ) {
            $messages[] = __('Your account is not qualified to use the reward points.');
        }

        return $messages;
    }
}
