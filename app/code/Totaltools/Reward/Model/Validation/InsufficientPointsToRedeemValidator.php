<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Model\Validation;

use Totaltools\Pronto\Model\Source\LoyaltyStatus;
use Totaltools\Reward\Api\ValidatorInterface;

/**
 * Class InsufficientPointsToRedeemValidator
 * @package Totaltools\Reward\Model\Validation
 */
class InsufficientPointsToRedeemValidator implements ValidatorInterface
{
    /**
     * {@inheritdoc}
     */
    public function validate($entity)
    {
        $messages = [];
        if ((int) $entity->getBalance() === 0 && (int)$entity->getLoyaltyStatus() ===LoyaltyStatus::ACTIVE) {
            $messages = $entity->getMessage();
        }

        return $messages;
    }
}
