<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Setup;

use Magento\Framework\DB\Ddl\Table;
use Magento\Quote\Setup\QuoteSetup;
use Magento\Quote\Setup\QuoteSetupFactory;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

/**
 * Class UpgradeData
 * @package Totaltools\Reward\Setup
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var QuoteSetupFactory
     */
    private $quoteSetupFactory;

    /**
     * UpgradeData constructor.
     * @param QuoteSetupFactory $quoteSetupFactory
     */
    public function __construct(
        QuoteSetupFactory $quoteSetupFactory
    ) {
        $this->quoteSetupFactory = $quoteSetupFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        if (version_compare($context->getVersion(), '0.1.1', '<')) {
            $this->addQuoteItemAttribute($setup);
        }
        if (version_compare($context->getVersion(), '0.1.2', '<')) {
            $this->addQuoteAttribute($setup);
        }
    }

    /**
     * @param ModuleDataSetupInterface $setup
     */
    private function addQuoteItemAttribute(ModuleDataSetupInterface $setup)
    {
        /** @var QuoteSetup $quoteSetup */
        $quoteSetup = $this->quoteSetupFactory->create(['setup' => $setup]);
        $rewardPointOptions = [
            'type' => Table::TYPE_DECIMAL,
            'visible' => true,
            'required' => false,
            'default' => 0.0000,
        ];

        $quoteSetup->addAttribute('quote_item', 'base_reward_point_amount', $rewardPointOptions);
        $quoteSetup->addAttribute('quote_item', 'reward_point_amount', $rewardPointOptions);
    }

    /**
     * @param ModuleDataSetupInterface $setup
     */
    private function addQuoteAttribute(ModuleDataSetupInterface $setup)
    {
        /** @var QuoteSetup $quoteSetup */
        $quoteSetup = $this->quoteSetupFactory->create(['setup' => $setup]);
        $rewardPointOptions = [
            'type' => Table::TYPE_INTEGER,
            'visible' => true,
            'required' => false,
            'default' => 0,
        ];

        $quoteSetup->addAttribute('quote', 'custom_reward_points_balance', $rewardPointOptions);
    }
}
