<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Cron;

use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Totaltools\Reward\Api\RewardManagementInterface;
use Magento\Customer\Model\CustomerFactory;
use Psr\Log\LoggerInterface;

/**
 * Class LockPointsRetry
 * @package Totaltools\Reward\Cron
 */
class LockPointsRetry
{
    const MAXIMUM_NUMBER_OF_RETRIES = 'totaltools_reward/general/maximum_number_of_retries';

    /**
     * @var array
     */
    private $fieldOrderCollections = [
        'increment_id',
        'customer_id',
        'reward_points_balance',
        'is_pronto_locked',
        'pronto_lock_retry'
    ];

    /**
     * @var OrderCollectionFactory
     */
    private $orderCollectionFactory;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var RewardManagementInterface
     */
    private $rewardManagement;

    /**
     * @var CustomerFactory
     */
    private $customerFactory;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * LockPointsRetry constructor.
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param RewardManagementInterface $rewardManagement
     * @param CustomerFactory $customerFactory
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        OrderCollectionFactory $orderCollectionFactory,
        RewardManagementInterface $rewardManagement,
        CustomerFactory $customerFactory,
        ScopeConfigInterface $scopeConfig,
        LoggerInterface $logger
    ) {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->rewardManagement = $rewardManagement;
        $this->customerFactory = $customerFactory;
        $this->scopeConfig = $scopeConfig;
        $this->logger = $logger;
    }

    /**
     * Lock points for order has reward points is not locked
     */
    public function execute()
    {
        $maximumNumberOfRetries = $this->getMaximumNumberOfRetries();
        $orderCollection = $this->orderCollectionFactory->create()
            ->addFieldToSelect($this->fieldOrderCollections)
            ->addFieldToFilter('reward_points_balance', ['gt' => 0])
            ->addFieldToFilter('is_pronto_locked', ['eq' => false])
            ->addFieldToFilter('pronto_lock_retry', ['lt' => $maximumNumberOfRetries]);
        /* @var $order \Magento\Sales\Model\Order */
        foreach ($orderCollection as $order) {
            $customer = $this->customerFactory->create()->load($order->getCustomerId());
            $amount = $order->getRewardPointsBalance();
            $orderResource = $order->getResource();
            try {
                $this->rewardManagement->lock($customer->getLoyaltyId(), $amount, $order->getIncrementId());
                $order->setIsProntoLocked(true);
            } catch (\Exception $exception) {
                $this->logger->error($exception->getMessage());
                $order->setIsProntoLocked(false);
                $order->setProntoLockRetry($order->getProntoLockRetry() + 1);
                $orderResource->saveAttribute($order, RewardManagementInterface::PRONTO_LOCK_RETRY);
            }
            $orderResource->saveAttribute($order, RewardManagementInterface::IS_PRONTO_LOCKED);
        }
    }

    /**
     * @return int
     */
    private function getMaximumNumberOfRetries()
    {
        return (int)$this->scopeConfig->getValue(self::MAXIMUM_NUMBER_OF_RETRIES);
    }
}
