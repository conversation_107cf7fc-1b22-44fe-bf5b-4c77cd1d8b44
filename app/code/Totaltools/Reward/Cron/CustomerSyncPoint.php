<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Cron;

use Magento\Company\Api\CompanyManagementInterface;
use Magento\Reward\Model\Reward;
use Totaltools\Reward\Api\RewardManagementInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Framework\DataObjectFactory;
use Totaltools\Reward\Helper\RewardHelper;

/**
 * Class CustomerSyncPoint
 * @package Totaltools\Reward\Cron
 */
class CustomerSyncPoint
{
    /**
     * @var CompanyManagementInterface
     */
    private $companyManagementInterface;

    /**
     * @var CustomerFactory
     */
    private $customerFactory;

    /**
     * @var RewardManagementInterface
     */
    private $rewardManagement;

    /**
     * @var \Totaltools\Reward\Helper\RewardHelper
     */
    private $totRewardHelper;

    /**
     * @var DataObjectFactory
     */
    private $dataObjectFactory;

    /**
     * CustomerSyncPoint constructor.
     * @param CompanyManagementInterface $companyManagementInterface
     * @param CustomerFactory $customerFactory
     * @param RewardManagementInterface $rewardManagement
     * @param DataObjectFactory $dataObjectFactory
     * @param RewardHelper $totRewardHelper
     */
    public function __construct(
        CompanyManagementInterface $companyManagementInterface,
        CustomerFactory $customerFactory,
        RewardManagementInterface $rewardManagement,
        DataObjectFactory $dataObjectFactory,
        RewardHelper $totRewardHelper
    ) {
        $this->companyManagementInterface = $companyManagementInterface;
        $this->customerFactory = $customerFactory;
        $this->rewardManagement = $rewardManagement;
        $this->totRewardHelper = $totRewardHelper;
        $this->dataObjectFactory = $dataObjectFactory;
    }

    /**
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        $customerCollection = $this->getCustomerCollection();
        foreach ($customerCollection as $customer) {
            $customer = $this->customerFactory->create()->load($customer->getId());
            if ($customer && $loyaltyId = $customer->getData('loyalty_id')) {
                if (!$this->companyManagementInterface->getByCustomerId($customer->getId())) {
                    $externalRewardInfo = $this->rewardManagement->balance($loyaltyId, 0, false);
                    if ($externalRewardInfo->isValid()) {
                        /** @var \Magento\Framework\DataObject $data */
                        $data = $this->dataObjectFactory->create();
                        $websiteId = $customer->getWebsiteId();
                        $data->setWebsiteId($websiteId)->setAction(Reward::REWARD_ACTION_ADMIN);
                        $this->totRewardHelper->setCustomer($customer)->updateRewardBalance($data, $externalRewardInfo);
                    }
                }
            }
        }
    }

    /**
     * @return mixed
     */
    private function getCustomerCollection()
    {
        return $this->customerFactory->create()->getCollection()
            ->addAttributeToSelect('loyalty_id', 'entity_id')
            ->addAttributeToFilter('loyalty_id', ['neq' => 'NULL'])
            ->load();
    }
}
