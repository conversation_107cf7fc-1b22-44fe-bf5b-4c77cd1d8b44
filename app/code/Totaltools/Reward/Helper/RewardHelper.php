<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Reward\Helper;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Framework\App\Helper\Context;
use Magento\Reward\Model\RewardFactory;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Session\SessionManagerInterface;

/**
 * Class RewardHelper
 * @package Totaltools\Reward\Helper
 */
class RewardHelper extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @var RewardFactory
     */
    private $rewardFactory;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var SessionManagerInterface
     */
    private $sessionManager;

    /**
     * @var \Magento\Customer\Model\Customer
     */
    private $customerModel;

    /**
     * @var CustomerFactory
     */
    private $customerFactory;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * RewardHelper constructor.
     * @param Context $context
     * @param RewardFactory $rewardFactory
     * @param StoreManagerInterface $storeManager
     * @param SessionManagerInterface $sessionManager
     * @param CustomerFactory $customerFactory
     */
    public function __construct(
        Context $context,
        RewardFactory $rewardFactory,
        StoreManagerInterface $storeManager,
        SessionManagerInterface $sessionManager,
        CustomerFactory $customerFactory,
        CustomerRepositoryInterface $customerRepository
    ) {
        $this->rewardFactory = $rewardFactory;
        $this->storeManager = $storeManager;
        $this->sessionManager = $sessionManager;
        $this->customerFactory = $customerFactory;
        $this->customerRepository = $customerRepository;
        parent::__construct($context);

    }

    /**
     * @param \Magento\Customer\Model\Customer $customerModel
     * @return $this
     */
    public function setCustomer(\Magento\Customer\Model\Customer $customerModel)
    {
        $this->customerModel = $customerModel;

        return $this;
    }

    /**
     * @param $data
     * @param \Totaltools\Reward\Api\Data\RewardInformationInterface $externalRewardInfo
     * @throws \Exception
     */
    public function updateRewardBalance($data, $externalRewardInfo)
    {
        $customerModel = $this->getCurrentCustomer()->getId() ? $this->getCurrentCustomer() : $this->customerModel;
        /** @var \Magento\Reward\Model\Reward $reward */
        $reward = $this->rewardFactory->create();
        $reward->setCustomer($customerModel)
            ->setWebsiteId($data->getWebsiteId())
            ->loadByCustomer();

	if($externalRewardInfo->getBalance() > 0 ) {
        $reward->setPointsBalance($externalRewardInfo->getBalance());
	} else {
        $reward->setPointsBalance(0);
	}

	$reward->setAction($data->getAction())
            ->setActionEntity($customerModel)
            ->updateRewardPoints();
        $customerId = $customerModel->getId();
        if ($customerId) {
            if ($this->customerModel) {
                $customer = $this->customerModel;
            } else {
                $customer = $this->customerFactory->create()->load($customerModel->getId());
            }
            $customerData = $customer->getDataModel();
            $customerData->setCustomAttribute('loyalty_status', (int)$externalRewardInfo->getLoyaltyStatus());
            $customer->updateData($customerData);
            /** @var \Magento\Customer\Model\Customer $customer */
            $customer->save();
        }
    }

    /**
     * Get the current customer from session.
     *
     * @return \Magento\Customer\Api\Data\CustomerInterface
     */
    public function getCurrentCustomer()
    {
        return $this->sessionManager->getQuote()->getCustomer();
    }
}
