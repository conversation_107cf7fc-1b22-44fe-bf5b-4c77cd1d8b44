<?php
/**
 * Totaltools Reward.
 *
 * @category  Totaltools
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Company\Model\CustomerFactory as CompanyCustomerFactory;
use Magento\Customer\Model\CustomerFactory as CustomerCustomerFactory;
use Magento\Reward\Helper\Data as RewardData;
use Magento\Reward\Model\Reward\RateFactory;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Pronto\Model\Source\LoyaltyStatus;
use Magento\Company\Model\ResourceModel\Customer;

class CheckRates implements ObserverInterface
{
    /**
     * Reward rate factory
     *
     * @var RateFactory
     */
    private $rateFactory;

    /**
     * Core model store manager interface
     *
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * Reward helper
     *
     * @var RewardData
     */
    private $rewardData;

    /**
     * @var CompanyCustomerFactory
     */
    private $companyCustomerFactory;

    /**
     * @var CustomerCustomerFactory
     */
    private $customerFactory;
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;
    /**
     * CheckRates constructor.
     * @param RewardData $rewardData
     * @param StoreManagerInterface $storeManager
     * @param RateFactory $rateFactory
     * @param CompanyCustomerFactory $companyCustomerFactory
     * @param CustomerCustomerFactory $customerFactory
     */
    public function __construct(
        RewardData $rewardData,
        StoreManagerInterface $storeManager,
        RateFactory $rateFactory,
        CompanyCustomerFactory $companyCustomerFactory,
        CustomerCustomerFactory $customerFactory,
        Customer  $companyCustomer
    ) {
        $this->rewardData = $rewardData;
        $this->storeManager = $storeManager;
        $this->rateFactory = $rateFactory;
        $this->companyCustomerFactory = $companyCustomerFactory;
        $this->customerFactory = $customerFactory;
        $this->companyCustomer = $companyCustomer;
    }

    /**
     * If not all rates found, we should disable reward points on frontend
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return $this
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        if (!$this->rewardData->isEnabledOnFront()) {
            return $this;
        }

        /** @var \Magento\Customer\Model\Session $customerSession */
        $customerSession = $observer->getEvent()->getCustomerSession();
        $customerId = $customerSession->getCustomer()->getId();
        if ($customerId) {
            $groupId = $customerSession->getCustomerGroupId();
            $websiteId = $this->storeManager->getStore()->getWebsiteId();
            $isCurrentUserCompanyUser = false;
            $isAllowReward = true;

            $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId($customerId);
            $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
            $b2bCustomer =  array_diff($currentCompanyIds, [0]);
            if (!empty($b2bCustomer)) {
                $isCurrentUserCompanyUser = true;
            }
            $customer =  $this->customerFactory->create()->load($customerId);
            if (!in_array((int)$customer->getLoyaltyStatus(), [LoyaltyStatus::ACTIVE, LoyaltyStatus::FROZEN])) {
                $isAllowReward = false;
            }
             /** @var \Magento\Reward\Model\Reward\Rate $rate */
            $rate = $this->rateFactory->create();
            $hasRates = $rate->fetch(
                    $groupId,
                    $websiteId,
                    \Magento\Reward\Model\Reward\Rate::RATE_EXCHANGE_DIRECTION_TO_CURRENCY
                )->getId() && $rate->reset()->fetch(
                    $groupId,
                    $websiteId,
                    \Magento\Reward\Model\Reward\Rate::RATE_EXCHANGE_DIRECTION_TO_POINTS
                )->getId();
            $this->rewardData->setHasRates($hasRates && !$isCurrentUserCompanyUser && $isAllowReward);
        }
        return $this;
    }
}
