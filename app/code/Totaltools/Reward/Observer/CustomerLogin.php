<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Observer;

use Magento\Company\Api\CompanyManagementInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer as EventObserver;
use Totaltools\Reward\Api\RewardValidatorInterface;
use Psr\Log\LoggerInterface as Logger;
use Magento\Customer\Api\Data\CustomerInterface;

/**
 * Class CustomerLogin
 * @package Totaltools\Reward\Observer
 */
class CustomerLogin implements ObserverInterface
{

    /**
     * @var RewardValidatorInterface
     */
    private $rewardValidator;

    /**
     * @var CompanyManagementInterface
     */
    private $companyManagementInterface;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * @var CustomerFactory
     */
    private $customerFactory;

    /**
     * CustomerLogin constructor.
     * @param RewardValidatorInterface $rewardValidator
     * @param CompanyManagementInterface $companyManagementInterface
     * @param Logger $logger
     * @param CustomerFactory $customerFactory
     */
    public function __construct(
        RewardValidatorInterface $rewardValidator,
        CompanyManagementInterface $companyManagementInterface,
        Logger $logger,
        CustomerFactory $customerFactory
    ) {
        $this->rewardValidator = $rewardValidator;
        $this->companyManagementInterface = $companyManagementInterface;
        $this->logger = $logger;
        $this->customerFactory = $customerFactory;
    }
    /**
     * {@inheritdoc}
     */
    public function execute(EventObserver $observer)
    {
        /** @var CustomerInterface $customer */
        $customer = $observer->getEvent()->getCustomer();
        if ($customer) {
            try {
                $customer = $this->customerFactory->create()->load($customer->getId());
                if ($customer && $loyaltyId = $customer->getData('loyalty_id')) {
                    if (!$this->companyManagementInterface->getByCustomerId($customer->getId())) {
                        $this->rewardValidator->setCustomer($customer)->validate($loyaltyId, 0);
                    }
                }

            } catch (\Exception $exception) {
                $this->logger->error($exception);
            }

        }
    }
}
