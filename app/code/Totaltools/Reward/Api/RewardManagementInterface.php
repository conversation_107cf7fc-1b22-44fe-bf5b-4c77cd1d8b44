<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
declare(strict_types=1);

namespace Totaltools\Reward\Api;

/**
 * @api
 */
interface RewardManagementInterface
{
    const IS_PRONTO_LOCKED = 'is_pronto_locked';
    const PRONTO_LOCK_RETRY = 'pronto_lock_retry';

    /**
     * Secure Balance
     * Perform a reward balance lookup.
     *
     * @param string $loyaltyId
     * @param string $amount
     * @param bool $useMaxPoints
     *
     * @return \Totaltools\Reward\Api\Data\RewardInformationInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function balance(string $loyaltyId, string $amount, bool $useMaxPoints = false);

    /**
     * Lock point after reward purchased.
     *
     * @param string $loyaltyId
     * @param string $amount
     * @param string $magentoOrderNumber
     *
     * @return \Totaltools\Reward\Api\Data\RewardInformationInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function lock(string $loyaltyId, string $amount, string $magentoOrderNumber);
}
