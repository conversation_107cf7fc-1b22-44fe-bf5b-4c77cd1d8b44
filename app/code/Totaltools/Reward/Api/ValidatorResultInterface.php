<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Api;

/**
 * @api
 */
interface ValidatorResultInterface
{
    /**
     * @param string $message
     */
    public function addMessage($message);

    /**
     * @return bool
     */
    public function hasMessages();

    /**
     * @return \string[]
     */
    public function getMessages();

    /**
     * @return string
     */
    public function getMessagesAsString();
}
