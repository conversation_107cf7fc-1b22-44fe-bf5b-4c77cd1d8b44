<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Api;

use Totaltools\Reward\Api\Data\RewardInformationInterface;
use Totaltools\Reward\Exception\CouldNotRedeemException;
use Totaltools\Reward\Exception\RewardValidationException;
use Magento\Framework\Exception\ConfigurationMismatchException;

/**
 * @api
 */
interface ExternalRewardValidatorInterface
{
    /**
     * To validate external reward balance entity.
     *
     * @param RewardInformationInterface $reward
     * @param array                      $validators
     *
     * @return ValidatorResultInterface
     *
     * @throws ConfigurationMismatchException
     * @throws GiftCardValidationException
     * @throws CouldNotRedeemException
     */
    public function validate(RewardInformationInterface $reward, array $validators);
}
