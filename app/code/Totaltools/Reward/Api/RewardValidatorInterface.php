<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Api;

interface RewardValidatorInterface
{
    /**
     * Set store ID for specified cases.
     *
     * @param $storeId
     *
     * @return \Totaltools\Reward\Api\RewardValidatorInterface
     */
    public function setStoreId($storeId);

    /**
     * Set Customer Model
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @return $this
     */
    public function setCustomer(\Magento\Customer\Model\Customer $customer);

    /**
     * Validate the reward and sending check requests.
     *
     * If the current request gift card valid then RETURN true.
     * If it doesn't then RETURN false.
     *
     * @param string $loyaltyId
     * @param string $amount
     * @param bool $useMaxPoints
     *
     * @return bool
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function validate(string $loyaltyId, string $amount, bool $useMaxPoints = false);
}
