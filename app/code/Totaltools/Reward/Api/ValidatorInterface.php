<?php
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Reward\Api;

use Totaltools\Reward\Exception\RewardValidationException;

/**
 * @api
 */
interface ValidatorInterface
{
    /**
     * @param object $entity
     *
     * @return \Magento\Framework\Phrase[]
     *
     * @throws RewardValidationException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function validate($entity);
}
