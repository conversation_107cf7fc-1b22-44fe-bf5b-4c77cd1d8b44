<?php
/**
 * RewardInformationInterface.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
declare(strict_types=1);

namespace Totaltools\Reward\Api\Data;

/**
 * @api
 */
interface RewardInformationInterface
{
    const REWARD_IS_VALID = 'is_valid';
    const REWARD_BALANCE = 'balance';
    const REWARD_MESSAGE = 'message';
    const REWARD_STATUS = 'status';
    const LOYALTY_STATUS = 'loyalty_status';

    /**
     * Is Reward Valid.
     *
     * @return bool
     */
    public function isValid();

    /**
     * Set Is Valid Value.
     *
     * @param bool $isValid
     *
     * @return \Totaltools\Reward\Api\Data\RewardInformationInterface
     */
    public function setIsValid($isValid);

    /**
     * Get Reward Balance.
     *
     * @return string
     */
    public function getBalance();

    /**
     * Set Reward Balance Value.
     *
     * @param $balance
     *
     * @return \Totaltools\Reward\Api\Data\RewardInformationInterface
     */
    public function setBalance($balance);

    /**
     * Add Error Message.
     *
     * @param string[] $message
     *
     * @return \Totaltools\Reward\Api\Data\RewardInformationInterface
     */
    public function addErrorMessage($message);

    /**
     * Get Error Messages.
     *
     * @return string[]
     */
    public function getErrorMessages();

    /**
     * Get Loyalty Status.
     *
     * @return int
     */
    public function getLoyaltyStatus();

    /**
     * Set Loyalty Status Value.
     *
     * @param $status
     *
     * @return \Totaltools\Reward\Api\Data\RewardInformationInterface
     */
    public function setLoyaltyStatus($status);
}
