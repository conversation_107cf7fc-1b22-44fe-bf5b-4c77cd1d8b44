<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
-->
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/reward/pronto/balance" method="POST">
        <service class="Totaltools\Reward\Api\RewardManagementInterface"
                 method="balance"/>
        <resources>
            <resource ref="Totaltools_Reward::manage"/>
        </resources>
    </route>
    <route url="/V1/reward/guest-pronto/balance" method="POST">
        <service class="Totaltools\Reward\Api\GuestRewardManagementInterface"
                 method="balance"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/reward/pronto/mine/balance" method="POST">
        <service class="Totaltools\Reward\Api\RewardManagementInterface"
                 method="balance"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>

    <route url="/V1/reward/pronto/lock" method="POST">
        <service class="Totaltools\Reward\Api\RewardManagementInterface"
                 method="lock"/>
        <resources>
            <resource ref="Totaltools_Reward::manage"/>
        </resources>
    </route>
    <route url="/V1/reward/guest-pronto/lock" method="POST">
        <service class="Totaltools\Reward\Api\GuestRewardManagementInterface"
                 method="lock"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/reward/pronto/mine/lock" method="POST">
        <service class="Totaltools\Reward\Api\RewardManagementInterface"
                 method="lock"/>
        <resources>
            <resource ref="self"/>
        </resources>
    </route>
</routes>