<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Totaltools Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Totaltools\Reward\Api\Data\RewardInformationInterface" type="Totaltools\Reward\Model\RewardInformation"/>
    <preference for="Totaltools\Reward\Api\ExternalRewardValidatorInterface" type="Totaltools\Reward\Model\ExternalRewardValidator"/>
    <preference for="Totaltools\Reward\Api\GuestRewardManagementInterface" type="Totaltools\Reward\Model\GuestRewardManagement"/>
    <preference for="Totaltools\Reward\Api\RewardManagementInterface" type="Totaltools\Reward\Model\RewardManagement"/>
    <preference for="Totaltools\Reward\Api\RewardValidatorInterface" type="Totaltools\Reward\Model\RewardValidator"/>
    <preference for="Totaltools\Reward\Api\ValidatorInterface" type="Totaltools\Reward\Model\Validator"/>
    <preference for="Totaltools\Reward\Api\ValidatorResultInterface" type="Totaltools\Reward\Model\ValidatorResult"/>
    <preference for="Magento\Reward\Model\ConfigProvider" type="Totaltools\Reward\Model\ConfigProvider"/>
    <type name="Magento\Reward\Model\PaymentDataImporter">
        <plugin name="totaltools-reward-import" type="Totaltools\Reward\Plugin\Model\PaymentDataImporter" />
    </type>
    <type name="Magento\Reward\Model\RewardManagement">
        <plugin name="totaltools-reward-set" type="Totaltools\Reward\Plugin\Model\RewardManagement" />
    </type>
    <virtualType name="Totaltools\Reward\Model\Logger\VirtualDebug" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/totaltools/pronto_reward.log</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\Reward\Model\Logger\VirtualLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="name" xsi:type="string">TOT</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Totaltools\Reward\Model\Logger\VirtualDebug</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Totaltools\Reward\Model\RewardValidator">
        <arguments>
            <argument name="logger" xsi:type="object">Totaltools\Reward\Model\Logger\VirtualLogger</argument>
        </arguments>
    </type>
    <type name="Totaltools\Reward\Plugin\Model\PaymentDataImporter">
        <arguments>
            <argument name="checkoutSession" xsi:type="object">Magento\Checkout\Model\Session\Proxy</argument>
            <argument name="request" xsi:type="object">Magento\Framework\Webapi\Rest\Request\Proxy</argument>
        </arguments>
    </type>
    <type name="Totaltools\Reward\Helper\RewardHelper">
        <arguments>
            <argument name="sessionManager" xsi:type="object">Magento\Checkout\Model\Session\Proxy</argument>
        </arguments>
    </type>
    <type name="Magento\Reward\Model\Reward">
        <plugin name="totaltools-reward-notification" type="Totaltools\Reward\Plugin\Model\Reward" />
    </type>
    <type name="Magento\Reward\Observer\RedeemForOrder">
        <plugin name="totaltools-reward-observer-redeem-reward" type="Totaltools\Reward\Plugin\Observer\RedeemForOrder" />
    </type>
    <type name="Magento\Reward\Model\Total\Quote\Reward">
        <plugin name="total_tools_update_quote_reward" type="Totaltools\Reward\Plugin\Model\Total\Quote\Reward"/>
    </type>
    <type name="Totaltools\Reward\Plugin\Model\Total\Quote\Reward">
        <arguments>
            <argument name="checkoutSession" xsi:type="object">Magento\Checkout\Model\Session\Proxy</argument>
        </arguments>
    </type>
    <type name="Magento\Reward\Observer\OrderCompleted">
        <plugin name="totaltools-lock-reward-after-order-completed" type="Totaltools\Reward\Plugin\Observer\OrderCompleted" />
    </type>
    <type name="Totaltools\Reward\Plugin\Observer\OrderCompleted">
        <arguments>
            <argument name="logger" xsi:type="object">Totaltools\Reward\Model\Logger\VirtualLogger</argument>
        </arguments>
    </type>
    <type name="Magento\Quote\Model\Quote">
        <plugin name="total_tools_quote_get_all_address" type="Totaltools\Reward\Plugin\Model\Quote"/>
    </type>
    <type name="Magento\Reward\Block\Sales\Order\Total">
        <plugin name="total_tools_block_sales_order_total" type="Totaltools\Reward\Plugin\Block\Sales\Order\Total"/>
    </type>
    <type name="Magento\Reward\Controller\Cart\Remove">
        <plugin name="total_tools_reward_controller_cart_remove" type="Totaltools\Reward\Plugin\Controller\Cart\Remove"/>
    </type>
</config>
