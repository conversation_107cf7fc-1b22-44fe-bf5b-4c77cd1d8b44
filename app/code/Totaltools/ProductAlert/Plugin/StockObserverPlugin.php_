<?php
namespace Totaltools\ProductAlert\Plugin;

use Magento\ProductAlert\Model\Observer;
use Magento\Catalog\Model\Product;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Psr\Log\LoggerInterface;
use Magento\ProductAlert\Model\Mailing\AlertProcessor;
use Magento\ProductAlert\Model\Mailing\Publisher;
use Magento\Store\Model\StoreManagerInterface;

class StockObserverPlugin
{
    /**
     * @var ResourceConnection
     */
    private $resourceConnection;
    
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;
    
    /**
     * @var LoggerInterface
     */
    private $logger;

    private $publisher;
    
    private $storeManager;

    

    /**
     * @param ResourceConnection $resourceConnection
     * @param ScopeConfigInterface $scopeConfig
     * @param LoggerInterface $logger
     * @param Publisher $publisher
     */
    public function __construct(
        ResourceConnection $resourceConnection,
        ScopeConfigInterface $scopeConfig,
        LoggerInterface $logger,
        StoreManagerInterface $storeManager,
        Publisher $publisher
        ) {
        $this->resourceConnection = $resourceConnection;
        $this->scopeConfig = $scopeConfig;
        $this->logger = $logger;
        $this->publisher = $publisher;
        $this->storeManager = $storeManager;
    }

    /**
     * Around plugin for process method to use stock_availability_code
     *
     * @param Observer $subject
     * @param \Closure $proceed
     * @return Observer
     */
    public function aroundProcess(Observer $subject, \Closure $proceed)
    {
        $this->logger->info('StockObserverPlugin: started processing stock alerts');
        if (!$this->scopeConfig->isSetFlag(
            'catalog/productalert/allow_stock',
            ScopeInterface::SCOPE_STORE
        )) {
            return $proceed();
        }
         
        try {
            $connection = $this->resourceConnection->getConnection();
            $productTable = $this->resourceConnection->getTableName('catalog_product_entity');
            
            $eavAttribute = $this->resourceConnection->getTableName('eav_attribute');
            $attributeId = $connection->fetchOne(
                $connection->select()
                    ->from($eavAttribute, ['attribute_id'])
                    ->where('attribute_code = ?', 'stock_availability_code')
                    ->where('entity_type_id = ?', 4) 
            );
            
            if (!$attributeId) {
                $this->logger->error('Stock availability code attribute not found');
                return $proceed();
            }
            
            $eavAttributeOption = $this->resourceConnection->getTableName('eav_attribute_option');
            $eavAttributeOptionValue = $this->resourceConnection->getTableName('eav_attribute_option_value');
            
            $oosOptionId = $connection->fetchOne(
                $connection->select()
                    ->from(['o' => $eavAttributeOption], ['option_id'])
                    ->join(
                        ['v' => $eavAttributeOptionValue],
                        'o.option_id = v.option_id',
                        []
                    )
                    ->where('o.attribute_id = ?', $attributeId)
                    ->where('v.value = ?', 'OOS')
            );
            
            if (!$oosOptionId) {
                $this->logger->error('OOS option value not found for stock_availability_code');
                return $proceed();
            }
            
            $productEntityInt = $this->resourceConnection->getTableName('catalog_product_entity_int');
            $alertStock = $this->resourceConnection->getTableName('product_alert_stock');
            
            $select = $connection->select()
                ->from(['alert' => $alertStock], ['alert_stock_id', 'product_id', 'website_id', 'customer_id'])
                ->join(
                    ['p' => $productTable],
                    'p.entity_id = alert.product_id',
                    []
                )
                ->join(
                    ['stock' => $productEntityInt],
                    'stock.row_id = p.row_id',
                    []
                )
                ->where('alert.status = 0')
                ->where('stock.attribute_id = ?', $attributeId)
                ->where('stock.value != ?', $oosOptionId);
            
            $productsForAlert = $connection->fetchAll($select);
            
            if (!empty($productsForAlert)) {
                $this->logger->info('StockObserverPlugin: Found ' . count($productsForAlert) . ' products with stock alerts to process');
                
                $alertIds = array_column($productsForAlert, 'alert_stock_id');
                $connection->update(
                    $alertStock,
                    ['status' => 1],
                    ['alert_stock_id IN (?)' => $alertIds]
                );

                $alertType = AlertProcessor::ALERT_TYPE_STOCK;
                foreach ($this->storeManager->getWebsites() as $website) {
                    if (!$website->getDefaultGroup() || !$website->getDefaultGroup()->getDefaultStore()) {
                        continue;
                    }
                    // $storeId = $website->getDefaultGroup()->getDefaultStore()->getId();
                    $websiteId = $website->getId();
    
                    // Filter products for current website
                    $websiteProducts = array_filter($productsForAlert, function($item) use ($websiteId) {
                        return $item['website_id'] == $websiteId;
                    });
                    
                    // Get customer IDs for this website
                    $customerIds = array_unique(array_column($websiteProducts, 'customer_id'));
                    


                    if (!empty($customerIds)) {
                        $this->publisher->execute($alertType, $customerIds, (int)$website->getId());
                    }
                }
                
                return $subject;
            }
            
            $this->logger->info('No stock alerts to process');
            return $subject;
            
        } catch (\Exception $e) {
            $this->logger->error('Error in StockObserverPlugin: ' . $e->getMessage());
            return $proceed();
        }
    }
}
