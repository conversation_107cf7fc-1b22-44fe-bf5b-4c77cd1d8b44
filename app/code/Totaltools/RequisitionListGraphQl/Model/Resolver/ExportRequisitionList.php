<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\RequisitionListGraphQl\Model\Resolver;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class ExportRequisitionList implements ResolverInterface
{

    private $exportRequisitionListDataProvider;

    /**
     * @param DataProvider\ExportRequisitionList $exportRequisitionListRepository
     */
    public function __construct(
        DataProvider\ExportRequisitionList $exportRequisitionListDataProvider
    ) {
        $this->exportRequisitionListDataProvider = $exportRequisitionListDataProvider;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $exportRequisitionListData = $this->exportRequisitionListDataProvider->getExportRequisitionList();
        return $exportRequisitionListData;
    }
}

