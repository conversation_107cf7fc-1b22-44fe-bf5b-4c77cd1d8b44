<?php
/**
 * Created by <PERSON>
 * Copyright (c) 2017 Balance Internet (http://www.balanceinternet.com.au/)
 * Date: 23/11/2017
 */

namespace Totaltools\Shipping\Plugin\Block\Adminhtml\Order;

class Packing
{
    /**
     * Packages should be an array
     *
     * @param \Magento\Shipping\Block\Adminhtml\Order\Packaging $subject
     * @param \Closure                                          $process
     * @return array
     */
    public function aroundGetPackages(
        \Magento\Shipping\Block\Adminhtml\Order\Packaging $subject,
        \Closure $process
    ) {
        $result = $process();
        if (!is_null($result) && !is_array($result))
            return unserialize($result);

        return $result;
    }

}