define(
    [
        'jquery',
        'mage/url',
        'Magento_Customer/js/customer-data',
        'ko'
    ],
    function (
        $,
        url,
        customerData,
        ko
    ) {

        'use strict';

        var location = ko.observable(null);

        var xhr = null;

        return {

            /**
             * Explicitly subscribe to location
             * in dependant components to get any changes
             */
            location: location,

            getLocation: function (useIP) {

                useIP = typeof useIP !== 'undefined' ? useIP : false;

                if (!location() || useIP) {

                    this.findGeoLocation(useIP);
                }

                return this.location;
            },

            setBrowserLocation: function () {

                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(this.showPosition);
                }

            },

            showPosition: function (position) {
                const latitude  = position.coords.latitude;
                const longitude = position.coords.longitude;
                $.ajax({
                    url: '/totaltools/location/nearest',
                    data: {
                    latitude: latitude,
                    longitude: longitude,
                    use_current_location: true
                    },
                    type: 'GET',
                    dataType: 'json',
                }).done(function (data) {
                    customerData.invalidate(['fulfilment-data']);
                    customerData.reload(['fulfilment-data'], true);
                });
            },

            findGeoLocation: function (useIP) {

                if (xhr && xhr.readystate != 4) {

                    xhr.abort();
                }

                xhr = $.ajax({
                    url: url.build('rest/V1/geo/locate?useIP=' + useIP),
                    type: "GET",
                    dataType: 'json'
                }).done(function (data) {
                    location(JSON.parse(data));
                });

            },

            setLocation: function (data) {
                location(data);
            }
        };
    }
);
