<?php

/**
 * <AUTHOR> Dev Team
 * @copyright Copyright (c) 2018 Totaltools (balanceinternet.com.au)
 * @package Totaltools_Geo
 */

namespace Totaltools\Geo\Model\Maxmind;

use Magento\Framework\HTTP\PhpEnvironment\Request;
use Magento\Framework\Module\Dir\Reader;
use Magento\Directory\Model\RegionFactory;
use Psr\Log\LoggerInterface as Logger;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Encryption\EncryptorInterface;

class City
{
    const DEFAULT_POST_CODE  = '3207';
    const DEFAULT_CITY       = 'PORT MELBOURNE';
    const DEFAULT_COUNTRY    = 'AU';
    const DEFAULT_STATE      = 'VIC';
    const USERNAME_XML_PATH  = 'totaltools_geo_maxmind/general/username';
    const PASSWORD_XML_PATH  = 'totaltools_geo_maxmind/general/password';

    /**
     * @var Request
     */
    protected $_request;

    /**
     * @var Logger
     */
    protected $_logger;

    /**
     * @var DirectoryList
     */
    protected $_directoryList;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    protected $encryptor;

    /**
     * @var \GeoIp2\WebService\Client
     */
    protected $_client;

    /**
     * @var RegionFactory
     */
    protected $regionFactory;

    /**
     * City constructor.
     * @param Request $request
     * @param Logger $logger
     * @param Reader $reader
     * @param DirectoryList $directoryList
     * @param ScopeConfigInterface $scopeConfig
     * @param EncryptorInterface $encryptor
     * @param RegionFactory $regionFactory
     */
    public function __construct(
        Request $request,
        Logger $logger,
        DirectoryList $directoryList,
        ScopeConfigInterface $scopeConfig,
        EncryptorInterface $encryptor,
        RegionFactory $regionFactory
    ) {
        $this->_request = $request;
        $this->_logger = $logger;
        $this->_directoryList = $directoryList;
        $this->scopeConfig  = $scopeConfig;
        $this->encryptor = $encryptor;
        $this->regionFactory = $regionFactory;
    }

    /**
     * Returns post code
     *
     * @return bool|\GeoIp2\Model\City
     */
    public function getPostCode()
    {
        $postCode = self::DEFAULT_POST_CODE;
        return $postCode;
    }

    /**
     * Returns country ISO 2 char code
     *
     * @return bool|\GeoIp2\Model\City
     */
    public function getCountry()
    {
        $country = self::DEFAULT_COUNTRY;
        return $country;
    }

    /**
     * Returns city
     *
     * @return bool|\GeoIp2\Model\City
     */
    public function getCity()
    {
        $city = self::DEFAULT_CITY;
        return $city;
    }

    /**
     * Returns array containing postcode, city and country of customer based on IP
     *
     * @return array
     */
    public function getLocation()
    {
        $location = [
            "postcode" => "",
            "city" => "",
            "country_id" => "AU",
            "state" => "",
            "region" => ""
        ];

        return $location;
    }

    /**
     * Get region data if available from Directory data.
     *
     * @param string $regionCode
     * @param string $countryId
     * @return mixed
     */
    private function getRegion($regionCode, $countryId)
    {
        $region = $this->regionFactory->create()
            ->loadByCode($regionCode, $countryId);

        if ($region->getId()) {
            return [
                'region_id'     => (string) $region->getId(),
                'region_code'   => $region->getData('code'),
                'region'        => $region->getName(),
            ];
        }

        return (object) [];
    }
}
