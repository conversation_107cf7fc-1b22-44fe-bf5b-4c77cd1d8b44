<?php

namespace Totaltools\Geo\Model;

use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Geo\Api\GeoLocateInterface;
use Magento\Framework\Json\Encoder;
use Magento\Directory\Model\RegionFactory;
use Totaltools\Geo\Helper\GeoLocateHelper;
use Magestore\Storelocator\Model\Status;
use Magento\Framework\App\ResourceConnection;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Customer\Helper\Session\CurrentCustomerAddress;
use Magento\Framework\Webapi\Rest\Request;
use Totaltools\Storelocator\Model\StoreRepository;
use Totaltools\Storelocator\Model\ShippingZoneRepository;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\App\CacheInterface;
use Magento\Framework\HTTP\PhpEnvironment\Request as HttpRequest;
use Magento\Company\Model\ResourceModel\Customer;

class GeoLocateModel implements GeoLocateInterface
{
    /**
     * Cache Id prefix
     */
    const CACHE_ID_PREFIX = 'totaltools_geo_maxmind_location_data';

    /**
     * Cache lifetime config path
     */
    const CACHE_LIFETIME = 'totaltools_geo_maxmind/general/sessionlife';

    /**
     * Default South Melbourne Store ID
     */
    const DEFAULT_STORE_ID = 594;

    /**
     * @var GeoLocateHelper
     */
    protected $_geoLocateHelper;

    /**
     * @var Json
     */
    protected $_jsonEncoder;

    /**
     * Resource Connection.
     *
     * @var ResourceConnection
     */
    protected $_resourceConnection;

    /**
     * @var CheckoutSession
     */
    protected $_checkoutSession;

    /**
     * Customer CustomerSession.
     *
     * @var CustomerSession
     */
    protected $_customerSession;

    /**
     * Current Customer Session Address.
     *
     * @var CurrentCustomerAddress
     */
    protected $_currentCustomerAddress;

    /**
     * @var Request
     */
    protected $_request;

    /**
     * @var StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var ShippingZoneRepository
     */
    protected $_shippingZoneRepository;

    /**
     * @var \Totaltools\Storelocator\Helper\Checkout\Data
     */
    protected $_checkoutHelper;

    /**
     * @var CacheInterface
     */
    private $cache;

    /**
     * @var HttpRequest
     */
    protected $httpRequest;

    /**
     * @var \Magento\Company\Model\CustomerFactory
     */
    private $companyCustomerFactory;

    /**
     * @var RegionFactory
     */
    protected $regionFactory;
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;
    /**
     * GeoLocateModel constructor.
     *
     * @param GeoLocateHelper                               $helper
     * @param Json                                          $encoder
     * @param ResourceConnection                            $resourceConnection
     * @param CheckoutSession                               $checkoutSession
     * @param CustomerSession                               $customerSession
     * @param CurrentCustomerAddress                        $currentCustomerAddress
     * @param Request                                       $request
     * @param StoreRepository                               $storeRepository
     * @param ShippingZoneRepository                        $shippingZoneRepository
     * @param \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper
     * @param RegionFactory                                 $regionFactory
     */
    public function __construct(
        GeoLocateHelper $helper,
        Json $encoder,
        ResourceConnection $resourceConnection,
        CheckoutSession $checkoutSession,
        CustomerSession $customerSession,
        CurrentCustomerAddress $currentCustomerAddress,
        Request $request,
        StoreRepository $storeRepository,
        ShippingZoneRepository $shippingZoneRepository,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper,
        \Magento\Company\Model\CustomerFactory $companyCustomerFactory,
        CacheInterface $cache,
        HttpRequest $httpRequest,
        RegionFactory $regionFactory,
        Customer  $companyCustomer
    ) {
        $this->_geoLocateHelper = $helper;
        $this->_jsonEncoder = $encoder;
        $this->_resourceConnection = $resourceConnection;
        $this->_checkoutSession = $checkoutSession;
        $this->_customerSession = $customerSession;
        $this->_currentCustomerAddress = $currentCustomerAddress;
        $this->_request = $request;
        $this->_storeRepository = $storeRepository;
        $this->_shippingZoneRepository = $shippingZoneRepository;
        $this->_checkoutHelper = $checkoutHelper;
        $this->companyCustomerFactory = $companyCustomerFactory;
        $this->cache = $cache;
        $this->httpRequest = $httpRequest;
        $this->regionFactory = $regionFactory;
        $this->companyCustomer = $companyCustomer;
    }

    /**
     * Returns json containing information about customer location based on IP Address
     *
     * @return string json containing information about location
     * @api
     */
    public function locate()
    {
        $forceUseIP = $this->_request->getParam('useIP');

        return $this->getLocation($forceUseIP, true);
    }

    /**
     * Get location information for current user based on session or IP Address.
     *
     * @param bool $forceUseIP
     * @param bool $jsonEncode
     *
     * @return string
     */
    public function getLocation($forceUseIP = false, $jsonEncode = true)
    {
        // Try to load location from checkout session
        $location = $this->_checkoutSession->getData('guest_location_session');
        $cacheId = self::CACHE_ID_PREFIX;

        // If not found in checkout session then load from logged in customers default shipping address
        if (!$location && $this->_customerSession->isLoggedIn()) {
            $shippingAddress = $this->_currentCustomerAddress->getDefaultShippingAddress();

            if ($shippingAddress) {
                $location = [
                    "postcode"   => $shippingAddress->getPostcode(),
                    "city"       => $shippingAddress->getCity(),
                    "region"      => $shippingAddress->getRegion()->getRegionCode() ?? $shippingAddress->getRegion(),
                    "country_id" => $shippingAddress->getCountryId(),
                    "state"     => (object) []
                ];

                if ($shippingAddress->getRegionId()) {
                    $location['state'] = [
                        'region_id'     => $shippingAddress->getRegionId(),
                        'region_code'   => $shippingAddress->getRegion()->getRegionCode(),
                        'region'        => $shippingAddress->getRegion()->getRegion()
                    ];
                }
            }
        } else {
            $getIpData = $this->cache->load($cacheId);

            if ($getIpData !== false) {
                $ip = $this->httpRequest->getClientIp();
                $getIpData = unserialize($getIpData);

                if (isset($getIpData[$ip])) {
                    $location = $getIpData[$ip];
                }
            }
        }

        if (!$location || $forceUseIP) {
            // If still no location is found then get based on IP
            $location = $this->_geoLocateHelper->getLocation();
            $sessionIpData = $this->cache->load($cacheId);
            $ip = $this->httpRequest->getClientIp();
            $ipData = [];

            if ($sessionIpData !== false) {
                $ipData = unserialize($sessionIpData);
            }

            $ipData[$ip] = $location;
            $this->cache->save(serialize($ipData), $cacheId);

            $this->_checkoutSession->setData('guest_location_session', $location);
        }

        $store = $this->_getPreferredStore();

        if ((!$store || $forceUseIP) && !empty($location['postcode'])) {

            /**
             * @var \Magestore\Storelocator\Model\Store $store
             */
            $store = $this->_storeRepository->getClickAndCollectStoreInZone($location['postcode']);

            if (
                $store &&
                $store->getId() &&
                (int) $store->getStatus() === Status::STATUS_ENABLED &&
                (int) $store->getIsVisible() === 1 &&
                (int) $store->getAllowStoreCollection() === 1
            ) {
                $this->_checkoutHelper->setSessionStoreId($store->getId());
            }
        }

        if (!$location || empty($location['postcode']) || !$store) {
            $default = $this->_getDefaultLocationAndStore();

            if (!isset($default['error'])) {
                $location = !$location || empty($location['postcode']) ? $default['location'] : $location;
                $store = $store ?? $default['store'];
            }
        }

        $dummyStore = [
            "storelocator_id" => 0,
            "store_name"      => "",
            "address"         => "",
            "zipcode"         => "",
            "state"           => "",
            "city"            => "",
            "phone"           => ""
        ];

        $location["store"] = $store ? $store->getData() : $dummyStore;

        return $jsonEncode ? $this->_jsonEncoder->serialize($location) : $location;
    }

    /**
     * Get store based on postcode
     *
     * @param $postcode
     *
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface
     */
    protected function _getStoreByPostcode($postcode)
    {
        $zone = $this->_shippingZoneRepository->getZoneByPostCode($postcode);

        if ($zone && $zone->getId()) {
            return $this->_storeRepository->findByZone($zone->getId());
        }

        return false;
    }

    /**
     * Get preferred store based on checkout and customer session.
     *
     * @return bool|\Totaltools\Storelocator\Model\Store
     */
    protected function _getPreferredStore()
    {
        // Try to load pickup store from checkout session.
        $storeId = false;
        if($this->_customerSession->getCustomerId() != null){
            $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId($this->_customerSession->getCustomerId());
            $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
            $b2bCustomer =  array_diff($currentCompanyIds, [0]);
            if (!empty($b2bCustomer)) {
                $storeId = false;
            }else{
                $storeId = $this->_checkoutHelper->getSessionStoreId();
            }
        }
        // If not found in checkout session then try finding customers preferred store.
        if (!$storeId) {

            $preferredStore = $this->_customerSession->getCustomerDataObject()->getCustomAttribute('preferred_store');

            if ($preferredStore) {
                try {
                    $store = $this->_storeRepository->getByErpId($preferredStore->getValue());
                    if ($store->getAllowStoreCollection()) {
                        $storeId = $store->getId();
                        $this->_checkoutHelper->setSessionStoreId($storeId);
                    } else {
                        return false;
                    }
                } catch (NoSuchEntityException $e) {
                    return false;
                }
            }
        }

        if (!$storeId) {
            return false;
        }

        try {
            return $this->_storeRepository->getValidStoreById($storeId);
        } catch (NoSuchEntityException $exception) {
            return false;
        }
    }

    /**
     * Returns default store details
     *
     * @return Array
     */
    protected function _getDefaultLocationAndStore()
    {
        $result = [];

        try {
            $store = $this->_storeRepository->getValidStoreById(self::DEFAULT_STORE_ID);

            if (!$this->_checkoutHelper->getSessionStoreId()) {
                $this->_checkoutHelper->setSessionStoreId($store->getId());
            }

            $_region = $this->getRegion(
                $store->getData('state'),
                $store->getData('country_id'),
                true
            );
            $result['store'] = $store;
            $result['location'] = [
                'city'          => $store->getData('city'),
                'postcode'      => $store->getData('zipcode'),
                'state'         => $store->getData('state'),
                'region'        => $_region['region_code'] ?? $store->getData('state'),
                'region_data'   => $_region,
                'country_id'    => $store->getData('country_id')
            ];
        } catch (NoSuchEntityException $e) {
            $result['error'] = $e->getMessage();
        }

        return $result;
    }

    /**
     * Get region data if available from Directory data.
     *
     * @param string $regionCode
     * @param string $countryId
     * @param int|null $regionId
     * @return mixed
     */
    private function getRegion($regionCode, $countryId, $loadByName = false)
    {
        $regionModel = $this->regionFactory->create();
        $region = $regionModel->loadByCode($regionCode, $countryId);

        if ($loadByName) {
            $region = $regionModel->loadByName($regionCode, $countryId);
        }

        if ($region->getId()) {
            return [
                'region_id'     => (string) $region->getId(),
                'region_code'   => $region->getData('code'),
                'region'        => $region->getName(),
            ];
        }

        return [];
    }
}
