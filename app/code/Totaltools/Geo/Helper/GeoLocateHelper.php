<?php

namespace Totaltools\Geo\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Totaltools\Geo\Model\Maxmind\City;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\Framework\Registry;
use Magento\Store\Model\StoreManagerInterface;

class GeoLocateHelper
{
    /**
     * @var City
     */
    protected $_maxMindCity;

    /**
     * @var ScopeConfigInterface
     */
    protected $_config;

    /**
     * @var CookieManagerInterface
     */
    protected $_cookieManager;

    /**
     * Core registry
     *
     * @var Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var State
     */
    protected $_state;

    /**
     * @var StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * GeoLocateHelper constructor.
     *
     * @param ScopeConfigInterface $scopeConfig
     * @param State $state
     * @param City $maxMindCountry
     * @param StoreManagerInterface $storeManager
     * @param Registry $registry
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        State $state,
        City $maxMindCountry,
        StoreManagerInterface $storeManager,
        Registry $registry
    )
    {
        $this->_config = $scopeConfig;
        $this->_state = $state;
        $this->_maxMindCity = $maxMindCountry;
        $this->_storeManager = $storeManager;
        $this->_coreRegistry = $registry;
    }

    public function getPostCode() {
        return $this->_maxMindCity->getPostCode();
    }

    public function getCountry() {
        return $this->_maxMindCity->getCountry();
    }

    public function getCity() {
        return $this->_maxMindCity->getCity();
    }

    public function getLocation() {
        return $this->_maxMindCity->getLocation();
    }

    public function getCoreRegistry()
    {
        return $this->_coreRegistry;
    }
}
