<?php
/**
 * <AUTHOR> Dev Team
 * @copyright Copyright (c) 2018 Totaltools (balanceinternet.com.au)
 * @package Totaltools_Geo
 */
namespace Totaltools\Geo\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Totaltools\Geo\Model\Maxmind\City;

//use Magento\Framework\App\Area;

class Config
{
    /**
     * @var Country
     */
    protected $_maxMindCountry;

    /**
     * @var ScopeConfigInterface
     */
    protected $_config;

    /**
     * @var \Magento\Framework\Stdlib\CookieManagerInterface
     */
    protected $_cookieManager;

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * Config constructor.
     *
     * @param ScopeConfigInterface $scopeConfig
     * @param State $state
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        State $state,
        City $maxMindCountry,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Registry $registry
    )
    {
        $this->_coreRegistry = $registry;
        $this->_state = $state;
        $this->_config = $scopeConfig;
        $this->_maxMindCountry = $maxMindCountry;
        $this->_storeManager = $storeManager;
    }

    public function getPostCode() {
        return $this->_maxMindCountry->getPostCode();
    }

    public function getCountry() {
        return $this->_maxMindCountry->getCountry();
    }

    public function getCity() {
        return $this->_maxMindCountry->getCity();
    }

    public function getCoreRegistry()
    {
        return $this->_coreRegistry;
    }
}
