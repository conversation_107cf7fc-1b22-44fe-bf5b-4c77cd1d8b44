<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="block-title">
    <strong>
        <span class="text" translate="'My Cart'"></span>
        <span
            class="qty empty"
            text="getCartParam('summary_count')"
            data-bind="css: { empty: !!getCartParam('summary_count') == false },
                       attr: { title: $t('Items in Cart') }">
        </span>
    </strong>
</div>

<div class="block-content">
    <button type="button"
            id="btn-minicart-close"
            class="action close"
            data-action="close"
            data-bind="
                attr: {
                    title: $t('Close')
                },
                click: closeMinicart()
            ">
        <span translate="'Close'"></span>
    </button>

    <if args="getCartParam('summary_count')">
        <div class="progress-content" data-bind="visible: canBeFreeDelivery()">
            <div class="progressbar-text">
                <span data-bind="text: 'Chuck more tools in the cart,', visible: !freeShipping()"></span>
                <span class="bold" data-bind="text: 'Free shipping over $'+freeShippingLevel(), visible: !freeShipping()"></span>
                <span class=" bold" data-bind="visible: freeShipping()">Mate, you've got free shipping!</span>
            </div>
             <div class="progress">
                    <div class="color" data-bind="css: { freeShipping: freeShipping() }, style: { width: percentComplete()  }"></div>
              </div>
        </div>
        <div class="items-total">
            <span class="count" if="maxItemsToDisplay < getCartLineItemsCount()" text="maxItemsToDisplay"></span>
            <translate args="'of'" if="maxItemsToDisplay < getCartLineItemsCount()"></translate>
            <span class="count" text="getCartParam('summary_count')"></span>
                <!-- ko if: (getCartParam('summary_count') > 1) -->
                    <span translate="'Items in Cart'"></span>
                <!--/ko-->
                <!-- ko if: (getCartParam('summary_count') === 1) -->
                    <span translate="'Item in Cart'"></span>
                <!--/ko-->
        </div>

        <each args="getRegion('subtotalContainer')" render=""></each>
        <each args="getRegion('extraInfo')" render=""></each>

        <div class="actions" if="getCartParam('possible_onepage_checkout')">
            <div class="primary">
                <button
                        id="top-cart-btn-checkout"
                        type="button"
                        class="action primary checkout"
                        data-action="close"
                        data-bind="
                            attr: {
                                title: $t('Proceed to Checkout')
                            },
                            click: proceedToCheckout
                        "
                        translate="'Proceed to Checkout'">
                </button>
                <div data-bind="html: getCartParamUnsanitizedHtml('extra_actions')"></div>
            </div>
        </div>
        <div class="mini-cart-static-icons">
            <span class="free-delivery-icon">
                Fast Delivery
            </span>
            <span class="online-return-icon">
                30 Day Online Returns*
            </span>
        </div>
    </if>

    <if args="getCartParam('summary_count')">
        <strong class="subtitle" translate="'Recently added item(s)'"></strong>
        <div data-action="scroll" class="minicart-items-wrapper">
            <ol id="mini-cart" class="minicart-items" data-bind="foreach: { data: getCartItems(), as: 'item' }">
                <each args="$parent.getRegion($parent.getItemRenderer(item.product_type))"
                      render="{name: getTemplate(), data: item, afterRender: function() {$parents[1].initSidebar()}}"></each>
            </ol>
        </div>
    </if>

    <ifnot args="getCartParam('summary_count')">
        <strong class="subtitle empty"
                translate="'You have no items in your shopping cart.'"></strong>
        <if args="getCartParam('cart_empty_message')">
            <p class="minicart empty text" text="getCartParam('cart_empty_message')"></p>
            <div class="actions">
                <div class="secondary">
                    <a class="action viewcart" data-bind="attr: {href: shoppingCartUrl}">
                        <span translate="'View and Edit Cart'"></span>
                    </a>
                </div>
            </div>
        </if>
    </ifnot>
    <div id="minicart-widgets" class="minicart-widgets" if="regionHasElements('promotion')">
        <each args="getRegion('promotion')" render=""></each>
    </div>
</div>
<div class="actions minicart-actions" if="getCartParam('summary_count')">
    <div class="primary view-cart">
        <a class="action link" data-bind="attr: { href: window.checkout.shoppingCartUrl }">
            <span translate="'View Cart'"></span>
        </a>
    </div>
    <div class="secondary empty-cart">
        <a href="javascript:void(0)" class="action link" id="empty-minicart" data-bind="click: emptyCartAction">
            <span translate="'Clear Cart'"></span>
        </a>
    </div>
</div>
<each args="getRegion('sign-in-popup')" render=""></each>
