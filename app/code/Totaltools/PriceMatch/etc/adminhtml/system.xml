<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="totaltools_zendesk" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Zendesk</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Pronto::config_zendesk</resource>
              <group id="pricematch" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Price Match Form Attributes</label>
                <field id="zendesk_pricematch_form_url" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Zendesk PriceMatch Form Url</label>
                </field>
                <field id="ticket_form_id" translate="label" type="text" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zendesk Ticket Form ID</label>
                </field>

                <field id="product_name_inputid" translate="label" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zendesk Product Name InputID</label>
                </field>
                <field id="product_url_inputid" translate="label" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zendesk Product URL InputID</label>
                </field>
                <field id="product_price_inputid" translate="label" type="text" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zendesk Product Price InputID</label>
                </field>
                <field id="product_sku_inputid" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zendesk Product SKU InputID</label>
                </field>
                <field id="product_brand_inputid" translate="label" type="text" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zendesk Product Brand InputID</label>
                </field>
                <field id="product_category_inputid" translate="label" type="text" sortOrder="120" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Zendesk Product Category InputID</label>
                </field>

            </group>
            </section>
    </system>
</config>
