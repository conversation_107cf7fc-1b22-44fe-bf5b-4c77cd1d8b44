<?php

/**
 *
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\PriceMatch\Controller\Index;

use Totaltools\PriceMatch\Model\ConfigInterface;
use Totaltools\PriceMatch\Model\MailInterface;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Framework\Exception\LocalizedException;
use Psr\Log\LoggerInterface;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\DataObject;
use Magento\Framework\Controller\Result\JsonFactory as MagentoJsonFactory;
use Totaltools\PriceMatch\Controller\Index as IndexController;

class Post extends IndexController
{
    /**
     * @var DataPersistorInterface
     */
    private $dataPersistor;

    /**
     * @var Context
     */
    private $context;

    /**
     * @var MailInterface
     */
    private $mail;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var Array
     */
    private $errors;

    /**
     * @var JsonFactory
     */
    protected $resultJsonFactory;

    /**
     * Post constructor.
     * @param Context $context
     * @param ConfigInterface $contactsConfig
     * @param MailInterface $mail
     * @param DataPersistorInterface $dataPersistor
     * @param LoggerInterface|null $logger
     * @param MagentoJsonFactory $resultJsonFactory
     */
    public function __construct(
        Context $context,
        ConfigInterface $contactsConfig,
        MailInterface $mail,
        DataPersistorInterface $dataPersistor,
        LoggerInterface $logger = null,
        MagentoJsonFactory $resultJsonFactory
    ) {
        parent::__construct($context, $contactsConfig);
        $this->context = $context;
        $this->mail = $mail;
        $this->dataPersistor = $dataPersistor;
        $this->logger = $logger ? : ObjectManager::getInstance()->get(LoggerInterface::class);
        $this->resultJsonFactory = $resultJsonFactory;
    }

    /**
     * Post user question
     *
     * @return Redirect
     */
    public function execute()
    {
        $result = $this->resultJsonFactory->create();
        $response = [];

        if (!$this->getRequest()->isPost() || !$this->getRequest()->isAjax()) {
            $response = [
                'success' => false,
                'message' => __('Oops! Something went wrong. Please try again later.')
            ];
            $result->setData($response);
            return $result;
        }

        try {
            $params = $this->validatedParams();
            if (!empty($this->errors)) {
                $message = "";
                foreach ($this->errors as $e) {
                    $message .= __($e);
                }

                $this->messageManager->addErrorMessage($message);
                $this->dataPersistor->set('price_match', $this->getRequest()->getParams());
                $response = [
                    'success' => false,
                    'message' => $message
                ];
            } else {
                $this->sendEmail($params);
                $message = __('Thanks for contacting us with your price match request. We\'ll respond to you very soon.');
                $this->messageManager->addSuccessMessage($message);
                $this->dataPersistor->clear('price_match');

                $response = [
                    'success' => true,
                    'message' => $message
                ];
            }
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
            $this->dataPersistor->set('price_match', $this->getRequest()->getParams());
            $response = [
                'success' => false,
                'message' => $e->getMessage()
            ];
        } catch (\Exception $e) {
            $this->logger->critical($e);
            $message = __('An error occurred while processing your form. Please try again later.');
            $this->messageManager->addErrorMessage($message);
            $this->dataPersistor->set('price_match', $this->getRequest()->getParams());
            $response = [
                'success' => false,
                'message' => $message
            ];
        }

        $result->setData($response);

        return $result;
    }

    /**
     * @param array $post Post data from contact form
     * @return void
     */
    private function sendEmail($post)
    {
        $this->mail->send(
            $post['email'],
            ['data' => new DataObject($post)]
        );
    }

    /**
     * @return array
     * @throws \Exception
     */
    private function validatedParams()
    {
        $request = $this->getRequest();

        if (trim($request->getParam('name')) === '') {
            $this->errors['name'] = __('Name is missing');
        }
        if (false === \strpos($request->getParam('email'), '@')) {
            $this->errors['email'] = __('Invalid email address');
        }
        if (trim($request->getParam('product')) === '') {
            $this->errors['product'] = __('Provide a product for price match');
            // throw new LocalizedException(__('Provide a product for price match'));
        }
        if (trim($request->getParam('competitor_link')) === '') {
            $this->errors['competitor_link'] = __('Provide a competitor link for price match');
        }
        if (trim($request->getParam('competitor_price')) === '') {
            $this->errors['competitor_price'] = __('Provide the competitor price to be matched');
        }
        if (trim($request->getParam('hideit')) !== '') {
            throw new \Exception();
        }
        if (trim($request->getParam('postcode')) === '') {
            $this->errors['postcode'] = trim($request->getParam('shipping_type')) === 'Delivery' ?
                __('Provide your postcode where you want items to be delivered')
                : __('Provide your postcode from where you want items to collect');
        }
        if (trim($request->getParam('shipping_type')) === '') {
            $this->errors['shipping_type'] = __('Provide the competitor selected shipping method');
        }
        if (trim($request->getParam('shipping_cost')) === '' &&
            trim($request->getParam('shipping_type')) === 'Delivery') {
            $this->errors['shipping_cost'] = __('Provide the competitor shipping cost');
        }

        return $request->getParams();
    }
}
