<?php

/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\PriceMatch\Controller;

use Totaltools\PriceMatch\Model\ConfigInterface;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\NotFoundException;

/**
 * Contact module base controller
 */
abstract class Index extends \Magento\Framework\App\Action\Action
{
    /**
     * Recipient email config path
     */
    const XML_PATH_EMAIL_RECIPIENT = ConfigInterface::XML_PATH_EMAIL_RECIPIENT;

    /**
     * Sender email config path
     */
    const XML_PATH_EMAIL_SENDER = ConfigInterface::XML_PATH_EMAIL_SENDER;

    /**
     * Email template config path
     */
    const XML_PATH_EMAIL_TEMPLATE = ConfigInterface::PRICE_MATCH_EMAIL_TEMPLATE_IDENTIFIER;

    /**
     * @var ConfigInterface
     */
    private $pricematchConfig;

    /**
     * @param Context $context
     * @param ConfigInterface $pricematchConfig
     */
    public function __construct(
        Context $context,
        ConfigInterface $pricematchConfig
    ) {
        parent::__construct($context);
        $this->pricematchConfig = $pricematchConfig;
    }

    /**
     * Dispatch request
     *
     * @param RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function dispatch(RequestInterface $request)
    {
        return parent::dispatch($request);
    }
}
