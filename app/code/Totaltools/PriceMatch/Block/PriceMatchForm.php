<?php
/**
 * <AUTHOR> Dev Team
 * @copyright  © 2019 Totaltools. All rights reserved.
 */
namespace Totaltools\PriceMatch\Block;

use Magento\Framework\View\Element\Template;
use Magento\Catalog\Model\Product;
use Totaltools\PriceMatch\Helper\Data as PriceMatchHelper;
use Totaltools\Megamenu\Block\Crumbs;

/**
 * Main Price match form block
 *
 * @api
 */
class PriceMatchForm extends Template
{    
    /**
     * PriceMatchHelper
     *
     * @var PriceMatchHelper
     */
    protected $helper;
        
    /**
     * product
     *
     * @var Product
     */
    protected $product;

    /**
     * @var \Magento\Framework\App\Request\Http
     */
    protected $request;

    /**
     * @param Template\Context $context
     * @param  \Magento\Framework\App\Request\Http $request
     * @param array $data
     */
    public function __construct(
        Template\Context $context, 
        PriceMatchHelper $helper,
        Product $product,
        \Magento\Framework\App\Request\Http $request,
        array $data = [])
    {
        $this->helper = $helper;
        $this->product = $product;
        $this->request = $request;
        parent::__construct($context, $data);
    }

    /**
     * Returns action url for price match form
     *
     * @return string
     */
    public function getFormAction()
    {
        return $this->getUrl('pricematch/index/post', ['_secure' => true]);
    }

    /**
     * Generate zendesk url for current product url for price match form
     *
     * @return string
     */
    public function getPriceMatchUrl()
    {
        $baseUrl = $this->helper->getZendeskFormUrl();
        $ticketFormId = $this->helper->getTicketFormId();
        $_product = $this->getProduct();
        $productName = $_product->getName();
        $url = $_product->getProductUrl();
        $price = $_product->getFinalPrice();
        $sku = $_product->getSku();
        $brand = $_product->getAttributeText('brand');
        $prontoClass =  $_product->getProntoClass();
        $params = [
            'ticket_form_id' => $ticketFormId,
            $this->helper->getFieldParam(PriceMatchHelper::CONFIG_PATH_ZENDESK_PRODUCT_NAME_INPUTID) => $productName,
            $this->helper->getFieldParam(PriceMatchHelper::CONFIG_PATH_ZENDESK_PRODUCT_URL_INPUTID) => $url,
            $this->helper->getFieldParam(PriceMatchHelper::CONFIG_PATH_ZENDESK_PRODUCT_PRICE_INPUTID) => $price,
            $this->helper->getFieldParam(PriceMatchHelper::CONFIG_PATH_ZENDESK_PRODUCT_SKU_INPUTID) => $sku,
            $this->helper->getFieldParam(PriceMatchHelper::CONFIG_PATH_ZENDESK_PRODUCT_BRAND_INPUTID) => $brand,
            $this->helper->getFieldParam(PriceMatchHelper::CONFIG_PATH_ZENDESK_PRODUCT_CATEGORY_INPUTID) => $prontoClass,
        ];

        return $baseUrl . '?' . http_build_query($params);
    }

    /**
     * Retrieve current product model
     *
     * @return \Magento\Catalog\Model\Product
     */
    public function getProduct()
    {
        return $this->helper->getCurrentProduct();
    }

}
