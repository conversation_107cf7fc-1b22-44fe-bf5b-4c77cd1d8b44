<?php

/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\PriceMatch\Helper;

use Totaltools\PriceMatch\Model\ConfigInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Helper\View as CustomerViewHelper;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\Registry;
use Magento\Framework\Pricing\Helper\Data as PriceHelper;
use Magento\Catalog\Helper\Data as CatalogHelper;

/**
 * PriceMatch base helper
 *
 * 
 */
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    const CONFIG_PATH_ZENDESK_TICKET_FORM_ID = 'totaltools_zendesk/pricematch/ticket_form_id';
    const CONFIG_PATH_ZENDESK_PRODUCT_NAME_INPUTID = 'totaltools_zendesk/pricematch/product_name_inputid';
    const CONFIG_PATH_ZENDESK_PRODUCT_URL_INPUTID = 'totaltools_zendesk/pricematch/product_url_inputid';
    const CONFIG_PATH_ZENDESK_PRODUCT_PRICE_INPUTID = 'totaltools_zendesk/pricematch/product_price_inputid';
    const CONFIG_PATH_ZENDESK_PRODUCT_SKU_INPUTID = 'totaltools_zendesk/pricematch/product_sku_inputid';
    const CONFIG_PATH_ZENDESK_PRODUCT_BRAND_INPUTID = 'totaltools_zendesk/pricematch/product_brand_inputid';
    const CONFIG_PATH_ZENDESK_PRODUCT_CATEGORY_INPUTID = 'totaltools_zendesk/pricematch/product_category_inputid';
    const CONFIG_PATH_ZENDESK_FORM_URL = 'totaltools_zendesk/pricematch/zendesk_pricematch_form_url';


    /**
     * Customer session
     *
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * @var \Magento\Customer\Helper\View
     */
    protected $_customerViewHelper;

    /**
     * @var DataPersistorInterface
     */
    private $dataPersistor;

    /**
     * @var  \Magento\Framework\Registry
     */
    protected $_registry;

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    private $_priceHelper;

    /**
     * @var CatalogHelper
     */
    protected $catalogHelper = null;

    /**
     * @var array
     */
    private $postData = null;

    /**
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Customer\Model\Session $customerSession 
     * @param \Magento\Framework\Pricing\Helper\Data $priceHelper
     * @param CustomerViewHelper $customerViewHelper
     * @param Registry $registry
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Customer\Model\Session $customerSession,
        PriceHelper $priceHelper,
        CatalogHelper $catalogHelper,
        CustomerViewHelper $customerViewHelper,
        Registry $registry
    ) {
        $this->_customerSession = $customerSession;
        $this->_priceHelper = $priceHelper;
        $this->_customerViewHelper = $customerViewHelper;
        $this->_registry = $registry;
        $this->catalogHelper = $catalogHelper;
        parent::__construct($context);
    }

    /**
     * Get user name
     *
     * @return string
     */
    public function getUserName()
    {
        if (!$this->_customerSession->isLoggedIn()) {
            return '';
        }
        /**
         * @var \Magento\Customer\Api\Data\CustomerInterface $customer
         */
        $customer = $this->_customerSession->getCustomerDataObject();

        return trim($this->_customerViewHelper->getCustomerName($customer));
    }

    /**
     * Get user email
     *
     * @return string
     */
    public function getUserEmail()
    {
        if (!$this->_customerSession->isLoggedIn()) {
            return '';
        }
        /**
         * @var CustomerInterface $customer
         */
        $customer = $this->_customerSession->getCustomerDataObject();

        return $customer->getEmail();
    }

    /**
     * Get value from POST by key
     *
     * @param string $key
     * @return string
     */
    public function getPostValue($key)
    {
        if (null === $this->postData) {
            $this->postData = (array)$this->getDataPersistor()->get('price_match');
            $this->getDataPersistor()->clear('price_match');
        }

        if (isset($this->postData[$key])) {
            return (string)$this->postData[$key];
        }

        return '';
    }

    /**
     * Get Data Persistor
     *
     * @return DataPersistorInterface
     */
    private function getDataPersistor()
    {
        if ($this->dataPersistor === null) {
            $this->dataPersistor = ObjectManager::getInstance()
                ->get(DataPersistorInterface::class);
        }

        return $this->dataPersistor;
    }

    /**
     * Get currently active product
     *
     * @return \Magento\Catalog\Model\Product|null
     */
    public function getCurrentProduct()
    {
        return $this->catalogHelper->getProduct();
    }

    /**
     * Getter for price helper
     * @return \Magento\Framework\Pricing\Helper\Data
     */
    public function getPriceHelper()
    {
        return $this->_priceHelper;
    }

    /**
     * Retrieve Zendesk Form URL
     */
    public function getZendeskFormUrl($storeId = null)
    {
        return $this->scopeConfig->getValue(self::CONFIG_PATH_ZENDESK_FORM_URL, ScopeInterface::SCOPE_STORE, $storeId);
    }

    public function getTicketFormId($storeId = null)
    {
        return $this->scopeConfig->getValue(self::CONFIG_PATH_ZENDESK_TICKET_FORM_ID, ScopeInterface::SCOPE_STORE, $storeId);
    }

    public function getFieldParam($configPath, $storeId = null)
    {
        $id = $this->scopeConfig->getValue($configPath, ScopeInterface::SCOPE_STORE, $storeId);
        return $id ? 'tf_' . $id : null;
    }  
    
}
