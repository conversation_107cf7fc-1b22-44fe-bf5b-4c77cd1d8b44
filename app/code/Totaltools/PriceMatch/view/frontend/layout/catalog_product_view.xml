<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="product.info.addto">
            <block class="Totaltools\PriceMatch\Block\PriceMatchForm" name="price.match.trigger" template="Totaltools_PriceMatch::pricematch.phtml" after="-"/>
            <block class="Totaltools\PriceMatch\Block\PriceMatchForm" name="price.match.form" template="Totaltools_PriceMatch::form.phtml">
                <container name="form.additional.info">
                    <block class="Magento\ReCaptchaUi\Block\ReCaptcha" name="recaptcha" after="-" template="Magento_ReCaptchaFrontendUi::recaptcha.phtml">
                        <arguments>
                            <argument name="recaptcha_for" xsi:type="string">pricematch_form</argument>
                            <argument name="jsLayout" xsi:type="array">
                                <item name="components" xsi:type="array">
                                    <item name="recaptcha" xsi:type="array">
                                        <item name="component" xsi:type="string">Magento_ReCaptchaFrontendUi/js/reCaptcha</item>
                                    </item>
                                </item>
                            </argument>
                        </arguments>
                    </block>
                </container>
            </block>
        </referenceContainer>
        <referenceContainer name="product.info.price">
            <block class="Totaltools\PriceMatch\Block\PriceMatchForm" name="price.match.below.price" template="Totaltools_PriceMatch::pricematch-popup.phtml" before="payinfour.product"/>
        </referenceContainer>
    </body>
</page>
