<?php
/**
 * <AUTHOR> Dev Team
 * @package Totaltools_PriceMatch
 * @copyright © 2019 Totaltools. All rights reserved.
 */

// @codingStandardsIgnoreFile
/** @var \Totaltools\PriceMatch\Block\PriceMatchForm $block */

$helper = $this->helper('Totaltools\PriceMatch\Helper\Data');
?>
<div class="price-match" id="price-match" style="display: none;">
    <form class="form price-match-form"
        action="<?php echo $block->escapeUrl($block->getFormAction()) ?>"
        id="price-match-form"
        method="post"
        data-hasrequired="<?php echo $block->escapeHtmlAttr(__('* Required Fields')) ?>"
        data-mage-init='{"validation":{}}'>
        <fieldset class="fieldset">
            <div class="field note no-label"><?php echo $block->escapeHtml(__('Should you find a lower advertised price from the identically stocked product elsewhere, we will happily match the price. Subject to Terms & Conditions')) ?></div>
            <div class="field choice name required">
                <label class="label" for="name"><span><?php echo $block->escapeHtml(__('Your Name')) ?></span></label>
                <div class="control">
                    <input name="name" id="name" title="<?php echo $block->escapeHtmlAttr(__('Your Name')) ?>" value="<?php echo $block->escapeHtmlAttr($helper->getPostValue('name') ? : $helper->getUserName()) ?>" class="input-text" type="text" data-validate="{required:true}"/>
                </div>
            </div>
            <div class="field choice email required">
                <label class="label" for="email"><span><?php echo $block->escapeHtml(__('Email')) ?></span></label>
                <div class="control">
                    <input name="email" id="email" title="<?php echo $block->escapeHtmlAttr(__('Email')) ?>" value="<?php echo $block->escapeHtmlAttr($helper->getPostValue('email') ? : $helper->getUserEmail()) ?>" class="input-text" type="email" data-validate="{required:true, 'validate-email':true}"/>
                </div>
            </div>
            <div class="field choice telephone option">
                <label class="label" for="telephone"><span><?php echo $block->escapeHtml(__('Contact Number')) ?><em>(<?php echo $block->escapeHtml(__('Optional')) ?>)</em></span></label>
                <div class="control">
                    <input name="telephone" id="telephone" title="<?php echo $block->escapeHtmlAttr(__('Contact Number')) ?>" value="<?php echo $block->escapeHtmlAttr($helper->getPostValue('telephone')) ?>" class="input-text" type="tel" />
                </div>
            </div>
            <div class="field choice product required">
                <label class="label" for="product"><span><?php echo $block->escapeHtml(__('Product Name')) ?></span></label>
                <div class="control">
                    <input name="product" id="product" title="<?php echo $block->escapeHtmlAttr(__('Product Name')) ?>" value="<?php echo  $block->escapeHtmlAttr(!is_null($helper->getCurrentProduct()) ? $helper->getCurrentProduct()->getName() : '' ) ?>" class="input-text" type="text" aria-disabled="true" readonly="readonly" />
                </div>
            </div>
            <div class="field choice product-attributes" style="display: none;">
                <input type="hidden" name="product_sku" aria-hidden="true" aria-required="false" value="<?php echo $block->escapeHtmlAttr(!is_null($helper->getCurrentProduct()) ? $helper->getCurrentProduct()->getSku() : '') ?>" />

                <input type="hidden" name="product_url" aria-hidden="true" aria-required="false" value="<?php echo $block->escapeHtmlAttr(!is_null($helper->getCurrentProduct()) ? $helper->getCurrentProduct()->getProductUrl() : '') ?>" />

                <input type="hidden" name="product_price" aria-hidden="true" aria-required="false" value="<?php echo /* @noEscape */ ($helper->getPriceHelper()->currency(!is_null($helper->getCurrentProduct()) ? $helper->getCurrentProduct()->getPrice() : 0, true, false)) ?>" />
            </div>
            <div class="field choice competitor_link required">
                <label class="label" for="competitor_link"><span><?php echo $block->escapeHtml(__('Competitor product link (insert web address)')) ?></span></label>
                <div class="control">
                    <input name="competitor_link" id="competitor_link" title="<?php echo $block->escapeHtmlAttr(__('Competitor Link')) ?>" value="<?php echo $block->escapeHtmlAttr($helper->getPostValue('competitor_link')) ?>" class="input-text" type="text" placeholder="<?php echo $block->escapeHtmlAttr(__('e.g https://www.someretailer.com/some_product')) ?>" data-validate="{required:true, 'validate-url':true}" />
                </div>
            </div>
            <div class="field choice competitor_price required">
                <label class="label" for="competitor_price"><span><?php echo $block->escapeHtml(__('Competitor Price')) ?></span></label>
                <div class="control">
                    <input name="competitor_price" id="competitor_price" title="<?php echo $block->escapeHtmlAttr(__('Competitor Price')) ?>" value="<?php echo $block->escapeHtmlAttr($helper->getPostValue('competitor_price')) ?>" class="input-text" type="text" placeholder="<?php echo $block->escapeHtmlAttr(__('$AUD')) ?>" data-validate="{required:true, number: true}" />
                </div>
            </div>
            <div class="field choice postcode required">
                <label class="label" for="postcode"><span><?php echo $block->escapeHtml(__('Enter your Postcode')) ?></span></label>
                <div class="control">
                    <input name="postcode" id="postcode" title="<?php echo $block->escapeHtmlAttr(__('Enter your Postcode')) ?>" value="<?php echo $block->escapeHtmlAttr($helper->getPostValue('postcode')) ?>" class="input-text" type="text" placeholder="<?php echo $block->escapeHtmlAttr(__('Enter your Postcode')) ?>" data-validate="{required:true}" />
                </div>
            </div>
            <div class="field choice shipping_type required">
                <label class="label" for="shipping_type"><span><?php echo $block->escapeHtml(__('Shipping Method')) ?></span></label>
                <div class="control">
                    <select onchange="showDiv('shippingCost', this)" id="shipping_type" name="shipping_type" data-role="shipping_type" class="input-text" title="<?php echo $block->escapeHtmlAttr(__('Shipping Method')) ?>" data-validate="{required:true}">
                        <option value="Delivery"><?php echo $block->escapeHtml(__('Delivery')) ?></option>
                        <option value="Click & Collect"><?php echo $block->escapeHtml(__('Click & Collect')) ?></option>
                    </select>
                </div>
            </div>
            <div class="field choice shipping_cost" id="shippingCost">
                <label class="label" for="shipping_cost"><span><?php echo $block->escapeHtml(__('Competitor shipping cost')) ?></span></label>
                <div class="control">
                    <input name="shipping_cost" id="shipping_cost" title="<?php echo $block->escapeHtmlAttr(__('Competitor shipping cost')) ?>" value="<?php echo $block->escapeHtmlAttr($helper->getPostValue('shipping_cost')) ?>" class="input-text" type="text" placeholder="<?php echo $block->escapeHtmlAttr(__('$AUD')) ?>" />
                </div>
            </div>
            <div class="field choice comment option">
                <label class="label" for="comment"><span><?php echo $block->escapeHtml(__('Additional Information')) ?><em>(<?php echo $block->escapeHtml(__('Optional')) ?>)</em></span></label>
                <div class="control">
                    <textarea name="comment" id="comment" title="<?php echo $block->escapeHtmlAttr(__('Additional Information')) ?>" class="input-text" cols="5" rows="3" placeholder="<?php echo $block->escapeHtmlAttr(__('Add comments')) ?>"><?php echo $block->escapeHtml($helper->getPostValue('comment')) ?></textarea>
                </div>
            </div>
            <?php echo /* @noEscape */ $block->getChildHtml('form.additional.info') ?>
        </fieldset>
        <div class="actions-toolbar">
            <div class="primary field">
                <input type="hidden" name="hideit" id="hideit" value="" />
                <button type="submit" title="<?php echo $block->escapeHtmlAttr(__('Submit')) ?>" class="action submit button-outline button-wide">
                    <span><?php echo $block->escapeHtml(__('Submit')) ?></span>
                </button>
            </div>
        </div>
    </form>
</div>
<script defer>
    require([
        'jquery',
        'Magento_Ui/js/modal/modal',
        'mage/mage'
    ], function($) {
        'use strict';

        /**
         * Price match popup initialization
         */
        var options = {
            type: 'popup',
            responsive: true,
            modalClass: 'price-match-modal modal-md',
            trigger: '#pricematch-trigger',
            innerScroll: true,
            title: '<?= $block->escapeHtml(__('Price Match')) ?>',
            buttons: []
        };

        $('#price-match').modal(options);

        /**
         * Form validation and submission handling
         */
        var $priceMatchForm = $('#price-match-form');
        $priceMatchForm.mage('validation', {});

        $priceMatchForm.on('submit', function(e){
            e.preventDefault();

            if ($priceMatchForm.validation('isValid')) {
                // Disable submit button when ajax request is in action
                var $submit = $(this).find('button[type="submit"]');

                $.ajax({
                    url: $priceMatchForm.attr('action'),
                    type: 'POST',
                    showLoader : true,
                    dataType: 'json',
                    data: $(this).serialize(),
                    beforeSend: function() {
                        $submit.attr("disabled", true);
                    },
                    error: function(res) {
                        $submit.attr("disabled", false);
                    },
                    success: function(res) {
                        if (res.success) {
                            $('#price-match').modal('closeModal');
                            $priceMatchForm.trigger("reset");
                        }
                    },
                    complete: function(xhr, status) {
                        $submit.attr("disabled", false);
                    }
                });
            }
        });
    });
    function showDiv(divId, element) {
        document.getElementById(divId).style.display = element.value === 'Delivery' ? 'block' : 'none';
    }
</script>
