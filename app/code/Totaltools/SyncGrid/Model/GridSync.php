<?php
/**
 * @category  Totaltools
 * @package   Totaltools_GridSync
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\SyncGrid\Model;

use Magento\Framework\App\ResourceConnection;
use Psr\Log\LoggerInterface;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Sales\Model\ResourceModel\Grid;

class GridSync
{
    const MAIN_TABLENAME = 'sales_order';
    const GRID_TABLENAME = 'sales_order_grid';

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var AdapterInterface
     */
    private $connection;

    /**
     * @var ResourceConnection
     */
    private ResourceConnection $resourceConnection;

    /**
     * @var Grid
     */
    private Grid $orderGrid;

    /**
     * @var string
     */
    protected $gridTableName;

    /**
     * @var string
     */
    protected $mainTableName;

    /**
     * @param LoggerInterface $logger
     * @param ResourceConnection $resourceConnection
     * @param Grid $entityGrid
     * @param $mainTableName
     * @param $gridTableName
     */
    public function __construct(
        LoggerInterface $logger,
        ResourceConnection $resourceConnection,
        Grid $entityGrid,
        $mainTableName,
        $gridTableName
    )
    {
        $this->logger = $logger;
        $this->resourceConnection = $resourceConnection;
        $this->orderGrid = $entityGrid;
        $this->mainTableName = $mainTableName;
        $this->gridTableName = $gridTableName;
    }

    public function execute()
    {
        $orderEntityIdList = $this->getIds($this->mainTableName, $this->gridTableName);
        if (is_array($orderEntityIdList) && !empty($orderEntityIdList)) {
            foreach($orderEntityIdList as $entityId) {
                try {
                    $result = $this->orderGrid->refresh($entityId);
                } catch (Exception $e) {
                    $this->logger->error($this->mainTableName . " : " . $this->gridTableName . " sync order cronjob orders exception: " . $e->getMessage());
                }
            }
            $this->logger->info("Ordersync cronjob for table: ". $this->mainTableName . " : " . $this->gridTableName . " : " . json_encode($orderEntityIdList));
        }
        return $this;
    }

    /**
     * @param $mainTableName
     * @param $gridTableName
     * @return array
     */
    protected function getIds($mainTableName, $gridTableName) {
        $mainTableName = $this->resourceConnection->getTableName($mainTableName);
        $gridTableName = $this->resourceConnection->getTableName($gridTableName);

        $select = $this->getConnection()->select()
            ->from($mainTableName, [$mainTableName . '.entity_id'])
            ->joinLeft(
                [$gridTableName => $gridTableName],
                sprintf(
                    '%s.%s = %s.%s',
                    $mainTableName,
                    'entity_id',
                    $gridTableName,
                    'entity_id'
                ),
                []
            )
            ->where($gridTableName . '.entity_id IS NULL');

        return $this->getConnection()->fetchAll($select, [], \Zend_Db::FETCH_COLUMN);
    }

    /**
     * Returns connection.
     *
     * @return AdapterInterface
     */
    private function getConnection()
    {
        if (!$this->connection) {
            $this->connection = $this->resourceConnection->getConnection();
        }

        return $this->connection;
    }
}
