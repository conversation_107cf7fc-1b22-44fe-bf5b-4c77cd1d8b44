/**
 * @category    Totaltools
 * @package     Totaltools_Label
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

define(['jquery', 'mage/utils/wrapper'], function ($, wrapper) {
    'use strict';

    return function (reload) {
        reload.reloadLabels = wrapper.wrap(
            reload.reloadLabels,
            function (reloadLabelsOriginal, container, data) {
                if ('string' === typeof container) {
                    container = $(container);
                }

                reloadLabelsOriginal(container, data);
            }
        );

        return reload;
    };
});
