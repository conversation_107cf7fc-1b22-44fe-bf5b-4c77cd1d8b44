//
//  Common
//  _____________________________________________

& when (@media-common =true) {
    .product-image-wrapper.has-hover-image {

        .product-image-photo,
        .product-hover-image {
            transition: all .3s linear;
            position: absolute;
        }

        &.hover-style {
            &-slide-left {
                .product-hover-image {
                    left: 100%;
                    right: auto;
                    width: 100%;
                    aspect-ratio: 1/1;
                    object-fit: contain;
                    margin: auto 4%;
                    max-width: 92%;
                    top: 0;
                    bottom: 0;
                }

                &:hover {

                    .product-image-photo {
                        left: -100% !important;
                        right: auto !important;
                    }

                    .product-hover-image {
                        left: 0;
                    }
                }
            }

            &-slide-right {
                .product-hover-image {
                    left: -100%;
                    right: auto;
                    width: 100%;
                    aspect-ratio: 1/1;
                    object-fit: contain;
                    margin: auto 4%;
                    max-width: 92%;
                    top: 0;
                    bottom: 0;
                }

                &:hover {
                    .product-image-photo {
                        left: 100% !important;
                        right: auto !important;
                    }

                    .product-hover-image {
                        left: 0;
                    }
                }
            }
        }
    }
}