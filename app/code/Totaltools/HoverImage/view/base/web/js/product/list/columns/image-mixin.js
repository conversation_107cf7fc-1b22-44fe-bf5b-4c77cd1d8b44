define([
    'underscore'
], function (_) {
    'use strict';

    return function (ImageColumn) {
        return ImageColumn.extend({
            /**
             * Find hover image by code in scope of images
             *
             * @param {Object} images
             * @returns {*|T}
             */
            getHoverImage: function (images) {
                return _.filter(images, function (image) {
                    return 'recently_viewed_products_grid_hover_image' === image.code;
                }, this).pop();
            },

            /**
             * Get hover image path.
             *
             * @param {Object} row
             * @return {String}
             */
            getHoverImageUrl: function (row) {
                var hoverImage = this.getHoverImage(row.images);
                if (hoverImage && hoverImage.url && !hoverImage.url.includes('/placeholder/') && window.hoverImageConfig.enabled) {
                    return hoverImage.url;
                }
                return false;
            }
        });
    };
});
