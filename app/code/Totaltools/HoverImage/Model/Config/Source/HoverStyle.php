<?php
/**
 * Copyright © Totaltools. All rights reserved.
 * See LICENSE.txt for license details.
 */

namespace Totaltools\HoverImage\Model\Config\Source;

use Magento\Framework\Data\OptionSourceInterface;

class HoverStyle implements OptionSourceInterface
{
    const SLIDE_LEFT = 'slide-left';
    const SLIDE_RIGHT = 'slide-right';

    /**
     * @return array[]
     */
    public function toOptionArray()
    {
        return [
            ['value' => self::SLIDE_LEFT, 'label' => __('Slide Left')],
            ['value' => self::SLIDE_RIGHT, 'label' => __('Slide Right')]
        ];
    }
}
