<?php

namespace Totaltools\HoverImage\Block;

use Totaltools\HoverImage\Helper\Data;

class HoverImageConfig extends \Magento\Framework\View\Element\Template
{
    /**
     * @var Data
     */
    protected $helper;

    /**
     * HoverImageConfig constructor.
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Totaltools\HoverImage\Helper\Data $helper
     */

    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        Data $helper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->helper = $helper;
    }

    public function isEnabled()
    {
        return $this->helper->isEnabled();
    }

    /**
     * @return string
     */
    public function getHoverStyle()
    {
        return $this->helper->getHoverStyle();
    }

}
