<?php
/**
 * Loadstores
 *
 * @category  Totaltools
 * @package   Totaltools_StorelocatorGraphql
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\StorelocatorGraphql\Model\Resolver;

use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\GraphQl\Config\Element\Field;
use Magento\Framework\GraphQl\Exception\GraphQlInputException;
use Magento\Framework\GraphQl\Exception\GraphQlNoSuchEntityException;
use Magento\Framework\GraphQl\Query\ResolverInterface;
use Magento\Framework\GraphQl\Schema\Type\ResolveInfo;

class Loadstores implements ResolverInterface
{
    
    /**
     * DataProvider\Loadstores
     *
     * @var DataProvider\Loadstores
     */
    private $loadstoresDataProvider;

    /**
     * @param DataProvider\Loadstores $loadstoresRepository
     */
    public function __construct(
        DataProvider\Loadstores $loadstoresDataProvider
    ) {
        $this->loadstoresDataProvider = $loadstoresDataProvider;
    }

    /**
     * @inheritdoc
     */
    public function resolve(
        Field $field,
        $context,
        ResolveInfo $info,
        array $value = null,
        array $args = null
    ) {
        $loadstoresData = $this->loadstoresDataProvider->getLoadstores($args);

        $stores = [];
        foreach ($loadstoresData as $store) {
            $stores[] = [
                'storelocator_id' => $store->getstorelocator_id(),
                'latitude' => $store->getLatitude(),
                'longitude' => $store->getLongitude(),
                'store_name' => $store->getStoreName(),
                'state' => $store->getState(),
                'city' => $store->getCity(),
                'zipcode' => $store->getZipcode(),
                'phone' => $store->getPhone(),
                'address' => $store->getAddress(),
                'marker_icon' => $store->getMarkerIcon(),
                'zoom_level' => $store->getZoomLevel(),
                'rewrite_request_path' => $store->getRewriteRequestPath(),
                'is_visible' => $store->getIsVisible(),
                'store_direction_url' => $store->getStoreDirectionUrl(),
                '__typename' => 'PickupLocation'
            ];
        }
        return $stores;
    }
}

