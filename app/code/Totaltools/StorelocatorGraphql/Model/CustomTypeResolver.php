<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\StorelocatorGraphql\Model;

use Magento\Framework\GraphQl\Query\Resolver\TypeResolverInterface;

/**
 * @inheritdoc
 */
class CustomTypeResolver implements TypeResolverInterface
{
    const STORELOCATOR = "Custom";
    const TYPE_RESOLVER = 'Custom';

    /**
     * @inheritdoc
     */
    public function resolveType(array $data) : string
    {
        if (isset($data['type_id']) && $data['type_id'] == self::STORELOCATOR) {
            return self::TYPE_RESOLVER;
        }
        return '';
    }
}
