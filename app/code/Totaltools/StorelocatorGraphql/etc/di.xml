<?xml version="1.0"?>
<!--
/**
 * Copyright © Magefan (<EMAIL>). All rights reserved.
 * Please visit Magefan.com for license details (https://magefan.com/end-user-license-agreement).
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- <type name="Magento\Framework\GraphQl\Query\Resolver\Argument\FieldEntityAttributesPool">
        <arguments>
            <argument name="attributesInstances" xsi:type="array">
                <item name="di_build_magefan_blog_post" xsi:type="object">Magefan\BlogGraphQl\Model\Posts\FilterArgument</item>
            
            </argument>
        </arguments>
    </type> -->
    <type name="Magento\UrlRewriteGraphQl\Model\Resolver\EntityUrl">
        <plugin
                name="storelocator_graphql_resolver"
                type="Totaltools\StorelocatorGraphql\Plugin\Magento\UrlRewriteGraphQl\Model\Resolver\EntityUrl"
                sortOrder="10"
                disabled="false"
        />
    </type>
     <type name="Magento\UrlRewriteGraphQl\Model\Resolver\UrlRewrite\UrlResolverIdentity">
        <arguments>
            <argument name="urlResolverIdentities" xsi:type="array">
                <item name="cms_page" xsi:type="object">Magento\CmsUrlRewriteGraphQl\Model\Resolver\UrlRewrite\CmsUrlResolverIdentity</item>
            </argument>
        </arguments>
    </type>
    <!-- <type name="Magento\UrlRewriteGraphQl\Model\Resolver\Route">
        <plugin
                name="Magefan_BlogGraphQl_Plugin_Magento_UrlRewriteGraphQl_Model_Resolver_EntityUrl"
                type="Magefan\BlogGraphQl\Plugin\Magento\UrlRewriteGraphQl\Model\Resolver\EntityUrl"
                sortOrder="10"
                disabled="false"
        />
    </type> -->
     <type name="Magento\UrlRewriteGraphQl\Model\DataProvider\EntityDataProviderComposite">
        <arguments>
            <argument name="dataProviders" xsi:type="array">
                <item name="custom" xsi:type="object">Totaltools\StorelocatorGraphql\Model\DataProvider\UrlRewrite\StorelocatorProviderComposite</item>
                
            </argument>
        </arguments>
    </type>
</config>
