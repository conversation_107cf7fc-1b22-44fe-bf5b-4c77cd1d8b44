<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\UrlRewriteGraphQl\Model\RoutableInterfaceTypeResolver">
        <arguments>
            <argument name="productTypeNameResolvers" xsi:type="array">
                <item name="custom_type_resolver" xsi:type="object">Totaltools\StorelocatorGraphql\Model\CustomTypeResolver</item>
            </argument>
        </arguments>
    </type>
</config>
