type Query {
  loadPickupLocations(
    radius: String @doc(description: "Query by radius.")
    latitude: String @doc(description: "Query by latitude.")
    longitude: String @doc(description: "Query by longitude.")
    tagIds: String @doc(description: "Query by tagIds.")
    country_id: String @doc(description: "Query by country_id.")
    store_name: String @doc(description: "Query by store_name.")
    state: String @doc(description: "Query by state.")
    city: String @doc(description: "Query by city.")
    zipcode: String @doc(description: "Query by zipcode.")
  ): [PickupLocations]
    @resolver(
      class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\Loadstores"
    )
    @doc(description: "Query by get complete list of store.")
  searchPostCode(
    query: String @doc(description: "Query string to get locations.")
  ): [PostCodes]
    @resolver(
      class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\PostCodes"
    )
    @doc(description: "Query by get complete list of locations.")

  storeListByPostCode(
    postcode: Int @doc(description: "Load stores by postcode.")
  ): [PickupLocations]
    @resolver(class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\Stores")
    @doc(description: "Get store list by post code.")
  viewStoreDetails(
    url_request: String @doc(description: "Load store by rewrite_request_path.")
    store_id: Int @doc(description: "Load store by store id.")
  ): StoreDetail
    @resolver(class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\View")
    @doc(description: "Get store details.")
    
    stockCheck(
        postcode: String @doc(description: "Stock check by Postcode")
        method: String @doc(description: "Shipping method")
        sku: String @doc(description: "Product sku")
    ) : [StoreDetail] 
    @resolver(class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\StockCheck")
    @doc(description: "Get stock check.")
    getNearestStore(
        latitude: String @doc(description: "Latitude of customer location")
        longitude: String @doc(description: "Longitude of customer location")
    ) : StoreDetail 
    @resolver(class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\NearestStore")
    @doc(description: "Get nearest store details.")
    changeStore(
        store_id: Int @doc(description: "Change store by id")
    ) : StoreDetail 
    @resolver(class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\ChangeStore")
    @doc(description: "Select store .")

    getQuote(
        sku: String @doc(description: "Change store by id")
        storelocator_id: Int @doc(description: "Change store by id")
        postcode: Int @doc(description: "Change store by id")
        city: String @doc(description: "Change store by id")
        region: String @doc(description: "Change store by id")
        country_id: String @doc(description: "Change store by id")
    ) : [Quote] 
    @resolver(class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\GetQuote")
    @doc(description: "Select store .")

    stockavAilabilityMessage(
        sku: String @doc(description: "Change store by id")
        storelocator_id: Int @doc(description: "Change store by id")
        postcode: Int @doc(description: "Change store by id")
        method: String @doc(description: "Change store by id")
    ) : StockavAilabilityMessage 
    @resolver(class: "Totaltools\\StorelocatorGraphql\\Model\\Resolver\\StockavAilabilityMessage")
    @doc(description: "Select store .")

}

type Mutation {
    changeLocation (input: ChangeLocationInput! @doc(description: "An input object that defines the change location parameters.")): ChangeLocationOutput @resolver(class: "\\Totaltools\\StorelocatorGraphql\\Model\\Resolver\\ChangeLocation") @doc(description:"Change location response.") 
}



type PickupLocations {
  storelocator_id: String @doc(description: "Store id.")
  latitude: String @doc(description: "Store latitude.")
  longitude: String @doc(description: "Store longitude.")
  store_name: String @doc(description: "Store Name.")
  state: String @doc(description: "State in which store is located.")
  city: String @doc(description: "Store city.")
  zipcode: String @doc(description: "Assigned zipcode.")
  phone: String @doc(description: "Store contact number.")
  address: String @doc(description: "Store address.")
  marker_icon: String @doc(description: "Store marker icon.")
  zoom_level: String @doc(description: "Zoom level.")
  rewrite_request_path: String @doc(description: "Store url.")
  is_visible: String @doc(description: "if store is visible.")
  store_direction_url: String @doc(description: "Store direction url.")
}
type StoreDetail {
  storelocator_id: String @doc(description: "Store id.")
  address: String @doc(description: "Store Address.")
  baseimage_id: String @doc(description: "Store base image.")
  city: String @doc(description: "Store located in city")
  country_id: String @doc(description: "Country Id")
  description: String @doc(description: "Store description")
  email: String @doc(description: "store email address")
  fax: String @doc(description: "Store fax")
  link: String @doc(description: "store link")
  latitude: String @doc(description: "Store latitude.")
  longitude: String @doc(description: "Store longitude")
  marker_icon: String @doc(description: " Store marker icon")
  meta_description: String @doc(description: "Store meta description")
  meta_keywords: String @doc(description: "Store keywords")
  meta_title: String @doc(description: "Store meta title")
  phone: String @doc(description: "Store phone number")
  rewrite_request_path: String @doc(description: "Pretty url of the store")
  sort_order: String @doc(description: "Store sort Order")
  state: String @doc(description: "Store address state")
  status: String @doc(description: "Store enable/disable status")
  stockMessage: StockMessage @doc(description: "Get store stock messages")
  store_name: String @doc(description: "Store Name")
  zipcode: String @doc(description: "Zipcode of store location")
  zoom_level: String @doc(description: "Zoom Level")
  facebook_link: String @doc(description: "Store facebook page link")
  instagram_link: String @doc(description: "Store instagram link")
  store_direction_url: String @doc(description: "Store location direction url")
  monday_open: String @doc(description: "Monday Opening schedule")
  tuesday_open: String @doc(description: "Tuesday Opening schedule")
  wednesday_open: String @doc(description: "Wednesday Opening schedule")
  thursday_open: String @doc(description: "Thursday Opening schedule")
  friday_open: String @doc(description: "Friday opening schedule")
  saturday_open: String @doc(description: "Saturday opening schedule")
  sunday_open: String @doc(description: "Sunday opening schedule")
  monday_close: String @doc(description: "Monday closing schedule")
  tuesday_close: String @doc(description: "Tuesday  closing schedule")
  wednesday_close: String @doc(description: "Wednesday closing schedule")
  thursday_close: String @doc(description: "Thurday closing schedule")
  friday_close: String @doc(description: "Friday closing schedule")
  saturday_close: String @doc(description: "Saturday closing schedule")
  sunday_close: String @doc(description: "Sunday closing schedule")
  upcoming_holiday: UpcomingHoliday @doc(description: "Upcoming holiday")
  upcoming_special_day: UpcomingSpecialDay @doc(description: "Upcoming special day")
}
type PostCodes {
  batch_id: String @doc(description: "Postcode batch id.")
  category: String @doc(description: "Postcode Category.")
  latitude: String @doc(description: "Postcode latitude.")
  locality: String @doc(description: "Locality.")
  longitude: String @doc(description: "longitude.")
  postcode: String @doc(description: "postcode.")
  postcode_id: String @doc(description: "postcode_id.")
  state: String @doc(description: "Post code state.")
}

type StockMessage {
  cart_message: String @doc(description: "Stock cart message.")
  cc_message: String @doc(description: "Click and Collect message.")
  code: String @doc(description: "Message code.")
  dangerous_item: String @doc(description: "Is it dangerouse item.")
  message: String @doc(description: "Stock Message.")
  name: String @doc(description: "Product Name.")
  pre_order_item: String @doc(description: "Pre Order item.")
  productmessage: String @doc(description: "Product message.")
  shipping_dangerous: String @doc(description: "Shipping dangerous.")
  store_message: String @doc(description: "Stock message.")
  thirty_minute_click_and_collect_available: String @doc(description: "Thirty minutes click and collect available.")
  url: String @doc(description: "Product Url.")
}
type UpcomingHoliday {
  name: String @doc(description: "Upcoming Holiday Name.")
  date: [String] @doc(description: "Upcoming Holiday Date.")
  
}
type UpcomingSpecialDay {
  name: String @doc(description: "Upcoming Special Day name.")
  time_open: String @doc(description: "Upcoming Special Day store opening time.")
  time_close: String @doc(description: "Upcoming Special Day store closing time.")
  date: [String] @doc(description: "Upcoming Special Day date.")
  
}

input ChangeLocationInput  @doc(description: "An input object for changing location.") {
    postcode: String @doc(description: "New store post code.")
    city: String @doc(description: "city.")
    country_id: String @doc(description: "Country id.")
    region: String @doc(description: "New location region.")
}
type ChangeLocationOutput  @doc(description: "An output object for changing location.") {
    location: Location @doc(description: "New location .")
    store_id: String @doc(description: "store id.")
}

type Location  @doc(description: "An input object for changing location.") {
    city: String @doc(description: "Postcode latitude.")
    country_id: String @doc(description: "Locality.")
    postcode: String @doc(description: "longitude.")
    state: String @doc(description: "postcode.")
}


type Quote  @doc(description: "An input object for changing location.") {
    shipping_price: String @doc(description: "shipping price.")
    shipping_name: String @doc(description: "shipping method name.")
    shipping_class: String @doc(description: "shipping method class.")
    shipping_message: String @doc(description: "shipping message .")
    product_price: String @doc(description: "product price .")
}

type StockavAilabilityMessage  @doc(description: "An input object for changing location.") {
    cart_message: String @doc(description: "Postcode latitude.")
    code: String @doc(description: "Postcode latitude.")
    dangerous_item: String @doc(description: "Postcode latitude.")
    message:  String @doc(description: "Postcode latitude.")
    name:  String @doc(description: "Postcode latitude.")
    pre_order_item: String @doc(description: "Postcode latitude.")
    product_page_express_message:  String @doc(description: "Postcode latitude.")
    product_page_standard_message:  String @doc(description: "Postcode latitude.")
    productmessage:  String @doc(description: "Postcode latitude.")
    shipping_dangerous: String @doc(description: "Postcode latitude.")
    store_message:  String @doc(description: "Postcode latitude.")
    thirty_minute_click_and_collect_available:  String @doc(description: "Postcode latitude.")
    url:  String @doc(description: "Postcode latitude.")
}