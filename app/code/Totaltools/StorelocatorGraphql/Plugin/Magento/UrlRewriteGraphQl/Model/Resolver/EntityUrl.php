<?php

namespace Totaltools\StorelocatorGraphql\Plugin\Magento\UrlRewriteGraphQl\Model\Resolver;

class EntityUrl extends \Magento\UrlRewriteGraphQl\Model\Resolver\EntityUrl {
    // public function resolve(array $args) {
    //     // Your custom logic to resolve the URL for 'search.html'
    //     // This should return information consistent with the UrlRewriteEntityTypeEnum
    // }
    public function afterResolve(
        $subject,
        $result,
        $field,
        $context,
        $info,
        $value = null,
        $args = null
    ) {
        if (!empty($result)) {
            return $result;
        }

        $path = $args['url'];
        

        $type = 'Custom';
        if (stripos($path, 'storelocator') === false) {
            $type = 'Storelocator';
        }
        $type = strtoupper($type);

        $result = [
            'type' => $type,
            'type_id' => $type,
            'relative_url' => $path,
            'redirect_code' => 0
        ];

        return $result;
    }
}
