<?php
/**
 * Total Tools ZipMoney.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\ZipMoney\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class Update
 * @package Totaltools\ZipMoney\Console\Command
 */
class Update extends Command
{
    const NAME = 'totaltools:zipmoney:migrate';
    const DESCRIPTION = 'Orders by ZipMoney';

    /**
     * @var \Magento\Framework\App\State
     */
    private $appState;

    /**
     * @var \Totaltools\ZipMoney\Model\Order
     */
    private $order;

    /**
     * Update constructor.
     * @param \Magento\Framework\App\State $appState
     * @param \Totaltools\ZipMoney\Model\Order $order
     * @param string|null $name
     */
    public function __construct(
        \Magento\Framework\App\State $appState,
        \Totaltools\ZipMoney\Model\Order $order,
        string $name = null
    ) {
        parent::__construct($name);
        $this->appState = $appState;
        $this->order = $order;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this->setName(self::NAME)->setDescription(self::DESCRIPTION);
        parent::configure();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(
        InputInterface $input,
        OutputInterface $output
    )
    {
        $self = $this;
        try {
            $self->appState->emulateAreaCode(\Magento\Framework\App\Area::AREA_FRONTEND, function () use ($self, $output) {
                $output->writeln("Orders by ZipMoney migration is started.\n");
                $this->order->migrationOrder($output);
                $output->writeln("Orders by ZipMoney migration is finished.\n");
            });
        } catch (\Exception $e) {
            $output->writeln('<error>' . sprintf($e->getMessage()) . '</error>');
            debug_print_backtrace();
        }
    }
}
