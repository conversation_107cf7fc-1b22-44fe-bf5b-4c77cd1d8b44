<?php

namespace Totaltools\ZipMoney\Plugin;

/**
 * @category    Totaltools
 * @package     Totaltools_ZipMoney
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

use Zip\ZipPayment\Model\Ui\ConfigProvider as ZipMoneyConfig;
use Zip\ZipPayment\Model\Config as ZipMoneyHelper;

class ConfigProviderPlugin
{
    const ZIPPAYMENT_DESCRIPTION_PATH = 'description';

    /**
     * @var ZipMoneyHelper
     */
    protected $helper;

    /**
     * @param ZipMoneyHelper $helper
     */
    public function __construct(ZipMoneyHelper $helper)
    {
        $this->helper = $helper;
    }

    /**
     * @param ZipMoneyConfig $subject
     * @param array $config
     * @return array
     */
    public function afterGetConfig($subject, $config)
    {
        if (is_array($config) && isset($config['payment'][ZipMoneyConfig::CODE])) {
            $config['payment'][ZipMoneyConfig::CODE]['description'] = $this
                ->helper
                ->getValue(self::ZIPPAYMENT_DESCRIPTION_PATH);
        }

        return $config;
    }
}
