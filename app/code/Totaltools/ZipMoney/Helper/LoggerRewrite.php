<?php

namespace Totaltools\<PERSON>ipMoney\Helper;

/**
 * @category    Totaltools
 * @package     Totaltools_ZipMoney
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

use Zip\ZipPayment\Helper\Logger;

class LoggerRewrite extends Logger
{
    /**
     * @inheritdoc
     */
    public function sanitizePrivateData($debug)
    {
        /** TOT0024-11: Converted to string on null value to avoid json_encode null args exception */
        if (is_null($debug)) {
            $debug = '';
        }

        return parent::sanitizePrivateData($debug);
    }

    /**
     * @inheritdoc
     */
    protected function sanitizeArrData($debugData)
    {
        $privateData = $this->getPrivateData();

        if (is_array($debugData) && !empty($privateData)) {
            foreach ($debugData as $key => $val) {
                if (is_array($val)) {
                    $debugData[$key] = $this->sanitizeArrData($debugData[$key]);
                } elseif (in_array($key, $this->getPrivateData()) && !is_numeric($key)) {
                    $debugData[$key] = '****';
                } elseif (!is_null($val) && stristr($val, 'Authorization: Bearer') !== false) {
                    /** TOT0024-11: ↑ Added null check to avoid stristr null args exception */
                    $debugData[$key] = '****';
                }
            }
        }

        return $debugData;
    }
}
