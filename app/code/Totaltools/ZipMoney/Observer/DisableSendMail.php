<?php
/**
 *  Developed by Balanceinternet
 *  Do not edit or add to this file if you wish to upgrade This to newer
 *  versions in the future.
 *  <AUTHOR>
 *
 */

namespace Totaltools\ZipMoney\Observer;

use Magento\Framework\Event\ObserverInterface;

/**
 * Class DisableSendMail
 * @package Totaltools\ZipMoney\Observer\DisableSendMail
 */
class DisableSendMail implements ObserverInterface
{
    /**
     * Skip sending order confirmation until the transaction is captured/authorized
     *
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(
        \Magento\Framework\Event\Observer $observer
    )
    {
        $order = $observer->getEvent()->getOrder();
        $paymentMethod = $order->getPayment()->getMethod();
        if ($paymentMethod == \Zip\ZipPayment\Model\Config::METHOD_CODE) {
            $order->setCanSendNewEmailFlag(false);
        }
    }
}
