<?php

/**
 * Total Tools ZipMoney.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\ZipMoney\Model;

use Magento\GiftCard\Model\Catalog\Product\Type\Giftcard as ProductGiftCard;
use Magento\Sales\Model\Order;
use Magento\Framework\Exception\NotFoundException;
use Zip\ZipPayment\MerchantApi\Lib\ApiException;

/**
 * @codingStandardsIgnoreFile
 * @codeCoverageIgnoreFile
 */
class Charge extends \Zip\ZipPayment\Model\Charge
{
    /**
     * @var \Magento\Framework\Event\ManagerInterface
     */
    protected $_eventManager;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\StoreShippitService
     */
    private $shippitService;

    /**
     * @var \Magento\Sales\Model\OrderFactory
     */
    protected $orderFactory;

    /**
     * Charge constructor.
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\Quote\Api\CartManagementInterface $cartManagement
     * @param \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
     * @param \Magento\Sales\Api\OrderPaymentRepositoryInterface $orderPaymentRepository
     * @param \Magento\Customer\Api\AccountManagementInterface $accountManagement
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     * @param \Magento\Customer\Model\Url $customerUrl
     * @param Order\Email\Sender\OrderSender $orderSender
     * @param \Magento\Framework\DataObject\Copy $objectCopyService
     * @param \Magento\Framework\Api\DataObjectHelper $dataObjectHelper
     * @param \Magento\Framework\Event\ManagerInterface $eventManager
     * @param \Zip\ZipPayment\Helper\Payload $payloadHelper
     * @param \Zip\ZipPayment\Helper\Logger $logger
     * @param \Zip\ZipPayment\Helper\Data $helper
     * @param \Zip\ZipPayment\Model\Config $config
     * @param \Zip\ZipPayment\MerchantApi\Lib\Api\ChargesApi $chargesApi
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\StoreShippitService $shippitService
     * @param array $data
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function __construct(
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\Quote\Api\CartManagementInterface $cartManagement,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository,
        \Magento\Sales\Api\OrderPaymentRepositoryInterface $orderPaymentRepository,
        \Magento\Customer\Api\AccountManagementInterface $accountManagement,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Magento\Customer\Model\Url $customerUrl,
        \Magento\Sales\Model\Order\Email\Sender\OrderSender $orderSender,
        \Magento\Framework\DataObject\Copy $objectCopyService,
        \Magento\Framework\Api\DataObjectHelper $dataObjectHelper,
        \Magento\Framework\Event\ManagerInterface $eventManager,
        \Zip\ZipPayment\Helper\Payload $payloadHelper,
        \Zip\ZipPayment\Helper\Logger $logger,
        \Zip\ZipPayment\Helper\Data $helper,
        \Zip\ZipPayment\Model\Config $config,
        \Zip\ZipPayment\MerchantApi\Lib\Api\ChargesApi $chargesApi,
        \Zip\ZipPayment\Model\TokenisationFactory $tokenFactory,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreShippitService $shippitService,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Sales\Model\OrderFactory $orderFactory,
        array $data = []
    ) {
        parent::__construct(
            $customerSession,
            $checkoutSession,
            $customerFactory,
            $quoteRepository,
            $cartManagement,
            $orderRepository,
            $orderPaymentRepository,
            $accountManagement,
            $customerRepository,
            $messageManager,
            $customerUrl,
            $orderSender,
            $objectCopyService,
            $dataObjectHelper,
            $payloadHelper,
            $logger,
            $helper,
            $config,
            $chargesApi,
            $tokenFactory,
            $storeManager,
            $data
        );

        $this->_eventManager = $eventManager;
        $this->storeRepository = $storeRepository;
        $this->shippitService = $shippitService;
        $this->orderFactory = $orderFactory;
    }

    /**
     * Charges the customer against the order
     *
     * @return \Zip\ZipPayment\MerchantApi\Lib\Model\Charge
     * @throws \Magento\Framework\Exception\LocalizedException
     * @param bool $token
     */
    public function charge($token)
    {
        if (!$this->_order || !$this->_order->getId()) {
            throw new \Magento\Framework\Exception\LocalizedException(__('The order does not exist.'));
        }

        $payload = $this->_payloadHelper->getChargePayload($this->_order, $token);

        $this->_logger->debug("Charge Payload:- " . $this->_logger->sanitizePrivateData($payload));

        try {
            $charge = $this->getApi()
                ->chargesCreate($payload, $this->genIdempotencyKey());

            $this->_logger->debug("Charge Response:- " . $this->_logger->sanitizePrivateData($charge));

            if (isset($charge->error)) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Could not create the charge'));
            }

            if (!$charge->getState() || !$charge->getId()) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Invalid Charge'));
            }

            $this->_logger->debug($this->_helper->__("Charge State:- %s", $charge->getState()));

            if ($charge->getId()) {
                $additionalPaymentInfo = $this->_order->getPayment()->getAdditionalInformation();
                $additionalPaymentInfo['receipt_number'] = $charge->getReceiptNumber();
                $additionalPaymentInfo['zip_charge_id'] = $charge->getId();

                $payment = $this->_order->getPayment()
                    ->setAdditionalInformation($additionalPaymentInfo);
                $this->_orderPaymentRepository->save($payment);
            }

            // trigger sending confirmation email when transaction is captured/authorized
            $this->_order->setCanSendNewEmailFlag(true);

            $this->_chargeResponse($charge, $this->_config->isCharge());

            // https://jira.balancenet.com.au/browse/TOT0017-1766
            // ZipMoney by default doesn't dispatch an event for `checkout_submit_all_after`
            // Shippit and Pronto use that event
            $this->_eventManager->dispatch(
                'checkout_submit_all_after',
                [
                    'order' => $this->_order,
                    'quote' => $this->_quote,
                ]
            );

            /*if (!$this->_quote->isVirtual()) {
                $store = $this->getOrderStore($this->_order);
                $this->_logger->debug(__('Push order #%1 to the Shippit', $this->_order->getIncrementId()));

                try {
                    $this->shippitService->sendOrder($this->_order, $store);
                } catch (NotFoundException $e) {
                    $this->_logger->critical(__('Shippit Error while pushing order from signifyd. Order Id -%1', $this->_order->getIncrementId()));
                    $this->_logger->critical($e->getMessage());
                }
            }*/
        } catch (ApiException $e) {
            list($apiError, $message, $logMessage) = $this->_helper->handleException($e);

            // Cancel the order
            $this->_helper->cancelOrder($this->_order, $apiError);
            throw new \Magento\Framework\Exception\LocalizedException(__($message));
        }

        return $charge;
    }


    /**
     * Captures the charge
     *
     * @param string $txnId
     * @param bool $isAuthAndCapture
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function _capture($txnId, $isAuthAndCapture = false)
    {
        /* If the capture has a corresponding authorisation before
         * authorise -> capture
         */
        if ($isAuthAndCapture) {

            // Check if order has valid state and status
            $orderStatus = $this->_order->getStatus();
            $orderState = $this->_order->getState();

            if (($orderState != Order::STATE_PROCESSING && $orderState != Order::STATE_PENDING_PAYMENT) ||
                ($orderStatus != self::STATUS_MAGENTO_AUTHORIZED)
            ) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Invalid order state or status.'));
            }
        } else {
            // Check if order has valid state and status
            $this->_verifyOrderState();
        }

        // Check if the transaction exists
        $this->_checkTransactionExists($txnId);

        $payment = $this->_order->getPayment();

        $parentTxnId = null;

        $orderId = $this->_order->getId();

        /* If the capture has a corresponding authorisation before
         * authorise -> capture
         */
        if ($isAuthAndCapture) {

            $authorizationTransaction = $payment->getAuthorizationTransaction();

            if (!$authorizationTransaction || !$authorizationTransaction->getId()) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Cannot find payment authorization transaction.'));
            }

            if ($authorizationTransaction->getTxnType() != \Magento\Sales\Model\Order\Payment\Transaction::TYPE_AUTH) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Incorrect payment transaction type.'));
            }
            $parentTxnId = $authorizationTransaction->getTxnId();
        }

        if (!$this->_order->canInvoice()) {
            throw new \Magento\Framework\Exception\LocalizedException(__('Cannot create invoice for the order.'));
        }

        $amount = $this->_order->getBaseTotalDue();

        if ($parentTxnId) {
            $payment->setParentTransactionId($parentTxnId);
            $payment->setShouldCloseParentTransaction(true);
        }

        // Capture
        $payment->setTransactionId($txnId)
            ->setPreparedMessage('')
            ->setIsTransactionClosed(0)
            ->registerCaptureNotification($amount);

        $this->_logger->info($this->_helper->__("Payment Captured"));

        $orderState = \Magento\Sales\Model\Order::STATE_PROCESSING;
        $orderStatus = $this->_order->getConfig()->getStateDefaultStatus($orderState);
        $this->_logger->debug("Order capture after " . $this->_order->getIncrementId() . "-  order state: " . $orderState . " status: " . $orderStatus);
        $this->_order->setState($orderState)->setStatus($orderStatus);

        $this->_orderRepository->save($this->_order);

        // Invoice
        $invoice = $payment->getCreatedInvoice();

        if ($invoice) {
            if ($this->_order->getCanSendNewEmailFlag()) {
                try {
                    $this->_orderSender->send($this->_order);
                } catch (\Exception $e) {
                    $this->_logger->critical($e);
                }
            }

            // Load product by order id
            $order = $this->_orderRepository->get($orderId);

            $order->addStatusHistoryComment($this->_helper->__('Notified customer about invoice #%s.', $invoice->getIncrementId()))
                ->setIsCustomerNotified(true);

            $orderState = \Magento\Sales\Model\Order::STATE_PROCESSING;
            $orderStatus = $this->_order->getConfig()->getStateDefaultStatus($orderState);
            $this->_logger->debug("Order invoice create after " . $this->_order->getIncrementId() .
                "-  order state: " . $orderState . " status: " . $orderStatus);
            $order->setState($orderState)->setStatus($orderStatus);

            $this->_orderRepository->save($order);
        }
    }

    /**
     * @param Order $order
     * @return \Totaltools\Storelocator\Model\Store
     */
    protected function getOrderStore(Order $order)
    {
        $storeId = (int)$order->getStorelocatorId();
        $store = $this->storeRepository->getById($storeId);
        return $store;
    }
}
