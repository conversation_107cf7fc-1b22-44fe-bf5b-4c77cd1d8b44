<?php
/**
 * Totaltools ZipMoney.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\ZipMoney\Model;

use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class Order
 * @package Totaltools\ZipMoney\Model
 */
class Order
{
    /**
     * @var OrderCollectionFactory
     */
    private $orderCollectionFactory;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\StoreShippitService
     */
    private $shippitService;

    /**
     * Order constructor.
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\StoreShippitService $shippitService
     */
    public function __construct(
        OrderCollectionFactory $orderCollectionFactory,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreShippitService $shippitService
    )
    {
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->storeRepository = $storeRepository;
        $this->shippitService = $shippitService;
    }

    /**
     * @param OutputInterface $output
     */
    public function migrationOrder(OutputInterface $output)
    {
        $paymentMethod = \Zip\ZipPayment\Model\Config::METHOD_CODE;
        $orderCollection = $this->orderCollectionFactory->create()
            ->addFieldToFilter('warehouse', ['null' => true])
            ->addFieldToFilter('store', ['null' => true]);
        $orderCollection->getSelect()
            ->join(
                ['payment' => 'sales_order_payment'],
                'main_table.entity_id = payment.parent_id',
                ['method']
            )
            ->where('payment.method = ?', $paymentMethod);

        $orderCollection->setOrder(
            'created_at',
            'desc'
        );
        /** @var Order $order */
        foreach ($orderCollection as $order) {
            try {
                $output->writeln(sprintf('Migration for order %s is started.', $order->getId()));
                $storeId = $order->getStorelocatorId();
                if ($storeId) {
                    /** @var \Totaltools\Storelocator\Model\Store $store */
                    $store = $this->storeRepository->getById($storeId);
                    /** @var \Magento\Sales\Model\Order $order */
                    $order->setWarehouse($store->getErpId());
                    $order->setStore($store->getErpCode());
                    $order->setStorelocatorId($store->getId());
                    $order->save();
                    /** @var \Shippit\Shipping\Model\Shippit $syncOrderObject */
                    $this->shippitService->sendOrder($order, $store);
                    $output->writeln('Successfull !');
                }
                if (!$storeId) {
                    $output->writeln(sprintf('Order %s is missing Storelocator Id.', $order->getId()));
                }
                $output->writeln(sprintf(' Migration for order %s is finished', $order->getId()));
            } catch (\Exception $e) {
                $output->writeln($e->getMessage());
            }
        }
    }
}
