<?php

namespace Totaltools\Wagento\Setup;

use Magento\Customer\Model\Customer;
use Magento\Eav\Model\Config;
use Magento\Eav\Model\Entity\Attribute\SetFactory;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;

class UpgradeData implements UpgradeDataInterface
{
    const ZD_USER_ID = 'zd_user_id';

    /**
     * @var Config
     */
    private Config $eavConfig;

    /**
     * @var SetFactory
     */
    private SetFactory $setFactory;

    /**
     * @param Config $eavConfig
     * @param SetFactory $setFactory
     */
    public function __construct(
        Config $eavConfig,
        SetFactory $setFactory
    ) {
        $this->eavConfig = $eavConfig;
        $this->setFactory = $setFactory;
    }

    /**
     * @inheritDoc
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        if (version_compare($context->getVersion(), '0.1.1', '<')) {
            $this->setCustomerAttributeSetAndGroup();
        }
    }

    /**
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function setCustomerAttributeSetAndGroup()
    {
        $customAttribute = $this->eavConfig->getAttribute(Customer::ENTITY, self::ZD_USER_ID);

        $customerEntity = $this->eavConfig->getEntityType('customer');
        $attributeSetId = $customerEntity->getDefaultAttributeSetId();

        $attributeSet = $this->setFactory->create();
        $attributeGroupId = $attributeSet->getDefaultGroupId($attributeSetId);

        $customAttribute->addData([
            'attribute_set_id' => $attributeSetId,
            'attribute_group_id' => $attributeGroupId,
            'used_in_forms' => ['adminhtml_customer']
        ]);
        $customAttribute->save();
    }
}
