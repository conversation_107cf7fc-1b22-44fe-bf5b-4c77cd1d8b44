<?php

namespace Totaltools\Loyalty\Block;

use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\View\Element\Template;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use Totaltools\Loyalty\Model\CustomerSession as CustomerSession;
use Totaltools\Pronto\Api\CustomerRequestManagerInterface;
use Totaltools\Loyalty\Model\Data\Collection;

/**
 * Class InsiderReward
 * @package Totaltools\Loyalty\Block
 */
class InsiderReward extends Template
{
    const CUSTOMER_BRONZE_LEVEL = 1;
    /**
     * @var \Magento\Customer\Model\Customer
     */
    protected $customerSession;

    /**
     * @var \Magento\Framework\Data\CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var CustomerRequestManagerInterface
     */
    private $prontoRequestManager;

    /**
     * @var TimezoneInterface
     */
    protected $_timezone;

    protected $_collection;

    /**
     * InsiderReward constructor.
     * @param Template\Context $context
     * @param CustomerSession $customerSession
     * @param \Magento\Framework\Data\CollectionFactory $collectionFactory
     * @param CustomerRequestManagerInterface $prontoRequestManager
     * @param TimezoneInterface $localeDate
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        CustomerSession $customerSession,
        \Magento\Framework\Data\CollectionFactory $collectionFactory,
        CustomerRequestManagerInterface $prontoRequestManager,
        TimezoneInterface $localeDate,
        Collection $_collection,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->customerSession = $customerSession;
        $this->collectionFactory = $collectionFactory;
        $this->_collection = $_collection;
        $this->prontoRequestManager = $prontoRequestManager;
        $this->_timezone = $localeDate;
    }

    /**
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomer()
    {
        return $this->customerSession->getCustomer();
    }

    /**
     * @return bool
     */
    public function isLoyal()
    {
        return $this->getCustomer()->getData('is_loyal');
    }

    /**
     * @return bool
     */
    public function getLoyaltyLevel()
    {
        return $this->getCustomer()->getData('loyalty_level');
    }

    /**
     * @return bool
     */
    public function isCustomerVerified()
    {
        return empty($this->getCustomer()->getData('unverified_loyalty_id'));
    }

    /**
     * @return bool
     */
    public function isCustomerStatusVerified()
    {
        return $this->getCustomer()->getData('email_verification_status') === EmailVerification::VERIFIED;
    }

    /**
     * Get Extend Expiry Url
     *
     * @return string
     */
    public function getExtendExpiryUrl()
    {
        return $this->getUrl('*/*/extendexpiry');
    }
     /**
     * @return array|\Magento\Framework\Data\Collection
     * @throws \Exception
     */
    public function _getActiveInsiderRewards()
    {
        return $this->getActiveInsiderRewards($this->getRequest()->getParams());
    }

    /**
     * @return array|\Magento\Framework\Data\Collection
     * @throws \Exception
     */
    public function getActiveInsiderRewards($param)
    {
        $page = $param['p'] ?? 1;
        $pageSize = $param['limit'] ?? 10;
        $loyaltyPoints = $this->prontoRequestManager->getMemberPoints($this->getCustomer());
        $offset = ($page - 1) * $pageSize;
        $customerLevel = $this->getLoyaltyLevel();
        if ($loyaltyPoints) {
            $today = $this->_timezone->date()->format('d-m-Y');
            foreach ($loyaltyPoints as $key => $item) {
                if (isset($item['ExpiryDate']) && !is_array($item['ExpiryDate'])) {
                    $expiry = $this->formatExpiryDate($item['ExpiryDate']);
                    if ($expiry) {
                        if (strtotime($today) > strtotime($expiry)) {
                            unset($loyaltyPoints[$key]);
                            continue;
                        }
                        $item['ExpiryDate'] = $expiry;
                    }
                } else {
                    unset($loyaltyPoints[$key]);
                    continue;
                }
                if ($item['OriginalValue'] <= 0 && $item['PointAmount'] > 0) {
                    $loyaltyPoints[$key]['OriginalValue'] = ($item['PointAmount']/100);
                }

                if ($loyaltyPoints[$key]['OriginalValue']  <= 0) {
                    unset($loyaltyPoints[$key]);
                    continue;
                }

                if ($loyaltyPoints[$key]['PointsRemaining']  <= 0) {
                    unset($loyaltyPoints[$key]);
                    continue;
                }

                $validTypes = ['AR', 'PR', 'TR', 'BR'];
                if (!in_array($item['TransType'], $validTypes)) {
                    unset($loyaltyPoints[$key]);
                    continue;
                }
                if($customerLevel > self::CUSTOMER_BRONZE_LEVEL) {
                    $extendExpiry = ['AR','TR','BR'];
                    if (in_array($item['TransType'], $extendExpiry)) {
                        $loyaltyPoints[$key]['ExtendExpiryDate'] = 1;
                    }
                }
                
                if(is_array($item['SeqNo'])) {
                    $loyaltyPoints[$key]['SeqNo'] = $item['SeqNo'][0];
                }
            }
            $collectionSize = count($loyaltyPoints);
            $loyaltyPoints = array_slice(array_reverse($loyaltyPoints), $offset, $pageSize);
            $this->_collection->addItems($loyaltyPoints);
            $this->_collection->setSize($collectionSize);
            $this->_collection->setCurPage($page);
            $this->_collection->setPageSize($pageSize);
        }
        return $this->_collection;
    }

    /**
     * Get formatted expiry date.
     *
     * @param $expiryDate
     *
     * @return string
     */
    public function formatExpiryDate($expiryDate)
    {
        try {
            return $this->_timezone
                ->date(new \DateTime($expiryDate))
                ->format('d-m-Y');
        } catch (\Exception $exception) {
            return '';
        }
    }

    /**
     * @return $this|Template
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        if ($this->_collection) {
            $pager = $this->getLayout()->createBlock(
                'Magento\Theme\Block\Html\Pager',
                'insider.info.pager'
            )->setShowPerPage(true)->setCollection(
                $this->_collection
            );
            $this->setChild('pager', $pager);
            $this->_collection->load();
        }
        return $this;
    }

    /**
     * @return string
     */
    public function getPagerHtml()
    {
        return $this->getChildHtml('pager');
    }
}
