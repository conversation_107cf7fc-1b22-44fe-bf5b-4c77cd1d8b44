<?php

namespace Totaltools\Loyalty\Block;

use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use Totaltools\Loyalty\Model\CustomerSession as CustomerSession;
use Totaltools\Pronto\Helper\Data as ProntoHelper;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

/**
 * Info block
 */
class Info extends Template
{
    /**#@+
     * Constants defined for keys of the data array.
     * Identical to the name of the getter in snake case
     */
    const XML_POINT = 'point';
    const XML_AMOUNT = 'amount';
    const XML_EXPIRY = 'expiry';
    const XML_LEVEL_TEXT = 'level_text';
    const XML_NEXT_AMOUNT = 'next_amount';
    const XML_NEXT_LEVEL_TEXT = 'next_level_text';
    const XML_LOYALTY_STATUS = 'loyalty_status';
    const XML_REFERRAL_CODE = 'referral_code';

    /**
     * Customer session
     *
     * @var CustomerSession
     */
    protected $currentCustomer;

    /**
     * @var \Magento\Directory\Model\Currency $currency,
     */
    private $currency;

    /**
     * @var \Totaltools\Pronto\Model\LoyaltyTierRepository
     */
    private $loyaltyTierRepository;

    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var \Totaltools\Pronto\Model\Request\Customer
     */
    private $customerRequest;
    /**
     * @var \Magento\Customer\Model\SessionFactory
     */
    public $customerSession;

    /**
     * @var \Totaltools\Customer\Helper\AttributeData
     */
    private $customerHelper;

    /**
     * @var ProntoHelper
     */
    private $prontoHelper;

    /**
     * Info constructor.
     * @param Context $context
     * @param CustomerSession $customerSession
     * @param \Magento\Directory\Model\Currency $currency
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Totaltools\Pronto\Model\LoyaltyTierRepository $loyaltyTierRepository
     * @param \Totaltools\Pronto\Model\Request\Customer $customerRequest
     * @param \Totaltools\Customer\Helper\AttributeData $customerHelper
     * @param ProntoHelper $prontoHelper
     * @param array $data
     */
    public function __construct(
        Context $context,
        CustomerSession $customerSession,
        \Magento\Directory\Model\Currency $currency,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Totaltools\Pronto\Model\LoyaltyTierRepository $loyaltyTierRepository,
        \Totaltools\Pronto\Model\Request\Customer $customerRequest,
        \Totaltools\Customer\Helper\AttributeData $customerHelper,
        ProntoHelper $prontoHelper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->currentCustomer = $customerSession;
        $this->customerSession = $this->currentCustomer->getCustomerSession();
        $this->currency = $currency;
        $this->loyaltyTierRepository = $loyaltyTierRepository;
        $this->customerRepository = $customerRepository;
        $this->customerRequest = $customerRequest;
        $this->customerHelper = $customerHelper;
        $this->prontoHelper = $prontoHelper;
    }

    /**
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomer()
    {
        return $this->currentCustomer->getCustomer();
    }

    /**
     * @return bool
     */
    public function isLoyal()
    {
        return $this->getCustomer()->getData('is_loyal');
    }

    /**
     * @return bool
     */
    public function isCustomerVerified()
    {
        return empty($this->getCustomer()->getData('unverified_loyalty_id'));
    }

    /**
     * @return bool
     */
    public function isCustomerStatusVerified()
    {
        return $this->getCustomer()->getData('email_verification_status') === EmailVerification::VERIFIED;
    }

    /**
     * @return string
     */
    public function getCurrencySymbol()
    {
        return $this->currency->getCurrencySymbol();
    }

    /**
     * @return boolean
     */
    public function isB2cCustomer()
    {
        return !$this->customerHelper->isB2bCustomer();
    }
}
