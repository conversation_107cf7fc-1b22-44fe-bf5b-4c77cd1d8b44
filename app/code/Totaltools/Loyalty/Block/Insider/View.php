<?php

/**
 * <AUTHOR> Internet
 * @package Totaltools_Loyalty
 */
namespace Totaltools\Loyalty\Block\Insider;

/**
 * view instore order block
 * @SuppressWarnings(PHPMD.ExcessivePublicCount)
 */
class View extends \Magento\Framework\View\Element\Template
{

    /**
     * @var InstoreOrderFactory
     */
    protected $instoreOrderFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    protected $dataCurrency;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    protected $timezoneInterface;

    /**
     * @var \Totaltools\Loyalty\Helper\Convert
     */
    private $convertHelper;
    /**
     * @var \Magento\Sales\Api\Data\OrderInterface
     */
    private $order;
    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    private $timezone;

    /**
     * View constructor.
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory
     * @param \Totaltools\Loyalty\Model\CustomerSession $customerSession
     * @param \Magento\Framework\Pricing\Helper\Data $dataCurrency
     * @param \Totaltools\Loyalty\Helper\Convert $convertHelper
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezone
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Magento\Framework\Pricing\Helper\Data $dataCurrency,
        \Totaltools\Loyalty\Helper\Convert $convertHelper,
        \Magento\Sales\Api\Data\OrderInterface $order,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezone,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->instoreOrderFactory = $instoreOrderFactory;
        $this->customerSession = $customerSession;
        $this->dataCurrency = $dataCurrency;
        $this->timezoneInterface = $context->getLocaleDate();
        $this->convertHelper = $convertHelper;
        $this->order = $order;
        $this->timezone = $timezone;
    }

    public function getCustomer()
    {
        if (!($customerId = $this->customerSession->getCustomerId())) {
            return false;
        } else {
            return $this->customerSession->getCustomer();
        }
    }

    /**
     * @return \Totaltools\Pronto\Model\InstoreOrder | false
     */
    public function getInstoreOrder()
    {
        if (!($customerId = $this->getCustomer()->getId())) {
            return false;
        }

        if ($this->getRequest()->getParams('id')) {
            $order = $this->instoreOrderFactory->create()->load($this->getRequest()->getParam('id'));
            if ($order->getId()) {
                return $order;
            }
        }

        return false;
    }

    /**
     * @return \Totaltools\Pronto\Model\ResourceModel\InstoreOrderItem\Collection | false
     */
    public function getOrderItems()
    {
        $order = $this->getInstoreOrder();
        if ($order->getId()) {
            return $order->getOrderItems();
        } else {
            return false;
        }
    }

    /**
     * @return string
     */
    public function currency($value)
    {
        return $this->dataCurrency->currency($value);
    }

    /**
     * @param $value
     * @return mixed
     */
    public function convertString($value)
    {
        return $this->convertHelper->convertString($value);
    }

    public function getOrderDate($_order)
    {
        $orderId = $_order->getOnlineOrderId();
        $order = $this->order->loadByIncrementId($orderId);
        if (!empty($order->getId())) {
            
            $orderStoreDate = $this->formatDate(
                $order->getCreatedAt(),
                \IntlDateFormatter::MEDIUM,
                true,
                $this->getTimezoneForStore($order->getStore())
            );

            $orderCreatedDate = $this->formatDate(
                $this->getOrderCreatedDate($order->getCreatedAt()),
                \IntlDateFormatter::MEDIUM,
                true
            );

            if ( $orderCreatedDate != $orderStoreDate ) 
            {
                return $orderStoreDate;
            } else {
                return $orderCreatedDate;
            }   
        } else {
            return $_order->getTransDate();
        }
    }

    /**
     * Get timezone for store
     *
     * @param mixed $store
     * @return string
     */
    public function getTimezoneForStore($store)
    {
        return $this->_localeDate->getConfigTimezone(
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store->getCode()
        );
    }

    /**
     * Get object created at date
     *
     * @param string $createdAt
     * @return \DateTime
     */
    public function getOrderCreatedDate($createdAt)
    {
        return $this->_localeDate->date(new \DateTime($createdAt));
    }
}
