<?php
/**
 * @package Totaltools_Layalty
 * <AUTHOR>
 * @copyright 2023 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Loyalty\Block\Insider;

/**
 * check order block
 * @SuppressWarnings(PHPMD.ExcessivePublicCount)
 */
class CheckOrder extends \Magento\Framework\View\Element\Template
{
    /**
     * @var InstoreOrderCollectionFactory
     */
    protected $instoreOrderFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;


    /**
     * checkorder constructor.
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory
     * @param \Totaltools\Loyalty\Model\CustomerSession $customerSession
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->instoreOrderFactory = $instoreOrderFactory;
        $this->customerSession = $customerSession;
    }

    public function getCustomer()
    {
        if (!($customerId = $this->customerSession->getCustomerId())) {
            return false;
        } else {
            return $this->customerSession->getCustomer();
        }
    }

    /**
     * @return bool
     */
    public function getOrderData($prontoOrderId, $customerId)
    {
        $instoreOrder = $this->instoreOrderFactory->create();
        $connection = $instoreOrder->getResourceCollection()->getConnection();
        $instoreTable = $connection->getTableName('totaltools_instore_order');
        $query = $connection->select()
        ->from(['c' => $instoreTable], ['order_no', 'customer_id'])
        ->where(
            "c.customer_id = $customerId"
        )->where(
            "c.entity_id = $prontoOrderId"
        );
        $result = $connection->fetchAll($query);
        if(!empty($result)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * @return bool
     */
    public function getIsOrderExist($prontoOrderId)
    {
        $instoreOrder = $this->instoreOrderFactory->create();
        $connection = $instoreOrder->getResourceCollection()->getConnection();
        $instoreTable = $connection->getTableName('totaltools_instore_order');
        $query = $connection->select()
        ->from(['c' => $instoreTable], ['order_no', 'customer_id'])
        ->where(
            "c.entity_id = $prontoOrderId"
        );
        $result = $connection->fetchAll($query);
        if(!empty($result)) {
            return true;
        } else {
            return false;
        }
    }



}
