<?php

/**
 * <AUTHOR> Internet
 * @package Totaltools_Loyalty
 */
namespace Totaltools\Loyalty\Block\Insider;

/**
 * print order block
 * @SuppressWarnings(PHPMD.ExcessivePublicCount)
 */
class PrintOrders extends \Magento\Framework\View\Element\Template
{

    /**
     * @var InstoreOrderCollectionFactory
     */
    protected $instoreOrderFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @var \Magento\Directory\Model\RegionFactory
     */
    protected $regionFactory;

    /**
     * @var \Magento\Sales\Model\OrderFactory
     */
    protected $salesOrderFactory;

    /**
     * @var \Magento\Catalog\Model\Product\OptionFactory
     */
    protected $_productOptionFactory;

    /**
     * @var \Totaltools\Pronto\Model\ResourceModel\InstoreOrder\Collection
     */
    protected $orders;

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    protected $dataCurrency;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    protected $timezoneInterface;

    /**
     * PrintOrders constructor.
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory
     * @param \Totaltools\Loyalty\Model\CustomerSession $customerSession
     * @param \Magento\Directory\Model\RegionFactory $regionFactory
     * @param \Magento\Sales\Model\OrderFactory $salesOrderFactory
     * @param \Magento\Catalog\Model\Product\OptionFactory $productOptionFactory
     * @param \Magento\Framework\Pricing\Helper\Data $dataCurrency
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Magento\Directory\Model\RegionFactory $regionFactory,
        \Magento\Sales\Model\OrderFactory $salesOrderFactory,
        \Magento\Catalog\Model\Product\OptionFactory $productOptionFactory,
        \Magento\Framework\Pricing\Helper\Data $dataCurrency,
        array $data = []
    )
    {
        parent::__construct($context, $data);
        $this->instoreOrderFactory = $instoreOrderFactory;
        $this->customerSession = $customerSession;
        $this->regionFactory = $regionFactory;
        $this->salesOrderFactory = $salesOrderFactory;
        $this->_productOptionFactory = $productOptionFactory;
        $this->dataCurrency = $dataCurrency;
        $this->timezoneInterface = $context->getLocaleDate();
    }

    public function getCustomer()
    {
        if (!($customerId = $this->customerSession->getCustomerId())) {
            return false;
        } else {
            return $this->customerSession->getCustomer();
        }
    }

    /**
     * @return array
     */
    public function getOrders()
    {
        if (!($customerId = $this->customerSession->getCustomerId())) {
            return false;
        }
        if (!$this->orders) {
            $instoreOrder = $this->instoreOrderFactory->create();
            $connection = $instoreOrder->getResourceCollection()->getConnection();
            $instoreTable = $connection->getTableName('totaltools_instore_order');
            $salesTable = $connection->getTableName('sales_order');

            $selectInstore = 'SELECT order_no as order_id, trans_date as order_date FROM ' . $instoreTable . '
            WHERE customer_id =' . $customerId;

            $selectSales = 'SELECT entity_id as order_id, created_at as order_date FROM ' . $salesTable . '
            WHERE customer_id =' . $customerId;

            if ($this->getRequest()->getParam('orders_from')) {
                $selectInstore .= " AND trans_date >= '" . $this->getRequest()->getParam('orders_from') . "'";
                $selectSales .= " AND created_at >= '" . $this->getRequest()->getParam('orders_from') . "'";
            }

            if ($this->getRequest()->getParam('orders_to')) {
                $selectInstore .= " AND trans_date >= '" . $this->getRequest()->getParam('orders_to') . "'";
                $selectSales .= " AND created_at >= '" . $this->getRequest()->getParam('orders_to') . "'";
            }
            $query = $selectInstore . ' UNION ' . $selectSales . ' ORDER BY order_date';

            $result = $connection->fetchAll($query);

            $this->orders = $result;
        }
        return $this->orders;
    }


    /**
     * @param string $orderId
     * @return Object | false
     *
     * */
    public function getOrderInfo($orderId)
    {
        $order = $this->instoreOrderFactory->create()->loadByOrderNo($orderId);

        if (!$order->getId()) {
            $order = $this->salesOrderFactory->create()->load($orderId);
        }
        if ($order->getId()) {
            return $order;
        } else {
            return false;
        }
    }

    /**
     * @param string $configPath
     * @return string
     *
     * */
    public function getConfig($configPath)
    {
        if ($configPath == 'general/store_information/region_id') {
            $regionId = $this->_scopeConfig->getValue($configPath);
            $region = $this->regionFactory->create()->load($regionId);
            if ($region->getId()) {
                return $region->getName();
            } else {
                return $regionId;
            }
        } else {
            return $this->_scopeConfig->getValue($configPath);
        }
    }

    /**
     * @return string
     *
     * */
    public function getFilterFrom()
    {
        if ($this->getRequest()->getParam('orders_from')) {
            return $this->getDatetime($this->getRequest()->getParam('orders_from'));
        } else {
            return false;
        }

    }

    /**
     * @return string
     *
     * */
    public function getFilterTo()
    {
        if ($this->getRequest()->getParam('orders_to')) {
            return $this->getDatetime($this->getRequest()->getParam('orders_to'));
        } else {
            return $this->getDatetime();
        }
    }

    /**
     * @param \Magento\Sales\Model\Order\Item
     * @return array
     */
    public function getItemOptions($orderItem)
    {
        $result = [];
        $options = $orderItem->getProductOptions();
        if ($options) {
            if (isset($options['options'])) {
                $result = array_merge($result, $options['options']);
            }
            if (isset($options['additional_options'])) {
                $result = array_merge($result, $options['additional_options']);
            }
            if (isset($options['attributes_info'])) {
                $result = array_merge($result, $options['attributes_info']);
            }
        }
        return $result;
    }

    /**
     * Accept option value and return its formatted view
     *
     * @param mixed $optionValue
     * Method works well with these $optionValue format:
     *      1. String
     *      2. Indexed array e.g. array(val1, val2, ...)
     *      3. Associative array, containing additional option info, including option value, e.g.
     *          array
     *          (
     *              [label] => ...,
     *              [value] => ...,
     *              [print_value] => ...,
     *              [option_id] => ...,
     *              [option_type] => ...,
     *              [custom_view] =>...,
     *          )
     *
     * @return array
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    public function getFormatedOptionValue($optionValue)
    {
        $optionInfo = [];

        // define input data format
        if (is_array($optionValue)) {
            if (isset($optionValue['option_id'])) {
                $optionInfo = $optionValue;
                if (isset($optionInfo['value'])) {
                    $optionValue = $optionInfo['value'];
                }
            } elseif (isset($optionValue['value'])) {
                $optionValue = $optionValue['value'];
            }
        }

        // render customized option view
        if (isset($optionInfo['custom_view']) && $optionInfo['custom_view']) {
            $_default = ['value' => $optionValue];
            if (isset($optionInfo['option_type'])) {
                try {
                    $group = $this->_productOptionFactory->create()->groupFactory($optionInfo['option_type']);
                    return ['value' => $group->getCustomizedView($optionInfo)];
                } catch (\Exception $e) {
                    return $_default;
                }
            }
            return $_default;
        }

        // truncate standard view
        $result = [];
        if (is_array($optionValue)) {
            $truncatedValue = implode("\n", $optionValue);
            $truncatedValue = nl2br($truncatedValue);
            return ['value' => $truncatedValue];
        } else {
            $truncatedValue = $this->filterManager->truncate($optionValue, ['length' => 55, 'etc' => '']);
            $truncatedValue = nl2br($truncatedValue);
        }

        $result = ['value' => $truncatedValue];

        if ($this->string->strlen($optionValue) > 55) {
            $result['value'] = $result['value'] . ' <a href="#" class="dots tooltip toggle" onclick="return false">...</a>';
            $optionValue = nl2br($optionValue);
            $result = array_merge($result, ['full_view' => $optionValue]);
        }

        return $result;
    }

    /**
     * get datetime format
     *
     * @param string $date
     * @param string $format
     * @return string
     */
    public function getDatetime($date = null, $format='d/m/Y')
    {
        return $this->timezoneInterface->date($date)->format($format);
    }

    /**
     * get currency format
     *
     * @param float $value
     * @return string
     */
    public function getCurrency($value)
    {
        return $this->dataCurrency->currencyByStore($value);
    }

    /**
     * get number format for qty
     *
     * @param float $value
     * @return int
     */
    public function getQtyFormat($value)
    {
        return number_format($value, 0);
    }
}
