<?php
/**
 * <AUTHOR> Internet
 * @package Totaltools_Loyalty
 */

namespace Totaltools\Loyalty\Block\Insider;

use \Totaltools\Pronto\Model\Source\InsiderLevel as ProntoInsiderLevel;

/**
 * Block for displaying a different static block based on the
 * currently logged in customer's loyalty level.
 */
class Content
    extends \Magento\Cms\Block\Block
{

    const STATIC_BLOCK_ID_NONE    = 'account_insider_none';
    const STATIC_BLOCK_ID_INSIDER = 'account_insider_insider';
    const STATIC_BLOCK_ID_PLUS    = 'account_insider_plus';
    const STATIC_BLOCK_ID_MAX     = 'account_insider_max';


    /**
     * @var \Magento\Customer\Helper\Session\CurrentCustomer
     */
    protected $currentCustomerHelper;


    /**
     * @param \Magento\Customer\Helper\Session\CurrentCustomer $currentCustomerHelper
     * @param \Magento\Framework\View\Element\Context          $context
     * @param \Magento\Cms\Model\Template\FilterProvider       $filterProvider
     * @param \Magento\Store\Model\StoreManagerInterface       $storeManager
     * @param \Magento\Cms\Model\BlockFactory                  $blockFactory
     * @param array                                            $data
     */
    public function __construct(
        \Magento\Customer\Helper\Session\CurrentCustomer $currentCustomerHelper,
        \Magento\Framework\View\Element\Context $context,
        \Magento\Cms\Model\Template\FilterProvider $filterProvider,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Cms\Model\BlockFactory $blockFactory,
        array $data = []
    ) {
        parent::__construct($context, $filterProvider, $storeManager, $blockFactory, $data);
        $this->currentCustomerHelper = $currentCustomerHelper;
    }


    /**
     * Get static block ID for customer's loyalty level
     *
     * @param  int $customerLoyaltyLevel
     * @return string
     */
    protected function getStaticBlockId($customerLoyaltyLevel)
    {
        switch ($customerLoyaltyLevel) {
            case ProntoInsiderLevel::INSIDER:
                $staticBlockId = self::STATIC_BLOCK_ID_INSIDER;
                break;
            case ProntoInsiderLevel::INSIDER_PLUS:
                $staticBlockId = self::STATIC_BLOCK_ID_PLUS;
                break;
            case ProntoInsiderLevel::INSIDER_MAX:
                $staticBlockId = self::STATIC_BLOCK_ID_MAX;
                break;
            default:
                $staticBlockId = self::STATIC_BLOCK_ID_NONE;
                break;
        }
        return $staticBlockId;
    }


    /**
     * Get current logged in customer's loyalty level
     *
     * @return int|null
     */
    public function getCustomerLoyaltyLevel()
    {
        $customerLoyaltyLevel = null;

        $customer = $this->currentCustomerHelper->getCustomer();
        if ($customer) {
            $customerLoyaltyLevelAttr = $customer->getCustomAttribute('loyalty_level');
            if ($customerLoyaltyLevelAttr) {
                $customerLoyaltyLevel = intval($customerLoyaltyLevelAttr->getValue());
            }
        }

        return $customerLoyaltyLevel;
    }


    /**
     * Set static block ID & call parent
     *
     * @return string
     */
    protected function _toHtml()
    {
        $customerLoyaltyLevel = $this->getCustomerLoyaltyLevel();
        $staticBlockId = $this->getStaticBlockId($customerLoyaltyLevel);

        $this->setBlockId($staticBlockId);

        return parent::_toHtml();
    }

}
