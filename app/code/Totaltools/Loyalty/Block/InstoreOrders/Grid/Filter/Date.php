<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Loyalty\Block\InstoreOrders\Grid\Filter;

/**
 * Class Date
 * @package Totaltools\Loyalty\Block\InstoreOrders\Grid\Filter
 */
class Date extends \Magento\Framework\View\Element\Template
{
    /**
     * @return string
     */
    public function getInsiderInfoUrl()
    {
        return $this->getUrl('loyalty/insider/invoices');
    }

    /**
     * @return string
     */
    protected function getPath()
    {
        return $this->_getData('path') ?: '*/*/*';
    }

    /**
     * @return string
     */
    public function getDateValidatorPath()
    {
        return $this->getUrl('*/*/date');
    }
}