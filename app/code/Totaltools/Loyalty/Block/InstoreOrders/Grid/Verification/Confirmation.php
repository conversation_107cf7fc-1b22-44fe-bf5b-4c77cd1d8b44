<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
namespace Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification;

use Magento\Customer\Model\SessionFactory;
use Magento\Framework\View\Element\Template\Context;
use Totaltools\Customer\Helper\AttributeData as CustomerHelper;
use Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification;
use Totaltools\Loyalty\Model\CustomerSession;

/**
 * Confirmation
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
class Confirmation extends Verification
{
    const MESSAGE_RESEND_DURATION = "totaltools_customer/sendsms/minutes_duration";

    /**
     * SessionFactory
     *
     * @var SessionFactory
     */
    protected $session;

    /**
     * Confirmation constructor.
     *
     * @param Context         $context
     * @param CustomerSession $customerSession
     * @param CustomerHelper  $customerHelper
     * @param array           $data
     */
    public function __construct(
        Context $context,
        CustomerSession $customerSession,
        CustomerHelper $customerHelper,
        array $data = []
    ) {
        parent::__construct($context, $customerSession, $customerHelper, $data);
        $this->session = $this->customerSession->getCustomerSession();
    }

    /**
     * Returns form action url.
     *
     * @return string
     */
    public function getFromActionUrl()
    {
        return $this->_urlBuilder->getUrl(
            'loyalty/insider/verification',
            [
                '_secure' => $this->getRequest()->isSecure()
            ]
        );
    }

    /**
     * Returns sms confirmation url
     *
     * @return string
     */
    public function getSmsFormActionUrl()
    {
        return $this->_urlBuilder->getUrl(
            'customer/account/verifysms',
            [
                '_secure' => $this->getRequest()->isSecure()
            ]
        );
    }

    /**
     * Returns sms resend url
     *
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getResendActionUrl()
    {
        return $this->getUrl("customer/account/resend/");
    }

    /**
     * Returns previously sent sms time
     *
     * @return bool
     */
    public function getLastTextTime()
    {
        $sentBeforeAt = $this->session->getLastVerificationSmsTime();
        if (isset($sentBeforeAt)) {
            return time() - $sentBeforeAt;
        }
        return 0;
    }

    /**
     * Returns time limit before next sms can be sent
     * 
     * @return float|int
     */
    public function getSmsDuration()
    {
        return (int)$this->_scopeConfig->getValue(self::MESSAGE_RESEND_DURATION) * 60;
    }

    /**
     * Get Agreement
     *
     * @return mixed
     */
    public function getAgreementText()
    {
        $text = '';
        try {
            $text = $text  . ' ' . $this->getLayout()
                ->createBlock('Magento\Cms\Block\Block')
                ->setBlockId('customer_data_modification_agreement')
                ->toHtml();
        } catch (\Exception $exception) {
            $this->_logger->error($exception->getMessage());
        }
        return $text;

    }
}