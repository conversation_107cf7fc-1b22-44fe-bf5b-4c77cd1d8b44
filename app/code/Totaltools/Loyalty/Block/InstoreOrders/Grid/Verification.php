<?php

namespace Totaltools\Loyalty\Block\InstoreOrders\Grid;

use Totaltools\Loyalty\Model\CustomerSession as CustomerSession;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use Totaltools\Customer\Helper\AttributeData as CustomerHelper;

class Verification extends \Magento\Framework\View\Element\Template
{
    /**
     * @const string Manual Verification Required block name.
     */
    const MANUAL_VERIFICATION_REQUIRED_BLOCK_NAME = 'manual_verification_required_block';

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var string Email Verification Status value.
     */
    protected $emailVerificationStatus;

    /**
     * @var CustomerHelper
     */
    protected $customerHelper; 

    /**
     * Verification constructor.
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param CustomerSession                                  $customerSession
     * @param CustomerHelper                                   $customerHelper
     * @param array                                            $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        CustomerSession $customerSession,
        CustomerHelper $customerHelper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->customerSession = $customerSession;
        $this->customerHelper = $customerHelper;
    }

    /**
     * Get email verification status.
     *
     * @return string
     */
    public function getEmailVerificationStatus()
    {
        if ($this->emailVerificationStatus === null) {
            $this->emailVerificationStatus = $this->customerSession
                ->getCustomer()
                ->getData('email_verification_status');
        }

        return $this->emailVerificationStatus;
    }

    /**
     * Check whether customer is verified.
     *
     * @return bool
     */
    public function isCustomerVerified()
    {
        return $this->getEmailVerificationStatus() === EmailVerification::VERIFIED;
    }

    /**
     * Check whether customer is required for manual verification.
     *
     * @return bool
     */
    public function isManualVerificationRequired()
    {
        return $this->getEmailVerificationStatus() === EmailVerification::MANUAL_VERIFICATION_REQUIRED;
    }

    /**
     * Check whether customer is required for email verification.
     *
     * @return bool
     */
    public function isEmailVerificationRequired()
    {
        return $this->getEmailVerificationStatus() === EmailVerification::PENDING_VERIFICATION;
    }

    /**
     * Check whether customer account is in pending sync state.
     *
     * @return bool
     */
    public function isCustomerInPendingSync()
    {
        return $this->getEmailVerificationStatus() === EmailVerification::PENDING_SYNC;
    }

    /**
     * Get Pending Sync Message display status.
     *
     * @return string
     */
    public function showPendingSyncMessage()
    {
        return $this->_scopeConfig->getValue('totaltools_loyalty/confirmation_email/show_pending_sync_message');
    }

    /**
     * Get Pending Sync Message display status.
     *
     * @return string
     */
    public function getPendingSyncMessage()
    {
        return $this->_scopeConfig->getValue('totaltools_loyalty/confirmation_email/pending_sync_msg');
    }

    /**
     * Get Manual Verification Required block.
     *
     * @return string
     */
    public function getManualVerificationRequiredBlock()
    {
        try {
            return $this->getLayout()
                ->createBlock(\Magento\Cms\Block\Block::class)
                ->setBlockId(self::MANUAL_VERIFICATION_REQUIRED_BLOCK_NAME)
                ->toHtml();
        } catch (\Magento\Framework\Exception\LocalizedException $exception) {
            return '';
        }
    }

    /**
     * Check whether is B2B customer or not.
     * 
     * @return boolean
     */
    public function isB2BCustomer()
    {
        return $this->customerHelper->isB2bCustomer();
    }
}