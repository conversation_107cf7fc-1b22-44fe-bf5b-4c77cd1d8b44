<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Loyalty
 */

namespace Totaltools\Loyalty\Block\InstoreOrders;

use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use \Totaltools\Pronto\Model\ResourceModel\InstoreOrder\CollectionFactory as InstoreOrderCollectionFactory;

/**
 * grid block
 *
 * @SuppressWarnings(PHPMD.ExcessivePublicCount)
 */
class Grid extends \Magento\Framework\View\Element\Template
{

    const MY_INVOICES_LAYOUT_NAME = 'loyalty.instoreorder.grid';

    const RECENT_INVOICES_LAYOUT_NAME = 'loyalty.recent.invoices.grid';

    /**
     * @var InstoreOrderCollectionFactory
     */
    protected $instoreOrderCollectionFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    protected $pricingHelper;

    /**
     * @var \Totaltools\Pronto\Model\ResourceModel\InstoreOrder\Collection;
     */
    protected $orders;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var \Shippit\Shipping\Model\Sync\Order
     */
    private $syncOrder;

    /**
     * @var \Shippit\Shipping\Model\ResourceModel\Sync\Order
     */
    private $orderResource;

    /**
     * @var \Magento\Sales\Api\Data\OrderInterface
     */
    private $order;

    /**
     * @var \Totaltools\Shippit\Helper\Data
     */
    private $helper;

    /**
     * Grid constructor.
     *
     * @param \Magento\Framework\View\Element\Template\Context   $context
     * @param InstoreOrderCollectionFactory                      $instoreOrderCollectionFactory
     * @param \Totaltools\Loyalty\Model\CustomerSession          $customerSession
     * @param \Magento\Framework\Pricing\Helper\Data             $pricingHelper
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Shippit\Shipping\Model\Sync\Order                 $syncOrder
     * @param \Shippit\Shipping\Model\ResourceModel\Sync\Order   $orderResource
     * @param \Magento\Sales\Api\Data\OrderInterface             $order
     * @param \Totaltools\Shippit\Helper\Data                    $helper
     * @param array                                              $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        InstoreOrderCollectionFactory $instoreOrderCollectionFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Magento\Framework\Pricing\Helper\Data $pricingHelper,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Shippit\Shipping\Model\Sync\Order $syncOrder,
        \Shippit\Shipping\Model\ResourceModel\Sync\Order $orderResource,
        \Magento\Sales\Api\Data\OrderInterface $order,
        \Totaltools\Shippit\Helper\Data $helper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->instoreOrderCollectionFactory = $instoreOrderCollectionFactory;
        $this->customerSession = $customerSession;
        $this->pricingHelper = $pricingHelper;
        $this->scopeConfig = $scopeConfig;
        $this->syncOrder = $syncOrder;
        $this->orderResource = $orderResource;
        $this->order = $order;
        $this->helper = $helper;
    }

    /**
     * @return \Magento\Customer\Model\Customer
     */
    protected function getCustomer()
    {
        return $this->customerSession->getCustomer();
    }

    /**
     * @return bool
     */
    public function isCustomerVerified()
    {
        return $this->getCustomer()->getData('email_verification_status') === EmailVerification::VERIFIED;
    }

    /**
     * @return bool|\Magento\Sales\Model\ResourceModel\Order\Collection
     */
    public function getInstoreOrders()
    {
        if (!$this->isCustomerVerified()) {
            return false;
        }
        if (!$this->orders) {
            $layoutName = $this->getNameInLayout();

            $this->orders = $this->instoreOrderCollectionFactory->create()
                ->addFieldToSelect(['entity_id','order_no','trans_date','store_name','online_order_id'])
                ->addFieldToFilter('customer_id', $this->getCustomer()->getId())
                ->setOrder('trans_date', 'desc');
            if ($layoutName === self::RECENT_INVOICES_LAYOUT_NAME) {
                $transactionsShow = $this->scopeConfig->getValue(
                    'totaltools_loyalty/insider_rewards/transactions_recent_invoices',
                    \Magento\Store\Model\ScopeInterface::SCOPE_STORE
                );
                if ($transactionsShow) {
                    $this->orders->setPageSize($transactionsShow)
                        ->setCurPage(1);
                }
            }
            $condition = array();
            if ($this->getRequest()->getParam('orders_from')) {
                $condition['from'] = $this->formatRequestDate($this->getRequest()->getParam('orders_from'));
            }
            if ($this->getRequest()->getParam('orders_to')) {
                $condition['to']  = $this->formatRequestDate($this->getRequest()->getParam('orders_to'));
            }
            if (count($condition) > 0 && $layoutName === self::MY_INVOICES_LAYOUT_NAME) {
                $this->orders->addFieldToFilter('trans_date', $condition);
            }
        }

        return $this->orders;
    }

    /**
     * @param  string $date
     * @return string
     */
    private function formatRequestDate(string $date)
    {
        return date('Y-m-d', strtotime(str_replace('/', '-', $date)));
    }

    /**
     * @return $this
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        $instoreOrders = $this->getInstoreOrders();

        if ($instoreOrders) {
            $this->getChildBlock('pager')->setCollection($instoreOrders);
        }

        return $this;
    }

    /**
     * @return string
     */
    public function getPagerHtml()
    {
        return $this->getChildHtml('pager');
    }

    /**
     * @param  object $instoreOrder
     * @return string
     */
    public function getViewUrl($instoreOrder)
    {
        return $this->getUrl('loyalty/insider/view', ['id' => $instoreOrder->getId()]);
    }

    /**
     * @param object $instoreOrder
     * @return string
     */
    public function getDownloadUrl($instoreOrder)
    {
        return $this->getUrl('loyalty/insider/download', ['id' => $instoreOrder->getOrderNo()]);
    }

    /**
     * @param  object $instoreOrder
     * @return string
     */
    public function getPdfUrl($instoreOrder)
    {
        return $this->getUrl('loyalty/insider/printview', ['id' => $instoreOrder->getId()]);
    }

    /**
     * retrieve current url
     * */
    public function getCsvPath()
    {
        return $this->getUrl('*/*/download', array('_query' => $this->getRequest()->getParams()));
    }

    /**
     * @return string
     */
    public function getEmailPath()
    {
        return $this->getUrl('*/*/email');
    }

    /**
     * Format order total
     *
     * @param  float $total
     * @return string
     */
    public function formatTotal($total)
    {
        return $this->pricingHelper->currency($total, true, false);
    }

    /**
     * Get customer is loyal
     *
     * @return boolean
     */
    public function getIsLoyal()
    {
        return $this->customerSession->getCustomer()->getData('is_loyal');
    }

    /**
     * @return string
     */
    public function getInvoicesCookieName()
    {
        return \Totaltools\Loyalty\Helper\Cookie::SELECTED_INVOICES_COOKIE_NAME;
    }

    /**
     * @return bool
     */
    public function customerVerified()
    {
        return empty($this->getCustomer()->getData('unverified_loyalty_id'));
    }

    /**
     * @return string
     */
    public function getViewAllUrl()
    {
        return $this->getUrl('loyalty/insider/invoices');
    }

    /**
     * Get Order tracking code
     *
     * @param $orderId
     *
     * @return string
     */
    public function getTracking($orderId)
    {
        $orderId = $this->getOrderByIncrementId($orderId);
        $this->orderResource->load($this->syncOrder, $orderId, 'order_id');
        if ($orderId != $this->syncOrder->getOrderId()) {
            return '';
        }
        $shippitTrackingNumber = $this->syncOrder->getTrackingNumber();
        if ($shippitTrackingNumber) {
            return $this->decorateTrackNumber($shippitTrackingNumber);
        }
        return '';
    }

    /**
     *  Process Tracking Number
     *
     * @param $value
     *
     * @return string
     */
    public function decorateTrackNumber($value)
    {

        $shippingMethod = $this->syncOrder->getShippingMethod();
        if ($shippingMethod == 'click_and_collect') {
            $tooltip = 'You will receive an email once your order is ready for collection at your selected Total Tools Store';
            $value = sprintf('<img width="24px" src="%s" title="%s">', $this->getViewFileUrl('Totaltools_Loyalty::images/info-64.png'), $tooltip);
            return $value;
        }
        if (empty($value)) {
            return $value;
        }
        $url = $this->helper->getEnvironmentUrl().$value;
        $cell = sprintf(
            '<a href="%s" title="Track Shipment" target="_blank">%s</a>',
            $url,
            __('Track Shipment')
        );
        return $cell;
    }

    /**
     * Get order by Increment ID
     *
     * @param $incrementId
     *
     * @return mixed
     */
    public function getOrderByIncrementId($incrementId)
    {
        $order = $this->order->loadByIncrementId($incrementId);
        if ($incrementId == $order->getIncrementId()) {
            return $order->getEntityId();
        }
        return null;
    }
}
