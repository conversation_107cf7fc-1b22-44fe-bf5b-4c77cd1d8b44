<?php

// @codingStandardsIgnoreFile

/**
 * @var \Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification $block
 */
$_customer = $block->getCustomer()['customerData'];
if(isset($_customer) && !is_null($_customer)) {
?>
    <p class="greeting"><?php echo __('Dear %1,', $_customer->getName() ) ?></p>
    <p><?php echo __('Please confirm your %1 account', $_customer->getEmail() ) ?></p>

    <table  width="100%" border="0" cellspacing="0" cellpadding="0">
        <tr>
            <td>
                <table class="inner-wrapper" border="0" cellspacing="0" cellpadding="0" align="center">
                    <tr>
                        <td align="center" class="default-theme-btn" style="vertical-align:top;text-align:center;text-decoration:none;background-color:#e41b13;text-transform:uppercase;font-weight:700;padding:12px 10px;height:auto;color:#fff;width:250px;font-family:'Arial';">
                            <a href="<?php echo $block->getVerificationUrl() ?>" target="_blank" style="text-transform:uppercase;display:inline-block;padding:0;font-weight:700;font-size:16px;text-decoration:none;color:#fff;width: 100%;" ><?php echo __('Confirm Your Account') ?></a>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
<?php
}
?>

