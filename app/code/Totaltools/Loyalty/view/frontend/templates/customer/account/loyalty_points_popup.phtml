<?php 
/* @var $block \Totaltools\Loyalty\Block\Info */
if ($block->customerSession->getData('show_loyalty_points_popup') && $block->customerSession->getPoint()) :
    $currencySymbol = $block->getCurrencySymbol();
?>

<div id="loyalty-points-popup">
    <span class="close"></span>
    <h2>Loyalty Points</h2>
    <p>You have <?php echo $currencySymbol . $block->customerSession->getAmount() ?> points available.</p>
</div>
<?php $block->customerSession->unsetData('show_loyalty_points_popup'); ?>
<script>
    require(['jquery'], function($) {
        $(document).ready(function() {
            $("#loyalty-points-popup").prependTo("header .header");
            $("#loyalty-points-popup .close").on("click", function(){
                $(this).parent().css({'opacity': '0', 'pointer-events': 'none'});
            });

            setTimeout(function() { 
                $("#loyalty-points-popup").css({'opacity': '0', 'pointer-events': 'none'});
            }, 9000);
        });
    });
</script>
<?php endif; ?>
