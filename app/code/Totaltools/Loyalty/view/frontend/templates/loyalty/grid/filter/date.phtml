<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

// @codingStandardsIgnoreFile

/**
 * @var Totaltools\Loyalty\Helper\Cookie $helper
 */
$helper = $this->helper('Totaltools\Loyalty\Helper\Cookie');
/**
 * @var \Totaltools\Loyalty\Block\InstoreOrders\Grid\Filter\Date $block
 */
?>
<div class="filters" id="invoices-date-filters" style="float: left;margin-bottom: 10px;">
    <div class="date-box">
        <span class="label"><?php echo __('Start date') ?></span>
        <span class="input-date">
            <input id="orders_from" name="orders_from" value="<?php echo $block->getRequest()->getParam('orders_from') ? $block->getRequest()->getParam('orders_from') : ''?>"/>
        </span>
    </div>
    <div class="date-text">
        <span>-</span>
    </div>
    <div class="date-box">
        <span class="label"><?php echo __('End date') ?></span>
        <span class="input-date">
            <input id="orders_to" name="orders_to" value="<?php echo $block->getRequest()->getParam('orders_to') ? $block->getRequest()->getParam('orders_to') : ''?>"/>
        </span>
    </div>
    <div class="date-text clear-filters">
        <a href="#" id="clear-date-filters">
            <span class="label">
                <?php echo __('Clear filters') ?>
        </span>
        </a>
    </div>
</div>

<script type="text/x-magento-init">
{
    "#invoices-date-filters": {
        "loyalty.datefilter": {
            "buttonImage": "<?= $block->getViewFileUrl('Magento_Theme::images/calendar.png')?>",
            "buttonText": "<?= __('Select Date'); ?>",
            "cookieName": "<?= $helper->getCookieName() ?>",
            "validatorPath": "<?= $block->getDateValidatorPath() ?>",
            "selectDateIds": "#orders_to,#orders_from",
            "clearButton": "#clear-date-filters",
            "baseUrl": "<?= $block->getInsiderInfoUrl() ?>"
        }
    }
}
</script>