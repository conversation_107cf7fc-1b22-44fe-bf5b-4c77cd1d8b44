<?php

/* @var $block \Totaltools\Loyalty\Block\Info */

if (!$block->isLoyal() || !$block->isCustomerStatusVerified()) {
    return;
}
$currencySymbol = $block->getCurrencySymbol();
$customer = $block->getCustomer();
$loyaltyId = $customer->getLoyaltyId(); 
$helper = $this->helper('Totaltools\Loyalty\Helper\Data');
$barCodeUrl = $helper->getConfig('totaltools_loyalty/insider_rewards/barcode_url');
$liveBarCode = $barCodeUrl."?code=$loyaltyId";
?>
<div class="insider-barcode">
    <div class="box-barcode">
        <div class="barcode-title">
            <h2>Scan instore to earn rewards:</h2>
        </div>
    </div>
    <div class="box-barcode">
        <img src="<?= $liveBarCode?>" />
        <div class="barcode-id"><?php echo $loyaltyId; ?></div>
    </div>
</div>
<?php if ($customer->getData('business_account')) : ?>
<div class="trade-reward-banner">
    <img src="<?php echo $this->getViewFileUrl('Totaltools_Loyalty::images/trade-reward-logo.png'); ?>" alt="Trade Rewards" />
    <div class="trade-reward-inner"></div>
</div>
<?php endif; ?>
<div class="loyalty-insider-wrapper">
    <div class="loyalty_insider_info _member">
        <div class="loyalty_member">
            <div class="loyalty_insider_header">
                <h2 class="loyalty_member_header_left"><?= /* @escapeNotVerified */ __('MEMBER') ?></h2>
                <a href="javascript:void(0)" class="loyalty_header_right" data-role="insider-modal-trigger"
                    data-target="member-benefits"><?= /* @escapeNotVerified */ __('Member Benefits') ?></a>
            </div>
            <div class="under">
                <?php if (!empty($block->customerSession->getLevelText()) && in_array(strtolower($block->customerSession->getLevelText() ),array('insider bronze','insider silver','insider gold'))): ?>
                <div class="left-info loyalty_member_image">
                    <img class="member-img"
                        src="<?php echo $this->getViewFileUrl('Totaltools_Loyalty::images/'.str_replace(' ','_',strtolower($block->customerSession->getLevelText())).'.svg'); ?>"
                        alt="" />
                </div>
                <?php else: ?>
                <div class="left-info loyalty_member_image">
                    <img class="member-img"
                        src="<?php echo $this->getViewFileUrl('Totaltools_Loyalty::images/totaltools.svg'); ?>"
                        alt="" />
                </div>
                <?php endif;?>
                <div class="right-info loyalty_member_content">
                    <div class="loyalty_member_content_1">
                        <?= /* @escapeNotVerified */ __('You are ') ?><strong><?php echo $block->customerSession->getLevelText();?></strong>
                    </div>
                    <?php if ($block->customerSession->getNextAmount() > 0) : ?>
                    <div class="loyalty_member_content_2">
                        <?= /* @escapeNotVerified */ __('Spend <span>'. $currencySymbol . $block->customerSession->getNextAmount() .'</span> by ') ?><strong><?php echo $block->customerSession->getTierExpiry() ?></strong>
                    </div>
                    <div class="loyalty_member_content_3">
                        <?= /* @escapeNotVerified */ __('to reach ')?>
                        <span><?php echo $block->customerSession->getNextLevelText()?></span>
                    </div>
                    <?php elseif ($block->customerSession->getSpendToRetain() > 0) : ?>
                    <div class="loyalty_member_content_2">
                        <?= /* @escapeNotVerified */ __('Spend <span>'. $currencySymbol . $block->customerSession->getSpendToRetain() .'</span> by ') ?><strong><?php echo $block->customerSession->getTierExpiry() ?></strong>
                    </div>
                    <div class="loyalty_member_content_3">
                        <?= /* @escapeNotVerified */ __('to retain ')?>
                        <span><?php echo $block->customerSession->getNextLevelText()?></span>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
<?php
$insiderDollars =  $block->customerSession->getAmount();
$insiderDollarsSplit = explode(".", $insiderDollars?? '0.00');
$insiderDollarsAmount =  '<span class="currency-symbol">'.$currencySymbol.'</span>'.$insiderDollarsSplit[0] . '<span class="decimal-dot">.</span><span class="price-decimal">' . $insiderDollarsSplit[1] . '</span>';
?>
    <div class="loyalty_insider_info _point">
        <div class="loyalty_insider_points">
            <div class="loyalty_insider_header">
                <h2 class="loyalty_insider_points_header_left"><?= /* @escapeNotVerified */ __('INSIDER DOLLARS') ?>
                </h2>
                <a href="javascript:void(0)" class="loyalty_header_right" data-role="insider-modal-trigger"
                    data-target="insider-points"><?= /* @escapeNotVerified */ __('Insider Dollars') ?></a>
            </div>
            <div class="under">
                <div class="left-info loyalty_insider_points_content_left">
                    <span class="loyalty_insider_points_content_point"><?php echo  $insiderDollarsAmount ?></span>
                </div>
                <div class="right-info loyalty_insider_points_content_right">
                    <div class="loyalty_insider_points_content_1">
                        <?= /* @escapeNotVerified */ __('You have ') ?><strong><?php echo $currencySymbol . $block->customerSession->getAmount() ?></strong>
                    </div>
                    <div class="loyalty_insider_points_content_2">
                        <strong><?= /* @escapeNotVerified */ __('Insider Dollars to redeem') ?></strong>
                    </div>
                    <div class="loyalty_insider_points_content_3"><?= /* @escapeNotVerified */ __('Expiry: ') ?>
                        <span><?php echo $block->customerSession->getExpiry() ?></span>
                    </div>
                    <p class="loyalty_insider_points_note">This value includes both Insider Dollars and Active Insider Rewards</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="loyalty_insider_comment">
    <span><?= __('* Insider Dollars may take up to 48 hours to appear. Balance includes any Insider Rewards earned and these may expire sooner. Check Active Insider Rewards.'); ?></span>
</div>

<!-- Insider Rewards Modals -->
<div id="member-benefits" data-role="insider-modal" style="display: none">
    <?= $block->getChildHtml('loyalty_insider_info.member_benefits'); ?>
</div>

<div id="insider-points" data-role="insider-modal" style="display: none">
    <?= $block->getChildHtml('loyalty_insider_info.insider_points'); ?>
</div>

<script>
require([
    'jquery',
    'Magento_Ui/js/modal/modal'
], function($, modal) {
    var options = {
        type: 'popup',
        responsive: true,
        innerScroll: true,
        modalClass: 'overview-modal insider-modal',
    };
    var selector = $('[data-role="insider-modal"]');

    if (selector.length) {
        for (var i = 0; i < selector.length; i++) {
            new modal(options, selector[i]);
        }

        $('[data-role="insider-modal-trigger"]').on('click', function() {
            var target = $(this).data('target');
            $('#' + target).modal('openModal');
        });
    }
});
</script>