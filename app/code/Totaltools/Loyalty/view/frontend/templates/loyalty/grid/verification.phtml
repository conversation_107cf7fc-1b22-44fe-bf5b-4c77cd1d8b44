<?php
// @codingStandardsIgnoreFile
/**
 * @var \Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification $block
 */
?>
<?php if($block->isCustomerVerified() === false && !$block->isB2BCustomer()): ?>
        <?php if($block->isManualVerificationRequired()): ?>
            <?php echo $block->getChildHtml('loyalty.instoreorder.grid.confirmation_sms_verification') ?>
        <?php elseif ($block->isEmailVerificationRequired()): ?>
            <?php echo $block->getChildHtml('loyalty.instoreorder.grid.confirmation_email_button') ?>
        <?php elseif ($block->isCustomerInPendingSync() && $block->showPendingSyncMessage() ): ?>
            <div class="block block-dashboard-text">
                <p> <?php echo $block->getPendingSyncMessage() ?></p>
            </div>
        <?php endif; ?>
<?php endif; ?>