<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php /** @var $block \Totaltools\Loyalty\Block\Insider\View */ ?>
<?php
$order = $block->getInstoreOrder();
$items = $order->getId() ? $order->getOrderItems() : [];
$shippingLines = [];
$discountLines = [];
?>
<?php if ($order && count($items)): ?>
    <div class="table-wrapper orders-history">
        <table class="data table table-order-items history" id="my-orders-table">
            <thead>
            <tr>
                <th scope="col" class="col date"><?php /* @escapeNotVerified */ echo __('Item Code') ?></th>
                <th scope="col" class="col shipping"><?php /* @escapeNotVerified */ echo __('Item Description') ?></th>
                <th scope="col" class="col total" align="center"><?php /* @escapeNotVerified */ echo __('QTY') ?></th>
                <th scope="col" class="col total" align="center"><?php /* @escapeNotVerified */ echo __('GST') ?></th>
                <th scope="col" class="col total" align="center"><?php /* @escapeNotVerified */ echo __('Total Price (Inc GST)') ?></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($items as $item):
                $item->setStockDescription(
                    $block->convertString(trim($item->getStockDescription()))
                );
                $item->setStockCode(
                    $block->convertString(trim($item->getStockCode()))
                );
                if ($item->getStockCode() === 'Discount') {
                    $discountLines[] = $item;
                    continue;
                } elseif ($item->getStockCode() === 'Delivery') {
                    $shippingLines[] = $item;
                    continue;
                }
                ?>
                <tr>
                    <td data-th="<?php echo $block->escapeHtml(__('Item Code')) ?>" class="col"><?php /* @escapeNotVerified */ echo $item->getStockCode(); ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('Item Description')) ?>" class="col"><?php echo $item->getStockDescription(); ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('QTY')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $item->getQty() + 0; ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('GST')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $block->currency($item->getLineTax()) ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('Total Price (Inc GST)')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $block->currency($item->getLineValue()) ?></td>
                </tr>
            <?php endforeach; ?>
            <?php foreach ($shippingLines as $item): ?>
                <tr>
                    <td data-th="<?php echo $block->escapeHtml(__('Item Code')) ?>" class="col"><?php /* @escapeNotVerified */ echo $item->getStockCode(); ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('Item Description')) ?>" class="col"><?php echo $item->getStockDescription(); ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('QTY')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $item->getQty() + 0; ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('GST')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $block->currency($item->getLineTax()) ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('Total Price (Inc GST)')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $block->currency($item->getLineValue()) ?></td>
                </tr>
            <?php endforeach; ?>
            <?php foreach ($discountLines as $item): ?>
                <tr>
                    <td data-th="<?php echo $block->escapeHtml(__('Item Code')) ?>" class="col"><?php /* @escapeNotVerified */ echo $item->getStockCode(); ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('Item Description')) ?>" class="col"><?php echo $item->getStockDescription(); ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('QTY')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $item->getQty() + 0; ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('GST')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $block->currency($item->getLineTax()) ?></td>
                    <td data-th="<?php echo $block->escapeHtml(__('Total Price (Inc GST)')) ?>" class="col" align="center"><?php /* @escapeNotVerified */ echo $block->currency($item->getLineValue()) ?></td>
                </tr>
            <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr class="subtotal" align="right">
                    <th class="mark" colspan="4" bgcolor="#F6F7F9">
                        <strong><?php echo __('TOTAL (Inc GST)'); ?></strong>
                    </th>
                    <th class="amount" bgcolor="#F6F7F9"><?php echo $block->currency($order->getOrderTotal()); ?></th>
                </tr>
                <tr class="subtotal" align="right">
                    <th class="mark" colspan="4">
                        <?php echo __('GST'); ?>
                    </th>
                    <th class="amount"><?php echo $block->currency($order->getOrderTax()); ?></th>
                </tr>
            </tfoot>
        </table>
    </div>
<?php endif; ?>