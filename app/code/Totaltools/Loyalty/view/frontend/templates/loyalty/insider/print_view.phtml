<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php /** @var $block \Totaltools\Loyalty\Block\Insider\View */ ?>
<?php
/** @var \Totaltools\Pronto\Model\InstoreOrder $_order */
$_order = $block->getInstoreOrder();
if ($_order): ?>
    <div class="invoice-header">
        <a class="print-logo" href="#">
            <img class="logo-img" src="<?php echo $this->helper('Magento\Cms\Helper\Wysiwyg\Images')->getBaseUrl().'logo/default/logo_1.png'; ?>"/>
        </a>
        <a href="#" class="print-button-invoice" onclick="window.print();">Print</a>
        <div class="invoice-address">
            <table>
                <tbody>
                <tr>
                    <td><strong><?php echo __('STORE:'); ?></strong></td>
                    <td><strong><?php echo $_order->getStoreName(); ?></strong></td>
                </tr>
                <tr>
                    <td><strong><?php echo __('Address:'); ?></strong></td>
                    <td><?php echo $_order->getAddress2(); ?></td>
                </tr>
                <?php if ($address3 = $_order->getAddress3()): ?>
                    <tr>
                        <td></td>
                        <td><?php echo $address3; ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($address4 = $_order->getAddress4()): ?>
                    <tr>
                        <td></td>
                        <td><?php echo $address4; ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($address5 = $_order->getAddress5()): ?>
                    <tr>
                        <td></td>
                        <td><?php echo $address5; ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($address6 = $_order->getAddress6()): ?>
                    <tr>
                        <td></td>
                        <td><?php echo $address6; ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($postCode = $_order->getPostCode()): ?>
                    <tr>
                        <td></td>
                        <td><?php echo $postCode; ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($phone = $_order->getPhone()): ?>
                    <tr>
                        <td><strong><?php echo __('Phone:'); ?></strong></td>
                        <td><?php echo $phone; ?></td>
                    </tr>
                <?php endif; ?>
                <?php if ($email = $_order->getEmail()): ?>
                    <tr>
                        <td><strong><?php echo __('Email:'); ?></strong></td>
                        <td class="email"><?php echo $email; ?></td>
                    </tr>
                <?php endif; ?>
                <tr>
                    <td><strong><?php echo __('Web:'); ?></strong></td>
                    <td><?php echo __('www.totaltools.com.au'); ?></td>
                </tr>
                <?php if ($abn = $_order->getAbn()): ?>
                    <tr>
                        <td><strong><?php echo __('ABN:'); ?></strong></td>
                        <td><strong><?php echo $abn; ?></strong></td>
                    </tr>
                <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
    <div class="block block-order-details-view">
        <div class="tax-invoice-title">
            <h2><?php echo __('Tax Invoice'); ?></h2>
        </div>
        <div class="block-content">
            <div class="block-col-left">
                <p><strong><?php echo __('Invoice No.'); ?></strong></p>
                <p><?php echo $_order->getInvoiceNumber(); ?></p>
                <p>&nbsp;</p>
                <p><strong><?php echo __('Order Date'); ?></strong></p>
                <p><?php echo $block->getOrderDate($_order); ?></p>
            </div>
            <div class="block-col-right">
                <p><strong><?php echo __('Order No.'); ?></strong></p>
                <p><?php echo $_order->getOrderNo(); ?></p>
                <p>&nbsp;</p>
                <?php if ($onlineOrderId = $_order->getOnlineOrderId()): ?>
                    <p><strong><?php echo __('Online Order ID'); ?></strong></p>
                    <p><?php echo $onlineOrderId; ?></p>
                <?php endif; ?>
            </div>
            <div class="block-col-left">
                <?php if ($insiderId = $_order->getInsiderId()): ?>
                    <p><strong><?php echo __('Insider ID:'); ?></strong></p>
                    <p><?php echo $insiderId; ?></p>
                <?php endif; ?>
                <p>&nbsp;</p>
                <?php if ($company = $_order->getCompany()): ?>
                    <p><strong><?php echo __('Company:'); ?></strong></p>
                    <p><?php echo $company; ?></p>
                <?php endif; ?>
            </div>
            <div class="block-col-right">
                <?php if ($firstName = $_order->getFirstName()): ?>
                    <p><strong><?php echo __('First Name:'); ?></strong></p>
                    <p><?php echo $firstName; ?></p>
                <?php endif; ?>
                <p>&nbsp;</p>
                <?php if ($lastName = $_order->getLastName()): ?>
                    <p><strong><?php echo __('Last Name:'); ?></strong></p>
                    <p><?php echo $lastName; ?></p>
                <?php endif; ?>
            </div>
            <div class="block-col-left no-border">
                <?php if ($payments = $_order->getPaymentMethods()): ?>
                    <p><strong><?php echo __('Payment Method'); ?></strong></p>
                    <p><?php echo $payments; ?></p>
                <?php endif; ?>
            </div>
            <div class="block-col-right no-border">
                <p><strong><?php echo __('Invoice Total'); ?></strong></p>
                <p><?php echo $block->currency($_order->getOrderTotal()); ?></p>
            </div>
        </div>
    </div>
    <div class="order-details-items ordered">
        <div class="order-title"><strong><?php echo __('INVOICE SUMMARY'); ?></strong></div>
        <?php echo $block->getChildHtml('', true) ?>
    </div>
<?php else: ?>
    <div class="error-message">
        <?php echo __('The order does not exist'); ?>
    </div>
<?php endif; ?>