<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php /** @var $block \Totaltools\Loyalty\Block\Insider\View */ ?>
<?php
$_order = $block->getInstoreOrder();
if ($_order):?>
<div class="block block-order-details-view">
    <div class="block-title">
        <strong><?= /* @escapeNotVerified */ __('Order Information') ?></strong>
    </div>
    <div class="block-content">
        <div class="invoice-number">
            <b><?= __('Invoice Number: '); ?></b>
            <?= $_order->getInvoiceNumber() ?>
        </div>
        <div class="order-no">
            <b><?= __('Order No: '); ?></b>
            <?= $_order->getOrderNo() ?>
        </div>
        <?php if ($_order->getOnlineOrderId()) { ?>
            <div class="online-order-no">
                <b><?= __('Online Order No: '); ?></b>
                <?= $_order->getOnlineOrderId() ?>
            </div>
        <?php } ?>
        <div class="order-date">
            <b><?= __('Order Date: '); ?></b>
            <?= $block->formatDate($_order->getTransDate()) ?>
        </div>
        <div class="store-name">
            <b><?= __('Store Name: '); ?></b>
            <?= $_order->getStoreName() ?>
        </div>

    </div>
</div>
<div class="order-details-items ordered">
    <div class="order-title"><strong><?= __('Items Ordered'); ?></strong></div>
    <?= $block->getChildHtml('', true) ?>
</div>
<?php else: ?>
    <div class="error-message">
        <?= __('The order does not exist'); ?>
    </div>
<?php endif;?>