<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
/**
 * @var \Totaltools\Loyalty\Block\InstoreOrders\Grid $block
 */
if (!$block->getIsLoyal() || !$block->isCustomerVerified()) return;

$layoutName = $block->getNameInLayout();
/**
 * @var Totaltools\Loyalty\Helper\Cookie $helper
 */
$helper = $this->helper('Totaltools\Loyalty\Helper\Cookie');
$_orders = $block->getInstoreOrders();
?>
<div class="table-wrapper orders-history">
    <?php if ($_orders && count($_orders)): ?>
    <div class="selected-orders-action-buttons">
        <button class="button button-secondary-2 button3" id="select_all_invoices" name="select_all_invoices"><span><?php echo __('Select All Invoices');?></span></button>
        <button class="button button-secondary-2 button3" id="deselect_all_invoices" name="deselect_all_invoices"><span><?php echo __('Deselect All Invoices');?></span></button>
        <button class="button button-secondary-2 button3" id="download_history_pdf" name="download_history_pdf"><span><?php echo __('Download Selected Invoices');?></span></button>
        <button class="button button-secondary-2 button3" id="email-selcted-invoices" name="email_history_pdf"><span><?php echo __('Email Selected Invoices');?></span></button>
    </div>
    <?php endif; ?>
    <?php if ($layoutName === $block::MY_INVOICES_LAYOUT_NAME ): echo $block->getChildHtml('loyalty.instoreorder.grid.filter.date'); ?>
    <?php else: echo $block->getChildHtml('recent.order.title');?>
    <?php endif; ?>
    
    <?php if ($_orders && count($_orders)): ?>
        <table class="data table table-order-items history" id="my-orders-table">
            <thead>
            <tr>
                <th scope="col" class="col"></th>
                <th scope="col" class="col id"><?php echo __('Order No.'); ?></th>
                <th scope="col" class="col id"><?php echo __('Online Order No.'); ?></th>
                <th scope="col" class="col date"><?php echo __('Order Date'); ?></th>
                <th scope="col" class="col store"><?php echo __('Store Name'); ?></th>
                <th scope="col" class="col total"><?php echo __('Amount'); ?></th>
                <th scope="col" class="col actions">&nbsp;</th>
                <th scope="col" class="col actions">&nbsp;</th>
                <th scope="col" class="col actions">&nbsp;</th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($_orders as $_order): ?>
                <tr>
                    <td data-th="<?php echo __('#'); ?>" class="col">
                    <div class="selected-order-id-wrap">
                        <input type="checkbox" name="order_ids[]" value="<?= $_order->getOrderNo() ?>" class="selected-order-id" data-order-id="<?= $_order->getOrderNo() ?>">
                        <label></label>
                    <div>
                    </td>
                    <td data-th="<?php echo __('Order No.'); ?>" class="col id"><?php /* @escapeNotVerified */ echo $_order->getOrderNo() ?></td>
                    <td data-th="<?php echo __('Online Order No.'); ?>" class="col id"><?php /* @escapeNotVerified */ echo $_order->getOnlineOrderId() ?></td>
                    <td data-th="<?php echo __('Order Date'); ?>" class="col date"><?php /* @escapeNotVerified */ echo $block->formatDate($_order->getTransDate()) ?></td>
                    <td data-th="<?php echo __('Store Name'); ?>" class="col store"><?php /* @escapeNotVerified */ echo $_order->getStoreName(); ?></td>
                    <td data-th="<?php echo __('Amount'); ?>" class="col"><?php /* @escapeNotVerified */ echo $block->formatTotal($_order->getOrderTotal()); ?></td>
                    <td data-th="<?php echo __('Actions'); ?>" class="col actions">
                    <?php if ($_order->getStoreName() === 'Total Tools Online') : ?>    <?php /* @escapeNotVerified */ echo $block->getTracking($_order->getOnlineOrderId()); ?> <?php endif; ?>
                    </td>
                    <td data-th="<?php echo __('Actions'); ?>" class="col actions">
                        <a href="<?php /* @escapeNotVerified */ echo $block->getViewUrl($_order) ?>" target="_self" class="action view">
                            <span><?php echo __('View Order') ?></span>
                        </a>
                    </td>
                    <td data-th="<?php echo __('Actions'); ?>" class="col actions">
                        <a href="<?php /* @escapeNotVerified */ echo $block->getDownloadUrl($_order) ?>" target="_self" class="action order">
                            <span><?php echo __('Download') ?></span>
                        </a>
                    </td>
                </tr>
            <?php endforeach; ?>
            </tbody>
        </table>
        <?php if ($block->getPagerHtml()): ?>
            <div class="order-products-toolbar toolbar bottom">
                <?php if ($layoutName === $block::MY_INVOICES_LAYOUT_NAME ): echo $block->getPagerHtml(); ?>
                <?php else: ?>
                    <div class="invoice-view-all">
                        <a href="<?php /* @escapeNotVerified */ echo $block->getViewAllUrl() ?>" class="invoice-view-all-link"><?php echo __('View all') ?></a>
                    </div>
                <?php endif; ?>
               
            </div>
        <?php endif ?>
    <?php else: ?>
        <div class="message info empty" style="clear: both"><span><?php echo __('You have placed no orders.'); ?></span></div>
    <?php endif ?>
</div>
<div id="select-email-form" style="display: none">
    <p>Leave empty for default email</p>
    <form method="post" id="send-invoices-to-form" enctype="multipart/form-data">
        <input type="email" name="send_invoices_to">
    </form>
</div>

<script type="text/x-magento-init">
{
    "#my-orders-table": {
        "loyalty.selectinvoice": {
            "orderSelector": ".selected-order-id",
            "tableId": "#my-orders-table",
            "cookieName" : "<?php echo $block->getInvoicesCookieName(); ?>",
            "selectAllBtn": "#select_all_invoices",
            "deselectAllBtn": "#deselect_all_invoices"
        }
    },
    "#email-selcted-invoices": {
        "loyalty.selectemail": {
            "modalForm": "#select-email-form",
            "orderSelector": ".selected-order-id",
            "modalButton": "#email-selcted-invoices",
            "emailLink": "<?php echo $block->getEmailPath();?>",
            "formId": "#send-invoices-to-form",
            "cookieName" : "<?php echo $block->getInvoicesCookieName(); ?>"
        }
    },
     "#download_history_pdf": {
        "loyalty.downloadinvoices": {
            "urlPath": "<?php echo $block->getCsvPath(); ?>",
            "cookieName" : "<?php echo $helper->getCookieName(); ?>",
            "buttonId" : "#download_history_pdf",
            "orderCheckbox": ".selected-order-id"
        }
    }
}
</script>
