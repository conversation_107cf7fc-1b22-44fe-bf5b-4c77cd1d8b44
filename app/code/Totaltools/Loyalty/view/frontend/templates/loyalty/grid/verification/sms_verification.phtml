<?php
// @codingStandardsIgnoreFile
/**
 * @var \Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification\Confirmation $block
 */
?>
<div class="block block-dashboard-text">
    <?php $lastText = $block->getLastTextTime() ?>
    <?php $duration = $block->getSmsDuration() ?>
    <?php
    $cssClass = ' show ';
    $remaining = 0;
    if ($lastText && $lastText < $duration) {
        $cssClass = ' hidden ';
        $remaining = $duration - $lastText;
    }
    $cssClass = $lastText < $duration ? ' hidden' : ' ';
    ?>
    <form action="<?php /* @escapeNotVerified */ echo $block->getSmsFormActionUrl() ?>" class="form-sms-verification" method="post" id="verification-sms-form"  data-mage-init='{"validation":{}}'>
        <fieldset class="fieldset sms-verification-fieldset">
            <legend class="legend">
                <span><?php /* @escapeNotVerified */ echo __('Your mobile number is not verified') ?></span>
            </legend>
            <div class="field verification_code required">
                <label class="label" for="verification_code"><span><?php /* @escapeNotVerified */ echo __('Verification Code') ?></span></label>
                <div class="control">
                    <input name="verification_code" type="text" id="verification_code" class="input-text" title="<?php /* @escapeNotVerified */ echo __('Verification') ?>" data-validate="{required:true}" placeholder="<?php echo __('Enter Verification Code'); ?>">
                    <div class="field note"><?php /* @escapeNotVerified */ echo __('Please enter verification code that was just sent to your mobile.') ?></div>
                    <div class="field  remaining-time "><?php  echo __('Note: This can take a few minutes.'); ?> <span id="timertext"><?php echo $remaining ? "Retry after <span id='timer'></span> seconds.": '';  ?></span></div>
                </div>
            </div>
            <div class="field required ">
                <div class="control">
                    <div class="options">
                        <input type="checkbox" name="mismatch" title="Update data" value="1" id="mismatch" class="checkbox required" />
                        <label for="mismatch" class="label "><span><?= __($block->getAgreementText()) ?></span></label>
                    </div>
                </div>
            </div>
            <div class="actions-toolbar verification-form-actions">
                <div class="primary">
                    <button type="submit" class="action submit button-primary-3">
                        <span><?php /* @escapeNotVerified */ echo __('Verify') ?></span>
                    </button>
                </div>
                <div id="resend"  class="secondary">
                    <span>Didn't receive a code? </span>
                    <a class="action resend" href="<?php /* @escapeNotVerified */ echo $block->getResendActionUrl() ?>">
                        <span><?php /* @escapeNotVerified */ echo __('Resend') ?></span>
                    </a>
                </div>
            </div>
        </fieldset>
    </form>
</div>
<script>
    var  counter = <?php echo $remaining?>;
    window.onload = function(){
        (function(){
            let span = document.getElementById("timer");
            let resend = document.getElementById("resend");
            let timertext = document.getElementById("timertext");
            setInterval(function() {
                counter--;
                if (counter >= 0) {
                    span.innerHTML = counter;
                }
                if (counter === 0) {
                    clearInterval(counter);
                    resend.classList.remove("hidden");
                    resend.classList.add('show');
                    timertext.classList.add('hidden');
                }
            }, 1000);
        })();
    }
</script>