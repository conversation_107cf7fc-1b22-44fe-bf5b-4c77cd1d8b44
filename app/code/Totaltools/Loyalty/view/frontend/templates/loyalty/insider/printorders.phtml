<?php
/**
 *
 */
/* @var $block \Totaltools\Loyalty\Block\Insider\PrintOrders */
?>
<?php
    $customer = $block->getCustomer();
if ($customer):
?>
<div class="loyalty-insider-header">
    <div class="f-left loyalty-insider-customerinfo">
        <p><?php echo $customer->getSuffix().' '.$customer->getFirstname(). ' '. $customer->getLastname(); ?></p>
        <?php
            $primaryAddress = $customer->getPrimaryAddresses();
            $address = isset($primaryAddress[0]) ? $primaryAddress[0] : false;
            if ($address) :
        ?>
            <?php if ($address->getCompany()): ?>
                <p><?php echo $address->getCompany(); ?></p>
            <?php endif;?>

            <?php $street = $address->getStreet();
                if ($street):
            ?>
                <p><?php echo  $street[0]; ?></p>
            <?php endif; ?>

            <?php if ($address->getRegion()): ?>
                <p><?php echo $address->getRegion().' '. $address->getPostcode(); ?></p>
            <?php endif;?>
        <?php endif; ?>
    </div>
    <div class="f-right loyalty-insider-totaltoolsinfo">
        <p><?php echo $block->getConfig('general/store_information/name');?></p>
        <p><?php echo __('ABN: ').$block->getConfig('general/store_information/abn');?></p>
        <p><?php echo $block->getConfig('general/store_information/street_line1');?></p>
        <p><?php echo $block->getConfig('general/store_information/region_id').' '.$block->getConfig('general/store_information/postcode');?></p>
    </div>
    <div class="clearer"></div>
</div>
<div class="loyalty-insider-ordersummary">
    <p class="title"><?php echo __('Order Summary'); ?></p>
    <p class="date-time">
        <?php
            if ($block->getFilterFrom() && $block->getFilterTo()) {
                echo $block->getFilterFrom(). ' - '. $block->getFilterTo();
            } else if (!$block->getFilterFrom() && $block->getFilterTo()) {
                echo 'To '. $block->getFilterTo();
            }
        ?>
    </p>
    <div class="clearer"></div>
    <div class="order-items">
        <?php
            $orders = $block->getOrders();
            if (sizeof($orders) >0):
        ?>
        <table>
            <thead>
                <tr>
                    <th class="col name" width="8%"><?php /* @escapeNotVerified */ echo __('Order #') ?></th>
                    <th class="col date" width="10%"><?php /* @escapeNotVerified */ echo __('Date') ?></th>
                    <th class="col store" width="15%"><?php /* @escapeNotVerified */ echo __('Store Name') ?></th>
                    <th class="col product_name"><?php /* @escapeNotVerified */ echo __('Product') ?></th>
                    <th class="col qty" width="5%"><?php /* @escapeNotVerified */ echo __('Qty') ?></th>
                    <th class="col price" width="10%"><?php /* @escapeNotVerified */ echo __('Price (ea)') ?></th>
                    <th class="col gst" width="8%"><?php /* @escapeNotVerified */ echo __('GST') ?></th>
                    <th class="col total" width="10%"><?php /* @escapeNotVerified */ echo __('Total') ?></th>
                </tr>
            </thead>
            <tbody>
                <?php $grandTotal = 0; ?>
                <?php foreach ($orders as $order) :?>
                    <?php
                        $order = $block->getOrderInfo($order['order_id']);
                    if ($order instanceof \Magento\Sales\Model\Order) {
                        $orderItems = $order->getAllItems();
                        foreach ($orderItems as $orderItem):
                    ?>
                    <tr>
                        <td class="col"><?php echo $order->getIncrementId(); ?></td>
                        <td class="col"><?php echo $block->getDatetime($order->getCreatedAt()); ?></td>
                        <td class="col"><?php echo __('Total Tools Online'); ?></td>
                        <td class="col">
                            <?php echo $orderItem->getProduct()->getName(); ?>
                            <?php if ($_options = $block->getItemOptions($orderItem)): ?>
                                <dl class="item options">
                                    <?php foreach ($_options as $_option) : ?>
                                        <dt><?php echo $block->escapeHtml($_option['label']) ?></dt>
                                        <?php if (!$block->getPrintStatus()): ?>
                                            <?php $_formatedOptionValue = $block->getFormatedOptionValue($_option) ?>
                                            <dd<?php if (isset($_formatedOptionValue['full_view'])): ?> class="tooltip wrapper"<?php endif; ?>>
                                                <?php /* @escapeNotVerified */ echo $_formatedOptionValue['value'] ?>
                                                <?php if (isset($_formatedOptionValue['full_view'])): ?>
                                                    <div class="tooltip content">
                                                        <dl class="item options">
                                                            <dt><?php echo $block->escapeHtml($_option['label']) ?></dt>
                                                            <dd><?php /* @escapeNotVerified */ echo $_formatedOptionValue['full_view'] ?></dd>
                                                        </dl>
                                                    </div>
                                                <?php endif; ?>
                                            </dd>
                                        <?php else: ?>
                                            <dd><?php echo $block->escapeHtml((isset($_option['print_value']) ? $_option['print_value'] : $_option['value'])) ?></dd>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </dl>
                            <?php endif; ?>
                        </td>
                        <td class="col qty"><?php echo $block->getQtyFormat($orderItem->getQtyOrdered()); ?></td>
                        <td class="col price"><?php echo $block->getCurrency($orderItem->getPrice()); ?></td>
                        <td class="col gst"><?php echo $block->getCurrency($orderItem->getTaxAmount()); ?></td>
                        <td class="col total"><?php echo $block->getCurrency($orderItem->getRowTotalInclTax()); ?></td>
                    </tr>
                    <?php
                            $grandTotal += $orderItem->getRowTotalInclTax();
                        endforeach;
                    } else {
                        /*@var \Totaltools\Pronto\Model\InstoreOrder $order*/
                        $orderItems = $order->getOrderItems();
                        foreach ($orderItems as $orderItem):
                    ?>
                    <tr>
                        <td class="col"><?php echo $order->getOrderNo(); ?></td>
                        <td class="col"><?php echo $block->getDatetime($order->getTransDate()); ?></td>
                        <td class="col"><?php echo $order->getStoreName(); ?></td>
                        <td class="col"><?php echo $orderItem->getStockDescription(); ?></td>
                        <td class="col qty"><?php echo $block->getQtyFormat($orderItem->getQty()); ?></td>
                        <td class="col price"><?php echo $block->getCurrency($orderItem->getLineValue()); ?></td>
                        <td class="col gst"><?php echo $block->getCurrency($orderItem->getLineTax()); ?></td>
                        <td class="col total"><?php echo $block->getCurrency($orderItem->getLineValue()); ?></td>
                    </tr>
                    <?php
                            $grandTotal += $orderItem->getLineValue();
                        endforeach;
                    }
                    ?>
                <?php endforeach; ?>
            </tbody>
        </table>
        <table class="tb-grantotal">
            <thead>
                <tr>
                    <th class="col grandtotal-title"><?php echo __('Grand Totals'); ?></th>
                    <!--<th class="col qty"><?php //echo $block->getCurrency($grantPrice); ?></th>
                    <th class="col gst"><?php //echo $block->getCurrency($grandGst); ?></th>-->
                    <th class="col grandtotal-total" width="10%"><?php echo $block->getCurrency($grandTotal); ?></th>
                </tr>
            </thead>
        </table>
        <?php else: ?>
            <div class="no-item"><?php echo __('You have no orders.'); ?></div>
        <?php endif;?>
    </div>
</div>
<?php endif; ?>