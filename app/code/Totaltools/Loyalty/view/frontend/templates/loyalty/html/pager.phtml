<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
/**
 * Pager template
 *
 * @see \Magento\Theme\Block\Html\Pager
 */
?>
<?php if ($block->getCollection()->getSize()): ?>

    <?php if ($block->getUseContainer()): ?>
    <div class="pager">
    <?php endif ?>
        <?php if ($block->getLastPageNum()>1): ?>
        <div class="pages">
            <strong class="label pages-label" id="paging-label"><?php /* @escapeNotVerified */ echo __('Page') ?></strong>
            <ul class="items pages-items" aria-labelledby="paging-label">
            <?php if (!$block->isFirstPage()): ?>
                <li class="item pages-item-previous">
                    <?php $text = $block->getAnchorTextForPrevious() ? $block->getAnchorTextForPrevious() : '';?>
                    <a class="<?php /* @escapeNotVerified */ echo $text ? 'link ' : 'action '?> previous" href="<?php /* @escapeNotVerified */ echo $block->getPreviousPageUrl() ?>" title="<?php /* @escapeNotVerified */ echo $text ? $text : __('Previous') ?>">
                        <span class="label"><?php /* @escapeNotVerified */ echo __('Page') ?></span>
                        <span><?php /* @escapeNotVerified */ echo $text ? $text : __('Previous') ?></span>
                    </a>
                </li>
            <?php endif;?>

            <?php if ($block->canShowFirst()): ?>
                <li class="item">
                    <a class="page first" href="<?php /* @escapeNotVerified */ echo $block->getFirstPageUrl() ?>">
                        <span class="label"><?php /* @escapeNotVerified */ echo __('Page') ?></span>
                        <span>1</span>
                    </a>
                </li>
            <?php endif;?>

            <?php if ($block->canShowPreviousJump()): ?>
                <li class="item">
                    <a class="page previous jump" title="" href="<?php /* @escapeNotVerified */ echo $block->getPreviousJumpUrl() ?>">
                        <span>...</span>
                    </a>
                </li>
            <?php endif;?>

            <?php foreach ($block->getFramePages() as $_page): ?>
                <?php if ($block->isPageCurrent($_page)): ?>
                    <li class="item current">
                        <strong class="page">
                            <span class="label"><?php /* @escapeNotVerified */ echo __('You\'re currently reading page') ?></span>
                            <span><?php /* @escapeNotVerified */ echo $_page ?></span>
                        </strong>
                    </li>
                <?php else: ?>
                    <li class="item">
                        <a href="<?php /* @escapeNotVerified */ echo $block->getPageUrl($_page) ?>" class="page">
                            <span class="label"><?php /* @escapeNotVerified */ echo __('Page') ?></span>
                            <span><?php /* @escapeNotVerified */ echo $_page ?></span>
                        </a>
                    </li>
                <?php endif;?>
            <?php endforeach;?>

            <?php if ($block->canShowNextJump()): ?>
                <li class="item">
                    <a class="page next jump" title="" href="<?php /* @escapeNotVerified */ echo $block->getNextJumpUrl() ?>">
                        <span>...</span>
                    </a>
                </li>
            <?php endif;?>

            <?php if ($block->canShowLast()): ?>
              <li class="item">
                  <a class="page last" href="<?php /* @escapeNotVerified */ echo $block->getLastPageUrl() ?>">
                      <span class="label"><?php /* @escapeNotVerified */ echo __('Page') ?></span>
                      <span><?php /* @escapeNotVerified */ echo $block->getLastPageNum() ?></span>
                  </a>
              </li>
            <?php endif;?>

            <?php if (!$block->isLastPage()): ?>
                <li class="item pages-item-next">
                    <?php $text = $block->getAnchorTextForNext() ? $block->getAnchorTextForNext() : '';?>
                    <a class="<?php /* @escapeNotVerified */ echo $text ? 'link ' : 'action '?> next" href="<?php /* @escapeNotVerified */ echo $block->getNextPageUrl() ?>" title="<?php /* @escapeNotVerified */ echo $text ? $text : __('Next') ?>">
                        <span class="label"><?php /* @escapeNotVerified */ echo __('Page') ?></span>
                        <span><?php /* @escapeNotVerified */ echo $text ? $text : __('Next') ?></span>
                    </a>
                </li>
            <?php endif;?>
            </ul>
        </div>
        <?php endif; ?>

    <?php if ($block->isShowPerPage()): ?>
        <div class="limiter">
            <strong class="limiter-label"><?php /* @escapeNotVerified */ echo __('Show') ?></strong>
            <select id="limiter" data-mage-init='{"redirectUrl": {"event":"change"}}' class="limiter-options">
                <?php foreach ($block->getAvailableLimit() as $_key => $_limit): ?>
                    <option value="<?php /* @escapeNotVerified */ echo $block->getLimitUrl($_key) ?>"<?php if ($block->isLimitCurrent($_key)): ?>
                        selected="selected"<?php endif ?>>
                        <?php /* @escapeNotVerified */ echo $_limit ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <span class="limiter-text"><?php /* @escapeNotVerified */ echo __('per page') ?></span>
        </div>
    <?php endif ?>



    <?php if ($block->getUseContainer()): ?>
    </div>
    <?php endif ?>

<?php endif ?>
