<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php /** @var $block \Totaltools\Loyalty\Block\Insider\View */ ?>
<?php
$order = $block->getInstoreOrder();
$items = $order->getId() ? $order->getOrderItems() : [];
?>
<?php if ($order && count($items)): ?>
    <div class="table-wrapper orders-history">
        <table class="data table table-order-items history" id="my-orders-table">
            <thead>
            <tr>
                <th scope="col" class="col date"><?= /* @escapeNotVerified */ __('Item Code') ?></th>
                <th scope="col" class="col shipping"><?= /* @escapeNotVerified */ __('Item Description') ?></th>
                <th scope="col" class="col total" style="text-align: center;"><?= /* @escapeNotVerified */ __('QTY') ?></th>
                <th scope="col" class="col total" style="text-align: center;"><?= /* @escapeNotVerified */ __('GST') ?></th>
                <th scope="col" align="right" class="col total" style="text-align: right;"><?= /* @escapeNotVerified */ __('Total Price (Inc GST)') ?></th>
            </tr>
            </thead>
            <tbody>
            <?php foreach ($items as $item): ?>
                <?php
                    $stockCode  = $block->convertString(trim($item->getStockCode()));
                    $stockDescription = $block->convertString(trim($item->getStockDescription()));
                ?>
                <tr>
                    <td data-th="<?= $block->escapeHtml(__('Item Code')) ?>" class="col"><?= /* @escapeNotVerified */ $stockCode; ?></td>
                    <td data-th="<?= $block->escapeHtml(__('Item Description')) ?>" class="col"><?= $stockDescription; ?></td>
                    <td data-th="<?= $block->escapeHtml(__('Qty')) ?>" class="col" style="text-align: center;"><?= /* @escapeNotVerified */ ($item ->getQty() +0); ?>
                    <td data-th="<?= $block->escapeHtml(__('Tax')) ?>" class="col" style="text-align: center;"><?= /* @escapeNotVerified */ $block->currency($item->getLineTax()) ?></td>
                    <td align="right" data-th="<?= $block->escapeHtml(__('Total Price (Inc GST)')) ?>" class="col"><?= /* @escapeNotVerified */ $block->currency($item->getLineValue()) ?></td>
                </tr>
            <?php endforeach; ?>
            </tbody>
            <tfoot>
                <tr class="subtotal"  align="right">
                    <th class="mark" colspan="4">
                        <strong><?= __('Total (Inc GST)'); ?></strong>
                    </th>
                    <th class="amount" align="right" style="padding-right: 20px;">
                        <strong><?= $block->currency($order->getOrderTotal()); ?></strong>
                    </th>
                </tr>
                <tr class="subtotal"  align="right">
                    <th class="mark" colspan="4">
                        <?= __('GST'); ?>
                    </th>
                    <th class="amount" align="right" style="padding-right: 20px;">
                        <strong><?= $block->currency($order->getOrderTax()); ?></strong>
                    </th>
                </tr>
            </tfoot>
        </table>
    </div>
<?php endif; ?>