define([
    'jquery',
    'jquery/jquery.cookie'
], function ($) {
    'use strict';

    $.widget('loyalty.downloadinvoices', {
        /**
         * Widget options.
         */
        options: {
            urlPath: '',
            cookieName: '',
            buttonId: '',
            orderCheckbox: ''
        },

        /**
         * Creates widget instance.
         *
         * @return void
         */
        _create: function () {
            var _self = this;
            _self._initDownloadHandler();
        },

        _initDownloadHandler: function() {
            var self = this;

            $(self.options.buttonId).on('click', function (ev) {
                ev.preventDefault();

                var orderIds = $.map($(self.options.orderCheckbox + ':checked'), function(order_id) {
                    return order_id.value;
                });

                if (!orderIds.length) {
                    alert('Please select at least one order.');
                    return;
                }

                let path = self.options.urlPath;
                let downloadPath = path + (path.includes('?') ? '&' : '?') + 'id=' + orderIds.join(',');

                location.href = downloadPath;

                setTimeout(function() {
                    $.cookie(self.options.cookieName, '', { expires: -1 });
                    $(self.options.orderCheckbox).prop('checked', false);
                    $(self.options.orderCheckbox).trigger('orderSelector.clear');
                }, 3000);
            });
        },
    });

    return $.loyalty.downloadinvoices;
});