define(['jquery', 'mage/url', 'uiComponent', 'ko'], function ($,  url, Component, ko) {
    'use strict';

    ko.bindingHandlers.attrIf = {
        update: function (element, valueAccessor, allBindingsAccessor) {
            var h = ko.utils.unwrapObservable(valueAccessor());
            var show = ko.utils.unwrapObservable(h._if);
            if (show) {
                ko.bindingHandlers.attr.update(element, valueAccessor, allBindingsAccessor);
            } else {
                for (var k in h) {
                    if (h.hasOwnProperty(k) && k.indexOf("_") !== 0) {
                        $(element).removeAttr(k);
                    }
                }
            }
        }
    };
    
    return Component.extend({
        defaults: {
            template: 'Totaltools_Loyalty/insider/loyalty_insider_reward'
        },
        insiderRewardsItems : ko.observableArray([]),

        pageNumber : ko.observable(0),
        totalItems : ko.observable(0),
        nbPerPage : 10,
        totalPagesHolder : ko.observableArray([]),
        initialize: function () {
            var self = this;
            this.getInsiderRewardsList();            
            this.totalPages = ko.computed(function() {
                self.totalItems(self.insiderRewardsItems().length);
                var div = Math.floor(self.insiderRewardsItems().length / self.nbPerPage);
                div += self.insiderRewardsItems().length % self.nbPerPage > 0 ? 1 : 0;
                    
                    var pages = div - 1;
                    self.totalPagesHolder.removeAll();
                    for(var i=0; i < pages + 1; i++) {
                        self.totalPagesHolder.push(i + 1);
                    }		
                if (div ==0) {
                    return div;
                }
                return div - 1;
            });
            self.pageController = function(targetPage) {
                return self.pageNumber(targetPage - 1);
            },
                
            this.paginated = ko.computed(function() {
                var first = self.pageNumber() * self.nbPerPage;
                return self.insiderRewardsItems.slice(first, first + self.nbPerPage);
            });

            self.pageController = function(targetPage) {
                return self.pageNumber(targetPage - 1);
            }
            
            this.hasPrevious = ko.computed(function() {
                return self.pageNumber() !== 0;
            });
            this.hasNext = ko.computed(function() {
                return self.pageNumber() !== self.totalPages();
            });

            this.paginationText = ko.computed(function() {
                if ( self.totalItems() < 1) {
                    return 'No transactions found.';
                }
                var first =  self.pageNumber() * self.nbPerPage +1;
                var last = first + self.paginated().length -1;
                return 'Items '+first+' to '+last+' of '+ self.totalItems()+' total';
            });

            

            this._super();
        },

        getInsiderRewardsList: function () {
            var self = this;
            $.ajax({
                url: self._getInsiderRewardsUrl(),
                processData: false,
                contentType: false,
                type: 'GET',
                dataType: 'json',
                success: function (response) {
                    self.insiderRewardsItems(response?.content?.items.reverse());
                }
            });
        },

        next: function() {
            var self = this;
            if(self.pageNumber() < self.totalPages()) {
                self.pageNumber(self.pageNumber() + 1);
            }
        },
        previous: function() {
            var self = this;
            if(self.pageNumber() != 0) {
                self.pageNumber(self.pageNumber() - 1);
            }
        },
        

        _getInsiderRewardsUrl: function () {
            return url.build('loyalty/insider/insiderrewards');
        },
    });
}
);