define([
    'jquery',
    'jquery/jquery.cookie'
], function ($) {
    'use strict';

    $.widget('loyalty.selectinvoice', {
        /**
         * Widget options.
         */
        options: {
            orderSelector: '',
            cookieName: '',
            selectedOrders: [],
            tableId: '',
            selectAllBtn: '',
            deselectAllBtn: ''
        },

        /**
         * Creates widget instance.
         *
         * @return void
         */
        _create: function () {
            var _self = this;

            _self._initSelectedOrders();
            $(_self.options.orderSelector).on('click', function (e) {
                _self._saveSelectedIdToCookie(e)
            });

            $(_self.options.orderSelector).on('orderSelector.clear', function () {
                _self.options.selectedOrders = [];
            });

            $(_self.options.selectAllBtn).on('click', function () {
                _self._selectAllOrders();
                $(this).hide();
                $(_self.options.deselectAllBtn).show();
            });

            $(_self.options.deselectAllBtn).on('click', function () {
                _self._deselectAllOrders();
                $(this).hide();
                $(_self.options.selectAllBtn).show();
            });
        },

        

        _initSelectedOrders: function () {
            var _self = this,
                cookieData = $.cookie(_self.options.cookieName);
            _self.options.selectedOrders = cookieData ? cookieData.split(',') : [];

            if (_self.options.selectedOrders.length > 0) {
                $.each(_self.options.selectedOrders, function (index, orderId) {
                    var selectedOrderCheckbox = $(_self.options.tableId).find("[data-order-id='" + orderId + "']");

                    if (selectedOrderCheckbox.length > 0) {
                        selectedOrderCheckbox.trigger('click');
                    }
                });
            }
        },

        _saveSelectedIdToCookie: function (e) {
            var element = e.target,
                _self = this,
                orderId = element.getAttribute('data-order-id');

            if ($(element).is(':checked')) {
                _self.options.selectedOrders.push(orderId);
            } else {
                _self.options.selectedOrders.splice($.inArray(orderId, _self.options.selectedOrders), 1);
            }
            _self._updateOrdersCookie();
        },

        /**
         * Select all orders.
         *
         * @return void
         */
        _selectAllOrders: function () {
            var _self = this;
            $(_self.options.orderSelector).each(function () {
                if (!$(this).is(':checked')) {
                    $(this).prop('checked', true);
                    $(this).data('was-checked', true);
                    var orderId = $(this).attr('data-order-id');
                    _self.options.selectedOrders.push(orderId);
                }
            });
            _self._updateOrdersCookie();
        },

        /**
         * Deselect all orders.
         *
         * @return void
         */
        _deselectAllOrders: function () {
            var _self = this;
            $(_self.options.orderSelector).each(function () {
                if ($(this).is(':checked')) {
                    $(this).prop('checked', false);
                    $(this).data('was-checked', false);
                    var orderId = $(this).attr('data-order-id');
                    _self.options.selectedOrders.splice($.inArray(orderId, _self.options.selectedOrders), 1);
                }
            });
            _self._updateOrdersCookie();

        },

        /**
         * Update orders cookie.
         *
         * @return void
         */
        _updateOrdersCookie: function () {
            var _self = this;
            _self.options.selectedOrders = $.unique(_self.options.selectedOrders);
            $.cookie(_self.options.cookieName, _self.options.selectedOrders.join(','), {
                expires: 1,
            })
        }
    });

    return $.loyalty.selectinvoice;
});