define(
    [
        'jquery',
        'mage/url',
        'Magento_Customer/js/customer-data',
        'jquery/jquery.cookie'
    ],
    function($, url, customerData) {
        "use strict";

        $.widget('loyalty.selectemail', {
            options: {
                modalForm: '',
                orderSelector: '',
                modalButton: '',
                emailLink: '',
                formId: '',
                cookieName: ''
            },

            _create: function() {
                this.options.modalOption = this._getModalOptions();
                this.options.emailLink = url.build(this.options.emailLink);
                this._showPopup();
            },

            _getModalOptions: function() {
                var _self = this;
                var options = {
                    type: 'popup',
                    responsive: true,
                    title: $.mage.__('Select Email'),
                    buttons: [{
                        text: $.mage.__('Continue'),
                        class: '',
                        click: function (data) {
                            $.ajax({
                                url: _self.options.emailLink,
                                showLoader: true,
                                type: 'POST',
                                data : $(_self.options.formId).serialize(),
                                success: function (data) {
                                    if (data.success) {
                                        $(_self.options.formId).trigger('reset');
                                        _self._uncheckAll();
                                    }
                                },
                                always: function(data) {
                                    customerData.reload('messages');
                                }
                            });

                            this.closeModal();
                        }
                    }]
                };

                return options;
            },

            _uncheckAll: function() {
                var _self = this;

                $(_self.options.orderSelector).each(function () {
                    if ($(this).is(':checked')) {
                        $(this).prop('checked', false);
                        $(this).data('was-checked', false);
                    }
                });

                $.cookie(_self.options.cookieName, '', {
                    expires: -1,
                });

                $(_self.options.orderSelector).trigger('orderSelector.clear');
            },

            _showPopup: function() {
                var _self = this;

                this._on({
                    click: function (e) {
                        $(_self.options.modalForm).modal(_self.options.modalOption);
                        $(_self.options.modalForm).trigger('openModal');
                    },
                });
            }
        });

        return $.loyalty.selectemail;
    }
);