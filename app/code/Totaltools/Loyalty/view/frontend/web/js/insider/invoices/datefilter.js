define([
    'jquery',
    'mage/url',
    'mage/calendar'
], function ($, url) {
    'use strict';

    $.widget('loyalty.datefilter', {
        /**
         * Widget options.
         */
        options: {
            buttonImage: '',
            buttonText: '',
            cookieName: '',
            validatorPath: '',
            selectDateIds: '',
            clearButton: '',
            baseUrl: ''

        },

        /**
         * Creates widget instance.
         *
         * @return void
         */
        _create: function () {
            var _self = this;
            _self._initSelectDateHandler();
            _self._initClearDateHandler();

        },

        _resetCookie: function() {
            var self = this;

            $.cookie(self.options.cookieName, '', {
                expires: 0,
            });
        },

        _initClearDateHandler: function() {
            var self = this;

            $(self.options.clearButton).on('click', function (e) {
                e.preventDefault();
                self._resetCookie();
                $(location).attr("href", url.build(self.options.baseUrl));
            });
        },

        _initSelectDateHandler: function() {
            var self = this;

            $(self.options.selectDateIds).calendar({
                showsTime: false,
                showOn: "both",
                dateFormat: "dd/M/yy",
                buttonImage: self.options.buttonImage,
                buttonText: self.options.buttonText,
                onSelect: function(selectedDate) {
                   self._resetCookie();

                    $.ajax({
                        url: url.build(self.options.validatorPath),
                        data: {
                            filterType: this.getAttribute('name'),
                            dateValue: selectedDate
                        },
                        type: 'POST',
                        cache: false,
                        dataType: 'json',
                        beforeSend: function() {
                            $('body').trigger('processStart');
                        }
                    }).done(function(url) {
                        $(location).attr("href", url);
                    });
                }
            });
        },
    });

    return $.loyalty.datefilter;
});