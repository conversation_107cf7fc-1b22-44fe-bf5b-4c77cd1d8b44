/**
 * @package     Totaltools_Loyalty
 * <AUTHOR> I<PERSON> <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

define([
    'uiComponent',
    'ko',
    'Magento_Customer/js/customer-data',
    'Magento_Catalog/js/price-utils'
], function (Component, ko, customerData, priceUtils) {
    'use strict';

    return Component.extend({
        /**
         * Observable of type number.
         */
        dollars: ko.observable(0),

        /**
         * {@inheritdoc}
         */
        initialize: function() {
            this._super();
            this._initInsiderDollars();
        },        

        /**
         * @returns {void}
         */
        _initInsiderDollars: function() {
            var insiderDollars = customerData.get('insider-dollars');
            var insiderData = insiderDollars();

            if (typeof insiderData === 'object' && insiderData.hasOwnProperty('dollars')) {
                this.setDollars(insiderData.dollars);
            }

            insiderDollars.subscribe(function(data) {
                this.setDollars(data.dollars);
            }, this);

            return this;
        },

        /**
         * @returns {string}
         */
        getInsiderDollars: function() {
            return this.currency + this.getDollars();
        },

        /**
         * @returns {string}
         */
        getDollars: function() {
            return this.dollars();
        },

        /**
         * @param {number}
         * @returns {void}
         */
        setDollars: function(amount) {
            this.dollars(amount);
        }
    });
});
