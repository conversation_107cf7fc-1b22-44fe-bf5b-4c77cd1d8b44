<div class="component-wrapper">
    <div class="table-wrapper loyalty_insider_reward orders-history">
        <div class="loyalty_insider_reward_header"><h2 data-bind="text: 'ACTIVE INSIDER REWARDS'"></h2></div>
             <!-- ko if: paginated().length > 0-->
                <table class="data table table-active-insider-reward table-order-items" id="table-active-insider-reward"  data-bind="if: paginated()"> 
                    <thead>
                    <tr>
                        <th scope="col" class="col type" data-bind="text: 'Type'"></th>
                        <th scope="col" class="col value"  data-bind="text: 'Price Reward'"></th>
                        <th scope="col" class="col value"  data-bind="text: 'Points Remaining'"></th>
                        <th scope="col" class="col expiry"  data-bind="text: 'Expiry'"></th>
                    </tr>
                    </thead>

                    <tbody data-bind="foreach: paginated()">
                
                        <tr>
                            <td data-th="'Type'"
                                class="col type" data-bind="text: TransDescription"></td>
                            <td data-th="'Original Value'"
                                class="col value"  data-bind="text: OriginalValue"></td>
                            <td data-th="'Points Remaining'"
                                class="col value"  data-bind="text: PointsRemaining"></td>
                            <td data-th="'Expiry'"
                                class="col expiry"  data-bind="text: ExpiryDate"></td>
                        </tr>
                
                    </tbody>
                </table>

                <div class="pager"  data-bind="visible: paginated">
                    <p class="toolbar-amount">
                        <span class="toolbar-number" data-bind="text:paginationText()"></span>
                    </p>

                

                    <div class="pages">
                        <ul class="items pages-items" aria-labelledby="paging-label">
                            <li class="item pages-item-previous" data-bind="click: previous, visible: hasPrevious">
                                <a class="action  previous"  title="Previous">
                                    <span>&lt;</span>
                                </a>
                            </li>
                            <!-- ko foreach: totalPagesHolder -->
                                <li class="item" data-bind="class:  $parent.pageNumber() + 1 == $data ? 'current' : ''">
                                    <a href="#" data-bind="click: $parent.pageController" class="page">
                                        <span data-bind="text: $data "></span>
                                    </a>
                                </li>
                            
                            <!-- /ko -->
                            <li class="item pages-item-next" data-bind="click: next, visible: hasNext">
                                <a class="action  next"  href="#" title="Next">
                                    <span>&gt;</span>
                                </a>
                            </li>
        
                        </ul>
                    </div>

                </div>
            <!-- /ko -->
            <!-- ko if: paginated().length < 1-->
                <div class="message info empty" style="clear: both"><span  data-bind="text :'No transactions found.' "></span></div>
            <!-- /ko -->​
    </div>
</div>