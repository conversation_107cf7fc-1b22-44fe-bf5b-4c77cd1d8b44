<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <referenceBlock name="page.top.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Insider Rewards</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <container name="loyalty.info.wrapper" >
                <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification"
                    name="loyalty.instoreorder.grid.verification"
                    template="Totaltools_Loyalty::loyalty/grid/verification.phtml">
                    <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification\Confirmation"
                        name="loyalty.instoreorder.grid.confirmation_email_button"
                        template="Totaltools_Loyalty::loyalty/grid/verification/confirmation_button.phtml" />                    
                        <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification\Confirmation"
                        name="loyalty.instoreorder.grid.confirmation_sms_verification"
                        template="Totaltools_Loyalty::loyalty/grid/verification/sms_verification.phtml" />                
                </block>
            </container>
            <block class="Totaltools\Loyalty\Block\Info" name="loyalty_insider_info" as="info" template="Totaltools_Loyalty::loyalty/info.phtml" cacheable="false">
                <block class="Magento\Cms\Block\Block" name="loyalty_insider_info.member_benefits" group="insider_info">
                    <arguments>
                        <argument name="block_id" xsi:type="string">insider_rewards_member_benefits</argument>
                    </arguments>
                </block>
                <block class="Magento\Cms\Block\Block" name="loyalty_insider_info.insider_points" group="insider_info">
                    <arguments>
                        <argument name="block_id" xsi:type="string">insider_rewards_insider_points</argument>
                    </arguments>
                </block>
            </block>
            <block class="Totaltools\Loyalty\Block\InsiderReward" name="loyalty_insider_reward" as="loyalty_insider_reward" template="Totaltools_Loyalty::loyalty/insider/insider_reward.phtml"/>
            <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid" name="loyalty.recent.invoices.grid" as="instoreorder_list" template="Totaltools_Loyalty::loyalty/grid.phtml" ifconfig="totaltools_loyalty/insider_rewards/invoices_in_rewards" >
                <block class="Magento\Framework\View\Element\Template" name="recent.order.title" before="-" template="Magento_Theme::text.phtml">
                    <arguments>
                        <argument translate="true" name="text" xsi:type="string">Recent Invoices</argument>
                        <argument name="tag" xsi:type="string">h2</argument>
                    </arguments>
                </block>
                <block class="Magento\Theme\Block\Html\Pager" name="loyalty.instoreorder.grid.pager" as="pager" template="Totaltools_Loyalty::loyalty/html/pager.phtml" />
            </block>

        </referenceContainer>

        <referenceBlock name="page.top.title">
            <block class="Magento\Framework\View\Element\Template" name="page.description" after="-" template="Magento_Theme::text.phtml">
                <arguments>
                    <argument translate="true" name="text" xsi:type="string">Track your Insider Rewards and points balance, access your recent invoices and update your personal information.</argument>
                    <argument name="tag" xsi:type="string">p</argument>
                    <argument name="css_class" xsi:type="string">main-description</argument>
                </arguments>
            </block>
        </referenceBlock>

        <referenceBlock name="banner.data" remove="true" />
        <referenceBlock name="ec_events" remove="true" />
        <referenceBlock name="ec_footer" remove="true" />
    </body>
</page>
