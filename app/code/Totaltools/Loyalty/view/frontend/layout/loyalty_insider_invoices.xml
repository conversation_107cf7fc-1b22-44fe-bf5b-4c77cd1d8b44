<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <attribute name="class" value="loyalty-insider-info"/>
        <referenceBlock name="page.top.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Orders &#38; Invoices</argument>
            </action>
            <block class="Magento\Framework\View\Element\Template" name="recent.order.title.description" after="-" template="Magento_Theme::text.phtml">
                <arguments>
                    <argument translate="true" name="text" xsi:type="string">View and download invoices for your tax and warranty with our updated invoice search.</argument>
                    <argument name="tag" xsi:type="string">p</argument>
                    <argument name="css_class" xsi:type="string">main-description</argument>
                </arguments>
            </block>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification"
                   name="loyalty.instoreorder.grid.verification"
                   template="Totaltools_Loyalty::loyalty/grid/verification.phtml" cacheable="false">
                <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification\Confirmation"
                       name="loyalty.instoreorder.grid.confirmation_email_button"
                       template="Totaltools_Loyalty::loyalty/grid/verification/confirmation_button.phtml" />
                <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid\Verification\Confirmation"
                       name="loyalty.instoreorder.grid.confirmation_sms_verification"
                       template="Totaltools_Loyalty::loyalty/grid/verification/sms_verification.phtml" />
            </block>
            <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid"
                   name="loyalty.instoreorder.grid" as="instoreorder_list"
                   template="Totaltools_Loyalty::loyalty/grid.phtml" cacheable="false">
                <block class="Totaltools\Loyalty\Block\InstoreOrders\Grid\Filter\Date"
                       name="loyalty.instoreorder.grid.filter.date"
                       template="Totaltools_Loyalty::loyalty/grid/filter/date.phtml" />
                <block class="Magento\Theme\Block\Html\Pager" name="loyalty.instoreorder.grid.pager" as="pager"
                       template="Totaltools_Loyalty::loyalty/html/pager.phtml" />
            </block>
        </referenceContainer>
    </body>
</page>
