<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="customer_account"/>
    <body>
        <referenceBlock name="page.top.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">Insider Rewards</argument>
            </action>
        </referenceBlock>
        <referenceBlock name="breadcrumbs">
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">invoices</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string" translate="true">Invoices</item>
                    <item name="label" xsi:type="string" translate="true">Invoices</item>
                    <item name="link" xsi:type="string">/loyalty/insider/invoices/</item>
                </argument>
            </action>
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">orders</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string" translate="true">View Order</item>
                    <item name="label" xsi:type="string" translate="true">View Order</item>
                    <item name="last" xsi:type="boolean">true</item>
                </argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Totaltools\Loyalty\Block\Insider\View" name="insider.view" template="Totaltools_Loyalty::loyalty/insider/view.phtml" cacheable="false">
                <block class="Totaltools\Loyalty\Block\Insider\View" name="insider.items" as="insider_items" template="Totaltools_Loyalty::loyalty/insider/items.phtml" />
            </block>
        </referenceContainer>
    </body>
</page>
