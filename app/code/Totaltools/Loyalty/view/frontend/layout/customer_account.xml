<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" label="Customer My Account (All Pages)" design_abstraction="custom">
    <body>
        <referenceBlock name="company-customer-account-navigation-orders-link" remove="true"/>
        <referenceBlock name="customer_account_navigation">
            <block class="Magento\Customer\Block\Account\SortLinkInterface" name="customer-account-navigation-instoreorder-invoices-link">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Orders &#38; Invoices</argument>
                    <argument name="path" xsi:type="string">loyalty/insider/invoices</argument>
                    <argument name="sortOrder" xsi:type="number">240</argument>
                </arguments>
            </block>
            <block class="Totaltools\Loyalty\Block\Link\Insider" name="customer-account-navigation-instoreorder-list-link">
                <arguments>
                    <argument name="label" xsi:type="string" translate="true">Insider Rewards</argument>
                    <argument name="path" xsi:type="string">loyalty/insider/info</argument>
                    <argument name="sortOrder" xsi:type="number">300</argument>
                </arguments>
            </block>
        </referenceBlock>
    </body>
</page>
