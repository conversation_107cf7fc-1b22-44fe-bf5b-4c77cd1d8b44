<?php

/**
 * @package     Totaltools_Loyalty
 * <AUTHOR> <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Loyalty\CustomerData;

use Magento\Customer\CustomerData\SectionSourceInterface;
use Magento\Framework\App\ObjectManager;
use Psr\Log\LoggerInterface;

/** @class InsiderDollars */
class InsiderDollars extends \Totaltools\Loyalty\Block\Info implements SectionSourceInterface
{
    /**
     * @inheritdoc
     */
    public function getSectionData()
    {
        try {
            return [
                'dollars' => $this->getAmount() ?: 0
            ];
        } catch (Exception $e) {
            $logger = ObjectManager::getInstance()->get(LoggerInterface::class);
            $logger->info("InsiderDollars: ". $e->getMessage());
        }
    }
}
