<?php

namespace Totaltools\Loyalty\Model;

class SystemConfig
{
    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     *
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig Config.
     */
    public function __construct(\Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Get limit of attempts of sending emails.
     *
     * @return int
     */
    public function getEmailAttemptsLimit()
    {
        return (int) $this->scopeConfig->getValue('totaltools_loyalty/confirmation_email/attempt_limit');
    }

    /**
     * Get duration of next attempt to sending verification email.
     *
     * @return int
     */
    public function getNextAttemptsDurationValue()
    {
        return (int) $this->scopeConfig->getValue('totaltools_loyalty/confirmation_email/next_attempt_duration');
    }
}