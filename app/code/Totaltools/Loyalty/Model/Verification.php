<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Loyalty\Model;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\State\InvalidTransitionException;
use Magento\Framework\Exception\StateException;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;

/**
 * Class Verification
 * @package Totaltools\Loyalty\Model
 */
class Verification
{
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * Verification constructor.
     *
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
    )
    {
        $this->customerRepository = $customerRepository;
    }

    /**
     * Handle verification customer.
     *
     * @param int $customerId
     * @param string $key
     *
     * @return \Magento\Customer\Api\Data\CustomerInterface
     *
     * @throws InvalidTransitionException
     * @throws LocalizedException
     * @throws StateException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\State\InputMismatchException
     */
    public function verifyCustomer(int $customerId, string $key)
    {
        $customer = $this->customerRepository->getById($customerId);
        $customerKey = $customer->getCustomAttribute('email_verification_hash');

        if ($customerKey && $customerKey->getValue()) {
            $verificationAttribute = $customer->getCustomAttribute('email_verification_status');

            if ($verificationAttribute
                && $verificationAttribute->getValue()
                && $verificationAttribute->getValue() === EmailVerification::VERIFIED
            ) {
                throw new InvalidTransitionException(__('Account has already been verified.'));
            }

            if ($customerKey->getValue() !== $key) {
                throw new StateException(__('This confirmation key is invalid.'));
            }
        }

        $customer
            ->setCustomAttribute('email_verification_status', EmailVerification::VERIFIED)
            ->setCustomAttribute('unverified_loyalty_id', null)
            ->setCustomAttribute('email_attempts_limit', 0);

        $this->customerRepository->save($customer);

        return $customer;
    }
}