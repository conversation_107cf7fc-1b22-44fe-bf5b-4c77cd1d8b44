<?php
/**
 * Total Tools Loyalty.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Loyalty\Model;

/**
 * Class CustomerSession
 * @package Totaltools\Loyalty\Model
 */
class CustomerSession
{
    /**
     * Customer session
     *
     * @var \Magento\Customer\Model\SessionFactory
     */
    private $currentCustomerFactory;

    /**
     * @var \Magento\Framework\App\Http\Context
     */
    private $httpContext;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    private $customerFactory;

    /**
     * CustomerSession constructor.
     * @param \Magento\Customer\Model\SessionFactory $customerSessionFactory
     * @param \Magento\Framework\App\Http\Context $httpContext
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     */
    public function __construct(
        \Magento\Customer\Model\SessionFactory $customerSessionFactory,
        \Magento\Framework\App\Http\Context $httpContext,
        \Magento\Customer\Model\CustomerFactory $customerFactory
    ) {
        $this->currentCustomerFactory = $customerSessionFactory;
        $this->httpContext = $httpContext;
        $this->customerFactory = $customerFactory;
    }

    /**
     * @return \Magento\Customer\Model\SessionFactory
     */
    public function getCustomerSession()
    {
        return $this->currentCustomerFactory->create();
    }

    /**
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomer()
    {
        return $this->getCustomerSession()->getCustomer();
    }

    /**
     * Check customer login / logout
     *
     * @return bool
     */
    public function authenticate()
    {
        return $this->getCustomerSession()->authenticate();
    }

    /**
     * Get Customer Id
     *
     * @return int|null
     */
    public function getCustomerId()
    {
        return $this->getCustomer()->getId();
    }

    /**
     * Set customer data for customer session.
     *
     * @param $customer
     */
    public function setCustomerDataAsLoggedIn($customer)
    {
        return $this->getCustomerSession()->setCustomerDataAsLoggedIn($customer);
    }
}
