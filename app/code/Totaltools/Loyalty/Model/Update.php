<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Loyalty
 */

namespace Totaltools\Loyalty\Model;

/**
 * Class Update
 * @package Totaltools\Loyalty\Model
 */
class Update
{

    /**
     * Customer factory
     * @var \Magento\Customer\Model\CustomerFactory
     */
    private $customerFactory;

    /**
     * Pronto customer
     * @var \Totaltools\Pronto\Api\CustomerRequestManagerInterface
     */
    private $prontoRequestManager;

    /**
     * Update constructor.
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager
     */
    public function __construct(
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager
    ) {
        $this->customerFactory = $customerFactory;
        $this->prontoRequestManager = $prontoRequestManager;
    }

    /**
     * Update customers by email
     *
     * @param string|string[] $emails Email or array of emails
     * @return int
     */
    public function updateCustomersByEmail($emails)
    {
        $count = 0;

        if (!is_array($emails)) {
            $emails = [$emails];
        }

        foreach ($emails as $email) {
            $customer = $this->customerFactory->create()->loadByEmail($email);
            if ($customer->getId()) {
                $this->prontoRequestManager->updateCustomerInfo($customer, true);
                $count++;
            }
        }

        return $count;
    }

}