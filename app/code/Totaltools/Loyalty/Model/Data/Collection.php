<?php
namespace Totaltools\Loyalty\Model\Data;

use Magento\Framework\Data\Collection as DataCollection;
use Magento\Framework\DataObject;

class Collection extends DataCollection
{
    /**
     * Retrieve collection page size
     *
     * @return int
     */
    public function setSize($size)
    {
        return $this->_totalRecords = $size;
    }

    public function addItems(array $items)
    {
        foreach ($items as $item) {
            $this->addItem(new DataObject($item));
        }
    }
}