<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Loyalty\Model\Invoices\Pdf;

/**
 * Class FileWriter
 * @package Totaltools\Loyalty\Model\Invoices\Pdf
 */
class FileWriter
{
    const INVOICES_FOLDER_NAME = 'invoices';
    const INVOICES_ARCHIVE_NAME = 'invoices.zip';

    /**
     * @var \Totaltools\Pronto\Model\InstoreOrderFactory
     */
    protected $_instoreOrderFactory;

    /**
     * @var \Magento\Sales\Model\OrderFactory
     */
    protected $_salesOrderFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $_customerSession;

    /**
     * @var \Magento\Framework\Filesystem\Io\File
     */
    protected $_ioFile;

    /**
     * @var \Magento\Framework\App\Filesystem\DirectoryList
     */
    protected $_directoryList;

    /**
     * @var \Magento\Framework\Archive\Zip
     */
    protected $_zipArchive;

    /**
     * @var PdfHandler
     */
    protected $_pdfHandler;

    /**
     * FileWriter constructor.
     * @param \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory
     * @param \Magento\Sales\Model\OrderFactory $_salesOrderFactory
     * @param \Totaltools\Loyalty\Model\CustomerSession $customerSession
     * @param \Magento\Framework\Filesystem\Io\File $ioFile
     * @param \Magento\Framework\App\Filesystem\DirectoryList $directoryList
     * @param \Magento\Framework\Archive\Zip $_zipArchive
     * @param PdfHandler $pdfHandler
     */
    public function __construct(
        \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory,
        \Magento\Sales\Model\OrderFactory $_salesOrderFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Magento\Framework\Filesystem\Io\File $ioFile,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList,
        \Magento\Framework\Archive\Zip $_zipArchive,
        PdfHandler $pdfHandler
    ) {
        $this->_instoreOrderFactory = $instoreOrderFactory;
        $this->_salesOrderFactory = $_salesOrderFactory;
        $this->_customerSession = $customerSession;
        $this->_ioFile = $ioFile;
        $this->_directoryList = $directoryList;
        $this->_zipArchive = $_zipArchive;
        $this->_pdfHandler = $pdfHandler;
    }


    /**
     * @param $invoiceIds
     * @param bool $email
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Zend_Pdf_Exception
     */
    public function createFiles($invoiceIds, $email = false)
    {
        $customerId = $this->_customerSession->getCustomerId();
        $fullPath = $this->_directoryList->getPath(\Magento\Framework\App\Filesystem\DirectoryList::VAR_DIR);
        $this->_ioFile->cd($fullPath);

        if (!$this->_ioFile->fileExists(self::INVOICES_FOLDER_NAME, false)) {
            $this->_ioFile->mkdir(self::INVOICES_FOLDER_NAME);
        }

        $this->_ioFile->cd(self::INVOICES_FOLDER_NAME);

        if ($email) {
            if (!$this->_ioFile->fileExists($customerId, false)) {
                $this->_ioFile->mkdir($customerId);
            }

            $this->_ioFile->cd($customerId);
        }

        $archiveName = $email ? 0 : $customerId;

        foreach ($invoiceIds as $index => $invoiceId) {
            if ($email && $index % 5 == 0) {
                $archiveName++;
            }

            $order = $this->_instoreOrderFactory->create()->loadByOrderNo($invoiceId);

            if (!$order->getId()) {
                $order = $this->_salesOrderFactory->create()->load($invoiceId);
            }

            if (!$order->getId()) {
                continue;
            }

            $pdf = $this->_pdfHandler->getPdf($order);
            $fileName = $invoiceId . '.pdf';
            $this->_ioFile->write($fileName, $pdf->render());
            $this->_zipArchive->pack($fileName, $archiveName . '.zip');
            $this->_ioFile->rm($fileName);
        }
    }
}