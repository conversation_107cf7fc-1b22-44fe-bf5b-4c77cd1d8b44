<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Loyalty\Model\Invoices\Pdf;

use Magento\Sales\Model\Order\Pdf\AbstractPdf;
use Magento\Sales\Model\Order\Pdf\Config;

/**
 * Class PdfHandler
 * @package Totaltools\Loyalty\Model\Invoices\Pdf
 */
class PdfHandler extends AbstractPdf
{

    const LOGO_IMAGE = '/logo/default/logo.png';

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    private $pricingHelper;

    /**
     * @var \Totaltools\Loyalty\Helper\Convert
     */
    private $convertHelper;

    /**
     * PdfHandler constructor.
     * @param \Magento\Payment\Helper\Data $paymentData
     * @param \Magento\Framework\Stdlib\StringUtils $string
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\Filesystem $filesystem
     * @param Config $pdfConfig
     * @param \Magento\Sales\Model\Order\Pdf\Total\Factory $pdfTotalFactory
     * @param \Magento\Sales\Model\Order\Pdf\ItemsFactory $pdfItemsFactory
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate
     * @param \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation
     * @param \Magento\Sales\Model\Order\Address\Renderer $addressRenderer
     * @param \Magento\Framework\Pricing\Helper\Data $pricingHelper
     * @param \Totaltools\Loyalty\Helper\Convert $convertHelper
     * @param array $data
     */
    public function __construct(
        \Magento\Payment\Helper\Data $paymentData,
        \Magento\Framework\Stdlib\StringUtils $string,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Filesystem $filesystem,
        Config $pdfConfig,
        \Magento\Sales\Model\Order\Pdf\Total\Factory $pdfTotalFactory,
        \Magento\Sales\Model\Order\Pdf\ItemsFactory $pdfItemsFactory,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate,
        \Magento\Framework\Translate\Inline\StateInterface $inlineTranslation,
        \Magento\Sales\Model\Order\Address\Renderer $addressRenderer,
        \Magento\Framework\Pricing\Helper\Data $pricingHelper,
        \Totaltools\Loyalty\Helper\Convert $convertHelper,
        array $data = []
    ) {
        $this->pricingHelper = $pricingHelper;
        $this->convertHelper = $convertHelper;
        parent::__construct(
            $paymentData,
            $string,
            $scopeConfig,
            $filesystem,
            $pdfConfig,
            $pdfTotalFactory,
            $pdfItemsFactory,
            $localeDate,
            $inlineTranslation,
            $addressRenderer,
            $data
        );
    }

    /**
     * @param null $order
     * @return \Zend_Pdf
     * @throws \Zend_Pdf_Exception
     */
    public function getPdf($order = null)
    {
        $pdf = new \Zend_Pdf();
        $this->_setPdf($pdf);
        $page = $this->newPage();
        $this->insertStoreInfo($page, $order);
        $this->insertLogo($page, 1);
        $this->_insertOrder($page, $order);
        $this->_insertItems($page, $order);
        $this->_insertOrderTotals($page, $order);

        return $pdf;
    }

    /**
     * Format order total
     * @param  float $total
     * @return string
     */
    public function formatTotal($total)
    {
        return $this->pricingHelper->currency($total, true, false);
    }

    /**
     * @param $page
     * @param \Totaltools\Pronto\Model\InstoreOrder $order
     * @throws \Zend_Pdf_Exception
     */
    private function insertStoreInfo(&$page, $order)
    {
        $storeName = $order->getStoreName();
        $page->setFillColor(new \Zend_Pdf_Color_Rgb(0.9647, 0.9686, 0.9765));
        $page->setLineColor(new \Zend_Pdf_Color_Rgb(0.9647, 0.9686, 0.9765));
        $page->drawRectangle(0, 842, 595, 645);
        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0));
        $this->y = 800;
        $this->printNextLineStoreInfo($page, 'STORE:', $storeName);
        $this->printNextLineStoreInfo($page, 'Address:', $order->getAddress2());
        if ($address3 = $order->getAddress3()) {
            $this->printNextLineStoreInfo($page, null, $address3);
        }
        if ($address4 = $order->getAddress4()) {
            $this->printNextLineStoreInfo($page, null, $address4);
        }
        if ($address5 = $order->getAddress5()) {
            $this->printNextLineStoreInfo($page, null, $address5);
        }
        if ($address6 = $order->getAddress6()) {
            $this->printNextLineStoreInfo($page, null, $address6);
        }
        if ($postCode = $order->getPostCode()) {
            $this->printNextLineStoreInfo($page, null, $postCode);
        }
        if ($phone = $order->getPhone()) {
            $this->printNextLineStoreInfo($page, 'Phone:', $phone);
        }
        if ($email = $order->getEmail()) {
            $this->printNextLineStoreInfo($page, 'Email:', $email);
        }
        $this->printNextLineStoreInfo($page, 'Web:', 'www.totaltools.com.au');
        if ($abn = $order->getAbn()) {
            $this->printNextLineStoreInfo($page, 'ABN:', $abn);
        }
    }

    /**
     * @param \Zend_Pdf_Page $page
     * @param $label
     * @param $value
     * @throws \Zend_Pdf_Exception
     */
    private function printNextLineStoreInfo(&$page, $label, $value)
    {
        $x1 = 295;
        $x2 = 360;
        if ($label) {
            $this->setFontBoldCustom($page, 11);
            $page->drawText(__($label), $x1, $this->y, 'UTF-8');
        }
        if (!in_array($label, ['STORE:', 'ABN:'])) {
            $this->setFontRegularCustom($page, 11);
        }
        $page->drawText(__($value), $x2, $this->y, 'UTF-8');
        $this->y -= 14;
    }

    /**
     * @param \Zend_Pdf_Page $page
     * @param \Totaltools\Pronto\Model\InstoreOrder $order
     */
    protected function _insertOrderTotals(&$page, $order)
    {
        if ($this->y < 128) {
            $page = $this->newPage();
            $this->y -= 35;
        }
        $orderGrandTotal = $this->formatTotal(number_format((float)$order->getOrderTotal(), 2, '.', ''));
        $orderTax = $this->formatTotal(number_format((float)$order->getOrderTax(), 2, '.', ''));
        $top = $this->y - 20;
        $page->setFillColor(new \Zend_Pdf_Color_Rgb(0.9647, 0.9686, 0.9765));
        $page->setLineColor(new \Zend_Pdf_Color_Rgb(0.9647, 0.9686, 0.9765));
        $page->drawRectangle(25, $top, 580, $top - 30);
        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0));

        $this->setFontBoldCustom($page, 12);
        $page->drawText(__('TOTAL (Inc GST)'), 320, $top - 20, 'UTF-8');
        $page->drawText(__('GST'), 395, $top - 60, 'UTF-8');

        $font = $this->setFontRegularCustom($page, 10);
        $text = $orderGrandTotal;
        $textWidth = $this->getTextWidth($text, $font, 11);
        $page->drawText($text, 530 - $textWidth - 11, $top - 20, 'UTF-8');
        $text = $orderTax;
        $textWidth = $this->getTextWidth($text, $font, 11);
        $page->drawText($text, 530 - $textWidth - 11, $top - 60, 'UTF-8');
        $this->y = $top;
    }


    /**
     * @param \Zend_Pdf_Page $page
     * @param \Totaltools\Pronto\Model\InstoreOrder $order
     * @param bool $putOrderId
     */
    protected function _insertOrder(&$page, $order, $putOrderId = true)
    {
        $top = 610;
        $this->setFontBoldCustom($page, 29);
        $page->setFillColor(new \Zend_Pdf_Color_Rgb(0.1451, 0.2471, 0.5608));
        $page->setLineColor(new \Zend_Pdf_Color_Rgb(0.1451, 0.2471, 0.5608));
        $page->drawText(__('Tax Invoice'), 25, $top, 'UTF-8');

        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0));
        $this->printNextLineOrderInfo($page, 'Invoice No.', $order->getInvoiceNumber(), 25, 570);
        $this->printNextLineOrderInfo($page, 'Order Date', $order->getTransDate(), 25, 530);
        $this->printNextLineOrderInfo($page, 'Order No.', $order->getOrderNo(), 295, 570);
        if ($onlineOrderId = $order->getOnlineOrderId()) {
            $this->printNextLineOrderInfo($page, 'Order ID', $onlineOrderId, 295, 530);
        }
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0.6));
        $page->drawLine(25, 470, 270, 470);
        $page->drawLine(295, 470, 580, 470);

        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0));
        if ($insiderId = $order->getInsiderId()) {
            $this->printNextLineOrderInfo($page, 'Insider ID:', $insiderId, 25, 450);
        }
        if ($company = $order->getCompany()) {
            $this->printNextLineOrderInfo($page, 'Company:', $company, 25, 410);
        }
        if ($firstName = $order->getFirstName()) {
            $this->printNextLineOrderInfo($page, 'First Name:', $firstName, 295, 450);
        }
        if ($lastName = $order->getLastName()) {
            $this->printNextLineOrderInfo($page, 'Last Name:', $lastName, 295, 410);
        }
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0.6));
        $page->drawLine(25, 350, 270, 350);
        $page->drawLine(295, 350, 580, 350);

        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0));
        if ($payments = $order->getPaymentMethods()) {
            $this->printNextLineOrderInfo($page, 'Payment Method', $payments, 25, 330);
        }
        if ($invoiceTotal = $order->getOrderTotal()) {
            $this->printNextLineOrderInfo($page, 'Invoice Total', $this->formatTotal($invoiceTotal), 295, 330);
        }
    }

    /**
     * @param $page
     * @param $label
     * @param $value
     * @param $x
     * @param $y
     * @throws \Zend_Pdf_Exception
     */
    private function printNextLineOrderInfo(&$page, $label, $value, $x, $y)
    {
        $this->setFontBoldCustom($page, 11);
        $page->drawText(__($label), $x, $y, 'UTF-8');
        $this->setFontRegularCustom($page, 11);
        $page->drawText($value, $x, $y - 14, 'UTF-8');
    }

    /**
     * @param $page
     * @param \Totaltools\Pronto\Model\InstoreOrder $order
     */
    protected function _insertItems(&$page, $order)
    {
        $this->y = 280;
        $this->setFontBoldCustom($page, 15);
        $page->drawText(__('INVOICE SUMMARY'), 25, $this->y, 'UTF-8');
        $this->y -= 17;
        $this->printItemHeader($page, $this->y);
        $this->y -= 35;
        $orderItems = $order->getOrderItems()->getItems();
        $shippingLines = [];
        $discountLines = [];
        foreach ($orderItems as $index => $orderItem) {
            if ($this->y < 128) {
                $page = $this->newPage();
                $this->printItemHeader($page, $this->y);
                $this->y -= 35;
            }
            $orderItem->setStockDescription(
                $this->convertHelper->convertString(trim($orderItem->getStockDescription()))
            );
            $orderItem->setStockCode(
                $this->convertHelper->convertString(trim($orderItem->getStockCode()))
            );
            if ($orderItem->getStockCode() === 'Discount') {
                $discountLines[] = $orderItem;
                continue;
            } elseif ($orderItem->getStockCode() === 'Delivery') {
                $shippingLines[] = $orderItem;
                continue;
            }
            $this->printItemLines(
                $page,
                $orderItem->getStockCode(),
                $orderItem->getStockDescription(),
                $orderItem->getQty() + 0,
                $this->formatTotal(number_format((float)$orderItem->getLineTax(), 2, '.', '')),
                $this->formatTotal(number_format((float)$orderItem->getLineValue(), 2, '.', '')),
                $this->y - 20
            );
        }
        foreach ($shippingLines as $orderItem) {
            if ($this->y < 128) {
                $page = $this->newPage();
                $this->printItemHeader($page, $this->y);
                $this->y -= 35;
            }
            $this->printItemLines(
                $page,
                $orderItem->getStockCode(),
                $orderItem->getStockDescription(),
                $orderItem->getQty() + 0,
                $this->formatTotal(number_format((float)$orderItem->getLineTax(), 2, '.', '')),
                $this->formatTotal(number_format((float)$orderItem->getLineValue(), 2, '.', '')),
                $this->y - 20
            );
        }
        foreach ($discountLines as $orderItem) {
            if ($this->y < 128) {
                $page = $this->newPage();
                $this->printItemHeader($page, $this->y);
                $this->y -= 35;
            }
            $this->printItemLines(
                $page,
                $orderItem->getStockCode(),
                $orderItem->getStockDescription(),
                $orderItem->getQty() + 0,
                $this->formatTotal(number_format((float)$orderItem->getLineTax(), 2, '.', '')),
                $this->formatTotal(number_format((float)$orderItem->getLineValue(), 2, '.', '')),
                $this->y - 20
            );
        }
    }

    /**
     * @param $page
     * @param $y
     */
    private function printItemHeader(&$page, $y)
    {
        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0));
        $page->drawLine(25, $y, 580, $y);
        $page->drawLine(25, $y - 33, 580, $y - 33);

        $this->setFontBoldCustom($page, 11);
        $page->drawText(__('Item Code'), 25, $y - 20, 'UTF-8');
        $page->drawText(__('Item Description'), 120, $y - 20, 'UTF-8');
        $page->drawText(__('QTY'), 300, $y - 20, 'UTF-8');
        $page->drawText(__('GST'), 372, $y - 20, 'UTF-8');
        $page->drawText(__('Total Price (Inc GST)'), 450, $y - 20, 'UTF-8');
    }

    /**
     * @param $page
     * @param $y
     */
    private function printItemLines(&$page, $itemCode, $itemDescription, $qty, $gst, $total, $y)
    {
        $page->setFillColor(new \Zend_Pdf_Color_GrayScale(0));
        $page->setLineColor(new \Zend_Pdf_Color_GrayScale(0));
        $font = $this->setFontRegularCustom($page, 11);
        $page->drawText($itemCode, 25, $y, 'UTF-8');
        $page->drawText($qty, 307, $y, 'UTF-8');
        $text = $gst;
        $textWidth = $this->getTextWidth($text, $font, 11);
        $page->drawText($text, 412 - $textWidth - 11, $y, 'UTF-8');
        $text = $total;
        $textWidth = $this->getTextWidth($text, $font, 11);
        $page->drawText($text, 530 - $textWidth - 11, $y, 'UTF-8');
        $textChunk = wordwrap($itemDescription, 25, "\n");
        foreach (explode("\n", $textChunk) as $textLine) {
            if ($itemDescription !== '') {
                $page->drawText(strip_tags(ltrim($textLine)), 120, $y, 'UTF-8');
                $y -= 15;
            }
        }
        $this->y = $y;
    }

    private function getTextWidth($text, $font, $font_size)
    {
        $drawing_text = iconv('', 'UTF-8', $text);
        $characters = [];
        for ($i = 0; $i < strlen($drawing_text); $i++) {
            $characters[] = ord($drawing_text[$i]);
        }
        $glyphs = $font->glyphNumbersForCharacters($characters);
        $widths = $font->widthsForGlyphs($glyphs);
        return (array_sum($widths) / $font->getUnitsPerEm()) * $font_size;
    }

    /**
     * @param \Zend_Pdf_Page $page
     * @param null $store
     * @throws \Zend_Pdf_Exception
     */
    protected function insertLogo(&$page, $store = null)
    {
        $top = 800;
        $imagePath = $this->_scopeConfig->getValue(
            'sales/identity/logo',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $store
        );
        $imagePath = $imagePath ? '/sales/store/logo/' . $imagePath : self::LOGO_IMAGE;

        if ($this->_mediaDirectory->isFile($imagePath)) {
            $image = \Zend_Pdf_Image::imageWithPath($this->_mediaDirectory->getAbsolutePath($imagePath));
            $widthLimit = $heightLimit = 190;
            $width = $image->getPixelWidth();
            $height = $image->getPixelHeight();
            $ratio = $width / $height;

            if ($ratio > 1 && $width > $widthLimit) {
                $width = $widthLimit;
                $height = $width / $ratio;
            } elseif ($ratio < 1 && $height > $heightLimit) {
                $height = $heightLimit;
                $width = $height * $ratio;
            } elseif ($ratio == 1 && $height > $heightLimit) {
                $height = $heightLimit;
                $width = $widthLimit;
            }

            $y1 = $top - $height;
            $y2 = $top;
            $x1 = 25;
            $x2 = $x1 + $width;
            $page->drawImage($image, $x1, $y1, $x2, $y2);
            $this->y = $y1 - 10;
        }
    }

    /**
     * @param $page
     * @param int $size
     * @return \Zend_Pdf_Resource_Font
     * @throws \Zend_Pdf_Exception
     */
    private function setFontRegularCustom($page, $size = 7)
    {
        return $this->_setFontRegular($page, $size);
    }

    /**
     * @param $page
     * @param int $size
     * @return \Zend_Pdf_Resource_Font
     * @throws \Zend_Pdf_Exception
     */
    private function setFontBoldCustom($page, $size = 7)
    {
        return $this->_setFontBold($page, $size);
    }
}
