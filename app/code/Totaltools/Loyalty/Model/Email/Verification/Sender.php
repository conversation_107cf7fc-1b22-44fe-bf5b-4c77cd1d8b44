<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Loyalty\Model\Email\Verification;

use Magento\Framework\App\Area;

/**
 * Class Sender
 * @package Totaltools\Pronto\Model\Customer\Verification
 */
class Sender
{
    /**
     * @var \Magento\Framework\Mail\Template\TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var \Magento\Customer\Model\CustomerRegistry
     */
    private $customerRegistry;

    /**
     * @var \Magento\Framework\Mail\Template\SenderResolverInterface
     */
    private $senderResolver;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var \Magento\Framework\Reflection\DataObjectProcessor
     */
    protected $dataProcessor;

    /**
     * @var \Magento\Customer\Helper\View
     */
    protected $customerViewHelper;

    /**
     * Url Builder
     *
     * @var \Magento\Framework\UrlInterface
     */
    protected $urlBuilder;

    /**
     * Sender constructor.
     * @param \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder
     * @param \Magento\Customer\Model\CustomerRegistry $customerRegistry
     * @param \Magento\Framework\Mail\Template\SenderResolverInterface $senderResolver
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\Reflection\DataObjectProcessor $dataObjectProcessor
     * @param \Magento\Customer\Helper\View $customerViewHelper
     */
    public function __construct(
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Magento\Customer\Model\CustomerRegistry $customerRegistry,
        \Magento\Framework\Mail\Template\SenderResolverInterface $senderResolver,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Reflection\DataObjectProcessor $dataObjectProcessor,
        \Magento\Customer\Helper\View $customerViewHelper,
        \Magento\Framework\UrlInterface $urlBuilder
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->customerRegistry = $customerRegistry;
        $this->senderResolver = $senderResolver;
        $this->scopeConfig = $scopeConfig;
        $this->dataProcessor = $dataObjectProcessor;
        $this->customerViewHelper = $customerViewHelper;
        $this->urlBuilder = $urlBuilder;
    }

    /**
     * Get customer verification url.
     *
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     *
     * @return string
     */
    private function getVerificationUrl($customer)
    {
        return $this->urlBuilder->getUrl(
            'loyalty/account/verification/',
            [
                'id' => $customer->getId(),
                'hash' => $customer->getCustomAttribute('email_verification_hash')->getValue()
            ]
        );
    }

    /**
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     * @throws \Magento\Framework\Exception\MailException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function sendVerificationEmail(\Magento\Customer\Api\Data\CustomerInterface $customer)
    {
        $customerEmailData = $this->getFullCustomerObject($customer);
        $templateVars = [
            'customer' => ['customerData' =>$customerEmailData],
            'verification_url' => $this->getVerificationUrl($customer),
            'extensions' => [],
        ];

        $mailer = $this->transportBuilder
            ->setTemplateIdentifier('customer_verification_email')
            ->setTemplateOptions([
                'area' => Area::AREA_FRONTEND,
                'store' => $customer->getStoreId(),
            ])
            ->setTemplateVars($templateVars)
            ->setFrom(
                $this->senderResolver->resolve(
                $this->scopeConfig->getValue(
                    \Magento\Customer\Model\EmailNotification::XML_PATH_REGISTER_EMAIL_IDENTITY,
                    'store',
                    $customer->getStoreId()
                ),
                $customer->getStoreId()
            )
            )
            ->addTo($customer->getEmail());
        $transport = $mailer->getTransport();
        $transport->getMessage()->setSubject(__('Customer Verification'));
        $transport->sendMessage();
    }

    /**
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     * @return \Magento\Customer\Model\Data\CustomerSecure
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getFullCustomerObject(\Magento\Customer\Api\Data\CustomerInterface $customer)
    {
        $mergedCustomerData = $this->customerRegistry->retrieveSecureData($customer->getId());
        $customerData = $this->dataProcessor
            ->buildOutputDataArray($customer, \Magento\Customer\Api\Data\CustomerInterface::class);
        $mergedCustomerData->addData($customerData);
        $mergedCustomerData->setData('name', $this->customerViewHelper->getCustomerName($customer));

        return $mergedCustomerData;
    }
}