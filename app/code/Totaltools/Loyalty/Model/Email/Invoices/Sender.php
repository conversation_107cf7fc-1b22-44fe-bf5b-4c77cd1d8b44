<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Loyalty\Model\Email\Invoices;

use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\Store;

/**
 * Class Sender
 * @package Totaltools\Loyalty\Model\Email\Invoices
 */
class Sender
{
    /**
     * @var \Magento\Framework\Mail\Template\TransportBuilder
     */
    protected $transportBuilder;

    /**
     * @var Attachment
     */
    protected $attachment;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * Sender constructor.
     * @param \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder
     * @param \Totaltools\Loyalty\Model\CustomerSession $customerSession
     * @param ScopeConfigInterface $scopeConfig,
     * @param Attachment $attachment
     */
    public function __construct(
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        ScopeConfigInterface $scopeConfig,
        Attachment $attachment
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->attachment = $attachment;
        $this->customerSession = $customerSession;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param $recipient
     * @throws \Exception
     */
    public function sendSelectedInvoices($recipient)
    {
        try {
            $recipient = $recipient ? $recipient : $this->customerSession->getCustomer()->getEmail();
            $attachments = $this->attachment->getAttachments($this->customerSession->getCustomerId());
            $template = $this->scopeConfig->getValue('totaltools_loyalty/email_selected_invoices/template');
            $sender = $this->scopeConfig->getValue('totaltools_loyalty/email_selected_invoices/sender');
            if (!$attachments) {
                throw new \Exception(__('Error while preparing attachment'));
            }

            $mailer = $this->transportBuilder
                ->setTemplateIdentifier($template)
                ->setTemplateOptions([
                    'area' => Area::AREA_FRONTEND,
                    'store' => Store::DEFAULT_STORE_ID,
                ])
                ->setTemplateVars([
                    'name' => $this->customerSession->getCustomer()->getName(),
                    'email' => $this->customerSession->getCustomer()->getEmail()
                ])
                ->setFrom($sender)
                ->addTo($recipient);
            $transport = $mailer->getTransport();
            $messageBody = $transport->getMessage()->getBody();
            $existParts = $messageBody->getParts();
            $transport->getMessage()->setSubject(__('Invoices'));

            foreach ($attachments as $attachment) {
                $messageBody->setParts(array_merge($existParts, [$attachment]));
                $transport->getMessage()->setBody($messageBody);
                $transport->sendMessage();
            }
        } catch (\Exception $exception) {
            throw $exception;
        }
    }
}