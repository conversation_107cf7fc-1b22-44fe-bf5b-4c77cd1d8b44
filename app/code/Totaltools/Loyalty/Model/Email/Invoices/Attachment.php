<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Loyalty\Model\Email\Invoices;

use Totaltools\Loyalty\Model\Invoices\Pdf\FileWriter;
use Laminas\Mime\Mime;
use Laminas\Mime\PartFactory;

/**
 * Class Attachment
 * @package Totaltools\Loyalty\Model\Email\Invoices
 */
class Attachment
{
    /**
     * @var  \Laminas\Mime\PartFactory
     */
    protected $partFactory;

    /**
     * @var \Magento\Framework\App\Filesystem\DirectoryList
     */
    protected $directoryList;

    /**
     * @var \Magento\Framework\Filesystem\Io\File
     */
    protected $ioFile;

    /**
     * Attachment constructor.
     * @param \Laminas\Mime\PartFactory
     * @param \Magento\Framework\Filesystem\Io\File $ioFile
     * @param \Magento\Framework\App\Filesystem\DirectoryList $directoryList
     */
    public function __construct(
        \Laminas\Mime\PartFactory $partFactory,
        \Magento\Framework\Filesystem\Io\File $ioFile,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList
    )
    {
        $this->partFactory = $partFactory;
        $this->ioFile = $ioFile;
        $this->directoryList = $directoryList;
    }

    /**
     * @param $customerId
     * @return array
     * @throws \Exception
     */
    public function getAttachments($customerId)
    {
        try {
            $folderPath = $this->directoryList->getPath(\Magento\Framework\App\Filesystem\DirectoryList::VAR_DIR) . '/' . FileWriter::INVOICES_FOLDER_NAME . '/';
            $this->ioFile->cd($folderPath . $customerId);
            $files = $this->ioFile->ls();
            $attachmentParts = [];

            foreach ($files as $file) {
                $attachmentPart = $this->partFactory->create();
                $attachmentPart->setContent($this->ioFile->read($file['text']))
                    ->setType('application/zip')
                    ->setFileName(FileWriter::INVOICES_ARCHIVE_NAME)
                    ->setDisposition(\Laminas\Mime\Mime::DISPOSITION_ATTACHMENT)
                    ->setEncoding(\Laminas\Mime\Mime::ENCODING_BASE64);
                $attachmentParts[] = $attachmentPart;
            }

            $this->ioFile->cd($folderPath);
            $this->ioFile->rmdir($customerId, true);
        } catch (\Exception $exception) {
            throw $exception;
        }

        return $attachmentParts;
    }
}