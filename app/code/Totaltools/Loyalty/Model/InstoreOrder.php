<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Loyalty
 */

namespace Totaltools\Loyalty\Model;

use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use \Totaltools\Pronto\Model\ResourceModel\InstoreOrder\CollectionFactory as InstoreOrderCollectionFactory;

/**
 * Class InstoreOrder
 * @package Totaltools\Loyalty\Model
 */
class InstoreOrder
{
    const MY_INVOICES_LAYOUT_NAME = 'loyalty.instoreorder.grid';

    const RECENT_INVOICES_LAYOUT_NAME = 'loyalty.recent.invoices.grid';
    private  $totalCount;
    /**
     * @var InstoreOrderCollectionFactory
     */
    protected $instoreOrderCollectionFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    protected $pricingHelper;

    /**
     * @var \Totaltools\Pronto\Model\ResourceModel\InstoreOrder\Collection;
     */
    protected $orders;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var \Shippit\Shipping\Model\Sync\Order
     */
    private $syncOrder;

    /**
     * @var \Shippit\Shipping\Model\ResourceModel\Sync\Order
     */
    private $orderResource;

    /**
     * @var \Magento\Sales\Api\Data\OrderInterface
     */
    private $order;

    /**
     * @var \Totaltools\Shippit\Helper\Data
     */
    private $helper;

    /**
     * @var \Totaltools\Loyalty\Model\Customer 
     */
    private $customer;

    /**
     * @var \Totaltools\Loyalty\Helper\Convert
     */
    private $convertHelper;

    private $localeDate;

    

    /**
     * Grid constructor.
     *
     * @param \Magento\Framework\View\Element\Template\Context   $context
     * @param InstoreOrderCollectionFactory                      $instoreOrderCollectionFactory
     * @param \Totaltools\Loyalty\Model\CustomerSession          $customerSession
     * @param \Magento\Framework\Pricing\Helper\Data             $pricingHelper
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Shippit\Shipping\Model\Sync\Order                 $syncOrder
     * @param \Shippit\Shipping\Model\ResourceModel\Sync\Order   $orderResource
     * @param \Magento\Sales\Api\Data\OrderInterface             $order
     * @param \Totaltools\Shippit\Helper\Data                    $helper
     * @param \Totaltools\Loyalty\Helper\Convert $convertHelper
     * @param array                                              $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        InstoreOrderCollectionFactory $instoreOrderCollectionFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Magento\Framework\Pricing\Helper\Data $pricingHelper,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Shippit\Shipping\Model\Sync\Order $syncOrder,
        \Shippit\Shipping\Model\ResourceModel\Sync\Order $orderResource,
        \Magento\Sales\Api\Data\OrderInterface $order,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate,
        \Totaltools\Shippit\Helper\Data $helper,
        \Totaltools\Loyalty\Helper\Convert $convertHelper
    ) {
        $this->instoreOrderCollectionFactory = $instoreOrderCollectionFactory;
        $this->customerSession = $customerSession;
        $this->pricingHelper = $pricingHelper;
        $this->scopeConfig = $scopeConfig;
        $this->syncOrder = $syncOrder;
        $this->orderResource = $orderResource;
        $this->order = $order;
        $this->helper = $helper;
        $this->localeDate = $localeDate;
        $this->convertHelper = $convertHelper;
    }

    /**
     * @return \Magento\Customer\Model\Customer
     */
    protected function getCustomer()
    {
        if(!isset($this->customer)) {
            return $this->customerSession->getCustomer();
        }
        return $this->customer;
    }

    /**
     * @return bool
     */
    public function isCustomerVerified()
    {
        $customAttributeValue = null;
        $customAttribute = $this->getCustomer()->getCustomAttribute('email_verification_status');
        if ($customAttribute !== null) {
            $customAttributeValue = $customAttribute->getValue();
        }
        return $customAttributeValue === EmailVerification::VERIFIED;
        
    }

    /**
     * @return bool|\Magento\Sales\Model\ResourceModel\Order\Collection
     */
    public function getCustomerInstoreOrders($customer, $params)
    {
        $this->customer = $customer;
        $this->totalCount = 0;
        $items = [];
        $currentPage = isset($params['page'])? $params['page'] : 1;
        $pageSize = $this->scopeConfig->getValue(
            'totaltools_loyalty/insider_rewards/transactions_recent_invoices',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        $instorOrderData = $this->getInstoreOrders($params, $currentPage, $pageSize);
        foreach ($instorOrderData as $key =>  $_order) {
            $items[$key]['order_id'] = $_order->getId();
            $items[$key]['order_no'] = $_order->getOrderNo();
            $items[$key]['online_order_id'] = $_order->getOnlineOrderId();
            $items[$key]['trans_date'] =  $this->formatDate($_order->getTransDate());
            $items[$key]['store_name'] = $_order->getStoreName();
            $items[$key]['order_total'] = $this->formatTotal($_order->getOrderTotal());
            $items[$key]['order_total_tax'] = $this->formatTotal($_order->getOrderTax());
            if ($_order->getStoreName() === 'Total Tools Online') {
                $items[$key]['tracking_url'] = $this->getTracking($_order->getOnlineOrderId());
            }
            $orderLineItems = $_order->getOrderItems();
            
            foreach($orderLineItems as $index => $orderLineItem) {
                $items[$key]['order_line_items'][$index]['code'] = $this->convertHelper->convertString($orderLineItem->getStockCode());
                $items[$key]['order_line_items'][$index]['description'] = $this->convertHelper->convertString($orderLineItem->getStockDescription());
                $items[$key]['order_line_items'][$index]['qty'] =  ($orderLineItem->getQty() +0);
                $items[$key]['order_line_items'][$index]['tax'] = $orderLineItem->getLineTax();
                $items[$key]['order_line_items'][$index]['item_price'] = $orderLineItem->getLineValue();;
            }
            
        }
         $pageInfo = [
            'page_size' => $pageSize,
            'current_page' => $currentPage,
            'total_pages' => (integer) ceil((double)$this->totalCount/$pageSize),
        ];

        return [
            'items' => $items,
            'page_info' => $pageInfo,
            'total_count' => $this->totalCount,
        ];
    }
    public function getInstoreOrders($params, $currentPage, $pageSize)
    {

        if (!$this->isCustomerVerified()) {
            return false;
        }
        if (!$this->orders) {
            $condition = array();
            $this->orders = $this->instoreOrderCollectionFactory->create()
                ->addFieldToSelect(['entity_id','order_no','trans_date','store_name','online_order_id'])
                ->addFieldToFilter('customer_id', $this->getCustomer()->getId())
                ->setOrder('trans_date', 'desc');
            
            if (isset($params['orders_from'])) {
                $condition['from'] = $this->formatRequestDate($params['orders_from']);
            }
            if (isset($params['orders_to'])) {
                $condition['to']  = $this->formatRequestDate($params['orders_to']);
            }
            if (isset($params['order_id'])) {
                $this->orders->addFieldToFilter('entity_id', $params['order_id']);
            }
            if (count($condition) > 0 ) {

                $this->orders->addFieldToFilter('trans_date', $condition);
            }

            $this->totalCount = $this->orders->getSize();
                if ($pageSize) {
                    $this->orders->setPageSize($pageSize)
                        ->setCurPage($currentPage);
                }
            
            
        }

        return $this->orders;
    }

    /**
     * @param  string $date
     * @return string
     */
    private function formatRequestDate(string $date)
    {
        return date('Y-m-d', strtotime(str_replace('/', '-', $date)));
    }

    /**
     * Retrieve formatting date
     *
     * @param null|string|\DateTimeInterface $date
     * @param int $format
     * @param bool $showTime
     * @param null|string $timezone
     * @return string
     */
    public function formatDate(
        $date = null,
        $format = \IntlDateFormatter::SHORT,
        $showTime = false,
        $timezone = null
    ) {

        $date = $date instanceof \DateTimeInterface ? $date : new \DateTime($date);
        return $this->localeDate->formatDateTime(
            $date,
            $format,
            $showTime ? $format : \IntlDateFormatter::NONE,
            null,
            $timezone
        );
    }



    /**
     * Format order total
     *
     * @param  float $total
     * @return string
     */
    public function formatTotal($total)
    {
        return $this->pricingHelper->currency($total, true, false);
    }

    /**
     * Get customer is loyal
     *
     * @return boolean
     */
    public function getIsLoyal()
    {
        return $this->getCustomer()->getData('is_loyal');
    }

    /**
     * @return string
     */
    public function getInvoicesCookieName()
    {
        return \Totaltools\Loyalty\Helper\Cookie::SELECTED_INVOICES_COOKIE_NAME;
    }

    /**
     * @return bool
     */
    public function customerVerified()
    {
        return empty($this->getCustomer()->getData('unverified_loyalty_id'));
    }


    /**
     * Get Order tracking code
     *
     * @param $orderId
     *
     * @return string
     */
    public function getTracking($orderId)
    {
        $orderId = $this->getOrderByIncrementId($orderId);
        $this->orderResource->load($this->syncOrder, $orderId, 'order_id');
        if ($orderId != $this->syncOrder->getOrderId()) {
            return '';
        }
        $shippitTrackingNumber = $this->syncOrder->getTrackingNumber();
        if ($shippitTrackingNumber) {
            return $this->decorateTrackNumber($shippitTrackingNumber);
        }
        return '';
    }

    /**
     *  Process Tracking Number
     *
     * @param $value
     *
     * @return string
     */
    public function decorateTrackNumber($value)
    {

        $shippingMethod = $this->syncOrder->getShippingMethod();
        if ($shippingMethod == 'click_and_collect') {
            $tooltip = 'You will receive an email once your order is ready for collection at your selected Total Tools Store';
            $value = sprintf('<img width="24px" src="%s" title="%s">', $this->getViewFileUrl('Totaltools_Loyalty::images/info-64.png'), $tooltip);
            return $value;
        }
        if (empty($value)) {
            return $value;
        }
        $url = $this->helper->getEnvironmentUrl().$value;
        $cell = sprintf(
            '<a href="%s" title="Track Shipment" target="_blank">%s</a>',
            $url,
            __('Track Shipment')
        );
        return $cell;
    }

    /**
     * Get order by Increment ID
     *
     * @param $incrementId
     *
     * @return mixed
     */
    public function getOrderByIncrementId($incrementId)
    {
        $order = $this->order->loadByIncrementId($incrementId);
        if ($incrementId == $order->getIncrementId()) {
            return $order->getEntityId();
        }
        return null;
    }
}
