<?php

namespace Totaltools\Loyalty\Model\Rest\Customer;

use Totaltools\Loyalty\Api\RewardManagementInterface;
use Magento\Customer\Model\CustomerRegistry;

class RewardManagement implements RewardManagementInterface
{
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $_request;
    /**
     * Reward helper
     *
     * @var \Magento\Reward\Helper\Data
     */
    protected $_rewardData;
    /**
     * Customer converter
     *
     * @var CustomerRegistry
     */
    protected $_customerRegistry;
    /**
     * Reward factory
     *
     * @var \Magento\Reward\Model\RewardFactory
     */
    protected $_rewardFactory;

    /**
     * RewardManagement constructor.
     *
     * @param \Magento\Framework\Webapi\Rest\Request $request
     * @param \Magento\Reward\Helper\Data            $rewardData
     */
    public function __construct(
        \Magento\Framework\Webapi\Rest\Request $request,
        \Magento\Reward\Helper\Data $rewardData,
        CustomerRegistry $customerRegistry,
        \Magento\Reward\Model\RewardFactory $rewardFactory
    )
    {
        $this->_request = $request;
        $this->_rewardData = $rewardData;
        $this->_customerRegistry = $customerRegistry;
        $this->_rewardFactory = $rewardFactory;
    }

    /**
     * Update a customers native rewards points balance with an email address.
     *
     * @param string $email
     *
     * @api
     * @return boolean
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function setBalance($email)
    {
        if (!$this->_request->isPut() || !$this->_rewardData->isEnabled()) {
            return false;
        }

        $params = $this->_request->getBodyParams();
        if (isset($params['customer_reward']) && isset($params['customer_reward']['balance'])) {
            /** @var \Magento\Customer\Api\Data\CustomerInterface $customerModel */
            $customerModel = $this->_customerRegistry->retrieveByEmail($email);
            /** @var \Magento\Reward\Model\Reward $reward */
            $reward = $this->_rewardFactory->create();
            $reward->setCustomer($customerModel)
                ->setWebsiteId($customerModel->getWebsiteId())
                ->loadByCustomer();
            $reward->setPointsBalance($params['customer_reward']['balance']);
            $reward->setAction(\Magento\Reward\Model\Reward::REWARD_ACTION_ADMIN)
                ->setActionEntity($customerModel)
                ->updateRewardPoints();
        }

        return true;
    }
}