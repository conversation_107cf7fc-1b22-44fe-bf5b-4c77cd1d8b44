<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Loyalty
 */

namespace Totaltools\Loyalty\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\Loyalty\Model\Update as LoyaltyUpdate;

class UpdateCustomerCommand extends Command
{

    const NAME        = 'totaltools:loyalty:update-customer';
    const DESCRIPTION = 'Update customer loyalty data from Pronto Connect';

    const MESSAGE_SUCCESS = 'Updated %s customer(s) in %s';
    const MESSAGE_ERROR   = 'Error: %s';

    const INPUT_KEY_EMAIL = 'email';

    /**
     * Loyalty update
     * @var LoyaltyUpdate
     */
    private $loyaltyUpdate;

    /**
     * UpdateCustomerCommand constructor
     *
     * @param LoyaltyUpdate $loyaltyUpdate
     */
    public function __construct(
        LoyaltyUpdate $loyaltyUpdate
    ) {
        parent::__construct();
        $this->loyaltyUpdate = $loyaltyUpdate;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure ()
    {
        $this
            ->setName(self::NAME)
            ->setDescription(self::DESCRIPTION)
            ->setDefinition($this->getInputList())
        ;
        parent::configure();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute (
        InputInterface $input,
        OutputInterface $output
    ) {
        try {
            $startTime  = microtime(true);

            $count = 0;
            $emails = $input->getArgument(self::INPUT_KEY_EMAIL);
            if (count($emails)) {
                $count = $this->loyaltyUpdate->updateCustomersByEmail($emails);
            }

            $resultTime = microtime(true) - $startTime;
            $time = gmdate('H:i:s', $resultTime);
            $output->writeln('<info>' . sprintf(self::MESSAGE_SUCCESS, $count, $time) . '</info>');
        } catch (\Exception $e) {
            $output->writeln('<error>' . sprintf(self::MESSAGE_ERROR, $e->getMessage()) . '</error>');
            debug_print_backtrace();
        }
    }

    /**
     * Get list of options and arguments for the command
     *
     * @return mixed[]
     */
    public function getInputList()
    {
        return [
            new InputArgument(
                self::INPUT_KEY_EMAIL,
                InputArgument::REQUIRED | InputArgument::IS_ARRAY,
                'Space-separated list of customer email addresses to update'
            ),
        ];
    }

}