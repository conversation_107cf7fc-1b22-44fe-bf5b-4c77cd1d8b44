<?php
/**
 * <AUTHOR> Internet
 * @package Totaltools\Loyalty
 */
namespace Totaltools\Loyalty\Helper;

use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use \Totaltools\Pronto\Model\ResourceModel\InstoreOrder\CollectionFactory as InstoreOrderCollectionFactory;
use Magento\Framework\View\Asset\Repository;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{

    const MY_INVOICES_LAYOUT_NAME = 'loyalty.instoreorder.grid';

    const RECENT_INVOICES_LAYOUT_NAME = 'loyalty.recent.invoices.grid';
    private  $totalCount;

    /**
     * @var InstoreOrderCollectionFactory
     */
    protected $instoreOrderCollectionFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    protected $pricingHelper;

    /**
     * @var \Totaltools\Pronto\Model\ResourceModel\InstoreOrder\Collection;
     */
    protected $orders;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var \Shippit\Shipping\Model\Sync\Order
     */
    protected $syncOrder;

    /**
     * @var \Shippit\Shipping\Model\ResourceModel\Sync\Order
     */
    protected $orderResource;

    /**
     * @var \Magento\Sales\Api\Data\OrderInterface
     */
    protected $order;

    /**
     * @var \Totaltools\Shippit\Helper\Data
     */
    protected $helper;

    /**
     * @var Repository
     */
    protected $assetRepo;

    /**
     * @var InstoreOrderFactory
     */
    protected $instoreOrderFactory;

    /**
     * @var \Totaltools\Loyalty\Helper\Convert
     */
    protected $convertHelper;

    
    /**
     * Grid constructor.
     *
     * @param \Magento\Framework\App\Helper\Context   $context
     * @param InstoreOrderCollectionFactory                      $instoreOrderCollectionFactory
     * @param \Totaltools\Loyalty\Model\CustomerSession          $customerSession
     * @param \Magento\Framework\Pricing\Helper\Data             $pricingHelper
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Shippit\Shipping\Model\Sync\Order                 $syncOrder
     * @param \Shippit\Shipping\Model\ResourceModel\Sync\Order   $orderResource
     * @param \Magento\Sales\Api\Data\OrderInterface             $order
     * @param \Totaltools\Shippit\Helper\Data                    $helper
     * @param \Totaltools\Shippit\Helper\Repository              $assetRepo
     * @param \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory
     * @param \Totaltools\Loyalty\Helper\Convert $convertHelper
     * 
     * @param array                                              $data
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        InstoreOrderCollectionFactory $instoreOrderCollectionFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Magento\Framework\Pricing\Helper\Data $pricingHelper,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Shippit\Shipping\Model\Sync\Order $syncOrder,
        \Shippit\Shipping\Model\ResourceModel\Sync\Order $orderResource,
        \Magento\Sales\Api\Data\OrderInterface $order,
        \Totaltools\Shippit\Helper\Data $helper,
        Repository              $assetRepo,
        \Totaltools\Pronto\Model\InstoreOrderFactory $instoreOrderFactory,
        \Totaltools\Loyalty\Helper\Convert $convertHelper,
        array $data = []
    ) {
        parent::__construct($context);
        $this->instoreOrderCollectionFactory = $instoreOrderCollectionFactory;
        $this->customerSession = $customerSession;
        $this->pricingHelper = $pricingHelper;
        $this->scopeConfig = $scopeConfig;
        $this->syncOrder = $syncOrder;
        $this->orderResource = $orderResource;
        $this->order = $order;
        $this->helper = $helper;
        $this->assetRepo = $assetRepo;
        $this->instoreOrderFactory = $instoreOrderFactory;
        $this->convertHelper = $convertHelper;
    }

    /**
     * @param string $configPath
     * @return string | null
     *
     * */
    public function getConfig($configPath)
    {
        return $this->scopeConfig->getValue($configPath);
    }

    /**
     * @return \Magento\Customer\Model\Customer
     */
    protected function getCustomer()
    {
        return $this->customerSession->getCustomer();
    }

    /**
     * @return bool
     */
    public function isCustomerVerified()
    {
        return $this->getCustomer()->getData('email_verification_status') === EmailVerification::VERIFIED;
    }

    /**
     * getInstoreOrders
     *
     * @param  mixed $params
     * @return Magento\Framework\Data\Collection
     */
     /**
     * @return bool|\Magento\Sales\Model\ResourceModel\Order\Collection
     */
    public function getInstoreOrdersGraphql($params)
    {
        // $instoreOrders = $this->getInstoreOrders($params);
        $orders = [];
        $this->totalCount = 0;
        $currentPage = isset($params['page'])? $params['page'] : 1;
        $pageSize = $this->scopeConfig->getValue(
            'totaltools_loyalty/insider_rewards/transactions_recent_invoices',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        $instoreOrders = $this->getInstoreOrders($params, $currentPage, $pageSize);
        foreach ($instoreOrders as $key => $instoreOrder) {
            $orders[$key]['order_id'] = $instoreOrder->getId();
            $orders[$key]['order_no'] = $instoreOrder->getOrderNo();
            $orders[$key]['online_order_id'] = $instoreOrder->getOnlineOrderId();
            $orders[$key]['trans_date'] = $instoreOrder->getTransDate();
            $orders[$key]['store_name'] = $instoreOrder->getStoreName();
            $orders[$key]['order_tax'] = $instoreOrder->getOrderTax();
            $orders[$key]['order_total'] = $instoreOrder->getOrderTotal();
            $orders[$key]['first_name'] = $instoreOrder->getFirstName();
            $orders[$key]['last_name'] = $instoreOrder->getLastName();
            $orders[$key]['abn'] = $instoreOrder->getAbn();
            $orders[$key]['address_1'] = $instoreOrder->getAddress1();
            $orders[$key]['address_2'] = $instoreOrder->getAddress2();
            $orders[$key]['address_3'] = $instoreOrder->getAddress3();
            $orders[$key]['post_code'] = $instoreOrder->getPostCode();
            $orders[$key]['phone'] = $instoreOrder->getPhone();
            $orders[$key]['email'] = $instoreOrder->getEmail();
            $orders[$key]['payment_methods'] = $instoreOrder->getPaymentMethods();
            $orders[$key]['tracking_url'] = $this->getTracking($instoreOrder->getOnlineOrderId());
        }
        $pageInfo = [
            'page_size' => $pageSize,
            'current_page' => $currentPage,
            'total_pages' => (integer) ceil((double)$this->totalCount/$pageSize),
        ];

        return [
            'items' => $orders,
            'page_info' => $pageInfo,
            'total_count' => $this->totalCount,
        ];
        // return $orders;
    }

    /**
     * getInstoreOrderDetailsGraphql
     *
     * @param  mixed $params
     * @return Magento\Framework\Data\Collection
     */
     /**
     * @return bool|\Magento\Sales\Model\ResourceModel\Order
     */
    public function getInstoreOrderDetailsGraphql($params)
    {
        // $instoreOrders = $this->getInstoreOrders($params);
        $order = [];
        $this->totalCount = 0;
        $currentPage = isset($params['page'])? $params['page'] : 1;
        $pageSize = $this->scopeConfig->getValue(
            'totaltools_loyalty/insider_rewards/transactions_recent_invoices',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        $instoreOrders = $this->getInstoreOrders($params, $currentPage, $pageSize);
        foreach ($instoreOrders as $key => $instoreOrder) {
                $order[$key]['order_id'] = $instoreOrder->getId();
                $order[$key]['order_no'] = $instoreOrder->getOrderNo();
                $order[$key]['online_order_id'] = $instoreOrder->getOnlineOrderId();
                $order[$key]['trans_date'] = $instoreOrder->getTransDate();
                $order[$key]['store_name'] = $instoreOrder->getStoreName();
                $order[$key]['order_tax'] = $instoreOrder->getOrderTax();
                $order[$key]['order_total'] = $instoreOrder->getOrderTotal();
                $order[$key]['first_name'] = $instoreOrder->getFirstName();
                $order[$key]['last_name'] = $instoreOrder->getLastName();
                $order[$key]['abn'] = $instoreOrder->getAbn();
                $order[$key]['address_1'] = $instoreOrder->getAddress1();
                $order[$key]['address_2'] = $instoreOrder->getAddress2();
                $order[$key]['address_3'] = $instoreOrder->getAddress3();
                $order[$key]['post_code'] = $instoreOrder->getPostCode();
                $order[$key]['invoice_number'] = $instoreOrder->getInvoiceNumber();
                $order[$key]['insider_id'] = $instoreOrder->getInsiderId();
                $order[$key]['company'] = $instoreOrder->getCompany();
                $order[$key]['phone'] = $instoreOrder->getPhone();
                $order[$key]['email'] = $instoreOrder->getEmail();
                $order[$key]['payment_methods'] = $instoreOrder->getPaymentMethods();
                $order[$key]['tracking_url'] = $this->getTracking($instoreOrder->getOnlineOrderId());
                
            
            

                if ($instoreOrder->getId()) {
                    $orderItems = $instoreOrder->getOrderItems();
                } else {
                    $orderItems = false;
                    
                }
            // $order['count_items'] = $orderItems->getSize();
                foreach ($orderItems as $index => $orderItem) {
                    $stockCode  = $this->convertString(trim($orderItem->getStockCode()));
                    $stockDescription = $this->convertString(trim($orderItem->getStockDescription()));
                    $order[$key]['order_items'][$index]['stock_code'] = $stockCode;
                    $order[$key]['order_items'][$index]['stock_description'] = $stockDescription;
                    $order[$key]['order_items'][$index]['qty'] = $orderItem->getQty();
                    $order[$key]['order_items'][$index]['tax'] = $orderItem->getLineTax();
                    $order[$key]['order_items'][$index]['item_total_price'] = $orderItem->getLineValue();
                }
            }
        $pageInfo = [
            'page_size' => $pageSize,
            'current_page' => $currentPage,
            'total_pages' => (integer) ceil((double)$this->totalCount/$pageSize),
        ];

        return [
            'items' => $order,
            'page_info' => $pageInfo,
            'total_count' => $this->totalCount,
        ];
    }
    
     /**
     * getInstoreOrders
     *
     * @param  mixed $customer
     * @return Magento\Framework\Data\Collection
     */
     /**
     * @return bool|\Magento\Sales\Model\ResourceModel\Order\Collection
     */
    public function getInstoreOrders($params, $currentPage, $pageSize)
    {
        if (!$this->isCustomerVerified()) {
            return [];
        }
        
        if (!$this->orders) {
            $this->orders = $this->instoreOrderCollectionFactory->create()
                ->addFieldToSelect('*')
                ->addFieldToFilter('customer_id', $this->getCustomer()->getId())
                ->setOrder('trans_date', 'desc');
                $transactionsShow = $this->scopeConfig->getValue(
                    'totaltools_loyalty/insider_rewards/transactions_recent_invoices',
                    \Magento\Store\Model\ScopeInterface::SCOPE_STORE
                );
                if ($transactionsShow) {
                    $this->orders->setPageSize($transactionsShow)
                        ->setCurPage($currentPage);
                }
            
            $condition = array();
            if ( isset($params['orders_from']) && $params['orders_from']) {
                $condition['from'] = $this->formatRequestDate($params['orders_from']);
            }
            if ( isset($params['orders_to']) && $params['orders_to']) {
                $condition['to']  = $this->formatRequestDate($params['orders_to']);
            }
            if (isset($params['order_id'])) {
                $this->orders->addFieldToFilter('entity_id', $params['order_id']);
            }
            if (count($condition) > 0 ) {
                $this->orders->addFieldToFilter('trans_date', $condition);
            }
            $this->totalCount = $this->orders->getSize();
                if ($pageSize) {
                    $this->orders->setPageSize($pageSize)
                        ->setCurPage($currentPage);
                }
        }

        return $this->orders;
    }

    /**
     * getInstoreOrderDetail
     *
     * @param  mixed $params
     * @return Magento\Framework\Data\Collection
     */
    public function getInstoreOrderDetail($params)
    {
        if (!$this->isCustomerVerified()) {
            return [];
        }
        if (!($customerId = $this->getCustomer()->getId())) {
            return false;
        }

        if ($params['order_id']) {
            $order = $this->instoreOrderFactory->create()->load($params['order_id']);
            if ($order->getId()) {
                return $order;
            }
        }

        return false;
    }

    /**
     * @param  string $date
     * @return string
     */
    private function formatRequestDate(string $date)
    {
        return date('Y-m-d', strtotime(str_replace('/', '-', $date)));
    }

    /**
     * Format order total
     *
     * @param  float $total
     * @return string
     */
    public function formatTotal($total)
    {
        return $this->pricingHelper->currency($total, true, false);
    }

    /**
     * Get customer is loyal
     *
     * @return boolean
     */
    public function getIsLoyal()
    {
        return $this->customerSession->getCustomer()->getData('is_loyal');
    }

     /**
     * Get Order tracking code
     *
     * @param $orderId
     *
     * @return string
     */
    public function getTracking($orderId)
    {
        $orderId = $this->getOrderByIncrementId($orderId);
        $this->orderResource->load($this->syncOrder, $orderId, 'order_id');
        if ($orderId != $this->syncOrder->getOrderId()) {
            return '';
        }
        $shippitTrackingNumber = $this->syncOrder->getTrackingNumber();
        if ($shippitTrackingNumber) {
            return $this->decorateTrackNumber($shippitTrackingNumber);
        }
        return '';
    }

    /**
     *  Process Tracking Number
     *
     * @param $value
     *
     * @return string
     */
    public function decorateTrackNumber($value)
    {

        $shippingMethod = $this->syncOrder->getShippingMethod();
        if ($shippingMethod == 'click_and_collect') {
            $params = array('_secure' => $this->_request->isSecure());
            $tooltip = 'You will receive an email once your order is ready for collection at your selected Total Tools Store';
            $value = sprintf('<img width="24px" src="%s" title="%s">', $this->assetRepo->getUrlWithParams('Totaltools_Loyalty::images/info-64.png', $params), $tooltip);
            return $value;
        }
        if (empty($value)) {
            return $value;
        }
        $url = $this->helper->getEnvironmentUrl().$value;
        $cell = sprintf(
            '<a href="%s" title="Track Shipment" target="_blank">%s</a>',
            $url,
            __('Track Shipment')
        );
        return $cell;
    }

    /**
     * Get order by Increment ID
     *
     * @param $incrementId
     *
     * @return mixed
     */
    public function getOrderByIncrementId($incrementId)
    {
        $order = $this->order->loadByIncrementId($incrementId);
        if ($incrementId == $order->getIncrementId()) {
            return $order->getEntityId();
        }
        return null;
    }

   

    /**
     * @param $value
     * @return mixed
     */
    public function convertString($value)
    {
        return $this->convertHelper->convertString($value);
    }
}