<?php
/**
 * Totaltools Loyalty.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Loyalty\Helper;

/**
 * Class Convert
 * @package Totaltools\Loyalty\Helper
 */
class Convert
{
    /**
     * @var array
     */
    private $data = [
        'Charge' => 'Delivery',
        'COUPONDISCOUNT' => 'Discount',
        'COUPON DISCOUNT' => 'Discount',
        'Shippit - Standard' => 'Delivery Method - Standard',
        'Free Shipping - Free Shipping' => 'Free Shipping',
        'Shippit Click and Collect' => 'Delivery Method - Click and Collect'
    ];

    /**
     * @param $value
     * @return mixed
     */
    public function convertString($value)
    {
        if(array_key_exists($value, $this->data)) {
            return $this->data[$value];
        }

        return $value;
    }
}
