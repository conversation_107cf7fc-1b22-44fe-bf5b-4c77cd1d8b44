<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Loyalty\Controller\Insider;

use Magento\Framework\App\Action\Context;

/**
 * Class Email
 * @package Totaltools\Loyalty\Controller\Insider
 */
class Email extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Totaltools\Loyalty\Model\Email\Invoices\Sender
     */
    protected $_invoiceSender;

    /**
     * @var \Magento\Framework\Stdlib\CookieManagerInterface
     */
    protected $_cookieManager;

    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    protected $_jsonResultFactory;

    /**
     * @var \Totaltools\Loyalty\Model\Invoices\Pdf\FileWriter
     */
    protected $_fileWriter;

    /**
     * Email constructor.
     * @param Context $context
     * @param \Totaltools\Loyalty\Model\Email\Invoices\Sender $_invoiceSender
     * @param \Magento\Framework\Controller\Result\JsonFactory $_jsonResultFactory
     * @param \Magento\Framework\Stdlib\CookieManagerInterface $_cookieManager
     * @param \Totaltools\Loyalty\Model\Invoices\Pdf\FileWriter $fileWriter
     */
    public function __construct(
        Context $context,
        \Totaltools\Loyalty\Model\Email\Invoices\Sender $_invoiceSender,
        \Magento\Framework\Controller\Result\JsonFactory $_jsonResultFactory,
        \Magento\Framework\Stdlib\CookieManagerInterface $_cookieManager,
        \Totaltools\Loyalty\Model\Invoices\Pdf\FileWriter $fileWriter
    )
    {
        parent::__construct($context);
        $this->_invoiceSender = $_invoiceSender;
        $this->_cookieManager = $_cookieManager;
        $this->_jsonResultFactory = $_jsonResultFactory;
        $this->_fileWriter = $fileWriter;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Json|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $result = $this->_jsonResultFactory->create();
        $success = true;

        try {
            $invoiceIds = $this->_cookieManager->getCookie(\Totaltools\Loyalty\Helper\Cookie::SELECTED_INVOICES_COOKIE_NAME);
            $emailTo = $this->getRequest()->getParam('send_invoices_to', false);

            if (empty($invoiceIds)) {
                $this->messageManager->addErrorMessage(__('Please, select invoices'));
                $success = false;
            }
            $validator = new \Laminas\Validator\EmailAddress();
            if ($emailTo && !$validator->isValid($emailTo, \Magento\Framework\Validator\EmailAddress::class)) {
                $this->messageManager->addNoticeMessage(__('Wrong email format'));
                $success = false;
            }

            if (!$success) {
                $result->setData(['success' => $success]);

                return $result;
            }

            $invoiceIds = explode(',', $invoiceIds);
            $this->_fileWriter->createFiles($invoiceIds, true);
            $this->_invoiceSender->sendSelectedInvoices($emailTo);
            $this->messageManager->addSuccessMessage(__('Invoices sent successfully.'));
        } catch (\Exception $e) {
            $success = false;
            $this->messageManager->addErrorMessage(__($e->getMessage()));
        }

        $result->setData(['success' => $success]);

        return $result;
    }
}
