<?php
/**
 * Total-Info Tech
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
namespace Totaltools\Loyalty\Controller\Insider;

use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\UrlFactory;
use Totaltools\Loyalty\Model\Data\Collection;
use Magento\Framework\Controller\Result\JsonFactory;

class InsiderRewards extends \Magento\Framework\App\Action\Action implements \Magento\Framework\App\ActionInterface
{

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * Pronto customer
     * @var \Totaltools\Pronto\Api\CustomerRequestManagerInterface
     */
    private $prontoRequestManager;

     /**
     * Customer factory
     * @var \Magento\Customer\Model\CustomerFactory
     */
    private $customerFactory;
    const CUSTOMER_BRONZE_LEVEL = 1;

    /**
     * @var \Magento\Framework\Data\CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var JsonFactory
     */
    private $resultJsonFactory;
    



    protected $_collection;

    /**
     * @var TimezoneInterface
     */
    protected $_timezone;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;


    /**
     * @param \Magento\Framework\App\Action\Context      $context
     * @param \Totaltools\Loyalty\Model\CustomerSession            $customerSession
     * @param \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory,
     * @param UrlFactory         $urlFactory         UrlFactory
     * @param JsonFactory         $resultJsonFactory         
     * @param TimezoneInterface $localeDate
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager,
        TimezoneInterface $localeDate,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        Collection $_collection,
        JsonFactory $resultJsonFactory,
        
        UrlFactory $urlFactory
    ) {
        $this->customerSession = $customerSession;
        $this->prontoRequestManager = $prontoRequestManager;
        $this->customerFactory = $customerFactory;
        $this->_timezone = $localeDate;
        $this->urlModel = $urlFactory->create();
        $this->_collection = $_collection;
        $this->resultJsonFactory = $resultJsonFactory;
        return parent::__construct($context);
    }


    /**
     * Check customer authentication
     *
     * @param  \Magento\Framework\App\RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function dispatch(\Magento\Framework\App\RequestInterface $request)
    {
        if (!$this->customerSession->authenticate()) {
            $this->_actionFlag->set('', 'no-dispatch', true);
        }
        return parent::dispatch($request);
    }


    /**
     * Dispatch request
     *
     * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function execute()
    {
        $page = ($this->getRequest()->getParam('p')) ? $this->getRequest()->getParam('p') : 1;
        $pageSize = ($this->getRequest()->getParam('limit')) ? $this->getRequest()->getParam('limit') : 10;
        $loyaltyPoints = $this->prontoRequestManager->getMemberPoints($this->getCustomer());
        $offset = ($page - 1) * $pageSize;
        $customerLevel = $this->getLoyaltyLevel();
        if ($loyaltyPoints) {
            $today = $this->_timezone->date()->format('d-m-Y');
            foreach ($loyaltyPoints as $key => $item) {
                if (isset($item['ExpiryDate']) && !is_array($item['ExpiryDate'])) {
                    $expiry = $this->formatExpiryDate($item['ExpiryDate']);
                    if ($expiry) {
                        if (strtotime($today) > strtotime($expiry)) {
                            unset($loyaltyPoints[$key]);
                            continue;
                        }
                        $item['ExpiryDate'] = $expiry;
                    }
                } else {
                    unset($loyaltyPoints[$key]);
                    continue;
                }
                if ($item['OriginalValue'] <= 0 && $item['PointAmount'] > 0) {
                    $loyaltyPoints[$key]['OriginalValue'] = ($item['PointAmount']/100);
                }

                if ($loyaltyPoints[$key]['OriginalValue']  <= 0) {
                    unset($loyaltyPoints[$key]);
                    continue;
                }

                if ($loyaltyPoints[$key]['PointsRemaining']  <= 0) {
                    unset($loyaltyPoints[$key]);
                    continue;
                } else {
                    $loyaltyPoints[$key]['PointsRemaining'] = $loyaltyPoints[$key]['PointsRemaining']/100;
                }

                $validTypes = ['AR', 'PR', 'TR', 'BR'];
                if (!in_array($item['TransType'], $validTypes)) {
                    unset($loyaltyPoints[$key]);
                    continue;
                }
               
                if($customerLevel > self::CUSTOMER_BRONZE_LEVEL) {
                    $extendExpiry = ['AR','TR','BR'];
                    if (in_array($item['TransType'], $extendExpiry)) {
                        $loyaltyPoints[$key]['ExtendExpiryDate'] = 1;
                        $loyaltyPoints[$key]['extendedTitle'] = '';
                        $loyaltyPoints[$key]['DisableExtend'] = 0;
                        $alreadyExtended = $item['Extended'];
                        if(isset($alreadyExtended) && $alreadyExtended == 'True') { 

                            $loyaltyPoints[$key]['DisableExtend'] = 1;
                            $loyaltyPoints[$key]['extendedTitle'] = "Expiry date has been extended for this transaction";
                        } 
                    } else {
                        $loyaltyPoints[$key]['ExtendExpiryDate'] = 0;
                    }
                }
                
                if(is_array($item['SeqNo'])) {
                    $loyaltyPoints[$key]['SeqNo'] = $item['SeqNo'][0];
                }
            }
            $this->_collection->addItems($loyaltyPoints);
            
        }
        $resultJson = $this->resultJsonFactory->create();
        $result = $this->_collection->toArray();
        $result['pageSize'] = $pageSize;
        $result['page'] = $page;
        return  $resultJson->setData(['content' =>$result]);
    }

    /**
     * @return bool
     */
    public function getLoyaltyLevel()
    {
        return $this->getCustomer()->getData('loyalty_level');
    }

    /**
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomer()
    {
        return $this->customerSession->getCustomer();
    }
    /**
     * Get formatted expiry date.
     *
     * @param $expiryDate
     *
     * @return string
     */
    public function formatExpiryDate($expiryDate)
    {
        try {
            return $this->_timezone
                ->date(new \DateTime($expiryDate))
                ->format('d-m-Y');
        } catch (\Exception $exception) {
            return '';
        }
    }
}