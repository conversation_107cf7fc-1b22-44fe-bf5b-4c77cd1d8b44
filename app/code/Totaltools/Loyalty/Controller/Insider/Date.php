<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Loyalty\Controller\Insider;

use Magento\Framework\App\Action\Context;

/**
 * Class Date
 * @package Totaltools\Loyalty\Controller\Insider
 */
class Date extends \Magento\Framework\App\Action\Action implements \Magento\Framework\App\ActionInterface
{
    const AVAILABLE_URL_PARAMS = [
        'orders_from',
        'orders_to'
    ];

    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    protected $jsonResultFactory;

    /**
     * Date constructor.
     * @param Context $context
     * @param \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory
     */
    public function __construct(
        Context $context,
        \Magento\Framework\Controller\Result\JsonFactory $jsonResultFactory
    )
    {
        parent::__construct($context);
        $this->jsonResultFactory = $jsonResultFactory;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Json|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $result = $this->jsonResultFactory->create();
        $selectedDate = $this->getRequest()->getParam('dateValue');
        $filterType = $this->getRequest()->getParam('filterType');
        $params = $this->extractParams($this->_redirect->getRefererUrl());
        $params[$filterType] = $selectedDate;

        if (!$this->validateUrlParams($params)) {
            $this->messageManager->addErrorMessage(__('Select correct date!'));
            $params = [];
        }

        $result->setData($this->_url->getUrl('loyalty/insider/invoices', ['_query' => http_build_query($params)]));

        return $result;
    }

    /**
     * @param array $data
     * @return bool
     */
    protected function validateUrlParams(array $data)
    {
        foreach ($data as $key => $date) {
            if (!strtotime(str_replace('/', '-', $date))) {
                return false;
            }

            if (!in_array($key, self::AVAILABLE_URL_PARAMS)) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param string $url
     * @return array
     */
    protected function extractParams(string $url = '')
    {
        $params = [];
        $parsedUrl = parse_url($url, PHP_URL_QUERY);

        if ($parsedUrl) {
            parse_str($parsedUrl, $params);
        }

        return $params;
    }
}