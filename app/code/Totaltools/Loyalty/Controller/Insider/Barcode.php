<?php

namespace Totaltools\Loyalty\Controller\Insider;

/**
 * @package     Totaltools_Loyalty
 * <AUTHOR> a<PERSON> <<EMAIL>>
 * @since       1.0.0
 * @copyright   2023 (c) Totaltools. <https://totaltools.com.au>
 */

class Barcode extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Magento\Framework\App\Request\Http
     */
    protected $request;

    /**
     * @var \Totaltools\Sales\Lib\BarcodeGenerator
     */
    protected $barcodeGenerator;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\App\Request\Http $request
     * @param \Totaltools\Sales\Lib\BarcodeGenerator $barcodeGenerator
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\App\Request\Http $request,
        \Totaltools\Sales\Lib\BarcodeGenerator $barcodeGenerator,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
    ) {
        $this->request = $request;
        $this->barcodeGenerator = $barcodeGenerator;
        parent::__construct($context);
    }

    /**
     * @inheritdoc
     */
    public function execute()
    {
        return $this->barcodeGenerator->output_image(
            'png',
            'code-128',
            $this->request->getParam('s'),
            [
                'h' => 150,
                'ts' => 2,
                'th' => 12,
                'w'=>300
            ]
        );
    }
}
