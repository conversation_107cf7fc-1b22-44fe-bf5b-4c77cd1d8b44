<?php

namespace Totaltools\Loyalty\Controller\Insider;

use Totaltools\Loyalty\Model\CustomerSession as CustomerSession;
use Magento\Framework\Exception\LocalizedException;
use Totaltools\Loyalty\Model\SystemConfig;
use Magento\Customer\Api\CustomerRepositoryInterface as CustomerRepository;

/**
 * Class Verification
 *
 * @package Totaltools\Loyalty\Controller\Insider
 */
class Verification extends \Magento\Framework\App\Action\Action
{
    /**
     * @var CustomerSession
     */
    protected $customer;

    /**
     * @var CustomerRepository
     */
    protected $customerRepository;

    /**
     * @var SystemConfig
     */
    protected $systemConfig;

    /**
     * Verification constructor.
     *
     * @param \Magento\Framework\App\Action\Context $context
     * @param CustomerSession                       $customer
     * @param SystemConfig                          $systemConfig
     * @param CustomerRepository                    $customerRepository
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        CustomerSession $customer,
        SystemConfig $systemConfig,
        CustomerRepository $customerRepository
    )
    {
        parent::__construct($context);
        $this->customer = $customer;
        $this->customerRepository = $customerRepository;
        $this->systemConfig = $systemConfig;
    }

    /**
     * Execute method.
     *
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Redirect
     */
    public function execute()
    {
        try {
            $customer = $this->customerRepository->getById($this->customer->getCustomerId());

            if ($this->isSendingEmailsAllowed($customer)) {
                $this->_triggerVerificationEmail($customer);

                $this->messageManager->addSuccessMessage(__('Confirmation email has been sent to your email.'));
            } else {
                $message = 'Limit of sending verification email is reached. '
                    . 'The next attempt will be available in %1 minutes.';
                $this->messageManager->addErrorMessage(__(
                    $message,
                    $this->systemConfig->getNextAttemptsDurationValue()
                ));
            }
        } catch (LocalizedException $exception) {
        } catch (\Exception $exception) {}

        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setRefererUrl();

        return $resultRedirect;
    }

    /**
     * Is sending verification emails limit is not reached.
     *
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     *
     * @return bool
     * @throws \Exception
     */
    public function isSendingEmailsAllowed($customer)
    {
        $durationAttempts = $this->systemConfig->getNextAttemptsDurationValue();

        // made attempts to send email
        $attemptsDone = $customer->getCustomAttribute('email_attempts_limit');
        $attemptsDone = ($attemptsDone) ? (int) $attemptsDone->getValue() : 0;

        if ($attemptsDone <= $this->systemConfig->getEmailAttemptsLimit()) {
            // increase counter of attempts
            $customer->setCustomAttribute('email_attempts_limit', ++$attemptsDone);

            return true;
        }

        // date when the last email has been sent
        $createdAt = $customer->getCustomAttribute('email_verification_created_at');
        $createdAt = ($createdAt) ? $createdAt->getValue() : 'now';
        $createdAt = (new \DateTime($createdAt));

        $currentDate = (new \DateTime('now'));

        // check how long the last email has been sent
        if ((int) $createdAt->diff($currentDate)->format('%y%m%d%h%i') > $durationAttempts) {
            $customer->setCustomAttribute('email_attempts_limit', 0);

            return true;
        }

        return  false;
    }

    /**
     * Trigger sending verification email.
     *
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     *
     * @return void
     */
    protected function _triggerVerificationEmail($customer)
    {
        $this->_eventManager->dispatch(
            'totaltools_send_verification_email',
            ['customer' => $customer]
        );
    }
}