<?php
/**
 * <AUTHOR> Internet
 * @package Totaltools_Loyalty
 */

namespace Totaltools\Loyalty\Controller\Insider;

/**
 * List instore order
 */
class View extends \Magento\Framework\App\Action\Action implements \Magento\Framework\App\ActionInterface
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $pageFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
       * @var Totaltools\Loyalty\Block\Insider\CheckOrder
       */
    protected $checkOrder;

    /**
      * @var  \Magento\Framework\Message\ManagerInterface
      */
    protected $messageManager;

    /**
     * @param \Magento\Framework\App\Action\Context      $context
     * @param \Magento\Framework\View\Result\PageFactory $pageFactory
     * @param  \Totaltools\Loyalty\Block\Insider\CheckOrder  $checkOrder
     *  @param \Magento\Framework\Message\ManagerInterface $messageManager
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $pageFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Totaltools\Loyalty\Block\Insider\CheckOrder $checkOrder,
        \Magento\Framework\Message\ManagerInterface $messageManager
    ) {
        $this->pageFactory = $pageFactory;
        $this->customerSession = $customerSession;
        $this->checkOrder = $checkOrder;
        $this->messageManager = $messageManager;
        return parent::__construct($context);
    }


    /**
     * Check customer authentication
     *
     * @param  \Magento\Framework\App\RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function dispatch(\Magento\Framework\App\RequestInterface $request)
    {
        if (!$this->customerSession->authenticate()) {
            $this->_actionFlag->set('', 'no-dispatch', true);
        }
        return parent::dispatch($request);
    }
    /**
     * Dispatch request
     *
     * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function execute()
    {
        //get current customer id
        $currentCustomerId =$this->customerSession->getCustomerId();
        //check pronto order id
        $prontoOrderId = $this->getRequest()->getParam('id');
        //get order detials from table by customer id and pronto order id
        $isCurrentCustomerOrder = $this->checkOrder->getOrderData($prontoOrderId, $currentCustomerId);
        //is order exisit
        $isOrderIdExist = $this->checkOrder->getIsOrderExist($prontoOrderId);

        if ($this->getRequest()->getParam('id') && $isCurrentCustomerOrder == true) {
            /** @var \Magento\Framework\View\Result\Page $resultPage */
            $resultPage = $this->pageFactory->create();
            $resultPage->getConfig()->getTitle()->set(__('Total Tools View Order'));
            return $resultPage;
        } elseif(!$isOrderIdExist) {
            $this->messageManager->addErrorMessage('The order does not exist');
            $this->_redirect('loyalty/insider/invoices/');
        } else {
            $this->messageManager->addErrorMessage('You do not have permission to view this page');
            $this->_redirect('loyalty/insider/invoices/');
            return;
        }
    }

}
