<?php
/**
 * <AUTHOR> Internet
 * @package Totaltools_Loyalty
 */

namespace Totaltools\Loyalty\Controller\Insider;

use Totaltools\Loyalty\Model\Invoices\Pdf\FileWriter;

/**
 * Download list of instore order
 */
class Download extends \Magento\Framework\App\Action\Action implements \Magento\Framework\App\ActionInterface
{
    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @var \Totaltools\Loyalty\Model\Invoices\Pdf\FileWriter
     */
    protected $fileWriter;

    /**
     * @var \Magento\Framework\Stdlib\CookieManagerInterface
     */
    protected $cookieManager;

    /**
     * @var \Magento\Framework\App\Response\Http\FileFactory
     */
    protected $fileFactory;

    /**
     * Download constructor.
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Totaltools\Loyalty\Model\CustomerSession $customerSession
     * @param \Totaltools\Loyalty\Model\Invoices\Pdf\FileWriter $fileWriter
     * @param \Magento\Framework\Stdlib\CookieManagerInterface $cookieManager
     * @param \Magento\Framework\App\Response\Http\FileFactory $fileFactory
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Totaltools\Loyalty\Model\Invoices\Pdf\FileWriter $fileWriter,
        \Magento\Framework\Stdlib\CookieManagerInterface $cookieManager,
        \Magento\Framework\App\Response\Http\FileFactory $fileFactory
    ) {
        $this->customerSession = $customerSession;
        $this->fileWriter = $fileWriter;
        $this->cookieManager = $cookieManager;
        $this->fileFactory = $fileFactory;
        return parent::__construct($context);

    }


    /**
     * Check customer authentication
     *
     * @param \Magento\Framework\App\RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function dispatch(\Magento\Framework\App\RequestInterface $request)
    {
        if (!$this->customerSession->authenticate()) {
            $this->_actionFlag->set('', 'no-dispatch', true);
        }

        return parent::dispatch($request);
    }


    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface|void
     */
    public function execute()
    {
        try {
            $invoiceIds = $this->getRequest()->getParam('id');

            if (empty($invoiceIds)) {
                $this->messageManager->addErrorMessage(__('Please, select invoices'));

                $this->_redirect('*/*/invoices');
            } else {
                $invoiceIds = explode(',', $invoiceIds);
                $this->fileWriter->createFiles($invoiceIds);
               return $this->downloadArchive();
            }
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage(__($e->getMessage()));
        }
    }

    /**
     * @throws \Exception
     */
    protected function downloadArchive()
    {
        $customerId = $this->customerSession->getCustomerId();
        return $this->fileFactory->create(
            FileWriter::INVOICES_ARCHIVE_NAME,
            [
                'type' => 'filename',
                'value' => '/' . FileWriter::INVOICES_FOLDER_NAME . '/' . $customerId .  '.zip',
                'rm' => true
            ],
            \Magento\Framework\App\Filesystem\DirectoryList::VAR_DIR,
            'application/zip'
        );
    }
}
