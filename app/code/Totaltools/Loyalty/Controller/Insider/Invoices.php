<?php
/**
 * Created by PhpStorm.
 * User: andrey
 * Date: 22.02.19
 * Time: 16:49
 */

namespace Totaltools\Loyalty\Controller\Insider;


class Invoices extends \Magento\Framework\App\Action\Action implements \Magento\Framework\App\ActionInterface
{
    /**
     * @var \Magento\Framework\View\Result\PageFactory
     */
    protected $pageFactory;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;


    /**
     * @param \Magento\Framework\App\Action\Context      $context
     * @param \Magento\Framework\View\Result\PageFactory $pageFactory
     * @param \Totaltools\Loyalty\Model\CustomerSession            $customerSession
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $pageFactory,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession
    ) {
        $this->pageFactory = $pageFactory;
        $this->customerSession = $customerSession;
        return parent::__construct($context);
    }


    /**
     * Check customer authentication
     *
     * @param  \Magento\Framework\App\RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function dispatch(\Magento\Framework\App\RequestInterface $request)
    {
        if (!$this->customerSession->authenticate()) {
            $this->_actionFlag->set('', 'no-dispatch', true);
        }
        return parent::dispatch($request);
    }


    /**
     * Dispatch request
     *
     * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function execute()
    {
        /** @var \Magento\Framework\View\Result\Page $resultPage */
        $resultPage = $this->pageFactory->create();
        $resultPage->getConfig()->getTitle()->set(__('Invoices'));
        return $resultPage;
    }
}