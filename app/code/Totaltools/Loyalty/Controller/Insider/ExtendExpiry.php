<?php
/**
 * Total-Info Tech
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
namespace Totaltools\Loyalty\Controller\Insider;

use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\UrlFactory;

class ExtendExpiry extends \Magento\Framework\App\Action\Action implements \Magento\Framework\App\ActionInterface
{

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * Pronto customer
     * @var \Totaltools\Pronto\Api\CustomerRequestManagerInterface
     */
    private $prontoRequestManager;

     /**
     * Customer factory
     * @var \Magento\Customer\Model\CustomerFactory
     */
    private $customerFactory;
    /**
     * @var TimezoneInterface
     */
    protected $_timezone;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;


    /**
     * @param \Magento\Framework\App\Action\Context      $context
     * @param \Totaltools\Loyalty\Model\CustomerSession            $customerSession
     * @param \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory,
     * @param UrlFactory         $urlFactory         UrlFactory
     * @param TimezoneInterface $localeDate
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager,
        TimezoneInterface $localeDate,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        UrlFactory $urlFactory
    ) {
        $this->customerSession = $customerSession;
        $this->prontoRequestManager = $prontoRequestManager;
        $this->customerFactory = $customerFactory;
        $this->_timezone = $localeDate;
        $this->urlModel = $urlFactory->create();
        return parent::__construct($context);
    }


    /**
     * Check customer authentication
     *
     * @param  \Magento\Framework\App\RequestInterface $request
     * @return \Magento\Framework\App\ResponseInterface
     */
    public function dispatch(\Magento\Framework\App\RequestInterface $request)
    {
        if (!$this->customerSession->authenticate()) {
            $this->_actionFlag->set('', 'no-dispatch', true);
        }
        return parent::dispatch($request);
    }


    /**
     * Dispatch request
     *
     * @return \Magento\Framework\Controller\ResultInterface|ResponseInterface
     * @throws \Magento\Framework\Exception\NotFoundException
     */
    public function execute()
    {
        $transactionId =$this->_request->getParam('transaction');
        $loyaltyId = $this->customerSession->getCustomer()->getLoyaltyId();
        $loyaltyPoints = $this->prontoRequestManager->getMemberPoints($this->customerSession->getCustomer());
        $today = $this->_timezone->date()->format('d-m-Y');
        
        $filteredPoints = current(array_filter($loyaltyPoints, function($loyaltyPoint) use($transactionId) {
            if(is_array($loyaltyPoint['SeqNo'])) {
                $loyaltyPoint['SeqNo'] = $loyaltyPoint['SeqNo'][0];
            }
            return $loyaltyPoint['SeqNo'] == $transactionId;
        }));
        
        $expiry = $this->_timezone
        ->date(new \DateTime($filteredPoints['ExpiryDate']))
        ->format('d-m-Y'); 
        $extendExpiry = ['AR','TR','BR'];
        
        if(
            $loyaltyId == $filteredPoints['LoyaltyID'] 
            && strtotime($today) < strtotime($expiry)
            && in_array($filteredPoints['TransType'], $extendExpiry)
            ) {
           $result = $this->prontoRequestManager->extendExpiryDate($loyaltyId, $transactionId);
        }
        if (isset($result['Response'])) {
            $this->messageManager->addSuccessMessage($result['Response']);
        } 
        $defaultUrl = $this->urlModel->getUrl('loyalty/insider/info', ['_secure' => true]);
        /**
         * Redirect
         *
         * @var Redirect $resultRedirect
        */
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
        return $resultRedirect;
    }
}