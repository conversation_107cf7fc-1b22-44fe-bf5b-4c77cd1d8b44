<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Loyalty\Controller\Account;

use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Exception\LocalizedException;

/**
 * Class Verification
 * @package Totaltools\Loyalty\Controller\Account
 */
class Verification extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Totaltools\Loyalty\Model\Verification
     */
    protected $verifier;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @var \Magento\Framework\Stdlib\Cookie\PhpCookieManager
     */
    protected $cookieMetadataManager;

    /**
     * @var \Magento\Framework\Stdlib\Cookie\CookieMetadataFactory
     */
    protected $cookieMetadataFactory;

    /**
     * Verification constructor.
     *
     * @param Context $context
     * @param \Totaltools\Loyalty\Model\Verification $verifier
     * @param \Totaltools\Loyalty\Model\CustomerSession $customerSession
     * @param \Magento\Framework\Stdlib\Cookie\PhpCookieManager $cookieMetadataManager
     * @param \Magento\Framework\Stdlib\Cookie\CookieMetadataFactory $cookieMetadataFactory
     */
    public function __construct(
        Context $context,
        \Totaltools\Loyalty\Model\Verification $verifier,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Magento\Framework\Stdlib\Cookie\PhpCookieManager $cookieMetadataManager,
        \Magento\Framework\Stdlib\Cookie\CookieMetadataFactory $cookieMetadataFactory
    )
    {
        parent::__construct($context);
        $this->verifier = $verifier;
        $this->customerSession = $customerSession;
        $this->cookieMetadataManager = $cookieMetadataManager;
        $this->cookieMetadataFactory = $cookieMetadataFactory;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Redirect|\Magento\Framework\Controller\ResultInterface
     * @throws LocalizedException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Stdlib\Cookie\FailureToSendException
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);

        try {
            $customerId = $this->getRequest()->getParam('id', false);
            $hash = $this->getRequest()->getParam('hash', false);

            if (empty($customerId) || empty($hash)) {
                throw new LocalizedException(__('An unspecified error occurred. Please contact us for assistance.'));
            }

            $customer = $this->verifier->verifyCustomer($customerId, $hash);
            $this->customerSession->setCustomerDataAsLoggedIn($customer);
            $this->_refreshCookie();
            $this->messageManager->addSuccessMessage(__('Account successfully verified'));
            $resultRedirect->setUrl($this->getSuccessRedirect());

            return $resultRedirect;
        } catch (LocalizedException $e) {
            $this->messageManager->addExceptionMessage(
                $e,
                __('An unspecified error occurred. Please contact us for assistance.')
            );
        }

        $url = $this->_url->getUrl('customer/account', ['_secure' => true]);

        return $resultRedirect->setUrl($this->_redirect->error($url));
    }

    /**
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Stdlib\Cookie\FailureToSendException
     */
    private function _refreshCookie()
    {
        if ($this->cookieMetadataManager->getCookie('mage-cache-sessid')) {
            $metadata = $this->cookieMetadataFactory->createCookieMetadata();
            $metadata->setPath('/');
            $this->cookieMetadataManager->deleteCookie('mage-cache-sessid', $metadata);
        }
    }

    /**
     * @return string
     */
    protected function getSuccessRedirect()
    {
        return $this->_redirect->success($this->_url->getUrl('customer/account', ['_secure' => true]));
    }
}