<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label" sortOrder="0">
            <label>Total Tools</label>
        </tab>
        <section id="general">
            <group id="store_information">
                <field id="abn" translate="label" type="text" sortOrder="70"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="0">
                    <label>ABN</label>
                </field>
            </group>
        </section>
        <section id="totaltools_loyalty" translate="label" sortOrder="40"
                 showInDefault="1"
                 showInWebsite="1"
                 showInStore="0">
            <label>Loyalty</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Loyalty::config_loyalty</resource>
            <group id="confirmation_email" translate="label" type="text" sortOrder="10"
                   showInDefault="1"
                   showInWebsite="1"
                   showInStore="0">
                <label>Confirmation Email</label>
                <field id="attempt_limit" translate="label" type="text" sortOrder="10"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="0">
                    <label>Attempts Limitation</label>
                    <comment>Limit of attempts allowed customer to send verification email.</comment>
                </field>
                <field id="next_attempt_duration" translate="label" type="text" sortOrder="20"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="0">
                    <label>Next Attempt Duration</label>
                    <comment>Duration for next attempts in case if a customer exceeded allowed attempts limit.</comment>
                </field>
                 <field id="show_pending_sync_message" translate="label" type="select" sortOrder="30"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="0">
                    <label>Show message for Pending Sync</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Show custom message to customers whos account is in pending sync state.</comment>
                </field>
                 <field id="pending_sync_msg" translate="label" type="text" sortOrder="40"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="0">
                    <label>Pending Sync message</label>
                    <comment>This text will be displayed to customers with account status as pending sync.</comment>
                </field>
            </group>
            <group id="insider_rewards" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Insider Rewards</label>
                <field id="transactions_recent_invoices" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Transactions show in Recent Invoices</label>
                </field>
                <field id="invoices_in_rewards" translate="label" type="select" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="20" >
                    <label>Show Recent Invoices in Info section</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="barcode_url" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Barcode URL</label>
                </field>
                <field id="show_referral" translate="label comment" type="select" sortOrder="40" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Show Referral Code</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[Set to yes to Show Referral Code]]></comment>
                </field>
            </group>
            <group id="sync" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Sync Settings</label>
                <field id="sync_timeout" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Sync Timeout</label>
                    <comment><![CDATA[Timeout in minutes for Pronto to Magento sync of customer data]]></comment>
                </field>
                <field id="invoices_sync_timeout" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Invoices Sync Timeout</label>
                    <comment><![CDATA[Timeout in minutes for Pronto to Magento sync of customer Invoices data]]></comment>
                </field>
                <field id="rewards_sync_timeout" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Rewards Sync Timeout</label>
                    <comment><![CDATA[Timeout in minutes for Pronto to Magento sync of customer Rewards data]]></comment>
                </field>
            </group>
            <group id="email_selected_invoices" translate="label" type="text" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Invoices Email Settings</label>
                <field id="template" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Template for send invoices in email</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                 <field id="sender" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Invoices Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
