<?xml version="1.0"  ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="controller_action_predispatch_loyalty_insider_invoices">
        <observer name="loyalty_insider_invoices" instance="Totaltools\Loyalty\Observer\InsiderInvoices" />
    </event>
    <event name="controller_action_predispatch_loyalty_insider_info">
        <observer name="loyalty_insider_info_invoices" instance="Totaltools\Loyalty\Observer\InsiderInvoices" />
    </event>
    <event name="view_block_abstract_to_html_before">
        <observer name="remove_widget" instance="Totaltools\Loyalty\Observer\RemoveWidget" />
    </event>
</config>