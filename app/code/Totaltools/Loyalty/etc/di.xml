<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="totaltoolsLoyaltyUpdateCustomerCommand" xsi:type="object">Totaltools\Loyalty\Console\Command\UpdateCustomerCommand</item>
            </argument>
        </arguments>
    </type>

    <preference for="Totaltools\Loyalty\Api\RewardManagementInterface"
                type="Totaltools\Loyalty\Model\Rest\Customer\RewardManagement" />
</config>