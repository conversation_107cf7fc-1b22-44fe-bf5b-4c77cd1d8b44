<?php
/**
 * RemoveWidget
 *
 * @category  Totaltools
 * @package   Totaltools_Loyalty
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
namespace Totaltools\Loyalty\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;


class RemoveWidget implements ObserverInterface
{    
    /**
     * _request
     *
     * @var Magento\Framework\App\Request\Http
     */
    protected $_request;    
    
    /**
     * _scopeConfig
     *
     * @var Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $_scopeConfig;
    
    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        \Magento\Framework\App\Request\Http $request,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
    ) {
        $this->_request = $request;
        $this->_scopeConfig = $scopeConfig;
    }
    
    /**
     * execute
     *
     * @param  Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        /** @var \Magento\Framework\View\Element\Template $block */
        $block = $observer->getBlock();
        if ($block->getType() == 'Magento\Cms\Block\Widget\Block' || $block->getType()=='Magento\Cms\Block\Widget\Block\Interceptor') {
            if ($this->_request->getFullActionName() == 'loyalty_insider_printview') {
                $block->setTemplate(false);
            }
        }
    }
}