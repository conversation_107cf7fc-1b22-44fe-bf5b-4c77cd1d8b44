<?php
namespace Totaltools\Loyalty\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Customer\Model\Session;

/**
 * Class CustomerLogin
 */
class CustomerLogin implements ObserverInterface
{
    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * Constructor
     *
     * @param Session $customerSession
     */
    public function __construct(Session $customerSession)
    {
        $this->customerSession = $customerSession;
    }

    /**
     * Execute observer method.
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        $this->customerSession->setData('show_loyalty_points_popup', true);
    }
}
