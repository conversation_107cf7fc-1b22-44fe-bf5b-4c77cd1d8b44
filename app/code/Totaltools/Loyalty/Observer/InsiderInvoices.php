<?php

namespace Totaltools\Loyalty\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Totaltools\Pronto\Cron\LoyaltyIntegration;
use Magento\Customer\Model\Customer;
use Unirgy\RapidFlow\Exception;
use Totaltools\Customer\Helper\Data;

class InsiderInvoices implements ObserverInterface
{
    protected $customerSession;

    protected $orderHistory;

    protected $messageManager;

    protected $customerFactory;

    protected $loyaltyIntegration;

    protected $customer;

    protected $_customerSession;

    protected $customerHelper;

    /**
     * InsiderInvoices constructor.
     * @param \Magento\Customer\Model\SessionFactory $customerSession
     * @param \Totaltools\Pronto\Model\Request\OrderHistory $orderHistory
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param LoyaltyIntegration $loyaltyIntegration
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     * @param Customer $customer
     * @param Data $customerHelper
     */
    public function __construct(
        \Magento\Customer\Model\SessionFactory $customerSession,
        \Totaltools\Pronto\Model\Request\OrderHistory $orderHistory,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        LoyaltyIntegration $loyaltyIntegration,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        Customer $customer,
        Data $customerHelper
    ) {
        $this->loyaltyIntegration = $loyaltyIntegration;
        $this->_customerSession = $customerSession->create();
        $this->orderHistory = $orderHistory;
        $this->customerFactory = $customerFactory;
        $this->messageManager = $messageManager;
        $this->customer = $customer;
        $this->customerHelper = $customerHelper;
    }

    /**
     * @param Observer $observer
     * @return bool|void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute(Observer $observer)
    {
      $this->syncInsiderInvoices();
        return true;
    }

    public function syncInsiderInvoices()
    {
        try {
            $customer = $this->getCustomerData();
            if ($customer && $customer->getId() && $this->customerHelper->isInvoiceSyncReady() ) {
                $this->customerHelper->setInvoiceSyncTimestamp(time());
                $this->orderHistory->updateCustomerData($customer);
                $now = date('Y-m-d H:i:s');
                $customer->setLoyaltyLastUpdated($now);
                $customer->setData('loyalty_last_updated', $now);
                $customer->getResource()->saveAttribute($customer, 'loyalty_last_updated');
            }
        } catch (Exception $exception) {
            $this->messageManager->addErrorMessage('Failed to load updated invoices');
        }
    }

    /**
     * @return Customer|bool
     */
    public function getCustomerData() {
        if ($this->_customerSession->isLoggedIn()) {
            return $this->_customerSession->getCustomer();
        }
        return false;
    }
}