<?php
/**
 * <AUTHOR> Internet
 * @package Totaltools\Loyalty
 */

namespace Totaltools\Loyalty\Setup;

use Magento\Framework\Exception\LocalizedException;
use Totaltools\Loyalty\Block\Insider\Content as InsiderContentBlock;
use Magento\Eav\Setup\EavSetupFactory;

class UpgradeData implements \Magento\Framework\Setup\UpgradeDataInterface
{
    /**
     * @var \Magento\Cms\Api\BlockRepositoryInterface
     */
    protected $cmsBlockRepository;

    /**
     * @var \Magento\Cms\Api\Data\BlockInterfaceFactory
     */
    protected $cmsBlockDataInterfaceFactory;
    
    /**
     * EAV setup factory
     *
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @param \Magento\Cms\Api\BlockRepositoryInterface   $cmsBlockRepository
     * @param \Magento\Cms\Api\Data\BlockInterfaceFactory $cmsBlockDataInterfaceFactory
     * @param EavSetupFactory $eavSetupFactory
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Cms\Api\BlockRepositoryInterface $cmsBlockRepository,
        \Magento\Cms\Api\Data\BlockInterfaceFactory $cmsBlockDataInterfaceFactory,
        EavSetupFactory $eavSetupFactory,
        \Psr\Log\LoggerInterface $logger
    )
    {
        $this->cmsBlockRepository = $cmsBlockRepository;
        $this->cmsBlockDataInterfaceFactory = $cmsBlockDataInterfaceFactory;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->logger = $logger;
    }

    /**
     * Upgrade module data
     *
     * @param \Magento\Framework\Setup\ModuleDataSetupInterface $setup
     * @param \Magento\Framework\Setup\ModuleContextInterface   $context
     *
     * @return void
     */
    public function upgrade(
        \Magento\Framework\Setup\ModuleDataSetupInterface $setup,
        \Magento\Framework\Setup\ModuleContextInterface $context
    )
    {
        $version = $context->getVersion();

        if ($version) {
            if (version_compare($version, '0.2.0') < 0) {
                try {
                    $this->createInsiderStaticBlocks();
                } catch (LocalizedException $e) {
                    $this->logger->warning(__($e->getMessage()));
                }
            }

            if (version_compare($version, '1.0.2') < 0) {
                $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

                foreach (['is_loyal', 'loyalty_level'] as $attributeCode) {
                    $eavSetup->updateAttribute(\Magento\Customer\Model\Customer::ENTITY, $attributeCode, 'default_value', '1');
                }
            }

            if (version_compare($version, '1.0.3')) {
                try {
                    $this->createInsiderPopupCmsBlocks();
                } catch (LocalizedException $e) {
                    $this->logger->warning(__($e->getMessage()));
                }
            }
        }
    }

    /**
     * Create static blocks for Account Insider page
     *
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function createInsiderStaticBlocks()
    {
        $block = $this->cmsBlockDataInterfaceFactory->create()
            ->setIdentifier(InsiderContentBlock::STATIC_BLOCK_ID_NONE)
            ->setTitle('Account - Insider (non-insider)')
            ->setContent('<p>non-insider - content here</p>')
            ->setIsActive(true);
        $this->cmsBlockRepository->save($block);

        $block = $this->cmsBlockDataInterfaceFactory->create()
            ->setIdentifier(InsiderContentBlock::STATIC_BLOCK_ID_INSIDER)
            ->setTitle('Account - Insider (Insider)')
            ->setContent('<p>Insider - content here</p>')
            ->setIsActive(true);
        $this->cmsBlockRepository->save($block);

        $block = $this->cmsBlockDataInterfaceFactory->create()
            ->setIdentifier(InsiderContentBlock::STATIC_BLOCK_ID_PLUS)
            ->setTitle('Account - Insider (Insider+)')
            ->setContent('<p>Insider+ - content here</p>')
            ->setIsActive(true);
        $this->cmsBlockRepository->save($block);

        $block = $this->cmsBlockDataInterfaceFactory->create()
            ->setIdentifier(InsiderContentBlock::STATIC_BLOCK_ID_MAX)
            ->setTitle('Account - Insider (Insider Max)')
            ->setContent('<p>Insider Max - content here</p>')
            ->setIsActive(true);
        $this->cmsBlockRepository->save($block);
    }

    /**
     * Add cms blocks for popups inside customer account's insider reward page
     * 
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function createInsiderPopupCmsBlocks()
    {
        $memberBenefitsBlock = $this->cmsBlockDataInterfaceFactory->create()
            ->setIdentifier('insider_rewards_member_benefits')
            ->setTitle('Insider Rewards - Member Benefits')
            ->setContent($this->memberBenefitsContent())
            ->setIsActive(true);
        $this->cmsBlockRepository->save($memberBenefitsBlock);

        $insiderPointsBlock = $this->cmsBlockDataInterfaceFactory->create()
            ->setIdentifier('insider_rewards_insider_points')
            ->setTitle('Insider Rewards - Insider Points')
            ->setContent($this->insiderPointsContent())
            ->setIsActive(true);
        $this->cmsBlockRepository->save($insiderPointsBlock);
    }

    /**
     * @return string
     */
    private function memberBenefitsContent()
    {
        return '
        <h2 style="text-align: center;">MEMBER PERKS</h2>
        <div style="text-align: center;">
        <p><img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="member tiers" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_05a_MemberPerks.jpg"}}" alt="member tiers" width="580" height="650" /> <img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="bronze" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_05b_MemberBronze.jpg"}}" alt="bronze" width="580" height="650" /></p>
        <p><img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="silver" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_05c_MemberSilver.jpg"}}" alt="silver" width="580" height="790" /> <img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="gold" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_05c_MemberGold.jpg"}}" alt="gold" width="580" height="790" /></p>
        <p style="text-align: center;">*Annually refers to the Insider Rewards membership year, July 1 – June 30 spend within Total Tools online or instore. ^Excludes Milwaukee MX, Festool, Fein and Gift Cards. By participating in the Total Tools Insider Rewards Program you have agreed to the program Terms and Conditions which can be found <a href="https://www.totaltools.com.au/media/product-attachments/TT-Insider-Program-terms-and-conditions-2019.pdf" target="_blank">here.</a></p>
        <p><img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="want to know more" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_06a_WantToKnowMore.jpg"}}" alt="want to know more about insider rewards" width="593" height="100" /><a href="https://www.totaltools.com.au/insider-rewards/faqs"><img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="faqs" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_06b_FAQs.jpg"}}" alt="faqs" width="593" height="100" /></a></p>
        </div>';
    }

    /**
     * @return string
     */
    private function insiderPointsContent()
    {
        return '<h2 style="text-align: center;">HOW IT WORKS</h2>
        <p style="text-align: center;"><img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="Get points" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_04a_GetPoints.jpg"}}" alt="Get points" width="288" height="514" /> <img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="Get tools" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_04b_GetTools.jpg"}}" alt="Get tools" width="288" height="514" /> <img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="get rewards" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_04c_GetDeals.jpg"}}" alt="get rewards" width="288" height="515" /> <img style="margin: 0px 2px 0px 2px; padding: 0px 2px 0px 2px; display: inline-block;" title="get receipts" src="{{media url="wysiwyg/Insider_pages/Insider_rewards_lp/InsiderLandingPage_04d_GetReciepts.jpg"}}" alt="get receipts" width="288" height="514" /></p>
        <p style="text-align: center;">*Excludes Milwaukee MX, Festool and Fein branded tools and Total Tools gift cards.</p>';
    }
}
