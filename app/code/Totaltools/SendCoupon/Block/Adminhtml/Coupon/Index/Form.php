<?php



namespace Totaltools\SendCoupon\Block\Adminhtml\Coupon\Index;

use Magento\Backend\Block\Widget\Form\Generic;

/**
 * Class Tab GeneralTab
 */
class Form extends Generic
{
    /**
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function _prepareForm()
    {

        /** @var \Magento\Framework\Data\Form $form */
        $form = $this->_formFactory->create(
            [
                'data' => [
                    'id'      => 'edit_form',
                    'action'  => $this->getUrl('*/*/sendProcess'),
                    'method'  => 'post',
                    'enctype' => 'multipart/form-data',
                ],
            ]
        );

        $fieldset = $form->addFieldset('general_fieldset', ['legend' => __('Send Coupon Codes')]);

        $fieldset->addField(
            'filecsv',
            'file',
            [
                'title'    => __('Upload CSV File'),
                'label'    => __('Upload CSV File'),
                'name'     => 'filecsv',
                'required' => true,
                'note'     => 'Only csv file is supported. Click <a target="_blank" href="'
                    . $this->getUrl('couponcodes/coupon/sampleFile')
                    . '">here</a> to download the Sample CSV file',
            ]
        );

        $form->setUseContainer(true);
        $this->setForm($form);

        return parent::_prepareForm();
    }
}
