<?xml version="1.0" encoding="UTF-8"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="sendcoupon" translate="label" type="text" sortOrder="600" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Coupon Codes</label>
            <tab>totaltools</tab>
            <resource>Totaltools_SendCoupon::config_sendcoupon</resource>
            <group id="email" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Email Settings</label>
                <field id="sendcoupon_email_template" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Coupon Code Email Template</label>
                    <source_model>Totaltools\Storelocator\Model\Config\Source\Email\Template</source_model>
                </field>
                 <field id="sendcoupon_email_sender" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Send Coupon Code Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                </field>
            </group>
        </section>
    </system>
</config>