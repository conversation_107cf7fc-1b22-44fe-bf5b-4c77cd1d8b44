<?php

namespace Totaltools\SendCoupon\Controller\Adminhtml\Coupon;

use Magento\Framework\App\Filesystem\DirectoryList;


class SampleFile extends \Magento\Backend\App\Action
{
    /**
     * Execute action
     */
    public function execute()
    {
        $fileName = 'couponcodes.csv';

        /** @var \Magento\Framework\App\Response\Http\FileFactory $fileFactory */
        $fileFactory = $this->_objectManager->get('Magento\Framework\App\Response\Http\FileFactory');

        return $fileFactory->create(
            $fileName,
            $this->getCouponCodesSampleData(),
            DirectoryList::VAR_DIR
        );
    }

    public function getCouponCodesSampleData()
    {
        /** @var \Magento\Framework\Module\Dir $moduleReader */
        $moduleReader = $this->_objectManager->get('Magento\Framework\Module\Dir');
        /** @var \Magento\Framework\Filesystem\DriverPool $drivePool */
        $drivePool = $this->_objectManager->get('Magento\Framework\Filesystem\DriverPool');
        $drive = $drivePool->getDriver(\Magento\Framework\Filesystem\DriverPool::FILE);

        return $drive->fileGetContents($moduleReader->getDir('Totaltools_SendCoupon')
            . DIRECTORY_SEPARATOR . '_fixtures' . DIRECTORY_SEPARATOR . 'couponcodes.csv');
    }
}
