<?php

namespace Totaltools\SendCoupon\Controller\Adminhtml\Coupon;

use Psr\Log\LoggerInterface;

class SendProcess extends \Magento\Backend\App\Action
{
    /**
     * @var \Totaltools\SendCoupon\Model\CustomerEmail
     */
    private $customerEmail;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Store email constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Totaltools\SendCoupon\Model\CustomerEmail $customerEmail
     * @param LoggerInterface $logger
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Totaltools\SendCoupon\Model\CustomerEmail $customerEmail,
        LoggerInterface $logger
    ) {
        parent::__construct($context);
        $this->customerEmail = $customerEmail;
        $this->logger = $logger;
    }
	
    public function execute()
    {
        $resultRedirect = $this->resultRedirectFactory->create();

        if (isset($_FILES['filecsv'])) {
            if (substr($_FILES['filecsv']["name"], -4)!='.csv') {
                $this->messageManager->addError(__('Please choose a CSV file'));
                return $resultRedirect->setPath('*/*/index');
            }
			
			$fileName = $_FILES['filecsv']['tmp_name'];
			$csvObject = $this->_objectManager->create('Magento\Framework\File\Csv');
			$data = $csvObject->getData($fileName);
			array_shift($data);
			$success = [];
			$errors = [];
			foreach ($data as $customer)
			{
				$customerData = [
					'order_number'      => $customer[0],
					'customer_name'     => $customer[1],
					'customer_email'    => $customer[2],
					'coupon_amount'     => $customer[3],
					'coupon_code'       => $customer[4]
				];
				
				if( filter_var($customerData['customer_email'], FILTER_VALIDATE_EMAIL) )
				{
					$this->customerEmail->sendMail($customerData);
					$success[] = $customerData['customer_email']; 
				} else {
					$errors[] = $customerData['customer_email']; 
				}
			}
			if($success)
			{
				$successString = implode("<br>",$success);
				$successString = "Email sent to: <br>" . $successString;
				$this->messageManager->addSuccess(__($successString));
			}

			if($errors)
			{
				$errorsString = implode("<br>",$errors);
				$errorsString = "Email failed to: <br>" . $errorsString;
				$this->messageManager->addError(__($errorsString));
			}
			
			return $resultRedirect->setPath('*/*/index');
        }
    }
}
