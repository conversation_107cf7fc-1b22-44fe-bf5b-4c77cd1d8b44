<?php

namespace Totaltools\SendCoupon\Helper;


class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    
    /**
     * Scope Config Interface.
     *
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $_scopeConfig;

    /**
     * @return mixed
     */
    public function getCouponCodeTemplate()
    {
        return $this->scopeConfig->getValue('sendcoupon/email/sendcoupon_email_template');
    }

    /**
     * @return mixed
     */
    public function getCouponCodeSender()
    {
        return $this->scopeConfig->getValue('sendcoupon/email/sendcoupon_email_sender');
    }

}
