<?php

namespace Totaltools\SendCoupon\Model;

use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Area;
use Magento\Framework\Mail\Template\TransportBuilder;
use Psr\Log\LoggerInterface;
use Totaltools\SendCoupon\Helper\Data as DataHelper;

/**
 * Class CustomerEmail
 * @package Totaltools\SendCoupon\Model
 */
class CustomerEmail
{
    /**
     * @var \Totaltools\SendCoupon\Helper\Data
     */
    private $dataHelper;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var Context
     */
    private $context;

    /**
     * CustomerEmail constructor.
     * @param LoggerInterface $logger
     * @param \Totaltools\SendCoupon\Helper\Data $dataHelper
     * @param TransportBuilder $transportBuilder
     * @param Context $context
     */
    public function __construct(
        LoggerInterface $logger,
        DataHelper $dataHelper,
        TransportBuilder $transportBuilder,
        Context $context
    ) {
        $this->dataHelper = $dataHelper;
        $this->transportBuilder = $transportBuilder;
        $this->logger = $logger;
        $this->context = $context;
    }

    

    /**
     * @param array $customer
     * @return bool
     * @throws \Exception
     */
    public function sendMail($customer)
    {
        $sender = $this->dataHelper->getCouponCodeSender();
        $template = $this->dataHelper->getCouponCodeTemplate();
        $sendTo = [
            [
                'email' => $customer['customer_email'],
                'name' => $customer['customer_name'],
            ]
        ];
        
       
        foreach ($sendTo as $recipient) {

            $templateVars = [
                'customer_email'    => $customer['customer_email'],
                'customer_name'     => $customer['customer_name'],
                'order_number'      => $customer['order_number'],
                'coupon_code'       => $customer['coupon_code'],
                'coupon_amount'     => $customer['coupon_amount'],
            ];

            try {
                $mailer = $this->transportBuilder
                    ->setTemplateIdentifier($template)
                    ->setTemplateOptions([
                        'area' => Area::AREA_FRONTEND,
                        'store' => 1,
                    ])
                    ->setTemplateVars($templateVars)
                    ->setFrom($sender)
                    ->addTo($recipient['email'], $recipient['name']);

                $transport = $mailer->getTransport();
                $transport->sendMessage();

            } catch (\Exception $e) {
                $this->logger->critical($e->getMessage());
                $this->logger->critical($e->getTraceAsString());
            }
        }
    }

    
}
