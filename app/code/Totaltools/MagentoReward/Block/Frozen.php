<?php
/**
 * Totaltools Magento Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\MagentoReward\Block;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

/**
 * Class Frozen
 * @package Totaltools\MagentoReward\Block
 */
class Frozen extends Template
{
    const FROZEN_POPUP_MESSAGE = 'totaltools_reward/general/frozen_popup_message';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * Notice constructor.
     * @param Context $context
     * @param ScopeConfigInterface $scopeConfig
     * @param array $data
     */
    public function __construct(
        Context $context,
        ScopeConfigInterface $scopeConfig,
        array $data = []
    ) {
        $this->scopeConfig = $scopeConfig;
        parent::__construct($context, $data);
    }

    /**
     * @return mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getContentMessage()
    {
        return $this->scopeConfig->getValue(self::FROZEN_POPUP_MESSAGE);
    }
}
