<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_MagentoReward
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\MagentoReward\Block\Checkout\Cart;

use Magento\Framework\View\Element\Template;
use Magento\Reward\Helper\Data as RewardHelper;
use Magento\Reward\Model\Reward;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Checkout\Model\Session as CheckoutSession;

/**
 * Class Points
 * @package Totaltools\MagentoReward\Block
 */
class Points extends \Magento\Checkout\Block\Cart\AbstractCart
{
    /**
     * @var RewardHelper
     */
    protected $rewardHelper;

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var \Magento\Reward\Model\RewardFactory
     */
    protected $rewardFactory;

    /**
     * @var Reward
     */
    protected $reward;

    /**
     * Points constructor.
     *
     * @param Template\Context                    $context
     * @param RewardHelper                        $rewardHelper
     * @param CustomerSession                     $customerSession
     * @param CheckoutSession                     $checkoutSession
     * @param \Magento\Reward\Model\RewardFactory $rewardFactory
     * @param array                               $data
     */
    public function __construct(
        Template\Context $context,
        RewardHelper $rewardHelper,
        CustomerSession $customerSession,
        CheckoutSession $checkoutSession,
        \Magento\Reward\Model\RewardFactory $rewardFactory,
        array $data = []
    ) {
        parent::__construct($context, $customerSession, $checkoutSession, $data);
        $this->rewardHelper = $rewardHelper;
        $this->customerSession = $customerSession;
        $this->rewardFactory = $rewardFactory;
    }

    /**
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function isAvailable()
    {
        if (!$this->rewardHelper->getHasRates()
            || !$this->rewardHelper->isEnabledOnFront()) {
            return false;
        }

        $customer = $this->customerSession->getCustomer();

        if ($customer && !empty($customer->getData('unverified_loyalty_id'))) {
            return false;
        }

        $minPointsToUse = $this->rewardHelper->getGeneralConfig(
            'min_points_balance',
            (int)$this->_storeManager->getWebsite()->getId()
        );

        return (float)$this->getRewardModel()->getCurrencyAmount() > 0
            && $this->getRewardModel()->getPointsBalance() >= $minPointsToUse;
    }

    /**
     * @return Reward
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    protected function getRewardModel()
    {
        if (!$this->reward) {
            $this->reward = $this->rewardFactory->create()
                ->setCustomerId($this->customerSession->getCustomerId())
                ->setWebsiteId($this->_storeManager->getStore()->getWebsiteId())
                ->loadByCustomer();
        }

        return $this->reward;
    }

    /**
     * Retrieve reward label
     *
     * @return \Magento\Framework\Phrase
     */
    public function getRewardLabel()
    {
        $format = '%s.00';
        $points = sprintf($format, $this->getRewardModel()->getPointsBalance());
        if (null !== $this->getRewardModel()->getCurrencyAmount() && $this->rewardHelper->getHasRates()) {
            $amount = sprintf(
                $format,
                $this->rewardHelper->formatAmount($this->getRewardModel()->getCurrencyAmount(), true, null)
            );
            return __('%1 store reward points available (%2)', $points, $amount);
        }
        return __('%1 store reward points available', $points);
    }
}