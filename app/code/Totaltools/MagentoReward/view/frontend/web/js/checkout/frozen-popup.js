/**
 * Totaltools Magento Reward.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

define(
    [
        'jquery',
        'mage/translate',
        'underscore',
        'Magento_Ui/js/modal/modal',
        'jquery-ui-modules/widget'
    ],
    function (
        $, $t, _, modal) {
        'use strict';
        $.widget('mage.frozenPopup', {

            /** @inheritdoc */
            _create: function () {
                this.showPopup();
            },

            showPopup: function () {
                var options = {
                    type: 'popup',
                    responsive: false,
                    innerScroll: true,
                    title: '',
                    modalClass: 'frozen-modal',
                    buttons: [{
                        click: function () {
                            this.closeModal();
                        }
                    }],
                    opened: function() {
                        $('.modal-footer button').remove();
                    }
                };
                var popupId = 'frozen-message';
                modal(options, $('#'+popupId));
                $('#'+popupId).modal('openModal');
            },
        });

        return $.mage.frozenPopup;
    }
);
