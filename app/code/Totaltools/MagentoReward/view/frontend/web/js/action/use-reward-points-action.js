/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

/**
 * @api
 */
define([
    'jquery',
    'Magento_Checkout/js/model/url-builder',
    'mage/storage',
    'Magento_Checkout/js/model/error-processor',
    'Magento_Ui/js/model/messageList',
    'mage/translate',
    'Magento_Checkout/js/model/full-screen-loader',
    'Magento_Checkout/js/action/get-payment-information',
    'Magento_Checkout/js/model/totals',
    'mage/template',
    'Magento_Checkout/js/model/cart/totals-processor/default',
    'Magento_Checkout/js/model/cart/cache',
    'Totaltools_MagentoReward/js/checkout/frozen-popup'
], function (
    $,
    urlBuilder,
    storage,
    errorProcessor,
    messageList,
    $t,
    fullScreenLoader,
    getPaymentInformationAction,
    totals,
    mageTemplate,
    defaultTotal,
    cartCache,
    frozenPopup
) {
    'use strict';

    return function (data) {
        if (!$.validator.validateSingleElement('#reward-points-amount')) {
            return;
        }
        var successMessage = $t('Insider Dollars successfully applied.');
        var falseMessage = $t('Some of the products in your cart are not eligible for points redemption.');
        var dataTemplate = {
                type: 'success',
                message: successMessage
            };
        var messageTemplate =
            '<div role="alert" class="messages">' +
            '<div class="message-<%- dataTemplate.type %> <%- dataTemplate.type %> message">' +
            '<div><%- dataTemplate.message %></div>' +
            '</div>' +
            '</div>';

        messageList.clear();
        fullScreenLoader.startLoader();
        totals.isLoading(true);

        storage.post(
            urlBuilder.createUrl('/reward/mine/use-reward', {}),
            data
        ).done(function (response) {
            if ($.isArray(response) && response[0] == 'FROZEN') {
                frozenPopup();
            } else {
                var deferred,
                    message,
                    messageContainer = $('.checkout-cart-index #maincontent');

                if (response) {
                    deferred = $.Deferred();
                    getPaymentInformationAction(deferred);
                }
                $.when(deferred).done(function () {
                    totals.isLoading(false);
                    fullScreenLoader.stopLoader();
                });
                if (response === true) {
                    messageList.addSuccessMessage({message: successMessage});
                }
                if (response === false) {
                    dataTemplate = {
                        type: 'error',
                        message: falseMessage
                    };
                    messageList.addErrorMessage({message: falseMessage});
                }
                if (typeof response === 'string') {
                    dataTemplate = {
                        type: 'error',
                        message: response
                    };
                    messageList.addErrorMessage({message: response});
                }
                if (messageContainer.length > 0) {
                    $('.messages').remove();
                    message = mageTemplate(messageTemplate, {
                        dataTemplate: dataTemplate
                    });
                    messageContainer.prepend(message);
                }
            }
        }).fail(function (response) {
            totals.isLoading(false);
            fullScreenLoader.stopLoader();
            errorProcessor.process(response);
        });
    };
});
