define([
    'jquery',
    'Totaltools_MagentoReward/js/action/use-reward-points-action'
], function($, useRewardPointsAction) {

    $.widget('Totaltools.rewardPoints', {
        options: {
            applyButton: "",
            useMaximumPoints: "",
            pointsAmount: ""
        },

        /**
         * Create action.
         *
         * @returns void
         */
        _create: function () {
            this._initApplyPointsHandler();
        },

        _getParams: function() {
            var self = this;

            return JSON.stringify({
                    use_maximum_points: $(self.options.useMaximumPoints).is(':checked'),
                    reward_points_amount: $(self.options.pointsAmount).val()
                }
            );
        },

        /**
         * Init apply points handler.
         *
         * @returns void
         */
        _initApplyPointsHandler: function() {
            var self = this;

            $(self.options.applyButton).off('click').on('click', function (e) {
                e.preventDefault();
                useRewardPointsAction(self._getParams());
            });

            var handleRewardsChange = function(ev) {
                let applyBtn = $(self.options.applyButton);
                let pointsAmount = $(self.options.pointsAmount);
                let useMaxPoints = $(self.options.useMaximumPoints).is(':checked');

                applyBtn.attr('disabled', !Boolean(pointsAmount.val() || useMaxPoints));
            }

            $(self.options.pointsAmount).off('change').on('input', handleRewardsChange);
            $(self.options.useMaximumPoints).off('change').on('change', handleRewardsChange);
        }
    });

    return $.Totaltools.rewardPoints;
});