<?php
namespace Totaltools\Autogeneratemeta\Plugin;


class PageTitlePlugin
{


    /**
    * @var \Magento\Catalog\Model\Product
    */
    protected $product;

    /**
    * @var \Magento\Catalog\Model\Category
    */
    protected $category;


    /**
     * @param \Magento\Framework\Registry $registry
     */
    public function __construct (
        \Magento\Framework\Registry $registry
    ) {
        $this->product  = $registry->registry('product');
        $this->category = $registry->registry('current_category');
    }


    protected function getProductMetaTitle () {
        $name      = $this->product->getName();
        $metaTitle = $this->product->getMetaTitle();
        if ($metaTitle && $metaTitle !== $name) {
            return $metaTitle;
        }
        return sprintf(__('%s'), $name);
    }


    protected function getCategoryMetaTitle () {
        $name      = $this->category->getName();
        $metaTitle = $this->category->getMetaTitle();
        if ($metaTitle && $metaTitle !== $name) {
            return $metaTitle;
        }
        return sprintf(__('%s'), $name);
    }


    protected function getMetaTitle () {
        if ($this->product) {
            return $this->getProductMetaTitle();
        } else if ($this->category) {
            return $this->getCategoryMetaTitle();
        }
    }


    /**
     * @param \Magento\Framework\View\Page\Title $subject
     */
    public function beforeGet (\Magento\Framework\View\Page\Title $subject) {
        $metaTitle = $this->getMetaTitle();
        if ($metaTitle) {
            $subject->set($metaTitle);
        }
    }


}
