<?php
namespace Totaltools\Autogeneratemeta\Plugin;


class PageConfigPlugin
{


    const CATEGORY_TOP_LEVEL = 2;


    /**
    * @var \Magento\Catalog\Model\Product
    */
    protected $product;

    /**
    * @var \Magento\Catalog\Model\Category
    */
    protected $category;


    /**
     * @param \Magento\Framework\Registry $registry
     */
    public function __construct (
        \Magento\Framework\Registry $registry
    ) {
        $this->product  = $registry->registry('product');
        $this->category = $registry->registry('current_category');
    }


    protected function getProductMetaDescription () {
        $name            = $this->product->getName();
        $brand           = $this->product->getAttributeText('brand');
        $description     = $this->product->getDescription();
        $metaDescription = $this->product->getMetaDescription();

        if ($name . ' ' . $description !== $metaDescription && $metaDescription) {
            return $metaDescription;
        }

        if ($brand) {
            $name = sprintf('%s from %s', $name, $brand);
        }
        return sprintf(__('%s — Buy Online for Australia wide delivery or Click and Collect'), $name);
    }


    protected function getCategoryMetaDescription () {
        $name            = $this->category->getName();
        $parent          = $this->category->getParentCategory();
        $metaDescription = $this->category->getMetaDescription();

        if ($metaDescription) {
            return $metaDescription;
        }

        if ($this->category->getLevel() > self::CATEGORY_TOP_LEVEL && $parent) {
            $name = sprintf('%s — %s', $name, $parent->getName());
        }
        return sprintf(__('%s — Buy Online for Australia wide delivery or Click and Collect'), $name);
    }


    protected function getMetaDescription () {
        if ($this->product) {
            return $this->getProductMetaDescription();
        } else if ($this->category) {
            return $this->getCategoryMetaDescription();
        }
    }


    /**
     * @param \Magento\Framework\View\Page\Config $subject
     */
    public function beforeGetDescription (\Magento\Framework\View\Page\Config $subject) {
        $metaDescription = $this->getMetaDescription();
        if ($metaDescription) {
            $subject->setDescription($metaDescription);
        }
    }


}
