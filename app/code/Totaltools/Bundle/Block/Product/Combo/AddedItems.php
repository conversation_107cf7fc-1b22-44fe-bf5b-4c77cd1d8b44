<?php

namespace Totaltools\Bundle\Block\Product\Combo;

use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Collection\AbstractCollection;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\DataObject\IdentityInterface;


/**
 * rewrite block box to filter start and end publishing date
 */
class AddedItems extends \Magento\Catalog\Block\Product\AbstractProduct implements IdentityInterface
{

    /**
     * Product Collection
     *
     * @var AbstractCollection
     */
    protected $_productCollection;

    /**
     * @var \Magento\Framework\Data\Helper\PostHelper
     */
    protected $_postDataHelper;

    /**
     * @var \Magento\Framework\Url\Helper\Data
     */
    protected $urlHelper;

    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $categoryFactory;

    protected $productCollectionFactory;
    protected $catalogProductVisibility;
    protected $catalogConfig;

    /**
     * @param \Magento\Catalog\Block\Product\Context                           $context
     * @param \Magento\Framework\Data\Helper\PostHelper                        $postDataHelper
     * @param \Magento\Catalog\Model\CategoryFactory                           $categoryFactory
     * @param \Magento\Catalog\Model\ResourceModel\Category\Collection\Factory $categoryCollectionFactory
     * @param \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory   $productCollectionFactory
     * @param \Magento\Catalog\Model\Product\Visibility                        $catalogProductVisibility
     * @param \Magento\Framework\Url\Helper\Data                               $urlHelper
     * @param array                                                            $data
     */
    public function __construct(
        \Magento\Catalog\Block\Product\Context $context,
        \Magento\Framework\Data\Helper\PostHelper $postDataHelper,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Magento\Catalog\Model\ResourceModel\Category\Collection\Factory $categoryCollectionFactory,
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Catalog\Model\Product\Visibility $catalogProductVisibility,
        \Magento\Framework\Url\Helper\Data $urlHelper,
        array $data = []
    ) {
        $this->_postDataHelper = $postDataHelper;
        $this->categoryFactory = $categoryFactory;
        $this->categoryCollectionFactory = $categoryCollectionFactory;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->catalogProductVisibility = $catalogProductVisibility;
        $this->catalogConfig = $context->getCatalogConfig();
        $this->urlHelper = $urlHelper;
        parent::__construct(
            $context,
            $data
        );
    }

    /**
     * Retrieve loaded category collection
     *
     * @return AbstractCollection
     */
    protected function _getProductCollection()
    {
        if ($this->_productCollection === null) {
            $category = $this->getCategory();
            if ($category && $category->getId()) {
                $productCollection = $this->productCollectionFactory->create();
                $productCollection->addAttributeToSelect($this->catalogConfig->getProductAttributes())
                    ->addMinimalPrice()
                    ->addFinalPrice()
                    ->addTaxPercents()
                    ->addUrlRewrite($category->getId())
                    ->setVisibility($this->catalogProductVisibility->getVisibleInCatalogIds())
                    ->addFieldToFilter('type_id', \Magento\Catalog\Model\Product\Type::TYPE_SIMPLE)
                    ->addAttributeToFilter('combo_link', $this->getProduct()->getData('combo_link'))
                ;
                $this->_productCollection = $productCollection;
            }
        }

        return $this->_productCollection;
    }

    /**
     * Retrieve category object
     *
     * @return AbstractCollection
     */
    public function getProduct()
    {

        if ($this->getRequest()->getParam('id')) {

        }
        return null;
    }


    /**
     * Retrieve loaded category collection
     *
     * @return AbstractCollection
     */
    public function getLoadedProductCollection()
    {
        return $this->_getProductCollection();
    }

    /**
     * Retrieve current view mode
     *
     * @return string
     */
    public function getMode()
    {
        return \Magento\Catalog\Helper\Product\ProductList::VIEW_MODE_GRID;
    }

    /**
     * Return identifiers for produced content
     *
     * @return array
     */
    public function getIdentities()
    {
        $identities = [];
        foreach ($this->_getProductCollection() as $item) {
            $identities = array_merge($identities, $item->getIdentities());
        }
        $category = $this->getCategory();
        if ($category) {
            $identities[] = Product::CACHE_PRODUCT_CATEGORY_TAG . '_' . $category->getId();
        }
        return $identities;
    }

    /**
     * @param \Magento\Catalog\Model\Product $product
     * @return string
     */
    public function getProductPrice(\Magento\Catalog\Model\Product $product)
    {
        $priceRender = $this->getPriceRender();

        $price = '';
        if ($priceRender) {
            $price = $priceRender->render(
                \Magento\Catalog\Pricing\Price\FinalPrice::PRICE_CODE,
                $product,
                [
                    'include_container' => true,
                    'display_minimal_price' => true,
                    'zone' => \Magento\Framework\Pricing\Render::ZONE_ITEM_LIST,
                    'list_category_page' => true
                ]
            );
        }

        return $price;
    }

    /**
     * @return \Magento\Framework\Pricing\Render
     */
    protected function getPriceRender()
    {
        return $this->getLayout()->getBlock('product.price.render.default');
    }

    /**
     * get heading text
     * @return string
     */
    public function getBlockTitle()
    {
        if ($this->getHeadingText()) {
            return $this->getHeadingText();
        }
        return __('Specials');
    }

    /**
     * get heading level
     * @return string
     */
    public function getBlockTitleLevel()
    {
        if ($this->getHeadingLevel() && in_array($this->getHeadingLevel(), array('1', '2', '3', '4', '5', '6'))) {
            return 'h'.$this->getHeadingLevel();
        }
        return 'h2';
    }
}
