<?php

namespace Totaltools\Bundle\Block\Product\Combo;

use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Model\Category;
use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Collection\AbstractCollection;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\DataObject\IdentityInterface;


/**
 * rewrite block box to filter start and end publishing date
 */
class ItemList extends \Magento\Catalog\Block\Product\AbstractProduct
{

    /**
     * Product Collection
     *
     * @var AbstractCollection
     */
    protected $_productCollection;

    /**
     * @var \Magento\Framework\Data\Helper\PostHelper
     */
    protected $_postDataHelper;

    /**
     * @var \Magento\Framework\Url\Helper\Data
     */
    protected $urlHelper;

    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $categoryFactory;

    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $productCollectionFactory;

    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $catalogProductVisibility;

    /**
     * @var \Magento\CatalogInventory\Helper\Stock
     */
    protected $stockStatus;

    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $catalogConfig;

    /**
     * @param \Magento\Catalog\Block\Product\Context                           $context
     * @param \Magento\Framework\Data\Helper\PostHelper                        $postDataHelper
     * @param \Magento\Catalog\Model\CategoryFactory                           $categoryFactory
     * @param \Magento\Catalog\Model\ResourceModel\Category\Collection\Factory $categoryCollectionFactory
     * @param \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory   $productCollectionFactory
     * @param \Magento\Catalog\Model\Product\Visibility                        $catalogProductVisibility
     * @param \Magento\Framework\Url\Helper\Data                               $urlHelper
     * @param \Magento\CatalogInventory\Helper\Stock                           $stockStatus
     * @param array                                                            $data
     */
    public function __construct(
        \Magento\Catalog\Block\Product\Context $context,
        \Magento\Framework\Data\Helper\PostHelper $postDataHelper,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Magento\Catalog\Model\ResourceModel\Category\Collection\Factory $categoryCollectionFactory,
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Catalog\Model\Product\Visibility $catalogProductVisibility,
        \Magento\CatalogInventory\Helper\Stock $stockStatus,
        \Magento\Framework\Url\Helper\Data $urlHelper,
        array $data = []
    ) {
        $this->_postDataHelper = $postDataHelper;
        $this->categoryFactory = $categoryFactory;
        $this->categoryCollectionFactory = $categoryCollectionFactory;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->catalogProductVisibility = $catalogProductVisibility;
        $this->stockStatus = $stockStatus;
        $this->catalogConfig = $context->getCatalogConfig();
        $this->urlHelper = $urlHelper;
        parent::__construct(
            $context,
            $data
        );
    }

    /**
     * add class into catalog product view body
     */
    public function _prepareLayout()
    {
        if ($this->getProduct()->getId() && $this->getProduct()->getData('combo_base')) {
            $this->pageConfig->addBodyClass('combo-kit-product');
        }
        return parent::_prepareLayout();
    }


    /**
     * Retrieve loaded category collection
     *
     * @return AbstractCollection
     */
    protected function _getProductCollection()
    {
        if ($this->_productCollection === null) {
            if ($this->getProduct()->getId() && $this->getProduct()->getData('combo_base')) {
                $productCollection = $this->productCollectionFactory->create();
                $productCollection->addAttributeToSelect($this->catalogConfig->getProductAttributes())
                    ->addMinimalPrice()
                    ->addFinalPrice()
                    ->addTaxPercents()
                    ->addUrlRewrite()
                    ->setVisibility($this->catalogProductVisibility->getVisibleInCatalogIds())
                    ->addFieldToFilter('type_id', \Magento\Catalog\Model\Product\Type::TYPE_SIMPLE)
                    ->addFieldToFilter('has_options', '0')
                    ->addFieldToFilter('entity_id', array('nin' => array($this->getProduct()->getId())))
                    ->addAttributeToFilter('combo_link', $this->getProduct()->getData('combo_link'))
                    ->addAttributeToFilter('combo_base', false)
                ;

                $this->stockStatus->addInStockFilterToCollection($productCollection);
                $this->_productCollection = $productCollection;
            }
        }

        return $this->_productCollection;
    }

    /**
     * Retrieve category object
     *
     * @return AbstractCollection
     */
    public function getProduct()
    {
        if ($this->_coreRegistry->registry('product')) {
            return parent::getProduct();
        } else {
            $product = $this->productFactory->create();
            if ($this->getRequest()->getParam('id')) {
                $product->load($this->getRequest()->getParam('id'));
            }
            return $product;
        }
    }


    /**
     * Retrieve loaded category collection
     *
     * @return AbstractCollection
     */
    public function getLoadedProductCollection()
    {
        return $this->_getProductCollection();
    }

    /**
     * Retrieve current view mode
     *
     * @return string
     */
    public function getMode()
    {
        return \Magento\Catalog\Helper\Product\ProductList::VIEW_MODE_GRID;
    }

    /**
     * @param \Magento\Catalog\Model\Product $product
     * @return string
     */
    public function getProductPrice(\Magento\Catalog\Model\Product $product)
    {
        $priceRender = $this->getPriceRender();

        $price = '';
        if ($priceRender) {
            $price = $priceRender->render(
                \Magento\Catalog\Pricing\Price\FinalPrice::PRICE_CODE,
                $product,
                [
                    'include_container' => true,
                    'display_minimal_price' => true,
                    'zone' => \Magento\Framework\Pricing\Render::ZONE_ITEM_LIST,
                    'list_category_page' => true
                ]
            );
        }

        return $price;
    }

    /**
     * @return \Magento\Framework\Pricing\Render
     */
    protected function getPriceRender()
    {
        return $this->getLayout()->getBlock('product.price.render.default');
    }

}
