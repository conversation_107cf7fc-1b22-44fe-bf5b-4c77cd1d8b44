<?php
namespace Totaltools\Bundle\Block\Product\View;

use \Magento\Framework\View\Element\Template;
use \Magento\Framework\View\Element\Template\Context;
use \Magento\Framework\Registry;
use \Magento\Catalog\Model\Product;
use \Magento\Framework\Data\CollectionFactory;
use \Magento\Store\Model\StoreManagerInterface;
use \Magento\Framework\DataObject;
use \Magento\Framework\UrlInterface;

class Document extends Template
{

    /**
     * Number of attachment attributes
     */
    const ATTACHMENTS_ATTRIBUTE_COUNT = 5;

    /**
     * Attachments directory inside media
     */
    const ATTACHMENTS_DIRECTORY = 'product-attachments';

    /**
     * Current Product
     *
     * @var Product
     */
    protected $product;

    /**
     * Core registry
     *
     * @var Registry
     */
    protected $coreRegistry;

    /**
     * Collection factory
     *
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * Store Manager
     *
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * Attachments collection
     *
     * @var \Magento\Framework\Data\Collection
     */
    protected $attachments;

    /**
     * Constructor
     *
     * @param Context $context
     * @param Registry $registry
     * @param CollectionFactory $collectionFactory
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        CollectionFactory $collectionFactory,
        array $data = []
    ) {
        $this->coreRegistry = $registry;
        $this->collectionFactory = $collectionFactory;
        $this->storeManager = $context->getStoreManager();
        parent::__construct($context, $data);
    }

    /**
     * Get current Product
     *
     * @return Product|null
     */
    protected function getProduct()
    {
        if (!$this->product) {
            $this->product = $this->coreRegistry->registry('product');
        }
        return $this->product;
    }

    /**
     * Get media directory URL
     *
     * @return string
     */
    protected function getBaseMediaUrl()
    {
        return $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA);
    }

    /**
     * Get attachment URL from filename
     *
     * @param string $filename
     * @return string
     */
    protected function getAttachmentUrl($filename)
    {
        return $this->getBaseMediaUrl() . self::ATTACHMENTS_DIRECTORY . '/' . $filename;
    }

    /**
     * Get product attachments
     *
     * @return \Magento\Framework\Data\Collection|null
     */
    public function getAttachments()
    {
        if (!$this->attachments) {
            $attachments = $this->collectionFactory->create();
            $product = $this->getProduct();
            if ($product) {
                for ($i = 1; $i <= self::ATTACHMENTS_ATTRIBUTE_COUNT; $i++) {
                    if ($attachment = $product->getData('attachment_' . $i)) {
                        $attachments->addItem(new DataObject([
                            'title' => $product->getData('attachment_title_' . $i),
                            'url'   => $this->getAttachmentUrl($attachment),
                        ]));
                    }
                }
            }
            $this->attachments = $attachments;
        }
        return $this->attachments;
    }

}
