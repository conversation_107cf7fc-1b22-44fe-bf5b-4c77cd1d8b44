<?php
namespace Totaltools\Bundle\Block\Product\View;

use \Magento\Catalog\Block\Product\Gallery;

class Video extends \Magento\Catalog\Block\Product\Gallery
{
    /**
     * @return Collection
     */
    public function getGalleryCollection()
    {
        $videos = $this->getProduct()->getMediaGalleryImages();
        foreach ($videos as $video) {
            if ($video->getMediaType() == 'external-video') {
                continue;
            }
            $videos->removeItemByKey($video->getId());
        }
        return $videos;
    }

}
