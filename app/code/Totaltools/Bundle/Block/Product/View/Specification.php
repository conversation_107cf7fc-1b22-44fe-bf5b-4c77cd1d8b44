<?php
namespace Totaltools\Bundle\Block\Product\View;

use Magento\Catalog\Model\Product;

class Specification extends \Magento\Framework\View\Element\Template
{
    /**
     * @var Product
     */
    protected $_product = null;

    /**
     * @var array
     */
    protected $options;

    /**
     * Catalog product
     *
     * @var \Magento\Catalog\Helper\Product
     */
    protected $catalogProduct;

    /**
     * @var \Magento\Catalog\Model\Config
     */
    protected $_config;

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @var \Magento\Bundle\Model\ResourceModel\Selection\CollectionFactory
     */
    protected $_bundleCollection;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Catalog\Helper\Product $catalogProduct,
        \Magento\Bundle\Model\ResourceModel\Selection\CollectionFactory $bundleCollection,
        \Magento\Catalog\Model\Config $config,
        array $data = []
    ) {
        $this->_coreRegistry = $registry;
        $this->catalogProduct = $catalogProduct;
        $this->_bundleCollection = $bundleCollection;
        $this->_config = $config;
        parent::__construct($context, $data);
        if ($this->getProduct() && $this->getProduct()->getTypeId() == 'simple') {
            $this->setTemplate('Totaltools_Bundle::product/view/specification_simple.phtml');
        } else if ($this->getProduct() && $this->getProduct()->getTypeId() == 'bundle') {
            $this->setTemplate('Totaltools_Bundle::product/view/specification_bundle.phtml');
        } else {
            $this->setTemplate('Totaltools_Bundle::product/view/specification.phtml');
        }
    }

    /**
     * @return Product
     */
    public function getProduct()
    {
        if (!$this->_product) {
            $this->_product = $this->_coreRegistry->registry('product');
        }
        return $this->_product;
    }

    /**
     * $excludeAttr is optional array of attribute codes to
     * exclude them from additional data array
     *
     * @param Magento\Catalog\Model\Product $product
     * @param array $excludeAttr
     * @return array
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    public function getAdditionalData($product, array $excludeAttr = [])
    {
        $data = [];
        $attributes = $product->getAttributes();
        foreach ($attributes as $attribute) {
            if ($attribute->getIsVisibleOnFront() && !in_array($attribute->getAttributeCode(), $excludeAttr)) {
                $value = $attribute->getFrontend()->getValue($product);

                if (!$product->hasData($attribute->getAttributeCode())) {
                    $value = __('N/A');
                } elseif ((string)$value == '') {
                    $value = __('No');
                } elseif ($attribute->getFrontendInput() == 'price' && is_string($value)) {
                    $value = $this->priceCurrency->convertAndFormat($value);
                }

                if (is_string($value) && strlen($value)) {
                    $data[$attribute->getAttributeCode()] = [
                        'label' => __($attribute->getStoreLabel()),
                        'value' => $value,
                        'code' => $attribute->getAttributeCode(),
                    ];
                }
            }
        }
        return $data;
    }


    /**
     * @return array
     */
    public function getOptions()
    {
        if (!$this->options) {
            $product = $this->getProduct();
            $typeInstance = $product->getTypeInstance();
            $typeInstance->setStoreFilter($product->getStoreId(), $product);
            $optionCollection = $typeInstance->getOptionsCollection($product);
            // Add selection to option collection
            // learn from getSelectionsCollection() on \Magento\Bundle\Model\Product\Type
            $selectionCollection = $this->_bundleCollection->create()
                ->addAttributeToSelect($this->_config->getProductAttributes())
                ->setFlag('require_stock_items', true)
                ->setFlag('product_children', true)
                ->setPositionOrder()
                ->addStoreFilter($product->getStoreId())
                ->setStoreId($product->getStoreId())
                ->addFilterByRequiredOptions()
                ->setOptionIdsFilter($typeInstance->getOptionsIds($product));
            $this->options = $optionCollection->appendSelections(
                $selectionCollection,
                false,
                true
            );
        }

        return $this->options;
    }


}
