<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Bundle\Model\Combo;

use Magento\Catalog\Model\Product;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\DataObject;
use Magento\Setup\Exception;

/**
 * Shopping cart model
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 * @deprecated
 */
class Cart extends DataObject
{
    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * @var \Magento\CatalogInventory\Api\StockRegistryInterface
     */
    protected $stockRegistry;

    /**
     * @var \Magento\CatalogInventory\Api\StockStateInterface
     */
    protected $stockState;

    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    protected $quoteRepository;

    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    protected $productRepository;

    /**
     * @var \Magento\Quote\Model\QuoteFactory
     */
    protected $quoteFactory;

    /**
     * @param \Magento\Framework\Event\ManagerInterface $eventManager
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Framework\Message\ManagerInterface $messageManager
     * @param \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry
     * @param \Magento\CatalogInventory\Api\StockStateInterface $stockState
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     * @param \Magento\Quote\Model\QuoteFactory $quoteFactory
     * @param array $data
     * @codeCoverageIgnore
     * @SuppressWarnings(PHPMD.ExcessiveParameterList)
     */
    public function __construct(
        \Magento\Framework\Event\ManagerInterface $eventManager,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Magento\CatalogInventory\Api\StockRegistryInterface $stockRegistry,
        \Magento\CatalogInventory\Api\StockStateInterface $stockState,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        \Magento\Quote\Model\QuoteFactory $quoteFactory,
        array $data = []
    ) {
        $this->_eventManager = $eventManager;
        $this->storeManager = $storeManager;
        $this->_customerSession = $customerSession;
        $this->stockRegistry = $stockRegistry;
        $this->stockState = $stockState;
        $this->quoteRepository = $quoteRepository;
        parent::__construct($data);
        $this->productRepository = $productRepository;
        $this->quoteFactory = $quoteFactory;
    }

    /**
     * Initialize Product instance for virtual quote
     *
     * @return \Magento\Catalog\Model\Product|false
     */
    private function initItem($itemId)
    {
        if ($itemId) {
            $storeId = $this->storeManager->getStore()->getId();
            try {
                $product = $this->productRepository->getById($itemId, false, $storeId);
                if (!$product->getId()) {
                    return false;
                } else {
                    return $product;
                }
            } catch (NoSuchEntityException $e) {
                return false;
            }
        }
        return false;
    }

    /**
     * add items to virtual quote and calculate to response
     *
     * @return \Magento\Checkout\Model\ResourceModel\Cart
     * @codeCoverageIgnore
     */
    public function calculate($productInfo)
    {
        $quote = $this->quoteFactory->create();
        $quoteInfo = array();
        $errorItems = array();
        // add combo base first:
        $productInfo = $this->processProductInfo($productInfo);
        $comboProductRequest = $productInfo['comboProduct'];
        $comboId = $comboProductRequest['product'];
        $comboProduct = $this->initItem($comboId);

        if ($comboProduct) {

            if ($comboProduct->getTypeId() != \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE) {
                $comboQty = $comboProductRequest['qty'];
                if ($qty = $this->getStockItem($comboProduct, $comboQty)) {

                    $comboProductRequest['qty'] = $qty;
                    $request = new \Magento\Framework\DataObject($comboProductRequest);
                    try {
                        $quote->addProduct($comboProduct, $request);
                    } catch (Exception $e) {
                        return array(
                            'error' => 1,
                            'message' => __('Can not calculate this bundle!')
                        );
                    }

                } else {
                    return array(
                        'error' => 1,
                        'message' => __('Product is out of stock, can not calculate this bundle!')
                    );
                }
            } else {

                $request = new \Magento\Framework\DataObject($comboProductRequest);
                try {
                    $quote->addProduct($comboProduct, $request);
                } catch(Exception $e) {
                    return array(
                        'error' => 1,
                        'message' => __('Product is out of stock, can not calculate this bundle!')
                    );
                }
            }

            $comboItemIds = array();
            $comboItems = $productInfo['comboItem']['isAdded'];
            foreach ($comboItems as $itemId => $isAdded) {
                if ($isAdded) {
                    if ($product = $this->initItem($itemId)) {
                        $qty = 1;

                        $request = array();
                        $finalQty = $this->getStockItem($product, $qty);
                        //If product was not found in cart and there is set minimal qty for it
                        if ($finalQty && !$quote->hasProductId($product->getId())
                        ) {
                            $request = new \Magento\Framework\DataObject(['qty' => $finalQty]);
                        }

                        if ($request) {
                            try {
                                $quote->addProduct($product, $request);
                                $comboItemIds[] = $product->getId();
                            } catch (Exception $e) {
                                $errorItems = $itemId;
                                continue;
                            }

                        } else {
                            $errorItems[] = $itemId;
                        }
                    } else {
                        $errorItems[] = $itemId;
                    }
                }
            }

            $storeId = $this->storeManager->getStore()->getId();
            $quote->setStoreId($storeId);
            $quote->getBillingAddress();
            $quote->setCheckoutMethod('');
            $quote->getShippingAddress()->setCollectShippingRates(true);
            $quote->getShippingAddress()->collectShippingRates();
            $quote->collectTotals();

            $discount = 0;
            $addOns = 0;
            $countItem = 0;
            foreach ($quote->getAllItems() as $item) {
                $comboProductIds = array($comboId);

                if (isset($comboProduct['selected_configurable_option'])) {
                    $selectedOptionProducts = explode(',', $comboProduct['selected_configurable_option']);
                    $comboProductIds = array_unique(array_merge($comboProductIds, $selectedOptionProducts));
                }

                if (!$item->getParentItem()) {
                    if (!in_array($item->getProduct()->getId(), $comboProductIds)) {
                        $itemPrice = $item->getRowTotalInclTax() - $item->getDiscountAmount();
                        $quoteInfo['items'][] = array(
                            'itemProductId' => $item->getProduct()->getId(),
                            'itemName' => $item->getProduct()->getName(),
                            'itemPrice' => $itemPrice,
                            'originalPrice' => $item->getRowTotalInclTax(),
                            'discount' => $item->getDiscountAmount()
                        );
                        $discount += $item->getDiscountAmount();
                        $addOns += $itemPrice;
                        $countItem++;
                    }

                    if ($item->getProduct()->getId() == $comboId) {
                        $quoteInfo['comboBasePrice'] = $item->getRowTotalInclTax();
                    }
                }
            }

            $quoteInfo['discount'] = $discount;
            $quoteInfo['comboAddons'] = $addOns;
            $quoteInfo['grandTotal'] = $quote->getGrandTotal();
            $quoteInfo['countItem'] = $countItem;

            if ($errorItems) {
                $quoteInfo['errorItems'] = $errorItems;
            }

            return $quoteInfo;
        } else {
            return array(
                'error' => 1,
                'message' => __('Product is not exist!')
            );
        }
    }

    /*
     * process request to get correct formart
     *
     * */
    public function processProductInfo($productInfo)
    {
        $comboProductRequest = array();
        foreach ($productInfo as $key=>$value) {
            if ($key != 'comboItem') {
                $comboProductRequest[$key] = $value;
                unset ($productInfo[$key]);
            }
        }
        $productInfo['comboProduct'] = $comboProductRequest;
        return $productInfo;
    }

    public function getStockItem($product, $qty) {
        $stockItem = $this->stockRegistry->getStockItem($product->getId(), $product->getStore()->getWebsiteId());
        $minimumQty = $stockItem->getMinSaleQty();

        //If product was not found in cart and there is set minimal qty for it
        if ($minimumQty && $minimumQty > 0 && $qty) {
            return $minimumQty;
        } else {
            return $qty;
        }
    }
}
