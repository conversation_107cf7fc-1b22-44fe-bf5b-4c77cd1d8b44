<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Bundle\Model;

use Magento\Quote\Model\Quote\Item\CartItemProcessorInterface;
use Magento\Quote\Api\Data\CartItemInterface;
use Magento\Bundle\Api\Data\BundleOptionInterfaceFactory;
use Magento\Quote\Api\Data as QuoteApi;

class CartItemProcessor extends \Magento\Bundle\Model\CartItemProcessor
{

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function processOptions(CartItemInterface $cartItem)
    {
        if ($cartItem->getProductType() !== \Magento\Catalog\Model\Product\Type::TYPE_BUNDLE) {
            return $cartItem;
        }
        $productOptions = [];
        $bundleOptions = $cartItem->getBuyRequest()->getBundleOption();
        $bundleOptionsQty = $cartItem->getBuyRequest()->getBundleOptionQty();
        $bundleOptionsQty = is_array($bundleOptionsQty) ? $bundleOptionsQty : [];
        if (isset($bundleOptions) && is_array($bundleOptions) && count($bundleOptions)) {
            foreach ($bundleOptions as $optionId => $optionSelections) {
                if (empty($optionSelections)) {
                    continue;
                }
                $optionSelections = is_array($optionSelections) ? $optionSelections : [$optionSelections];
                $optionQty = isset($bundleOptionsQty[$optionId]) ? $bundleOptionsQty[$optionId] : 1;

                /** @var \Magento\Bundle\Api\Data\BundleOptionInterface $productOption */
                $productOption = $this->bundleOptionFactory->create();
                $productOption->setOptionId($optionId);
                $productOption->setOptionSelections($optionSelections);
                $productOption->setOptionQty($optionQty);
                $productOptions[] = $productOption;
            }
        }
        

        $extension = $this->productOptionExtensionFactory->create()->setBundleOptions($productOptions);
        if (!$cartItem->getProductOption()) {
            $cartItem->setProductOption($this->productOptionFactory->create());
        }
        $cartItem->getProductOption()->setExtensionAttributes($extension);
        return $cartItem;
    }
}
