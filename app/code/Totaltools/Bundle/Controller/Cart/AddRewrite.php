<?php
/**
 *
 * Copyright Â© 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Bundle\Controller\Cart;

use Magento\Checkout\Model\Cart\RequestQuantityProcessor;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Checkout\Model\Cart as CustomerCart;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Customer\CustomerData\Section\Identifier;
use Magento\Customer\CustomerData\SectionPoolInterface;
use Totaltools\Vii\Model\RewriteGiftCardAccount as ModelGiftcardaccount;
use Magento\Framework\Escaper;

/**
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class AddRewrite extends \Magento\Checkout\Controller\Cart\Add
{
    /**
     * @var Identifier
     */
    protected $sectionIdentifier;

    /**
     * @var SectionPoolInterface
     */
    protected $sectionPool;

    /**
     * @var Escaper
     */
    protected $escaper;
    /**
     * @var Magento\GiftCardAccount\Helper\Data
     */
    protected $giftCardHelper;

    /**
     * @var ModelGiftcardaccount
     */
    protected $giftCardAccount;
    /**
     * @var RequestQuantityProcessor
     */
    private $quantityProcessor;

    /**
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Framework\Data\Form\FormKey\Validator $formKeyValidator
     * @param CustomerCart $cart
     * @param ProductRepositoryInterface $productRepository
     * @param \Magento\GiftCardAccount\Helper\Data $giftCardHelper
     * @param ModelGiftcardaccount $giftCardAccount
     * @param Escaper $escaper
     * @param RequestQuantityProcessor|null $quantityProcessor
     * @codeCoverageIgnore
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\Data\Form\FormKey\Validator $formKeyValidator,
        CustomerCart $cart,
        ProductRepositoryInterface $productRepository,
        Identifier $sectionIdentifier,
        SectionPoolInterface $sectionPool,
        \Magento\GiftCardAccount\Helper\Data $giftCardHelper,
        ModelGiftcardaccount $giftCardAccount,
        Escaper $escaper,
        ?RequestQuantityProcessor $quantityProcessor = null
    ) {
        parent::__construct(
            $context,
            $scopeConfig,
            $checkoutSession,
            $storeManager,
            $formKeyValidator,
            $cart,
            $productRepository
        );
        $this->sectionIdentifier = $sectionIdentifier;
        $this->sectionPool = $sectionPool;
        $this->giftCardHelper = $giftCardHelper;
        $this->giftCardAccount = $giftCardAccount;
        $this->escaper = $escaper;
        $this->quantityProcessor = $quantityProcessor
            ?? ObjectManager::getInstance()->get(RequestQuantityProcessor::class);
    }

    /**
     * Add product to shopping cart action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    public function execute()
    {
        if (!$this->_formKeyValidator->validate($this->getRequest())) {
            return $this->resultRedirectFactory->create()->setPath('*/*/');
        }

        $params = $this->getRequest()->getParams();
        try {
            if (isset($params['qty'])) {
                $filter = new \Magento\Framework\Filter\LocalizedToNormalized(
                    ['locale' => $this->_objectManager->get('Magento\Framework\Locale\ResolverInterface')->getLocale()]
                );
                $params['qty'] = $this->quantityProcessor->prepareQuantity($params['qty']);
                $params['qty'] = $filter->filter($params['qty']);
            }

            $product = $this->_initProduct();
            $related = $this->getRequest()->getParam('related_product');

            /**
             * Check product availability
             */
            if (!$product) {
                return $this->goBack();
            }

            $checkGiftCardError = false;
            if ( $product->getTypeId() == \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD) {
                /**
                 * check if cart has products are not physical giftcard
                 */
                foreach ($this->cart->getQuote()->getAllItems() as $item) {
                    $productType =$item->getProduct()->getTypeId();
                    if ( $productType != \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD) {
                        $checkGiftCardError = true;
                        break;
                    }
                }

            } else {
                /**
                 * check if cart has physical gift card
                 */
                foreach ($this->cart->getQuote()->getAllItems() as $item) {
                    $productItem = $item->getProduct();
                    $productType = $productItem->getTypeId();
                    if ($productType == \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD) {
                        $checkGiftCardError = true;
                        break;
                    }
                }
            }


            if ($checkGiftCardError) {
                $this->messageManager->addNotice(
                    $this->_objectManager->get('Magento\Framework\Escaper')->escapeHtml(
                        __('Sorry, you are unable to purchase a gift card with other items in your cart.
                        Please place 2 separate orders for your gift card and other items.'))
                );
                return $this->goBack(null, $product);
            }

            $this->cart->addProduct($product, $params);
            if($product->getTypeId() == \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD) {
                $quote = $this->cart->getQuote();
                $quoteCards = $this->giftCardHelper->getCards($quote);
                foreach ($quoteCards as $card) {
                    $code = $card[ModelGiftcardaccount::CODE];
                    $pin = $card[ModelGiftcardaccount::PIN];
                    if ($pin) {
                        try {
                            $this->giftCardAccount->loadByCode(
                                $code,
                                $pin
                            )->removeFromCart();
                            $this->messageManager->addSuccess(
                                __(
                                    'Gift Card "%1" was removed.',
                                    $this->escaper->escapeHtml($code)
                                )
                            );
                        } catch (\Magento\Framework\Exception\LocalizedException $e) {
                            $this->messageManager->addErrorMessage($e->getMessage());
                        } catch (\Exception $e) {
                            $this->messageManager->addExceptionMessage($e, __('You can\'t remove this gift card.'));
                        }

                    }
                    $quote->collectTotals();
                }
            }
            if (!empty($related)) {
                $this->cart->addProductsByIds(explode(',', $related));
            }

            if (isset($params['combo-item-ids'])) {
                $this->cart->addProductsByIds(explode(',', $params['combo-item-ids']));
            }

            $this->cart->save();

            /**
             * @todo remove wishlist observer \Magento\Wishlist\Observer\AddToCart
             */
            $this->_eventManager->dispatch(
                'checkout_cart_add_product_complete',
                ['product' => $product, 'request' => $this->getRequest(), 'response' => $this->getResponse()]
            );

            if (!$this->_checkoutSession->getNoCartRedirect(true)) {
                if (!$this->cart->getQuote()->getHasError()) {
                    $message = __(
                        'You added %1 to your shopping cart.',
                        $product->getName()
                    );
                    $this->messageManager->addSuccessMessage($message);
                }
                return $this->goBack(null, $product);
            }
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            if ($this->_checkoutSession->getUseNotice(true)) {
                $this->messageManager->addNotice(
                    $this->_objectManager->get('Magento\Framework\Escaper')->escapeHtml($e->getMessage())
                );
            } else {
                $messages = array_unique(explode("\n", $e->getMessage()));
                foreach ($messages as $message) {
                    $this->messageManager->addError(
                        $this->_objectManager->get('Magento\Framework\Escaper')->escapeHtml($message)
                    );
                }
            }

            $url = $this->_checkoutSession->getRedirectUrl(true);

            if (!$url) {
                $cartUrl = $this->_objectManager->get('Magento\Checkout\Helper\Cart')->getCartUrl();
                $url = $this->_redirect->getRedirectUrl($cartUrl);
            }

            return $this->goBack($url);

        } catch (\Exception $e) {
            $this->messageManager->addException($e, __('We can\'t add this item to your shopping cart right now.'));
            $this->_objectManager->get('Psr\Log\LoggerInterface')->critical($e);
            return $this->goBack();
        }
    }

    /**
     * Resolve response
     *
     * @param string $backUrl
     * @param \Magento\Catalog\Model\Product $product
     * @return $this|\Magento\Framework\Controller\Result\Redirect
     */
    protected function goBack($backUrl = null, $product = null)
    {
        if (!$this->getRequest()->isAjax()) {
            return parent::_goBack($backUrl);
        }

        $result = [];

        if ($backUrl || $backUrl = $this->getBackUrl()) {
            $result['backUrl'] = $backUrl;
        } else {
            if ($product && !$product->getIsSalable()) {
                $result['product'] = [
                    'statusText' => __('Out of stock')
                ];
            }
        }

        /*TOT0001-551: cart cart info into response*/
        $sectionNames = 'cart';
        $sectionNames = $sectionNames ? array_unique(\explode(',', $sectionNames)) : null;
        $cartUpdate = $this->sectionPool->getSectionsData($sectionNames, true);

        if (isset($cartUpdate['cart'])) {
            $result['cart'] = $cartUpdate;
        }
        /*TOT0001-551: cart cart info into response*/

        $this->getResponse()->representJson(
            $this->_objectManager->get('Magento\Framework\Json\Helper\Data')->jsonEncode($result)
        );
    }

    /**
     * Initialize product instance from request data
     *
     * @return \Magento\Catalog\Model\Product|false
     */
    protected function _initProduct()
    {
        $productId = (int)$this->getRequest()->getParam('product');
        if ($productId) {
            $storeId = $this->_objectManager->get(
                \Magento\Store\Model\StoreManagerInterface::class
            )->getStore()->getId();
            try {
                return $this->productRepository->getById($productId, false, $storeId);
            } catch (NoSuchEntityException $e) {
                return false;
            }
        }
        $productSku = $this->getRequest()->getParam('sku');
        if ($productSku) {
            $storeId = $this->_objectManager->get(
                \Magento\Store\Model\StoreManagerInterface::class
            )->getStore()->getId();
            try {
                return $this->productRepository->get($productSku, false, $storeId);
            } catch (NoSuchEntityException $e) {
                return false;
            }
        }
        return false;
    }
}
