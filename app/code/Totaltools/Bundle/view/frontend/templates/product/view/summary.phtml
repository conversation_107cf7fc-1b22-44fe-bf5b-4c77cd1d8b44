<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>

<?php
    $_product = $block->getProduct();
?>
<?php if ($_product->isSaleable() && $block->hasOptions()): ?>
<div id="bundleSummary"
     class="block-bundle-summary"
     data-mage-init='{"sticky":{"container": ".product-add-form"}}'>
    <div class="title no-display">
        <strong><?php /* @escapeNotVerified */ echo __('Your Customization'); ?></strong>
    </div>
    <div class="content">
        <div class="bundle-info">
            <?php //echo $block->getImage($_product, 'bundled_product_customization_page')->toHtml(); ?>
            <div class="product-details">
                <?php echo $block->getChildHtml('', true);?>
            </div>
        </div>
        <div class="bundle-summary no-display">
            <strong class="subtitle"><?php /* @escapeNotVerified */ echo __('Summary'); ?></strong>
            <div id="bundle-summary" data-container="product-summary">
                <ul data-mage-init='{"productSummary": []}' class="bundle items"></ul>
                <script data-template="bundle-summary" type="text/x-magento-template">
                    <li>
                        <strong class="label"><%- data._label_ %>:</strong>
                        <div data-container="options"></div>
                    </li>
                </script>
                <script data-template="bundle-option" type="text/x-magento-template">
                    <div><?php /* @escapeNotVerified */ echo __('%1 x %2', '<%- data._quantity_ %>', '<%- data._label_ %>') ?></div>
                </script>
            </div>
        </div>
    </div>
</div>
<script type="text/x-magento-init">
    {
        ".product-add-form": {
            "slide": {
                "slideSpeed": 1500,
                "slideSelector": "#bundle-slide",
                "slideBackSelector": ".action.customization.back",
                "bundleProductSelector": "#bundleProduct",
                "bundleOptionsContainer": ".product-add-form"
                <?php if ($block->isStartCustomization()): ?>
                ,"autostart": true
                <?php endif;?>
            }
        }
    }
</script>
<?php endif; ?>
