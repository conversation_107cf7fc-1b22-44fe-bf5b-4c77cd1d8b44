<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
use Magento\Framework\App\Action\Action;

// @codingStandardsIgnoreFile

?>
<?php
/**
 * Product list template
 *
 * @var $block \Totaltools\Bundle\Block\Product\Combo\ItemList
 */
?>
<?php
$_productCollection = $this->getLoadedProductCollection();
$_helper = $this->helper('Magento\Catalog\Helper\Output');

if (sizeof($_productCollection) > 0) :
    $viewMode = 'grid';
    $image = 'category_page_grid';
?>
    <div class="block additional-items-combo">
        <div class="combo-bundle-list-loader please-wait load indicator" style="display: none;"></div>
        <form class="form additional-combo-items" id="additional-combo-items"
            action="<?php echo $block->getUrl('totaltools_bundle/combo/update'); ?>" method="post"
            data-mage-init='{"Totaltools_Bundle/js/combo-kit": {
                "updateItems": ".action.update-combo-items",
                "updateUrl": "<?php echo $block->getUrl('totaltools_bundle/combo/update'); ?>",
                "comboForm": "#additional-combo-items",
                "comboBundleSpinnerId": ".combo-bundle-list-loader, .combo-bundle-added-item-loader"
                }
            }'
            >
            <div class="additional-items-combo-wrapper">
                <div class="block-title title"><strong><?php echo __('Add to your bundle'); ?></strong></div>
                <?php $iterator = 0; ?>
                <ul id="additional-items-combo-list" class="additional-items-combo-list">
                    <?php /** @var $_product \Magento\Catalog\Model\Product */ ?>
                    <?php foreach ($_productCollection as $_product): ?>
                        <?php $iterator++; ?>
                        <?php if ($iterator%4 == 1): ?>
                        <li class="item">
                        <?php endif; ?>
                            <?php if ($iterator%4 == 1) { ?>
                                <div class="combo-odd">
                            <?php } else if ($iterator%4 == 3) { ?>
                                <div class="combo-even">
                            <?php } ?>

                            <div class="product-info">
                                <?php $productImage = $block->getImage($_product, $image);?>
                                <?php // Product Image ?>
                                <a href="<?php /* @escapeNotVerified */ echo $_product->getProductUrl() ?>" class="product photo product-item-photo" tabindex="-1">
                                    <?php echo $productImage->toHtml(); ?>
                                </a>
                                <div class="product-details">
                                    <?php $_productNameStripped = $block->stripTags($_product->getName(), null, true); ?>
                                    <div class="product-name">
                                        <a class="product-item-link"
                                           href="<?php /* @escapeNotVerified */ echo $_product->getProductUrl() ?>">
                                            <?php /* @escapeNotVerified */ echo $_helper->productAttribute($_product, $_product->getName(), 'name'); ?>
                                        </a>
                                    </div>

                                    <?php /* @escapeNotVerified */ echo $block->getProductPrice($_product) ?>

                                    <div class="product-actions">
                                        <div class="actions">
                                            <button type="button" title="Add Item" id = "combo-update-button-<?php echo $_product->getId(); ?>" class="action primary update-combo-items"
                                                    data-item="<?php echo $_product->getId(); ?>">
                                                <span class="add"><?php echo __('Add'); ?></span>
                                            </button>
                                        </div>
                                        <input type="hidden" id="combo-item-is-added-<?php echo $_product->getId(); ?>" name="comboItem[isAdded][<?php echo $_product->getId(); ?>]" value="0"/>
                                    </div>
                                </div>
                            </div>
                            <?php
                                $closeTag = false;
                                if ($iterator%4 == 2) {
                                    $closeTag = true;
                                }

                                if ($iterator%4 == 0) {
                                    $closeTag = true;
                                }

                                if (($iterator !=0 || $iterator !=2)) {
                                    if ($iterator == sizeof($_productCollection)) {
                                        $closeTag = true;
                                    }
                                }
                            ?>
                            <?php if ($closeTag): ?>
                                </div>
                            <?php endif; ?>
                        <?php if (($iterator%4 == 0) || ($iterator == sizeof($_productCollection))): ?>
                        </li>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </ul>
            </div>
        </form>
    </div>
<script>
    require([
        'jquery',
        'balance/box'
    ], function ($) {
        var options = {
            flickityOptions: {
                contain: true,
                pageDots: false,
                cellAlign: "left",
                wrapAround: true
            }
        };
        var $items;
        $(document).ready(function(){
            $items  = $('#additional-items-combo-list');
            jQuery.balance.boxSlider(options, $items);

            var max_height_productname = 0;
            $(".product-name",$items).each(function(){
                if (max_height_productname < $(this).height()){
                    max_height_productname = $(this).height();
                }
            })
            $(".product-name",$items).css("height",max_height_productname);
        });

        $(window).resize(function(){
            $(".product-name",$items).css("height","auto");
            changeProductProductNameHeight($items);
        });

        function changeProductProductNameHeight(el){
            var max_height_info = 0;
            $(".product-name",el).each(function(){
                if (max_height_info < $(this).height()){
                    max_height_info = $(this).height();
                }
            })
            $(".product-name",el).css("height",max_height_info);
        }

    });
</script>
<?php endif; ?>