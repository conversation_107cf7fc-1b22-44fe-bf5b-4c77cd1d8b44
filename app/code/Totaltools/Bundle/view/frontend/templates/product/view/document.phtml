<?php
/** @var $block \Totaltools\Bundle\Block\Product\View\Document */
$attachments = $block->getAttachments();

if (!$attachments->getSize()) {
    return;
}
?>
<div class="product-attachments">
    <h2 class="title"><?php echo __('Manuals'); ?></h2>
    <ul>
        <?php /** @var \Magento\Framework\DataObject $attachment */ ?>
        <?php foreach ($attachments as $attachment) : ?>
            <li>
                <a href="<?php /* @escapeNotVerified */ echo $attachment->getData('url'); ?>" target="_blank">
                    <?php /* @escapeNotVerified */ echo __($attachment->getData('title')); ?>
                </a>
            </li>
        <?php endforeach; ?>
    </ul>
</div>