<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**
 * Product view template
 *
 * @var $block \Magento\Catalog\Block\Product\View
 */
?>
<?php $_helper = $this->helper('Magento\Catalog\Helper\Output'); ?>
<?php $_product = $block->getProduct(); ?>
<?php
    $themeHelper = $this->helper(\Totaltools\Theme\Helper\Data::class);
    $loggedIn = $themeHelper->isLoggedIn();
    $isUsedForReward = $_product->getData('is_used_for_reward');
?>

<?php if ($_product->getAttributeText('stock_availability_code')!='SP'):?>
<div class="product-add-form">
    <form data-product-sku="<?= $block->escapeHtml($_product->getSku()) ?>" action="<?php /* @escapeNotVerified */ echo $block->getSubmitUrl($_product) ?>" method="post"
          id="product_addtocart_form"<?php if ($_product->getOptions()): ?> enctype="multipart/form-data"<?php endif; ?>>
        <input type="hidden" name="product" value="<?php /* @escapeNotVerified */ echo $_product->getId() ?>" />
        <input type="hidden" name="selected_configurable_option" value="" />
        <input type="hidden" name="related_product" id="related-products-field" value="" />
        <input type="hidden" name="combo-item-ids" id="combo-item-ids-field" value=""/>
        <?php echo $block->getBlockHtml('formkey')?>
        <?php echo $block->getChildHtml('form_top'); ?>
        <?php if (!$block->hasOptions()):?>
            <?php echo $block->getChildHtml('product_info_form_content'); ?>
        <?php else:?>
            <?php if ($_product->isSaleable() && $block->getOptionsContainer() == 'container1'):?>
                <?php echo $block->getChildChildHtml('options_container') ?>
            <?php endif;?>
        <?php endif; ?>

        <?php if ($_product->isSaleable() && $block->hasOptions() && $block->getOptionsContainer() == 'container2'):?>
            <?php echo $block->getChildChildHtml('options_container') ?>
        <?php endif;?>
        <?php echo $block->getChildHtml('form_bottom'); ?>
        <?php echo $block->getChildHtml('popup.payment'); ?>
    </form>
</div>
<script type="text/x-magento-init">
    {
        "[data-role=priceBox][data-price-box=product-id-<?= $block->escapeHtml($_product->getId()) ?>]": {
            "priceBox": {
                "priceConfig":  <?= /* @noEscape */ $block->getJsonConfig() ?>
            }
        }
    }
</script>
<script>
    require([
        'jquery',
        'underscore',
        'Magento_Catalog/js/price-box'
    ], function($, _){

        $(window).scroll(_.debounce(function() {
            if (!$('.box-tocart').length) return;

            var hT = $('.box-tocart').offset().top,
                hH = $('#fulfilment-boxes-component').length ? $('#fulfilment-boxes-component').offset().top : $('.product-column-right').offset().top,
                height = hH - hT,
                wS = $(this).scrollTop();

            if (wS >= hT || wS < height ) {
                $('#product-addto-bar').fadeIn();
                if($(window).width() <= 768) {
                    $('#___ratingbadge_0').fadeOut();
                }
            } else {
                $('#product-addto-bar').fadeOut();
                if($(window).width() <= 768) {
                    $('#___ratingbadge_0').fadeIn();
                }
            }
        }, 50));
    });
</script>
<?php else: ?>
    <?php echo $block->getLayout()->createBlock('Magento\Cms\Block\Block')->setBlockId('specialty_product')->toHtml();?>
    <?php //echo $this->getChildHtml('specialty_product_form'); ?>
<?php endif; ?>

<?php if (!$_product->isSaleable()): ?>
<style>
    .product-addto-container {display: none;}
</style>
<?php endif; ?>
