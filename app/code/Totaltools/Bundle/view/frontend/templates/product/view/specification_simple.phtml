<?php /** @var $block Totaltools\Bundle\Block\Product\View\Specification */ ?>
<?php
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$_product = $block->getProduct();
?>
<div>
    <div class="product-specification">
        <?php if ($_additional = $block->getAdditionalData($this->getProduct())): ?>
            <div class="additional-attributes-wrapper table-wrapper">
                <table class="data table additional-attributes" id="product-attribute-specs-table">
                    <caption class="table-caption"><?php /* @escapeNotVerified */ echo __('Specification') ?></caption>
                    <tbody>
                    <?php foreach ($_additional as $_data): ?>
                        <tr>
                            <th class="col label" scope="row"><?php echo $block->escapeHtml(__($_data['label'])) ?></th>
                            <td class="col data" data-th="<?php echo $block->escapeHtml(__($_data['label'])) ?>"><?php /* @escapeNotVerified */ echo $_helper->productAttribute($_product, $_data['value'], $_data['code']) ?></td>
                        </tr>
                    <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif;?>
    </div>
</div>


