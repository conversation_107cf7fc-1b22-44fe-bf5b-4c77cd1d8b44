<?php

/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var $block \Magento\Catalog\Block\Product\View */
?>
<?php $_product = $block->getProduct(); ?>
<?php $buttonTitle = __('Add to Cart'); ?>

<?php /* Dont show addtocart when product type is SP */ ?>
<?php if ($_product->getAttributeText('stock_availability_code') != 'SP') : ?>
    <?php $hasComboBase = $_product->getData('combo_base') ?  'has-combo-base' : ''; ?>
    <?php
    if ($hasComboBase) :
        $addedItemsBlock = $block->getLayout()->createBlock(
            'Magento\Framework\View\Element\Template',
            'comboItemsAdded',
            array('template' => 'Totaltools_Bundle::product/combo/added_items.phtml')
        )->setTemplate('Totaltools_Bundle::product/combo/added_items.phtml');

        echo $addedItemsBlock->toHtml();
    endif;
    ?>
    <?php if ($_product->isSaleable()) : ?>
        <div class="box-tocart">
            <div class="fieldset">
                <?php if ($block->shouldRenderQuantity()) : ?>
                    <div class="field qty">
                        <label class="label" for="qty"><span><?php /* @escapeNotVerified */ echo __('Qty') ?></span></label>
                        <div class="control">
                            <input type="number" name="qty" id="qty" maxlength="12" value="<?php /* @escapeNotVerified */ echo $block->getProductDefaultQty() * 1 ?>" title="<?php /* @escapeNotVerified */ echo __('Qty') ?>" class="input-text qty" data-validate="<?php echo $block->escapeHtml(json_encode($block->getQuantityValidators())) ?>" />
                        </div>
                    </div>
                <?php endif; ?>
                <div class="actions">
                    <button type="submit" title="<?php /* @escapeNotVerified */ echo $buttonTitle ?>" class="action primary tocart" id="product-addtocart-button">
                        <span><?php /* @escapeNotVerified */ echo $buttonTitle ?></span>
                    </button>
                    <?php echo $block->getChildHtml('', true) ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
    <?php if ($block->isRedirectToCartEnabled()) : ?>
        <script type="text/x-magento-init">
            {
            "#product_addtocart_form": {
                "Magento_Catalog/product/view/validation": {
                    "radioCheckboxClosest": ".nested"
                }
            }
        }
    </script>
    <?php else : ?>
        <script>
            require([
                'jquery',
                'mage/mage',
                'Magento_Catalog/product/view/validation',
                'Magento_Catalog/js/catalog-add-to-cart'
            ], function($) {
                'use strict';
                $(document).on('ajaxComplete', function(event, xhr, settings) {
                    $('#product-addtocart-button').removeAttr('disabled');
                });

                $('#product_addtocart_form').mage('validation', {
                    radioCheckboxClosest: '.nested',
                    submitHandler: function(form) {
                        var widget = $(form).catalogAddToCart({
                            bindSubmit: false
                        });

                        widget.catalogAddToCart('submitForm', $(form));

                        return false;
                    }
                });
            });
        </script>
    <?php endif; ?>
<?php endif; ?>