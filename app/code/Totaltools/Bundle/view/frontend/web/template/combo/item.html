<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div data-role="additional-combo-items" id="additional-combo-items" class="additional-combo-items">
    <!-- ko if: (countItem()) -->
        <div class="title">Currently in your bundle</div>
        <!-- ko foreach: getItems() -->
        <div class="item">
            <a class="remove-item" data-bind="click: function(itemProductId) { $parent.removeItem(itemProductId); }"><span>Remove</span></a>
            <div class="item-name" data-bind="text: itemName"></div>
            <div class="item-price" data-bind="text: $parent.getFormatPriceItem(itemPrice)"></div>
        </div>
        <!--/ko-->
        <div class="summary">
            <div class="combo-base">
                <div class="combo-base-content">
                    <div class="title">Base Combo</div>
                    <div class="price"><!-- ko text: getComboBasePrice()--><!-- /ko --></div>
                </div>
            </div>
            <div class="add-ons">
                <div class="add-ons-content">
                    <div class="title">Add-ons</div>
                    <div class="price"><!-- ko text: getComboAddons()--><!-- /ko --></div>
                </div>
            </div>
            <div class="total">
                <div class="title">Total Price</div>
                <div class="price"><!-- ko text: getGrandTotal()--><!-- /ko --></div>
            </div>
        </div>
        <!-- ko if: (getDiscount()) -->
        <div class="discount">Your total savings: <span class="price"><!-- ko text: getDiscount()--><!-- /ko --></span></div>
        <!--/ko-->
    <!--/ko-->
</div>