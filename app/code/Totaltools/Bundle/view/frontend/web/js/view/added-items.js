/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
define([
    'ko',
    'jquery',
    'uiComponent',
    'Totaltools_Bundle/js/model/items',
    'Totaltools_Bundle/js/action/update-combo',
    'Magento_Catalog/js/price-utils'
], function (ko, $, Component, addedItems, updateCombo, priceUtils) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Bundle/combo/item',
            addedItems: addedItems
        },

        initObservable: function () {
            this._super().observe(addedItems);
            return this;
        },

        countItem: function() {
            return this.addedItems.countItem();
        },

        getItems: function() {
            return this.addedItems.items();
        },

        getFormatPriceItem: function(itemPrice) {
            return priceUtils.formatPrice(itemPrice);
        },

        getComboBasePrice: function() {
            return priceUtils.formatPrice(this.addedItems.comboBasePrice());
        },

        getComboAddons: function(){
            return priceUtils.formatPrice(this.addedItems.comboAddons());
        },

        getGrandTotal: function() {
            return priceUtils.formatPrice(this.addedItems.grandTotal());
        },

        getDiscount: function(){
            return priceUtils.formatPrice(this.addedItems.discount());
        },

        removeItem: function(item) {
            if (item) {
                var options = {
                    updateItems: '.action.update-combo-items',
                    comboForm: '#additional-combo-items',
                    comboBundleSpinnerId: ".combo-bundle-list-loader, .combo-bundle-added-item-loader",
                    productAddToCartForm: '#product_addtocart_form',
                    currentIsAddedItem: '',
                    currentUpdateButton: '',
                    firstValue: '0',
                    secondValue: '1'
                };

                options.updateUrl = $(options.comboForm).attr('action');
                options.currentIsAddedItem = '#combo-item-is-added-' + item.itemProductId;
                options.currentUpdateButton = '#combo-update-button-' + item.itemProductId;
                options.firstValue = '1';
                options.secondValue = '0';
                if ($(options.productAddToCartForm).validation().valid()) {
                    $("#combo-item-is-added-" + item.itemProductId).val(options.secondValue);
                    updateCombo(options);
                }
            }
        }
    });
});
