/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
define([
    'jquery',
    /*'Totaltools_Bundle/js/model/items',*/
    'Totaltools_Bundle/js/action/update-combo'
], function ($, updateCombo) {
    'use strict';

    /**
     * Widget product Summary:
     * Handles rendering of Bundle options and displays them in the Summary box
     */
    $.widget('totaltools.combo', {
        options: {
            updateItems: '.action.update-combo-items',
            updateUrl: '',
            comboForm: '#additional-combo-items',
            comboBundleSpinnerId: ".combo-bundle-list-loader, .combo-bundle-added-item-loader",
            productAddToCartForm: '#product_addtocart_form',
            currentIsAddedItem: '',
            currentUpdateButton: '',
            firstValue: '0',
            secondValue: '1'
        },
        _create: function () {
            var self = this, options = this.options;
            $(this.options.updateItems).on('click', function() {
                if ($(options.productAddToCartForm).validation().valid()) {
                    var itemId = $(this).attr('data-item');
                    var firstValue = $("#combo-item-is-added-" + itemId).val();
                    var secondValue = '0';
                    if (firstValue == '0') {
                        secondValue = '1';
                    } else {
                        secondValue = '0';
                    }
                    $("#combo-item-is-added-" + itemId).val(secondValue);

                    options.currentIsAddedItem = "#combo-item-is-added-" + itemId;
                    options.currentUpdateButton = '#combo-update-button-' + itemId;
                    options.firstValue = firstValue;
                    options.secondValue = secondValue;
                    updateCombo(options);
                }

            });
        }
    });

    return $.totaltools.combo;
});
