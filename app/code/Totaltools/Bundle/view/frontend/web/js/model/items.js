/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
define(
    ['ko', 'jquery'], function (ko, $) {
    'use strict';
    return {
        countItem: ko.observable(false),
        items: ko.observableArray(),
        discount: ko.observable(false),
        comboBasePrice: ko.observable(''),
        comboAddons: ko.observable(''),
        grandTotal: ko.observable(''),
        updateComboItems: function(quote) {
            try {
                this.countItem(quote.countItem);
                this.discount(quote.discount);
                this.comboBasePrice(quote.comboBasePrice);
                this.comboAddons(quote.comboAddons);
                this.grandTotal(quote.grandTotal);
                this.items.removeAll();
                var self = this;
                var items = '';
                var i = 0;
                if (quote.countItem > 0) {
                    $.each(quote.items, function(index, item){
                        if (i == 0) {
                            items = item.itemProductId;
                            i++;
                        } else {
                            items += ',' + item.itemProductId;
                        }

                        self.items.push(item);
                    });
                }
                $('#combo-item-ids-field').val(items);
                return true;
            } catch (err) {
                return false;
            }
        },

        clear: function() {

        }

    };
});
