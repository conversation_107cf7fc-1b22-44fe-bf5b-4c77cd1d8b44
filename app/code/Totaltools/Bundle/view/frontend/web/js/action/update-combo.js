/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
define(['jquery',
        'Totaltools_Bundle/js/model/items',
        'mage/validation'
    ],
    function ($, addedItem) {
        'use strict';

        return function (options, result) {
            if ($(options.productAddToCartForm).validation().valid()) {

                var dataJson = $(options.productAddToCartForm).serialize() + '&' + $(options.comboForm).serialize();

                $.ajax({
                    url: options.updateUrl,
                    type: 'post',
                    cache: false,
                    data: dataJson,
                    dataType: 'json',
                    beforeSend: function() {
                        $(options.comboBundleSpinnerId).show();
                    },
                    success: function(response) {
                        if (response.error) {
                            $(options.currentIsAddedItem).val(options.firstValue);
                        } else {
                            addedItem.updateComboItems(response);

                            if (options.firstValue == '0') {
                                $(options.currentUpdateButton).html('<span class="remove">Remove</span>');
                                $(options.currentUpdateButton).addClass('red');
                            } else {
                                $(options.currentUpdateButton).html('<span class="add">Add</span>');
                                $(options.currentUpdateButton).removeClass('red');
                            }
                        }
                    },
                    complete: function() {
                        $(options.comboBundleSpinnerId).hide();
                    }
                });
            }
        };
    }
);
