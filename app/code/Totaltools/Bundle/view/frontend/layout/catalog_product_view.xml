<?xml version="1.0"?>
<!--
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page layout="1column" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <!--<attribute name="class" value="\Totaltools\Bundle\Helper\Body"/>-->
        <referenceBlock name="product.attributes" remove="true"/>
        <referenceBlock name="product.info.description" remove="true"/>

        <referenceBlock name="product.info.details">
            <block class="Magento\Catalog\Block\Product\View\Description" name="product.info.descriptions" as="descriptions" template="Magento_Catalog::product/view/description.phtml" group="detailed_info">
                <arguments>
                    <argument name="css_class" xsi:type="string">descriptions</argument>
                    <argument name="at_label" xsi:type="string">none</argument>
                    <argument name="title" translate="true" xsi:type="string">Description</argument>
                </arguments>
            </block>
            <block class="Totaltools\Bundle\Block\Product\View\Specification" name="product.info.specification" as="specification" group="detailed_info">
                <arguments>
                    <argument name="css_class" xsi:type="string">description-specification</argument>
                    <argument name="at_label" xsi:type="string">none</argument>
                    <argument translate="true" name="title" xsi:type="string">Specification</argument>
                </arguments>
                <block class="Totaltools\Catalog\Block\Product\AttributeIcons" name="product.info.attribute_icons" as="attribute_icons" template="Totaltools_Catalog::product/attributeicons.phtml"/>
            </block>
            <block class="Magento\Cms\Block\Block" name="product.info.shippingreturn" as="shippingreturn" group="detailed_info">
                <arguments>
                    <argument name="css_class" xsi:type="string">shipping-return</argument>
                    <argument name="block_id" xsi:type="string">shipping_and_return</argument>
                    <argument translate="true" name="title" xsi:type="string">Shipping &amp; Returns</argument>
                </arguments>
            </block>
            <block class="Totaltools\Bundle\Block\Product\View\Document" name="product.video.document" as="videomedia" group="detailed_info" template="Totaltools_Bundle::product/view/document.phtml">
                <arguments>
                    <argument name="css_class" xsi:type="string">video-manuals</argument>
                    <argument translate="true" name="title" xsi:type="string">Video &amp; Manuals</argument>
                </arguments>
            </block>
        </referenceBlock>

        <referenceContainer name="right.column">
            <container name="list.combo.bundle.product" htmlTag="div" htmlClass="list-combo-bundle-product" after="catalog.product.related">
                <block class="Totaltools\Bundle\Block\Product\Combo\ItemList" name="combo.list" template="Totaltools_Bundle::product/combo/list.phtml"/>
            </container>
        </referenceContainer>

        <referenceBlock name="product.info.addtocart">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">Totaltools_Bundle::product/view/addtocart.phtml</argument>
            </action>
        </referenceBlock>

        <referenceBlock name="product.info.addtocart.additional">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">Totaltools_Bundle::product/view/addtocart.phtml</argument>
            </action>
        </referenceBlock>

        <referenceBlock name="product.info">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">Totaltools_Bundle::product/view/form.phtml</argument>
            </action>
        </referenceBlock>
        <move element="reviews.tab" destination="product.reviews.wrapper" as="product.reviews" after="-"/>
    </body>
</page>
