<?xml version="1.0"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="bundle.back.button" remove="true" />

        <referenceContainer name="product.info.main">
            <container name="customize.button.container" htmlTag="div" htmlClass="customize-button-container no-display" />
            <block class="Humm\HummPaymentGateway\Block\Advert\Widget" name="product.info.addtocart.Humm.productimage" template="advert/widget.phtml">
                <arguments>
                    <argument name="page_type" xsi:type="string">product</argument>
                </arguments>
            </block>
        </referenceContainer>

        <referenceContainer name="product.info.form.options">
            <block class="Magento\Catalog\Block\Product\View" name="bundle.product.options.wrapper.container" template="Totaltools_Bundle::product/view/bundle/options_container.phtml"/>
        </referenceContainer>

        <move element="customize.button" destination="customize.button.container"/>

        <referenceBlock name="bundle.summary">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">Totaltools_Bundle::product/view/summary.phtml</argument>
            </action>
        </referenceBlock>

        <referenceBlock name="product.info.addtocart.bundle">
            <action method="setTemplate">
                <argument name="template" xsi:type="string">Totaltools_Bundle::product/view/addtocart.phtml</argument>
            </action>
        </referenceBlock>

        <referenceBlock name="product.info.specification">
            <arguments>
                <argument name="template" xsi:type="string">Totaltools_Bundle::product/view/specification_bundle.phtml</argument>
            </arguments>
        </referenceBlock>

        <move element="bundle.options.container" destination="product.info.main"  before="product.info.extrahint" />
        <move element="bundle.product.options.wrapper" destination="bundle.product.options.wrapper.container"/>

        <move element="product.info" destination="product.info.addto.container" after="-" />
    </body>
</page>
