<?php

namespace Totaltools\Subcategories\Model\Config;

class HeadingLevel implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * Get Groups
     *
     * @return array
     */
    public function getAllOptions() {
        for ($i = 1; $i<=6; $i++) {
            $this->_options[] = array(
                'label' => 'H'.$i,
                'value' => $i
            );
        }
        return $this->_options;
    }

    public function toOptionArray() {
        return $this->getAllOptions();
    }
}