<?php

namespace Totaltools\Subcategories\Model\Config;

class Categories implements \Magento\Framework\Option\ArrayInterface
{
    /**
     * @var \Magento\Catalog\Model\ResourceModel\Category\Collection\Factory
     */
    protected $categoryCollectionFactory;

    /**
     * Constructor
     *
     * @param \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $collectionFactory
     */
    public function __construct(\Magento\Catalog\Model\ResourceModel\Category\Collection\Factory $collectionFactory) {
        $this->categoryCollectionFactory = $collectionFactory;
    }

    /**
     * Get level 2 categories
     *
     * @return array
     */
    public function getAllOptions() {
        $this->_options = $this->CategoryTree();
        return $this->_options;
    }

    /**
     * retrieve category tree
     *
     * @return array
     */
    public function CategoryTree() {
        $options = array();
        $categoryCollection = $this->categoryCollectionFactory->create()->addRootLevelFilter();
        //$i = 0;
        foreach ($categoryCollection as $rootCategory) {
            //$options[$i]['label'] = $rootCategory->getName();
            $level2Categories = $rootCategory->getChildrenCategories($rootCategory);
            foreach ($level2Categories as $category) {
                $options[] = ['label' => $category->getName(), 'value' => $category->getUrlKey()];
            }
        }
        return $options;
    }


    public function toOptionArray() {
        return $this->getAllOptions();
    }
}