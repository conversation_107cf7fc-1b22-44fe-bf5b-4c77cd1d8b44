<?php
/**
 * @var $block \Totaltools\Subcategories\Block\Subcategories
 */
$_categories = $block->getCurrentChildCategories();
if (!$_categories) return;

$_count = is_array($_categories) ? count($_categories) : $_categories->count();
if (!$_count) return;

?>

<?php $_categories = $block->getCurrentChildCategories(); ?>
<?php $_count = is_array($_categories) ? count($_categories) : $_categories->count(); ?>
<?php if ($_count): ?>
    <div class="subcategories-wrapper">
        <?php if ($this->getTitle()): ?>
            <h<?php echo $this->getTitleHeaderType(); ?>>
                <?php echo $this->getTitle(); ?>
            </h<?php echo $this->getTitleHeaderType(); ?>>
        <?php endif; ?>
        <ul class="subcategories-list">
            <?php foreach ($_categories as $_category): ?>
                <li class="item">
                    <a href="<?php echo $_category->getUrl() ?>" title="<?php echo $block->escapeHtml($_category->getName()) ?>">
                        <img src="<?php echo $_category->getImageUrl(); ?>" alt="<?php echo $block->escapeHtml($_category->getName()) ?>" />
                    </a>
                </li>
            <?php endforeach ?>
        </ul>
    </div>

    <script>
        require([
            'jquery',
            'balance/box'
        ], function ($) {
            var count_item  = <?php echo $_count;?>;
            var wrapAround  = false;
            if (count_item > 6){
                wrapAround  = true;
            }
            var options = {
                flickityOptions: {
                    contain: true,
                    pageDots: false,
                    cellAlign: "left",
                    wrapAround: wrapAround
                }
            };
            var htmlBrand = '';
            jQuery(document).ready(function(){
                var windowWidth = jQuery(window).width();
                if(windowWidth > 767){
                    htmlBrand = jQuery('.subcategories-wrapper').html();
                    var $image  = jQuery('.subcategories-wrapper .subcategories-list');
                    jQuery.balance.boxSlider(options, $image);
                } else {
                    jQuery('.subcategories-wrapper').addClass('mobile');
                }
            });

            jQuery(window).resize(function(){
                var windowWidth = jQuery(window).width();
                if(windowWidth > 767){
                    if(jQuery('.subcategories-wrapper').hasClass('mobile')){
                        var $image  = jQuery('.subcategories-wrapper.mobile .subcategories-list');
                        jQuery.balance.boxSlider(options, $image);
                        jQuery('.subcategories-wrapper').removeClass('mobile');
                    }

                } else{
                    // jQuery('.subcategories-wrapper').html(htmlBrand);
                    jQuery('.subcategories-wrapper').addClass('mobile');
                }
            });

        });

    </script>
<?php endif; ?>