<?php
namespace Totaltools\Subcategories\Block;

use Magento\Catalog\Model\ResourceModel\Category\Collection as CategoryCollection;
use Magento\Catalog\Model\ResourceModel\Category\Collection\Factory as CategoryCollectionFactory;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;

class Subcategories
    extends Template
{

    const DEFAULT_TEMPLATE = 'Totaltools_Subcategories::subcategories.phtml';

    const BRAND_CATEGORY_ID = 581;

    /**
     * Category Collection factory
     * @var CategoryCollectionFactory
     */
    protected $categoryCollectionFactory;

    /**
     * Category collection
     * @var CategoryCollection
     */
    protected $childCategories;

    /**
     * Subcategories constructor
     *
     * @param Context $context
     * @param CategoryCollectionFactory $categoryCollectionFactory
     * @param array $data
     */
    public function __construct (
        Context $context,
        CategoryCollectionFactory $categoryCollectionFactory,
        array $data = []
    ) {
        $this->categoryCollectionFactory = $categoryCollectionFactory;
        parent::__construct($context, $data);
        if (!$this->hasData('template')) {
            $this->setData('template', self::DEFAULT_TEMPLATE);
            $this->setTemplate(self::DEFAULT_TEMPLATE);
        }
    }

    /**
     * Get heading
     *
     * @return string
     */
    public function getTitle ()
    {
        $title = '';
        if ($this->getHeadingText()) {
            $title = $this->getHeadingText();
        }
        return $title;
    }

    /**
     * Get heading level
     *
     * @return int Number in 1-6
     */
    public function getTitleHeaderType ()
    {
        $headingLevel = 2;
        if ($dataHeadingLevel = (int) $this->getHeadingLevel()) {
            if (in_array($dataHeadingLevel, [1, 2, 3, 4, 5, 6])) {
                $headingLevel = $dataHeadingLevel;
            }
        }
        return $headingLevel;
    }

    /**
     * Retrieve child categories of category
     *
     * @return CategoryCollection|null
     */
    public function getCurrentChildCategories ()
    {
        if (!$this->childCategories) {
            $this->childCategories = $this->categoryCollectionFactory->create()
                ->addAttributeToSelect('*')
                ->addAttributeToFilter('is_active', 1)
                ->addAttributeToFilter('include_on_homepage', 1)
                ->addAttributeToFilter('parent_id', self::BRAND_CATEGORY_ID)
                ->setOrder('name', 'ASC')
            ;
        }
        return $this->childCategories;
    }

}
