<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget id="subcategories_widget" class="Totaltools\Subcategories\Block\SubcategoriesWidget">
        <label translate="true">Subcategories Block</label>
        <description translate="true">Block shows slider of category images</description>
        <parameters>
            <parameter name="heading_text" xsi:type="text" visible="true" required="false" sort_order="10">
                <label translate="true">Heading Text</label>
                <description translate="true">If empty, the heading is not disappeared</description>
            </parameter>
            <parameter name="heading_level" xsi:type="select" sort_order="20" required="false" visible="true"
                       source_model="Totaltools\Subcategories\Model\Config\HeadingLevel" >
                <label translate="true">Heading Level</label>
                <description translate="true">Heading Tag for Heading Title</description>
            </parameter>
            <parameter name="parent_category" xsi:type="select" sort_order="30" required="true" visible="true"
                       source_model="Totaltools\Subcategories\Model\Config\Categories" >
                <label translate="true">Parent Category</label>
            </parameter>
        </parameters>
    </widget>
</widgets>
