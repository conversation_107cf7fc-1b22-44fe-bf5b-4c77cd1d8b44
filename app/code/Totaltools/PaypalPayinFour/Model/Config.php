<?php

/**
 * @package   Totaltools_PaypalPayinFour
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\PaypalPayinFour\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

/**
 * Paypal Pay in 4 module configuration
 */
class Config implements ConfigInterface
{
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @inheritdoc
     */
    public function showOnProduct() : ?bool
    {
        return $this->scopeConfig->getValue(
            ConfigInterface::XML_PATH_SHOW_ON_PRODUCT,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * @inheritdoc
     */
    public function showOnCategory() : ?bool
    {
        return $this->scopeConfig->getValue(
            ConfigInterface::XML_PATH_SHOW_ON_CATEGORY,
            ScopeInterface::SCOPE_STORE
        );
    }
    
    /**
     * @inheritdoc
     */
    public function showOnCart() : ?bool
    {
        return $this->scopeConfig->getValue(
            ConfigInterface::XML_PATH_SHOW_ON_CART,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * @inheritdoc
     */
    public function showOnCheckout() : ?bool
    {
        return $this->scopeConfig->getValue(
            ConfigInterface::XML_PATH_SHOW_ON_CHECKOUT,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get View In Set Enable/Disable
     *
     * @return bool|null
     */
    public function isEnabled() : ?bool
    {
        return (bool)$this->scopeConfig->getValue(
            ConfigInterface::XML_PATH_PAYINFOUR_ENABLED,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * Get Paypal SDK
     *
     * @return string|null
     */
    public function getPaypalSdk() : ?string
    {
        return $this->scopeConfig->getValue(
            ConfigInterface::XML_PATH_GET_PAYPAL_SDK,
            ScopeInterface::SCOPE_STORE
        );
    }
}