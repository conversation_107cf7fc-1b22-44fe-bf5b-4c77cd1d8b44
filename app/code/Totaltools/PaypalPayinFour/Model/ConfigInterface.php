<?php

/**
 * @package   Totaltools_PaypalPayinFour
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\PaypalPayinFour\Model;

/**
 * Paypal Pay in 4 module configuration
 *
 * @api
 * @since 100.2.0
 */
interface ConfigInterface
{
    /**
     * Pay In 4 Enabled config path
     */
    const XML_PATH_PAYINFOUR_ENABLED = 'totaltools_paypalpayinfour/payinfour/enable';

    /**
     * Show On Product config path
     */
    const XML_PATH_SHOW_ON_PRODUCT = 'totaltools_paypalpayinfour/payinfour/show_on_product';

    /**
     * Show On Category config path
     */
    const XML_PATH_SHOW_ON_CATEGORY = 'totaltools_paypalpayinfour/payinfour/show_on_category';

    /**
     * Show On Cart config path
     */
    const XML_PATH_SHOW_ON_CART = 'totaltools_paypalpayinfour/payinfour/show_on_cart';

    /**
     * Show On Checkout config path
     */
    const XML_PATH_SHOW_ON_CHECKOUT = 'totaltools_paypalpayinfour/payinfour/show_on_checkout';

    /**
     * Get paypal sdk config path
     */
    const XML_PATH_GET_PAYPAL_SDK = 'totaltools_paypalpayinfour/payinfour/get_paypal_sdk';

    /**
     * Show On Product
     *
     * @return bool|null
     * @since 100.2.0
     */
    public function showOnProduct() : ?bool;

    /**
     * Show On Category
     *
     * @return bool|null
     * @since 100.2.0
     */
    public function showOnCategory() : ?bool;

    /**
     * Show On Cart
     *
     * @return bool|null
     * @since 100.2.0
     */
    public function showOnCart() : ?bool;

    /**
     * Show On Checkout
     *
     * @return bool|null
     * @since 100.2.0
     */
    public function showOnCheckout() : ?bool;

    /**
     * Check module is enable
     *
     * @return bool|null
     */
    public function isEnabled() : ?bool;

    /**
     * Get Paypal SDK
     *
     * @return string|null
     */
    public function getPaypalSdk() : ?string;
}