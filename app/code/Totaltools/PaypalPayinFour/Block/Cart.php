<?php
/**
 * @package   Totaltools_PaypalPayinFour
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */
namespace Totaltools\PaypalPayinFour\Block;

use Magento\Framework\View\Element\Template;
use Totaltools\PaypalPayinFour\Model\ConfigInterface;

class Cart extends Template
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var ConfigInterface $configInterface
     */
    private $configInterface;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param ConfigInterface $config
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Checkout\Model\Session $checkoutSession,
        ConfigInterface $config,
        array $data = []
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->configInterface = $config;
        parent::__construct($context, $data);
    }

    /**
     * Get quote object associated with cart. By default it is current customer session quote
     *
     * @return \Magento\Quote\Model\Quote
     */
    public function getQuoteData()
    {
        $this->_checkoutSession->getQuote();
        if (!$this->hasData('quote')) {
            $this->setData('quote', $this->_checkoutSession->getQuote());
        }
        return $this->_getData('quote');
    }

    /**
     * Check if quote is virtual
     *
     * @return bool
     */
    public function isVirtual()
    {
        return $this->_checkoutSession->getQuote()->isVirtual();
    }

    /**
     * Module is Enable
     *
     * @return bool|null
     */
    public function isEnabled() : ?bool
    {
        return $this->configInterface->isEnabled();
    }

    /**
     * Show on Product Page
     *
     * @return bool|null
     */
    public function showOnCart() : ?bool
    {
        return $this->configInterface->showOnCart();
    }

    /**
     * Show on Product Page
     *
     * @return bool|null
     */
    public function showOnCheckout() : ?bool
    {
        return $this->configInterface->showOnCheckout();
    }
    
}
