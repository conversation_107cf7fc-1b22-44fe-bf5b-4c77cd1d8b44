<?php
/**
 * @package   Totaltools_PaypalPayinFour
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\PaypalPayinFour\Block;

use Magento\Framework\View\Element\Template;
use Totaltools\PaypalPayinFour\Model\ConfigInterface;

class Config extends Template
{
   
    /**
     * @var ConfigInterface $configInterface
     */
    private $configInterface;

    /**
     * Product Block Constructor
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param ConfigInterface $config
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        ConfigInterface $config,
        array $data = []
    ) {
        $this->configInterface = $config;
        parent::__construct($context, $data);
    }

    

    /**
     * Module is Enable
     *
     * @return bool|null
     */
    public function isEnabled() : ?bool
    {
        return $this->configInterface->isEnabled();
    }

    /**
     * Show on Product Page
     *
     * @return bool|null
     */
    public function showOnProduct() : ?bool
    {
        return $this->configInterface->showOnProduct();
    }

    /**
     * Show on Product Page
     *
     * @return bool|null
     */
    public function showOnCategory() : ?bool
    {
        return $this->configInterface->showOnCategory();
    }

    /**
     * Show on Product Page
     *
     * @return bool|null
     */
    public function showOnCart() : ?bool
    {
        return $this->configInterface->showOnCart();
    }

    /**
     * Show on Product Page
     *
     * @return bool|null
     */
    public function showOnCheckout() : ?bool
    {
        return $this->configInterface->showOnCheckout();
    }

    /**
     * Get Paypal SDK
     *
     * @return string|null
     */
    public function getPaypalSdk() : ?string
    {
        return $this->configInterface->getPaypalSdk();
    }
    
}
