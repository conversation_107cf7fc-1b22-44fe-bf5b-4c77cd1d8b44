<?php

/**
 * @package   Totaltools_PaypalPayinFour
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */

/** @var Totaltools\PaypalPayinFour\Block\Config $block */

$isEnabled = $block->isEnabled();
$paypalSdk  = $block->getPaypalSdk();

if( $isEnabled && $paypalSdk && in_array($block->getRequest()->getFullActionName(), ['catalog_category_view', 'catalog_product_view', 'checkout_cart_index']) ) 
{
    echo $paypalSdk;
}


if( in_array($block->getRequest()->getFullActionName(), ['catalog_category_view', 'catalog_product_view', 'checkout_cart_index']) ) : ?>

<script>
require([
    'jquery'
], function ($) {

    $(document).ready(function($) {
        var isB2BCustomer = false;
        var mageCacheStorage = localStorage['mage-cache-storage'];
        
        if (typeof mageCacheStorage != 'undefined') {
            let mageCacheObj = JSON.parse(mageCacheStorage);
            isB2BCustomer = mageCacheObj.company && mageCacheObj.company.has_customer_company;
        }

        if(!isB2BCustomer)
        {
            $("#payinfour-product-widget").show();
            $("#payinfour-category-widget").show();
            $(".cart-data-pp-amount").show();  
        } 
    });

});
</script>
<?php endif; ?>
