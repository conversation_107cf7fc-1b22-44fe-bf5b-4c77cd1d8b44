<?php
/**
 * @package   Totaltools_PaypalPayinFour
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */

/** @var \Totaltools\PaypalPayinFour\Block\Config $block */
/** @var Magento\Framework\Escaper $escaper */
/** @var \Totaltools\Checkout\Helper\Data $checkoutHelper */
$checkoutHelper = $this->helper(\Totaltools\Checkout\Helper\Data::class);
/** @var Totaltools\Catalog\Helper\Data $catalogHelper */
$catalogHelper = $this->helper(\Totaltools\Catalog\Helper\Data::class);

$isEnabled = $block->isEnabled();

if ($this->getData('page_type') == 'cart') {
    $price = (float) $checkoutHelper->getQuoteGrandTotal();
    $showWidget = $block->showOnCart();
    $isVirtual = $checkoutHelper->isVirtualQuote();
} else {
    $product = $catalogHelper->getCurrentProduct();
    $price = $catalogHelper->getCurrentProductFinalPrice();
    $showWidget = $block->showOnProduct();
    $isVirtual = !$product || in_array($product->getTypeId(), ['virtual']);
}
$isB2BCustomer = $this->helper('\Totaltools\Customer\Helper\AttributeData')->isB2BCustomer() ? 1 : 0;
if($isEnabled && $showWidget && !$isVirtual && !$isB2BCustomer) :
?>

<?= /** @noEscape */ $block->getPaypalSdk();?>
<?php
$maxPrice = $checkoutHelper->getPaypalPayInFourMaxAmount();
$minPrice = $checkoutHelper->getPaypalPayInFourMinAmount();
$quadPrice = $catalogHelper->convertAndFormatPrice($price / 4);
$formattedMinPrice = $catalogHelper->convertAndFormatPrice($checkoutHelper->getPaypalPayInFourMinAmount());
$formattedMaxPrice = $catalogHelper->convertAndFormatPrice($checkoutHelper->getPaypalPayInFourMaxAmount());
$message = $price <= $maxPrice && $price >= $minPrice ? __('4x payments of %1', $quadPrice) :
    __('Available for orders between %1', $formattedMinPrice . ' - ' . $formattedMaxPrice);

?>
<div class="paypal-widget">
    <div class="info-container">
        <img src="<?= $block->getViewFileUrl('images/paypal-logo.png') ?>" alt="Paypal" width="75" height="26"/>
    </div>
    <span class="payment-detail"><?= $escaper->escapeHtml($message) ?></span>
    <div class="payment-widget-iframe"
        data-pp-message="<?= $escaper->escapeHtml($message) ?>"
        data-pp-placement="product"
        data-pp-amount="<?= /** @noEscape */ $price?>"
        data-pp-style-logo-type="primary"
        data-pp-style-logo-position="top"
        data-pp-style-text-size="16"
        data-pp-style-text-align="center"
    >
    </div>
</div>
<?php endif; ?>
