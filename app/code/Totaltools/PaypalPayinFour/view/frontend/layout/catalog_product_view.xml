<?xml version="1.0"?>
<!--
/**
 * Copyright Â© 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page layout="1column"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<body>

		<referenceContainer name="product.info.price">
            <block class="Totaltools\PaypalPayinFour\Block\Config" name="payinfour.product" template="Totaltools_PaypalPayinFour::payinfour_product.phtml" after="product.info.price.additional">
                <arguments>
                    <argument name="ctn-class" xsi:type="string">paypal-ctn</argument>
                    <argument name="sort-order" xsi:type="string">10</argument>
                </arguments>
            </block>
        </referenceContainer>

	</body>
</page>
