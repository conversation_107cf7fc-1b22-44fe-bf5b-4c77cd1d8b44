<?xml version="1.0"?>
<!--
/**
 * @package   Totaltools_PaypalPayinFour
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label" sortOrder="0">
            <label>Total Tools</label>
        </tab>
        <section id="totaltools_paypalpayinfour" translate="label" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Paypal Pay In 4</label>
            <tab>totaltools</tab>
            <resource>Totaltools_PaypalPayinFour::config</resource>
                <group id="payinfour" translate="label" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="60">
                    <label>PayPal Pay in 4 Setting</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Fieldset</frontend_model>
                    <field id="enable" translate="label" type="select" sortOrder="21" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Enabled</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="show_on_product" translate="label" type="select" sortOrder="22" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Show On Product Page</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="show_on_category" translate="label" type="select" sortOrder="23" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Show On Category Page</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="show_on_cart" translate="label" type="select" sortOrder="24" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Show On Cart Page</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="get_paypal_sdk" translate="label" type="textarea" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Paypal SDK</label>
                    </field>
                    <field id="min_order_total" translate="label comment" type="text" sortOrder="40" showInDefault="1"
                           showInWebsite="1" showInStore="0">
                        <label>Minimum Order Amount</label>
                        <comment>For PDP Displaying</comment>
                        <validate>validate-number</validate>
                    </field>
                    <field id="max_order_total" translate="label comment" type="text" sortOrder="45" showInDefault="1"
                           showInWebsite="1" showInStore="0">
                        <label>Maximum Order Amount</label>
                        <comment>For PDP Displaying</comment>
                        <validate>validate-number</validate>
                    </field>
                </group>
        </section>
    </system>
</config>
