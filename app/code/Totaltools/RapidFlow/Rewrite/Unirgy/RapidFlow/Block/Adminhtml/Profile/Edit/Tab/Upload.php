<?php
/**
 * Copyright ©  All rights reserved.
 */
namespace Totaltools\RapidFlow\Rewrite\Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab;

use Magento\Backend\Block\Widget;

class Upload extends \Unirgy\RapidFlow\Block\Adminhtml\Profile\Edit\Tab\Upload
{
    protected function _prepareLayout()
    {
        /** @var \Magento\Backend\Block\Media\Uploader $uploader */
        $uploader = $this->getLayout()->createBlock('Magento\Backend\Block\Media\Uploader');
        $this->setChild('uploader', $uploader);
        $uploader->setTemplate('Unirgy_RapidFlow::urapidflow/upload/uploader.phtml');
        $uploader->getConfig()
            ->setUrl($this->_backendModelUrl->getUrl('*/*/upload', $this->_params()))
            ->setFileField('file')
            ->setFilters([
                             'csv' => [
                                 'label' => __('CSV and Tab Separated files (.csv, .txt)'),
                                 'files' => ['*.csv', '*.txt']
                             ],
                             'all' => [
                                 'label' => __('All Files'),
                                 'files' => ['*.*']
                             ]
                         ]);

        return Widget::_prepareLayout();
    }
}

