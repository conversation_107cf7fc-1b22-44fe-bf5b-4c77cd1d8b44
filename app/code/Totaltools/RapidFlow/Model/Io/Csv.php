<?php

namespace Totaltools\RapidFlow\Model\Io;

use Unirgy\RapidFlow\Exception;

class Csv extends \Unirgy\RapidFlow\Model\Io\File
{
    public function open($filename, $mode)
    {
        parent::open($filename, $mode);
        return $this;
    }

    public function read()
    {
        if (!$this->_fp) {
            return false;
        }
        // for PHP 5.3.0 only
        #$result = fgetcsv($this->_fp, 0, $this->getDelimiter(), $this->getEnclosure(), $this->getEscape());
        $result = fgetcsv($this->_fp, 0, $this->getDelimiter(), $this->getEnclosure());
        return $result;
    }

    public function write($data)
    {
        if (!$this->_fp) {
            throw new \Exception(__('Resource is closed, unable to write to the file'));
        }
        #$result = $this->putcsv($this->_fp, $data, $this->getDelimiter(), $this->getEnclosure(), $this->getEscape());
        $result = fputcsv($this->_fp, $data, $this->getDelimiter(), $this->getEnclosure());
        if (!$result) {
            throw new \Exception(__('Unable to write to the file'));
        }
        return $this;
    }

    public function getDelimiter()
    {
        $delimiter = $this->_getData('delimiter');
        if (!$delimiter) {
            $this->setData('delimiter', ',');
        } elseif ($delimiter === '\\t') {
            $this->setData('delimiter', "\t");
        }
        return $this->_getData('delimiter');
    }

    public function getEnclosure()
    {
        if (!$this->_getData('enclosure')) {
            $this->setData('enclosure', '"');
        }
        return $this->_getData('enclosure');
    }

    public function getEscape()
    {
        if (!$this->_getData('escape')) {
            $this->setData('escape', '\\');
        }
        return $this->_getData('escape');
    }

    /**
     * Implements custom escape char
     *
     * @param mixed $handle
     * @param mixed $fields
     * @param mixed $delimiter
     * @param mixed $enclosure
     * @param mixed $escape
     * @return int
     */
    public function putcsv(&$handle, array $fields, $delimiter = ',', $enclosure = '"', $escape = '\\')
    {
        $i = 0;
        $csvline = '';
        $fieldCnt = count($fields);
        $encIsQuote = in_array($enclosure, array('"', "'"));
        reset($fields);

        foreach ($fields as $field) {
            /* enclose a field that contains a delimiter, an enclosure character, or a newline */
            if (is_string($field) && (
                    strpos($field, $delimiter) !== false ||
                    strpos($field, $enclosure) !== false ||
                    strpos($field, $escape) !== false ||
                    strpos($field, "\n") !== false ||
                    strpos($field, "\r") !== false ||
                    strpos($field, "\t") !== false ||
                    strpos($field, ' ') !== false
                )
            ) {

                $fieldLen = strlen($field);
                $escaped = 0;

                $csvline .= $enclosure;
                for ($ch = 0; $ch < $fieldLen; $ch++) {
                    if ($field[$ch] == $escape && $field[$ch + 1] == $enclosure && $encIsQuote) {
                        continue;
                    } elseif ($field[$ch] == $escape) {
                        $escaped = 1;
                    } elseif (!$escaped && $field[$ch] == $enclosure) {
                        $csvline .= $enclosure;
                    } else {
                        $escaped = 0;
                    }
                    $csvline .= $field[$ch];
                }
                $csvline .= $enclosure;
            } else {
                $csvline .= $field;
            }

            if ($i++ != $fieldCnt) {
                $csvline .= $delimiter;
            }
        }

        $csvline .= "\n";

        return fwrite($handle, $csvline);
    }
}
