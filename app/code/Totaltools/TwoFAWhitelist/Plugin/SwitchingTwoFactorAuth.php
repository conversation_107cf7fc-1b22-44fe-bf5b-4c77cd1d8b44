<?php
declare(strict_types=1);

namespace Totaltools\TwoFAWhitelist\Plugin;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\TwoFactorAuth\Model\TfaSession;

class SwitchingTwoFactorAuth
{
    const XML_PATH_CONFIG_ENABLE = 'twofactorauth/general/enable';

    /** @var ScopeConfigInterface */
    private $scopeConfig;

    /**
     * BypassTwoFactorAuth constructor.
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig
    ) {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Enables the bypass of 2FA for admin access.
     * This can be useful within development & integration environments.
     * If 2FA is enabled, return the original result.
     * If 2FA is disabled, always return true so all requests bypass 2FA.
     *
     * NOTE: Always keep 2FA enabled within production environments for security purposes.
     *
     * @param TfaSession $subject
     * @param bool $result
     * @return bool
     */
    public function afterIsGranted(
        TfaSession $subject,
        bool $result
    ): bool {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_CONFIG_ENABLE)
            ? $result
            : true;
    }
}
