<?php

namespace Totaltools\TwoFAWhitelist\Helper;

class Config extends \Magento\Framework\App\Helper\AbstractHelper
{
    const XML_PATH_2FA_IP_WHITELIST = 'twofactorauth/general/ip_whitelist';

    /**
     * @var string[]
     */
    private $additionalWhitelistIps = [];

    /**
     * Retrives array with ips converted to int
     *
     * @return mixed[]
     */
    public function getWhitelist():array
    {
        $whitelistIps = [];

        if ($whitelistConfig = $this->getWhitelistConfig()) {
            $whitelistIps = array_filter(array_map([$this, 'filterIpRows'], explode(PHP_EOL, $whitelistConfig)));
        }

        return array_merge($whitelistIps, $this->additionalWhitelistIps);
    }

    /**
     * Retrives whitelist config
     *
     * @return string
     */
    private function getWhitelistConfig()
    {
        return trim($this->scopeConfig->getValue(self::XML_PATH_2FA_IP_WHITELIST));
    }

    /**
     * Cleans whitelist row from spaces and tries to convert ip address to int
     *
     * @param string $ipRow
     * @return int|false
     */
    public function filterIpRows(string $ipRow)
    {
        return ip2long(trim($ipRow));
    }
}
