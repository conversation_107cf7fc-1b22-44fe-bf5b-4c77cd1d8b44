<?php

namespace Totaltools\TwoFAWhitelist\Observer;

use Magento\Authorization\Model\UserContextInterface;
use Magento\Framework\App\ActionFlag;
use Magento\Framework\AuthorizationInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\HTTP\PhpEnvironment\RemoteAddress;
use Magento\TwoFactorAuth\Api\TfaInterface;
use Magento\TwoFactorAuth\Api\TfaSessionInterface;
use Magento\TwoFactorAuth\Api\UserConfigRequestManagerInterface;
use Magento\TwoFactorAuth\Model\UserConfig\HtmlAreaTokenVerifier;
use Totaltools\TwoFAWhitelist\Helper\Config;

class TwoFAControllerActionPredispatch extends \Magento\TwoFactorAuth\Observer\ControllerActionPredispatch
{
    /**
     * @var Config
     */
    private $configHelper;
    /**
     * @var RemoteAddress
     */
    private $remoteAddress;
    private UserConfigRequestManagerInterface $userConfigRequestManager;

    /**
     * @param TfaInterface $tfa
     * @param TfaSessionInterface $tfaSession
     * @param UserConfigRequestManagerInterface $configRequestManager
     * @param HtmlAreaTokenVerifier $tokenManager
     * @param ActionFlag $actionFlag
     * @param \Magento\Framework\UrlInterface $url
     * @param AuthorizationInterface $authorization
     * @param UserContextInterface $userContext
     * @param Config $configHelper
     * @param RemoteAddress $remoteAddress
     */
    public function __construct(
        TfaInterface                      $tfa,
        TfaSessionInterface               $tfaSession,
        UserConfigRequestManagerInterface $configRequestManager,
        HtmlAreaTokenVerifier             $tokenManager,
        ActionFlag                        $actionFlag,
        \Magento\Framework\UrlInterface   $url,
        AuthorizationInterface            $authorization,
        UserContextInterface              $userContext,
        Config $configHelper,
        RemoteAddress $remoteAddress
    ) {
        parent::__construct(
            $tfa,
            $tfaSession,
            $configRequestManager,
            $tokenManager,
            $actionFlag,
            $url,
            $authorization,
            $userContext
        );
        $this->configHelper = $configHelper;
        $this->remoteAddress = $remoteAddress;
    }

    /**
     * Skip tfa validation for setup ips
     *
     * @param Observer $observer
     */
    public function execute(Observer $observer)
    {
        if ($this->isIpWhitelisted()) {
            return;
        }

        parent::execute($observer);
    }

    /**
     * Checks if current user ip matches with any ip-address in whitelist
     *
     * @return bool
     */
    private function isIpWhitelisted()
    {
        if (empty($whitelist = $this->configHelper->getWhitelist())) {
            return false;
        }
        // Wrap by ip2long because of bug in getRemoteAddress method
        $remoteIp = ip2long($this->remoteAddress->getRemoteAddress());

        return  in_array($remoteIp, $whitelist);
    }
}
