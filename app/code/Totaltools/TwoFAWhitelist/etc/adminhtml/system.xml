<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="twofactorauth">
            <group id="general">
                <field id="enable" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" canRestore="1">
                    <label>Enable 2FA</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Warning: Enabling 2FA will immediately prompt admin user for OTP code.</comment>
                </field>
                <field id="ip_whitelist" translate="label" type="textarea" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="90">
                    <label>IP Whitelist</label>
                    <comment><![CDATA[^ Enter each ip-address on <strong>New Line</strong>]]></comment>
                </field>
            </group>
        </section>
    </system>
</config>
