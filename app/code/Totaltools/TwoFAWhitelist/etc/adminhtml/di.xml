<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="\Magento\TwoFactorAuth\Observer\ControllerActionPredispatch"
				type="\Totaltools\TwoFAWhitelist\Observer\TwoFAControllerActionPredispatch"/>
    <type name="Totaltools\TwoFAWhitelist\Observer\TwoFAControllerActionPredispatch">
        <arguments>
            <argument name="remoteAddress"  xsi:type="object">Magento\Framework\HTTP\PhpEnvironment\RemoteAddress\Proxy</argument>
        </arguments>
    </type>
    <type name="Magento\TwoFactorAuth\Model\TfaSession">
        <plugin name="bypassTwoFactorAuth" type="Totaltools\TwoFAWhitelist\Plugin\SwitchingTwoFactorAuth"/>
    </type>
</config>
