<?php

namespace Totaltools\CompanyCredit\Plugin\Model;

/**
 * Class PaymentMethodStatus
 * @package Totaltools\CompanyCredit\Plugin\Model
 */
class PaymentMethodStatus
{
    /**
     * @param \Magento\CompanyCredit\Model\PaymentMethodStatus $subject
     * @param $result
     * @return bool
     */
    public function afterIsEnabled(
        \Magento\CompanyCredit\Model\PaymentMethodStatus $subject,
        $result
    ) {
        return false;
    }
}
