<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_CatalogInventory
 */

namespace Totaltools\CatalogInventory\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\Loyalty\Model\Update as LoyaltyUpdate;

class RebuildStockStatusCommand extends Command
{

    const NAME        = 'totaltools:cataloginventory:rebuild-stock-status-bundle';
    const DESCRIPTION = 'Rebuild stock status of bundle products';

    const MESSAGE_SUCCESS = 'Rebuild bundle product id %s complete!';
    const MESSAGE_ERROR   = 'Error: %s';

    const INPUT_KEY_BUNDLE_ID = 'entity_id';

    /**
     * stock index
     * @var LoyaltyUpdate
     */
    private $stockIndex;

    private $productCollectionFactory;

    /**
     * UpdateCustomerCommand constructor
     *
     * @param LoyaltyUpdate $loyaltyUpdate
     */
    public function __construct(
        \Magento\CatalogInventory\Api\StockIndexInterface $stockIndex,
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory
    ) {
        parent::__construct();
        $this->stockIndex = $stockIndex;
        $this->productCollectionFactory = $productCollectionFactory;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure ()
    {
        $this
            ->setName(self::NAME)
            ->setDescription(self::DESCRIPTION)
            ->setDefinition($this->getInputList())
        ;
        parent::configure();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute (
        InputInterface $input,
        OutputInterface $output
    ) {
        try {
            $bundleIds = $input->getArgument(self::INPUT_KEY_BUNDLE_ID);
            if (count($bundleIds)) {
                foreach ($bundleIds as $bundleId) {
                    $this->stockIndex->rebuild($bundleId);
                    $output->writeln('<info>' . sprintf(self::MESSAGE_SUCCESS, $bundleId) . '</info>');
                }
            } else {
                //rebuild stock status of all bundle products
                $bundleCollection = $this->productCollectionFactory->create();
                $bundleCollection->addAttributeToSelect('entity_id')
                    ->addAttributeToFilter('type_id', \Magento\Catalog\Model\Product\Type::TYPE_BUNDLE);

                foreach ($bundleCollection as $bundle) {
                    $this->stockIndex->rebuild($bundle->getId());
                    $output->writeln('<info>' . sprintf(self::MESSAGE_SUCCESS, $bundle->getId()) . '</info>');
                }
            }

        } catch (\Exception $e) {
            $output->writeln('<error>' . sprintf(self::MESSAGE_ERROR, $e->getMessage()) . '</error>');
            debug_print_backtrace();
        }
    }

    /**
     * Get list of options and arguments for the command
     *
     * @return mixed[]
     */
    public function getInputList()
    {
        return [
            new InputArgument(
                self::INPUT_KEY_BUNDLE_ID,
                InputArgument::OPTIONAL | InputArgument::IS_ARRAY,
                'Space-separated list of bundle product entity id to rebuild status stock'
            ),
        ];
    }

}