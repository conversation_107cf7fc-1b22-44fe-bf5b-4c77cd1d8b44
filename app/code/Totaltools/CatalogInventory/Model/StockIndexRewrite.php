<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

namespace Totaltools\CatalogInventory\Model;

use Magento\Catalog\Model\Product\Type as ProductType;
use Magento\Catalog\Model\Product\Website as ProductWebsite;
use Magento\Catalog\Model\ProductFactory;
use Magento\CatalogInventory\Api\StockIndexInterface;
use Magento\CatalogInventory\Model\Spi\StockRegistryProviderInterface;
use Magento\Catalog\Api\ProductRepositoryInterface;

/**
 * Class StockIndex
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class StockIndexRewrite extends \Magento\CatalogInventory\Model\StockIndex
{

    /**
     * Process children stock status
     *
     * @param int $productId
     * @param int $websiteId
     * @param int $qty
     * @param int $status
     * @return $this
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    protected function processChildren(
        $productId,
        $websiteId,
        $qty = 0,
        $status = \Magento\CatalogInventory\Model\Stock\Status::STATUS_IN_STOCK
    ) {
        if ($status == \Magento\CatalogInventory\Model\Stock\Status::STATUS_OUT_OF_STOCK) {
            $this->getStockStatusResource()->saveProductStatus($productId, $status, $qty, $websiteId);
            return;
        }

        $statuses = [];
        $websitesWithStores = $this->getWebsitesWithDefaultStores($websiteId);

        foreach (array_keys($websitesWithStores) as $websiteId) {
            /* @var $website \Magento\Store\Model\Website */
            $statuses[$websiteId] = $status;
        }

        /** @var \Magento\Catalog\Model\Product $product */
        $product = $this->productRepository->getById($productId);
        $typeInstance = $product->getTypeInstance();

        $requiredChildrenIds = $typeInstance->getChildrenIds($productId, true);
        if ($requiredChildrenIds) {
            $childrenIds = [];
            foreach ($requiredChildrenIds as $groupedChildrenIds) {
                $childrenIds = array_merge($childrenIds, $groupedChildrenIds);
            }
            $childrenWebsites = $this->productWebsite->getWebsites($childrenIds);
            foreach ($websitesWithStores as $websiteId => $storeId) {
                $childrenStatus = $this->getStockStatusResource()->getProductStatus($childrenIds, $storeId);
                $childrenStock = $this->getStockStatusResource()->getProductsStockStatuses($childrenIds, $websiteId);
                $websiteStatus = $statuses[$websiteId];
                foreach ($requiredChildrenIds as $groupedChildrenIds) {
                    $optionStatus = false;
                    foreach ($groupedChildrenIds as $childId) {
                        if (isset($childrenStatus[$childId])
                            && isset($childrenWebsites[$childId])
                            && isset($childrenWebsites[$childId][$websiteId])
                            && $childrenStatus[$childId] == \Magento\Catalog\Model\Product\Attribute\Source\Status::STATUS_ENABLED
                            && isset($childrenStock[$childId])
                            && $childrenStock[$childId] == \Magento\CatalogInventory\Model\Stock\Status::STATUS_IN_STOCK
                        ) {
                            $optionStatus = true;
                        }
                    }
                    $websiteStatus = $websiteStatus && $optionStatus;
                }
                $statuses[$websiteId] = (int)$websiteStatus;
            }
        }
        foreach ($statuses as $websiteId => $websiteStatus) {
            $this->getStockStatusResource()->saveProductStatus($productId, $websiteStatus, $qty, $websiteId);
        }
    }

}
