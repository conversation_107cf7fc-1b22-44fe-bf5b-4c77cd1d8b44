<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\CatalogInventory\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\CatalogInventory\Api\StockConfigurationInterface;
use Magento\CatalogInventory\Api\StockIndexInterface;
use Magento\CatalogInventory\Api\StockItemRepositoryInterface;
use Magento\CatalogInventory\Api\StockRegistryInterface;
use Magento\Framework\Event\Observer as EventObserver;

class SaveInventoryDataObserverRewrite extends \Magento\CatalogInventory\Observer\SaveInventoryDataObserver
{

    /**
     * Prepare stock item data for save
     *
     * @param \Magento\Catalog\Model\Product $product
     * @return $this
     */
    protected function saveStockItemData($product)
    {
        $stockItemData = $product->getStockData();
        $stockItemData['product_id'] = $product->getId();

        if (!isset($stockItemData['website_id'])) {
            $stockItemData['website_id'] = $this->stockConfiguration->getDefaultScopeId();
        }
        $stockItemData['stock_id'] = $this->stockRegistry->getStock($stockItemData['website_id'])->getStockId();

        foreach ($this->paramListToCheck as $dataKey => $configPath) {
            if (null !== $product->getData($configPath['item']) && null === $product->getData($configPath['config'])) {
                $stockItemData[$dataKey] = false;
            }
        }

        $originalQty = $product->getData('stock_data/original_inventory_qty');
        if ($originalQty && (float) $originalQty > 0) {
            $stockItemData['qty_correction'] = ($stockItemData['qty'] ?? 0) - $originalQty;
        }

        // todo resolve issue with builder and identity field name
        $stockItem = $this->stockRegistry->getStockItem($stockItemData['product_id'], $stockItemData['website_id']);

        $stockItem->addData($stockItemData);
        $this->stockItemRepository->save($stockItem);
        /**
         * TOT0001-950: rebuild stock index after saving product,
         * update stock_status on table cataloginventory_stock_status
         */
        $this->stockIndex->rebuild($product->getId());
        return $this;
    }
}
