<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\CatalogInventory\Observer\SaveInventoryDataObserver;

use Magento\Framework\Event\Observer as EventObserver;

/**
 * Product View block rewrite
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class Plugin
{
    public function beforeExecute(
        \Magento\CatalogInventory\Observer\SaveInventoryDataObserver $inventoryDataObserver,
        EventObserver $observer)
    {
        $product = $observer->getEvent()->getProduct();
        if ($product->getAttributeText('stock_availability_code') == 'OD')
        {
            $stockData = $product->getStockData();
            $stockData['backorders'] = 1;
            $stockData['use_config_backorders'] = 0;
            $product->setStockData($stockData);
        }
    }
}
