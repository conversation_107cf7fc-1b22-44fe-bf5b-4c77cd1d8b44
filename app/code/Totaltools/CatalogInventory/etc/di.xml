<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogInventory\Observer\SaveInventoryDataObserver">
        <plugin name="auto-backorder-product" type="Totaltools\CatalogInventory\Observer\SaveInventoryDataObserver\Plugin" />
    </type>

    <preference for="Magento\CatalogInventory\Api\StockIndexInterface" type="Totaltools\CatalogInventory\Model\StockIndexRewrite"/>
    <preference for="Magento\CatalogInventory\Model\ResourceModel\Stock\Status" type="Totaltools\CatalogInventory\Model\ResourceModel\Stock\StatusRewrite"/>
    <preference for="Magento\CatalogInventory\Observer\SaveInventoryDataObserver" type="Totaltools\CatalogInventory\Observer\SaveInventoryDataObserverRewrite"/>

    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="rebuildStockStatusBundleProducts" xsi:type="object">Totaltools\CatalogInventory\Command\RebuildStockStatusCommand</item>
            </argument>
        </arguments>
    </type>
</config>