<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_ShippingRule
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\ShippingRule\Model;

use Magento\Framework\Model\AbstractModel;
use Totaltools\ShippingRule\Api\RuleRepositoryInterface;
use Magento\Framework\Exception\StateException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\CouldNotSaveException;

/**
 * Class RuleRepository
 * @package Totaltools\ShippingRule\Model
 */
class RuleRepository implements RuleRepositoryInterface
{
    /**
     * @var \Totaltools\ShippingRule\Model\ResourceModel\Rule
     */
    protected $_ruleResource;

    /**
     * @var RuleFactory
     */
    protected $_ruleFactory;


    public function __construct(
        \Totaltools\ShippingRule\Model\ResourceModel\Rule $_ruleResource,
        RuleFactory $ruleFactory
    )
    {
        $this->_ruleResource = $_ruleResource;
        $this->_ruleFactory = $ruleFactory;
    }

    /**
     * @param \Totaltools\ShippingRule\Api\Data\RuleInterface $shippingRule
     * @return bool|mixed
     * @throws StateException
     */
    public function delete(\Totaltools\ShippingRule\Api\Data\RuleInterface $shippingRule)
    {
        try {
            /**
             * @var AbstractModel $shippingRule
             */
            $this->_ruleResource->delete($shippingRule);
        } catch (\Exception $e) {
            throw new StateException(
                __(
                    'Cannot delete shipping rule with id %1',
                    $shippingRule->getId()
                ),
                $e
            );
        }

        return true;
    }

    /**
     * @param $shippingRuleId
     * @return mixed|Rule
     * @throws NoSuchEntityException
     */
    public function get($shippingRuleId)
    {
        /**
         * @var Rule $ruleModel
         */
        $ruleModel = $this->_ruleFactory->create();
        $ruleModel->load($shippingRuleId);

        if (!$ruleModel->getId()) {
            throw NoSuchEntityException::singleField('rule_id', $shippingRuleId);
        }

        return $ruleModel;
    }

    /**
     * @param \Totaltools\ShippingRule\Api\Data\RuleInterface $shippingRule
     * @return mixed|void
     * @throws CouldNotSaveException
     */
    public function save(\Totaltools\ShippingRule\Api\Data\RuleInterface $shippingRule)
    {
        try {
            /**
             * @var AbstractModel $shippingRule
             */
            $this->_ruleResource->save($shippingRule);
        } catch (\Exception $e) {
            throw new CouldNotSaveException(
                __(
                    'Could not save shipping rule: %1',
                    $e->getMessage()
                ),
                $e
            );
        }
    }
}