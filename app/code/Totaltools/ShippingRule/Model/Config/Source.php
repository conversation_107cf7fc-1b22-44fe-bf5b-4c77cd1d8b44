<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_ShippingRule
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\ShippingRule\Model\Config;

/**
 * System Config Source
 */
abstract class Source
{
    /**
     * The array of options in the configuration item.
     *
     * This array's keys are the values used in the database etc. and the
     * values of this array are used as labels on the frontend.
     *
     * @var array
     */
    protected $_options;

    public function __construct()
    {
        $this->_setupOptions();
    }

    /**
     * Sets up the $_options array with the correct values.
     *
     * This function is called in the constructor.
     *
     * @return \Totaltools\ShippingRule\Model\Config\Source
     */
    protected abstract function _setupOptions();

    /**
     * Gets all the options in the key => value type array.
     *
     * @return array
     */
    public function getOptions($please_select = false)
    {
        $options = $this->_options;

        if ($please_select) {
            $options = [null => __('--Please Select--')] + $options;
        }

        return $options;
    }

    /**
     * Converts the options into a format suitable for use in the admin area.
     *
     * @return array
     */
    public function toOptionArray()
    {
        return $this->_toOptionArray($this->_options);
    }

    protected function _toOptionArray($input)
    {
        $array = [];

        foreach ($input as $key => $value) {
            $array[] = [
                'value' => $key,
                'label' => $value,
            ];
        }

        return $array;
    }

    /**
     * Looks up an option by key and gets the label.
     *
     * @param mixed $value
     *
     * @return mixed
     */
    public function getOptionLabel($value)
    {
        if (array_key_exists($value, $this->_options)) {
            return $this->_options[$value];
        }
        return null;
    }
}
