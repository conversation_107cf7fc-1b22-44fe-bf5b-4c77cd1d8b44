<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_ShippingRule
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\ShippingRule\Model;

use Magento\Quote\Model\Quote\Address;
use Magento\Rule\Model\AbstractModel;
use Totaltools\ShippingRule\Api\Data\RuleInterface;

/**
 * Class Rule
 *
 * @package Totaltools\ShippingRule\Model
 */
class Rule extends AbstractModel implements RuleInterface
{
    /**
     * Prefix of model events names.
     *
     * @var string
     */
    protected $_eventPrefix = 'shippingrules_rules';

    /**
     * Parameter name in event.
     *
     * In observe method you can use $observer->getEvent()->getRule() in this case
     *
     * @var string
     */
    protected $_eventObject = 'shippingrules_rules';

    /**
     * Name of object id field
     *
     * @var string
     */
    protected $_idFieldName = 'rule_id';

    /**
     * @var \Magento\SalesRule\Model\Rule\Condition\CombineFactory
     */
    protected $_condCombineFactory;

    /**
     * Rule Product Combine Factory.
     *
     * @var \Magento\SalesRule\Model\Rule\Condition\Product\CombineFactory
     */
    protected $_condProdCombineF;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_session;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $_logger;

    /**
     * @var \Magento\Checkout\Model\Cart
     */
    protected $_cart;

    /**
     * @var \Magento\Catalog\Model\ProductFactory
     */
    protected $_productFactory;

    /**
     * @var \Totaltools\ShippingRule\Helper\Data
     */
    protected $_helper;

    /**
     * @var \Magento\Backend\Model\Session\Quote
     */
    protected $_backendSession;

    /**
     * @var \Magento\Framework\App\State
     */
    protected $_appState;

    /**
     * Store already validated addresses and validation results
     *
     * @var array
     */
    protected $validatedAddresses = [];

    /**
     * Rule constructor.
     * @param \Magento\SalesRule\Model\Rule\Condition\CombineFactory $condCombineFactory
     * @param \Magento\SalesRule\Model\Rule\Condition\Product\CombineFactory $condProdCombineF
     * @param \Magento\Checkout\Model\Session $session
     * @param \Magento\Checkout\Model\Cart $cart
     * @param \Magento\Catalog\Model\ProductFactory $productFactory
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Data\FormFactory $formFactory
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate
     * @param \Totaltools\ShippingRule\Helper\Data $helper
     * @param \Magento\Backend\Model\Session\Quote $backendSession
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\SalesRule\Model\Rule\Condition\CombineFactory $condCombineFactory,
        \Magento\SalesRule\Model\Rule\Condition\Product\CombineFactory $condProdCombineF,
        \Magento\Checkout\Model\Session $session,
        \Magento\Checkout\Model\Cart $cart,
        \Magento\Catalog\Model\ProductFactory $productFactory,
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\FormFactory $formFactory,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $localeDate,
        \Totaltools\ShippingRule\Helper\Data $helper,
        \Magento\Backend\Model\Session\Quote $backendSession,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {
        $this->_condCombineFactory = $condCombineFactory;
        $this->_condProdCombineF = $condProdCombineF;
        $this->_session = $session;
        $this->_logger = $context->getLogger();
        $this->_cart = $cart;
        $this->_productFactory = $productFactory;
        $this->_helper = $helper;
        $this->_backendSession = $backendSession;
        $this->_appState = $context->getAppState();
        parent::__construct($context, $registry, $formFactory, $localeDate, $resource, $resourceCollection, $data);
    }

    /**
     * Set resource model and Id field name.
     *
     * @return void
     */
    protected function _construct()
    {
        parent::_construct();

        $this->_init('Totaltools\ShippingRule\Model\ResourceModel\Rule');
        $this->setIdFieldName('rule_id');
    }

    /**
     * Get rule condition combine model instance.
     *
     * @return \Magento\SalesRule\Model\Rule\Condition\Combine
     */
    public function getConditionsInstance()
    {
        return $this->_condCombineFactory->create();
    }

    /**
     * Get rule condition product combine model instance.
     *
     * @return \Magento\SalesRule\Model\Rule\Condition\Product\Combine
     */
    public function getActionsInstance()
    {
        return $this->_condProdCombineF->create();
    }

    /**
     * Check cached validation result for specific address.
     *
     * @param Address $address
     *
     * @return bool
     */
    public function hasIsValidForAddress($address)
    {
        $addressId = $this->_getAddressId($address);

        return isset($this->validatedAddresses[$addressId]) ? true : false;
    }

    /**
     * Set validation result for specific address to results cache.
     *
     * @param Address $address
     * @param bool $validationResult
     *
     * @return $this
     */
    public function setIsValidForAddress($address, $validationResult)
    {
        $addressId = $this->_getAddressId($address);
        $this->validatedAddresses[$addressId] = $validationResult;

        return $this;
    }

    /**
     * Get cached validation result for specific address.
     *
     * @param Address $address
     *
     * @return bool
     *
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function getIsValidForAddress($address)
    {
        $addressId = $this->_getAddressId($address);

        return isset($this->validatedAddresses[$addressId]) ? $this->validatedAddresses[$addressId] : false;
    }

    /**
     * Return id for address.
     *
     * @param Address $address
     *
     * @return string
     */
    private function _getAddressId($address)
    {
        if ($address instanceof Address) {
            return $address->getId();
        }

        return $address;
    }


    /**
     * Validates current date against the configured range (from/to)
     * of this rule
     *
     * @return bool
     */
    public function validateDate()
    {
        $current_date = strtotime(date('Y-m-d'));

        if ($fromDate = $this->getData('from_date')) {
            $from = strtotime($fromDate);

            if ($current_date < $from) {
                return false;
            }
        }

        if ($toDate = $this->getData('to_date')) {
            $to = strtotime($toDate);

            if ($current_date>$to) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if rule satisfies current conditions
     *
     * @param $object
     *
     * @return boolean
     */
    public function isValid($object)
    {
        if (!$this->getAttributeSetId()) {
            return true;
        }

        $salesQuote = null;

        if ($this->_helper->isAdmin($this->_appState)) {
            $salesQuote = $this->_backendSession->getQuote();
        } else {
            $salesQuote = $this->_session->getQuote();
        }

        $items = $salesQuote->getAllVisibleItems();

        foreach ($items as $_key => $_item) {
            $product = $this->_productFactory->create();
            $product = $product->loadByAttribute('sku', $_item->getSku());

            if ($this->getAttributeSetId() != $product->getAttributeSetId()) {

                return false;
            }
        }

        return true;
    }

    /**
     * Does this rule return dynamic carrier quote?
     *
     * @return boolean
     */
    public function isDynamic()
    {
        if ($this->getActionRateType() == \Totaltools\ShippingRule\Model\Config\Source\Rule\Type::DYNAMIC) {

            return true;
        }

        return false;
    }

    /**
     * @return int
     */
    public function getId()
    {
        return $this->_getData($this->_idFieldName);
    }

    /**
     * Identifier setter
     *
     * @param int $value
     * @return $this
     */
    public function setId($value)
    {
        $this->setData($this->_idFieldName, $value);

        return $this;
    }
}