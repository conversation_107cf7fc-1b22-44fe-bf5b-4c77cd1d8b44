<?php

namespace Totaltools\ShippingRule\Ui\Component\Listing\Column;

use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;
use Magento\Framework\UrlInterface;

/**
 * Class RuleActions
 *
 * @package Totaltools\ShippingRule\Ui\Component\Listing\Column
 */
class RuleActions extends Column
{
    /**
     * Edit Rule URL.
     */
    const SHIPPING_RULE_URL_PATH_EDIT = 'totaltools_shippingrule/rule/edit';

    /**
     * Delete Rule URL.
     */
    const SHIPPING_RULE_URL_PATH_DELETE = 'totaltools_shippingrule/rule/delete';

    /**
     * URL Builder.
     *
     * @var UrlInterface
     */
    protected $urlBuilder;

    /**
     * RuleActions constructor.
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param UrlInterface $urlBuilder
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        UrlInterface $urlBuilder,
        array $components = [],
        array $data = []
    )
    {
        $this->urlBuilder = $urlBuilder;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     *
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {

            foreach ($dataSource['data']['items'] as & $item) {
                $name = $this->getData('name');

                if (isset($item['rule_id'])) {
                    $item[$name]['edit'] = [
                        'href' => $this->urlBuilder->getUrl(self::SHIPPING_RULE_URL_PATH_EDIT, ['rule_id' => $item['rule_id']]),
                        'label' => __('Edit')
                    ];
                    $item[$name]['delete'] = [
                        'href' => $this->urlBuilder->getUrl(self::SHIPPING_RULE_URL_PATH_DELETE, ['rule_id' => $item['rule_id']]),
                        'label' => __('Delete')
                    ];
                }
            }
        }

        return $dataSource;
    }
}