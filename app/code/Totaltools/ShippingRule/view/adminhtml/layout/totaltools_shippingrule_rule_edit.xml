<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_ShippingRule
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="admin-2columns-left" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="editor"/>
    <head>
        <script src="Totaltools_ShippingRule/js/rule/shipping-rule.js" />
        <css src="Magento_Catalog::web/catalog/category-selector.css" />
    </head>
    <body>
        <referenceContainer name="content">
            <block class="Totaltools\ShippingRule\Block\Adminhtml\Rule\Edit" name="shippingrule_edit" />
        </referenceContainer>
        <referenceContainer name="left">
            <block class="Totaltools\ShippingRule\Block\Adminhtml\Rule\Edit\Tabs" name="rule_edit_tabs">
                <block class="Totaltools\ShippingRule\Block\Adminhtml\Rule\Edit\Tabs\General" name="rule_edit_tab_general" />
                <block class="Totaltools\ShippingRule\Block\Adminhtml\Rule\Edit\Tabs\Conditions" name="rule_edit_tab_conditions"/>
                <block class="Totaltools\ShippingRule\Block\Adminhtml\Rule\Edit\Tabs\Action" name="rule_edit_tab_action"/>
            </block>
        </referenceContainer>
    </body>
</page>