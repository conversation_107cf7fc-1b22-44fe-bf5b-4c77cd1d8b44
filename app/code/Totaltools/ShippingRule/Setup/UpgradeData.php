<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_ShippingRule
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\ShippingRule\Setup;

use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use \Magento\Framework\App\State;

/**
 * Class UpgradeData
 *
 * @package Totaltools\ShippingRule\Setup
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var State
     */
    protected $appState;

    /**
     * @var \Totaltools\ShippingRule\Model\RuleFactory
     */
    protected $_ruleFactory;

    /**
     * UpgradeData constructor.
     *
     * @param State $appState
     * @param \Totaltools\ShippingRule\Model\RuleFactory $ruleFactory
     */
    public function __construct(
        \Magento\Framework\App\State $appState,
        \Totaltools\ShippingRule\Model\RuleFactory $ruleFactory
    ) {
        $this->appState = $appState;
        $this->_ruleFactory = $ruleFactory;
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface $context
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function upgrade(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $this->appState->setAreaCode('adminhtml');

        if (version_compare($context->getVersion(), '1.0.1', '<')) {

            foreach ($this->_getRulesData() as $index => $rulesData) {
                $model = $this->_ruleFactory->create();
                $model->setData($rulesData);
                $model->save();
            }
        }
    }

    /**
     * Returns data for Cheapest and Fastest shipping rules
     *
     * @return array
     */
    private function _getRulesData()
    {
        return [
            'cheapest' => [
                'name' => 'Cheapest',
                'is_active' => 1,
                'priority' => 9,
                'stop_other' => 0,
                'store_ids' => '1,2,3,4',
                'category_ids' => '',
                'consitions_serialized' => 'a:6:{s:4:"type";s:46:"Magento\SalesRule\Model\Rule\Condition\Combine";s:9:"attribute";N;s:8:"operator";N;s:5:"value";b:1;s:18:"is_value_processed";N;s:10:"aggregator";s:3:"all";}',
                'action_rate_type' => 3,
                'action_static_value' => 0.00,
                'action_dynamic_filter' => 3,
                'action_dynamic_adjustment_type' => 3,
                'action_dynamic_adjustment_value' => 1.1,
                'action_dynamic_adjustment_roundup' => 1,
                'action_dynamic_show_carrier_name' => 0,
                'action_dynamic_show_carrier_method' => 0,
                'action_dynamic_show_carrier_time' => 0,
                'action_dynamic_label' => 'Cheapest',
                'servicetype' => 3,
                'attribute_set_id' => 0,
            ],
            'fastest' => [
                'name' => 'Fastest',
                'is_active' => 0,
                'priority' => 9,
                'stop_other' => 0,
                'store_ids' => '1,2,3,4',
                'category_ids' => '',
                'condition_serialized' => 'a:6:{s:4:"type";s:46:"Magento\SalesRule\Model\Rule\Condition\Combine";s:9:"attribute";N;s:8:"operator";N;s:5:"value";b:1;s:18:"is_value_processed";N;s:10:"aggregator";s:3:"all";}',
                'action_rate_type' => 3,
                'action_static_value' => 0.00,
                'action_dynamic_filter' => 2,
                'action_dynamic_adjustment_type' => 3,
                'action_dynamic_adjustment_value' => 1.1,
                'action_dynamic_adjustment_roundup' => 1,
                'action_dynamic_show_carrier_name' => 0,
                'action_dynamic_show_carrier_method' => 0,
                'action_dynamic_show_carrier_time'=> 0,
                'action_dynamic_label' => 'Fastest',
                'servicetype' => 3,
                'attribute_set_id' => 0,
            ],
        ];
    }
}