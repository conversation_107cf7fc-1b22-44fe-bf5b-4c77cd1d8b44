<?php

namespace Totaltools\ShippingRule\Setup;

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

class InstallSchema implements InstallSchemaInterface
{
    /**
     * {@inheritdoc}
     *
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        /**
         * Create table 'shipping_rule'
         */
        $ruleTable = $setup->getConnection()->newTable(
            $setup->getTable('shipping_rule')
        )->addColumn(
            'rule_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'Id'
        )->addColumn(
            'name',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => false, 'unique' => true, 'length' => 50],
            'Name'
        )->addColumn(
            'is_active',
            \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
            false,
            ['nullable' => false],
            'Active'
        )->addColumn(
            'from_date',
            \Magento\Framework\DB\Ddl\Table::TYPE_DATE,
            null,
            ['nullable' => true],
            'From Date'
        )->addColumn(
            'to_date',
            \Magento\Framework\DB\Ddl\Table::TYPE_DATE,
            null,
            ['nullable' => true],
            'To Date'
        )->addColumn(
            'priority',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            10,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Priority'
        )->addColumn(
            'stop_other',
            \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
            false,
            ['nullable' => false],
            'Stop other rules from processing'
        )->addColumn(
            'store_ids',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => false, 'length' => 255],
            'Stores'
        )->addColumn(
            'category_ids',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => false, 'length' => 255],
            'Categories'
        )->addColumn(
            'group_ids',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'length' => 255],
            'Groups'
        )->addColumn(
            'condition_time_type',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Condition time type'
        )->addColumn(
            'condition_time_value',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 8],
            'Condition time value'
        )->addColumn(
            'condition_time_operator',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Condition time operator'
        )->addColumn(
            'secondary_condition_time_type',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Secondary condition time type'
        )->addColumn(
            'secondary_condition_time_value',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 8],
            'Secondary condition time value'
        )->addColumn(
            'condition_day',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 8],
            'Condition day'
        )->addColumn(
            'conditions_serialized',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 65535],
            'Conditions serialized'
        )->addColumn(
            'action_rate_type',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Action rate type'
        )->addColumn(
            'action_rate_fee',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Action rate fee'
        )->addColumn(
            'action_static_value',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            '12,2',
            ['nullable' => false],
            'Static value'
        )->addColumn(
            'action_static_label',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 255],
            'Action static label'
        )->addColumn(
            'action_dynamic_carriers',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 65535],
            'Action dynamic carriers'
        )->addColumn(
            'action_dynamic_filter',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Action dynamic filter'
        )->addColumn(
            'action_dynamic_filter_auspost',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Action dynamic filter AusPost'
        )->addColumn(
            'action_dynamic_adjustment_type',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => true, 'default' => null],
            'Action dynamic adjustment type'
        )->addColumn(
            'action_dynamic_adjustment_value',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 15],
            'Action dynamic adjustment value'
        )->addColumn(
            'action_dynamic_adjustment_roundup',
            \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
            true,
            ['nullable' => false, 'default' => false],
            'Action dynamic adjustment round up to nearest whole number'
        )->addColumn(
            'action_dynamic_show_carrier_name',
            \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
            true,
            ['nullable' => false],
            'Action show dynamic carrier name'
        )->addColumn(
            'action_dynamic_show_carrier_method',
            \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
            true,
            ['nullable' => false],
            'Action show dynamic carrier method'
        )->addColumn(
            'action_dynamic_show_carrier_time',
            \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
            true,
            ['nullable' => false],
            'Action show dynamic carrier time'
        )->addColumn(
            'action_dynamic_label',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 255],
            'Action dynamic label'
        )->addColumn(
            'action_restrict_note',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 500],
            'Action restrict note'
        )->addColumn(
            'action_additional_charge_items',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            null,
            ['nullable' => true, 'default' => null, 'length' => 500],
            'Action additional charge items'
        )->addColumn(
            'servicetype',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            4,
            ['unsigned' => true, 'nullable' => false, 'default' => 3],
            'Service type'
        )->addColumn(
            'attribute_set_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            10,
            ['unsigned' => true, 'nullable' => true],
            'Attribute Set Id'
        );

        $setup->getConnection()->createTable($ruleTable);
        $setup->endSetup();
    }
}