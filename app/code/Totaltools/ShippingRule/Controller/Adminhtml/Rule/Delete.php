<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_ShippingRule
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\ShippingRule\Controller\Adminhtml\Rule;

/**
 * Class Delete
 *
 * @package Totaltools\ShippingRule\Controller\Adminhtml\Rule
 */
class Delete extends \Magento\Backend\App\Action
{
    /**
     * @var \Totaltools\ShippingRule\Api\RuleRepositoryInterface
     */
    protected $_ruleRepository;

    /**
     * @param \Magento\Backend\App\Action\Context $context
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Totaltools\ShippingRule\Api\RuleRepositoryInterface $ruleRepository
    )
    {
        parent::__construct($context);

        $this->_ruleRepository = $ruleRepository;
    }

    /**
     * {@inheritdoc}
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Totaltools_ShippingRule::shippingrule_delete');
    }

    /**
     * Delete action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $id = $this->getRequest()->getParam('rule_id');
        $resultRedirect = $this->resultRedirectFactory->create();

        if ($id) {
            try {
                $rule = $this->_ruleRepository->get($id);
                $this->_ruleRepository->delete($rule);
                $this->messageManager->addSuccessMessage(__('The rule has been deleted'));
                return $resultRedirect->setPath('*/*/');
            } catch (\Exception $e) {
                $this->messageManager->addErrorMessage($e->getMessage());

                return $resultRedirect->setPath('*/*/edit', ['rule_id' => $id]);
            }
        }

        $this->messageManager->addErrorMessage(__('We can\'t find a rule to delete.'));

        return $resultRedirect->setPath('*/*/');
    }
}