<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_ShippingRule
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\ShippingRule\Controller\Adminhtml\Rule;

use Magento\Backend\App\Action;

/**
 * Class Save
 *
 * @package Totaltools\ShippingRule\Controller\Adminhtml\Rule
 */
class Save extends \Magento\Backend\App\Action
{
    /**
     * @var \Magento\Backend\Model\Session
     */
    protected $_session;

    /**
     * @var \Totaltools\ShippingRule\Model\RuleFactory
     */
    protected $_ruleFactory;

    /**
     * @var \Totaltools\ShippingRule\Api\RuleRepositoryInterface
     */
    protected $_ruleRepository;

    /**
     * Construct.
     *
     * @param Action\Context $context
     */
    public function __construct(
        Action\Context $context,
        \Magento\Backend\Model\Session $session,
        \Totaltools\ShippingRule\Model\RuleFactory $ruleFactory,
        \Totaltools\ShippingRule\Api\RuleRepositoryInterface $ruleRepository
    )
    {
        parent::__construct($context);
        $this->_ruleRepository = $ruleRepository;
        $this->_session = $session;
        $this->_ruleFactory = $ruleFactory;
    }

    /**
     * {@inheritdoc}
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Totaltools_ShippingRule::shippingrule_save');
    }

    /**
     * Save action
     *
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();

        if (isset($data['store_ids'])) {
            $data['store_ids'] = implode(',', $data['store_ids']);
        } else {
            $data['store_ids'] = '';
        }

        $resultRedirect = $this->resultRedirectFactory->create();

        if ($data) {
            $model = $this->_ruleFactory->create();

            $id = $this->getRequest()->getParam('rule_id');

            if ($id) {
                $model = $this->_ruleRepository->get($id);
            }

            $model->setData($data);

            $this->_eventManager->dispatch(
                'totaltools_shippingrule_prepare_save',
                ['rule' => $model, 'request' => $this->getRequest()]
            );

            try {
                $this->_ruleRepository->save($model);
                $this->messageManager->addSuccess(__('You saved this Rule.'));

                $this->_session->setFormData(false);

                if ($this->getRequest()->getParam('back')) {

                    return $resultRedirect->setPath('*/*/edit', ['rule_id' => $model->getId(), '_current' => true]);
                }
                return $resultRedirect->setPath('*/*/');
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $this->messageManager->addError($e->getMessage());
            } catch (\RuntimeException $e) {
                $this->messageManager->addError($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addError($e->getMessage());
            }

            $this->_getSession()->setFormData($data);
            return $resultRedirect->setPath('*/*/edit', ['rule_id' => $this->getRequest()->getParam('rule_id')]);
        }
        return $resultRedirect->setPath('*/*/');
    }
}