<?xml version="1.0"?>
<!--
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_ShippingRule
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Totaltools\ShippingRule\Api\RuleRepositoryInterface" type="Totaltools\ShippingRule\Model\RuleRepository" />
    <virtualType name="RuleGridDataProvider" type="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
        <arguments>
            <argument name="collection" xsi:type="object" shared="false">Totaltools\ShippingRule\Model\Resource\Rule\Grid\Collection</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\ShippingRule\Model\ResourceModel\Rule\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">shipping_rule</argument>
            <argument name="resourceModel" xsi:type="string">Totaltools\ShippingRule\Model\ResourceModel\Rule</argument>
        </arguments>
    </virtualType>
    <type name="Totaltools\ShippingRule\Model\Resource\Rule\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">shipping_rule</argument>
            <argument name="eventPrefix" xsi:type="string">shippingrules_rules</argument>
            <argument name="eventObject" xsi:type="string">shippingrules_rules</argument>
            <argument name="resourceModel" xsi:type="string">Totaltools\ShippingRule\Model\ResourceModel\Rule</argument>
        </arguments>
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="shippingrule_listing_data_source" xsi:type="string">Totaltools\ShippingRule\Model\ResourceModel\Rule\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
</config>
