<?php
/**
 * @category  Totaltools
 * @package Totaltools_Order
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      https://www.totaltools.com.au
 */

namespace Totaltools\Sales\Plugin\Model;

use Psr\Log\LoggerInterface;

 /**
 * Order class to intercept \Magento\Sales\Model\Order
 */
class Order
{

    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    protected $timezone;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    public function __construct(
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezone,
        LoggerInterface $logger
    )
    {
        $this->timezone = $timezone;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Sales\Model\Order $subject
     * return date format Y/m/d
     */
    public function aroundGetCreatedAtFormatted(\Magento\Sales\Model\Order $subject,
    \Closure $proceed, $format)
    {
        if ($format == 10)
        {
            return $this->timezone->date(new \DateTime($subject->getCreatedAt()))->format('d/m/Y');
        }
        else
        {
            return $proceed($format);
        }

    }

    /**
     * Get customer name
     *
     * @param \Magento\Sales\Model\Order $subject
     * @param Closure $proceed
     * 
     * @return string
    */
    public function aroundGetCustomerName(
        \Magento\Sales\Model\Order $subject,
    \Closure $proceed
    ) {
        $customerName = '';
        if (null === $subject->getCustomerFirstname()) {
            $customerName  .= $subject->getBillingAddress()->getFirstname() . ' ';
            $customerName  .= $subject->getBillingAddress()->getLastname() . ' ';
            return $customerName;
        } else {
            return $proceed();
        }
    }

    
     /**
     * Return customer_group_id
     *
     * @return int|null
     */
    public function afterGetCustomerGroupId(
          \Magento\Sales\Model\Order $subject,
          $result
    ) {
        return $result??0;
    }

    public function beforePlace(\Magento\Sales\Model\Order $subject)
    {
        $this->logger->info('Before Place Plugin - Order Details:', [
            'increment_id' => $subject->getIncrementId(),
            'customer_email' => $subject->getCustomerEmail()
        ]);
    }
}
