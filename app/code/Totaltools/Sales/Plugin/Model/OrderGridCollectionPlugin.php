<?php
/**
 * @category  Totaltools
 * @package   Totaltools_
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Sales\Plugin\Model;

class OrderGridCollectionPlugin
{
    /**
     * @param Collection $subject
     * @param bool $printQuery
     * @param bool $logQuery
     */
    public function beforeLoad(\Magento\Sales\Model\ResourceModel\Order\Grid\Collection $subject, $printQuery = false, $logQuery = false)
    {
        if (!$subject->isLoaded()) {
            if ($where = $subject->getSelect()->getPart(\Zend_Db_Select::WHERE)) {
                foreach ($where as $key => $condition) {
                    if (strpos($condition, 'created_at')) {
                        $new_condition = str_replace("`created_at`", "`main_table`.created_at", $condition);
                        $where[$key] = $new_condition;
                    }
                }
                $subject->getSelect()->setPart('where', $where);
            }
        }

        return [$printQuery, $logQuery];
    }
}
