<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Sales
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2020 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Sales\Plugin\Sales\Controller\Adminhtml\Order;

class Email extends \Magento\Sales\Controller\Adminhtml\Order\Email
{

    public function aroundExecute(
        \Magento\Sales\Controller\Adminhtml\Order\Email $subject,
        \Closure $proceed
    ) {
        //Your plugin code
        $result = $proceed();
        $order = $subject->_coreRegistry->registry('sales_order');
        if ($order) {
            $order->addStatusHistoryComment('Order Email Send to Customer Manually.');
            $order->save();
        }
        return $result;
    }
}
