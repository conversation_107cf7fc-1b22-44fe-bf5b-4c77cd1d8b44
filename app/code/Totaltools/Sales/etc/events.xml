<?xml version="1.0"?>
<!--
/**
 * @category    Totaltools
 * @package     Totaltools_Sales
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="email_order_set_template_vars_before">
        <observer name="order_custom_email_vars" instance="Totaltools\Sales\Observer\CustomOrderEmailVars"/>
    </event>
    <event name="email_invoice_set_template_vars_before">
        <observer name="invoice_custom_email_vars" instance="Totaltools\Sales\Observer\CustomOrderEmailVars"/>
    </event>
    <event name="email_shipment_set_template_vars_before">
        <observer name="shipment_custom_email_vars" instance="Totaltools\Sales\Observer\CustomOrderEmailVars"/>
    </event>
    <event name="email_shipment_comment_set_template_vars_before">
        <observer name="shipment_comment_custom_email_vars" instance="Totaltools\Sales\Observer\CustomOrderEmailVars"/>
    </event>
    <event name="email_creditmemo_set_template_vars_before">
        <observer name="creditmemo_custom_email_vars" instance="Totaltools\Sales\Observer\CustomOrderEmailVars"/>
    </event>
    <event name="checkout_submit_all_after">
        <observer name="check_for_empty_shipping_address" instance="Totaltools\Sales\Observer\ShippingAddress" />
    </event>
</config>
