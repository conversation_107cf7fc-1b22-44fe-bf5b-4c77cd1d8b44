<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Sales\Model\OrderNotifier" type="Totaltools\Sales\Model\OrderNotifier"/>
    <preference for="Magento\Sales\Model\Order\Email\Sender\CreditmemoSender" type="Totaltools\Sales\Model\Order\Email\Sender\CreditmemoSender"/>
    <preference for="Magento\Sales\Block\Order\Totals" type="Totaltools\Sales\Block\Order\Totals"/>
    <preference for="Magento\Sales\Model\Order" type="Totaltools\Sales\Model\Order"/>
    <type name="Magento\Backend\Block\Widget\Button\Toolbar">
        <plugin name="totaltools_sales_hide_email_button" type="Totaltools\Sales\Plugin\Block\Widget\Button\Toolbar" />
    </type>
    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="sales_order_grid_data_source" xsi:type="string">Totaltools\Sales\Model\ResourceModel\Order\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Totaltools\Sales\Model\ResourceModel\Order\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">sales_order_grid</argument>
            <argument name="resourceModel" xsi:type="string">Magento\Sales\Model\ResourceModel\Order</argument>
        </arguments>
    </type>
    <type name="Magento\Sales\Model\Order">
        <plugin name="totaltools_sales_format_date" type="Totaltools\Sales\Plugin\Model\Order" />
    </type>
    <type name="Magento\Sales\Model\ResourceModel\Order\Grid\Collection">
        <plugin name="orderGridCollectionPlugin" type="Totaltools\Sales\Plugin\Model\OrderGridCollectionPlugin" />
    </type>
</config>
