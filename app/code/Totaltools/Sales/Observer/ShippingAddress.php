<?php

namespace Totaltools\Sales\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\ResourceModel\Order as OrderResourceModel;

class ShippingAddress implements ObserverInterface
{
    /**
     * @var OrderResourceModel
     */
    protected $orderResourceModel;

    /**
     * ShippingAddress constructor.
     * @param OrderResourceModel $orderResourceModel
     */
    public function __construct(
        OrderResourceModel $orderResourceModel
    ) {
        $this->orderResourceModel = $orderResourceModel;
    }

    /**
     * @inheritdoc
     */
    public function execute(Observer $observer)
    {
        /** @var $order Order */
        $order = $observer->getEvent()->getOrder();
        if ($order) {
            $shippingAddress = $order->getShippingAddress();
            if (empty($shippingAddress) && !$order->getIsVirtual()) {
                $order->hold();
                $order->addCommentToStatusHistory("Shipping Address is missing: after order place");
                $this->orderResourceModel->save($order);
            }
        }
        return true;
    }

    
}
