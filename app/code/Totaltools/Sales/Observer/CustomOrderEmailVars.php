<?php

namespace Totaltools\Sales\Observer;

/**
 * @category    Totaltools
 * @package     Totaltools_Sales
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;
use Magento\Framework\DataObject;
use Magento\Customer\Model\Address\Config as AddressConfig;
use Totaltools\Storelocator\Model\Store;
use Totaltools\Storelocator\Model\StoreRepository;
use Totaltools\Pronto\Helper\Data;

class CustomOrderEmailVars implements ObserverInterface
{
    /**
     * Click and collect method name
     */
    const STORE_PICKUP_METHOD = 'shippitcc_shippitcc';

    /**
     * Express Delivery method name
     */
    const EXPRESS_DELIVERY = 'shippit_Express';

    /**
     * OnDemand Delivery method name
     */
    const ONDEMAND_DELIVERY = 'shippit_ondemand';

    /**
     * Standard Delivery method name
     */
    const STANDARD_DELIVERY = 'shippit_Standard';

    /**
     * Free Delivery method name
     */
    const FREE_DELIVERY = 'freeshipping';

    /**
     * @var StoreRepository
     */
    protected $storeRepository;

    /**
     * @var AddressConfig
     */
    protected $addressConfig;

    /**
     * @var Data
     */
    private $prontoHelper;

    /**
     * @param StoreRepository $storeRepository
     * @param AddressConfig $addressConfig
     * @param Data $prontoHelper
     */
    public function __construct(
        StoreRepository $storeRepository,
        AddressConfig $addressConfig,
        Data $prontoHelper
    ) {
        $this->storeRepository = $storeRepository;
        $this->addressConfig = $addressConfig;
        $this->prontoHelper = $prontoHelper;
    }

    /**
     * @inheritdoc
     */
    public function execute(Observer $observer)
    {
        /** @var DataObject $transportObject */
        $transportObject = $observer->getData('transportObject');

        /** @var Order $order */
        $order = $transportObject->getOrder();
        if ($order->getId() && $order->getIsNotVirtual()) {
            $shippingMethod = $order->getShippingMethod();
            $isStorePickup = (bool) ($shippingMethod == self::STORE_PICKUP_METHOD);
            $isExpressDelivery = (bool) ($shippingMethod == self::EXPRESS_DELIVERY);
            $isOnDemandDelivery = (bool) ($shippingMethod == self::ONDEMAND_DELIVERY);
            $isPriorityDelivery = (bool) (str_contains($shippingMethod, 'Priority'));
            $isStandardDelivery = (bool) ($shippingMethod == self::STANDARD_DELIVERY);
            $isFreeDelivery = (bool) (str_contains($shippingMethod, self::FREE_DELIVERY));

            $transportObject->setData('is_store_pickup', $isStorePickup);
            $transportObject->setData('is_standard_delivery', $isStandardDelivery);
            $transportObject->setData('is_free_delivery', $isFreeDelivery);
            $transportObject->setData('is_express_delivery', $isExpressDelivery);
            $transportObject->setData('is_ondemand_delivery', $isOnDemandDelivery);
            $transportObject->setData('is_priority_delivery', $isPriorityDelivery);
            $transportObject->setData('is_express', $isExpressDelivery);
            $transportObject->setData('is_ondemand', $isOnDemandDelivery);
            $transportObject->setData('is_priority', $isPriorityDelivery);
            $transportObject->setData('is_ed', $isExpressDelivery);
            $transportObject->setData('is_od', $isOnDemandDelivery);
            $transportObject->setData('is_pd', $isPriorityDelivery);
            
            $storeLocatorId = (int) $order->getData('storelocator_id');
            if ($storeLocatorId > 0) {
                $store = $this->storeRepository->getById($storeLocatorId);
                $transportObject->setData('assignedStore', $store);
                if ($isStorePickup) {
                    $transportObject->setData('formattedShippingAddress', $this->getFormattedStoreAddress($store));
                }
            }
        }
        $transportObject->setData('custom_created_at_formatted', $order->getCreatedAtFormatted(10));
        if($order->getId()) {
            $guestCustomer = $order->getCustomerIsGuest();
            if(!$guestCustomer) {
                $customerId = $order->getCustomerId();
                $customerLoyaltyData = $this->prontoHelper->getCustomerLoyaltyData($customerId);
                $transportObject->setData('customerLoyaltyData', $customerLoyaltyData);
            }
        }
    }

    /**
     * @param Store $store
     * @return string|null
     */
    private function getFormattedStoreAddress($store)
    {
        if (!$store->getStorelocatorId()) {
            return null;
        }

        $storeAddress = [
            'firstname' => $store->getStoreName(),
            'street' => [$store->getAddress()],
            'city' => $store->getCity(),
            'region' => $store->getState(),
            'postcode' => $store->getZipcode(),
            'country_id' => $store->getCountryId(),
            'telephone' => $store->getPhone()
        ];

        $formatType = $this->addressConfig->getFormatByCode('html');
        if (!$formatType || !$formatType->getRenderer()) {
            return null;
        }

        return $formatType->getRenderer()->renderArray($storeAddress);
    }
}
