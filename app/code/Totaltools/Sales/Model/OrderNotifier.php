<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Sales\Model;
/**
 * Class Notifier
 * @package Totaltools\Sales\Model
 */
class OrderNotifier extends \Magento\Sales\Model\OrderNotifier
{
    /**
     * Notify user
     *
     * @param AbstractModel $model
     * @return bool
     * @throws \Magento\Framework\Exception\MailException
     */
    public function notify(\Magento\Sales\Model\AbstractModel $model)
    {
        try {
            /*TOT0000-28: always allow to resend order email on the backend*/
            $this->sender->send($model, true);
            if (!$model->getEmailSent()) {
                return false;
            }
            $historyItem = $this->historyCollectionFactory->create()
                ->getUnnotifiedForInstance($model);
            if ($historyItem) {
                $historyItem->setIsCustomerNotified(1);
                $historyItem->save();
            }
        } catch (\Magento\Framework\Exception\MailException $e) {
            $this->logger->critical($e);
            return false;
        }
        return true;
    }
}
