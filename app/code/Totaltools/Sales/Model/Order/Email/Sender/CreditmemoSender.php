<?php

namespace Totaltools\Sales\Model\Order\Email\Sender;

use Magento\Sales\Model\Order\Creditmemo;
use Magento\Sales\Model\Order\Email\Sender\CreditmemoSender as CoreCreditmemoSender;

class CreditmemoSender extends CoreCreditmemoSender
{

    public function send(Creditmemo $creditmemo, $forceSyncMode = false)
    {
        return parent::send($creditmemo, true); // Always send memo email synchronously
    }

}