<?php

namespace Totaltools\Sales\Model\ResourceModel\Order\Grid;

/**
 * @package Totaltools_Sales
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 */

/**
 * Order grid extended collection
 */
class Collection extends \Magento\Sales\Model\ResourceModel\Order\Grid\Collection
{
    /**
     * @inheritdoc
     */
    protected function _initSelect()
    {
        $salesOrderTable = $this->getTable('sales_order');
        $this->addFilterToMap('assigned_store_id', "{$salesOrderTable}.storelocator_id");

        parent::_initSelect();
    }

    /**
     * @inheritdoc
     */
    protected function _renderFiltersBefore()
    {
        $salesOrderTable = $this->getTable('sales_order');
        $storeLocatorTable = $this->getTable(\Magestore\Storelocator\Setup\InstallSchema::SCHEMA_STORE);

        if (!$this->isTableJoined($this->getSelect(), $salesOrderTable)) {
            $this->getSelect()
            ->joinLeft(
                $salesOrderTable,
                "main_table.entity_id = {$salesOrderTable}.entity_id",
                ['storelocator_id']
            );
        }

        $this->getSelect()
            ->joinLeft(
                $storeLocatorTable,
                "{$salesOrderTable}.storelocator_id = {$storeLocatorTable}.storelocator_id",
                ['storelocator_id', 'store_name']
            );

        parent::_renderFiltersBefore();
    }

    /**
     * Check if a specific table is already joined in the select query.
     *
     * @param \Magento\Framework\DB\Select $select
     * @param string $tableName
     * @return bool
     */
    protected function isTableJoined(\Magento\Framework\DB\Select $select, string $tableName): bool
    {
        // Get the "from" and "join" clauses from the query.
        $parts = $select->getPart(\Zend_Db_Select::FROM);

        // Iterate through the parts to check if the table is already joined.
        foreach ($parts as $alias => $data) {
            if ($data['tableName'] === $tableName) {
                return true; // Table is already joined.
            }
        }

        return false; // Table is not joined yet.
    }
}
