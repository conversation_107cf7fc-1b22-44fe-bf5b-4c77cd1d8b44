<?php
/**
 * Track
 *
 * @category  Totaltools
 * @package   Totaltools_Sales
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022(c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Sales\Controller\Order;

use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\ResultFactory;

class Track extends \Magento\Framework\App\Action\Action
{
    /**
     * @var \Magento\Framework\UrlFactory
     */
    private $urlFactory;

    /**
     * @var \Shippit\Shipping\Model\Sync\Order
     */
    private $syncOrder;

    /**
     * @var \Shippit\Shipping\Model\ResourceModel\Sync\Order
     */
    private $orderResource;

    /**
     * @var \Totaltools\Shippit\Helper\Data
     */
    private $helper;

    /**
     * @var \Magento\Sales\Api\Data\OrderInterface
     */
    private $order;

    /**
     * Track constructor.
     * @param \Shippit\Shipping\Model\Sync\Order $syncOrder
     * @param \Magento\Framework\UrlFactory $urlFactory
     * @param \Shippit\Shipping\Model\ResourceModel\Sync\Order $orderResource
     * @param \Magento\Sales\Api\Data\OrderInterface $order
     * @param \Totaltools\Shippit\Helper\Data $helper
     * @param Context $context
     */
    public function __construct(
        \Shippit\Shipping\Model\Sync\Order $syncOrder,
        \Magento\Framework\UrlFactory $urlFactory,
        \Shippit\Shipping\Model\ResourceModel\Sync\Order $orderResource,
        \Magento\Sales\Api\Data\OrderInterface $order,
        \Totaltools\Shippit\Helper\Data $helper,
        Context $context
    ) {
        parent::__construct($context);
        $this->urlFactory = $urlFactory->create();
        $this->syncOrder = $syncOrder;
        $this->orderResource = $orderResource;
        $this->order = $order;
        $this->helper = $helper;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $post = $this->getRequest()->getParams();
        $defaultUrl = $this->urlFactory->getUrl('*/*/tracking', ['_secure' => true]);
        if (!isset($post['order_id']) || !isset($post['email'])) {
            $this->messageManager->addErrorMessage('Both Fields are required');
            $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
            $resultRedirect->setUrl($defaultUrl);
            return $resultRedirect;
        }
        $order = $this->getOrderByIncrementId($post['order_id']);
        if (!is_null($order)) {
            if ($order->getCustomerEmail() == $post['email']) {
                $this->orderResource->load($this->syncOrder, $order->getEntityId(), 'order_id');
                $shippitTrackingNumber = $this->syncOrder->getTrackingNumber();
                if ($shippitTrackingNumber) {
                    $redirectUrl = $this->helper->getEnvironmentUrl(). $this->syncOrder->getTrackingNumber();
                    $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
                    $resultRedirect->setUrl($redirectUrl);
                    return $resultRedirect;
                } else {
                    $this->messageManager->addErrorMessage('No Shipping details found for order : '.$post['order_id']);
                }
            } else {
                $this->messageManager->addErrorMessage('Customer email from order doesn\'t match provided email' );
            }
        }
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setUrl($defaultUrl);
        return $resultRedirect;
    }

    /**
     * @param $incrementId
     * @return |null
     */
    public function getOrderByIncrementId($incrementId)
    {
        $order = $this->order->loadByIncrementId($incrementId);
        if ($order->getId()) {
           return $order;
        }
        $this->messageManager->addErrorMessage('Unable to find an order with this order number: '.$incrementId);
        return null;

    }
}