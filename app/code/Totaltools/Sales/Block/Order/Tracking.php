<?php
/**
 * Tracking
 *
 * @category  Totaltools
 * @package   Totaltools_Sales
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022(c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Sales\Block\Order;

use Magento\Customer\Model\Session;


class Tracking extends \Magento\Framework\View\Element\Template
{

    private Session $session;

    /**
     * Constructor
     *
     * @param \Magento\Framework\View\Element\Template\Context  $context
     * @param Session $session
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        Session $session,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->session = $session;
    }

    /**
     * @return string
     */
    public function getPostActionUrl()
    {
        return $this->getUrl("sales/order/track");
    }

    /**
     * @return string
     */
    public function getCustomerEmail()
    {
        if ($this->session->isLoggedIn()) {
            return $this->session->getCustomer()->getEmail();
        }
        return "";
    }
}

