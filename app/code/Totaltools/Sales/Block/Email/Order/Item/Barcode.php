<?php

namespace Totaltools\Sales\Block\Email\Order\Item;

/**
 * @package     Totaltools_Sales
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

class Barcode extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Magento\Catalog\Model\ProductRepository
     */
    protected $productRepository;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\Catalog\Model\ProductRepository $productRepository
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Catalog\Model\ProductRepository $productRepository,
        array $data = []
    ) {
        $this->storeManager = $storeManager;
        $this->productRepository = $productRepository;
        parent::__construct($context, $data);
    }

    /**
     * @return string
     */
    public function getStoreUrl()
    {
        return $this->storeManager->getStore()->getBaseUrl();
    }

    /**
     * @return null|string
     */
    public function getBarcode()
    {
        $_product = $this->getProduct();
        return $_product instanceof \Magento\Catalog\Model\Product ? $_product->getData('barcode') : null;
    }

    /**
     * @return \Magento\Catalog\Model\Product
     */
    protected function getProduct()
    {
        /** @var \Magento\Sales\Model\Order\Item $orderItem */
        $orderItem = $this->getItem();
        return $this->productRepository->get($orderItem->getSku());
    }
}
