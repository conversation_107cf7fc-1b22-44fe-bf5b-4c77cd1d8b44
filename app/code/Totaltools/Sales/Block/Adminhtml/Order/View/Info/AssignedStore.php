<?php

namespace Totaltools\Sales\Block\Adminhtml\Order\View\Info;

/**
 * @package     Totaltools_Sales
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

use \Magento\Framework\Exception\NoSuchEntityException;

class AssignedStore extends \Magento\Sales\Block\Adminhtml\Order\AbstractOrder
{
    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @param \Magento\Backend\Block\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Sales\Helper\Admin $adminHelper
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Psr\Log\LoggerInterface $logger
     * @param array $data
     */
    public function __construct(
        \Magento\Backend\Block\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Sales\Helper\Admin $adminHelper,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Psr\Log\LoggerInterface $logger,
        array $data = []
    ) {
        $this->storeRepository = $storeRepository;
        $this->logger = $logger;

        parent::__construct($context, $registry, $adminHelper, $data);
    }

    /**
     * @return string|null
     * @throws NoSuchEntityException
     */
    public function getAssignedStoreName()
    {
        $storeId = $this->getOrder()->getData('storelocator_id');

        if ($storeId) {
            try {
                /** @var \Totaltools\Storelocator\Model\Store $store */
                $store = $this->storeRepository->getById($storeId);
                return $store->getStoreName();
            } catch (NoSuchEntityException $e) {
                $this->logger->warning($e->getMessage());
            }
        }

        return null;
    }
}
