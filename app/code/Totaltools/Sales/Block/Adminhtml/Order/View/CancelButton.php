<?php
namespace Totaltools\Sales\Block\Adminhtml\Order\View;

use Magento\Framework\View\Element\AbstractBlock;

class CancelButton extends AbstractBlock
{
    protected $context;

    public function __construct(
        \Magento\Backend\Block\Widget\Context $context
    ) {
        $this->context = $context;
        parent::__construct($context);
    }

    protected function getOrderId()
    {
        return $this->context->getRequest()->getParam('order_id');
    }

    public function getCancelUrls()
    {
        return $this->context->getUrlBuilder()->getUrl(
            'totaltools_sales/order/cancel',
            ['order_id' => $this->getOrderId()]
        );
    }
    
    public function getButtonData()
    {
        return [
            'label' => __('Cancel Order'),
            'on_click' => 'confirmSetLocation(\'' . __('Are you sure you want to cancel this order?') . '\', \'' . $this->getCancelUrl() . '\')',
            'class' => 'cancel primary',
            'sort_order' => 20,
        ];
    }

    public function getCancelUrl()
    {
        return 'totaltools_sales/order/cancel';
    }
}
