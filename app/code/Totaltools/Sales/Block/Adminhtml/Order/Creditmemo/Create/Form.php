<?php
/**
 * @category  Totaltools
 * @package   Totaltools_Sales
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */
namespace Totaltools\Sales\Block\Adminhtml\Order\Creditmemo\Create;

use Magento\Backend\Block\Template\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Registry;
use Magento\Sales\Helper\Admin;
use Totaltools\Pronto\Model\Request\Customer;

class Form extends \Magento\Sales\Block\Adminhtml\Order\Creditmemo\Create\Form
{
    /**
     * @var Customer
     */
    protected $prontoCustomer;

    /**
     * @var array|bool
     */
    protected $customerMember;

    /**
     * Form constructor.
     * @param Context $context
     * @param Registry $registry
     * @param Admin $adminHelper
     * @param Customer $pronto
     * @param array $data
     */
    public function __construct(
        Context $context,
        Registry $registry,
        Admin $adminHelper,
        Customer $pronto,
        array $data = []
    ) {
        parent::__construct($context, $registry, $adminHelper, $data);
        $this->prontoCustomer = $pronto;
    }

    /**
     * @param $customerEmail
     * @return bool|mixed
     * @throws LocalizedException
     */
    public function getLoyaltyId($customerEmail)
    {
        $customerData = $this->getProntoCustomer($customerEmail);
        if (!empty($customerData)) {
            return $customerData['LoyaltyCustomers']['LoyaltyCustomer']['LoyaltyID'];
        }
        return false;
    }

    /**
     * @param $email
     * @return array|bool
     * @throws LocalizedException
     */
    public function getProntoCustomer($email)
    {
        if (!$this->customerMember) {
            $this->customerMember = $this->prontoCustomer->getMember([
                'Parameters' => [
                    'EmailAddress' => $email,
                ]
            ]);
        }
        return $this->customerMember;
    }
}