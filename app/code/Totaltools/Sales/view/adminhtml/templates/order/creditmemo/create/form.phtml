<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php $_order = $block->getOrder(); ?>
<?php $rewardsPointBalance = $_order->getRewardPointsBalance() ?>
<?php if ($rewardsPointBalance > 0) { ?>
    <?php $loyaltyId = $block->getLoyaltyId($_order->getCustomerEmail()); ?>
    <div data-role="messages" id="messages">
        <div class="messages">
            <div class="message-notify">
                <div data-ui-id="messages-message-error"><?php echo __("This order is attached to loyalty ID: {$loyaltyId} Please revert point redemption in Pronto."); ?>
                </div>
            </div>
        </div>
    </div>
<?php } ?>