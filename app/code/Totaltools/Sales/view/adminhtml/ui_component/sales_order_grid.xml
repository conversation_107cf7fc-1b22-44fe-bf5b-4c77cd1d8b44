<?xml version="1.0" encoding="UTF-8"?>
<!--
 * @package Totaltools_Sales
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <listingToolbar name="listing_top">
        <filters name="listing_filters">
            <filterSelect name="assigned_store_id" provider="${ $.parentName }" component="Magento_Ui/js/form/element/ui-select" template="ui/grid/filters/elements/ui-select">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="filterOptions" xsi:type="boolean">true</item>
                    </item>
                </argument>
                <settings>
                    <options class="\Totaltools\Sales\Ui\Component\Listing\Column\AssignedStore\Options"/>
                    <label translate="true">Assigned Store</label>
                    <dataScope>assigned_store_id</dataScope>
                    <imports>
                        <link name="visible">ns = ${ $.ns }, componentType = column, index = ${ $.index }:visible</link>
                    </imports>
                </settings>
            </filterSelect>
        </filters>
    </listingToolbar>
    <columns name="sales_order_columns">
        <column name="store_name">
            <settings>
                <label translate="true">Assigned Store</label>
            </settings>
        </column>
    </columns>
</listing>