<?xml version="1.0" ?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
	<body>
		<attribute name="class" value="customer-account-login"/>
        <attribute name="class" value="sales-order-tracking"/>
		<referenceContainer name="content">
		 	<container  label="Online Order Tracking" htmlTag="div" htmlClass="login-container">
				<block class="Totaltools\Sales\Block\Order\Tracking" name="sales_tracking_index"
					template="Totaltools_Sales::order/tracking.phtml" cacheable="false">
					<container name="form.additional.info" label="Form Additional Info">
						<block class="Magento\ReCaptchaUi\Block\ReCaptcha" name="recaptcha" after="-" template="Magento_ReCaptchaFrontendUi::recaptcha.phtml" ifconfig="recaptcha_frontend/type_for/shippit_order_tracking_captcha">
							<arguments>
								<argument name="recaptcha_for" xsi:type="string">shippit_order_tracking_captcha</argument>
								<argument name="jsLayout" xsi:type="array">
									<item name="components" xsi:type="array">
										<item name="recaptcha" xsi:type="array">
											<item name="component" xsi:type="string">Magento_ReCaptchaFrontendUi/js/reCaptcha</item>
										</item>
									</item>
								</argument>
							</arguments>
						</block>
					</container>
                </block>
			</container>
		</referenceContainer>
	</body>
</page>
