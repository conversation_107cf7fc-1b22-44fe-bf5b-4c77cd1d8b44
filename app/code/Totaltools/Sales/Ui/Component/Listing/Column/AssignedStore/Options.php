<?php

namespace Totaltools\Sales\Ui\Component\Listing\Column\AssignedStore;

/**
 * @package Totaltools_Sales
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 */

use Magento\Framework\Data\OptionSourceInterface;
use Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory;

/**
 * Class Options
 */
class Options implements OptionSourceInterface
{
    /**
     * @var CollectionFactory
     */
    private $storeCollectionFactory;

    /**
     * Constructor
     *
     * @param CollectionFactory $storeCollectionFactory
     */
    public function __construct(CollectionFactory $storeCollectionFactory)
    {
        $this->storeCollectionFactory = $storeCollectionFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function toOptionArray()
    {
        $options = [];
        /** @var \Magestore\Storelocator\Model\ResourceModel\Store\Collection */
        $collection = $this->storeCollectionFactory
            ->create()
            ->setOrder('store_name', 'ASC')
            ->load();

        foreach ($collection as $store) {
            /** @var \Magestore\Storelocator\Model\Store $store */
            $options[] = ['label' => $store->getStoreName(), 'value' => $store->getId()];
        }

        return $options;
    }
}
