<?php
/**
 * Group
 *
 * @category  Totaltools
 * @package   Totaltools_Box
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
namespace Totaltools\Box\Block;

use Balance\Box\Model\Box as BoxModel;
use Magento\Store\Model\Store;

/**
 * rewrite block box to filter start and end publishing date
 */
class Group extends \Balance\Box\Block\Group
{
    /**
    * @var \Magento\Store\Model\StoreManagerInterface
    */
    protected $storeManager;

    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Balance\Box\Model\GroupFactory $groupFactory,
        \Balance\Box\Model\ResourceModel\Box\CollectionFactory $boxCollectionFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        parent::__construct($context, $groupFactory, $boxCollectionFactory);
        $this->_storeManager = $storeManager;  
        $this->_timezone = $context->getLocaleDate();
    }
    
    /**
     * @return \Balance\Box\Model\ResourceModel\Box\Collection
     */
    public function getBoxes() {
        if (!$this->hasData('boxes')) {
            $date = $this->_timezone->date()->format('Y-m-d H:i:s');
            $storeId =  $this->getStoreId(); 
            if ($group = $this->getGroup()) {
                $boxes = $this->_boxCollectionFactory
                    ->create()
                    ->addFieldToFilter(BoxModel::GROUP_ID, $group->getId())
                    ->addFieldToFilter(BoxModel::IS_ACTIVE, BoxModel::STATUS_ENABLED)
                    ->addfieldtofilter('from_date', 
                        array(
                            array('to' => $date),
                            array('from_date', 'null'=>'')
                            )
                        )
                   ->addfieldtofilter('to_date',
                        array(
                            array('gteq' => $date),
                            array('to_date', 'null'=>'')
                            )
                        )
                    ->addOrder(BoxModel::POSITION, \Balance\Box\Model\ResourceModel\Box\Collection::SORT_ORDER_ASC)
                    ->addFieldToFilter('store_ids', [
                        ['finset' => Store::DEFAULT_STORE_ID],
                        ['finset' => $storeId]
                    ]);
                if ($boxes->count()) {
                    $this->setData('boxes', $boxes);
                }
            }
        }
        return $this->getData('boxes');
    }

     /**
     * Get store identifier
     *
     * @return  int
     */
    public function getStoreId()
    {
        return $this->_storeManager->getStore()->getId();
    }
}
