<?php

namespace Totaltools\Box\Block;
/**
 * rewrite block box to filter start and end publishing date
 */
class Box extends \Balance\Box\Block\Box
{
    protected $date;
    /**
     * Construct
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Balance\Box\Model\BoxFactory $boxFactory
     * @param array $data = []
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Balance\Box\Model\BoxFactory $boxFactory,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        array $data = []
    ) {
        parent::__construct($context, $boxFactory, $data);
        $this->_boxFactory = $boxFactory;
        $this->date = $date;
    }

    /**
     * @return \Balance\Box\Model\Box
     */
    public function getBox() {
        if (!$this->hasData('box')) {
            if ($identifier = $this->getData(\Balance\Box\Model\Box::IDENTIFIER)) {
                /** @var \Balance\Box\Model\Box $box */
                $box = $this->_boxFactory
                    ->create()
                    ->load($identifier, \Balance\Box\Model\Box::IDENTIFIER)
                ;
                $now = $this->date->timestamp();
                $published = true;
                if ($box->getFromDate()) {
                    $fromDate = $this->date->timestamp($box->getFromDate());
                    if ($now<=$fromDate) {
                        $published = false;
                    }
                }

                if ($box->getToDate()) {
                    $toDate = $this->date->timestamp($box->getToDate());
                    if ($now>=$toDate) {
                        $published = false;
                    }
                }
                if ($published && $box->getIsActive()) {
                    $this->setData('box', $box);
                }
            }
        }
        return $this->getData('box');
    }
}
