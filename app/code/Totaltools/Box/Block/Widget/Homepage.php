<?php

/**
 * <AUTHOR> Internet
 * @package    Totaltools_Box
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2016, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Box\Block\Widget;

use Balance\Box\Model\Group as GroupModel;
use Balance\Box\Model\Box as BoxModel;
use Balance\Box\Block\Group as GroupBlock;
use Magento\Widget\Block\BlockInterface;

class Homepage extends GroupBlock implements BlockInterface
{
    /**
     * @var string
     */
    protected $groupIdParameter = 'group_id';

    /**
     * @var string[]
     */
    protected $boxIdParameters = [
        'box_1_id',
        'box_2_id',
        'box_3_id',
        'box_4_id',
    ];

    /**
     * @var string
     */
    protected $uniqueId;

    /**
     * @var int[]
     */
    protected $boxIds;

    /**
     * @var \Balance\Box\Model\ResourceModel\Box\Collection
     */
    protected $boxes;

    /**
     * @var  \Balance\Box\Model\ResourceModel\Box\CollectionFactory
     */
    protected $boxCollectionFactory;

    /**
     * @var \Magento\Framework\View\Layout
     */
    protected $viewLayout;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Balance\Box\Model\GroupFactory $groupFactory
     * @param \Balance\Box\Model\ResourceModel\Box\CollectionFactory $boxCollectionFactory
     * @param \Magento\Framework\View\LayoutFactory $layoutFactory
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Balance\Box\Model\GroupFactory $groupFactory,
        \Balance\Box\Model\ResourceModel\Box\CollectionFactory $boxCollectionFactory,
        \Magento\Framework\View\LayoutFactory $layoutFactory
    ) {
        parent::__construct($context, $groupFactory, $boxCollectionFactory);

        $this->boxCollectionFactory = $boxCollectionFactory;
        $this->viewLayout = $layoutFactory->create();
    }


    /**
     * Returns custom template file for selected layout
     *
     * @return string
     */
    public function getTemplate()
    {
        switch ($this->getData('layout')) {
            case 'd':
                $template = 'd.phtml';
                break;
            case 'b':
                $template = 'b.phtml';
                break;
            case 'c':
                $template = 'c.phtml';
                break;
            case 'a':
            default:
                $template = 'a.phtml';
                break;
        }

        return 'Totaltools_Box::homepage/' . $template;
    }


    /**
     * Returns amount of boxes for selected layout
     *
     * @return int
     */
    protected function getBoxCount()
    {
        switch ($this->getData('layout')) {
            case 'b':
                return 4;
                break;
            case 'a':
            case 'c':
            default:
                return 3;
                break;
        }
    }


    /**
     * Returns all Box IDs
     *
     * @return int[]
     */
    protected function getBoxIds()
    {
        if (!$this->boxIds) {
            $boxIds = [];
            $i = 0;
            $boxCount = $this->getBoxCount();
            foreach ($this->boxIdParameters as $boxIdParameter) {
                $boxIds[] = intval($this->getData($boxIdParameter));
                $i++;
                if ($i >= $boxCount) break;
            }
            $this->boxIds = array_unique($boxIds);
        }
        return $this->boxIds;
    }


    /**
     * Returns random unique ID
     *
     * @return string
     */
    public function getUniqueId()
    {
        if (!$this->uniqueId) {
            $this->uniqueId = mt_rand(1000, 9999);
        }
        return $this->uniqueId;
    }


    /**
     * Returns Box Collection model of selected Boxes
     *
     * @return \Balance\Box\Model\ResourceModel\Box\Collection
     */
    public function getBoxes()
    {
        if (!$this->boxes) {
            $this->boxes = $this->boxCollectionFactory
                ->create()
                ->addFieldToFilter(BoxModel::BOX_ID, ['in' => $this->getBoxIds()])
                ->addFieldToFilter(BoxModel::IS_ACTIVE, BoxModel::STATUS_ENABLED);
        }
        return $this->boxes;
    }


    /**
     * Creates Box Group block for selected Group & returns HTML
     *
     * @return string
     */
    public function getSliderHtml()
    {
        return $this->viewLayout
            ->createBlock('Balance\Box\Block\Group')
            ->setData(GroupModel::GROUP_ID, intval($this->getData($this->groupIdParameter)))
            ->toHtml();
    }
}
