<?php

namespace Totaltools\Box\Block\Widget;

use Balance\Box\Api\Data\BoxInterface;
use Balance\Box\Model\ResourceModel\Box\Collection as BoxCollection;
use Magento\Framework\ObjectManagerInterface;

class Box extends \Balance\Box\Block\Widget\Box implements \Magento\Widget\Block\BlockInterface
{
    protected $date;

    /**
     * Construct
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Balance\Box\Model\Box $box
     * @param \Balance\Box\Model\BoxFactory $boxFactory
     * @param array $data = []
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Balance\Box\Model\BoxFactory $boxFactory,
        \Balance\Box\Model\Box $box,
        \Magento\Framework\Stdlib\DateTime\DateTime $date,
        array $data = []
    ) {
        parent::__construct($context, $boxFactory, $data);
        $this->_box = $box;
        $this->_boxFactory = $boxFactory;
        $this->date = $date;
    }

    protected function _construct() {
        parent::_construct();
        $this->setTemplate('single.phtml');
    }

    /**
     * @return \Balance\Box\Model\Box
     */
    public function getBox() {
        if (!$this->hasData('box')) {
            if ($this->getBoxId()) {
                /** @var \Balance\Box\Model\Box $box */
                $box = $this->_boxFactory->create();
            } else {
                $box = $this->_box;
            }

            $now = $this->date->timestamp();
            $published = true;
            if ($box->getFromDate()) {
                $fromDate = $this->date->timestamp($box->getFromDate());
                if ($now<=$fromDate) {
                    $published = false;
                }
            }

            if ($box->getToDate()) {
                $toDate = $this->date->timestamp($box->getToDate());
                if ($now>=$toDate) {
                    $published = false;
                }
            }

            if ($published && $box->getIsActive()) {
                $this->setData('box', $box);
            }
        }
        return $this->getData('box');
    }
}