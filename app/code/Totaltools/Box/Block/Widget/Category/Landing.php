<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Box
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2016, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Box\Block\Widget\Category;

use \Balance\Box\Model\Box as BoxModel;
use Magento\Store\Model\Store;

class Landing
    extends \Magento\Framework\View\Element\Template
    implements \Magento\Widget\Block\BlockInterface
{

    /**
     * @var  string[]
     */
    protected $boxIdParameters = [
        'box_1_id',
        'box_2_id',
        'box_3_id',
        'box_4_id',
    ];


    /**
     * @var  string
     */
    protected $uniqueId;

    /**
     * @var  int[]
     */
    protected $boxIds;

    /**
     * @var  \Balance\Box\Model\ResourceModel\Box\Collection
     */
    protected $boxes;


    /**
     * @var  \Balance\Box\Model\ResourceModel\Box\CollectionFactory
     */
    protected $boxCollectionFactory;


    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Balance\Box\Model\GroupFactory $groupFactory
     * @param \Balance\Box\Model\ResourceModel\Box\CollectionFactory $boxCollectionFactory
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Balance\Box\Model\GroupFactory $groupFactory,
        \Balance\Box\Model\ResourceModel\Box\CollectionFactory $boxCollectionFactory,
        \Magento\Catalog\Model\Layer\Resolver $layerResolver,
        array $data = []
    ) {

        parent::__construct($context, $data);
        $this->boxCollectionFactory = $boxCollectionFactory;
        $this->setTemplate('Totaltools_Box::category/landing.phtml');
        $this->_timezone = $context->getLocaleDate();
        $this->layerResolver = $layerResolver;
    }


    /**
     * @return  int
     */
    protected function getBoxCount () {
        switch ($this->getData('layout')) {
            case 'one':
                return 1;
                break;
            case 'two-even':
            case 'two-left':
            case 'two-right':
                return 2;
                break;
            case 'three':
                return 3;
                break;
            case 'four':
            default:
                return 4;
                break;
        }
    }


    /**
     * @return  int[]
     */
    protected function getBoxIds () {
        if (!$this->boxIds) {
            $boxIds = [];
            $i = 0;
            $boxCount = $this->getBoxCount();
            foreach ($this->boxIdParameters as $boxIdParameter) {
                $boxIds[] = intval($this->getData($boxIdParameter));
                $i++;
                if ($i >= $boxCount) break;
            }
            $this->boxIds = array_unique($boxIds);
        }
        return $this->boxIds;
    }


    /**
     * @return  string
     */
    public function getUniqueId () {
        if (!$this->uniqueId) {
            $this->uniqueId = mt_rand(1000, 9999);
        }
        return $this->uniqueId;
    }


    /**
     * @return  \Balance\Box\Model\ResourceModel\Box\Collection
     */
    public function getBoxes () {
        $isValid = $this->isValidCategory();
        if (!$this->boxes && $isValid) {
            $date = $this->_timezone->date()->format('Y-m-d H:i:s');
            $storeId = $this->getStoreId(); 
            $this->boxes = $this->boxCollectionFactory
                ->create()
                ->addFieldToFilter(BoxModel::BOX_ID, ['in' => $this->getBoxIds()])
                ->addFieldToFilter(BoxModel::IS_ACTIVE, BoxModel::STATUS_ENABLED)
                ->addfieldtofilter('from_date', 
                        array(
                            array('to' => $date),
                            array('from_date', 'null'=>'')
                            )
                        )
                   ->addfieldtofilter('to_date',
                        array(
                            array('gteq' => $date),
                            array('to_date', 'null'=>'')
                            )
                        )
                    ->addOrder(BoxModel::POSITION, \Balance\Box\Model\ResourceModel\Box\Collection::SORT_ORDER_ASC)
                    ->addFieldToFilter('store_ids', [
                        ['finset' => Store::DEFAULT_STORE_ID],
                        ['finset' => $storeId]
                    ]);
            ;
        }
        return $this->boxes;
    }

    /**
     * Render block HTML
     *
     * @return string
     */
    protected function _toHtml()
    { 
        if ($this->getBoxes() && !$this->getBoxes()->count()) {
            return '';
        }
        return parent::_toHtml();
    }

    /**
     * Get store identifier
     *
     * @return  int
     */
    public function getStoreId()
    {
        return $this->_storeManager->getStore()->getId();
    }

    /**
     * Get if category can show banner
     *
     * @return  bool
     */
    public function isValidCategory()
    {
        $nonDisplayModes = [
            'SUBCATEGORIES_ONLY',
            'PRODUCTS',
            'SUBCATEGORIES_AND_PRODUCTS',
        ];
        $category = $this->getCurrentCategory();
        if(in_array($category->getDisplayMode(), $nonDisplayModes)){
            return false;
        }
        return true;
    }

    /**
     * Get current category
     *
     * @return  \Magento\Catalog\Model\Category
     */
    public function getCurrentCategory()
    {
        return $this->layerResolver->get()->getCurrentCategory();
    }

}
