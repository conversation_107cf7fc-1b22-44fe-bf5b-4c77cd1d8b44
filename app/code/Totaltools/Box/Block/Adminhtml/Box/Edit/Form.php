<?php
/**
 * Form
 *
 * @category  Totaltools
 * @package   Totaltools_Box
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
namespace Totaltools\Box\Block\Adminhtml\Box\Edit;

use Magento\Backend\Block\Store\Switcher\Form\Renderer\Fieldset\Element;

/**
 * Class Form
 * @package Totaltools\Box\Block\Adminhtml\Box\Edit
 */
class Form extends \Balance\Box\Block\Adminhtml\Box\Edit\Form 
{
    protected function _prepareForm()
    {
        $formResult = parent::_prepareForm();
        $model = $this->_coreRegistry->registry('box_box');
        $form = $this->getForm();
        $fieldsetPublishing = $form->getElement('publishing_fieldset');
        $fieldsetPublishing->removeField('from_date');
        $fieldsetPublishing->removeField('to_date');
        $timeFormat = $this->_localeDate->getTimeFormat(\IntlDateFormatter::SHORT);
        $fieldsetPublishing->addField(
            'from_date',
            'date',
            [
                'label' => __('From Date'),
                'title'  => __('From Date'),
                'name' => 'from_date',
                'time'   => true,
                'date_format' => \Magento\Framework\Stdlib\DateTime::DATE_INTERNAL_FORMAT,
                'time_format' => $timeFormat,
                'class' => 'admin__control-text'
            ]
        );

        $fieldsetPublishing->addField(
            'to_date',
            'date',
            [
                'name'   => 'to_date',
                'label'  => __('To Date'),
                'title'  => __('To Date'),
                'time'   => true,
                'options' => ['showsTime'=>true],
                'date_format' => \Magento\Framework\Stdlib\DateTime::DATE_INTERNAL_FORMAT,
                'time_format' => $timeFormat,
            ]
        );

        if (!$this->_storeManager->isSingleStoreMode()) {
            /** @var RendererInterface $rendererBlock */
            $rendererBlock = $this->getLayout()->createBlock(
                Element::class
            );
            $fieldsetPublishing->addField('store_ids', 'multiselect', [
                'name' => 'store_ids',
                'label' => __('Store Views'),
                'title' => __('Store Views'),
                'values' => $this->_systemStore->getStoreValuesForForm(false, true)
            ])->setRenderer($rendererBlock);
            if (!$model->hasData('store_ids')) {
                $model->setStoreIds(0);
            }
        } else {
            $fieldsetPublishing->addField('store_ids', 'hidden', [
                'name' => 'store_ids',
                'value' => $this->_storeManager->getStore()->getId()
            ]);
        }
        $form->setValues($model->getData());
        $this->setForm($form);

        return $formResult;
    }
}