<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Box
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2016, Balance Internet  (http://www.balanceinternet.com.au/)
 */
-->
<widgets xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Widget:etc/widget.xsd">
    <widget id="totaltools_box_category_landing" class="Totaltools\Box\Block\Widget\Category\Landing">
        <label translate="true">Category Landing Boxes</label>
        <description translate="true">Display Boxes on a Category landing page</description>
        <parameters>
            <parameter name="box_1_id" xsi:type="select" source_model="Balance\Box\Model\Box\Source\Box" required="true" visible="true" sort_order="10">
                <label translate="true">Box 1</label>
            </parameter>
            <parameter name="box_2_id" xsi:type="select" source_model="Balance\Box\Model\Box\Source\Box" visible="true" sort_order="20">
                <label translate="true">Box 2</label>
            </parameter>
            <parameter name="box_3_id" xsi:type="select" source_model="Balance\Box\Model\Box\Source\Box" visible="true" sort_order="30">
                <label translate="true">Box 3</label>
            </parameter>
            <parameter name="box_4_id" xsi:type="select" source_model="Balance\Box\Model\Box\Source\Box" visible="true" sort_order="40">
                <label translate="true">Box 4</label>
            </parameter>
            <parameter name="layout" xsi:type="select" required="true" visible="true" sort_order="50">
                <label translate="true">Layout</label>
                <options>
                    <option name="one" value="one">
                        <label translate="true">1 box</label>
                    </option>
                    <option name="two-even" value="two-even">
                        <label translate="true">2 boxes (even)</label>
                    </option>
                    <option name="two-left" value="two-left">
                        <label translate="true">2 boxes (larger left)</label>
                    </option>
                    <option name="two-right" value="two-right">
                        <label translate="true">2 boxes (larger right)</label>
                    </option>
                    <option name="three" value="three">
                        <label translate="true">3 boxes</label>
                    </option>
                    <option name="four" value="four">
                        <label translate="true">4 boxes</label>
                    </option>
                </options>
            </parameter>
        </parameters>
    </widget>
    <widget id="totaltools_box_homepage" class="Totaltools\Box\Block\Widget\Homepage">
        <label translate="true">Homepage Boxes</label>
        <description translate="true">Display Boxes on the homepage</description>
        <parameters>
            <parameter name="group_id" xsi:type="select" source_model="Balance\Box\Model\Group\Source\Group" required="true" visible="true" sort_order="10">
                <label translate="true">Slider Group</label>
            </parameter>
            <parameter name="box_1_id" xsi:type="select" source_model="Balance\Box\Model\Box\Source\Box" required="true" visible="true" sort_order="20">
                <label translate="true">Box 1</label>
            </parameter>
            <parameter name="box_2_id" xsi:type="select" source_model="Balance\Box\Model\Box\Source\Box" visible="true" sort_order="30">
                <label translate="true">Box 2</label>
            </parameter>
            <parameter name="box_3_id" xsi:type="select" source_model="Balance\Box\Model\Box\Source\Box" visible="true" sort_order="40">
                <label translate="true">Box 3</label>
            </parameter>
            <parameter name="box_4_id" xsi:type="select" source_model="Balance\Box\Model\Box\Source\Box" visible="true" sort_order="50">
                <label translate="true">Box 4</label>
            </parameter>
            <parameter name="layout" xsi:type="select" required="true" visible="true" sort_order="60">
                <label translate="true">Layout</label>
                <options>
                    <option name="a" value="a">
                        <label translate="true">A</label>
                    </option>
                    <option name="b" value="b">
                        <label translate="true">B</label>
                    </option>
                    <option name="c" value="c">
                        <label translate="true">C</label>
                    </option>
                    <option name="d" value="d">
                        <label translate="true">D</label>
                    </option>
                </options>
            </parameter>
        </parameters>
    </widget>
    <widget id="totaltools_homepage_popuplar_brands" class="Totaltools\Box\Block\Widget\Brands">
        <label translate="true">Homepage Popular Brands</label>
        <description translate="true">Display popular brands on the homepage</description>
        <parameters>
            <parameter name="group_id" xsi:type="select" source_model="Balance\Box\Model\Group\Source\Group" required="true" visible="true" sort_order="0">
                <label translate="true">Group</label>
            </parameter>
            <parameter name="template" xsi:type="select">
                <options>
                    <option name="slider" value="Totaltools_Box::homepage/popular-brands.phtml">
                        <label translate="true">Brands Slider Template</label>
                    </option>
                </options>
            </parameter>
        </parameters>
        <containers>
            <container name="content">
                <template name="slider" value="slider" />
            </container>
            <container name="content.top">
                <template name="slider" value="slider" />
            </container>
            <container name="content.bottom">
                <template name="slider" value="slider" />
            </container>
            <container name="home.top.banner">
                <template name="slider" value="slider" />
            </container>
        </containers>
    </widget>
    <widget id="totaltools_emarsys_recommended_widget" class="Totaltools\Box\Block\Widget\EmarsysRecommended">
        <label translate="true">Emarsys Recommended Products</label>
        <description translate="true">Display Emarsys recommended widget with different products logics</description>
        <parameters>
            <parameter name="widget_title" xsi:type="text" visible="true" required="false" sort_order="10">
                <label translate="true">Block Heading</label>
                <description translate="true">If empty, the heading is not shown</description>
            </parameter>
            <parameter name="products_count" xsi:type="text" visible="true" required="true" sort_order="20">
                <label translate="true">Products Count</label>
                <description translate="true">Number of products to show in the widget</description>
            </parameter>
            <parameter name="widget_logic" xsi:type="select" required="true" source_model="Totaltools\Box\Model\Emarsys\Source\Logic" visible="true" sort_order="30">
                <label translate="true">Emarsys Logic</label>
                <description translate="true">Widget logic as per Emarsys's specification</description>
            </parameter>
            <parameter name="gtm_event_name" xsi:type="text" visible="true" required="false" sort_order="40">
                <label translate="true">GTM Event Name</label>
                <description translate="true">GTM Event name to be pushed with GTM calls</description>
            </parameter>
            <parameter name="gtm_category_name" xsi:type="text" visible="true" required="false" sort_order="50">
                <label translate="true">GTM Category Name</label>
                <description translate="true">Category name to be pushed with GTM calls</description>
            </parameter>
            <parameter name="gtm_list_name" xsi:type="text" visible="true" required="false" sort_order="60">
                <label translate="true">GTM List Name</label>
                <description translate="true">List name to be pushed with GTM calls</description>
            </parameter>
            <parameter name="cache_products" xsi:type="select" source_model="Magento\Config\Model\Config\Source\Yesno" visible="true" required="false" sort_order="65">
                <label translate="true">Cache Products</label>
                <description translate="true">Cache products for specific time once loaded from Emarsys</description>
            </parameter>
            <parameter name="reload_duration" xsi:type="text" visible="true" required="false" sort_order="70">
                <label translate="true">Products Reload Duration</label>
                <description translate="true">Time (in minutes) after which products will be reloaded from server</description>
            </parameter>
            <parameter name="widget_id" xsi:type="text" visible="false" required="false" sort_order="80">
                <label translate="true">Widget Id</label>
            </parameter>
        </parameters>
    </widget>
</widgets>
