<?php

namespace Totaltools\Box\Model\Emarsys\Source;

/**
 * @category   Totaltools
 * @package    Totaltools_Box
 * <AUTHOR> Iqbal  <<EMAIL>>
 * @copyright  Copyright (c) 2021, Totaltools. (http://totaltools.com.au/)
 */

class Logic implements \Magento\Framework\Data\OptionSourceInterface
{
    /**
     * @inheritdoc
     */
    public function toOptionArray()
    {
        return [
            [
                'label' => __('Personal'),
                'value' => 'PERSONAL'
            ],
            [
                'label' => __('Also Bought'),
                'value' => 'ALSO_BOUGHT'
            ],
            [
                'label' => __('Related'),
                'value' => 'RELATED'
            ],
            [
                'label' => __('Cart'),
                'value' => 'CART'
            ],
            [
                'label' => __('Search'),
                'value' => 'SEARCH'
            ],
            [
                'label' => __('Category'),
                'value' => 'CATEGORY'
            ],
            [
                'label' => __('Popular'),
                'value' => 'POPULAR'
            ],
            [
                'label' => __('Department'),
                'value' => 'DEPARTMENT'
            ]
        ];
    }
}
