<?php
/**
 * Box
 *
 * @category  Totaltools
 * @package   Totaltools_Box
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
namespace Totaltools\Box\Model\ResourceModel;

/**
 * Box mysql resource
 */
class Box extends \Balance\Box\Model\ResourceModel\Box
{
    
    /**
     * Process box data before saving
     *
     * @param \Magento\Framework\Model\AbstractModel $object
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function _beforeSave(\Magento\Framework\Model\AbstractModel $object) 
    {
        if (is_array($object->getStoreIds())) {
            $object->setStoreIds(implode(',', $object->getStoreIds()));
        }
        return parent::_beforeSave($object);
    }

}
