<?php
/**
 * Box
 *
 * @category  Totaltools
 * @package   Totaltools_Box
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Box\Model\ResourceModel\Widget;

use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\App\ObjectManager;

/**
 * Widget Instance Resource Model
 *
 * @api
 * @since 100.0.2
 */
class Instance extends \Magento\Widget\Model\ResourceModel\Widget\Instance
{
     /**
     * @var Json
     */
    public $jsonSerializer;

    /**
     * @param \Magento\Framework\Serialize\Serializer\Json $serializer
     */
    public function _construct(
        Json $jsonSerializer = null
    ) {
        $this->_init('widget_instance', 'instance_id');
        $this->jsonSerializer = $jsonSerializer ?: ObjectManager::getInstance()->get(Json::class); 
    }

    /**
     * Perform actions after object save
     *
     * @param \Magento\Widget\Model\Widget\Instance $object
     * @return $this
     */
    protected function _afterSave(AbstractModel $object)
    {
        $widgetId = $object->getId();
        $widgetTable = $this->getTable('widget_instance');
        $connection = $this->getConnection();
        $parameters = $this->jsonSerializer->unserialize($object->getData('widget_parameters'));
        if (is_array($parameters)) {
            if (array_key_exists('widget_id', $parameters)) {
                $parameters['widget_id'] =  $widgetId;
                $data = [
                    'widget_parameters' => $this->jsonSerializer->serialize($parameters)
                ];
                $connection->update($widgetTable, $data, ['instance_id = ?' => (int)$widgetId]);
            }
        }
        return parent::_afterSave($object);
    }

   
}
