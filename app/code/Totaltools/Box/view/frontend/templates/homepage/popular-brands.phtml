<?php

/**
 * @category   Totaltools
 * @package    Totaltools_Box
 * <AUTHOR> Iqbal  <<EMAIL>>
 * @copyright  Copyright (c) 2021, Totaltools. (http://totaltools.com.au/)
 */

 /**
  * @var \Totaltools\Box\Block\Widget\Brands $block
  */

$group = $block->getGroup();
$boxes = $block->getBoxes();
if (!$group || !$boxes) return;
?>

<div class="homepage-top-brands" id="homepage-top-brands-<?= $block->escapeQuote($group->getIdentifier()); ?>">
    <div id="homepage-brands-container-<?= $block->escapeQuote($group->getIdentifier()); ?>" class="homepage-brands-container <?= $block->escapeQuote((string)$block->getCssClass()); ?>">
        <?php foreach ($boxes as $box) : ?>
            <div class="homepage-top-brand <?= $block->escapeQuote((string)$box->getLayoutClass()); ?>">
                <?php if ($box->getLink()) { ?><a href="<?= $block->escapeUrl($box->getLink()); ?>"><?php } ?>
                    <picture class="homepage-brand-image">
                        <?php if ($box->getMobileImageUrl()): ?>
                        <source media="(max-width: 768px)" srcset="<?= $box->getMobileImageUrl(); ?>" />
                        <?php endif; ?>
                        <?php if ($box->getDesktopImageUrl()): ?>
                        <source srcset="<?= $box->getDesktopImageUrl(); ?>" />
                        <img src="<?= $box->getDesktopImageUrl(); ?>" alt="<?= $block->escapeHtml($box->getAltText()); ?>" />
                        <?php endif; ?>
                    </picture>
                <?php if ($box->getLink()) { ?></a><?php } ?>
            </div>
        <?php endforeach ?>
    </div>

    <div class="view-more">
        <a href="<?= $block->getUrl('brands'); ?>" role="button"><?= /** @noEscape */ __('View All Brands'); ?></a>
    </div>
</div>
<script>
    require(['jquery', 'slick'], function($) {
        'use strict';

        var opts = {
            infinite: true,
            dots: false,
            arrow: true,
            slidesToShow: 6,
            slidesToScroll: 1,
            responsive: [
                {
                    breakpoint: 1024,
                    settings: {
                        slidesToShow: 4,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 768,
                    settings: {
                        slidesToShow: 6,
                        slidesToScroll: 1,
                        infinite: false,
                        arrow: false,
                    }
                }
            ]
        };

        $('#homepage-brands-container-<?= $block->escapeQuote($group->getIdentifier()); ?>').slick(opts);
    });
</script>
