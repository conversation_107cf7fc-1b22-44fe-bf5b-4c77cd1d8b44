<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Box
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2016, Balance Internet  (http://www.balanceinternet.com.au/)
 */

/**
 * @var $block \Totaltools\Box\Block\Widget\Homepage
 */

$slider = $block->getSliderHtml();
$boxes  = $block->getBoxes();
if (!$slider && !$boxes) return;

?>

<div class="box-wrapper layout-c">
    <?php echo $block->getSliderHtml(); ?>
</div>

<div id="js__homepage-boxes-<?php echo $block->getUniqueId(); ?>" class="homepage-boxes homepage-boxes-<?php echo $block->getData('layout'); ?>">

    <?php foreach($boxes as $box) : ?>
        <div class="homepage-box <?php echo $box->getLayoutClass(); ?>">

            <div class="homepage-box-container">
                <?php if ($box->getHeading()) : ?>
                    <h2 class="homepage-box-heading">
                        <?php echo $block->escapeHtml($box->getHeading()); ?>
                    </h2>
                <?php endif; ?>

                <?php if ($box->getContent()) : ?>
                    <div class="homepage-box-content std">
                        <?php echo $box->getContent(); ?>
                    </div>
                <?php endif; ?>

                <?php if ($box->getLink()) : ?>
                    <a href="<?php echo $box->getLink(); ?>" class="button homepage-box-button">
                        <span><span>
                            <?php echo $block->escapeHtml($box->getButtonText()); ?>
                        </span></span>
                    </a>
                <?php endif; ?>
            </div>

            <?php if ($box->getMobileImageUrl() || $box->getDesktopImageUrl()) : ?>
                <img alt="<?php echo $block->escapeHtml($box->getAltText()); ?>" data-mobile-image="<?php echo $box->getMobileImageUrl(); ?>" data-desktop-image="<?php echo $box->getDesktopImageUrl(); ?>" class="homepage-box-image">
            <?php endif; ?>

        </div>
    <?php endforeach ?>
</div>

<script>
require([
    'jquery',
    'balance/box'
], function ($) {

    $('#js__homepage-boxes-<?php echo $block->getUniqueId(); ?>').find('.homepage-box-image').each(function () {
        $.balance.box({}, $(this));
    });

});
</script>
