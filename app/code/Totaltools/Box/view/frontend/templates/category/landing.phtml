<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Box
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2016, Balance Internet  (http://www.balanceinternet.com.au/)
 */

/**
 * @var $block \Totaltools\Box\Block\Widget\Category\Landing
 */

$boxes = $block->getBoxes();
if (!$boxes) return;

?>
<div id="js__categorylanding-boxes-<?php echo $block->getUniqueId(); ?>" class="categorylanding-boxes categorylanding-boxes-<?php echo $block->getData('layout'); ?>">

    <?php foreach($boxes as $box) : ?>
        <?php if ($box->getLink()) : ?><a href="<?php echo $box->getLink(); ?>"><?php endif; ?>
        <div class="categorylanding-box <?php echo $box->getLayoutClass(); ?>">
            <div class="categorylanding-box-container">
                <?php if ($box->getHeading()) : ?>
                    <h2 class="categorylanding-box-heading">
                        <?php echo $block->escapeHtml($box->getHeading()); ?>
                    </h2>
                <?php endif; ?>

                <?php if ($box->getContent()) : ?>
                    <div class="categorylanding-box-content std">
                        <?php echo $box->getContent(); ?>
                    </div>
                <?php endif; ?>

                <?php if ($box->getLink() && $box->getButtonText() != '') : ?>
                    <button onclick="window.location='<?php echo $box->getLink(); ?>'" class="button categorylanding-box-button">
                        <span><span>
                            <?php echo $block->escapeHtml($box->getButtonText()); ?>
                        </span></span>
                    </button>
                <?php endif; ?>
            </div>

            <?php if ($box->getMobileImageUrl() || $box->getDesktopImageUrl()) : ?>
                <img alt="<?php echo $block->escapeHtml($box->getAltText()); ?>" data-mobile-image="<?php echo $box->getMobileImageUrl(); ?>" data-desktop-image="<?php echo $box->getDesktopImageUrl(); ?>" class="categorylanding-box-image">
            <?php endif; ?>
        </div>
        <?php if ($box->getLink()) : ?></a><?php endif; ?>
    <?php endforeach ?>
</div>

<script>
require([
    'jquery',
    'balance/box'
], function ($) {

    $('#js__categorylanding-boxes-<?php echo $block->getUniqueId(); ?>').find('.categorylanding-box-image').each(function () {
        $.balance.box({}, $(this));
    });

});
</script>
