/**
 * @category   Totaltools
 * @package    Totaltools_Box
 * <AUTHOR>  <<EMAIL>>
 * @copyright  Copyright (c) 2021, Totaltools. (http://totaltools.com.au/)
 */

define([
    'ko',
    'jquery',
    'uiComponent',
    'priceUtils',
    'inputSanitizer',
    'Magento_Customer/js/customer-data',
    'mage/url',
    'matchMedia',
    'mage/cookies',
    'slick',
    'jquery/jquery-storageapi',
], function (ko, $, Component, priceUtils, sanitizeInput, customerData, url, mediaCheck) {
    'use strict';

    var cacheKey = 'emarsys-product-storage',
        storage = $.initNamespaceStorage(cacheKey).localStorage;

    window.dataLayer = window.dataLayer || [];
    window.ScarabQueue = window.ScarabQueue || [];

    url.setBaseUrl(BASE_URL);

    function loadWebRecommender() {
        var self = this;

        (function (id) {
            if (document.getElementById(id))
                return self.onWebRecommenderLoad.apply(self);
            var js = document.createElement('script');
            js.id = id;
            js.async = true;
            js.onload = self.onWebRecommenderLoad.bind(self);
            js.src =
                '//cdn.scarabresearch.com/js/1DA9B74C6CFBAE7B/scarab-v2.js';
            var fs = document.getElementsByTagName('script')[0];
            fs.parentNode.insertBefore(js, fs);
        })('scarab-js-api');
    }

    return Component.extend({
        isLoading: ko.observable(true),
        products: ko.observableArray([]),
        gtmCategory: 'Home',
        gtmList: 'Emarsys Web Recommended',
        gtmEvent: 'widgetViewNonInteractive',
        formKey: $.mage.cookies.get('form_key'),
        addToCartUrl: url.build('checkout/cart/add'),
        addToCartEnabled: false,
        minicartSelector: '[data-block="minicart"]',
        messagesSelector: '[data-placeholder="messages"]',
        slickConfig: {},
        widgetId: '',
        storageId: null,
        cacheProducts: false,
        reloadDuration: 30,
        container: null,

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();
            this.storageId = cacheKey + '-' + this.widgetId;

            return this;
        },

        /**
         * @param {HTMLElement} elem
         */
        renderCallback: function (elem) {
            this.container = elem.parentElement.parentElement;
            let storedProducts = storage.get(this.storageId);

            if (this.cacheProducts == false || this.isExpired() || !Array.isArray(storedProducts)) {
                loadWebRecommender.call(this);
            } else {
                this.products(storedProducts);
                window.preventGoCommand && ScarabQueue.push(['go']);
            }
        },

        /**
         * Determine whether a localStorage has expired
         * @returns {Boolean}
         */
        isExpired: function () {
            var now = new Date().getTime(),
                _age = parseInt(this.reloadDuration) * 60 * 1000,
                cookieId = 'emarsys_storage_timeout' + '_' + this.widgetId,
                isExpired = true,
                emarsysStorageAge = $.mage.cookies.get(cookieId) || 0;

            if (emarsysStorageAge) {
                //Whether the current time is greater than the expiration time
                isExpired = now > emarsysStorageAge;
            }

            if (isExpired) {
                //set cookie value
                let timeout = now + _age;
                $.mage.cookies.set(cookieId, timeout, {
                    expires: new Date(timeout),
                });
            }

            return isExpired;
        },

        onWebRecommenderLoad: function () {
            var self = this;

            this.isLoading(true);

            switch (this.widgetLogic) {
                case 'RELATED':
                case 'ALSO_BOUGHT':
                    this.relatedLogicWidget(ScarabQueue);
                    break;
                case 'CART':
                    this.cartLogicWidget(ScarabQueue);
                    break;
                case 'SEARCH':
                    this.cartLogicWidget(ScarabQueue);
                    break;
                case 'CATEGORY':
                case 'DEPARTMENT':
                case 'POPULAR':
                    this.categoryLogicWidget(ScarabQueue);
                    break;
                default:
                    break;
            }

            ScarabQueue.push([
                'recommend',
                {
                    logic: this.widgetLogic,
                    containerId: 'web-recommender-' + self.widgetId,
                    templateId: 'web-recommender-template',
                    limit: self.productsCount,
                    success: function (SC, render) {
                        self.products(SC.page.products || self.products());
                        if (self.cacheProducts) {
                            storage.set(self.storageId, self.products());
                        }
                    },
                },
            ]);

            ScarabQueue.push(['go']);
        },

        /**
         * @param {Array} ScarabQueue
         */
        relatedLogicWidget: function (ScarabQueue) {
            var sku = $('[itemprop="sku"]');

            if (!sku.length) {
                console.error(
                    'No sku found! Make sure you are using this widget on a product page.'
                );
                return;
            }

            ScarabQueue.push(['view', sku[0].innerText]);
        },

        /**
         * @param {Array} ScarabQueue
         */
        cartLogicWidget: function (ScarabQueue) {
            var cart = customerData.get('cart'),
                cartItems = cart()['items'] || [],
                items = [];

            if (cartItems.length) {
                for (var i = 0; i < cartItems.length; i++) {
                    var item = cartItems[i];

                    items.push({
                        item: item.product_sku,
                        price: Number(
                            parseFloat(item.product_price_value).toFixed(2)
                        ),
                        quantity: item.qty,
                    });
                }
            } else {
                console.error(
                    'Make sure you have items in the cart. This widget should be called from cart page only.'
                );
                return;
            }

            ScarabQueue.push(['cart', items]);
        },

        /**
         * @param {Array} ScarabQueue
         */
        searchLogicWidget: function (ScarabQueue) {
            var params = {};
            location.search.replace(
                /[?&]+([^=&]+)=([^&]*)/gi,
                function (s, k, v) {
                    params[k] = v;
                }
            );
            var query = params['s'] || '';

            if (!query || query == '') {
                console.error(
                    'No search query found! Make sure you are using this widget on search result page.'
                );
                return;
            }

            ScarabQueue.push(['searchTerm', sanitizeInput(query)]);
        },

        /**
         * @param {Array} ScarabQueue
         */
        categoryLogicWidget: function (ScarabQueue) {
            var category = dataLayer.pageCategory || '';

            if (!category || category == '') {
                console.error(
                    'No category specified! Make sure you are using this widget on category page.'
                );
                return;
            }

            ScarabQueue.push(['category', category]);
        },

        /**
         * @param {HTMLElement} productList
         */
        listRenderCallback: function (productList) {
            var self = this,
                defaults = {
                    infinite: false,
                    dots: false,
                    slidesToShow: 5,
                    responsive: [
                        {
                            breakpoint: 1025,
                            settings: {
                                slidesToShow: 4,
                            },
                        },
                        {
                            breakpoint: 769,
                            settings: {
                                slidesToShow: 3,
                            },
                        },
                        {
                            breakpoint: 641,
                            settings: {
                                slidesToShow: 2,
                            },
                        },
                        {
                            breakpoint: 375,
                            settings: {
                                slidesToShow: 1,
                            },
                        },
                    ],
                },
                config = $.extend(defaults, this.slickConfig);

            setTimeout(function () {
                $(self.container).show();
                $(productList).slick(config);

                self.isLoading(false);

                $(productList).on('click', 'button.todetails', function (ev) {
                    self.handleProductClick($(this).data(), ev);
                });

                self.pushGtmImpressions(self.products());

                let container = $('.product-notforsale-container');

                if (container.length) {
                    $('#emarsys-recommended h2').text('Alternative tool options for you to consider');
                    mediaCheck({
                        media: '(max-width: 768px)',
                        entry: function () {
                            $('#emarsys-recommended').appendTo(container);
                        },
                        exit: function () {
                            if ($('.product-info-bottom').length) {
                                $('#emarsys-recommended').insertBefore('.product-info-bottom');
                            } else {
                                $('#emarsys-recommended').prependTo('.product-recently-view');
                            }
                        }
                    });
                }
            }, 100);
        },

        /**
         * @param {Number} price
         * @returns {String}
         */
        formatPrice: function (price) {
            if (typeof priceUtils.getPriceHtml !== 'function') {
                return price;
            }

            return priceUtils.getPriceHtml(price);
        },

        /**
         * @param {Object} data
         * @param {jQuery.Event} ev
         */
        handleProductClick: function (data, ev) {
            ev.preventDefault && ev.preventDefault();
            AEC && AEC.gtm() && AEC.click(ev.currentTarget, window.dataLayer || []);

            if (data.link != null) {
                window.location = decodeURI(data.link);
            }

            return false;
        },

        /**
         * @param {Object} form
         * @returns {Object}
         */
        handleAddToCart: function (form) {
            if (!this.addToCartEnabled) {
                return;
            }

            var self = this,
                $form = $(form),
                $submitBtn = $form.find('button[type="submit"]'),
                payload = $form.serializeArray();

            payload.push({ form_key: self.formKey });

            return $.ajax({
                url: self.addToCartUrl,
                type: 'POST',
                data: payload,
                dataType: 'json',
                beforeSend: function () {
                    $(self.minicartSelector).trigger('contentLoading');
                    $('body').trigger('processStart');
                },
                success: function (res) {
                    if (res.backUrl) {
                        window.location = res.backUrl;
                        return;
                    }

                    if (res.messages) {
                        $(self.messagesSelector).html(res.messages);
                    }

                    if (res.minicart) {
                        $(self.minicartSelector).replaceWith(res.minicart);
                        $(self.minicartSelector).trigger('contentUpdated');
                    }

                    customerData.reload(['cart']);

                    $(document).trigger('ajax:addToCart', {
                        sku: $form[0]['value'],
                        form: $form,
                        response: res,
                    });
                },
                error: function (err) {
                    console.error(err);
                },
                complete: function () {
                    $('body').trigger('processStop');
                    $submitBtn.attr('disabled', false);
                },
            });
        },

        /**
         * @param {Array} products
         */
        pushGtmImpressions: function (products) {
            if (!products.length || typeof AEC === 'undefined' || !AEC.gtm()) return;

            var self = this,
                dataLayer = window.dataLayer || [],
                impressionProducts = [];

            for (var i = 0; i < products.length; i++) {
                var prod = products[i];

                impressionProducts.push({
                    id: prod.id,
                    name: prod.title,
                    price: prod.price,
                    category: self.gtmCategory,
                    list: self.gtmList,
                    brand: prod.brand,
                    quantity: 1,
                    position: i + 1,
                    store: AEC.storeName,
                });
            }

            AEC.Cookie.impressions({
                event: 'emarsysWidget',
                ecommerce: {
                    currencyCode: AEC.currencyCode,
                    actionField: {
                        list: self.gtmList,
                    },
                    impressions: impressionProducts,
                },
            }).push(dataLayer, false);
        },
    });
});
