<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_CustomerImport
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\CustomerImport\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class CustomerImport
 * @package Totaltools\CustomerImport\Console\Command
 */
class CustomerImport extends Command
{
    /**
     * @var \Magento\Framework\App\State
     */
    protected $appState;

    /**
     * @var \Totaltools\CustomerImport\Model\Import\Writer
     */
    protected $writer;

    /**
     * @var \Totaltools\CustomerImport\Model\Import\Reader
     */
    protected $reader;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * CustomerImport constructor.
     * @param \Magento\Framework\App\State $appState
     * @param \Totaltools\CustomerImport\Model\Import\Writer $writer
     * @param \Totaltools\CustomerImport\Model\Import\Reader $reader
     * @param \Psr\Log\LoggerInterface $logger
     * @param string|null $name
     */
    public function __construct(
        \Magento\Framework\App\State $appState,
        \Totaltools\CustomerImport\Model\Import\Writer $writer,
        \Totaltools\CustomerImport\Model\Import\Reader $reader,
        \Psr\Log\LoggerInterface $logger,
        string $name = null
    )
    {
        parent::__construct($name);
        $this->appState = $appState;
        $this->writer = $writer;
        $this->reader = $reader;
        $this->logger = $logger;
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName('totaltools:customer-bulk-import');
        $this->setDescription("Customer bulk import");
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void|null
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $self = $this;
        try {
            $self->appState->emulateAreaCode(\Magento\Framework\App\Area::AREA_FRONTEND, function () use ($self, $output) {
                $output->writeln("Bulk customers import started.\n");
                $files = $self->reader->getFiles();
                $output->writeln("Files found " . count($files) . "\n");
                $self->logger->log('INFO', 'BULK IMPORT: Files found: ' . count($files));

                foreach ($files as $index => $file) {
                    $output->writeln("Start processing file " . $file['text'] . "\n");
                    try {
                        $fileData = $self->reader->getFileData($file['text']);
                    } catch (\Exception $e) {
                        $self->logger->log('ERROR', 'BULK IMPORT: file ' . $file['text'] . ' : ' . $e->getMessage());
                        $self->reader->moveToFailDir($file['text']);
                        continue;
                    }

                    $importStatus = $self->writer->import($fileData, $file['text']);

                    if ($importStatus) {
                        $self->reader->moveToProcessedDir($file['text']);
                        $output->writeln("Finished import file " . $file['text']. "\n");
                    } else {
                        $output->writeln("Failed to import file " . $file['text']. "\n");
                        $self->reader->moveToFailDir($file['text']);
                    }
                }

                $output->writeln("Bulk customers import finished.\n");
            });
        } catch (\Exception $exception) {
            $this->logger->log('ERROR', 'BULK IMPORT: ' . $exception->getMessage());
        }
    }
}