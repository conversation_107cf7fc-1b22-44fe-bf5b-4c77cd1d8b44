<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_CustomerImport
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\CustomerImport\Model\Import;

use Magento\Framework\Exception\LocalizedException;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;

/**
 * Class Writer
 * @package Totaltools\CustomerImport\Model\Import
 */
class Writer
{
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var \Magento\Customer\Model\ResourceModel\Customer
     */
    protected $customerResource;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    protected $customerFactory;

    /**
     * @var DataConverter
     */
    protected $dataConverter;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Writer constructor.
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Customer\Model\ResourceModel\Customer $customerResource
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param DataConverter $dataConverter
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Customer\Model\ResourceModel\Customer $customerResource,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        DataConverter $dataConverter,
        \Psr\Log\LoggerInterface $logger
    )
    {
        $this->customerRepository = $customerRepository;
        $this->customerResource = $customerResource;
        $this->customerFactory = $customerFactory;
        $this->dataConverter = $dataConverter;
        $this->logger = $logger;
    }

    /**
     * @param array $customerData
     * @param string $filename
     * @return bool
     */
    public function import(array $customerData, string $filename)
    {
        $this->logger->log('INFO', 'BULK IMPORT: Start processing file ' . $filename);
        $result = [
            'customersUpdated' => 0,
            'customersCreated' => 0,
            'failedCreatingCustomers' => 0,
            'customersFoundByEmail' => 0,
            'customersFoundByMobile' => 0,
            'errorMessages' => [],
        ];
        $importStatus = true;

        foreach ($customerData as $index => $data) {
            if (!$this->hasEmail($data) && !$this->hasPhone($data)) {
                $result['errorMessages'][] = __('Row %1 does not have email and phone', $index + 1);
                continue;
            }

            $foundByEmail = true;

            if ($this->hasEmail($data)) {
                $searchResult = $this->findByEmail($data);

                if (!$searchResult) {
                    $foundByEmail = false;
                    $searchResult = $this->findByMobile($data);
                }
            } else {
                $foundByEmail = false;
                $searchResult = $this->findByMobile($data);
            }

            if (!$searchResult && $this->hasEmail($data)) {
                try {
                    $this->createCustomer($data);
                    $result['customersCreated']++;
                } catch (\Exception $exception) {
                    $result['errorMessages'][] = __('Error while saving customer from row %1. Error message: %2', $index + 1, $exception->getMessage());
                    $importStatus = false;
                    $result['failedCreatingCustomers']++;
                }
                continue;
            }

            if ($foundByEmail) {
                $result['customersFoundByEmail']++;
            } else {
                $result['customersFoundByMobile']++;
            }

            try {
                $customerInterface = $this->customerRepository->get($searchResult['email']);
                unset($searchResult['email']);

                foreach ($searchResult as $attrKey => $attrValue) {
                    $customerInterface->setCustomAttribute(
                        $attrKey,
                        $attrValue
                    );
                }

                $this->customerRepository->save($customerInterface);
            } catch (LocalizedException $e) {
                $result['errorMessages'][] = $e->getMessage();
                $importStatus = false;
            }

            $result['customersUpdated']++;
        }

        if (!empty($result['errorMessages'])) {
            foreach ($result['errorMessages'] as $errorMessage) {
                $this->logger->log('ERROR', 'BULK IMPORT: file ' . $filename. '. ' . $errorMessage);
            }
        }

        $this->logger->log('INFO', 'BULK IMPORT: Number of customers updated from file ' . $filename . ' : ' . $result['customersUpdated']);
        $this->logger->log('INFO', 'BULK IMPORT: Number of customers created from file ' . $filename . ' : ' .  $result['customersCreated']);
        $this->logger->log('INFO', 'BULK IMPORT: Failed creating customers ' . $filename . ' : ' .  $result['failedCreatingCustomers']);
        $this->logger->log('INFO', 'BULK IMPORT: Number of customers found by email from file ' . $filename . ' : ' .  $result['customersFoundByEmail']);
        $this->logger->log('INFO', 'BULK IMPORT: Number of customers found by phone from file ' . $filename . ' : ' .  $result['customersFoundByMobile']);
        $this->logger->log('INFO', 'BULK IMPORT: Finished processing file ' . $filename);

        return $importStatus;
    }

    /**
     * @param array $data
     * @throws \Magento\Framework\Exception\AlreadyExistsException
     */
    private function createCustomer(array $data)
    {
        /**@var \Magento\Customer\Model\Customer $customerModel*/
        $customerModel = $this->customerFactory->create();
        $customerModel->setData($this->dataConverter->convertData($data));
        $this->customerResource->save($customerModel);
    }

    /**
     * @param array $data
     * @return array
     */
    private function findByEmail(array $data)
    {
        $customerModel = $this->getByEmail($data['email']);
        $searchResult = [];

        if ($customerModel && $customerModel->getId()) {
            if (!empty($data['mobile_number']) && $data['mobile_number'] == $customerModel->getData('mobile_number')) {
                $verificationStatus = EmailVerification::VERIFIED;
                $searchResult['loyalty_id'] = $data['loyalty_id'];
                $searchResult['unverified_loyalty_id'] = null;
            } else {
                $verificationStatus = EmailVerification::PENDING_VERIFICATION;
                $searchResult['unverified_loyalty_id'] = $data['loyalty_id'];
            }

            $searchResult['email_verification_status'] = $verificationStatus;
            $searchResult['email'] = $customerModel->getData('email');
        }

        return $searchResult;
    }

    /**
     * @param array $data
     * @return array
     */
    private function findByMobile(array $data)
    {
        $customerModel = $this->getByMobile($data['mobile_number']);
        $searchResult = [];

        if ($customerModel && $customerModel->getId()) {
            $searchResult['email_verification_status'] = EmailVerification::PENDING_VERIFICATION;
            $searchResult['unverified_loyalty_id'] = $data['loyalty_id'];
            $searchResult['email'] = $customerModel->getData('email');
        }

        return $searchResult;
    }

    /**
     * @param array $data
     * @return bool
     */
    private function hasEmail(array $data)
    {
        return !empty($data['email']);
    }

    /**
     * @param array $data
     * @return bool
     */
    private function hasPhone(array $data)
    {
        return !empty($data['mobile_number']);
    }

    /**
     * @return \Magento\Customer\Model\ResourceModel\Customer\Collection
     */
    protected function getCollection()
    {
        return $this->customerFactory->create()->getCollection();
    }

    /**
     * @param string $email
     * @return \Magento\Framework\DataObject
     */
    protected function getByEmail(string $email)
    {
        return $this->getCollection()
            ->addFieldToFilter('email', $email)
            ->getFirstItem();
    }

    /**
     * @param string $mobileNumber
     * @return \Magento\Framework\DataObject
     * @throws LocalizedException
     */
    protected function getByMobile(string $mobileNumber)
    {
        return $this->getCollection()
            ->addAttributeToFilter('mobile_number', ['eq' => (int) $mobileNumber])
            ->getFirstItem();
    }
}