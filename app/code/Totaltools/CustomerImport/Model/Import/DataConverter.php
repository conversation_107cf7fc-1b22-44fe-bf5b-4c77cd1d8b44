<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_CustomerImport
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\CustomerImport\Model\Import;

/**
 * Class DataConverter
 * @package Totaltools\CustomerImport\Model\Import
 */
class DataConverter
{
    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;

    /**
     * @var \Totaltools\Customer\Model\Attribute\Source\Position
     */
    private $positionSource;

    /**
     * @var \Totaltools\Customer\Model\Attribute\Source\Role
     */
    private $roleSource;

    /**
     * @var \Totaltools\Pronto\Model\Source\InsiderLevel
     */
    private $insiderLevelSource;

    /**
     * DataConverter constructor.
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Customer\Model\Attribute\Source\Position $positionSource
     * @param \Totaltools\Customer\Model\Attribute\Source\Role $roleSource
     */
    public function __construct(
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Customer\Model\Attribute\Source\Position $positionSource,
        \Totaltools\Customer\Model\Attribute\Source\Role $roleSource,
        \Totaltools\Pronto\Model\Source\InsiderLevel $insiderLevelSource
    )
    {
        $this->storeRepository = $storeRepository;
        $this->positionSource = $positionSource;
        $this->roleSource = $roleSource;
        $this->insiderLevelSource = $insiderLevelSource;
    }

    /**
     * @param array $data
     * @return array
     */
    public function convertData(array $data)
    {
        $store = $this->storeRepository->getStoreByStoreName($data['preferred_store']);
        $data['store'] = $data['_store'];
        $data['website'] = $data['_website'];
        $data['oci_customer'] = (int) ($data['oci_customer'] === 'Yes');
        $data['is_loyal'] = (int) ($data['is_loyal'] === 'Yes');
        $data['is_subscribed_direct_marketing'] = (int) ($data['is_subscribed_direct_marketing'] === 'Yes');
        $data['is_subscribed_sms_promo'] = (int) ($data['is_subscribed_sms_promo'] === 'Yes');
        $data['preferred_store'] = $store && $store->getId() ? $store->getErpId() : null;
        $data['pronto_position'] = $this->getPositionValue($data['pronto_position']) ?: '999';
        $data['role'] = $this->getRoleValue($data['role']);
        $data['loyalty_level'] = $this->getInsiderLevelValue($data['loyalty_level']);

        unset($data['_store']);
        unset($data['_website']);

        return $data;
    }

    /**
     * @param string $position
     * @return string|null
     */
    private function getPositionValue(string $position)
    {
        foreach ($this->positionSource->getAllOptions(false) as $positionOption) {
            if ($positionOption['label']->getText() === $position) {
                return $positionOption['value'];
            }
        }

        return null;
    }

    /**
     * @param string $role
     * @return string|null
     */
    private function getRoleValue(string $role)
    {
        foreach ($this->roleSource->getAllOptions(false) as $roleOptions) {
            if ($roleOptions['label']->getText() === $role) {
                return $roleOptions['value'];
            }
        }

        return null;
    }

    /**
     * @param string $insiderLevel
     * @return string|null
     */
    private function getInsiderLevelValue(string $insiderLevel)
    {
        foreach ($this->insiderLevelSource->getAllOptions() as $levelOptions) {
            if ($levelOptions['label'] === $insiderLevel) {
                return $levelOptions['value'];
            }
        }

        return null;
    }
}