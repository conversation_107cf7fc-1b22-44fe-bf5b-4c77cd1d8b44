<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_CustomerImport
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\CustomerImport\Model\Import;

use Magento\Framework\Exception\LocalizedException;
use Totaltools\CustomerImport\Model\System\SystemConfig;

/**
 * Class Reader
 * @package Totaltools\CustomerImport\Model\Import
 */
class Reader
{
    const FAILED_DIR = 'failed';
    const PROCESSED_DIR = 'processed';

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $systemConfig;

    /**
     * @var \Magento\Framework\Filesystem\Io\File
     */
    protected $ioFile;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csv;

    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var Validator
     */
    protected $validator;

    /**
     * @var string
     */
    protected static $importPath;

    /**
     * Reader constructor.
     * @param SystemConfig $systemConfig
     * @param \Magento\Framework\Filesystem\Io\File $ioFile
     * @param \Magento\Framework\File\Csv $csv
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param Validator $validator
     */
    public function __construct(
        \Totaltools\CustomerImport\Model\System\SystemConfig $systemConfig,
        \Magento\Framework\Filesystem\Io\File $ioFile,
        \Magento\Framework\File\Csv $csv,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        Validator $validator
    )
    {
        $this->systemConfig = $systemConfig;
        $this->ioFile = $ioFile;
        $this->csv = $csv;
        $this->customerRepository = $customerRepository;
        $this->validator = $validator;
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\FileSystemException
     * @throws \Exception
     */
    public function getFiles()
    {
        self::$importPath = $this->systemConfig->getImportFilePath();

        if (!$this->checkImportDirectory()) {
            throw new \Exception('Import folder does not exist');
        }

        $this->ioFile->cd(self::$importPath);
        $files = $this->ioFile->ls();

        return $this->validator->getValidFiles($files);
    }

    /**
     * @param string $fileName
     * @return array
     * @throws LocalizedException
     */
    public function getFileData(string $fileName)
    {
        $data = [];
        $fullFilePath = self::$importPath . '/' . $fileName;
        $fileData = $this->csv->getData($fullFilePath);
        $fileHeaders = [];

        if (!empty($fileData[0])) {
            $fileHeaders = $fileData[0];
            unset($fileData[0]);
        }

        if (!$this->validator->validateHeaders($fileHeaders)) {
            throw new LocalizedException(__('Format of the file %1 is not correct', $fileName));
        }

        foreach ($fileData as $rowNumber => $rowData) {
            $data[] = $this->convertData($rowData, $fileHeaders);
        }

        return $data;
    }

    /**
     * @param string $filename
     */
    public function moveToProcessedDir(string $filename)
    {
        if (!$this->ioFile->fileExists(self::PROCESSED_DIR)) {
            $this->ioFile->mkdir(self::PROCESSED_DIR);
        }

        $this->ioFile->mv(self::$importPath . '/' . $filename, self::$importPath . '/' . self::PROCESSED_DIR . '/' . $filename);
    }

    /**
     * @param string $filename
     */
    public function moveToFailDir(string $filename)
    {
        if (!$this->ioFile->fileExists(self::FAILED_DIR)) {
            $this->ioFile->mkdir(self::FAILED_DIR);
        }

        $this->ioFile->mv(self::$importPath . '/' . $filename, self::$importPath . '/' . self::FAILED_DIR . '/' . $filename);
    }

    /**
     * @param array $row
     * @return array
     */
    private function convertData(array $row, array $headers)
    {
        $converted = [];

        foreach ($row as $key => $value) {
            if (!empty($headers[$key])) {
                $converted[$headers[$key]] = $value;
            }
        }

        return $converted;
    }

    /**
     * @return bool
     */
    protected function checkImportDirectory()
    {
        if (empty(self::$importPath) || !$this->ioFile->fileExists(self::$importPath, false)) {
            return false;
        }

        return true;
    }
}