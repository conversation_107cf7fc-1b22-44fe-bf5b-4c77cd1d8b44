<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_CustomerImport
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\CustomerImport\Model\Import;

/**
 * Class Validator
 * @package Totaltools\CustomerImport\Model\Import
 */
class Validator
{
    const ALLOWED_HEADERS = [
        'email',
        '_website',
        '_store',
        'account_code',
        'confirmation',
        'created_at',
        'created_in',
        'firstname',
        'group_id',
        'is_loyal',
        'is_subscribed_direct_marketing',
        'is_subscribed_sms_promo',
        'lastname',
        'loyalty_id',
        'loyalty_level',
        'mobile_number',
        'oci_customer',
        'preferred_store',
        'pronto_position',
        'role',
        'store_id',
    ];

    const ALLOWED_FORMATS = [
        'csv'
    ];

    /**
     * Check if all columns exist in file
     *
     * @param array $headers
     * @return bool
     */
    public function validateHeaders(array $headers)
    {
        foreach (self::ALLOWED_HEADERS as $index => $necessaryHeader) {
            if (!in_array($necessaryHeader, $headers)) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param array $files
     * @return array
     */
    public function getValidFiles(array $files)
    {
        $validated = [];

        foreach ($files as $index => $file) {
            if ($this->_isValidFormat($file)) {
                $validated[] = $file;
            }
        }

        return $validated;
    }

    /**
     * @param array $fileData
     * @return bool
     */
    private function _isValidFormat(array $fileData)
    {
        if (!empty($fileData['filetype']) && in_array($fileData['filetype'] , self::ALLOWED_FORMATS)) {
            return true;
        }

        return false;
    }
}