<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label" sortOrder="0">
            <label>Total Tools</label>
        </tab>
        <section id="totaltools_customerimport" translate="label" sortOrder="50"
                 showInDefault="1"
                 showInWebsite="1"
                 showInStore="0">
            <label>Customer Bulk Import</label>
            <tab>totaltools</tab>
            <resource>Totaltools_CustomerImport::totaltools_customerimport</resource>
            <group id="import" translate="label" type="text" sortOrder="10"
                   showInDefault="1"
                   showInWebsite="1"
                   showInStore="1">
                <label>Import Settings</label>
                <field id="path_from" translate="label comment" type="text" sortOrder="10"
                       showInDefault="1"
                       showInWebsite="1"
                       showInStore="1">
                    <label>Path from</label>
                    <comment>Path to folder on the server in var folder where from csv files will be taken for import.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
