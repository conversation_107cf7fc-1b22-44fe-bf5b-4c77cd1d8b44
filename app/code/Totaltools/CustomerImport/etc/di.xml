<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Internet
 * @package  Totaltools_CustomerImport
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="customer_bulk_import"
                      xsi:type="object">Totaltools\CustomerImport\Console\Command\CustomerImport</item>
            </argument>
        </arguments>
    </type>
</config>
