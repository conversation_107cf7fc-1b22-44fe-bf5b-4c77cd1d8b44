<?xml version="1.0"?>
<!--
* @category    Totaltools
* @package     Totaltools_Loqate
* <AUTHOR> I<PERSON>bal <<EMAIL>>
* @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="totaltools_loqate" translate="label" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Totaltools Loqate</label>
            <tab>service</tab>
            <resource>Magento_Config::config</resource>
            <group id="settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <field id="enabled" translate="label" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Module</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>

                <field id="licence_key" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
					<label>Licence Key</label>
					<validate>required-entry</validate>

					<!-- do not use encryption because each time Save Config, it will encrypt the already encrypted value -->
					<!--<backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>	-->
					<comment><![CDATA[
						Must be in this format: <b>username-without-domain:password</b>.<br/><br/>
						Get started with a FREE licence key <a href='https://hosted.mastersoftgroup.com/console/#/'>here</a>.
					]]></comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
				</field>

                <field id="url" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>URL</label>
                    <validate>required-entry</validate>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>

                <field id="widget_options" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Default Widget Options</label>
                    <comment>
                        <![CDATA[
						Must be in valid JSON format and use single-quote for String value. For a list of options, see our <a href='http://developer.mastersoftgroup.com/harmony/api/object/address.html#FeatureOption'>FeatureOption</a> reference.
					]]>
                    </comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>

                <field id="widget_options_au" translate="label" type="textarea" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Widget Options for AUSTRALIA</label>
                    <validate>required-entry</validate>
                    <comment>
                        <![CDATA[
						Must be in valid JSON format and use single-quote for String value. <b>If the same key is defined in the Default Widget Options, the value here will take precedence for AUSTRALIA.</b></br></br>
						For a list of options, see our <a href='http://developer.mastersoftgroup.com/harmony/api/object/address.html#FeatureOption'>FeatureOption</a> reference.
					]]>
                    </comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>

                <field id="widget_options_nz" translate="label" type="textarea" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Widget Options for NEW ZEALAND</label>
                    <validate>required-entry</validate>
                    <comment>
                        <![CDATA[
						Must be in valid JSON format and use single-quote for String value. <b>If the same key is defined in the Default Widget Options, the value here will take precedence for NEW ZEALAND.</b><br/><br/>
						For a list of options, see our <a href='http://developer.mastersoftgroup.com/harmony/api/object/address.html#FeatureOption'>FeatureOption</a> reference.
					]]>
                    </comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>