<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Loqate
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Loqate\Block;

use Magento\Framework\View\Element\Template\Context;
use Magento\Directory\Api\CountryInformationAcquirerInterface;
use Totaltools\Loqate\Helper\Data;

class Config extends \Magento\Framework\View\Element\Template
{
    /**
     * @inheritdoc
     */
    protected $_template = 'Totaltools_Loqate::config.phtml';

    /**
     * @var CountryInformationAcquirerInterface
     */
    protected $countryInformationAcquirer;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * @param CountryInformationAcquirerInterface $countryInformationAcquirer
     * @param Data $helper
     * @param Context $context
     */
    public function __construct(CountryInformationAcquirerInterface $countryInformationAcquirer, Data $helper, Context $context)
    {
        $this->countryInformationAcquirer = $countryInformationAcquirer;
        $this->helper = $helper;

        parent::__construct($context);
    }

    /**
     * @return Data
     */
    public function getHelper()
    {
        return $this->helper;
    }

    /**
     * @return string
     */
    public function getDirectoryJson()
    {
        $data = [];

        $countries = $this->countryInformationAcquirer->getCountriesInfo();

        foreach ($countries as $country) {
            // Get regions for this country:
            $regions = [];

            if ($availableRegions = $country->getAvailableRegions()) {
                foreach ($availableRegions as $region) {
                    $regions[$region->getId()] = [
                        'id'   => $region->getId(),
                        'code' => $region->getCode(),
                        'name' => $region->getName()
                    ];
                }
            }

            // Add to data:
            $data[$country->getId()] = [
                'code' => $country->getTwoLetterAbbreviation(),
                'name'   => __($country->getFullNameLocale())
            ];

            if (!empty($regions)) {
                $data[$country->getId()]['regions'] = $regions;
            }
        }

        return \Laminas\Json\Json::encode($data);
    }
}
