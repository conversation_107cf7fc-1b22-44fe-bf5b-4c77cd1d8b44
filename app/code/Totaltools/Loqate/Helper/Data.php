<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Loqate
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Loqate\Helper;

use Magento\Store\Model\ScopeInterface;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @var string
     */
    const XML_PATH_TOTALTOOLS_LOQATE = 'totaltools_loqate/settings/';

    /**
     * @param string $field
     * @return string
     */
    public function getConfig($field)
    {
        return $this->scopeConfig->getValue(self::XML_PATH_TOTALTOOLS_LOQATE . $field, ScopeInterface::SCOPE_STORE);
    }

    /**
     * @return bool
     */
    public function isEnabled()
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_TOTALTOOLS_LOQATE . 'enabled', ScopeInterface::SCOPE_STORE);
    }
}
