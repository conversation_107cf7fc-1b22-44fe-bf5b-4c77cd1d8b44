<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Loqate
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Loqate\Plugin\Block;

class LayoutProcessor
{
    const ELEMENT_TEMPLATE = 'Totaltools_Loqate/form/element/street';

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $block
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess($block, $jsLayout)
    {
        $shippingConfig = &$jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fieldset']; // phpcs:ignore

        if (isset($shippingConfig)) {
            $shippingConfig['children']['street']['children'][0]['config'] = [
                'elementTmpl' => self::ELEMENT_TEMPLATE,
                'notice' => __('Special characters are not allowed. Please use numbers and alphabets only.')
            ];
        }

        $billingConfig = &$jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']['children']['form-fields']; // phpcs:ignore

        if (isset($billingConfig)) {
            $billingConfig['children']['street']['children'][0]['config'] = [
                'elementTmpl' => self::ELEMENT_TEMPLATE,
                'notice' => __('Special characters are not allowed. Please use numbers and alphabets only.')
            ];
        }

        return $jsLayout;
    }
}
