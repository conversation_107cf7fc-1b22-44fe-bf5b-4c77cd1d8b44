define([
        'jquery',
        'ko',
        'mage/url'
    ], function ($, ko, url) {

        'use strict';

        return {

            xhr: null,

            apiUrl: window.loqate_fetch_suburb_api_url,

            apiKey: window.loqate_fetch_suburb_api_key,

            fetchSuburbsByPostcode: function (query, $postcode, suburbs) {
                var self = this;

                $.when(self.fetchByPostcode(query, "AUS", false)).done(function (result) {
                    $postcode
                        .removeClass("avs-active")
                        .parent()
                        .removeClass("loading");

                    if(result && result[0] && result[0].Matches) {
                        $.each(result[0].Matches, function(index, match) {
                            if (typeof match.Locality !== 'undefined') {
                                suburbs.push({
                                    "city": match.Locality,
                                    "cityHtml": match.Locality,
                                    "postcode": match.PostalCodePrimary,
                                    "postcodeHtml": match.PostalCodePrimary,
                                    "country_id": "AU",
                                    "region": match.AdministrativeArea
                                });
                            }
                        });
                    }

                    $('.error_message_search_store').hide();
                }).fail(function (result) {
                    $('.error_message_search_store').show();
                    setTimeout(function () {
                        $postcode
                            .removeClass("avs-active")
                            .parent()
                            .removeClass("loading");
                        $('.error_message_search_store').hide();
                    }, 3000)
                });
            },

            fetchByPostcode: function (postcode, country, showLoader) {
                var _self = this;

                if (postcode.length < 3) {
                    return;
                }

                if (_self.xhr && _self.xhr.readystate != 4) {
                    _self.xhr.abort();
                }

                var data = {
                    "Key": _self.apiKey,
                    "Options": {
                        "Process": "Query",
                        "ServerOptions": {
                            "Table": "rd_AU_vfy",
                            "QueryString": "(Locality LIKE \"" + postcode + "%*\") || (PostalCodePrimary LIKE \"" + postcode + "%*\")",
                            "OutputFields": "PostalCodePrimary,Locality, AdministrativeArea",
                            "QueryClause": "Distinct",
                            "AliasPreference": "First",
                            "RangefieldPreference": "Match"
                        }
                    },
                };

                return _self.xhr = $.ajax({
                    showLoader: showLoader,
                    url: _self.apiUrl,
                    data: JSON.stringify(data),
                    type: "POST",
                    contentType: "application/json; charset=utf-8",
                    dataType: "json"
                });
            }
        };
    }
);