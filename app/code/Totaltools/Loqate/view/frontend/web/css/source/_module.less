//
//  Variables
//  _____________________________________________

@loqate-item__border: 1px solid #DDD;
@loqate-menu__background-color: #FFF;
@loqate-menu-item__color: #333;
@loqate-menu-item-icon__content: '\e815';
@loqate-menu-item-icon__color: @link__color;
@loqate-menu-manual-item__color: @link__color;

//
//  Common
//  _____________________________________________
//@media-common: true;

& when (@media-common = true) {
    div[name*=".street"][aria-busy="true"] {
        position: relative;

        &:before {
            content: '';
            position: absolute;
            z-index: 10;
            background: transparent url('../images/loader-2.gif') no-repeat center center;
            background-size: 24px 24px;
            display: block;
            width: 100%;
            height: 100%;
        }
    }

    .loqate-address {
        position: relative;

        .ui-menu {
            background-color: @loqate-menu__background-color;
            border: @loqate-item__border;
            position: absolute !important;
            z-index: 999;
            top: 0;
            left: 0 !important;
            padding: 0;
            margin: 0;
            list-style: none;
            .lib-css(border-radius, 0, 1);
            .lib-css(box-shadow, 0 8px 17px 0 rgba(0, 0, 0, 0.2), 1);
        }

        .ui-menu-item {
            .lib-font-size(16);
            .lib-line-height(22);
            .lib-css(opacity, 1, 1);
            font-family: @font-family__base;
            color: @loqate-menu-item__color;
            border-bottom: @loqate-item__border;
            margin: 0;
            padding: 12px 0 12px 35px;
            position: relative;
            cursor: pointer;

            &:hover {
                background-color: #F0F0F0;
            }

            .lib-icon-font(
                @loqate-menu-item-icon__content,
                @_icon-font-size: 22px,
                @_icon-font-color: @loqate-menu-item-icon__color,
                @_icon-font-margin: -2px 8px 0 8px
            );

            &:before {
                position: absolute;
                left: 0;
            }

            display: block;

            a {
                cursor: pointer;
                .lib-css(color, @loqate-menu-item__color);
            }

            &:hover a,
            a:hover {
                background: none;
                border: 0 none;
                text-decoration: none;
            }
        }

        .ui-manual-item {
            background-color: @loqate-menu__background-color;
            .lib-font-size(16);
            border: 0 none;
            padding: 18px 0;
            text-align: center;
            color: @loqate-menu-manual-item__color;
            border-top: @loqate-item__border;
            cursor: pointer;

            a {
                color: @loqate-menu-manual-item__color;
                font-family: @font-family__base;
                text-decoration: underline;
            }

            &:before {
                display: none;
            }
        }
    }

    .ui-hidden {
        display: none !important;
    }
}
