<?php

/** @var \Totaltools\Loqate\Block\Config $block */
$helper = $block->getHelper();

if ($helper->isEnabled()): ?>
<script>
    window.totaltoolsLoqateConfig = {
        keyConfig: '<?= /** @noEscape */ $helper->getConfig('licence_key'); ?>',
        urlConfig: '<?= /** @noEscape */ $helper->getConfig('url'); ?>',
        widgetOptions: <?= /** @noEscape */ $helper->getConfig('widget_options') ?? '{}'; ?>,
        widgetOptionsAU: <?= /** @noEscape */ $helper->getConfig('widget_options_au') ?? '{}'; ?>,
        widgetOptionsNZ: <?= /** @noEscape */ $helper->getConfig('widget_options_nz') ?? '{}'; ?>,
        directory: <?= /** @noEscape */ $block->getDirectoryJson() ?? '{}'; ?>
    }
</script>
<?php endif; ?>
