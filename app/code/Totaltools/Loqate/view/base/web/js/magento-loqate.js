/**
 * @category    Totaltools
 * @package     Totaltools_Loqate
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */
 define([
    'jquery',
    'mage/template',
    'mage/translate',
    'jquery-ui-modules/widget',
    'jquery-ui-modules/autocomplete'
], function ($, mageTemplate, $t) {
    'use strict';

    $.widget('totaltools.loqate', {
        options: {
            directory: {},
            isAdmin: false,
            manual: !1,
            fieldMap: {
                street: 'street[]',
                street2: 'street[1]',
                locality: 'city',
                region: 'region',
                regionId: 'region_id',
                postcode: 'postcode',
                countryId: 'country_id',
            },
            skippedFields: ['countryId', 'regionId'],
            fieldClass: 'div.field',
            addressClass: '.selected-address',
            errorClass: '._error',
            errorElClass: '.field-error > span',
            addressTmpl: `<div aria-label="address" class="selected-address<% if (errors.length) { %> _error<% } %>"><address><%= address %></address><% if (errors.length) { %><div class="validation-error" aria-invalid="true"><%= errors.join('<br/>') %></div><% } %><a class="close"><span class="close-icon">x</span></a></div>`,
        },
        fakeInput: null,
        form: null,
        errors: [],
        selectedAddress: {},

        /**
         * @inheritdoc
         */
        _create: function () {
            this.form = this.element.length
                ? this.element[0].form
                    ? this.element[0].form
                    : $(this.element).parents('.address-form-fields')
                : null;

            this._setOptions(window.totaltoolsLoqateConfig || {})
                ._createFakeInput()
                ._generateID()
                ._buildDropdown()
                ._fetchFieldsData()
                ._buildAddressBox();

            this.element.on('manual', function() {
                this._showInputs();
                this.element.data('ui-autocomplete') && this.element.autocomplete('destroy');
                this.options.manual = !0;
                $(this.form)
                    .find(this.options.errorClass)
                    .eq(0)
                    .focus();
                this.element.parents('.modal-content').css({ overflowY: 'auto' });
            }.bind(this));

            return this;
        },

        _createFakeInput: function() {
            if (this.options.isAdmin) {
                this.fakeInput = this.element;
                return this;
            }

            this.fakeInput = $('<input>').attr({
                id: this.element.attr('id'),
                class: "input-text",
                type: 'text',
                name: 'address_placehoder',
                placeholder: $t('Start typing your address')
            });

            this.fakeInput.on('input blur', function(ev) {
                if (ev?.target?.value) {
                    this.fakeInput.closest(this.options.fieldClass)
                        .removeClass('_error');
                    this.fakeInput.closest(this.options.fieldClass)
                        .find('.field-error')
                        .remove();
                    this.element.val(ev.target.value);
                    this.element.trigger('change');
                }
            }.bind(this));

            const note = '<div class="note">Start typing and select your address from the list of addresses. <span>Please do not use the browser autofill feature.</span></div>';

            const addressField = $('<div class="field"/>').append(this.fakeInput);
            addressField.prepend(note);
            addressField.insertBefore(this.element.parent().parent());

            this.fakeInput.on('input', (ev) => {
                this.element.val(ev.target.value);
            });

            return this;
        },

        /**
         * Builds address box when sufficient fields are filled.
         */
        _buildAddressBox: function () {
            if (this.options.isAdmin) return this;

            let address = [];

            this.processFieldError(this.element[0]);

            for (let key of Object.keys(this.selectedAddress)) {
                if (this.selectedAddress[key] && !this.options.skippedFields.includes(key)) {
                    address.push(`<span class="${key}">${this.selectedAddress[key].trim()}</span>`);
                }
            }

            if (address.length >= 4) {
                var self = this,
                    addressDiv = this.fakeInput.parent(),
                    template = mageTemplate(this.options.addressTmpl);

                addressDiv.after(
                    $(template({ address: address.join(', '), errors: self.errors }))
                        .delegate('.close', 'click', function (ev) {
                            $(ev.target).parent().remove();
                            self.fakeInput.parent().show();
                            self.element.val('')
                                .trigger('keyup')
                                .trigger('change');
                            addressDiv.show();
                        })
                );

                this.fakeInput.val('')
                    .parent()
                    .hide();
            }

            this._hideInputs();

            return this;
        },

        /**
         * Read all address form fields and store values in selectedAddress object.
         */
        _fetchFieldsData: function () {
            const fields = this.options.fieldMap;

            this.selectedAddress = {};
            this.errors = [];

            if (this.element.val()) {
                this.selectedAddress['street'] = this.element.val();
            }

            for (var key of Object.keys(fields)) {
                let name = fields[key],
                    field = $(this.form)
                        .find(`[name="${name}"]`);

                if (field.length) {
                    this.observeField(field[0]);
                    this.processFieldError(field[0]);

                    if (field.val() && field.val().length) {
                        this.selectedAddress[key] = $.trim(field.val());
                    }
                }
            }

            if (this.selectedAddress.countryId) {
                const { countryId, regionId } = this.selectedAddress;

                this.selectedAddress.region = this.getStateCode(countryId, regionId);
                this.selectedAddress.country = this.getCountryName(countryId);
            }

            return this;
        },

        /**
         * Build an address dropdown that provides autocomplete search as user types.
         */
        _buildDropdown: function () {
            var self = this,
                opts = this.options,
                auth = this._getAuthorization();

            this.fakeInput.parent()
                .addClass('ui-front loqate-address')
                .css({ position: 'relative' });

            this.fakeInput.autocomplete({
                minLength: 2,
                delay: 50,
                position: { collision: 'flip' },
                appendTo: self.fakeInput.parent(),
                source: function (request, response) {
                    if (!auth || auth == '') return;

                    $.ajax({
                        url: `${opts.urlConfig}/harmony/rest/v2/address/find`,
                        type: 'POST',
                        data: JSON.stringify({
                            payload: [
                                {
                                    fullAddress: request.term,
                                    country: 'AU',
                                },
                            ],
                            sourceOfTruth: 'AUPAF',
                            featureOptions: opts.widgetOptions,
                            transactionID: window.loqateToken,
                        }),
                        contentType: 'application/json',
                        headers: {
                            Authorization: auth,
                        },
                        success: function (res) {
                            this.selectedAddress = {};
                            this.errors = [];
                            var data = [];
                            res.status == 'SUCCESS'
                                ? ((data = $.map(res.payload, function (a) {
                                      return {
                                          label: a.fullAddress,
                                          value: a.id,
                                      };
                                  })),
                                  response(data))
                                : console.info(
                                      res.messages && res.messages.length > 0
                                          ? res.messages[0]
                                          : 'Request is not successful. Please contact admin.'
                                  );
                        },
                        error: function (err) {
                            console.error(err);
                        },
                    });
                },
                response: function(ev, ui) {
                    if (ui.content.length) {
                        var manualAddress = {
                            value:"#manual",
                            label: $t("Let me type my address")
                        };
                        ui.content.push(manualAddress);
                    }
                },
                open: function (ev, ui) {
                    self.element
                        .parents('.modal-content')
                        .css({ overflowY: 'initial' });

                    $(this).autocomplete('widget')
                        .find('.ui-manual-link').on('click',  function(ev) {
                            ev.preventDefault();
                            self.element.trigger('manual');
                        });
                },
                close: function(ev, ui) {
                    self.element.parents('.modal-content').css({ overflowY: 'auto' });
                },
                select: function (ev, address) {
                    ev.preventDefault();

                    self.fakeInput
                        .parents(self.options.fieldClass)
                        .attr('aria-busy', !0);

                    self.retrieve(address.item).done(function (res) {
                        if (res.status == 'SUCCESS') {
                            self._fetchFieldsData();
                            !opts.isAdmin && self._buildAddressBox();
                        }
                    });
                },
            }).data("ui-autocomplete")
                ._renderItem = function(ul, item) {
                    let linkClass, lineClass;
                    let label = String(item.label).replace(new RegExp(this.term, "gi"),"<b>$&</b>");

                    if (item.value === '#manual') {
                        lineClass = 'ui-manual-item';
                        linkClass = 'ui-manual-link';
                    }

                    return $('<li></li>')
                        .addClass(lineClass)
                        .data("ui-autocomplete-item", item)
                        .append(`<a class="${linkClass}">${label}</a>`)
                        .appendTo(ul);
                };

            return this;
        },

        /**
         * Create Base64 encoded authorisation token.
         * @returns {String}
         */
        _getAuthorization: function () {
            return 'Basic ' + btoa(this.options.keyConfig);
        },

        /**
         * Hides address fields other than street input.
         */
        _hideInputs: function () {
            const fields = this.options.fieldMap;

            for (var key of Object.keys(fields)) {
                $(this.form)
                    .find('[name="' + fields[key] + '"]')
                    .parents(this.options.fieldClass)
                    .attr('aria-hidden', !0)
                    .addClass('ui-hidden');
            }

            return this;
        },

        /**
         * Shows address fields other than street input.
         */
        _showInputs: function () {
            const fields = this.options.fieldMap;

            this.fakeInput.parent().hide();
            this.element
                .parent()
                .next(this.options.addressClass)
                .remove();

            for (var key of Object.keys(fields)) {
                $(this.form)
                    .find('[name="' + fields[key] + '"]')
                    .parents(this.options.fieldClass)
                    .attr('aria-hidden', !1)
                    .removeClass('ui-hidden');
            }

            return this;
        },

        /**
         * @param {String} key
         */
         processErrors: function () {
            if (this.errors.length && !this.options.manual)
                this.element.trigger('manual');
        },

        /**
         * @param {HTMLElement} el
         */
         observeField: function (el) {
            let MutationObserver = window.MutationObserver || window.WebKitMutationObserver || window.MozMutationObserver,
                observer = new MutationObserver(this.validationCallback.bind(this));

            observer.observe(el, {
                attributes: true,
                attributeFilter: ['aria-invalid'],
            });

            return this;
        },

        /**
         * @param {MutationRecord[]} mutations
         */
        validationCallback: function (mutations) {
            let self = this,
                hasErrors = false;

            if (this.element.attr('aria-invalid')) {
                const inputParent = this.fakeInput.closest(self.options.fieldClass);
                const fieldError = this.element.closest(self.options.fieldClass)
                    .find('.field-error')
                    .clone();

                if (!inputParent.hasClass('_error') && fieldError?.length) {
                    inputParent.append(fieldError);
                    inputParent.addClass('_error');
                }
            } else {
                this.fakeInput.closest('.field')
                    .removeClass('_error')
            }

            if (document.activeElement == this.fakeInput[0] || this.element.val() == '')
                return;

            mutations.forEach(function (mutation) {
                if (mutation.type == 'attributes') {
                    let invalid = Boolean(
                        mutation.target.getAttribute('aria-invalid')
                    );

                    if (invalid) {
                        hasErrors = true;
                        self.processFieldError(mutation.target);
                    }
                }
            });

            hasErrors && this.processErrors();
        },

        /**
         * @param {HTMLElement} el
         * @param {String} key
         * @returns {String}
         */
        processFieldError: function (el) {
            let fieldError = $(el.parentNode)
                    .find(this.options.errorElClass)
                    .text(),
                fieldLabel = $(el).closest(this.options.fieldClass)
                    .find('label > span')
                    .text();

            if (fieldError && fieldError.trim().length) {
                let msg = `${fieldLabel}: ${fieldError.trim()}`;

                if (!this.errors.includes(msg))
                    this.errors.push(msg);
            }

            return fieldError;
        },

        /**
         *
         */
        _generateID: function () {
            var opts = this.options,
                auth = this._getAuthorization();

            if (window.loqateToken || !auth || auth == '') return this;

            $.ajax({
                url: `${opts.urlConfig}/harmony/rest/au/generateID`,
                type: 'POST',
                data: JSON.stringify({
                    payload: [],
                }),
                contentType: 'application/json',
                headers: {
                    Authorization: auth,
                },
                success: function (res) {
                    window.loqateToken = res.payload;
                },
                error: function (err) {
                    window.loqateToken = null;
                },
            });

            return this;
        },

        /**
         * Retrieve an address object by its id.
         * @param {Object} address
         * @returns {$.ajax}
         */
        retrieve: function (address) {
            var self = this,
                opts = this.options,
                auth = this._getAuthorization();

            if (!auth || auth == '') return;

            return $.ajax({
                url: `${opts.urlConfig}/harmony/rest/v2/address/retrieve`,
                type: 'POST',
                beforeSend: function() {
                    $(self.form)
                        .find(`[name="${opts.fieldMap.countryId}"]`)
                        .val(address?.value?.split('|')[0] ?? '')
                        .trigger('change');
                },
                data: JSON.stringify({
                    payload: [
                        {
                            id: address.value,
                        },
                    ],
                    featureOptions: {
                        exposeAttributes: '1',
                        displayGnafLot: '1',
                        caseType: 'TITLE',
                    },
                }),
                contentType: 'application/json',
                headers: {
                    Authorization: auth,
                },
                success: function (res) {
                    if (res.payload.length) {
                        self._mapAddress(res.payload[0]);
                    }
                },
                error: function (err) {
                    console.log(err);
                },
                complete: function (xhr) {
                    self.fakeInput
                        .parents(self.options.fieldClass)
                        .attr('aria-busy', !1);
                },
            });
        },

        /**
         * Popuplate address form fields with selected address.
         * @param {Object} addr
         */
        _mapAddress: function (addr) {
            const fields = this.options.fieldMap;

            let street = $.trim(`${addr.subdwelling} ${addr.streetNumber} ${addr.streetName} ${addr.streetType}`);

            if (!street.length) {
                street = addr.postal;
            }

            addr.countryId = addr.attributes.CountryIso2;
            addr.region_id = addr.regionId = this.getStateId(addr.countryId, addr.state);

            $(this.form).find(`[name="${fields.street2}"]`).val('').trigger('keyup');

            for (var key of Object.keys(fields)) {
                let field = fields[key],
                    formField = $(this.form).find('[name="' + field + '"]'),
                    value = addr[key] ? addr[key] : '';

                if (key == 'street') {
                    formField = this.element;
                    value = street;
                }

                if (!formField.length || !value.length) {
                    continue;
                }

                formField.val(value);
                formField.prop('tagName') == 'SELECT'
                    ? formField.trigger('change')
                    : formField.trigger('keyup');
            }
        },

        /**
         * Get country name based on ISO code.
         *
         * @param {String} countryId
         * @returns {String}
         */
        getCountryName: function(countryId) {
            return this.options.directory?.[countryId]?.name ?? countryId;
        },

        /**
         * Get state Id by given country and region code.
         *
         * @param {String} countryCode
         * @param {String} stateCode
         * @returns {String}
         */
        getStateId: function (countryCode, stateCode) {
            const countryRegions = this.options?.directory?.[countryCode]?.['regions'] ?? {};

            return Object.keys(countryRegions).find(
                (id) => countryRegions[id]['code'] == stateCode
            );
        },

        /**
         * Get state code by given country and region Id.
         *
         * @param {String} countryId
         * @param {*} regionId
         * @returns
         */
        getStateCode: function (countryId, regionId) {
            return this.options.directory?.[countryId]?.['regions']?.[regionId]?.code;
        },
    });

    return $.totaltools.loqate;
});
