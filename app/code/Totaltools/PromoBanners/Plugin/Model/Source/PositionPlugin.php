<?php

namespace Totaltools\PromoBanners\Plugin\Model\Source;

/**
 * @category    Totaltools
 * @package     Totaltools_PromoBanners
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2024 (c) Totaltools. <https://totaltools.com.au>
 */

use Amasty\PromoBanners\Model\Source\Position;
use Totaltools\PromoBanners\Model\Rule;

class PositionPlugin
{
    public function afterToOptionArray(Position $subject, array $options): array
    {
        if (!empty($options)) {
            $options[Rule::POS_MINICART] = __('Mini Cart');
            $options[Rule::POS_CHECKOUT_SUMMARY] = __('Checkout Summary');
        }

        return $options;
    }
}