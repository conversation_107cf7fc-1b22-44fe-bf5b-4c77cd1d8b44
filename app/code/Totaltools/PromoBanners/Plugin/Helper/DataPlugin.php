<?php

namespace Totaltools\PromoBanners\Plugin\Helper;

/**
 * @category    Totaltools
 * @package     Totaltools_PromoBanners
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2024 (c) Totaltools. <https://totaltools.com.au>
 */

use Amasty\PromoBanners\Helper\Data;
use Totaltools\PromoBanners\Model\Rule;

class DataPlugin
{
    public function afterGetPosition(Data $subject, array $positions): array
    {
        if (!empty($positions)) {
            $positions[Rule::POS_MINICART] = __('Mini Cart');
            $positions[Rule::POS_CHECKOUT_SUMMARY] = __('Checkout Summary');
        }

        return $positions;
    }
}