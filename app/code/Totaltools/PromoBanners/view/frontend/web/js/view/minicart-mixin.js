define(["jquery", "Magento_Customer/js/customer-data"], function (
    $,
    customerData
) {
    "use strict";

    var miniCart = $("[data-block='minicart']"),
        bannersInitialized = false;

    return function (Minicart) {
        return Minicart.extend({
            /**
             * @inheritdoc
             */
            initialize: function () {
                this._super();
                this._registerListeners();
            },

            /**
             * Register event listeners
             */
            _registerListeners: function () {
                miniCart.on("dropdowndialogopen", function () {
                    if (!bannersInitialized) {
                        $(window).trigger('amShopBy:afterReloadHtml');
                        bannersInitialized = true;
                    }
                });
            },
        });
    };
});
