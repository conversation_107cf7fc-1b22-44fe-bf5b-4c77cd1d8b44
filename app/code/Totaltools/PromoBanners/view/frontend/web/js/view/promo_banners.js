define([
    'jquery',
    'ko',
    'uiComponent',
    'Magento_Customer/js/customer-data',
    'Amasty_PromoBanners/js/loader',
], function ($, ko, Component, customerData, bannerLoader) {
    'use strict';

    var bannersConfig = customerData.get('promo-banners');

    return Component.extend({
        /**
         * @var {ko.observable}
         */
        bannersConfig: bannersConfig,

        /**
         * @inheritdoc
         */
        initialize: function() {
            this._super();
            this.initDynamicBanners();
        },

        /**
         * @returns {this}
         */
        initDynamicBanners: function() {
            this.bannersConfig.subscribe((config) => {
                this.initBanners(config);
            });

            return this;
        },

        /**
         *
         * @param {Object} config
         * @returns {void}
         */
        initBanners: function(config) {
            if (JSON.stringify(config) !== '{}') {
                let bannersConf = $.extend({ bannersData: config }, {
                    bannerContainer: '[data-role="amasty-dynamic-banner-container"]'
                });

                bannerLoader['Amasty_PromoBanners/js/loader'](bannersConf);
            }
        },

        /**
         * @param {HTMLElement} elem
         * @returns {void}
         */
        renderCallback: function (elem) {
            setTimeout(() => {
                this.initBanners(this.bannersConfig());
            }, 600);
        },
    });
});
