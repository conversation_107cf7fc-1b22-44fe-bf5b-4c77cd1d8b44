<?xml version="1.0"?>
<!--
/**
 * @category    Totaltools
 * @package     Totaltools_PromoBanners
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2024 (c) Totaltools. <https://totaltools.com.au>
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="sidebar" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="summary" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="promo-banners" xsi:type="array">
                                                    <item name="component" xsi:type="string">Totaltools_PromoBanners/js/view/promo_banners</item>
                                                    <item name="displayArea" xsi:type="string">after_summary</item>
                                                    <item name="sortOrder" xsi:type="number">10</item>
                                                    <item name="config" xsi:type="array">
                                                        <item name="template" xsi:type="string">Totaltools_PromoBanners/promo_banners</item>
                                                        <item name="position" xsi:type="number">17</item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>
    </body>
</page>