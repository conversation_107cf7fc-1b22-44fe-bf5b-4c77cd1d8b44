<?php

namespace Totaltools\PromoBanners\CustomerData;

/**
 * @category    Totaltools
 * @package     Totaltools_PromoBanners
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2024 (c) Totaltools. <https://totaltools.com.au>
 */

use Magento\Customer\CustomerData\SectionSourceInterface;
use Amasty\PromoBanners\Model\Banner\Data;
use Totaltools\PromoBanners\Model\Rule;

class PromoBanners implements SectionSourceInterface
{
    /**
     * @var Data
     */
    public Data $dataSource;

    /**
     * @param Data $dataSource
     */
    public function __construct(Data $dataSource)
    {
        $this->dataSource = $dataSource;
    }

    /**
     * @inheritdoc
     */
    public function getSectionData()
    {
        $banners = $this->dataSource->getBanners();

        if (empty($banners)) return [];

        return $this->filterStaticSections($banners);
    }

    /**
     * @param array $banners
     * @return array
     */
    protected function filterStaticSections(array $banners): array
    {
        $dynamicSections = [
            Rule::POS_MINICART,
            Rule::POS_CHECKOUT_SUMMARY
        ];

        if (isset($banners['sections'])) {
            foreach($banners['sections'] as $sectionId => $section) {
                if (!in_array($sectionId, $dynamicSections)) {
                    unset($banners['sections'][$sectionId]);
                }
            }
        }

        return $banners;
    }
}