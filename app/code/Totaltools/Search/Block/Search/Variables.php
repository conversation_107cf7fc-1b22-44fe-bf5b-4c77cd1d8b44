<?php

namespace Totaltools\Search\Block\Search;

/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright (c) 2020 Totaltools Pty Ltd. <https://www.totaltools.com.au>
 */

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Directory\Model\CurrencyFactory;
use Totaltools\Search\Helper\Data;

class Variables extends Template
{
    /**
     * @var CurrencyFactory
     */
    private $currencyFactory;

    /**
     * @var Data
     */
    private $helper;

    /**
     * @param CurrencyFactory $currencyFactory,
     * @param Data $helper,
     * @param Context $context,
     * @param array $data = []
     */
    public function __construct(
        CurrencyFactory $currencyFactory,
        Data $helper,
        Context $context,
        array $data = []
    ) {
        $this->currencyFactory = $currencyFactory;
        $this->helper = $helper;

        parent::__construct($context, $data);
    }

    public function getAutosuggestUrl(): string
    {
        return sprintf(
            $this->helper->getAutoSuggestEndpoint($this->_storeManager->getStore()),
            $this->getApiKey(),
            $this->getSiteKey()
        );
    }

    public function getAutocompleteUrl(): string
    {
        return sprintf(
            $this->helper->getAutoCompleteEndpoint($this->_storeManager->getStore()),
            $this->getApiKey(),
            $this->getSiteKey()
        );
    }

    public function getSearchUrl(): string
    {
        return sprintf(
            $this->helper->getSearchEndpoint($this->_storeManager->getStore()),
            $this->getApiKey(),
            $this->getSiteKey()
        );
    }

    /**
     * Get currently active currency code.
     *
     * @return string
     */
    public function getCurrencyCode()
    {
        return $this->_storeManager->getStore()->getCurrentCurrency()->getCode();
    }

    /**
     * Get currently active currency symbol.
     *
     * @return string
     */
    public function getCurrencySymbol()
    {
        $currencyCode = $this->getCurrencyCode();

        if (!$currencyCode) return null;

        return $this->currencyFactory->create()->load($currencyCode)->getCurrencySymbol();
    }

    /**
     * Get unbxd site key for current store.
     *
     * @return string
     */
    public function getSiteKey(): string
    {
        return $this->helper->getSiteKey($this->_storeManager->getStore());
    }

    /**
     * Get unbxd api key for current store.
     *
     * @return string
     */
    public function getApiKey(): string
    {
        return $this->helper->getApiKey($this->_storeManager->getStore());
    }

    /**
     * Get page size for unbxd search.
     *
     * @return int
     */
    public function getSearchPageSize()
    {
        return (int) $this->helper->getSearchPageSize();
    }

    /**
     * Get currently active store name.
     *
     * @return string
     */
    public function getCurrentStoreName()
    {
        return $this->_storeManager->getStore()->getName();
    }

    /**
     * Get placeholder image url.
     *
     * @return string
     */
    public function getPlaceholderImgUrl()
    {
        $mediaUrl = $this->_storeManager->getStore()->getBaseUrl(\Magento\Framework\UrlInterface::URL_TYPE_MEDIA);
        $placeHolderPath = $this->_storeManager->getStore()->getConfig('catalog/placeholder/thumbnail_placeholder');

        return ($mediaUrl && $placeHolderPath) ? $mediaUrl . 'catalog/product/placeholder/' . $placeHolderPath : null;
    }

    /**
     * Get autosuggest typing delay setting.
     *
     * @return int
     */
    public function getAutosuggestTypingDelay()
    {
        return (int) $this->helper->getAutosuggestTypingDelay();
    }

    /**
     * @return boolean
     */
    public function getShowPrice()
    {
        return (bool) $this->helper->getShowPrice() ?: 0;
    }

    /**
     * @return Data
     */
    public function getHelper()
    {
        return $this->helper;
    }
}
