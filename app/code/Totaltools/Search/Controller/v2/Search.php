<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Controller\v2;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\JsonFactory;
use Totaltools\Search\Service\SearchService;

class Search extends Action
{
    /**
     * @var SearchService
     */
    private $searchService;
    /**
     * @var JsonFactory
     */
    private $jsonFactory;

    /**
     * Search constructor.
     * @param Context $context
     * @param JsonFactory $jsonFactory
     * @param SearchService $searchService
     */
    public function __construct(
        Context $context,
        JsonFactory $jsonFactory,
        SearchService $searchService
    ) {
        parent::__construct($context);
        $this->jsonFactory = $jsonFactory;
        $this->searchService = $searchService;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Json|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $jsonFactory = $this->jsonFactory->create();
        $data = ['data' => $this->searchService->getSearchResult()];
        $jsonFactory->setData($data);
        return $jsonFactory;
    }
}
