<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Controller\V2;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Staging\Controller\Result\JsonFactory;
use Totaltools\Search\Service\AutoSuggestService;

class Autocomplete extends Action
{
    /**
     * @var JsonFactory
     */
    private $jsonFactory;

    /**
     * @var AutoSuggestService
     */
    private $autoSuggestService;

    /**
     * Autocomplete constructor.
     * @param Context $context
     * @param JsonFactory $jsonFactory
     * @param AutoSuggestService $autoSuggestService
     */
    public function __construct(
        Context $context,
        JsonFactory $jsonFactory,
        AutoSuggestService $autoSuggestService
    )
    {
        parent::__construct($context);
        $this->jsonFactory = $jsonFactory;
        $this->autoSuggestService = $autoSuggestService;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Json|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $jsonFactory = $this->jsonFactory->create();
        $data = ["data" => $this->autoSuggestService->getAutoSuggestResult()];
        $jsonFactory->setData($data);
        return $jsonFactory;
    }
}
