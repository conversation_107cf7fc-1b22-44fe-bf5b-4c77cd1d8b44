<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Api\Search;

use Totaltools\Search\Model\Search\Response\MetaData;

interface MetaDataInterface
{
    /**
     * @return MetaData
     */
    public function getMetaData() : array;

    /**
     * @return int|null
     */
    public function getStatusCode() : ?int;

    /**
     * @param int $statusCode
     * @return \Totaltools\Search\Model\Search\Response\MetaData
     */
    public function setStatusCode(int $statusCode) : MetaData;

    /**
     * @return int
     */
    public function getPageNo() : int;

    /**
     * @param int $pageNo
     * @return MetaData
     */
    public function setPageNo(int $pageNo) : MetaData;

    /**
     * @return int
     */
    public function getTotalCount() : int;

    /**
     * @param int $totalCount
     * @return MetaData
     */
    public function setTotalCount(int $totalCount) : MetaData;

    /**
     * @return string|null
     */
    public function getErrorCode() : ?string;

    /**
     * @param string $errorCode
     * @return MetaData
     */
    public function setErrorCode(string $errorCode) : MetaData;

    /**
     * @return string|null
     */
    public function getMessage() : ?string;

    /**
     * @param string $message
     * @return MetaData
     */
    public function setMessage(string $message) : MetaData;

    /**
     * @return Time taken to query results in milliseconds
     */
    public function getQueryTime();

    /**
     * @param $queryTime
     * @return MetaData
     */
    public function setQueryTime($queryTime) : MetaData;

    /**
     * @return Stats. Refer {@link Stats}
     */
    public function getStats();

    /**
     * @param $stats
     * @return MetaData
     */
    public function setStats($stats) : MetaData;

    /**
     * @return mixed
     */
    public function getQueryParams();

    /**
     * @param $queryParams
     * @return MetaData
     */
    public function setQueryParams($queryParams) : MetaData;

    /**
     * @return int
     */
    public function getPageSize() : int;

    /**
     * @param int $pageSize
     * @return MetaData
     */
    public function setPageSize(int $pageSize) : MetaData;

    /**
     * @param string $displayMessage
     * @return MetaData
     */
    public function setDisplayMessage(string $displayMessage) : MetaData;

    /**
     * @return string
     */
    public function getDisplayMessage() : ?string;

    /**
     * @param int $storeId
     * @return MetaData
     */
    public function setStoreId(int $storeId) : MetaData;

    /**
     * @return int
     */
    public function getStoreId() : ?int;
}
