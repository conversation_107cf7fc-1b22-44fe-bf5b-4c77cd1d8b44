<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Api\Search;


interface SearchClientInterface
{
    /**
     * @return mixed
     */
    public function execute();

    /**
     * @param string $query
     * @param array $queryParams
     * @return mixed
     */
    public function search(string $query, array $queryParams=array());

    /**
     * @param string $type
     * @return mixed
     */
    public function setRequestType(string $type);

    /**
     * @param string $categoryFilter
     * @return mixed
     */
    public function setCategoryFilter(string $categoryFilter);
}
