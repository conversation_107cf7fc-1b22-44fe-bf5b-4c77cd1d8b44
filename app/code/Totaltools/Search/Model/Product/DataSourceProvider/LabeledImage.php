<?php

namespace Totaltools\Search\Model\Product\DataSourceProvider;

/**
 * @package     Totaltools_Search
 * <AUTHOR> Iqbal <<EMAIL>
 * @copyright   2020 (c) Totaltools.<https://totaltools.com.au>
 */

use Magento\Catalog\Model\Product\Media\ConfigInterface;
use Unbxd\ProductFeed\Model\Indexer\Product\Full\DataSourceProviderInterface;
use Unbxd\ProductFeed\Model\Feed\DataHandler\Image as ImageDataHandler;
use Psr\Log\LoggerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Exception;

class LabeledImage implements DataSourceProviderInterface
{
    /**
     * Related data source code
     */
    const DATA_SOURCE_CODE = 'labeled_image';

    /**
     * @var ImageDataHandler
     */
    protected $imageDataHandler;

    /**
     * @var ConfigInterface
     */
    protected $mediaConfig;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * Constructor.
     * @param ImageDataHandler $imageDataHandler
     * @param ConfigInterface $mediaConfig
     * @param LoggerInterface $logger
     */
    public function __construct(
        ImageDataHandler $imageDataHandler,
        ConfigInterface $mediaConfig,
        LoggerInterface $logger
    ) {
        $this->imageDataHandler = $imageDataHandler;
        $this->mediaConfig = $mediaConfig;
        $this->logger = $logger;
    }

    /**
     * {@inheritdoc}
     */
    public function getDataSourceCode()
    {
        return self::DATA_SOURCE_CODE;
    }

    /**
     * Append image data to the product index data
     *
     * {@inheritdoc}
     */
    public function appendData($storeId, array $indexData)
    {
        foreach (array_keys($indexData) as $key) {
            try {
                $data = $indexData[$key];
                $imagePath = isset($data[self::DATA_SOURCE_CODE]) ? implode($data[self::DATA_SOURCE_CODE]) : false;
                if ($imagePath && $imagePath != 'no_selection' && $key != 'fields' ) {
                    $productId = $data['entity_id'];
                    $indexData[$key][self::DATA_SOURCE_CODE] = $this->imageDataHandler->getImageUrl($productId, $imagePath, self::DATA_SOURCE_CODE, $storeId);
                }
            } catch (NoSuchEntityException | LocalizedException | Exception $e) {
                $this->logger->emergency($e->getMessage());
            }
        }

        $indexData = $this->addIndexedFields($indexData);

        return $indexData;
    }

    /**
     * @param $indexData
     * @return mixed
     */
    private function addIndexedFields($indexData)
    {
        $alreadyExistFields = array_key_exists('fields', $indexData) ? $indexData['fields'] : [];
        $indexData['fields'] = array_merge($alreadyExistFields, LabeledImageSchema::LABELED_IMAGE_SCHEMA_DEFINITION);
        return $indexData;
    }
}
