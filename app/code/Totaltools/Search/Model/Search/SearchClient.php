<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search;

use Magento\Framework\HTTP\Header;
use Magento\Framework\HTTP\PhpEnvironment\Request;
use Magento\Framework\Session\SessionManagerInterface;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Search\Api\Search\MetaDataInterface;
use Totaltools\Search\Api\Search\SearchClientInterface;
use Totaltools\Search\Model\Search\Exception\SearchException;
use Totaltools\Search\Helper\Data;
use Totaltools\Search\Model\Search\Response\SearchResponseFactory;

class SearchClient implements SearchClientInterface
{
    const PAGE_SIZE = 24;
    const PAGE_NO = 1;

    private $query = "";
    private $queryParams = [];
    private $categoryIds;
    private $filters = [];
    private $rangefilters = [];
    private $categoryFilter;
    private $sorts = [];
    private $pageNo = 1;
    private $pageSize = 24;
    private $otherParams = [];
    private $results;

    protected $requestType;

    /**
     * @var Data
     */
    private $helper;
    /**
     * @var SearchResponseFactory
     */
    private $searchResponseFactory;

    /**
     * @var MetaDataInterface
     */
    private $metaData;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var Header
     */
    private $httpHeaders;

    /**
     * @var CookieManagerInterface
     */
    private $cookieManager;

    private $httpRequest;

    /**
     * SearchClient constructor.
     * @param SearchResponseFactory $searchResponseFactory
     * @param Data $data
     * @param MetaDataInterface $metaData
     * @param StoreManagerInterface $storeManager
     * @param Header $header
     * @param CookieManagerInterface $cookieManager
     */
    public function __construct(
        SearchResponseFactory $searchResponseFactory,
        Data $data,
        MetaDataInterface $metaData,
        StoreManagerInterface $storeManager,
        Header $header,
        CookieManagerInterface $cookieManager,
        Request $request
    ) {
        $this->helper = $data;
        $this->searchResponseFactory = $searchResponseFactory;
        $this->metaData = $metaData;
        $this->storeManager = $storeManager;
        $this->httpHeaders = $header;
        $this->cookieManager = $cookieManager;
        $this->httpRequest = $request;
    }

    /**
     * @return string
     */
    private function getSearchUrl(): string
    {
        return sprintf(
            $this->helper->getSearchEndpoint($this->storeManager->getStore()),
            $this->helper->getApiKey($this->storeManager->getStore()),
            $this->helper->getSiteKey($this->storeManager->getStore())
        );
    }

    /**
     * @return string
     */
    private function getAutoSuggestUrl(): string
    {
        return sprintf(
            $this->helper->getAutoSuggestEndpoint($this->storeManager->getStore()),
            $this->helper->getApiKey($this->storeManager->getStore()),
            $this->helper->getSiteKey($this->storeManager->getStore())
        );
    }

    /**
     * @return string
     */
    private function getBrowseUrl(): string
    {
        return sprintf(
            $this->helper->getBrowseEndpoint($this->storeManager->getStore()),
            $this->helper->getApiKey($this->storeManager->getStore()),
            $this->helper->getSiteKey($this->storeManager->getStore())
        );
    }

    /**
     * @param boolean $forceCreate
     * @return string
     * @throws SearchException
     */
    public function generateUrl($forceCreate = false)
    {
        if (isset($this->query) && isset($this->categoryIds)) {
            throw new SearchException("Can't set query and node id at the same time");
        }

        try {
            $sb = "";
            if (isset($this->query) || $forceCreate) {

                switch ($this->requestType) {
                    case RequestType::AUTO_SUGGEST_REQUEST:
                        $sb .= $this->getAutoSuggestUrl();


                        if ($popularProducts = $this->getPopularProductFields()) {
                            $sb .= "&fields=" . $popularProducts;
                        }
                        break;
                    default:
                        $sb .= $this->getSearchUrl();
                        break;
                }

                $sb .= !empty($this->query) ? "&q=" . urlencode($this->query) : '';

            }

            if (isset($this->categoryFilter)) {
                $sb .= "&category-filter=" . urlencode($this->categoryFilter);
            }

            if (isset($this->queryParams) && count($this->queryParams)) {
                foreach ($this->queryParams as $key => $value) {
                    $sb .= "&$key=" . urlencode($this->queryParams[$key]);
                }
            }

            if (isset($this->filters) && count($this->filters) > 0) {
                foreach ($this->filters as $key => $val) {
                    $sb .= "&filter=" . urlencode($key . ':"' . join(("\" OR " . $key . ":\""), $val) . '"');
                }
            }

            if (isset($this->rangefilters) && count($this->rangefilters) > 0) {
                foreach ($this->rangefilters as $key => $val) {
                    $sb .= "&filter=" . urlencode($key . ':' . join((" OR " . $key . ":"), $val));
                }
            }

            if (isset($this->sorts) && count($this->sorts) > 0) {
                $sorts = array();
                foreach ($this->sorts as $key => $val) {
                    array_push($sorts, $key . ' ' . strtolower((string)$val));
                }
                $sb .= "&sort=" . urlencode(implode(",", $sorts));
            }


            $sb .= "&page=" . $this->getPageNo();
            $sb .= "&rows=" . $this->getPageSize();


            if (isset($this->otherparams) && count($this->otherparams) > 0) {
                foreach ($this->otherparams as $key => $val) {
                    $sb .= "&" . urlencode($key . '=' . $val);
                }
            }

            return (string)$sb;
        } catch (\Exception $e) {
            throw new SearchException($e);
        }
    }

    /**
     * @param string $query
     * @param array $queryParams
     * @return $this|mixed
     */
    public function search(string $query, array $queryParams = array())
    {
        $this->query = $query;
        $this->queryParams = $queryParams;
        return $this;
    }

    /**
     * @return mixed
     * @throws SearchException
     */
    public function execute()
    {
        try {
            $errors = NULL;
            $url = $this->generateUrl();
            $request = curl_init($url);
            $headers = [
                'user-agent: ' . $this->getUserAgent(),
                'unbxd-user-id: ' . $this->getUnBxdUserId(),
                'X-Forwarded-For: ' . $this->getClientIp(),
                'Accept-Encoding: gzip'
            ];

            curl_setopt($request, CURLOPT_HTTPHEADER, $headers);
            curl_setopt($request, CURLOPT_RETURNTRANSFER, TRUE);
            curl_setopt($request, CURLOPT_ENCODING, "gzip");

            $response = curl_exec($request);

            if (curl_errno($request)) {
                $errors = curl_error($request);
            }

            $info = curl_getinfo($request);
            curl_close($request);

            if (isset($errors) && !is_null($errors) && $errors != "") {
                throw new SearchException($errors);
            }

            if ($info['http_code'] != 200) {
                throw  new SearchException($response);
            }

            $searchResponse = $this->searchResponseFactory->create(['params' => json_decode($response, TRUE)]);
            $this->results['metaData'] = $this->metaData->getMetaData();
            $this->results['products'] = $searchResponse->getResults();
            if ($searchResponse->haveFacets()) {
                $this->results['facets'] = $searchResponse->getFacets();
            }

            if ($searchResponse->haveBanner()) {
                $this->results['banners'] = $searchResponse->getBanners();
            }

            if ($spellCorrection = $searchResponse->getSpellCorrections()) {
                $this->results['spellCorrection'] = $spellCorrection;
            }

            if ($redirect = $searchResponse->getRedirect()) {
                $this->results['redirect'] = $redirect;
            }

            return $this->results;
        } catch (\Exception $e) {
            $this->metaData->setErrorCode($e->getCode());
            $this->metaData->setStatusCode(400);
            $this->metaData->setMessage($e->getMessage());
            $this->metaData->setDisplayMessage($this->helper->getErrorMessage());
            throw new SearchException($e->getMessage(), $e->getCode());
        }
    }

    /**
     * @param string $type
     * @return $this|mixed
     */
    public function setRequestType(string $type)
    {
        $this->requestType = $type;
        return $this;
    }

    /**
     * @return string
     */
    public function getPopularProductFields()
    {
        return $this->helper->getAutoCompletePupularProductFields();
    }

    /**
     * @param int $pageSize
     * @return SearchClient
     */
    public function setPageSize(int $pageSize): SearchClient
    {
        $this->pageSize = $pageSize;
        $this->metaData->setPageSize($pageSize);
        return $this;
    }

    /**
     * @return int
     */
    public function getPageSize(): int
    {
        return $this->pageSize ?? self::PAGE_SIZE;
    }

    /**
     * @param int $pageNo
     * @return SearchClient
     */
    public function setPageNo(int $pageNo): SearchClient
    {
        $this->pageNo = $pageNo;
        return $this;

    }

    /**
     * @return int
     */
    public function getPageNo(): int
    {
        return $this->pageNo ?? self::PAGE_NO;
    }

    /**
     * @param string $sort
     * @return SearchClient
     */
    public function setSort(string $sort): SearchClient
    {
        list($field, $order) = explode(" ", $sort);
        $this->sorts[$field] = $order;

        return $this;
    }

    /**
     * @return array
     */
    public function getSort(): array
    {
        return $this->sorts ?? [];
    }

    /**
     * @param $fieldName
     * @param array $values
     * @return $this
     */
    public function addTextFilter($fieldName, array $values)
    {
        foreach ($values as $key => $value) {
            $this->filters[$fieldName][] = $values[$key];
        }
        return $this;
    }

    /**
     * @param $fieldName
     * @param $Start
     * @param $Stop
     * @return $this
     */
    public function addRangeFilter($fieldName, $Start, $Stop)
    {
        $this->rangefilters[$fieldName][] = "[" . $Start . " TO " . $Stop . "]";
        return $this;
    }

    /**
     * @param $Otherkey
     * @param $Othervalue
     * @return $this
     */
    public function addOtherParams($Otherkey, $Othervalue)
    {
        $this->OtherParams[$Otherkey] = $Othervalue;
        return $this;
    }

    /**
     * @param string $categoryFilter
     * @return mixed|void
     */
    public function setCategoryFilter(string $categoryFilter)
    {
        $this->categoryFilter = str_replace("|", ">", $categoryFilter);
    }

    /**
     * @return string
     */
    protected function getUserAgent()
    {
        return $this->httpHeaders->getHttpUserAgent() ?? "";
    }

    /**
     * @return string
     */
    protected function getUnBxdUserId()
    {
        return ($this->cookieManager->getCookie("unbxd_userId")) ?? "";
    }

    /**
     * @return mixed
     */
    protected function getClientIp()
    {
        $clientIps = explode(",", $this->httpRequest->getClientIp());
        if (is_array($clientIps)) {
            return $clientIps[0];
        }

        return $clientIps;
    }
}
