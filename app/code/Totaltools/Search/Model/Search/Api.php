<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search;

use Magento\Framework\Webapi\Request;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Search\Api\Search\MetaDataInterface;
use Totaltools\Search\Api\Search\SearchClientInterface;
use Totaltools\Search\Api\SearchInterface;
use Totaltools\Search\Model\Search\Exception\SearchException;
use Totaltools\Search\Helper\Data;
use Magento\Framework\Webapi\Response;

class Api implements SearchInterface
{
    const TEXT_TYPE_FILTER = "text";
    const RANGE_TYPE_FILTER = "range";

    private $searchClient;
    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var MetaDataInterface
     */
    private $metaData;

    /**
     * @var Request
     */
    private $request;

    /**
     * @var Data
     */
    private $helper;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    private $response;
    /**
     * Api constructor.
     * @param SearchClientInterface $searchClient
     * @param LoggerInterface $logger
     * @param MetaDataInterface $metaData
     * @param Request $request
     * @param StoreManagerInterface $storeManager
     * @param Data $data
     */
    public function __construct(
        SearchClientInterface $searchClient,
        LoggerInterface $logger,
        MetaDataInterface $metaData,
        Request $request,
        StoreManagerInterface $storeManager,
        Data $data,
        Response $response
    ) {
        $this->logger = $logger;
        $this->searchClient = $searchClient;
        $this->metaData = $metaData;
        $this->request = $request;
        $this->storeManager = $storeManager;
        $this->helper = $data;
        $this->response = $response;
    }

    /**
     * @return array|mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getAutoSuggestResults()
    {
        if ($q = $this->request->getParam('q')) {
            try {
                $this->metaData->setStoreId($this->storeManager->getStore()->getId());
                $response = $this->searchClient->setRequestType(RequestType::AUTO_SUGGEST_REQUEST)->search($q, [])->execute();

                return [$response];
            } catch (SearchException $e) {
                $this->logger->emergency($e->getMessage());
                return  [
                    [
                        "metaData" => $this->metaData->getMetaData()
                    ]
                ];
            }
        }

        return [
            [
                "metaData" => $this->metaData->getMetaData()
            ]
        ];
    }


    /**
     * @return array|mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getResults()
    {

        if ($q = $this->request->getParam('q')) {
            try {
                $this->metaData->setStoreId($this->storeManager->getStore()->getId());
                $this->searchClient->setRequestType(RequestType::SEARCH_REQUEST)->search($q, []);

                if ($sort = $this->request->getParam("sort")) {
                    $this->searchClient->setSort($sort);
                }

                if ($pageSize = $this->request->getParam("pageSize")) {
                    $this->searchClient->setPageSize($pageSize);
                } elseif ( $pageSize = $this->helper->getSearchPageSize()) {
                    $this->searchClient->setPageSize($pageSize);
                }

                if ($pageNo = $this->request->getParam('page')) {
                    $this->searchClient->setPageNo($pageNo);
                }

                if ($categoryFilter = $this->request->getParam("category-filter")) {
                    $this->searchClient->setCategoryFilter($categoryFilter);
                }

                if (sizeof($this->request->getParams()) > 1) {
                    $this->setAttributesFilter();
                }

                $response = $this->searchClient->execute();

                return [$response];

            } catch (SearchException $e) {
                $this->logger->emergency($e->getMessage());
                return [
                    [
                        "metaData" => $this->metaData->getMetaData()
                    ]
                ];
            }
        }

        return [
            [
                "metaData" => $this->metaData->getMetaData()
            ]
        ];
    }

    /**
     * set unbxd text & range attributes
     */
    protected function setAttributesFilter()
    {
        if ($filters = $this->request->getParams()) {
            foreach ($filters as $field => $filter) {
                if ($field != "q") {
                    $fieldType  = explode("_", $field);
                    $field = str_replace($fieldType[0]."_","", $field);
                    switch ($fieldType[0]) {
                        case self::TEXT_TYPE_FILTER:
                            $values = explode('|', $filter);
                            $this->searchClient->addTextFilter($field, $values);
                            break;
                        case self::RANGE_TYPE_FILTER:
                            $values = explode('-', $filter);
                            if (sizeof($values) > 1) {
                                $this->searchClient->addRangeFilter($field, $values[0], $values[1]);
                            }
                            break;
                    }
                }
            }
        }
    }
}
