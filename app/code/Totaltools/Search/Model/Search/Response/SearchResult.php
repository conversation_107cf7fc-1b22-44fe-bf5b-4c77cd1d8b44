<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class SearchResult
{
    /**
     * @var string
     */
    private $_uniqueId;

    /**
     * @var array
     */
    private $_attributes;

    /**
     * SearchResult constructor.
     * @param array $product
     */
    public function __construct(array $product)
    {
        $this->_attributes = $product;
        if (isset($product['uniqueId'])) {
            $this->_uniqueId = (string) $this->_attributes["uniqueId"];
        }
    }

    /**
     * @return Attributes of the product
     */
    public function getAttributes()
    {
        return $this->_attributes;
    }

    /**
     * @return string
     */
    public function getUniqueId() : string
    {
        return $this->_uniqueId;
    }

    /**
     * @param fieldName
     * @return Attribute of the product for given field name
     */
    public function getAttribute(string $fieldName)
    {
        return $this->_attributes[$fieldName];
    }
}
