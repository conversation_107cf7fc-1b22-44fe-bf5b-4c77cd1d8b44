<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

use Totaltools\Search\Api\Search\MetaDataInterface;

class SearchResponse
{
    private $_results;
    private $_buckets;
    private $_facets;
    private $_stats;
    private $_spellCorrections;
    private $_banner;
    private $_reponse;
    private $_pageNo;
    private $_redirect;

    /**
     * @var SearchResultsFactory
     */
    private $searchResultsFactory;

    private $facetsFactory;

    private $metaData;

    /**
     * SearchResponse constructor.
     * @param array $params
     * @param SearchResultsFactory $searchResultsFactory
     * @param FacetsFactory $facetsFactory
     */
    public function __construct(
        array $params,
        SearchResultsFactory $searchResultsFactory,
        FacetsFactory $facetsFactory,
        MetaDataInterface $metaData
    ) {
        $this->searchResultsFactory = $searchResultsFactory;
        $this->facetsFactory = $facetsFactory;
        $this->metaData = $metaData;
        $this->_redirect = "";

       if (array_key_exists("error", $params) && isset($params["error"])) {
           $error = $params["error"];
           $this->metaData->setErrorCode((int) $error["code"]);
           $this->metaData->setMessage($error["msg"]);
       } else {

           // respone set meta data
           $metaData = $params["searchMetaData"];
           $this->metaData->setMessage("OK");
           $this->metaData->setQueryParams($metaData['queryParams']);
           $this->metaData->setStatusCode((int)$metaData["status"]);
           $this->metaData->setQueryTime((int)$metaData["queryTime"]);

           if (array_key_exists("response", $params) && isset($params["response"])) {
               $response = $params["response"];
               $this->metaData->setTotalCount((int) $response["numberOfProducts"]);
               $this->metaData->setPageNo((int) $response["start"] + 1);
               $searchResults = $this->searchResultsFactory->create(["products" => $response["products"]]);
               $this->_results =  $searchResults->getResults();
           }

           if (array_key_exists("facets", $params) && isset($params["facets"])) {
               $facets = $params["facets"];
               $this->_facets = $this->facetsFactory->create(["params" => $facets]);
           }

           if (array_key_exists("banner", $params) && isset($params["banner"])) {
               if (!empty($params["banner"]['banners'])) {
                   $this->_banner = $params["banner"]['banners'];
               }
           }

           if (array_key_exists("didYouMean", $params) && isset($params["didYouMean"])) {
               $this->_spellCorrections = array();
               $dym = $params["didYouMean"];
               foreach ($dym as $suggestion) {
                   array_push($this->_spellCorrections,$suggestion["suggestion"]);

               }
           }

           if (array_key_exists("redirect", $params) && isset($params["redirect"])) {
               $this->_spellCorrections = array();
               $redirect = $params["redirect"];
               if ($redirect['value']) {
                   $this->_redirect = $redirect['value'];
               }
           }
       }
   }

    /**
     * @return Total number of results found.
     */
    public function getTotalResultsCount()
    {
        return $this->_totalResultsCount;
    }

    /**
     * @return Results. Refer {@link SearchResults}
     */
    public function getResults()
    {
        return $this->_results;
    }

    /**
     * @return List of spell corrections in the order of relevance
     */
    public function getSpellCorrections()
    {
        return $this->_spellCorrections;
    }

    /**
     * @return Bucketed Response. Refer {@link BucketResults}
     */
    public function getBuckets()
    {
        return $this->_buckets;
    }

    /**
     * @return mixed
     */
    public function getBanners()
    {
        return $this->_banner;
    }

    /**
     * @return array
     */
    public function getFacets()
    {
        $facets = $this->_facets->getFacets();
        $facetData = [];
        $facetIndex = 0;
        $rangeIndex = 0;
        $categoryIndex = 0;
        foreach ($facets as $facetsList) {
            foreach($facetsList as $facet) {
                switch ($facet->getType()) {
                    case "range":
                        $facetData[$facet->getType()][$rangeIndex]["displayName"] = $facet->getDisplayName();
                        $facetData[$facet->getType()][$rangeIndex]["position"] = $facet->getPosition();
                        $facetData[$facet->getType()][$rangeIndex]["name"] = $facet->getName();
                        $facetData[$facet->getType()][$rangeIndex]["gap"] = $facet->getGap();
                        $facetCount = [];

                        foreach ($facet->getRangeEntries() as $idx => $entries) {
                            $facetCount[] = $entries->getFrom();
                            $facetData[$facet->getType()][$rangeIndex]["values"][$idx]['count'] = $entries->getCount();
                            $facetData[$facet->getType()][$rangeIndex]["values"][$idx]['value'] = $entries->getFrom();
                        }

                        $facetData[$facet->getType()][$rangeIndex]["start"] = min($facetCount);
                        $facetData[$facet->getType()][$rangeIndex]["end"] = max($facetCount) + $facet->getGap();

                        $rangeIndex++;
                        break;
                    case "text";
                        $facetData[$facet->getType()][$facetIndex]["displayName"] = $facet->getDisplayName();
                        $facetData[$facet->getType()][$facetIndex]["position"] = $facet->getPosition();
                        $facetData[$facet->getType()][$facetIndex]["name"] = $facet->getName();

                        foreach ($facet->getEntries() as $idx => $entries) {
                            $facetData[$facet->getType()][$facetIndex]["values"][$idx]['term'] = $entries->getTerm();
                            $facetData[$facet->getType()][$facetIndex]["values"][$idx]['count'] = $entries->getCount();
                        }
                        $facetIndex++;
                        break;
                    case "multilevel":
                        $facetData[$facet->getType()][$categoryIndex]["bucket"] = $facet->getEntries();
                        $facetData[$facet->getType()][$categoryIndex]["breadcrumb"] = $facet->getBreadcrumbentries();
                        $categoryIndex++;
                        break;
                }
            }
        }

        return array_reverse($facetData);
    }

    /**
     * @return Banners category.
     */
    public function getAppliedcategory()
    {
        return $this->_banner->getAppliedcategory();
    }

    /**
     * @return bool
     */
    public function haveFacets() : bool
    {
        return !empty($this->_facets) ? true : false;
    }

    /**
     * @return bool
     */
    public function haveBanner() : bool
    {
        return !empty($this->_banner) ? true : false;
    }

    /**
     * @return mixed
     */
    public function getRedirect()
    {
        return $this->_redirect;
    }
}
