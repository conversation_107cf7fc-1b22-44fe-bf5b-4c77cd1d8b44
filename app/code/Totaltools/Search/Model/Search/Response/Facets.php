<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class Facets
{
    const FACET_RANGE = "range";
    const FACET_TEXT = "text";
    const FACET_MULTILEVEL = "multilevel";

    /**
     * @var array
     */
    private $_facets;
    /**
     * @var array
     */
    private $_facetsMap;
    /**
     * @var FacetFactory
     */
    private $facetFactory;
    /**
     * @var RangeFacetFactory
     */
    private $rangeFacetFactory;

    /**
     * @var MultilevelFactFactory
     */
    private $multilevelFactFactory;

    /**
     * Facets constructor.
     * @param array $params
     * @param FacetFactory $facetFactory
     * @param RangeFacetFactory $rangeFacetFactory
     * @param MultilevelFactFactory $multilevelFactFactory
     */
    public function __construct(
        array $params,
        FacetFactory $facetFactory,
        RangeFacetFactory $rangeFacetFactory,
        MultilevelFactFactory $multilevelFactFactory
    ) {
        $this->_facets = array();
        $this->_facetsMap = array();
        $this->facetFactory = $facetFactory;
        $this->rangeFacetFactory = $rangeFacetFactory;
        $this->multilevelFactFactory = $multilevelFactFactory;
        // for custom sorting.
        $facetList = [];
        array_push($facetList, self::FACET_RANGE);
        array_push($facetList, self::FACET_TEXT);
        array_push($facetList, self::FACET_MULTILEVEL);

        foreach ($params as $field => $value) {
            switch ($field) {
                case self::FACET_TEXT:
                    $facetParams = $value['list'];
                    foreach ($facetParams as $param) {
                        $facet = $this->facetFactory->create(["faceType" => $field, "params" => $param]);
                        ${self::FACET_TEXT}[] = $facet;
                        $this->_facetsMap[$field]=$facet;
                    }
                    break;
                case self::FACET_RANGE:
                    $facetParams = $value['list'];
                    foreach ($facetParams as $param) {
                        $facet = $this->rangeFacetFactory->create(["faceType" => $field, "params" => $param]);
                        ${self::FACET_RANGE}[]= $facet;
                        $this->_facetsMap[$field]=$facet;
                    }
                    break;
                case self::FACET_MULTILEVEL:
                    $facet = $this->multilevelFactFactory->create(["faceType" => $field, "params" => $value]);
                    ${self::FACET_MULTILEVEL}[] = $facet;
                    $this->_facetsMap[$field]=$facet;
                    break;
            }
        }

        foreach ($facetList as $facets) {
            if (!empty(${$facets})) {
                array_push($this->_facets, ${$facets});
            }
        }
    }

    /**
     * @return List of {@link Facet}
     */
    public function getFacets() {
        return $this->_facets;
    }

    /**
     * @return Map of field --> {@link Facet}
     */
    public function getFacetsAsMap(){
        return $this->_facetsMap;
    }

    /**
     * @param facetName
     * @return Facet for given field name
     */
    public function getFacet($facetName){
        return $this->_facetsMap[$facetName];
    }
}
