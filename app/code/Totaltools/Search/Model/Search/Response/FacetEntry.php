<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class FacetEntry
{
    /**
     * @var string
     */
    private $term;
    /**
     * @var int
     */
    private $count;

    /**
     * FacetEntry constructor.
     * @param string $term
     * @param int $count
     */
    public function __construct(string $term, int $count)
    {
        $this->term = $term;
        $this->count = $count;
    }

    /**
     * @return string
     */
    public function getTerm() : string
    {
        return $this->term;
    }

    /**
     * @return int
     */
    public function getCount() : int
    {
        return $this->count;
    }

    /**
     * @return array
     */
    public function getFaceEntry() : array
    {
        return ['term' => $this->term, "count" => $this->count];
    }
}
