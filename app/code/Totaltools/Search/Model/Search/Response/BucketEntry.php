<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class BucketEntry
{
    /**
     * @var string
     */
    private $name;

    /**
     * @var int
     */
    private $count;

    /**
     * @var int
     */
    private $level;

    /**
     * BucketEntry constructor.
     * @param string $name
     * @param int $count
     * @param int $level
     */
    public function __construct(
        string $name,
        int $count,
        int $level
    ) {
        $this->name = $name;
        $this->count = $count;
        $this->level = $level;
    }

    /**
     * @return string
     */
    public function getName() : string
    {
        return $this->name;
    }

    /**
     * @return int
     */
    public function getCount() : int
    {
        return $this->count;
    }

    /**
     * @return array
     */
    public function getBucketEntry() : array
    {
        return [
            'name' => $this->name,
            "count" => $this->count,
            "level" => $this->level
        ];
    }
}
