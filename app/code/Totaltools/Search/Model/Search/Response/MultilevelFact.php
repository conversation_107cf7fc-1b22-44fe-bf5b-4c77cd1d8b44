<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

use Magento\Framework\App\ObjectManager;

class MultilevelFact
{
    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $_type;

    /**
     * @var array
     */
    protected $_bucketEntries;

    /**
     * @var array
     */
    protected $_breadcrumbEntries;

    /**
     * @var mixed
     */
    protected $_position;


    /**
     * Facet constructor.
     * @param string $facetName
     * @param array $params
     */
    public function __construct(
        string $faceType,
        array $params
    ) {

        $this->_type = $faceType;
        $this->_position = NULL;


        if (is_array($params["bucket"])) {
            foreach ($params["bucket"] as $bucket) {
                $map = (array)$bucket['values'];
                $this->generateEntries($map, $bucket['level']);
            }
        }


        if (is_array($params["breadcrumb"])) {
               $this->_breadcrumbEntries = array();
               $this->generateBreadcrumbEntries($params["breadcrumb"]);
         }
    }

    /**
     * @param array $breadcrumb
     */
    public function generateBreadcrumbEntries(array $breadcrumb)
    {

        if (isset($breadcrumb['values'][0])) {
            $categoryName =  $breadcrumb['values'][0]['value'];
            $categoryLevel = (int) $breadcrumb['level'];
            $breadcrumbEntry =  new BreadCrumbEntry($categoryName, $categoryLevel);
            array_push($this->_breadcrumbEntries, $breadcrumbEntry);
            if (!empty($breadcrumb['child'])) {
                $this->generateBreadcrumbEntries($breadcrumb['child']);
            }
        }
    }

    /**
     * @return string
     */
    public function getName() : string
    {
        return $this->name;
    }

    /**
     * @return mixed
     */
    public function getPosition()
    {
        return $this->_position;
    }

    /**
     * @return string
     */
    public function getType() : string
    {
        return $this->_type;
    }

    /**
     * @param array $values
     */
    protected function generateEntries(array $values, int $level)
    {
        $categoryList = array_map("trim", $this->getSkipCategoryList());

        $this->_bucketEntries = array();
        $term = NULL;
        for ($i = 0; $i < count($values); $i++) {
            if (($i%2) == 0) {
                $term = (string)$values[$i];
            } else {

                if(is_array($categoryList) && in_array(strtolower($term), $categoryList)) {
                    continue;
                }

                $count = (int)$values[$i];
                $bucketEntry =  new BucketEntry($term, $count, $level);
                array_push($this->_bucketEntries, $bucketEntry);
            }
        }
    }

    /**
     * @return array
     */
    public function getEntries()
    {
        $bucket = [];
        foreach ($this->_bucketEntries as $buckets) {
               $bucket[] = $buckets->getBucketEntry();
        }
        return $bucket;
    }

    /**
     * @return array
     */
    public function getBreadcrumbentries()
    {
        $breadcrumb = [];
        foreach ($this->_breadcrumbEntries as $breadcrumbs) {
            $breadcrumb[] = $breadcrumbs->getBreadcrumbEntry();
        }
        return $breadcrumb;
    }

    /**
     * @return false|string[]
     */
    protected function getSkipCategoryList()
    {
        $object_manager = ObjectManager::getInstance();
        $helper = $object_manager->get('Totaltools\Search\Helper\Data');
        return array_map("strtolower", explode("," , $helper->getSkipCategoryList()));
    }
}
