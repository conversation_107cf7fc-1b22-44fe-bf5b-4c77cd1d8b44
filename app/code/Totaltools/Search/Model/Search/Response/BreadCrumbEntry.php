<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class BreadCrumbEntry
{
    /**
     * @var string
     */
    protected $name;

    /**
     * @var int
     */
    protected $level;

    /**
     * BreadCrumbEntry constructor.
     * @param string $name
     * @param int $level
     */
    public function __construct(
        string $name,
        int $level
    ) {
        $this->name = $name;
        $this->level = $level;
    }

    /**
     * @return int
     */
    public function getLevel() : int
    {
        return $this->level;
    }

    /**
     * @return string
     */
    public function getName() : string
    {
        return $this->name;
    }

    /**
     * @return array
     */
    public function getBreadcrumbEntry()
    {
        return ["name" => $this->name, "level" => $this->level];
    }
}
