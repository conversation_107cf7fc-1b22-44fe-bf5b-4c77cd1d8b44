<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class SearchResults
{
    /**
     * @var int
     */
    private $_resultsCount;

    /**
     * @var array
     */
    private $_results;

    /**
     * @var SearchResultFactory
     */
    private $searchResultFactory;

    /**
     * SearchResults constructor.
     * @param array $products
     * @param SearchResultFactory $searchResultFactory
     */
    public function __construct(
        array $products,
        SearchResultFactory $searchResultFactory
    ) {
        $this->_resultsCount = count($products);
        $this->_results = array();
        $this->searchResultFactory = $searchResultFactory;

        foreach ($products as $product) {
            $searchResult = $this->searchResultFactory->create(["product" => $product]);
            array_push($this->_results, $searchResult->getAttributes());
        }
    }

    /**
     * @return Number of results
     */
    public function getResultsCount()
    {
        return $this->_resultsCount;
    }

    /**
     * @param $i
     * @return mixed|null
     */
    public function getAt($i)
    {
        if($i > $this->_resultsCount || $this->_resultsCount == 0){
            return NULL;
        }
        return $this->_results[$i];
    }

    /**
     * @return List of products. Refer {@link SearchResult}
     */
    public  function getResults()
    {
        return $this->_results;
    }
}

