<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class RangeFacetEntry extends FacetEntry
{
    private $from;
    private $to;

    /**
     * RangeFacetEntry constructor.
     * @param float $from
     * @param float $to
     * @param int $count
     */
    public function __construct(float $from, float $to, int $count)
    {
        parent::__construct("",$count);

        $this->from = $from;
        $this->to =$to;
    }

    /**
     * @return float
     */
    public function getFrom() : float
    {
        return $this->from;
    }

    /**
     * @return float
     */
    public function getTo() : float
    {
        return $this->to;
    }

    /**
     * @return array
     */
    public function getRangeFacet() : array
    {
        return ['from' => $this->from, "to" => $this->to];
    }

}