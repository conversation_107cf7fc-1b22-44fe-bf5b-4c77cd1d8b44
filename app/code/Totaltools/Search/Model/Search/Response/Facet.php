<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class Facet
{
    /**
     * @var string
     */
    protected $name;

    /**
     * @var string
     */
    protected $_type;

    /**
     * @var array
     */
    protected $_facetEntries;
    /**
     * @var mixed
     */

    /**
     * @var string
     */
    protected $_displayname;

    /**
     * @var mixed
     */
    protected $_position;

    private $facetEntryFactory;

    /**
     * Facet constructor.
     * @param string $facetName
     * @param array $params
     */
    public function __construct(
        string $faceType,
        array $params
    ) {

        $this->_type = $faceType;
        $this->_position = NULL;
        $this->_displayname= NULL;

        if (isset($params["facetName"])) {
            $this->name = $params["facetName"];
        }

        if (isset($params["position"])) {
            $this->_position = $params["position"];
        }

        if (isset($params["displayName"])) {
            $this->_displayname = $params["displayName"];
        }

        if (is_array($params["values"])) {
            $map = (array) $params["values"];
            if (array_key_exists("counts", $map)) {
                $this->generateEntries($map["counts"]);
            } else {
                $this->generateEntries($map);
            }
        }
    }

    /**
     * @return string
     */
    public function getName() : string
    {
        return $this->name;
    }

    /**
     * @return mixed
     */
    public function getPosition()
    {
        return $this->_position;
    }

    /**
     * @return string
     */
    public function getDisplayname() : string
    {
        return $this->_displayname;
    }

    /**
     * @return string
     */
    public function getType() : string
    {
        return $this->_type;
    }

    /**
     * @param array $values
     */
    protected function generateEntries(array $values)
    {
        $this->_facetEntries = array();
        $term = NULL;
        for ($i = 0; $i < count($values); $i++) {
            if(($i%2) == 0){
                $term = (string)$values[$i];
            }else{
                $count = (int)$values[$i];
                $faceEntry =  new FacetEntry($term, $count);
                array_push($this->_facetEntries, $faceEntry);
            }
        }
    }

    /**
     * @return List of {@link FacetEntry}
     */

    public function getEntries()
    {
        return $this->_facetEntries;
    }
}
