<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Search\Api\Search\MetaDataInterface;

class MetaData implements MetaDataInterface
{
    const PAGE_SIZE = 10;
    const PAGE_NO = 0;

    protected $statusCode;
    protected $pageNo;
    protected $totalCount;
    protected $queryParams;
    protected $queryTime;
    protected $errorCode;
    protected $message;
    protected $stats;
    protected $pageSize;
    protected $displayMessage;
    protected $storeId;

    /**
     * @return array
     */
    public function getMetaData() : array
    {
        return [
            "statusCode" => $this->getStatusCode(),
            "pageNo" => $this->getPageNo(),
            "totalCount" => $this->getTotalCount(),
            "queryParams" => $this->getQueryParams(),
            "queryTime" => $this->getQueryTime(),
            "message" => $this->getMessage(),
            "errorCode" => $this->getErrorCode(),
            "status" => $this->getStats(),
            "pageSize" => $this->getPageSize(),
            "displayMessage" => $this->getDisplayMessage(),
            "storeId" => $this->getStoreId()
        ];
    }

    /**
     * @return int|null
     */
    public function getStatusCode() : ?int
    {
        return $this->statusCode;
    }

    /**
     * @param int $statusCode
     * @return MetaData
     */
    public function setStatusCode(int $statusCode) : MetaData
    {
        $this->statusCode = $statusCode;
        return $this;
    }

    /**
     * @return int
     */
    public function getPageNo() : int
    {
        return $this->pageNo ?? self::PAGE_NO;
    }

    /**
     * @param int $pageNo
     * @return MetaData
     */
    public function setPageNo(int $pageNo) : MetaData
    {
        $this->pageNo = $pageNo;
        return $this;
    }

    /**
     * @return int
     */
    public function getTotalCount() : int
    {
        return $this->totalCount ?? 0;
    }

    /**
     * @param int $totalCount
     * @return MetaData
     */
    public function setTotalCount(int $totalCount) : MetaData
    {
        $this->totalCount = $totalCount;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getErrorCode() : ?string
    {
        return $this->errorCode;
    }

    /**
     * @param string $errorCode
     * @return MetaData
     */
    public function setErrorCode(string $errorCode) : MetaData
    {
        $this->errorCode = $errorCode;
        return $this;
    }

    /**
     * @return string|null
     */
    public function getMessage() : ?string
    {
        return $this->message;
    }

    /**
     * @param string $message
     * @return MetaData
     */
    public function setMessage(string $message) : MetaData
    {
        $this->message = $message;
        return $this;
    }

    /**
     * @return Time taken to query results in milliseconds
     */
    public function getQueryTime()
    {
        return $this->queryTime;
    }

    /**
     * @param $queryTime
     * @return MetaData
     */
    public function setQueryTime($queryTime) : MetaData
    {
        $this->queryTime = $queryTime;
        return $this;
    }

    /**
     * @return Stats. Refer {@link Stats}
     */
    public function getStats()
    {
        return $this->stats;
    }

    /**
     * @param $stats
     * @return MetaData
     */
    public function setStats($stats) : MetaData
    {
        $this->stats = $stats;
        return $this;
    }

    /**
     * @return mixed
     */
    public function getQueryParams()
    {
        return $this->queryParams;
    }

    /**
     * @param $queryParams
     * @return MetaData
     */
    public function setQueryParams($queryParams) : MetaData
    {
        $this->queryParams = $queryParams;
        return $this;
    }

    /**
     * @return int
     */
    public function getPageSize() : int
    {
        return $this->pageSize ?? self::PAGE_SIZE;
    }

    /**
     * @param int $pageSize
     * @return MetaData
     */
    public function setPageSize(int $pageSize) : MetaData
    {
        $this->pageSize = $pageSize;
        return $this;
    }

    /**
     * @param string $displayMessage
     * @return MetaData
     */
    public function setDisplayMessage(string $displayMessage) : MetaData
    {
        $this->displayMessage = $displayMessage;
        return $this;
    }

    /**
     * @return string
     */
    public function getDisplayMessage() : ?string
    {
        return $this->displayMessage;
    }

    /**
     * @param int $storeId
     * @return MetaData
     */
    public function setStoreId(int $storeId) : MetaData
    {
        $this->storeId = $storeId;
        return $this;
    }

    /**
     * @return int
     */
    public function getStoreId() : ?int
    {
        return $this->storeId;
    }
}
