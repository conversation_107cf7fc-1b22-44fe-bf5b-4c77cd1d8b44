<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\Search\Response;

class RangeFacet extends Facet
{
    /**
     * @var float
     */
    private $_gap;
    /**
     * @var float
     */
    private $_start;
    /**
     * @var float
     */
    private $_end;

    /**
     * @var
     */
    protected $_rangeFacetEntries;

    /**
     * RangeFacet constructor.
     * @param string $facetName
     * @param array $params
     */
    public function __construct(
        string $faceType,
        array $params
    ) {
        parent::__construct($faceType, $params);
        $this->_gap = (double)$params["values"]["gap"];
        $this->_start = (double)$params["values"]["start"];
        $this->_end = (double)$params["values"]["end"];
    }

    /**
     * @param array $values
     */
    protected function generateEntries(array $values)
    {
        parent::generateEntries($values);

        $this->_rangeFacetEntries = array();

        foreach ($this->getEntries() as $entry) {
            $from = (double)$entry->getTerm();
            $to = $from + $this->_gap;
            $rangeFacetEntry = new RangeFacetEntry($from, $to, $entry->getCount());
            array_push($this->_rangeFacetEntries, $rangeFacetEntry);
        }
    }

    /**
     * @return float
     */
    public function getGap() : float
    {
        return $this->_gap;
    }

    /**
     * @return float
     */
    public function getStart() : float
    {
        return $this->_start;
    }

    /**
     * @return float
     */
    public function getEnd() : float
    {
        return $this->_end;
    }

    /**
     * @return array
     */
    public function getRangeEntries() : array
    {
        return $this->_rangeFacetEntries;
    }
}
