<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\AutoSuggest;

use Magento\Framework\Webapi\Request;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Search\Api\AutoCompleteInterface;
use Totaltools\Search\Api\Search\MetaDataInterface;
use Totaltools\Search\Model\AutoSuggest\Exception\AutoSuggestException;

class Api implements AutoCompleteInterface
{
    /**
     * @var AutoSuggestClient
     */
    private $autoSuggestClient;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var Request 
     */
    private $request;

    /**
     * @var MetaDataInterface
     */
    private $metaData;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * Api constructor.
     * @param AutoSuggestClient $autoSuggestClient
     * @param LoggerInterface $logger
     * @param Request $request
     * @param MetaDataInterface $metaData
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        AutoSuggestClient $autoSuggestClient,
        LoggerInterface $logger,
        Request $request,
        MetaDataInterface $metaData,
        StoreManagerInterface $storeManager)
    {
        $this->logger = $logger;
        $this->autoSuggestClient = $autoSuggestClient;
        $this->request = $request;
        $this->metaData = $metaData;
        $this->storeManager = $storeManager;
    }

    /**
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getResults()
    {
        if ($q = $this->request->getParam('q')) {
            try {
                $this->metaData->setStoreId($this->storeManager->getStore()->getId());
                $this->autoSuggestClient->autosuggest($q);
                $response = $this->autoSuggestClient->execute();
                return [$response];
            } catch (AutoSuggestException $e) {
                $this->logger->emergency($e->getMessage());
                return  [
                    [
                        "metaData" => $this->metaData->getMetaData()
                    ]
                ];
            }
        }

        return  [
            [
                "metaData" => $this->metaData->getMetaData()
            ]
        ];
    }
}
