<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\AutoSuggest;

use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Search\Api\AutoSuggest\AutoSuggestClientInterface;
use Totaltools\Search\Api\Search\MetaDataInterface;
use Totaltools\Search\Model\AutoSuggest\Exception\AutoSuggestException;
use Totaltools\Search\Model\AutoSuggest\Response\AutoSuggestResponse;
use Totaltools\Search\Helper\Data;

class AutoSuggestClient implements AutoSuggestClientInterface
{
    private $secure = true;
    private $query;
    private $inFieldsCount = -1;
    private $popularProductsCount = -1;
    private $popularProductsFields = "";
    private $keywordSuggestionsCount = -1;
    private $topQueriesCount = -1;

    /**
     * @var AutoSuggestResponse
     */
    private $autoSuggestRespones;

    /**
     * @var Data
     */
    private $helper;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var MetaDataInterface
     */
    private $metaData;

    /**
     * AutoSuggestClient constructor.
     * @param AutoSuggestResponse $autoSuggestResponse
     * @param Data $data
     * @param StoreManagerInterface $storeManager
     * @param MetaDataInterface $metaData
     */
    public function __construct(
        AutoSuggestResponse $autoSuggestResponse,
        Data $data,
        StoreManagerInterface $storeManager,
        MetaDataInterface $metaData
    ) {
        $this->helper = $data;
        $this->storeManager = $storeManager;
        $this->autoSuggestRespones = $autoSuggestResponse;
        $this->metaData = $metaData;

        $this->popularProductsFields = $this->helper->getAutoCompletePupularProductFields() ?? "";
        $this->keywordSuggestionsCount = $this->helper->getAutoCompleteKeywordSuggestionCount() ?? -1;
        $this->popularProductsCount = $this->helper->getAutoCompletePupularProductCount() ?? -1;
    }

    /**
     * @return string
     */
    private function getAutoSuggestUrl()
    {
        return sprintf(
            $this->helper->getAutoCompleteEndpoint($this->storeManager->getStore()),
            $this->helper->getApiKey($this->storeManager->getStore()),
            $this->helper->getSiteKey($this->storeManager->getStore())
        );
    }

    /**
     * @param string $query
     * @return $this
     */
    public function autosuggest(string $query)
    {
        $this->query = $query;
        return $this;
    }

    /**
     * Sets number of in_fields to be returned in results
     * @param inFieldsCount
     * @return this
     */
    public function setInFieldsCount(int $inFieldsCount){
        $this->inFieldsCount = $inFieldsCount;
        return $this;
    }

    /**
     * Sets number of popular products to be returned in results
     * @param popularProductsCount
     * @return this
     */
    public function setPopularProductsCount(int $popularProductsCount){
        $this->popularProductsCount = $popularProductsCount;
        return $this;
    }

    /**
     * @return int
     */
    public function getPopularProductsCount()
    {
        return (int) $this->helper->getAutoCompletePupularProductCount();
    }

    /**
     * @param $popularProductsFields
     * @return $this
     */
    public function setPopularProductsFields($popularProductsFields)
    {
        $this->popularProductsFields = $popularProductsFields;
        return $this;
    }

    /**
     * @return string
     */
    public function getPopularProductFields()
    {
        return $this->helper->getAutoCompletePupularProductFields();
    }


    /**
     * Sets number of keyword suggestions to be returned in results
     * @param keywordSuggestionsCount
     * @return this
     */

    public function setKeywordSuggestionsCount($keywordSuggestionsCount/*int*/){
        $this->keywordSuggestionsCount = $keywordSuggestionsCount;
        return $this;
    }

    /**
     * @return int
     */
    protected function getKeywordSuggestionCount() : int
    {
        return (int) $this->helper->getAutoCompleteKeywordSuggestionCount();
    }

    /**
     * Sets number of popular queries to be returned in results
     * @param topQueriesCount
     * @return this
     */

    public function setTopQueriesCount($topQueriesCount/*int*/){
        $this->topQueriesCount = $topQueriesCount;
        return $this;
    }

    /**
     * @param boolean $forceCreate
     * @return string
     * @throws AutoSuggestException
     */
    public function generateUrl($forceCreate = false){
        try{
            $sb="";
            if(isset($this->query) || $forceCreate){
                $sb .= $this->getAutoSuggestUrl();
                $sb .= isset($this->query) ? "&q=".urlencode(utf8_encode($this->query)) : '';
            }
            if($this->inFieldsCount != -1){
                $sb .= "&inFields.count=".urlencode(utf8_encode($this->inFieldsCount));
            }
            if($this->popularProductsCount != -1){
                $sb .= "&popularProducts.count=".$this->getPopularProductsCount();
            }
            if($this->keywordSuggestionsCount != -1){
                $sb .= "&keywordSuggestions.count=".$this->keywordSuggestionsCount;
            }
            if($this->topQueriesCount != -1){
                $sb .= "&topQueries.count=".$this->topQueriesCount;
            }

            if($this->popularProductsFields != "") {
                $sb .= "&popularProducts.fields=".$this->getPopularProductFields();
            }

            return $sb;
        } catch (\Exception $e) {
            throw new AutoSuggestException($e);
        }
    }

    /**
     * @return Response\Results
     * @throws AutoSuggestException
     */
    public function execute(){
        try {
            $url = $this->generateUrl();
            $request = curl_init($url);
            curl_setopt($request, CURLOPT_RETURNTRANSFER, TRUE);
            $response = curl_exec($request);
            if(curl_errno($request)){
                $errors = curl_error($request);
            }
            $info = curl_getinfo($request);
            curl_close($request);

            if (isset($errors) && !is_null($errors) && $errors!="") {
                throw new AutoSuggestException($errors);
            }

            if ($info['http_code']!=200) {
                throw new AutoSuggestException($response);
            }

            $response = json_decode($response);
            $this->autoSuggestRespones->setResponse($response);
            $this->autoSuggestRespones->execute();
            return $this->autoSuggestRespones->getResults();

        } catch (\Exception $e) {
            $this->metaData->setErrorCode($e->getCode());
            $this->metaData->setStatusCode(400);
            $this->metaData->setMessage($e->getMessage());
            $this->metaData->setDisplayMessage( $this->helper->getErrorMessage());
            throw new AutoSuggestException($e->getMessage());
        }
    }
}
