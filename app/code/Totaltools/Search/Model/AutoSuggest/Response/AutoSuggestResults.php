<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\AutoSuggest\Response;

use Totaltools\Search\Model\AutoSuggest\AutoSuggestType;

class AutoSuggestResults
{
    /**
     * @var array
     */
    private $_resultSections;
    /**
     * @var
     */
    private $_products;
    /**
     * @var AutoSuggestResultSelectionFactory
     */
    private $autoSuggestResultSelectionFactory;
    /**
     * @var \Totaltools\Search\Model\AutoSuggest\AutoSuggestTypeFactory
     */
    private $autoSuggestTypeFactory;

    /**
     * AutoSuggestResults constructor.
     * @param AutoSuggestResultSelectionFactory $autoSuggestResultSelectionFactory
     * @param \Totaltools\Search\Model\AutoSuggest\AutoSuggestTypeFactory $autoSuggestTypeFactory
     */
    public function __construct(
        \Totaltools\Search\Model\AutoSuggest\Response\AutoSuggestResultSelectionFactory  $autoSuggestResultSelectionFactory,
        \Totaltools\Search\Model\AutoSuggest\AutoSuggestTypeFactory $autoSuggestTypeFactory
    ) {
        $this->_resultSections = array();//array(string=>AutoSuggestResultSection)
        $this->autoSuggestResultSelectionFactory = $autoSuggestResultSelectionFactory;
        $this->autoSuggestTypeFactory = $autoSuggestTypeFactory;
    }

    /**
     * @throws \Exception
     */
    public function execute()
    {
        $this->_resultSections = array();
        $products = $this->getProducts();
        foreach ($products as $result) {
            $type = (string) $result->doctype;
            if(!array_key_exists($type, $this->_resultSections)){
                if ($type==="IN_FIELD") {
                    $autoSuggestType = $this->autoSuggestTypeFactory->create();
                    $autoSuggestType->setValue(AutoSuggestType::IN_FIELD);
                    $this->_resultSections[$type] = $this->autoSuggestResultSelectionFactory->create();
                } elseif ($type==="POPULAR_PRODUCTS") {
                    $autoSuggestType = $this->autoSuggestTypeFactory->create();
                    $autoSuggestType->setValue(AutoSuggestType::POPULAR_PRODUCTS);
                    $this->_resultSections[$type] = $this->autoSuggestResultSelectionFactory->create();
                } elseif ($type==="TOP_SEARCH_QUERIES") {
                    $autoSuggestType = $this->autoSuggestTypeFactory->create();
                    $autoSuggestType->setValue(AutoSuggestType::TOP_SEARCH_QUERIES);
                    $this->_resultSections[$type] = $this->autoSuggestResultSelectionFactory->create();
                } elseif($type==="KEYWORD_SUGGESTION") {
                    $autoSuggestType = $this->autoSuggestTypeFactory->create();
                    $autoSuggestType->setValue(AutoSuggestType::KEYWORD_SUGGESTION);
                    $this->_resultSections[$type] = $this->autoSuggestResultSelectionFactory->create();
                } elseif($type==="PROMOTED_SUGGESTION") {
                    $autoSuggestType = $this->autoSuggestTypeFactory->create();
                    $autoSuggestType->setValue(AutoSuggestType::PROMOTED_SUGGESTION);
                    $this->_resultSections[$type] = $this->autoSuggestResultSelectionFactory->create();
                }
            }

            if(!empty($this->_resultSections[$type])) {
                $this->_resultSections[$type]->addResult($result);
            }

        }
    }

    /**
     * @param array $products
     */
    public function setProducts(array $products)
    {
        $this->_products = $products;
    }

    /**
     * @return mixed
     */
    protected function getProducts()
    {
        return $this->_products;
    }

    /**
     * @return array
     */
    public function getResultSections()
    {
        return $this->_resultSections;
    }

    /**
     * @return array|mixed
     */
    public function getInFieldSuggestions()
    {
        return $this->_resultSections["IN_FIELD"] ?? [];
    }

    /**
     * @return array|mixed
     */
    public function getPopularProducts()
    {
        return $this->_resultSections["POPULAR_PRODUCTS"] ?? [];
    }

    /**
     * @return array|mixed
     */
    public function getKeywordSuggestions()
    {
        return $this->_resultSections["KEYWORD_SUGGESTION"] ?? [];
    }

    /**
     * @return array|mixed
     */
    public function getPromotedSuggestions()
    {
        return $this->_resultSections["PROMOTED_SUGGESTION"] ?? [];
    }

    /**
     * @return mixed
     */
    public function getTopQueries()
    {
        return $this->_resultSections["TOP_SEARCH_QUERIES"];

    }
}
