<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\AutoSuggest\Response;

use Totaltools\Search\Api\AutoSuggest\DidYouMeanInterface;

class DidYouMean implements DidYouMeanInterface
{
    protected $suggestion;

    /**
     * @return array|null
     */
    public function getSuggestion() : ?array
    {
        return $this->suggestion;
    }

    /**
     * @param array $suggestion
     * @return mixed|void
     */
    public function setSuggestion(array $suggestion)
    {
        foreach ($suggestion as $spellCorrection) {
            if(!empty( $spellCorrection->suggestion)) {
                $this->suggestion[] = $spellCorrection->suggestion;
            }
        }
    }
}
