<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\AutoSuggest\Response;

use Totaltools\Search\Model\AutoSuggest\AutoSuggestType;

class AutoSuggestResultSelection
{
    /**
     * @var AutoSuggestType
     */
    private $_type;
    /**
     * @var
     */
    private $_resultsCount;
    /**
     * @var array
     */
    private $_results;
    /**
     * @var AutoSuggestResultFactory
     */
    private $autoSuggestResultFactory;

    /**
     * AutoSuggestResultSelection constructor.
     * @param AutoSuggestType $type
     * @param AutoSuggestResultFactory $autoSuggestResultFactory
     */
    public function __construct(
        AutoSuggestType $type,
        \Totaltools\Search\Model\AutoSuggest\Response\AutoSuggestResultFactory $autoSuggestResultFactory
    ) {
        $this->_type = $type;
        $this->autoSuggestResultFactory = $autoSuggestResultFactory;
        $this->_results = array();
    }

    /**
     * @param \stdClass $params
     */
    public function addResult(\stdClass $params)
    {
        $autoSuggestResult = $this->autoSuggestResultFactory->create();
        $autoSuggestResult->setAttributes($params);
        array_push($this->_results, $autoSuggestResult);
        $this->_resultsCount++;
    }

    /**
     * @return AutoSuggestType
     */
    public function getType(){
        return $this->_type;
    }

    /**
     * @return mixed
     */
    public function getResultsCount()
    {
        return $this->_resultsCount;
    }

    /**
     * @param int $i
     * @return mixed|null
     */
    public function getAt(int $i)
    {
        if ($i >= $this->_resultsCount) {
            return NULL;
        }

        return $this->_results[$i];
    }

    /**
     * @return array
     */
    public function getResults()
    {
        return $this->_results;
    }
}
