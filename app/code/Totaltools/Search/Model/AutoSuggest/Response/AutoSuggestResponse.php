<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\AutoSuggest\Response;

use Totaltools\Search\Api\AutoSuggest\DidYouMeanInterface;
use Totaltools\Search\Api\Search\MetaDataInterface;

class AutoSuggestResponse
{
    const UNBXD_PRODUCTS = "products";
    const UNBXD_AUTOSUGGEST = "autoSuggest";
    const UNBXD_PROMOTEDSUGGESTION = "promotedSuggestion";
    const UNBXD_INFIELDSUGGESTION = "inFieldSuggestion";

    CONST UNBXD_CATEGORY = "category";
    const UNBXD_STATS = "stats";

    private $_response;
    private $_statusCode;//int
    private $_errorCode;//int
    private $_message;//string
    private $_queryTime;//string
    private $_totalResultsCount;//int
    private $_results;//AutoSuggestResults
    private $metaData;
    /**
     * @var AutoSuggestResults
     */
    private $autoSuggestResults;

    /**
     * @var DidYouMeanInterface
     */
    private DidYouMeanInterface $didYouMean;

    /**
     * AutoSuggestResponse constructor.
     * @param AutoSuggestResults $autoSuggestResults
     * @param MetaDataInterface $metaData
     * @param DidYouMeanInterface $didYouMean
     */
    public function __construct(
        AutoSuggestResults $autoSuggestResults,
        MetaDataInterface $metaData,
        DidYouMeanInterface $didYouMean
    ) {
        $this->autoSuggestResults = $autoSuggestResults;
        $this->metaData = $metaData;
        $this->didYouMean = $didYouMean;
    }

    /**
     * @param $response
     */
    public function setResponse($response)
    {
        $this->_response = $response;
    }

    /**
     * @return mixed
     */
    public function getResponse()
    {
        return $this->_response;
    }

    /**
     * @return $this
     */
    public function execute()
    {
        $params = $this->getResponse();
        if (property_exists($params, "error")) {
            $error = $params["error"];
            $this->metaData->setErrorCode((int) $error["code"]);
            $this->metaData->setMessage($error["msg"]);
        } else {
            $this->_message = "OK";
            $metaData = json_decode(json_encode($params->searchMetaData));
            $this->metaData->setMessage("OK");
            $this->metaData->setQueryParams($metaData->queryParams);
            $this->metaData->setStatusCode((int)$metaData->status);
            $this->metaData->setQueryTime((int)$metaData->queryTime);

            if (isset($params->didYouMean)) {
                $didYouMean = json_decode(json_encode($params->didYouMean));
                if (isset($didYouMean) && !empty($didYouMean[0]->suggestion))
                {
                    $this->didYouMean->setSuggestion($didYouMean);
                }
            }

            if (isset($params->response)) {
                $response =$params->response;
                $this->_totalResultsCount = (int) $response->numberOfProducts;
                $this->autoSuggestResults->setProducts($response->products);
                $this->autoSuggestResults->execute();

                $this->metaData->setTotalCount((int) $response->numberOfProducts);
                $this->metaData->setPageNo((int) $response->start + 1);

                $this->_results["metaData"] = $this->metaData->getMetaData();

                if (!empty($suggestion = $this->didYouMean->getSuggestion())) {
                    $this->_results["spellCorrections"] = $suggestion;
                }

                if ($popularProducts = $this->autoSuggestResults->getPopularProducts()) {
                    $this->_results[self::UNBXD_PRODUCTS] = $this->getResultAttribute($popularProducts->getResults());
                }

                if ($keywordSuggestion = $this->autoSuggestResults->getKeywordSuggestions()) {
                    $this->_results[self::UNBXD_AUTOSUGGEST] = $this->getResultAttribute($keywordSuggestion->getResults());
                }

                if ($promotedSuggestion = $this->autoSuggestResults->getPromotedSuggestions()) {
                    $this->_results[self::UNBXD_PROMOTEDSUGGESTION] = $this->getResultAttribute($promotedSuggestion->getResults());
                }

                if ($inFieldSuggestion = $this->autoSuggestResults->getInFieldSuggestions()) {
                    $this->_results[self::UNBXD_INFIELDSUGGESTION] = $this->getResultAttribute($inFieldSuggestion->getResults());
                }
            }
        }

        return $this;
    }

    /**
     * @return mixed
     */
    public function getStatusCode()
    {
        return $this->_statusCode;
    }

    /**
     * @return mixed
     */
    public function getErrorCode()
    {
        return $this->_errorCode;
    }

    /**
     * @return mixed
     * @description OK if successfull. Error message otherwise
     */
    public function getMessage()
    {
        return $this->_message;
    }

    /**
     * @return mixed
     * @description Time taken to query results in milliseconds
     */
    public function getQueryTime()
    {
        return $this->_queryTime;
    }

    /**
     * @return mixed
     * @description Total number of results found.
     */
    public function getTotalResultsCount()
    {
        return $this->_totalResultsCount;
    }

    /**
     * @return mixed
     */
    public function getResults(){
        return $this->_results;
    }

    /**
     * @param $results
     * @return array
     */
    public function getResultAttribute($results)
    {
        $attributes = [];
        foreach ($results as $result) {
            $attributes[] = $result->getAttributes();
        }
        return $attributes;
    }
}
