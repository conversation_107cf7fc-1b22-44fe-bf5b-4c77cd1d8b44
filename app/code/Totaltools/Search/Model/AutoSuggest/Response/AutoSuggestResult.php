<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Model\AutoSuggest\Response;

class AutoSuggestResult
{
    /**
     * @var
     */
    private $_attributes;

    /**
     * @param \stdClass $attributes
     */
    public function setAttributes(\stdClass $attributes)
    {
        $this->_attributes = $attributes;
    }

    /**
     * @return mixed
     */
    public function getAttributes()
    {
        return $this->_attributes;
    }

    /**
     * @param string $fieldName
     * @return mixed
     */
    public function getAttribute(string $fieldName)
    {
        return $this->_attributes[$fieldName];
    }

    /**
     * @return string
     */
    public function getSuggestion()
    {
        return (string) $this->getAttribute("autosuggest");
    }
}
