<?php

namespace Totaltools\Search\Preference\Controller\Search\Result;

/**
 * @package     Totaltools_Search
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

use Magento\Framework\App\Action\Context;
use Magento\Search\Model\QueryFactory;
use Magento\Framework\Controller\ResultFactory;

class Index extends \Magento\Framework\App\Action\Action
{
    /**
     * @var QueryFactory
     */
    protected $queryFactory;

    /**
     * @param Context $context
     * @param QueryFactory $queryFactory
     */
    public function __construct(Context $context, QueryFactory $queryFactory)
    {
        parent::__construct($context);
        $this->queryFactory = $queryFactory;
    }

    /**
     * Display search result
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        /** @var $query \Magento\Search\Model\Query */
        $query = $this->queryFactory->get();

        if ($query->getQueryText() != '') {
            return $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        } else {
            $this->getResponse()->setRedirect($this->_redirect->getRedirectUrl());
        }
    }
}
