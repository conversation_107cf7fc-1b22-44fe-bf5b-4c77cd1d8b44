<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/api/autocomplete" method="GET">
        <service class="Totaltools\Search\Api\AutoCompleteInterface" method="getResults"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/api/search" method="GET">
        <service class="Totaltools\Search\Api\SearchInterface" method="getResults"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>

    <route url="/V1/api/autosuggest" method="GET">
        <service class="Totaltools\Search\Api\SearchInterface" method="getAutoSuggestResults"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
</routes>