<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

    <preference for="Totaltools\Search\Api\AutoCompleteInterface"
                type="Totaltools\Search\Model\AutoSuggest\Api" />

    <preference for="Totaltools\Search\Api\AutoSuggest\DidYouMeanInterface"
                type="Totaltools\Search\Model\AutoSuggest\Response\DidYouMean" />

    <preference for="Totaltools\Search\Api\SearchInterface"
                type="Totaltools\Search\Model\Search\Api" />

    <preference for="Totaltools\Search\Api\Search\SearchClientInterface"
                type="Totaltools\Search\Model\Search\SearchClient" />

	<preference for="Totaltools\Search\Api\Search\MetaDataInterface"
                type="Totaltools\Search\Model\Search\Response\MetaData" />

    <preference for="Amasty\Shopby\Controller\Search\Result\Index"
                type="Totaltools\Search\Preference\Controller\Search\Result\Index" />

    <preference for="Magento\CatalogSearch\Controller\Result\Index"
                type="Totaltools\Search\Preference\Controller\Search\Result\Index" />

    <type name="Unbxd\ProductFeed\Helper\Data">
        <plugin name="totaltools_search_unbxd_helper_plugin"
                type="Totaltools\Search\Plugin\Helper\DataPlugin" />
    </type>

	<type name="Unbxd\ProductFeed\Model\Indexer\Product\Full\DataSourceProvider">
        <arguments>
            <argument name="dataSources" xsi:type="array">
                <item name="attribute" xsi:type="object">Unbxd\ProductFeed\Model\Indexer\Product\Full\DataSourceProvider\Attribute</item>
                <item name="labeled_image" xsi:type="object">Totaltools\Search\Model\Product\DataSourceProvider\LabeledImage</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Catalog\Ui\DataProvider\Product\ProductRenderCollectorComposite">
        <arguments>
            <argument name="productProviders" xsi:type="array">
                <item name="labeled_image" xsi:type="object">Totaltools\Search\Ui\DataProvider\Product\LabeledImage</item>
            </argument>
        </arguments>
    </type>

    <type name="Unbxd\ProductFeed\Model\Feed\Config">
        <plugin name="totaltools_unbxd_feed_config" type="Totaltools\Search\Plugin\Feed\Config"/>
    </type>
</config>
