<?xml version="1.0"?>
<!--
/**
 * @package     Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */    
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="unbxd" translate="label" sortOrder="10000" class="unbxd-tab">
            <label>Unbxd</label>
        </tab>
        <section id="unbxd_setup" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
            <!-- Common settings -->
            <group id="common" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Common</label>

                <field id="show_price" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Price</label>
                    <comment><![CDATA[Show/Hide Price for Autocomplete & Instantsearch]]></comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>

                <field id="show_product_labels" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Show Product Labels</label>
                    <comment><![CDATA[Show/Hide Product Labels for Autocomplete & Instantsearch]]></comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <!-- Autocomplete settings -->
            <group id="auto_complete" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Auto Complete Configuration</label>
                <fieldset_css>unbxd-fieldset</fieldset_css>

                <field id="auto_suggest_endpoint" translate="label comment" type="text" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Auto Suggest</label>
                    <comment><![CDATA[Auto Suggest Endpoint Get KEYWROD SUGGESTION data.]]></comment>
                </field>

                <field id="endpoint" translate="label comment" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Auto Complete</label>
                    <comment><![CDATA[Auto Complete Endpoint.]]></comment>
                </field>

                <field id="product_fields" translate="label comment" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Popular Products Fields</label>
                    <comment><![CDATA[Comma Separated Product Fields]]></comment>
                </field>

                <field id="popular_product_count" translate="label comment" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Popular Products Count</label>
                    <comment><![CDATA[No of popular products return]]></comment>
                </field>
                <field id="keyword_suggestion_count" translate="label comment" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Keyword Suggestion Count</label>
                    <comment><![CDATA[No. of keywrod suggest return]]></comment>
                </field>
                <field id="typing_delay" translate="label comment" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Typing Delay</label>
                    <comment><![CDATA[Typing delay before API request]]></comment>
                </field>
                <field id="popular_search_terms" translate="label comment" type="text" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Popular Search Terms</label>
                    <comment><![CDATA[Comma Separated Popular Search Terms]]></comment>
                </field>
            </group>
            <!-- Instant search settings -->
            <group id="search_result" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Search Result Configuration</label>
                <fieldset_css>unbxd-fieldset</fieldset_css>
                <field id="endpoint" translate="label comment" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Search Result</label>
                    <comment><![CDATA[Search Result EndPoint]]></comment>
                </field>
                <field id="attribute_labels_map" translate="label comment" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Attribute Labels Map</label>
                    <comment><![CDATA[Comma and pipe seperated combinations]]></comment>
                </field>
                <field id="page_size" translate="label comment" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Page Size</label>
                    <comment><![CDATA[How many record api return.]]></comment>
                </field>
                <field id="error_message" translate="label comment" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Error Message</label>
                    <comment><![CDATA[Error message if app throw exception.]]></comment>
                </field>
                <field id="skip_category" translate="label comment" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Categories List</label>
                    <comment><![CDATA[Comma separated categries. These categories hide from layered filters]]></comment>
                </field>
            </group>
        </section>
        <section id="unbxd_catalog" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
            <group id="images" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <field id="labeled_image_id" translate="label comment" type="select" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Labeled Image</label>
                    <source_model>Unbxd\ProductFeed\Model\Config\Source\MediaTypes</source_model>
                    <comment><![CDATA[Please specify image ID which use for <strong>labeleled image</strong>.<br/>
                    All media types are declared in the theme being used and can be found in <i>etc/view.xml</i>]]></comment>
                    <depends>
                        <field id="use_cached_product_images">1</field>
                    </depends>
                </field>
                <field id="hover_image_id" translate="label comment" type="select" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Hover Image</label>
                    <source_model>Unbxd\ProductFeed\Model\Config\Source\MediaTypes</source_model>
                    <comment><![CDATA[Please specify image ID which use for <strong>hover image</strong>.<br/>
                    All media types are declared in the theme being used and can be found in <i>etc/view.xml</i>]]></comment>
                    <depends>
                        <field id="use_cached_product_images">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>