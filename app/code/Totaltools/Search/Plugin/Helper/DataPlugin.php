<?php

namespace Totaltools\Search\Plugin\Helper;

/**
 * @package     Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

use Magento\Store\Model\ScopeInterface;
use Totaltools\Search\Model\Product\DataSourceProvider\LabeledImage;

class DataPlugin
{
    /**
     * Config path for labled image settings
     */
    const CONFIG_PATH = 'unbxd_catalog/images/labeled_image_id';

    const HOVER_IMAGE_ID_PATH = 'unbxd_catalog/images/hover_image_id';

    const HOVER_IMAGE_TYPE = 'hover_image';

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     */
    public function __construct(\Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param \Unbxd\ProductFeed\Helper\Data $subject
     * @param callable $proceed
     * @param string $type
     * @param int|null $store
     * @return string|null
     */
    public function aroundGetImageByType(
        $subject,
        $proceed,
        $type,
        $store = null
    ) {
        if ($type === LabeledImage::DATA_SOURCE_CODE) {
            return $this->scopeConfig->getValue(self::CONFIG_PATH, ScopeInterface::SCOPE_STORE, $store);
        }

        if ($type === self::HOVER_IMAGE_TYPE) {
            return $this->scopeConfig->getValue(self::HOVER_IMAGE_ID_PATH, ScopeInterface::SCOPE_STORE, $store);
        }

        return $proceed($type, $store);
    }
}
