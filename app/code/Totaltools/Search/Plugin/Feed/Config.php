<?php
namespace Totaltools\Search\Plugin\Feed;

use Unbxd\ProductFeed\Model\Feed\Config as UnbxdConfig;

class Config
{
    const FIELD_KEY_HOVER_IMAGE_PATH = 'hover_image';

    /**
     * After plugin for getUrlAttributes method
     *
     * @param UnbxdConfig $subject
     * @param array $result
     * @return array
     */
    public function afterGetUrlAttributes(
        UnbxdConfig $subject,
        array $result
    ) {
        $result[] = self::FIELD_KEY_HOVER_IMAGE_PATH;
        return $result;
    }
}