<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Service;

use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Search\Api\Search\MetaDataInterface;
use Totaltools\Search\Api\Search\SearchClientInterface;
use Totaltools\Search\Model\Search\Exception\SearchException;
use Totaltools\Search\Model\Search\RequestType;

class SearchService
{
    const TEXT_TYPE_FILTER = "text";
    const RANGE_TYPE_FILTER = "range";

    /**
     * @var SearchClientInterface
     */
    private $searchClient;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var \Totaltools\Search\Helper\Data
     */
    private $helper;

    /**
     * @var MetaDataInterface
     */
    private $metaData;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * SearchService constructor.
     * @param SearchClientInterface $searchClient
     * @param RequestInterface $request
     * @param LoggerInterface $logger
     */
    public function __construct(
        SearchClientInterface $searchClient,
        RequestInterface $request,
        LoggerInterface $logger,
        \Totaltools\Search\Helper\Data $data,
        MetaDataInterface $metaData,
        StoreManagerInterface $storeManager
    ) {
        $this->searchClient = $searchClient;
        $this->request = $request;
        $this->logger = $logger;
        $this->helper = $data;
        $this->metaData = $metaData;
        $this->storeManager = $storeManager;
    }

    /**
     * @return array|mixed
     */
    public function getSearchResult()
    {
        if ($q = $this->request->getParam('q')) {
            try {
                $this->metaData->setStoreId($this->storeManager->getStore()->getId());
                $this->searchClient->setRequestType(RequestType::SEARCH_REQUEST)->search($q, []);

                if ($sort = $this->request->getParam("sort")) {
                    $this->searchClient->setSort($sort);
                }

                if ($pageSize = $this->request->getParam("pageSize")) {
                    $this->searchClient->setPageSize($pageSize);
                } elseif ($pageSize = $this->helper->getSearchPageSize()) {
                    $this->searchClient->setPageSize($pageSize);
                }

                if ($pageNo = $this->request->getParam('page')) {
                    $this->searchClient->setPageNo($pageNo);
                }

                if ($categoryFilter = $this->request->getParam("category-filter")) {
                    $this->searchClient->setCategoryFilter($categoryFilter);
                }

                if (sizeof($this->request->getParams()) > 1) {
                    $this->setAttributesFilter();
                 }

                return $this->searchClient->execute();
            } catch (SearchException $e) {
                $this->logger->emergency($e->getMessage());
                return [
                        "metaData" => $this->metaData->getMetaData()
                ];
            }
        }

        return [
            "metaData" => $this->metaData->getMetaData()
        ];
    }

    /**
     * set unbxd text & range attributes
     */
    protected function setAttributesFilter()
    {
        if ($filters = $this->request->getParams()) {
            foreach ($filters as $field => $filter) {
                if ($field != "q") {
                    $fieldType  = explode("_", $field);
                    $field = str_replace($fieldType[0]."_","", $field);
                    switch ($fieldType[0]) {
                        case self::TEXT_TYPE_FILTER:
                            $values = explode('|', $filter);
                            $this->searchClient->addTextFilter($field, $values);
                            break;
                        case self::RANGE_TYPE_FILTER:
                            $values = explode('-', $filter);
                            if (sizeof($values) > 1) {
                                $this->searchClient->addRangeFilter($field, $values[0], $values[1]);
                            }
                            break;
                    }
                }
            }
        }
    }

    /**
     * @return array|mixed
     */
    public function getAutoSuggest()
    {
        if ($q = $this->request->getParam('q')) {
            try {
                $this->metaData->setStoreId($this->storeManager->getStore()->getId());
                return $this->searchClient->setRequestType(RequestType::AUTO_SUGGEST_REQUEST)->search($q, [])->execute();
            } catch (SearchException $e) {
                $this->logger->emergency($e->getMessage());
                return [
                    "metaData" => $this->metaData->getMetaData()
                ];
            }
        }
        return [
            "metaData" => $this->metaData->getMetaData()
        ];
    }
}

