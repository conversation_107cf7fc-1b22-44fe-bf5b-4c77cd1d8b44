<?php
/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Service;

use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Search\Api\Search\MetaDataInterface;
use Totaltools\Search\Model\AutoSuggest\AutoSuggestClient;
use Totaltools\Search\Model\AutoSuggest\Exception\AutoSuggestException;

class AutoSuggestService
{
    /**
     * @var AutoSuggestClient
     */
    private $autoSuggestClient;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var RequestInterface
     */
    private $request;

    /**
     * @var MetaDataInterface
     */
    private $metaData;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * SearchService constructor.
     * @param AutoSuggestClient $autoSuggestClient
     * @param RequestInterface $request
     * @param LoggerInterface $logger
     */
    public function __construct(
        AutoSuggestClient $autoSuggestClient,
        RequestInterface $request,
        LoggerInterface $logger,
        MetaDataInterface $metaData,
        StoreManagerInterface $storeManager
    ) {
        $this->autoSuggestClient = $autoSuggestClient;
        $this->request = $request;
        $this->logger = $logger;
        $this->metaData = $metaData;
        $this->storeManager = $storeManager;
    }

    /**
     * @return \Totaltools\Search\Model\AutoSuggest\Response\Results
     * @throws \Totaltools\Search\Model\AutoSuggest\Exception\AutoSuggestException
     */
    public function getAutoSuggestResult()
    {
        if ($q = $this->request->getParam('q')) {
            try {
                $this->metaData->setStoreId($this->storeManager->getStore()->getId());
                $this->autoSuggestClient->autosuggest($q);
                return $this->autoSuggestClient->execute();
            } catch (AutoSuggestException $e) {
                $this->logger->emergency($e->getMessage());
                return [
                    "metaData" => $this->metaData->getMetaData()
                ];
            }
        }

        return [
            "metaData" => $this->metaData->getMetaData()
        ];
    }
}