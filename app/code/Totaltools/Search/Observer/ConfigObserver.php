<?php

namespace Totaltools\Search\Observer;

/**
 * @package     Totaltools_Search
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\App\Cache\Manager as CacheManager;
use Totaltools\Search\Helper\Data as Helper;
use Psr\Log\LoggerInterface;

/** @class CofingObserver */
class ConfigObserver implements ObserverInterface
{
    /**
     * @var CacheManager
     */
    private $_cacheManager;

    /**
     * @var LoggerInterface
     */
    private $_logger;

    /**
     * @param CacheManager $cacheManager
     */
    public function __construct(
        CacheManager $cacheManager,
        LoggerInterface $logger
    ) {
        $this->_cacheManager = $cacheManager;
        $this->_logger = $logger;
    }

    /**
     * @inheritdoc
     */
    public function execute(Observer $observer)
    {
        $changedPaths = (array) $observer->getEvent()->getChangedPaths();

        if (in_array(Helper::XML_PATH_UNBXD_SETUP_COMMON_SHOW_PRICE, $changedPaths)) {
            try {
                $this->_cacheManager->clean([
                    'block_html',
                    'full_page'
                ]);
            } catch (\Exception $e) {
                $this->_logger->warning($e->getMessage());
            }
        }
    }
}
