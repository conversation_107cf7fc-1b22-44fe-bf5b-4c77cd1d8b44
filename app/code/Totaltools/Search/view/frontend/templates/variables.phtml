<?php

/**
 * @package     Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2020 Totaltools. <https://totaltools.com.au>
 */

 /**
  * @var \Totaltools\Search\Block\Search\Variables $block
  */
?>

<script type="text/javascript">
    window.unbxdConfig = {
        storeName: '<?php echo /* @noEscape */ $block->getCurrentStoreName(); ?>',
        siteKey: '<?php echo /* @noEscape */ $block->getSiteKey(); ?>',
        apiKey: '<?= /* @noEscape */ $block->getApiKey(); ?>',
        autoSugguestUrl: '<?= /* @noEscape */ $block->getAutosuggestUrl(); ?>',
        autoCompleteUrl: '<?= /* @noEscape */ $block->getAutocompleteUrl(); ?>',
        searchUrl: '<?= /* @noEscape */ $block->getSearchUrl(); ?>',
        storeCurrencyCode: '<?php echo /* @noEscape */ $block->getCurrencyCode(); ?>',
        storeCurrencySymbol: '<?php echo /* @noEscape */ $block->getCurrencySymbol(); ?>',
        searchPageSize: <?php echo /* @noEscape */ $block->getSearchPageSize(); ?>,
        placeholderImgUrl: '<?php echo /* @noEscape */ $block->getPlaceholderImgUrl(); ?>',
        autoSuggestDelay: <?php echo /* @noEscape */ $block->getAutosuggestTypingDelay(); ?>,
        showPrice: <?php echo /* @noEscape */ $block->getShowPrice(); ?>,
        skippedCategories: '<?= /** @noEscape */ $block->getHelper()->getSkipCategoryList(); ?>',
        popularSearchTerms: '<?= /** @noEscape */ $block->getHelper()->getPopularSearchTerms(); ?>',
        attributeLabelsMap: '<?= /** @noEscape */ $block->getHelper()->getAttributeLabelsMap(); ?>',
        isHoverEnabled: '<?= /** @noEscape */ $block->getHelper()->isHoverEnabled(); ?>',
        hoverStyle: '<?= /** @noEscape */ $block->getHelper()->getHoverStyle(); ?>',
        catalogFrontendDisplayMode: '<?= /** @noEscape */ $block->getHelper()->getCatalogFrontendDisplayMode(); ?>',
    };
    <?php if ($block->getSiteKey() && $block->getSiteKey() != '') : ?>

    (function(w, AEC) {
        w.dataLayer = w.dataLayer || [];

        AEC.Cookie.impressions({
            unbxdSiteKey: w.unbxdConfig.siteKey
        }).push(dataLayer, false);
    })(window, AEC);
    <?php endif; ?>
</script>
