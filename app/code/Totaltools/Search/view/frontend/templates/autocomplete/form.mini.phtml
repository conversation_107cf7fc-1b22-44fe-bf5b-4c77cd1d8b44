<?php

/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile
?>
<?php
/** @var $block \Magento\Framework\View\Element\Template */
/** @var $helper \Magento\Search\Helper\Data */
$helper = $this->helper('Magento\Search\Helper\Data');
?>
<div id="search-main" class="block block-search">
    <div class="block block-title"><strong><?php /* @escapeNotVerified */ echo __('Search'); ?></strong></div>
    <div class="block block-content">
        <form class="form minisearch opened" id="search_mini_form" action="<?php /* @escapeNotVerified */ echo $helper->getResultUrl() ?>" method="get">
            <div class="field search">
                <label id="minisearch-label" class="label opened" for="search" data-role="minisearch-label">
                    <span><?php /* @escapeNotVerified */ echo __('Search'); ?></span>
                </label>
                <div class="control opened" id="minisearch-control">
                    <div class="new-search-autocomplete">
                        <input id="search" type="text" name="<?php /* @escapeNotVerified */ echo $helper->getQueryParamName() ?>" placeholder="<?php /* @escapeNotVerified */ echo __('Find your tools...'); ?>" class="input-text" maxlength="<?php /* @escapeNotVerified */ echo $helper->getMaxQueryLength(); ?>" role="combobox" aria-haspopup="false" aria-autocomplete="both" autocomplete="off" />
                        <?php echo $block->getChildHtml() ?>
                    </div>
                </div>
            </div>
            <div class="actions">
                <button type="submit" title="<?php echo $block->escapeHtml(__('Search')) ?>" class="action search">
                    <span><?php /* @escapeNotVerified */ echo __('Search'); ?></span>
                </button>
            </div>
        </form>
    </div>
    <div class="search-layout"></div>
</div>
<script>
    require([
        'jquery',
        'Magento_Checkout/js/model/full-screen-loader'
    ], function($, fullScreenLoader) {
        'use strict';
        $(document).ready(function() {
            $('body').on('click', '.search-autocomplete-more a, a.recently-viewed-label', function() {
                $('body').trigger('processStart');
            });
        });
    });
</script>