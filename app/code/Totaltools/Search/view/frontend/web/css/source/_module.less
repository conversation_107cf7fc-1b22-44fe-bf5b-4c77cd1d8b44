// Import
@import "./lib/_carousel.less";

// Variables
@miniform-mobile-input__background-color: #E41B13;
@tt-icon-cross: "\e906";

@autosuggest__background-color: #fff;
@autosuggest__control__background-color: #E41B13;
@autosuggest__border-color: #ccc;
@autosuggest__base-color: #333;
@autosuggest__font-family: "@{font-family-name__base}-condensed";
@applyfilters__background-color: #2E2D76;
@clearfilters__hover-color: lighten(#2E2D76, 5%);
@clearfilter__button: #c7c7c7;
@clearfilter__hover-color: #494949;

@products-savings__font-family: "@{font-family-name__base}-extra-condensed";
@products-savings__background-color: #FFF32A;
@products-link-hover__background-color: #f2f2f2;
@products-info-title__color: #2E2D76;
@products-mpn__color: grey;
@products-price__color: #E41B13;

@suggestions__background-color: #f9f9f9;

@categories-section-title__color: #a6a6a6;

@instantsearch-loader__background-color: @products-info-title__color;

@custom-form-element__border-color: #c6c6c6;
@color-gray10: #e9e9e9;
@font-tt-icons: 'TT Icons';
@screen__ml: 1280px;
@color-black-75: rgba(0,0,0,0.75);
@color-blue: #2e2d76;

// Common styles
& when (@media-common =true) {

    //*********************************
    // Search miniform
    //*********************************
    .minisearch {
        .control {
            input#search {
                background-color: @autosuggest__background-color;
                color: @autosuggest__base-color;
                border: 0 none;
                height: 42px;
                .lib-line-height(42);
            }
        }

        .action.search {
            &:before {
                background: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0nNDAnIGhlaWdodD0nNDAnIHZpZXdCb3g9JzAgMCA0MCA0MCcgIGZpbGw9JyNlNDFiMTMnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zyc+PHBhdGggZD0nTTE1LjU1MyAzMS4xMDdjOC41OSAwIDE1LjU1NC02Ljk2NCAxNS41NTQtMTUuNTU0UzI0LjE0MyAwIDE1LjU1MyAwIDAgNi45NjQgMCAxNS41NTNjMCA4LjU5IDYuOTY0IDE1LjU1NCAxNS41NTMgMTUuNTU0em0wLTMuODg4YzYuNDQzIDAgMTEuNjY2LTUuMjI1IDExLjY2Ni0xMS42NjggMC02LjQ0Mi01LjIyNS0xMS42NjUtMTEuNjY4LTExLjY2NS02LjQ0MiAwLTExLjY2NSA1LjIyMy0xMS42NjUgMTEuNjY1IDAgNi40NDMgNS4yMjMgMTEuNjY2IDExLjY2NSAxMS42NjZ6bTEyLjIxIDMuODRhMi4wMDUgMi4wMDUgMCAwIDEgLjAwMi0yLjgzM2wuNDYzLS40NjNhMi4wMDggMi4wMDggMCAwIDEgMi44MzMtLjAwM2w4LjE3IDguMTY4Yy43OC43OC43OCAyLjA1LS4wMDQgMi44MzNsLS40NjIuNDYzYTIuMDA4IDIuMDA4IDAgMCAxLTIuODM0LjAwNGwtOC4xNjgtOC4xN3onIGZpbGwtcnVsZT0nZXZlbm9kZCcvPjwvc3ZnPg==) no-repeat center right / 20px !important;
            }
        }
    }

    //*********************************
    // Autocomplete Search
    //*********************************

    .search-autocomplete {
        width: 100%;

        &-container {
            font-family: @autosuggest__font-family;
            position: absolute;
            width: 100%;
            top: 100%;
            left: 0;
            .lib-clearfix();
            .lib-css(box-sizing, border-box, 1);
            .loading-mask {
                background-color: rgba(0,0,0,.4);
            }
        }

        &-wrapper {
            width: 100%;
            .lib-css(box-sizing, border-box, 1);
            position: relative;

            .col-left {
                padding: 0 10px;
                .lib-css(transition, all 0.2s ease-in-out, 1);

                & > div {
                    & > p {
                        font-size: 14px;
                        line-height: 20px;
                        color: @color-gray-light6;
                        position: relative;
                    }
                }

                .recently-viewed-list {
                    position: relative;

                    .categories-list-item {
                        .lib-vendor-prefix-display;
                        .lib-css(justify-content, space-between, 1);
                    }

                    .recently-viewed-products {
                        padding-top: 15px;

                        h5 {
                            color: @color-blue2;
                            font-family: @font-family-name__base;
                            .lib-css(font-weight, @font-weight__bold);
                            line-height: 20px;
                            font-size: 18px;
                            letter-spacing: 1px;
                            text-transform: uppercase;
                            margin: 0;
                            padding: 0 0 3px;
                            border-bottom: 1px solid @color-gray10;
                            text-align: left;
                            .lib-css(order, 1, 1);
                            margin-bottom: 10px;
                        }

                        .recently-viewd-item {
                            .lib-vendor-prefix-display;
                            .lib-vendor-prefix-flex-wrap(nowrap);
                            overflow-x: auto;

                            .search-autocomplete-product {
                                .info {
                                    .price-block, 
                                    .price-box {
                                        .product-price .price-main {
                                            font-size: 3rem;
                                        }
                                    }
                                }
                            }
                        }
                    }

                    .searched-terms {
                        .searched {
                            h5 {
                                padding: 0px 10px;
                            }

                            border-bottom: 1px dotted #cccccc;
                            height: 40px;

                            .clear-all {
                                position: absolute;
                                cursor: pointer;
                                right: 5px;
                                top: 5px;

                                .a {
                                    padding: 10px;
                                    text-decoration: none;

                                }
                            }
                        }
                    }
                }

                .search-autocomplete-corrections {
                    .ttl {
                        display: none;
                    }

                    .opt {
                        display: block;
                        text-transform: capitalize;
                        color: @autosuggest__base-color;
                        padding: 10px 10px 7px 15px;
                        cursor: pointer;
                        border-bottom: 1px dotted @autosuggest__border-color;

                        &:after {
                            content: ",";
                        }

                        &:last-child:after {
                            display: none;
                        }
                    }
                }
            }

            .col-right {
                background-color: white;
                .lib-css(transition, all 0.2s ease-in-out, 1);
                padding-top: 15px;

                .search-autocomplete-corrections {
                    margin-top: 10px;
                    text-align: center;

                    .ttl {
                        display: block;
                    }

                    .opt {
                        text-transform: capitalize;
                        color: @products-info-title__color;
                        padding-right: 3px;
                        cursor: pointer;
                        font-weight: 700;

                        &:after {
                            content: ",";
                        }

                        &:last-child:after {
                            display: none;
                        }

                        &:hover {
                            text-decoration: underline;
                        }
                    }
                }
            }

            .col-left[aria-busy="true"],
            .col-right[aria-busy="true"] {
                position: relative;
                min-height: 130px;
                overflow: hidden;

                &:before {
                    content: "";
                    background-color: rgba(255, 255, 255, 0.35);
                    display: block;
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                    z-index: 999;
                }

                .instant-search-loader {
                    position: absolute;
                    z-index: 1000;
                    top: 50%;
                    left: 50%;
                    margin-top: -32px;
                    margin-left: -32px;
                    width: 64px;
                    height: 64px;
                }
            }
        }

        &-back {
            display: none;
        }

        &-dropdown {
            border: 0 none;
            display: block;
            background-color: @color-white;
            .lib-css(transition, all .2s ease, 1);
            position: relative;
            padding: 25px 15px 15px;
            opacity: 0;
            transition: opacity 0.1s ease-in-out;
            pointer-events: none;
            z-index: -1;

            &.open {
                opacity: 1;
                pointer-events: unset;
                z-index: 30;
            }

            .close-search-btn {
                width: 20px;
                height: 20px;
                margin-left: auto;
                border: 0;
                background: transparent;
                padding: 0;
                display: block;
                font-size: 0;

                &::before {
                    font-size: 20px;
                    line-height: 1;
                    color: @color-black;
                    content: "\e906";
                    font-family: 'TT Icons';
                    vertical-align: middle;
                    display: inline-block;
                    font-weight: normal;
                    text-align: center;
                }
            }

            .search-wrapper {
                position: relative;
            }

            &.show {
                display: block;
            }

            &.hide {
                display: none;
            }
        }

        &-error,
        &-no-result {
            text-align: center;

            p {
                margin: 0;
            }
        }

        &-categories {
            .section {
                &-title {
                    .lib-font-size(12.8px);
                    font-weight: 700;
                    text-transform: uppercase;
                    color: @categories-section-title__color;
                }

                &-content {
                    color: @autosuggest__base-color;

                    &.no-result {
                        font-style: italic;
                    }
                }
            }

            .categories-list {
                .categories-list-item {
                    .category-container {
                        display: flex;
                        align-items: center;
                    }
                    .spinner {
                        position: relative;
                        width: 35px;
                        height: 35px;
                        display: inline-block;
                        border-radius: 10px;
                        @-webkit-keyframes fade {
                            from {
                                opacity: 1;
                            }
    
                            to {
                                opacity: 0.25;
                            }
                        }
                        div {
                            width: 3%;
                            height: 16%;
                            background: rgba(0, 0, 0, 0.8);
                            position: absolute;
                            left: 49%;
                            top: 43%;
                            opacity: 0;
                            -webkit-border-radius: 50px;
                            -webkit-box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
                            -webkit-animation: fade 1s linear infinite;

                        }

                        .bar1 {
                            -webkit-transform: rotate(0deg) translate(0, -130%);
                            -webkit-animation-delay: 0s;
                        }

                        .bar2 {
                            -webkit-transform: rotate(30deg) translate(0, -130%);
                            -webkit-animation-delay: -0.9167s;
                        }

                        .bar3 {
                            -webkit-transform: rotate(60deg) translate(0, -130%);
                            -webkit-animation-delay: -0.833s;
                        }

                        .bar4 {
                            -webkit-transform: rotate(90deg) translate(0, -130%);
                            -webkit-animation-delay: -0.7497s;
                        }

                        .bar5 {
                            -webkit-transform: rotate(120deg) translate(0, -130%);
                            -webkit-animation-delay: -0.667s;
                        }

                        .bar6 {
                            -webkit-transform: rotate(150deg) translate(0, -130%);
                            -webkit-animation-delay: -0.5837s;
                        }

                        .bar7 {
                            -webkit-transform: rotate(180deg) translate(0, -130%);
                            -webkit-animation-delay: -0.5s;
                        }

                        .bar8 {
                            -webkit-transform: rotate(210deg) translate(0, -130%);
                            -webkit-animation-delay: -0.4167s;
                        }

                        .bar9 {
                            -webkit-transform: rotate(240deg) translate(0, -130%);
                            -webkit-animation-delay: -0.333s;
                        }

                        .bar10 {
                            -webkit-transform: rotate(270deg) translate(0, -130%);
                            -webkit-animation-delay: -0.2497s;
                        }

                        .bar11 {
                            -webkit-transform: rotate(300deg) translate(0, -130%);
                            -webkit-animation-delay: -0.167s;
                        }

                        .bar12 {
                            -webkit-transform: rotate(330deg) translate(0, -130%);
                            -webkit-animation-delay: -0.0833s;
                        }
                    }
                    &:hover {
                        text-decoration: none;
                        font-weight: 700;
                        background-color: white;
                    }
                    margin-top: 2px;
                    cursor: pointer;

                    &.list-item-single {
                        cursor: default;
                    }
                }
                list-style: none;
                padding: 0;
                margin: 0;

                &-item {
                    margin: 0;
                    border-bottom: 1px dotted @autosuggest__border-color;

                    a {
                        &.recently-viewed-icons-close {
                            display: flex;
                            align-items: center;

                            .lib-icon-font(@_icon-font-content: @tt-icon-cross,
                                @_icon-font-display: block,
                                @_icon-font-size: 12px,
                                @_icon-font-position: after);

                            &::after {
                                line-height: 1;
                            }
                        }

                        &.recently-viewed-label {
                            .lib-css(flex, 1, 1);

                        }

                        color: @autosuggest__base-color;
                        display: block;
                        padding: 10px 10px 7px 15px;
                        text-transform: capitalize;
                        .lib-font-size(14);
                        .lib-line-height(20);
                        .lib-css(border-radius, 2px, 1);

                        &.active,
                        &:hover {
                            text-decoration: none;
                            font-weight: 700;
                            background-color: white;
                        }

                        &.active {
                            position: relative;
                        }
                    }
                }
            }
        }

        &-products {
            padding: 0 10px;

            h5 {
                color: @color-blue2;
                font-family: @font-family-name__base;
                .lib-css(font-weight, @font-weight__bold);
                line-height: 20px;
                font-size: 18px;
                letter-spacing: 1px;
                text-transform: uppercase;
                margin: 0;
                padding: 0 0 3px;
                border-bottom: 1px solid @color-gray10;
                width: 82%;
                text-align: left;
                .lib-css(order, 1, 1);
            }

            >div {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;

                &:last-child {
                    .lib-vendor-prefix-flex-wrap(nowrap);
                    .lib-css(order, 3, 1);
                    .lib-css(overflow-x, auto, 1);
                    width: 100%;
                }
            }
        }

        .search-autocomplete-categories {
            padding-top: 0px;
        }

        &-product {
            width: 155px;
            min-width: 155px;

            & > a {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-direction(row);
                .lib-vendor-prefix-flex-wrap;
                width: 100%;
                white-space: normal;
                position: relative;
                padding: 8px;
                color: @autosuggest__base-color;
                text-decoration: none;
                text-align: left;
                border: 2px solid @color-white;
                .lib-css(border-radius, 2px, 1);

                &:hover {
                    border-color: @color-blue;
                    text-decoration: none;
                }
            }

            .thumb {
                min-width: 100%;    
                padding-top: 100%;
                position: relative;

                img {
                    position: absolute;
                    width: 100%;
                    height: 100%;
                    top: 0;
                    left: 0;
                }
            }

            .product-labels {
                .product-label {
                    display: block;
                    .lib-clearfix();
                    margin-top: 5px;
                }
            }

            .info {
                width: 100%;
                padding-top: 10px;

                >span,
                .product-title {
                    color: @autosuggest__base-color;
                    font-weight: 700;
                    text-align: center;
                    font-size: 14px;
                    line-height: 16px;
                    height: 64px;
                    margin-bottom: 5px;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 4;
                }

                .product-title {
                    width: 100%;
                    .lib-css(order, 1, 1);
                }

                .mpn {
                    .lib-css(order, 2, 1);
                    .lib-font-size(18);
                    font-family: @products-savings__font-family;
                    font-weight: normal;
                    color: @products-mpn__color;
                    display: block;
                    width: 100%;
                    word-break: break-all;
                    word-wrap: break-word;
                }

                .price-block,
                .price-box {
                    .lib-css(order, 3, 1);
                    .lib-vendor-prefix-display;
                    .lib-vendor-prefix-flex-wrap;
                    .lib-css(align-items, center, 1);
                    .lib-css(justify-content, center, 1);
                    margin-bottom: 5px;

                    .product-price {
                        position: relative;
                        top: 2px;

                        .currency-symbol {
                            font-family: @products-savings__font-family;
                            font-weight: 700;
                            .lib-font-size(16);
                            color: @products-price__color;
                            vertical-align: super;
                            position: relative;
                            top: -3px;
                        }

                        .price-main {
                            font-family: @products-savings__font-family;
                            font-weight: 700;
                            .lib-font-size(26);
                            color: @products-price__color;
                            
                            .decimal-dot {
                                font-size: 20%;
                                line-height: inherit;
                                color: transparent;
                                vertical-align: super;
                            }

                            .price-decimal {
                                .lib-font-size(16);
                                vertical-align: super;
                                position: relative;
                                top: -3px;
                            }
                        }
                    }

                    .you-save-statement {
                        .lib-vendor-prefix-display();
                        .lib-vendor-prefix-flex-wrap(wrap);
                        .lib-css(justify-content, center, 1);
                        .lib-css(border-radius, 3px, 1);
                        .lib-css(background-color, @products-savings__background-color);
                        .lib-css(font-family, @font-family-name__base);
                        .lib-css(color, @autosuggest__base-color);
                        .lib-css(box-sizing, border-box, 1);
                        .lib-font-size(16);
                        line-height: 1;
                        font-weight: 800;
                        font-style: normal;
                        text-transform: uppercase;
                        white-space: nowrap;
                        padding: 5px 6px 4px;
                        position: relative;
                        z-index: 1;
                        margin-left: 3px;

                        .wrap {
                            .lib-vendor-prefix-display();
                            .lib-vendor-prefix-flex-wrap(nowrap);
                            .lib-vendor-box-align(flex-start);
                        }

                        .you-save-decimal,
                        .savings-label-currency,
                        .savings-label-decimal,
                        .currency-symbol,
                        .decimal-symbol,
                        .price-decimal {
                            .lib-font-size(12);
                        }

                        .currency-symbol,
                        .savings-label-currency {
                            margin-left: 3px;
                        }

                        .decimal-symbol,
                        .you-save-decimal,
                        .price-decimal,
                        .savings-label-decimal {
                            margin-left: 0.5px;
                        }
                    }
                }

                .price-block {
                    min-height: 37.5px;
                }

                .product-item-actions {
                    button,
                    a {
                        &.todetails,
                        &.tocart {
                            position: static;
                            background-color: @products-price__color;
                            border: 2px solid #cc1811 !important;
                            color: @autosuggest__background-color;
                            width: 100%;
                            padding: 5px 7px;
                            white-space: nowrap !important;
                            font-family: Arial;
                            .lib-font-size(14);
                            .lib-line-height(22);
                            min-width: 110px;
                            font-weight: 700;
                            .lib-css(border-radius, 2px, 1);
                            text-align: center;

                            &:hover {
                                background-color: #ee352d;
                            }

                            &::before {
                                display: none;
                            }
                        }

                        &.todetails {
                            &::after {
                                content: '\E82A';
                                font-size: inherit;
                                line-height: inherit;
                                color: inherit;
                                .lib-css(font-family, @font-tt-icons);
                                margin: -3px 0 0 3px;
                                vertical-align: middle;
                                display: inline-block;
                                .lib-css(font-weight, @font-weight__regular);
                            }
                        }
                    }
                }

                .labels-wrapper {
                    margin-left: auto;
                    .lib-css(order, 4, 1);

                    .shipping-label {
                        .lib-icon-font(@_icon-font-content: "\f0d1",
                            @_icon-font-margin: -2px 3px 0 0);

                        padding: 2px 3px 2px;
                        letter-spacing: normal;
                        .lib-css(border-radius, 4px, 1);
                    }
                }
            }
        }

        &-suggestions {
            background-color: @suggestions__background-color;
            padding: 10px 0;
            border-top: 1px solid @autosuggest__border-color;
            border-left: 1px solid @autosuggest__border-color;
            text-align: center;
            color: @autosuggest__base-color;

            a,
            strong {
                color: @products-info-title__color;
                font-weight: 700;
                cursor: pointer;

                &:hover {
                    text-decoration: underline;
                }
            }
        }

        &-more {
            margin: 15px 0;
            .lib-css(order, 2, 1);
            width: 100%;

            a.button {
                display: block;
                width: 100%;
                background: white;
                border: 2px solid #272664;
                color: #2E2D76;
                cursor: pointer;
                font-weight: 700;
                margin: 0;
                padding: 13px 18px;
                .lib-font-size(17);
                .lib-line-height(18);
                box-sizing: border-box;
                vertical-align: middle;
                text-decoration: none;
                text-align: center;

                &:hover {
                    background: #2E2D76;
                    color:white;
                }

                span {
                    font-weight: 800;
                    text-transform: capitalize;
                }
            }

            &.hidden {
                display: none !important;
            }
        }
        
    }

    .search-autocomplete {
        width: 100%;
    }

    //*********************************
    // Instant Search
    //*********************************
    .instant-search {
        text-align: center;

        &-page {
            .page-title-wrapper {
                .page-title {
                    font-weight: 700;
                }
            }

            .columns {
                .column.main {
                    width: 100% !important;
                    padding-left: 0;
                }

                .sidebar,
                .sidebar-main {
                    display: none !important;
                }
            }
        }

        &-wrap[aria-busy="true"] {
            position: relative;

            &:before {
                content: "";
                display: block;
                width: 100%;
                height: 100%;
                position: absolute;
                z-index: 999;
                top: 0;
                left: 0;
                background-color: rgba(255, 255, 255, 0.35);
            }

            .instant-search-loader {
                position: absolute;
                top: 7%;
                left: 50%;
                z-index: 9999;
            }
        }

        &-row {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap;
            width: 100%;
        }

        &-banners {
            margin-bottom: @indent__m;
        }

        &-results,
        &-sidebar {
            .lib-css(box-sizing, border-box, 1);
        }

        &-results {
            .sorter {
                .sorter-action {
                    &.sort-relevant {
                        display: none;
                    }
                }
            }

            .pages {
                .current a {
                    border-color: @autosuggest__border-color;
                    color: @autosuggest__base-color;
                    font-weight: 400;
                    cursor: default;
                }

                .item {
                    float: left;
                }

                .action {
                    padding: 0 !important;
                    min-width: 42px !important;
                    span {
                        display: none;
                    }
                    &.previous {
                        .lib-icon-font(
                            @_icon-font-content: @icon-prev,
                            @_icon-font-size: 28px,
                            @_icon-font-line-height: 38px
                        );
                    }
            
                    &.next {
                        .lib-icon-font(
                            @_icon-font-content: @icon-next,
                            @_icon-font-size: 28px,
                            @_icon-font-line-height: 38px
                        );
                    }
                }
            }

            .product-item-actions {
                width: 100%;
                text-align: center;

                .tocart:before {
                    display: none;
                }
            }
        }

        &-no-result {
            width: 100%;

            h2 {
                font-family: @font-family-name__base;
                .lib-font-size(24);
                font-weight: 300;
                color: #666;
                margin-top: 20px;

                strong {
                    font-weight: 500;
                    color: @products-info-title__color;
                }
            }

            h3 {
                font-family: @autosuggest__font-family;
                .lib-font-size(22);
                font-weight: 400;
                margin-top: 14px;
                margin-bottom: 12px;

                a,
                span {
                    font-weight: 500;
                    color: @products-info-title__color;
                    cursor: pointer;

                    &:hover {
                        text-decoration: underline;
                    }
                }
            }

            .page-title {
                font-weight: 700;
            }

            .no-result-found {
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                justify-content: center;
                background: @color-white-fog;
                padding: 15px 10px;
                margin-bottom: 10px;

                h2 {
                    color: @color-gray20;
                    .lib-font-size(22);
                    margin: 0;
                } 

                h3 {
                    margin: 0;
                }
            }
        }

        &-loader {
            display: inline-block;
            position: relative;
            width: 80px;
            height: 80px;
            margin: 0 auto;
            font-family: @autosuggest__font-family;

            div {
                position: absolute;
                top: 33px;
                width: 13px;
                height: 13px;
                .lib-css(border-radius, 50%, 1);
                background: @instantsearch-loader__background-color;
                animation-timing-function: cubic-bezier(0, 1, 1, 0);

                &:nth-child(1) {
                    left: 8px;
                    animation: lds-ellipsis1 0.6s infinite;
                }

                &:nth-child(2) {
                    left: 8px;
                    background-color: @products-price__color;
                    animation: lds-ellipsis2 0.6s infinite;
                }

                &:nth-child(3) {
                    left: 32px;
                    animation: lds-ellipsis2 0.6s infinite;
                }

                &:nth-child(4) {
                    left: 56px;
                    background-color: @products-price__color;
                    animation: lds-ellipsis3 0.6s infinite;
                }
            }
        }

        &-sidebar {
            text-align: left;

            .filter-options {
                .filter-options-title {
                    cursor: pointer;
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .title-icon {
                        color: @products-info-title__color;
                        font-size: 25px;
                        margin: 0 5px;
                    }

                    p {
                        margin: 0;
                    }

                    &:after {
                        display: none;
                    }
                }

                .filter-options-content {
                    .item {
                        padding-left: 0;
                    }

                    &.filter-options-categories {
                        ul {
                            list-style: none;
                            margin: 0;
                            padding: 0;

                            li {
                                &.level-2 {
                                    margin-left: 10px;
                                }

                                &.level-3 {
                                    margin-left: 20px;
                                }

                                &.level-4 {
                                    margin-left: 30px;
                                }

                                a {
                                    display: inline-block;
                                    color: @autosuggest__base-color;

                                    &:hover {
                                        cursor: pointer;
                                        text-decoration: underline;
                                        color: @products-info-title__color;
                                    }
                                }

                                &.active {
                                    position: relative;

                                    &:before {
                                        content: "";
                                        display: block;
                                        position: absolute;
                                        top: 0;
                                        left: -10px;
                                        width: 4px;
                                        height: 100%;
                                        background-color: @products-info-title__color;
                                    }

                                    &:after {
                                        content: "";
                                        display: block;
                                        position: absolute;
                                        z-index: 9;
                                        top: 0;
                                        right: 0;
                                        width: 0;
                                        height: 100%;
                                        background: linear-gradient(90deg,
                                                transparent 0%,
                                                rgba(255, 255, 255, 1) 100%);
                                        .lib-css(transition, all 0.2s ease-in-out, 1);
                                    }

                                    &:hover {
                                        &:after {
                                            width: 75%;
                                        }
                                    }

                                    a:hover {
                                        cursor: default;
                                        color: @autosuggest__base-color;
                                        text-decoration: none;
                                    }
                                }

                                .name {
                                    padding-right: 5px;
                                }

                                .count {
                                    margin-right: 5px;

                                    &:before {
                                        content: "\(";
                                    }

                                    &:after {
                                        content: "\)";
                                    }
                                }

                                .clear {
                                    display: block;
                                    width: 18px;
                                    height: 18px;
                                    color: @products-price__color;
                                    border: 1px solid @products-price__color;
                                    text-align: center;
                                    position: absolute;
                                    z-index: 10;
                                    top: 50%;
                                    right: 0;
                                    margin-top: -9px;
                                    cursor: pointer;
                                    .lib-css(opacity, 0.35, 1);
                                    .lib-font-size(14);
                                    .lib-line-height(15);
                                    .lib-css(border-radius, 50%, 1);
                                    .lib-css(transition, all 0.1s ease, 1);
                                    .lib-css(box-sizing, border-box, 1);

                                    &:hover,
                                    &:active {
                                        .lib-css(transform, scale(1.15), 1);
                                    }
                                }

                                &:hover {
                                    .clear {
                                        .lib-css(opacity, 1, 1);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .checkbox {

                &:checked,
                &:not(:checked) {
                    position: absolute;
                    left: -9999px;

                    &+label {
                        position: relative;
                        margin-top: -3px;
                        padding-top: 3px;
                        padding-left: 34px;
                        cursor: pointer;
                        line-height: 20px;
                        display: inline-block;
                        clear: both;

                        &:before {
                            content: "";
                            position: absolute;
                            left: 0;
                            top: 0;
                            width: 22px;
                            height: 22px;
                            background: @autosuggest__background-color;
                            border: 1px solid @custom-form-element__border-color;
                            .lib-css(border-radius, 3px, 1);
                        }

                        &:after {
                            content: "\f00c";
                            font-family: "TT Icons";
                            width: 22px;
                            height: 22px;
                            background: transparent;
                            color: @products-info-title__color;
                            position: absolute;
                            top: 2px;
                            left: 4px;
                            .lib-css(border-radius, 0, 1);
                            .lib-css(opacity, 0, 1);
                            .lib-css(transform, scale(0), 1);
                            .lib-css(transition, all 0.2s ease-in-out, 1);
                        }
                    }
                }

                &:checked {
                    &+label {
                        font-weight: 700;

                        &:after {
                            .lib-css(opacity, 1, 1);
                            .lib-css(transform, scale(1), 1);
                        }
                    }
                }
            }

            .range-filter-content {
                padding-bottom: 50px;
            }

            // nouislider overrides
            .noUi-connect {
                background: @products-info-title__color;
            }

            .noUi-tooltip {
                display: none;
            }

            .noUi-active .noUi-tooltip {
                display: block;
            }

            .noUi-value {
                .lib-font-size(13);
                .lib-line-height(22);
            }

            .noUi-horizontal {
                height: 14px;

                .noUi-handle {
                    width: 16px;
                    top: -8px;
                    right: -8px;

                    &:before {
                        left: 6px;
                    }

                    &:after {
                        left: 8px;
                    }
                }

                .noUi-tooltip {
                    font-family: @autosuggest__font-family;
                    .lib-font-size(13);
                    font-weight: bold;
                    color: @products-info-title__color;
                    padding: 3px 5px 2px;
                }
            }
        }
    }

    @keyframes lds-ellipsis1 {
        0% {
            transform: scale(0);
        }

        100% {
            transform: scale(1);
        }
    }

    @keyframes lds-ellipsis3 {
        0% {
            transform: scale(1);
        }

        100% {
            transform: scale(0);
        }
    }

    @keyframes lds-ellipsis2 {
        0% {
            transform: translate(0, 0);
        }

        100% {
            transform: translate(24px, 0);
        }
    }
}

// Mobile only
.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__m) {

    //*********************************
    // Search miniform
    //*********************************
    .minisearch {
        .label:before {
            display: none !important;
        }

        .control {
            background-color: @autosuggest__control__background-color;
            display: block;
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            z-index: 20;
            // .lib-css(box-shadow, 3px 3px 2px 0 rgba(0, 0, 0, 0.2), 1);
            .lib-css(box-sizing, border-box, 1);

            input[type="text"] {
                border: 0 none;
                margin-top: 0;
                margin-bottom: 10px;
                height: 44px;
                .lib-line-height(44);

                &::placeholder {
                    color: @products-mpn__color;
                }

                &::-webkit-input-placeholder {
                    color: @products-mpn__color;
                }

                &::-moz-placeholder {
                    color: @products-mpn__color;
                    opacity: 1;
                }

                &:-moz-placeholder {
                    color: @products-mpn__color;
                    opacity: 1;
                }

                &:-ms-input-placeholder {
                    color: @products-mpn__color;
                }
            }
        }

        .actions {
            position: absolute;
            bottom: -20%;
            right: 4%;
            display: none;

            .action.search {
                position: absolute;
                right: 10px;
                top: -5px;
                z-index: 20;
                display: inline-block;
                background-image: none;
                background: none;
                border: 0;
                box-shadow: none;
                line-height: inherit;
                margin: 0;
                padding: 0;
                text-decoration: none;
                text-shadow: none;
                font-weight: 400;
                padding: 5px 0;

                span {
                    display: none;
                }
            }
        }

        &.opened {
            .actions {
                display: block;
            }
        }
    }

    //*********************************
    // Search autocomplete
    //*********************************
    .search-autocomplete {
        &-product {
            .info {

                .price-block,
                .price-box {

                    .you-save-statement,
                    .savings-label {
                        padding-right: 4px !important;
                        padding-left: 4px !important;
                        .lib-font-size(12) !important;
                        .lib-line-height(11) !important;

                        .you-save-decimal,
                        .savings-label-currency,
                        .savings-label-decimal,
                        .currency-symbol,
                        &-currency,
                        &-decimal {
                            .lib-font-size(10) !important;
                            .lib-line-height(10) !important;
                        }
                    }
                }
            }
        }

        &-dropdown { 
            position: fixed;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            width: 100vw;
            overflow: auto;
            z-index: 50;
            min-height: -webkit-fill-available;

            .close-search-btn {
                margin-bottom: 23px;
            }

            .info {
                .price-box {
                    min-height: 52px;
                }
            }
        }
    }

    //*********************************
    // Instant Search
    //*********************************
    .instant-search {
        &-page {
            .page-title-wrapper {
                .page-title {
                    border-bottom: 1px solid #ccc;
                }
            }
        }

        &-row {
            .lib-vendor-prefix-flex-direction(column);
        }

        &-results {
            width: 100%;
            .lib-vendor-prefix-order(2);

            .product-item-info {
                .price-box .special-price {
                    display: inline-block;
                }
            }

            .page-title,
            .category-short-description { 
                display: none;
            }
        }

        &-sidebar {
            width: 100%;
            .lib-vendor-prefix-order(1);

            .clear-filters {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0;
                margin-bottom: 10px;
                cursor: pointer;
                color: @applyfilters__background-color;
                fill: @applyfilters__background-color;
                &:hover {
                    color: @clearfilters__hover-color;
                    fill: @clearfilters__hover-color;
                }
                span {
                    font-size: 14px;
                    text-transform: capitalize;
                }
            }

            .filter-by-text {
                display: flex;
                align-items: center;
                text-transform: uppercase;
                font-size: 14px;
                margin-bottom: 10px;
            }

            .filters-icon {
               width: 25px;
               height: 25px; 
               margin-bottom: 5px;
               color: inherit;
               fill: inherit;
            }

            .instantsearch-facets-toggle {
                display: block;
                text-align: center;
                border: 2px solid @applyfilters__background-color;
                background-color: 1px solid @products-link-hover__background-color;
                padding: 10px;
                margin-bottom: 20px;
                .lib-css(transition, all 0.2s ease, 1);

                span {
                    font-weight: 700;
                    text-transform: uppercase;
                    color: @applyfilters__background-color;

                    &:before {
                        padding-right: 3px;
                        content: "+";
                    }

                    &.active:before {
                        content: "-";
                    }
                }

                &.active {
                    span {
                        &:before {
                            content: "-";
                        }
                    }
                }
            }

            .filter-list-item {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                margin: 0;
            }

            .filters-list {
                padding-left: 10px;
            }

            .remove-filter-button {
                cursor: pointer;
                margin-bottom: 2px;
            }

            .remove-filter-button::before {
                content: @tt-icon-cross;
                font-family: 'TT Icons';
                font-size: 12px;
                margin-right: 10px;
                color: @clearfilter__button;
            }

            .remove-filter-button:hover::before {
                color: @clearfilter__hover-color
            }
            .remove-filter-button:hover ~ .filter-text {
                opacity: 0.5;
            }

            .instant-search-facets {
                display: block;
                overflow: hidden;
                height: 0;
                .lib-css(opacity, 0, 1);
                .lib-css(transition, all 0.3s ease, 1);
                margin: 0;

                &.active {
                    height: auto;
                    .lib-css(opacity, 1, 1);
                }
            }

            .filter-options {
                .filter-options-content {
                    display: block;
                }
            }

            .filter-apply {
                position: fixed;
                bottom: 30px;
                right: 10px;
                text-transform: capitalize;
                z-index: 100;
                background-color: @applyfilters__background-color;
                color: @autosuggest__background-color;
            }

            .noUi-horizontal {
                max-width: ~"calc(100% - 30px)";
                margin-left: 12px;
            }
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__xs) { 

    //*********************************
    // Search autocomplete
    //*********************************
    .search-autocomplete {
        &-dropdown { 
            .close-search-btn {
                margin-bottom: 17px;
            }
        }
    }
}

// Misc
& when (@media-common =true) {
    body {
        &.search-is-active {
            overflow: hidden;
            position: fixed;
            width: 100%;
        }
    }

    // // Tablets only
    @media screen and (min-width: @screen__m) and (max-width: @screen__l) {

        //*********************************
        // Search miniform
        //*********************************
        .minisearch {
            position: relative;
        }

        .header .block-search .control {
            padding: 0 !important;
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__m) { 

    //*********************************
    // Autocomplete Search
    //*********************************
    body {
        &.search-is-active {
            padding-right: 17px;
        }
    }
    
    .search-autocomplete {
        &-dropdown {
            padding: 14px 15px 15px;
            position: fixed;
            z-index: 10;
            top: 0;
            left: 0;
            width: 100vw;
            max-height: 100vh;
            overflow-y: auto;
            min-height: 50vh;
            box-shadow: 0 10px 25px @color-black-75;

            &::before {
                display: none;
            }

            .close-btn-wrap {
                position: absolute;
                right: 15px;
                top: 15px;
            }

            .search-wrapper {
                width: ~"calc(42.8% - 25px)" !important;
                left: ~"calc(50% + 8.9vw)" !important;
                transform: translateX(-50%);
                position: relative;
            }

            .info {
                .price-box {
                    min-height: 57px;
                }
            }
        }

        &-products {
            padding: 5px 0 0;
        }

        &-wrapper {
            width: ~"calc(42.8% - 25px)";
            left: ~"calc(50% + 8.9vw)";
            transform: translateX(-50%);

            .col-left {
                padding: 0;

                .recently-viewed-list {
                    .recently-viewed-products {
                        .recently-viewd-item {

                            .search-autocomplete-product {
                                width: 33.33%;
                                min-width: 98px;
                    
                                & > a {
                                    padding: 5px;
                                    border: 1px solid @color-white;
                                    .lib-css(border-radius, 2px, 1);
                                }
                    
                                .info {
                                    padding-top: 5px;
                    
                                    >span,
                                    .product-title {
                                        font-size: 10px;
                                        line-height: 12px;
                                        height: 48px;
                                        margin-bottom: 5px;
                                    }
                    
                                    .price-block,
                                    .price-box {
                                        margin-bottom: 5px;
                    
                                        .product-price {
                                            .currency-symbol {
                                                .lib-font-size(10);
                                                top: 0;
                                            }
                    
                                            .price-main {
                                                .lib-font-size(20);
                    
                                                .price-decimal {
                                                    .lib-font-size(10);
                                                    top: 0;
                                                }
                                            }
                                        }
                
                                    }
                    
                                    .price-block {
                                        min-height: 25px;
                                    }

                                    .product-item-actions {
                                        button,
                                        a {
                                            &.todetails,
                                            &.tocart {
                                                border-width: 1px !important;
                                                padding: 4px 5px 3px;
                                                .lib-font-size(10);
                                                .lib-line-height(16);
                                                min-width: 78px;
                                            }
                    
                                            &.todetails {
                                                &::after {
                                                    margin: 0 0 0 3px;
                                                    .lib-font-size(10);
                                                    .lib-line-height(10);
                                                }
                                            }
                                        }
                                    }
                    
                                    .labels-wrapper {
                                        margin-left: auto;
                                        .lib-css(order, 4, 1);
                    
                                        .shipping-label {
                                            .lib-icon-font(@_icon-font-content: "\f0d1",
                                                @_icon-font-margin: -2px 3px 0 0);
                    
                                            padding: 2px 3px 2px;
                                            letter-spacing: normal;
                                            .lib-css(border-radius, 4px, 1);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            .col-right {
                padding: 0; 
            }
        }
    }
    //*********************************
    // Instant Search
    //*********************************
    .instant-search {
        &-page {
            .page-title-wrapper {
                .page-title {
                    margin-bottom: 40px;
                }
            }
        }

        &-no-result {
            .page-title {
                margin-bottom: 40px;
            }
        }

        &-results {
            .page-title {
                font-size: 28px;
                line-height: 42px;
                font-weight: 700;
                margin: 0 0 15px;
                text-align: left;
                text-transform: uppercase;
            }
             
            .category-short-description {
                text-align: left;
                display: block !important;
                margin-bottom: 20px;
            }
        }
    }
}

// Desktops
.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__l) {

    //*********************************
    // Search miniform
    //*********************************
    .minisearch {
        position: relative;
    }

    .header .block-search .control {
        padding: 0 !important;
    }

    //*********************************
    // Autocomplete Search
    //*********************************
    .search-autocomplete {
        &-dropdown {
            padding-top: 22px;
            width: 100%;
            max-width: 100%;

            .search-wrapper {
                width: ~"calc(37vw - 87px)" !important;
                left: 356px !important;
                transform: unset;
            }

            .info {
                .price-box {
                    min-height: unset;
                }
            }
        }

        &-wrapper {
            width: ~"calc(74vw - 174px)" !important;
            left: 356px !important;
            transform: unset;

            .col-left {
                width: 50%;

                .recently-viewed-list {
                    .recently-viewed-products {
                        .recently-viewd-item {

                            .search-autocomplete-product {
                                width: 33.33%;
                                min-width: 98px;
                    
                                & > a {
                                    padding: 5px;
                                    border: 1px solid @color-white;
                                    .lib-css(border-radius, 2px, 1);
                                }
                    
                                .info {
                                    padding-top: 5px;
                    
                                    >span,
                                    .product-title {
                                        font-size: 10px;
                                        line-height: 12px;
                                        height: 48px;
                                        margin-bottom: 5px;
                                    }
                    
                                    .price-block,
                                    .price-box {
                                        margin-bottom: 5px;
                    
                                        .product-price {
                                            .currency-symbol {
                                                .lib-font-size(10);
                                                top: 0;
                                            }
                    
                                            .price-main {
                                                .lib-font-size(20);
                    
                                                .price-decimal {
                                                    .lib-font-size(10);
                                                    top: 0;
                                                }
                                            }
                                        }
                
                                    }
                    
                                    .product-item-actions {
                                        button,
                                        a {
                                            &.todetails,
                                            &.tocart {
                                                border-width: 1px !important;
                                                padding: 4px 5px 3px;
                                                .lib-font-size(10);
                                                .lib-line-height(16);
                                                min-width: 78px;
                                            }
                    
                                            &.todetails {
                                                &::after {
                                                    margin: 0 0 0 3px;
                                                    .lib-font-size(10);
                                                    .lib-line-height(10);
                                                }
                                            }
                                        }
                                    }
                    
                                    .labels-wrapper {
                                        margin-left: auto;
                                        .lib-css(order, 4, 1);
                    
                                        .shipping-label {
                                            .lib-icon-font(@_icon-font-content: "\f0d1",
                                                @_icon-font-margin: -2px 3px 0 0);
                    
                                            padding: 2px 3px 2px;
                                            letter-spacing: normal;
                                            .lib-css(border-radius, 4px, 1);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            &.search-result {
                display: flex;
                width: 200%;
    
                .col-left {
                    width: 50%;
                }

                .col-right {
                    width: 50%;
                    margin-top: -44px;

                    .search-autocomplete-products {
                        & > div:last-child {
                            display: block;
                            overflow: unset;
                            column-count: unset;    
                            height: 40vh;
                            overflow-y: auto;
                        }

                        .search-autocomplete-product {
                            width: 100%;
                            min-width: unset;

                            a {
                                display: flex;
                                flex-direction: row;
                                flex-wrap: wrap;
                                width: 100%;
                                white-space: normal;
                                position: relative;
                                padding: 15px 5px;
                                color: #333;
                                text-decoration: none;
                                text-align: left;
                                border: 0;
                                border-top: 1px solid #ccc;
                            }

                            .thumb {
                                min-width: 50px;
                                height: 50px;
                                padding: 0;
                            }

                            .info {
                                padding: 0 0 0 10px;
                                overflow: hidden;
                                width: ~"calc(100% - 60px)";
                                display: flex;
                                flex-wrap: wrap;
                                box-sizing: border-box;
                                flex-grow: 1;
                                align-items: center;

                                .product-title {
                                    font-size: 16px;
                                    line-height: 1.25;
                                    font-weight: 400;
                                    height: unset;
                                    color: #2e2d76;
                                    text-align: left;
                                    margin: 0;
                                    display: block;
                                }

                                .mpn {
                                    display: block;
                                }

                                .price-block, 
                                .price-box {
                                    margin-bottom: 0;

                                    .product-price {
                                        top: 0;

                                        .currency-symbol {
                                            top: 0px;
                                        }

                                        .price-main {
                                            font-size: 2.6rem;
                                            line-height: 2.6rem;

                                            .price-decimal {
                                                top: 0px;
                                            }
                                        }
                                    }
                                }

                                .product-item-actions {
                                    display: none;
                                }

                                .labels-wrapper {
                                    margin-left: auto;
                                }
                            }
                        }
                    }
                }

                .search-autocomplete-corrections {
                    .lib-font-size(21);
                }
            }

            .col-right {
                width: 50%;
            }
        }

        &-products {
            padding: 0 10px;
            
            h5 {
                display: none;
            }
        }

        &-more {
            margin-top: 0;

            a.button {
                padding: 11px 18px;
            }
        }

        &-error,
        &-no-result {
            padding: 0 10px;

            p {
                .lib-font-size(21);
            }
        }

        &-products>div {
            padding: 0;
            .lib-vendor-prefix-column-count(2);
            .lib-css(justify-content, space-between, 1);
            .lib-css(box-sizing, border-box, 1);
        }

        &-back {
            display: none;
        }

        &-product {
            width: 100%;

            &:first-child {
                border-top: 0 none;
            }
        }
    }

    //*********************************
    // Instant Search
    //*********************************
    .instant-search {
        &-sidebar {
            .instantsearch-facets-toggle {
                display: none;
            }
          
            .filter-list-item {
                display: flex;
                align-items: center;
                flex-wrap: wrap;
                margin: 0;
            }

            .filters-list {
                padding-left: 10px;
            }

            .remove-filter-button {
                cursor: pointer;
                margin-bottom: 2px;
            }

            .remove-filter-button::before {
                content: @tt-icon-cross;
                font-family: 'TT Icons';
                font-size: 12px;
                margin-right: 10px;
                color: @clearfilter__button;
            }

            .remove-filter-button:hover::before {
                color: @clearfilter__hover-color
            }
            .remove-filter-button:hover ~ .filter-text {
                opacity: 0.5;
            }

            .clear-filters {
                display: flex;
                align-items: center;
                padding: 0;
                margin-bottom: 10px;
                width: max-content;
                cursor: pointer;
                color: @applyfilters__background-color;
                fill: @applyfilters__background-color;
                &:hover {
                    color: @clearfilters__hover-color;
                    fill: @clearfilters__hover-color;
                }
                span {
                    font-size: 14px;
                    text-transform: capitalize;
                }
            }

            .filter-by-text {
                display: flex;
                align-items: center;
                text-transform: uppercase;
                font-size: 14px;
                margin-bottom: 10px;
            }

            .filters-icon {
               width: 25px;
               height: 25px; 
               margin-bottom: 5px;
               color: inherit;
               fill: inherit;
            }
        }

        &-results {
            width: 80%;
            .lib-vendor-prefix-order(2);
        }

        &-sidebar {
            width: 20%;
            padding-right: 1.6%;
            text-align: left;
            .lib-vendor-prefix-order(1);
        }
    }
}

.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__ml) {
    //*********************************
    // Autocomplete Search
    //*********************************
    .search-autocomplete {
        &-dropdown {
            .search-wrapper {
                width: 439.22px !important;
                left: ~"calc(50% - 60px)" !important;
                transform: translateX(-50%);
            }
        }
        &-wrapper {
            width: 878.44px !important;
            left: ~"calc(50% + 159px)" !important;
            transform: translateX(-50%);
                
            .col-left {
                .recently-viewed-list {
                    .recently-viewed-products {
                        .recently-viewd-item {
                            .search-autocomplete-product {
                                width: 20%;
                                min-width: 78.5px;

                                & > a {
                                    padding: 4px;
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}