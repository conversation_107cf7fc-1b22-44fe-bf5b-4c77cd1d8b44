import { FILTER_ADD, FILTER_REMOVE, FILTERS_RESET, FILTERS_CLEAR } from '../../constants';

export const resetFitlers = () => {
  return (dispatch) => {
    return new Promise((res, rej) => {
      dispatch({ type: FILTERS_RESET });
      res();
    });
  };
};

export const clearAllFilters = () => {
  return (dispatch) => {
    return new Promise((res, rej) => {
      dispatch({ type: FILTERS_CLEAR });
      res();
    });
  };
};

export const addFilter = (filter) => {
  return (dispatch) => {
    return new Promise((res, rej) => {
      dispatch({ type: FILTER_ADD, filter });
      res();
    });
  };
};

export const removeFilter = (filter) => {
  return (dispatch) => {
    return new Promise((res) => {
      dispatch({ type: FILTER_REMOVE, filter });
      res();
    });
  };
};
