/******/ (function() { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./src/actions/index.js":
/*!******************************!*\
  !*** ./src/actions/index.js ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   clearActiveTerm: function() { return /* binding */ clearActiveTerm; },
/* harmony export */   clearProducts: function() { return /* binding */ clearProducts; },
/* harmony export */   clearSearch: function() { return /* binding */ clearSearch; },
/* harmony export */   correctionProductSearchSuccesss: function() { return /* binding */ correctionProductSearchSuccesss; },
/* harmony export */   fetchCorrectionTermResult: function() { return /* binding */ fetchCorrectionTermResult; },
/* harmony export */   fetchSearchTerms: function() { return /* binding */ fetchSearchTerms; },
/* harmony export */   fetchTermResult: function() { return /* binding */ fetchTermResult; },
/* harmony export */   searchCorrectionError: function() { return /* binding */ searchCorrectionError; },
/* harmony export */   searchCorrectionStarted: function() { return /* binding */ searchCorrectionStarted; },
/* harmony export */   searchQuery: function() { return /* binding */ searchQuery; },
/* harmony export */   searchRequest: function() { return /* binding */ searchRequest; },
/* harmony export */   searchResult: function() { return /* binding */ searchResult; },
/* harmony export */   searchResultError: function() { return /* binding */ searchResultError; },
/* harmony export */   suggestionClick: function() { return /* binding */ suggestionClick; },
/* harmony export */   suggestionFailure: function() { return /* binding */ suggestionFailure; },
/* harmony export */   suggestionRequest: function() { return /* binding */ suggestionRequest; },
/* harmony export */   suggestionSuccess: function() { return /* binding */ suggestionSuccess; },
/* harmony export */   toggleDropdown: function() { return /* binding */ toggleDropdown; },
/* harmony export */   toggleMainLoader: function() { return /* binding */ toggleMainLoader; },
/* harmony export */   updateLSProducts: function() { return /* binding */ updateLSProducts; }
/* harmony export */ });
/* harmony import */ var _constants_api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../constants/api */ "./src/constants/api.js");
/* harmony import */ var _constants_action_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants/action-types */ "./src/constants/action-types.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils */ "./src/utils/index.js");
/* harmony import */ var _utils_common__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/common */ "./src/utils/common.js");
/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! axios */ "./node_modules/axios/lib/axios.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _regeneratorRuntime() { "use strict"; /*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */ _regeneratorRuntime = function _regeneratorRuntime() { return exports; }; var exports = {}, Op = Object.prototype, hasOwn = Op.hasOwnProperty, defineProperty = Object.defineProperty || function (obj, key, desc) { obj[key] = desc.value; }, $Symbol = "function" == typeof Symbol ? Symbol : {}, iteratorSymbol = $Symbol.iterator || "@@iterator", asyncIteratorSymbol = $Symbol.asyncIterator || "@@asyncIterator", toStringTagSymbol = $Symbol.toStringTag || "@@toStringTag"; function define(obj, key, value) { return Object.defineProperty(obj, key, { value: value, enumerable: !0, configurable: !0, writable: !0 }), obj[key]; } try { define({}, ""); } catch (err) { define = function define(obj, key, value) { return obj[key] = value; }; } function wrap(innerFn, outerFn, self, tryLocsList) { var protoGenerator = outerFn && outerFn.prototype instanceof Generator ? outerFn : Generator, generator = Object.create(protoGenerator.prototype), context = new Context(tryLocsList || []); return defineProperty(generator, "_invoke", { value: makeInvokeMethod(innerFn, self, context) }), generator; } function tryCatch(fn, obj, arg) { try { return { type: "normal", arg: fn.call(obj, arg) }; } catch (err) { return { type: "throw", arg: err }; } } exports.wrap = wrap; var ContinueSentinel = {}; function Generator() {} function GeneratorFunction() {} function GeneratorFunctionPrototype() {} var IteratorPrototype = {}; define(IteratorPrototype, iteratorSymbol, function () { return this; }); var getProto = Object.getPrototypeOf, NativeIteratorPrototype = getProto && getProto(getProto(values([]))); NativeIteratorPrototype && NativeIteratorPrototype !== Op && hasOwn.call(NativeIteratorPrototype, iteratorSymbol) && (IteratorPrototype = NativeIteratorPrototype); var Gp = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(IteratorPrototype); function defineIteratorMethods(prototype) { ["next", "throw", "return"].forEach(function (method) { define(prototype, method, function (arg) { return this._invoke(method, arg); }); }); } function AsyncIterator(generator, PromiseImpl) { function invoke(method, arg, resolve, reject) { var record = tryCatch(generator[method], generator, arg); if ("throw" !== record.type) { var result = record.arg, value = result.value; return value && "object" == _typeof(value) && hasOwn.call(value, "__await") ? PromiseImpl.resolve(value.__await).then(function (value) { invoke("next", value, resolve, reject); }, function (err) { invoke("throw", err, resolve, reject); }) : PromiseImpl.resolve(value).then(function (unwrapped) { result.value = unwrapped, resolve(result); }, function (error) { return invoke("throw", error, resolve, reject); }); } reject(record.arg); } var previousPromise; defineProperty(this, "_invoke", { value: function value(method, arg) { function callInvokeWithMethodAndArg() { return new PromiseImpl(function (resolve, reject) { invoke(method, arg, resolve, reject); }); } return previousPromise = previousPromise ? previousPromise.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg(); } }); } function makeInvokeMethod(innerFn, self, context) { var state = "suspendedStart"; return function (method, arg) { if ("executing" === state) throw new Error("Generator is already running"); if ("completed" === state) { if ("throw" === method) throw arg; return doneResult(); } for (context.method = method, context.arg = arg;;) { var delegate = context.delegate; if (delegate) { var delegateResult = maybeInvokeDelegate(delegate, context); if (delegateResult) { if (delegateResult === ContinueSentinel) continue; return delegateResult; } } if ("next" === context.method) context.sent = context._sent = context.arg;else if ("throw" === context.method) { if ("suspendedStart" === state) throw state = "completed", context.arg; context.dispatchException(context.arg); } else "return" === context.method && context.abrupt("return", context.arg); state = "executing"; var record = tryCatch(innerFn, self, context); if ("normal" === record.type) { if (state = context.done ? "completed" : "suspendedYield", record.arg === ContinueSentinel) continue; return { value: record.arg, done: context.done }; } "throw" === record.type && (state = "completed", context.method = "throw", context.arg = record.arg); } }; } function maybeInvokeDelegate(delegate, context) { var methodName = context.method, method = delegate.iterator[methodName]; if (undefined === method) return context.delegate = null, "throw" === methodName && delegate.iterator.return && (context.method = "return", context.arg = undefined, maybeInvokeDelegate(delegate, context), "throw" === context.method) || "return" !== methodName && (context.method = "throw", context.arg = new TypeError("The iterator does not provide a '" + methodName + "' method")), ContinueSentinel; var record = tryCatch(method, delegate.iterator, context.arg); if ("throw" === record.type) return context.method = "throw", context.arg = record.arg, context.delegate = null, ContinueSentinel; var info = record.arg; return info ? info.done ? (context[delegate.resultName] = info.value, context.next = delegate.nextLoc, "return" !== context.method && (context.method = "next", context.arg = undefined), context.delegate = null, ContinueSentinel) : info : (context.method = "throw", context.arg = new TypeError("iterator result is not an object"), context.delegate = null, ContinueSentinel); } function pushTryEntry(locs) { var entry = { tryLoc: locs[0] }; 1 in locs && (entry.catchLoc = locs[1]), 2 in locs && (entry.finallyLoc = locs[2], entry.afterLoc = locs[3]), this.tryEntries.push(entry); } function resetTryEntry(entry) { var record = entry.completion || {}; record.type = "normal", delete record.arg, entry.completion = record; } function Context(tryLocsList) { this.tryEntries = [{ tryLoc: "root" }], tryLocsList.forEach(pushTryEntry, this), this.reset(!0); } function values(iterable) { if (iterable) { var iteratorMethod = iterable[iteratorSymbol]; if (iteratorMethod) return iteratorMethod.call(iterable); if ("function" == typeof iterable.next) return iterable; if (!isNaN(iterable.length)) { var i = -1, next = function next() { for (; ++i < iterable.length;) if (hasOwn.call(iterable, i)) return next.value = iterable[i], next.done = !1, next; return next.value = undefined, next.done = !0, next; }; return next.next = next; } } return { next: doneResult }; } function doneResult() { return { value: undefined, done: !0 }; } return GeneratorFunction.prototype = GeneratorFunctionPrototype, defineProperty(Gp, "constructor", { value: GeneratorFunctionPrototype, configurable: !0 }), defineProperty(GeneratorFunctionPrototype, "constructor", { value: GeneratorFunction, configurable: !0 }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, toStringTagSymbol, "GeneratorFunction"), exports.isGeneratorFunction = function (genFun) { var ctor = "function" == typeof genFun && genFun.constructor; return !!ctor && (ctor === GeneratorFunction || "GeneratorFunction" === (ctor.displayName || ctor.name)); }, exports.mark = function (genFun) { return Object.setPrototypeOf ? Object.setPrototypeOf(genFun, GeneratorFunctionPrototype) : (genFun.__proto__ = GeneratorFunctionPrototype, define(genFun, toStringTagSymbol, "GeneratorFunction")), genFun.prototype = Object.create(Gp), genFun; }, exports.awrap = function (arg) { return { __await: arg }; }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, asyncIteratorSymbol, function () { return this; }), exports.AsyncIterator = AsyncIterator, exports.async = function (innerFn, outerFn, self, tryLocsList, PromiseImpl) { void 0 === PromiseImpl && (PromiseImpl = Promise); var iter = new AsyncIterator(wrap(innerFn, outerFn, self, tryLocsList), PromiseImpl); return exports.isGeneratorFunction(outerFn) ? iter : iter.next().then(function (result) { return result.done ? result.value : iter.next(); }); }, defineIteratorMethods(Gp), define(Gp, toStringTagSymbol, "Generator"), define(Gp, iteratorSymbol, function () { return this; }), define(Gp, "toString", function () { return "[object Generator]"; }), exports.keys = function (val) { var object = Object(val), keys = []; for (var key in object) keys.push(key); return keys.reverse(), function next() { for (; keys.length;) { var key = keys.pop(); if (key in object) return next.value = key, next.done = !1, next; } return next.done = !0, next; }; }, exports.values = values, Context.prototype = { constructor: Context, reset: function reset(skipTempReset) { if (this.prev = 0, this.next = 0, this.sent = this._sent = undefined, this.done = !1, this.delegate = null, this.method = "next", this.arg = undefined, this.tryEntries.forEach(resetTryEntry), !skipTempReset) for (var name in this) "t" === name.charAt(0) && hasOwn.call(this, name) && !isNaN(+name.slice(1)) && (this[name] = undefined); }, stop: function stop() { this.done = !0; var rootRecord = this.tryEntries[0].completion; if ("throw" === rootRecord.type) throw rootRecord.arg; return this.rval; }, dispatchException: function dispatchException(exception) { if (this.done) throw exception; var context = this; function handle(loc, caught) { return record.type = "throw", record.arg = exception, context.next = loc, caught && (context.method = "next", context.arg = undefined), !!caught; } for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i], record = entry.completion; if ("root" === entry.tryLoc) return handle("end"); if (entry.tryLoc <= this.prev) { var hasCatch = hasOwn.call(entry, "catchLoc"), hasFinally = hasOwn.call(entry, "finallyLoc"); if (hasCatch && hasFinally) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } else if (hasCatch) { if (this.prev < entry.catchLoc) return handle(entry.catchLoc, !0); } else { if (!hasFinally) throw new Error("try statement without catch or finally"); if (this.prev < entry.finallyLoc) return handle(entry.finallyLoc); } } } }, abrupt: function abrupt(type, arg) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc <= this.prev && hasOwn.call(entry, "finallyLoc") && this.prev < entry.finallyLoc) { var finallyEntry = entry; break; } } finallyEntry && ("break" === type || "continue" === type) && finallyEntry.tryLoc <= arg && arg <= finallyEntry.finallyLoc && (finallyEntry = null); var record = finallyEntry ? finallyEntry.completion : {}; return record.type = type, record.arg = arg, finallyEntry ? (this.method = "next", this.next = finallyEntry.finallyLoc, ContinueSentinel) : this.complete(record); }, complete: function complete(record, afterLoc) { if ("throw" === record.type) throw record.arg; return "break" === record.type || "continue" === record.type ? this.next = record.arg : "return" === record.type ? (this.rval = this.arg = record.arg, this.method = "return", this.next = "end") : "normal" === record.type && afterLoc && (this.next = afterLoc), ContinueSentinel; }, finish: function finish(finallyLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.finallyLoc === finallyLoc) return this.complete(entry.completion, entry.afterLoc), resetTryEntry(entry), ContinueSentinel; } }, catch: function _catch(tryLoc) { for (var i = this.tryEntries.length - 1; i >= 0; --i) { var entry = this.tryEntries[i]; if (entry.tryLoc === tryLoc) { var record = entry.completion; if ("throw" === record.type) { var thrown = record.arg; resetTryEntry(entry); } return thrown; } } throw new Error("illegal catch attempt"); }, delegateYield: function delegateYield(iterable, resultName, nextLoc) { return this.delegate = { iterator: values(iterable), resultName: resultName, nextLoc: nextLoc }, "next" === this.method && (this.arg = undefined), ContinueSentinel; } }, exports; }
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) { try { var info = gen[key](arg); var value = info.value; } catch (error) { reject(error); return; } if (info.done) { resolve(value); } else { Promise.resolve(value).then(_next, _throw); } }
function _asyncToGenerator(fn) { return function () { var self = this, args = arguments; return new Promise(function (resolve, reject) { var gen = fn.apply(self, args); function _next(value) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "next", value); } function _throw(err) { asyncGeneratorStep(gen, resolve, reject, _next, _throw, "throw", err); } _next(undefined); }); }; }





var CancelToken = axios__WEBPACK_IMPORTED_MODULE_4__["default"].CancelToken;
var cancelSearch;
var cancelTerms;
var searchQuery = function searchQuery(query) {
  return function (dispatch) {
    return new Promise(function (res, rej) {
      dispatch({
        type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_QUERY,
        query: query
      });
      res(query);
    });
  };
};

// export const queryProductsCount = (payload) => ({
//   type: SEARCH_QUERY_PRODUCTS_COUNT,
//   payload,
// });

var clearSearch = function clearSearch() {
  return function (dispatch) {
    return new Promise(function (res, rej) {
      if (cancelSearch) cancelSearch();
      if (cancelTerms) cancelTerms();
      dispatch({
        type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_CLEAR
      });
      res();
    });
  };
};
var clearProducts = function clearProducts(query) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.PRODUCTS_CLEAR,
    query: query
  };
};
var searchRequest = function searchRequest(query) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_REQUEST,
    query: query
  };
};
var searchCorrectionStarted = function searchCorrectionStarted() {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_CORRECTIONS_STARTED
  };
};
var searchCorrectionError = function searchCorrectionError(error) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_CORRECTIONS_ERROR,
    error: error
  };
};
var searchResult = function searchResult(query, terms, payload) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_RESULT,
    query: query,
    terms: terms,
    payload: payload
  };
};
var searchResultError = function searchResultError(error) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_RESULT_ERROR,
    error: error
  };
};
var suggestionClick = function suggestionClick(term) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SUGGEST_CLICK,
    term: term
  };
};
var suggestionRequest = function suggestionRequest(term, initial) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SUGGEST_REQUEST,
    term: term,
    initial: initial
  };
};
var suggestionSuccess = function suggestionSuccess(term, payload) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SUGGEST_SUCCESS,
    term: term,
    payload: payload
  };
};
var clearActiveTerm = function clearActiveTerm() {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.CLEAR_ACTIVE_TERM
  };
};
var correctionProductSearchSuccesss = function correctionProductSearchSuccesss(term, payload) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_CORRECTION_SUCCESS,
    term: term,
    payload: payload
  };
};
var suggestionFailure = function suggestionFailure(error) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SUGGEST_FAILURE,
    error: error
  };
};
var fetchSearchTerms = function fetchSearchTerms(query) {
  return /*#__PURE__*/function () {
    var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee(dispatch) {
      return _regeneratorRuntime().wrap(function _callee$(_context) {
        while (1) switch (_context.prev = _context.next) {
          case 0:
            dispatch(searchRequest(query));
            cancelTerms && cancelTerms();
            axios__WEBPACK_IMPORTED_MODULE_4__["default"].get("".concat(_constants_api__WEBPACK_IMPORTED_MODULE_0__.AUTOCOMPLETE_API_URL, "&q=").concat(query), {
              method: 'GET',
              headers: {
                'Content-Type': 'application/json'
              },
              cancelToken: new CancelToken(function (c) {
                return cancelTerms = c;
              })
            }).then(function (_ref2) {
              var data = _ref2.data;
              var preparedData = (0,_utils_common__WEBPACK_IMPORTED_MODULE_3__.prepareAutoCompleteResults)(data);
              var _ref3 = preparedData || {},
                metaData = _ref3.metaData;
              if (metaData && metaData.statusCode && metaData.statusCode === 400) {
                var msg = metaData.displayMessage || metaData.message;
                dispatch(searchResultError(msg));
              } else {
                var q = query;
                var autoSuggest = parseAutosuggestTerms(preparedData.autoSuggest) || [];
                var spellCorrections = preparedData.spellCorrections || [];
                var inFieldSuggestion = parseTerms(preparedData.inFieldSuggestion) || [];
                var brands = parseBrands(preparedData.inFieldSuggestion || [], [q].concat(_toConsumableArray(spellCorrections), _toConsumableArray(autoSuggest), _toConsumableArray(inFieldSuggestion)));
                var promotedSuggestion = parseTerms(preparedData.promotedSuggestion) || [];
                var terms = [].concat(_toConsumableArray(spellCorrections), _toConsumableArray(promotedSuggestion), _toConsumableArray(autoSuggest), _toConsumableArray(inFieldSuggestion), _toConsumableArray(brands));
                preparedData.spellCorrections = [].concat(_toConsumableArray(spellCorrections), _toConsumableArray(promotedSuggestion), _toConsumableArray(inFieldSuggestion));
                dispatch(searchResult(query, terms, preparedData));
              }
            }).catch(function (error) {
              if (!axios__WEBPACK_IMPORTED_MODULE_4__["default"].isCancel(error)) dispatch(searchResultError('An error occurred during the request.'));
            });
          case 3:
          case "end":
            return _context.stop();
        }
      }, _callee);
    }));
    return function (_x) {
      return _ref.apply(this, arguments);
    };
  }();
};
var fetchCorrectionTermResult = function fetchCorrectionTermResult(term) {
  return function (dispatch) {
    dispatch(searchCorrectionStarted());
    axios__WEBPACK_IMPORTED_MODULE_4__["default"].get("".concat(_constants_api__WEBPACK_IMPORTED_MODULE_0__.SEARCH_API_URL, "&q=").concat(term, "&pageSize=5"), {
      cancelToken: new CancelToken(function (c) {
        return cancelSearch = c;
      })
    }).then(function (_ref4) {
      var data = _ref4.data;
      var preparedData = (0,_utils_common__WEBPACK_IMPORTED_MODULE_3__.prepareAutoCompleteResults)(data) || {};
      dispatch(correctionProductSearchSuccesss(term, preparedData));
    }).catch(function (error) {
      if (!axios__WEBPACK_IMPORTED_MODULE_4__["default"].isCancel(error)) dispatch(searchCorrectionError(error));
    });
  };
};
var fetchTermResult = function fetchTermResult(term) {
  var initial = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
  return function (dispatch) {
    dispatch(suggestionRequest(term, initial));
    cancelSearch && cancelSearch();
    return axios__WEBPACK_IMPORTED_MODULE_4__["default"].get("".concat(_constants_api__WEBPACK_IMPORTED_MODULE_0__.SEARCH_API_URL), {
      method: 'GET',
      cancelToken: new CancelToken(function (c) {
        return cancelSearch = c;
      }),
      params: {
        q: term,
        pageSize: 12
      }
    }).then(function (response) {
      var data = response === null || response === void 0 ? void 0 : response.data;
      var preparedData = (0,_utils_common__WEBPACK_IMPORTED_MODULE_3__.prepareAutoCompleteResults)(data) || {};
      if ((preparedData.products || []).length === 0) {
        (0,_utils__WEBPACK_IMPORTED_MODULE_2__.setTermsOnLocalStorage)(term, false);
      }
      dispatch(suggestionSuccess(term, preparedData));
    }).catch(function (error) {
      (0,_utils__WEBPACK_IMPORTED_MODULE_2__.setTermsOnLocalStorage)(term, false);
      if (!axios__WEBPACK_IMPORTED_MODULE_4__["default"].isCancel(error)) dispatch(suggestionFailure(error));
    });
  };
};

/**
 * @param {Array} arr
 * @returns {Array}
 */
var parseTerms = function parseTerms(arr) {
  return arr === null || arr === void 0 ? void 0 : arr.map(function (term) {
    var _term$autosuggest;
    return term === null || term === void 0 || (_term$autosuggest = term.autosuggest) === null || _term$autosuggest === void 0 ? void 0 : _term$autosuggest.trim();
  });
};
var parseAutosuggestTerms = function parseAutosuggestTerms(arr) {
  return arr === null || arr === void 0 ? void 0 : arr.map(function (term) {
    var _term$autosuggest2;
    return {
      term: term === null || term === void 0 || (_term$autosuggest2 = term.autosuggest) === null || _term$autosuggest2 === void 0 ? void 0 : _term$autosuggest2.trim(),
      product_count: term.result_unbxd_double
    };
  });
};

/**
 * @param {Array} inFields
 * @param {Array} corrections
 * @param {String} query
 * @returns {Array}
 */
var parseBrands = function parseBrands(inFields, terms) {
  var brands = [],
    suffix = terms.length ? terms[0] : '';
  for (var i = 0; i < inFields.length; i++) {
    if ('undefined' !== typeof inFields[i]['brand_in']) {
      var brandIn = inFields[i]['brand_in'].filter(function (bi) {
        var brand = bi.trim();
        return terms.indexOf(brand) !== -1 && brand !== suffix;
      }).map(function (br) {
        return br.trim() + ' ' + suffix;
      });
      brands = [].concat(_toConsumableArray(brands), _toConsumableArray(brandIn));
    }
  }
  return brands;
};
var toggleDropdown = function toggleDropdown(payload) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_DROPDOWN,
    payload: payload
  };
};
var updateLSProducts = function updateLSProducts() {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.UPDATE_LS_PRODUCTS
  };
};
var toggleMainLoader = function toggleMainLoader(loading) {
  return {
    type: _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_MAIN_LOADER,
    payload: {
      loading: loading
    }
  };
};

/***/ }),

/***/ "./src/components/auto-suggest/categories.js":
/*!***************************************************!*\
  !*** ./src/components/auto-suggest/categories.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils */ "./src/utils/index.js");
/* harmony import */ var _actions_index__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../actions/index */ "./src/actions/index.js");
/* harmony import */ var _utils_Spinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/Spinner */ "./src/utils/Spinner.js");
/* harmony import */ var _hintMessage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./hintMessage */ "./src/components/auto-suggest/hintMessage.js");
/* harmony import */ var _productsCount__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./productsCount */ "./src/components/auto-suggest/productsCount.js");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }







var Categories = function Categories(_ref) {
  var categories = _ref.categories,
    isSearchFullType = _ref.isSearchFullType;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
      return state.suggestions;
    }),
    activeTerm = _useSelector.activeTerm,
    isFetching = _useSelector.fetching,
    redirects = _useSelector.redirects;
  var redirect = redirects && redirects[activeTerm] ? redirects[activeTerm] : null;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(),
    _useState2 = _slicedToArray(_useState, 2),
    redirectValue = _useState2[0],
    setRedirectValue = _useState2[1];
  var handleClick = function handleClick(e, term) {
    e.preventDefault();
    var url;
    if (term) url = "/catalogsearch/result/?q=".concat(encodeURIComponent(term));
    dispatch((0,_actions_index__WEBPACK_IMPORTED_MODULE_3__.toggleMainLoader)(true));
    dispatch((0,_actions_index__WEBPACK_IMPORTED_MODULE_3__.suggestionClick)(term));
    (0,_utils__WEBPACK_IMPORTED_MODULE_2__.setTermsOnLocalStorage)(term);
    dispatch((0,_actions_index__WEBPACK_IMPORTED_MODULE_3__.updateLSProducts)());
    window.location.href = url;
  };
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (redirect) setRedirectValue(redirect);
  }, [redirect]);
  var renderCategories = function renderCategories(categories) {
    return categories.map(function (cat, idx) {
      var _cat$term;
      var isActive = activeTerm ? (activeTerm === null || activeTerm === void 0 ? void 0 : activeTerm.toLowerCase()) == (cat === null || cat === void 0 || (_cat$term = cat.term) === null || _cat$term === void 0 ? void 0 : _cat$term.toLowerCase()) : false;
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("li", {
        key: idx,
        className: "categories-list-item"
      }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("a", {
        className: "category-container ".concat(isActive ? 'active' : 'inactive'),
        onClick: function onClick(e) {
          return handleClick(e, cat === null || cat === void 0 ? void 0 : cat.term);
        }
      }, isSearchFullType ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("p", null, cat.term), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_productsCount__WEBPACK_IMPORTED_MODULE_6__["default"], {
        count: cat === null || cat === void 0 ? void 0 : cat.product_count
      }), cat === activeTerm && isFetching && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_utils_Spinner__WEBPACK_IMPORTED_MODULE_4__["default"], {
        style: {
          marginLeft: '10px'
        }
      })) : cat.term));
    });
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-categories suggested-keywords"
  }, isSearchFullType ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_hintMessage__WEBPACK_IMPORTED_MODULE_5__["default"], {
    show: true,
    message: "Continue typing to refine search suggestions & results..."
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h5", null, "suggested keywords")) : null, categories && categories.length ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("ul", {
    className: "categories-list"
  }, renderCategories(categories)) : null));
};
/* harmony default export */ __webpack_exports__["default"] = (Categories);

/***/ }),

/***/ "./src/components/auto-suggest/error.js":
/*!**********************************************!*\
  !*** ./src/components/auto-suggest/error.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var Error = function Error(_ref) {
  var error = _ref.error,
    errorMsg = _ref.errorMsg,
    query = _ref.query;
  var errorHtml = errorMsg && errorMsg.replace("{{%q}}", "<strong>\"".concat(query, "\"</strong>"));
  return error && query ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-error"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("p", {
    dangerouslySetInnerHTML: {
      __html: errorHtml
    }
  })) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (Error);

/***/ }),

/***/ "./src/components/auto-suggest/hintMessage.js":
/*!****************************************************!*\
  !*** ./src/components/auto-suggest/hintMessage.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var HintMessage = function HintMessage(_ref) {
  var show = _ref.show,
    message = _ref.message;
  return show ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("p", null, message)) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (HintMessage);

/***/ }),

/***/ "./src/components/auto-suggest/no-result.js":
/*!**************************************************!*\
  !*** ./src/components/auto-suggest/no-result.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _products__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./products */ "./src/components/auto-suggest/products.js");
/* harmony import */ var _components_auto_suggest_no_result_corrections__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../components/auto-suggest/no-result/corrections */ "./src/components/auto-suggest/no-result/corrections.js");
/* harmony import */ var _utils_HammerSpinner__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils/HammerSpinner */ "./src/utils/HammerSpinner.js");
/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../actions */ "./src/actions/index.js");






var NoResult = function NoResult(_ref) {
  var show = _ref.show,
    activeTerm = _ref.activeTerm;
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
      return state;
    }),
    isSearching = _useSelector.isSearching,
    _useSelector$correcti = _useSelector.corrections,
    products = _useSelector$correcti.products,
    corrections = _useSelector$correcti.terms,
    isLoadingProducts = _useSelector$correcti.isFetchingProducts;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (corrections && corrections[0]) {
      dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_5__.fetchCorrectionTermResult)(corrections[0]));
    }
  }, [corrections === null || corrections === void 0 ? void 0 : corrections.length, dispatch]);
  return show && activeTerm ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-no-result"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("p", null, "No results found for ", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("strong", null, "\"".concat(activeTerm, "\""))), !isSearching ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_no_result_corrections__WEBPACK_IMPORTED_MODULE_3__["default"], null) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    style: {
      marginTop: '10px'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_utils_HammerSpinner__WEBPACK_IMPORTED_MODULE_4__["default"], {
    show: isLoadingProducts
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_products__WEBPACK_IMPORTED_MODULE_2__["default"], {
    products: products
  }))) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (NoResult);

/***/ }),

/***/ "./src/components/auto-suggest/no-result/corrections.js":
/*!**************************************************************!*\
  !*** ./src/components/auto-suggest/no-result/corrections.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../utils */ "./src/utils/index.js");
/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../actions */ "./src/actions/index.js");




var Corrections = function Corrections() {
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
      return state.corrections;
    }),
    corrections = _useSelector.terms,
    redirects = _useSelector.redirects;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  var handleClick = function handleClick(term, ev) {
    ev.preventDefault();
    var capitalizeTerm = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.capitalize)(term);
    var redirect = redirects && redirects[capitalizeTerm] ? redirects[capitalizeTerm] : null;
    var url;
    if (redirect) {
      url = redirect;
    } else {
      url = "/catalogsearch/result/?q=".concat(encodeURIComponent(term));
    }
    dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.toggleMainLoader)(true));
    (0,_utils__WEBPACK_IMPORTED_MODULE_2__.setTermsOnLocalStorage)(term);
    window.location.href = url;
  };
  return corrections && corrections.length ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-corrections"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "ttl"
  }, "Are you looking for:"), corrections === null || corrections === void 0 ? void 0 : corrections.map(function (corr, idx) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
      className: "opt",
      key: idx,
      onClick: function onClick(ev) {
        return handleClick(corr, ev);
      }
    }, (0,_utils__WEBPACK_IMPORTED_MODULE_2__.capitalize)(corr));
  })) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (Corrections);

/***/ }),

/***/ "./src/components/auto-suggest/product/product.img.js":
/*!************************************************************!*\
  !*** ./src/components/auto-suggest/product/product.img.js ***!
  \************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../utils */ "./src/utils/index.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }


var ProdutImage = function ProdutImage(_ref) {
  var data = _ref.data,
    placeholder = _ref.placeholder;
  var getImgUrl = function getImgUrl(data) {
    var attrOrder = ['labeledImage', 'thumbnail', 'smallImage'];
    var extsRegex = /\.(gif|jpe?g|tiff?|png|webp|bmp)$/i;
    var imgUrl;
    attrOrder.forEach(function (attr) {
      if (!imgUrl && data[attr] && extsRegex.test(data[attr])) {
        imgUrl = data[attr];
        return imgUrl;
      }
    });
    if (!imgUrl && data.imageUrl && data.imageUrl.length) {
      imgUrl = data.imageUrl[0];
    }
    return imgUrl;
  };
  var unbxdAmastyLabelTopRight = data.unbxdAmastyLabelTopRight,
    unbxdAmastyLabelTopLeft = data.unbxdAmastyLabelTopLeft,
    unbxdAmastyLabelBottomRight = data.unbxdAmastyLabelBottomRight,
    unbxdAmastyLabelBottomLeft = data.unbxdAmastyLabelBottomLeft;
  var amastyLabels = [{
    label: unbxdAmastyLabelTopLeft,
    position: '0'
  }, {
    label: unbxdAmastyLabelTopRight,
    position: '2'
  }, {
    label: unbxdAmastyLabelBottomLeft,
    position: '6'
  }, {
    label: unbxdAmastyLabelBottomRight,
    position: '8'
  }];
  var imgRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);
  var imgUrl = getImgUrl(data);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "thumb"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("img", {
    src: imgUrl,
    ref: imgRef,
    onError: function onError() {
      if (imgRef.current && imgRef.current.src !== placeholder) {
        imgRef.current.src = placeholder;
      }
    }
  }), amastyLabels.map(function (_ref2) {
    var label = _ref2.label,
      position = _ref2.position;
    if (!label) return null;
    var amastyLabelStyles = (0,_utils__WEBPACK_IMPORTED_MODULE_1__.getAmastyLabelStyles)(position);
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
      key: label,
      className: "amasty_label_image",
      style: _objectSpread(_objectSpread({}, amastyLabelStyles), {}, {
        backgroundImage: "url(".concat(label, ")")
      })
    });
  }));
};
/* harmony default export */ __webpack_exports__["default"] = (ProdutImage);

/***/ }),

/***/ "./src/components/auto-suggest/product/product.js":
/*!********************************************************!*\
  !*** ./src/components/auto-suggest/product/product.js ***!
  \********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _product_price__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./product.price */ "./src/components/auto-suggest/product/product.price.js");
/* harmony import */ var _product_savings__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./product.savings */ "./src/components/auto-suggest/product/product.savings.js");
/* harmony import */ var _product_shipping_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./product.shipping.label */ "./src/components/auto-suggest/product/product.shipping.label.js");
/* harmony import */ var _product_img__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./product.img */ "./src/components/auto-suggest/product/product.img.js");
/* harmony import */ var _product_labels__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./product.labels */ "./src/components/auto-suggest/product/product.labels.js");
/* harmony import */ var _utils_common__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../../utils/common */ "./src/utils/common.js");
/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../../actions */ "./src/actions/index.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }









var Product = function Product(_ref) {
  var _data$notForSale, _data$notForSale2, _data$notForSale3;
  var data = _ref.data,
    position = _ref.position;
  var config = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
    return state.config;
  });
  var query = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
    return state.suggestions.activeTerm || state.query;
  });
  var _getProductPriceAttri = (0,_utils_common__WEBPACK_IMPORTED_MODULE_7__.getProductPriceAttributes)(data),
    finalPrice = _getProductPriceAttri.finalPrice,
    storeSpecialPrice = _getProductPriceAttri.storeSpecialPrice,
    storeOriginalPrice = _getProductPriceAttri.storeOriginalPrice;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  var handleTracking = function handleTracking(url, ev) {
    ev.preventDefault();
    dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_8__.toggleMainLoader)(true));
    if (_typeof(window.AEC) === 'object' && window.AEC.gtm()) {
      window.dataLayer = window.dataLayer || [];
      window.AEC.click(ev.currentTarget, window.dataLayer);
      window.dataLayer.push({
        event: 'unbxdSearchQuery',
        searchQueryPayload: {
          query: query
        }
      });
    }
    window.location = url;
  };
  var formatPrice = function formatPrice() {
    var price = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
    var saving = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var priceStr = price.toFixed(2).toString();
    if (priceStr.indexOf('.') === -1) {
      return price;
    }
    var priceArr = priceStr.split('.');
    if (priceArr.length > 1 && priceArr[1] !== '00') {
      price = "".concat(priceArr[0], "<span class=").concat(saving ? 'you-save-decimal' : 'price-decimal', ">").concat(priceArr[1], "</span>");
    } else {
      price = priceArr[0];
    }
    return price;
  };
  var placeholderUrl = config.placeholderImgUrl || '';
  var originalPrice = data.typeId === 'bundle' && storeSpecialPrice ? finalPrice / (1 - (100 - storeSpecialPrice) / 100) : storeOriginalPrice;
  var typeId = data.sku.endsWith('v') ? 'configurable' : data.typeId;
  var isB2BCustomer = false;
  var mageCacheStorage = localStorage['mage-cache-storage'];
  var isForSale = (data === null || data === void 0 || (_data$notForSale = data.notForSale) === null || _data$notForSale === void 0 ? void 0 : _data$notForSale.toLowerCase()) !== 'true';
  var savings = originalPrice && originalPrice !== finalPrice ? originalPrice - finalPrice : false;
  var showPrice = typeof data.price !== 'undefined' && !isB2BCustomer && (data === null || data === void 0 || (_data$notForSale2 = data.notForSale) === null || _data$notForSale2 === void 0 ? void 0 : _data$notForSale2.toLowerCase()) !== 'true';
  var showSaving = typeId !== 'configurable' && savings && data.showSavedLabel && !isB2BCustomer && (data === null || data === void 0 || (_data$notForSale3 = data.notForSale) === null || _data$notForSale3 === void 0 ? void 0 : _data$notForSale3.toLowerCase()) !== 'true';
  if (typeof mageCacheStorage !== 'undefined') {
    var mageCacheObj = JSON.parse(mageCacheStorage);
    isB2BCustomer = mageCacheObj.company && mageCacheObj.company.has_customer_company;
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-product",
    role: "options",
    id: 'option-' + data.sku
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("a", {
    className: "search-autocomplete-hit",
    href: data.productUrl,
    "data-id": data.sku,
    "data-name": data.title,
    "data-price": data.price,
    "data-position": position,
    "data-brand": data.brand && data.brand.length ? data.brand[0] : '',
    "data-category": 'Search Autosuggest',
    "data-list": 'Search Autosuggest',
    "data-event": 'productClick',
    "data-store": config.storeName || '',
    "data-attributes": '[]',
    onClick: function onClick(ev) {
      return handleTracking(data.productUrl, ev);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_product_img__WEBPACK_IMPORTED_MODULE_5__["default"], {
    data: data,
    placeholder: placeholderUrl
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "info"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    role: "title",
    className: "product-title"
  }, data.title), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "mpn"
  }, "MPN: ", data.partNo), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "price-box"
  }, showPrice && isForSale ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_product_price__WEBPACK_IMPORTED_MODULE_2__["default"], {
    currency: config.storeCurrencySymbol || '$',
    finalPrice: finalPrice,
    format: formatPrice
  }) : null, showSaving && isForSale ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_product_savings__WEBPACK_IMPORTED_MODULE_3__["default"], {
    currency: config.storeCurrencySymbol || '$',
    savings: savings,
    finalPrice: finalPrice,
    originalPrice: originalPrice,
    format: formatPrice,
    type: data.showSavedLabel
  })) : null), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "product-item-actions"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("button", {
    className: "action todetails primary",
    "data-id": data.sku,
    "data-name": data.title,
    "data-price": data.price,
    "data-quantity": 1,
    "data-position": position,
    "data-brand": data.brand && data.brand.length ? data.brand[0] : '',
    "data-category": 'Search Results',
    "data-list": 'Search Results',
    "data-event": 'productClick',
    "data-store": config.storeName || '',
    "data-attributes": '[]',
    onClick: function onClick(e) {
      return handleTracking(data.productUrl, ev);
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", null, "View Details"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "labels-wrapper"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_product_shipping_label__WEBPACK_IMPORTED_MODULE_4__["default"], {
    label: data.shippingLabel
  }), config.showProductLabels ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_product_labels__WEBPACK_IMPORTED_MODULE_6__["default"], {
    top: data.labelTop,
    bottom: data.labelBottom,
    template: config.labelsUrlTemplate
  }) : null))));
};
/* harmony default export */ __webpack_exports__["default"] = (Product);

/***/ }),

/***/ "./src/components/auto-suggest/product/product.labels.js":
/*!***************************************************************!*\
  !*** ./src/components/auto-suggest/product/product.labels.js ***!
  \***************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var ProductLabels = function ProductLabels(_ref) {
  var top = _ref.top,
    bottom = _ref.bottom,
    template = _ref.template;
  var getLabelImg = function getLabelImg(label) {
    var url = template.replace("{{label_type}}", label).replace("{{img_type}}", "thumbnail");
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("img", {
      src: url,
      alt: "Product Label"
    });
  };
  return top || bottom ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "product-labels"
  }, top ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "product-label label_top"
  }, getLabelImg(top)) : null, bottom ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "product-label label_bottom"
  }, getLabelImg(bottom)) : null) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (ProductLabels);

/***/ }),

/***/ "./src/components/auto-suggest/product/product.price.js":
/*!**************************************************************!*\
  !*** ./src/components/auto-suggest/product/product.price.js ***!
  \**************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var Price = function Price(_ref) {
  var finalPrice = _ref.finalPrice,
    currency = _ref.currency,
    format = _ref.format;
  return finalPrice !== "undefined" ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "product-price"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "currency-symbol"
  }, currency), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "price-main",
    dangerouslySetInnerHTML: {
      __html: format(finalPrice, false)
    }
  })) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (Price);

/***/ }),

/***/ "./src/components/auto-suggest/product/product.savings.js":
/*!****************************************************************!*\
  !*** ./src/components/auto-suggest/product/product.savings.js ***!
  \****************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var Savings = function Savings(props) {
  var renderLabel = function renderLabel() {
    var savings = props.savings,
      currency = props.currency,
      format = props.format,
      type = props.type,
      originalPrice = props.originalPrice,
      finalPrice = props.finalPrice;
    return !type.match(/percentage/gi) ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, "Save ", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
      className: "currency-symbol"
    }, currency), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
      className: "save-price wrap",
      dangerouslySetInnerHTML: {
        __html: format(savings, !0)
      }
    })) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, "Save ", Math.round((originalPrice - finalPrice) * 100 / originalPrice), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
      className: "price-decimal"
    }, "%"));
  };
  var savings = props.savings,
    type = props.type;
  if (type.match(/(not|n't)/gi) || savings <= 0 || type.match(/do not/gi)) {
    return null;
  }
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "you-save-statement"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "wrap"
  }, renderLabel(props)));
};
/* harmony default export */ __webpack_exports__["default"] = (Savings);

/***/ }),

/***/ "./src/components/auto-suggest/product/product.shipping.label.js":
/*!***********************************************************************!*\
  !*** ./src/components/auto-suggest/product/product.shipping.label.js ***!
  \***********************************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var ShippingLabel = function ShippingLabel(_ref) {
  var label = _ref.label;
  return label && label.search(/free/gi) !== -1 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "shipping-label"
  }, "Free") : null;
};
/* harmony default export */ __webpack_exports__["default"] = (ShippingLabel);

/***/ }),

/***/ "./src/components/auto-suggest/products.js":
/*!*************************************************!*\
  !*** ./src/components/auto-suggest/products.js ***!
  \*************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../utils */ "./src/utils/index.js");
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../actions */ "./src/actions/index.js");
/* harmony import */ var _product_product__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./product/product */ "./src/components/auto-suggest/product/product.js");





var Products = function Products(_ref) {
  var products = _ref.products,
    activeTerm = _ref.activeTerm,
    redirect = _ref.redirect;
  var formRef = document.getElementById("search_mini_form");
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_2__.useDispatch)();
  var renderProducts = function renderProducts(prods) {
    return prods.map(function (prod, idx) {
      return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_product_product__WEBPACK_IMPORTED_MODULE_4__["default"], {
        key: idx,
        position: idx + 1,
        data: prod
      });
    });
  };
  var handleClick = function handleClick() {
    var url = "/catalogsearch/result/?q=".concat(encodeURIComponent(activeTerm));
    if (redirect) {
      url = redirect;
    }
    dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.toggleMainLoader)(true));
    (0,_utils__WEBPACK_IMPORTED_MODULE_1__.setTermsOnLocalStorage)(activeTerm);
    window.location.href = url;
  };
  return products && products.length > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-products"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h5", null, "search results"), activeTerm && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-more".concat(products.length < 5 ? " hidden" : "")
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("a", {
    className: "button button-primary__outline",
    onClick: handleClick
  }, "View all results for ", /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", null, "\"".concat(activeTerm, "\"")))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", null, renderProducts(products))) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (Products);

/***/ }),

/***/ "./src/components/auto-suggest/productsCount.js":
/*!******************************************************!*\
  !*** ./src/components/auto-suggest/productsCount.js ***!
  \******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var ProductsCount = function ProductsCount(_ref) {
  var count = _ref.count;
  return count > 0 ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "product-count"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", null, count), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", null, count > 1 ? 'products' : 'product')) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (ProductsCount);

/***/ }),

/***/ "./src/components/mainLoader.js":
/*!**************************************!*\
  !*** ./src/components/mainLoader.js ***!
  \**************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   MainLoader: function() { return /* binding */ MainLoader; }
/* harmony export */ });
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _utils_constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../utils/constants */ "./src/utils/constants.js");
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");



var MainLoader = function MainLoader(_ref) {
  var showMainLoaderMask = _ref.showMainLoaderMask;
  var showMainLoader = (0,react_redux__WEBPACK_IMPORTED_MODULE_2__.useSelector)(function (state) {
    return state.showMainLoader;
  });
  return showMainLoader || showMainLoaderMask ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "loading-mask",
    "data-role": "loader"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "loader"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("img", {
    alt: "Loading...",
    src: _utils_constants__WEBPACK_IMPORTED_MODULE_1__.HAMMER_SPINNER_BASE64
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("p", null, "Please wait..."))) : null;
};

/***/ }),

/***/ "./src/components/search-box/search-input.js":
/*!***************************************************!*\
  !*** ./src/components/search-box/search-input.js ***!
  \***************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../utils */ "./src/utils/index.js");
/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../actions */ "./src/actions/index.js");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }




var removeDotsFromQueryStart = function removeDotsFromQueryStart(query) {
  var withoutDotsQuery = query === null || query === void 0 ? void 0 : query.replace(/^\.*/, '');
  return withoutDotsQuery;
};
var magentoSearchInputRef = document.getElementById('search');
var searchForm = document.getElementById('search_mini_form');
var searchButton = searchForm.querySelector('button[type="submit"]');
var SearchInput = function SearchInput(_ref) {
  var isFocused = _ref.isFocused,
    isSearchFullType = _ref.isSearchFullType,
    setShowMainLoaderMask = _ref.setShowMainLoaderMask;
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
      return state;
    }),
    showDropdown = _useSelector.showDropdown,
    isSearching = _useSelector.isSearching,
    _useSelector$suggesti = _useSelector.suggestions,
    activeTerm = _useSelector$suggesti.activeTerm,
    fetching = _useSelector$suggesti.fetching,
    redirects = _useSelector$suggesti.redirects;
  var redirect = redirects && redirects[activeTerm] ? redirects[activeTerm] : null;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  var searchInputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(''),
    _useState2 = _slicedToArray(_useState, 2),
    inputVal = _useState2[0],
    setInputVal = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState4 = _slicedToArray(_useState3, 2),
    submitClicked = _useState4[0],
    setSubmitClicked = _useState4[1];
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var _searchInputRef$curre;
    if (isFocused) searchInputRef === null || searchInputRef === void 0 || (_searchInputRef$curre = searchInputRef.current) === null || _searchInputRef$curre === void 0 ? void 0 : _searchInputRef$curre.focus();
  }, [isFocused]);
  var makeSearchCall = function makeSearchCall(searchText) {
    var searchLabel = document.getElementById('minisearch-label');
    var searchControl = document.getElementById('minisearch-control');
    var rightCol = document.getElementsByClassName('col-right');
    if (searchForm && !searchForm.classList.contains('opened')) {
      var _searchForm$classList, _searchLabel$classLis, _searchControl$classL;
      searchForm === null || searchForm === void 0 || (_searchForm$classList = searchForm.classList) === null || _searchForm$classList === void 0 ? void 0 : _searchForm$classList.add('opened');
      searchLabel === null || searchLabel === void 0 || (_searchLabel$classLis = searchLabel.classList) === null || _searchLabel$classLis === void 0 ? void 0 : _searchLabel$classLis.add('opened');
      searchControl === null || searchControl === void 0 || (_searchControl$classL = searchControl.classList) === null || _searchControl$classL === void 0 ? void 0 : _searchControl$classL.add('opened');
    }
    if (rightCol.length) {
      rightCol[0].style.display = 'none';
    }
    dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.searchQuery)(searchText));
    var query = searchText || '';
    if (!query || query.length < 2) {
      dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.clearSearch)());
    } else {
      dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.clearSearch)()).then(function () {
        return dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.searchQuery)(query));
      }).then(function () {
        return dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.fetchSearchTerms)(query));
      }).then(function () {
        return dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.suggestionClick)(query));
      }).then(function () {
        return dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.fetchTermResult)(query, true));
      });
    }
  };
  var handleInputChange = (0,_utils__WEBPACK_IMPORTED_MODULE_2__.debounce)(function (value) {
    makeSearchCall(value);
  }, 500);
  var handleChange = function handleChange(event) {
    var newValue = event.target.value;
    var withoutDotsValue = removeDotsFromQueryStart(newValue);
    setInputVal(withoutDotsValue);
    handleInputChange(withoutDotsValue);
  };
  var handleClick = function handleClick() {
    var _searchInputRef$curre2;
    searchInputRef === null || searchInputRef === void 0 || (_searchInputRef$curre2 = searchInputRef.current) === null || _searchInputRef$curre2 === void 0 ? void 0 : _searchInputRef$curre2.focus();
  };
  var handleSubmitClick = function handleSubmitClick(ev) {
    ev.preventDefault();
    ev.stopPropagation();
    if (inputVal && (inputVal === null || inputVal === void 0 ? void 0 : inputVal.length) >= 2) {
      setShowMainLoaderMask(true);
      redirectOnSearchPage();
    }
  };
  var redirectOnSearchPage = function redirectOnSearchPage() {
    var term = (magentoSearchInputRef === null || magentoSearchInputRef === void 0 ? void 0 : magentoSearchInputRef.value) || '';
    if (term) {
      var withoutDotsTerm = removeDotsFromQueryStart(term);
      var url = encodeURIComponent(withoutDotsTerm) ? "/catalogsearch/result/?q=".concat(encodeURIComponent(withoutDotsTerm)) : '';
      if (url) {
        (0,_utils__WEBPACK_IMPORTED_MODULE_2__.setTermsOnLocalStorage)(withoutDotsTerm);
        window.location.href = url;
        return;
      }
    }
  };
  var handleInputFocus = function handleInputFocus(e) {
    e.stopPropagation();
    e.preventDefault();
    if (activeTerm && !showDropdown) dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.toggleDropdown)(true));
  };
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    magentoSearchInputRef === null || magentoSearchInputRef === void 0 ? void 0 : magentoSearchInputRef.addEventListener('input', handleChange);
    return function () {
      if (magentoSearchInputRef) {
        magentoSearchInputRef === null || magentoSearchInputRef === void 0 ? void 0 : magentoSearchInputRef.removeEventListener('input', handleChange);
        magentoSearchInputRef.removeEventListener('click', handleInputFocus, false);
        searchForm.removeEventListener('submit', handleSubmitClick);
        searchButton === null || searchButton === void 0 ? void 0 : searchButton.removeEventListener('click', handleSubmitClick);
      }
    };
  }, []);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    magentoSearchInputRef.addEventListener('click', handleInputFocus);
  }, [activeTerm, showDropdown]);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    searchForm === null || searchForm === void 0 ? void 0 : searchForm.addEventListener('submit', handleSubmitClick);
    searchButton === null || searchButton === void 0 ? void 0 : searchButton.addEventListener('click', handleSubmitClick);
  }, [inputVal, isSearching, fetching, activeTerm, redirect]);
  return isSearchFullType ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("input", {
    ref: searchInputRef,
    className: "input-text",
    type: "text",
    name: "search",
    value: inputVal,
    placeholder: "Find your tools...",
    maxLength: "128",
    role: "combobox",
    onChange: handleChange,
    onClick: handleClick
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "actions"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("button", {
    type: "submit",
    className: "action search",
    onClick: function onClick(e) {
      return handleSubmitClick(e);
    }
  }, "search"))) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (SearchInput);

/***/ }),

/***/ "./src/constants/action-types.js":
/*!***************************************!*\
  !*** ./src/constants/action-types.js ***!
  \***************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   CLEAR_ACTIVE_TERM: function() { return /* binding */ CLEAR_ACTIVE_TERM; },
/* harmony export */   PRODUCTS_CLEAR: function() { return /* binding */ PRODUCTS_CLEAR; },
/* harmony export */   SEARCH_CLEAR: function() { return /* binding */ SEARCH_CLEAR; },
/* harmony export */   SEARCH_CORRECTIONS_ERROR: function() { return /* binding */ SEARCH_CORRECTIONS_ERROR; },
/* harmony export */   SEARCH_CORRECTIONS_STARTED: function() { return /* binding */ SEARCH_CORRECTIONS_STARTED; },
/* harmony export */   SEARCH_CORRECTION_SUCCESS: function() { return /* binding */ SEARCH_CORRECTION_SUCCESS; },
/* harmony export */   SEARCH_QUERY: function() { return /* binding */ SEARCH_QUERY; },
/* harmony export */   SEARCH_REQUEST: function() { return /* binding */ SEARCH_REQUEST; },
/* harmony export */   SEARCH_RESULT: function() { return /* binding */ SEARCH_RESULT; },
/* harmony export */   SEARCH_RESULT_ERROR: function() { return /* binding */ SEARCH_RESULT_ERROR; },
/* harmony export */   SUGGEST_CLICK: function() { return /* binding */ SUGGEST_CLICK; },
/* harmony export */   SUGGEST_FAILURE: function() { return /* binding */ SUGGEST_FAILURE; },
/* harmony export */   SUGGEST_REQUEST: function() { return /* binding */ SUGGEST_REQUEST; },
/* harmony export */   SUGGEST_SUCCESS: function() { return /* binding */ SUGGEST_SUCCESS; },
/* harmony export */   TOGGLE_DROPDOWN: function() { return /* binding */ TOGGLE_DROPDOWN; },
/* harmony export */   TOGGLE_MAIN_LOADER: function() { return /* binding */ TOGGLE_MAIN_LOADER; },
/* harmony export */   UPDATE_LS_PRODUCTS: function() { return /* binding */ UPDATE_LS_PRODUCTS; }
/* harmony export */ });
var SEARCH_QUERY = 'SEARCH_QUERY';
var SEARCH_CLEAR = 'SEARCH_CLEAR';
var SEARCH_REQUEST = 'SEARCH_REQUEST';
var SEARCH_RESULT = 'SEARCH_RESULT';
var SEARCH_RESULT_ERROR = 'SEARCH_RESULT_ERROR';
var SEARCH_CORRECTIONS_STARTED = 'SEARCH_CORRECTIONS_STARTED';
var SEARCH_CORRECTION_SUCCESS = 'SEARCH_CORRECTION_SUCCESS';
var SEARCH_CORRECTIONS_ERROR = 'SEARCH_CORRECTIONS_ERROR';
var CLEAR_ACTIVE_TERM = 'CLEAR_ACTIVE_TERM';
var PRODUCTS_CLEAR = 'PRODUCTS_CLEAR';
var SUGGEST_CLICK = 'SUGGEST_CLICK';
var SUGGEST_REQUEST = 'SUGGEST_REQUEST';
var SUGGEST_SUCCESS = 'SUGGEST_SUCCESS';
var SUGGEST_FAILURE = 'SUGGEST_FAILURE';
var TOGGLE_DROPDOWN = 'TOGGLE_DROPDOWN';
var UPDATE_LS_PRODUCTS = 'UPDATE_LS_PRODUCTS';
var TOGGLE_MAIN_LOADER = 'TOGGLE_MAIN_LOADER';
//TODO will remove this as we don't need it.
// export const SEARCH_QUERY_PRODUCTS_COUNT = "SEARCH_QUERY_PRODUCTS_COUNT";

/***/ }),

/***/ "./src/constants/api.js":
/*!******************************!*\
  !*** ./src/constants/api.js ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AUTOCOMPLETE_API_URL: function() { return /* binding */ AUTOCOMPLETE_API_URL; },
/* harmony export */   AUTOSUGGEST_API_URL: function() { return /* binding */ AUTOSUGGEST_API_URL; },
/* harmony export */   ORIGINAL_PRICE_FIELD: function() { return /* binding */ ORIGINAL_PRICE_FIELD; },
/* harmony export */   PRICE_FIELD: function() { return /* binding */ PRICE_FIELD; },
/* harmony export */   SEARCH_API_URL: function() { return /* binding */ SEARCH_API_URL; },
/* harmony export */   SERVER_ERROR_MSG: function() { return /* binding */ SERVER_ERROR_MSG; },
/* harmony export */   SPECIAL_PRICE_FIELD: function() { return /* binding */ SPECIAL_PRICE_FIELD; },
/* harmony export */   STORES_ENUM: function() { return /* binding */ STORES_ENUM; }
/* harmony export */ });
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
var _window, _STORES_ENUM;
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
var unbxdConfig = (_window = window) === null || _window === void 0 ? void 0 : _window.unbxdConfig;
var AUTOCOMPLETE_API_URL = unbxdConfig === null || unbxdConfig === void 0 ? void 0 : unbxdConfig.autoCompleteUrl;
var AUTOSUGGEST_API_URL = unbxdConfig === null || unbxdConfig === void 0 ? void 0 : unbxdConfig.autoSugguestUrl;
var SEARCH_API_URL = unbxdConfig === null || unbxdConfig === void 0 ? void 0 : unbxdConfig.searchUrl;
var SERVER_ERROR_MSG = 'Oops! Something went wrong while searching for {{%q}}. Please try again.';
var STORE_DEFAULT = 'Default Store View';
var STORE_QUEENSLAND = 'Queensland Store View';
var STORE_SOUTH_AUS = 'South Australia Store View';
var STORE_WEST_AUS = 'Western Australia Store View';
var PRICE_FIELD = 'price';
var SPECIAL_PRICE_FIELD = 'specialPrice';
var ORIGINAL_PRICE_FIELD = 'originalPrice';
var STORES_ENUM = (_STORES_ENUM = {}, _defineProperty(_STORES_ENUM, STORE_DEFAULT, 1), _defineProperty(_STORES_ENUM, STORE_QUEENSLAND, 2), _defineProperty(_STORES_ENUM, STORE_SOUTH_AUS, 3), _defineProperty(_STORES_ENUM, STORE_WEST_AUS, 4), _STORES_ENUM);

/***/ }),

/***/ "./src/containers/search.js":
/*!**********************************!*\
  !*** ./src/containers/search.js ***!
  \**********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _search_auto_complete__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./search/auto-complete */ "./src/containers/search/auto-complete.js");


var Search = function Search() {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_search_auto_complete__WEBPACK_IMPORTED_MODULE_1__["default"], null));
};
/* harmony default export */ __webpack_exports__["default"] = (Search);

/***/ }),

/***/ "./src/containers/search/auto-complete.js":
/*!************************************************!*\
  !*** ./src/containers/search/auto-complete.js ***!
  \************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _autoCompleteFullType__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./autoCompleteFullType */ "./src/containers/search/autoCompleteFullType.js");
/* harmony import */ var _autoCompleteSimpleType__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./autoCompleteSimpleType */ "./src/containers/search/autoCompleteSimpleType.js");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }



var AutoComplete = function AutoComplete() {
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState2 = _slicedToArray(_useState, 2),
    isSearchFullType = _useState2[0],
    setIsSearchTypeFull = _useState2[1];
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var body = document.body;
    var checkClass = function checkClass() {
      if (body.classList.contains('search-type-full')) {
        setIsSearchTypeFull(true);
      } else {
        setIsSearchTypeFull(false);
      }
    };
    checkClass();
    var observer = new MutationObserver(checkClass);
    observer.observe(body, {
      attributes: true,
      attributeFilter: ['class']
    });
    return function () {
      observer.disconnect();
    };
  }, []);
  return isSearchFullType ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_autoCompleteFullType__WEBPACK_IMPORTED_MODULE_1__["default"], null) : /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_autoCompleteSimpleType__WEBPACK_IMPORTED_MODULE_2__["default"], null);
};
/* harmony default export */ __webpack_exports__["default"] = (AutoComplete);

/***/ }),

/***/ "./src/containers/search/autoCompleteFullType.js":
/*!*******************************************************!*\
  !*** ./src/containers/search/autoCompleteFullType.js ***!
  \*******************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _components_auto_suggest_categories__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/auto-suggest/categories */ "./src/components/auto-suggest/categories.js");
/* harmony import */ var _utils_HammerSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/HammerSpinner */ "./src/utils/HammerSpinner.js");
/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../actions */ "./src/actions/index.js");
/* harmony import */ var _components_mainLoader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/mainLoader */ "./src/components/mainLoader.js");
/* harmony import */ var _components_auto_suggest_error__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../../components/auto-suggest/error */ "./src/components/auto-suggest/error.js");
/* harmony import */ var _components_auto_suggest_products__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../../components/auto-suggest/products */ "./src/components/auto-suggest/products.js");
/* harmony import */ var _components_auto_suggest_no_result__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../../components/auto-suggest/no-result */ "./src/components/auto-suggest/no-result.js");
/* harmony import */ var _recently_viewed__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./recently-viewed */ "./src/containers/search/recently-viewed.js");
/* harmony import */ var _components_auto_suggest_hintMessage__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../../components/auto-suggest/hintMessage */ "./src/components/auto-suggest/hintMessage.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../../utils */ "./src/utils/index.js");
/* harmony import */ var _search_box__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./search-box */ "./src/containers/search/search-box.js");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }













var searchForm = document.getElementById('search_mini_form');
var AutoComplete = function AutoComplete() {
  var dropDownRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var mainContainer = document.getElementById('minisearch-control');
  var outerMostContainer = document.getElementById('search-main');
  var bodyElement = document.body;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState2 = _slicedToArray(_useState, 2),
    showMainLoaderMask = _useState2[0],
    setShowMainLoaderMask = _useState2[1];
  var _useState3 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState4 = _slicedToArray(_useState3, 2),
    showRightCol = _useState4[0],
    setShowRightCol = _useState4[1];
  var _useState5 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState6 = _slicedToArray(_useState5, 2),
    openDropDown = _useState6[0],
    setOpenDropDown = _useState6[1];
  var _useState7 = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(''),
    _useState8 = _slicedToArray(_useState7, 2),
    redirectUrl = _useState8[0],
    setRedirectUrl = _useState8[1];
  var isDesktop = window.innerWidth > 767;
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
      return state;
    }),
    query = _useSelector.query,
    isSearching = _useSelector.isSearching,
    noResult = _useSelector.noResult,
    error = _useSelector.error,
    errorMsg = _useSelector.errorMsg,
    productTerms = _useSelector.productTerms,
    suggestions_ = _useSelector.suggestions,
    correctionTerms = _useSelector.corrections.terms;
  var isInitialFetch = suggestions_.isInitialFetch,
    isFetching = suggestions_.fetching,
    suggestions = suggestions_.terms,
    activeTerm = suggestions_.activeTerm,
    redirects = suggestions_.redirects,
    products = suggestions_.products;
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    if (redirect) setRedirectUrl(redirect);else setRedirectUrl('');
  }, [activeTerm, Object.keys(redirects).length]);
  var handleFocus = function handleFocus() {
    var _mainContainer$classL, _outerMostContainer$c, _bodyElement$classLis;
    if (!openDropDown) {
      setOpenDropDown(true);
    }
    mainContainer === null || mainContainer === void 0 || (_mainContainer$classL = mainContainer.classList) === null || _mainContainer$classL === void 0 ? void 0 : _mainContainer$classL.add('active');
    outerMostContainer === null || outerMostContainer === void 0 || (_outerMostContainer$c = outerMostContainer.classList) === null || _outerMostContainer$c === void 0 ? void 0 : _outerMostContainer$c.add('active');
    bodyElement === null || bodyElement === void 0 || (_bodyElement$classLis = bodyElement.classList) === null || _bodyElement$classLis === void 0 ? void 0 : _bodyElement$classLis.add('search-is-active');
  };
  var handlePressEnter = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(function (event) {
    console.log('handlePressEnter , Called Focus OUtInput');
    var eventKeyPressed = event === null || event === void 0 ? void 0 : event.keyCode;
    if (eventKeyPressed === 13) {
      var url = "/catalogsearch/result/?q=".concat(encodeURIComponent(event === null || event === void 0 ? void 0 : event.target.value));
      if (redirectUrl) {
        url = redirectUrl;
      }
      if (url) {
        if (isDesktop) {
          var _mainContainer$classL2, _outerMostContainer$c2;
          mainContainer === null || mainContainer === void 0 || (_mainContainer$classL2 = mainContainer.classList) === null || _mainContainer$classL2 === void 0 ? void 0 : _mainContainer$classL2.remove('active');
          outerMostContainer === null || outerMostContainer === void 0 || (_outerMostContainer$c2 = outerMostContainer.classList) === null || _outerMostContainer$c2 === void 0 ? void 0 : _outerMostContainer$c2.remove('active');
        }
        setOpenDropDown(false);
        dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_4__.toggleMainLoader)(true));
        (0,_utils__WEBPACK_IMPORTED_MODULE_11__.setTermsOnLocalStorage)(event === null || event === void 0 ? void 0 : event.target.value);
        window.location.href = url;
      }
    }
  }, [isDesktop, redirectUrl]);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var outerInputElement = document.getElementById('search');
    outerInputElement === null || outerInputElement === void 0 ? void 0 : outerInputElement.addEventListener('focus', handleFocus);
    outerInputElement === null || outerInputElement === void 0 ? void 0 : outerInputElement.addEventListener('keyup', handlePressEnter);
    document.addEventListener('mousedown', handleClick, false);
    return function () {
      if (outerInputElement) {
        outerInputElement === null || outerInputElement === void 0 ? void 0 : outerInputElement.removeEventListener('focus', handleFocus);
        outerInputElement === null || outerInputElement === void 0 ? void 0 : outerInputElement.removeEventListener('keyup', handlePressEnter);
      }
      document.removeEventListener('mousedown', handleClick, false);
    };
  }, []);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var activeProducts = products[activeTerm];
    setShowRightCol(false);
    if (activeTerm && !isFetching) {
      if ((suggestions === null || suggestions === void 0 ? void 0 : suggestions.length) === 1) {
        setShowRightCol(true);
      } else if (activeProducts !== null && activeProducts !== void 0 && activeProducts.length || correctionTerms !== null && correctionTerms !== void 0 && correctionTerms.length) {
        setShowRightCol(true);
      }
    }
  }, [isFetching, products, activeTerm, isInitialFetch, correctionTerms === null || correctionTerms === void 0 ? void 0 : correctionTerms.length, suggestions.length]);
  var handleClick = function handleClick(e) {
    if (searchForm && dropDownRef && dropDownRef.current && !searchForm.contains(e.target) && !dropDownRef.current.contains(e.target)) {
      var _mainContainer$classL3, _outerMostContainer$c3, _bodyElement$classLis2;
      setOpenDropDown(false);
      mainContainer === null || mainContainer === void 0 || (_mainContainer$classL3 = mainContainer.classList) === null || _mainContainer$classL3 === void 0 ? void 0 : _mainContainer$classL3.remove('active');
      outerMostContainer === null || outerMostContainer === void 0 || (_outerMostContainer$c3 = outerMostContainer.classList) === null || _outerMostContainer$c3 === void 0 ? void 0 : _outerMostContainer$c3.remove('active');
      bodyElement === null || bodyElement === void 0 || (_bodyElement$classLis2 = bodyElement.classList) === null || _bodyElement$classLis2 === void 0 ? void 0 : _bodyElement$classLis2.remove('search-is-active');
    }
  };
  var handleCloseBtnClick = function handleCloseBtnClick(event) {
    var _mainContainer$classL4, _outerMostContainer$c4, _bodyElement$classLis3;
    event.preventDefault();
    event.stopPropagation();
    setOpenDropDown(false);
    mainContainer === null || mainContainer === void 0 || (_mainContainer$classL4 = mainContainer.classList) === null || _mainContainer$classL4 === void 0 ? void 0 : _mainContainer$classL4.remove('active');
    outerMostContainer === null || outerMostContainer === void 0 || (_outerMostContainer$c4 = outerMostContainer.classList) === null || _outerMostContainer$c4 === void 0 ? void 0 : _outerMostContainer$c4.remove('active');
    bodyElement === null || bodyElement === void 0 || (_bodyElement$classLis3 = bodyElement.classList) === null || _bodyElement$classLis3 === void 0 ? void 0 : _bodyElement$classLis3.remove('search-is-active');
  };
  var redirect = redirects && redirects[activeTerm] ? redirects[activeTerm] : null;
  var shouldRecentlyItemSHow = !isSearching && !isFetching && !suggestions.length && !query;
  var dropDownOpen = suggestions.length > 0 || (productTerms === null || productTerms === void 0 ? void 0 : productTerms.length) > 0;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "new-search-autocomplete search-autocomplete-dropdown  ".concat(openDropDown ? 'open' : ''),
    ref: dropDownRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "close-btn-wrap"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("button", {
    className: "close-search-btn",
    type: "button",
    onClick: function onClick(e) {
      return handleCloseBtnClick(e);
    }
  }, "close")), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-wrapper"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_search_box__WEBPACK_IMPORTED_MODULE_12__["default"], {
    isFocused: openDropDown,
    isSearchFullType: true
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_mainLoader__WEBPACK_IMPORTED_MODULE_5__.MainLoader, {
    showMainLoaderMask: showMainLoaderMask
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-wrapper  search-result"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "col-left",
    "aria-busy": isSearching
  }, !dropDownOpen && !activeTerm && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-categories suggested-keywords"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_hintMessage__WEBPACK_IMPORTED_MODULE_10__["default"], {
    show: true,
    message: "Start typing to find all tools for all trades"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "searched"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h5", null, "Recently Searched"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("p", null, "No recent search"))), shouldRecentlyItemSHow && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_recently_viewed__WEBPACK_IMPORTED_MODULE_9__["default"], null), suggestions !== null && suggestions !== void 0 && suggestions.length ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_categories__WEBPACK_IMPORTED_MODULE_2__["default"], {
    categories: suggestions,
    isSearchFullType: true
  }) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_utils_HammerSpinner__WEBPACK_IMPORTED_MODULE_3__["default"], {
    show: isSearching
  })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "col-right ".concat(activeTerm ? 'show' : '', " ").concat(products && products[activeTerm] ? 'search-result' : ''),
    "aria-busy": isFetching,
    style: {
      display: showRightCol ? 'block' : 'none'
    }
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
    className: "overlay",
    onClick: function onClick() {
      return dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_4__.clearProducts)());
    }
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_no_result__WEBPACK_IMPORTED_MODULE_8__["default"], {
    show: noResult,
    query: query,
    activeTerm: activeTerm
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_error__WEBPACK_IMPORTED_MODULE_6__["default"], {
    error: error,
    query: query,
    errorMsg: errorMsg
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_products__WEBPACK_IMPORTED_MODULE_7__["default"], {
    products: products[activeTerm],
    activeTerm: activeTerm,
    redirects: redirects,
    redirect: redirect
  }))));
};
/* harmony default export */ __webpack_exports__["default"] = (AutoComplete);

/***/ }),

/***/ "./src/containers/search/autoCompleteSimpleType.js":
/*!*********************************************************!*\
  !*** ./src/containers/search/autoCompleteSimpleType.js ***!
  \*********************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _components_auto_suggest_categories__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/auto-suggest/categories */ "./src/components/auto-suggest/categories.js");
/* harmony import */ var _utils_HammerSpinner__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../utils/HammerSpinner */ "./src/utils/HammerSpinner.js");
/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../actions */ "./src/actions/index.js");
/* harmony import */ var _components_mainLoader__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/mainLoader */ "./src/components/mainLoader.js");
/* harmony import */ var _search_box__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./search-box */ "./src/containers/search/search-box.js");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }







var AutoComplete = function AutoComplete() {
  var autocompleteRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false),
    _useState2 = _slicedToArray(_useState, 2),
    showMainLoaderMask = _useState2[0],
    setShowMainLoaderMask = _useState2[1];
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
      return state;
    }),
    isSearching = _useSelector.isSearching,
    showDropdown = _useSelector.showDropdown,
    suggestions_ = _useSelector.suggestions;
  var suggestions = suggestions_.terms;
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var handleClickOutside = function handleClickOutside(e) {
      var _autocompleteRef$curr;
      var element = e === null || e === void 0 ? void 0 : e.target;
      var isSearchSubmitButtonClicked = (element === null || element === void 0 ? void 0 : element.tagName) === 'BUTTON' && (element === null || element === void 0 ? void 0 : element.type) === 'submit';
      if (autocompleteRef !== null && autocompleteRef !== void 0 && autocompleteRef.current && !(autocompleteRef !== null && autocompleteRef !== void 0 && (_autocompleteRef$curr = autocompleteRef.current) !== null && _autocompleteRef$curr !== void 0 && _autocompleteRef$curr.contains(element)) && !isSearchSubmitButtonClicked && (element === null || element === void 0 ? void 0 : element.id) !== 'search') {
        dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_4__.toggleDropdown)(false));
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return function () {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "new-search-autocomplete",
    ref: autocompleteRef
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_search_box__WEBPACK_IMPORTED_MODULE_6__["default"], {
    setShowMainLoaderMask: setShowMainLoaderMask
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-dropdown ".concat(showDropdown ? 'open' : ''),
    role: "list-box"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_mainLoader__WEBPACK_IMPORTED_MODULE_5__.MainLoader, {
    showMainLoaderMask: showMainLoaderMask
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-wrapper  search-result"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "col-left",
    "aria-busy": isSearching
  }, suggestions !== null && suggestions !== void 0 && suggestions.length ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_categories__WEBPACK_IMPORTED_MODULE_2__["default"], {
    categories: suggestions
  }) : null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_utils_HammerSpinner__WEBPACK_IMPORTED_MODULE_3__["default"], {
    show: isSearching
  })))));
};
/* harmony default export */ __webpack_exports__["default"] = (AutoComplete);

/***/ }),

/***/ "./src/containers/search/recently-viewed.js":
/*!**************************************************!*\
  !*** ./src/containers/search/recently-viewed.js ***!
  \**************************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _components_auto_suggest_product_product_shipping_label__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../components/auto-suggest/product/product.shipping.label */ "./src/components/auto-suggest/product/product.shipping.label.js");
/* harmony import */ var _actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../actions */ "./src/actions/index.js");
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../utils */ "./src/utils/index.js");
/* harmony import */ var _components_auto_suggest_hintMessage__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../../components/auto-suggest/hintMessage */ "./src/components/auto-suggest/hintMessage.js");
function _slicedToArray(arr, i) { return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest(); }
function _nonIterableRest() { throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function _iterableToArrayLimit(arr, i) { var _i = null == arr ? null : "undefined" != typeof Symbol && arr[Symbol.iterator] || arr["@@iterator"]; if (null != _i) { var _s, _e, _x, _r, _arr = [], _n = !0, _d = !1; try { if (_x = (_i = _i.call(arr)).next, 0 === i) { if (Object(_i) !== _i) return; _n = !1; } else for (; !(_n = (_s = _x.call(_i)).done) && (_arr.push(_s.value), _arr.length !== i); _n = !0); } catch (err) { _d = !0, _e = err; } finally { try { if (!_n && null != _i.return && (_r = _i.return(), Object(_r) !== _r)) return; } finally { if (_d) throw _e; } } return _arr; } }
function _arrayWithHoles(arr) { if (Array.isArray(arr)) return arr; }






var RecentlyViewed = function RecentlyViewed() {
  var _useSelector = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useSelector)(function (state) {
      return state;
    }),
    productTerms = _useSelector.productTerms,
    productData = _useSelector.productData,
    recentlyViewedProduct = _useSelector.recentlyViewedProduct,
    showDropdown = _useSelector.showDropdown,
    activeTerm = _useSelector.activeTerm;
  var _useState = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]),
    _useState2 = _slicedToArray(_useState, 2),
    products = _useState2[0],
    setProducts = _useState2[1];
  var inputBoxRef = document.getElementById('search');
  var dispatch = (0,react_redux__WEBPACK_IMPORTED_MODULE_1__.useDispatch)();
  var handleInputClick = function handleInputClick() {
    if (productTerms !== null && productTerms !== void 0 && productTerms.length) {
      dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.toggleDropdown)(!showDropdown));
      var rightDiv = document.querySelector('.col-right');
      if (activeTerm && activeTerm.length > 1) {
        rightDiv.style.display = 'block';
      } else {
        rightDiv.style.display = 'none';
      }
    }
  };
  var handleBoxInput = function handleBoxInput() {
    var rightDiv = document.querySelector('.col-right');
    if (inputBoxRef.value.length > 1) {
      rightDiv.style.display = 'block';
    }
  };
  var handleStorageChange = function handleStorageChange() {
    dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.updateLSProducts)());
  };
  var handleOnLoad = function handleOnLoad(event) {
    if (event.target.readyState === 'complete') {
      dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.updateLSProducts)());
    }
  };
  var onHandleClickDelete = function onHandleClickDelete(i) {
    var newItems = productTerms.filter(function (item) {
      return item !== i;
    });
    (0,_utils__WEBPACK_IMPORTED_MODULE_4__.setLocalStorage)('recently_searched_terms', JSON.stringify(newItems));
    dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.updateLSProducts)());
  };
  var onHandleClickDeleteAll = function onHandleClickDeleteAll() {
    (0,_utils__WEBPACK_IMPORTED_MODULE_4__.setLocalStorage)('recently_searched_terms', JSON.stringify([]));
    dispatch((0,_actions__WEBPACK_IMPORTED_MODULE_3__.updateLSProducts)());
  };
  var handleViewItemClick = function handleViewItemClick(value) {
    var inputDiv = document.querySelector('.col-right');
    setProducts([]);
    inputBoxRef.value = value;
    window.location.href = "/catalogsearch/result/?q=".concat(value);
    inputDiv.style.display = 'none';
  };
  var formatPrice = function formatPrice() {
    var price = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;
    var saving = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;
    var priceStr = price.toFixed(2).toString();
    if (priceStr.indexOf('.') === -1) {
      return price;
    }
    var priceArr = priceStr.split('.');
    if (priceArr.length > 1 && priceArr[1] !== '00') {
      price = "".concat(priceArr[0], "<span class=").concat(saving ? 'you-save-decimal' : 'price-decimal', ">").concat(priceArr[1], "</span>");
    } else {
      price = priceArr[0];
    }
    return price;
  };
  var updateLocalState = function updateLocalState() {
    var RECENTLY_VIEWED_COUNT = 5;
    var recentlyViewedProducts = Object.values(recentlyViewedProduct).filter(function (item) {
      var dataExists = productData.hasOwnProperty(item['product_id']);
      var currentTime = new Date();
      var productTime = new Date(item.added_at * 1000);
      var timeout = 60 * 60 * 1000; // one hour

      return dataExists && item.scope_id === window.checkout.websiteId && currentTime - productTime < timeout;
    }).sort(function (a, b) {
      return b.added_at - a.added_at;
    }).map(function (recent) {
      return productData[recent.product_id];
    });
    if (recentlyViewedProducts.length > RECENTLY_VIEWED_COUNT) {
      recentlyViewedProducts.length = RECENTLY_VIEWED_COUNT;
    }
    setProducts(recentlyViewedProducts);
  };
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var _window, _window$document;
    updateLocalState();
    (_window = window) === null || _window === void 0 ? void 0 : _window.addEventListener('storage', handleStorageChange, false);
    (_window$document = window.document) === null || _window$document === void 0 ? void 0 : _window$document.addEventListener('readystatechange', handleOnLoad, false);
    return function () {
      var _window2, _window$document2;
      (_window2 = window) === null || _window2 === void 0 ? void 0 : _window2.removeEventListener('storage', handleStorageChange, false);
      (_window$document2 = window.document) === null || _window$document2 === void 0 ? void 0 : _window$document2.removeEventListener('readystatechange', handleOnLoad, false);
    };
  }, []);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    var _Object$keys, _Object$keys2;
    var productCounts = Object === null || Object === void 0 || (_Object$keys = Object.keys(recentlyViewedProduct)) === null || _Object$keys === void 0 ? void 0 : _Object$keys.length;
    var prevProductCounts = Object === null || Object === void 0 || (_Object$keys2 = Object.keys(recentlyViewedProduct)) === null || _Object$keys2 === void 0 ? void 0 : _Object$keys2.length;
    if (productCounts !== prevProductCounts) {
      updateLocalState();
    }
  }, [recentlyViewedProduct]);
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    inputBoxRef === null || inputBoxRef === void 0 ? void 0 : inputBoxRef.addEventListener('click', handleInputClick, false);
    inputBoxRef === null || inputBoxRef === void 0 ? void 0 : inputBoxRef.addEventListener('change', handleBoxInput, false);
    return function () {
      inputBoxRef === null || inputBoxRef === void 0 ? void 0 : inputBoxRef.removeEventListener('click', handleInputClick, false);
      inputBoxRef === null || inputBoxRef === void 0 ? void 0 : inputBoxRef.removeEventListener('change', handleBoxInput, false);
    };
  });
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "recently-viewed-list"
  }, productTerms.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "searched-terms"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_hintMessage__WEBPACK_IMPORTED_MODULE_5__["default"], {
    show: true,
    message: "Start typing to find all tools for all trades"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "searched"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h5", null, "Recently Searched"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "clear-all"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("a", {
    onClick: onHandleClickDeleteAll
  }, "Clear All"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "search-autocomplete-categories"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "section"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "section-content"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("ul", {
    className: "categories-list"
  }, productTerms && (productTerms === null || productTerms === void 0 ? void 0 : productTerms.map(function (item) {
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("li", {
      key: item,
      className: "categories-list-item"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("a", {
      onClick: function onClick() {
        return handleViewItemClick(item);
      },
      className: "recently-viewed-label"
    }, item), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("a", {
      className: "recently-viewed-icons-close",
      onClick: function onClick() {
        return onHandleClickDelete(item);
      }
    }));
  }))))))), products.length > 0 && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "recently-viewed-products"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("h5", null, "recently viewed products"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "recently-viewd-item"
  }, products.map(function (product, idx) {
    var _product$extension_at;
    return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      key: idx,
      className: "search-autocomplete-product",
      role: "options",
      id: "options-".concat(product.id)
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("a", {
      className: "search-autocomplete-hit",
      href: product.url,
      "data-id": "product_id",
      "data-name": "product.name",
      "data-price": "product.price_info.final_price",
      "data-position": "",
      "data-brand": "",
      "data-category": "",
      "data-list": "",
      "data-event": "",
      "data-store": "",
      "data-attributes": "[]"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "thumb"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("img", {
      src: product.extension_attributes.labeled_image || product.images[0].url,
      alt: product.name
    })), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "info"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      role: "title",
      className: "product-title"
    }, product.name), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "price-block"
    }, (product === null || product === void 0 || (_product$extension_at = product.extension_attributes) === null || _product$extension_at === void 0 ? void 0 : _product$extension_at.not_for_sale) !== true && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement((react__WEBPACK_IMPORTED_MODULE_0___default().Fragment), null, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
      className: "product-price"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
      className: "currency-symbol"
    }, "$"), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", {
      className: "price-main",
      dangerouslySetInnerHTML: {
        __html: formatPrice(product.price_info.final_price)
      }
    })))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "product-item-actions"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("a", {
      className: "action todetails primary",
      "data-quantity": 1,
      "data-category": 'Search Results',
      "data-list": 'Search Results',
      "data-event": 'productClick',
      "data-attributes": '[]'
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", null, "View Details"))), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
      className: "labels-wrapper"
    }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_auto_suggest_product_product_shipping_label__WEBPACK_IMPORTED_MODULE_2__["default"], {
      label: product.shippingLabel
    })))));
  }))));
};
/* harmony default export */ __webpack_exports__["default"] = (RecentlyViewed);

/***/ }),

/***/ "./src/containers/search/search-box.js":
/*!*********************************************!*\
  !*** ./src/containers/search/search-box.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _components_search_box_search_input__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../components/search-box/search-input */ "./src/components/search-box/search-input.js");


var searchControl = document.getElementById('minisearch-control'),
  searchLabel = document.getElementById('minisearch-label');
var SearchBox = function SearchBox(props) {
  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(function () {
    document.addEventListener('mousedown', toggleMobileSearch, false);
    return function () {
      return document.removeEventListener('mousedown', toggleMobileSearch, false);
    };
  }, []);
  var toggleMobileSearch = function toggleMobileSearch(e) {
    if (searchLabel && e.target.contains(searchLabel)) {
      searchControl && searchControl.classList.toggle('opened');
    }
  };
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_components_search_box_search_input__WEBPACK_IMPORTED_MODULE_1__["default"], props);
};
/* harmony default export */ __webpack_exports__["default"] = (SearchBox);

/***/ }),

/***/ "./src/index.js":
/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var react_dom_client__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom/client */ "./node_modules/react-dom/client.js");
/* harmony import */ var react_redux__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react-redux */ "./node_modules/react-redux/es/index.js");
/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./store */ "./src/store.js");
/* harmony import */ var _containers_search__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./containers/search */ "./src/containers/search.js");





var store = (0,_store__WEBPACK_IMPORTED_MODULE_3__["default"])();
var App = function App() {
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(_containers_search__WEBPACK_IMPORTED_MODULE_4__["default"], null);
};
var searchContainer = react_dom_client__WEBPACK_IMPORTED_MODULE_1__.createRoot(document.getElementById('search-autocomplete-container'));
if (searchContainer) {
  searchContainer.render( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(react_redux__WEBPACK_IMPORTED_MODULE_2__.Provider, {
    store: store
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement(App, null)));
}

/***/ }),

/***/ "./src/reducers/index.js":
/*!*******************************!*\
  !*** ./src/reducers/index.js ***!
  \*******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../utils */ "./src/utils/index.js");
/* harmony import */ var _constants_action_types__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants/action-types */ "./src/constants/action-types.js");
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }


var lsProductsInfo = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getProductsFromLocalStrorage)();
var initialState = _objectSpread({
  showMainLoader: false,
  isSearching: false,
  showDropdown: false,
  noResult: false,
  query: null,
  products: [],
  productsCount: 0,
  suggestions: {
    isInitialFetch: true,
    fetching: false,
    activeTerm: null,
    term: null,
    terms: [],
    products: {},
    redirects: {},
    error: false
  },
  corrections: {
    terms: [],
    products: [],
    isFetchingProducts: false,
    errorMsgFetchingProducts: null,
    redirects: {}
  },
  config: window.unbxdConfig || {},
  error: false,
  errorMsg: null
}, lsProductsInfo);
var rootReducer = function rootReducer() {
  var _state$suggestions, _state$corrections, _state$suggestions2;
  var state = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : initialState;
  var action = arguments.length > 1 ? arguments[1] : undefined;
  var products = {};
  var activeProducts = [];
  var capitalizedActiveTerm = '';
  switch (action.type) {
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_CLEAR:
      var newProductLSValues = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getProductsFromLocalStrorage)();
      return Object.assign({}, _objectSpread(_objectSpread({}, initialState), newProductLSValues));
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_QUERY:
      return Object.assign({}, state, {
        isSearching: false,
        query: action.query,
        error: false,
        errorMsg: null,
        noResult: false
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_REQUEST:
      return Object.assign({}, state, {
        isSearching: true,
        query: action.query,
        showDropdown: true
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.CLEAR_ACTIVE_TERM:
      return Object.assign({}, state, {
        suggestions: _objectSpread(_objectSpread({}, state.suggestions), {}, {
          activeTerm: null
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_RESULT:
      var allTerms = _toConsumableArray(action.terms);
      if ('undefined' !== typeof action.payload.products) {
        var key = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.validateKey)(action.query);
        products[key] = action.payload.products;
      }
      var suggestionsTerms = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.filterDuplicates)([].concat(_toConsumableArray((_state$suggestions = state.suggestions) === null || _state$suggestions === void 0 ? void 0 : _state$suggestions.terms), _toConsumableArray(allTerms)));
      var correctionTerms = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.filterDuplicates)([].concat(_toConsumableArray(state === null || state === void 0 || (_state$corrections = state.corrections) === null || _state$corrections === void 0 ? void 0 : _state$corrections.terms), _toConsumableArray(action.payload.spellCorrections)));
      return Object.assign({}, state, {
        isSearching: false,
        showDropdown: true,
        suggestions: _objectSpread(_objectSpread({}, state.suggestions), {}, {
          terms: suggestionsTerms
        }),
        corrections: _objectSpread(_objectSpread({}, state.corrections), {}, {
          terms: correctionTerms
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_RESULT_ERROR:
      return Object.assign({}, state, {
        isSearching: false,
        error: true,
        noResult: false,
        errorMsg: action.error,
        products: []
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.PRODUCTS_CLEAR:
      return Object.assign({}, state, {
        products: [],
        suggestions: _objectSpread(_objectSpread({}, state.suggestions), {}, {
          activeTerm: null
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SUGGEST_CLICK:
      capitalizedActiveTerm = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.capitalize)(action.term);
      activeProducts = state.suggestions.products[capitalizedActiveTerm] || [];
      return Object.assign({}, state, {
        noResult: !activeProducts.length,
        suggestions: _objectSpread(_objectSpread({}, state.suggestions), {}, {
          isInitialFetch: false,
          activeTerm: capitalizedActiveTerm
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SUGGEST_REQUEST:
      return Object.assign({}, state, {
        noResult: false,
        suggestions: _objectSpread(_objectSpread({}, state.suggestions), {}, {
          isInitialFetch: action.initial,
          fetching: true
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SUGGEST_SUCCESS:
      var d = action.payload,
        redirects = state.suggestions.redirects || {};
      var capitalizedTerm = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.capitalize)(action.term);
      products[capitalizedTerm] = d.products;
      if (d.products.length) {
        products[capitalizedTerm] = d.products;
      }
      var suggestionTerms = [{
        term: capitalizedTerm,
        product_count: d.queryProductsCount
      }].concat(_toConsumableArray(state === null || state === void 0 || (_state$suggestions2 = state.suggestions) === null || _state$suggestions2 === void 0 ? void 0 : _state$suggestions2.terms));
      if (d.redirect) {
        redirects[capitalizedTerm] = d.redirect;
      }
      return Object.assign({}, state, {
        noResult: d.products.length < 1 && !state.suggestions.products[capitalizedTerm],
        suggestions: _objectSpread(_objectSpread({}, state.suggestions), {}, {
          fetching: false,
          terms: suggestionTerms,
          products: _objectSpread(_objectSpread({}, state.suggestions.products), _defineProperty({}, capitalizedTerm, d.products)),
          redirects: redirects
        }),
        corrections: _objectSpread(_objectSpread({}, state.corrections), {}, {
          terms: d.spellCorrections || []
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_CORRECTION_SUCCESS:
      var getCorrectedTerm = action.payload.metaData.queryParams.q;
      var redirectLink = action.payload.redirect;
      var getRedirects = {};
      if (redirectLink) {
        getRedirects[(0,_utils__WEBPACK_IMPORTED_MODULE_0__.capitalize)(getCorrectedTerm)] = redirectLink;
      }
      var correctedProducts = action.payload.products;
      return Object.assign({}, state, {
        corrections: _objectSpread(_objectSpread({}, state.corrections), {}, {
          isFetchingProducts: false,
          products: correctedProducts || {},
          redirects: getRedirects
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_CORRECTIONS_STARTED:
      return Object.assign({}, state, {
        corrections: _objectSpread(_objectSpread({}, state.corrections), {}, {
          isFetchingProducts: true
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SEARCH_CORRECTIONS_ERROR:
      return Object.assign({}, state, {
        corrections: _objectSpread(_objectSpread({}, state.corrections), {}, {
          isFetchingProducts: false,
          errorMsgFetchingProducts: action.error
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.SUGGEST_FAILURE:
      return Object.assign({}, state, {
        showDropdown: true,
        suggestions: _objectSpread(_objectSpread({}, state.suggestions), {}, {
          error: action.error
        })
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_DROPDOWN:
      return Object.assign({}, state, {
        showDropdown: Boolean(action.payload)
      });
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.UPDATE_LS_PRODUCTS:
      var updatedProducts = (0,_utils__WEBPACK_IMPORTED_MODULE_0__.getProductsFromLocalStrorage)();
      return Object.assign({}, state, _objectSpread({}, updatedProducts));
    case _constants_action_types__WEBPACK_IMPORTED_MODULE_1__.TOGGLE_MAIN_LOADER:
      {
        var _action$payload;
        var oldState = _objectSpread({}, state);
        var loading = action === null || action === void 0 || (_action$payload = action.payload) === null || _action$payload === void 0 ? void 0 : _action$payload.loading;
        return _objectSpread(_objectSpread({}, oldState), {}, {
          showMainLoader: loading
        });
      }
    default:
      return state;
  }
};
/* harmony default export */ __webpack_exports__["default"] = (rootReducer);

/***/ }),

/***/ "./src/store.js":
/*!**********************!*\
  !*** ./src/store.js ***!
  \**********************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": function() { return /* binding */ configureStore; }
/* harmony export */ });
/* harmony import */ var redux_thunk__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! redux-thunk */ "./node_modules/redux-thunk/es/index.js");
/* harmony import */ var redux__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! redux */ "./node_modules/redux/es/redux.js");
/* harmony import */ var _reducers_index__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./reducers/index */ "./src/reducers/index.js");



var enableChromeExtention = true;
var composeEnhancers = enableChromeExtention ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || redux__WEBPACK_IMPORTED_MODULE_1__.compose : redux__WEBPACK_IMPORTED_MODULE_1__.compose;
function configureStore(initialState) {
  return (0,redux__WEBPACK_IMPORTED_MODULE_1__.createStore)(_reducers_index__WEBPACK_IMPORTED_MODULE_0__["default"], initialState, composeEnhancers((0,redux__WEBPACK_IMPORTED_MODULE_1__.applyMiddleware)(redux_thunk__WEBPACK_IMPORTED_MODULE_2__["default"])));
}

/***/ }),

/***/ "./src/utils/HammerSpinner.js":
/*!************************************!*\
  !*** ./src/utils/HammerSpinner.js ***!
  \************************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./constants */ "./src/utils/constants.js");


var HammerSpinner = function HammerSpinner(_ref) {
  var show = _ref.show,
    message = _ref.message;
  return show ? /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "instant-search-loader"
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("img", {
    src: _constants__WEBPACK_IMPORTED_MODULE_1__.HAMMER_SPINNER_BASE64,
    alt: "loading"
  }), message && /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("span", null, message)) : null;
};
/* harmony default export */ __webpack_exports__["default"] = (HammerSpinner);

/***/ }),

/***/ "./src/utils/Spinner.js":
/*!******************************!*\
  !*** ./src/utils/Spinner.js ***!
  \******************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ "./node_modules/react/index.js");
/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);

var Spinner = function Spinner(_ref) {
  var style = _ref.style;
  return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "spinner",
    style: style
  }, /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar1"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar2"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar3"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar4"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar5"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar6"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar7"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar8"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar9"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar10"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar11"
  }), /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createElement("div", {
    className: "bar12"
  }));
};
/* harmony default export */ __webpack_exports__["default"] = (Spinner);

/***/ }),

/***/ "./src/utils/common.js":
/*!*****************************!*\
  !*** ./src/utils/common.js ***!
  \*****************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   getProductPriceAttributes: function() { return /* binding */ getProductPriceAttributes; },
/* harmony export */   prepareAutoCompleteResults: function() { return /* binding */ prepareAutoCompleteResults; }
/* harmony export */ });
/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ "./src/utils/constants.js");
/* harmony import */ var _constants_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../constants/api */ "./src/constants/api.js");
function _toConsumableArray(arr) { return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread(); }
function _nonIterableSpread() { throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _iterableToArray(iter) { if (typeof Symbol !== "undefined" && iter[Symbol.iterator] != null || iter["@@iterator"] != null) return Array.from(iter); }
function _arrayWithoutHoles(arr) { if (Array.isArray(arr)) return _arrayLikeToArray(arr); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }


var prepareAutoCompleteResults = function prepareAutoCompleteResults(payload) {
  var _payload$response, _payload$redirect;
  var allProducts = payload === null || payload === void 0 || (_payload$response = payload.response) === null || _payload$response === void 0 ? void 0 : _payload$response.products;
  var metaData = payload === null || payload === void 0 ? void 0 : payload.searchMetaData;
  var spellCorrections = (payload === null || payload === void 0 ? void 0 : payload.didYouMean) && [payload === null || payload === void 0 ? void 0 : payload.didYouMean[0].suggestion];
  var products = allProducts === null || allProducts === void 0 ? void 0 : allProducts.filter(function (prod) {
    return prod.hasOwnProperty('sku') && prod;
  });
  var queryProductsCount = payload.response.numberOfProducts || 0;
  var autoSuggest = allProducts === null || allProducts === void 0 ? void 0 : allProducts.filter(function (prod) {
    return (prod === null || prod === void 0 ? void 0 : prod.doctype) === _constants__WEBPACK_IMPORTED_MODULE_0__.KEYWORD_SUGGESTION && prod;
  });
  var topSearchedQueries = allProducts === null || allProducts === void 0 ? void 0 : allProducts.filter(function (prod) {
    return (prod === null || prod === void 0 ? void 0 : prod.doctype) === _constants__WEBPACK_IMPORTED_MODULE_0__.TOP_SEARCH_QUERIES && prod;
  });
  var inFieldsSuggestion = allProducts === null || allProducts === void 0 ? void 0 : allProducts.filter(function (prod) {
    return (prod === null || prod === void 0 ? void 0 : prod.doctype) === _constants__WEBPACK_IMPORTED_MODULE_0__.IN_FIELD && prod;
  });
  var redirect = payload === null || payload === void 0 || (_payload$redirect = payload.redirect) === null || _payload$redirect === void 0 ? void 0 : _payload$redirect.value;
  return {
    metaData: metaData,
    products: products,
    queryProductsCount: queryProductsCount,
    autoSuggest: [].concat(_toConsumableArray(topSearchedQueries), _toConsumableArray(autoSuggest)),
    inFieldsSuggestion: inFieldsSuggestion,
    spellCorrections: spellCorrections,
    redirect: redirect
  };
};
var getProductPriceAttributes = function getProductPriceAttributes(data) {
  var _window;
  if (!data) return;
  var storeName = (_window = window) === null || _window === void 0 || (_window = _window.unbxdConfig) === null || _window === void 0 ? void 0 : _window.storeName;
  if (!storeName) {
    return {
      finalPrice: data === null || data === void 0 ? void 0 : data.price,
      storeSpecialPrice: data === null || data === void 0 ? void 0 : data.specialPrice,
      storeOriginalPrice: data === null || data === void 0 ? void 0 : data.originalPrice
    };
  }
  var storeId = _constants_api__WEBPACK_IMPORTED_MODULE_1__.STORES_ENUM[storeName];
  var storeField = "Store".concat(storeId);
  var finalPrice = (data === null || data === void 0 ? void 0 : data["".concat(_constants_api__WEBPACK_IMPORTED_MODULE_1__.PRICE_FIELD).concat(storeField)]) || (data === null || data === void 0 ? void 0 : data[_constants_api__WEBPACK_IMPORTED_MODULE_1__.PRICE_FIELD]);
  var storeSpecialPrice = (data === null || data === void 0 ? void 0 : data["".concat(_constants_api__WEBPACK_IMPORTED_MODULE_1__.SPECIAL_PRICE_FIELD).concat(storeField)]) || (data === null || data === void 0 ? void 0 : data[_constants_api__WEBPACK_IMPORTED_MODULE_1__.SPECIAL_PRICE_FIELD]);
  var storeOriginalPrice = (data === null || data === void 0 ? void 0 : data["".concat(_constants_api__WEBPACK_IMPORTED_MODULE_1__.ORIGINAL_PRICE_FIELD).concat(storeField)]) || (data === null || data === void 0 ? void 0 : data[_constants_api__WEBPACK_IMPORTED_MODULE_1__.ORIGINAL_PRICE_FIELD]);
  return {
    finalPrice: finalPrice,
    storeSpecialPrice: storeSpecialPrice,
    storeOriginalPrice: storeOriginalPrice
  };
};

/***/ }),

/***/ "./src/utils/constants.js":
/*!********************************!*\
  !*** ./src/utils/constants.js ***!
  \********************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   HAMMER_SPINNER_BASE64: function() { return /* binding */ HAMMER_SPINNER_BASE64; },
/* harmony export */   IN_FIELD: function() { return /* binding */ IN_FIELD; },
/* harmony export */   KEYWORD_SUGGESTION: function() { return /* binding */ KEYWORD_SUGGESTION; },
/* harmony export */   TOP_SEARCH_QUERIES: function() { return /* binding */ TOP_SEARCH_QUERIES; }
/* harmony export */ });
var HAMMER_SPINNER_BASE64 = 'data:image/gif;base64,R0lGODlhQABAAPAAAAAAAO4xJCH5BA0EAAAAIf8LTkVUU0NBUEUyLjADAQAAACwAAAAAQABAAAAClYSPqcvtD6OctNqLs968+w+G4kiW5ommZcC2LqtC7/zGDo3DtpzrO4/7RXrCRy9QbByTiyOSiXD6mNIWtFoT5g7bXZdL8wYTY9WXPIud0S7zWtFOEaHsMD36vuap++T8bvAHCNDnVzZYWJSodXgn6Lgo1kj3SBlpU6k3CZk2iNfpCRYX+vlEeoqaqrrK2ur6ChsrS1IAACH5BA0EAAAALAAAAABAAEAAAAKQhI+py+0Po5y02ouz3rz7D4biSJbmiabqugWuy17vDMcSjds3/uoRX/M9gEIIseg4IhXA4NLQdCKjsye1WrzSslqsrZuLaQ+81ThRPp2Z6dF6oQR1jWH5dbf9zCdezxtvd7f050Oos8clmEg1aHio2Nj0hNY2SVZniZCXycbp+QkaKjpKWmp6ipqqusraqlIAACH5BA0EAAAALAAAAABAAEAAAAKQhI+py+0Po5y02ouz3rz7D4biSJbmiabqyrZuGcSvFdczVdt3lOv705P9HMHAkBg8NorKBbOZKBqhBulUafXdss8WN7v6ik/islBkTl89aumhu2n3lvOMvI7MVe5JCTzPt+bXBxRo9+fU1uFWOBZC2Mg4ojchSYVHlam5ydnp+QkaKjpKWmp6ipqqusra+lkAACH5BA0EAAAALAAAAABAAEAAAAKFhI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyfAQ2Ldk3/ugB39MBHb4hw/czKpDKZbFZY0IByCS0ah1hn4ktN+gNi8fVHPmMxkbS7Pa6DSe/4/StuY4XzusWtZlVNhV4JUX4ZeTXZKeYiNjoeDglOUlZaXmJmam5ydnp+QkaKtpSAAAh+QQNBAAAACwAAAAAQABAAAACkISPqcvtD6OctNqLs968+w+G4kiW5omm6sq27gvH8kzX9g0E+BPoO9PzXYJCUxBDJJaUlaSz+GFOntSeRyqparGUZHcLPn6t07CZDPGWz2yHOsM2K96duB0NusuNeiqrLwZjdxP2U/Vj4IeoaOiEmEi38/SYE4nDKDm56PioSWlJGSo6SlpqeoqaqrrK2hpRAAAh+QQNBAAAACwAAAAAQABAAAACmYSPqcvtD6OctNqLs968+w+G4kiW5omm6sq27gvHchUEs1Tnt5P3O9ML/hLB4hBQTNp+SiOz6dQsN9DopeapCq/Yj1ZH64K+4ghYRJ5Czugvrtx2P3ymNI9dsi/oKfKe3+d3sNWSZlVoCOeSuKN3I3cEdTSoNGlQaYmUlIk52Rm5afkJejgUyllKioeqlun6ChsrO0tbawtbAAAh+QQNBAAAACwAAAAAQABAAAACmoSPqcvtD6OctNqLs968+w+G4kiW5omm6sp2wRu0FgzLE03bEZ7rDY/z/YA14YJYNCaQL+UR6VQwo0so9cCMXQ3TLaC7BV/FVPKjiTIPVernql1lwbFotnWolRPxef1emtRi1mNDRqhzF2SUpbjIWOfIGPXYp5TlNVd4V7bplCn46fc3OToG5MV1ivrVuHq4ygorO0tba3tLVQAAIfkEDQQAAAAsAAAAAEAAQAAAAqGEj6nL7Q+jnLTai7PevPsPhuJIlmWAmh3KBqrWsm8Wy7NV2/eUpzvVc/14uSGwaIwEk0ok89F7QqLSBrXKuGIV2u1h6U10w4CxF0w2mLfodJu8Zjvdc2syrvYN8bp9zdH3s9byZEZY+MelhxgjFng3d1hF1YgVlGh5uZh5CdcZpikk9zkZKiplesqUqroaCoqHipmW90i3SZuru8vb68tQAAAh+QQNBAAAACwAAAAAQABAAAACqYSPqcvtD6OctNqLs968+w8eQUgCwXmWHsqqHdu6GozKMz3aGZ7rFu/D4IKXIfFHO1aMSgmzGXlCHdJpo2pVYLOILdcA/DK837A4YT53k2o0OZtum9hy8BtOr9+te35e3jf1t9DTRJYiOCiCmBhDyAiF5dhYowVJWbl4GZk3iQdjl8nFo8hJuokZp3Sq6sNKmvpauCrrSVsrBiRaBlqnOesbLDxMXGy8UAAAIfkEDQQAAAAsAAAAAEAAQAAAAqGEj6nL7Q+jnLTai7PevPfghUpAlqAomiWaqif7ubDnvrNW31yu43LvMwGDwuGFZ6wgk5MlM+J8PqLSBrW6uGJHqi3057WCw9kxOaElp8Nrb3v7xsarc3r3zDDjD/o9oL9X9yTIRFh454cAiLe4AoeIRuLWV/RYyec4CQlp97PIUhMqeihaKklqOoqa+jnDGtr5eho7C5t4i5uru8vb69tRAAAh+QQNBAAAACwAAAAAQABAAAACnoSPqcvtD6OctNqLs968+w86wUiGIYmaHpqqHFu6GzzKM21reJ7t/OX7VYLCCbEYgSEtyqWx6UxCo48jdWG9JqZaLLe7/YIP2XEZfNbSAmOv2PyGs9ruFh2RVsfR83vY7geQdzVItccHGHjYtUjYaPgokygYV2PjkzV5ssapoLnJidmXE1o6SmpqWpRayshKp3q3Fkhba3uLm6u7m1EAACH5BA0EAAAALAAAAABAAEAAAAKchI+py+0Po5y02ouz3rz7D4ZiFpTBGJom2qkqu7krTMonfdk3bul8b/sBZcJKsDjxISXHJUTpfDSjjil1Yb0qstoDtJvggr9gL7EcFnfJZXZbrYVf3eMzGiGn5vX2O2AfBejU5yco8rJFiOSSyBhIaJiiM8k3ScdjefmTqYi3Q8M54ym6GarJYtpZmroWWhjpFys7S1tre4ubW1EAACH5BA0EAAAALAAAAABAAEAAAAKnhI+py+0Po5y02ouz3rzzAIbi6GXjiZYXyooq1cbBO8ktLdksHukp//CdgEGhi9gwHpELZYiZdEIZ0inVZ638srUht+v9Qnbi8bYcDaOv6nXi5m6e44YY/WC712V6QF6vA+j09DVImGUIwkSSOAOU0kjzBJlYMkRp6IHpEtmxOVmJkbcTuqLod/kppJLa+mflNdc3S1tre4ubq7vL2+v7CxwsPEwcUQAAIfkEDQQAAAAsAAAAAEAAQAAAAqqEj6nL7Q+jnLTai7PevPsPPsFIBiFYpuOpqW7JUu9sxhE92yL+6g7f8zGALuGQmDIukEllgklyKqAr6RNqbRCzPxq3G/wem2JtsWxWodPkNSLsPuTib3h56wbS9XEeHeC3hyOkBqjG9xFlmNTD1MF4WERV9RgJOVnDIWk5mXgJg0lZGbWJqVPaefpJ5VQTmtlqiub4t3hWO4Wru8vb6/sLHCw8TFxsfAxcAAAh+QQNBAAAACwAAAAAQABAAAACr4SPqcvtD6OctNqLs968+w+G4hYEo1imp5e25pq5MnzJNj3Z+os7+95j/H5BxZBYPByBScNS13Q+XVHlVFW1PrPGJbeL/GrDYsCxPIaizep1Gutmt7/bTilW57Sk8EaeRXQHNnQCRfV2UzhjeLZyw0gIQwVJ5qhCyZSU+IeDGRn0eCXYsyd6SGpiegqq2ofaylMESydaNhXXiJsZJ7fHC/YbLDxMXGx8jJysvMy8UAAAIfkEDQQAAAAsAAAAAEAAQAAAAq2Ej6nL7Q+jnLTai7PevPsPhuJIluaJXkGQbuvLthYMyxVd2xFO6w+P8zGAPGGCSDQakEkjsyl8ApXSILX6Ui6xsZ8JK8mKwBMxqDorZsgqa1qqmTrMADZmulpw859iD7E3ViMXOCJGaDe25ZdIksTVgggnwzhp09MYmQfptGjpxHlVGLXHBzpKiupT2nX6KfqkBRgrewRVO6uGa/u3q0fnGyw8TFxsfIycrCxUAAAh+QQNBAAAACwAAAAAQABAAAACt4SPqcvtD6OctNqLs968+w+G4kiW5omm6sq2rhsEbxXX8gzZNe7oOr/w+S4xkXBIvHmOwqSSw2xais9MFDn9aa5aWhfD/Uqk1nDRiwWbqzkke7I+w22GXTZ+B6QP8kS8f/cl9mfnJEXIVcZUh3gE5ajXSLbVJMlY2KFleUIXiahy1gjzOfqHQwiEB+SZuMp65foa5boWW0trZhsWKzuZ2qq7yHu5x1s8zIrMAKjc7PwMHS09TS1RAAAh+QQNBAAAACwAAAAAQABAAAACtYSPqcvtD6OctNqLs968+w+G4kiW5omm6sq27hIE7xXPtGxTdc6X8S4Can4/ELE4POKSSuHtyGwiMc2odPkkZq7QqMEp4XapuLFODLZI1Whs1hxuuyvcRBoht47h9rZeC6DUIHcX11X3RQhIBqToWBjR+Di5MWkJCXFpWanpyNEJePXRqcD3Sfmi2BPot9q6yioGG4sI+9pziys7m8uDNku7xisKnGhqu1isvMzc7PwMHS09UgAAIfkEDQQAAAAsAAAAAEAAQAAAArKEj6nL7Q+jnLTai7PevPsPhuJIluYZBuinrl3Quhosb3Q9xzgF93vV8/0kwdsQUjQeG0ndctF8OopSnrJ6aDqxAO2WqPV4L94rZgwsmy3ltJrTtr5tyQSVqf6mFWhGXs/2B9iVBycoBPVHd3hHKLjIGNl4JlmJSGlZCZnJaMjZuflZGCoalzP6oEhaF4G6etk6KTaH5Spl+4S7pHvE22ta28flODhsfIycrLzM3Oz8jFMAACH5BA0EAAAALAAAAABAAEAAAAK3hI+py+0Po5y02ouz3rz7D4biSDJBUIZnCq6s574cKs9xfZ06nus7T/H9gBLhjfgw0pAQJTN4fC580qKzOlUusQjtlmvwghPi8aFsDl/N0fTZuzbFbXCho/7t4amN/WZv14f3B8inAEhYONTlp6FoRNaY8UgJ6ViJmVeRWUnHWej5KTlZ9/YZahmJmYiWhHgJNzGKMWhVyqolWwvbGnHLm0sbiGroptZmnKy8zNzs/AwdLT1NrVAAACH5BA0EAAAALAAAAABAAEAAAAK8hI+py+0Po5y02ouz3rz7D4ZiFATjWJ5hqoJs65lwV74zVtt3ldf71dP9IEHh0FE8WnpKks8gaz6S0mkxWl1cjdktNovwghnbcfgVNB+YUG71qk543c15umtn1/N6Jb9/9PfUAMghmENIRXM4KCe2wYjoOKcRCciXYXkHgImjqelpx/lZOJF3RvpFIaqQGvrYCvoKhxRZCWvVCUnrdPpRmsg6pgtGXEwZt8ab3CbJPPkcLT1NXW19jZ2dUAAAIfkEDQQAAAAsAAAAAEAAQAAAAr2Ej6nL7Q+jnLTai7PevPsPhiIWjGNQmiCqrmzrvXCG1vNW2/eV6zvV8/0kQdnwwQoeIzmDcMkoAoxQRZFatV5TWe22m0h+wU6dlFxuTp/daxq9HsPjcvL23L4rs/r6r++3A+h2NIj3Z7g3lKgoyNio9fF4iNATMwlJp6YBeKBXGVjRF3bnWcp5CorJZkHotcpFY9kAi0XCqooJtVo1mWcIxvg7aEdcPAqHPPc5l5a5PNtMaitdbX2Nna2NUAAAIfkEDQQAAAAsAAAAAEAAQAAAArGEj6nL7Q+jnLTai7PevPsPhuJIluaJTsGabmvQXuwbVy9dUzeeRzvfY8x+sKCQRjQufoCdMkEcFp+HKItaLVqxhls3imWCqWDm0yqdGtFIdZDtJcPj67nzbTfX8uMWf5vyB3gi2EdYeOeHCISyyNiIlpWUY9g0GKOHcIn5KJlp2ak4+ZXYA7i5V1gnqITYqrrKFyuLx1pLe2t3hvs6xwWKuhssN/rrGfpLZ7zM3OwcUQAAIfkEDQQAAAAsAAAAAEAAQAAAAqyEj6nL7Q+jnLTai7PevPsPhuJIluaJpurKtlUAuxAc04Hs2Dq+3IBO4ylsP6BPaLwZccFkkejaJZXBKPApPbKmBqd1Sr1uwVWxinyFntHL2Jq9HMPjqzm4bve+87s9v+r3J/fnhidGJmPWpffFwCg0RAcZKTmJ8Gi5qJip2cd5KenJFFeZaAeZN5nKw8cqaNoKu/oV20LoOksLh7RrifZ599lZKHwAWIycnFwAACH5BA0EAAAALAAAAABAAEAAAAKqhI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyGwRGjduIjuZAjrsBbcDSsPY7JovB0HG4ZEKdz2o0qPRUt0jltMPlXrFIUHgrLJLO6Oya7W2e4Flej65m4b+rva/l99f3dTazE2Z4iJaYxsc4VvZ48CSpmFdJWdkoiOnW6Sjp+ckpNwNniCezF+PHugoTCNtKM+sSS/uKS+e6y8uGaqVZqklcbHzsUAAAIfkEDQQAAAAsAAAAAEAAQAAAApyEj6nL7Q+jnLTai7PevPsPhuJIluaJVkGQbuvaZi8cWzOb4HVz6we9W9wUr6BwmAMafz3EbEnsSaFJKZJqsDaxAO0W631yw8VxmMucop3WNVvt7rbjcrhbS69/7/M4nv7n18dnh+YFGLh2aEZmSOYD9Qi5JKn4aNnoCFQYqaTn2Ql5ZRhVlpcldooKisiq+gobKztLW2t7i5trUAAAIfkEDQQAAAAsAAAAAEAAQAAAAo2Ej6nL7Q+jnLTai7PevPsPhuJIluaJpurKtu4axME7yTIN2Tf+6DPv8AF7OkVsaBAmdkDfL8nEOafDKZVnvb6yVhpXy/qCVeLlE1aufpHp5pr9dnPhc5fYub3bvPojXy/XJiWI5QdVBGe2hwRwdoDIaAQZ+ThJ2WhJqXTJ2en5CRoqOkpaanqKmqqKUgAAIfkEDQQAAAAsAAAAAEAAQAAAAoiEj6nL7Q+jnLTai7PevPsPhuJIluaJpurKtu4Lx/JMd0FQO/e+5wnP8yGAPaGBiDMeiMohsHlE/m4xZHBZXFm3TxX3ey2Bx+EQ+UytdJ1otLrtnpYj8LG4bj3h16hg25UFJpPExickBcXUhKiU16gIBZAVSVlpeYmZqbnJ2en5CRoqOkpa2lAAACH5BA0EAAAALAAAAABAAEAAAAJ/hI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTNf2jef6GfT9bvAJfbrhsHIUGYmU5e/jtDiZk2lUas1qrZet9yvEBg7g8jLxbJrXzzCSDXer43DxdJOt3kH7CHeUpHcGBEBFeIiYqLjI2Oj4CBkpOUlZaXmJmam5yYlRAAAh+QQNBAAAACwAAAAAQABAAAACfoSPqcvtD6OctNqLs968+w+G4kiW5omm6sq27gvH8kzX9v0F+o4n+x/oGYDAHpGIOyKFQ6XO6CzSolGKNEPNBhtOq/Z7bVYh4LKZF6ECzmCMksF+a36S+DPGtn2FY+Zer5bUxXfEJHZnmKi4yNjo+AgZKTlJWWl5iZmpuflSAAAh+QQNBAAAACwAAAAAQABAAAAChYSPqcvtD6OctNqLs968+w+G4kiW5omm6sq27gvHQEAHMlTn9q3oPt/z1YANIZHxOwZ1yoSQxoMenjkZ9bp7Ya/aLbXrfYKlhrDSfEQT1c7sim1khbnyubhlj+PzQ9i2OfMFGJg0qGdYCIilSJcm2HQ3SChJWWl5iZmpucnZ6fkJGiqqUQAAIfkEDQQAAAAsAAAAAEAAQAAAAo2Ej6nL7Q+jnLTai7PevPsPhuJIluaJpuoKBO4bsBRMyxMN2zeuz3Uf4QEhwqHhhcDFjAelknl0Fo3SKbBqvWKR0NYS201Uw0kpuWk+e53q9VM9hqflbPr7HMfPj9Dtb+j31xPIBUi4REXYd5fDVIfWlUUmGUYZeWcnWLm3iUnXBhoqOkpaanqKmqoqWgAAIfkEDQQAAAAsAAAAAEAAQAAAApaEj6nL7Q+jnLTai7PevPsPhuJIlmSApoHJqSm7uSisyXTt3tCc2LqjOshWv8ZwWAQeg0nGktlcPHlR6bTqvGKNyC3X57WCw4gjWZw797pqg7ktZMPfc3mbXh+rl3C3ff93hgeoR5jWN0VElkjlxajomCjIuEgZRgeFZfcSmRl3eYhYaMgpOlp5ilrax9rq+gobKzsLWwAAIfkEDQQAAAAsAAAAAEAAQAAAAqGEj6nL7Q+jnLTai7PevPsPAsEYhKZBjieYqqvXlm/lIu1M12iKU6R960liROGQGDQ+kEmlg/lzLqHSI7IaYWKtvC3k6p02w4si+Rk7N8xqBbudeMN347l8LkrjD3c4eJ9X56cHGNgF2NeWqEi4t3j2CNmIp+VIRXk5mKkGFSXZKfPZyZh0SDbpeWrKF6q6ikKaiihoR1srW5iru8vb64tXAAAh+QQNBAAAACwAAAAAQABAAAACqoSPqcvtD6OctNqLs9689xB4YgOCoxcipXlqZbKm7fWq64zVR4zTeizrTYBEYYUINFKQPOWQGXRGmFILsmpNYpfaraTo/XbDkDHZYT4z0moFu21rwhfgufs9r9vjtz0MD6fnZwAYKDdIeIgo6Me452gHmVeoJmmo2GhZebUINQgV1QbKMgkaOUq6qYUZZtYn+rqTqjoLoAMbm/iYi2jL23vbKzxMXGx8jFAAACH5BA0EAAAALAAAAABAAEAAAAKqhI+py+0Po5y02ouz3rz7DzJBEpRhV45Hqp4YS6buKyPwbN1rjVNs+2v1IsHi0Ff8HSdJ5VLSND2ZySkyaL3qskQsF+r9QozicbjcOKNFzjW77VaQ43I13TC/77Z6PLyfB2h3F6hXSPgnyGc4SHfo2Bj3KJnIuIhYCRmJ1tQHECXkBiolOkrKaRoqlqr6dZaZlXjJJXu6tshTavvZimq7S+kpPExcbHycUQAAIfkEDQQAAAAsAAAAAEAAQAAAAqeEj6nL7Q+jnLTai7PevPsPhqIVlGNYmoh6Xmmwsi31JvVMp7aOTy98uPUiv6Js6DAWkUSlkNlw8qAPKZCadGKryi3E6G3+wtwxOWs+L7rq9bKtYMNj6XnwbTeA8/e6XZ63xwf4hxcoeOhXqAiHmPi0yNjmOEfYSKkm9WgZZnXU6fm5FSqKBYZpCilJ5bfKOqUHCXokSwYLcDsZVxrI5/sLHCw8TMxRAAAh+QQNBAAAACwAAAAAQABAAAACooSPqcvtD6OctNqLs968+w+G4kiWphakJ5eqK9oGifxOMX249XPP+t644QAxoENI6xkZSOSS2VQ+fdHfNFe1XrPabfW6iIKhwjG5bKai04gmW118Y51yortuX8vvdf7ezyaGBxhI12f4h1iomCbYmPXGNQQmuQj5WGkm2bUkxfgU1xZK2aIgNVYqmvpoOoqXNzm4+mrASXuLm6u7y9vr+4tQAAAh+QQNBAAAACwAAAAAQABAAAACjISPqcvtD6OctNqLs968+w+G4kiW5omm6sq2VQDHrhTX8PzYOr7oPp/wCYEH4Y9oPPKSOyCzuXzKnNIbtYqsBmZaG7drxXWJACkZkTwrhuqiAaquleXt+ZQex9vz0zpa72cXFii4RVh4WGgYqFTH5vfo2NgWSTkpCcjYlziY6PkJGio6SlpqeoqaGloAACH5BA0EAAAALAAAAABAAEAAAAJ+hI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTG/Bjd91kvf67gsGasIgrWiUIYWwJfPlfJ6i0RS1ar1Ks9qeq4v7ArpQXfHoG3tnz3UsiQg34bwf60xE7tR4YN+/tRco6LYXJ2eYqLjI2Oj4CBkpOUlZaXmJmam5qVIAACH5BA0EAAAALAAAAABAAEAAAAJ7hI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTNf2jed6GfT+j/sJgbSh0ZdCJo7MHqoJNT6jVOepWp1ioaEhYMvdRL/gI6QsRXsf6sCi7Y5g5cqGtdK01EVmzH20tyM4SFhoeIiYqLjI2Oj4CBkpOUlZaXmJKVgAACH5BA0EAAAALAAAAABAAEAAAAJ7hI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTNf2jef6zvdvACwBh0EM0UEMVJLM5fHAjCoh0mpRYc0mH9qu0ttFgq2AsZY6FEfLZvKp7TbBq6i5tJ6GwvFTLNgWlrPmQ1hoeIiYqLjI2Oj4CBkpOUlZaXmJqVIAACH5BA0EAAAALAAAAABAAEAAAAJ/hI+py+0Po5y02ouz3rz7D4biSJbmiabqyrbuC8fyTNf2EwS3k/f5nvD5gAfhkGgUIpPKHbOJez11jJ7sGbTSsADojNk13oZTcKw8laLTqzW67S6r4m+UNkw/uvJ3Nf37B7g2VmcjR2TghbjI2Oj4CBkpOUlZaXmJmam5yalQAAAh+QQNBAAAACwAAAAAQABAAAACioSPqcvtD6OctNqLs968+w+G4kiW5omm6sq2rhEE7xXH81Tn95Pru9Or/YBB2ZBRNB4VyWWj6ET2olIfNTG9YrPaA9erPAbBtmjW6tR90zJot3lNon9y964un+Hxr3196DfXF1h2Rximd0inuBgo5vi4x8Znlhdn9ybUtcnZ6fkJGio6SlpqekpSAAAh+QQNBAAAACwAAAAAQABAAAACk4SPqcvtD6OctNqLs968+w+G4kiWYWCCwYqmG8u6GhzLF73aGN7qFO+zAIMVGlEIOxaTyl+tKWFCIcYp9Wl1SLONLXfh/SbC4sM2VwY8seIcLm1+w3m9Nj1NZ0/z5Ca/r/SnByVYZ1VoSFj4hcjVmIWYGPi4R1m5eGipKMjI2ZmHBxoqB6c2WIqaqrrK2ur6CqtUAAAh+QQNBAAAACwAAAAAQABAAAACmoSPqcvtD6OctNqLs968+98FIkga4lmSJ5p6K9tybxCHby3P+Kbv2u37wYKYFVFIOxaHSgqzKTFCK8/pQ2qNjrLaLRdS/Sqw4ka4fCCjx17vGrAFvk299+w8vePzere47/fXN0d3RwinRwioOGi3uAYYmBUpaUUJeVlGmSSY+bXJ+ekpGon5qFlqmuhoyCh3qHYoO0tba3tbVgAAIfkEDQQAAAAsAAAAAEAAQAAAAp2Ej6nL7Q+jnLTai7PeKPjAhc73iWZCeucKpCBrui8curRo31yub2lfKwF9wmGGZCTOkpci0+J8UpBSqKpaiWIh2u3o6pV0w4sxGQU+N8xqA7XNYMM7y3mLp5a9z3q5tJ+W12d3J0NY+EOod4i4B7eoCDk3OAlYadkGGBimWcfZmQkqqBlKWko5ispnemr4iPnqaid76Md4i5urm1QAACH5BA0EAAAALAAAAABAAEAAAAKahI+py+0Po5y02ouz3i54z4XdF4imQpbnCqTsmYKv6M40ad9fHuJ8L/ttdsJhsIghIpPHZUXpfDajEii1Or0+stpRl8L9osJihLW8OKMT6vWh7Y4zYrU1vV6++9B68lUvZ3AX2BJDWIgXR0cIKNeoOAgZ6danYtdHWZmJecnJp9n5mAf66TlqKlZpWboo2eq655h4SFtre4tWAAAh+QQNBAAAACwAAAAAQABAAAACnISPqcvtD6OctNqLs941BA5G3hiWyUiaKuqpJvu5JSzPbB3SOHfvfOrboIK/GFEDPF6GSkyy2WlBLcwp5WkVSbMTLLfh/TLCYgW5fKiix9s12+g2nN0weBxQn1vz+infjvZ3x3cnV1doqDN4WEjYmPcIGfcHKEY5eUmXuUZZmdXpudepuVkGSurIWWoquCiJyYioiEhba3uLm+tQAAAh+QQNBAAAACwAAAAAQABAAAACl4SPqcvtD6OctNqLs968+w+GoheUY1imwUmqK7u5L6zJdG3fWK5fbr9TAYOmoUVoPBaTFCRzkno2o9KIs/qgYrPabaPrXVzDiTEZAQ5r094fYHZ+u+MH2TJuZ2Pz+ie/n/R3t8eHV3gmiPinuKgmCFf1CBn56FjZJmnZuCU56ZeJmaiZx3hItllqdqpK1+r6ChsrO0vLUQAAOw==';
var KEYWORD_SUGGESTION = 'KEYWORD_SUGGESTION';
var IN_FIELD = 'IN_FIELD';
var TOP_SEARCH_QUERIES = 'TOP_SEARCH_QUERIES';

/***/ }),

/***/ "./src/utils/index.js":
/*!****************************!*\
  !*** ./src/utils/index.js ***!
  \****************************/
/***/ (function(__unused_webpack_module, __webpack_exports__, __webpack_require__) {

__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   capitalize: function() { return /* binding */ capitalize; },
/* harmony export */   debounce: function() { return /* binding */ debounce; },
/* harmony export */   filterDuplicates: function() { return /* binding */ filterDuplicates; },
/* harmony export */   getAmastyLabelStyles: function() { return /* binding */ getAmastyLabelStyles; },
/* harmony export */   getProductsFromLocalStrorage: function() { return /* binding */ getProductsFromLocalStrorage; },
/* harmony export */   isJSON: function() { return /* binding */ isJSON; },
/* harmony export */   isMobile: function() { return /* binding */ isMobile; },
/* harmony export */   setLocalStorage: function() { return /* binding */ setLocalStorage; },
/* harmony export */   setTermsOnLocalStorage: function() { return /* binding */ setTermsOnLocalStorage; },
/* harmony export */   validateKey: function() { return /* binding */ validateKey; }
/* harmony export */ });
function _typeof(obj) { "@babel/helpers - typeof"; return _typeof = "function" == typeof Symbol && "symbol" == typeof Symbol.iterator ? function (obj) { return typeof obj; } : function (obj) { return obj && "function" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? "symbol" : typeof obj; }, _typeof(obj); }
function ownKeys(object, enumerableOnly) { var keys = Object.keys(object); if (Object.getOwnPropertySymbols) { var symbols = Object.getOwnPropertySymbols(object); enumerableOnly && (symbols = symbols.filter(function (sym) { return Object.getOwnPropertyDescriptor(object, sym).enumerable; })), keys.push.apply(keys, symbols); } return keys; }
function _objectSpread(target) { for (var i = 1; i < arguments.length; i++) { var source = null != arguments[i] ? arguments[i] : {}; i % 2 ? ownKeys(Object(source), !0).forEach(function (key) { _defineProperty(target, key, source[key]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(target, Object.getOwnPropertyDescriptors(source)) : ownKeys(Object(source)).forEach(function (key) { Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key)); }); } return target; }
function _defineProperty(obj, key, value) { key = _toPropertyKey(key); if (key in obj) { Object.defineProperty(obj, key, { value: value, enumerable: true, configurable: true, writable: true }); } else { obj[key] = value; } return obj; }
function _toPropertyKey(arg) { var key = _toPrimitive(arg, "string"); return _typeof(key) === "symbol" ? key : String(key); }
function _toPrimitive(input, hint) { if (_typeof(input) !== "object" || input === null) return input; var prim = input[Symbol.toPrimitive]; if (prim !== undefined) { var res = prim.call(input, hint || "default"); if (_typeof(res) !== "object") return res; throw new TypeError("@@toPrimitive must return a primitive value."); } return (hint === "string" ? String : Number)(input); }
function _createForOfIteratorHelper(o, allowArrayLike) { var it = typeof Symbol !== "undefined" && o[Symbol.iterator] || o["@@iterator"]; if (!it) { if (Array.isArray(o) || (it = _unsupportedIterableToArray(o)) || allowArrayLike && o && typeof o.length === "number") { if (it) o = it; var i = 0; var F = function F() {}; return { s: F, n: function n() { if (i >= o.length) return { done: true }; return { done: false, value: o[i++] }; }, e: function e(_e) { throw _e; }, f: F }; } throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method."); } var normalCompletion = true, didErr = false, err; return { s: function s() { it = it.call(o); }, n: function n() { var step = it.next(); normalCompletion = step.done; return step; }, e: function e(_e2) { didErr = true; err = _e2; }, f: function f() { try { if (!normalCompletion && it.return != null) it.return(); } finally { if (didErr) throw err; } } }; }
function _unsupportedIterableToArray(o, minLen) { if (!o) return; if (typeof o === "string") return _arrayLikeToArray(o, minLen); var n = Object.prototype.toString.call(o).slice(8, -1); if (n === "Object" && o.constructor) n = o.constructor.name; if (n === "Map" || n === "Set") return Array.from(o); if (n === "Arguments" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen); }
function _arrayLikeToArray(arr, len) { if (len == null || len > arr.length) len = arr.length; for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i]; return arr2; }
/**
 * Replaces non alphanumeric characters with underscore.
 *
 * @param {String} key
 * @returns {String}
 */
var validateKey = function validateKey(key) {
  return key.replace(/[^a-zA-Z0-9]/gi, '_').toLowerCase();
};

/**
 * Detects device type by screen size.
 *
 * @returns {Boolean}
 */
var isMobile = function isMobile() {
  var w = window,
    d = document,
    e = d.documentElement,
    g = d.getElementsByTagName('body')[0],
    x = w.innerWidth || e.clientWidth || g.clientWidth;
  return x < 768;
};

/**
 * Takes an array with duplicates and filter duplicates.
 *
 * @param {Array}
 * @returns {Array}
 */
var filterDuplicates = function filterDuplicates(arr) {
  return arr.filter(function (elem, idx) {
    return arr.indexOf(elem) == idx;
  });
};

/**
 * Takes a string and returns same word with first letter capitalized.
 *
 * @param {String} str
 * @returns {String}
 */
var capitalize = function capitalize(str) {
  return typeof str === 'string' ? str.split(' ').map(function (word) {
    return word.charAt(0).toUpperCase() + word.slice(1);
  }).join(' ') : str;
};

/**
 * Check if array has value
 */

function containsCaseInsensitive(array, value) {
  if (!Array.isArray(array)) {
    return;
  }

  // Convert the value and array elements to lowercase for case-insensitive comparison
  var lowercaseValue = value.toLowerCase();
  var _iterator = _createForOfIteratorHelper(array),
    _step;
  try {
    for (_iterator.s(); !(_step = _iterator.n()).done;) {
      var element = _step.value;
      if (element.toLowerCase() === lowercaseValue) {
        return true; // Found a case-insensitive match
      }
    }
  } catch (err) {
    _iterator.e(err);
  } finally {
    _iterator.f();
  }
  return false; // No case-insensitive match found
}

/**
 * Set values on Localstorage
 */

var setTermsOnLocalStorage = function setTermsOnLocalStorage() {
  var term = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : '';
  var hasData = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;
  var stKey = "recently_searched_terms";
  var searchTerms = JSON.parse(localStorage.getItem(stKey) || '[]');
  if (!term || containsCaseInsensitive(searchTerms, term)) {
    return false;
  }
  if (hasData) {
    if (!searchTerms.includes(term)) {
      searchTerms.unshift(term);
    }
  } else {
    searchTerms = searchTerms.filter(function (t) {
      return t !== term;
    });
  }
  if (searchTerms.length > 5) {
    searchTerms.pop();
  }
  localStorage.setItem(stKey, JSON.stringify(searchTerms));
};
var getProductsFromLocalStrorage = function getProductsFromLocalStrorage() {
  var productTerms = window.localStorage.getItem('recently_searched_terms');
  var productData = window.localStorage.getItem('product_data_storage');
  var recentlyViewedProduct = window.localStorage.getItem('recently_viewed_product');
  return {
    productTerms: isJSON(productTerms) ? JSON.parse([productTerms]) : [],
    productData: isJSON(productData) ? JSON.parse([productData]) : {},
    recentlyViewedProduct: isJSON(recentlyViewedProduct) ? JSON.parse([recentlyViewedProduct]) : {}
  };
};
var isJSON = function isJSON(str) {
  try {
    return JSON.parse(str) && !!str;
  } catch (e) {
    return false;
  }
};
var setLocalStorage = function setLocalStorage(key, value) {
  window.localStorage.setItem(key, value);
};
var debounce = function debounce(func, delay) {
  var timeoutId;
  return function () {
    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {
      args[_key] = arguments[_key];
    }
    var context = this;
    clearTimeout(timeoutId);
    timeoutId = setTimeout(function () {
      func.apply(context, args);
    }, delay);
  };
};
var getAmastyLabelStyles = function getAmastyLabelStyles(labelPosition) {
  var commonStyles = {
    position: 'absolute',
    width: '45%',
    height: '45%',
    zIndex: '1',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat'
  };
  var commonTopStyles = _objectSpread(_objectSpread({}, commonStyles), {}, {
    top: 0,
    backgroundPosition: 'top'
  });
  var commonBottomStyles = _objectSpread(_objectSpread({}, commonStyles), {}, {
    bottom: 0,
    backgroundPosition: 'bottom'
  });
  switch (labelPosition) {
    case '0':
      return _objectSpread(_objectSpread({}, commonTopStyles), {}, {
        left: 0
      });
    case '2':
      return _objectSpread(_objectSpread({}, commonTopStyles), {}, {
        right: 0
      });
    case '6':
      return _objectSpread(_objectSpread({}, commonBottomStyles), {}, {
        left: 0
      });
    case '8':
      return _objectSpread(_objectSpread({}, commonBottomStyles), {}, {
        right: 0
      });
    default:
      return _objectSpread(_objectSpread({}, commonTopStyles), {}, {
        left: 0
      });
  }
};

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			id: moduleId,
/******/ 			loaded: false,
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Flag the module as loaded
/******/ 		module.loaded = true;
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/******/ 	// expose the modules object (__webpack_modules__)
/******/ 	__webpack_require__.m = __webpack_modules__;
/******/ 	
/************************************************************************/
/******/ 	/* webpack/runtime/chunk loaded */
/******/ 	!function() {
/******/ 		var deferred = [];
/******/ 		__webpack_require__.O = function(result, chunkIds, fn, priority) {
/******/ 			if(chunkIds) {
/******/ 				priority = priority || 0;
/******/ 				for(var i = deferred.length; i > 0 && deferred[i - 1][2] > priority; i--) deferred[i] = deferred[i - 1];
/******/ 				deferred[i] = [chunkIds, fn, priority];
/******/ 				return;
/******/ 			}
/******/ 			var notFulfilled = Infinity;
/******/ 			for (var i = 0; i < deferred.length; i++) {
/******/ 				var chunkIds = deferred[i][0];
/******/ 				var fn = deferred[i][1];
/******/ 				var priority = deferred[i][2];
/******/ 				var fulfilled = true;
/******/ 				for (var j = 0; j < chunkIds.length; j++) {
/******/ 					if ((priority & 1 === 0 || notFulfilled >= priority) && Object.keys(__webpack_require__.O).every(function(key) { return __webpack_require__.O[key](chunkIds[j]); })) {
/******/ 						chunkIds.splice(j--, 1);
/******/ 					} else {
/******/ 						fulfilled = false;
/******/ 						if(priority < notFulfilled) notFulfilled = priority;
/******/ 					}
/******/ 				}
/******/ 				if(fulfilled) {
/******/ 					deferred.splice(i--, 1)
/******/ 					var r = fn();
/******/ 					if (r !== undefined) result = r;
/******/ 				}
/******/ 			}
/******/ 			return result;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/compat get default export */
/******/ 	!function() {
/******/ 		// getDefaultExport function for compatibility with non-harmony modules
/******/ 		__webpack_require__.n = function(module) {
/******/ 			var getter = module && module.__esModule ?
/******/ 				function() { return module['default']; } :
/******/ 				function() { return module; };
/******/ 			__webpack_require__.d(getter, { a: getter });
/******/ 			return getter;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/define property getters */
/******/ 	!function() {
/******/ 		// define getter functions for harmony exports
/******/ 		__webpack_require__.d = function(exports, definition) {
/******/ 			for(var key in definition) {
/******/ 				if(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
/******/ 					Object.defineProperty(exports, key, { enumerable: true, get: definition[key] });
/******/ 				}
/******/ 			}
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/hasOwnProperty shorthand */
/******/ 	!function() {
/******/ 		__webpack_require__.o = function(obj, prop) { return Object.prototype.hasOwnProperty.call(obj, prop); }
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/make namespace object */
/******/ 	!function() {
/******/ 		// define __esModule on exports
/******/ 		__webpack_require__.r = function(exports) {
/******/ 			if(typeof Symbol !== 'undefined' && Symbol.toStringTag) {
/******/ 				Object.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });
/******/ 			}
/******/ 			Object.defineProperty(exports, '__esModule', { value: true });
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/node module decorator */
/******/ 	!function() {
/******/ 		__webpack_require__.nmd = function(module) {
/******/ 			module.paths = [];
/******/ 			if (!module.children) module.children = [];
/******/ 			return module;
/******/ 		};
/******/ 	}();
/******/ 	
/******/ 	/* webpack/runtime/jsonp chunk loading */
/******/ 	!function() {
/******/ 		// no baseURI
/******/ 		
/******/ 		// object to store loaded and loading chunks
/******/ 		// undefined = chunk not loaded, null = chunk preloaded/prefetched
/******/ 		// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded
/******/ 		var installedChunks = {
/******/ 			"autocomplete": 0
/******/ 		};
/******/ 		
/******/ 		// no chunk on demand loading
/******/ 		
/******/ 		// no prefetching
/******/ 		
/******/ 		// no preloaded
/******/ 		
/******/ 		// no HMR
/******/ 		
/******/ 		// no HMR manifest
/******/ 		
/******/ 		__webpack_require__.O.j = function(chunkId) { return installedChunks[chunkId] === 0; };
/******/ 		
/******/ 		// install a JSONP callback for chunk loading
/******/ 		var webpackJsonpCallback = function(parentChunkLoadingFunction, data) {
/******/ 			var chunkIds = data[0];
/******/ 			var moreModules = data[1];
/******/ 			var runtime = data[2];
/******/ 			// add "moreModules" to the modules object,
/******/ 			// then flag all "chunkIds" as loaded and fire callback
/******/ 			var moduleId, chunkId, i = 0;
/******/ 			if(chunkIds.some(function(id) { return installedChunks[id] !== 0; })) {
/******/ 				for(moduleId in moreModules) {
/******/ 					if(__webpack_require__.o(moreModules, moduleId)) {
/******/ 						__webpack_require__.m[moduleId] = moreModules[moduleId];
/******/ 					}
/******/ 				}
/******/ 				if(runtime) var result = runtime(__webpack_require__);
/******/ 			}
/******/ 			if(parentChunkLoadingFunction) parentChunkLoadingFunction(data);
/******/ 			for(;i < chunkIds.length; i++) {
/******/ 				chunkId = chunkIds[i];
/******/ 				if(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {
/******/ 					installedChunks[chunkId][0]();
/******/ 				}
/******/ 				installedChunks[chunkId] = 0;
/******/ 			}
/******/ 			return __webpack_require__.O(result);
/******/ 		}
/******/ 		
/******/ 		var chunkLoadingGlobal = self["webpackChunkunbxd_search_app"] = self["webpackChunkunbxd_search_app"] || [];
/******/ 		chunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));
/******/ 		chunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));
/******/ 	}();
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module depends on other loaded chunks and execution need to be delayed
/******/ 	var __webpack_exports__ = __webpack_require__.O(undefined, ["autocomplete-vendor"], function() { return __webpack_require__("./src/index.js"); })
/******/ 	__webpack_exports__ = __webpack_require__.O(__webpack_exports__);
/******/ 	
/******/ })()
;
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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
