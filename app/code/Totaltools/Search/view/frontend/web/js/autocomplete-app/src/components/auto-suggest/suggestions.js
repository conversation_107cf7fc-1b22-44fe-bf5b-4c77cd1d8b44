import React, { useCallback } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { clearSearch, searchQuery, fetchSearchTerms } from '../../actions';

const Suggestions = React.memo(({ show }) => {
  const suggestions = useSelector((state) => state.suggestions);
  const dispatch = useDispatch();

  const handleClick = useCallback((query) => {
    const input = document.getElementById('unbxd-search-input');
    if (input && query) {
      dispatch(clearSearch());
      dispatch(searchQuery(query));
      dispatch(fetchSearchTerms(query));
      input.value = query;
    }
  }, [dispatch]);

  const buildSuggestionsList = useCallback((suggestions) => {
    return suggestions.map((sug, idx) => (
      <strong key={idx} onClick={() => handleClick(sug.autosuggest)}>
        {sug.autosuggest}
      </strong>
    ));
  }, [handleClick]);

  return show && suggestions && suggestions.length ? (
    <div className="search-autocomplete-suggestions">
      Are you looking for: {buildSuggestionsList(suggestions)}
    </div>
  ) : null;
});

export default Suggestions;
