import { STORES_ENUM, PRICE_FIELD, SPECIAL_PRICE_FIELD, ORIGINAL_PRICE_FIELD } from '../constants';

export const prepareSearchResults = (data, isCorrectionTerm) => {
  const metaData = data?.searchMetaData;
  const queryParams = metaData?.queryParams;
  const response = data?.response;
  const isSpellCorrection = !!data?.didYouMean;
  const numberOfproducts = data?.response?.numberOfProducts;
  const banners = data?.banner?.banners;
  const redirect = data?.redirect ? { redirect: data?.redirect?.value } : {};
  const allFacets = data?.facets;
  const spellCorrection = data?.didYouMean?.map(({ suggestion }) => suggestion);
  let facets = { multilevel: {}, range: {}, text: {} };

  if (isSpellCorrection && !numberOfproducts) {
    return { spellCorrection };
  }

  if (allFacets) {
    Object.entries(data?.facets).forEach(([key, value]) => {
      switch (key) {
        case 'multilevel': {
          const breadcrumb = prepareBreadcrumbValues(value?.breadcrumb);
          const bucket = value?.bucket[0];
          const skipCategories = window?.unbxdConfig?.skippedCategories?.split(',') || [];
          const lowerCaseSkippedCategories = skipCategories?.map((name) => name.toLowerCase().trim());
          const buckets = bucket?.values?.reduce((result, item, index, array) => {
            if (index % 2 === 0) {
              result.push({ name: item, count: array[index + 1], level: bucket?.level });
            }
            return result;
          }, []);
          const filteredBucket = buckets.filter(({ name }) => !lowerCaseSkippedCategories.includes(name.toLowerCase()));
          facets['multilevel'] = [{ breadcrumb, bucket: filteredBucket }];
          break;
        }

        case 'range': {
          const rangeValues = value['list'];
          const rangeFacet = rangeValues.map(({ displayName, facetName, position, values }) => {
            const facetRangeValues = prepareRangeFacetValues(values['counts']);

            return {
              displayName,
              name: facetName,
              position,
              gap: values['gap'],
              start: values['start'],
              end: values['end'],
              values: facetRangeValues,
            };
          });
          facets['range'] = rangeFacet;
          break;
        }

        case 'text': {
          const textValues = value['list'];
          const textFacet = textValues.map(({ displayName, facetName, position, values }) => {
            const facetTextValues = prepareTextFacetValues(values);
            return {
              displayName,
              name: facetName,
              position,
              values: facetTextValues,
            };
          });
          facets['text'] = textFacet;
          break;
        }
      }
    });
  }

  const data_ =
    {
      products: [...response?.products],
      facets,
      metaData: {
        statusCode: metaData.status,
        pageNo: queryParams.page,
        totalCount: response?.numberOfProducts,
        queryParams: queryParams,
        queryTime: metaData.queryTime,
        message: 'OK',
        errorCode: null,
        status: null,
        pageSize: queryParams.rows,
        displayMessage: null,
        storeId: 1,
      },
      banners,
      ...redirect,
    } || {};
  return data_;
};

const prepareTextFacetValues = (data) => {
  return data?.reduce((result, item, index, array) => {
    if (index % 2 === 0) {
      result.push({ term: item, count: array[index + 1] });
    }
    return result;
  }, []);
};

const prepareRangeFacetValues = (data) => {
  return data?.reduce((result, item, index, array) => {
    if (index % 2 === 0) {
      result.push({ count: array[index + 1], value: parseInt(item) });
    }
    return result;
  }, []);
};

const prepareBreadcrumbValues = (breadcrumb) => {
  const breadcrumbs = getBreadcrumbValues(breadcrumb);
  const processChildBreadcrumbs = (child, depth) => {
    if (!child || depth >= 4) {
      return;
    }
    const childValues = getBreadcrumbValues(child);
    if (childValues) breadcrumbs.push(...childValues);
    processChildBreadcrumbs(child.child, depth + 1);
  };

  processChildBreadcrumbs(breadcrumb.child, 0);
  return breadcrumbs.length ? breadcrumbs : [];
};

const getBreadcrumbValues = (breadcrumb) => {
  if (breadcrumb?.values?.length > 0) {
    const breadcrumbs = breadcrumb?.values?.map(({ value }) => ({ name: value, level: breadcrumb.level }));
    return breadcrumbs;
  }
  return [];
};

export const formatPirceRange = (arr) => {
  const [start, end] = arr.match(/\d+/g);
  const formatted = `[${start} TO ${Math.floor(end)}]`;
  return formatted;
};

export const extractFacetNameFromFilter = (facet) => {
  if (facet.includes(':')) {
    const splittedFacet = facet.split(':');
    return splittedFacet[1];
  } else {
    return facet;
  }
};

export const getFlattenFacetFilters = (facets, category_name = null) => {
  if (!facets || Object.keys(facets).length === 0) return [];
  const flattenFacetFilters = Object.entries(facets).flatMap(([key, value]) => {
    if (Array.isArray(value)) {
      return value.map((val) => ({
        name: val,
        type: key,
        value: val,
      }));
    } else if (typeof value === 'object') {
      return getFlattenFacetFilters(value, key);
    } else {
      return {
        name: key,
        type: key,
        value: value,
      };
    }
  });

  return flattenFacetFilters;
};

export const getFormattedPriceRange = (range) => {
  const [start, end] = range
    .slice(1, -1)
    .split('TO')
    ?.map((str) => str.trim());
  return `$${start}-$${end}`;
};

export const getImgUrl = (data) => {
  const attrOrder = ['labeledImage', 'thumbnail', 'smallImage'];
  const extsRegex = new RegExp(/\.(gif|jpe?g|tiff?|png|webp|bmp)$/i);
  var imgUrl;

  attrOrder.forEach((attr) => {
    if (!imgUrl && data[attr] && extsRegex.test(data[attr])) {
      imgUrl = data[attr];
      return imgUrl;
    }
  });

  if (!imgUrl && data.imageUrl && data.imageUrl.length) {
    imgUrl = data.imageUrl[0];
  }

  return imgUrl;
};

export const getAmastyLabelStyles = (labelPosition) => {
  const commonStyles = {
    position: 'absolute',
    width: '100px',
    height: '50px',
    zIndex: '1',
    backgroundSize: 'contain',
    backgroundRepeat: 'no-repeat',
  };
  const commonTopStyles = {
    ...commonStyles,
    top: 0,
    backgroundPosition: 'top',
  };
  const commonBottomStyles = {
    ...commonStyles,
    bottom: 0,
    backgroundPosition: 'bottom',
  };
  switch (labelPosition) {
    case '0':
      return { ...commonTopStyles, left: 0 };
    case '2':
      return { ...commonTopStyles, right: 0 };
    case '6':
      return { ...commonBottomStyles, left: 0 };
    case '8':
      return { ...commonBottomStyles, right: 0 };
    default:
      return { ...commonTopStyles, left: 0 };
  }
};

export const getProductPriceAttributes = (data) => {
  if (!data) return;
  const storeName = window?.unbxdConfig?.storeName;
  if (!storeName) {
    return { storePrice: data?.price, storeSpecialPrice: data?.specialPrice, storeOriginalPrice: data?.originalPrice };
  }
  const storeId = STORES_ENUM[storeName];
  const storeField = `Store${storeId}`;
  const storePrice = data?.[`${PRICE_FIELD}${storeField}`] || data?.[PRICE_FIELD];
  const storeSpecialPrice = data?.[`${SPECIAL_PRICE_FIELD}${storeField}`] || data?.[SPECIAL_PRICE_FIELD];
  const storeOriginalPrice = data?.[`${ORIGINAL_PRICE_FIELD}${storeField}`] || data?.[ORIGINAL_PRICE_FIELD];
  return { storePrice, storeSpecialPrice, storeOriginalPrice };
};

export const updatePageTitle = (query) => {
  const pageTitle = document.querySelector('.page-title span');
  if (pageTitle) {
    pageTitle['textContent'] = `Search results for: '${query}'`;
  }
};

export const updatePageBreadCrumb = (query, searchPage = false) => {
  const pageBreadCrumb = document.querySelector(`${searchPage ? '.item.search' : '.item.keyword'}`);
  const content = searchPage ? `Search results for: '${query}'` : query;
  if (pageBreadCrumb) pageBreadCrumb['textContent'] = content ? content : `''`;
};
