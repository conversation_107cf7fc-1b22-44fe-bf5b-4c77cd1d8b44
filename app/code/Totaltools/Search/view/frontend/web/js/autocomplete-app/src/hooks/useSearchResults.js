import { useDispatch } from 'react-redux';
import { fetchSearchTerms, searchQuery, clearSearch, suggestionClick, fetchTermResult } from '../actions';
import { useEffect, useState } from 'react';

export const useSearchResults = (config, openDropDown) => {
  let input = document.getElementsByClassName('input-text');
  const value = input[1]?.value;
  const [timeoutValue, setTimeoutValue] = useState(null);
  const [inputValue, setInputValue] = useState(value)
  const dispatch = useDispatch();

  useEffect(() => {
    setInputValue(value)
  }, [value])
  
  useEffect(() => {
    const searchForm = document.getElementById('search_mini_form');
    const searchLabel = document.getElementById('minisearch-label');
    const searchControl = document.getElementById('minisearch-control');
    const rightCol = document.getElementsByClassName('col-right');

    if (searchForm && !searchForm.classList.contains('opened')) {
      searchForm?.classList?.add('opened');
      searchLabel?.classList?.add('opened');
      searchControl?.classList?.add('opened');
    }

    if (rightCol.length) {
      rightCol[0].style.display = 'none';
    }

    dispatch(searchQuery(inputValue));
    clearTimeout(timeoutValue);
    const timeout = setTimeout(() => {
      let query = inputValue || '';
      if (!query || query.length < 2 || !openDropDown) {
        dispatch(clearSearch());
      } else {
        dispatch(clearSearch())
          .then(() => dispatch(searchQuery(query)))
          .then(() => dispatch(fetchSearchTerms(query)))
          .then(() => dispatch(suggestionClick(query)))
          .then(() => dispatch(fetchTermResult(query, true)));
      }
    }, config?.autoSuggestDelay || 500);
    setTimeoutValue(timeout);

    return () => clearTimeout(timeoutValue);
  }, [inputValue, openDropDown]);
  return null;
};
