const TerserPlugin = require("terser-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const path = require("path");

module.exports = {
    entry: {
        autocomplete: "./src/index.js",
    },
    output: {
        path: __dirname + "/../",
        publicPath: "/",
        filename: "[name].min.js",
    },
    devtool: "inline-source-map",
    module: {
        rules: [
            {
                test: /\.(js|jsx)$/,
                exclude: /node_modules/,
                use: ["babel-loader"],
            },
        ],
    },
    resolve: {
        extensions: [".jsx", ".js"],
    },
    optimization: {
        minimize: true,
        minimizer: [new TerserPlugin({ test: /\.js(\?.*)?$/i })],
        splitChunks: {
            cacheGroups: {
                vendor: {
                    test: /[\\/]node_modules[\\/]/,
                    name: "autocomplete-vendor",
                    chunks: "all",
                },
            },
        },
    },
    plugins: [
        new HtmlWebpackPlugin({
            template: "./public/index.html",
            filename: "index.html",
        }),
    ],
    devServer: {
        port: 3003,
    },
};
