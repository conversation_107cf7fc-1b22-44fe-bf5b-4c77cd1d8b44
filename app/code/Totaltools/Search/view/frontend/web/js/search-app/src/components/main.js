import React, { useState, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { searchReset, searchQuery, searchRequest, fetchSearchResults, resetFitlers } from '../redux';

import { getUrlParam, getUrlParams, isValidate, sanitizedQuery, updatePageBreadCrumb, updatePageTitle } from '../utils';

import { SearchResults, SearchSidebar, NoResult } from '../containers';
import { HammerSpinner } from '../components';

export const Main = () => {
  const dispatch = useDispatch();
  const urlSearchParams = new URLSearchParams(window.location.search);
  const queryParams = Object.fromEntries(urlSearchParams.entries());
  const { isSearching, noResult, products, spellCorrectionProducts } = useSelector((state) => ({
    isSearching: state.search.isSearching,
    noResult: state.search.noResult,
    products: state.search.products,
    spellCorrectionProducts: state.search.spellCorrectionProducts,
  }));
  const mode = window?.unbxdConfig?.catalogFrontendDisplayMode;
  const [displayMode, setDisplayMode] = useState(mode);

  const { perPage } = useSelector((state) => state.search);
  const initSearch = () => {
    let query = getUrlParam('q');
    let params = getUrlParams();
    params = {
      q: query,
      ...queryParams,
      rows: perPage
    };

    const pathName = location.pathname;
    if (!query && pathName?.includes('/buy/')) {
      const fullPath = pathName.split('/buy/');
      const longtailKeyword_ = fullPath[fullPath?.length - 1];
      const longtailKeyword = longtailKeyword_ ? decodeURIComponent(longtailKeyword_) : '';
      if (longtailKeyword) {
        if (isValidate(longtailKeyword)) {
          query = longtailKeyword?.replace(/([-_])/gi, ' ');
        } else {
          const sanitizedQuery_ = sanitizedQuery(longtailKeyword);
          updatePageTitle(sanitizedQuery_);
          updatePageBreadCrumb(sanitizedQuery_);
          query = sanitizedQuery_?.replace(/([-_])/gi, ' ');
        }
      } else {
        query = '';
      }
      params = { q: query, rows: perPage};
    }
    if (query) {
      dispatch(searchReset());
      dispatch(resetFitlers());
      dispatch(searchQuery(query));
      dispatch(searchRequest(params)).then(() => {
        dispatch(fetchSearchResults(params, false));
      });
    }
  };

  useEffect(() => {
    initSearch();
    window.addEventListener('popstate', initSearch);

    return () => {
      window.removeEventListener('popstate', initSearch);
    };
  }, [dispatch]);

  const activeMode = displayMode?.split('-')[0] || displayMode;

  return (
    <div className="instant-search-wrap page-products" aria-busy={isSearching}>
      {noResult && <NoResult />}
      <div className={`instant-search-row products-${activeMode}`}>
        <HammerSpinner show={isSearching} />
        {(!!products.length || !!spellCorrectionProducts.length) && (
          <>
            <SearchResults setDisplayMode={setDisplayMode} displayMode={displayMode} />
            <SearchSidebar show />
          </>
        )}
      </div>
    </div>
  );
};
