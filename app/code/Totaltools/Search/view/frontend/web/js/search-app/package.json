{"name": "unbxd-search-app", "version": "1.0.0", "description": "Unbxd search app to override native Magento2 catalog search", "main": "index.js", "scripts": {"update-dep": "ncu -u", "dev": "webpack serve --mode development", "build": "webpack --mode production", "format": "prettier --write './**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc"}, "keywords": ["unbxd", "unbxd-search"], "author": "<PERSON><PERSON>", "dependencies": {"html-react-parser": "^4.0.0", "nouislider-react": "^3.4.1", "query-string": "^8.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-equalizer": "^1.3.0", "react-js-pagination": "^3.0.3", "react-redux": "^8.1.1", "react-responsive-carousel": "^3.2.23", "redux": "^4.2.1", "redux-thunk": "^2.4.2"}, "devDependencies": {"@babel/compat-data": "7.22.9", "@babel/core": "^7.22.9", "@babel/preset-env": "^7.22.9", "@babel/preset-react": "^7.22.5", "babel-loader": "^9.1.3", "babel-plugin-transform-class-properties": "^6.24.1", "css-loader": "^6.8.1", "dedupe-plugin": "^1.0.1", "eslint": "^8.45.0", "eslint-plugin-react": "^7.32.2", "html-webpack-plugin": "^5.5.3", "less": "^4.1.3", "less-loader": "^11.1.3", "npm-check-updates": "^16.10.16", "prettier": "^3.0.1", "redux-logger": "^3.0.6", "style-loader": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "webpack": "^5.88.1", "webpack-bundle-analyzer": "^4.9.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1"}, "resolutions": {"@babel/preset-env": "7.5.5"}}