import {
  SEARCH_RESULT,
  <PERSON>ARCH_QUERY,
  <PERSON>ARCH_RESET,
  SEARCH_REQUEST,
  SEARCH_ERROR,
  SORT_RESULT_FIELD,
  PER_PAGE_CHANGE,
  SORT_CLEAR,
  PAGE_RESET,
  CORRECTION_SEARCH_RESULT,
  SORT_RESULT_FIELD_AND_DIR,
} from '../../constants';
import { getUrlParam } from '../../utils';

const sortParams = getUrlParam('sort').split(' ');

const initialState = {
  isSearching: false,
  noResult: false,
  query: null,
  urlParams: {},
  products: [],
  banners: [],
  facets: {},
  pagination: {
    count: 0,
    active: 1,
    range: 7,
    itemsCount: 0,
  },
  sort: {
    field: sortParams[0] || null,
    direction: sortParams[1] || 'asc',
  },
  perPage: 36,
  errors: null,
  spellCorrection: [],
  spellCorrectionProducts: [],
  config: window.unbxdConfig || { showPrice: true },
};

const searchReducer = (state = initialState, action) => {
  const data = action.payload;
  const products = data?.products || [];
  const meta = data?.metaData || {};
  const banners = data?.banners || [];

  switch (action.type) {
    case SEARCH_RESET:
      return Object.assign({}, initialState);
    case SEARCH_QUERY:
      return Object.assign({}, state, {
        isSearching: false,
        query: action.query,
      });
    case SEARCH_REQUEST:
      let params = action.urlParams;
      return Object.assign({}, state, {
        isSearching: true,
        urlParams: { ...state?.urlParams, ...params },
        pagination: {
          ...state.pagination,
          active: params?.page || state.pagination.active,
        },
      });
    case SEARCH_RESULT:
      return Object.assign({}, state, {
        isSearching: false,
        noResult: meta.totalCount < 1 || products.length < 1,
        products: products,
        facets: data.facets,
        pagination: {
          ...state.pagination,
          active: state.urlParams.page || state.pagination.active,
          count: meta.totalCount || 0,
          itemsCount: products.length,
          perPage: meta.pageSize || state.perPage,
        },
        spellCorrection: data.spellCorrection || [],
        banners: banners,
      });

    case CORRECTION_SEARCH_RESULT: {
      return Object.assign({}, state, {
        isSearching: false,
        facets: data.facets,
        pagination: {
          ...state.pagination,
          active: state.urlParams.page || state.pagination.active,
          count: meta.totalCount || 0,
          itemsCount: products.length,
          perPage: meta.pageSize || state.perPage,
        },
        spellCorrectionProducts: products,
        banners: banners,
      });
    }
    case SEARCH_ERROR:
      const { message } = action.payload;
      return Object.assign({}, state, {
        isSearching: false,
        errors: action.payload,
        noResult: message.length > 0 || false,
      });
    case SORT_RESULT_FIELD:
      return Object.assign({}, state, {
        sort: { ...state.sort, ...action.field },
        urlParams: {
          ...state.urlParams,
          sort: `${action.field.field} ${state.sort.direction || 'asc'}`,
        },
      });
    case SORT_RESULT_FIELD_AND_DIR:
      return Object.assign({}, state, {
        sort: {
          ...state.sort,
          field: action.field,
          direction: action.direction,
        },
        urlParams: {
          ...state.urlParams,
          sort: `${action.field} ${action.direction}`,
        },
      });
    case PER_PAGE_CHANGE:
      return Object.assign({}, state, {
        perPage: action.perPage,
        urlParams: {
          ...state.urlParams,
          rows: action.perPage || 36,
        },
      });
    case SORT_CLEAR:
      return Object.assign({}, state, {
        sort: { field: null, direction: null },
        urlParams: { ...state.urlParams, sort: null },
      });
    case PAGE_RESET:
      return Object.assign({}, state, {
        ...state,
        pagination: {
          ...state.pagination,
          active: 1,
        },
        urlParams: {
          ...state.urlParams,
          page: null,
        },
      });
    default:
      return state;
  }
};

export default searchReducer;
