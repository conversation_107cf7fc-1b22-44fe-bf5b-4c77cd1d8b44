/*! For license information please see autocomplete.min.js.LICENSE.txt */
!function(){"use strict";var e,t={124:function(e,t,r){var n=r(294),o=r(745),a=r(998),i=r(894),l=r(791);function c(e){return c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c(e)}function u(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function s(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?u(Object(r),!0).forEach((function(t){A(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):u(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function A(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==c(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==c(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===c(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function m(e,t){var r="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!r){if(Array.isArray(e)||(r=function(e,t){if(!e)return;if("string"==typeof e)return f(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return f(e,t)}(e))||t&&e&&"number"==typeof e.length){r&&(e=r);var n=0,o=function(){};return{s:o,n:function(){return n>=e.length?{done:!0}:{done:!1,value:e[n++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,i=!0,l=!1;return{s:function(){r=r.call(e)},n:function(){var e=r.next();return i=e.done,e},e:function(e){l=!0,a=e},f:function(){try{i||null==r.return||r.return()}finally{if(l)throw a}}}}function f(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var d=function(e){return e.replace(/[^a-zA-Z0-9]/gi,"_").toLowerCase()},p=function(e){return"string"==typeof e?e.split(" ").map((function(e){return e.charAt(0).toUpperCase()+e.slice(1)})).join(" "):e};var v=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"",t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1],r="recently_searched_terms",n=JSON.parse(localStorage.getItem(r)||"[]");if(!e||function(e,t){if(Array.isArray(e)){var r,n=t.toLowerCase(),o=m(e);try{for(o.s();!(r=o.n()).done;)if(r.value.toLowerCase()===n)return!0}catch(e){o.e(e)}finally{o.f()}return!1}}(n,e))return!1;t?n.includes(e)||n.unshift(e):n=n.filter((function(t){return t!==e})),n.length>5&&n.pop(),localStorage.setItem(r,JSON.stringify(n))},y=function(){var e=window.localStorage.getItem("recently_searched_terms"),t=window.localStorage.getItem("product_data_storage"),r=window.localStorage.getItem("recently_viewed_product");return{productTerms:h(e)?JSON.parse([e]):[],productData:h(t)?JSON.parse([t]):{},recentlyViewedProduct:h(r)?JSON.parse([r]):{}}},h=function(e){try{return JSON.parse(e)&&!!e}catch(e){return!1}},g=function(e,t){window.localStorage.setItem(e,t)},b="SEARCH_QUERY",E="SEARCH_CLEAR",w="SEARCH_REQUEST",S="SEARCH_PRODUCTS",q="SEARCH_RESULT",O="SEARCH_RESULT_ERROR",C="SEARCH_CORRECTIONS_STARTED",L="SEARCH_CORRECTION_SUCCESS",P="SEARCH_CORRECTIONS_ERROR",N="PRODUCTS_CLEAR",j="SUGGEST_CLICK",I="SUGGEST_REQUEST",k="SUGGEST_SUCCESS",Q="SUGGEST_FAILURE",B="TOGGLE_DROPDOWN",T="UPDATE_LS_PRODUCTS",Z="TOGGLE_MAIN_LOADER";function G(e){return G="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},G(e)}function J(e){return function(e){if(Array.isArray(e))return D(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return D(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return D(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function D(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function x(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function F(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?x(Object(r),!0).forEach((function(t){Y(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):x(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Y(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==G(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==G(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===G(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var W,U,K=y(),R=function(e){var t=new Set;return null==e?void 0:e.filter((function(e){var r=null==e?void 0:e.term.toLowerCase(),n=t.has(r);return t.add(r),!n}))},z=F({showMainLoader:!1,isSearching:!1,showDropdown:!1,noResult:!1,query:null,products:[],productsCount:0,suggestions:{isInitialFetch:!0,fetching:!1,activeTerm:null,term:null,terms:[],products:{},redirects:{},error:!1},corrections:{terms:[],products:[],isFetchingProducts:!1,errorMsgFetchingProducts:null,redirects:{}},config:window.unbxdConfig||{},error:!1,errorMsg:null},K),H=function(){var e,t,r,n=arguments.length>0&&void 0!==arguments[0]?arguments[0]:z,o=arguments.length>1?arguments[1]:void 0,a={},i=[],l="";switch(o.type){case E:var c=y();return Object.assign({},F(F({},z),c));case b:return Object.assign({},n,{isSearching:!1,query:o.query,error:!1,errorMsg:null,noResult:!1});case w:return Object.assign({},n,{isSearching:!0,query:o.query,showDropdown:!0});case"CLEAR_ACTIVE_TERM":return Object.assign({},n,{suggestions:F(F({},n.suggestions),{},{activeTerm:null})});case q:var u=J(o.terms);if(void 0!==o.payload.products)a[d(o.query)]=o.payload.products;var s=R([].concat(J(null===(e=n.suggestions)||void 0===e?void 0:e.terms),J(u))),A=R([].concat(J(null==n||null===(t=n.corrections)||void 0===t?void 0:t.terms),J(o.payload.spellCorrections)));return Object.assign({},n,{isSearching:!1,showDropdown:!0,suggestions:F(F({},n.suggestions),{},{terms:s}),corrections:F(F({},n.corrections),{},{terms:A})});case S:if(void 0!==o.payload.products)a[d(o.query)]=o.payload.products;case O:return Object.assign({},n,{isSearching:!1,error:!0,noResult:!1,errorMsg:o.error,products:[]});case N:return Object.assign({},n,{products:[],suggestions:F(F({},n.suggestions),{},{activeTerm:null})});case j:return l=p(o.term),i=n.suggestions.products[l]||[],Object.assign({},n,{noResult:!i.length,suggestions:F(F({},n.suggestions),{},{isInitialFetch:!1,activeTerm:l})});case I:return Object.assign({},n,{noResult:!1,suggestions:F(F({},n.suggestions),{},{isInitialFetch:o.initial,fetching:!0})});case k:var m=o.payload,f=n.suggestions.redirects||{},v=p(o.term),h=p(n.query);a[v]=m.products,m.products.length&&(a[v]=m.products);var g=R([v===h?{term:v,product_count:m.queryProductsCount}:{term:h}].concat(J(null==n||null===(r=n.suggestions)||void 0===r?void 0:r.terms)));return m.redirect&&((f={})[v]=m.redirect),Object.assign({},n,{noResult:m.products.length<1&&!n.suggestions.products[v],suggestions:F(F({},n.suggestions),{},{fetching:!1,terms:g,products:F(F({},n.suggestions.products),Y({},v,m.products)),redirects:f}),corrections:F(F({},n.corrections),{},{terms:m.spellCorrections||[]})});case L:var G=o.payload.metaData.queryParams.q,D=o.payload.redirect,x={};D&&(x[p(G)]=D);var W=o.payload.products;return Object.assign({},n,{corrections:F(F({},n.corrections),{},{isFetchingProducts:!1,products:W||{},redirects:x})});case C:return Object.assign({},n,{corrections:F(F({},n.corrections),{},{isFetchingProducts:!0})});case P:return Object.assign({},n,{corrections:F(F({},n.corrections),{},{isFetchingProducts:!1,errorMsgFetchingProducts:o.error})});case Q:return Object.assign({},n,{showDropdown:!0,suggestions:F(F({},n.suggestions),{},{error:o.error})});case B:return Object.assign({},n,{showDropdown:Boolean(o.payload)});case T:var U=y();return Object.assign({},n,F({},U));case Z:var K,H=F({},n),X=null==o||null===(K=o.payload)||void 0===K?void 0:K.loading;return F(F({},H),{},{showMainLoader:X});default:return n}},X=window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__||l.qC;function M(e){return M="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},M(e)}function V(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==M(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==M(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===M(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var _=null===(W=window)||void 0===W?void 0:W.unbxdConfig,$=null==_?void 0:_.autoCompleteUrl,ee=(null==_||_.autoSugguestUrl,null==_?void 0:_.searchUrl),te="price",re="specialPrice",ne="originalPrice",oe=(V(U={},"Default Store View",1),V(U,"Queensland Store View",2),V(U,"South Australia Store View",3),V(U,"Western Australia Store View",4),U),ae="data:image/gif;base64,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";function ie(e){return function(e){if(Array.isArray(e))return le(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return le(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return le(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function le(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ce=function(e){var t,r,n=null==e||null===(t=e.response)||void 0===t?void 0:t.products,o=null==e?void 0:e.searchMetaData,a=(null==e?void 0:e.didYouMean)&&[null==e?void 0:e.didYouMean[0].suggestion],i=null==n?void 0:n.filter((function(e){return e.hasOwnProperty("sku")&&e})),l=e.response.numberOfProducts||0,c=null==n?void 0:n.filter((function(e){return"KEYWORD_SUGGESTION"===(null==e?void 0:e.doctype)&&e})),u=null==n?void 0:n.filter((function(e){return"TOP_SEARCH_QUERIES"===(null==e?void 0:e.doctype)&&e})),s=null==n?void 0:n.filter((function(e){return"IN_FIELD"===(null==e?void 0:e.doctype)&&e})),A=null==e||null===(r=e.redirect)||void 0===r?void 0:r.value;return{metaData:o,products:i,queryProductsCount:l,autoSuggest:[].concat(ie(u),ie(c)),inFieldsSuggestion:s,spellCorrections:a,redirect:A}},ue=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,t=e.toFixed(2).toString();if(-1===t.indexOf("."))return e;var r=t.split(".");return e=r.length>1&&"00"!==r[1]?r:r[0]},se=r(238);function Ae(e){return Ae="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Ae(e)}function me(){me=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,n=Object.defineProperty||function(e,t,r){e[t]=r.value},o="function"==typeof Symbol?Symbol:{},a=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",l=o.toStringTag||"@@toStringTag";function c(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,r){return e[t]=r}}function u(e,t,r,o){var a=t&&t.prototype instanceof m?t:m,i=Object.create(a.prototype),l=new O(o||[]);return n(i,"_invoke",{value:E(e,r,l)}),i}function s(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=u;var A={};function m(){}function f(){}function d(){}var p={};c(p,a,(function(){return this}));var v=Object.getPrototypeOf,y=v&&v(v(C([])));y&&y!==t&&r.call(y,a)&&(p=y);var h=d.prototype=m.prototype=Object.create(p);function g(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function b(e,t){function o(n,a,i,l){var c=s(e[n],e,a);if("throw"!==c.type){var u=c.arg,A=u.value;return A&&"object"==Ae(A)&&r.call(A,"__await")?t.resolve(A.__await).then((function(e){o("next",e,i,l)}),(function(e){o("throw",e,i,l)})):t.resolve(A).then((function(e){u.value=e,i(u)}),(function(e){return o("throw",e,i,l)}))}l(c.arg)}var a;n(this,"_invoke",{value:function(e,r){function n(){return new t((function(t,n){o(e,r,t,n)}))}return a=a?a.then(n,n):n()}})}function E(e,t,r){var n="suspendedStart";return function(o,a){if("executing"===n)throw new Error("Generator is already running");if("completed"===n){if("throw"===o)throw a;return L()}for(r.method=o,r.arg=a;;){var i=r.delegate;if(i){var l=w(i,r);if(l){if(l===A)continue;return l}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===n)throw n="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);n="executing";var c=s(e,t,r);if("normal"===c.type){if(n=r.done?"completed":"suspendedYield",c.arg===A)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(n="completed",r.method="throw",r.arg=c.arg)}}}function w(e,t){var r=t.method,n=e.iterator[r];if(void 0===n)return t.delegate=null,"throw"===r&&e.iterator.return&&(t.method="return",t.arg=void 0,w(e,t),"throw"===t.method)||"return"!==r&&(t.method="throw",t.arg=new TypeError("The iterator does not provide a '"+r+"' method")),A;var o=s(n,e.iterator,t.arg);if("throw"===o.type)return t.method="throw",t.arg=o.arg,t.delegate=null,A;var a=o.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,A):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,A)}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function q(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function O(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function C(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(r.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=void 0,t.done=!0,t};return o.next=o}}return{next:L}}function L(){return{value:void 0,done:!0}}return f.prototype=d,n(h,"constructor",{value:d,configurable:!0}),n(d,"constructor",{value:f,configurable:!0}),f.displayName=c(d,l,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===f||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,d):(e.__proto__=d,c(e,l,"GeneratorFunction")),e.prototype=Object.create(h),e},e.awrap=function(e){return{__await:e}},g(b.prototype),c(b.prototype,i,(function(){return this})),e.AsyncIterator=b,e.async=function(t,r,n,o,a){void 0===a&&(a=Promise);var i=new b(u(t,r,n,o),a);return e.isGeneratorFunction(r)?i:i.next().then((function(e){return e.done?e.value:i.next()}))},g(h),c(h,l,"Generator"),c(h,a,(function(){return this})),c(h,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=Object(e),r=[];for(var n in t)r.push(n);return r.reverse(),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}return e.done=!0,e}},e.values=C,O.prototype={constructor:O,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(q),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function n(r,n){return i.type="throw",i.arg=e,t.next=r,n&&(t.method="next",t.arg=void 0),!!n}for(var o=this.tryEntries.length-1;o>=0;--o){var a=this.tryEntries[o],i=a.completion;if("root"===a.tryLoc)return n("end");if(a.tryLoc<=this.prev){var l=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(l&&c){if(this.prev<a.catchLoc)return n(a.catchLoc,!0);if(this.prev<a.finallyLoc)return n(a.finallyLoc)}else if(l){if(this.prev<a.catchLoc)return n(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return n(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var a=o;break}}a&&("break"===e||"continue"===e)&&a.tryLoc<=t&&t<=a.finallyLoc&&(a=null);var i=a?a.completion:{};return i.type=e,i.arg=t,a?(this.method="next",this.next=a.finallyLoc,A):this.complete(i)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),A},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),q(r),A}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var n=r.completion;if("throw"===n.type){var o=n.arg;q(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:C(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),A}},e}function fe(e){return function(e){if(Array.isArray(e))return de(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return de(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return de(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function de(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}function pe(e,t,r,n,o,a,i){try{var l=e[a](i),c=l.value}catch(e){return void r(e)}l.done?t(c):Promise.resolve(c).then(n,o)}var ve,ye,he=se.Z.CancelToken,ge=function(e){return function(t){return new Promise((function(r,n){t({type:b,query:e}),r(e)}))}},be=function(){return function(e){return new Promise((function(t,r){ve&&ve(),ye&&ye(),e({type:E}),t()}))}},Ee=function(e){return{type:w,query:e}},we=function(e,t,r){return{type:q,query:e,terms:t,payload:r}},Se=function(e){return{type:O,error:e}},qe=function(e){return{type:j,term:e}},Oe=function(e){return function(){var t,r=(t=me().mark((function t(r){return me().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:r(Ee(e)),ye&&ye(),se.Z.get("".concat($,"&q=").concat(e),{method:"GET",headers:{"Content-Type":"application/json"},cancelToken:new he((function(e){return ye=e}))}).then((function(t){var n=t.data,o=ce(n),a=(o||{}).metaData;if(a&&a.statusCode&&400===a.statusCode){var i=a.displayMessage||a.message;r(Se(i))}else{var l=e,c=Ne(o.autoSuggest)||[],u=o.spellCorrections||[],s=Pe(o.inFieldSuggestion)||[],A=je(o.inFieldSuggestion||[],[l].concat(fe(u),fe(c),fe(s))),m=Pe(o.promotedSuggestion)||[],f=[].concat(fe(u),fe(m),fe(c),fe(s),fe(A));o.spellCorrections=[].concat(fe(u),fe(m),fe(s)),r(we(e,f,o))}})).catch((function(e){se.Z.isCancel(e)||r(Se("An error occurred during the request."))}));case 3:case"end":return t.stop()}}),t)})),function(){var e=this,r=arguments;return new Promise((function(n,o){var a=t.apply(e,r);function i(e){pe(a,n,o,i,l,"next",e)}function l(e){pe(a,n,o,i,l,"throw",e)}i(void 0)}))});return function(e){return r.apply(this,arguments)}}()},Ce=function(e){return function(t){t({type:C}),se.Z.get("".concat(ee,"&q=").concat(e,"&pageSize=5"),{cancelToken:new he((function(e){return ve=e}))}).then((function(r){var n=r.data,o=ce(n)||{};t(function(e,t){return{type:L,term:e,payload:t}}(e,o))})).catch((function(e){se.Z.isCancel(e)||t(function(e){return{type:P,error:e}}(e))}))}},Le=function(e){var t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];return function(r){return r(function(e,t){return{type:I,term:e,initial:t}}(e,t)),ve&&ve(),se.Z.get("".concat(ee),{method:"GET",cancelToken:new he((function(e){return ve=e})),params:{q:e,pageSize:12}}).then((function(t){var n=null==t?void 0:t.data,o=ce(n)||{};0===(o.products||[]).length&&v(e,!1),r(function(e,t){return{type:k,term:e,payload:t}}(e,o))})).catch((function(t){v(e,!1),se.Z.isCancel(t)||r(function(e){return{type:Q,error:e}}(t))}))}},Pe=function(e){return null==e?void 0:e.map((function(e){var t;return null==e||null===(t=e.autosuggest)||void 0===t?void 0:t.trim()}))},Ne=function(e){return null==e?void 0:e.map((function(e){var t;return{term:null==e||null===(t=e.autosuggest)||void 0===t?void 0:t.trim(),product_count:e.result_unbxd_double}}))},je=function(e,t){for(var r=[],n=t.length?t[0]:"",o=0;o<e.length;o++)if(void 0!==e[o].brand_in){var a=e[o].brand_in.filter((function(e){var r=e.trim();return-1!==t.indexOf(r)&&r!==n})).map((function(e){return e.trim()+" "+n}));r=[].concat(fe(r),fe(a))}return r},Ie=function(e){return{type:B,payload:e}},ke=function(){return{type:T}},Qe=function(e){return{type:Z,payload:{loading:e}}},Be=function(e){var t=e.style;return n.createElement("div",{className:"spinner",style:t},n.createElement("div",{className:"bar1"}),n.createElement("div",{className:"bar2"}),n.createElement("div",{className:"bar3"}),n.createElement("div",{className:"bar4"}),n.createElement("div",{className:"bar5"}),n.createElement("div",{className:"bar6"}),n.createElement("div",{className:"bar7"}),n.createElement("div",{className:"bar8"}),n.createElement("div",{className:"bar9"}),n.createElement("div",{className:"bar10"}),n.createElement("div",{className:"bar11"}),n.createElement("div",{className:"bar12"}))},Te=function(e){var t=e.show,r=e.message;return t?n.createElement("div",null,n.createElement("p",null,r)):null},Ze=function(e){var t=e.count;return t>0?n.createElement("div",{className:"product-count"},n.createElement("span",null,t),n.createElement("span",null,t>1?"products":"product")):null};function Ge(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return Je(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return Je(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Je(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var De=function(e){var t=e.categories,r=e.isSearchFullType,o=(0,a.I0)(),i=(0,a.v9)((function(e){return e.suggestions})),l=i.activeTerm,c=i.fetching,u=i.redirects,s=u&&u[l]?u[l]:null,A=Ge((0,n.useState)(),2),m=(A[0],A[1]);(0,n.useEffect)((function(){s&&m(s)}),[s]);return n.createElement(n.Fragment,null,n.createElement("div",{className:"search-autocomplete-categories suggested-keywords"},r?n.createElement(n.Fragment,null,n.createElement(Te,{show:!0,message:"Continue typing to refine search suggestions & results..."}),n.createElement("h5",null,"suggested keywords")):null,t&&t.length?n.createElement("ul",{className:"categories-list"},function(e){return e.map((function(e,t){var a,i=!!l&&(null==l?void 0:l.toLowerCase())==(null==e||null===(a=e.term)||void 0===a?void 0:a.toLowerCase());return n.createElement("li",{key:t,className:"categories-list-item"},n.createElement("a",{className:"category-container ".concat(i?"active":"inactive"),onClick:function(t){return function(e,t){var r;e.preventDefault(),t&&(r="/catalogsearch/result/?q=".concat(encodeURIComponent(t))),o(Qe(!0)),o(qe(t)),v(t),o(ke()),window.location.href=r}(t,null==e?void 0:e.term)}},r?n.createElement(n.Fragment,null,n.createElement("p",null,e.term),n.createElement(Ze,{count:null==e?void 0:e.product_count}),e===l&&c&&n.createElement(Be,{style:{marginLeft:"10px"}})):e.term))}))}(t)):null))},xe=function(e){var t=e.show,r=e.message;return t?n.createElement("div",{className:"instant-search-loader"},n.createElement("img",{src:ae,alt:"loading"}),r&&n.createElement("span",null,r)):null},Fe=function(e){var t=e.showMainLoaderMask;return(0,a.v9)((function(e){return e.showMainLoader}))||t?n.createElement("div",{className:"loading-mask","data-role":"loader"},n.createElement("div",{className:"loader"},n.createElement("img",{alt:"Loading...",src:ae}),n.createElement("p",null,"Please wait..."))):null},Ye=function(e){var t=e.error,r=e.errorMsg,o=e.query,a=r&&r.replace("{{%q}}",o);return t&&o?n.createElement("div",{className:"search-autocomplete-error"},n.createElement("p",null,n.createElement("strong",null,a))):null},We=function(e){var t=e.finalPrice,r=e.currency,o=(0,e.format)(t,!1);return"undefined"!==t?n.createElement("span",{className:"product-price"},n.createElement("span",{className:"currency-symbol"},r),n.createElement("span",{className:"price-main"},Array.isArray(o)?n.createElement(n.Fragment,null,null==o?void 0:o[0],n.createElement("span",{class:"decimal-dot"},"."),n.createElement("span",{class:"price-decimal"},null==o?void 0:o[1])):o)):null},Ue=function(e){var t=e.savings,r=e.type;return r.match(/(not|n't)/gi)||t<=0||r.match(/do not/gi)?null:n.createElement("span",{className:"you-save-statement"},n.createElement("span",{className:"wrap"},function(){var t=e.savings,r=e.currency,o=e.format,a=e.type,i=e.originalPrice,l=e.finalPrice,c=o(t);return a.match(/percentage/gi)?n.createElement(n.Fragment,null,"Save ","",function(e,t){var r=(e-t)/e*100;return Math.floor(r)}(i,l),n.createElement("span",{className:"price-decimal"},"%")):n.createElement(n.Fragment,null,"Save ",n.createElement("span",{className:"currency-symbol"},r),n.createElement("span",{className:"save-price wrap"},Array.isArray(c)?n.createElement(n.Fragment,null,null==c?void 0:c[0],n.createElement("span",{class:"decimal-dot"},"."),n.createElement("span",{class:"you-save-decimal"},null==c?void 0:c[1])):c))}()))},Ke=function(e){var t=e.label;return t&&-1!==t.search(/free/gi)?n.createElement("span",{className:"shipping-label"},"Free"):null};function Re(e){return Re="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Re(e)}function ze(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),r.push.apply(r,n)}return r}function He(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{};t%2?ze(Object(r),!0).forEach((function(t){Xe(e,t,r[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(r)):ze(Object(r)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(r,t))}))}return e}function Xe(e,t,r){return(t=function(e){var t=function(e,t){if("object"!==Re(e)||null===e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!==Re(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"===Re(t)?t:String(t)}(t))in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}var Me=function(e){var t=e.data,r=e.placeholder,o=t.unbxdAmastyLabelTopRight,a=t.unbxdAmastyLabelTopLeft,i=t.unbxdAmastyLabelBottomRight,l=[{label:a,position:"0"},{label:o,position:"2"},{label:t.unbxdAmastyLabelBottomLeft,position:"6"},{label:i,position:"8"}],c=(0,n.useRef)(null),u=function(e){var t,r=/\.(gif|jpe?g|tiff?|png|webp|bmp)$/i;return["labeledImage","thumbnail","smallImage"].forEach((function(n){if(!t&&e[n]&&r.test(e[n]))return t=e[n]})),!t&&e.imageUrl&&e.imageUrl.length&&(t=e.imageUrl[0]),t}(t);return n.createElement("div",{className:"thumb"},n.createElement("img",{src:u,ref:c,onError:function(){c.current&&c.current.src!==r&&(c.current.src=r)}}),l.map((function(e){var t=e.label,r=e.position;if(!t)return null;var o=function(e){var t={position:"absolute",width:"45%",height:"45%",zIndex:"1",backgroundSize:"contain",backgroundRepeat:"no-repeat"},r=s(s({},t),{},{top:0,backgroundPosition:"top"}),n=s(s({},t),{},{bottom:0,backgroundPosition:"bottom"});switch(e){case"0":default:return s(s({},r),{},{left:0});case"2":return s(s({},r),{},{right:0});case"6":return s(s({},n),{},{left:0});case"8":return s(s({},n),{},{right:0})}}(r);return n.createElement("span",{key:t,className:"amasty_label_image",style:He(He({},o),{},{backgroundImage:"url(".concat(t,")")})})})))},Ve=function(e){var t=e.top,r=e.bottom,o=e.template,a=function(e){var t=o.replace("{{label_type}}",e).replace("{{img_type}}","thumbnail");return n.createElement("img",{src:t,alt:"Product Label"})};return t||r?n.createElement("span",{className:"product-labels"},t?n.createElement("span",{className:"product-label label_top"},a(t)):null,r?n.createElement("span",{className:"product-label label_bottom"},a(r)):null):null};function _e(e){return _e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_e(e)}var $e=function(e){var t,r,o,i=e.data,l=e.position,c=(0,a.v9)((function(e){return e.config})),u=(0,a.v9)((function(e){return e.suggestions.activeTerm||e.query})),s=function(e){var t;if(e){var r=null===(t=window)||void 0===t||null===(t=t.unbxdConfig)||void 0===t?void 0:t.storeName;if(!r)return{finalPrice:null==e?void 0:e.price,storeSpecialPrice:null==e?void 0:e.specialPrice,storeOriginalPrice:null==e?void 0:e.originalPrice};var n="Store".concat(oe[r]);return{finalPrice:(null==e?void 0:e["".concat(te).concat(n)])||(null==e?void 0:e[te]),storeSpecialPrice:(null==e?void 0:e["".concat(re).concat(n)])||(null==e?void 0:e[re]),storeOriginalPrice:(null==e?void 0:e["".concat(ne).concat(n)])||(null==e?void 0:e[ne])}}}(i),A=s.finalPrice,m=s.storeSpecialPrice,f=s.storeOriginalPrice,d=(0,a.I0)(),p=function(e,t){t.preventDefault(),d(Qe(!0)),"object"===_e(window.AEC)&&window.AEC.gtm()&&(window.dataLayer=window.dataLayer||[],window.AEC.click(t.currentTarget,window.dataLayer),window.dataLayer.push({event:"unbxdSearchQuery",searchQueryPayload:{query:u}})),window.location=e},v=c.placeholderImgUrl||"",y="bundle"===i.typeId&&m?A/(1-(100-m)/100):f,h=i.sku.endsWith("v")?"configurable":i.typeId,g=!1,b=localStorage["mage-cache-storage"],E="true"!==(null==i||null===(t=i.notForSale)||void 0===t?void 0:t.toLowerCase()),w=!(!y||y===A)&&y-A,S=void 0!==i.price&&!g&&"true"!==(null==i||null===(r=i.notForSale)||void 0===r?void 0:r.toLowerCase()),q="configurable"!==h&&w&&i.showSavedLabel&&!g&&"true"!==(null==i||null===(o=i.notForSale)||void 0===o?void 0:o.toLowerCase());if(void 0!==b){var O=JSON.parse(b);g=O.company&&O.company.has_customer_company}return n.createElement("div",{className:"search-autocomplete-product",role:"options",id:"option-"+i.sku},n.createElement("a",{className:"search-autocomplete-hit",href:i.productUrl,"data-id":i.sku,"data-name":i.title,"data-price":i.price,"data-position":l,"data-brand":i.brand&&i.brand.length?i.brand[0]:"","data-category":"Search Autosuggest","data-list":"Search Autosuggest","data-event":"productClick","data-store":c.storeName||"","data-attributes":"[]",onClick:function(e){return p(i.productUrl,e)}},n.createElement(Me,{data:i,placeholder:v}),n.createElement("div",{className:"info"},n.createElement("div",{role:"title",className:"product-title"},i.title),n.createElement("div",{className:"mpn"},"MPN: ",i.partNo),n.createElement("div",{className:"price-box"},S&&E?n.createElement(We,{currency:c.storeCurrencySymbol||"$",finalPrice:A,format:ue}):null,q&&E?n.createElement(n.Fragment,null,n.createElement(Ue,{currency:c.storeCurrencySymbol||"$",savings:w,finalPrice:A,originalPrice:y,format:ue,type:i.showSavedLabel})):null),n.createElement("div",{className:"product-item-actions"},n.createElement("button",{className:"action todetails primary","data-id":i.sku,"data-name":i.title,"data-price":i.price,"data-quantity":1,"data-position":l,"data-brand":i.brand&&i.brand.length?i.brand[0]:"","data-category":"Search Results","data-list":"Search Results","data-event":"productClick","data-store":c.storeName||"","data-attributes":"[]",onClick:function(e){return p(i.productUrl,ev)}},n.createElement("span",null,"View Details"))),n.createElement("div",{className:"labels-wrapper"},n.createElement(Ke,{label:i.shippingLabel}),c.showProductLabels?n.createElement(Ve,{top:i.labelTop,bottom:i.labelBottom,template:c.labelsUrlTemplate}):null))))},et=function(e){var t=e.products,r=e.activeTerm,o=e.redirect,i=(document.getElementById("search_mini_form"),(0,a.I0)());return t&&t.length>0?n.createElement("div",{className:"search-autocomplete-products"},n.createElement("h5",null,"search results"),r&&n.createElement("div",{className:"search-autocomplete-more".concat(t.length<5?" hidden":"")},n.createElement("a",{className:"button button-primary__outline",onClick:function(){var e="/catalogsearch/result/?q=".concat(encodeURIComponent(r));o&&(e=o),i(Qe(!0)),v(r),window.location.href=e}},"View all results for ",n.createElement("span",null,'"'.concat(r,'"')))),n.createElement("div",null,t.map((function(e,t){return n.createElement($e,{key:t,position:t+1,data:e})})))):null},tt=function(){var e=(0,a.v9)((function(e){return e.corrections})),t=e.terms,r=e.redirects,o=(0,a.I0)();return t&&t.length?n.createElement("div",{className:"search-autocomplete-corrections"},n.createElement("span",{className:"ttl"},"Are you looking for:"),null==t?void 0:t.map((function(e,t){return n.createElement("span",{className:"opt",key:t,onClick:function(t){return function(e,t){t.preventDefault();var n,a=p(e),i=r&&r[a]?r[a]:null;n=i||"/catalogsearch/result/?q=".concat(encodeURIComponent(e)),o(Qe(!0)),v(e),window.location.href=n}(e,t)}},p(e))}))):null},rt=function(e){var t=e.show,r=e.activeTerm,o=(0,a.v9)((function(e){return e})),i=o.isSearching,l=o.corrections,c=l.products,u=l.terms,s=l.isFetchingProducts,A=(0,a.I0)();return(0,n.useEffect)((function(){u&&u[0]&&A(Ce(u[0]))}),[null==u?void 0:u.length,A]),t&&r?n.createElement("div",{className:"search-autocomplete-no-result"},n.createElement("p",null,"No results found for ",n.createElement("strong",null,'"'.concat(r,'"'))),i?null:n.createElement(tt,null),n.createElement("div",{style:{marginTop:"10px"}},n.createElement(xe,{show:s}),n.createElement(et,{products:c}))):null};function nt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return ot(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ot(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ot(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var at=function(){var e=(0,a.v9)((function(e){return e})),t=e.productTerms,r=e.productData,o=e.recentlyViewedProduct,i=e.showDropdown,l=e.activeTerm,c=nt((0,n.useState)([]),2),u=c[0],s=c[1],A=document.getElementById("search"),m=(0,a.I0)(),f=function(){if(null!=t&&t.length){m(Ie(!i));var e=document.querySelector(".col-right");l&&l.length>1?e.style.display="block":e.style.display="none"}},d=function(){var e=document.querySelector(".col-right");A.value.length>1&&(e.style.display="block")},p=function(){m(ke())},v=function(e){"complete"===e.target.readyState&&m(ke())},y=function(){var e=Object.values(o).filter((function(e){var t=r.hasOwnProperty(e.product_id),n=new Date,o=new Date(1e3*e.added_at);return t&&e.scope_id===window.checkout.websiteId&&n-o<36e5})).sort((function(e,t){return t.added_at-e.added_at})).map((function(e){return r[e.product_id]}));e.length>5&&(e.length=5),s(e)};return(0,n.useEffect)((function(){var e,t;return y(),null===(e=window)||void 0===e||e.addEventListener("storage",p,!1),null===(t=window.document)||void 0===t||t.addEventListener("readystatechange",v,!1),function(){var e,t;null===(e=window)||void 0===e||e.removeEventListener("storage",p,!1),null===(t=window.document)||void 0===t||t.removeEventListener("readystatechange",v,!1)}}),[]),(0,n.useEffect)((function(){var e,t;(null==Object||null===(e=Object.keys(o))||void 0===e?void 0:e.length)!==(null==Object||null===(t=Object.keys(o))||void 0===t?void 0:t.length)&&y()}),[o]),(0,n.useEffect)((function(){return null==A||A.addEventListener("click",f,!1),null==A||A.addEventListener("change",d,!1),function(){null==A||A.removeEventListener("click",f,!1),null==A||A.removeEventListener("change",d,!1)}})),n.createElement("div",{className:"recently-viewed-list"},t.length>0&&n.createElement("div",{className:"searched-terms"},n.createElement(Te,{show:!0,message:"Start typing to find all tools for all trades"}),n.createElement("div",{className:"searched"},n.createElement("h5",null,"Recently Searched"),n.createElement("div",{className:"clear-all"},n.createElement("a",{onClick:function(){g("recently_searched_terms",JSON.stringify([])),m(ke())}},"Clear All"))),n.createElement("div",{className:"search-autocomplete-categories"},n.createElement("div",{className:"section"},n.createElement("div",{className:"section-content"},n.createElement("ul",{className:"categories-list"},t&&(null==t?void 0:t.map((function(e){return n.createElement("li",{key:e,className:"categories-list-item"},n.createElement("a",{onClick:function(){return t=e,r=document.querySelector(".col-right"),s([]),A.value=t,window.location.href="/catalogsearch/result/?q=".concat(t),void(r.style.display="none");var t,r},className:"recently-viewed-label"},e),n.createElement("a",{className:"recently-viewed-icons-close",onClick:function(){return r=e,n=t.filter((function(e){return e!==r})),g("recently_searched_terms",JSON.stringify(n)),void m(ke());var r,n}}))})))))))),u.length>0&&n.createElement("div",{className:"recently-viewed-products"},n.createElement("h5",null,"recently viewed products"),n.createElement("div",{className:"recently-viewd-item"},u.map((function(e,t){var r,o=ue(e.price_info.final_price);return n.createElement("div",{key:t,className:"search-autocomplete-product",role:"options",id:"options-".concat(e.id)},n.createElement("a",{className:"search-autocomplete-hit",href:e.url,"data-id":"product_id","data-name":"product.name","data-price":"product.price_info.final_price","data-position":"","data-brand":"","data-category":"","data-list":"","data-event":"","data-store":"","data-attributes":"[]"},n.createElement("div",{className:"thumb"},n.createElement("img",{src:e.extension_attributes.labeled_image||e.images[0].url,alt:e.name})),n.createElement("div",{className:"info"},n.createElement("div",{role:"title",className:"product-title"},e.name),n.createElement("div",{className:"price-block"},!0!==(null==e||null===(r=e.extension_attributes)||void 0===r?void 0:r.not_for_sale)&&n.createElement(n.Fragment,null,n.createElement("span",{className:"product-price"},n.createElement("span",{className:"currency-symbol"},"$"),n.createElement("span",{className:"price-main"},Array.isArray(o)?n.createElement(n.Fragment,null,null==o?void 0:o[0],n.createElement("span",{class:"decimal-dot"},"."),n.createElement("span",{class:"price-decimal"},null==o?void 0:o[1])):o)))),n.createElement("div",{className:"product-item-actions"},n.createElement("a",{className:"action todetails primary","data-quantity":1,"data-category":"Search Results","data-list":"Search Results","data-event":"productClick","data-attributes":"[]"},n.createElement("span",null,"View Details"))),n.createElement("div",{className:"labels-wrapper"},n.createElement(Ke,{label:e.shippingLabel})))))})))))};function it(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return lt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return lt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function lt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ct=document.getElementById("search"),ut=document.getElementById("search_mini_form"),st=ut.querySelector('button[type="submit"]'),At=function(e){var t=e.isFocused,r=e.isSearchFullType,o=e.setShowMainLoaderMask,i=(0,a.v9)((function(e){return e})),l=i.showDropdown,c=i.isSearching,u=i.suggestions,s=u.activeTerm,A=u.fetching,m=u.redirects,f=m&&m[s]?m[s]:null,d=(0,a.I0)(),p=(0,n.useRef)(),y=it((0,n.useState)(""),2),h=y[0],g=y[1];(0,n.useEffect)((function(){var e;t&&(null==p||null===(e=p.current)||void 0===e||e.focus())}),[t]);var b,E,w,S=(b=function(e){!function(e){var t,r,n,o=document.getElementById("minisearch-label"),a=document.getElementById("minisearch-control"),i=document.getElementsByClassName("col-right");ut&&!ut.classList.contains("opened")&&(null==ut||null===(t=ut.classList)||void 0===t||t.add("opened"),null==o||null===(r=o.classList)||void 0===r||r.add("opened"),null==a||null===(n=a.classList)||void 0===n||n.add("opened")),i.length&&(i[0].style.display="none"),d(ge(e));var l=e||"";!l||l.length<2?d(be()):d(be()).then((function(){return d(ge(l))})).then((function(){return d(Oe(l))})).then((function(){return d(qe(l))})).then((function(){return d(Le(l,!0))}))}(e)},E=500,function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];var n=this;clearTimeout(w),w=setTimeout((function(){b.apply(n,t)}),E)}),q=function(e){var t,r=e.target.value,n="";r.match(/<[^>]*?>|<\/[^>]+>|<[^>]*$|^[^<]*>/g)?n=""===(t=r.replace(/<[^>]*?>[^<]*|<\/[^>]+>|<[^>]*$|>[^<]*/g,"")).trim()?"":t.trim():n=r;ct&&(ct.value=n),g(n),S(n)},O=function(e){e.preventDefault(),e.stopPropagation(),h&&(null==h?void 0:h.length)>=2&&(null==o||o(!0),C())},C=function(){var e,t=(null==ct?void 0:ct.value)||h||"";if(t){var r=null==(e=t)?void 0:e.replace(/^\.*/,""),n=encodeURIComponent(r)?"/catalogsearch/result/?q=".concat(encodeURIComponent(r)):"";if(n)return v(r),void(window.location.href=n)}},L=function(e){e.stopPropagation(),e.preventDefault(),s&&!l&&d(Ie(!0))};return(0,n.useEffect)((function(){return null==ct||ct.addEventListener("input",q),function(){ct&&(null==ct||ct.removeEventListener("input",q),ct.removeEventListener("click",L,!1),ut.removeEventListener("submit",O),null==st||st.removeEventListener("click",O))}}),[]),(0,n.useEffect)((function(){ct.addEventListener("click",L)}),[s,l]),(0,n.useEffect)((function(){null==ut||ut.addEventListener("submit",O),null==st||st.addEventListener("click",O)}),[h,c,A,s,f]),r?n.createElement(n.Fragment,null,n.createElement("input",{ref:p,className:"input-text",type:"text",name:"search",value:h,placeholder:"Find your tools...",maxLength:"128",role:"combobox",onChange:q,onClick:function(){var e;null==p||null===(e=p.current)||void 0===e||e.focus()}}),n.createElement("div",{className:"actions"},n.createElement("button",{type:"submit",className:"action search",onClick:function(e){return O(e)}},"search"))):null},mt=document.getElementById("minisearch-control"),ft=document.getElementById("minisearch-label"),dt=function(e){(0,n.useEffect)((function(){return document.addEventListener("mousedown",t,!1),function(){return document.removeEventListener("mousedown",t,!1)}}),[]);var t=function(e){ft&&e.target.contains(ft)&&mt&&mt.classList.toggle("opened")};return n.createElement(At,e)},pt=function(){var e,t=null===(e=window)||void 0===e||null===(e=e.unbxdConfig)||void 0===e?void 0:e.popularSearchTerms,r=null==t?void 0:t.split(",").filter((function(e){return""!==e.trim()})),o=document.getElementById("search");return null!=r&&r.length?n.createElement("div",{className:"trending-search"},n.createElement("h5",null,"Trending Searches"),n.createElement("ul",{className:"categories-list"},null==r?void 0:r.map((function(e){return n.createElement("li",{key:e,className:"categories-list-item"},n.createElement("button",{type:"button",onClick:function(){return t=e,r=document.querySelector(".col-right"),o.value=t,window.location.href="/catalogsearch/result/?q=".concat(t),void(r.style.display="none");var t,r}},e))})))):null};function vt(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var r=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=r){var n,o,a,i,l=[],c=!0,u=!1;try{if(a=(r=r.call(e)).next,0===t){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=a.call(r)).done)&&(l.push(n.value),l.length!==t);c=!0);}catch(e){u=!0,o=e}finally{try{if(!c&&null!=r.return&&(i=r.return(),Object(i)!==i))return}finally{if(u)throw o}}return l}}(e,t)||function(e,t){if(!e)return;if("string"==typeof e)return yt(e,t);var r=Object.prototype.toString.call(e).slice(8,-1);"Object"===r&&e.constructor&&(r=e.constructor.name);if("Map"===r||"Set"===r)return Array.from(e);if("Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return yt(e,t)}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yt(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var ht=document.getElementById("search_mini_form"),gt=function(){var e=(0,n.useRef)(),t=document.getElementById("minisearch-control"),r=document.getElementById("search-main"),o=document.body,i=vt((0,n.useState)([]),2),l=i[0],c=i[1],u=vt((0,n.useState)(!1),2),s=u[0],A=u[1],m=vt((0,n.useState)(!1),2),f=m[0],d=m[1],y=vt((0,n.useState)(!1),2),h=y[0],g=y[1],b=vt((0,n.useState)(""),2),E=b[0],w=b[1],q=window.innerWidth>767,O=(0,a.I0)(),C=(0,a.v9)((function(e){return e})),L=C.query,P=C.isSearching,j=C.noResult,I=C.error,k=C.errorMsg,Q=C.productTerms,B=C.suggestions,T=C.corrections.terms,Z=B.isInitialFetch,G=B.fetching,J=B.terms,D=B.activeTerm,x=B.redirects,F=B.products,Y=null==L?void 0:L.slice(0,-1),W=p(Y),U=function(){var e,n,a;h||g(!0),null==t||null===(e=t.classList)||void 0===e||e.add("active"),null==r||null===(n=r.classList)||void 0===n||n.add("active"),null==o||null===(a=o.classList)||void 0===a||a.add("search-is-active")},K=(0,n.useCallback)((function(e){if(console.log("handlePressEnter , Called Focus OUtInput"),13===(null==e?void 0:e.keyCode)){var n="/catalogsearch/result/?q=".concat(encodeURIComponent(null==e?void 0:e.target.value));if(E&&(n=E),n){var o,a;if(q)null==t||null===(o=t.classList)||void 0===o||o.remove("active"),null==r||null===(a=r.classList)||void 0===a||a.remove("active");g(!1),O(Qe(!0)),v(null==e?void 0:e.target.value),window.location.href=n}}}),[q,E]);(0,n.useEffect)((function(){var e,t=document.getElementById("search");return null==t||t.addEventListener("focus",U),null==t||t.addEventListener("keyup",K),document.addEventListener("mousedown",R,!1),null==o||null===(e=o.classList)||void 0===e||e.add("search-type-full"),function(){var e;t&&(null==t||t.removeEventListener("focus",U),null==t||t.removeEventListener("keyup",K)),document.removeEventListener("mousedown",R,!1),null==o||null===(e=o.classList)||void 0===e||e.remove("search-type-full")}}),[]),(0,n.useEffect)((function(){var e=E?F[W]:F[D];d(!1),D&&!G&&(null!=e&&e.length||null!=T&&T.length)&&(d(!0),c(e))}),[G,F,D,Z,null==T?void 0:T.length,J.length]);var R=function(n){var a,i,l;ht&&e&&e.current&&!ht.contains(n.target)&&!e.current.contains(n.target)&&(g(!1),null==t||null===(a=t.classList)||void 0===a||a.remove("active"),null==r||null===(i=r.classList)||void 0===i||i.remove("active"),null==o||null===(l=o.classList)||void 0===l||l.remove("search-is-active"))},z=x&&x[D]?x[D]:null,H=!(P||G||J.length||L),X=J.length>0||(null==Q?void 0:Q.length)>0;return(0,n.useEffect)((function(){w(z||"")}),[D,z]),(0,n.useEffect)((function(){E&&O(function(e){return function(t){return new Promise((function(r,n){t(Le(e)).then((function(n){t({type:S,query:e,payload:{products:n}}),r(n)})).catch((function(e){console.error("Error fetching products:",e),n(e)}))}))}}(Y))}),[E]),n.createElement("div",{className:"new-search-autocomplete search-autocomplete-dropdown ".concat(h?"open":""),ref:e},n.createElement("div",{className:"close-btn-wrap"},n.createElement("button",{className:"close-search-btn",type:"button",onClick:function(e){return(n=e).preventDefault(),n.stopPropagation(),g(!1),null==t||null===(a=t.classList)||void 0===a||a.remove("active"),null==r||null===(i=r.classList)||void 0===i||i.remove("active"),void(null==o||null===(l=o.classList)||void 0===l||l.remove("search-is-active"));var n,a,i,l}},"close")),n.createElement("div",{className:"search-wrapper"},n.createElement(dt,{isFocused:h,setShowMainLoaderMask:A,isSearchFullType:!0})),n.createElement(Fe,{showMainLoaderMask:s}),n.createElement("div",{className:"search-autocomplete-wrapper  search-result"},n.createElement("div",{className:"col-left","aria-busy":P},!X&&!D&&n.createElement("div",{className:"search-autocomplete-categories suggested-keywords"},n.createElement(Te,{show:!0,message:"Start typing to find all tools for all trades"}),n.createElement("div",{className:"searched"},n.createElement("h5",null,"Recently Searched"),n.createElement("p",null,"No recent search"))),H&&n.createElement(n.Fragment,null,n.createElement(at,null),n.createElement(pt,null)),null!=J&&J.length?n.createElement(De,{categories:J,isSearchFullType:!0}):null,n.createElement(xe,{show:P})),n.createElement("div",{className:"col-right ".concat(D?"show":""," ").concat(F&&F[D]?"search-result":""),"aria-busy":G,style:{display:f?"block":"none"}},n.createElement("span",{className:"overlay",onClick:function(){return O(function(e){return{type:N,query:e}}())}}),n.createElement(rt,{show:j,query:L,activeTerm:D}),n.createElement(Ye,{error:I,query:L,errorMsg:k}),j?null:n.createElement(et,{products:l,activeTerm:D,redirects:x,redirect:z}))))},bt=function(){return n.createElement(gt,null)},Et=function(){return n.createElement(n.Fragment,null,n.createElement(bt,null))},wt=function(e){return(0,l.MT)(H,e,X((0,l.md)(i.Z)))}(),St=function(){return n.createElement(Et,null)},qt=o.createRoot(document.getElementById("search-autocomplete-container"));qt&&qt.render(n.createElement(a.zt,{store:wt},n.createElement(St,null)))}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.m=t,e=[],n.O=function(t,r,o,a){if(!r){var i=1/0;for(s=0;s<e.length;s++){r=e[s][0],o=e[s][1],a=e[s][2];for(var l=!0,c=0;c<r.length;c++)(!1&a||i>=a)&&Object.keys(n.O).every((function(e){return n.O[e](r[c])}))?r.splice(c--,1):(l=!1,a<i&&(i=a));if(l){e.splice(s--,1);var u=o();void 0!==u&&(t=u)}}return t}a=a||0;for(var s=e.length;s>0&&e[s-1][2]>a;s--)e[s]=e[s-1];e[s]=[r,o,a]},n.d=function(e,t){for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},function(){var e={413:0};n.O.j=function(t){return 0===e[t]};var t=function(t,r){var o,a,i=r[0],l=r[1],c=r[2],u=0;if(i.some((function(t){return 0!==e[t]}))){for(o in l)n.o(l,o)&&(n.m[o]=l[o]);if(c)var s=c(n)}for(t&&t(r);u<i.length;u++)a=i[u],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0;return n.O(s)},r=self.webpackChunkunbxd_search_app=self.webpackChunkunbxd_search_app||[];r.forEach(t.bind(null,0)),r.push=t.bind(null,r.push.bind(r))}();var o=n.O(void 0,[425],(function(){return n(124)}));o=n.O(o)}();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiYXV0b2NvbXBsZXRlLm1pbi5qcyIsIm1hcHBpbmdzIjoiOzZCQUFJQSxrMEVDTUcsSUFBTUMsRUFBYyxTQUFDQyxHQUMxQixPQUFPQSxFQUFJQyxRQUFRLGlCQUFrQixLQUFLQyxhQUM1QyxFQW1DYUMsRUFBYSxTQUFDQyxHQUN6QixNQUFzQixpQkFBUkEsRUFDVkEsRUFDR0MsTUFBTSxLQUNOQyxLQUFJLFNBQUNDLEdBQUksT0FBS0EsRUFBS0MsT0FBTyxHQUFHQyxjQUFnQkYsRUFBS0csTUFBTSxFQUFFLElBQzFEQyxLQUFLLEtBQ1JQLENBQ04sRUEyQk8sSUFBTVEsRUFBeUIsV0FBK0IsSUFBOUJDLEVBQUlDLFVBQUFDLE9BQUEsUUFBQUMsSUFBQUYsVUFBQSxHQUFBQSxVQUFBLEdBQUcsR0FBSUcsSUFBT0gsVUFBQUMsT0FBQSxRQUFBQyxJQUFBRixVQUFBLEtBQUFBLFVBQUEsR0FDakRJLEVBQVEsMEJBRVZDLEVBQWNDLEtBQUtDLE1BQU1DLGFBQWFDLFFBQVFMLElBQVUsTUFDNUQsSUFBS0wsR0F6QlAsU0FBaUNXLEVBQU9DLEdBQ3RDLEdBQUtDLE1BQU1DLFFBQVFILEdBQW5CLENBS0EsSUFFMkJJLEVBRnJCQyxFQUFpQkosRUFBTXZCLGNBQWM0QixFQUFBQyxFQUVyQlAsR0FBSyxJQUEzQixJQUFBTSxFQUFBRSxNQUFBSixFQUFBRSxFQUFBRyxLQUFBQyxNQUNFLEdBRGdCTixFQUFBSCxNQUNKdkIsZ0JBQWtCMkIsRUFDNUIsT0FBTyxDQUVWLE9BQUFNLEdBQUFMLEVBQUFNLEVBQUFELEVBQUEsU0FBQUwsRUFBQU8sR0FBQSxDQUVELE9BQU8sQ0FYUCxDQVlGLENBVWVDLENBQXdCbkIsRUFBYU4sR0FDaEQsT0FBTyxFQUdMSSxFQUNHRSxFQUFZb0IsU0FBUzFCLElBQ3hCTSxFQUFZcUIsUUFBUTNCLEdBR3RCTSxFQUFjQSxFQUFZc0IsUUFBTyxTQUFDQyxHQUFDLE9BQUtBLElBQU03QixDQUFJLElBR2hETSxFQUFZSixPQUFTLEdBQ3ZCSSxFQUFZd0IsTUFFZHJCLGFBQWFzQixRQUFRMUIsRUFBT0UsS0FBS3lCLFVBQVUxQixHQUM3QyxFQUVhMkIsRUFBK0IsV0FDMUMsSUFBSUMsRUFBZUMsT0FBTzFCLGFBQWFDLFFBQVEsMkJBQzNDMEIsRUFBY0QsT0FBTzFCLGFBQWFDLFFBQVEsd0JBQzFDMkIsRUFBd0JGLE9BQU8xQixhQUFhQyxRQUFRLDJCQUV4RCxNQUFPLENBQ0x3QixhQUFjSSxFQUFPSixHQUFnQjNCLEtBQUtDLE1BQU0sQ0FBQzBCLElBQWlCLEdBQ2xFRSxZQUFhRSxFQUFPRixHQUFlN0IsS0FBS0MsTUFBTSxDQUFDNEIsSUFBZ0IsQ0FBQyxFQUNoRUMsc0JBQXVCQyxFQUFPRCxHQUF5QjlCLEtBQUtDLE1BQU0sQ0FBQzZCLElBQTBCLENBQUMsRUFFbEcsRUFFYUMsRUFBUyxTQUFDL0MsR0FDckIsSUFDRSxPQUFPZ0IsS0FBS0MsTUFBTWpCLE1BQVVBLENBQzlCLENBQUUsTUFBT2dDLEdBQ1AsT0FBTyxDQUNULENBQ0YsRUFFYWdCLEVBQWtCLFNBQUNwRCxFQUFLeUIsR0FDbkN1QixPQUFPMUIsYUFBYXNCLFFBQVE1QyxFQUFLeUIsRUFDbkMsRUN6SGE0QixFQUFlLGVBQ2ZDLEVBQWUsZUFDZkMsRUFBaUIsaUJBQ2pCQyxFQUFrQixrQkFDbEJDLEVBQWdCLGdCQUNoQkMsRUFBc0Isc0JBQ3RCQyxFQUE2Qiw2QkFDN0JDLEVBQTRCLDRCQUM1QkMsRUFBMkIsMkJBRTNCQyxFQUFpQixpQkFFakJDLEVBQWdCLGdCQUNoQkMsRUFBa0Isa0JBQ2xCQyxFQUFrQixrQkFDbEJDLEVBQWtCLGtCQUNsQkMsRUFBa0Isa0JBQ2xCQyxFQUFxQixxQkFDckJDLEVBQXFCLHMvRENNbEMsUUFBTUMsRUFBaUJ4QixJQUVqQnlCLEVBQW1CLFNBQUNDLEdBQ3hCLElBQU1DLEVBQWMsSUFBSUMsSUFDeEIsT0FBT0YsYUFBRyxFQUFIQSxFQUFLL0IsUUFBTyxTQUFDa0MsR0FDbEIsSUFBTTlELEVBQU84RCxhQUFJLEVBQUpBLEVBQU05RCxLQUFLWCxjQUNsQjBFLEVBQWNILEVBQVlJLElBQUloRSxHQUVwQyxPQURBNEQsRUFBWUssSUFBSWpFLElBQ1IrRCxDQUNWLEdBQ0YsRUFHTUcsRUFBWUMsRUFBQSxDQUNoQkMsZ0JBQWdCLEVBQ2hCQyxhQUFhLEVBQ2JDLGNBQWMsRUFDZEMsVUFBVSxFQUNWQyxNQUFPLEtBQ1BDLFNBQVUsR0FDVkMsY0FBZSxFQUNmQyxZQUFhLENBQ1hDLGdCQUFnQixFQUNoQkMsVUFBVSxFQUNWQyxXQUFZLEtBQ1o5RSxLQUFNLEtBQ04rRSxNQUFPLEdBQ1BOLFNBQVUsQ0FBQyxFQUNYTyxVQUFXLENBQUMsRUFDWkMsT0FBTyxHQUVUQyxZQUFhLENBQ1hILE1BQU8sR0FDUE4sU0FBVSxHQUNWVSxvQkFBb0IsRUFDcEJDLHlCQUEwQixLQUMxQkosVUFBVyxDQUFDLEdBRWRLLE9BQVFsRCxPQUFPbUQsYUFBZSxDQUFDLEVBQy9CTCxPQUFPLEVBQ1BNLFNBQVUsTUFDUDlCLEdBa01MLEVBL0xvQixXQUFrQyxJQUFBK0IsRUFBQUMsRUFBQUMsRUFBakNDLEVBQUsxRixVQUFBQyxPQUFBLFFBQUFDLElBQUFGLFVBQUEsR0FBQUEsVUFBQSxHQUFHaUUsRUFBYzBCLEVBQU0zRixVQUFBQyxPQUFBLEVBQUFELFVBQUEsUUFBQUUsRUFDM0NzRSxFQUFXLENBQUMsRUFDWm9CLEVBQWlCLEdBQ2pCQyxFQUF3QixHQUM1QixPQUFRRixFQUFPRyxNQUNiLEtBQUt0RCxFQUNILElBQU11RCxFQUFxQi9ELElBQzNCLE9BQU9nRSxPQUFPQyxPQUFPLENBQUMsRUFBQy9CLEVBQUFBLEVBQUEsR0FBT0QsR0FBaUI4QixJQUNqRCxLQUFLeEQsRUFDSCxPQUFPeUQsT0FBT0MsT0FBTyxDQUFDLEVBQUdQLEVBQU8sQ0FDOUJ0QixhQUFhLEVBQ2JHLE1BQU9vQixFQUFPcEIsTUFDZFMsT0FBTyxFQUNQTSxTQUFVLEtBQ1ZoQixVQUFVLElBRWQsS0FBSzdCLEVBQ0gsT0FBT3VELE9BQU9DLE9BQU8sQ0FBQyxFQUFHUCxFQUFPLENBQzlCdEIsYUFBYSxFQUNiRyxNQUFPb0IsRUFBT3BCLE1BQ2RGLGNBQWMsSUFFbEIsSURqRjZCLG9CQ2tGM0IsT0FBTzJCLE9BQU9DLE9BQU8sQ0FBQyxFQUFHUCxFQUFPLENBQzlCaEIsWUFBV1IsRUFBQUEsRUFBQSxHQUNOd0IsRUFBTWhCLGFBQVcsSUFDcEJHLFdBQVksU0FHbEIsS0FBS2xDLEVBQ0gsSUFBTXVELEVBQVFDLEVBQU9SLEVBQU9iLE9BQzVCLFFBQUksSUFBdUJhLEVBQU9TLFFBQVE1QixTQUV4Q0EsRUFEVXZGLEVBQVkwRyxFQUFPcEIsUUFDYm9CLEVBQU9TLFFBQVE1QixTQUdqQyxJQUFNNkIsRUFBbUI1QyxFQUFpQixHQUFENkMsT0FBQUgsRUFBc0IsUUFBdEJaLEVBQUtHLEVBQU1oQixtQkFBVyxJQUFBYSxPQUFBLEVBQWpCQSxFQUFtQlQsT0FBS3FCLEVBQUtELEtBQ3JFSyxFQUFrQjlDLEVBQWlCLEdBQUQ2QyxPQUFBSCxFQUFLVCxTQUFrQixRQUFiRixFQUFMRSxFQUFPVCxtQkFBVyxJQUFBTyxPQUFBLEVBQWxCQSxFQUFvQlYsT0FBS3FCLEVBQUtSLEVBQU9TLFFBQVFJLG9CQUUxRixPQUFPUixPQUFPQyxPQUFPLENBQUMsRUFBR1AsRUFBTyxDQUM5QnRCLGFBQWEsRUFDYkMsY0FBYyxFQUNkSyxZQUFXUixFQUFBQSxFQUFBLEdBQ053QixFQUFNaEIsYUFBVyxJQUNwQkksTUFBT3VCLElBRVRwQixZQUFXZixFQUFBQSxFQUFBLEdBQ053QixFQUFNVCxhQUFXLElBQ3BCSCxNQUFPeUIsTUFJYixLQUFLN0QsRUFDSCxRQUFJLElBQXVCaUQsRUFBT1MsUUFBUTVCLFNBRXhDQSxFQURVdkYsRUFBWTBHLEVBQU9wQixRQUNib0IsRUFBT1MsUUFBUTVCLFNBR25DLEtBQUs1QixFQUNILE9BQU9vRCxPQUFPQyxPQUFPLENBQUMsRUFBR1AsRUFBTyxDQUM5QnRCLGFBQWEsRUFDYlksT0FBTyxFQUNQVixVQUFVLEVBQ1ZnQixTQUFVSyxFQUFPWCxNQUNqQlIsU0FBVSxLQUVkLEtBQUt4QixFQUNILE9BQU9nRCxPQUFPQyxPQUFPLENBQUMsRUFBR1AsRUFBTyxDQUM5QmxCLFNBQVUsR0FDVkUsWUFBV1IsRUFBQUEsRUFBQSxHQUNOd0IsRUFBTWhCLGFBQVcsSUFDcEJHLFdBQVksU0FHbEIsS0FBSzVCLEVBR0gsT0FGQTRDLEVBQXdCeEcsRUFBV3NHLEVBQU81RixNQUMxQzZGLEVBQWlCRixFQUFNaEIsWUFBWUYsU0FBU3FCLElBQTBCLEdBQy9ERyxPQUFPQyxPQUFPLENBQUMsRUFBR1AsRUFBTyxDQUM5QnBCLFVBQVdzQixFQUFlM0YsT0FDMUJ5RSxZQUFXUixFQUFBQSxFQUFBLEdBQ053QixFQUFNaEIsYUFBVyxJQUNwQkMsZ0JBQWdCLEVBQ2hCRSxXQUFZZ0IsTUFHbEIsS0FBSzNDLEVBQ0gsT0FBTzhDLE9BQU9DLE9BQU8sQ0FBQyxFQUFHUCxFQUFPLENBQzlCcEIsVUFBVSxFQUNWSSxZQUFXUixFQUFBQSxFQUFBLEdBQ053QixFQUFNaEIsYUFBVyxJQUNwQkMsZUFBZ0JnQixFQUFPYyxRQUN2QjdCLFVBQVUsTUFHaEIsS0FBS3pCLEVBQ0gsSUFBSXVELEVBQUlmLEVBQU9TLFFBQ2JyQixFQUFZVyxFQUFNaEIsWUFBWUssV0FBYSxDQUFDLEVBQ3hDNEIsRUFBa0J0SCxFQUFXc0csRUFBTzVGLE1BQ3BDNkcsRUFBa0J2SCxFQUFXcUcsRUFBTW5CLE9BQ3pDQyxFQUFTbUMsR0FBbUJELEVBQUVsQyxTQUMxQmtDLEVBQUVsQyxTQUFTdkUsU0FDYnVFLEVBQVNtQyxHQUFtQkQsRUFBRWxDLFVBR2hDLElBQU1xQyxFQUFrQnBELEVBQWlCLENBQ3ZDa0QsSUFBb0JDLEVBQWtCLENBQUU3RyxLQUFNNEcsRUFBaUJHLGNBQWVKLEVBQUVLLG9CQUF1QixDQUFDaEgsS0FBTTZHLElBQWdCTixPQUFBSCxFQUMzSFQsU0FBa0IsUUFBYkQsRUFBTEMsRUFBT2hCLG1CQUFXLElBQUFlLE9BQUEsRUFBbEJBLEVBQW9CWCxTQVF6QixPQUxJNEIsRUFBRU0sWUFDSmpDLEVBQVksQ0FBQyxHQUNINEIsR0FBbUJELEVBQUVNLFVBRzFCaEIsT0FBT0MsT0FBTyxDQUFDLEVBQUdQLEVBQU8sQ0FDOUJwQixTQUFVb0MsRUFBRWxDLFNBQVN2RSxPQUFTLElBQU15RixFQUFNaEIsWUFBWUYsU0FBU21DLEdBQy9EakMsWUFBV1IsRUFBQUEsRUFBQSxHQUNOd0IsRUFBTWhCLGFBQVcsSUFDcEJFLFVBQVUsRUFDVkUsTUFBTytCLEVBQ1ByQyxTQUFRTixFQUFBQSxFQUFBLEdBQ0h3QixFQUFNaEIsWUFBWUYsVUFBUXlDLEVBQUEsR0FDdkJOLEVBQWtCRCxFQUFFbEMsV0FFNUJPLFVBQUFBLElBRUZFLFlBQVdmLEVBQUFBLEVBQUEsR0FDTndCLEVBQU1ULGFBQVcsSUFDcEJILE1BQU80QixFQUFFRixrQkFBb0IsT0FHbkMsS0FBSzFELEVBQ0gsSUFBTW9FLEVBQW1CdkIsRUFBT1MsUUFBUWUsU0FBU0MsWUFBWUMsRUFDdkRDLEVBQWUzQixFQUFPUyxRQUFRWSxTQUNoQ08sRUFBZSxDQUFDLEVBQ2hCRCxJQUNGQyxFQUFhbEksRUFBVzZILElBQXFCSSxHQUUvQyxJQUFrQkUsRUFBc0I3QixFQUFPUyxRQUF2QzVCLFNBQ1IsT0FBT3dCLE9BQU9DLE9BQU8sQ0FBQyxFQUFHUCxFQUFPLENBQzlCVCxZQUFXZixFQUFBQSxFQUFBLEdBQ053QixFQUFNVCxhQUFXLElBQ3BCQyxvQkFBb0IsRUFDcEJWLFNBQVVnRCxHQUFxQixDQUFDLEVBQ2hDekMsVUFBV3dDLE1BR2pCLEtBQUsxRSxFQUNILE9BQU9tRCxPQUFPQyxPQUFPLENBQUMsRUFBR1AsRUFBTyxDQUM5QlQsWUFBV2YsRUFBQUEsRUFBQSxHQUNOd0IsRUFBTVQsYUFBVyxJQUNwQkMsb0JBQW9CLE1BRzFCLEtBQUtuQyxFQUNILE9BQU9pRCxPQUFPQyxPQUFPLENBQUMsRUFBR1AsRUFBTyxDQUM5QlQsWUFBV2YsRUFBQUEsRUFBQSxHQUNOd0IsRUFBTVQsYUFBVyxJQUNwQkMsb0JBQW9CLEVBQ3BCQyx5QkFBMEJRLEVBQU9YLFVBR3ZDLEtBQUs1QixFQUNILE9BQU80QyxPQUFPQyxPQUFPLENBQUMsRUFBR1AsRUFBTyxDQUM5QnJCLGNBQWMsRUFDZEssWUFBV1IsRUFBQUEsRUFBQSxHQUNOd0IsRUFBTWhCLGFBQVcsSUFDcEJNLE1BQU9XLEVBQU9YLFVBR3BCLEtBQUszQixFQUNILE9BQU8yQyxPQUFPQyxPQUFPLENBQUMsRUFBR1AsRUFBTyxDQUM5QnJCLGFBQWNvRCxRQUFROUIsRUFBT1MsV0FFakMsS0FBSzlDLEVBQ0gsSUFBTW9FLEVBQWtCMUYsSUFDeEIsT0FBT2dFLE9BQU9DLE9BQU8sQ0FBQyxFQUFHUCxFQUFLeEIsRUFBQSxHQUN6QndELElBR1AsS0FBS25FLEVBQW9CLElBQUFvRSxFQUNqQkMsRUFBUTFELEVBQUEsR0FBUXdCLEdBQ2hCbUMsRUFBVWxDLFNBQWUsUUFBVGdDLEVBQU5oQyxFQUFRUyxlQUFPLElBQUF1QixPQUFBLEVBQWZBLEVBQWlCRSxRQUNqQyxPQUFBM0QsRUFBQUEsRUFBQSxHQUFZMEQsR0FBUSxJQUFFekQsZUFBZ0IwRCxJQUd4QyxRQUNFLE9BQU9uQyxFQUViLEVDMVBNb0MsRUFBMkM1RixPQUFPNkYsc0NBQXdDQyxFQUFBQSx3cUJDUGhHLElBQU0zQyxFQUFvQixRQUFUNEMsRUFBRy9GLGNBQU0sSUFBQStGLE9BQUEsRUFBTkEsRUFBUTVDLFlBQ2Y2QyxFQUF1QjdDLGFBQVcsRUFBWEEsRUFBYThDLGdCQUVwQ0MsSUFEc0IvQyxTQUFBQSxFQUFhZ0QsZ0JBQ2xCaEQsYUFBVyxFQUFYQSxFQUFhaUQsV0FNOUJDLEdBQWMsUUFDZEMsR0FBc0IsZUFDdEJDLEdBQXVCLGdCQUN2QkMsSUFBV3pCLEVBQUEwQixFQUFHLENBQUMsRUFQTixxQkFRSCxHQUFDMUIsRUFBQTBCLEVBUEssd0JBUUgsR0FBQzFCLEVBQUEwQixFQVBDLDZCQVFILEdBQUMxQixFQUFBMEIsRUFQQywrQkFRSCxHQUFDQSxHQ2hCUkMsR0FDWCwrellDRUssSUFBTUMsR0FBNkIsU0FBQ3pDLEdBQVksSUFBQTBDLEVBQUFDLEVBQy9DQyxFQUFjNUMsU0FBaUIsUUFBVjBDLEVBQVAxQyxFQUFTNkMsZ0JBQVEsSUFBQUgsT0FBQSxFQUFqQkEsRUFBbUJ0RSxTQUNqQzJDLEVBQVdmLGFBQU8sRUFBUEEsRUFBUzhDLGVBQ3BCMUMsR0FBbUJKLGFBQU8sRUFBUEEsRUFBUytDLGFBQWMsQ0FBQy9DLGFBQU8sRUFBUEEsRUFBUytDLFdBQVcsR0FBR0MsWUFDbEU1RSxFQUFXd0UsYUFBVyxFQUFYQSxFQUFhckgsUUFBTyxTQUFDMEgsR0FBSSxPQUFLQSxFQUFLQyxlQUFlLFFBQVVELENBQUksSUFDM0V0QyxFQUFxQlgsRUFBUTZDLFNBQVNNLGtCQUFvQixFQUMxREMsRUFBY1IsYUFBVyxFQUFYQSxFQUFhckgsUUFBTyxTQUFDMEgsR0FBSSxNRE5iLHdCQ01rQkEsYUFBSSxFQUFKQSxFQUFNSSxVQUFrQ0osQ0FBSSxJQUN4RkssRUFBcUJWLGFBQVcsRUFBWEEsRUFBYXJILFFBQU8sU0FBQzBILEdBQUksTURMcEIsd0JDS3lCQSxhQUFJLEVBQUpBLEVBQU1JLFVBQWtDSixDQUFJLElBQy9GTSxFQUFxQlgsYUFBVyxFQUFYQSxFQUFhckgsUUFBTyxTQUFDMEgsR0FBSSxNRFA5QixjQ09tQ0EsYUFBSSxFQUFKQSxFQUFNSSxVQUF3QkosQ0FBSSxJQUNyRnJDLEVBQVdaLFNBQWlCLFFBQVYyQyxFQUFQM0MsRUFBU1ksZ0JBQVEsSUFBQStCLE9BQUEsRUFBakJBLEVBQW1CcEksTUFDcEMsTUFBTyxDQUNMd0csU0FBQUEsRUFDQTNDLFNBQUFBLEVBQ0F1QyxtQkFBQUEsRUFDQXlDLFlBQWEsR0FBRmxELE9BQUFILEdBQU11RCxHQUFrQnZELEdBQUtxRCxJQUN4Q0csbUJBQUFBLEVBQ0FuRCxpQkFBQUEsRUFDQVEsU0FBQUEsRUFFSixFQWdCYTRDLEdBQWMsV0FBZSxJQUFkQyxFQUFLN0osVUFBQUMsT0FBQSxRQUFBQyxJQUFBRixVQUFBLEdBQUFBLFVBQUEsR0FBRyxFQUM5QjhKLEVBQVdELEVBQU1FLFFBQVEsR0FBR0MsV0FFaEMsSUFBK0IsSUFBM0JGLEVBQVNHLFFBQVEsS0FDbkIsT0FBT0osRUFHVCxJQUFJSyxFQUFXSixFQUFTdkssTUFBTSxLQVE5QixPQUxFc0ssRUFERUssRUFBU2pLLE9BQVMsR0FBcUIsT0FBaEJpSyxFQUFTLEdBQzFCQSxFQUVBQSxFQUFTLEVBSXJCLG1RQ3JEQUMsR0FBQSxrQkFBQUMsQ0FBQSxNQUFBQSxFQUFBLEdBQUFDLEVBQUFyRSxPQUFBc0UsVUFBQUMsRUFBQUYsRUFBQWYsZUFBQWtCLEVBQUF4RSxPQUFBd0UsZ0JBQUEsU0FBQUMsRUFBQXZMLEVBQUF3TCxHQUFBRCxFQUFBdkwsR0FBQXdMLEVBQUEvSixLQUFBLEVBQUFnSyxFQUFBLG1CQUFBQyxPQUFBQSxPQUFBLEdBQUFDLEVBQUFGLEVBQUFHLFVBQUEsYUFBQUMsRUFBQUosRUFBQUssZUFBQSxrQkFBQUMsRUFBQU4sRUFBQU8sYUFBQSx5QkFBQUMsRUFBQVYsRUFBQXZMLEVBQUF5QixHQUFBLE9BQUFxRixPQUFBd0UsZUFBQUMsRUFBQXZMLEVBQUEsQ0FBQXlCLE1BQUFBLEVBQUF5SyxZQUFBLEVBQUFDLGNBQUEsRUFBQUMsVUFBQSxJQUFBYixFQUFBdkwsRUFBQSxLQUFBaU0sRUFBQSxhQUFBOUosR0FBQThKLEVBQUEsU0FBQVYsRUFBQXZMLEVBQUF5QixHQUFBLE9BQUE4SixFQUFBdkwsR0FBQXlCLENBQUEsV0FBQTRLLEVBQUFDLEVBQUFDLEVBQUFDLEVBQUFDLEdBQUEsSUFBQUMsRUFBQUgsR0FBQUEsRUFBQW5CLHFCQUFBdUIsRUFBQUosRUFBQUksRUFBQUMsRUFBQTlGLE9BQUErRixPQUFBSCxFQUFBdEIsV0FBQTBCLEVBQUEsSUFBQUMsRUFBQU4sR0FBQSxXQUFBbkIsRUFBQXNCLEVBQUEsV0FBQW5MLE1BQUF1TCxFQUFBVixFQUFBRSxFQUFBTSxLQUFBRixDQUFBLFVBQUFLLEVBQUFDLEVBQUEzQixFQUFBNEIsR0FBQSxXQUFBdkcsS0FBQSxTQUFBdUcsSUFBQUQsRUFBQUUsS0FBQTdCLEVBQUE0QixHQUFBLE9BQUFoTCxHQUFBLE9BQUF5RSxLQUFBLFFBQUF1RyxJQUFBaEwsRUFBQSxFQUFBK0ksRUFBQW1CLEtBQUFBLEVBQUEsSUFBQWdCLEVBQUEsWUFBQVYsSUFBQSxVQUFBVyxJQUFBLFVBQUFDLElBQUEsS0FBQUMsRUFBQSxHQUFBdkIsRUFBQXVCLEVBQUE3QixHQUFBLDhCQUFBOEIsRUFBQTNHLE9BQUE0RyxlQUFBQyxFQUFBRixHQUFBQSxFQUFBQSxFQUFBRyxFQUFBLE1BQUFELEdBQUFBLElBQUF4QyxHQUFBRSxFQUFBK0IsS0FBQU8sRUFBQWhDLEtBQUE2QixFQUFBRyxHQUFBLElBQUFFLEVBQUFOLEVBQUFuQyxVQUFBdUIsRUFBQXZCLFVBQUF0RSxPQUFBK0YsT0FBQVcsR0FBQSxTQUFBTSxFQUFBMUMsR0FBQSwwQkFBQTJDLFNBQUEsU0FBQUMsR0FBQS9CLEVBQUFiLEVBQUE0QyxHQUFBLFNBQUFiLEdBQUEsWUFBQWMsUUFBQUQsRUFBQWIsRUFBQSxnQkFBQWUsRUFBQXRCLEVBQUF1QixHQUFBLFNBQUFDLEVBQUFKLEVBQUFiLEVBQUFrQixFQUFBQyxHQUFBLElBQUFDLEVBQUF0QixFQUFBTCxFQUFBb0IsR0FBQXBCLEVBQUFPLEdBQUEsYUFBQW9CLEVBQUEzSCxLQUFBLEtBQUE0SCxFQUFBRCxFQUFBcEIsSUFBQTFMLEVBQUErTSxFQUFBL00sTUFBQSxPQUFBQSxHQUFBLFVBQUFnTixHQUFBaE4sSUFBQTRKLEVBQUErQixLQUFBM0wsRUFBQSxXQUFBME0sRUFBQUUsUUFBQTVNLEVBQUFpTixTQUFBQyxNQUFBLFNBQUFsTixHQUFBMk0sRUFBQSxPQUFBM00sRUFBQTRNLEVBQUFDLEVBQUEsYUFBQW5NLEdBQUFpTSxFQUFBLFFBQUFqTSxFQUFBa00sRUFBQUMsRUFBQSxJQUFBSCxFQUFBRSxRQUFBNU0sR0FBQWtOLE1BQUEsU0FBQUMsR0FBQUosRUFBQS9NLE1BQUFtTixFQUFBUCxFQUFBRyxFQUFBLGFBQUExSSxHQUFBLE9BQUFzSSxFQUFBLFFBQUF0SSxFQUFBdUksRUFBQUMsRUFBQSxJQUFBQSxFQUFBQyxFQUFBcEIsSUFBQSxLQUFBMEIsRUFBQXZELEVBQUEsZ0JBQUE3SixNQUFBLFNBQUF1TSxFQUFBYixHQUFBLFNBQUEyQixJQUFBLFdBQUFYLEdBQUEsU0FBQUUsRUFBQUMsR0FBQUYsRUFBQUosRUFBQWIsRUFBQWtCLEVBQUFDLEVBQUEsV0FBQU8sRUFBQUEsRUFBQUEsRUFBQUYsS0FBQUcsRUFBQUEsR0FBQUEsR0FBQSxhQUFBOUIsRUFBQVYsRUFBQUUsRUFBQU0sR0FBQSxJQUFBdEcsRUFBQSxpQ0FBQXdILEVBQUFiLEdBQUEsaUJBQUEzRyxFQUFBLFVBQUF1SSxNQUFBLGlEQUFBdkksRUFBQSxjQUFBd0gsRUFBQSxNQUFBYixFQUFBLE9BQUE2QixHQUFBLEtBQUFsQyxFQUFBa0IsT0FBQUEsRUFBQWxCLEVBQUFLLElBQUFBLElBQUEsS0FBQThCLEVBQUFuQyxFQUFBbUMsU0FBQSxHQUFBQSxFQUFBLEtBQUFDLEVBQUFDLEVBQUFGLEVBQUFuQyxHQUFBLEdBQUFvQyxFQUFBLElBQUFBLElBQUE3QixFQUFBLGdCQUFBNkIsQ0FBQSxjQUFBcEMsRUFBQWtCLE9BQUFsQixFQUFBc0MsS0FBQXRDLEVBQUF1QyxNQUFBdkMsRUFBQUssU0FBQSxhQUFBTCxFQUFBa0IsT0FBQSx1QkFBQXhILEVBQUEsTUFBQUEsRUFBQSxZQUFBc0csRUFBQUssSUFBQUwsRUFBQXdDLGtCQUFBeEMsRUFBQUssSUFBQSxnQkFBQUwsRUFBQWtCLFFBQUFsQixFQUFBeUMsT0FBQSxTQUFBekMsRUFBQUssS0FBQTNHLEVBQUEsZ0JBQUErSCxFQUFBdEIsRUFBQVgsRUFBQUUsRUFBQU0sR0FBQSxjQUFBeUIsRUFBQTNILEtBQUEsSUFBQUosRUFBQXNHLEVBQUE1SyxLQUFBLDZCQUFBcU0sRUFBQXBCLE1BQUFFLEVBQUEsZ0JBQUE1TCxNQUFBOE0sRUFBQXBCLElBQUFqTCxLQUFBNEssRUFBQTVLLEtBQUEsV0FBQXFNLEVBQUEzSCxPQUFBSixFQUFBLFlBQUFzRyxFQUFBa0IsT0FBQSxRQUFBbEIsRUFBQUssSUFBQW9CLEVBQUFwQixJQUFBLFlBQUFnQyxFQUFBRixFQUFBbkMsR0FBQSxJQUFBMEMsRUFBQTFDLEVBQUFrQixPQUFBQSxFQUFBaUIsRUFBQXJELFNBQUE0RCxHQUFBLFFBQUF4TyxJQUFBZ04sRUFBQSxPQUFBbEIsRUFBQW1DLFNBQUEsZUFBQU8sR0FBQVAsRUFBQXJELFNBQUE2RCxTQUFBM0MsRUFBQWtCLE9BQUEsU0FBQWxCLEVBQUFLLFNBQUFuTSxFQUFBbU8sRUFBQUYsRUFBQW5DLEdBQUEsVUFBQUEsRUFBQWtCLFNBQUEsV0FBQXdCLElBQUExQyxFQUFBa0IsT0FBQSxRQUFBbEIsRUFBQUssSUFBQSxJQUFBdUMsVUFBQSxvQ0FBQUYsRUFBQSxhQUFBbkMsRUFBQSxJQUFBa0IsRUFBQXRCLEVBQUFlLEVBQUFpQixFQUFBckQsU0FBQWtCLEVBQUFLLEtBQUEsYUFBQW9CLEVBQUEzSCxLQUFBLE9BQUFrRyxFQUFBa0IsT0FBQSxRQUFBbEIsRUFBQUssSUFBQW9CLEVBQUFwQixJQUFBTCxFQUFBbUMsU0FBQSxLQUFBNUIsRUFBQSxJQUFBc0MsRUFBQXBCLEVBQUFwQixJQUFBLE9BQUF3QyxFQUFBQSxFQUFBek4sTUFBQTRLLEVBQUFtQyxFQUFBVyxZQUFBRCxFQUFBbE8sTUFBQXFMLEVBQUErQyxLQUFBWixFQUFBYSxRQUFBLFdBQUFoRCxFQUFBa0IsU0FBQWxCLEVBQUFrQixPQUFBLE9BQUFsQixFQUFBSyxTQUFBbk0sR0FBQThMLEVBQUFtQyxTQUFBLEtBQUE1QixHQUFBc0MsR0FBQTdDLEVBQUFrQixPQUFBLFFBQUFsQixFQUFBSyxJQUFBLElBQUF1QyxVQUFBLG9DQUFBNUMsRUFBQW1DLFNBQUEsS0FBQTVCLEVBQUEsVUFBQTBDLEVBQUFDLEdBQUEsSUFBQUMsRUFBQSxDQUFBQyxPQUFBRixFQUFBLFNBQUFBLElBQUFDLEVBQUFFLFNBQUFILEVBQUEsU0FBQUEsSUFBQUMsRUFBQUcsV0FBQUosRUFBQSxHQUFBQyxFQUFBSSxTQUFBTCxFQUFBLFNBQUFNLFdBQUFDLEtBQUFOLEVBQUEsVUFBQU8sRUFBQVAsR0FBQSxJQUFBMUIsRUFBQTBCLEVBQUFRLFlBQUEsR0FBQWxDLEVBQUEzSCxLQUFBLGdCQUFBMkgsRUFBQXBCLElBQUE4QyxFQUFBUSxXQUFBbEMsQ0FBQSxVQUFBeEIsRUFBQU4sR0FBQSxLQUFBNkQsV0FBQSxFQUFBSixPQUFBLFNBQUF6RCxFQUFBc0IsUUFBQWdDLEVBQUEsV0FBQVcsT0FBQSxZQUFBOUMsRUFBQStDLEdBQUEsR0FBQUEsRUFBQSxLQUFBQyxFQUFBRCxFQUFBaEYsR0FBQSxHQUFBaUYsRUFBQSxPQUFBQSxFQUFBeEQsS0FBQXVELEdBQUEsc0JBQUFBLEVBQUFkLEtBQUEsT0FBQWMsRUFBQSxJQUFBRSxNQUFBRixFQUFBNVAsUUFBQSxLQUFBK1AsR0FBQSxFQUFBakIsRUFBQSxTQUFBQSxJQUFBLE9BQUFpQixFQUFBSCxFQUFBNVAsUUFBQSxHQUFBc0ssRUFBQStCLEtBQUF1RCxFQUFBRyxHQUFBLE9BQUFqQixFQUFBcE8sTUFBQWtQLEVBQUFHLEdBQUFqQixFQUFBM04sTUFBQSxFQUFBMk4sRUFBQSxPQUFBQSxFQUFBcE8sV0FBQVQsRUFBQTZPLEVBQUEzTixNQUFBLEVBQUEyTixDQUFBLFNBQUFBLEVBQUFBLEtBQUFBLENBQUEsU0FBQUEsS0FBQWIsRUFBQSxVQUFBQSxJQUFBLE9BQUF2TixXQUFBVCxFQUFBa0IsTUFBQSxVQUFBb0wsRUFBQWxDLFVBQUFtQyxFQUFBakMsRUFBQXVDLEVBQUEsZUFBQXBNLE1BQUE4TCxFQUFBcEIsY0FBQSxJQUFBYixFQUFBaUMsRUFBQSxlQUFBOUwsTUFBQTZMLEVBQUFuQixjQUFBLElBQUFtQixFQUFBeUQsWUFBQTlFLEVBQUFzQixFQUFBeEIsRUFBQSxxQkFBQWIsRUFBQThGLG9CQUFBLFNBQUFDLEdBQUEsSUFBQUMsRUFBQSxtQkFBQUQsR0FBQUEsRUFBQUUsWUFBQSxRQUFBRCxJQUFBQSxJQUFBNUQsR0FBQSx1QkFBQTRELEVBQUFILGFBQUFHLEVBQUFFLE1BQUEsRUFBQWxHLEVBQUFtRyxLQUFBLFNBQUFKLEdBQUEsT0FBQW5LLE9BQUF3SyxlQUFBeEssT0FBQXdLLGVBQUFMLEVBQUExRCxJQUFBMEQsRUFBQU0sVUFBQWhFLEVBQUF0QixFQUFBZ0YsRUFBQWxGLEVBQUEsc0JBQUFrRixFQUFBN0YsVUFBQXRFLE9BQUErRixPQUFBZ0IsR0FBQW9ELENBQUEsRUFBQS9GLEVBQUFzRyxNQUFBLFNBQUFyRSxHQUFBLE9BQUF1QixRQUFBdkIsRUFBQSxFQUFBVyxFQUFBSSxFQUFBOUMsV0FBQWEsRUFBQWlDLEVBQUE5QyxVQUFBUyxHQUFBLDBCQUFBWCxFQUFBZ0QsY0FBQUEsRUFBQWhELEVBQUF1RyxNQUFBLFNBQUFuRixFQUFBQyxFQUFBQyxFQUFBQyxFQUFBMEIsUUFBQSxJQUFBQSxJQUFBQSxFQUFBdUQsU0FBQSxJQUFBQyxFQUFBLElBQUF6RCxFQUFBN0IsRUFBQUMsRUFBQUMsRUFBQUMsRUFBQUMsR0FBQTBCLEdBQUEsT0FBQWpELEVBQUE4RixvQkFBQXpFLEdBQUFvRixFQUFBQSxFQUFBOUIsT0FBQWxCLE1BQUEsU0FBQUgsR0FBQSxPQUFBQSxFQUFBdE0sS0FBQXNNLEVBQUEvTSxNQUFBa1EsRUFBQTlCLE1BQUEsS0FBQS9CLEVBQUFELEdBQUE1QixFQUFBNEIsRUFBQTlCLEVBQUEsYUFBQUUsRUFBQTRCLEVBQUFsQyxHQUFBLDBCQUFBTSxFQUFBNEIsRUFBQSxxREFBQTNDLEVBQUEwRyxLQUFBLFNBQUFDLEdBQUEsSUFBQUMsRUFBQWhMLE9BQUErSyxHQUFBRCxFQUFBLFdBQUE1UixLQUFBOFIsRUFBQUYsRUFBQXJCLEtBQUF2USxHQUFBLE9BQUE0UixFQUFBRyxVQUFBLFNBQUFsQyxJQUFBLEtBQUErQixFQUFBN1EsUUFBQSxLQUFBZixFQUFBNFIsRUFBQWpQLE1BQUEsR0FBQTNDLEtBQUE4UixFQUFBLE9BQUFqQyxFQUFBcE8sTUFBQXpCLEVBQUE2UCxFQUFBM04sTUFBQSxFQUFBMk4sQ0FBQSxRQUFBQSxFQUFBM04sTUFBQSxFQUFBMk4sQ0FBQSxHQUFBM0UsRUFBQTBDLE9BQUFBLEVBQUFiLEVBQUEzQixVQUFBLENBQUErRixZQUFBcEUsRUFBQTJELE1BQUEsU0FBQXNCLEdBQUEsUUFBQUMsS0FBQSxPQUFBcEMsS0FBQSxPQUFBVCxLQUFBLEtBQUFDLFdBQUFyTyxFQUFBLEtBQUFrQixNQUFBLE9BQUErTSxTQUFBLFVBQUFqQixPQUFBLFlBQUFiLFNBQUFuTSxFQUFBLEtBQUFzUCxXQUFBdkMsUUFBQXlDLElBQUF3QixFQUFBLFFBQUFaLEtBQUEsV0FBQUEsRUFBQTVRLE9BQUEsSUFBQTZLLEVBQUErQixLQUFBLEtBQUFnRSxLQUFBUCxPQUFBTyxFQUFBMVEsTUFBQSxXQUFBMFEsUUFBQXBRLEVBQUEsRUFBQWtSLEtBQUEsZ0JBQUFoUSxNQUFBLE1BQUFpUSxFQUFBLEtBQUE3QixXQUFBLEdBQUFHLFdBQUEsYUFBQTBCLEVBQUF2TCxLQUFBLE1BQUF1TCxFQUFBaEYsSUFBQSxZQUFBaUYsSUFBQSxFQUFBOUMsa0JBQUEsU0FBQStDLEdBQUEsUUFBQW5RLEtBQUEsTUFBQW1RLEVBQUEsSUFBQXZGLEVBQUEsY0FBQXdGLEVBQUFDLEVBQUFDLEdBQUEsT0FBQWpFLEVBQUEzSCxLQUFBLFFBQUEySCxFQUFBcEIsSUFBQWtGLEVBQUF2RixFQUFBK0MsS0FBQTBDLEVBQUFDLElBQUExRixFQUFBa0IsT0FBQSxPQUFBbEIsRUFBQUssU0FBQW5NLEtBQUF3UixDQUFBLFNBQUExQixFQUFBLEtBQUFSLFdBQUF2UCxPQUFBLEVBQUErUCxHQUFBLElBQUFBLEVBQUEsS0FBQWIsRUFBQSxLQUFBSyxXQUFBUSxHQUFBdkMsRUFBQTBCLEVBQUFRLFdBQUEsWUFBQVIsRUFBQUMsT0FBQSxPQUFBb0MsRUFBQSxVQUFBckMsRUFBQUMsUUFBQSxLQUFBK0IsS0FBQSxLQUFBUSxFQUFBcEgsRUFBQStCLEtBQUE2QyxFQUFBLFlBQUF5QyxFQUFBckgsRUFBQStCLEtBQUE2QyxFQUFBLGlCQUFBd0MsR0FBQUMsRUFBQSxTQUFBVCxLQUFBaEMsRUFBQUUsU0FBQSxPQUFBbUMsRUFBQXJDLEVBQUFFLFVBQUEsV0FBQThCLEtBQUFoQyxFQUFBRyxXQUFBLE9BQUFrQyxFQUFBckMsRUFBQUcsV0FBQSxTQUFBcUMsR0FBQSxRQUFBUixLQUFBaEMsRUFBQUUsU0FBQSxPQUFBbUMsRUFBQXJDLEVBQUFFLFVBQUEsWUFBQXVDLEVBQUEsVUFBQTNELE1BQUEsa0RBQUFrRCxLQUFBaEMsRUFBQUcsV0FBQSxPQUFBa0MsRUFBQXJDLEVBQUFHLFdBQUEsS0FBQWIsT0FBQSxTQUFBM0ksRUFBQXVHLEdBQUEsUUFBQTJELEVBQUEsS0FBQVIsV0FBQXZQLE9BQUEsRUFBQStQLEdBQUEsSUFBQUEsRUFBQSxLQUFBYixFQUFBLEtBQUFLLFdBQUFRLEdBQUEsR0FBQWIsRUFBQUMsUUFBQSxLQUFBK0IsTUFBQTVHLEVBQUErQixLQUFBNkMsRUFBQSxvQkFBQWdDLEtBQUFoQyxFQUFBRyxXQUFBLEtBQUF1QyxFQUFBMUMsRUFBQSxPQUFBMEMsSUFBQSxVQUFBL0wsR0FBQSxhQUFBQSxJQUFBK0wsRUFBQXpDLFFBQUEvQyxHQUFBQSxHQUFBd0YsRUFBQXZDLGFBQUF1QyxFQUFBLFVBQUFwRSxFQUFBb0UsRUFBQUEsRUFBQWxDLFdBQUEsVUFBQWxDLEVBQUEzSCxLQUFBQSxFQUFBMkgsRUFBQXBCLElBQUFBLEVBQUF3RixHQUFBLEtBQUEzRSxPQUFBLFlBQUE2QixLQUFBOEMsRUFBQXZDLFdBQUEvQyxHQUFBLEtBQUF1RixTQUFBckUsRUFBQSxFQUFBcUUsU0FBQSxTQUFBckUsRUFBQThCLEdBQUEsYUFBQTlCLEVBQUEzSCxLQUFBLE1BQUEySCxFQUFBcEIsSUFBQSxnQkFBQW9CLEVBQUEzSCxNQUFBLGFBQUEySCxFQUFBM0gsS0FBQSxLQUFBaUosS0FBQXRCLEVBQUFwQixJQUFBLFdBQUFvQixFQUFBM0gsTUFBQSxLQUFBd0wsS0FBQSxLQUFBakYsSUFBQW9CLEVBQUFwQixJQUFBLEtBQUFhLE9BQUEsY0FBQTZCLEtBQUEsa0JBQUF0QixFQUFBM0gsTUFBQXlKLElBQUEsS0FBQVIsS0FBQVEsR0FBQWhELENBQUEsRUFBQXdGLE9BQUEsU0FBQXpDLEdBQUEsUUFBQVUsRUFBQSxLQUFBUixXQUFBdlAsT0FBQSxFQUFBK1AsR0FBQSxJQUFBQSxFQUFBLEtBQUFiLEVBQUEsS0FBQUssV0FBQVEsR0FBQSxHQUFBYixFQUFBRyxhQUFBQSxFQUFBLFlBQUF3QyxTQUFBM0MsRUFBQVEsV0FBQVIsRUFBQUksVUFBQUcsRUFBQVAsR0FBQTVDLENBQUEsR0FBQXlGLE1BQUEsU0FBQTVDLEdBQUEsUUFBQVksRUFBQSxLQUFBUixXQUFBdlAsT0FBQSxFQUFBK1AsR0FBQSxJQUFBQSxFQUFBLEtBQUFiLEVBQUEsS0FBQUssV0FBQVEsR0FBQSxHQUFBYixFQUFBQyxTQUFBQSxFQUFBLEtBQUEzQixFQUFBMEIsRUFBQVEsV0FBQSxhQUFBbEMsRUFBQTNILEtBQUEsS0FBQW1NLEVBQUF4RSxFQUFBcEIsSUFBQXFELEVBQUFQLEVBQUEsUUFBQThDLENBQUEsWUFBQWhFLE1BQUEsMEJBQUFpRSxjQUFBLFNBQUFyQyxFQUFBZixFQUFBRSxHQUFBLFlBQUFiLFNBQUEsQ0FBQXJELFNBQUFnQyxFQUFBK0MsR0FBQWYsV0FBQUEsRUFBQUUsUUFBQUEsR0FBQSxjQUFBOUIsU0FBQSxLQUFBYixTQUFBbk0sR0FBQXFNLENBQUEsR0FBQW5DLENBQUEsVUFBQWpFLEdBQUF6QyxHQUFBLGdCQUFBQSxHQUFBLEdBQUE5QyxNQUFBQyxRQUFBNkMsR0FBQSxPQUFBeU8sR0FBQXpPLEVBQUEsQ0FBQTBPLENBQUExTyxJQUFBLFNBQUFtTixHQUFBLHVCQUFBakcsUUFBQSxNQUFBaUcsRUFBQWpHLE9BQUFFLFdBQUEsTUFBQStGLEVBQUEscUJBQUFqUSxNQUFBeVIsS0FBQXhCLEVBQUEsQ0FBQXlCLENBQUE1TyxJQUFBLFNBQUE2TyxFQUFBQyxHQUFBLElBQUFELEVBQUEsMkJBQUFBLEVBQUEsT0FBQUosR0FBQUksRUFBQUMsR0FBQSxJQUFBclIsRUFBQTZFLE9BQUFzRSxVQUFBTixTQUFBc0MsS0FBQWlHLEdBQUEzUyxNQUFBLGlCQUFBdUIsR0FBQW9SLEVBQUFsQyxjQUFBbFAsRUFBQW9SLEVBQUFsQyxZQUFBQyxNQUFBLFdBQUFuUCxHQUFBLFFBQUFBLEVBQUEsT0FBQVAsTUFBQXlSLEtBQUFFLEdBQUEsaUJBQUFwUixHQUFBLDJDQUFBc1IsS0FBQXRSLEdBQUEsT0FBQWdSLEdBQUFJLEVBQUFDLEVBQUEsQ0FBQUUsQ0FBQWhQLElBQUEscUJBQUFrTCxVQUFBLHdJQUFBK0QsRUFBQSxVQUFBUixHQUFBek8sRUFBQWtQLElBQUEsTUFBQUEsR0FBQUEsRUFBQWxQLEVBQUF6RCxVQUFBMlMsRUFBQWxQLEVBQUF6RCxRQUFBLFFBQUErUCxFQUFBLEVBQUE2QyxFQUFBLElBQUFqUyxNQUFBZ1MsR0FBQTVDLEVBQUE0QyxFQUFBNUMsSUFBQTZDLEVBQUE3QyxHQUFBdE0sRUFBQXNNLEdBQUEsT0FBQTZDLENBQUEsVUFBQUMsR0FBQUMsRUFBQXhGLEVBQUFDLEVBQUF3RixFQUFBQyxFQUFBL1QsRUFBQW1OLEdBQUEsUUFBQXdDLEVBQUFrRSxFQUFBN1QsR0FBQW1OLEdBQUExTCxFQUFBa08sRUFBQWxPLEtBQUEsT0FBQXFFLEdBQUEsWUFBQXdJLEVBQUF4SSxFQUFBLENBQUE2SixFQUFBek4sS0FBQW1NLEVBQUE1TSxHQUFBaVEsUUFBQXJELFFBQUE1TSxHQUFBa04sS0FBQW1GLEVBQUFDLEVBQUEsQ0F1QkEsSUFFSUMsR0FDQUMsR0FIRUMsR0FBY0MsR0FBQUEsRUFBTUQsWUFLYkUsR0FBYyxTQUFDL08sR0FDMUIsT0FBTyxTQUFDZ1AsR0FDTixPQUFPLElBQUkzQyxTQUFRLFNBQUM0QyxFQUFLQyxHQUN2QkYsRUFBUyxDQUFFek4sS0FBTXZELEVBQWNnQyxNQUFBQSxJQUMvQmlQLEVBQUlqUCxFQUNOLEdBQ0YsQ0FDRixFQU9hbVAsR0FBYyxXQUN6QixPQUFPLFNBQUNILEdBQ04sT0FBTyxJQUFJM0MsU0FBUSxTQUFDNEMsRUFBS0MsR0FDbkJQLElBQWNBLEtBQ2RDLElBQWFBLEtBQ2pCSSxFQUFTLENBQUV6TixLQUFNdEQsSUFDakJnUixHQUNGLEdBQ0YsQ0FDRixFQUVhRyxHQUFnQixTQUFDcFAsR0FBSyxNQUFNLENBQUV1QixLQUFNckQsRUFBZ0I4QixNQUFBQSxFQUFPLEVBUTNEcVAsR0FBZSxTQUFDclAsRUFBT08sRUFBT3NCLEdBQU8sTUFBTSxDQUN0RE4sS0FBTW5ELEVBQ040QixNQUFBQSxFQUNBTyxNQUFBQSxFQUNBc0IsUUFBQUEsRUFDRCxFQUNZeU4sR0FBb0IsU0FBQzdPLEdBQUssTUFBTSxDQUMzQ2MsS0FBTWxELEVBQ05vQyxNQUFBQSxFQUNELEVBQ1k4TyxHQUFrQixTQUFDL1QsR0FBSSxNQUFNLENBQUUrRixLQUFNN0MsRUFBZWxELEtBQUFBLEVBQU0sRUFzQjFEZ1UsR0FBbUIsU0FBQ3hQLEdBQy9CLHNCQTlGRjZILEVBOEZFNEgsR0E5RkY1SCxFQThGRWpDLEtBQUFvRyxNQUFPLFNBQUEwRCxFQUFPVixHQUFRLE9BQUFwSixLQUFBb0IsTUFBQSxTQUFBMkksR0FBQSxjQUFBQSxFQUFBL0MsS0FBQStDLEVBQUFuRixNQUFBLE9BQ3BCd0UsRUFBU0ksR0FBY3BQLElBQ3ZCNE8sSUFBZUEsS0FFZkUsR0FBQUEsRUFBTWMsSUFBSSxHQUFEN04sT0FBSTRCLEVBQW9CLE9BQUE1QixPQUFNL0IsR0FBUyxDQUM5QzJJLE9BQVEsTUFDUmtILFFBQVMsQ0FDUCxlQUFnQixvQkFFbEJDLFlBQWEsSUFBSWpCLElBQVksU0FBQ2tCLEdBQUMsT0FBTW5CLEdBQWNtQixDQUFDLE1BRW5EekcsTUFBSyxTQUFBMEcsR0FBYyxJQUFYQyxFQUFJRCxFQUFKQyxLQUNEQyxFQUFlNUwsR0FBMkIyTCxHQUN4Q3JOLEdBQWFzTixHQUFnQixDQUFDLEdBQTlCdE4sU0FDUixHQUFJQSxHQUFZQSxFQUFTdU4sWUFBc0MsTUFBeEJ2TixFQUFTdU4sV0FBb0IsQ0FDbEUsSUFBTUMsRUFBTXhOLEVBQVN5TixnQkFBa0J6TixFQUFTME4sUUFDaER0QixFQUFTTSxHQUFrQmMsR0FDN0IsS0FBTyxDQUNMLElBQU10TixFQUFJOUMsRUFDSmlGLEVBQWNzTCxHQUFzQkwsRUFBYWpMLGNBQWdCLEdBQ2pFaEQsRUFBbUJpTyxFQUFhak8sa0JBQW9CLEdBQ3BEdU8sRUFBb0JDLEdBQVdQLEVBQWFNLG9CQUFzQixHQUNsRUUsRUFBU0MsR0FBWVQsRUFBYU0sbUJBQXFCLEdBQUksQ0FDL0QxTixHQUFDZixPQUFBSCxHQUNFSyxHQUFnQkwsR0FDaEJxRCxHQUFXckQsR0FDWDRPLEtBRUNJLEVBQXFCSCxHQUFXUCxFQUFhVSxxQkFBdUIsR0FDcEVyUSxFQUFRLEdBQUh3QixPQUFBSCxHQUFPSyxHQUFnQkwsR0FBS2dQLEdBQWtCaFAsR0FBS3FELEdBQVdyRCxHQUFLNE8sR0FBaUI1TyxHQUFLOE8sSUFFcEdSLEVBQWFqTyxpQkFBbUIsR0FBSEYsT0FBQUgsR0FBT0ssR0FBZ0JMLEdBQUtnUCxHQUFrQmhQLEdBQUs0TyxJQUNoRnhCLEVBQVNLLEdBQWFyUCxFQUFPTyxFQUFPMlAsR0FDdEMsQ0FDRixJQUNDekMsT0FBTSxTQUFDaE4sR0FDRHFPLEdBQUFBLEVBQU0rQixTQUFTcFEsSUFBUXVPLEVBQVNNLEdBQWtCLHlDQUN6RCxJQUFHLHdCQUFBSyxFQUFBOUMsT0FBQSxHQUFBNkMsRUFBQSxJQW5JVCxlQUFBdkksRUFBQSxLQUFBMkosRUFBQXJWLFVBQUEsV0FBQTRRLFNBQUEsU0FBQXJELEVBQUFDLEdBQUEsSUFBQXVGLEVBQUEzRyxFQUFBa0osTUFBQTVKLEVBQUEySixHQUFBLFNBQUFyQyxFQUFBclMsR0FBQW1TLEdBQUFDLEVBQUF4RixFQUFBQyxFQUFBd0YsRUFBQUMsRUFBQSxPQUFBdFMsRUFBQSxVQUFBc1MsRUFBQTVSLEdBQUF5UixHQUFBQyxFQUFBeEYsRUFBQUMsRUFBQXdGLEVBQUFDLEVBQUEsUUFBQTVSLEVBQUEsQ0FBQTJSLE9BQUE5UyxFQUFBLE1Bb0lHLGdCQUFBcVYsR0FBQSxPQUFBdkIsRUFBQXNCLE1BQUEsS0FBQXRWLFVBQUEsRUF0Q0QsRUF1Q0YsRUFFYXdWLEdBQTRCLFNBQUN6VixHQUN4QyxPQUFPLFNBQUN3VCxHQUNOQSxFQW5GMEMsQ0FDNUN6TixLQUFNakQsSUFvRkp3USxHQUFBQSxFQUFNYyxJQUFJLEdBQUQ3TixPQUFJOEIsR0FBYyxPQUFBOUIsT0FBTXZHLEVBQUksZUFBZSxDQUNsRHNVLFlBQWEsSUFBSWpCLElBQVksU0FBQ2tCLEdBQUMsT0FBTXBCLEdBQWVvQixDQUFDLE1BRXBEekcsTUFBSyxTQUFBNEgsR0FBYyxJQUFYakIsRUFBSWlCLEVBQUpqQixLQUNEQyxFQUFlNUwsR0FBMkIyTCxJQUFTLENBQUMsRUFDMURqQixFQTNEdUMsU0FBQ3hULEVBQU1xRyxHQUFPLE1BQU0sQ0FDakVOLEtBQU1oRCxFQUNOL0MsS0FBQUEsRUFDQXFHLFFBQUFBLEVBQ0QsQ0F1RGdCc1AsQ0FBZ0MzVixFQUFNMFUsR0FDakQsSUFDQ3pDLE9BQU0sU0FBQ2hOLEdBQ0RxTyxHQUFBQSxFQUFNK0IsU0FBU3BRLElBQVF1TyxFQTFGQyxTQUFDdk8sR0FBSyxNQUFNLENBQy9DYyxLQUFNL0MsRUFDTmlDLE1BQUFBLEVBQ0QsQ0F1RjRDMlEsQ0FBc0IzUSxHQUM3RCxHQUNKLENBQ0YsRUFFYTRRLEdBQWtCLFNBQUM3VixHQUEwQixJQUFwQjBHLEVBQU96RyxVQUFBQyxPQUFBLFFBQUFDLElBQUFGLFVBQUEsSUFBQUEsVUFBQSxHQUMzQyxPQUFPLFNBQUN1VCxHQUdOLE9BRkFBLEVBbEY2QixTQUFDeFQsRUFBTTBHLEdBQU8sTUFBTSxDQUNuRFgsS0FBTTVDLEVBQ05uRCxLQUFBQSxFQUNBMEcsUUFBQUEsRUFDRCxDQThFWW9QLENBQWtCOVYsRUFBTTBHLElBQ2pDeU0sSUFBZ0JBLEtBQ1RHLEdBQUFBLEVBQU1jLElBQUksR0FBRDdOLE9BQUk4QixJQUFrQixDQUNwQzhFLE9BQVEsTUFDUm1ILFlBQWEsSUFBSWpCLElBQVksU0FBQ2tCLEdBQUMsT0FBTXBCLEdBQWVvQixDQUFDLElBQ3JEd0IsT0FBUSxDQUNOek8sRUFBR3RILEVBQ0hnVyxTQUFVLE1BR1hsSSxNQUFLLFNBQUM1RSxHQUNMLElBQU11TCxFQUFPdkwsYUFBUSxFQUFSQSxFQUFVdUwsS0FDakJDLEVBQWU1TCxHQUEyQjJMLElBQVMsQ0FBQyxFQUNiLEtBQXhDQyxFQUFhalEsVUFBWSxJQUFJdkUsUUFDaENILEVBQXVCQyxHQUFNLEdBRS9Cd1QsRUE3RnlCLFNBQUN4VCxFQUFNcUcsR0FBTyxNQUFNLENBQ25ETixLQUFNM0MsRUFDTnBELEtBQUFBLEVBQ0FxRyxRQUFBQSxFQUNELENBeUZnQjRQLENBQWtCalcsRUFBTTBVLEdBQ25DLElBQ0N6QyxPQUFNLFNBQUNoTixHQUNObEYsRUFBdUJDLEdBQU0sR0FDeEJzVCxHQUFBQSxFQUFNK0IsU0FBU3BRLElBQVF1TyxFQW5GSCxTQUFDdk8sR0FBSyxNQUFNLENBQUVjLEtBQU0xQyxFQUFpQjRCLE1BQUFBLEVBQU8sQ0FtRmhDaVIsQ0FBa0JqUixHQUN6RCxHQUNKLENBQ0YsRUEwQk1nUSxHQUFhLFNBQUN0UixHQUNsQixPQUFPQSxhQUFHLEVBQUhBLEVBQUtsRSxLQUFJLFNBQUNPLEdBQUksSUFBQW1XLEVBQUEsT0FBS25XLFNBQWlCLFFBQWJtVyxFQUFKblcsRUFBTW9XLG1CQUFXLElBQUFELE9BQUEsRUFBakJBLEVBQW1CRSxNQUFNLEdBQ3JELEVBRU10QixHQUF3QixTQUFDcFIsR0FDN0IsT0FBT0EsYUFBRyxFQUFIQSxFQUFLbEUsS0FBSSxTQUFDTyxHQUFJLElBQUFzVyxFQUFBLE1BQU0sQ0FBRXRXLEtBQU1BLFNBQWlCLFFBQWJzVyxFQUFKdFcsRUFBTW9XLG1CQUFXLElBQUFFLE9BQUEsRUFBakJBLEVBQW1CRCxPQUFRdFAsY0FBZS9HLEVBQUt1VyxvQkFBcUIsR0FDekcsRUFRTXBCLEdBQWMsU0FBQ3FCLEVBQVV6UixHQUk3QixJQUhBLElBQUltUSxFQUFTLEdBQ1h1QixFQUFTMVIsRUFBTTdFLE9BQVM2RSxFQUFNLEdBQUssR0FFNUJrTCxFQUFJLEVBQUdBLEVBQUl1RyxFQUFTdFcsT0FBUStQLElBQ25DLFFBQUksSUFBdUJ1RyxFQUFTdkcsR0FBYSxTQUFHLENBQ2xELElBQUl5RyxFQUFVRixFQUFTdkcsR0FBYSxTQUNqQ3JPLFFBQU8sU0FBQytVLEdBQ1AsSUFBSUMsRUFBUUQsRUFBR04sT0FDZixPQUFpQyxJQUExQnRSLEVBQU1tRixRQUFRME0sSUFBaUJBLElBQVVILENBQ2xELElBQ0NoWCxLQUFJLFNBQUNvWCxHQUNKLE9BQU9BLEVBQUdSLE9BQVMsSUFBTUksQ0FDM0IsSUFFRnZCLEVBQVMsR0FBSDNPLE9BQUFILEdBQU84TyxHQUFNOU8sR0FBS3NRLEdBQzFCLENBR0YsT0FBT3hCLENBQ1QsRUFFYTRCLEdBQWlCLFNBQUN6USxHQUFPLE1BQU0sQ0FBRU4sS0FBTXpDLEVBQWlCK0MsUUFBQUEsRUFBUyxFQUNqRTBRLEdBQW1CLFdBQUgsTUFBVSxDQUFFaFIsS0FBTXhDLEVBQW9CLEVBQ3REeVQsR0FBbUIsU0FBQ2xQLEdBQU8sTUFBTSxDQUM1Qy9CLEtBQU12QyxFQUNONkMsUUFBUyxDQUFFeUIsUUFBQUEsR0FDWixFQ2hPRCxHQW5CZ0IsU0FBSG1NLEdBQWtCLElBQVpnRCxFQUFLaEQsRUFBTGdELE1BQ2YsT0FDSUMsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFVBQVVGLE1BQU9BLEdBQzVCQyxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsU0FDZkQsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFNBQ2ZELEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxTQUNmRCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsU0FDZkQsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFNBQ2ZELEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxTQUNmRCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsU0FDZkQsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFNBQ2ZELEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxTQUNmRCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsVUFDZkQsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFVBQ2ZELEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxVQUczQixFQ1RBLEdBUm9CLFNBQUhsRCxHQUF3QixJQUFuQm1ELEVBQUluRCxFQUFKbUQsS0FBTXRDLEVBQU9iLEVBQVBhLFFBQ3hCLE9BQU9zQyxFQUNIRixFQUFBQSxjQUFBLFdBQ0lBLEVBQUFBLGNBQUEsU0FBSXBDLElBRVIsSUFDUixFQ0dBLEdBVHNCLFNBQUhiLEdBQWtCLElBQVpvRCxFQUFLcEQsRUFBTG9ELE1BQ3ZCLE9BQU9BLEVBQVEsRUFDYkgsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLGlCQUNiRCxFQUFBQSxjQUFBLFlBQU9HLEdBQ1BILEVBQUFBLGNBQUEsWUFBT0csRUFBUSxFQUFJLFdBQWEsWUFFaEMsSUFDTixxaUNDREEsSUE4REEsR0E5RG1CLFNBQUhwRCxHQUF5QyxJQUFuQ3FELEVBQVVyRCxFQUFWcUQsV0FBWUMsRUFBZ0J0RCxFQUFoQnNELGlCQUMxQi9ELEdBQVdnRSxFQUFBQSxFQUFBQSxNQUNqQkMsR0FBd0RDLEVBQUFBLEVBQUFBLEtBQVksU0FBQy9SLEdBQUssT0FBS0EsRUFBTWhCLFdBQVcsSUFBeEZHLEVBQVUyUyxFQUFWM1MsV0FBc0I2UyxFQUFVRixFQUFwQjVTLFNBQXNCRyxFQUFTeVMsRUFBVHpTLFVBQ3BDaUMsRUFBV2pDLEdBQWFBLEVBQVVGLEdBQWNFLEVBQVVGLEdBQWMsS0FDMUI4UyxFQUFBQyxJQUFWQyxFQUFBQSxFQUFBQSxZQUFVLEdBQTlCQyxHQUFGSCxFQUFBLEdBQWtCQSxFQUFBLEtBZXRDSSxFQUFBQSxFQUFBQSxZQUFVLFdBQ0ovUSxHQUFVOFEsRUFBaUI5USxFQUNqQyxHQUFHLENBQUNBLElBMEJKLE9BQ0VpUSxFQUFBQSxjQUFBQSxFQUFBQSxTQUFBLEtBQ0VBLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxxREFDWkksRUFDQ0wsRUFBQUEsY0FBQUEsRUFBQUEsU0FBQSxLQUNFQSxFQUFBQSxjQUFDZSxHQUFXLENBQUNiLE1BQU0sRUFBTXRDLFFBQVEsOERBQ2pDb0MsRUFBQUEsY0FBQSxVQUFJLHVCQUVKLEtBQ0hJLEdBQWNBLEVBQVdwWCxPQUFTZ1gsRUFBQUEsY0FBQSxNQUFJQyxVQUFVLG1CQWpDOUIsU0FBQ0csR0FDeEIsT0FBT0EsRUFBVzdYLEtBQUksU0FBQ3lZLEVBQUtDLEdBQVEsSUFBQUMsRUFDNUJDLElBQVd2VCxJQUFhQSxhQUFVLEVBQVZBLEVBQVl6RixpQkFBaUI2WSxTQUFTLFFBQU5FLEVBQUhGLEVBQUtsWSxZQUFJLElBQUFvWSxPQUFBLEVBQVRBLEVBQVcvWSxlQUN0RSxPQUNFNlgsRUFBQUEsY0FBQSxNQUFJL1gsSUFBS2daLEVBQUtoQixVQUFTLHdCQUNyQkQsRUFBQUEsY0FBQSxLQUNFQyxVQUFTLHNCQUFBNVEsT0FBd0I4UixFQUFXLFNBQVcsWUFDdkRDLFFBQVMsU0FBQy9XLEdBQUMsT0F4QkQsU0FBQ0EsRUFBR3ZCLEdBRXRCLElBQUl1WSxFQURKaFgsRUFBRWlYLGlCQUdFeFksSUFBTXVZLEVBQU0sNEJBQUhoUyxPQUErQmtTLG1CQUFtQnpZLEtBRS9Ed1QsRUFBU3dELElBQWlCLElBQzFCeEQsRUFBU08sR0FBZ0IvVCxJQUN6QkQsRUFBdUJDLEdBQ3ZCd1QsRUFBU3VELE1BQ1Q1VSxPQUFPdVcsU0FBU0MsS0FBT0osQ0FDekIsQ0FhMEJLLENBQVlyWCxFQUFHMlcsYUFBRyxFQUFIQSxFQUFLbFksS0FBSyxHQUV4Q3VYLEVBQ0NMLEVBQUFBLGNBQUFBLEVBQUFBLFNBQUEsS0FDRUEsRUFBQUEsY0FBQSxTQUFJZ0IsRUFBSWxZLE1BQ1JrWCxFQUFBQSxjQUFDMkIsR0FBYSxDQUFDeEIsTUFBT2EsYUFBRyxFQUFIQSxFQUFLblIsZ0JBQzFCbVIsSUFBUXBULEdBQWM2UyxHQUFjVCxFQUFBQSxjQUFDNEIsR0FBTyxDQUFDN0IsTUFBTyxDQUFFOEIsV0FBWSxXQUdyRWIsRUFBSWxZLE1BS2QsR0FDRixDQVcwRWdaLENBQWlCMUIsSUFBb0IsTUFJakgsRUN6REEsR0FSc0IsU0FBSHJELEdBQUEsSUFBTW1ELEVBQUluRCxFQUFKbUQsS0FBTXRDLEVBQU9iLEVBQVBhLFFBQU8sT0FDbENzQyxFQUNJRixFQUFBQSxjQUFBLE9BQUtDLFVBQVUseUJBQ1hELEVBQUFBLGNBQUEsT0FBSytCLElBQUtwUSxHQUF1QnFRLElBQUksWUFDcENwRSxHQUFXb0MsRUFBQUEsY0FBQSxZQUFPcEMsSUFFdkIsSUFBSSxFQ05DcUUsR0FBYSxTQUFIbEYsR0FBNkIsSUFBeEJtRixFQUFrQm5GLEVBQWxCbUYsbUJBRXhCLE9BRHVCMUIsRUFBQUEsRUFBQUEsS0FBWSxTQUFDL1IsR0FBSyxPQUFLQSxFQUFNdkIsY0FBYyxLQUN6Q2dWLEVBQ3JCbEMsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLGVBQWUsWUFBVSxVQUNwQ0QsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFVBQ1hELEVBQUFBLGNBQUEsT0FBS2dDLElBQUksYUFBYUQsSUFBS3BRLEtBQzNCcU8sRUFBQUEsY0FBQSxTQUFHLG9CQUdYLElBQ1IsRUNDQSxHQVpjLFNBQUhqRCxHQUFtQyxJQUE3QmhQLEVBQUtnUCxFQUFMaFAsTUFBT00sRUFBUTBPLEVBQVIxTyxTQUFVZixFQUFLeVAsRUFBTHpQLE1BQzFCNlUsRUFBb0I5VCxHQUFZQSxFQUFTbkcsUUFBUSxTQUFVb0YsR0FFakUsT0FBT1MsR0FBU1QsRUFDZDBTLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSw2QkFDYkQsRUFBQUEsY0FBQSxTQUNFQSxFQUFBQSxjQUFBLGNBQVNtQyxLQUdYLElBQ04sRUNXQSxHQXJCYyxTQUFIcEYsR0FBeUMsSUFBbkNxRixFQUFVckYsRUFBVnFGLFdBQVlDLEVBQVF0RixFQUFSc0YsU0FDckJ6UCxHQUFRMFAsRUFENkJ2RixFQUFOdUYsUUFDaEJGLEdBQVksR0FFakMsTUFBc0IsY0FBZkEsRUFDTHBDLEVBQUFBLGNBQUEsUUFBTUMsVUFBVSxpQkFDZEQsRUFBQUEsY0FBQSxRQUFNQyxVQUFVLG1CQUFtQm9DLEdBQ25DckMsRUFBQUEsY0FBQSxRQUFNQyxVQUFVLGNBQ2J0VyxNQUFNQyxRQUFRZ0osR0FDYm9OLEVBQUFBLGNBQUFBLEVBQUFBLFNBQUEsS0FDR3BOLGFBQUssRUFBTEEsRUFBUSxHQUNUb04sRUFBQUEsY0FBQSxRQUFNdUMsTUFBTSxlQUFjLEtBQzFCdkMsRUFBQUEsY0FBQSxRQUFNdUMsTUFBTSxpQkFBaUIzUCxhQUFLLEVBQUxBLEVBQVEsS0FHdkNBLElBSUosSUFDTixFQzZCQSxHQXpDZ0IsU0FBQzRQLEdBQ2YsSUEyQlFDLEVBQWtCRCxFQUFsQkMsUUFBUzVULEVBQVMyVCxFQUFUM1QsS0FFakIsT0FBSUEsRUFBSzZULE1BQU0sZ0JBQWtCRCxHQUFXLEdBQUs1VCxFQUFLNlQsTUFBTSxZQUNuRCxLQUlQMUMsRUFBQUEsY0FBQSxRQUFNQyxVQUFVLHNCQUNkRCxFQUFBQSxjQUFBLFFBQU1DLFVBQVUsUUFuQ0EsV0FDbEIsSUFBUXdDLEVBQStERCxFQUEvREMsUUFBU0osRUFBc0RHLEVBQXRESCxTQUFVQyxFQUE0Q0UsRUFBNUNGLE9BQVF6VCxFQUFvQzJULEVBQXBDM1QsS0FBTThULEVBQThCSCxFQUE5QkcsY0FBZVAsRUFBZUksRUFBZkosV0FDbERRLEVBQWNOLEVBQU9HLEdBQzNCLE9BQVE1VCxFQUFLNlQsTUFBTSxnQkFnQmpCMUMsRUFBQUEsY0FBQUEsRUFBQUEsU0FBQSxLQUFFLFFBQ00sR0E1Qm1CLFNBQUMyQyxFQUFlUCxHQUMvQyxJQUNNUyxHQURhRixFQUFnQlAsR0FDR08sRUFBaUIsSUFFdkQsT0FEMEJHLEtBQUtDLE1BQU1GLEVBRXZDLENBd0JTRyxDQUF5QkwsRUFBZVAsR0FDekNwQyxFQUFBQSxjQUFBLFFBQU1DLFVBQVUsaUJBQWdCLE1BbEJsQ0QsRUFBQUEsY0FBQUEsRUFBQUEsU0FBQSxLQUFFLFFBQ0tBLEVBQUFBLGNBQUEsUUFBTUMsVUFBVSxtQkFBbUJvQyxHQUN4Q3JDLEVBQUFBLGNBQUEsUUFBTUMsVUFBVSxtQkFDYnRXLE1BQU1DLFFBQVFnWixHQUNiNUMsRUFBQUEsY0FBQUEsRUFBQUEsU0FBQSxLQUNHNEMsYUFBVyxFQUFYQSxFQUFjLEdBQ2Y1QyxFQUFBQSxjQUFBLFFBQU11QyxNQUFNLGVBQWMsS0FDMUJ2QyxFQUFBQSxjQUFBLFFBQU11QyxNQUFNLG9CQUFvQkssYUFBVyxFQUFYQSxFQUFjLEtBR2hEQSxHQVdWLENBVTRCSyxJQUc5QixFQ3hDQSxHQU5zQixTQUFIbEcsR0FBa0IsSUFBWm1HLEVBQUtuRyxFQUFMbUcsTUFDdkIsT0FBT0EsSUFBcUMsSUFBNUJBLEVBQU1DLE9BQU8sVUFDM0JuRCxFQUFBQSxjQUFBLFFBQU1DLFVBQVUsa0JBQWlCLFFBQy9CLElBQ04sa3ZDQ0pBLElBNkRBLEdBN0RvQixTQUFIbEQsR0FBOEIsSUFBeEJRLEVBQUlSLEVBQUpRLEtBQU02RixFQUFXckcsRUFBWHFHLFlBb0JuQkMsRUFDTjlGLEVBRE04Rix5QkFBMEJDLEVBQ2hDL0YsRUFEZ0MrRix3QkFBeUJDLEVBQ3pEaEcsRUFEeURnRyw0QkFFckRDLEVBQWUsQ0FDbkIsQ0FBRU4sTUFBT0ksRUFBeUJHLFNBQVUsS0FDNUMsQ0FBRVAsTUFBT0csRUFBMEJJLFNBQVUsS0FDN0MsQ0FBRVAsTUFKRjNGLEVBRHNGbUcsMkJBS2pERCxTQUFVLEtBQy9DLENBQUVQLE1BQU9LLEVBQTZCRSxTQUFVLE1BRzVDRSxHQUFTQyxFQUFBQSxFQUFBQSxRQUFPLE1BQ2hCQyxFQTdCWSxTQUFDdEcsR0FDakIsSUFFSXNHLEVBREVDLEVBQVkscUNBY2xCLE1BZmtCLENBQUMsZUFBZ0IsWUFBYSxjQUl0QzlOLFNBQVEsU0FBQytOLEdBQ2pCLElBQUtGLEdBQVV0RyxFQUFLd0csSUFBU0QsRUFBVXRJLEtBQUsrQixFQUFLd0csSUFFL0MsT0FEQUYsRUFBU3RHLEVBQUt3RyxFQUdsQixLQUVLRixHQUFVdEcsRUFBS3lHLFVBQVl6RyxFQUFLeUcsU0FBU2hiLFNBQzVDNmEsRUFBU3RHLEVBQUt5RyxTQUFTLElBR2xCSCxDQUNULENBWWVJLENBQVUxRyxHQUV6QixPQUNFeUMsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFNBQ2JELEVBQUFBLGNBQUEsT0FDRStCLElBQUs4QixFQUNMSyxJQUFLUCxFQUNMUSxRQUFTLFdBQ0hSLEVBQU9TLFNBQVdULEVBQU9TLFFBQVFyQyxNQUFRcUIsSUFDM0NPLEVBQU9TLFFBQVFyQyxJQUFNcUIsRUFFekIsSUFFREksRUFBYWpiLEtBQUksU0FBQStVLEdBQXlCLElBQXRCNEYsRUFBSzVGLEVBQUw0RixNQUFPTyxFQUFRbkcsRUFBUm1HLFNBQzFCLElBQUtQLEVBQU8sT0FBTyxLQUNuQixJQUFNbUIsRWxCMEZzQixTQUFDQyxHQUNuQyxJQUFNQyxFQUFlLENBQ25CZCxTQUFVLFdBQ1ZlLE1BQU8sTUFDUEMsT0FBUSxNQUNSQyxPQUFRLElBQ1JDLGVBQWdCLFVBQ2hCQyxpQkFBa0IsYUFFZEMsRUFBZTVYLEVBQUFBLEVBQUEsR0FDaEJzWCxHQUFZLElBQ2ZPLElBQUssRUFDTEMsbUJBQW9CLFFBRWhCQyxFQUFrQi9YLEVBQUFBLEVBQUEsR0FDbkJzWCxHQUFZLElBQ2ZVLE9BQVEsRUFDUkYsbUJBQW9CLFdBRXRCLE9BQVFULEdBQ04sSUFBSyxJQVFMLFFBQ0UsT0FBQXJYLEVBQUFBLEVBQUEsR0FBWTRYLEdBQWUsSUFBRUssS0FBTSxJQVByQyxJQUFLLElBQ0gsT0FBQWpZLEVBQUFBLEVBQUEsR0FBWTRYLEdBQWUsSUFBRU0sTUFBTyxJQUN0QyxJQUFLLElBQ0gsT0FBQWxZLEVBQUFBLEVBQUEsR0FBWStYLEdBQWtCLElBQUVFLEtBQU0sSUFDeEMsSUFBSyxJQUNILE9BQUFqWSxFQUFBQSxFQUFBLEdBQVkrWCxHQUFrQixJQUFFRyxNQUFPLElBSTdDLENrQnpIa0NDLENBQXFCM0IsR0FDL0MsT0FDRXpELEVBQUFBLGNBQUEsUUFDRS9YLElBQUtpYixFQUNMakQsVUFBVSxxQkFDVkYsTUFBSzlTLEdBQUFBLEdBQUEsR0FDQW9YLEdBQWlCLElBQ3BCZ0IsZ0JBQWlCLE9BQUZoVyxPQUFTNlQsRUFBSyxRQUlyQyxJQUdOLEVDcENBLEdBdkJzQixTQUFIbkcsR0FBa0MsSUFBNUIrSCxFQUFHL0gsRUFBSCtILElBQUtHLEVBQU1sSSxFQUFOa0ksT0FBUUssRUFBUXZJLEVBQVJ1SSxTQUM5QkMsRUFBYyxTQUFDckMsR0FDbkIsSUFBTTdCLEVBQU1pRSxFQUNUcGQsUUFBUSxpQkFBa0JnYixHQUMxQmhiLFFBQVEsZUFBZ0IsYUFFM0IsT0FBTzhYLEVBQUFBLGNBQUEsT0FBSytCLElBQUtWLEVBQUtXLElBQUksaUJBQzVCLEVBRUEsT0FBTzhDLEdBQU9HLEVBQ1pqRixFQUFBQSxjQUFBLFFBQU1DLFVBQVUsa0JBQ2I2RSxFQUNDOUUsRUFBQUEsY0FBQSxRQUFNQyxVQUFVLDJCQUEyQnNGLEVBQVlULElBQ3JELEtBQ0hHLEVBQ0NqRixFQUFBQSxjQUFBLFFBQU1DLFVBQVUsOEJBQ2JzRixFQUFZTixJQUViLE1BRUosSUFDTiwyT0NaQSxJQXFIQSxHQXJIZ0IsU0FBSGxJLEdBQTJCLElBQUF5SSxFQUFBQyxFQUFBQyxFQUFyQm5JLEVBQUlSLEVBQUpRLEtBQU1rRyxFQUFRMUcsRUFBUjBHLFNBQ2pCdFYsR0FBU3FTLEVBQUFBLEVBQUFBLEtBQVksU0FBQy9SLEdBQUssT0FBS0EsRUFBTU4sTUFBTSxJQUM1Q2IsR0FBUWtULEVBQUFBLEVBQUFBLEtBQVksU0FBQy9SLEdBQUssT0FBS0EsRUFBTWhCLFlBQVlHLFlBQWNhLEVBQU1uQixLQUFLLElBQ2hGcVksRWRVdUMsU0FBQ3BJLEdBQVMsSUFBQXZNLEVBQ2pELEdBQUt1TSxFQUFMLENBQ0EsSUFBTXFJLEVBQWtCLFFBQVQ1VSxFQUFHL0YsY0FBTSxJQUFBK0YsR0FBYSxRQUFiQSxFQUFOQSxFQUFRNUMsbUJBQVcsSUFBQTRDLE9BQUEsRUFBbkJBLEVBQXFCNFUsVUFDdkMsSUFBS0EsRUFDSCxNQUFPLENBQUV4RCxXQUFZN0UsYUFBSSxFQUFKQSxFQUFNM0ssTUFBT2lULGtCQUFtQnRJLGFBQUksRUFBSkEsRUFBTXVJLGFBQWNDLG1CQUFvQnhJLGFBQUksRUFBSkEsRUFBTW9GLGVBRXJHLElBQ01xRCxFQUFhLFFBQUgzVyxPQURBb0MsR0FBWW1VLElBSzVCLE1BQU8sQ0FBRXhELFlBSFU3RSxhQUFJLEVBQUpBLEVBQUksR0FBQWxPLE9BQU1pQyxJQUFXakMsT0FBRzJXLE9BQWlCekksYUFBSSxFQUFKQSxFQUFPak0sS0FHOUN1VSxtQkFGS3RJLGFBQUksRUFBSkEsRUFBSSxHQUFBbE8sT0FBTWtDLElBQW1CbEMsT0FBRzJXLE9BQWlCekksYUFBSSxFQUFKQSxFQUFPaE0sS0FFMUN3VSxvQkFEYnhJLGFBQUksRUFBSkEsRUFBSSxHQUFBbE8sT0FBTW1DLElBQW9CbkMsT0FBRzJXLE9BQWlCekksYUFBSSxFQUFKQSxFQUFPL0wsS0FUbkUsQ0FXbkIsQ2N0QmdFeVUsQ0FBMEIxSSxHQUFoRjZFLEVBQVV1RCxFQUFWdkQsV0FBWXlELEVBQWlCRixFQUFqQkUsa0JBQW1CRSxFQUFrQkosRUFBbEJJLG1CQUNqQ3pKLEdBQVdnRSxFQUFBQSxFQUFBQSxNQUVYNEYsRUFBaUIsU0FBQzdFLEVBQUs4RSxHQUMzQkEsRUFBRzdFLGlCQUNIaEYsRUFBU3dELElBQWlCLElBQ0EsV0FBdEJwSixHQUFPekwsT0FBT21iLE1BQW9CbmIsT0FBT21iLElBQUlDLFFBQy9DcGIsT0FBT3FiLFVBQVlyYixPQUFPcWIsV0FBYSxHQUN2Q3JiLE9BQU9tYixJQUFJRyxNQUFNSixFQUFHSyxjQUFldmIsT0FBT3FiLFdBQzFDcmIsT0FBT3FiLFVBQVU5TixLQUFLLENBQ3BCaU8sTUFBTyxtQkFDUEMsbUJBQW9CLENBQ2xCcFosTUFBT0EsTUFJYnJDLE9BQU91VyxTQUFXSCxDQUNwQixFQUVNc0YsRUFBaUJ4WSxFQUFPeVksbUJBQXFCLEdBQzdDakUsRUFDWSxXQUFoQnBGLEVBQUtzSixRQUF1QmhCLEVBQ3hCekQsR0FBYyxHQUFLLElBQU15RCxHQUFxQixLQUM5Q0UsRUFDQWMsRUFBU3RKLEVBQUt1SixJQUFJQyxTQUFTLEtBQU8sZUFBaUJ4SixFQUFLc0osT0FFMURHLEdBQWdCLEVBQ2RDLEVBQW1CMWQsYUFBYSxzQkFDaEMyZCxFQUFnRCxVQUFwQzNKLFNBQWdCLFFBQVppSSxFQUFKakksRUFBTTRKLGtCQUFVLElBQUEzQixPQUFBLEVBQWhCQSxFQUFrQnJkLGVBQzlCc2EsS0FBVUUsR0FBaUJBLElBQWtCUCxJQUFhTyxFQUFnQlAsRUFDMUVnRixPQUFrQyxJQUFmN0osRUFBSzNLLFFBQTBCb1UsR0FBcUQsVUFBcEN6SixTQUFnQixRQUFaa0ksRUFBSmxJLEVBQU00SixrQkFBVSxJQUFBMUIsT0FBQSxFQUFoQkEsRUFBa0J0ZCxlQUNyRmtmLEVBQ08saUJBQVhSLEdBQ0FwRSxHQUNBbEYsRUFBSytKLGlCQUNKTixHQUNtQyxVQUFwQ3pKLFNBQWdCLFFBQVptSSxFQUFKbkksRUFBTTRKLGtCQUFVLElBQUF6QixPQUFBLEVBQWhCQSxFQUFrQnZkLGVBQ3BCLFFBQWdDLElBQXJCOGUsRUFBa0MsQ0FDM0MsSUFBTU0sRUFBZWxlLEtBQUtDLE1BQU0yZCxHQUNoQ0QsRUFBZ0JPLEVBQWFDLFNBQVdELEVBQWFDLFFBQVFDLG9CQUMvRCxDQUVBLE9BQ0V6SCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsOEJBQThCeUgsS0FBSyxVQUFVQyxHQUFJLFVBQVlwSyxFQUFLdUosS0FDL0U5RyxFQUFBQSxjQUFBLEtBQ0VDLFVBQVUsMEJBQ1Z3QixLQUFNbEUsRUFBS3FLLFdBQ1gsVUFBU3JLLEVBQUt1SixJQUNkLFlBQVd2SixFQUFLc0ssTUFDaEIsYUFBWXRLLEVBQUszSyxNQUNqQixnQkFBZTZRLEVBQ2YsYUFBWWxHLEVBQUttQyxPQUFTbkMsRUFBS21DLE1BQU0xVyxPQUFTdVUsRUFBS21DLE1BQU0sR0FBSyxHQUM5RCxnQkFBZSxxQkFDZixZQUFXLHFCQUNYLGFBQVksZUFDWixhQUFZdlIsRUFBT3lYLFdBQWEsR0FDaEMsa0JBQWlCLEtBQ2pCeEUsUUFBUyxTQUFDK0UsR0FBRSxPQUFLRCxFQUFlM0ksRUFBS3FLLFdBQVl6QixFQUFHLEdBRXBEbkcsRUFBQUEsY0FBQzhILEdBQVcsQ0FBQ3ZLLEtBQU1BLEVBQU02RixZQUFhdUQsSUFDdEMzRyxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsUUFDYkQsRUFBQUEsY0FBQSxPQUFLMEgsS0FBSyxRQUFRekgsVUFBVSxpQkFDekIxQyxFQUFLc0ssT0FFUjdILEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxPQUFNLFFBQU0xQyxFQUFLd0ssUUFDaEMvSCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsYUFDWm1ILEdBQWFGLEVBQ1psSCxFQUFBQSxjQUFDZ0ksR0FBSyxDQUFDM0YsU0FBVWxVLEVBQU84WixxQkFBdUIsSUFBSzdGLFdBQVlBLEVBQVlFLE9BQVEzUCxLQUNsRixLQUNIMFUsR0FBY0gsRUFDYmxILEVBQUFBLGNBQUFBLEVBQUFBLFNBQUEsS0FDRUEsRUFBQUEsY0FBQ2tJLEdBQU8sQ0FDTjdGLFNBQVVsVSxFQUFPOFoscUJBQXVCLElBQ3hDeEYsUUFBU0EsRUFDVEwsV0FBWUEsRUFDWk8sY0FBZUEsRUFDZkwsT0FBUTNQLEdBQ1I5RCxLQUFNME8sRUFBSytKLGtCQUdiLE1BRU50SCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsd0JBQ2JELEVBQUFBLGNBQUEsVUFDRUMsVUFBVSwyQkFDVixVQUFTMUMsRUFBS3VKLElBQ2QsWUFBV3ZKLEVBQUtzSyxNQUNoQixhQUFZdEssRUFBSzNLLE1BQ2pCLGdCQUFlLEVBQ2YsZ0JBQWU2USxFQUNmLGFBQVlsRyxFQUFLbUMsT0FBU25DLEVBQUttQyxNQUFNMVcsT0FBU3VVLEVBQUttQyxNQUFNLEdBQUssR0FDOUQsZ0JBQWUsaUJBQ2YsWUFBVyxpQkFDWCxhQUFZLGVBQ1osYUFBWXZSLEVBQU95WCxXQUFhLEdBQ2hDLGtCQUFpQixLQUNqQnhFLFFBQVMsU0FBQy9XLEdBQUMsT0FBSzZiLEVBQWUzSSxFQUFLcUssV0FBWXpCLEdBQUcsR0FFbkRuRyxFQUFBQSxjQUFBLFlBQU0sa0JBSVZBLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxrQkFDYkQsRUFBQUEsY0FBQ21JLEdBQWEsQ0FBQ2pGLE1BQU8zRixFQUFLNkssZ0JBQzFCamEsRUFBT2thLGtCQUNOckksRUFBQUEsY0FBQ3NJLEdBQWEsQ0FBQ3hELElBQUt2SCxFQUFLZ0wsU0FBVXRELE9BQVExSCxFQUFLaUwsWUFBYWxELFNBQVVuWCxFQUFPc2Esb0JBQzVFLFFBTWhCLEVDN0VBLEdBM0NpQixTQUFIMUwsR0FBMkMsSUFBckN4UCxFQUFRd1AsRUFBUnhQLFNBQVVLLEVBQVVtUCxFQUFWblAsV0FBWW1DLEVBQVFnTixFQUFSaE4sU0FFaEN1TSxHQURVb00sU0FBU0MsZUFBZSxxQkFDdkJySSxFQUFBQSxFQUFBQSxPQW1CakIsT0FBTy9TLEdBQVlBLEVBQVN2RSxPQUFTLEVBQ2pDZ1gsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLGdDQUNYRCxFQUFBQSxjQUFBLFVBQUksa0JBQ0hwUyxHQUNHb1MsRUFBQUEsY0FBQSxPQUNJQyxVQUFTLDJCQUFBNVEsT0FDTDlCLEVBQVN2RSxPQUFTLEVBQUksVUFBWSxLQUd0Q2dYLEVBQUFBLGNBQUEsS0FDSUMsVUFBVSxpQ0FDVm1CLFFBdEJBLFdBQ2hCLElBQUlDLEVBQU0sNEJBQUhoUyxPQUErQmtTLG1CQUFtQjNULElBRXJEbUMsSUFDQXNSLEVBQU10UixHQUVWdU0sRUFBU3dELElBQWlCLElBQzFCalgsRUFBdUIrRSxHQUN2QjNDLE9BQU91VyxTQUFTQyxLQUFPSixDQUMzQixHQWNpQix3QkFDd0JyQixFQUFBQSxjQUFBLGdCQUFBM1EsT0FBV3pCLEVBQVUsUUFJdERvUyxFQUFBQSxjQUFBLFdBQXFCelMsRUFqQ1poRixLQUFJLFNBQUM2SixFQUFNNk8sR0FDcEIsT0FBT2pCLEVBQUFBLGNBQUM0SSxHQUFPLENBQUMzZ0IsSUFBS2daLEVBQUt3QyxTQUFVeEMsRUFBTSxFQUFHMUQsS0FBTW5MLEdBQ3ZELE1BaUNBLElBQ1IsRUNYQSxHQS9Cb0IsV0FDbEIsSUFBQW1PLEdBQTBDQyxFQUFBQSxFQUFBQSxLQUFZLFNBQUMvUixHQUFLLE9BQUtBLEVBQU1ULFdBQVcsSUFBbkVBLEVBQVd1UyxFQUFsQjFTLE1BQW9CQyxFQUFTeVMsRUFBVHpTLFVBQ3RCd08sR0FBV2dFLEVBQUFBLEVBQUFBLE1BaUJqQixPQUFPdFMsR0FBZUEsRUFBWWhGLE9BQ2hDZ1gsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLG1DQUNiRCxFQUFBQSxjQUFBLFFBQU1DLFVBQVUsT0FBTSx3QkFDckJqUyxhQUFXLEVBQVhBLEVBQWF6RixLQUFJLFNBQUNzZ0IsRUFBTTVILEdBQUcsT0FDMUJqQixFQUFBQSxjQUFBLFFBQU1DLFVBQVUsTUFBTWhZLElBQUtnWixFQUFLRyxRQUFTLFNBQUMrRSxHQUFFLE9BcEI5QixTQUFDcmQsRUFBTXFkLEdBQ3pCQSxFQUFHN0UsaUJBQ0gsSUFHSUQsRUFIRXlILEVBQWlCMWdCLEVBQVdVLEdBRTVCaUgsRUFBV2pDLEdBQWFBLEVBQVVnYixHQUFrQmhiLEVBQVVnYixHQUFrQixLQUdwRnpILEVBREV0UixHQUdJLDRCQUFIVixPQUErQmtTLG1CQUFtQnpZLElBRXZEd1QsRUFBU3dELElBQWlCLElBQzFCalgsRUFBdUJDLEdBQ3ZCbUMsT0FBT3VXLFNBQVNDLEtBQU9KLENBQ3pCLENBTXVESyxDQUFZbUgsRUFBTTFDLEVBQUcsR0FDbkUvZCxFQUFXeWdCLEdBQ1AsS0FHVCxJQUNOLEVDQ0EsR0E1QmlCLFNBQUg5TCxHQUE2QixJQUF2Qm1ELEVBQUluRCxFQUFKbUQsS0FBTXRTLEVBQVVtUCxFQUFWblAsV0FDeEIyUyxHQUdJQyxFQUFBQSxFQUFBQSxLQUFZLFNBQUMvUixHQUFLLE9BQUtBLENBQUssSUFGOUJ0QixFQUFXb1QsRUFBWHBULFlBQVc0YixFQUFBeEksRUFDWHZTLFlBQWVULEVBQVF3YixFQUFSeGIsU0FBaUJTLEVBQVcrYSxFQUFsQmxiLE1BQXdDbWIsRUFBaUJELEVBQXJDOWEsbUJBR3pDcU8sR0FBV2dFLEVBQUFBLEVBQUFBLE1BUWpCLE9BTkFRLEVBQUFBLEVBQUFBLFlBQVUsV0FDSjlTLEdBQWVBLEVBQVksSUFDN0JzTyxFQUFTaUMsR0FBMEJ2USxFQUFZLElBRW5ELEdBQUcsQ0FBQ0EsYUFBVyxFQUFYQSxFQUFhaEYsT0FBUXNULElBRWxCNEQsR0FBUXRTLEVBQ2JvUyxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsaUNBQ2JELEVBQUFBLGNBQUEsU0FBRyx3QkFDb0JBLEVBQUFBLGNBQUEsa0JBQUEzUSxPQUFhekIsRUFBVSxPQUU1Q1QsRUFBZ0MsS0FBbEI2UyxFQUFBQSxjQUFDaUosR0FBVyxNQUM1QmpKLEVBQUFBLGNBQUEsT0FBS0QsTUFBTyxDQUFFbUosVUFBVyxTQUN2QmxKLEVBQUFBLGNBQUNtSixHQUFhLENBQUNqSixLQUFNOEksSUFDckJoSixFQUFBQSxjQUFDb0osR0FBUSxDQUFDN2IsU0FBVUEsTUFHdEIsSUFDTixxaUNDekJBLElBNE5BLEdBNU51QixXQUNyQixJQUFBZ1QsR0FBdUZDLEVBQUFBLEVBQUFBLEtBQVksU0FBQy9SLEdBQUssT0FBS0EsQ0FBSyxJQUEzR3pELEVBQVl1VixFQUFadlYsYUFBY0UsRUFBV3FWLEVBQVhyVixZQUFhQyxFQUFxQm9WLEVBQXJCcFYsc0JBQXVCaUMsRUFBWW1ULEVBQVpuVCxhQUFjUSxFQUFVMlMsRUFBVjNTLFdBQzVCOFMsRUFBQUMsSUFBWkMsRUFBQUEsRUFBQUEsVUFBUyxJQUFHLEdBQXJDclQsRUFBUW1ULEVBQUEsR0FBRTJJLEVBQVczSSxFQUFBLEdBQ3RCNEksRUFBY1osU0FBU0MsZUFBZSxVQUN0Q3JNLEdBQVdnRSxFQUFBQSxFQUFBQSxNQUVYaUosRUFBbUIsV0FDdkIsR0FBSXZlLFNBQUFBLEVBQWNoQyxPQUFRLENBQ3hCc1QsRUFBU3NELElBQWdCeFMsSUFDekIsSUFBTW9jLEVBQVdkLFNBQVNlLGNBQWMsY0FDcEM3YixHQUFjQSxFQUFXNUUsT0FBUyxFQUNwQ3dnQixFQUFTekosTUFBTTJKLFFBQVUsUUFFekJGLEVBQVN6SixNQUFNMkosUUFBVSxNQUU3QixDQUNGLEVBRU1DLEVBQWlCLFdBQ3JCLElBQU1ILEVBQVdkLFNBQVNlLGNBQWMsY0FDcENILEVBQVk1ZixNQUFNVixPQUFTLElBQzdCd2dCLEVBQVN6SixNQUFNMkosUUFBVSxRQUU3QixFQUVNRSxFQUFzQixXQUMxQnROLEVBQVN1RCxLQUNYLEVBRU1nSyxFQUFlLFNBQUNwRCxHQUNZLGFBQTVCQSxFQUFNcUQsT0FBT0MsWUFDZnpOLEVBQVN1RCxLQUViLEVBcUJNbUssRUFBbUIsV0FDdkIsSUFFTUMsRUFBeUJsYixPQUFPOEcsT0FBTzFLLEdBQzFDVCxRQUFPLFNBQUN3ZixHQUNQLElBQU1DLEVBQWFqZixFQUFZbUgsZUFBZTZYLEVBQWlCLFlBQ3pERSxFQUFjLElBQUlDLEtBQ2xCQyxFQUFjLElBQUlELEtBQXFCLElBQWhCSCxFQUFLSyxVQUdsQyxPQUFPSixHQUFjRCxFQUFLTSxXQUFhdmYsT0FBT3dmLFNBQVNDLFdBQWFOLEVBQWNFLEVBRmxFLElBR2xCLElBQ0NLLE1BQUssU0FBQ0MsRUFBR0MsR0FDUixPQUFPQSxFQUFFTixTQUFXSyxFQUFFTCxRQUN4QixJQUNDaGlCLEtBQUksU0FBQ3VpQixHQUNKLE9BQU81ZixFQUFZNGYsRUFBT0MsV0FDNUIsSUFFRWQsRUFBdUJqaEIsT0FsQkcsSUFtQjVCaWhCLEVBQXVCamhCLE9BbkJLLEdBc0I5QnFnQixFQUFZWSxFQUNkLEVBZ0NBLE9BOUJBbkosRUFBQUEsRUFBQUEsWUFBVSxXQUFNLElBQUE5UCxFQUFBZ2EsRUFLZCxPQUpBaEIsSUFDTSxRQUFOaFosRUFBQS9GLGNBQU0sSUFBQStGLEdBQU5BLEVBQVFpYSxpQkFBaUIsVUFBV3JCLEdBQXFCLEdBQzFDLFFBQWZvQixFQUFBL2YsT0FBT3lkLGdCQUFRLElBQUFzQyxHQUFmQSxFQUFpQkMsaUJBQWlCLG1CQUFvQnBCLEdBQWMsR0FFN0QsV0FBTSxJQUFBcUIsRUFBQUMsRUFDTCxRQUFORCxFQUFBamdCLGNBQU0sSUFBQWlnQixHQUFOQSxFQUFRRSxvQkFBb0IsVUFBV3hCLEdBQXFCLEdBQzdDLFFBQWZ1QixFQUFBbGdCLE9BQU95ZCxnQkFBUSxJQUFBeUMsR0FBZkEsRUFBaUJDLG9CQUFvQixtQkFBb0J2QixHQUFjLEVBQ3pFLENBQ0YsR0FBRyxLQUVIL0ksRUFBQUEsRUFBQUEsWUFBVSxXQUFNLElBQUF1SyxFQUFBQyxHQUNRdmMsY0FBbUMsUUFBN0JzYyxFQUFOdGMsT0FBUThLLEtBQUsxTyxVQUFzQixJQUFBa2dCLE9BQUEsRUFBbkNBLEVBQXFDcmlCLFdBQ2pDK0YsY0FBbUMsUUFBN0J1YyxFQUFOdmMsT0FBUThLLEtBQUsxTyxVQUFzQixJQUFBbWdCLE9BQUEsRUFBbkNBLEVBQXFDdGlCLFNBRzdEZ2hCLEdBRUosR0FBRyxDQUFDN2UsS0FFSjJWLEVBQUFBLEVBQUFBLFlBQVUsV0FJUixPQUhBd0ksU0FBQUEsRUFBYTJCLGlCQUFpQixRQUFTMUIsR0FBa0IsR0FDekRELFNBQUFBLEVBQWEyQixpQkFBaUIsU0FBVXRCLEdBQWdCLEdBRWpELFdBQ0xMLFNBQUFBLEVBQWE4QixvQkFBb0IsUUFBUzdCLEdBQWtCLEdBQzVERCxTQUFBQSxFQUFhOEIsb0JBQW9CLFNBQVV6QixHQUFnQixFQUM3RCxDQUNGLElBR0UzSixFQUFBQSxjQUFBLE9BQUtDLFVBQVUsd0JBQ1pqVixFQUFhaEMsT0FBUyxHQUNyQmdYLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxrQkFDYkQsRUFBQUEsY0FBQ2UsR0FBVyxDQUFDYixNQUFNLEVBQU10QyxRQUFRLGtEQUNqQ29DLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxZQUNiRCxFQUFBQSxjQUFBLFVBQUkscUJBQ0pBLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxhQUNiRCxFQUFBQSxjQUFBLEtBQUdvQixRQTdFZ0IsV0FDN0IvVixFQUFnQiwwQkFBMkJoQyxLQUFLeUIsVUFBVSxLQUMxRHdSLEVBQVN1RCxLQUNYLEdBMEVnRCxlQUd4Q0csRUFBQUEsY0FBQSxPQUFLQyxVQUFVLGtDQUNiRCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsV0FDYkQsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLG1CQUNiRCxFQUFBQSxjQUFBLE1BQUlDLFVBQVUsbUJBQ1hqVixJQUNDQSxhQUFZLEVBQVpBLEVBQWN6QyxLQUFJLFNBQUMyaEIsR0FBSSxPQUNyQmxLLEVBQUFBLGNBQUEsTUFBSS9YLElBQUtpaUIsRUFBTWpLLFVBQVUsd0JBQ3ZCRCxFQUFBQSxjQUFBLEtBQUdvQixRQUFTLGtCQWxGTDFYLEVBa0YrQndnQixFQWpGcERxQixFQUFXN0MsU0FBU2UsY0FBYyxjQUN4Q0osRUFBWSxJQUNaQyxFQUFZNWYsTUFBUUEsRUFDcEJ1QixPQUFPdVcsU0FBU0MsS0FBTyw0QkFBSHBTLE9BQStCM0YsUUFDbkQ2aEIsRUFBU3hMLE1BQU0ySixRQUFVLFFBTEMsSUFBQ2hnQixFQUNyQjZoQixDQWlGeUQsRUFBRXRMLFVBQVUseUJBQ3BEaUssR0FFSGxLLEVBQUFBLGNBQUEsS0FBR0MsVUFBVSw4QkFBOEJtQixRQUFTLGtCQWhHN0NySSxFQWdHdUVtUixFQS9GNUZzQixFQUFXeGdCLEVBQWFOLFFBQU8sU0FBQ3dmLEdBQUksT0FBS0EsSUFBU25SLENBQUMsSUFDekQxTixFQUFnQiwwQkFBMkJoQyxLQUFLeUIsVUFBVTBnQixTQUMxRGxQLEVBQVN1RCxNQUhpQixJQUFDOUcsRUFDckJ5UyxDQStGaUcsSUFDaEYsVUFTcEJqZSxFQUFTdkUsT0FBUyxHQUNqQmdYLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSw0QkFDYkQsRUFBQUEsY0FBQSxVQUFJLDRCQUNKQSxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsdUJBQ1oxUyxFQUFTaEYsS0FBSSxTQUFDa2pCLEVBQVN4SyxHQUFRLElBQUF5SyxFQUN4QkMsRUFBd0JoWixHQUFZOFksRUFBUUcsV0FBV0MsYUFDN0QsT0FDRTdMLEVBQUFBLGNBQUEsT0FBSy9YLElBQUtnWixFQUFLaEIsVUFBVSw4QkFBOEJ5SCxLQUFLLFVBQVVDLEdBQUUsV0FBQXRZLE9BQWFvYyxFQUFROUQsS0FDM0YzSCxFQUFBQSxjQUFBLEtBQ0VDLFVBQVUsMEJBQ1Z3QixLQUFNZ0ssRUFBUXBLLElBQ2QsVUFBUSxhQUNSLFlBQVUsZUFDVixhQUFXLGlDQUNYLGdCQUFjLEdBQ2QsYUFBVyxHQUNYLGdCQUFjLEdBQ2QsWUFBVSxHQUNWLGFBQVcsR0FDWCxhQUFXLEdBQ1gsa0JBQWdCLE1BRWhCckIsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFNBQ2JELEVBQUFBLGNBQUEsT0FDRStCLElBQUswSixFQUFRSyxxQkFBcUJDLGVBQWlCTixFQUFRTyxPQUFPLEdBQUczSyxJQUNyRVcsSUFBS3lKLEVBQVFwUyxRQUdqQjJHLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxRQUNiRCxFQUFBQSxjQUFBLE9BQUswSCxLQUFLLFFBQVF6SCxVQUFVLGlCQUN6QndMLEVBQVFwUyxNQUVYMkcsRUFBQUEsY0FBQSxPQUFLQyxVQUFVLGdCQUNvQyxLQUFoRHdMLFNBQTZCLFFBQXRCQyxFQUFQRCxFQUFTSyw0QkFBb0IsSUFBQUosT0FBQSxFQUE3QkEsRUFBK0JPLGVBQzlCak0sRUFBQUEsY0FBQUEsRUFBQUEsU0FBQSxLQUNFQSxFQUFBQSxjQUFBLFFBQU1DLFVBQVUsaUJBQ2RELEVBQUFBLGNBQUEsUUFBTUMsVUFBVSxtQkFBa0IsS0FDbENELEVBQUFBLGNBQUEsUUFBTUMsVUFBVSxjQUNidFcsTUFBTUMsUUFBUStoQixHQUNiM0wsRUFBQUEsY0FBQUEsRUFBQUEsU0FBQSxLQUNHMkwsYUFBcUIsRUFBckJBLEVBQXdCLEdBQ3pCM0wsRUFBQUEsY0FBQSxRQUFNdUMsTUFBTSxlQUFjLEtBQzFCdkMsRUFBQUEsY0FBQSxRQUFNdUMsTUFBTSxpQkFBaUJvSixhQUFxQixFQUFyQkEsRUFBd0IsS0FHdkRBLE1BT1ozTCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsd0JBQ2JELEVBQUFBLGNBQUEsS0FDRUMsVUFBVSwyQkFDVixnQkFBZSxFQUNmLGdCQUFlLGlCQUNmLFlBQVcsaUJBQ1gsYUFBWSxlQUNaLGtCQUFpQixNQUVqQkQsRUFBQUEsY0FBQSxZQUFNLGtCQUdWQSxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsa0JBQ2JELEVBQUFBLGNBQUNtSSxHQUFhLENBQUNqRixNQUFPdUksRUFBUXJELG1CQU0xQyxNQU1aLHFpQ0NyTkEsSUFBTThELEdBQXdCeEQsU0FBU0MsZUFBZSxVQUNoRHdELEdBQWF6RCxTQUFTQyxlQUFlLG9CQUNyQ3lELEdBQWVELEdBQVcxQyxjQUFjLHlCQWlKOUMsR0EvSW9CLFNBQUgxTSxHQUErRCxJQUF6RHNQLEVBQVN0UCxFQUFUc1AsVUFBV2hNLEVBQWdCdEQsRUFBaEJzRCxpQkFBa0JpTSxFQUFxQnZQLEVBQXJCdVAsc0JBQ2xEL0wsR0FJSUMsRUFBQUEsRUFBQUEsS0FBWSxTQUFDL1IsR0FBSyxPQUFLQSxDQUFLLElBSDlCckIsRUFBWW1ULEVBQVpuVCxhQUNBRCxFQUFXb1QsRUFBWHBULFlBQVdvZixFQUFBaE0sRUFDWDlTLFlBQWVHLEVBQVUyZSxFQUFWM2UsV0FBWUQsRUFBUTRlLEVBQVI1ZSxTQUFVRyxFQUFTeWUsRUFBVHplLFVBRWpDaUMsRUFBV2pDLEdBQWFBLEVBQVVGLEdBQWNFLEVBQVVGLEdBQWMsS0FDeEUwTyxHQUFXZ0UsRUFBQUEsRUFBQUEsTUFDWGtNLEdBQWlCNUksRUFBQUEsRUFBQUEsVUFDcUJsRCxFQUFBQyxJQUFaQyxFQUFBQSxFQUFBQSxVQUFTLElBQUcsR0FBckM2TCxFQUFRL0wsRUFBQSxHQUFFZ00sRUFBV2hNLEVBQUEsSUFFNUJJLEVBQUFBLEVBQUFBLFlBQVUsV0FBTSxJQUFBNkwsRUFDVk4sSUFBV0csU0FBdUIsUUFBVEcsRUFBZEgsRUFBZ0JwSSxlQUFPLElBQUF1SSxHQUF2QkEsRUFBeUJDLFFBQzFDLEdBQUcsQ0FBQ1AsSUFFSixJekIyRnVCUSxFQUFNQyxFQUN6QkMsRXlCaEVFQyxHekIrRGlCSCxFeUIvRFksU0FBQ25qQixJQTVCYixTQUFDdWpCLEdBQ3RCLElBSTREQyxFQUFBQyxFQUFBQyxFQUp0REMsRUFBYzNFLFNBQVNDLGVBQWUsb0JBQ3RDMkUsRUFBZ0I1RSxTQUFTQyxlQUFlLHNCQUN4QzRFLEVBQVc3RSxTQUFTOEUsdUJBQXVCLGFBRTdDckIsS0FBZUEsR0FBV3NCLFVBQVVDLFNBQVMsWUFDL0N2QixVQUFxQixRQUFYZSxFQUFWZixHQUFZc0IsaUJBQVMsSUFBQVAsR0FBckJBLEVBQXVCbmdCLElBQUksVUFDM0JzZ0IsU0FBc0IsUUFBWEYsRUFBWEUsRUFBYUksaUJBQVMsSUFBQU4sR0FBdEJBLEVBQXdCcGdCLElBQUksVUFDNUJ1Z0IsU0FBd0IsUUFBWEYsRUFBYkUsRUFBZUcsaUJBQVMsSUFBQUwsR0FBeEJBLEVBQTBCcmdCLElBQUksV0FHNUJ3Z0IsRUFBU3ZrQixTQUNYdWtCLEVBQVMsR0FBR3hOLE1BQU0ySixRQUFVLFFBRzlCcE4sRUFBU0QsR0FBWTRRLElBQ3JCLElBQUkzZixFQUFRMmYsR0FBYyxJQUNyQjNmLEdBQVNBLEVBQU10RSxPQUFTLEVBQzNCc1QsRUFBU0csTUFFVEgsRUFBU0csTUFDTjdGLE1BQUssa0JBQU0wRixFQUFTRCxHQUFZL08sR0FBTyxJQUN2Q3NKLE1BQUssa0JBQU0wRixFQUFTUSxHQUFpQnhQLEdBQU8sSUFDNUNzSixNQUFLLGtCQUFNMEYsRUFBU08sR0FBZ0J2UCxHQUFPLElBQzNDc0osTUFBSyxrQkFBTTBGLEVBQVNxQyxHQUFnQnJSLEdBQU8sR0FBTSxHQUV4RCxDQUdFcWdCLENBQWVqa0IsRUFDakIsRXpCNkQ2Qm9qQixFeUI3RDFCLEl6QmdFSSxXQUFtQixRQUFBYyxFQUFBN2tCLFVBQUFDLE9BQU5vVixFQUFJLElBQUF6VSxNQUFBaWtCLEdBQUFDLEVBQUEsRUFBQUEsRUFBQUQsRUFBQUMsSUFBSnpQLEVBQUl5UCxHQUFBOWtCLFVBQUE4a0IsR0FDdEIsSUFBTTlZLEVBQVUrWSxLQUVoQkMsYUFBYWhCLEdBRWJBLEVBQVlpQixZQUFXLFdBQ3JCbkIsRUFBS3hPLE1BQU10SixFQUFTcUosRUFDdEIsR0FBRzBPLEVBQ0wsR3lCdEVNbUIsRUFBZSxTQUFDeEgsR0FDcEIsSW5CT0l5SCxFbUJQRUMsRUFBVzFILEVBQU1xRCxPQUFPcGdCLE1BQzFCMGtCLEVBQWEsR0FDQUQsRW5CSkF6TCxNQURGLHVDbUJTYjBMLEVuQkU0QixNQUQxQkYsRW1CRm9DQyxFbkJFZmptQixRQURILDBDQUM0QixLQUMvQmlYLE9BQWdCLEdBQUsrTyxFQUFjL08sT21CTHBEaVAsRUFBYUQsRUFNWGpDLEtBQ0ZBLEdBQXNCeGlCLE1BQVEwa0IsR0FFaEMxQixFQUFZMEIsR0FDWnBCLEVBQWtCb0IsRUFDcEIsRUFNTUMsRUFBb0IsU0FBQ2xJLEdBQ3pCQSxFQUFHN0UsaUJBQ0g2RSxFQUFHbUksa0JBQ0M3QixJQUFZQSxhQUFRLEVBQVJBLEVBQVV6akIsU0FBVSxJQUNsQ3NqQixTQUFBQSxHQUF3QixHQUN4QmlDLElBRUosRUFFTUEsRUFBdUIsV0FDM0IsSW5CdkNxQ2poQixFbUJ1Qy9CeEUsR0FBT29qQixjQUFxQixFQUFyQkEsR0FBdUJ4aUIsUUFBUytpQixHQUFZLEdBQ3pELEdBQUkzakIsRUFBTSxDQUNSLElBQU0wbEIsRW5CeENlbGhCLE9BRGNBLEVtQnlDY3hFLFFuQnhDdkIsRUFBTHdFLEVBQU9wRixRQUFRLE9BQVEsSW1CeUN4Q21aLEVBQU1FLG1CQUFtQmlOLEdBQWdCLDRCQUFBbmYsT0FDYmtTLG1CQUFtQmlOLElBQy9DLEdBQ0osR0FBSW5OLEVBR0YsT0FGQXhZLEVBQXVCMmxCLFFBQ3ZCdmpCLE9BQU91VyxTQUFTQyxLQUFPSixFQUczQixDQUNGLEVBRU1vTixFQUFtQixTQUFDcGtCLEdBQ3hCQSxFQUFFaWtCLGtCQUNGamtCLEVBQUVpWCxpQkFDRTFULElBQWVSLEdBQWNrUCxFQUFTc0QsSUFBZSxHQUMzRCxFQXdCQSxPQXRCQWtCLEVBQUFBLEVBQUFBLFlBQVUsV0FHUixPQUZBb0wsVUFBQUEsR0FBdUJqQixpQkFBaUIsUUFBU2dELEdBRTFDLFdBQ0QvQixLQUNGQSxVQUFBQSxHQUF1QmQsb0JBQW9CLFFBQVM2QyxHQUNwRC9CLEdBQXNCZCxvQkFBb0IsUUFBU3FELEdBQWtCLEdBQ3JFdEMsR0FBV2Ysb0JBQW9CLFNBQVVpRCxHQUN6Q2pDLFVBQUFBLEdBQWNoQixvQkFBb0IsUUFBU2lELEdBRS9DLENBQ0YsR0FBRyxLQUVIdk4sRUFBQUEsRUFBQUEsWUFBVSxXQUNSb0wsR0FBc0JqQixpQkFBaUIsUUFBU3dELEVBQ2xELEdBQUcsQ0FBQzdnQixFQUFZUixLQUVoQjBULEVBQUFBLEVBQUFBLFlBQVUsV0FDUnFMLFVBQUFBLEdBQVlsQixpQkFBaUIsU0FBVW9ELEdBQ3ZDakMsVUFBQUEsR0FBY25CLGlCQUFpQixRQUFTb0QsRUFDMUMsR0FBRyxDQUFDNUIsRUFBVXRmLEVBQWFRLEVBQVVDLEVBQVltQyxJQUUxQ3NRLEVBQ0xMLEVBQUFBLGNBQUFBLEVBQUFBLFNBQUEsS0FDRUEsRUFBQUEsY0FBQSxTQUNFa0UsSUFBS3NJLEVBQ0x2TSxVQUFVLGFBQ1ZwUixLQUFLLE9BQ0x3SyxLQUFLLFNBQ0wzUCxNQUFPK2lCLEVBQ1BySixZQUFZLHFCQUNac0wsVUFBVSxNQUNWaEgsS0FBSyxXQUNMaUgsU0FBVVYsRUFDVjdNLFFBcEVjLFdBQU0sSUFBQXdOLEVBQ3hCcEMsU0FBdUIsUUFBVG9DLEVBQWRwQyxFQUFnQnBJLGVBQU8sSUFBQXdLLEdBQXZCQSxFQUF5QmhDLE9BQzNCLElBb0VJNU0sRUFBQUEsY0FBQSxPQUFLQyxVQUFVLFdBQ2JELEVBQUFBLGNBQUEsVUFBUW5SLEtBQUssU0FBU29SLFVBQVUsZ0JBQWdCbUIsUUFBUyxTQUFDL1csR0FBQyxPQUFLZ2tCLEVBQWtCaGtCLEVBQUUsR0FBRSxZQUt4RixJQUNOLEVDMUpNaWpCLEdBQWdCNUUsU0FBU0MsZUFBZSxzQkFDNUMwRSxHQUFjM0UsU0FBU0MsZUFBZSxvQkFnQnhDLEdBZGtCLFNBQUNuRyxJQUNqQjFCLEVBQUFBLEVBQUFBLFlBQVUsV0FFUixPQURBNEgsU0FBU3VDLGlCQUFpQixZQUFhNEQsR0FBb0IsR0FDcEQsa0JBQU1uRyxTQUFTMEMsb0JBQW9CLFlBQWF5RCxHQUFvQixFQUFNLENBQ25GLEdBQUcsSUFFSCxJQUFNQSxFQUFxQixTQUFDeGtCLEdBQ3RCZ2pCLElBQWVoakIsRUFBRXlmLE9BQU80RCxTQUFTTCxLQUNuQ0MsSUFBaUJBLEdBQWNHLFVBQVVxQixPQUFPLFNBRXBELEVBQ0EsT0FBTzlPLEVBQUFBLGNBQUMrTyxHQUFnQnZNLEVBQzFCLEVDZUEsR0FoQ3lCLFdBQU0sSUFBQXhSLEVBQ3ZCZ2UsRUFBMkIsUUFBVGhlLEVBQUcvRixjQUFNLElBQUErRixHQUFhLFFBQWJBLEVBQU5BLEVBQVE1QyxtQkFBVyxJQUFBNEMsT0FBQSxFQUFuQkEsRUFBcUJnZSxtQkFDMUNDLEVBQTBCRCxhQUFrQixFQUFsQkEsRUFBb0IxbUIsTUFBTSxLQUFLb0MsUUFBTyxTQUFBNUIsR0FBSSxNQUFvQixLQUFoQkEsRUFBS3FXLE1BQWEsSUFDMUZtSyxFQUFjWixTQUFTQyxlQUFlLFVBUzVDLE9BQUtzRyxTQUFBQSxFQUF5QmptQixPQUs1QmdYLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxtQkFDYkQsRUFBQUEsY0FBQSxVQUFJLHFCQUNKQSxFQUFBQSxjQUFBLE1BQUlDLFVBQVUsbUJBQ1hnUCxhQUF1QixFQUF2QkEsRUFBeUIxbUIsS0FBSSxTQUFDMmhCLEdBQUksT0FDakNsSyxFQUFBQSxjQUFBLE1BQUkvWCxJQUFLaWlCLEVBQU1qSyxVQUFVLHdCQUN2QkQsRUFBQUEsY0FBQSxVQUFRblIsS0FBSyxTQUFTdVMsUUFBUyxrQkFqQloxWCxFQWlCc0N3Z0IsRUFoQjNEcUIsRUFBVzdDLFNBQVNlLGNBQWMsY0FDeENILEVBQVk1ZixNQUFRQSxFQUNwQnVCLE9BQU91VyxTQUFTQyxLQUFPLDRCQUFIcFMsT0FBK0IzRixRQUNuRDZoQixFQUFTeEwsTUFBTTJKLFFBQVUsUUFKQyxJQUFDaGdCLEVBQ3JCNmhCLENBZ0JnRSxHQUMzRHJCLEdBRUEsTUFaSixJQWlCWCxxaUNDakJBLElBQU1pQyxHQUFhekQsU0FBU0MsZUFBZSxvQkF3TDNDLEdBdkxxQixXQUNuQixJQUFNdUcsR0FBY3RMLEVBQUFBLEVBQUFBLFVBQ2R1TCxFQUFnQnpHLFNBQVNDLGVBQWUsc0JBQ3hDeUcsRUFBcUIxRyxTQUFTQyxlQUFlLGVBRTdDMEcsRUFBYzNHLFNBQVM0RyxLQUV1QjVPLEVBQUFDLElBQVpDLEVBQUFBLEVBQUFBLFVBQVMsSUFBRyxHQUE3QzJPLEVBQVk3TyxFQUFBLEdBQUU4TyxFQUFlOU8sRUFBQSxHQUMrQitPLEVBQUE5TyxJQUFmQyxFQUFBQSxFQUFBQSxXQUFTLEdBQU0sR0FBNURzQixFQUFrQnVOLEVBQUEsR0FBRW5ELEVBQXFCbUQsRUFBQSxHQUNPQyxFQUFBL08sSUFBZkMsRUFBQUEsRUFBQUEsV0FBUyxHQUFNLEdBQWhEK08sRUFBWUQsRUFBQSxHQUFFRSxFQUFlRixFQUFBLEdBQ21CRyxFQUFBbFAsSUFBZkMsRUFBQUEsRUFBQUEsV0FBUyxHQUFNLEdBQWhEa1AsRUFBWUQsRUFBQSxHQUFFRSxFQUFlRixFQUFBLEdBQ2NHLEVBQUFyUCxJQUFaQyxFQUFBQSxFQUFBQSxVQUFTLElBQUcsR0FBM0NxUCxFQUFXRCxFQUFBLEdBQUVFLEVBQWNGLEVBQUEsR0FFNUJHLEVBQVlsbEIsT0FBT21sQixXQUFhLElBRWhDOVQsR0FBV2dFLEVBQUFBLEVBQUFBLE1BQ2pCQyxHQVNJQyxFQUFBQSxFQUFBQSxLQUFZLFNBQUMvUixHQUFLLE9BQUtBLENBQUssSUFSOUJuQixFQUFLaVQsRUFBTGpULE1BQ0FILEVBQVdvVCxFQUFYcFQsWUFDQUUsRUFBUWtULEVBQVJsVCxTQUNBVSxFQUFLd1MsRUFBTHhTLE1BQ0FNLEVBQVFrUyxFQUFSbFMsU0FDQXJELEVBQVl1VixFQUFadlYsYUFDYXFsQixFQUFZOVAsRUFBekI5UyxZQUNzQjZCLEVBQWVpUixFQUFyQ3ZTLFlBQWVILE1BR1RILEVBQThGMmlCLEVBQTlGM2lCLGVBQTBCK1MsRUFBb0U0UCxFQUE5RTFpQixTQUE2QkYsRUFBaUQ0aUIsRUFBeER4aUIsTUFBb0JELEVBQW9DeWlCLEVBQXBDemlCLFdBQVlFLEVBQXdCdWlCLEVBQXhCdmlCLFVBQVdQLEVBQWE4aUIsRUFBYjlpQixTQUNuRitpQixFQUFnQmhqQixhQUFLLEVBQUxBLEVBQU8zRSxNQUFNLEdBQUksR0FDakM0bkIsRUFBMEJub0IsRUFBV2tvQixHQUVyQ0UsRUFBYyxXQUFNLElBQUFDLEVBQUFDLEVBQUFDLEVBQ25CYixHQUNIQyxHQUFnQixHQUVsQlosU0FBd0IsUUFBWHNCLEVBQWJ0QixFQUFlMUIsaUJBQVMsSUFBQWdELEdBQXhCQSxFQUEwQjFqQixJQUFJLFVBQzlCcWlCLFNBQTZCLFFBQVhzQixFQUFsQnRCLEVBQW9CM0IsaUJBQVMsSUFBQWlELEdBQTdCQSxFQUErQjNqQixJQUFJLFVBQ25Dc2lCLFNBQXNCLFFBQVhzQixFQUFYdEIsRUFBYTVCLGlCQUFTLElBQUFrRCxHQUF0QkEsRUFBd0I1akIsSUFBSSxtQkFDOUIsRUFFTTZqQixHQUFtQkMsRUFBQUEsRUFBQUEsY0FDdkIsU0FBQ3BLLEdBR0MsR0FGQXFLLFFBQVFDLElBQUksNENBRVksTUFERnRLLGFBQUssRUFBTEEsRUFBT3VLLFNBQ0QsQ0FDMUIsSUFBSTNQLEVBQU0sNEJBQUhoUyxPQUErQmtTLG1CQUFtQmtGLGFBQUssRUFBTEEsRUFBT3FELE9BQU9wZ0IsUUFJdkUsR0FISXVtQixJQUNGNU8sRUFBTTRPLEdBRUo1TyxFQUFLLENBQ1EsSUFBQTRQLEVBQUFDLEVBQWYsR0FBSWYsRUFDRmhCLFNBQXdCLFFBQVg4QixFQUFiOUIsRUFBZTFCLGlCQUFTLElBQUF3RCxHQUF4QkEsRUFBMEJFLE9BQU8sVUFDakMvQixTQUE2QixRQUFYOEIsRUFBbEI5QixFQUFvQjNCLGlCQUFTLElBQUF5RCxHQUE3QkEsRUFBK0JDLE9BQU8sVUFFeENwQixHQUFnQixHQUNoQnpULEVBQVN3RCxJQUFpQixJQUMxQmpYLEVBQXVCNGQsYUFBSyxFQUFMQSxFQUFPcUQsT0FBT3BnQixPQUNyQ3VCLE9BQU91VyxTQUFTQyxLQUFPSixDQUN6QixDQUNGLENBQ0YsR0FDQSxDQUFDOE8sRUFBV0YsS0FHZG5QLEVBQUFBLEVBQUFBLFlBQVUsV0FBTSxJQUFBc1EsRUFDUkMsRUFBb0IzSSxTQUFTQyxlQUFlLFVBS2xELE9BSkEwSSxTQUFBQSxFQUFtQnBHLGlCQUFpQixRQUFTdUYsR0FDN0NhLFNBQUFBLEVBQW1CcEcsaUJBQWlCLFFBQVMyRixHQUM3Q2xJLFNBQVN1QyxpQkFBaUIsWUFBYXZKLEdBQWEsR0FDcEQyTixTQUFzQixRQUFYK0IsRUFBWC9CLEVBQWE1QixpQkFBUyxJQUFBMkQsR0FBdEJBLEVBQXdCcmtCLElBQUksb0JBQ3JCLFdBQU0sSUFBQXVrQixFQUNQRCxJQUNGQSxTQUFBQSxFQUFtQmpHLG9CQUFvQixRQUFTb0YsR0FDaERhLFNBQUFBLEVBQW1Cakcsb0JBQW9CLFFBQVN3RixJQUVsRGxJLFNBQVMwQyxvQkFBb0IsWUFBYTFKLEdBQWEsR0FDdkQyTixTQUFzQixRQUFYaUMsRUFBWGpDLEVBQWE1QixpQkFBUyxJQUFBNkQsR0FBdEJBLEVBQXdCSCxPQUFPLG1CQUNqQyxDQUNGLEdBQUcsS0FFSHJRLEVBQUFBLEVBQUFBLFlBQVUsV0FDUixJQUFNblMsRUFBaUJzaEIsRUFBYzFpQixFQUFTZ2pCLEdBQTJCaGpCLEVBQVNLLEdBQ2xGZ2lCLEdBQWdCLEdBQ1poaUIsSUFBZTZTLElBQ2Q5UixTQUFBQSxFQUFnQjNGLFFBQVVzRyxTQUFBQSxFQUFpQnRHLFVBQzVDNG1CLEdBQWdCLEdBQ2hCSixFQUFnQjdnQixHQUd0QixHQUFHLENBQUM4UixFQUFZbFQsRUFBVUssRUFBWUYsRUFBZ0I0QixhQUFlLEVBQWZBLEVBQWlCdEcsT0FBUXlFLEVBQVl6RSxTQUUzRixJQUFNMFksRUFBYyxTQUFDclgsR0FPakIsSUFBQWtuQixFQUFBQyxFQUFBQyxFQUxBdEYsSUFDQStDLEdBQ0FBLEVBQVk5SyxVQUNYK0gsR0FBV3VCLFNBQVNyakIsRUFBRXlmLFVBQ3RCb0YsRUFBWTlLLFFBQVFzSixTQUFTcmpCLEVBQUV5ZixVQUVoQ2lHLEdBQWdCLEdBQ2hCWixTQUF3QixRQUFYb0MsRUFBYnBDLEVBQWUxQixpQkFBUyxJQUFBOEQsR0FBeEJBLEVBQTBCSixPQUFPLFVBQ2pDL0IsU0FBNkIsUUFBWG9DLEVBQWxCcEMsRUFBb0IzQixpQkFBUyxJQUFBK0QsR0FBN0JBLEVBQStCTCxPQUFPLFVBQ3RDOUIsU0FBc0IsUUFBWG9DLEVBQVhwQyxFQUFhNUIsaUJBQVMsSUFBQWdFLEdBQXRCQSxFQUF3Qk4sT0FBTyxvQkFFbkMsRUFXTXBoQixFQUFXakMsR0FBYUEsRUFBVUYsR0FBY0UsRUFBVUYsR0FBYyxLQUN4RThqQixJQUEwQnZrQixHQUFnQnNULEdBQWVoVCxFQUFZekUsUUFBV3NFLEdBQ2hGcWtCLEVBQWVsa0IsRUFBWXpFLE9BQVMsSUFBS2dDLGFBQVksRUFBWkEsRUFBY2hDLFFBQVMsRUFjdEUsT0FaQThYLEVBQUFBLEVBQUFBLFlBQVUsV0FFTm9QLEVBREVuZ0IsR0FHYSxHQUVuQixHQUFHLENBQUNuQyxFQUFZbUMsS0FFaEIrUSxFQUFBQSxFQUFBQSxZQUFVLFdBQ0ptUCxHQUFhM1QsRXJCb0NRLFNBQUNoUCxHQUM1QixPQUFPLFNBQUNnUCxHQUNOLE9BQU8sSUFBSTNDLFNBQVEsU0FBQ3JELEVBQVNDLEdBQzNCK0YsRUFBU3FDLEdBQWdCclIsSUFDdEJzSixNQUFLLFNBQUNySixHQUNMK08sRUFBUyxDQUNQek4sS0FBTXBELEVBQ042QixNQUFBQSxFQUNBNkIsUUFBUyxDQUFFNUIsU0FBQUEsS0FFYitJLEVBQVEvSSxFQUNWLElBQ0N3TixPQUFNLFNBQUNoTixHQUNOK2lCLFFBQVEvaUIsTUFBTSwyQkFBNEJBLEdBQzFDd0ksRUFBT3hJLEVBQ1QsR0FDSixHQUNGLENBQ0YsQ3FCdEQ4QjZqQixDQUFjdEIsR0FDMUMsR0FBRyxDQUFDTCxJQUdGalEsRUFBQUEsY0FBQSxPQUNFQyxVQUFTLHdEQUFBNVEsT0FBMER5Z0IsRUFBZSxPQUFTLElBQzNGNUwsSUFBS2dMLEdBRUxsUCxFQUFBQSxjQUFBLE9BQUtDLFVBQVUsa0JBQ2JELEVBQUFBLGNBQUEsVUFBUUMsVUFBVSxtQkFBbUJwUixLQUFLLFNBQVN1UyxRQUFTLFNBQUMvVyxHQUFDLE9BL0J2Q29jLEVBK0JnRXBjLEdBOUJyRmlYLGlCQUNObUYsRUFBTTZILGtCQUNOeUIsR0FBZ0IsR0FDaEJaLFNBQXdCLFFBQVgwQyxFQUFiMUMsRUFBZTFCLGlCQUFTLElBQUFvRSxHQUF4QkEsRUFBMEJWLE9BQU8sVUFDakMvQixTQUE2QixRQUFYMEMsRUFBbEIxQyxFQUFvQjNCLGlCQUFTLElBQUFxRSxHQUE3QkEsRUFBK0JYLE9BQU8sZUFDdEM5QixTQUFzQixRQUFYMEMsRUFBWDFDLEVBQWE1QixpQkFBUyxJQUFBc0UsR0FBdEJBLEVBQXdCWixPQUFPLHFCQU5MLElBQUMxSyxFQUFVb0wsRUFBQUMsRUFBQUMsQ0ErQndELEdBQUUsVUFJN0YvUixFQUFBQSxjQUFBLE9BQUtDLFVBQVUsa0JBQ2JELEVBQUFBLGNBQUNnUyxHQUFTLENBQUMzRixVQUFXeUQsRUFBY3hELHNCQUF1QkEsRUFBdUJqTSxrQkFBZ0IsS0FFcEdMLEVBQUFBLGNBQUNpQyxHQUFVLENBQUNDLG1CQUFvQkEsSUFDaENsQyxFQUFBQSxjQUFBLE9BQUtDLFVBQVMsOENBQ1pELEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxXQUFXLFlBQVc5UyxJQUNqQ3drQixJQUFpQi9qQixHQUNqQm9TLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxxREFDYkQsRUFBQUEsY0FBQ2UsR0FBVyxDQUFDYixNQUFNLEVBQU10QyxRQUFRLGtEQUNqQ29DLEVBQUFBLGNBQUEsT0FBS0MsVUFBVSxZQUNiRCxFQUFBQSxjQUFBLFVBQUkscUJBQ0pBLEVBQUFBLGNBQUEsU0FBRyxzQkFJUjBSLEdBQTBCMVIsRUFBQUEsY0FBQUEsRUFBQUEsU0FBQSxLQUMzQkEsRUFBQUEsY0FBQ2lTLEdBQWMsTUFDZmpTLEVBQUFBLGNBQUNrUyxHQUFnQixPQUVoQnprQixTQUFBQSxFQUFhekUsT0FBU2dYLEVBQUFBLGNBQUNtUyxHQUFVLENBQUMvUixXQUFZM1MsRUFBYTRTLGtCQUFnQixJQUFNLEtBRWxGTCxFQUFBQSxjQUFDbUosR0FBYSxDQUFDakosS0FBTS9TLEtBRXZCNlMsRUFBQUEsY0FBQSxPQUNFQyxVQUFTLGFBQUE1USxPQUFlekIsRUFBYSxPQUFTLEdBQUUsS0FBQXlCLE9BQUk5QixHQUFZQSxFQUFTSyxHQUFjLGdCQUFrQixJQUN6RyxZQUFXNlMsRUFDWFYsTUFBTyxDQUNMMkosUUFBU2lHLEVBQWUsUUFBVSxTQUdwQzNQLEVBQUFBLGNBQUEsUUFBTUMsVUFBVSxVQUFVbUIsUUFBUyxrQkFBTTlFLEVyQnRJdEIsU0FBQ2hQLEdBQUssTUFBTSxDQUFFdUIsS0FBTTlDLEVBQWdCdUIsTUFBQUEsRUFBTyxDcUJzSVo4a0IsR0FBZ0IsSUFDbEVwUyxFQUFBQSxjQUFDcVMsR0FBUSxDQUFDblMsS0FBTTdTLEVBQVVDLE1BQU9BLEVBQU9NLFdBQVlBLElBQ3BEb1MsRUFBQUEsY0FBQ2hKLEdBQUssQ0FBQ2pKLE1BQU9BLEVBQU9ULE1BQU9BLEVBQU9lLFNBQVVBLElBQzNDaEIsRUFFRSxLQURGMlMsRUFBQUEsY0FBQ29KLEdBQVEsQ0FBQzdiLFNBQVVnaUIsRUFBYzNoQixXQUFZQSxFQUFZRSxVQUFXQSxFQUFXaUMsU0FBVUEsTUFNdEcsRUNqTUEsR0FGcUIsV0FBSCxPQUFTaVEsRUFBQUEsY0FBQ3NTLEdBQW9CLEtBQUcsRUNPbkQsR0FOZSxXQUFILE9BQ1Z0UyxFQUFBQSxjQUFBQSxFQUFBQSxTQUFBLEtBQ0VBLEVBQUFBLGNBQUN1UyxHQUFZLE1BQ1osRUNBQ0MsRzVCR1MsU0FBd0J4bEIsR0FDckMsT0FBT3lsQixFQUFBQSxFQUFBQSxJQUFZQyxFQUFhMWxCLEVBQWM2RCxHQUFpQjhoQixFQUFBQSxFQUFBQSxJQUFnQkMsRUFBQUEsSUFDakYsQzRCTGNDLEdBQ1JDLEdBQU0sV0FBSCxPQUFTOVMsRUFBQUEsY0FBQytTLEdBQU0sS0FBRyxFQUV0QkMsR0FBa0JDLEVBQUFBLFdBQW9CdkssU0FBU0MsZUFBZSxrQ0FFaEVxSyxJQUNGQSxHQUFnQkUsT0FDZGxULEVBQUFBLGNBQUNtVCxFQUFBQSxHQUFRLENBQUNYLE1BQU9BLElBQ2Z4UyxFQUFBQSxjQUFDOFMsR0FBRyxVQ2JOTSxFQUEyQixDQUFDLEVBR2hDLFNBQVNDLEVBQW9CQyxHQUU1QixJQUFJQyxFQUFlSCxFQUF5QkUsR0FDNUMsUUFBcUJycUIsSUFBakJzcUIsRUFDSCxPQUFPQSxFQUFhcGdCLFFBR3JCLElBQUlxZ0IsRUFBU0osRUFBeUJFLEdBQVksQ0FHakRuZ0IsUUFBUyxDQUFDLEdBT1gsT0FIQXNnQixFQUFvQkgsR0FBVUUsRUFBUUEsRUFBT3JnQixRQUFTa2dCLEdBRy9DRyxFQUFPcmdCLE9BQ2YsQ0FHQWtnQixFQUFvQkssRUFBSUQsRWpDekJwQjFyQixFQUFXLEdBQ2ZzckIsRUFBb0JNLEVBQUksU0FBU2xkLEVBQVFtZCxFQUFVemUsRUFBSTBlLEdBQ3RELElBQUdELEVBQUgsQ0FNQSxJQUFJRSxFQUFlQyxJQUNuQixJQUFTaGIsRUFBSSxFQUFHQSxFQUFJaFIsRUFBU2lCLE9BQVErUCxJQUFLLENBQ3JDNmEsRUFBVzdyQixFQUFTZ1IsR0FBRyxHQUN2QjVELEVBQUtwTixFQUFTZ1IsR0FBRyxHQUNqQjhhLEVBQVc5ckIsRUFBU2dSLEdBQUcsR0FFM0IsSUFKQSxJQUdJaWIsR0FBWSxFQUNQQyxFQUFJLEVBQUdBLEVBQUlMLEVBQVM1cUIsT0FBUWlyQixNQUNwQixFQUFYSixHQUFzQkMsR0FBZ0JELElBQWE5a0IsT0FBTzhLLEtBQUt3WixFQUFvQk0sR0FBR08sT0FBTSxTQUFTanNCLEdBQU8sT0FBT29yQixFQUFvQk0sRUFBRTFyQixHQUFLMnJCLEVBQVNLLEdBQUssSUFDaEtMLEVBQVNPLE9BQU9GLElBQUssSUFFckJELEdBQVksRUFDVEgsRUFBV0MsSUFBY0EsRUFBZUQsSUFHN0MsR0FBR0csRUFBVyxDQUNianNCLEVBQVNvc0IsT0FBT3BiLElBQUssR0FDckIsSUFBSXFiLEVBQUlqZixTQUNFbE0sSUFBTm1yQixJQUFpQjNkLEVBQVMyZCxFQUMvQixDQUNELENBQ0EsT0FBTzNkLENBckJQLENBSkNvZCxFQUFXQSxHQUFZLEVBQ3ZCLElBQUksSUFBSTlhLEVBQUloUixFQUFTaUIsT0FBUStQLEVBQUksR0FBS2hSLEVBQVNnUixFQUFJLEdBQUcsR0FBSzhhLEVBQVU5YSxJQUFLaFIsRUFBU2dSLEdBQUtoUixFQUFTZ1IsRUFBSSxHQUNyR2hSLEVBQVNnUixHQUFLLENBQUM2YSxFQUFVemUsRUFBSTBlLEVBd0IvQixFa0M1QkFSLEVBQW9CNWpCLEVBQUksU0FBUzBELEVBQVNraEIsR0FDekMsSUFBSSxJQUFJcHNCLEtBQU9vc0IsRUFDWGhCLEVBQW9CL1gsRUFBRStZLEVBQVlwc0IsS0FBU29yQixFQUFvQi9YLEVBQUVuSSxFQUFTbEwsSUFDNUU4RyxPQUFPd0UsZUFBZUosRUFBU2xMLEVBQUssQ0FBRWtNLFlBQVksRUFBTStJLElBQUttWCxFQUFXcHNCLElBRzNFLEVDUEFvckIsRUFBb0IvWCxFQUFJLFNBQVM5SCxFQUFLOGdCLEdBQVEsT0FBT3ZsQixPQUFPc0UsVUFBVWhCLGVBQWVnRCxLQUFLN0IsRUFBSzhnQixFQUFPLEVDQ3RHakIsRUFBb0JlLEVBQUksU0FBU2poQixHQUNYLG9CQUFYUSxRQUEwQkEsT0FBT00sYUFDMUNsRixPQUFPd0UsZUFBZUosRUFBU1EsT0FBT00sWUFBYSxDQUFFdkssTUFBTyxXQUU3RHFGLE9BQU93RSxlQUFlSixFQUFTLGFBQWMsQ0FBRXpKLE9BQU8sR0FDdkQsYUNEQSxJQUFJNnFCLEVBQWtCLENBQ3JCLElBQUssR0FhTmxCLEVBQW9CTSxFQUFFTSxFQUFJLFNBQVNPLEdBQVcsT0FBb0MsSUFBN0JELEVBQWdCQyxFQUFnQixFQUdyRixJQUFJQyxFQUF1QixTQUFTQyxFQUE0Qm5YLEdBQy9ELElBS0krVixFQUFVa0IsRUFMVlosRUFBV3JXLEVBQUssR0FDaEJvWCxFQUFjcFgsRUFBSyxHQUNuQnFYLEVBQVVyWCxFQUFLLEdBR0l4RSxFQUFJLEVBQzNCLEdBQUc2YSxFQUFTaUIsTUFBSyxTQUFTbE4sR0FBTSxPQUErQixJQUF4QjRNLEVBQWdCNU0sRUFBVyxJQUFJLENBQ3JFLElBQUkyTCxLQUFZcUIsRUFDWnRCLEVBQW9CL1gsRUFBRXFaLEVBQWFyQixLQUNyQ0QsRUFBb0JLLEVBQUVKLEdBQVlxQixFQUFZckIsSUFHaEQsR0FBR3NCLEVBQVMsSUFBSW5lLEVBQVNtZSxFQUFRdkIsRUFDbEMsQ0FFQSxJQURHcUIsR0FBNEJBLEVBQTJCblgsR0FDckR4RSxFQUFJNmEsRUFBUzVxQixPQUFRK1AsSUFDekJ5YixFQUFVWixFQUFTN2EsR0FDaEJzYSxFQUFvQi9YLEVBQUVpWixFQUFpQkMsSUFBWUQsRUFBZ0JDLElBQ3JFRCxFQUFnQkMsR0FBUyxLQUUxQkQsRUFBZ0JDLEdBQVcsRUFFNUIsT0FBT25CLEVBQW9CTSxFQUFFbGQsRUFDOUIsRUFFSXFlLEVBQXFCcmdCLEtBQW1DLDZCQUFJQSxLQUFtQyw4QkFBSyxHQUN4R3FnQixFQUFtQjllLFFBQVF5ZSxFQUFxQk0sS0FBSyxLQUFNLElBQzNERCxFQUFtQnRjLEtBQU9pYyxFQUFxQk0sS0FBSyxLQUFNRCxFQUFtQnRjLEtBQUt1YyxLQUFLRCxPQy9DdkYsSUFBSUUsRUFBc0IzQixFQUFvQk0sT0FBRTFxQixFQUFXLENBQUMsTUFBTSxXQUFhLE9BQU9vcUIsRUFBb0IsSUFBTSxJQUNoSDJCLEVBQXNCM0IsRUFBb0JNLEVBQUVxQiIsInNvdXJjZXMiOlsid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvd2VicGFjay9ydW50aW1lL2NodW5rIGxvYWRlZCIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL3V0aWxzL2luZGV4LmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29uc3RhbnRzL2FjdGlvbi10eXBlcy5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL3JlZHVjZXJzL2luZGV4LmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvc3RvcmUuanMiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC8uL3NyYy9jb25zdGFudHMvYXBpLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvdXRpbHMvY29uc3RhbnRzLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvdXRpbHMvY29tbW9uLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvYWN0aW9ucy9pbmRleC5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL3V0aWxzL1NwaW5uZXIuanMiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC8uL3NyYy9jb21wb25lbnRzL2F1dG8tc3VnZ2VzdC9oaW50TWVzc2FnZS5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL2NvbXBvbmVudHMvYXV0by1zdWdnZXN0L3Byb2R1Y3RzQ291bnQuanMiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC8uL3NyYy9jb21wb25lbnRzL2F1dG8tc3VnZ2VzdC9jYXRlZ29yaWVzLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvdXRpbHMvSGFtbWVyU3Bpbm5lci5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL2NvbXBvbmVudHMvbWFpbkxvYWRlci5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL2NvbXBvbmVudHMvYXV0by1zdWdnZXN0L2Vycm9yLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29tcG9uZW50cy9hdXRvLXN1Z2dlc3QvcHJvZHVjdC9wcm9kdWN0LnByaWNlLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29tcG9uZW50cy9hdXRvLXN1Z2dlc3QvcHJvZHVjdC9wcm9kdWN0LnNhdmluZ3MuanMiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC8uL3NyYy9jb21wb25lbnRzL2F1dG8tc3VnZ2VzdC9wcm9kdWN0L3Byb2R1Y3Quc2hpcHBpbmcubGFiZWwuanMiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC8uL3NyYy9jb21wb25lbnRzL2F1dG8tc3VnZ2VzdC9wcm9kdWN0L3Byb2R1Y3QuaW1nLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29tcG9uZW50cy9hdXRvLXN1Z2dlc3QvcHJvZHVjdC9wcm9kdWN0LmxhYmVscy5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL2NvbXBvbmVudHMvYXV0by1zdWdnZXN0L3Byb2R1Y3QvcHJvZHVjdC5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL2NvbXBvbmVudHMvYXV0by1zdWdnZXN0L3Byb2R1Y3RzLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29tcG9uZW50cy9hdXRvLXN1Z2dlc3Qvbm8tcmVzdWx0L2NvcnJlY3Rpb25zLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29tcG9uZW50cy9hdXRvLXN1Z2dlc3Qvbm8tcmVzdWx0LmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29udGFpbmVycy9zZWFyY2gvcmVjZW50bHktdmlld2VkLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29tcG9uZW50cy9zZWFyY2gtYm94L3NlYXJjaC1pbnB1dC5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwLy4vc3JjL2NvbnRhaW5lcnMvc2VhcmNoL3NlYXJjaC1ib3guanMiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC8uL3NyYy9jb250YWluZXJzL3NlYXJjaC90cmVuZGluZ1NlYXJjaGVzLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29udGFpbmVycy9zZWFyY2gvYXV0b0NvbXBsZXRlRnVsbFR5cGUuanMiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC8uL3NyYy9jb250YWluZXJzL3NlYXJjaC9hdXRvLWNvbXBsZXRlLmpzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvLi9zcmMvY29udGFpbmVycy9zZWFyY2guanMiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC8uL3NyYy9pbmRleC5qcyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwL3dlYnBhY2svYm9vdHN0cmFwIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvd2VicGFjay9ydW50aW1lL2RlZmluZSBwcm9wZXJ0eSBnZXR0ZXJzIiwid2VicGFjazovL3VuYnhkLXNlYXJjaC1hcHAvd2VicGFjay9ydW50aW1lL2hhc093blByb3BlcnR5IHNob3J0aGFuZCIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwL3dlYnBhY2svcnVudGltZS9tYWtlIG5hbWVzcGFjZSBvYmplY3QiLCJ3ZWJwYWNrOi8vdW5ieGQtc2VhcmNoLWFwcC93ZWJwYWNrL3J1bnRpbWUvanNvbnAgY2h1bmsgbG9hZGluZyIsIndlYnBhY2s6Ly91bmJ4ZC1zZWFyY2gtYXBwL3dlYnBhY2svc3RhcnR1cCJdLCJzb3VyY2VzQ29udGVudCI6WyJ2YXIgZGVmZXJyZWQgPSBbXTtcbl9fd2VicGFja19yZXF1aXJlX18uTyA9IGZ1bmN0aW9uKHJlc3VsdCwgY2h1bmtJZHMsIGZuLCBwcmlvcml0eSkge1xuXHRpZihjaHVua0lkcykge1xuXHRcdHByaW9yaXR5ID0gcHJpb3JpdHkgfHwgMDtcblx0XHRmb3IodmFyIGkgPSBkZWZlcnJlZC5sZW5ndGg7IGkgPiAwICYmIGRlZmVycmVkW2kgLSAxXVsyXSA+IHByaW9yaXR5OyBpLS0pIGRlZmVycmVkW2ldID0gZGVmZXJyZWRbaSAtIDFdO1xuXHRcdGRlZmVycmVkW2ldID0gW2NodW5rSWRzLCBmbiwgcHJpb3JpdHldO1xuXHRcdHJldHVybjtcblx0fVxuXHR2YXIgbm90RnVsZmlsbGVkID0gSW5maW5pdHk7XG5cdGZvciAodmFyIGkgPSAwOyBpIDwgZGVmZXJyZWQubGVuZ3RoOyBpKyspIHtcblx0XHR2YXIgY2h1bmtJZHMgPSBkZWZlcnJlZFtpXVswXTtcblx0XHR2YXIgZm4gPSBkZWZlcnJlZFtpXVsxXTtcblx0XHR2YXIgcHJpb3JpdHkgPSBkZWZlcnJlZFtpXVsyXTtcblx0XHR2YXIgZnVsZmlsbGVkID0gdHJ1ZTtcblx0XHRmb3IgKHZhciBqID0gMDsgaiA8IGNodW5rSWRzLmxlbmd0aDsgaisrKSB7XG5cdFx0XHRpZiAoKHByaW9yaXR5ICYgMSA9PT0gMCB8fCBub3RGdWxmaWxsZWQgPj0gcHJpb3JpdHkpICYmIE9iamVjdC5rZXlzKF9fd2VicGFja19yZXF1aXJlX18uTykuZXZlcnkoZnVuY3Rpb24oa2V5KSB7IHJldHVybiBfX3dlYnBhY2tfcmVxdWlyZV9fLk9ba2V5XShjaHVua0lkc1tqXSk7IH0pKSB7XG5cdFx0XHRcdGNodW5rSWRzLnNwbGljZShqLS0sIDEpO1xuXHRcdFx0fSBlbHNlIHtcblx0XHRcdFx0ZnVsZmlsbGVkID0gZmFsc2U7XG5cdFx0XHRcdGlmKHByaW9yaXR5IDwgbm90RnVsZmlsbGVkKSBub3RGdWxmaWxsZWQgPSBwcmlvcml0eTtcblx0XHRcdH1cblx0XHR9XG5cdFx0aWYoZnVsZmlsbGVkKSB7XG5cdFx0XHRkZWZlcnJlZC5zcGxpY2UoaS0tLCAxKVxuXHRcdFx0dmFyIHIgPSBmbigpO1xuXHRcdFx0aWYgKHIgIT09IHVuZGVmaW5lZCkgcmVzdWx0ID0gcjtcblx0XHR9XG5cdH1cblx0cmV0dXJuIHJlc3VsdDtcbn07IiwiLyoqXG4gKiBSZXBsYWNlcyBub24gYWxwaGFudW1lcmljIGNoYXJhY3RlcnMgd2l0aCB1bmRlcnNjb3JlLlxuICpcbiAqIEBwYXJhbSB7U3RyaW5nfSBrZXlcbiAqIEByZXR1cm5zIHtTdHJpbmd9XG4gKi9cbmV4cG9ydCBjb25zdCB2YWxpZGF0ZUtleSA9IChrZXkpID0+IHtcbiAgcmV0dXJuIGtleS5yZXBsYWNlKC9bXmEtekEtWjAtOV0vZ2ksICdfJykudG9Mb3dlckNhc2UoKTtcbn07XG5cbi8qKlxuICogRGV0ZWN0cyBkZXZpY2UgdHlwZSBieSBzY3JlZW4gc2l6ZS5cbiAqXG4gKiBAcmV0dXJucyB7Qm9vbGVhbn1cbiAqL1xuZXhwb3J0IGNvbnN0IGlzTW9iaWxlID0gKCkgPT4ge1xuICBjb25zdCB3ID0gd2luZG93LFxuICAgIGQgPSBkb2N1bWVudCxcbiAgICBlID0gZC5kb2N1bWVudEVsZW1lbnQsXG4gICAgZyA9IGQuZ2V0RWxlbWVudHNCeVRhZ05hbWUoJ2JvZHknKVswXSxcbiAgICB4ID0gdy5pbm5lcldpZHRoIHx8IGUuY2xpZW50V2lkdGggfHwgZy5jbGllbnRXaWR0aDtcblxuICByZXR1cm4geCA8IDc2ODtcbn07XG5cbi8qKlxuICogVGFrZXMgYW4gYXJyYXkgd2l0aCBkdXBsaWNhdGVzIGFuZCBmaWx0ZXIgZHVwbGljYXRlcy5cbiAqXG4gKiBAcGFyYW0ge0FycmF5fVxuICogQHJldHVybnMge0FycmF5fVxuICovXG5leHBvcnQgY29uc3QgZmlsdGVyRHVwbGljYXRlcyA9IChhcnIpID0+IHtcbiAgcmV0dXJuIGFyci5maWx0ZXIoKGVsZW0sIGlkeCkgPT4ge1xuICAgIHJldHVybiBhcnIuaW5kZXhPZihlbGVtKSA9PSBpZHg7XG4gIH0pO1xufTtcblxuLyoqXG4gKiBUYWtlcyBhIHN0cmluZyBhbmQgcmV0dXJucyBzYW1lIHdvcmQgd2l0aCBmaXJzdCBsZXR0ZXIgY2FwaXRhbGl6ZWQuXG4gKlxuICogQHBhcmFtIHtTdHJpbmd9IHN0clxuICogQHJldHVybnMge1N0cmluZ31cbiAqL1xuZXhwb3J0IGNvbnN0IGNhcGl0YWxpemUgPSAoc3RyKSA9PiB7XG4gIHJldHVybiB0eXBlb2Ygc3RyID09PSAnc3RyaW5nJ1xuICAgID8gc3RyXG4gICAgICAgIC5zcGxpdCgnICcpXG4gICAgICAgIC5tYXAoKHdvcmQpID0+IHdvcmQuY2hhckF0KDApLnRvVXBwZXJDYXNlKCkgKyB3b3JkLnNsaWNlKDEpKVxuICAgICAgICAuam9pbignICcpXG4gICAgOiBzdHI7XG59O1xuXG4vKipcbiAqIENoZWNrIGlmIGFycmF5IGhhcyB2YWx1ZVxuICovXG5cbmZ1bmN0aW9uIGNvbnRhaW5zQ2FzZUluc2Vuc2l0aXZlKGFycmF5LCB2YWx1ZSkge1xuICBpZiAoIUFycmF5LmlzQXJyYXkoYXJyYXkpKSB7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgLy8gQ29udmVydCB0aGUgdmFsdWUgYW5kIGFycmF5IGVsZW1lbnRzIHRvIGxvd2VyY2FzZSBmb3IgY2FzZS1pbnNlbnNpdGl2ZSBjb21wYXJpc29uXG4gIGNvbnN0IGxvd2VyY2FzZVZhbHVlID0gdmFsdWUudG9Mb3dlckNhc2UoKTtcblxuICBmb3IgKGNvbnN0IGVsZW1lbnQgb2YgYXJyYXkpIHtcbiAgICBpZiAoZWxlbWVudC50b0xvd2VyQ2FzZSgpID09PSBsb3dlcmNhc2VWYWx1ZSkge1xuICAgICAgcmV0dXJuIHRydWU7IC8vIEZvdW5kIGEgY2FzZS1pbnNlbnNpdGl2ZSBtYXRjaFxuICAgIH1cbiAgfVxuXG4gIHJldHVybiBmYWxzZTsgLy8gTm8gY2FzZS1pbnNlbnNpdGl2ZSBtYXRjaCBmb3VuZFxufVxuXG4vKipcbiAqIFNldCB2YWx1ZXMgb24gTG9jYWxzdG9yYWdlXG4gKi9cblxuZXhwb3J0IGNvbnN0IHNldFRlcm1zT25Mb2NhbFN0b3JhZ2UgPSAodGVybSA9ICcnLCBoYXNEYXRhID0gdHJ1ZSkgPT4ge1xuICBjb25zdCBzdEtleSA9IGByZWNlbnRseV9zZWFyY2hlZF90ZXJtc2A7XG5cbiAgbGV0IHNlYXJjaFRlcm1zID0gSlNPTi5wYXJzZShsb2NhbFN0b3JhZ2UuZ2V0SXRlbShzdEtleSkgfHwgJ1tdJyk7XG4gIGlmICghdGVybSB8fCBjb250YWluc0Nhc2VJbnNlbnNpdGl2ZShzZWFyY2hUZXJtcywgdGVybSkpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICBpZiAoaGFzRGF0YSkge1xuICAgIGlmICghc2VhcmNoVGVybXMuaW5jbHVkZXModGVybSkpIHtcbiAgICAgIHNlYXJjaFRlcm1zLnVuc2hpZnQodGVybSk7XG4gICAgfVxuICB9IGVsc2Uge1xuICAgIHNlYXJjaFRlcm1zID0gc2VhcmNoVGVybXMuZmlsdGVyKCh0KSA9PiB0ICE9PSB0ZXJtKTtcbiAgfVxuXG4gIGlmIChzZWFyY2hUZXJtcy5sZW5ndGggPiA1KSB7XG4gICAgc2VhcmNoVGVybXMucG9wKCk7XG4gIH1cbiAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oc3RLZXksIEpTT04uc3RyaW5naWZ5KHNlYXJjaFRlcm1zKSk7XG59O1xuXG5leHBvcnQgY29uc3QgZ2V0UHJvZHVjdHNGcm9tTG9jYWxTdHJvcmFnZSA9ICgpID0+IHtcbiAgbGV0IHByb2R1Y3RUZXJtcyA9IHdpbmRvdy5sb2NhbFN0b3JhZ2UuZ2V0SXRlbSgncmVjZW50bHlfc2VhcmNoZWRfdGVybXMnKTtcbiAgbGV0IHByb2R1Y3REYXRhID0gd2luZG93LmxvY2FsU3RvcmFnZS5nZXRJdGVtKCdwcm9kdWN0X2RhdGFfc3RvcmFnZScpO1xuICBsZXQgcmVjZW50bHlWaWV3ZWRQcm9kdWN0ID0gd2luZG93LmxvY2FsU3RvcmFnZS5nZXRJdGVtKCdyZWNlbnRseV92aWV3ZWRfcHJvZHVjdCcpO1xuXG4gIHJldHVybiB7XG4gICAgcHJvZHVjdFRlcm1zOiBpc0pTT04ocHJvZHVjdFRlcm1zKSA/IEpTT04ucGFyc2UoW3Byb2R1Y3RUZXJtc10pIDogW10sXG4gICAgcHJvZHVjdERhdGE6IGlzSlNPTihwcm9kdWN0RGF0YSkgPyBKU09OLnBhcnNlKFtwcm9kdWN0RGF0YV0pIDoge30sXG4gICAgcmVjZW50bHlWaWV3ZWRQcm9kdWN0OiBpc0pTT04ocmVjZW50bHlWaWV3ZWRQcm9kdWN0KSA/IEpTT04ucGFyc2UoW3JlY2VudGx5Vmlld2VkUHJvZHVjdF0pIDoge30sXG4gIH07XG59O1xuXG5leHBvcnQgY29uc3QgaXNKU09OID0gKHN0cikgPT4ge1xuICB0cnkge1xuICAgIHJldHVybiBKU09OLnBhcnNlKHN0cikgJiYgISFzdHI7XG4gIH0gY2F0Y2ggKGUpIHtcbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cbn07XG5cbmV4cG9ydCBjb25zdCBzZXRMb2NhbFN0b3JhZ2UgPSAoa2V5LCB2YWx1ZSkgPT4ge1xuICB3aW5kb3cubG9jYWxTdG9yYWdlLnNldEl0ZW0oa2V5LCB2YWx1ZSk7XG59O1xuXG5leHBvcnQgY29uc3QgZGVib3VuY2UgPSAoZnVuYywgZGVsYXkpID0+IHtcbiAgbGV0IHRpbWVvdXRJZDtcblxuICByZXR1cm4gZnVuY3Rpb24gKC4uLmFyZ3MpIHtcbiAgICBjb25zdCBjb250ZXh0ID0gdGhpcztcblxuICAgIGNsZWFyVGltZW91dCh0aW1lb3V0SWQpO1xuXG4gICAgdGltZW91dElkID0gc2V0VGltZW91dCgoKSA9PiB7XG4gICAgICBmdW5jLmFwcGx5KGNvbnRleHQsIGFyZ3MpO1xuICAgIH0sIGRlbGF5KTtcbiAgfTtcbn07XG5cbmV4cG9ydCBjb25zdCBnZXRBbWFzdHlMYWJlbFN0eWxlcyA9IChsYWJlbFBvc2l0aW9uKSA9PiB7XG4gIGNvbnN0IGNvbW1vblN0eWxlcyA9IHtcbiAgICBwb3NpdGlvbjogJ2Fic29sdXRlJyxcbiAgICB3aWR0aDogJzQ1JScsXG4gICAgaGVpZ2h0OiAnNDUlJyxcbiAgICB6SW5kZXg6ICcxJyxcbiAgICBiYWNrZ3JvdW5kU2l6ZTogJ2NvbnRhaW4nLFxuICAgIGJhY2tncm91bmRSZXBlYXQ6ICduby1yZXBlYXQnLFxuICB9O1xuICBjb25zdCBjb21tb25Ub3BTdHlsZXMgPSB7XG4gICAgLi4uY29tbW9uU3R5bGVzLFxuICAgIHRvcDogMCxcbiAgICBiYWNrZ3JvdW5kUG9zaXRpb246ICd0b3AnLFxuICB9O1xuICBjb25zdCBjb21tb25Cb3R0b21TdHlsZXMgPSB7XG4gICAgLi4uY29tbW9uU3R5bGVzLFxuICAgIGJvdHRvbTogMCxcbiAgICBiYWNrZ3JvdW5kUG9zaXRpb246ICdib3R0b20nLFxuICB9O1xuICBzd2l0Y2ggKGxhYmVsUG9zaXRpb24pIHtcbiAgICBjYXNlICcwJzpcbiAgICAgIHJldHVybiB7IC4uLmNvbW1vblRvcFN0eWxlcywgbGVmdDogMCB9O1xuICAgIGNhc2UgJzInOlxuICAgICAgcmV0dXJuIHsgLi4uY29tbW9uVG9wU3R5bGVzLCByaWdodDogMCB9O1xuICAgIGNhc2UgJzYnOlxuICAgICAgcmV0dXJuIHsgLi4uY29tbW9uQm90dG9tU3R5bGVzLCBsZWZ0OiAwIH07XG4gICAgY2FzZSAnOCc6XG4gICAgICByZXR1cm4geyAuLi5jb21tb25Cb3R0b21TdHlsZXMsIHJpZ2h0OiAwIH07XG4gICAgZGVmYXVsdDpcbiAgICAgIHJldHVybiB7IC4uLmNvbW1vblRvcFN0eWxlcywgbGVmdDogMCB9O1xuICB9XG59O1xuIiwiZXhwb3J0IGNvbnN0IFNFQVJDSF9RVUVSWSA9ICdTRUFSQ0hfUVVFUlknO1xuZXhwb3J0IGNvbnN0IFNFQVJDSF9DTEVBUiA9ICdTRUFSQ0hfQ0xFQVInO1xuZXhwb3J0IGNvbnN0IFNFQVJDSF9SRVFVRVNUID0gJ1NFQVJDSF9SRVFVRVNUJztcbmV4cG9ydCBjb25zdCBTRUFSQ0hfUFJPRFVDVFMgPSAnU0VBUkNIX1BST0RVQ1RTJztcbmV4cG9ydCBjb25zdCBTRUFSQ0hfUkVTVUxUID0gJ1NFQVJDSF9SRVNVTFQnO1xuZXhwb3J0IGNvbnN0IFNFQVJDSF9SRVNVTFRfRVJST1IgPSAnU0VBUkNIX1JFU1VMVF9FUlJPUic7XG5leHBvcnQgY29uc3QgU0VBUkNIX0NPUlJFQ1RJT05TX1NUQVJURUQgPSAnU0VBUkNIX0NPUlJFQ1RJT05TX1NUQVJURUQnO1xuZXhwb3J0IGNvbnN0IFNFQVJDSF9DT1JSRUNUSU9OX1NVQ0NFU1MgPSAnU0VBUkNIX0NPUlJFQ1RJT05fU1VDQ0VTUyc7XG5leHBvcnQgY29uc3QgU0VBUkNIX0NPUlJFQ1RJT05TX0VSUk9SID0gJ1NFQVJDSF9DT1JSRUNUSU9OU19FUlJPUic7XG5leHBvcnQgY29uc3QgQ0xFQVJfQUNUSVZFX1RFUk0gPSAnQ0xFQVJfQUNUSVZFX1RFUk0nO1xuZXhwb3J0IGNvbnN0IFBST0RVQ1RTX0NMRUFSID0gJ1BST0RVQ1RTX0NMRUFSJztcblxuZXhwb3J0IGNvbnN0IFNVR0dFU1RfQ0xJQ0sgPSAnU1VHR0VTVF9DTElDSyc7XG5leHBvcnQgY29uc3QgU1VHR0VTVF9SRVFVRVNUID0gJ1NVR0dFU1RfUkVRVUVTVCc7XG5leHBvcnQgY29uc3QgU1VHR0VTVF9TVUNDRVNTID0gJ1NVR0dFU1RfU1VDQ0VTUyc7XG5leHBvcnQgY29uc3QgU1VHR0VTVF9GQUlMVVJFID0gJ1NVR0dFU1RfRkFJTFVSRSc7XG5leHBvcnQgY29uc3QgVE9HR0xFX0RST1BET1dOID0gJ1RPR0dMRV9EUk9QRE9XTic7XG5leHBvcnQgY29uc3QgVVBEQVRFX0xTX1BST0RVQ1RTID0gJ1VQREFURV9MU19QUk9EVUNUUyc7XG5leHBvcnQgY29uc3QgVE9HR0xFX01BSU5fTE9BREVSID0gJ1RPR0dMRV9NQUlOX0xPQURFUic7XG4vL1RPRE8gd2lsbCByZW1vdmUgdGhpcyBhcyB3ZSBkb24ndCBuZWVkIGl0LlxuLy8gZXhwb3J0IGNvbnN0IFNFQVJDSF9RVUVSWV9QUk9EVUNUU19DT1VOVCA9IFwiU0VBUkNIX1FVRVJZX1BST0RVQ1RTX0NPVU5UXCI7XG4iLCJpbXBvcnQgeyB2YWxpZGF0ZUtleSwgY2FwaXRhbGl6ZSwgZ2V0UHJvZHVjdHNGcm9tTG9jYWxTdHJvcmFnZSB9IGZyb20gJy4uL3V0aWxzJztcbmltcG9ydCB7XG4gIFNFQVJDSF9SRVNVTFQsXG4gIFNFQVJDSF9DTEVBUixcbiAgUFJPRFVDVFNfQ0xFQVIsXG4gIFNFQVJDSF9RVUVSWSxcbiAgU0VBUkNIX1JFUVVFU1QsXG4gIFNFQVJDSF9QUk9EVUNUUyxcbiAgU0VBUkNIX1JFU1VMVF9FUlJPUixcbiAgU1VHR0VTVF9DTElDSyxcbiAgU1VHR0VTVF9SRVFVRVNULFxuICBTVUdHRVNUX1NVQ0NFU1MsXG4gIFNVR0dFU1RfRkFJTFVSRSxcbiAgVE9HR0xFX0RST1BET1dOLFxuICBVUERBVEVfTFNfUFJPRFVDVFMsXG4gIFNFQVJDSF9DT1JSRUNUSU9OX1NVQ0NFU1MsXG4gIFNFQVJDSF9DT1JSRUNUSU9OU19TVEFSVEVELFxuICBTRUFSQ0hfQ09SUkVDVElPTlNfRVJST1IsXG4gIENMRUFSX0FDVElWRV9URVJNLFxuICBUT0dHTEVfTUFJTl9MT0FERVIsXG4gIC8vVE9ETyB3aWxsIHJlbW92ZSB0aGlzIGFzIHdlIGRvbid0IG5lZWQgaXQuXG4gIC8vU0VBUkNIX1FVRVJZX1BST0RVQ1RTX0NPVU5ULFxufSBmcm9tICcuLi9jb25zdGFudHMvYWN0aW9uLXR5cGVzJztcblxuY29uc3QgbHNQcm9kdWN0c0luZm8gPSBnZXRQcm9kdWN0c0Zyb21Mb2NhbFN0cm9yYWdlKCk7XG5cbmNvbnN0IGZpbHRlckR1cGxpY2F0ZXMgPSAoYXJyKSA9PiB7XG4gIGNvbnN0IHVuaXF1ZVRlcm1zID0gbmV3IFNldCgpO1xuICByZXR1cm4gYXJyPy5maWx0ZXIoKGVsZW0pID0+IHtcbiAgICBjb25zdCB0ZXJtID0gZWxlbT8udGVybS50b0xvd2VyQ2FzZSgpO1xuICAgIGNvbnN0IGlzRHVwbGljYXRlID0gdW5pcXVlVGVybXMuaGFzKHRlcm0pO1xuICAgIHVuaXF1ZVRlcm1zLmFkZCh0ZXJtKTtcbiAgICByZXR1cm4gIWlzRHVwbGljYXRlO1xuICB9KTtcbn07XG5cblxuY29uc3QgaW5pdGlhbFN0YXRlID0ge1xuICBzaG93TWFpbkxvYWRlcjogZmFsc2UsXG4gIGlzU2VhcmNoaW5nOiBmYWxzZSxcbiAgc2hvd0Ryb3Bkb3duOiBmYWxzZSxcbiAgbm9SZXN1bHQ6IGZhbHNlLFxuICBxdWVyeTogbnVsbCxcbiAgcHJvZHVjdHM6IFtdLFxuICBwcm9kdWN0c0NvdW50OiAwLFxuICBzdWdnZXN0aW9uczoge1xuICAgIGlzSW5pdGlhbEZldGNoOiB0cnVlLFxuICAgIGZldGNoaW5nOiBmYWxzZSxcbiAgICBhY3RpdmVUZXJtOiBudWxsLFxuICAgIHRlcm06IG51bGwsXG4gICAgdGVybXM6IFtdLFxuICAgIHByb2R1Y3RzOiB7fSxcbiAgICByZWRpcmVjdHM6IHt9LFxuICAgIGVycm9yOiBmYWxzZSxcbiAgfSxcbiAgY29ycmVjdGlvbnM6IHtcbiAgICB0ZXJtczogW10sXG4gICAgcHJvZHVjdHM6IFtdLFxuICAgIGlzRmV0Y2hpbmdQcm9kdWN0czogZmFsc2UsXG4gICAgZXJyb3JNc2dGZXRjaGluZ1Byb2R1Y3RzOiBudWxsLFxuICAgIHJlZGlyZWN0czoge30sXG4gIH0sXG4gIGNvbmZpZzogd2luZG93LnVuYnhkQ29uZmlnIHx8IHt9LFxuICBlcnJvcjogZmFsc2UsXG4gIGVycm9yTXNnOiBudWxsLFxuICAuLi5sc1Byb2R1Y3RzSW5mbyxcbn07XG5cbmNvbnN0IHJvb3RSZWR1Y2VyID0gKHN0YXRlID0gaW5pdGlhbFN0YXRlLCBhY3Rpb24pID0+IHtcbiAgbGV0IHByb2R1Y3RzID0ge307XG4gIGxldCBhY3RpdmVQcm9kdWN0cyA9IFtdO1xuICBsZXQgY2FwaXRhbGl6ZWRBY3RpdmVUZXJtID0gJyc7XG4gIHN3aXRjaCAoYWN0aW9uLnR5cGUpIHtcbiAgICBjYXNlIFNFQVJDSF9DTEVBUjpcbiAgICAgIGNvbnN0IG5ld1Byb2R1Y3RMU1ZhbHVlcyA9IGdldFByb2R1Y3RzRnJvbUxvY2FsU3Ryb3JhZ2UoKTtcbiAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCB7IC4uLmluaXRpYWxTdGF0ZSwgLi4ubmV3UHJvZHVjdExTVmFsdWVzIH0pO1xuICAgIGNhc2UgU0VBUkNIX1FVRVJZOlxuICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCB7XG4gICAgICAgIGlzU2VhcmNoaW5nOiBmYWxzZSxcbiAgICAgICAgcXVlcnk6IGFjdGlvbi5xdWVyeSxcbiAgICAgICAgZXJyb3I6IGZhbHNlLFxuICAgICAgICBlcnJvck1zZzogbnVsbCxcbiAgICAgICAgbm9SZXN1bHQ6IGZhbHNlLFxuICAgICAgfSk7XG4gICAgY2FzZSBTRUFSQ0hfUkVRVUVTVDpcbiAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSwge1xuICAgICAgICBpc1NlYXJjaGluZzogdHJ1ZSxcbiAgICAgICAgcXVlcnk6IGFjdGlvbi5xdWVyeSxcbiAgICAgICAgc2hvd0Ryb3Bkb3duOiB0cnVlLFxuICAgICAgfSk7XG4gICAgY2FzZSBDTEVBUl9BQ1RJVkVfVEVSTTpcbiAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSwge1xuICAgICAgICBzdWdnZXN0aW9uczoge1xuICAgICAgICAgIC4uLnN0YXRlLnN1Z2dlc3Rpb25zLFxuICAgICAgICAgIGFjdGl2ZVRlcm06IG51bGwsXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICBjYXNlIFNFQVJDSF9SRVNVTFQ6XG4gICAgICBjb25zdCBhbGxUZXJtcyA9IFsuLi5hY3Rpb24udGVybXNdO1xuICAgICAgaWYgKCd1bmRlZmluZWQnICE9PSB0eXBlb2YgYWN0aW9uLnBheWxvYWQucHJvZHVjdHMpIHtcbiAgICAgICAgbGV0IGtleSA9IHZhbGlkYXRlS2V5KGFjdGlvbi5xdWVyeSk7XG4gICAgICAgIHByb2R1Y3RzW2tleV0gPSBhY3Rpb24ucGF5bG9hZC5wcm9kdWN0cztcbiAgICAgIH1cblxuICAgICAgY29uc3Qgc3VnZ2VzdGlvbnNUZXJtcyA9IGZpbHRlckR1cGxpY2F0ZXMoWy4uLnN0YXRlLnN1Z2dlc3Rpb25zPy50ZXJtcywgLi4uYWxsVGVybXNdKTtcbiAgICAgIGNvbnN0IGNvcnJlY3Rpb25UZXJtcyA9IGZpbHRlckR1cGxpY2F0ZXMoWy4uLnN0YXRlPy5jb3JyZWN0aW9ucz8udGVybXMsIC4uLmFjdGlvbi5wYXlsb2FkLnNwZWxsQ29ycmVjdGlvbnNdKTtcblxuICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCB7XG4gICAgICAgIGlzU2VhcmNoaW5nOiBmYWxzZSxcbiAgICAgICAgc2hvd0Ryb3Bkb3duOiB0cnVlLFxuICAgICAgICBzdWdnZXN0aW9uczoge1xuICAgICAgICAgIC4uLnN0YXRlLnN1Z2dlc3Rpb25zLFxuICAgICAgICAgIHRlcm1zOiBzdWdnZXN0aW9uc1Rlcm1zLFxuICAgICAgICB9LFxuICAgICAgICBjb3JyZWN0aW9uczoge1xuICAgICAgICAgIC4uLnN0YXRlLmNvcnJlY3Rpb25zLFxuICAgICAgICAgIHRlcm1zOiBjb3JyZWN0aW9uVGVybXMsXG4gICAgICAgIH0sXG4gICAgICB9KTtcblxuICAgIGNhc2UgU0VBUkNIX1BST0RVQ1RTOlxuICAgICAgaWYgKCd1bmRlZmluZWQnICE9PSB0eXBlb2YgYWN0aW9uLnBheWxvYWQucHJvZHVjdHMpIHtcbiAgICAgICAgbGV0IGtleSA9IHZhbGlkYXRlS2V5KGFjdGlvbi5xdWVyeSk7XG4gICAgICAgIHByb2R1Y3RzW2tleV0gPSBhY3Rpb24ucGF5bG9hZC5wcm9kdWN0cztcbiAgICAgIH1cblxuICAgIGNhc2UgU0VBUkNIX1JFU1VMVF9FUlJPUjpcbiAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSwge1xuICAgICAgICBpc1NlYXJjaGluZzogZmFsc2UsXG4gICAgICAgIGVycm9yOiB0cnVlLFxuICAgICAgICBub1Jlc3VsdDogZmFsc2UsXG4gICAgICAgIGVycm9yTXNnOiBhY3Rpb24uZXJyb3IsXG4gICAgICAgIHByb2R1Y3RzOiBbXSxcbiAgICAgIH0pO1xuICAgIGNhc2UgUFJPRFVDVFNfQ0xFQVI6XG4gICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbih7fSwgc3RhdGUsIHtcbiAgICAgICAgcHJvZHVjdHM6IFtdLFxuICAgICAgICBzdWdnZXN0aW9uczoge1xuICAgICAgICAgIC4uLnN0YXRlLnN1Z2dlc3Rpb25zLFxuICAgICAgICAgIGFjdGl2ZVRlcm06IG51bGwsXG4gICAgICAgIH0sXG4gICAgICB9KTtcbiAgICBjYXNlIFNVR0dFU1RfQ0xJQ0s6XG4gICAgICBjYXBpdGFsaXplZEFjdGl2ZVRlcm0gPSBjYXBpdGFsaXplKGFjdGlvbi50ZXJtKTtcbiAgICAgIGFjdGl2ZVByb2R1Y3RzID0gc3RhdGUuc3VnZ2VzdGlvbnMucHJvZHVjdHNbY2FwaXRhbGl6ZWRBY3RpdmVUZXJtXSB8fCBbXTtcbiAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSwge1xuICAgICAgICBub1Jlc3VsdDogIWFjdGl2ZVByb2R1Y3RzLmxlbmd0aCxcbiAgICAgICAgc3VnZ2VzdGlvbnM6IHtcbiAgICAgICAgICAuLi5zdGF0ZS5zdWdnZXN0aW9ucyxcbiAgICAgICAgICBpc0luaXRpYWxGZXRjaDogZmFsc2UsXG4gICAgICAgICAgYWN0aXZlVGVybTogY2FwaXRhbGl6ZWRBY3RpdmVUZXJtLFxuICAgICAgICB9LFxuICAgICAgfSk7XG4gICAgY2FzZSBTVUdHRVNUX1JFUVVFU1Q6XG4gICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbih7fSwgc3RhdGUsIHtcbiAgICAgICAgbm9SZXN1bHQ6IGZhbHNlLFxuICAgICAgICBzdWdnZXN0aW9uczoge1xuICAgICAgICAgIC4uLnN0YXRlLnN1Z2dlc3Rpb25zLFxuICAgICAgICAgIGlzSW5pdGlhbEZldGNoOiBhY3Rpb24uaW5pdGlhbCxcbiAgICAgICAgICBmZXRjaGluZzogdHJ1ZSxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIGNhc2UgU1VHR0VTVF9TVUNDRVNTOlxuICAgICAgbGV0IGQgPSBhY3Rpb24ucGF5bG9hZCxcbiAgICAgICAgcmVkaXJlY3RzID0gc3RhdGUuc3VnZ2VzdGlvbnMucmVkaXJlY3RzIHx8IHt9O1xuICAgICAgY29uc3QgY2FwaXRhbGl6ZWRUZXJtID0gY2FwaXRhbGl6ZShhY3Rpb24udGVybSk7XG4gICAgICBjb25zdCBjYXBpdGFsaXplUXVlcnkgPSBjYXBpdGFsaXplKHN0YXRlLnF1ZXJ5KTtcbiAgICAgIHByb2R1Y3RzW2NhcGl0YWxpemVkVGVybV0gPSBkLnByb2R1Y3RzO1xuICAgICAgaWYgKGQucHJvZHVjdHMubGVuZ3RoKSB7XG4gICAgICAgIHByb2R1Y3RzW2NhcGl0YWxpemVkVGVybV0gPSBkLnByb2R1Y3RzO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBzdWdnZXN0aW9uVGVybXMgPSBmaWx0ZXJEdXBsaWNhdGVzKFtcbiAgICAgICAgY2FwaXRhbGl6ZWRUZXJtID09PSBjYXBpdGFsaXplUXVlcnkgPyB7IHRlcm06IGNhcGl0YWxpemVkVGVybSwgcHJvZHVjdF9jb3VudDogZC5xdWVyeVByb2R1Y3RzQ291bnQgfSA6IHt0ZXJtOiBjYXBpdGFsaXplUXVlcnl9ICxcbiAgICAgICAgLi4uc3RhdGU/LnN1Z2dlc3Rpb25zPy50ZXJtcyxcbiAgICAgIF0pO1xuXG4gICAgICBpZiAoZC5yZWRpcmVjdCkge1xuICAgICAgICByZWRpcmVjdHMgPSB7fTtcbiAgICAgICAgcmVkaXJlY3RzW2NhcGl0YWxpemVkVGVybV0gPSBkLnJlZGlyZWN0O1xuICAgICAgfVxuXG4gICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbih7fSwgc3RhdGUsIHtcbiAgICAgICAgbm9SZXN1bHQ6IGQucHJvZHVjdHMubGVuZ3RoIDwgMSAmJiAhc3RhdGUuc3VnZ2VzdGlvbnMucHJvZHVjdHNbY2FwaXRhbGl6ZWRUZXJtXSxcbiAgICAgICAgc3VnZ2VzdGlvbnM6IHtcbiAgICAgICAgICAuLi5zdGF0ZS5zdWdnZXN0aW9ucyxcbiAgICAgICAgICBmZXRjaGluZzogZmFsc2UsXG4gICAgICAgICAgdGVybXM6IHN1Z2dlc3Rpb25UZXJtcyxcbiAgICAgICAgICBwcm9kdWN0czoge1xuICAgICAgICAgICAgLi4uc3RhdGUuc3VnZ2VzdGlvbnMucHJvZHVjdHMsXG4gICAgICAgICAgICAuLi57IFtjYXBpdGFsaXplZFRlcm1dOiBkLnByb2R1Y3RzIH0sXG4gICAgICAgICAgfSxcbiAgICAgICAgICByZWRpcmVjdHMsXG4gICAgICAgIH0sXG4gICAgICAgIGNvcnJlY3Rpb25zOiB7XG4gICAgICAgICAgLi4uc3RhdGUuY29ycmVjdGlvbnMsXG4gICAgICAgICAgdGVybXM6IGQuc3BlbGxDb3JyZWN0aW9ucyB8fCBbXSxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIGNhc2UgU0VBUkNIX0NPUlJFQ1RJT05fU1VDQ0VTUzpcbiAgICAgIGNvbnN0IGdldENvcnJlY3RlZFRlcm0gPSBhY3Rpb24ucGF5bG9hZC5tZXRhRGF0YS5xdWVyeVBhcmFtcy5xO1xuICAgICAgY29uc3QgcmVkaXJlY3RMaW5rID0gYWN0aW9uLnBheWxvYWQucmVkaXJlY3Q7XG4gICAgICBsZXQgZ2V0UmVkaXJlY3RzID0ge307XG4gICAgICBpZiAocmVkaXJlY3RMaW5rKSB7XG4gICAgICAgIGdldFJlZGlyZWN0c1tjYXBpdGFsaXplKGdldENvcnJlY3RlZFRlcm0pXSA9IHJlZGlyZWN0TGluaztcbiAgICAgIH1cbiAgICAgIGNvbnN0IHsgcHJvZHVjdHM6IGNvcnJlY3RlZFByb2R1Y3RzIH0gPSBhY3Rpb24ucGF5bG9hZDtcbiAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSwge1xuICAgICAgICBjb3JyZWN0aW9uczoge1xuICAgICAgICAgIC4uLnN0YXRlLmNvcnJlY3Rpb25zLFxuICAgICAgICAgIGlzRmV0Y2hpbmdQcm9kdWN0czogZmFsc2UsXG4gICAgICAgICAgcHJvZHVjdHM6IGNvcnJlY3RlZFByb2R1Y3RzIHx8IHt9LFxuICAgICAgICAgIHJlZGlyZWN0czogZ2V0UmVkaXJlY3RzLFxuICAgICAgICB9LFxuICAgICAgfSk7XG4gICAgY2FzZSBTRUFSQ0hfQ09SUkVDVElPTlNfU1RBUlRFRDpcbiAgICAgIHJldHVybiBPYmplY3QuYXNzaWduKHt9LCBzdGF0ZSwge1xuICAgICAgICBjb3JyZWN0aW9uczoge1xuICAgICAgICAgIC4uLnN0YXRlLmNvcnJlY3Rpb25zLFxuICAgICAgICAgIGlzRmV0Y2hpbmdQcm9kdWN0czogdHJ1ZSxcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIGNhc2UgU0VBUkNIX0NPUlJFQ1RJT05TX0VSUk9SOlxuICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCB7XG4gICAgICAgIGNvcnJlY3Rpb25zOiB7XG4gICAgICAgICAgLi4uc3RhdGUuY29ycmVjdGlvbnMsXG4gICAgICAgICAgaXNGZXRjaGluZ1Byb2R1Y3RzOiBmYWxzZSxcbiAgICAgICAgICBlcnJvck1zZ0ZldGNoaW5nUHJvZHVjdHM6IGFjdGlvbi5lcnJvcixcbiAgICAgICAgfSxcbiAgICAgIH0pO1xuICAgIGNhc2UgU1VHR0VTVF9GQUlMVVJFOlxuICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCB7XG4gICAgICAgIHNob3dEcm9wZG93bjogdHJ1ZSxcbiAgICAgICAgc3VnZ2VzdGlvbnM6IHtcbiAgICAgICAgICAuLi5zdGF0ZS5zdWdnZXN0aW9ucyxcbiAgICAgICAgICBlcnJvcjogYWN0aW9uLmVycm9yLFxuICAgICAgICB9LFxuICAgICAgfSk7XG4gICAgY2FzZSBUT0dHTEVfRFJPUERPV046XG4gICAgICByZXR1cm4gT2JqZWN0LmFzc2lnbih7fSwgc3RhdGUsIHtcbiAgICAgICAgc2hvd0Ryb3Bkb3duOiBCb29sZWFuKGFjdGlvbi5wYXlsb2FkKSxcbiAgICAgIH0pO1xuICAgIGNhc2UgVVBEQVRFX0xTX1BST0RVQ1RTOlxuICAgICAgY29uc3QgdXBkYXRlZFByb2R1Y3RzID0gZ2V0UHJvZHVjdHNGcm9tTG9jYWxTdHJvcmFnZSgpO1xuICAgICAgcmV0dXJuIE9iamVjdC5hc3NpZ24oe30sIHN0YXRlLCB7XG4gICAgICAgIC4uLnVwZGF0ZWRQcm9kdWN0cyxcbiAgICAgIH0pO1xuXG4gICAgY2FzZSBUT0dHTEVfTUFJTl9MT0FERVI6IHtcbiAgICAgIGNvbnN0IG9sZFN0YXRlID0geyAuLi5zdGF0ZSB9O1xuICAgICAgY29uc3QgbG9hZGluZyA9IGFjdGlvbj8ucGF5bG9hZD8ubG9hZGluZztcbiAgICAgIHJldHVybiB7IC4uLm9sZFN0YXRlLCBzaG93TWFpbkxvYWRlcjogbG9hZGluZyB9O1xuICAgIH1cblxuICAgIGRlZmF1bHQ6XG4gICAgICByZXR1cm4gc3RhdGU7XG4gIH1cbn07XG5cbmV4cG9ydCBkZWZhdWx0IHJvb3RSZWR1Y2VyO1xuIiwiaW1wb3J0IHRodW5rTWlkZGxld2FyZSBmcm9tICdyZWR1eC10aHVuayc7XG5pbXBvcnQgeyBjcmVhdGVTdG9yZSwgYXBwbHlNaWRkbGV3YXJlLCBjb21wb3NlIH0gZnJvbSAncmVkdXgnO1xuXG5pbXBvcnQgcm9vdFJlZHVjZXIgZnJvbSAnLi9yZWR1Y2Vycy9pbmRleCc7XG5cbmNvbnN0IGVuYWJsZUNocm9tZUV4dGVudGlvbiA9IHRydWU7XG5cbmNvbnN0IGNvbXBvc2VFbmhhbmNlcnMgPSBlbmFibGVDaHJvbWVFeHRlbnRpb24gPyB3aW5kb3cuX19SRURVWF9ERVZUT09MU19FWFRFTlNJT05fQ09NUE9TRV9fIHx8IGNvbXBvc2UgOiBjb21wb3NlO1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBjb25maWd1cmVTdG9yZShpbml0aWFsU3RhdGUpIHtcbiAgcmV0dXJuIGNyZWF0ZVN0b3JlKHJvb3RSZWR1Y2VyLCBpbml0aWFsU3RhdGUsIGNvbXBvc2VFbmhhbmNlcnMoYXBwbHlNaWRkbGV3YXJlKHRodW5rTWlkZGxld2FyZSkpKTtcbn1cbiIsImNvbnN0IHVuYnhkQ29uZmlnID0gd2luZG93Py51bmJ4ZENvbmZpZztcbmV4cG9ydCBjb25zdCBBVVRPQ09NUExFVEVfQVBJX1VSTCA9IHVuYnhkQ29uZmlnPy5hdXRvQ29tcGxldGVVcmw7XG5leHBvcnQgY29uc3QgQVVUT1NVR0dFU1RfQVBJX1VSTCA9IHVuYnhkQ29uZmlnPy5hdXRvU3VnZ3Vlc3RVcmw7XG5leHBvcnQgY29uc3QgU0VBUkNIX0FQSV9VUkwgPSB1bmJ4ZENvbmZpZz8uc2VhcmNoVXJsO1xuZXhwb3J0IGNvbnN0IFNFUlZFUl9FUlJPUl9NU0cgPSAnT29wcyEgU29tZXRoaW5nIHdlbnQgd3Jvbmcgd2hpbGUgc2VhcmNoaW5nIGZvciB7eyVxfX0uIFBsZWFzZSB0cnkgYWdhaW4uJztcbmNvbnN0IFNUT1JFX0RFRkFVTFQgPSAnRGVmYXVsdCBTdG9yZSBWaWV3JztcbmNvbnN0IFNUT1JFX1FVRUVOU0xBTkQgPSAnUXVlZW5zbGFuZCBTdG9yZSBWaWV3JztcbmNvbnN0IFNUT1JFX1NPVVRIX0FVUyA9ICdTb3V0aCBBdXN0cmFsaWEgU3RvcmUgVmlldyc7XG5jb25zdCBTVE9SRV9XRVNUX0FVUyA9ICdXZXN0ZXJuIEF1c3RyYWxpYSBTdG9yZSBWaWV3JztcbmV4cG9ydCBjb25zdCBQUklDRV9GSUVMRCA9ICdwcmljZSc7XG5leHBvcnQgY29uc3QgU1BFQ0lBTF9QUklDRV9GSUVMRCA9ICdzcGVjaWFsUHJpY2UnO1xuZXhwb3J0IGNvbnN0IE9SSUdJTkFMX1BSSUNFX0ZJRUxEID0gJ29yaWdpbmFsUHJpY2UnO1xuZXhwb3J0IGNvbnN0IFNUT1JFU19FTlVNID0ge1xuICBbU1RPUkVfREVGQVVMVF06IDEsXG4gIFtTVE9SRV9RVUVFTlNMQU5EXTogMixcbiAgW1NUT1JFX1NPVVRIX0FVU106IDMsXG4gIFtTVE9SRV9XRVNUX0FVU106IDQsXG59O1xuIiwiZXhwb3J0IGNvbnN0IEhBTU1FUl9TUElOTkVSX0JBU0U2NCA9XG4gICdkYXRhOmltYWdlL2dpZjtiYXNlNjQsUjBsR09EbGhRQUJBQVBBQUFBQUFBTzR4SkNINUJBMEVBQUFBSWY4TFRrVlVVME5CVUVVeUxqQURBUUFBQUN3QUFBQUFRQUJBQUFBQ2xZU1BxY3Z0RDZPY3ROcUxzOTY4K3crRzRraVc1b21tWmNDMkxxdEM3L3pHRG8zRHRwenJPNC83UlhyQ1J5OVFiQnlUaXlPU2lYRDZtTklXdEZvVDVnN2JYWmRMOHdZVFk5V1hQSXVkMFM3eld0Rk9FYUhzTUQzNnZ1YXArK1Q4YnZBSENORG5WelpZV0pTb2RYZ242TGdvMWtqM1NCbHBVNmszQ1prMmlOZnBDUllYK3ZsRWVvcWFxcnJLMnVyNkNoc3JTMUlBQUNINUJBMEVBQUFBTEFBQUFBQkFBRUFBQUFLUWhJK3B5KzBQbzV5MDJvdXozcno3RDRiaVNKYm1pYWJxdWdXdXkxN3ZETWNTamRzMy91b1JYL005Z0VJSXNlZzRJaFhBNE5MUWRDS2pzeWUxV3J6U3NscXNyWnVMYVErODFUaFJQcDJaNmRGNm9RUjFqV0g1ZGJmOXpDZGV6eHR2ZDdmMDUwT29zOGNsbUVnMWFIaW8yTmowaE5ZMlNWWm5pWkNYeWNicCtRa2FLanBLV21wNmlwcXF1c3JhcWxJQUFDSDVCQTBFQUFBQUxBQUFBQUJBQUVBQUFBS1FoSStweSswUG81eTAyb3V6M3J6N0Q0YmlTSmJtaWFicXlyWnVHY1N2RmRjelZkdDNsT3Y3MDVQOUhNSEFrQmc4Tm9yS0JiT1pLQnFoQnVsVWFmWGRzczhXTjd2NmlrL2lzbEJrVGw4OWF1bWh1Mm4zbHZPTXZJN01WZTVKQ1R6UHQrYlhCeFJvOStmVTF1RldPQlpDMk1nNG9qY2hTWVZIbGFtNXlkbnArUWthS2pwS1dtcDZpcHFxdXNyYStsa0FBQ0g1QkEwRUFBQUFMQUFBQUFCQUFFQUFBQUtGaEkrcHkrMFBvNXkwMm91ejNyejdENGJpU0pibWlhYnF5cmJ1QzhmeWZBUTJMZGszL3VnQjM5TUJIYjRody9jektwREtaYkZaWTBJQnlDUzBhaDFobjRrdE4rZ05pOGZWSFBtTXhrYlM3UGE2RFNlLzQvU3R1WTRYenVzV3RabFZOaFY0SlVYNFplVFhaS2VZaU5qb2VEZ2xPVWxaYVhtSm1hbTV5ZG5wK1FrYUt0cFNBQUFoK1FRTkJBQUFBQ3dBQUFBQVFBQkFBQUFDa0lTUHFjdnRENk9jdE5xTHM5NjgrdytHNGtpVzVvbW02c3EyN2d2SDhrelg5ZzBFK0JQb085UHpYWUpDVXhCREpKYVVsYVN6K0dGT250U2VSeXFwYXJHVVpIY0xQbjZ0MDdDWkRQR1d6MnlIT3NNMks5NmR1QjBOdXN1TmVpcXJMd1pqZHhQMlUvVmo0SWVvYU9pRW1FaTM4L1NZRTRuREtEbTU2UGlvU1dsSkdTbzZTbHBxZW9xYXFycksyaHBSQUFBaCtRUU5CQUFBQUN3QUFBQUFRQUJBQUFBQ21ZU1BxY3Z0RDZPY3ROcUxzOTY4K3crRzRraVc1b21tNnNxMjdndkhjaFVFczFUbnQ1UDNPOU1ML2hMQjRoQlFUTnArU2lPejZkUXNOOURvcGVhcENxL1lqMVpINjRLKzRnaFlSSjVDenVndnJ0eDJQM3ltTkk5ZHNpL29LZktlMytkM3NOV1NabFZvQ09lU3VLTjNJM2NFZFRTb05HbFFhWW1VbElrNTJSbTVhZmtKZWpnVXlsbEtpb2VxbHVuNkNoc3JPMHRiYXd0YkFBQWgrUVFOQkFBQUFDd0FBQUFBUUFCQUFBQUNtb1NQcWN2dEQ2T2N0TnFMczk2OCt3K0c0a2lXNW9tbTZzcDJ3UnUwRmd6TEUwM2JFWjdyRFkvei9ZQTE0WUpZTkNhUUwrVVI2VlF3bzBzbzljQ01YUTNUTGFDN0JWL0ZWUEtqaVRJUFZlcm5xbDFsd2JGb3RuV29sUlB4ZWYxZW10UmkxbU5EUnFoekYyU1VwYmpJV09mSUdQWFlwNVRsTlZkNFY3YnBsQ240NmZjM09Ub0c1TVYxaXZyVnVIcTR5Z29yTzB0YmEzdExWUUFBSWZrRURRUUFBQUFzQUFBQUFFQUFRQUFBQXFHRWo2bkw3UStqbkxUYWk3UGV2UHNQaHVKSWxtV0FtaDNLQnFyV3NtOFd5N05WMi9lVXB6dlZjLzE0dVNHd2FJd0VrMG9rODlGN1FxTFNCclhLdUdJVjJ1MWg2VTEwdzRDeEYwdzJtTGZvZEp1OFpqdmRjMnN5cnZZTjhicDl6ZEgzczlieVpFWlkrTWVsaHhnakZuZzNkMWhGMVlnVmxHaDV1Wmg1Q2RjWnBpa2s5emtaS2lwbGVzcVVxcm9hQ29xSGlwbVc5MGkzU1p1cnU4dmI2OHRRQUFBaCtRUU5CQUFBQUN3QUFBQUFRQUJBQUFBQ3FZU1BxY3Z0RDZPY3ROcUxzOTY4K3c4ZVFVZ0N3WG1XSHNxcUhkdTZHb3pLTXozYUdaN3JGdS9ENElLWElmRkhPMWFNU2dtekdYbENIZEpwbzJwVllMT0lMZGNBL0RLODM3QTRZVDUzazJvME9adHVtOWh5OEJ0T3I5K3RlMzVlM2pmMXQ5RFRSSllpT0NpQ21CaER5QWlGNWRoWW93VkpXYmw0R1prM2lRZGpsOG5GbzhoSnVva1pwM1NxNnNOS212cGF1Q3JyU1ZzckJpUmFCbHFuT2VzYkxEeE1YR3k4VUFBQUlma0VEUVFBQUFBc0FBQUFBRUFBUUFBQUFxR0VqNm5MN1Eram5MVGFpN1BldlBmZ2hVcEFscUFvbWlXYXFpZjd1YkRudnJOVzMxeXU0M0x2TXdHRHd1R0ZaNndnazVNbE0rSjhQcUxTQnJXNnVHSkhxaTMwNTdXQ3c5a3hPYUVscDhOcmIzdjd4c2FyYzNyM3pERGpEL285b0w5WDl5VElSRmg0NTRjQWlMZTRBb2VJUnVMV1YvUll5ZWM0Q1FscDk3UElVaE1xZWloYUtrbHFPb3FhK2puREd0cjVlaG83QzV0NGk1dXJ1OHZiNjl0UkFBQWgrUVFOQkFBQUFDd0FBQUFBUUFCQUFBQUNub1NQcWN2dEQ2T2N0TnFMczk2OCt3ODZ3VWlHSVltYUhwcXFIRnU2R3p6S00yMXJlSjd0L09YN1ZZTENDYkVZZ1NFdHlxV3g2VXhDbzQ4amRXRzlKcVphTExlNy9ZSVAyWEVaZk5iU0FtT3YyUHlHczlydUZoMlJWc2ZSODN2WTdnZVFkelZJdGNjSEdIall0VWpZYVBnb2t5Z1lWMlBqa3pWNXNzYXBvTG5KaWRtWEUxbzZTbXBxV3BSYXlzaEtwM3EzRmtoYmEzdUxtNnU3bTFFQUFDSDVCQTBFQUFBQUxBQUFBQUJBQUVBQUFBS2NoSStweSswUG81eTAyb3V6M3J6N0Q0WmlGcFRCR0pvbTJxa3F1N2tyVE1vbmZkazNidWw4Yi9zQlpjSktzRGp4SVNYSEpVVHBmRFNqamlsMVliMHFzdG9EdEp2Z2dyOWdMN0VjRm5mSlpYWmJyWVZmM2VNekdpR241dlgyTzJBZkJlalU1eWNvOHJKRmlPU1N5QmhJYUppaU04azNTY2RqZWZtVHFZaTNROE01NHltNkdhckpZdHBabXJvV1doanBGeXM3UzF0cmU0dWJXMUVBQUNINUJBMEVBQUFBTEFBQUFBQkFBRUFBQUFLbmhJK3B5KzBQbzV5MDJvdXozcnp6QUliaTZHWGppWllYeW9vcTFjYkJPOGt0TGRrc0h1a3AvL0NkZ0VHaGk5Z3dIcEVMWllpWmRFSVowaW5WWjYzOHNyVWh0K3Y5UW5iaThiWWNEYU92Nm5YaTVtNmU0NFlZL1dDNzEyVjZRRjZ2QStqMDlEVkltR1VJd2tTU09BT1Uwa2p6QkpsWU1rUnA2SUhwRXRteE9WbUprYmNUdXFMb2Qva3BwSkxhK21mbE5kYzNTMXRyZTR1YnE3dkwyK3Y3Q3h3c1BFd2NVUUFBSWZrRURRUUFBQUFzQUFBQUFFQUFRQUFBQXFxRWo2bkw3UStqbkxUYWk3UGV2UHNQUHNGSUJpRllwdU9wcVc3SlV1OXN4aEU5MnlMKzZnN2Y4ekdBTHVHUW1ESXVrRWxsZ2tseUtxQXI2Uk5xYlJDelB4cTNHL3dlbTJKdHNXeFdvZFBrTlNMc1B1VGliM2g1NndiUzlYRWVIZUMzaHlPa0Jxakc5eEZsbU5URDFNRjRXRVJWOVJnSk9WbkRJV2s1bVhnSmcwbFpHYldKcVZQYWVmcEo1VlFUbXRscWl1YjR0M2hXTzRXcnU4dmI2L3NMSEN3OFRGeHNmQXhjQUFBaCtRUU5CQUFBQUN3QUFBQUFRQUJBQUFBQ3I0U1BxY3Z0RDZPY3ROcUxzOTY4K3crRzRoWUVvMWltcDVlMjVwcTVNbnpKTmozWitvczcrOTVqL0g1QnhaQllQQnlCU2NOUzEzUStYVkhsVkZXMVByUEdKYmVML0dyRFlzQ3hQSWFpemVwMUd1dG10Ny9iVGlsVzU3U2s4RWFlUlhRSE5uUUNSZlYyVXpoamVMWnl3MGdJUXdWSjVxaEN5WlNVK0llREdSbjBlQ1hZc3lkNlNHcGllZ3FxMm9mYXlsTUVTeWRhTmhYWGlKc1pKN2ZIQy9ZYkxEeE1YR3g4akp5c3ZNeThVQUFBSWZrRURRUUFBQUFzQUFBQUFFQUFRQUFBQXEyRWo2bkw3UStqbkxUYWk3UGV2UHNQaHVKSWx1YUpYa0dRYnV2THRoWU15eFZkMnhGTzZ3K1A4ekdBUEdHQ1NEUWFrRWtqc3lsOEFwWFNJTFg2VWk2eHNaOEpLOG1Ld0JNeHFEb3Jac2dxYTFxcW1Uck1BRFptdWxwdzg1OWlEN0UzVmlNWE9DSkdhRGUyNVpkSWtzVFZnZ2dud3pocDA5TVltUWZwdEdqcHhIbFZHTFhIQnpwS2l1cFQyblg2S2Zxa0JSZ3Jld1JWTzZ1R2EvdTNxMGZuR3l3OFRGeHNmSXljckN4VUFBQWgrUVFOQkFBQUFDd0FBQUFBUUFCQUFBQUN0NFNQcWN2dEQ2T2N0TnFMczk2OCt3K0c0a2lXNW9tbTZzcTJyaHNFYnhYWDhnelpOZTdvT3IvdytTNHhrWEJJdkhtT3dxU1N3MnhhaXM5TUZEbjlhYTVhV2hmRC9VcWsxbkRSaXdXYnF6a2tlN0krdzIyR1hUWitCNlFQOGtTOGYvY2w5bWZuSkVYSVZjWlVoM2dFNWFqWFNMYlZKTWxZMktGbGVVSVhpYWh5MWdqek9mcUhRd2lFQitTWnVNcDY1Zm9hNWJvV1cwdHJaaHNXS3p1WjJxcTd5SHU1eDFzOHpJck1BS2pjN1B3TUhTMDlUUzFSQUFBaCtRUU5CQUFBQUN3QUFBQUFRQUJBQUFBQ3RZU1BxY3Z0RDZPY3ROcUxzOTY4K3crRzRraVc1b21tNnNxMjdoSUU3eFhQdEd4VGRjNlg4UzRDYW40L0VMRTRQT0tTU3VIdHlHd2lNYzJvZFBra1pxN1FxTUVwNFhhcHVMRk9ETFpJMVdoczFoeHV1eXZjUkJvaHQ0N2g5clplQzZEVUlIY1gxMVgzUlFoSUJxVG9XQmpSK0RpNU1Xa0pDWEZwV2FucHlORUplUFhScWNEM1NmbWkyQlBvdDlxNnlpb0dHNHNJKzlweml5czdtOHVETmt1N3hpc0tuR2hxdTFpc3ZNemM3UHdNSFMwOVVnQUFJZmtFRFFRQUFBQXNBQUFBQUVBQVFBQUFBcktFajZuTDdRK2puTFRhaTdQZXZQc1BodUpJbHVZWkJ1aW5ybDNRdWhvc2IzUTl4emdGOTN2VjgvMGt3ZHNRVWpRZUcwbmRjdEY4T29wU25ySjZhRHF4QU8yV3FQVjRMOTRyWmd3c215M2x0SnJUdHI1dHlRU1ZxZjZtRldoR1hzLzJCOWlWQnljb0JQVkhkM2hIS0xqSUdObDRKbG1KU0dsWkNabkphTWpadWZsWkdDb2FselA2b0VoYUY0RzZldGs2S1RhSDVTcGwrNFM3cEh2RTIydGEyOGZsT0Roc2ZJeWNyTHpNM096OGpGTUFBQ0g1QkEwRUFBQUFMQUFBQUFCQUFFQUFBQUszaEkrcHkrMFBvNXkwMm91ejNyejdENGJpU0RKQlVJWm5DcTZzNTc0Y0tzOXhmWjA2bnVzN1QvSDlnQkxoamZndzBwQVFKVE40ZkM1ODBxS3pPbFV1c1FqdGxtdndnaFBpOGFGc0RsL04wZlRadXpiRmJYQ2hvLzd0NGFtTi9XWnYxNGYzQjhpbkFFaFlPTlRscDZGb1JOYVk4VWdKNlZpSm1WZVJXVW5IV2VqNUtUbFo5L1laYWhtSm1ZaVdoSGdKTnpHS01XaFZ5cW9sV3d2YkduSExtMHNiaUdyb3B0Wm1uS3k4ek56cy9Bd2RMVDFOclZBQUFDSDVCQTBFQUFBQUxBQUFBQUJBQUVBQUFBSzhoSStweSswUG81eTAyb3V6M3J6N0Q0WmlGQVRqV0o1aHFvSnM2NWx3Vjc0elZ0dDNsZGY3MWRQOUlFSGgwRkU4V25wS2tzOGdhejZTMG1reFdsMWNqZGt0Tm92d2dobmJjZmdWTkIrWVVHNzFxazU0M2MxNXVtdG4xL042SmI5LzlQZlVBTWdobUVOSVJYTTRLQ2Uyd1lqb09LY1JDY2lYWVhrSGdJbWpxZWxweC9sWk9KRjNSdnBGSWFxUUd2cllDdm9LaHhSWkNXdlZDVW5yZFBwUm1zZzZwZ3RHWEV3WnQ4YWIzQ2JKUFBrY0xUMU5YVzE5aloyZFVBQUFJZmtFRFFRQUFBQXNBQUFBQUVBQVFBQUFBcjJFajZuTDdRK2puTFRhaTdQZXZQc1BoaUlXakdOUW1pQ3FybXpydlhDRzF2TlcyL2VWNnp2VjgvMGtRZG53d1FvZUl6bURjTWtvQW94UVJaRmF0VjVUV2UyMm0waCt3VTZkbEZ4dVRwL2RheHE5SHNQamN2TDIzTDRycy9yNnIrKzNBK2gyTklqM1o3ZzNsS2dveU5pbzlmRjRpTkFUTXdsSnA2WUJlS0JYR1ZqUkYzYm5XY3A1Q29ySlprSG90Y3BGWTlrQWkwWENxb29KdFZvMW1XY0l4dmc3YUVkY1BBcUhQUGM1bDVhNVBOdE1haXRkYlgyTm5hMk5VQUFBSWZrRURRUUFBQUFzQUFBQUFFQUFRQUFBQXJHRWo2bkw3UStqbkxUYWk3UGV2UHNQaHVKSWx1YUpUc0dhYm12UVh1d2JWeTlkVXplZVJ6dmZZOHgrc0tDUVJqUXVmb0NkTWtFY0ZwK0hLSXRhTFZxeGhsczNpbVdDcVdEbTB5cWRHdEZJZFpEdEpjUGo2N256YlRmWDh1TVdmNXZ5QjNnaTJFZFllT2VIQ0lTeXlOaUlscFdVWTlnMEdLT0hjSW41S0pscDJhazQrWlhZQTdpNVYxZ25xSVRZcXJyS0Z5dUx4MXBMZTJ0M2h2czZ4d1dLdWhzc04vcnJHZnBMWjd6TTNPd2NVUUFBSWZrRURRUUFBQUFzQUFBQUFFQUFRQUFBQXF5RWo2bkw3UStqbkxUYWk3UGV2UHNQaHVKSWx1YUpwdXJLdGxVQXV4QWMwNEhzMkRxKzNJQk80eWxzUDZCUGFMd1pjY0Zra2VqYUpaWEJLUEFwUGJLbUJxZDFTcjF1d1ZXeGlueUZudEhMMkpxOUhNUGpxem00YnZlKzg3czl2K3IzSi9mbmhpZEdKbVBXcGZmRndDZzBSQWNaS1RtSjhHaTVxSmlwMmNkNUtlbkpGRmVaYUFlWk41bkt3OGNxYU5vS3Uvb1YyMExvT2tzTGg3UnJpZlo1OTlsWktId0FXSXljbkZ3QUFDSDVCQTBFQUFBQUxBQUFBQUJBQUVBQUFBS3FoSStweSswUG81eTAyb3V6M3J6N0Q0YmlTSmJtaWFicXlyYnVDOGZ5R3dSR2pkdUlqdVpBanJzQmJjRFNzUFk3Sm92QjBIRzRaRUtkejJvMHFQUlV0MGpsdE1QbFhyRklVSGdyTEpMTzZPeWE3VzJlNEZsZWo2NW00YitydmEvbDk5ZjNkVGF6RTJaNGlKYVl4c2M0VnZaNDhDU3BtRmRKV2Rrb2lPblc2U2pwK2NrcE53Tm5pQ2V6RitQSHVnb1RDTnRLTStzU1MvdUtTK2U2eTh1R2FxVlpxa2xjYkh6c1VBQUFJZmtFRFFRQUFBQXNBQUFBQUVBQVFBQUFBcHlFajZuTDdRK2puTFRhaTdQZXZQc1BodUpJbHVhSlZrR1FidXZhWmk4Y1d6T2I0SFZ6NndlOVc5d1VyNkJ3bUFNYWZ6M0ViRW5zU2FGSktaSnFzRGF4QU8wVzYzMXl3OFZ4bU11Y29wM1dOVnZ0N3JiamNyaGJTNjkvNy9NNG52N24xOGRuaCtZRkdMaDJhRVptU09ZRDlRaTVKS240YU5ub0NGUVlxYVRuMlFsNVpSaFZscGNsZG9vS2lzaXErZ29iS3p0TFcydDdpNXRyVUFBQUlma0VEUVFBQUFBc0FBQUFBRUFBUUFBQUFvMkVqNm5MN1Eram5MVGFpN1BldlBzUGh1SklsdWFKcHVyS3R1NGF4TUU3eVRJTjJUZis2RFB2OEFGN09rVnNhQkFtZGtEZkw4bkVPYWZES1pWbnZiNnlWaHBYeS9xQ1ZlTGxFMWF1ZnBIcDVwcjlkblBoYzVmWXViM2J2UG9qWHkvWEppV0k1UWRWQkdlMmh3Undkb0RJYUFRWitUaEoyV2hKcVhUSjJlbjVDUm9xT2twYWFucUttcXFLVWdBQUlma0VEUVFBQUFBc0FBQUFBRUFBUUFBQUFvaUVqNm5MN1Eram5MVGFpN1BldlBzUGh1SklsdWFKcHVyS3R1NEx4L0pNZDBGUU8vZSs1d25QOHlHQVBhR0JpRE1laU1vaHNIbEUvbTR4WkhCWlhGbTNUeFgzZXkyQngrRVErVXl0ZEoxb3RMcnRucFlqOExHNGJqM2gxNmhnMjVVRkpwUEV4aWNrQmNYVWhLaVUxNmdJQlpBVlNWbHBlWW1acWJuSjJlbjVDUm9xT2twYTJsQUFBQ0g1QkEwRUFBQUFMQUFBQUFCQUFFQUFBQUovaEkrcHkrMFBvNXkwMm91ejNyejdENGJpU0pibWlhYnF5cmJ1QzhmeVROZjJqZWY2R2ZUOWJ2QUpmYnJoc0hJVUdZbVU1ZS9qdERpWmsybFVhczFxclpldDl5dkVCZzdnOGpMeGJKclh6ekNTRFhlcjQzRHhkSk90M2tIN0NIZVVwSGNHQkVCRmVJaVlxTGpJMk9qNENCa3BPVWxaYVhtSm1hbTV5WWxSQUFBaCtRUU5CQUFBQUN3QUFBQUFRQUJBQUFBQ2ZvU1BxY3Z0RDZPY3ROcUxzOTY4K3crRzRraVc1b21tNnNxMjdndkg4a3pYOXYwRitvNG4reC9vR1lEQUhwR0lPeUtGUTZYTzZDelNvbEdLTkVQTkJodE9xL1o3YlZZaDRMS1pGNkVDem1DTWtzRithMzZTK0RQR3RuMkZZK1plcjViVXhYZkVKSFpubUtpNHlOam8rQWdaS1RsSldXbDVpWm1wdWZsU0FBQWgrUVFOQkFBQUFDd0FBQUFBUUFCQUFBQUNoWVNQcWN2dEQ2T2N0TnFMczk2OCt3K0c0a2lXNW9tbTZzcTI3Z3ZIUUVBSE1sVG45cTNvUHQvejFZQU5JWkh4T3daMXlvU1F4b01lbmprWjlicDdZYS9hTGJYcmZZS2xockRTZkVRVDFjN3NpbTFraGJueXViaGxqK1B6UTlpMk9mTUZHSmcwcUdkWUNJaWxTSmNtMkhRM1NDaEpXV2w1aVptcHVjblo2ZmtKR2lxcVVRQUFJZmtFRFFRQUFBQXNBQUFBQUVBQVFBQUFBbzJFajZuTDdRK2puTFRhaTdQZXZQc1BodUpJbHVhSnB1b0tCTzRic0JSTXl4TU4yemV1ejNVZjRRRWh3cUhoaGNERmpBZWxrbmwwRm8zU0tiQnF2V0tSME5ZUzIwMVV3MGtwdVdrK2U1M3E5Vk05aHFmbGJQcjdITWZQajlEdGIrajMxeFBJQlVpNFJFWFlkNWZEVklmV2xVVW1HVVlaZVdjbldMbTNpVW5YQmhvcU9rcGFhbnFLbXFvcVdnQUFJZmtFRFFRQUFBQXNBQUFBQUVBQVFBQUFBcGFFajZuTDdRK2puTFRhaTdQZXZQc1BodUpJbG1TQXBvSEpxU203dVNpc3lYVHQzdENjMkxxak9zaFd2OFp3V0FRZWcwbkdrdGxjUEhsUjZiVHF2R0tOeUMzWDU3V0N3NGdqV1p3Nzk3cHFnN2t0Wk1QZmMzbWJYaCtybDNDM2ZmOTNoZ2VvUjVqV04wVkVsa2pseGFqb21Dakl1RWdaUmdlRlpmY1NtUmwzZVloWWFNZ3BPbHA1aWxyYXg5cnErZ29iS3pzTFd3QUFJZmtFRFFRQUFBQXNBQUFBQUVBQVFBQUFBcUdFajZuTDdRK2puTFRhaTdQZXZQc1BBc0VZaEtaQmppZVlxcXZYbG0vbEl1MU0xMmlLVTZSOTYwbGlST0dRR0RRK2tFbWxnL2x6THFIU0k3SWFZV0t0dkMzazZwMDJ3NHNpK1JrN044eHFCYnVkZU1OMzQ3bDhMa3JqRDNjNGVKOVg1NmNIR05nRjJOZVdxRWk0dDNqMkNObUlwK1ZJUlhrNW1La0dGU1haS2ZQWnlaaDBTRGJwZVdyS0Y2cTZpa0thaWlob1Ixc3JXNWlydTh2YjY0dFhBQUFoK1FRTkJBQUFBQ3dBQUFBQVFBQkFBQUFDcW9TUHFjdnRENk9jdE5xTHM5Njg5eEI0WWdPQ294Y2lwWGxxWmJLbTdmV3E2NHpWUjR6VGVpenJUWUJFWVlVSU5GS1FQT1dRR1hSR21GSUxzbXBOWXBmYXJhVG8vWGJEa0RIWllUNHowbW9GdTIxcndoZmd1ZnM5cjl2anR6ME1ENmZuWndBWUtEZEllSWdvNk1lNDUyZ0htVmVvSm1tbzJHaFplYlVJTlFnVjFRYktNZ2thT1VxNnFZVVpadFluK3JxVHFqb0xvQU1ibS9pWWkyakwyM3ZiS3p4TVhHeDhqRkFBQUNINUJBMEVBQUFBTEFBQUFBQkFBRUFBQUFLcWhJK3B5KzBQbzV5MDJvdXozcno3RHpKQkVwUmhWNDVIcXA0WVM2YnVLeVB3Yk4xcmpWTnMrMnYxSXNIaTBGZjhIU2RKNVZMU05EMlp5U2t5YUwzcXNrUXNGK3I5UW96aWNiamNPS05GempXNzdWYVE0M0kxM1RDLzc3WjZQTHlmQjJoM0Y2aFhTUGdueUdjNFNIZm8yQmozS0puSXVJaFlDUm1KMXRRSEVDWGtCaW9sT2tyS2FSb3FscXI2ZFphWmxYakpKWHU2dHNoVGF2dlppbXE3UytrcFBFeGNiSHljVVFBQUlma0VEUVFBQUFBc0FBQUFBRUFBUUFBQUFxZUVqNm5MN1Eram5MVGFpN1BldlBzUGhxSVZsR05ZbW9oNlhtbXdzaTMxSnZWTXA3YU9UeTk4dVBVaXY2SnM2REFXa1VTbGtObHc4cUFQS1pDYWRHS3J5aTNFNkczK3d0d3hPV3MrTDdycTliS3RZTU5qNlhud2JUZUE4L2U2WFo2M3h3ZjRoeGNvZU9oWHFBaUhtUGkweU5qbU9FZllTS2ttOVdnWlpuWFU2Zm01RlNxS0JZWnBDaWxKNWJmS09xVUhDWG9rU3dZTGNEc1pWeHJJNS9zTEhDdzhUTXhSQUFBaCtRUU5CQUFBQUN3QUFBQUFRQUJBQUFBQ29vU1BxY3Z0RDZPY3ROcUxzOTY4K3crRzRraVdwaGFrSjVlcUs5b0dpZnhPTVgyNDlYUFArdDY0NFFBeG9FTkk2eGtaU09TUzJWUStmZEhmTkZlMVhyUGFiZlc2aUlLaHdqRzViS2FpMDRnbVcxMThZNTF5b3J0dVg4dnZkZjdlenlhR0J4aEkxMmY0aDFpb21DYlltUFhHTlFRbXVRajVXR2ttMmJVa3hmZ1UxeFpLMmFJZ05WWXFtdnBvT29xWE56bTQrbXJBU1h1TG02dTd5OXZyKzR0UUFBQWgrUVFOQkFBQUFDd0FBQUFBUUFCQUFBQUNqSVNQcWN2dEQ2T2N0TnFMczk2OCt3K0c0a2lXNW9tbTZzcTJWUURIcmhUWDhQellPcjdvUHAvd0NZRUg0WTlvUFBLU095Q3p1WHpLbk5JYnRZcXNCbVphRzdkcnhYV0pBQ2taa1R3cmh1cWlBYXF1bGVYdCtaUWV4OXZ6MHpwYTcyY1hGaWk0UlZoNFdHZ1lxRlRINXZmbzJOZ1dTVGtwQ2NqWWx6aVk2UGtKR2lvNlNscHFlb3FhR2xvQUFDSDVCQTBFQUFBQUxBQUFBQUJBQUVBQUFBSitoSStweSswUG81eTAyb3V6M3J6N0Q0YmlTSmJtaWFicXlyYnVDOGZ5VEcvQmpkOTFrdmY2N2dzR2FzSWdyV2lVSVlXd0pmUGxmSjZpMFJTMWFyMUtzOXFlcTR2N0FycFFYZkhvRzN0bnozVXNpUWczNGJ3ZjYweEU3dFI0WU4rL3RSY282TFlYSjJlWXFMakkyT2o0Q0JrcE9VbFphWG1KbWFtNXFWSUFBQ0g1QkEwRUFBQUFMQUFBQUFCQUFFQUFBQUo3aEkrcHkrMFBvNXkwMm91ejNyejdENGJpU0pibWlhYnF5cmJ1QzhmeVROZjJqZWQ2R2ZUK2ovc0pnYlNoMFpkQ0pvN01IcW9KTlQ2alZPZXBXcDFpb2FFaFlNdmRSTC9nSTZRc1JYc2Y2c0NpN1k1ZzVjcUd0ZEswMUVWbXpIMjB0eU00U0Zob2VJaVlxTGpJMk9qNENCa3BPVWxaYVhtSktWZ0FBQ0g1QkEwRUFBQUFMQUFBQUFCQUFFQUFBQUo3aEkrcHkrMFBvNXkwMm91ejNyejdENGJpU0pibWlhYnF5cmJ1QzhmeVROZjJqZWY2enZkdkFDd0JoMEVNMFVFTVZKTE01ZkhBakNvaDBtcFJZYzBtSDlxdTB0dEZncTJBc1pZNkZFZkxadktwN1RiQnE2aTV0SjZHd3ZGVExOZ1dsclBtUTFob2VJaVlxTGpJMk9qNENCa3BPVWxaYVhtSnFWSUFBQ0g1QkEwRUFBQUFMQUFBQUFCQUFFQUFBQUovaEkrcHkrMFBvNXkwMm91ejNyejdENGJpU0pibWlhYnF5cmJ1QzhmeVROZjJFd1Mzay9mNW52RDVnQWZoa0dnVUlwUEtIYk9KZXoxMWpKN3NHYlRTc0FEb2pOazEzb1pUY0t3OGxhTFRxelc2N1M2cjRtK1VOa3cvdXZKM05mMzdCN2cyVm1jalIyVGdoYmpJMk9qNENCa3BPVWxaYVhtSm1hbTV5YWxRQUFBaCtRUU5CQUFBQUN3QUFBQUFRQUJBQUFBQ2lvU1BxY3Z0RDZPY3ROcUxzOTY4K3crRzRraVc1b21tNnNxMnJoRUU3eFhIODFUbjk1UHJ1OU9yL1lCQjJaQlJOQjRWeVdXajZFVDJvbElmTlRHOVlyUGFBOWVyUEFiQnRtalc2dFI5MHpKb3QzbE5vbjl5OTY0dW4rSHhyMzE5NkRmWEYxaDJSeGltZDBpbnVCZ281dmk0eDhabmxoZG45eWJVdGNuWjZma0pHaW82U2xwcWVrcFNBQUFoK1FRTkJBQUFBQ3dBQUFBQVFBQkFBQUFDazRTUHFjdnRENk9jdE5xTHM5NjgrdytHNGtpV1lXQ0N3WXFtRzh1NkdoekxGNzNhR043cUZPK3pBSU1WR2xFSU94YVR5bCt0S1dGQ0ljWXA5V2wxU0xPTkxYZmgvU2JDNHNNMlZ3WThzZUljTG0xK3czbTlOajFOWjAvejVDYS9yL1NuQnlWWVoxVm9TRmo0aGNqVm1JV1lHUGk0UjFtNWVHaXBLTWpJMlptSEJ4b3FCNmMyV0lxYXFycksydXI2Q3F0VUFBQWgrUVFOQkFBQUFDd0FBQUFBUUFCQUFBQUNtb1NQcWN2dEQ2T2N0TnFMczk2OCs5OEZJa2dhNGxtU0o1cDZLOXR5YnhDSGJ5M1ArS2J2MnUzN3dZS1lGVkZJT3hhSFNncXpLVEZDSzgvcFEycU5qckxhTFJkUy9TcXc0a2E0ZkNDangxN3ZHckFGdmsyOTkrdzh2ZVB6ZXJlNDcvZlhOMGQzUndpblJ3aW9PR2kzdUFZWW1CVXBhVVVKZVZsR21TU1krYlhKK2VrcEdvbjVxRmxxbXVob3lDaDNxSFlvTzB0YmEzdGJWZ0FBSWZrRURRUUFBQUFzQUFBQUFFQUFRQUFBQXAyRWo2bkw3UStqbkxUYWk3UGVLUGpBaGM3M2lXWkNldWNLcENCcnVpOGN1clJvMzF5dWIybGZLd0Y5d21HR1pDVE9rcGNpMCtKOFVwQlNxS3BhaVdJaDJ1M282cFYwdzRzeEdRVStOOHhxQTdYTllNTTd5M21McDVhOXozcTV0SitXMTJkM0owTlkrRU9vZDRpNEI3ZW9DRGszT0FsWWFka0dHQmltV2NmWm1Ra3FxQmxLV2tvNWlzcG5lbXI0aVBucWFpZDc2TWQ0aTV1cm0xUUFBQ0g1QkEwRUFBQUFMQUFBQUFCQUFFQUFBQUthaEkrcHkrMFBvNXkwMm91ejNpNTR6NFhkRjRpbVFwYm5DcVRzbVlLdjZNNDBhZDlmSHVKOEwvdHRkc0poc0lnaElwUEhaVVhwZkRhakVpaTFPcjArc3RwUmw4TDlvc0ppaExXOE9LTVQ2dldoN1k0ellyVTF2VjYrKzlCNjhsVXZaM0FYMkJKRFdJZ1hSMGNJS05lb09BZ1o2ZGFuWXRkSFdabUplY25KcDluNW1BZjY2VGxxS2xacFdib28yZXE2NTVoNFNGdHJlNHRXQUFBaCtRUU5CQUFBQUN3QUFBQUFRQUJBQUFBQ25JU1BxY3Z0RDZPY3ROcUxzOTQxQkE1RzNoaVd5VWlhS3VxcEp2dTVKU3pQYkIzU09IZnZmT3Jib0lLL0dGRURQRjZHU2t5eTJXbEJMY3dwNVdrVlNiTVRMTGZoL1RMQ1lnVzVmS2lpeDlzMTIrZzJuTjB3ZUJ4UW4xdnoraW5manZaM3gzY25WMWRvcURONFdFalltUGNJR2ZjSEtFWTVlVW1YdVVaWm1kWHB1ZGVwdVZrR1N1cklXV29xdUNpSnlZaW9pRWhiYTN1TG0rdFFBQUFoK1FRTkJBQUFBQ3dBQUFBQVFBQkFBQUFDbDRTUHFjdnRENk9jdE5xTHM5NjgrdytHb2hlVVkxaW13VW1xSzd1NUw2ekpkRzNmV0s1ZmJyOVRBWU9tb1VWb1BCYVRGQ1J6a25vMm85S0lzL3FnWXJQYWJhUHJYVnpEaVRFWkFRNXIwOTRmWUhaK3UrTUgyVEp1WjJQeitpZS9uL1IzdDhlSFYzZ21pUGludUtnbUNGZjFDQm41NkZqWkptblp1Q1U1NlplSm1haVp4M2hJdGxscWRxcEsxK3I2Q2hzck8wdkxVUUFBT3c9PSc7XG5cbmV4cG9ydCBjb25zdCBLRVlXT1JEX1NVR0dFU1RJT04gPSAnS0VZV09SRF9TVUdHRVNUSU9OJztcbmV4cG9ydCBjb25zdCBJTl9GSUVMRCA9ICdJTl9GSUVMRCc7XG5leHBvcnQgY29uc3QgVE9QX1NFQVJDSF9RVUVSSUVTID0gJ1RPUF9TRUFSQ0hfUVVFUklFUyc7XG4iLCJpbXBvcnQgeyBLRVlXT1JEX1NVR0dFU1RJT04sIElOX0ZJRUxELCBUT1BfU0VBUkNIX1FVRVJJRVMgfSBmcm9tICcuL2NvbnN0YW50cyc7XG5pbXBvcnQgeyBTVE9SRVNfRU5VTSwgUFJJQ0VfRklFTEQsIFNQRUNJQUxfUFJJQ0VfRklFTEQsIE9SSUdJTkFMX1BSSUNFX0ZJRUxEIH0gZnJvbSAnLi4vY29uc3RhbnRzL2FwaSc7XG5cbmV4cG9ydCBjb25zdCBwcmVwYXJlQXV0b0NvbXBsZXRlUmVzdWx0cyA9IChwYXlsb2FkKSA9PiB7XG4gIGNvbnN0IGFsbFByb2R1Y3RzID0gcGF5bG9hZD8ucmVzcG9uc2U/LnByb2R1Y3RzO1xuICBjb25zdCBtZXRhRGF0YSA9IHBheWxvYWQ/LnNlYXJjaE1ldGFEYXRhO1xuICBjb25zdCBzcGVsbENvcnJlY3Rpb25zID0gcGF5bG9hZD8uZGlkWW91TWVhbiAmJiBbcGF5bG9hZD8uZGlkWW91TWVhblswXS5zdWdnZXN0aW9uXTtcbiAgY29uc3QgcHJvZHVjdHMgPSBhbGxQcm9kdWN0cz8uZmlsdGVyKChwcm9kKSA9PiBwcm9kLmhhc093blByb3BlcnR5KCdza3UnKSAmJiBwcm9kKTtcbiAgY29uc3QgcXVlcnlQcm9kdWN0c0NvdW50ID0gcGF5bG9hZC5yZXNwb25zZS5udW1iZXJPZlByb2R1Y3RzIHx8IDA7XG4gIGNvbnN0IGF1dG9TdWdnZXN0ID0gYWxsUHJvZHVjdHM/LmZpbHRlcigocHJvZCkgPT4gcHJvZD8uZG9jdHlwZSA9PT0gS0VZV09SRF9TVUdHRVNUSU9OICYmIHByb2QpO1xuICBjb25zdCB0b3BTZWFyY2hlZFF1ZXJpZXMgPSBhbGxQcm9kdWN0cz8uZmlsdGVyKChwcm9kKSA9PiBwcm9kPy5kb2N0eXBlID09PSBUT1BfU0VBUkNIX1FVRVJJRVMgJiYgcHJvZCk7XG4gIGNvbnN0IGluRmllbGRzU3VnZ2VzdGlvbiA9IGFsbFByb2R1Y3RzPy5maWx0ZXIoKHByb2QpID0+IHByb2Q/LmRvY3R5cGUgPT09IElOX0ZJRUxEICYmIHByb2QpO1xuICBjb25zdCByZWRpcmVjdCA9IHBheWxvYWQ/LnJlZGlyZWN0Py52YWx1ZTtcbiAgcmV0dXJuIHtcbiAgICBtZXRhRGF0YSxcbiAgICBwcm9kdWN0cyxcbiAgICBxdWVyeVByb2R1Y3RzQ291bnQsXG4gICAgYXV0b1N1Z2dlc3Q6IFsuLi50b3BTZWFyY2hlZFF1ZXJpZXMsIC4uLmF1dG9TdWdnZXN0XSxcbiAgICBpbkZpZWxkc1N1Z2dlc3Rpb24sXG4gICAgc3BlbGxDb3JyZWN0aW9ucyxcbiAgICByZWRpcmVjdCxcbiAgfTtcbn07XG5cbmV4cG9ydCBjb25zdCBnZXRQcm9kdWN0UHJpY2VBdHRyaWJ1dGVzID0gKGRhdGEpID0+IHtcbiAgaWYgKCFkYXRhKSByZXR1cm47XG4gIGNvbnN0IHN0b3JlTmFtZSA9IHdpbmRvdz8udW5ieGRDb25maWc/LnN0b3JlTmFtZTtcbiAgaWYgKCFzdG9yZU5hbWUpIHtcbiAgICByZXR1cm4geyBmaW5hbFByaWNlOiBkYXRhPy5wcmljZSwgc3RvcmVTcGVjaWFsUHJpY2U6IGRhdGE/LnNwZWNpYWxQcmljZSwgc3RvcmVPcmlnaW5hbFByaWNlOiBkYXRhPy5vcmlnaW5hbFByaWNlIH07XG4gIH1cbiAgY29uc3Qgc3RvcmVJZCA9IFNUT1JFU19FTlVNW3N0b3JlTmFtZV07XG4gIGNvbnN0IHN0b3JlRmllbGQgPSBgU3RvcmUke3N0b3JlSWR9YDtcbiAgY29uc3QgZmluYWxQcmljZSA9IGRhdGE/LltgJHtQUklDRV9GSUVMRH0ke3N0b3JlRmllbGR9YF0gfHwgZGF0YT8uW1BSSUNFX0ZJRUxEXTtcbiAgY29uc3Qgc3RvcmVTcGVjaWFsUHJpY2UgPSBkYXRhPy5bYCR7U1BFQ0lBTF9QUklDRV9GSUVMRH0ke3N0b3JlRmllbGR9YF0gfHwgZGF0YT8uW1NQRUNJQUxfUFJJQ0VfRklFTERdO1xuICBjb25zdCBzdG9yZU9yaWdpbmFsUHJpY2UgPSBkYXRhPy5bYCR7T1JJR0lOQUxfUFJJQ0VfRklFTER9JHtzdG9yZUZpZWxkfWBdIHx8IGRhdGE/LltPUklHSU5BTF9QUklDRV9GSUVMRF07XG4gIHJldHVybiB7IGZpbmFsUHJpY2UsIHN0b3JlU3BlY2lhbFByaWNlLCBzdG9yZU9yaWdpbmFsUHJpY2UgfTtcbn07XG5cbmV4cG9ydCBjb25zdCBmb3JtYXRQcmljZSA9IChwcmljZSA9IDApID0+IHtcbiAgbGV0IHByaWNlU3RyID0gcHJpY2UudG9GaXhlZCgyKS50b1N0cmluZygpO1xuXG4gIGlmIChwcmljZVN0ci5pbmRleE9mKCcuJykgPT09IC0xKSB7XG4gICAgcmV0dXJuIHByaWNlO1xuICB9XG5cbiAgbGV0IHByaWNlQXJyID0gcHJpY2VTdHIuc3BsaXQoJy4nKTtcblxuICBpZiAocHJpY2VBcnIubGVuZ3RoID4gMSAmJiBwcmljZUFyclsxXSAhPT0gJzAwJykge1xuICAgIHByaWNlID0gcHJpY2VBcnI7XG4gIH0gZWxzZSB7XG4gICAgcHJpY2UgPSBwcmljZUFyclswXTtcbiAgfVxuXG4gIHJldHVybiBwcmljZTtcbn07XG5cbmV4cG9ydCBjb25zdCByZW1vdmVEb3RzRnJvbVF1ZXJ5U3RhcnQgPSAocXVlcnkpID0+IHtcbiAgY29uc3Qgd2l0aG91dERvdHNRdWVyeSA9IHF1ZXJ5Py5yZXBsYWNlKC9eXFwuKi8sICcnKTtcbiAgcmV0dXJuIHdpdGhvdXREb3RzUXVlcnk7XG59O1xuXG5leHBvcnQgY29uc3QgaXNWYWxpZFF1ZXJ5ID0gKHF1ZXJ5KSA9PiB7XG4gIGNvbnN0IHRhZ1JlZ2V4ID0gLzxbXj5dKj8+fDxcXC9bXj5dKz58PFtePl0qJHxeW148XSo+L2c7XG4gIGNvbnN0IHRhZ3MgPSBxdWVyeS5tYXRjaCh0YWdSZWdleCk7XG4gIGlmICh0YWdzKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIHJldHVybiB0cnVlO1xufTtcblxuZXhwb3J0IGNvbnN0IHNhbml0aXplZFF1ZXJ5ID0gKGh0bWwpID0+IHtcbiAgY29uc3QgdGFnQW5kVGV4dFJlZ2V4ID0gLzxbXj5dKj8+W148XSp8PFxcL1tePl0rPnw8W14+XSokfD5bXjxdKi9nO1xuICBjb25zdCBzYW5pdGl6ZWRUZXh0ID0gaHRtbC5yZXBsYWNlKHRhZ0FuZFRleHRSZWdleCwgJycpO1xuICByZXR1cm4gc2FuaXRpemVkVGV4dC50cmltKCkgPT09ICcnID8gJycgOiBzYW5pdGl6ZWRUZXh0LnRyaW0oKTtcbn07XG4iLCJpbXBvcnQgeyBBVVRPQ09NUExFVEVfQVBJX1VSTCwgU0VBUkNIX0FQSV9VUkwsIFNFUlZFUl9FUlJPUl9NU0cgfSBmcm9tICcuLi9jb25zdGFudHMvYXBpJztcbmltcG9ydCB7XG4gIFNFQVJDSF9SRVNVTFQsXG4gIFNFQVJDSF9RVUVSWSxcbiAgU0VBUkNIX1JFUVVFU1QsXG4gIFNFQVJDSF9QUk9EVUNUUyxcbiAgU0VBUkNIX1JFU1VMVF9FUlJPUixcbiAgU0VBUkNIX0NMRUFSLFxuICBQUk9EVUNUU19DTEVBUixcbiAgU1VHR0VTVF9DTElDSyxcbiAgU1VHR0VTVF9SRVFVRVNULFxuICBTVUdHRVNUX1NVQ0NFU1MsXG4gIFNVR0dFU1RfRkFJTFVSRSxcbiAgVE9HR0xFX0RST1BET1dOLFxuICBVUERBVEVfTFNfUFJPRFVDVFMsXG4gIFNFQVJDSF9DT1JSRUNUSU9OX1NVQ0NFU1MsXG4gIFNFQVJDSF9DT1JSRUNUSU9OU19FUlJPUixcbiAgU0VBUkNIX0NPUlJFQ1RJT05TX1NUQVJURUQsXG4gIENMRUFSX0FDVElWRV9URVJNLFxuICBUT0dHTEVfTUFJTl9MT0FERVIsXG59IGZyb20gJy4uL2NvbnN0YW50cy9hY3Rpb24tdHlwZXMnO1xuaW1wb3J0IHsgc2V0VGVybXNPbkxvY2FsU3RvcmFnZSB9IGZyb20gJy4uL3V0aWxzJztcbmltcG9ydCB7IHByZXBhcmVBdXRvQ29tcGxldGVSZXN1bHRzIH0gZnJvbSAnLi4vdXRpbHMvY29tbW9uJztcbmltcG9ydCBBeGlvcyBmcm9tICdheGlvcyc7XG5jb25zdCBDYW5jZWxUb2tlbiA9IEF4aW9zLkNhbmNlbFRva2VuO1xuXG5sZXQgY2FuY2VsU2VhcmNoO1xubGV0IGNhbmNlbFRlcm1zO1xuXG5leHBvcnQgY29uc3Qgc2VhcmNoUXVlcnkgPSAocXVlcnkpID0+IHtcbiAgcmV0dXJuIChkaXNwYXRjaCkgPT4ge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzLCByZWopID0+IHtcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogU0VBUkNIX1FVRVJZLCBxdWVyeSB9KTtcbiAgICAgIHJlcyhxdWVyeSk7XG4gICAgfSk7XG4gIH07XG59O1xuXG4vLyBleHBvcnQgY29uc3QgcXVlcnlQcm9kdWN0c0NvdW50ID0gKHBheWxvYWQpID0+ICh7XG4vLyAgIHR5cGU6IFNFQVJDSF9RVUVSWV9QUk9EVUNUU19DT1VOVCxcbi8vICAgcGF5bG9hZCxcbi8vIH0pO1xuXG5leHBvcnQgY29uc3QgY2xlYXJTZWFyY2ggPSAoKSA9PiB7XG4gIHJldHVybiAoZGlzcGF0Y2gpID0+IHtcbiAgICByZXR1cm4gbmV3IFByb21pc2UoKHJlcywgcmVqKSA9PiB7XG4gICAgICBpZiAoY2FuY2VsU2VhcmNoKSBjYW5jZWxTZWFyY2goKTtcbiAgICAgIGlmIChjYW5jZWxUZXJtcykgY2FuY2VsVGVybXMoKTtcbiAgICAgIGRpc3BhdGNoKHsgdHlwZTogU0VBUkNIX0NMRUFSIH0pO1xuICAgICAgcmVzKCk7XG4gICAgfSk7XG4gIH07XG59O1xuZXhwb3J0IGNvbnN0IGNsZWFyUHJvZHVjdHMgPSAocXVlcnkpID0+ICh7IHR5cGU6IFBST0RVQ1RTX0NMRUFSLCBxdWVyeSB9KTtcbmV4cG9ydCBjb25zdCBzZWFyY2hSZXF1ZXN0ID0gKHF1ZXJ5KSA9PiAoeyB0eXBlOiBTRUFSQ0hfUkVRVUVTVCwgcXVlcnkgfSk7XG5leHBvcnQgY29uc3Qgc2VhcmNoQ29ycmVjdGlvblN0YXJ0ZWQgPSAoKSA9PiAoe1xuICB0eXBlOiBTRUFSQ0hfQ09SUkVDVElPTlNfU1RBUlRFRCxcbn0pO1xuZXhwb3J0IGNvbnN0IHNlYXJjaENvcnJlY3Rpb25FcnJvciA9IChlcnJvcikgPT4gKHtcbiAgdHlwZTogU0VBUkNIX0NPUlJFQ1RJT05TX0VSUk9SLFxuICBlcnJvcixcbn0pO1xuZXhwb3J0IGNvbnN0IHNlYXJjaFJlc3VsdCA9IChxdWVyeSwgdGVybXMsIHBheWxvYWQpID0+ICh7XG4gIHR5cGU6IFNFQVJDSF9SRVNVTFQsXG4gIHF1ZXJ5LFxuICB0ZXJtcyxcbiAgcGF5bG9hZCxcbn0pO1xuZXhwb3J0IGNvbnN0IHNlYXJjaFJlc3VsdEVycm9yID0gKGVycm9yKSA9PiAoe1xuICB0eXBlOiBTRUFSQ0hfUkVTVUxUX0VSUk9SLFxuICBlcnJvcixcbn0pO1xuZXhwb3J0IGNvbnN0IHN1Z2dlc3Rpb25DbGljayA9ICh0ZXJtKSA9PiAoeyB0eXBlOiBTVUdHRVNUX0NMSUNLLCB0ZXJtIH0pO1xuZXhwb3J0IGNvbnN0IHN1Z2dlc3Rpb25SZXF1ZXN0ID0gKHRlcm0sIGluaXRpYWwpID0+ICh7XG4gIHR5cGU6IFNVR0dFU1RfUkVRVUVTVCxcbiAgdGVybSxcbiAgaW5pdGlhbCxcbn0pO1xuZXhwb3J0IGNvbnN0IHN1Z2dlc3Rpb25TdWNjZXNzID0gKHRlcm0sIHBheWxvYWQpID0+ICh7XG4gIHR5cGU6IFNVR0dFU1RfU1VDQ0VTUyxcbiAgdGVybSxcbiAgcGF5bG9hZCxcbn0pO1xuXG5leHBvcnQgY29uc3QgY2xlYXJBY3RpdmVUZXJtID0gKCkgPT4gKHsgdHlwZTogQ0xFQVJfQUNUSVZFX1RFUk0gfSk7XG5cbmV4cG9ydCBjb25zdCBjb3JyZWN0aW9uUHJvZHVjdFNlYXJjaFN1Y2Nlc3NzID0gKHRlcm0sIHBheWxvYWQpID0+ICh7XG4gIHR5cGU6IFNFQVJDSF9DT1JSRUNUSU9OX1NVQ0NFU1MsXG4gIHRlcm0sXG4gIHBheWxvYWQsXG59KTtcblxuZXhwb3J0IGNvbnN0IHN1Z2dlc3Rpb25GYWlsdXJlID0gKGVycm9yKSA9PiAoeyB0eXBlOiBTVUdHRVNUX0ZBSUxVUkUsIGVycm9yIH0pO1xuXG5leHBvcnQgY29uc3QgZmV0Y2hTZWFyY2hUZXJtcyA9IChxdWVyeSkgPT4ge1xuICByZXR1cm4gYXN5bmMgKGRpc3BhdGNoKSA9PiB7XG4gICAgZGlzcGF0Y2goc2VhcmNoUmVxdWVzdChxdWVyeSkpO1xuICAgIGNhbmNlbFRlcm1zICYmIGNhbmNlbFRlcm1zKCk7XG5cbiAgICBBeGlvcy5nZXQoYCR7QVVUT0NPTVBMRVRFX0FQSV9VUkx9JnE9JHtxdWVyeX1gLCB7XG4gICAgICBtZXRob2Q6ICdHRVQnLFxuICAgICAgaGVhZGVyczoge1xuICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgfSxcbiAgICAgIGNhbmNlbFRva2VuOiBuZXcgQ2FuY2VsVG9rZW4oKGMpID0+IChjYW5jZWxUZXJtcyA9IGMpKSxcbiAgICB9KVxuICAgICAgLnRoZW4oKHsgZGF0YSB9KSA9PiB7XG4gICAgICAgIGNvbnN0IHByZXBhcmVkRGF0YSA9IHByZXBhcmVBdXRvQ29tcGxldGVSZXN1bHRzKGRhdGEpO1xuICAgICAgICBjb25zdCB7IG1ldGFEYXRhIH0gPSBwcmVwYXJlZERhdGEgfHwge307XG4gICAgICAgIGlmIChtZXRhRGF0YSAmJiBtZXRhRGF0YS5zdGF0dXNDb2RlICYmIG1ldGFEYXRhLnN0YXR1c0NvZGUgPT09IDQwMCkge1xuICAgICAgICAgIGNvbnN0IG1zZyA9IG1ldGFEYXRhLmRpc3BsYXlNZXNzYWdlIHx8IG1ldGFEYXRhLm1lc3NhZ2U7XG4gICAgICAgICAgZGlzcGF0Y2goc2VhcmNoUmVzdWx0RXJyb3IobXNnKSk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgY29uc3QgcSA9IHF1ZXJ5O1xuICAgICAgICAgIGNvbnN0IGF1dG9TdWdnZXN0ID0gcGFyc2VBdXRvc3VnZ2VzdFRlcm1zKHByZXBhcmVkRGF0YS5hdXRvU3VnZ2VzdCkgfHwgW107XG4gICAgICAgICAgY29uc3Qgc3BlbGxDb3JyZWN0aW9ucyA9IHByZXBhcmVkRGF0YS5zcGVsbENvcnJlY3Rpb25zIHx8IFtdO1xuICAgICAgICAgIGNvbnN0IGluRmllbGRTdWdnZXN0aW9uID0gcGFyc2VUZXJtcyhwcmVwYXJlZERhdGEuaW5GaWVsZFN1Z2dlc3Rpb24pIHx8IFtdO1xuICAgICAgICAgIGNvbnN0IGJyYW5kcyA9IHBhcnNlQnJhbmRzKHByZXBhcmVkRGF0YS5pbkZpZWxkU3VnZ2VzdGlvbiB8fCBbXSwgW1xuICAgICAgICAgICAgcSxcbiAgICAgICAgICAgIC4uLnNwZWxsQ29ycmVjdGlvbnMsXG4gICAgICAgICAgICAuLi5hdXRvU3VnZ2VzdCxcbiAgICAgICAgICAgIC4uLmluRmllbGRTdWdnZXN0aW9uLFxuICAgICAgICAgIF0pO1xuICAgICAgICAgIGNvbnN0IHByb21vdGVkU3VnZ2VzdGlvbiA9IHBhcnNlVGVybXMocHJlcGFyZWREYXRhLnByb21vdGVkU3VnZ2VzdGlvbikgfHwgW107XG4gICAgICAgICAgY29uc3QgdGVybXMgPSBbLi4uc3BlbGxDb3JyZWN0aW9ucywgLi4ucHJvbW90ZWRTdWdnZXN0aW9uLCAuLi5hdXRvU3VnZ2VzdCwgLi4uaW5GaWVsZFN1Z2dlc3Rpb24sIC4uLmJyYW5kc107XG5cbiAgICAgICAgICBwcmVwYXJlZERhdGEuc3BlbGxDb3JyZWN0aW9ucyA9IFsuLi5zcGVsbENvcnJlY3Rpb25zLCAuLi5wcm9tb3RlZFN1Z2dlc3Rpb24sIC4uLmluRmllbGRTdWdnZXN0aW9uXTtcbiAgICAgICAgICBkaXNwYXRjaChzZWFyY2hSZXN1bHQocXVlcnksIHRlcm1zLCBwcmVwYXJlZERhdGEpKTtcbiAgICAgICAgfVxuICAgICAgfSlcbiAgICAgIC5jYXRjaCgoZXJyb3IpID0+IHtcbiAgICAgICAgaWYgKCFBeGlvcy5pc0NhbmNlbChlcnJvcikpIGRpc3BhdGNoKHNlYXJjaFJlc3VsdEVycm9yKCdBbiBlcnJvciBvY2N1cnJlZCBkdXJpbmcgdGhlIHJlcXVlc3QuJykpO1xuICAgICAgfSk7XG4gIH07XG59O1xuXG5leHBvcnQgY29uc3QgZmV0Y2hDb3JyZWN0aW9uVGVybVJlc3VsdCA9ICh0ZXJtKSA9PiB7XG4gIHJldHVybiAoZGlzcGF0Y2gpID0+IHtcbiAgICBkaXNwYXRjaChzZWFyY2hDb3JyZWN0aW9uU3RhcnRlZCgpKTtcblxuICAgIEF4aW9zLmdldChgJHtTRUFSQ0hfQVBJX1VSTH0mcT0ke3Rlcm19JnBhZ2VTaXplPTVgLCB7XG4gICAgICBjYW5jZWxUb2tlbjogbmV3IENhbmNlbFRva2VuKChjKSA9PiAoY2FuY2VsU2VhcmNoID0gYykpLFxuICAgIH0pXG4gICAgICAudGhlbigoeyBkYXRhIH0pID0+IHtcbiAgICAgICAgY29uc3QgcHJlcGFyZWREYXRhID0gcHJlcGFyZUF1dG9Db21wbGV0ZVJlc3VsdHMoZGF0YSkgfHwge307XG4gICAgICAgIGRpc3BhdGNoKGNvcnJlY3Rpb25Qcm9kdWN0U2VhcmNoU3VjY2Vzc3ModGVybSwgcHJlcGFyZWREYXRhKSk7XG4gICAgICB9KVxuICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICBpZiAoIUF4aW9zLmlzQ2FuY2VsKGVycm9yKSkgZGlzcGF0Y2goc2VhcmNoQ29ycmVjdGlvbkVycm9yKGVycm9yKSk7XG4gICAgICB9KTtcbiAgfTtcbn07XG5cbmV4cG9ydCBjb25zdCBmZXRjaFRlcm1SZXN1bHQgPSAodGVybSwgaW5pdGlhbCA9IGZhbHNlKSA9PiB7XG4gIHJldHVybiAoZGlzcGF0Y2gpID0+IHtcbiAgICBkaXNwYXRjaChzdWdnZXN0aW9uUmVxdWVzdCh0ZXJtLCBpbml0aWFsKSk7XG4gICAgY2FuY2VsU2VhcmNoICYmIGNhbmNlbFNlYXJjaCgpO1xuICAgIHJldHVybiBBeGlvcy5nZXQoYCR7U0VBUkNIX0FQSV9VUkx9YCwge1xuICAgICAgbWV0aG9kOiAnR0VUJyxcbiAgICAgIGNhbmNlbFRva2VuOiBuZXcgQ2FuY2VsVG9rZW4oKGMpID0+IChjYW5jZWxTZWFyY2ggPSBjKSksXG4gICAgICBwYXJhbXM6IHtcbiAgICAgICAgcTogdGVybSxcbiAgICAgICAgcGFnZVNpemU6IDEyLFxuICAgICAgfSxcbiAgICB9KVxuICAgICAgLnRoZW4oKHJlc3BvbnNlKSA9PiB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSByZXNwb25zZT8uZGF0YTtcbiAgICAgICAgY29uc3QgcHJlcGFyZWREYXRhID0gcHJlcGFyZUF1dG9Db21wbGV0ZVJlc3VsdHMoZGF0YSkgfHwge307XG4gICAgICAgIGlmICgocHJlcGFyZWREYXRhLnByb2R1Y3RzIHx8IFtdKS5sZW5ndGggPT09IDApIHtcbiAgICAgICAgICBzZXRUZXJtc09uTG9jYWxTdG9yYWdlKHRlcm0sIGZhbHNlKTtcbiAgICAgICAgfVxuICAgICAgICBkaXNwYXRjaChzdWdnZXN0aW9uU3VjY2Vzcyh0ZXJtLCBwcmVwYXJlZERhdGEpKTtcbiAgICAgIH0pXG4gICAgICAuY2F0Y2goKGVycm9yKSA9PiB7XG4gICAgICAgIHNldFRlcm1zT25Mb2NhbFN0b3JhZ2UodGVybSwgZmFsc2UpO1xuICAgICAgICBpZiAoIUF4aW9zLmlzQ2FuY2VsKGVycm9yKSkgZGlzcGF0Y2goc3VnZ2VzdGlvbkZhaWx1cmUoZXJyb3IpKTtcbiAgICAgIH0pO1xuICB9O1xufTtcblxuZXhwb3J0IGNvbnN0IGZldGNoUHJvZHVjdHMgPSAocXVlcnkpID0+IHtcbiAgcmV0dXJuIChkaXNwYXRjaCkgPT4ge1xuICAgIHJldHVybiBuZXcgUHJvbWlzZSgocmVzb2x2ZSwgcmVqZWN0KSA9PiB7XG4gICAgICBkaXNwYXRjaChmZXRjaFRlcm1SZXN1bHQocXVlcnkpKVxuICAgICAgICAudGhlbigocHJvZHVjdHMpID0+IHtcbiAgICAgICAgICBkaXNwYXRjaCh7XG4gICAgICAgICAgICB0eXBlOiBTRUFSQ0hfUFJPRFVDVFMsXG4gICAgICAgICAgICBxdWVyeSxcbiAgICAgICAgICAgIHBheWxvYWQ6IHsgcHJvZHVjdHMgfSxcbiAgICAgICAgICB9KTtcbiAgICAgICAgICByZXNvbHZlKHByb2R1Y3RzKTtcbiAgICAgICAgfSlcbiAgICAgICAgLmNhdGNoKChlcnJvcikgPT4ge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHByb2R1Y3RzOicsIGVycm9yKTtcbiAgICAgICAgICByZWplY3QoZXJyb3IpO1xuICAgICAgICB9KTtcbiAgICB9KTtcbiAgfTtcbn07XG5cbi8qKlxuICogQHBhcmFtIHtBcnJheX0gYXJyXG4gKiBAcmV0dXJucyB7QXJyYXl9XG4gKi9cbmNvbnN0IHBhcnNlVGVybXMgPSAoYXJyKSA9PiB7XG4gIHJldHVybiBhcnI/Lm1hcCgodGVybSkgPT4gdGVybT8uYXV0b3N1Z2dlc3Q/LnRyaW0oKSk7XG59O1xuXG5jb25zdCBwYXJzZUF1dG9zdWdnZXN0VGVybXMgPSAoYXJyKSA9PiB7XG4gIHJldHVybiBhcnI/Lm1hcCgodGVybSkgPT4gKHsgdGVybTogdGVybT8uYXV0b3N1Z2dlc3Q/LnRyaW0oKSwgcHJvZHVjdF9jb3VudDogdGVybS5yZXN1bHRfdW5ieGRfZG91YmxlIH0pKTtcbn07XG5cbi8qKlxuICogQHBhcmFtIHtBcnJheX0gaW5GaWVsZHNcbiAqIEBwYXJhbSB7QXJyYXl9IGNvcnJlY3Rpb25zXG4gKiBAcGFyYW0ge1N0cmluZ30gcXVlcnlcbiAqIEByZXR1cm5zIHtBcnJheX1cbiAqL1xuY29uc3QgcGFyc2VCcmFuZHMgPSAoaW5GaWVsZHMsIHRlcm1zKSA9PiB7XG4gIGxldCBicmFuZHMgPSBbXSxcbiAgICBzdWZmaXggPSB0ZXJtcy5sZW5ndGggPyB0ZXJtc1swXSA6ICcnO1xuXG4gIGZvciAobGV0IGkgPSAwOyBpIDwgaW5GaWVsZHMubGVuZ3RoOyBpKyspIHtcbiAgICBpZiAoJ3VuZGVmaW5lZCcgIT09IHR5cGVvZiBpbkZpZWxkc1tpXVsnYnJhbmRfaW4nXSkge1xuICAgICAgbGV0IGJyYW5kSW4gPSBpbkZpZWxkc1tpXVsnYnJhbmRfaW4nXVxuICAgICAgICAuZmlsdGVyKChiaSkgPT4ge1xuICAgICAgICAgIGxldCBicmFuZCA9IGJpLnRyaW0oKTtcbiAgICAgICAgICByZXR1cm4gdGVybXMuaW5kZXhPZihicmFuZCkgIT09IC0xICYmIGJyYW5kICE9PSBzdWZmaXg7XG4gICAgICAgIH0pXG4gICAgICAgIC5tYXAoKGJyKSA9PiB7XG4gICAgICAgICAgcmV0dXJuIGJyLnRyaW0oKSArICcgJyArIHN1ZmZpeDtcbiAgICAgICAgfSk7XG5cbiAgICAgIGJyYW5kcyA9IFsuLi5icmFuZHMsIC4uLmJyYW5kSW5dO1xuICAgIH1cbiAgfVxuXG4gIHJldHVybiBicmFuZHM7XG59O1xuXG5leHBvcnQgY29uc3QgdG9nZ2xlRHJvcGRvd24gPSAocGF5bG9hZCkgPT4gKHsgdHlwZTogVE9HR0xFX0RST1BET1dOLCBwYXlsb2FkIH0pO1xuZXhwb3J0IGNvbnN0IHVwZGF0ZUxTUHJvZHVjdHMgPSAoKSA9PiAoeyB0eXBlOiBVUERBVEVfTFNfUFJPRFVDVFMgfSk7XG5leHBvcnQgY29uc3QgdG9nZ2xlTWFpbkxvYWRlciA9IChsb2FkaW5nKSA9PiAoe1xuICB0eXBlOiBUT0dHTEVfTUFJTl9MT0FERVIsXG4gIHBheWxvYWQ6IHsgbG9hZGluZyB9LFxufSk7XG4iLCJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbmNvbnN0IFNwaW5uZXIgPSAoeyBzdHlsZSB9KSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzcGlubmVyXCIgc3R5bGU9e3N0eWxlfT5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmFyMVwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJhcjJcIiAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiYXIzXCIgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmFyNFwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJhcjVcIiAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiYXI2XCIgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmFyN1wiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJhcjhcIiAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiYXI5XCIgLz5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYmFyMTBcIiAvPlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJiYXIxMVwiIC8+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImJhcjEyXCIgLz5cbiAgICAgICAgPC9kaXY+XG4gICAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNwaW5uZXI7XG4iLCJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbmNvbnN0IEhpbnRNZXNzYWdlID0gKHtzaG93LCBtZXNzYWdlfSkgPT4ge1xuICAgIHJldHVybiBzaG93ID8gKFxuICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPHA+e21lc3NhZ2V9PC9wPlxuICAgICAgICA8L2Rpdj5cbiAgICApIDogbnVsbDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEhpbnRNZXNzYWdlOyIsImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IFByb2R1Y3RzQ291bnQgPSAoeyBjb3VudCB9KSA9PiB7XG4gIHJldHVybiBjb3VudCA+IDAgPyAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJwcm9kdWN0LWNvdW50XCI+XG4gICAgICA8c3Bhbj57Y291bnR9PC9zcGFuPlxuICAgICAgPHNwYW4+e2NvdW50ID4gMSA/ICdwcm9kdWN0cycgOiAncHJvZHVjdCd9PC9zcGFuPlxuICAgIDwvZGl2PlxuICApIDogbnVsbDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFByb2R1Y3RzQ291bnQ7XG4iLCJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlRGlzcGF0Y2gsIHVzZVNlbGVjdG9yIH0gZnJvbSAncmVhY3QtcmVkdXgnO1xuaW1wb3J0IHsgc2V0VGVybXNPbkxvY2FsU3RvcmFnZSB9IGZyb20gJy4uLy4uL3V0aWxzJztcbmltcG9ydCB7IHN1Z2dlc3Rpb25DbGljaywgdXBkYXRlTFNQcm9kdWN0cywgdG9nZ2xlTWFpbkxvYWRlciB9IGZyb20gJy4uLy4uL2FjdGlvbnMvaW5kZXgnO1xuaW1wb3J0IFNwaW5uZXIgZnJvbSAnLi4vLi4vdXRpbHMvU3Bpbm5lcic7XG5pbXBvcnQgSGludE1lc3NhZ2UgZnJvbSAnLi9oaW50TWVzc2FnZSc7XG5pbXBvcnQgUHJvZHVjdHNDb3VudCBmcm9tICcuL3Byb2R1Y3RzQ291bnQnO1xuXG5jb25zdCBDYXRlZ29yaWVzID0gKHsgY2F0ZWdvcmllcywgaXNTZWFyY2hGdWxsVHlwZSB9KSA9PiB7XG4gIGNvbnN0IGRpc3BhdGNoID0gdXNlRGlzcGF0Y2goKTtcbiAgY29uc3QgeyBhY3RpdmVUZXJtLCBmZXRjaGluZzogaXNGZXRjaGluZywgcmVkaXJlY3RzIH0gPSB1c2VTZWxlY3Rvcigoc3RhdGUpID0+IHN0YXRlLnN1Z2dlc3Rpb25zKTtcbiAgY29uc3QgcmVkaXJlY3QgPSByZWRpcmVjdHMgJiYgcmVkaXJlY3RzW2FjdGl2ZVRlcm1dID8gcmVkaXJlY3RzW2FjdGl2ZVRlcm1dIDogbnVsbDtcbiAgY29uc3QgW3JlZGlyZWN0VmFsdWUsIHNldFJlZGlyZWN0VmFsdWVdID0gdXNlU3RhdGUoKTtcblxuICBjb25zdCBoYW5kbGVDbGljayA9IChlLCB0ZXJtKSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGxldCB1cmw7XG5cbiAgICBpZiAodGVybSkgdXJsID0gYC9jYXRhbG9nc2VhcmNoL3Jlc3VsdC8/cT0ke2VuY29kZVVSSUNvbXBvbmVudCh0ZXJtKX1gO1xuXG4gICAgZGlzcGF0Y2godG9nZ2xlTWFpbkxvYWRlcih0cnVlKSk7XG4gICAgZGlzcGF0Y2goc3VnZ2VzdGlvbkNsaWNrKHRlcm0pKTtcbiAgICBzZXRUZXJtc09uTG9jYWxTdG9yYWdlKHRlcm0pO1xuICAgIGRpc3BhdGNoKHVwZGF0ZUxTUHJvZHVjdHMoKSk7XG4gICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSB1cmw7XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocmVkaXJlY3QpIHNldFJlZGlyZWN0VmFsdWUocmVkaXJlY3QpO1xuICB9LCBbcmVkaXJlY3RdKTtcblxuICBjb25zdCByZW5kZXJDYXRlZ29yaWVzID0gKGNhdGVnb3JpZXMpID0+IHtcbiAgICByZXR1cm4gY2F0ZWdvcmllcy5tYXAoKGNhdCwgaWR4KSA9PiB7XG4gICAgICBjb25zdCBpc0FjdGl2ZSA9IGFjdGl2ZVRlcm0gPyBhY3RpdmVUZXJtPy50b0xvd2VyQ2FzZSgpID09IGNhdD8udGVybT8udG9Mb3dlckNhc2UoKSA6IGZhbHNlO1xuICAgICAgcmV0dXJuIChcbiAgICAgICAgPGxpIGtleT17aWR4fSBjbGFzc05hbWU9e2BjYXRlZ29yaWVzLWxpc3QtaXRlbWB9PlxuICAgICAgICAgIDxhXG4gICAgICAgICAgICBjbGFzc05hbWU9e2BjYXRlZ29yeS1jb250YWluZXIgJHtpc0FjdGl2ZSA/ICdhY3RpdmUnIDogJ2luYWN0aXZlJ31gfVxuICAgICAgICAgICAgb25DbGljaz17KGUpID0+IGhhbmRsZUNsaWNrKGUsIGNhdD8udGVybSl9XG4gICAgICAgICAgPlxuICAgICAgICAgICAge2lzU2VhcmNoRnVsbFR5cGUgPyAoXG4gICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgPHA+e2NhdC50ZXJtfTwvcD5cbiAgICAgICAgICAgICAgICA8UHJvZHVjdHNDb3VudCBjb3VudD17Y2F0Py5wcm9kdWN0X2NvdW50fSAvPlxuICAgICAgICAgICAgICAgIHtjYXQgPT09IGFjdGl2ZVRlcm0gJiYgaXNGZXRjaGluZyAmJiA8U3Bpbm5lciBzdHlsZT17eyBtYXJnaW5MZWZ0OiAnMTBweCcgfX0gLz59XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKSA6IChcbiAgICAgICAgICAgICAgY2F0LnRlcm1cbiAgICAgICAgICAgICl9XG4gICAgICAgICAgPC9hPlxuICAgICAgICA8L2xpPlxuICAgICAgKTtcbiAgICB9KTtcbiAgfTtcblxuICByZXR1cm4gKFxuICAgIDw+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlYXJjaC1hdXRvY29tcGxldGUtY2F0ZWdvcmllcyBzdWdnZXN0ZWQta2V5d29yZHNcIj5cbiAgICAgICAge2lzU2VhcmNoRnVsbFR5cGUgPyAoXG4gICAgICAgICAgPD5cbiAgICAgICAgICAgIDxIaW50TWVzc2FnZSBzaG93PXt0cnVlfSBtZXNzYWdlPVwiQ29udGludWUgdHlwaW5nIHRvIHJlZmluZSBzZWFyY2ggc3VnZ2VzdGlvbnMgJiByZXN1bHRzLi4uXCIgLz5cbiAgICAgICAgICAgIDxoNT5zdWdnZXN0ZWQga2V5d29yZHM8L2g1PlxuICAgICAgICAgIDwvPlxuICAgICAgICApIDogbnVsbH1cbiAgICAgICAge2NhdGVnb3JpZXMgJiYgY2F0ZWdvcmllcy5sZW5ndGggPyA8dWwgY2xhc3NOYW1lPVwiY2F0ZWdvcmllcy1saXN0XCI+e3JlbmRlckNhdGVnb3JpZXMoY2F0ZWdvcmllcyl9PC91bD4gOiBudWxsfVxuICAgICAgPC9kaXY+XG4gICAgPC8+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBDYXRlZ29yaWVzO1xuIiwiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuaW1wb3J0IHsgSEFNTUVSX1NQSU5ORVJfQkFTRTY0IH0gZnJvbSBcIi4vY29uc3RhbnRzXCI7XG5cbmNvbnN0IEhhbW1lclNwaW5uZXIgPSAoeyBzaG93LCBtZXNzYWdlIH0pID0+XG4gICAgc2hvdyA/IChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbnN0YW50LXNlYXJjaC1sb2FkZXJcIj5cbiAgICAgICAgICAgIDxpbWcgc3JjPXtIQU1NRVJfU1BJTk5FUl9CQVNFNjR9IGFsdD1cImxvYWRpbmdcIiAvPlxuICAgICAgICAgICAge21lc3NhZ2UgJiYgPHNwYW4+e21lc3NhZ2V9PC9zcGFuPn1cbiAgICAgICAgPC9kaXY+XG4gICAgKSA6IG51bGw7XG5cbmV4cG9ydCBkZWZhdWx0IEhhbW1lclNwaW5uZXI7XG4iLCJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5pbXBvcnQgeyBIQU1NRVJfU1BJTk5FUl9CQVNFNjQgfSBmcm9tIFwiLi4vdXRpbHMvY29uc3RhbnRzXCI7XG5pbXBvcnQgeyB1c2VTZWxlY3RvciB9IGZyb20gXCJyZWFjdC1yZWR1eFwiO1xuZXhwb3J0IGNvbnN0IE1haW5Mb2FkZXIgPSAoe3Nob3dNYWluTG9hZGVyTWFza30pID0+IHtcbiAgICBjb25zdCBzaG93TWFpbkxvYWRlciA9IHVzZVNlbGVjdG9yKChzdGF0ZSkgPT4gc3RhdGUuc2hvd01haW5Mb2FkZXIpO1xuICAgIHJldHVybiBzaG93TWFpbkxvYWRlciB8fCBzaG93TWFpbkxvYWRlck1hc2sgPyAoXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwibG9hZGluZy1tYXNrXCIgZGF0YS1yb2xlPVwibG9hZGVyXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImxvYWRlclwiPlxuICAgICAgICAgICAgICAgIDxpbWcgYWx0PVwiTG9hZGluZy4uLlwiIHNyYz17SEFNTUVSX1NQSU5ORVJfQkFTRTY0fSAvPlxuICAgICAgICAgICAgICAgIDxwPlBsZWFzZSB3YWl0Li4uPC9wPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICkgOiBudWxsO1xufTtcbiIsImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IEVycm9yID0gKHsgZXJyb3IsIGVycm9yTXNnLCBxdWVyeSB9KSA9PiB7XG4gIGNvbnN0IGZpbmFsRXJyb3JNZXNzYWdlID0gZXJyb3JNc2cgJiYgZXJyb3JNc2cucmVwbGFjZSgne3slcX19JywgcXVlcnkpO1xuXG4gIHJldHVybiBlcnJvciAmJiBxdWVyeSA/IChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInNlYXJjaC1hdXRvY29tcGxldGUtZXJyb3JcIj5cbiAgICAgIDxwPlxuICAgICAgICA8c3Ryb25nPntmaW5hbEVycm9yTWVzc2FnZX08L3N0cm9uZz5cbiAgICAgIDwvcD5cbiAgICA8L2Rpdj5cbiAgKSA6IG51bGw7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBFcnJvcjtcbiIsImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IFByaWNlID0gKHsgZmluYWxQcmljZSwgY3VycmVuY3ksIGZvcm1hdCB9KSA9PiB7XG4gIGNvbnN0IHByaWNlID0gZm9ybWF0KGZpbmFsUHJpY2UsIGZhbHNlKTtcblxuICByZXR1cm4gZmluYWxQcmljZSAhPT0gJ3VuZGVmaW5lZCcgPyAoXG4gICAgPHNwYW4gY2xhc3NOYW1lPVwicHJvZHVjdC1wcmljZVwiPlxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwiY3VycmVuY3ktc3ltYm9sXCI+e2N1cnJlbmN5fTwvc3Bhbj5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInByaWNlLW1haW5cIj5cbiAgICAgICAge0FycmF5LmlzQXJyYXkocHJpY2UpID8gKFxuICAgICAgICAgIDw+XG4gICAgICAgICAgICB7cHJpY2U/LlswXX1cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzPVwiZGVjaW1hbC1kb3RcIj4uPC9zcGFuPlxuICAgICAgICAgICAgPHNwYW4gY2xhc3M9XCJwcmljZS1kZWNpbWFsXCI+e3ByaWNlPy5bMV19PC9zcGFuPlxuICAgICAgICAgIDwvPlxuICAgICAgICApIDogKFxuICAgICAgICAgIHByaWNlXG4gICAgICAgICl9XG4gICAgICA8L3NwYW4+XG4gICAgPC9zcGFuPlxuICApIDogbnVsbDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFByaWNlO1xuIiwiaW1wb3J0IFJlYWN0IGZyb20gJ3JlYWN0JztcblxuY29uc3QgY2FsY3VsYXRlUGVyY2VudGFnZVNhdmVkID0gKG9yaWdpbmFsUHJpY2UsIGZpbmFsUHJpY2UpID0+IHtcbiAgY29uc3QgZGlmZmVyZW5jZSA9IG9yaWdpbmFsUHJpY2UgLSBmaW5hbFByaWNlO1xuICBjb25zdCBwZXJjZW50YWdlU2F2ZWQgPSAoZGlmZmVyZW5jZSAvIG9yaWdpbmFsUHJpY2UpICogMTAwO1xuICBjb25zdCBmbG9vcmVkUGVyY2VudGFnZSA9IE1hdGguZmxvb3IocGVyY2VudGFnZVNhdmVkKTtcbiAgcmV0dXJuIGZsb29yZWRQZXJjZW50YWdlO1xufTtcblxuY29uc3QgU2F2aW5ncyA9IChwcm9wcykgPT4ge1xuICBjb25zdCByZW5kZXJMYWJlbCA9ICgpID0+IHtcbiAgICBjb25zdCB7IHNhdmluZ3MsIGN1cnJlbmN5LCBmb3JtYXQsIHR5cGUsIG9yaWdpbmFsUHJpY2UsIGZpbmFsUHJpY2UgfSA9IHByb3BzO1xuICAgIGNvbnN0IHNhdmluZ1ByaWNlID0gZm9ybWF0KHNhdmluZ3MpO1xuICAgIHJldHVybiAhdHlwZS5tYXRjaCgvcGVyY2VudGFnZS9naSkgPyAoXG4gICAgICA8PlxuICAgICAgICBTYXZlIDxzcGFuIGNsYXNzTmFtZT1cImN1cnJlbmN5LXN5bWJvbFwiPntjdXJyZW5jeX08L3NwYW4+XG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInNhdmUtcHJpY2Ugd3JhcFwiPlxuICAgICAgICAgIHtBcnJheS5pc0FycmF5KHNhdmluZ1ByaWNlKSA/IChcbiAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgIHtzYXZpbmdQcmljZT8uWzBdfVxuICAgICAgICAgICAgICA8c3BhbiBjbGFzcz1cImRlY2ltYWwtZG90XCI+Ljwvc3Bhbj5cbiAgICAgICAgICAgICAgPHNwYW4gY2xhc3M9XCJ5b3Utc2F2ZS1kZWNpbWFsXCI+e3NhdmluZ1ByaWNlPy5bMV19PC9zcGFuPlxuICAgICAgICAgICAgPC8+XG4gICAgICAgICAgKSA6IChcbiAgICAgICAgICAgIHNhdmluZ1ByaWNlXG4gICAgICAgICAgKX1cbiAgICAgICAgPC9zcGFuPlxuICAgICAgPC8+XG4gICAgKSA6IChcbiAgICAgIDw+XG4gICAgICAgIFNhdmUgeycnfVxuICAgICAgICB7Y2FsY3VsYXRlUGVyY2VudGFnZVNhdmVkKG9yaWdpbmFsUHJpY2UsIGZpbmFsUHJpY2UpfVxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJwcmljZS1kZWNpbWFsXCI+JTwvc3Bhbj5cbiAgICAgIDwvPlxuICAgICk7XG4gIH07XG5cbiAgY29uc3QgeyBzYXZpbmdzLCB0eXBlIH0gPSBwcm9wcztcblxuICBpZiAodHlwZS5tYXRjaCgvKG5vdHxuJ3QpL2dpKSB8fCBzYXZpbmdzIDw9IDAgfHwgdHlwZS5tYXRjaCgvZG8gbm90L2dpKSkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8c3BhbiBjbGFzc05hbWU9XCJ5b3Utc2F2ZS1zdGF0ZW1lbnRcIj5cbiAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIndyYXBcIj57cmVuZGVyTGFiZWwocHJvcHMpfTwvc3Bhbj5cbiAgICA8L3NwYW4+XG4gICk7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBTYXZpbmdzO1xuIiwiaW1wb3J0IFJlYWN0IGZyb20gXCJyZWFjdFwiO1xuXG5jb25zdCBTaGlwcGluZ0xhYmVsID0gKHsgbGFiZWwgfSkgPT4ge1xuICByZXR1cm4gbGFiZWwgJiYgbGFiZWwuc2VhcmNoKC9mcmVlL2dpKSAhPT0gLTEgPyAoXG4gICAgPHNwYW4gY2xhc3NOYW1lPVwic2hpcHBpbmctbGFiZWxcIj5GcmVlPC9zcGFuPlxuICApIDogbnVsbDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNoaXBwaW5nTGFiZWw7XG4iLCJpbXBvcnQgUmVhY3QsIHsgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgZ2V0QW1hc3R5TGFiZWxTdHlsZXMgfSBmcm9tICcuLi8uLi8uLi91dGlscyc7XG5jb25zdCBQcm9kdXRJbWFnZSA9ICh7IGRhdGEsIHBsYWNlaG9sZGVyIH0pID0+IHtcbiAgY29uc3QgZ2V0SW1nVXJsID0gKGRhdGEpID0+IHtcbiAgICBjb25zdCBhdHRyT3JkZXIgPSBbJ2xhYmVsZWRJbWFnZScsICd0aHVtYm5haWwnLCAnc21hbGxJbWFnZSddO1xuICAgIGNvbnN0IGV4dHNSZWdleCA9IC9cXC4oZ2lmfGpwZT9nfHRpZmY/fHBuZ3x3ZWJwfGJtcCkkL2k7XG4gICAgbGV0IGltZ1VybDtcblxuICAgIGF0dHJPcmRlci5mb3JFYWNoKChhdHRyKSA9PiB7XG4gICAgICBpZiAoIWltZ1VybCAmJiBkYXRhW2F0dHJdICYmIGV4dHNSZWdleC50ZXN0KGRhdGFbYXR0cl0pKSB7XG4gICAgICAgIGltZ1VybCA9IGRhdGFbYXR0cl07XG4gICAgICAgIHJldHVybiBpbWdVcmw7XG4gICAgICB9XG4gICAgfSk7XG5cbiAgICBpZiAoIWltZ1VybCAmJiBkYXRhLmltYWdlVXJsICYmIGRhdGEuaW1hZ2VVcmwubGVuZ3RoKSB7XG4gICAgICBpbWdVcmwgPSBkYXRhLmltYWdlVXJsWzBdO1xuICAgIH1cblxuICAgIHJldHVybiBpbWdVcmw7XG4gIH07XG5cbiAgY29uc3QgeyB1bmJ4ZEFtYXN0eUxhYmVsVG9wUmlnaHQsIHVuYnhkQW1hc3R5TGFiZWxUb3BMZWZ0LCB1bmJ4ZEFtYXN0eUxhYmVsQm90dG9tUmlnaHQsIHVuYnhkQW1hc3R5TGFiZWxCb3R0b21MZWZ0IH0gPVxuICAgIGRhdGE7XG4gIGNvbnN0IGFtYXN0eUxhYmVscyA9IFtcbiAgICB7IGxhYmVsOiB1bmJ4ZEFtYXN0eUxhYmVsVG9wTGVmdCwgcG9zaXRpb246ICcwJyB9LFxuICAgIHsgbGFiZWw6IHVuYnhkQW1hc3R5TGFiZWxUb3BSaWdodCwgcG9zaXRpb246ICcyJyB9LFxuICAgIHsgbGFiZWw6IHVuYnhkQW1hc3R5TGFiZWxCb3R0b21MZWZ0LCBwb3NpdGlvbjogJzYnIH0sXG4gICAgeyBsYWJlbDogdW5ieGRBbWFzdHlMYWJlbEJvdHRvbVJpZ2h0LCBwb3NpdGlvbjogJzgnIH0sXG4gIF07XG5cbiAgY29uc3QgaW1nUmVmID0gdXNlUmVmKG51bGwpO1xuICBjb25zdCBpbWdVcmwgPSBnZXRJbWdVcmwoZGF0YSk7XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInRodW1iXCI+XG4gICAgICA8aW1nXG4gICAgICAgIHNyYz17aW1nVXJsfVxuICAgICAgICByZWY9e2ltZ1JlZn1cbiAgICAgICAgb25FcnJvcj17KCkgPT4ge1xuICAgICAgICAgIGlmIChpbWdSZWYuY3VycmVudCAmJiBpbWdSZWYuY3VycmVudC5zcmMgIT09IHBsYWNlaG9sZGVyKSB7XG4gICAgICAgICAgICBpbWdSZWYuY3VycmVudC5zcmMgPSBwbGFjZWhvbGRlcjtcbiAgICAgICAgICB9XG4gICAgICAgIH19XG4gICAgICAvPlxuICAgICAge2FtYXN0eUxhYmVscy5tYXAoKHsgbGFiZWwsIHBvc2l0aW9uIH0pID0+IHtcbiAgICAgICAgaWYgKCFsYWJlbCkgcmV0dXJuIG51bGw7XG4gICAgICAgIGNvbnN0IGFtYXN0eUxhYmVsU3R5bGVzID0gZ2V0QW1hc3R5TGFiZWxTdHlsZXMocG9zaXRpb24pO1xuICAgICAgICByZXR1cm4gKFxuICAgICAgICAgIDxzcGFuXG4gICAgICAgICAgICBrZXk9e2xhYmVsfVxuICAgICAgICAgICAgY2xhc3NOYW1lPVwiYW1hc3R5X2xhYmVsX2ltYWdlXCJcbiAgICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICAgIC4uLmFtYXN0eUxhYmVsU3R5bGVzLFxuICAgICAgICAgICAgICBiYWNrZ3JvdW5kSW1hZ2U6IGB1cmwoJHtsYWJlbH0pYCxcbiAgICAgICAgICAgIH19XG4gICAgICAgICAgLz5cbiAgICAgICAgKTtcbiAgICAgIH0pfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUHJvZHV0SW1hZ2U7XG4iLCJpbXBvcnQgUmVhY3QgZnJvbSBcInJlYWN0XCI7XG5cbmNvbnN0IFByb2R1Y3RMYWJlbHMgPSAoeyB0b3AsIGJvdHRvbSwgdGVtcGxhdGUgfSkgPT4ge1xuICBjb25zdCBnZXRMYWJlbEltZyA9IChsYWJlbCkgPT4ge1xuICAgIGNvbnN0IHVybCA9IHRlbXBsYXRlXG4gICAgICAucmVwbGFjZShcInt7bGFiZWxfdHlwZX19XCIsIGxhYmVsKVxuICAgICAgLnJlcGxhY2UoXCJ7e2ltZ190eXBlfX1cIiwgXCJ0aHVtYm5haWxcIik7XG5cbiAgICByZXR1cm4gPGltZyBzcmM9e3VybH0gYWx0PVwiUHJvZHVjdCBMYWJlbFwiIC8+O1xuICB9O1xuXG4gIHJldHVybiB0b3AgfHwgYm90dG9tID8gKFxuICAgIDxzcGFuIGNsYXNzTmFtZT1cInByb2R1Y3QtbGFiZWxzXCI+XG4gICAgICB7dG9wID8gKFxuICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJwcm9kdWN0LWxhYmVsIGxhYmVsX3RvcFwiPntnZXRMYWJlbEltZyh0b3ApfTwvc3Bhbj5cbiAgICAgICkgOiBudWxsfVxuICAgICAge2JvdHRvbSA/IChcbiAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHJvZHVjdC1sYWJlbCBsYWJlbF9ib3R0b21cIj5cbiAgICAgICAgICB7Z2V0TGFiZWxJbWcoYm90dG9tKX1cbiAgICAgICAgPC9zcGFuPlxuICAgICAgKSA6IG51bGx9XG4gICAgPC9zcGFuPlxuICApIDogbnVsbDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFByb2R1Y3RMYWJlbHM7XG4iLCJpbXBvcnQgUmVhY3QgZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU2VsZWN0b3IsIHVzZURpc3BhdGNoIH0gZnJvbSAncmVhY3QtcmVkdXgnO1xuXG5pbXBvcnQgUHJpY2UgZnJvbSAnLi9wcm9kdWN0LnByaWNlJztcbmltcG9ydCBTYXZpbmdzIGZyb20gJy4vcHJvZHVjdC5zYXZpbmdzJztcbmltcG9ydCBTaGlwcGluZ0xhYmVsIGZyb20gJy4vcHJvZHVjdC5zaGlwcGluZy5sYWJlbCc7XG5pbXBvcnQgUHJvZHV0SW1hZ2UgZnJvbSAnLi9wcm9kdWN0LmltZyc7XG5pbXBvcnQgUHJvZHVjdExhYmVscyBmcm9tICcuL3Byb2R1Y3QubGFiZWxzJztcbmltcG9ydCB7IGdldFByb2R1Y3RQcmljZUF0dHJpYnV0ZXMsIGZvcm1hdFByaWNlIH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMvY29tbW9uJztcbmltcG9ydCB7IHRvZ2dsZU1haW5Mb2FkZXIgfSBmcm9tICcuLi8uLi8uLi9hY3Rpb25zJztcblxuY29uc3QgUHJvZHVjdCA9ICh7IGRhdGEsIHBvc2l0aW9uIH0pID0+IHtcbiAgY29uc3QgY29uZmlnID0gdXNlU2VsZWN0b3IoKHN0YXRlKSA9PiBzdGF0ZS5jb25maWcpO1xuICBjb25zdCBxdWVyeSA9IHVzZVNlbGVjdG9yKChzdGF0ZSkgPT4gc3RhdGUuc3VnZ2VzdGlvbnMuYWN0aXZlVGVybSB8fCBzdGF0ZS5xdWVyeSk7XG4gIGNvbnN0IHsgZmluYWxQcmljZSwgc3RvcmVTcGVjaWFsUHJpY2UsIHN0b3JlT3JpZ2luYWxQcmljZSB9ID0gZ2V0UHJvZHVjdFByaWNlQXR0cmlidXRlcyhkYXRhKTtcbiAgY29uc3QgZGlzcGF0Y2ggPSB1c2VEaXNwYXRjaCgpO1xuXG4gIGNvbnN0IGhhbmRsZVRyYWNraW5nID0gKHVybCwgZXYpID0+IHtcbiAgICBldi5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGRpc3BhdGNoKHRvZ2dsZU1haW5Mb2FkZXIodHJ1ZSkpO1xuICAgIGlmICh0eXBlb2Ygd2luZG93LkFFQyA9PT0gJ29iamVjdCcgJiYgd2luZG93LkFFQy5ndG0oKSkge1xuICAgICAgd2luZG93LmRhdGFMYXllciA9IHdpbmRvdy5kYXRhTGF5ZXIgfHwgW107XG4gICAgICB3aW5kb3cuQUVDLmNsaWNrKGV2LmN1cnJlbnRUYXJnZXQsIHdpbmRvdy5kYXRhTGF5ZXIpO1xuICAgICAgd2luZG93LmRhdGFMYXllci5wdXNoKHtcbiAgICAgICAgZXZlbnQ6ICd1bmJ4ZFNlYXJjaFF1ZXJ5JyxcbiAgICAgICAgc2VhcmNoUXVlcnlQYXlsb2FkOiB7XG4gICAgICAgICAgcXVlcnk6IHF1ZXJ5LFxuICAgICAgICB9LFxuICAgICAgfSk7XG4gICAgfVxuICAgIHdpbmRvdy5sb2NhdGlvbiA9IHVybDtcbiAgfTtcblxuICBjb25zdCBwbGFjZWhvbGRlclVybCA9IGNvbmZpZy5wbGFjZWhvbGRlckltZ1VybCB8fCAnJztcbiAgY29uc3Qgb3JpZ2luYWxQcmljZSA9XG4gICAgZGF0YS50eXBlSWQgPT09ICdidW5kbGUnICYmIHN0b3JlU3BlY2lhbFByaWNlXG4gICAgICA/IGZpbmFsUHJpY2UgLyAoMSAtICgxMDAgLSBzdG9yZVNwZWNpYWxQcmljZSkgLyAxMDApXG4gICAgICA6IHN0b3JlT3JpZ2luYWxQcmljZTtcbiAgY29uc3QgdHlwZUlkID0gZGF0YS5za3UuZW5kc1dpdGgoJ3YnKSA/ICdjb25maWd1cmFibGUnIDogZGF0YS50eXBlSWQ7XG5cbiAgbGV0IGlzQjJCQ3VzdG9tZXIgPSBmYWxzZTtcbiAgY29uc3QgbWFnZUNhY2hlU3RvcmFnZSA9IGxvY2FsU3RvcmFnZVsnbWFnZS1jYWNoZS1zdG9yYWdlJ107XG4gIGNvbnN0IGlzRm9yU2FsZSA9IGRhdGE/Lm5vdEZvclNhbGU/LnRvTG93ZXJDYXNlKCkgIT09ICd0cnVlJztcbiAgY29uc3Qgc2F2aW5ncyA9IG9yaWdpbmFsUHJpY2UgJiYgb3JpZ2luYWxQcmljZSAhPT0gZmluYWxQcmljZSA/IG9yaWdpbmFsUHJpY2UgLSBmaW5hbFByaWNlIDogZmFsc2U7XG4gIGNvbnN0IHNob3dQcmljZSA9IHR5cGVvZiBkYXRhLnByaWNlICE9PSAndW5kZWZpbmVkJyAmJiAhaXNCMkJDdXN0b21lciAmJiBkYXRhPy5ub3RGb3JTYWxlPy50b0xvd2VyQ2FzZSgpICE9PSAndHJ1ZSc7XG4gIGNvbnN0IHNob3dTYXZpbmcgPVxuICAgIHR5cGVJZCAhPT0gJ2NvbmZpZ3VyYWJsZScgJiZcbiAgICBzYXZpbmdzICYmXG4gICAgZGF0YS5zaG93U2F2ZWRMYWJlbCAmJlxuICAgICFpc0IyQkN1c3RvbWVyICYmXG4gICAgZGF0YT8ubm90Rm9yU2FsZT8udG9Mb3dlckNhc2UoKSAhPT0gJ3RydWUnO1xuICBpZiAodHlwZW9mIG1hZ2VDYWNoZVN0b3JhZ2UgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgY29uc3QgbWFnZUNhY2hlT2JqID0gSlNPTi5wYXJzZShtYWdlQ2FjaGVTdG9yYWdlKTtcbiAgICBpc0IyQkN1c3RvbWVyID0gbWFnZUNhY2hlT2JqLmNvbXBhbnkgJiYgbWFnZUNhY2hlT2JqLmNvbXBhbnkuaGFzX2N1c3RvbWVyX2NvbXBhbnk7XG4gIH1cblxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic2VhcmNoLWF1dG9jb21wbGV0ZS1wcm9kdWN0XCIgcm9sZT1cIm9wdGlvbnNcIiBpZD17J29wdGlvbi0nICsgZGF0YS5za3V9PlxuICAgICAgPGFcbiAgICAgICAgY2xhc3NOYW1lPVwic2VhcmNoLWF1dG9jb21wbGV0ZS1oaXRcIlxuICAgICAgICBocmVmPXtkYXRhLnByb2R1Y3RVcmx9XG4gICAgICAgIGRhdGEtaWQ9e2RhdGEuc2t1fVxuICAgICAgICBkYXRhLW5hbWU9e2RhdGEudGl0bGV9XG4gICAgICAgIGRhdGEtcHJpY2U9e2RhdGEucHJpY2V9XG4gICAgICAgIGRhdGEtcG9zaXRpb249e3Bvc2l0aW9ufVxuICAgICAgICBkYXRhLWJyYW5kPXtkYXRhLmJyYW5kICYmIGRhdGEuYnJhbmQubGVuZ3RoID8gZGF0YS5icmFuZFswXSA6ICcnfVxuICAgICAgICBkYXRhLWNhdGVnb3J5PXsnU2VhcmNoIEF1dG9zdWdnZXN0J31cbiAgICAgICAgZGF0YS1saXN0PXsnU2VhcmNoIEF1dG9zdWdnZXN0J31cbiAgICAgICAgZGF0YS1ldmVudD17J3Byb2R1Y3RDbGljayd9XG4gICAgICAgIGRhdGEtc3RvcmU9e2NvbmZpZy5zdG9yZU5hbWUgfHwgJyd9XG4gICAgICAgIGRhdGEtYXR0cmlidXRlcz17J1tdJ31cbiAgICAgICAgb25DbGljaz17KGV2KSA9PiBoYW5kbGVUcmFja2luZyhkYXRhLnByb2R1Y3RVcmwsIGV2KX1cbiAgICAgID5cbiAgICAgICAgPFByb2R1dEltYWdlIGRhdGE9e2RhdGF9IHBsYWNlaG9sZGVyPXtwbGFjZWhvbGRlclVybH0gLz5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmZvXCI+XG4gICAgICAgICAgPGRpdiByb2xlPVwidGl0bGVcIiBjbGFzc05hbWU9XCJwcm9kdWN0LXRpdGxlXCI+XG4gICAgICAgICAgICB7ZGF0YS50aXRsZX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1wblwiPk1QTjoge2RhdGEucGFydE5vfTwvZGl2PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJpY2UtYm94XCI+XG4gICAgICAgICAgICB7c2hvd1ByaWNlICYmIGlzRm9yU2FsZSA/IChcbiAgICAgICAgICAgICAgPFByaWNlIGN1cnJlbmN5PXtjb25maWcuc3RvcmVDdXJyZW5jeVN5bWJvbCB8fCAnJCd9IGZpbmFsUHJpY2U9e2ZpbmFsUHJpY2V9IGZvcm1hdD17Zm9ybWF0UHJpY2V9IC8+XG4gICAgICAgICAgICApIDogbnVsbH1cbiAgICAgICAgICAgIHtzaG93U2F2aW5nICYmIGlzRm9yU2FsZSA/IChcbiAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICA8U2F2aW5nc1xuICAgICAgICAgICAgICAgICAgY3VycmVuY3k9e2NvbmZpZy5zdG9yZUN1cnJlbmN5U3ltYm9sIHx8ICckJ31cbiAgICAgICAgICAgICAgICAgIHNhdmluZ3M9e3NhdmluZ3N9XG4gICAgICAgICAgICAgICAgICBmaW5hbFByaWNlPXtmaW5hbFByaWNlfVxuICAgICAgICAgICAgICAgICAgb3JpZ2luYWxQcmljZT17b3JpZ2luYWxQcmljZX1cbiAgICAgICAgICAgICAgICAgIGZvcm1hdD17Zm9ybWF0UHJpY2V9XG4gICAgICAgICAgICAgICAgICB0eXBlPXtkYXRhLnNob3dTYXZlZExhYmVsfVxuICAgICAgICAgICAgICAgIC8+XG4gICAgICAgICAgICAgIDwvPlxuICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9kdWN0LWl0ZW0tYWN0aW9uc1wiPlxuICAgICAgICAgICAgPGJ1dHRvblxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJhY3Rpb24gdG9kZXRhaWxzIHByaW1hcnlcIlxuICAgICAgICAgICAgICBkYXRhLWlkPXtkYXRhLnNrdX1cbiAgICAgICAgICAgICAgZGF0YS1uYW1lPXtkYXRhLnRpdGxlfVxuICAgICAgICAgICAgICBkYXRhLXByaWNlPXtkYXRhLnByaWNlfVxuICAgICAgICAgICAgICBkYXRhLXF1YW50aXR5PXsxfVxuICAgICAgICAgICAgICBkYXRhLXBvc2l0aW9uPXtwb3NpdGlvbn1cbiAgICAgICAgICAgICAgZGF0YS1icmFuZD17ZGF0YS5icmFuZCAmJiBkYXRhLmJyYW5kLmxlbmd0aCA/IGRhdGEuYnJhbmRbMF0gOiAnJ31cbiAgICAgICAgICAgICAgZGF0YS1jYXRlZ29yeT17J1NlYXJjaCBSZXN1bHRzJ31cbiAgICAgICAgICAgICAgZGF0YS1saXN0PXsnU2VhcmNoIFJlc3VsdHMnfVxuICAgICAgICAgICAgICBkYXRhLWV2ZW50PXsncHJvZHVjdENsaWNrJ31cbiAgICAgICAgICAgICAgZGF0YS1zdG9yZT17Y29uZmlnLnN0b3JlTmFtZSB8fCAnJ31cbiAgICAgICAgICAgICAgZGF0YS1hdHRyaWJ1dGVzPXsnW10nfVxuICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4gaGFuZGxlVHJhY2tpbmcoZGF0YS5wcm9kdWN0VXJsLCBldil9XG4gICAgICAgICAgICA+XG4gICAgICAgICAgICAgIDxzcGFuPlZpZXcgRGV0YWlsczwvc3Bhbj5cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsYWJlbHMtd3JhcHBlclwiPlxuICAgICAgICAgICAgPFNoaXBwaW5nTGFiZWwgbGFiZWw9e2RhdGEuc2hpcHBpbmdMYWJlbH0gLz5cbiAgICAgICAgICAgIHtjb25maWcuc2hvd1Byb2R1Y3RMYWJlbHMgPyAoXG4gICAgICAgICAgICAgIDxQcm9kdWN0TGFiZWxzIHRvcD17ZGF0YS5sYWJlbFRvcH0gYm90dG9tPXtkYXRhLmxhYmVsQm90dG9tfSB0ZW1wbGF0ZT17Y29uZmlnLmxhYmVsc1VybFRlbXBsYXRlfSAvPlxuICAgICAgICAgICAgKSA6IG51bGx9XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9hPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUHJvZHVjdDtcbiIsImltcG9ydCBSZWFjdCBmcm9tIFwicmVhY3RcIjtcbmltcG9ydCB7IHNldFRlcm1zT25Mb2NhbFN0b3JhZ2UgfSBmcm9tIFwiLi4vLi4vdXRpbHNcIjtcbmltcG9ydCB7IHVzZURpc3BhdGNoIH0gZnJvbSBcInJlYWN0LXJlZHV4XCI7XG5pbXBvcnQgeyB0b2dnbGVNYWluTG9hZGVyIH0gZnJvbSBcIi4uLy4uL2FjdGlvbnNcIjtcbmltcG9ydCBQcm9kdWN0IGZyb20gXCIuL3Byb2R1Y3QvcHJvZHVjdFwiO1xuXG5jb25zdCBQcm9kdWN0cyA9ICh7IHByb2R1Y3RzLCBhY3RpdmVUZXJtLCByZWRpcmVjdCB9KSA9PiB7XG4gICAgY29uc3QgZm9ybVJlZiA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKFwic2VhcmNoX21pbmlfZm9ybVwiKTtcbiAgICBjb25zdCBkaXNwYXRjaCA9IHVzZURpc3BhdGNoKCk7XG5cbiAgICBjb25zdCByZW5kZXJQcm9kdWN0cyA9IChwcm9kcykgPT4ge1xuICAgICAgICByZXR1cm4gcHJvZHMubWFwKChwcm9kLCBpZHgpID0+IHtcbiAgICAgICAgICAgIHJldHVybiA8UHJvZHVjdCBrZXk9e2lkeH0gcG9zaXRpb249e2lkeCArIDF9IGRhdGE9e3Byb2R9IC8+O1xuICAgICAgICB9KTtcbiAgICB9O1xuXG4gICAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoKSA9PiB7XG4gICAgICAgIGxldCB1cmwgPSBgL2NhdGFsb2dzZWFyY2gvcmVzdWx0Lz9xPSR7ZW5jb2RlVVJJQ29tcG9uZW50KGFjdGl2ZVRlcm0pfWA7XG5cbiAgICAgICAgaWYgKHJlZGlyZWN0KSB7XG4gICAgICAgICAgICB1cmwgPSByZWRpcmVjdDtcbiAgICAgICAgfVxuICAgICAgICBkaXNwYXRjaCh0b2dnbGVNYWluTG9hZGVyKHRydWUpKTtcbiAgICAgICAgc2V0VGVybXNPbkxvY2FsU3RvcmFnZShhY3RpdmVUZXJtKTtcbiAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSB1cmw7XG4gICAgfTtcblxuICAgIHJldHVybiBwcm9kdWN0cyAmJiBwcm9kdWN0cy5sZW5ndGggPiAwID8gKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlYXJjaC1hdXRvY29tcGxldGUtcHJvZHVjdHNcIj5cbiAgICAgICAgICAgIDxoNT5zZWFyY2ggcmVzdWx0czwvaDU+XG4gICAgICAgICAgICB7YWN0aXZlVGVybSAmJiAoXG4gICAgICAgICAgICAgICAgPGRpdlxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9e2BzZWFyY2gtYXV0b2NvbXBsZXRlLW1vcmUke1xuICAgICAgICAgICAgICAgICAgICAgICAgcHJvZHVjdHMubGVuZ3RoIDwgNSA/IFwiIGhpZGRlblwiIDogXCJcIlxuICAgICAgICAgICAgICAgICAgICB9YH1cbiAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxhXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJidXR0b24gYnV0dG9uLXByaW1hcnlfX291dGxpbmVcIlxuICAgICAgICAgICAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlQ2xpY2t9XG4gICAgICAgICAgICAgICAgICAgID5cbiAgICAgICAgICAgICAgICAgICAgICAgIFZpZXcgYWxsIHJlc3VsdHMgZm9yIDxzcGFuPntgXCIke2FjdGl2ZVRlcm19XCJgfTwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgKX1cbiAgICAgICAgICAgIDxkaXY+e3JlbmRlclByb2R1Y3RzKHByb2R1Y3RzKX08L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgKSA6IG51bGw7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBQcm9kdWN0cztcbiIsImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VTZWxlY3RvciwgdXNlRGlzcGF0Y2ggfSBmcm9tICdyZWFjdC1yZWR1eCc7XG5pbXBvcnQgeyBzZXRUZXJtc09uTG9jYWxTdG9yYWdlLCBjYXBpdGFsaXplIH0gZnJvbSAnLi4vLi4vLi4vdXRpbHMnO1xuaW1wb3J0IHsgdG9nZ2xlTWFpbkxvYWRlciB9IGZyb20gJy4uLy4uLy4uL2FjdGlvbnMnO1xuXG5jb25zdCBDb3JyZWN0aW9ucyA9ICgpID0+IHtcbiAgY29uc3QgeyB0ZXJtczogY29ycmVjdGlvbnMsIHJlZGlyZWN0cyB9ID0gdXNlU2VsZWN0b3IoKHN0YXRlKSA9PiBzdGF0ZS5jb3JyZWN0aW9ucyk7XG4gIGNvbnN0IGRpc3BhdGNoID0gdXNlRGlzcGF0Y2goKTtcbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAodGVybSwgZXYpID0+IHtcbiAgICBldi5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGNvbnN0IGNhcGl0YWxpemVUZXJtID0gY2FwaXRhbGl6ZSh0ZXJtKTtcblxuICAgIGNvbnN0IHJlZGlyZWN0ID0gcmVkaXJlY3RzICYmIHJlZGlyZWN0c1tjYXBpdGFsaXplVGVybV0gPyByZWRpcmVjdHNbY2FwaXRhbGl6ZVRlcm1dIDogbnVsbDtcbiAgICBsZXQgdXJsO1xuICAgIGlmIChyZWRpcmVjdCkge1xuICAgICAgdXJsID0gcmVkaXJlY3Q7XG4gICAgfSBlbHNlIHtcbiAgICAgIHVybCA9IGAvY2F0YWxvZ3NlYXJjaC9yZXN1bHQvP3E9JHtlbmNvZGVVUklDb21wb25lbnQodGVybSl9YDtcbiAgICB9XG4gICAgZGlzcGF0Y2godG9nZ2xlTWFpbkxvYWRlcih0cnVlKSk7XG4gICAgc2V0VGVybXNPbkxvY2FsU3RvcmFnZSh0ZXJtKTtcbiAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVybDtcbiAgfTtcblxuICByZXR1cm4gY29ycmVjdGlvbnMgJiYgY29ycmVjdGlvbnMubGVuZ3RoID8gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwic2VhcmNoLWF1dG9jb21wbGV0ZS1jb3JyZWN0aW9uc1wiPlxuICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidHRsXCI+QXJlIHlvdSBsb29raW5nIGZvcjo8L3NwYW4+XG4gICAgICB7Y29ycmVjdGlvbnM/Lm1hcCgoY29yciwgaWR4KSA9PiAoXG4gICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cIm9wdFwiIGtleT17aWR4fSBvbkNsaWNrPXsoZXYpID0+IGhhbmRsZUNsaWNrKGNvcnIsIGV2KX0+XG4gICAgICAgICAge2NhcGl0YWxpemUoY29ycil9XG4gICAgICAgIDwvc3Bhbj5cbiAgICAgICkpfVxuICAgIDwvZGl2PlxuICApIDogbnVsbDtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IENvcnJlY3Rpb25zO1xuIiwiaW1wb3J0IFJlYWN0LCB7IHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVNlbGVjdG9yLCB1c2VEaXNwYXRjaCB9IGZyb20gJ3JlYWN0LXJlZHV4JztcbmltcG9ydCBQcm9kdWN0cyBmcm9tICcuL3Byb2R1Y3RzJztcbmltcG9ydCBDb3JyZWN0aW9ucyBmcm9tICcuLi8uLi9jb21wb25lbnRzL2F1dG8tc3VnZ2VzdC9uby1yZXN1bHQvY29ycmVjdGlvbnMnO1xuaW1wb3J0IEhhbW1lclNwaW5uZXIgZnJvbSAnLi4vLi4vdXRpbHMvSGFtbWVyU3Bpbm5lcic7XG5pbXBvcnQgeyBmZXRjaENvcnJlY3Rpb25UZXJtUmVzdWx0IH0gZnJvbSAnLi4vLi4vYWN0aW9ucyc7XG5cbmNvbnN0IE5vUmVzdWx0ID0gKHsgc2hvdywgYWN0aXZlVGVybSB9KSA9PiB7XG4gIGNvbnN0IHtcbiAgICBpc1NlYXJjaGluZyxcbiAgICBjb3JyZWN0aW9uczogeyBwcm9kdWN0cywgdGVybXM6IGNvcnJlY3Rpb25zLCBpc0ZldGNoaW5nUHJvZHVjdHM6IGlzTG9hZGluZ1Byb2R1Y3RzIH0sXG4gIH0gPSB1c2VTZWxlY3Rvcigoc3RhdGUpID0+IHN0YXRlKTtcblxuICBjb25zdCBkaXNwYXRjaCA9IHVzZURpc3BhdGNoKCk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoY29ycmVjdGlvbnMgJiYgY29ycmVjdGlvbnNbMF0pIHtcbiAgICAgIGRpc3BhdGNoKGZldGNoQ29ycmVjdGlvblRlcm1SZXN1bHQoY29ycmVjdGlvbnNbMF0pKTtcbiAgICB9XG4gIH0sIFtjb3JyZWN0aW9ucz8ubGVuZ3RoLCBkaXNwYXRjaF0pO1xuXG4gIHJldHVybiBzaG93ICYmIGFjdGl2ZVRlcm0gPyAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJzZWFyY2gtYXV0b2NvbXBsZXRlLW5vLXJlc3VsdFwiPlxuICAgICAgPHA+XG4gICAgICAgIE5vIHJlc3VsdHMgZm91bmQgZm9yIDxzdHJvbmc+e2BcIiR7YWN0aXZlVGVybX1cImB9PC9zdHJvbmc+XG4gICAgICA8L3A+XG4gICAgICB7IWlzU2VhcmNoaW5nID8gPENvcnJlY3Rpb25zIC8+IDogbnVsbH1cbiAgICAgIDxkaXYgc3R5bGU9e3sgbWFyZ2luVG9wOiAnMTBweCcgfX0+XG4gICAgICAgIDxIYW1tZXJTcGlubmVyIHNob3c9e2lzTG9hZGluZ1Byb2R1Y3RzfSAvPlxuICAgICAgICA8UHJvZHVjdHMgcHJvZHVjdHM9e3Byb2R1Y3RzfSAvPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICkgOiBudWxsO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgTm9SZXN1bHQ7XG4iLCJpbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZUVmZmVjdCB9IGZyb20gJ3JlYWN0JztcbmltcG9ydCB7IHVzZVNlbGVjdG9yLCB1c2VEaXNwYXRjaCB9IGZyb20gJ3JlYWN0LXJlZHV4JztcbmltcG9ydCBTaGlwcGluZ0xhYmVsIGZyb20gJy4uLy4uL2NvbXBvbmVudHMvYXV0by1zdWdnZXN0L3Byb2R1Y3QvcHJvZHVjdC5zaGlwcGluZy5sYWJlbCc7XG5pbXBvcnQgeyB0b2dnbGVEcm9wZG93biwgdXBkYXRlTFNQcm9kdWN0cyB9IGZyb20gJy4uLy4uL2FjdGlvbnMnO1xuaW1wb3J0IHsgc2V0TG9jYWxTdG9yYWdlIH0gZnJvbSAnLi4vLi4vdXRpbHMnO1xuaW1wb3J0IHsgZm9ybWF0UHJpY2UgfSBmcm9tICcuLi8uLi91dGlscy9jb21tb24nO1xuaW1wb3J0IEhpbnRNZXNzYWdlIGZyb20gJy4uLy4uL2NvbXBvbmVudHMvYXV0by1zdWdnZXN0L2hpbnRNZXNzYWdlJztcblxuY29uc3QgUmVjZW50bHlWaWV3ZWQgPSAoKSA9PiB7XG4gIGNvbnN0IHsgcHJvZHVjdFRlcm1zLCBwcm9kdWN0RGF0YSwgcmVjZW50bHlWaWV3ZWRQcm9kdWN0LCBzaG93RHJvcGRvd24sIGFjdGl2ZVRlcm0gfSA9IHVzZVNlbGVjdG9yKChzdGF0ZSkgPT4gc3RhdGUpO1xuICBjb25zdCBbcHJvZHVjdHMsIHNldFByb2R1Y3RzXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgaW5wdXRCb3hSZWYgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc2VhcmNoJyk7XG4gIGNvbnN0IGRpc3BhdGNoID0gdXNlRGlzcGF0Y2goKTtcblxuICBjb25zdCBoYW5kbGVJbnB1dENsaWNrID0gKCkgPT4ge1xuICAgIGlmIChwcm9kdWN0VGVybXM/Lmxlbmd0aCkge1xuICAgICAgZGlzcGF0Y2godG9nZ2xlRHJvcGRvd24oIXNob3dEcm9wZG93bikpO1xuICAgICAgY29uc3QgcmlnaHREaXYgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yKCcuY29sLXJpZ2h0Jyk7XG4gICAgICBpZiAoYWN0aXZlVGVybSAmJiBhY3RpdmVUZXJtLmxlbmd0aCA+IDEpIHtcbiAgICAgICAgcmlnaHREaXYuc3R5bGUuZGlzcGxheSA9ICdibG9jayc7XG4gICAgICB9IGVsc2Uge1xuICAgICAgICByaWdodERpdi5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVCb3hJbnB1dCA9ICgpID0+IHtcbiAgICBjb25zdCByaWdodERpdiA9IGRvY3VtZW50LnF1ZXJ5U2VsZWN0b3IoJy5jb2wtcmlnaHQnKTtcbiAgICBpZiAoaW5wdXRCb3hSZWYudmFsdWUubGVuZ3RoID4gMSkge1xuICAgICAgcmlnaHREaXYuc3R5bGUuZGlzcGxheSA9ICdibG9jayc7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZVN0b3JhZ2VDaGFuZ2UgPSAoKSA9PiB7XG4gICAgZGlzcGF0Y2godXBkYXRlTFNQcm9kdWN0cygpKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVPbkxvYWQgPSAoZXZlbnQpID0+IHtcbiAgICBpZiAoZXZlbnQudGFyZ2V0LnJlYWR5U3RhdGUgPT09ICdjb21wbGV0ZScpIHtcbiAgICAgIGRpc3BhdGNoKHVwZGF0ZUxTUHJvZHVjdHMoKSk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IG9uSGFuZGxlQ2xpY2tEZWxldGUgPSAoaSkgPT4ge1xuICAgIGNvbnN0IG5ld0l0ZW1zID0gcHJvZHVjdFRlcm1zLmZpbHRlcigoaXRlbSkgPT4gaXRlbSAhPT0gaSk7XG4gICAgc2V0TG9jYWxTdG9yYWdlKCdyZWNlbnRseV9zZWFyY2hlZF90ZXJtcycsIEpTT04uc3RyaW5naWZ5KG5ld0l0ZW1zKSk7XG4gICAgZGlzcGF0Y2godXBkYXRlTFNQcm9kdWN0cygpKTtcbiAgfTtcblxuICBjb25zdCBvbkhhbmRsZUNsaWNrRGVsZXRlQWxsID0gKCkgPT4ge1xuICAgIHNldExvY2FsU3RvcmFnZSgncmVjZW50bHlfc2VhcmNoZWRfdGVybXMnLCBKU09OLnN0cmluZ2lmeShbXSkpO1xuICAgIGRpc3BhdGNoKHVwZGF0ZUxTUHJvZHVjdHMoKSk7XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlVmlld0l0ZW1DbGljayA9ICh2YWx1ZSkgPT4ge1xuICAgIGNvbnN0IGlucHV0RGl2ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmNvbC1yaWdodCcpO1xuICAgIHNldFByb2R1Y3RzKFtdKTtcbiAgICBpbnB1dEJveFJlZi52YWx1ZSA9IHZhbHVlO1xuICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gYC9jYXRhbG9nc2VhcmNoL3Jlc3VsdC8/cT0ke3ZhbHVlfWA7XG4gICAgaW5wdXREaXYuc3R5bGUuZGlzcGxheSA9ICdub25lJztcbiAgfTtcblxuICBjb25zdCB1cGRhdGVMb2NhbFN0YXRlID0gKCkgPT4ge1xuICAgIGNvbnN0IFJFQ0VOVExZX1ZJRVdFRF9DT1VOVCA9IDU7XG5cbiAgICBjb25zdCByZWNlbnRseVZpZXdlZFByb2R1Y3RzID0gT2JqZWN0LnZhbHVlcyhyZWNlbnRseVZpZXdlZFByb2R1Y3QpXG4gICAgICAuZmlsdGVyKChpdGVtKSA9PiB7XG4gICAgICAgIGNvbnN0IGRhdGFFeGlzdHMgPSBwcm9kdWN0RGF0YS5oYXNPd25Qcm9wZXJ0eShpdGVtWydwcm9kdWN0X2lkJ10pO1xuICAgICAgICBjb25zdCBjdXJyZW50VGltZSA9IG5ldyBEYXRlKCk7XG4gICAgICAgIGNvbnN0IHByb2R1Y3RUaW1lID0gbmV3IERhdGUoaXRlbS5hZGRlZF9hdCAqIDEwMDApO1xuICAgICAgICBjb25zdCB0aW1lb3V0ID0gNjAgKiA2MCAqIDEwMDA7IC8vIG9uZSBob3VyXG5cbiAgICAgICAgcmV0dXJuIGRhdGFFeGlzdHMgJiYgaXRlbS5zY29wZV9pZCA9PT0gd2luZG93LmNoZWNrb3V0LndlYnNpdGVJZCAmJiBjdXJyZW50VGltZSAtIHByb2R1Y3RUaW1lIDwgdGltZW91dDtcbiAgICAgIH0pXG4gICAgICAuc29ydCgoYSwgYikgPT4ge1xuICAgICAgICByZXR1cm4gYi5hZGRlZF9hdCAtIGEuYWRkZWRfYXQ7XG4gICAgICB9KVxuICAgICAgLm1hcCgocmVjZW50KSA9PiB7XG4gICAgICAgIHJldHVybiBwcm9kdWN0RGF0YVtyZWNlbnQucHJvZHVjdF9pZF07XG4gICAgICB9KTtcblxuICAgIGlmIChyZWNlbnRseVZpZXdlZFByb2R1Y3RzLmxlbmd0aCA+IFJFQ0VOVExZX1ZJRVdFRF9DT1VOVCkge1xuICAgICAgcmVjZW50bHlWaWV3ZWRQcm9kdWN0cy5sZW5ndGggPSBSRUNFTlRMWV9WSUVXRURfQ09VTlQ7XG4gICAgfVxuXG4gICAgc2V0UHJvZHVjdHMocmVjZW50bHlWaWV3ZWRQcm9kdWN0cyk7XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICB1cGRhdGVMb2NhbFN0YXRlKCk7XG4gICAgd2luZG93Py5hZGRFdmVudExpc3RlbmVyKCdzdG9yYWdlJywgaGFuZGxlU3RvcmFnZUNoYW5nZSwgZmFsc2UpO1xuICAgIHdpbmRvdy5kb2N1bWVudD8uYWRkRXZlbnRMaXN0ZW5lcigncmVhZHlzdGF0ZWNoYW5nZScsIGhhbmRsZU9uTG9hZCwgZmFsc2UpO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIHdpbmRvdz8ucmVtb3ZlRXZlbnRMaXN0ZW5lcignc3RvcmFnZScsIGhhbmRsZVN0b3JhZ2VDaGFuZ2UsIGZhbHNlKTtcbiAgICAgIHdpbmRvdy5kb2N1bWVudD8ucmVtb3ZlRXZlbnRMaXN0ZW5lcigncmVhZHlzdGF0ZWNoYW5nZScsIGhhbmRsZU9uTG9hZCwgZmFsc2UpO1xuICAgIH07XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IHByb2R1Y3RDb3VudHMgPSBPYmplY3Q/LmtleXMocmVjZW50bHlWaWV3ZWRQcm9kdWN0KT8ubGVuZ3RoO1xuICAgIGNvbnN0IHByZXZQcm9kdWN0Q291bnRzID0gT2JqZWN0Py5rZXlzKHJlY2VudGx5Vmlld2VkUHJvZHVjdCk/Lmxlbmd0aDtcblxuICAgIGlmIChwcm9kdWN0Q291bnRzICE9PSBwcmV2UHJvZHVjdENvdW50cykge1xuICAgICAgdXBkYXRlTG9jYWxTdGF0ZSgpO1xuICAgIH1cbiAgfSwgW3JlY2VudGx5Vmlld2VkUHJvZHVjdF0pO1xuXG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgaW5wdXRCb3hSZWY/LmFkZEV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgaGFuZGxlSW5wdXRDbGljaywgZmFsc2UpO1xuICAgIGlucHV0Qm94UmVmPy5hZGRFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBoYW5kbGVCb3hJbnB1dCwgZmFsc2UpO1xuXG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIGlucHV0Qm94UmVmPy5yZW1vdmVFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZUlucHV0Q2xpY2ssIGZhbHNlKTtcbiAgICAgIGlucHV0Qm94UmVmPy5yZW1vdmVFdmVudExpc3RlbmVyKCdjaGFuZ2UnLCBoYW5kbGVCb3hJbnB1dCwgZmFsc2UpO1xuICAgIH07XG4gIH0pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdiBjbGFzc05hbWU9XCJyZWNlbnRseS12aWV3ZWQtbGlzdFwiPlxuICAgICAge3Byb2R1Y3RUZXJtcy5sZW5ndGggPiAwICYmIChcbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWFyY2hlZC10ZXJtc1wiPlxuICAgICAgICAgIDxIaW50TWVzc2FnZSBzaG93PXt0cnVlfSBtZXNzYWdlPVwiU3RhcnQgdHlwaW5nIHRvIGZpbmQgYWxsIHRvb2xzIGZvciBhbGwgdHJhZGVzXCIgLz5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlYXJjaGVkXCI+XG4gICAgICAgICAgICA8aDU+UmVjZW50bHkgU2VhcmNoZWQ8L2g1PlxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJjbGVhci1hbGxcIj5cbiAgICAgICAgICAgICAgPGEgb25DbGljaz17b25IYW5kbGVDbGlja0RlbGV0ZUFsbH0+Q2xlYXIgQWxsPC9hPlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWFyY2gtYXV0b2NvbXBsZXRlLWNhdGVnb3JpZXNcIj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwic2VjdGlvblwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlY3Rpb24tY29udGVudFwiPlxuICAgICAgICAgICAgICAgIDx1bCBjbGFzc05hbWU9XCJjYXRlZ29yaWVzLWxpc3RcIj5cbiAgICAgICAgICAgICAgICAgIHtwcm9kdWN0VGVybXMgJiZcbiAgICAgICAgICAgICAgICAgICAgcHJvZHVjdFRlcm1zPy5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICAgICAgICAgICAgICA8bGkga2V5PXtpdGVtfSBjbGFzc05hbWU9XCJjYXRlZ29yaWVzLWxpc3QtaXRlbVwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGEgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld0l0ZW1DbGljayhpdGVtKX0gY2xhc3NOYW1lPVwicmVjZW50bHktdmlld2VkLWxhYmVsXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtfVxuICAgICAgICAgICAgICAgICAgICAgICAgPC9hPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGEgY2xhc3NOYW1lPVwicmVjZW50bHktdmlld2VkLWljb25zLWNsb3NlXCIgb25DbGljaz17KCkgPT4gb25IYW5kbGVDbGlja0RlbGV0ZShpdGVtKX0+PC9hPlxuICAgICAgICAgICAgICAgICAgICAgIDwvbGk+XG4gICAgICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICAgIDwvdWw+XG4gICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgKX1cblxuICAgICAge3Byb2R1Y3RzLmxlbmd0aCA+IDAgJiYgKFxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlY2VudGx5LXZpZXdlZC1wcm9kdWN0c1wiPlxuICAgICAgICAgIDxoNT5yZWNlbnRseSB2aWV3ZWQgcHJvZHVjdHM8L2g1PlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVjZW50bHktdmlld2QtaXRlbVwiPlxuICAgICAgICAgICAge3Byb2R1Y3RzLm1hcCgocHJvZHVjdCwgaWR4KSA9PiB7XG4gICAgICAgICAgICAgIGNvbnN0IHByb2R1Y3RJbmZvRmluYWxQcmljZSA9IGZvcm1hdFByaWNlKHByb2R1Y3QucHJpY2VfaW5mby5maW5hbF9wcmljZSk7XG4gICAgICAgICAgICAgIHJldHVybiAoXG4gICAgICAgICAgICAgICAgPGRpdiBrZXk9e2lkeH0gY2xhc3NOYW1lPVwic2VhcmNoLWF1dG9jb21wbGV0ZS1wcm9kdWN0XCIgcm9sZT1cIm9wdGlvbnNcIiBpZD17YG9wdGlvbnMtJHtwcm9kdWN0LmlkfWB9PlxuICAgICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwic2VhcmNoLWF1dG9jb21wbGV0ZS1oaXRcIlxuICAgICAgICAgICAgICAgICAgICBocmVmPXtwcm9kdWN0LnVybH1cbiAgICAgICAgICAgICAgICAgICAgZGF0YS1pZD1cInByb2R1Y3RfaWRcIlxuICAgICAgICAgICAgICAgICAgICBkYXRhLW5hbWU9XCJwcm9kdWN0Lm5hbWVcIlxuICAgICAgICAgICAgICAgICAgICBkYXRhLXByaWNlPVwicHJvZHVjdC5wcmljZV9pbmZvLmZpbmFsX3ByaWNlXCJcbiAgICAgICAgICAgICAgICAgICAgZGF0YS1wb3NpdGlvbj1cIlwiXG4gICAgICAgICAgICAgICAgICAgIGRhdGEtYnJhbmQ9XCJcIlxuICAgICAgICAgICAgICAgICAgICBkYXRhLWNhdGVnb3J5PVwiXCJcbiAgICAgICAgICAgICAgICAgICAgZGF0YS1saXN0PVwiXCJcbiAgICAgICAgICAgICAgICAgICAgZGF0YS1ldmVudD1cIlwiXG4gICAgICAgICAgICAgICAgICAgIGRhdGEtc3RvcmU9XCJcIlxuICAgICAgICAgICAgICAgICAgICBkYXRhLWF0dHJpYnV0ZXM9XCJbXVwiXG4gICAgICAgICAgICAgICAgICA+XG4gICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGh1bWJcIj5cbiAgICAgICAgICAgICAgICAgICAgICA8aW1nXG4gICAgICAgICAgICAgICAgICAgICAgICBzcmM9e3Byb2R1Y3QuZXh0ZW5zaW9uX2F0dHJpYnV0ZXMubGFiZWxlZF9pbWFnZSB8fCBwcm9kdWN0LmltYWdlc1swXS51cmx9XG4gICAgICAgICAgICAgICAgICAgICAgICBhbHQ9e3Byb2R1Y3QubmFtZX1cbiAgICAgICAgICAgICAgICAgICAgICAvPlxuICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJpbmZvXCI+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiByb2xlPVwidGl0bGVcIiBjbGFzc05hbWU9XCJwcm9kdWN0LXRpdGxlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdC5uYW1lfVxuICAgICAgICAgICAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJpY2UtYmxvY2tcIj5cbiAgICAgICAgICAgICAgICAgICAgICAgIHtwcm9kdWN0Py5leHRlbnNpb25fYXR0cmlidXRlcz8ubm90X2Zvcl9zYWxlICE9PSB0cnVlICYmIChcbiAgICAgICAgICAgICAgICAgICAgICAgICAgPD5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJwcm9kdWN0LXByaWNlXCI+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJjdXJyZW5jeS1zeW1ib2xcIj4kPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwicHJpY2UtbWFpblwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7QXJyYXkuaXNBcnJheShwcm9kdWN0SW5mb0ZpbmFsUHJpY2UpID8gKFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDw+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICB7cHJvZHVjdEluZm9GaW5hbFByaWNlPy5bMF19XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8c3BhbiBjbGFzcz1cImRlY2ltYWwtZG90XCI+Ljwvc3Bhbj5cbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxzcGFuIGNsYXNzPVwicHJpY2UtZGVjaW1hbFwiPntwcm9kdWN0SW5mb0ZpbmFsUHJpY2U/LlsxXX08L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPC8+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICkgOiAoXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgcHJvZHVjdEluZm9GaW5hbFByaWNlXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8Lz5cbiAgICAgICAgICAgICAgICAgICAgICAgICl9XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9kdWN0LWl0ZW0tYWN0aW9uc1wiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPGFcbiAgICAgICAgICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiYWN0aW9uIHRvZGV0YWlscyBwcmltYXJ5XCJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS1xdWFudGl0eT17MX1cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS1jYXRlZ29yeT17J1NlYXJjaCBSZXN1bHRzJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS1saXN0PXsnU2VhcmNoIFJlc3VsdHMnfVxuICAgICAgICAgICAgICAgICAgICAgICAgICBkYXRhLWV2ZW50PXsncHJvZHVjdENsaWNrJ31cbiAgICAgICAgICAgICAgICAgICAgICAgICAgZGF0YS1hdHRyaWJ1dGVzPXsnW10nfVxuICAgICAgICAgICAgICAgICAgICAgICAgPlxuICAgICAgICAgICAgICAgICAgICAgICAgICA8c3Bhbj5WaWV3IERldGFpbHM8L3NwYW4+XG4gICAgICAgICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJsYWJlbHMtd3JhcHBlclwiPlxuICAgICAgICAgICAgICAgICAgICAgICAgPFNoaXBwaW5nTGFiZWwgbGFiZWw9e3Byb2R1Y3Quc2hpcHBpbmdMYWJlbH0gLz5cbiAgICAgICAgICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICAgICA8L2E+XG4gICAgICAgICAgICAgICAgPC9kaXY+XG4gICAgICAgICAgICAgICk7XG4gICAgICAgICAgICB9KX1cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICApfVxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgUmVjZW50bHlWaWV3ZWQ7XG4iLCJpbXBvcnQgUmVhY3QsIHsgdXNlRWZmZWN0LCB1c2VTdGF0ZSwgdXNlUmVmIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU2VsZWN0b3IsIHVzZURpc3BhdGNoIH0gZnJvbSAncmVhY3QtcmVkdXgnO1xuaW1wb3J0IHsgc2V0VGVybXNPbkxvY2FsU3RvcmFnZSwgZGVib3VuY2UgfSBmcm9tICcuLi8uLi91dGlscyc7XG5pbXBvcnQgeyByZW1vdmVEb3RzRnJvbVF1ZXJ5U3RhcnQsIGlzVmFsaWRRdWVyeSwgc2FuaXRpemVkUXVlcnkgfSBmcm9tICcuLi8uLi91dGlscy9jb21tb24nO1xuaW1wb3J0IHtcbiAgZmV0Y2hTZWFyY2hUZXJtcyxcbiAgc2VhcmNoUXVlcnksXG4gIGNsZWFyU2VhcmNoLFxuICBzdWdnZXN0aW9uQ2xpY2ssXG4gIGZldGNoVGVybVJlc3VsdCxcbiAgdG9nZ2xlRHJvcGRvd24sXG59IGZyb20gJy4uLy4uL2FjdGlvbnMnO1xuXG5jb25zdCBtYWdlbnRvU2VhcmNoSW5wdXRSZWYgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc2VhcmNoJyk7XG5jb25zdCBzZWFyY2hGb3JtID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3NlYXJjaF9taW5pX2Zvcm0nKTtcbmNvbnN0IHNlYXJjaEJ1dHRvbiA9IHNlYXJjaEZvcm0ucXVlcnlTZWxlY3RvcignYnV0dG9uW3R5cGU9XCJzdWJtaXRcIl0nKTtcblxuY29uc3QgU2VhcmNoSW5wdXQgPSAoeyBpc0ZvY3VzZWQsIGlzU2VhcmNoRnVsbFR5cGUsIHNldFNob3dNYWluTG9hZGVyTWFzayB9KSA9PiB7XG4gIGNvbnN0IHtcbiAgICBzaG93RHJvcGRvd24sXG4gICAgaXNTZWFyY2hpbmcsXG4gICAgc3VnZ2VzdGlvbnM6IHsgYWN0aXZlVGVybSwgZmV0Y2hpbmcsIHJlZGlyZWN0cyB9LFxuICB9ID0gdXNlU2VsZWN0b3IoKHN0YXRlKSA9PiBzdGF0ZSk7XG4gIGNvbnN0IHJlZGlyZWN0ID0gcmVkaXJlY3RzICYmIHJlZGlyZWN0c1thY3RpdmVUZXJtXSA/IHJlZGlyZWN0c1thY3RpdmVUZXJtXSA6IG51bGw7XG4gIGNvbnN0IGRpc3BhdGNoID0gdXNlRGlzcGF0Y2goKTtcbiAgY29uc3Qgc2VhcmNoSW5wdXRSZWYgPSB1c2VSZWYoKTtcbiAgY29uc3QgW2lucHV0VmFsLCBzZXRJbnB1dFZhbF0gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoaXNGb2N1c2VkKSBzZWFyY2hJbnB1dFJlZj8uY3VycmVudD8uZm9jdXMoKTtcbiAgfSwgW2lzRm9jdXNlZF0pO1xuXG4gIGNvbnN0IG1ha2VTZWFyY2hDYWxsID0gKHNlYXJjaFRleHQpID0+IHtcbiAgICBjb25zdCBzZWFyY2hMYWJlbCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdtaW5pc2VhcmNoLWxhYmVsJyk7XG4gICAgY29uc3Qgc2VhcmNoQ29udHJvbCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdtaW5pc2VhcmNoLWNvbnRyb2wnKTtcbiAgICBjb25zdCByaWdodENvbCA9IGRvY3VtZW50LmdldEVsZW1lbnRzQnlDbGFzc05hbWUoJ2NvbC1yaWdodCcpO1xuXG4gICAgaWYgKHNlYXJjaEZvcm0gJiYgIXNlYXJjaEZvcm0uY2xhc3NMaXN0LmNvbnRhaW5zKCdvcGVuZWQnKSkge1xuICAgICAgc2VhcmNoRm9ybT8uY2xhc3NMaXN0Py5hZGQoJ29wZW5lZCcpO1xuICAgICAgc2VhcmNoTGFiZWw/LmNsYXNzTGlzdD8uYWRkKCdvcGVuZWQnKTtcbiAgICAgIHNlYXJjaENvbnRyb2w/LmNsYXNzTGlzdD8uYWRkKCdvcGVuZWQnKTtcbiAgICB9XG5cbiAgICBpZiAocmlnaHRDb2wubGVuZ3RoKSB7XG4gICAgICByaWdodENvbFswXS5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICAgIH1cblxuICAgIGRpc3BhdGNoKHNlYXJjaFF1ZXJ5KHNlYXJjaFRleHQpKTtcbiAgICBsZXQgcXVlcnkgPSBzZWFyY2hUZXh0IHx8ICcnO1xuICAgIGlmICghcXVlcnkgfHwgcXVlcnkubGVuZ3RoIDwgMikge1xuICAgICAgZGlzcGF0Y2goY2xlYXJTZWFyY2goKSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIGRpc3BhdGNoKGNsZWFyU2VhcmNoKCkpXG4gICAgICAgIC50aGVuKCgpID0+IGRpc3BhdGNoKHNlYXJjaFF1ZXJ5KHF1ZXJ5KSkpXG4gICAgICAgIC50aGVuKCgpID0+IGRpc3BhdGNoKGZldGNoU2VhcmNoVGVybXMocXVlcnkpKSlcbiAgICAgICAgLnRoZW4oKCkgPT4gZGlzcGF0Y2goc3VnZ2VzdGlvbkNsaWNrKHF1ZXJ5KSkpXG4gICAgICAgIC50aGVuKCgpID0+IGRpc3BhdGNoKGZldGNoVGVybVJlc3VsdChxdWVyeSwgdHJ1ZSkpKTtcbiAgICB9XG4gIH07XG5cbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSBkZWJvdW5jZSgodmFsdWUpID0+IHtcbiAgICBtYWtlU2VhcmNoQ2FsbCh2YWx1ZSk7XG4gIH0sIDUwMCk7XG5cbiAgY29uc3QgaGFuZGxlQ2hhbmdlID0gKGV2ZW50KSA9PiB7XG4gICAgY29uc3QgbmV3VmFsdWUgPSBldmVudC50YXJnZXQudmFsdWU7XG4gICAgbGV0IGZpbmFsVmFsdWUgPSAnJztcbiAgICBpZiAoaXNWYWxpZFF1ZXJ5KG5ld1ZhbHVlKSkge1xuICAgICAgZmluYWxWYWx1ZSA9IG5ld1ZhbHVlO1xuICAgIH0gZWxzZSB7XG4gICAgICBjb25zdCBzYW5pdGl6ZWRWYWx1ZSA9IHNhbml0aXplZFF1ZXJ5KG5ld1ZhbHVlKTtcbiAgICAgIGZpbmFsVmFsdWUgPSBzYW5pdGl6ZWRWYWx1ZTtcbiAgICB9XG5cbiAgICBpZiAobWFnZW50b1NlYXJjaElucHV0UmVmKSB7XG4gICAgICBtYWdlbnRvU2VhcmNoSW5wdXRSZWYudmFsdWUgPSBmaW5hbFZhbHVlO1xuICAgIH1cbiAgICBzZXRJbnB1dFZhbChmaW5hbFZhbHVlKTtcbiAgICBoYW5kbGVJbnB1dENoYW5nZShmaW5hbFZhbHVlKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVDbGljayA9ICgpID0+IHtcbiAgICBzZWFyY2hJbnB1dFJlZj8uY3VycmVudD8uZm9jdXMoKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVTdWJtaXRDbGljayA9IChldikgPT4ge1xuICAgIGV2LnByZXZlbnREZWZhdWx0KCk7XG4gICAgZXYuc3RvcFByb3BhZ2F0aW9uKCk7XG4gICAgaWYgKGlucHV0VmFsICYmIGlucHV0VmFsPy5sZW5ndGggPj0gMikge1xuICAgICAgc2V0U2hvd01haW5Mb2FkZXJNYXNrPy4odHJ1ZSk7XG4gICAgICByZWRpcmVjdE9uU2VhcmNoUGFnZSgpO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCByZWRpcmVjdE9uU2VhcmNoUGFnZSA9ICgpID0+IHtcbiAgICBjb25zdCB0ZXJtID0gbWFnZW50b1NlYXJjaElucHV0UmVmPy52YWx1ZSB8fCBpbnB1dFZhbCB8fCAnJztcbiAgICBpZiAodGVybSkge1xuICAgICAgY29uc3Qgd2l0aG91dERvdHNUZXJtID0gcmVtb3ZlRG90c0Zyb21RdWVyeVN0YXJ0KHRlcm0pO1xuICAgICAgbGV0IHVybCA9IGVuY29kZVVSSUNvbXBvbmVudCh3aXRob3V0RG90c1Rlcm0pXG4gICAgICAgID8gYC9jYXRhbG9nc2VhcmNoL3Jlc3VsdC8/cT0ke2VuY29kZVVSSUNvbXBvbmVudCh3aXRob3V0RG90c1Rlcm0pfWBcbiAgICAgICAgOiAnJztcbiAgICAgIGlmICh1cmwpIHtcbiAgICAgICAgc2V0VGVybXNPbkxvY2FsU3RvcmFnZSh3aXRob3V0RG90c1Rlcm0pO1xuICAgICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IHVybDtcbiAgICAgICAgcmV0dXJuO1xuICAgICAgfVxuICAgIH1cbiAgfTtcblxuICBjb25zdCBoYW5kbGVJbnB1dEZvY3VzID0gKGUpID0+IHtcbiAgICBlLnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIGUucHJldmVudERlZmF1bHQoKTtcbiAgICBpZiAoYWN0aXZlVGVybSAmJiAhc2hvd0Ryb3Bkb3duKSBkaXNwYXRjaCh0b2dnbGVEcm9wZG93bih0cnVlKSk7XG4gIH07XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBtYWdlbnRvU2VhcmNoSW5wdXRSZWY/LmFkZEV2ZW50TGlzdGVuZXIoJ2lucHV0JywgaGFuZGxlQ2hhbmdlKTtcblxuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAobWFnZW50b1NlYXJjaElucHV0UmVmKSB7XG4gICAgICAgIG1hZ2VudG9TZWFyY2hJbnB1dFJlZj8ucmVtb3ZlRXZlbnRMaXN0ZW5lcignaW5wdXQnLCBoYW5kbGVDaGFuZ2UpO1xuICAgICAgICBtYWdlbnRvU2VhcmNoSW5wdXRSZWYucmVtb3ZlRXZlbnRMaXN0ZW5lcignY2xpY2snLCBoYW5kbGVJbnB1dEZvY3VzLCBmYWxzZSk7XG4gICAgICAgIHNlYXJjaEZvcm0ucmVtb3ZlRXZlbnRMaXN0ZW5lcignc3VibWl0JywgaGFuZGxlU3VibWl0Q2xpY2spO1xuICAgICAgICBzZWFyY2hCdXR0b24/LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2NsaWNrJywgaGFuZGxlU3VibWl0Q2xpY2spO1xuICAgICAgfVxuICAgIH07XG4gIH0sIFtdKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIG1hZ2VudG9TZWFyY2hJbnB1dFJlZi5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZUlucHV0Rm9jdXMpO1xuICB9LCBbYWN0aXZlVGVybSwgc2hvd0Ryb3Bkb3duXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBzZWFyY2hGb3JtPy5hZGRFdmVudExpc3RlbmVyKCdzdWJtaXQnLCBoYW5kbGVTdWJtaXRDbGljayk7XG4gICAgc2VhcmNoQnV0dG9uPy5hZGRFdmVudExpc3RlbmVyKCdjbGljaycsIGhhbmRsZVN1Ym1pdENsaWNrKTtcbiAgfSwgW2lucHV0VmFsLCBpc1NlYXJjaGluZywgZmV0Y2hpbmcsIGFjdGl2ZVRlcm0sIHJlZGlyZWN0XSk7XG5cbiAgcmV0dXJuIGlzU2VhcmNoRnVsbFR5cGUgPyAoXG4gICAgPD5cbiAgICAgIDxpbnB1dFxuICAgICAgICByZWY9e3NlYXJjaElucHV0UmVmfVxuICAgICAgICBjbGFzc05hbWU9XCJpbnB1dC10ZXh0XCJcbiAgICAgICAgdHlwZT1cInRleHRcIlxuICAgICAgICBuYW1lPVwic2VhcmNoXCJcbiAgICAgICAgdmFsdWU9e2lucHV0VmFsfVxuICAgICAgICBwbGFjZWhvbGRlcj1cIkZpbmQgeW91ciB0b29scy4uLlwiXG4gICAgICAgIG1heExlbmd0aD1cIjEyOFwiXG4gICAgICAgIHJvbGU9XCJjb21ib2JveFwiXG4gICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVDaGFuZ2V9XG4gICAgICAgIG9uQ2xpY2s9e2hhbmRsZUNsaWNrfVxuICAgICAgLz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWN0aW9uc1wiPlxuICAgICAgICA8YnV0dG9uIHR5cGU9XCJzdWJtaXRcIiBjbGFzc05hbWU9XCJhY3Rpb24gc2VhcmNoXCIgb25DbGljaz17KGUpID0+IGhhbmRsZVN1Ym1pdENsaWNrKGUpfT5cbiAgICAgICAgICBzZWFyY2hcbiAgICAgICAgPC9idXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8Lz5cbiAgKSA6IG51bGw7XG59O1xuXG5leHBvcnQgZGVmYXVsdCBTZWFyY2hJbnB1dDtcbiIsImltcG9ydCBSZWFjdCwgeyB1c2VFZmZlY3QgfSBmcm9tICdyZWFjdCc7XG5cbmltcG9ydCBTZWFyY2hJbnB1dCBmcm9tICcuLi8uLi9jb21wb25lbnRzL3NlYXJjaC1ib3gvc2VhcmNoLWlucHV0JztcblxuY29uc3Qgc2VhcmNoQ29udHJvbCA9IGRvY3VtZW50LmdldEVsZW1lbnRCeUlkKCdtaW5pc2VhcmNoLWNvbnRyb2wnKSxcbiAgc2VhcmNoTGFiZWwgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnbWluaXNlYXJjaC1sYWJlbCcpO1xuXG5jb25zdCBTZWFyY2hCb3ggPSAocHJvcHMpID0+IHtcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCB0b2dnbGVNb2JpbGVTZWFyY2gsIGZhbHNlKTtcbiAgICByZXR1cm4gKCkgPT4gZG9jdW1lbnQucmVtb3ZlRXZlbnRMaXN0ZW5lcignbW91c2Vkb3duJywgdG9nZ2xlTW9iaWxlU2VhcmNoLCBmYWxzZSk7XG4gIH0sIFtdKTtcblxuICBjb25zdCB0b2dnbGVNb2JpbGVTZWFyY2ggPSAoZSkgPT4ge1xuICAgIGlmIChzZWFyY2hMYWJlbCAmJiBlLnRhcmdldC5jb250YWlucyhzZWFyY2hMYWJlbCkpIHtcbiAgICAgIHNlYXJjaENvbnRyb2wgJiYgc2VhcmNoQ29udHJvbC5jbGFzc0xpc3QudG9nZ2xlKCdvcGVuZWQnKTtcbiAgICB9XG4gIH07XG4gIHJldHVybiA8U2VhcmNoSW5wdXQgey4uLnByb3BzfSAvPjtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IFNlYXJjaEJveDtcbiIsImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5cbmNvbnN0IFRyZW5kaW5nU2VhcmNoZXMgPSAoKSA9PiB7XG4gIGNvbnN0IHBvcHVsYXJTZWFyY2hUZXJtcyA9IHdpbmRvdz8udW5ieGRDb25maWc/LnBvcHVsYXJTZWFyY2hUZXJtcztcbiAgY29uc3QgcG9wdWxhclNlYXJjaFRlcm1zQXJyYXkgPSBwb3B1bGFyU2VhcmNoVGVybXM/LnNwbGl0KCcsJykuZmlsdGVyKHRlcm0gPT4gdGVybS50cmltKCkgIT09ICcnKTtcbiAgY29uc3QgaW5wdXRCb3hSZWYgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc2VhcmNoJyk7XG5cbiAgY29uc3QgaGFuZGxlVmlld0l0ZW1DbGljayA9ICh2YWx1ZSkgPT4ge1xuICAgIGNvbnN0IGlucHV0RGl2ID0gZG9jdW1lbnQucXVlcnlTZWxlY3RvcignLmNvbC1yaWdodCcpO1xuICAgIGlucHV0Qm94UmVmLnZhbHVlID0gdmFsdWU7XG4gICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSBgL2NhdGFsb2dzZWFyY2gvcmVzdWx0Lz9xPSR7dmFsdWV9YDtcbiAgICBpbnB1dERpdi5zdHlsZS5kaXNwbGF5ID0gJ25vbmUnO1xuICB9O1xuXG4gIGlmICghcG9wdWxhclNlYXJjaFRlcm1zQXJyYXk/Lmxlbmd0aCkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cInRyZW5kaW5nLXNlYXJjaFwiPlxuICAgICAgPGg1PlRyZW5kaW5nIFNlYXJjaGVzPC9oNT5cbiAgICAgIDx1bCBjbGFzc05hbWU9XCJjYXRlZ29yaWVzLWxpc3RcIj5cbiAgICAgICAge3BvcHVsYXJTZWFyY2hUZXJtc0FycmF5Py5tYXAoKGl0ZW0pID0+IChcbiAgICAgICAgICA8bGkga2V5PXtpdGVtfSBjbGFzc05hbWU9XCJjYXRlZ29yaWVzLWxpc3QtaXRlbVwiPlxuICAgICAgICAgICAgPGJ1dHRvbiB0eXBlPVwiYnV0dG9uXCIgb25DbGljaz17KCkgPT4gaGFuZGxlVmlld0l0ZW1DbGljayhpdGVtKX0+XG4gICAgICAgICAgICAgIHtpdGVtfVxuICAgICAgICAgICAgPC9idXR0b24+XG4gICAgICAgICAgPC9saT5cbiAgICAgICAgKSl9XG4gICAgICA8L3VsPlxuICAgIDwvZGl2PlxuICApO1xufTtcblxuZXhwb3J0IGRlZmF1bHQgVHJlbmRpbmdTZWFyY2hlcztcbiIsImltcG9ydCBSZWFjdCwgeyB1c2VSZWYsIHVzZUVmZmVjdCwgdXNlU3RhdGUsIHVzZUNhbGxiYWNrIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHsgdXNlU2VsZWN0b3IsIHVzZURpc3BhdGNoIH0gZnJvbSAncmVhY3QtcmVkdXgnO1xuaW1wb3J0IENhdGVnb3JpZXMgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9hdXRvLXN1Z2dlc3QvY2F0ZWdvcmllcyc7XG5pbXBvcnQgSGFtbWVyU3Bpbm5lciBmcm9tICcuLi8uLi91dGlscy9IYW1tZXJTcGlubmVyJztcbmltcG9ydCB7IGNsZWFyUHJvZHVjdHMsIHRvZ2dsZU1haW5Mb2FkZXIsIGZldGNoUHJvZHVjdHMgfSBmcm9tICcuLi8uLi9hY3Rpb25zJztcbmltcG9ydCB7IE1haW5Mb2FkZXIgfSBmcm9tICcuLi8uLi9jb21wb25lbnRzL21haW5Mb2FkZXInO1xuaW1wb3J0IEVycm9yIGZyb20gJy4uLy4uL2NvbXBvbmVudHMvYXV0by1zdWdnZXN0L2Vycm9yJztcbmltcG9ydCBQcm9kdWN0cyBmcm9tICcuLi8uLi9jb21wb25lbnRzL2F1dG8tc3VnZ2VzdC9wcm9kdWN0cyc7XG5pbXBvcnQgTm9SZXN1bHQgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9hdXRvLXN1Z2dlc3Qvbm8tcmVzdWx0JztcbmltcG9ydCBSZWNlbnRseVZpZXdlZCBmcm9tICcuL3JlY2VudGx5LXZpZXdlZCc7XG5pbXBvcnQgSGludE1lc3NhZ2UgZnJvbSAnLi4vLi4vY29tcG9uZW50cy9hdXRvLXN1Z2dlc3QvaGludE1lc3NhZ2UnO1xuaW1wb3J0IHsgc2V0VGVybXNPbkxvY2FsU3RvcmFnZSwgY2FwaXRhbGl6ZSB9IGZyb20gJy4uLy4uL3V0aWxzJztcblxuaW1wb3J0IFNlYXJjaEJveCBmcm9tICcuL3NlYXJjaC1ib3gnO1xuaW1wb3J0IFRyZW5kaW5nU2VhcmNoZXMgZnJvbSAnLi90cmVuZGluZ1NlYXJjaGVzJztcbmNvbnN0IHNlYXJjaEZvcm0gPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc2VhcmNoX21pbmlfZm9ybScpO1xuY29uc3QgQXV0b0NvbXBsZXRlID0gKCkgPT4ge1xuICBjb25zdCBkcm9wRG93blJlZiA9IHVzZVJlZigpO1xuICBjb25zdCBtYWluQ29udGFpbmVyID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ21pbmlzZWFyY2gtY29udHJvbCcpO1xuICBjb25zdCBvdXRlck1vc3RDb250YWluZXIgPSBkb2N1bWVudC5nZXRFbGVtZW50QnlJZCgnc2VhcmNoLW1haW4nKTtcblxuICBjb25zdCBib2R5RWxlbWVudCA9IGRvY3VtZW50LmJvZHk7XG5cbiAgY29uc3QgW3Byb2R1Y3RzRGF0YSwgc2V0UHJvZHVjdHNEYXRhXSA9IHVzZVN0YXRlKFtdKTtcbiAgY29uc3QgW3Nob3dNYWluTG9hZGVyTWFzaywgc2V0U2hvd01haW5Mb2FkZXJNYXNrXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3Nob3dSaWdodENvbCwgc2V0U2hvd1JpZ2h0Q29sXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW29wZW5Ecm9wRG93biwgc2V0T3BlbkRyb3BEb3duXSA9IHVzZVN0YXRlKGZhbHNlKTtcbiAgY29uc3QgW3JlZGlyZWN0VXJsLCBzZXRSZWRpcmVjdFVybF0gPSB1c2VTdGF0ZSgnJyk7XG5cbiAgY29uc3QgaXNEZXNrdG9wID0gd2luZG93LmlubmVyV2lkdGggPiA3Njc7XG5cbiAgY29uc3QgZGlzcGF0Y2ggPSB1c2VEaXNwYXRjaCgpO1xuICBjb25zdCB7XG4gICAgcXVlcnksXG4gICAgaXNTZWFyY2hpbmcsXG4gICAgbm9SZXN1bHQsXG4gICAgZXJyb3IsXG4gICAgZXJyb3JNc2csXG4gICAgcHJvZHVjdFRlcm1zLFxuICAgIHN1Z2dlc3Rpb25zOiBzdWdnZXN0aW9uc18sXG4gICAgY29ycmVjdGlvbnM6IHsgdGVybXM6IGNvcnJlY3Rpb25UZXJtcyB9LFxuICB9ID0gdXNlU2VsZWN0b3IoKHN0YXRlKSA9PiBzdGF0ZSk7XG5cbiAgY29uc3QgeyBpc0luaXRpYWxGZXRjaCwgZmV0Y2hpbmc6IGlzRmV0Y2hpbmcsIHRlcm1zOiBzdWdnZXN0aW9ucywgYWN0aXZlVGVybSwgcmVkaXJlY3RzLCBwcm9kdWN0cyB9ID0gc3VnZ2VzdGlvbnNfO1xuICBjb25zdCBwcm9kdWN0c1F1ZXJ5ID0gcXVlcnk/LnNsaWNlKDAsIC0xKTtcbiAgY29uc3QgY2FwaXRhbGl6ZWRQcm9kdWN0UXVlcnkgPSBjYXBpdGFsaXplKHByb2R1Y3RzUXVlcnkpO1xuXG4gIGNvbnN0IGhhbmRsZUZvY3VzID0gKCkgPT4ge1xuICAgIGlmICghb3BlbkRyb3BEb3duKSB7XG4gICAgICBzZXRPcGVuRHJvcERvd24odHJ1ZSk7XG4gICAgfVxuICAgIG1haW5Db250YWluZXI/LmNsYXNzTGlzdD8uYWRkKCdhY3RpdmUnKTtcbiAgICBvdXRlck1vc3RDb250YWluZXI/LmNsYXNzTGlzdD8uYWRkKCdhY3RpdmUnKTtcbiAgICBib2R5RWxlbWVudD8uY2xhc3NMaXN0Py5hZGQoJ3NlYXJjaC1pcy1hY3RpdmUnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVQcmVzc0VudGVyID0gdXNlQ2FsbGJhY2soXG4gICAgKGV2ZW50KSA9PiB7XG4gICAgICBjb25zb2xlLmxvZygnaGFuZGxlUHJlc3NFbnRlciAsIENhbGxlZCBGb2N1cyBPVXRJbnB1dCcpO1xuICAgICAgbGV0IGV2ZW50S2V5UHJlc3NlZCA9IGV2ZW50Py5rZXlDb2RlO1xuICAgICAgaWYgKGV2ZW50S2V5UHJlc3NlZCA9PT0gMTMpIHtcbiAgICAgICAgbGV0IHVybCA9IGAvY2F0YWxvZ3NlYXJjaC9yZXN1bHQvP3E9JHtlbmNvZGVVUklDb21wb25lbnQoZXZlbnQ/LnRhcmdldC52YWx1ZSl9YDtcbiAgICAgICAgaWYgKHJlZGlyZWN0VXJsKSB7XG4gICAgICAgICAgdXJsID0gcmVkaXJlY3RVcmw7XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHVybCkge1xuICAgICAgICAgIGlmIChpc0Rlc2t0b3ApIHtcbiAgICAgICAgICAgIG1haW5Db250YWluZXI/LmNsYXNzTGlzdD8ucmVtb3ZlKCdhY3RpdmUnKTtcbiAgICAgICAgICAgIG91dGVyTW9zdENvbnRhaW5lcj8uY2xhc3NMaXN0Py5yZW1vdmUoJ2FjdGl2ZScpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBzZXRPcGVuRHJvcERvd24oZmFsc2UpO1xuICAgICAgICAgIGRpc3BhdGNoKHRvZ2dsZU1haW5Mb2FkZXIodHJ1ZSkpO1xuICAgICAgICAgIHNldFRlcm1zT25Mb2NhbFN0b3JhZ2UoZXZlbnQ/LnRhcmdldC52YWx1ZSk7XG4gICAgICAgICAgd2luZG93LmxvY2F0aW9uLmhyZWYgPSB1cmw7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9LFxuICAgIFtpc0Rlc2t0b3AsIHJlZGlyZWN0VXJsXSxcbiAgKTtcblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGNvbnN0IG91dGVySW5wdXRFbGVtZW50ID0gZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3NlYXJjaCcpO1xuICAgIG91dGVySW5wdXRFbGVtZW50Py5hZGRFdmVudExpc3RlbmVyKCdmb2N1cycsIGhhbmRsZUZvY3VzKTtcbiAgICBvdXRlcklucHV0RWxlbWVudD8uYWRkRXZlbnRMaXN0ZW5lcigna2V5dXAnLCBoYW5kbGVQcmVzc0VudGVyKTtcbiAgICBkb2N1bWVudC5hZGRFdmVudExpc3RlbmVyKCdtb3VzZWRvd24nLCBoYW5kbGVDbGljaywgZmFsc2UpO1xuICAgIGJvZHlFbGVtZW50Py5jbGFzc0xpc3Q/LmFkZCgnc2VhcmNoLXR5cGUtZnVsbCcpO1xuICAgIHJldHVybiAoKSA9PiB7XG4gICAgICBpZiAob3V0ZXJJbnB1dEVsZW1lbnQpIHtcbiAgICAgICAgb3V0ZXJJbnB1dEVsZW1lbnQ/LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2ZvY3VzJywgaGFuZGxlRm9jdXMpO1xuICAgICAgICBvdXRlcklucHV0RWxlbWVudD8ucmVtb3ZlRXZlbnRMaXN0ZW5lcigna2V5dXAnLCBoYW5kbGVQcmVzc0VudGVyKTtcbiAgICAgIH1cbiAgICAgIGRvY3VtZW50LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ21vdXNlZG93bicsIGhhbmRsZUNsaWNrLCBmYWxzZSk7XG4gICAgICBib2R5RWxlbWVudD8uY2xhc3NMaXN0Py5yZW1vdmUoJ3NlYXJjaC10eXBlLWZ1bGwnKTtcbiAgICB9O1xuICB9LCBbXSk7XG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBhY3RpdmVQcm9kdWN0cyA9IHJlZGlyZWN0VXJsID8gcHJvZHVjdHNbY2FwaXRhbGl6ZWRQcm9kdWN0UXVlcnldIDogcHJvZHVjdHNbYWN0aXZlVGVybV07XG4gICAgc2V0U2hvd1JpZ2h0Q29sKGZhbHNlKTtcbiAgICBpZiAoYWN0aXZlVGVybSAmJiAhaXNGZXRjaGluZykge1xuICAgICBpZiAoYWN0aXZlUHJvZHVjdHM/Lmxlbmd0aCB8fCBjb3JyZWN0aW9uVGVybXM/Lmxlbmd0aCkge1xuICAgICAgICBzZXRTaG93UmlnaHRDb2wodHJ1ZSk7XG4gICAgICAgIHNldFByb2R1Y3RzRGF0YShhY3RpdmVQcm9kdWN0cyk7XG4gICAgICB9XG4gICAgfVxuICB9LCBbaXNGZXRjaGluZywgcHJvZHVjdHMsIGFjdGl2ZVRlcm0sIGlzSW5pdGlhbEZldGNoLCBjb3JyZWN0aW9uVGVybXM/Lmxlbmd0aCwgc3VnZ2VzdGlvbnMubGVuZ3RoXSk7XG5cbiAgY29uc3QgaGFuZGxlQ2xpY2sgPSAoZSkgPT4ge1xuICAgIGlmIChcbiAgICAgIHNlYXJjaEZvcm0gJiZcbiAgICAgIGRyb3BEb3duUmVmICYmXG4gICAgICBkcm9wRG93blJlZi5jdXJyZW50ICYmXG4gICAgICAhc2VhcmNoRm9ybS5jb250YWlucyhlLnRhcmdldCkgJiZcbiAgICAgICFkcm9wRG93blJlZi5jdXJyZW50LmNvbnRhaW5zKGUudGFyZ2V0KVxuICAgICkge1xuICAgICAgc2V0T3BlbkRyb3BEb3duKGZhbHNlKTtcbiAgICAgIG1haW5Db250YWluZXI/LmNsYXNzTGlzdD8ucmVtb3ZlKCdhY3RpdmUnKTtcbiAgICAgIG91dGVyTW9zdENvbnRhaW5lcj8uY2xhc3NMaXN0Py5yZW1vdmUoJ2FjdGl2ZScpO1xuICAgICAgYm9keUVsZW1lbnQ/LmNsYXNzTGlzdD8ucmVtb3ZlKCdzZWFyY2gtaXMtYWN0aXZlJyk7XG4gICAgfVxuICB9O1xuXG4gIGNvbnN0IGhhbmRsZUNsb3NlQnRuQ2xpY2sgPSAoZXZlbnQpID0+IHtcbiAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIGV2ZW50LnN0b3BQcm9wYWdhdGlvbigpO1xuICAgIHNldE9wZW5Ecm9wRG93bihmYWxzZSk7XG4gICAgbWFpbkNvbnRhaW5lcj8uY2xhc3NMaXN0Py5yZW1vdmUoJ2FjdGl2ZScpO1xuICAgIG91dGVyTW9zdENvbnRhaW5lcj8uY2xhc3NMaXN0Py5yZW1vdmUoJ2FjdGl2ZScpO1xuICAgIGJvZHlFbGVtZW50Py5jbGFzc0xpc3Q/LnJlbW92ZSgnc2VhcmNoLWlzLWFjdGl2ZScpO1xuICB9O1xuXG4gIGNvbnN0IHJlZGlyZWN0ID0gcmVkaXJlY3RzICYmIHJlZGlyZWN0c1thY3RpdmVUZXJtXSA/IHJlZGlyZWN0c1thY3RpdmVUZXJtXSA6IG51bGw7XG4gIGNvbnN0IHNob3VsZFJlY2VudGx5SXRlbVNIb3cgPSAhaXNTZWFyY2hpbmcgJiYgIWlzRmV0Y2hpbmcgJiYgIXN1Z2dlc3Rpb25zLmxlbmd0aCAmJiAhcXVlcnk7XG4gIGNvbnN0IGRyb3BEb3duT3BlbiA9IHN1Z2dlc3Rpb25zLmxlbmd0aCA+IDAgfHwgcHJvZHVjdFRlcm1zPy5sZW5ndGggPiAwO1xuICBcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAocmVkaXJlY3QpIHtcbiAgICAgIHNldFJlZGlyZWN0VXJsKHJlZGlyZWN0KTtcbiAgICB9IGVsc2Uge1xuICAgICAgc2V0UmVkaXJlY3RVcmwoJycpO1xuICAgIH1cbiAgfSwgW2FjdGl2ZVRlcm0sIHJlZGlyZWN0XSk7XG4gIFxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmIChyZWRpcmVjdFVybCkgZGlzcGF0Y2goZmV0Y2hQcm9kdWN0cyhwcm9kdWN0c1F1ZXJ5KSk7XG4gIH0sIFtyZWRpcmVjdFVybF0pO1xuXG4gIHJldHVybiAoXG4gICAgPGRpdlxuICAgICAgY2xhc3NOYW1lPXtgbmV3LXNlYXJjaC1hdXRvY29tcGxldGUgc2VhcmNoLWF1dG9jb21wbGV0ZS1kcm9wZG93biAke29wZW5Ecm9wRG93biA/ICdvcGVuJyA6ICcnfWB9XG4gICAgICByZWY9e2Ryb3BEb3duUmVmfVxuICAgID5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY2xvc2UtYnRuLXdyYXBcIj5cbiAgICAgICAgPGJ1dHRvbiBjbGFzc05hbWU9XCJjbG9zZS1zZWFyY2gtYnRuXCIgdHlwZT1cImJ1dHRvblwiIG9uQ2xpY2s9eyhlKSA9PiBoYW5kbGVDbG9zZUJ0bkNsaWNrKGUpfT5cbiAgICAgICAgICBjbG9zZVxuICAgICAgICA8L2J1dHRvbj5cbiAgICAgIDwvZGl2PlxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJzZWFyY2gtd3JhcHBlclwiPlxuICAgICAgICA8U2VhcmNoQm94IGlzRm9jdXNlZD17b3BlbkRyb3BEb3dufSBzZXRTaG93TWFpbkxvYWRlck1hc2s9e3NldFNob3dNYWluTG9hZGVyTWFza30gaXNTZWFyY2hGdWxsVHlwZSAvPlxuICAgICAgPC9kaXY+XG4gICAgICA8TWFpbkxvYWRlciBzaG93TWFpbkxvYWRlck1hc2s9e3Nob3dNYWluTG9hZGVyTWFza30gLz5cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgc2VhcmNoLWF1dG9jb21wbGV0ZS13cmFwcGVyICBzZWFyY2gtcmVzdWx0YH0+XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiY29sLWxlZnRcIiBhcmlhLWJ1c3k9e2lzU2VhcmNoaW5nfT5cbiAgICAgICAgICB7IWRyb3BEb3duT3BlbiAmJiAhYWN0aXZlVGVybSAmJiAoXG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlYXJjaC1hdXRvY29tcGxldGUtY2F0ZWdvcmllcyBzdWdnZXN0ZWQta2V5d29yZHNcIj5cbiAgICAgICAgICAgICAgPEhpbnRNZXNzYWdlIHNob3c9e3RydWV9IG1lc3NhZ2U9XCJTdGFydCB0eXBpbmcgdG8gZmluZCBhbGwgdG9vbHMgZm9yIGFsbCB0cmFkZXNcIiAvPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInNlYXJjaGVkXCI+XG4gICAgICAgICAgICAgICAgPGg1PlJlY2VudGx5IFNlYXJjaGVkPC9oNT5cbiAgICAgICAgICAgICAgICA8cD5ObyByZWNlbnQgc2VhcmNoPC9wPlxuICAgICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuICAgICAgICAgICl9XG4gICAgICAgICAge3Nob3VsZFJlY2VudGx5SXRlbVNIb3cgJiYgPD5cbiAgICAgICAgICA8UmVjZW50bHlWaWV3ZWQgLz5cbiAgICAgICAgICA8VHJlbmRpbmdTZWFyY2hlcyAvPlxuICAgICAgICAgIDwvPn1cbiAgICAgICAgICB7c3VnZ2VzdGlvbnM/Lmxlbmd0aCA/IDxDYXRlZ29yaWVzIGNhdGVnb3JpZXM9e3N1Z2dlc3Rpb25zfSBpc1NlYXJjaEZ1bGxUeXBlIC8+IDogbnVsbH1cblxuICAgICAgICAgIDxIYW1tZXJTcGlubmVyIHNob3c9e2lzU2VhcmNoaW5nfSAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgICAgPGRpdlxuICAgICAgICAgIGNsYXNzTmFtZT17YGNvbC1yaWdodCAke2FjdGl2ZVRlcm0gPyAnc2hvdycgOiAnJ30gJHtwcm9kdWN0cyAmJiBwcm9kdWN0c1thY3RpdmVUZXJtXSA/ICdzZWFyY2gtcmVzdWx0JyA6ICcnfWB9XG4gICAgICAgICAgYXJpYS1idXN5PXtpc0ZldGNoaW5nfVxuICAgICAgICAgIHN0eWxlPXt7XG4gICAgICAgICAgICBkaXNwbGF5OiBzaG93UmlnaHRDb2wgPyAnYmxvY2snIDogJ25vbmUnLFxuICAgICAgICAgIH19XG4gICAgICAgID5cbiAgICAgICAgICA8c3BhbiBjbGFzc05hbWU9XCJvdmVybGF5XCIgb25DbGljaz17KCkgPT4gZGlzcGF0Y2goY2xlYXJQcm9kdWN0cygpKX0+PC9zcGFuPlxuICAgICAgICAgIDxOb1Jlc3VsdCBzaG93PXtub1Jlc3VsdH0gcXVlcnk9e3F1ZXJ5fSBhY3RpdmVUZXJtPXthY3RpdmVUZXJtfSAvPlxuICAgICAgICAgIDxFcnJvciBlcnJvcj17ZXJyb3J9IHF1ZXJ5PXtxdWVyeX0gZXJyb3JNc2c9e2Vycm9yTXNnfSAvPlxuICAgICAgICAgIHshbm9SZXN1bHQgPyAoXG4gICAgICAgICAgICA8UHJvZHVjdHMgcHJvZHVjdHM9e3Byb2R1Y3RzRGF0YX0gYWN0aXZlVGVybT17YWN0aXZlVGVybX0gcmVkaXJlY3RzPXtyZWRpcmVjdHN9IHJlZGlyZWN0PXtyZWRpcmVjdH0gLz5cbiAgICAgICAgICApIDogbnVsbH1cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Rpdj5cbiAgKTtcbn07XG5cbmV4cG9ydCBkZWZhdWx0IEF1dG9Db21wbGV0ZTtcbiIsImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgQXV0b0NvbXBsZXRlRnVsbFR5cGUgZnJvbSAnLi9hdXRvQ29tcGxldGVGdWxsVHlwZSc7XG5jb25zdCBBdXRvQ29tcGxldGUgPSAoKSA9PiA8QXV0b0NvbXBsZXRlRnVsbFR5cGUgLz47XG5cbmV4cG9ydCBkZWZhdWx0IEF1dG9Db21wbGV0ZTtcbiIsImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgQXV0b0NvbXBsZXRlIGZyb20gJy4vc2VhcmNoL2F1dG8tY29tcGxldGUnO1xuXG5jb25zdCBTZWFyY2ggPSAoKSA9PiAoXG4gIDw+XG4gICAgPEF1dG9Db21wbGV0ZSAvPlxuICA8Lz5cbik7XG5cbmV4cG9ydCBkZWZhdWx0IFNlYXJjaDtcbiIsImltcG9ydCBSZWFjdCBmcm9tICdyZWFjdCc7XG5pbXBvcnQgUmVhY3RET00gZnJvbSAncmVhY3QtZG9tL2NsaWVudCc7XG5pbXBvcnQgeyBQcm92aWRlciB9IGZyb20gJ3JlYWN0LXJlZHV4JztcbmltcG9ydCBjb25maWd1cmVTdG9yZSBmcm9tICcuL3N0b3JlJztcbmltcG9ydCBTZWFyY2ggZnJvbSAnLi9jb250YWluZXJzL3NlYXJjaCc7XG5cbmNvbnN0IHN0b3JlID0gY29uZmlndXJlU3RvcmUoKTtcbmNvbnN0IEFwcCA9ICgpID0+IDxTZWFyY2ggLz47XG5cbmNvbnN0IHNlYXJjaENvbnRhaW5lciA9IFJlYWN0RE9NLmNyZWF0ZVJvb3QoZG9jdW1lbnQuZ2V0RWxlbWVudEJ5SWQoJ3NlYXJjaC1hdXRvY29tcGxldGUtY29udGFpbmVyJykpO1xuXG5pZiAoc2VhcmNoQ29udGFpbmVyKSB7XG4gIHNlYXJjaENvbnRhaW5lci5yZW5kZXIoXG4gICAgPFByb3ZpZGVyIHN0b3JlPXtzdG9yZX0+XG4gICAgICA8QXBwIC8+XG4gICAgPC9Qcm92aWRlcj4sXG4gICk7XG59XG4iLCIvLyBUaGUgbW9kdWxlIGNhY2hlXG52YXIgX193ZWJwYWNrX21vZHVsZV9jYWNoZV9fID0ge307XG5cbi8vIFRoZSByZXF1aXJlIGZ1bmN0aW9uXG5mdW5jdGlvbiBfX3dlYnBhY2tfcmVxdWlyZV9fKG1vZHVsZUlkKSB7XG5cdC8vIENoZWNrIGlmIG1vZHVsZSBpcyBpbiBjYWNoZVxuXHR2YXIgY2FjaGVkTW9kdWxlID0gX193ZWJwYWNrX21vZHVsZV9jYWNoZV9fW21vZHVsZUlkXTtcblx0aWYgKGNhY2hlZE1vZHVsZSAhPT0gdW5kZWZpbmVkKSB7XG5cdFx0cmV0dXJuIGNhY2hlZE1vZHVsZS5leHBvcnRzO1xuXHR9XG5cdC8vIENyZWF0ZSBhIG5ldyBtb2R1bGUgKGFuZCBwdXQgaXQgaW50byB0aGUgY2FjaGUpXG5cdHZhciBtb2R1bGUgPSBfX3dlYnBhY2tfbW9kdWxlX2NhY2hlX19bbW9kdWxlSWRdID0ge1xuXHRcdC8vIG5vIG1vZHVsZS5pZCBuZWVkZWRcblx0XHQvLyBubyBtb2R1bGUubG9hZGVkIG5lZWRlZFxuXHRcdGV4cG9ydHM6IHt9XG5cdH07XG5cblx0Ly8gRXhlY3V0ZSB0aGUgbW9kdWxlIGZ1bmN0aW9uXG5cdF9fd2VicGFja19tb2R1bGVzX19bbW9kdWxlSWRdKG1vZHVsZSwgbW9kdWxlLmV4cG9ydHMsIF9fd2VicGFja19yZXF1aXJlX18pO1xuXG5cdC8vIFJldHVybiB0aGUgZXhwb3J0cyBvZiB0aGUgbW9kdWxlXG5cdHJldHVybiBtb2R1bGUuZXhwb3J0cztcbn1cblxuLy8gZXhwb3NlIHRoZSBtb2R1bGVzIG9iamVjdCAoX193ZWJwYWNrX21vZHVsZXNfXylcbl9fd2VicGFja19yZXF1aXJlX18ubSA9IF9fd2VicGFja19tb2R1bGVzX187XG5cbiIsIi8vIGRlZmluZSBnZXR0ZXIgZnVuY3Rpb25zIGZvciBoYXJtb255IGV4cG9ydHNcbl9fd2VicGFja19yZXF1aXJlX18uZCA9IGZ1bmN0aW9uKGV4cG9ydHMsIGRlZmluaXRpb24pIHtcblx0Zm9yKHZhciBrZXkgaW4gZGVmaW5pdGlvbikge1xuXHRcdGlmKF9fd2VicGFja19yZXF1aXJlX18ubyhkZWZpbml0aW9uLCBrZXkpICYmICFfX3dlYnBhY2tfcmVxdWlyZV9fLm8oZXhwb3J0cywga2V5KSkge1xuXHRcdFx0T2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIGtleSwgeyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGRlZmluaXRpb25ba2V5XSB9KTtcblx0XHR9XG5cdH1cbn07IiwiX193ZWJwYWNrX3JlcXVpcmVfXy5vID0gZnVuY3Rpb24ob2JqLCBwcm9wKSB7IHJldHVybiBPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwob2JqLCBwcm9wKTsgfSIsIi8vIGRlZmluZSBfX2VzTW9kdWxlIG9uIGV4cG9ydHNcbl9fd2VicGFja19yZXF1aXJlX18uciA9IGZ1bmN0aW9uKGV4cG9ydHMpIHtcblx0aWYodHlwZW9mIFN5bWJvbCAhPT0gJ3VuZGVmaW5lZCcgJiYgU3ltYm9sLnRvU3RyaW5nVGFnKSB7XG5cdFx0T2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFN5bWJvbC50b1N0cmluZ1RhZywgeyB2YWx1ZTogJ01vZHVsZScgfSk7XG5cdH1cblx0T2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsICdfX2VzTW9kdWxlJywgeyB2YWx1ZTogdHJ1ZSB9KTtcbn07IiwiLy8gbm8gYmFzZVVSSVxuXG4vLyBvYmplY3QgdG8gc3RvcmUgbG9hZGVkIGFuZCBsb2FkaW5nIGNodW5rc1xuLy8gdW5kZWZpbmVkID0gY2h1bmsgbm90IGxvYWRlZCwgbnVsbCA9IGNodW5rIHByZWxvYWRlZC9wcmVmZXRjaGVkXG4vLyBbcmVzb2x2ZSwgcmVqZWN0LCBQcm9taXNlXSA9IGNodW5rIGxvYWRpbmcsIDAgPSBjaHVuayBsb2FkZWRcbnZhciBpbnN0YWxsZWRDaHVua3MgPSB7XG5cdDQxMzogMFxufTtcblxuLy8gbm8gY2h1bmsgb24gZGVtYW5kIGxvYWRpbmdcblxuLy8gbm8gcHJlZmV0Y2hpbmdcblxuLy8gbm8gcHJlbG9hZGVkXG5cbi8vIG5vIEhNUlxuXG4vLyBubyBITVIgbWFuaWZlc3RcblxuX193ZWJwYWNrX3JlcXVpcmVfXy5PLmogPSBmdW5jdGlvbihjaHVua0lkKSB7IHJldHVybiBpbnN0YWxsZWRDaHVua3NbY2h1bmtJZF0gPT09IDA7IH07XG5cbi8vIGluc3RhbGwgYSBKU09OUCBjYWxsYmFjayBmb3IgY2h1bmsgbG9hZGluZ1xudmFyIHdlYnBhY2tKc29ucENhbGxiYWNrID0gZnVuY3Rpb24ocGFyZW50Q2h1bmtMb2FkaW5nRnVuY3Rpb24sIGRhdGEpIHtcblx0dmFyIGNodW5rSWRzID0gZGF0YVswXTtcblx0dmFyIG1vcmVNb2R1bGVzID0gZGF0YVsxXTtcblx0dmFyIHJ1bnRpbWUgPSBkYXRhWzJdO1xuXHQvLyBhZGQgXCJtb3JlTW9kdWxlc1wiIHRvIHRoZSBtb2R1bGVzIG9iamVjdCxcblx0Ly8gdGhlbiBmbGFnIGFsbCBcImNodW5rSWRzXCIgYXMgbG9hZGVkIGFuZCBmaXJlIGNhbGxiYWNrXG5cdHZhciBtb2R1bGVJZCwgY2h1bmtJZCwgaSA9IDA7XG5cdGlmKGNodW5rSWRzLnNvbWUoZnVuY3Rpb24oaWQpIHsgcmV0dXJuIGluc3RhbGxlZENodW5rc1tpZF0gIT09IDA7IH0pKSB7XG5cdFx0Zm9yKG1vZHVsZUlkIGluIG1vcmVNb2R1bGVzKSB7XG5cdFx0XHRpZihfX3dlYnBhY2tfcmVxdWlyZV9fLm8obW9yZU1vZHVsZXMsIG1vZHVsZUlkKSkge1xuXHRcdFx0XHRfX3dlYnBhY2tfcmVxdWlyZV9fLm1bbW9kdWxlSWRdID0gbW9yZU1vZHVsZXNbbW9kdWxlSWRdO1xuXHRcdFx0fVxuXHRcdH1cblx0XHRpZihydW50aW1lKSB2YXIgcmVzdWx0ID0gcnVudGltZShfX3dlYnBhY2tfcmVxdWlyZV9fKTtcblx0fVxuXHRpZihwYXJlbnRDaHVua0xvYWRpbmdGdW5jdGlvbikgcGFyZW50Q2h1bmtMb2FkaW5nRnVuY3Rpb24oZGF0YSk7XG5cdGZvcig7aSA8IGNodW5rSWRzLmxlbmd0aDsgaSsrKSB7XG5cdFx0Y2h1bmtJZCA9IGNodW5rSWRzW2ldO1xuXHRcdGlmKF9fd2VicGFja19yZXF1aXJlX18ubyhpbnN0YWxsZWRDaHVua3MsIGNodW5rSWQpICYmIGluc3RhbGxlZENodW5rc1tjaHVua0lkXSkge1xuXHRcdFx0aW5zdGFsbGVkQ2h1bmtzW2NodW5rSWRdWzBdKCk7XG5cdFx0fVxuXHRcdGluc3RhbGxlZENodW5rc1tjaHVua0lkXSA9IDA7XG5cdH1cblx0cmV0dXJuIF9fd2VicGFja19yZXF1aXJlX18uTyhyZXN1bHQpO1xufVxuXG52YXIgY2h1bmtMb2FkaW5nR2xvYmFsID0gc2VsZltcIndlYnBhY2tDaHVua3VuYnhkX3NlYXJjaF9hcHBcIl0gPSBzZWxmW1wid2VicGFja0NodW5rdW5ieGRfc2VhcmNoX2FwcFwiXSB8fCBbXTtcbmNodW5rTG9hZGluZ0dsb2JhbC5mb3JFYWNoKHdlYnBhY2tKc29ucENhbGxiYWNrLmJpbmQobnVsbCwgMCkpO1xuY2h1bmtMb2FkaW5nR2xvYmFsLnB1c2ggPSB3ZWJwYWNrSnNvbnBDYWxsYmFjay5iaW5kKG51bGwsIGNodW5rTG9hZGluZ0dsb2JhbC5wdXNoLmJpbmQoY2h1bmtMb2FkaW5nR2xvYmFsKSk7IiwiLy8gc3RhcnR1cFxuLy8gTG9hZCBlbnRyeSBtb2R1bGUgYW5kIHJldHVybiBleHBvcnRzXG4vLyBUaGlzIGVudHJ5IG1vZHVsZSBkZXBlbmRzIG9uIG90aGVyIGxvYWRlZCBjaHVua3MgYW5kIGV4ZWN1dGlvbiBuZWVkIHRvIGJlIGRlbGF5ZWRcbnZhciBfX3dlYnBhY2tfZXhwb3J0c19fID0gX193ZWJwYWNrX3JlcXVpcmVfXy5PKHVuZGVmaW5lZCwgWzQyNV0sIGZ1bmN0aW9uKCkgeyByZXR1cm4gX193ZWJwYWNrX3JlcXVpcmVfXygxMjQpOyB9KVxuX193ZWJwYWNrX2V4cG9ydHNfXyA9IF9fd2VicGFja19yZXF1aXJlX18uTyhfX3dlYnBhY2tfZXhwb3J0c19fKTtcbiJdLCJuYW1lcyI6WyJkZWZlcnJlZCIsInZhbGlkYXRlS2V5Iiwia2V5IiwicmVwbGFjZSIsInRvTG93ZXJDYXNlIiwiY2FwaXRhbGl6ZSIsInN0ciIsInNwbGl0IiwibWFwIiwid29yZCIsImNoYXJBdCIsInRvVXBwZXJDYXNlIiwic2xpY2UiLCJqb2luIiwic2V0VGVybXNPbkxvY2FsU3RvcmFnZSIsInRlcm0iLCJhcmd1bWVudHMiLCJsZW5ndGgiLCJ1bmRlZmluZWQiLCJoYXNEYXRhIiwic3RLZXkiLCJzZWFyY2hUZXJtcyIsIkpTT04iLCJwYXJzZSIsImxvY2FsU3RvcmFnZSIsImdldEl0ZW0iLCJhcnJheSIsInZhbHVlIiwiQXJyYXkiLCJpc0FycmF5IiwiX3N0ZXAiLCJsb3dlcmNhc2VWYWx1ZSIsIl9pdGVyYXRvciIsIl9jcmVhdGVGb3JPZkl0ZXJhdG9ySGVscGVyIiwicyIsIm4iLCJkb25lIiwiZXJyIiwiZSIsImYiLCJjb250YWluc0Nhc2VJbnNlbnNpdGl2ZSIsImluY2x1ZGVzIiwidW5zaGlmdCIsImZpbHRlciIsInQiLCJwb3AiLCJzZXRJdGVtIiwic3RyaW5naWZ5IiwiZ2V0UHJvZHVjdHNGcm9tTG9jYWxTdHJvcmFnZSIsInByb2R1Y3RUZXJtcyIsIndpbmRvdyIsInByb2R1Y3REYXRhIiwicmVjZW50bHlWaWV3ZWRQcm9kdWN0IiwiaXNKU09OIiwic2V0TG9jYWxTdG9yYWdlIiwiU0VBUkNIX1FVRVJZIiwiU0VBUkNIX0NMRUFSIiwiU0VBUkNIX1JFUVVFU1QiLCJTRUFSQ0hfUFJPRFVDVFMiLCJTRUFSQ0hfUkVTVUxUIiwiU0VBUkNIX1JFU1VMVF9FUlJPUiIsIlNFQVJDSF9DT1JSRUNUSU9OU19TVEFSVEVEIiwiU0VBUkNIX0NPUlJFQ1RJT05fU1VDQ0VTUyIsIlNFQVJDSF9DT1JSRUNUSU9OU19FUlJPUiIsIlBST0RVQ1RTX0NMRUFSIiwiU1VHR0VTVF9DTElDSyIsIlNVR0dFU1RfUkVRVUVTVCIsIlNVR0dFU1RfU1VDQ0VTUyIsIlNVR0dFU1RfRkFJTFVSRSIsIlRPR0dMRV9EUk9QRE9XTiIsIlVQREFURV9MU19QUk9EVUNUUyIsIlRPR0dMRV9NQUlOX0xPQURFUiIsImxzUHJvZHVjdHNJbmZvIiwiZmlsdGVyRHVwbGljYXRlcyIsImFyciIsInVuaXF1ZVRlcm1zIiwiU2V0IiwiZWxlbSIsImlzRHVwbGljYXRlIiwiaGFzIiwiYWRkIiwiaW5pdGlhbFN0YXRlIiwiX29iamVjdFNwcmVhZCIsInNob3dNYWluTG9hZGVyIiwiaXNTZWFyY2hpbmciLCJzaG93RHJvcGRvd24iLCJub1Jlc3VsdCIsInF1ZXJ5IiwicHJvZHVjdHMiLCJwcm9kdWN0c0NvdW50Iiwic3VnZ2VzdGlvbnMiLCJpc0luaXRpYWxGZXRjaCIsImZldGNoaW5nIiwiYWN0aXZlVGVybSIsInRlcm1zIiwicmVkaXJlY3RzIiwiZXJyb3IiLCJjb3JyZWN0aW9ucyIsImlzRmV0Y2hpbmdQcm9kdWN0cyIsImVycm9yTXNnRmV0Y2hpbmdQcm9kdWN0cyIsImNvbmZpZyIsInVuYnhkQ29uZmlnIiwiZXJyb3JNc2ciLCJfc3RhdGUkc3VnZ2VzdGlvbnMiLCJfc3RhdGUkY29ycmVjdGlvbnMiLCJfc3RhdGUkc3VnZ2VzdGlvbnMyIiwic3RhdGUiLCJhY3Rpb24iLCJhY3RpdmVQcm9kdWN0cyIsImNhcGl0YWxpemVkQWN0aXZlVGVybSIsInR5cGUiLCJuZXdQcm9kdWN0TFNWYWx1ZXMiLCJPYmplY3QiLCJhc3NpZ24iLCJhbGxUZXJtcyIsIl90b0NvbnN1bWFibGVBcnJheSIsInBheWxvYWQiLCJzdWdnZXN0aW9uc1Rlcm1zIiwiY29uY2F0IiwiY29ycmVjdGlvblRlcm1zIiwic3BlbGxDb3JyZWN0aW9ucyIsImluaXRpYWwiLCJkIiwiY2FwaXRhbGl6ZWRUZXJtIiwiY2FwaXRhbGl6ZVF1ZXJ5Iiwic3VnZ2VzdGlvblRlcm1zIiwicHJvZHVjdF9jb3VudCIsInF1ZXJ5UHJvZHVjdHNDb3VudCIsInJlZGlyZWN0IiwiX2RlZmluZVByb3BlcnR5IiwiZ2V0Q29ycmVjdGVkVGVybSIsIm1ldGFEYXRhIiwicXVlcnlQYXJhbXMiLCJxIiwicmVkaXJlY3RMaW5rIiwiZ2V0UmVkaXJlY3RzIiwiY29ycmVjdGVkUHJvZHVjdHMiLCJCb29sZWFuIiwidXBkYXRlZFByb2R1Y3RzIiwiX2FjdGlvbiRwYXlsb2FkIiwib2xkU3RhdGUiLCJsb2FkaW5nIiwiY29tcG9zZUVuaGFuY2VycyIsIl9fUkVEVVhfREVWVE9PTFNfRVhURU5TSU9OX0NPTVBPU0VfXyIsImNvbXBvc2UiLCJfd2luZG93IiwiQVVUT0NPTVBMRVRFX0FQSV9VUkwiLCJhdXRvQ29tcGxldGVVcmwiLCJTRUFSQ0hfQVBJX1VSTCIsImF1dG9TdWdndWVzdFVybCIsInNlYXJjaFVybCIsIlBSSUNFX0ZJRUxEIiwiU1BFQ0lBTF9QUklDRV9GSUVMRCIsIk9SSUdJTkFMX1BSSUNFX0ZJRUxEIiwiU1RPUkVTX0VOVU0iLCJfU1RPUkVTX0VOVU0iLCJIQU1NRVJfU1BJTk5FUl9CQVNFNjQiLCJwcmVwYXJlQXV0b0NvbXBsZXRlUmVzdWx0cyIsIl9wYXlsb2FkJHJlc3BvbnNlIiwiX3BheWxvYWQkcmVkaXJlY3QiLCJhbGxQcm9kdWN0cyIsInJlc3BvbnNlIiwic2VhcmNoTWV0YURhdGEiLCJkaWRZb3VNZWFuIiwic3VnZ2VzdGlvbiIsInByb2QiLCJoYXNPd25Qcm9wZXJ0eSIsIm51bWJlck9mUHJvZHVjdHMiLCJhdXRvU3VnZ2VzdCIsImRvY3R5cGUiLCJ0b3BTZWFyY2hlZFF1ZXJpZXMiLCJpbkZpZWxkc1N1Z2dlc3Rpb24iLCJmb3JtYXRQcmljZSIsInByaWNlIiwicHJpY2VTdHIiLCJ0b0ZpeGVkIiwidG9TdHJpbmciLCJpbmRleE9mIiwicHJpY2VBcnIiLCJfcmVnZW5lcmF0b3JSdW50aW1lIiwiZXhwb3J0cyIsIk9wIiwicHJvdG90eXBlIiwiaGFzT3duIiwiZGVmaW5lUHJvcGVydHkiLCJvYmoiLCJkZXNjIiwiJFN5bWJvbCIsIlN5bWJvbCIsIml0ZXJhdG9yU3ltYm9sIiwiaXRlcmF0b3IiLCJhc3luY0l0ZXJhdG9yU3ltYm9sIiwiYXN5bmNJdGVyYXRvciIsInRvU3RyaW5nVGFnU3ltYm9sIiwidG9TdHJpbmdUYWciLCJkZWZpbmUiLCJlbnVtZXJhYmxlIiwiY29uZmlndXJhYmxlIiwid3JpdGFibGUiLCJ3cmFwIiwiaW5uZXJGbiIsIm91dGVyRm4iLCJzZWxmIiwidHJ5TG9jc0xpc3QiLCJwcm90b0dlbmVyYXRvciIsIkdlbmVyYXRvciIsImdlbmVyYXRvciIsImNyZWF0ZSIsImNvbnRleHQiLCJDb250ZXh0IiwibWFrZUludm9rZU1ldGhvZCIsInRyeUNhdGNoIiwiZm4iLCJhcmciLCJjYWxsIiwiQ29udGludWVTZW50aW5lbCIsIkdlbmVyYXRvckZ1bmN0aW9uIiwiR2VuZXJhdG9yRnVuY3Rpb25Qcm90b3R5cGUiLCJJdGVyYXRvclByb3RvdHlwZSIsImdldFByb3RvIiwiZ2V0UHJvdG90eXBlT2YiLCJOYXRpdmVJdGVyYXRvclByb3RvdHlwZSIsInZhbHVlcyIsIkdwIiwiZGVmaW5lSXRlcmF0b3JNZXRob2RzIiwiZm9yRWFjaCIsIm1ldGhvZCIsIl9pbnZva2UiLCJBc3luY0l0ZXJhdG9yIiwiUHJvbWlzZUltcGwiLCJpbnZva2UiLCJyZXNvbHZlIiwicmVqZWN0IiwicmVjb3JkIiwicmVzdWx0IiwiX3R5cGVvZiIsIl9fYXdhaXQiLCJ0aGVuIiwidW53cmFwcGVkIiwicHJldmlvdXNQcm9taXNlIiwiY2FsbEludm9rZVdpdGhNZXRob2RBbmRBcmciLCJFcnJvciIsImRvbmVSZXN1bHQiLCJkZWxlZ2F0ZSIsImRlbGVnYXRlUmVzdWx0IiwibWF5YmVJbnZva2VEZWxlZ2F0ZSIsInNlbnQiLCJfc2VudCIsImRpc3BhdGNoRXhjZXB0aW9uIiwiYWJydXB0IiwibWV0aG9kTmFtZSIsInJldHVybiIsIlR5cGVFcnJvciIsImluZm8iLCJyZXN1bHROYW1lIiwibmV4dCIsIm5leHRMb2MiLCJwdXNoVHJ5RW50cnkiLCJsb2NzIiwiZW50cnkiLCJ0cnlMb2MiLCJjYXRjaExvYyIsImZpbmFsbHlMb2MiLCJhZnRlckxvYyIsInRyeUVudHJpZXMiLCJwdXNoIiwicmVzZXRUcnlFbnRyeSIsImNvbXBsZXRpb24iLCJyZXNldCIsIml0ZXJhYmxlIiwiaXRlcmF0b3JNZXRob2QiLCJpc05hTiIsImkiLCJkaXNwbGF5TmFtZSIsImlzR2VuZXJhdG9yRnVuY3Rpb24iLCJnZW5GdW4iLCJjdG9yIiwiY29uc3RydWN0b3IiLCJuYW1lIiwibWFyayIsInNldFByb3RvdHlwZU9mIiwiX19wcm90b19fIiwiYXdyYXAiLCJhc3luYyIsIlByb21pc2UiLCJpdGVyIiwia2V5cyIsInZhbCIsIm9iamVjdCIsInJldmVyc2UiLCJza2lwVGVtcFJlc2V0IiwicHJldiIsInN0b3AiLCJyb290UmVjb3JkIiwicnZhbCIsImV4Y2VwdGlvbiIsImhhbmRsZSIsImxvYyIsImNhdWdodCIsImhhc0NhdGNoIiwiaGFzRmluYWxseSIsImZpbmFsbHlFbnRyeSIsImNvbXBsZXRlIiwiZmluaXNoIiwiY2F0Y2giLCJ0aHJvd24iLCJkZWxlZ2F0ZVlpZWxkIiwiX2FycmF5TGlrZVRvQXJyYXkiLCJfYXJyYXlXaXRob3V0SG9sZXMiLCJmcm9tIiwiX2l0ZXJhYmxlVG9BcnJheSIsIm8iLCJtaW5MZW4iLCJ0ZXN0IiwiX3Vuc3VwcG9ydGVkSXRlcmFibGVUb0FycmF5IiwiX25vbkl0ZXJhYmxlU3ByZWFkIiwibGVuIiwiYXJyMiIsImFzeW5jR2VuZXJhdG9yU3RlcCIsImdlbiIsIl9uZXh0IiwiX3Rocm93IiwiY2FuY2VsU2VhcmNoIiwiY2FuY2VsVGVybXMiLCJDYW5jZWxUb2tlbiIsIkF4aW9zIiwic2VhcmNoUXVlcnkiLCJkaXNwYXRjaCIsInJlcyIsInJlaiIsImNsZWFyU2VhcmNoIiwic2VhcmNoUmVxdWVzdCIsInNlYXJjaFJlc3VsdCIsInNlYXJjaFJlc3VsdEVycm9yIiwic3VnZ2VzdGlvbkNsaWNrIiwiZmV0Y2hTZWFyY2hUZXJtcyIsIl9yZWYiLCJfY2FsbGVlIiwiX2NvbnRleHQiLCJnZXQiLCJoZWFkZXJzIiwiY2FuY2VsVG9rZW4iLCJjIiwiX3JlZjIiLCJkYXRhIiwicHJlcGFyZWREYXRhIiwic3RhdHVzQ29kZSIsIm1zZyIsImRpc3BsYXlNZXNzYWdlIiwibWVzc2FnZSIsInBhcnNlQXV0b3N1Z2dlc3RUZXJtcyIsImluRmllbGRTdWdnZXN0aW9uIiwicGFyc2VUZXJtcyIsImJyYW5kcyIsInBhcnNlQnJhbmRzIiwicHJvbW90ZWRTdWdnZXN0aW9uIiwiaXNDYW5jZWwiLCJhcmdzIiwiYXBwbHkiLCJfeCIsImZldGNoQ29ycmVjdGlvblRlcm1SZXN1bHQiLCJfcmVmNCIsImNvcnJlY3Rpb25Qcm9kdWN0U2VhcmNoU3VjY2Vzc3MiLCJzZWFyY2hDb3JyZWN0aW9uRXJyb3IiLCJmZXRjaFRlcm1SZXN1bHQiLCJzdWdnZXN0aW9uUmVxdWVzdCIsInBhcmFtcyIsInBhZ2VTaXplIiwic3VnZ2VzdGlvblN1Y2Nlc3MiLCJzdWdnZXN0aW9uRmFpbHVyZSIsIl90ZXJtJGF1dG9zdWdnZXN0IiwiYXV0b3N1Z2dlc3QiLCJ0cmltIiwiX3Rlcm0kYXV0b3N1Z2dlc3QyIiwicmVzdWx0X3VuYnhkX2RvdWJsZSIsImluRmllbGRzIiwic3VmZml4IiwiYnJhbmRJbiIsImJpIiwiYnJhbmQiLCJiciIsInRvZ2dsZURyb3Bkb3duIiwidXBkYXRlTFNQcm9kdWN0cyIsInRvZ2dsZU1haW5Mb2FkZXIiLCJzdHlsZSIsIlJlYWN0IiwiY2xhc3NOYW1lIiwic2hvdyIsImNvdW50IiwiY2F0ZWdvcmllcyIsImlzU2VhcmNoRnVsbFR5cGUiLCJ1c2VEaXNwYXRjaCIsIl91c2VTZWxlY3RvciIsInVzZVNlbGVjdG9yIiwiaXNGZXRjaGluZyIsIl91c2VTdGF0ZTIiLCJfc2xpY2VkVG9BcnJheSIsInVzZVN0YXRlIiwic2V0UmVkaXJlY3RWYWx1ZSIsInVzZUVmZmVjdCIsIkhpbnRNZXNzYWdlIiwiY2F0IiwiaWR4IiwiX2NhdCR0ZXJtIiwiaXNBY3RpdmUiLCJvbkNsaWNrIiwidXJsIiwicHJldmVudERlZmF1bHQiLCJlbmNvZGVVUklDb21wb25lbnQiLCJsb2NhdGlvbiIsImhyZWYiLCJoYW5kbGVDbGljayIsIlByb2R1Y3RzQ291bnQiLCJTcGlubmVyIiwibWFyZ2luTGVmdCIsInJlbmRlckNhdGVnb3JpZXMiLCJzcmMiLCJhbHQiLCJNYWluTG9hZGVyIiwic2hvd01haW5Mb2FkZXJNYXNrIiwiZmluYWxFcnJvck1lc3NhZ2UiLCJmaW5hbFByaWNlIiwiY3VycmVuY3kiLCJmb3JtYXQiLCJjbGFzcyIsInByb3BzIiwic2F2aW5ncyIsIm1hdGNoIiwib3JpZ2luYWxQcmljZSIsInNhdmluZ1ByaWNlIiwicGVyY2VudGFnZVNhdmVkIiwiTWF0aCIsImZsb29yIiwiY2FsY3VsYXRlUGVyY2VudGFnZVNhdmVkIiwicmVuZGVyTGFiZWwiLCJsYWJlbCIsInNlYXJjaCIsInBsYWNlaG9sZGVyIiwidW5ieGRBbWFzdHlMYWJlbFRvcFJpZ2h0IiwidW5ieGRBbWFzdHlMYWJlbFRvcExlZnQiLCJ1bmJ4ZEFtYXN0eUxhYmVsQm90dG9tUmlnaHQiLCJhbWFzdHlMYWJlbHMiLCJwb3NpdGlvbiIsInVuYnhkQW1hc3R5TGFiZWxCb3R0b21MZWZ0IiwiaW1nUmVmIiwidXNlUmVmIiwiaW1nVXJsIiwiZXh0c1JlZ2V4IiwiYXR0ciIsImltYWdlVXJsIiwiZ2V0SW1nVXJsIiwicmVmIiwib25FcnJvciIsImN1cnJlbnQiLCJhbWFzdHlMYWJlbFN0eWxlcyIsImxhYmVsUG9zaXRpb24iLCJjb21tb25TdHlsZXMiLCJ3aWR0aCIsImhlaWdodCIsInpJbmRleCIsImJhY2tncm91bmRTaXplIiwiYmFja2dyb3VuZFJlcGVhdCIsImNvbW1vblRvcFN0eWxlcyIsInRvcCIsImJhY2tncm91bmRQb3NpdGlvbiIsImNvbW1vbkJvdHRvbVN0eWxlcyIsImJvdHRvbSIsImxlZnQiLCJyaWdodCIsImdldEFtYXN0eUxhYmVsU3R5bGVzIiwiYmFja2dyb3VuZEltYWdlIiwidGVtcGxhdGUiLCJnZXRMYWJlbEltZyIsIl9kYXRhJG5vdEZvclNhbGUiLCJfZGF0YSRub3RGb3JTYWxlMiIsIl9kYXRhJG5vdEZvclNhbGUzIiwiX2dldFByb2R1Y3RQcmljZUF0dHJpIiwic3RvcmVOYW1lIiwic3RvcmVTcGVjaWFsUHJpY2UiLCJzcGVjaWFsUHJpY2UiLCJzdG9yZU9yaWdpbmFsUHJpY2UiLCJzdG9yZUZpZWxkIiwiZ2V0UHJvZHVjdFByaWNlQXR0cmlidXRlcyIsImhhbmRsZVRyYWNraW5nIiwiZXYiLCJBRUMiLCJndG0iLCJkYXRhTGF5ZXIiLCJjbGljayIsImN1cnJlbnRUYXJnZXQiLCJldmVudCIsInNlYXJjaFF1ZXJ5UGF5bG9hZCIsInBsYWNlaG9sZGVyVXJsIiwicGxhY2Vob2xkZXJJbWdVcmwiLCJ0eXBlSWQiLCJza3UiLCJlbmRzV2l0aCIsImlzQjJCQ3VzdG9tZXIiLCJtYWdlQ2FjaGVTdG9yYWdlIiwiaXNGb3JTYWxlIiwibm90Rm9yU2FsZSIsInNob3dQcmljZSIsInNob3dTYXZpbmciLCJzaG93U2F2ZWRMYWJlbCIsIm1hZ2VDYWNoZU9iaiIsImNvbXBhbnkiLCJoYXNfY3VzdG9tZXJfY29tcGFueSIsInJvbGUiLCJpZCIsInByb2R1Y3RVcmwiLCJ0aXRsZSIsIlByb2R1dEltYWdlIiwicGFydE5vIiwiUHJpY2UiLCJzdG9yZUN1cnJlbmN5U3ltYm9sIiwiU2F2aW5ncyIsIlNoaXBwaW5nTGFiZWwiLCJzaGlwcGluZ0xhYmVsIiwic2hvd1Byb2R1Y3RMYWJlbHMiLCJQcm9kdWN0TGFiZWxzIiwibGFiZWxUb3AiLCJsYWJlbEJvdHRvbSIsImxhYmVsc1VybFRlbXBsYXRlIiwiZG9jdW1lbnQiLCJnZXRFbGVtZW50QnlJZCIsIlByb2R1Y3QiLCJjb3JyIiwiY2FwaXRhbGl6ZVRlcm0iLCJfdXNlU2VsZWN0b3IkY29ycmVjdGkiLCJpc0xvYWRpbmdQcm9kdWN0cyIsIkNvcnJlY3Rpb25zIiwibWFyZ2luVG9wIiwiSGFtbWVyU3Bpbm5lciIsIlByb2R1Y3RzIiwic2V0UHJvZHVjdHMiLCJpbnB1dEJveFJlZiIsImhhbmRsZUlucHV0Q2xpY2siLCJyaWdodERpdiIsInF1ZXJ5U2VsZWN0b3IiLCJkaXNwbGF5IiwiaGFuZGxlQm94SW5wdXQiLCJoYW5kbGVTdG9yYWdlQ2hhbmdlIiwiaGFuZGxlT25Mb2FkIiwidGFyZ2V0IiwicmVhZHlTdGF0ZSIsInVwZGF0ZUxvY2FsU3RhdGUiLCJyZWNlbnRseVZpZXdlZFByb2R1Y3RzIiwiaXRlbSIsImRhdGFFeGlzdHMiLCJjdXJyZW50VGltZSIsIkRhdGUiLCJwcm9kdWN0VGltZSIsImFkZGVkX2F0Iiwic2NvcGVfaWQiLCJjaGVja291dCIsIndlYnNpdGVJZCIsInNvcnQiLCJhIiwiYiIsInJlY2VudCIsInByb2R1Y3RfaWQiLCJfd2luZG93JGRvY3VtZW50IiwiYWRkRXZlbnRMaXN0ZW5lciIsIl93aW5kb3cyIiwiX3dpbmRvdyRkb2N1bWVudDIiLCJyZW1vdmVFdmVudExpc3RlbmVyIiwiX09iamVjdCRrZXlzIiwiX09iamVjdCRrZXlzMiIsImlucHV0RGl2IiwibmV3SXRlbXMiLCJwcm9kdWN0IiwiX3Byb2R1Y3QkZXh0ZW5zaW9uX2F0IiwicHJvZHVjdEluZm9GaW5hbFByaWNlIiwicHJpY2VfaW5mbyIsImZpbmFsX3ByaWNlIiwiZXh0ZW5zaW9uX2F0dHJpYnV0ZXMiLCJsYWJlbGVkX2ltYWdlIiwiaW1hZ2VzIiwibm90X2Zvcl9zYWxlIiwibWFnZW50b1NlYXJjaElucHV0UmVmIiwic2VhcmNoRm9ybSIsInNlYXJjaEJ1dHRvbiIsImlzRm9jdXNlZCIsInNldFNob3dNYWluTG9hZGVyTWFzayIsIl91c2VTZWxlY3RvciRzdWdnZXN0aSIsInNlYXJjaElucHV0UmVmIiwiaW5wdXRWYWwiLCJzZXRJbnB1dFZhbCIsIl9zZWFyY2hJbnB1dFJlZiRjdXJyZSIsImZvY3VzIiwiZnVuYyIsImRlbGF5IiwidGltZW91dElkIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJzZWFyY2hUZXh0IiwiX3NlYXJjaEZvcm0kY2xhc3NMaXN0IiwiX3NlYXJjaExhYmVsJGNsYXNzTGlzIiwiX3NlYXJjaENvbnRyb2wkY2xhc3NMIiwic2VhcmNoTGFiZWwiLCJzZWFyY2hDb250cm9sIiwicmlnaHRDb2wiLCJnZXRFbGVtZW50c0J5Q2xhc3NOYW1lIiwiY2xhc3NMaXN0IiwiY29udGFpbnMiLCJtYWtlU2VhcmNoQ2FsbCIsIl9sZW4iLCJfa2V5IiwidGhpcyIsImNsZWFyVGltZW91dCIsInNldFRpbWVvdXQiLCJoYW5kbGVDaGFuZ2UiLCJzYW5pdGl6ZWRUZXh0IiwibmV3VmFsdWUiLCJmaW5hbFZhbHVlIiwiaGFuZGxlU3VibWl0Q2xpY2siLCJzdG9wUHJvcGFnYXRpb24iLCJyZWRpcmVjdE9uU2VhcmNoUGFnZSIsIndpdGhvdXREb3RzVGVybSIsImhhbmRsZUlucHV0Rm9jdXMiLCJtYXhMZW5ndGgiLCJvbkNoYW5nZSIsIl9zZWFyY2hJbnB1dFJlZiRjdXJyZTIiLCJ0b2dnbGVNb2JpbGVTZWFyY2giLCJ0b2dnbGUiLCJTZWFyY2hJbnB1dCIsInBvcHVsYXJTZWFyY2hUZXJtcyIsInBvcHVsYXJTZWFyY2hUZXJtc0FycmF5IiwiZHJvcERvd25SZWYiLCJtYWluQ29udGFpbmVyIiwib3V0ZXJNb3N0Q29udGFpbmVyIiwiYm9keUVsZW1lbnQiLCJib2R5IiwicHJvZHVjdHNEYXRhIiwic2V0UHJvZHVjdHNEYXRhIiwiX3VzZVN0YXRlNCIsIl91c2VTdGF0ZTYiLCJzaG93UmlnaHRDb2wiLCJzZXRTaG93UmlnaHRDb2wiLCJfdXNlU3RhdGU4Iiwib3BlbkRyb3BEb3duIiwic2V0T3BlbkRyb3BEb3duIiwiX3VzZVN0YXRlMTAiLCJyZWRpcmVjdFVybCIsInNldFJlZGlyZWN0VXJsIiwiaXNEZXNrdG9wIiwiaW5uZXJXaWR0aCIsInN1Z2dlc3Rpb25zXyIsInByb2R1Y3RzUXVlcnkiLCJjYXBpdGFsaXplZFByb2R1Y3RRdWVyeSIsImhhbmRsZUZvY3VzIiwiX21haW5Db250YWluZXIkY2xhc3NMIiwiX291dGVyTW9zdENvbnRhaW5lciRjIiwiX2JvZHlFbGVtZW50JGNsYXNzTGlzIiwiaGFuZGxlUHJlc3NFbnRlciIsInVzZUNhbGxiYWNrIiwiY29uc29sZSIsImxvZyIsImtleUNvZGUiLCJfbWFpbkNvbnRhaW5lciRjbGFzc0wyIiwiX291dGVyTW9zdENvbnRhaW5lciRjMiIsInJlbW92ZSIsIl9ib2R5RWxlbWVudCRjbGFzc0xpczIiLCJvdXRlcklucHV0RWxlbWVudCIsIl9ib2R5RWxlbWVudCRjbGFzc0xpczMiLCJfbWFpbkNvbnRhaW5lciRjbGFzc0wzIiwiX291dGVyTW9zdENvbnRhaW5lciRjMyIsIl9ib2R5RWxlbWVudCRjbGFzc0xpczQiLCJzaG91bGRSZWNlbnRseUl0ZW1TSG93IiwiZHJvcERvd25PcGVuIiwiZmV0Y2hQcm9kdWN0cyIsIl9tYWluQ29udGFpbmVyJGNsYXNzTDQiLCJfb3V0ZXJNb3N0Q29udGFpbmVyJGM0IiwiX2JvZHlFbGVtZW50JGNsYXNzTGlzNSIsIlNlYXJjaEJveCIsIlJlY2VudGx5Vmlld2VkIiwiVHJlbmRpbmdTZWFyY2hlcyIsIkNhdGVnb3JpZXMiLCJjbGVhclByb2R1Y3RzIiwiTm9SZXN1bHQiLCJBdXRvQ29tcGxldGVGdWxsVHlwZSIsIkF1dG9Db21wbGV0ZSIsInN0b3JlIiwiY3JlYXRlU3RvcmUiLCJyb290UmVkdWNlciIsImFwcGx5TWlkZGxld2FyZSIsInRodW5rTWlkZGxld2FyZSIsImNvbmZpZ3VyZVN0b3JlIiwiQXBwIiwiU2VhcmNoIiwic2VhcmNoQ29udGFpbmVyIiwiUmVhY3RET00iLCJyZW5kZXIiLCJQcm92aWRlciIsIl9fd2VicGFja19tb2R1bGVfY2FjaGVfXyIsIl9fd2VicGFja19yZXF1aXJlX18iLCJtb2R1bGVJZCIsImNhY2hlZE1vZHVsZSIsIm1vZHVsZSIsIl9fd2VicGFja19tb2R1bGVzX18iLCJtIiwiTyIsImNodW5rSWRzIiwicHJpb3JpdHkiLCJub3RGdWxmaWxsZWQiLCJJbmZpbml0eSIsImZ1bGZpbGxlZCIsImoiLCJldmVyeSIsInNwbGljZSIsInIiLCJkZWZpbml0aW9uIiwicHJvcCIsImluc3RhbGxlZENodW5rcyIsImNodW5rSWQiLCJ3ZWJwYWNrSnNvbnBDYWxsYmFjayIsInBhcmVudENodW5rTG9hZGluZ0Z1bmN0aW9uIiwibW9yZU1vZHVsZXMiLCJydW50aW1lIiwic29tZSIsImNodW5rTG9hZGluZ0dsb2JhbCIsImJpbmQiLCJfX3dlYnBhY2tfZXhwb3J0c19fIl0sInNvdXJjZVJvb3QiOiIifQ==