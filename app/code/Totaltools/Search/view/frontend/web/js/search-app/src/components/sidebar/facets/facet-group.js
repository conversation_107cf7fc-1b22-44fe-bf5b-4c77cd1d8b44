import React from 'react';

import TextFacet from './facet.text';
import RangeFacet from './facet.range';
import MultilevelFacet from './facet.multilevel';

export const FacetGroup = ({ type, group }) => {
  const renderFacetGroup = (type, group) => {
    if (!group?.length) return null;
    return group.map((facet, idx) => {
      switch (type) {
        case 'multilevel':
          return <MultilevelFacet key={idx} type={type} data={facet} />;
        case 'range':
          return <RangeFacet key={idx} type={type} data={facet} />;
        case 'text':
          return <TextFacet key={idx} type={type} data={facet} />;
        default:
          return null;
      }
    });
  };

  return renderFacetGroup(type, group);
};
