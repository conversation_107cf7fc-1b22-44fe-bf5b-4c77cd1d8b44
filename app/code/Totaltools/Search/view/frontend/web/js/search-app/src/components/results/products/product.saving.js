import React from 'react';

const calculatePercentageSaved = (price, specialPrice) => {
  const difference = price - specialPrice;
  const percentageSaved = (difference / price) * 100;
  const flooredPercentage = Math.floor(percentageSaved);
  return flooredPercentage;
};

const Saving = ({ amount, currency, format, type, price, specialPrice }) => {
  const renderLabel = (props) => {
    const { amount, currency, format, type, price, specialPrice } = props;
    const formattedPrice = format(amount);
    const priceElement = Array.isArray(formattedPrice) ? (
      <>
        {formattedPrice[0]}
        <span class="decimal-dot">.</span>
        <span class="you-save-decimal">{formattedPrice[1]}</span>
      </>
    ) : (
      formattedPrice
    );
    return !type.match(/percentage/gi) ? (
      <>
        Save {''} <span className="currency-symbol">{currency}</span>
        <span className="save-price wrap">{priceElement}</span>
      </>
    ) : (
      <>
        Save {''}
        {calculatePercentageSaved(price, specialPrice)}
        <span className="price-decimal">%</span>
      </>
    );
  };

  if (amount <= 0 || !type || type === 'N' || type.match(/do not/gi)) return null;

  return (
    <span className="you-save-statement">
      <span className="wrap">{renderLabel({ amount, currency, format, type, price, specialPrice })}</span>
    </span>
  );
};

export default Saving;
