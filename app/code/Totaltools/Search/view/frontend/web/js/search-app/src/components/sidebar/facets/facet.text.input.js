import React from 'react';

export const TextFacetInput = ({ name, checked, option, onChange }) => {
  const checkboxId = `${name}_${option.term}`;
  const filterName = `${name}`;

  return (
    <li className="item" data-label={option.term}>
      <input
        type="checkbox"
        name={filterName}
        className={`checkbox no-uniform ${checked ? 'checked' : 'unchecked'}`}
        id={checkboxId}
        value={option.term}
        onChange={onChange}
        checked={checked}
      />
      <label htmlFor={checkboxId} className={`label ${checked ? 'checked' : 'unchecked'}`}>
        <span>{option?.term}</span>
        <span className="count">&nbsp;{option.count}</span>
      </label>
    </li>
  );
};
