import { FILTER_ADD, FILTER_REMOVE, FILTERS_RESET, FILTERS_CLEAR } from '../../constants';
import { FACET_TYPE_RANGE, FACET_TYPE_TEXT } from '../../constants';
import { getQueryFilters, formatPirceRange } from '../../utils';

const initialState = getQueryFilters();

const filtersReducer = (state = initialState, action) => {
  switch (action.type) {
    case FILTERS_RESET:
      let resetState = getQueryFilters();
      return Object.assign({}, resetState);

    case FILTERS_CLEAR:
      return {};

    case FILTER_ADD:
      let currentState = { ...state };
      if (action.filter) {
        let { name, value, type } = action.filter;
        const isRangeFacet = !!(type == FACET_TYPE_RANGE);
        const isTextFacet = !!(type == FACET_TYPE_TEXT);
        if (currentState.hasOwnProperty(name) && !name.match(/price/)) {
          let idx = typeof action.filter.level !== 'undefined' ? parseInt(action.filter.level) : -1;
          if (currentState[name][idx]) {
            currentState[name][idx] = value;
          } else {
            currentState[name].splice(currentState[name].length, 0, value);
          }
        } else {
          if (isRangeFacet || isTextFacet) {
            let filter = {};
            const prevFilters = { ...currentState.filter };

            if (isRangeFacet) {
              const priceRange = formatPirceRange(value);
              filter[name] = [priceRange];
            } else {
              filter[name] = prevFilters[name] ? [...new Set([...prevFilters[name], value])] : [value];
            }

            currentState['filter'] = { ...prevFilters, ...filter };
          } else {
            return { ...state, [name]: [value] };
          }
        }
      }

      return Object.assign({}, currentState);
    case FILTER_REMOVE: {
      let currentState = { ...state };
      if (action.filter) {
        const { name, value } = action.filter;
        if (currentState.hasOwnProperty(name) && currentState[name].indexOf(value) !== -1) {
          const level = typeof action.filter.level !== 'undefined' ? action.filter.level : -1;

          if (level !== -1) {
            currentState[name] = currentState[name].slice(0, level);
          } else {
            const filtered = currentState[name].filter((val) => val !== value);
            currentState[name] = filtered;
          }
        } else {
          if (currentState.hasOwnProperty('filter')) {
            const filters = { ...currentState.filter };
            if (Array.isArray(filters[name]) && filters[name].length > 0) {
              const filtered = filters[name].filter((val) => val !== value);
              if (filtered.length > 0) {
                currentState = {
                  ...currentState,
                  filter: {
                    ...filters,
                    [name]: filtered,
                  },
                };
              } else {
                const { [name]: removedFilter, ...updatedFilters } = filters;
                currentState = {
                  ...currentState,
                  filter: updatedFilters,
                };
              }
            }
          }
        }
      }
      return currentState;
    }
    default:
      return state;
  }
};

export default filtersReducer;
