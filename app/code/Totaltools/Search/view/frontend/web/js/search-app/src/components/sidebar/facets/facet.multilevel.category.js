import React, { useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { addFilter, removeFilter, fetchSearchResults, searchRequest, pageReset } from '../../../redux';

const MultilevelFacetCategory = ({ category }) => {
  const dispatch = useDispatch();
  const filters = useSelector((state) => state.filters);
  const breadcrumbs = useSelector((state) => state.search.facets.multilevel[0].breadcrumb || []);
  const urlParams = useSelector((state) => state.search.urlParams);
  const paramsRef = useRef({});

  useEffect(() => {
    paramsRef.current = { ...urlParams, ...filters };
  }, [urlParams, filters]);

  const handleClick = (checked, ev) => {
    ev.preventDefault();
    const { level, value, filter } = ev.currentTarget.dataset;
    const categoryFilter = {
      name: filter,
      value,
      type: '',
      level: level - 1,
    };

    if (!checked) {
      dispatch(addFilter(categoryFilter))
        .then(() => dispatch(pageReset()))
        .then(() => dispatch(searchRequest(paramsRef?.current)))
        .then(() => dispatch(fetchSearchResults(paramsRef?.current)))
        .then(() => {
          const leafCategories = filterLeafCategories(breadcrumbs, level);

          if (leafCategories.length) {
            const filterToAdd = {
              name: filter,
              value: leafCategories[0].name,
              level: leafCategories[0].level - 1,
            };
            dispatch(addFilter(filterToAdd));
          }
        });
    }
  };

  const filterLeafCategories = (input, comparedLevel) => {
    return Object.values(input).filter((obj) => obj.level > comparedLevel);
  };

  const handleClear = (checked, ev) => {
    ev.preventDefault();
    const { level, value, filter } = ev.currentTarget.dataset;
    const categoryFilter = {
      name: filter,
      value,
      type: '',
      level: level - 1,
    };

    if (checked) {
      dispatch(removeFilter(categoryFilter))
        .then(() => dispatch(pageReset()))
        .then(() => dispatch(searchRequest(paramsRef?.current)))
        .then((res) => dispatch(fetchSearchResults(paramsRef?.current)));
    }
  };

  const { name, level, count } = category;
  const filterName = 'category-filter';
  const categoryFilters = filters[filterName];
  const selected = categoryFilters && categoryFilters?.length > 0 && categoryFilters.includes(name);

  return (
    <li className={`level-${level} ${selected ? 'active' : ''}`}>
      <a
        onClick={(e) => handleClick(selected, e)}
        data-value={name}
        data-level={level}
        data-filter={filterName}
        data-checked={true}
      >
        <span className="name">{name}</span>
        {count ? <span className="count">{count}</span> : ''}
      </a>
      {selected ? (
        <span
          className="clear"
          onClick={(e) => handleClear(selected, e)}
          data-value={name}
          data-level={level}
          data-filter={filterName}
        >
          x
        </span>
      ) : null}
    </li>
  );
};

export default MultilevelFacetCategory;
