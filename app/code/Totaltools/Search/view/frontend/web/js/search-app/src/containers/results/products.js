import React, { useRef, useEffect } from 'react';
import { useSelector } from 'react-redux';

import Equalizer from 'react-equalizer';

import { Product } from '../../components';

const Products = ({displayMode}) => {
  const { products, spellCorrectionProducts } = useSelector((state) => state.search);

  const nodes = useRef([]);
  const priceNodes = useRef([]);

  const setRef = (ref) => {
    ref && nodes.current.push(ref);
  };

  const setPriceRef = (ref) => {
    ref && priceNodes.current.push(ref);
  };

  useEffect(() => {
    return () => {
      nodes.current = [];
      priceNodes.current = [];
    };
  }, []);

  const renderProducts = (prods) => {
    return prods.map((prod, idx) => (
      <Product key={prod.sku} position={idx + 1} data={prod} setRef={setRef} setPriceRef={setPriceRef} />
    ));
  };

  const renderedProducts = products && products.length ? products : spellCorrectionProducts;

  return renderedProducts && renderedProducts.length ? (
    <ol className="products list items product-items">
      <Equalizer className='list-container' style={{width: '100%'}} byRow={false} nodes={() => priceNodes.current}>
        <Equalizer byRow={false} nodes={() => nodes.current}>
          <Equalizer byRow={false} property="minHeight">
            {renderProducts(renderedProducts)}
          </Equalizer>
        </Equalizer>
      </Equalizer>
    </ol>
  ) : null;
};

export default Products;
