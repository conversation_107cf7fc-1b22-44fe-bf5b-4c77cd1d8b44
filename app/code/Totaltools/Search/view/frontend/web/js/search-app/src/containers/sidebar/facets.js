import React, { useRef, useState, useEffect, useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { ClearFiltersIcon, FiltersIcon, FacetGroup } from '../../components';
import { useIsMobile } from '../../hooks';
import { clearAllFilters, removeFilter, pageReset, searchRequest, fetchSearchResults, searchReset } from '../../redux';

import { FACET_TYPE_PRICE } from '../../constants';
import { getFlattenFacetFilters, getFormattedPriceRange } from '../../utils';

const hasFilters = (filters) => {
  const categoryFilters = filters?.['category-filter'];
  const commonFilters = filters['filter'] || {};
  if (categoryFilters?.length > 0 || Object.keys(commonFilters)?.length > 0) {
    return true;
  }
  return false;
};

const Facets = () => {
  const dispatch = useDispatch();
  const filters = useSelector((state) => state.filters);
  const facetFilters = getFlattenFacetFilters(filters);
  const [showApplyFilters, setShowApplyFilters] = useState(false);
  const [showFacetFilters, setShowFacetFilters] = useState(false);
  const isMobile = useIsMobile();
  const { facets, urlParams } = useSelector((state) => state.search);

  const paramsRef = useRef({});

  const renderFacetGroup = (facets) => {
    return Object?.keys(facets).map((key, idx) => {
      return <FacetGroup key={idx} type={key} group={facets[key]} />;
    });
  };

  const toggleFacets = () => setShowFacetFilters((prevState) => !prevState);

  const handleClearFilters = () => {
    dispatch(clearAllFilters())
      .then(() => dispatch(searchReset()))
      .then(() => dispatch(pageReset()))
      .then(() => dispatch(searchRequest({ q: urlParams.q })))
      .then(() => dispatch(fetchSearchResults({ q: urlParams.q })));
  };

  const handleRemoveFilter = useCallback(
    (filter) => {
      dispatch(removeFilter(filter)).then(() => {
        dispatch(pageReset());
        dispatch(searchRequest(paramsRef.current)).then(() => {
          dispatch(fetchSearchResults(paramsRef.current));
        });
      });
    },
    [dispatch],
  );

  useEffect(() => {
    paramsRef.current = { ...urlParams, ...filters };
  }, [urlParams, filters]);

  useEffect(() => {
    const scrollToTopButton = document.getElementById('btn-back-to-top');
    if (scrollToTopButton) {
      if (showApplyFilters) {
        scrollToTopButton.style.visibility = 'hidden';
      } else {
        scrollToTopButton.style.visibility = 'visible';
      }
    }
  }, [showApplyFilters]);

  const filterEffect = (filters?.['category-filter'] || []).length || Object.keys(filters?.['filter'] || {}).length;
  useEffect(() => {
    const hasFilters_ = hasFilters(filters);
    setShowApplyFilters(hasFilters_);
  }, [filterEffect]);

  return typeof facets === 'object' && Object.keys(facets).length ? (
    <>
      <div className={`instantsearch-facets-toggle ${showFacetFilters ? 'active' : ''}`} onClick={toggleFacets}>
        <span role="label">filter search</span>
      </div>
      {!isMobile || showApplyFilters ? (
        <div className="filter-by-text">
          <FiltersIcon className="filters-icon" />
          <strong>filter by</strong>
        </div>
      ) : null}
      {showApplyFilters && (
        <>
          <ol className="filters-list">
            {facetFilters.length
              ? facetFilters.map(({ name, type }, id) => {
                  const isPriceFilter = type == FACET_TYPE_PRICE;
                  return (
                    <li className="filter-list-item" key={id}>
                      <span
                        className="remove-filter-button"
                        onClick={() =>
                          handleRemoveFilter({
                            name: type,
                            value: name,
                          })
                        }
                      />
                      <span className="filter-text">{isPriceFilter ? getFormattedPriceRange(name) : name}</span>
                    </li>
                  );
                })
              : null}
          </ol>
          <div className="clear-filters" onClick={handleClearFilters}>
            <ClearFiltersIcon className="filters-icon" />
            <div>
              <span role="label">clear filters</span>
            </div>
          </div>
        </>
      )}
      <dl
        id="instant-search-facets"
        className={`filter-options instant-search-facets ${showFacetFilters ? 'active' : ''}`}
        role="tablist"
      >
        {renderFacetGroup(facets)}
      </dl>
      {isMobile && showFacetFilters && (
        <button className="filter-apply" onClick={toggleFacets}>
          apply filters
        </button>
      )}
    </>
  ) : null;
};

export default Facets;
