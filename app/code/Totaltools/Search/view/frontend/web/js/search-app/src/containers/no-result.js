import React, { useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import { searchReset, searchQuery, searchRequest, fetchSearchResults } from '../redux';

export const removePageTitle = () => {
  const pageTitle = document.querySelector('.page-title span');
  if (pageTitle) pageTitle['innerHTML'] = '';
};

export const NoResult = () => {
  const dispatch = useDispatch();
  const { corrections, urlParams, searchTerm, noResult } = useSelector((state) => {
    return {
      corrections: state.search.spellCorrection,
      urlParams: state.search.urlParams,
      searchTerm: state.search.query,
      noResult: state.search.noResult,
    };
  });

  const haveCorrections = corrections && corrections?.length;

  useEffect(() => {
    if (noResult && !haveCorrections) {
      removePageTitle();
    }
  }, [noResult, haveCorrections]);

  useEffect(() => {
    if (corrections && corrections.length) {
      const q = corrections[0];
      dispatch(searchRequest({ q })).then(() => dispatch(fetchSearchResults({ q }, false, true)));
      removePageTitle();
    }
  }, [corrections, dispatch]);

  const renderCorrections = (corrs) =>
    corrs
      .map((corr, index) => (
        <span key={index} onClick={(e) => handleClick(e, corr)}>
          {corr}
        </span>
      ))
      .reduce((prev, curr) => [prev, ', ', curr]);

  const handleClick = (e, q) => {
    e.preventDefault();

    if (unbxdConfig?.longtailSearch) {
      location.href = '/buy/' + q?.replace(' ', '-')?.toLowerCase();
      return;
    }

    if (q) {
      dispatch(searchReset());
      dispatch(searchQuery(q));
      dispatch(searchRequest({ q })).then(() => {
        dispatch(fetchSearchResults(urlParams, true));
      });

      handleTracking(q);
    }
  };

  const handleTracking = (query) => {
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push({
      event: 'unbxdSearchQuery',
      searchQueryPayload: {
        query: query,
      },
    });
  };

  return (
    <div className="instant-search-no-result">
      <div className="no-result-found">
        <h2>Sorry, No result found for '{searchTerm}'. </h2>
        {haveCorrections ? <h3>Did you mean: {renderCorrections(corrections)}</h3> : null}
      </div>
      {haveCorrections ? (
        <h1 className="page-title" style={{ textAlign: 'initial' }}>{`Search results for: '${corrections[0]}'`}</h1>
      ) : null}
    </div>
  );
};
