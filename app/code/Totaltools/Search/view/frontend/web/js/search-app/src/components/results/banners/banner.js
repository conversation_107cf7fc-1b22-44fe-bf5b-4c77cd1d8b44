import React from 'react';
import { Carousel } from 'react-responsive-carousel';
import parse from 'html-react-parser';

export const Banner = ({ banner, isHtml }) => {
  return isHtml && banner.bannerHtml ? (
    <Carousel
      showArrows={false}
      showStatus={false}
      showThumbs={false}
      autoPlay={true}
      infiniteLoop={true}
      interval={5000}
      transitionTime={1000}
      swipeable={true}
    >
      {parse(banner.bannerHtml.replace(/\r?\n/g, ''))}
    </Carousel>
  ) : (
    <div>
      <a href={banner.landingUrl}>
        <img src={banner.imageUrl} alt="Banner" />
      </a>
    </div>
  );
};
