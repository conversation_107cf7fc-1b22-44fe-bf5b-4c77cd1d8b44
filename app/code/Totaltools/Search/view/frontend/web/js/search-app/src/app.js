import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider } from 'react-redux';
import { Main } from './components';
import configureStore from './redux/store';

const store = configureStore();

const App = () => <Main />;

const searchContainer = createRoot(document.getElementById('instant-search'));

if (searchContainer) {
  searchContainer.render(
    <Provider store={store}>
      <App />
    </Provider>,
  );
} else {
  console.log('Selector not found, aborting!!!');
}
