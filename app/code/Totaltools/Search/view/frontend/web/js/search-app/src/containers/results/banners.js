import React from 'react';
import { useSelector } from 'react-redux';

import { Banner } from '../../components';

const Banners = () => {
  const banners = useSelector((state) => state.search.banners);

  const renderBanners = (banners) => {
    return banners.map((banner, idx) => (
      <Banner key={idx} banner={banner} isHtml={banner.hasOwnProperty('bannerHtml')} />
    ));
  };

  return banners.length ? <div className="instant-search-banners">{renderBanners(banners)}</div> : null;
};

export default Banners;
