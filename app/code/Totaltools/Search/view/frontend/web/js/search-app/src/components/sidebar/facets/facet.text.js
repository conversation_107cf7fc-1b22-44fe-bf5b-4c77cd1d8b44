import React, { useState, useRef, useEffect } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { addFilter, removeFilter, fetchSearchResults, searchRequest, pageReset } from '../../../redux';

import { TextFacetInput } from './facet.text.input';

const TextFacet = ({ data, type }) => {
  const dispatch = useDispatch();
  const filters = useSelector((state) => state.filters);
  const urlParams = useSelector((state) => state.search.urlParams);

  const paramsRef = useRef({});

  useEffect(() => {
    paramsRef.current = { ...urlParams, ...filters };
  }, [urlParams, filters]);

  const [expandCategories, setExpandCategories] = useState(false);

  useEffect(() => {
    const filterName = data?.name;
    const allFilters = filters?.['filter'];
    if (allFilters && Object.keys(allFilters).length) {
      setExpandCategories(allFilters?.hasOwnProperty(filterName) && allFilters?.[filterName].length);
    } else {
      setExpandCategories(false);
    }
  }, [filters, type, data.name]);

  const handleOption = (e) => {
    const { checked, value, name } = e.target;
    const filter = { name, value, type };
    switch (checked) {
      case true:
        return dispatch(addFilter(filter))
          .then(() => dispatch(pageReset()))
          .then(() => {
            return dispatch(searchRequest(paramsRef?.current));
          })
          .then(() => dispatch(fetchSearchResults(paramsRef?.current)));
      case false:
        return dispatch(removeFilter(filter))
          .then(() => dispatch(pageReset()))
          .then(() => {
            return dispatch(searchRequest(paramsRef?.current));
          })
          .then(() => {
            dispatch(fetchSearchResults(paramsRef?.current));
          });
      default:
        return false;
    }
  };

  const renderOptions = (options, name) => {
    return options.map((option, idx) => {
      const currFilter = filters?.filter?.[name];
      const checked = currFilter ? currFilter.includes(option.term) : false;

      return (
        <TextFacetInput
          key={idx}
          option={option}
          name={name}
          checked={checked || false}
          type={type}
          onChange={handleOption}
        />
      );
    });
  };

  return (
    <>
      <dt
        role="tab"
        className="filter-options-title"
        style={{ cursor: 'pointer' }}
        onClick={() => setExpandCategories(!expandCategories)}
      >
        <p>{data.displayName}</p>
        <p className="title-icon">{expandCategories ? '-' : '+'}</p>
      </dt>
      <dd
        className="filter-options-content"
        role="tabpanel"
        style={{
          display: expandCategories ? 'block' : 'none',
        }}
      >
        <ol className="items">{renderOptions(data.values, data.name)}</ol>
      </dd>
    </>
  );
};

export default TextFacet;
