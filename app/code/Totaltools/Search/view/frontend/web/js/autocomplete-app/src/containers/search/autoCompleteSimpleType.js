import React, { useRef, useEffect, useState } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import Categories from '../../components/auto-suggest/categories';
import HammerSpinner from '../../utils/HammerSpinner';
import { toggleDropdown } from '../../actions';
import { MainLoader } from '../../components/mainLoader';

import SearchBox from './search-box';

const AutoComplete = () => {
  let autocompleteRef = useRef();
  const [showMainLoaderMask, setShowMainLoaderMask] = useState(false);

  const dispatch = useDispatch();
  const { isSearching, showDropdown, suggestions: suggestions_ } = useSelector((state) => state);
  const { terms: suggestions } = suggestions_;
  useEffect(() => {
    const handleClickOutside = (e) => {
      const element = e?.target;
      const isSearchSubmitButtonClicked = element?.tagName === 'BUTTON' && element?.type === 'submit';
      if (
        autocompleteRef?.current &&
        !autocompleteRef?.current?.contains(element) &&
        !isSearchSubmitButtonClicked &&
        element?.id !== 'search'
      ) {
        dispatch(toggleDropdown(false));
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="new-search-autocomplete" ref={autocompleteRef}>
      <SearchBox setShowMainLoaderMask={setShowMainLoaderMask} />
      <div className={`search-autocomplete-dropdown ${showDropdown ? 'open' : ''}`} role="list-box">
        <MainLoader showMainLoaderMask={showMainLoaderMask} />
        <div className={`search-autocomplete-wrapper  search-result`}>
          <div className="col-left" aria-busy={isSearching}>
            {suggestions?.length ? <Categories categories={suggestions} /> : null}
            <HammerSpinner show={isSearching} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AutoComplete;
