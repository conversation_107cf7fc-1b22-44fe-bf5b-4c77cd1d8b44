import React from 'react';
import { useSelector } from 'react-redux';

const Pager = () => {
  const { activePage, perPage, totalItems, itemsCount } = useSelector((state) => ({
    activePage: state.search.pagination.active,
    perPage: state.search.pagination.perPage,
    totalItems: state.search.pagination.count,
    itemsCount: state.search.pagination.itemsCount,
  }));

  const calcRangeFrom = (a, p) => {
    return p * (a - 1) + 1;
  };

  const calcRangeTo = (a, p, i) => {
    return p * (a - 1) + i;
  };

  const rangeFrom = calcRangeFrom(activePage, perPage);
  const rangeTo = calcRangeTo(activePage, perPage, itemsCount);

  return rangeFrom && rangeTo && totalItems ? (
    <p className="toolbar-amount" id="toolbar-amount">
      Showing <span className="toolbar-number">{rangeFrom}</span>-<span className="toolbar-number">{rangeTo}</span>
      &nbsp;of&nbsp;
      <span className="toolbar-number">{totalItems}</span> results
    </p>
  ) : null;
};

export default Pager;
