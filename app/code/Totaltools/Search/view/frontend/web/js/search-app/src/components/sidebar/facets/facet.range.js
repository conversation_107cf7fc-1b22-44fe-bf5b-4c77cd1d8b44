import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import Nouislider from 'nouislider-react';
import 'nouislider/distribute/nouislider.min.css';
import { addFilter, fetchSearchResults, searchRequest, pageReset } from '../../../redux';

const RangeFacet = ({ data, type }) => {
  const dispatch = useDispatch();
  const filters = useSelector((state) => state.filters);
  const urlParams = useSelector((state) => state.search.urlParams);
  const [expandCategories, setExpandCategories] = useState(false);

  const paramsRef = useRef({});

  useEffect(() => {
    paramsRef.current = { ...urlParams, ...filters };
  }, [urlParams, filters]);

  useEffect(() => {
    const filterName = data?.name;
    const allFilters = filters?.['filter'];
    if (allFilters && Object.keys(allFilters).length) {
      setExpandCategories(allFilters?.hasOwnProperty(filterName) && allFilters?.[filterName].length);
    } else {
      setExpandCategories(false);
    }
  }, [filters, data.name, type]);

  const renderSlider = (slider) => {
    const filterName = data?.name;
    const allFilters = filters?.['filter'];
    const { values, gap } = slider;
    const maximumPrice = values?.reduce((max, priceRange) => Math.max(priceRange?.value, max), 0);
    const priceRangeEnd = maximumPrice + gap;

    let startFrom = slider.start;
    let startTo = priceRangeEnd;

    if (allFilters && Object.keys(allFilters).length) {
      if (allFilters.hasOwnProperty(filterName) && allFilters[filterName].length) {
        const priceFilter = allFilters[filterName].length ? allFilters[filterName][0] : '';
        if (priceFilter) {
          const [start, end] = priceFilter.match(/\d+/g).map(Number);
          startFrom = parseInt(start);
          startTo = parseInt(end);
        }
      }
    }

    if (slider.start === priceRangeEnd) {
      slider.start = slider.start - 1;
    }

    const priceRange = { min: slider.start, max: priceRangeEnd };
    return (
      <Nouislider
        range={priceRange}
        start={[startFrom, startTo]}
        format={{
          to: function (val) {
            return parseInt(val);
          },
          from: function (val) {
            return parseInt(val);
          },
        }}
        pips={{ mode: 'range', density: 3 }}
        tooltips={true}
        onChange={handleChange}
        connect
      />
    );
  };

  const handleChange = (range) => {
    const value = range.join('-');

    if (data?.name && value) {
      dispatch(addFilter({ name: data?.name, value, type }))
        .then(() => dispatch(pageReset()))
        .then(() => dispatch(searchRequest(paramsRef?.current)))
        .then(() => dispatch(fetchSearchResults(paramsRef?.current)));
    }
  };

  return (
    <React.Fragment>
      <dt
        role="tab"
        className="filter-options-title"
        data-role="title"
        style={{ cursor: 'pointer' }}
        onClick={() => setExpandCategories(!expandCategories)}
      >
        <p>{data.displayName}</p>
        <p className="title-icon">{expandCategories ? '-' : '+'}</p>
      </dt>
      <dd
        className="filter-options-content range-filter-content"
        role="tabpanel"
        data-role="content"
        style={{
          display: expandCategories ? 'block' : 'none',
        }}
      >
        {renderSlider(data)}
      </dd>
    </React.Fragment>
  );
};

export default RangeFacet;
