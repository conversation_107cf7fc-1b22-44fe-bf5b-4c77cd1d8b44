import React from 'react';
import Pagination from 'react-js-pagination';
import { connect } from 'react-redux';

import { searchQuery, searchRequest, fetchSearchResults } from '../../redux/actions/search-actions';
import { paramsToString } from '../../utils/url';

class ProductsPagination extends React.Component {
    constructor(props) {
        super(props);
        this.handlePagination = this.handlePagination.bind(this);
    }

    handlePagination(pageNum) {
        this.props.searchRequest({ page: pageNum }).then(res => {
            this.props.fetchSearchResults(this.props.urlParams);
        });
    }

    render() {
        const { activePage, itemsPerPage, totalItemsCount, pageRange } = this.props;

        return totalItemsCount > itemsPerPage ? (
            <div className="pages" style={{ textAlign: 'right' }}>
                <Pagination
                    hideFirstLastPages
                    hideDisabled={true}
                    innerClass="items pages-items"
                    itemClass="item"
                    linkClass="page"
                    activeClass="current"
                    linkClassPrev="action previous"
                    linkClassNext="action next"
                    prevPageText={<span>Previous</span>}
                    nextPageText={<span>Next</span>}
                    activePage={parseInt(activePage)}
                    itemsCountPerPage={itemsPerPage}
                    totalItemsCount={totalItemsCount}
                    pageRangeDisplayed={pageRange}
                    onChange={this.handlePagination}
                />
            </div>
        ) : null;
    }
}

const mapStateToProps = state => ({
    totalItemsCount: state.search.pagination.count,
    activePage: state.search.pagination.active,
    itemsPerPage: state.search.pagination.perPage,
    pageRange: state.search.pagination.range,
    urlParams: state.search.urlParams
});

const mapDispatchToProps = { searchQuery, searchRequest, fetchSearchResults };

export default connect(mapStateToProps, mapDispatchToProps)(ProductsPagination);
