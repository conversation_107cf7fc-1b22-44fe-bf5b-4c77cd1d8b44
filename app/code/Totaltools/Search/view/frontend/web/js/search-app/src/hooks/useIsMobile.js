import { useState, useEffect } from 'react';

const getIsMobile = () => window.innerWidth <= 425;

export const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(getIsMobile());
  const onResize = () => {
    setIsMobile(getIsMobile());
  };
  useEffect(() => {
    window.addEventListener('resize', onResize);
    return () => {
      window.removeEventListener('resize', onResize);
    };
  }, []);

  return isMobile;
};
