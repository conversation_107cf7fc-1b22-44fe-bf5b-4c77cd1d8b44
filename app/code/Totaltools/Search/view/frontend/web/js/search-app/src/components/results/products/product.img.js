import React, { useState } from 'react';
import { getImgUrl, getAmastyLabelStyles } from '../../../utils';

const prepareUnbxdLabels = (configLabelAttributes, unbxdFields) => {
  if (!configLabelAttributes) return [];
  const labels = configLabelAttributes.split(',');
  const allLabels = [];
  labels?.forEach((label) => {
    const [labelField, labelImage] = label.split('|');
    if (unbxdFields.hasOwnProperty(labelField)) {
      const currentunbxdFieldValue = unbxdFields?.[labelField];
      const capitalizedUnbxdFieldValue = String(currentunbxdFieldValue)?.toUpperCase();
      const isFieldValue = capitalizedUnbxdFieldValue === 'TRUE';
      const isSupplierOrder = String(unbxdFields?.supplierOrder)?.toUpperCase() === 'TRUE';
      if (
        (labelField == 'stockAvailabilityCode' && capitalizedUnbxdFieldValue == 'OX') ||
        (labelField !== 'stockAvailabilityCode' && isFieldValue)
      ) {
        if (labelField === 'preOrder' && isSupplierOrder) {
          return;
        }
        const imageUrl = `/media/amasty/amlabel/search/${labelImage}`;
        allLabels.push(imageUrl);
      }
    }
  });
  return allLabels;
};

const ProductImage = ({ data, placeholder }) => {
  const imgUrl = getImgUrl(data);
  const hoverImage = data?.hoverImage;
  const {
    unbxdAmastyLabelTopRight,
    unbxdAmastyLabelTopLeft,
    unbxdAmastyLabelBottomRight,
    unbxdAmastyLabelBottomLeft,
    ...unbxdFields
  } = data;

  const configLabelAttributes = window?.unbxdConfig?.attributeLabelsMap;
  const unbxdLabels = prepareUnbxdLabels(configLabelAttributes, unbxdFields);

  const amastyLabels = [
    { label: [unbxdAmastyLabelTopLeft, ...unbxdLabels], position: '0' },
    { label: unbxdAmastyLabelTopRight, position: '2' },
    { label: unbxdAmastyLabelBottomLeft, position: '6' },
    { label: unbxdAmastyLabelBottomRight, position: '8' },
  ];

  const isHoverEnabled = window.unbxdConfig.isHoverEnabled === '1';
  const hoverStyle = window?.unbxdConfig?.hoverStyle;

  return (
    <span className="product-image-container" style={{ width: '100%' }}>
      <span className={`product-image-wrapper ${isHoverEnabled && hoverImage ? "has-hover-image" : null} ${hoverStyle && hoverImage === "slide-left" ? "hover-style-slide-left" : 'hover-style-slide-right'}`} style={{ paddingBottom: '100%' }}>
        {amastyLabels.map(({ label, position }) => {
          if (!label) return null;
          const amastyLabelStyles = getAmastyLabelStyles(position);
          if (Array.isArray(label)) {
            return (
              <div
                className="labels-wrapper"
                style={{
                  ...amastyLabelStyles,
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                {label?.map((label_) => {
                  return (
                    <img
                      key={label}
                      className="amasty_label_image"
                      src={label_}
                      style={{
                        maxWidth: '100px',
                      }}
                    />
                  );
                })}
              </div>
            );
          } else
            return (
              <span
                key={label}
                className="amasty_label_image"
                style={{
                  ...amastyLabelStyles,
                  backgroundImage: `url(${label})`,
                }}
              />
            );
        })}

        <ProductImageWithFallback imgUrl={imgUrl} placeholder={placeholder} hoverImage={hoverImage} />
      </span>
    </span>
  );
};

export default ProductImage;

const ProductImageWithFallback = ({ imgUrl, placeholder, hoverImage }) => {
  const [imageSrc, setImageSrc] = useState(imgUrl);

  const handleImageError = () => {
    if (placeholder && imageSrc !== placeholder) {
      setImageSrc(placeholder);
    }
  };

  const isHoverEnabled = window.unbxdConfig.isHoverEnabled === '1';
  const hoverStyle = window?.unbxdConfig?.hoverStyle;

  return <>
    <img className="product-image-photo" src={imageSrc} onError={handleImageError} alt="Product Image" />
    {isHoverEnabled && hoverImage ? <img className="product-hover-image" src={hoverImage} onError={handleImageError} alt="Product Image on Hover" /> : null}
  </>;
};