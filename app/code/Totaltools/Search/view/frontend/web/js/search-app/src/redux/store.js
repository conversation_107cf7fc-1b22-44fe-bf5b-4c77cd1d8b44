import { createStore, applyMiddleware, compose } from 'redux';
import thunkMiddleware from 'redux-thunk';

import reducer from './reducers/index';
const enableChromeExtention = false;

const composeEnhancers = enableChromeExtention ? window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose : compose;

export default function configureStore(initialState) {
  return createStore(reducer, initialState, composeEnhancers(applyMiddleware(thunkMiddleware)));
}
