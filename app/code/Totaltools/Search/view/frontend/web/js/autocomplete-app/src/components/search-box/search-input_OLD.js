import React, { useEffect, useState, useCallback, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { setTermsOnLocalStorage, debounce } from '../../utils';
import {
  toggleMainLoader,
  fetchSearchTerms,
  searchQuery,
  clearSearch,
  suggestionClick,
  fetchTermResult,
} from '../../actions';

const SearchInput = ({ isFocused }) => {
  const {
    suggestions: { activeTerm, redirects },
  } = useSelector((state) => state);
  const redirect = redirects && redirects[activeTerm] ? redirects[activeTerm] : null;
  const dispatch = useDispatch();
  const searchInputRef = useRef();
  const [inputVal, setInputVal] = useState('');

  useEffect(() => {
    if (isFocused) searchInputRef?.current?.focus();
  }, [isFocused]);

  const makeSearchCall = (searchText) => {
    const searchForm = document.getElementById('search_mini_form');
    const searchLabel = document.getElementById('minisearch-label');
    const searchControl = document.getElementById('minisearch-control');
    const rightCol = document.getElementsByClassName('col-right');

    if (searchForm && !searchForm.classList.contains('opened')) {
      searchForm?.classList?.add('opened');
      searchLabel?.classList?.add('opened');
      searchControl?.classList?.add('opened');
    }

    if (rightCol.length) {
      rightCol[0].style.display = 'none';
    }

    dispatch(searchQuery(searchText));
    let query = searchText || '';
    if (!query || query.length < 2) {
      dispatch(clearSearch());
    } else {
      dispatch(clearSearch())
        .then(() => dispatch(searchQuery(query)))
        .then(() => dispatch(fetchSearchTerms(query)))
        .then(() => dispatch(suggestionClick(query)))
        .then(() => dispatch(fetchTermResult(query, true)));
    }
  };

  const handleInputChange = debounce((value) => {
    makeSearchCall(value);
  }, 500);

  const handleChange = (event) => {
    const newValue = event.target.value;
    setInputVal(newValue);
    handleInputChange(newValue);
  };

  const handleClick = () => {
    searchInputRef?.current?.focus();
  };

  const handleSubmitClick = (ev) => {
    ev.preventDefault();
    ev.stopPropagation();
    const term = inputVal || '';
    let url = encodeURIComponent(term) ? `/catalogsearch/result/?q=${encodeURIComponent(term)}` : '';
    if (redirect) {
      url = redirect;
    }
    if (url) {
      dispatch(toggleMainLoader(true));
      setTermsOnLocalStorage(term);

      window.location.href = url;
    }
    return;
  };

  return (
    <>
      <input
        ref={searchInputRef}
        className="input-text"
        type="text"
        name="search"
        value={inputVal}
        placeholder="Find your tools..."
        maxLength="128"
        role="combobox"
        onChange={handleChange}
        onClick={handleClick}
      />
      <div className="actions">
        <button type="submit" className="action search" onClick={(e) => handleSubmitClick(e)}>
          search
        </button>
      </div>
    </>
  );
};

export default SearchInput;
