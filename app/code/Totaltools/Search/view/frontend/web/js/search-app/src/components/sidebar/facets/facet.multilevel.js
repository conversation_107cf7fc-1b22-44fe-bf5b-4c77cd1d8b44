import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import MultilevelFacetCategory from './facet.multilevel.category';

const getDistinctCategories = (array) => {
  var unique = Object.create(null);
  var distinct = [];

  for (let i = 0; i < array.length; i++) {
    if (!unique[array[i].name]) {
      distinct.push(array[i]);
      unique[array[i].name] = 1;
    }
  }

  return distinct;
};

const MultilevelFacet = ({ data }) => {
  const filters = useSelector((state) => state.filters);
  const [expandCategories, setExpandCategories] = useState(false);

  useEffect(() => {
    const categoryFilters = filters?.['category-filter'];
    if (categoryFilters && categoryFilters.length) {
      setExpandCategories(true);
    } else {
      setExpandCategories(false);
    }
  }, [filters]);

  const { bucket, breadcrumb } = data;
  const categories = [...breadcrumb, ...bucket];
  const uniqueCategories = getDistinctCategories(categories);

  const renderCategories = (categories) => {
    return categories.map((category, idx) => <MultilevelFacetCategory key={idx} category={category} />);
  };

  return uniqueCategories.length ? (
    <>
      <dt
        role="tab"
        className="filter-options-title"
        data-role="title"
        style={{ cursor: 'pointer' }}
        onClick={() => setExpandCategories((prevState) => !prevState)}
      >
        <p>Categories</p>
        <p className="title-icon">{expandCategories ? '-' : '+'}</p>
      </dt>
      <dd
        className="filter-options-content filter-options-categories"
        role="tabpanel"
        data-role="content"
        style={{ display: expandCategories ? 'block' : 'none' }}
      >
        <ul>{renderCategories(uniqueCategories)}</ul>
      </dd>
    </>
  ) : null;
};

export default MultilevelFacet;
