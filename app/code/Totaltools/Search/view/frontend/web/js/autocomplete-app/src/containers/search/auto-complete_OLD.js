import React, { useEffect, useState } from 'react';
import AutoCompleteFullType from './autoCompleteFullType';
import AutoCompleteSimpleType from './autoCompleteSimpleType';
const AutoComplete = () => {
  const [isSearchFullType, setIsSearchTypeFull] = useState(false);

  useEffect(() => {
    const checkClass = () => {
      const body = document.body;
      if (body.classList.contains('search-type-full')) {
        setIsSearchTypeFull(true);
      } else {
        setIsSearchTypeFull(false);
      }
    };

    checkClass();

    const observer = new MutationObserver(checkClass);
    observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });

    return () => {
      observer.disconnect();
    };
  }, []);

  return isSearchFullType ? <AutoCompleteFullType /> : <AutoCompleteSimpleType />;
};

export default AutoComplete;
