import React from 'react';

const DisplayMode = ({ onGridButtonClick, onListButtonClick, displayMode }) => {
    const mode = window?.unbxdConfig?.catalogFrontendDisplayMode;
    const showModeButtons = mode === 'grid-list' || mode === 'list-grid';
    return (
        <>
            <div class="modes">
                <strong class="modes-label" id="modes-label">View as</strong>
                {showModeButtons ? <button onClick={onGridButtonClick} title="Grid" className={`modes-mode mode-grid ${displayMode === 'grid-list' ? 'active' : null}`}>
                    <span>Grid</span>
                </button> : null}
                {showModeButtons ? <button onClick={onListButtonClick} title="List" className={`modes-mode mode-list ${displayMode === 'list-grid' ? 'active' : null}`}>
                    <span>List</span>
                </button> : null}
            </div>
        </>
    );
};

export default DisplayMode;