import React, { useEffect, useRef } from 'react';
import Pagination from 'react-js-pagination';
import { useSelector, useDispatch } from 'react-redux';
import { searchRequest, fetchSearchResults } from '../../redux';

const ProductsPagination = ({ onPageChange }) => {
  const { totalItemsCount, activePage, itemsPerPage, pageRange, urlParams } = useSelector((state) => ({
    totalItemsCount: state.search.pagination.count,
    activePage: state.search.pagination.active,
    itemsPerPage: state.search.pagination.perPage,
    pageRange: state.search.pagination.range,
    urlParams: state.search.urlParams,
  }));

  const dispatch = useDispatch();
  const paramsRef = useRef({});

  useEffect(() => {
    paramsRef.current = { ...urlParams };
  }, [urlParams]);

  const handlePagination = (pageNum) => {
    onPageChange?.();
    dispatch(searchRequest({ page: pageNum })).then((res) => {
      dispatch(fetchSearchResults(paramsRef?.current));
    });
  };

  return totalItemsCount > itemsPerPage ? (
    <div className="pages" style={{ textAlign: 'right' }}>
      <Pagination
        hideFirstLastPages
        hideDisabled={true}
        innerClass="items pages-items"
        itemClass="item"
        linkClass="page"
        activeClass="current"
        linkClassPrev="action previous"
        linkClassNext="action next"
        prevPageText={<span>Previous</span>}
        nextPageText={<span>Next</span>}
        activePage={parseInt(activePage)}
        itemsCountPerPage={parseInt(itemsPerPage)}
        totalItemsCount={totalItemsCount}
        pageRangeDisplayed={pageRange}
        onChange={handlePagination}
      />
    </div>
  ) : null;
};

export default ProductsPagination;
