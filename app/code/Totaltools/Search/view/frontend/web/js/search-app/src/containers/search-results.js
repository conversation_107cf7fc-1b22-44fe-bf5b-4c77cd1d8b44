import React, { useRef, useState } from 'react';
import { Toolbar } from '../components';
import Products from './results/products';
import ProductsPagination from './results/pagination';
import Pager from './results/pager';
import Sorter from './results/sorter';
import Banners from './results/banners';
import DisplayMode from './results/displayMode';

export const SearchResults = ({displayMode, setDisplayMode}) => {
  const onPageChange = () => {
    window?.scrollTo({
      top: 0,
      behavior: 'smooth',
    });
  };

  const pageTitle = document.querySelector('.page-title span')?.textContent;
  const heading = unbxdConfig?.longtailSearch ? <h2 class="page-title">{pageTitle}</h2> : null;
  const categoryShortDescription = document.querySelector('.category-short-description')?.textContent;
  const shortDescription = unbxdConfig?.longtailSearch ? <div class="category-short-description">{categoryShortDescription}</div> : null;

  return (
    <div id="instant-search-results" className="instant-search-results">
      {heading}
      {shortDescription}
      <Banners />
      <Toolbar show={true}>
        <DisplayMode onGridButtonClick={() => setDisplayMode('grid-list')} onListButtonClick={() => setDisplayMode('list-grid')}  displayMode={displayMode}/>
        <Pager />
        <Sorter />
      </Toolbar>
      <Products/>
      <ProductsPagination onPageChange={onPageChange} />
    </div>
  );
};