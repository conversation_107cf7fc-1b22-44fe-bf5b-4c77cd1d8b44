import React, { useEffect, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';

import {
  searchRequest,
  fetchSearchResults,
  sortField,
  sortClear,
  pageReset,
  sortFieldAndDirection,
  perPageChange,
} from '../../redux';
import { SORT_ASC, SORT_DESC } from '../../constants';

function Sorter() {
  const dispatch = useDispatch();
  const urlSearchParams = new URLSearchParams(window.location.search);
  const queryParams = Object.fromEntries(urlSearchParams.entries());
  const { field, direction, urlParams } = useSelector((state) => ({
    field: state.search.sort.field || '',
    direction: state.search.sort.direction,
    urlParams: state.search.urlParams,
  }));
  const { perPage } = useSelector((state) => state.search);

  const paramsRef = useRef({});

  useEffect(() => {
    if (queryParams?.rows) {
      dispatch(perPageChange({ perPage: queryParams?.rows }));
    }
  }, [dispatch, queryParams?.rows]);

  useEffect(() => {
    paramsRef.current = { ...urlParams };
  }, [urlParams]);

  const handleSorting = (e) => {
    e.preventDefault();
    const fieldValue = e.target.value;
    const [field, direction] = fieldValue.split('-');

    if (field === '') {
      dispatch(sortClear())
        .then(() => dispatch(pageReset()))
        .then(() => dispatch(searchRequest(paramsRef?.current)))
        .then(() => dispatch(fetchSearchResults(paramsRef?.current)));
    }

    if (field.length && !direction.length) {
      dispatch(sortField({ field }))
        .then(() => dispatch(pageReset()))
        .then(() => dispatch(searchRequest(paramsRef?.current)))
        .then(() => dispatch(fetchSearchResults(paramsRef?.current)));
    }

    if (field.length && direction.length) {
      dispatch(sortFieldAndDirection({ sortField: field, direction }))
        .then(() => dispatch(pageReset()))
        .then(() => dispatch(searchRequest(paramsRef?.current)))
        .then(() => dispatch(fetchSearchResults(paramsRef?.current)));
    }
  };

  const handlePerPage = (e) => {
    e.preventDefault();
    const fieldValue = e.target.value;

    dispatch(perPageChange({ perPage: fieldValue }))
      .then(() => dispatch(pageReset()))
      .then(() => dispatch(searchRequest(paramsRef?.current)))
      .then(() => dispatch(fetchSearchResults(paramsRef?.current)));
  };

  return (
    <div class="toolbar-sorter-wrap">
      <div class="field limiter">
        <label class="label" for="limiter">
          <span>Show</span>
        </label>
        <div class="control">
          <select id="limiter" data-role="limiter" class="limiter-options" onChange={handlePerPage} value={perPage}>
            <option value="24">24</option>
            <option value="36" selected>
              36
            </option>
            <option value="48">48</option>
          </select>
        </div>
        <span class="limiter-text"> per page</span>
      </div>
      <div className="toolbar-sorter sorter">
        <label className="sorter-label" htmlFor="sorter">
          Sort By
        </label>
        <select
          id="sorter"
          data-role="sorter"
          className="sorter-options"
          onChange={handleSorting}
          value={`${field}-${direction}`}
        >
          <option value="">Relevance</option>
          <option value={`noOfStoresInStock-${SORT_DESC}`}>Stock - Low to High</option>
          <option value={`noOfStoresInStock-${SORT_ASC}`}>Stock - High to Low</option>
          <option value={`searchOrder-${SORT_ASC}`}>Best Sellers - Low to High</option>
          <option value={`searchOrder-${SORT_DESC}`}>Best Sellers - High to Low</option>
          <option value={`price-${SORT_ASC}`}>Price - Low to High</option>
          <option value={`price-${SORT_DESC}`}>Price - High to Low</option>
        </select>
      </div>
    </div>
  );
}

export default Sorter;
