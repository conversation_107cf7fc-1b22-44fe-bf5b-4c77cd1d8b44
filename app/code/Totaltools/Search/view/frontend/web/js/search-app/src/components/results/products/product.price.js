import React from 'react';
import Saving from './product.saving';

const formatPrice = (price) => {
  if (!price) return;
  let priceStr = price.toString();

  if (priceStr.indexOf('.') === -1) {
    return price;
  }

  let priceArr = priceStr.split('.');

  if (priceArr.length > 1 && priceArr[1] !== '00') {
    price = priceArr;
  } else {
    price = priceArr[0];
  }

  return price;
};

const Price = ({ price, specialPrice, showSavedLabel, currency, productId, setRef, sku, originalPrice, isForSale }) => {
  originalPrice = sku?.endsWith('v') ? 'configurable' : originalPrice;
  const saving =
    originalPrice && typeof originalPrice === 'number' && !isNaN(originalPrice) && originalPrice !== price
      ? (originalPrice - price)?.toFixed(2)
      : false;
  const formattedPrice = formatPrice(price?.toFixed(2));
  const originalFormattedPrice =
    typeof originalPrice === 'number' ? formatPrice(originalPrice?.toFixed(2)) : originalPrice;
  return price !== null ? (
    <div
      className="price-box price-final_price"
      data-role="priceBox"
      data-product-id={productId}
      data-price-box={'product-id-' + productId}
      ref={setRef}
    >
      <span className={specialPrice ? 'special-price' : 'normal-price'}>
        <span className="price-container price-final_price tax">
          <span
            className="price-wrapper"
            id={'product-price-' + productId}
            data-price-amount={price}
            data-price-type="finalPrice"
          >
            <span className="price">
              {price && isForSale ? (
                <>
                  <span className="currency-symbol">{currency}</span>
                  <span>
                    {Array.isArray(formattedPrice) ? (
                      <>
                        {formattedPrice?.[0]}
                        <span class="decimal-dot">.</span>
                        <span class="price-decimal">{formattedPrice?.[1]}</span>
                      </>
                    ) : (
                      formattedPrice
                    )}
                  </span>
                </>
              ) : null}
            </span>
          </span>
        </span>
      </span>
      {saving && isForSale ? (
        <span className="old-price">
          <span className="price-container price-final_price tax weee">
            <span className="price-label">Was&nbsp;</span>
            <span className="price-wrapper" data-price-type="oldPrice" data-price-amount={originalPrice}>
              <span className="price">
                <span className="currency-symbol">{currency}</span>
                <span>
                  {Array.isArray(originalFormattedPrice) ? (
                    <>
                      {originalFormattedPrice?.[0]}
                      <span class="decimal-dot">.</span>
                      <span class="price-decimal">{originalFormattedPrice?.[1]}</span>
                    </>
                  ) : (
                    originalFormattedPrice
                  )}
                </span>
              </span>
            </span>
          </span>
        </span>
      ) : null}
      {saving && originalPrice !== 'configurable' && isForSale ? (
        <Saving
          amount={saving}
          format={formatPrice}
          currency={currency}
          type={showSavedLabel}
          price={originalPrice}
          specialPrice={price}
        />
      ) : null}
    </div>
  ) : null;
};

export default Price;
