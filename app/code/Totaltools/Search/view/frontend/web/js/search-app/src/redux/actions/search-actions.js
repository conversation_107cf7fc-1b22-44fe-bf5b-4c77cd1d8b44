import {
  SEARCH_RESULT,
  <PERSON>ARCH_QUERY,
  <PERSON>ARCH_RESET,
  SEARCH_REQUEST,
  SEARCH_ERROR,
  SORT_RESULT_FIELD,
  SORT_CLEAR,
  PER_PAGE_CHANGE,
  PAGE_RESET,
  CORRECTION_SEARCH_RESULT,
  SORT_RESULT_FIELD_AND_DIR,
} from '../../constants';
import { paramsToString, setUrlParams, prepareSearchResults, updatePageTitle } from '../../utils';

export const searchQuery = (query) => ({ type: SEARCH_QUERY, query });

export const searchRequest = (params) => {
  return (dispatch) => {
    return new Promise((res) => {
      dispatch({
        type: SEARCH_REQUEST,
        urlParams: params,
      });
      res();
    });
  };
};

export const searchReset = () => ({ type: SEARCH_RESET });

export const searchResult = (payload) => ({ type: <PERSON>AR<PERSON>_RESULT, payload });
export const correctionSearchResult = (payload) => ({
  type: CORRECTION_SEARCH_RESULT,
  payload,
});

export const searchError = (payload) => ({ type: SEARCH_ERROR, ...payload });

export const sortField = (sortField) => {
  return (dispatch) => {
    return new Promise((res, rej) => {
      dispatch({ type: SORT_RESULT_FIELD, field: sortField });
      res();
    });
  };
};

export const sortFieldAndDirection = ({ sortField, direction }) => {
  return (dispatch) => {
    return new Promise((res) => {
      dispatch({
        type: SORT_RESULT_FIELD_AND_DIR,
        field: sortField,
        direction,
      });
      res();
    });
  };
};

export const perPageChange = ({ perPage }) => {
  return (dispatch) => {
    return new Promise((res) => {
      dispatch({
        type: PER_PAGE_CHANGE,
        perPage,
      });
      res();
    });
  };
};


export const sortClear = () => {
  return (dispatch) => {
    return new Promise((res) => {
      dispatch({ type: SORT_CLEAR });
      res();
    });
  };
};

export const pageReset = () => {
  return (dispatch) => {
    return new Promise((res, rej) => {
      dispatch({ type: PAGE_RESET });
      res();
    });
  };
};
let prevAbortController;

export const fetchSearchResults = (queryParams, setParams = true, isCorrectionTerm = false) => {
  return (dispatch) => {
    if (prevAbortController) {
      prevAbortController.abort();
    }

    const abortController = new AbortController();
    const signal = abortController.signal;

    prevAbortController = abortController;

    const { apiUrl, queryString } = paramsToString({ ...queryParams });

    if (setParams && !isCorrectionTerm) setUrlParams(queryString);

    if (apiUrl) {
      return fetch(apiUrl, { signal })
        .then((response) => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.json();
        })
        .then((responseData) => {
          const data_ = prepareSearchResults(responseData, isCorrectionTerm);
          if (data_?.redirect) {
            checkRedirect(data_.redirect);
          } else {
            if (isCorrectionTerm) {
              dispatch(correctionSearchResult(data_));
            } else {
              dispatch(searchResult(data_));
              if (!unbxdConfig?.longtailSearch) {
                updatePageTitle(queryParams.q);
              }
            }
          }
        })
        .catch((error) => {
          if (error.name === 'AbortError') {
            console.log('<x> Previous request canceled </x>', error);
          } else {
            dispatch(searchError({ payload: { message: error.message } }));
          }
        });
    }
  };
};

const checkRedirect = (url) => {
  if (url) {
    window.location = url;
  }
};
