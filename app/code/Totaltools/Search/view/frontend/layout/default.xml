<?xml version="1.0"?>
<!--
/**
 * @package Totaltools_Search
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 Totaltools Pty Ltd. <https://www.totaltools.com.au>
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="header-wrapper">
            <referenceBlock name="top.search">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Totaltools_Search::autocomplete/form.mini.phtml</argument>
                </action>
                <block class="Magento\Framework\View\Element\Template" name="search.autocomplete.container" template="Totaltools_Search::autocomplete/container.phtml" after="top.search" />
            </referenceBlock>
        </referenceContainer>
        <referenceContainer name="before.body.end">
            <block class="Magento\Framework\View\Element\Template" name="search.autocomplete" template="Totaltools_Search::autocomplete/script.phtml" after="-" />
        </referenceContainer>
        <referenceContainer name="head.additional">
            <block class="Totaltools\Search\Block\Search\Variables" name="search.autocomplete.variables" template="Totaltools_Search::variables.phtml" before="-" />
        </referenceContainer>

    </body>

</page>