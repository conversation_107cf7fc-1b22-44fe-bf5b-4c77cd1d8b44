<?php

namespace Totaltools\Search\Ui\DataProvider\Product;

/**
 * @category    Totaltools
 * @package     Totaltools_Search
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\Data\ProductRenderExtensionFactory;
use Magento\Catalog\Api\Data\ProductRenderInterface;
use Magento\Catalog\Ui\DataProvider\Product\ProductRenderCollectorInterface;
use Unbxd\ProductFeed\Model\Feed\DataHandler\Image as ImageDataHandler;

class LabeledImage implements ProductRenderCollectorInterface
{
    protected ProductRenderExtensionFactory $productRenderExtensionFactory;

    protected ImageDataHandler $imageHelper;

    public function __construct(
        ProductRenderExtensionFactory $productRenderExtensionFactory,
        ImageDataHandler $imageHelper
    ) {
        $this->productRenderExtensionFactory = $productRenderExtensionFactory;
        $this->imageHelper = $imageHelper;
    }

    /**
     * @inheritdoc
     */
    public function collect(ProductInterface $product, ProductRenderInterface $productRender)
    {
        $extensionAttributes = $productRender->getExtensionAttributes();

        if (!$extensionAttributes) {
            $extensionAttributes = $this->productRenderExtensionFactory->create();
        }

        $labeledImage = $product->getLabeledImage() ?: null;

        if ($labeledImage && $labeledImage != 'no_selection') {
            $labeledImageUrl = $this->imageHelper->getImageUrl(
                $product->getId(),
                $labeledImage,
                'labeled_image'
            );

            $extensionAttributes->setLabeledImage($labeledImageUrl);
        }

        $productRender->setExtensionAttributes($extensionAttributes);
    }
}
