<?php

/**
 * @package Totaltools_Search
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Search\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;

class Data
{
    /**
     * site setup
     */
    const XML_PATH_SETUP_SITE_KEY = 'unbxd_setup/general/site_key';
    const XML_PATH_SETUP_SECRET_KEY = 'unbxd_setup/general/secret_key';
    const XML_PATH_SETUP_API_KEY = 'unbxd_setup/general/api_key';

    /**
     * Common Configuration
     */
    const XML_PATH_UNBXD_SETUP_COMMON_SHOW_PRICE = 'unbxd_setup/common/show_price';
    const XML_PATH_UNBXD_SETUP_COMMON_SHOW_PRODUCT_LABELS = 'unbxd_setup/common/show_product_labels';

    /**
     * Search result configuration
     */
    const XML_PATH_UNBXD_SETUP_SEARCH_RESULT_ENDPOINT = "unbxd_setup/search_result/endpoint";
    const XML_PATH_UNBXD_SETUP_SEARCH_RESULT_ATTRIBUTE_LABELS_MAP = "unbxd_setup/search_result/attribute_labels_map";
    const XML_PATH_UNBXD_SETUP_SEARCH_RESULT_PAGE_SIZE = "unbxd_setup/search_result/page_size";
    const XML_PATH_UNBXD_SETUP_SEARCH_RESULT_ERROR_MESSAGE = "unbxd_setup/search_result/error_message";
    const XML_PATH_UNBXD_SETUP_SEARCH_RESULT_SKIP_CATEGORY = "unbxd_setup/search_result/skip_category";
    
    /**
     * Auto-complete configuration
     */
    const XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_ENDPOINT = "unbxd_setup/auto_complete/endpoint";
    const XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_AUTO_SUGGEST_ENDPOINT = "unbxd_setup/auto_complete/auto_suggest_endpoint";
    const XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_PRODUCT_FIELDS = "unbxd_setup/auto_complete/product_fields";
    const XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_POPULAR_PRODUCT_COUNT = "unbxd_setup/auto_complete/popular_product_count";
    const XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_KEYWORD_SUGGESTION_COUNT = "unbxd_setup/auto_complete/keyword_suggestion_count";
    const XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_TYPING_DELAY = "unbxd_setup/auto_complete/typing_delay";
    const XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_POPULAR_SEARCH_TERMS = "unbxd_setup/auto_complete/popular_search_terms";

    const XML_PATH_HOVER_ENABLED = 'totaltools_hover_image/general/enabled';
    const XML_PATH_HOVER_STYLE = 'totaltools_hover_image/general/hover_style';


    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * Data constructor.
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(ScopeConfigInterface $scopeConfig)
    {
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * @param null $store
     * @return string | null
     */
    public function getSiteKey($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_SETUP_SITE_KEY,
            ScopeInterface::SCOPE_STORE,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string | null
     */
    public function getSecretKey($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_SETUP_SECRET_KEY,
            ScopeInterface::SCOPE_STORE,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string | null
     */
    public function getApiKey($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_SETUP_API_KEY,
            ScopeInterface::SCOPE_STORE,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string | null
     */
    public function getAutoSuggestEndpoint($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_AUTO_SUGGEST_ENDPOINT,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string | null
     */
    public function getAutoCompleteEndpoint($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_ENDPOINT,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string
     */
    public function getSearchEndpoint($store = null): string
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_SEARCH_RESULT_ENDPOINT,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string
     */
    public function getBrowseEndpoint($store = null): string
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_SEARCH_RESULT_ENDPOINT,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string | null
     */
    public function getAutoCompletePupularProductFields($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_PRODUCT_FIELDS,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string | null
     */
    public function getAutoCompletePupularProductCount($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_POPULAR_PRODUCT_COUNT,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string | null
     */
    public function getAutoCompleteKeywordSuggestionCount($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_KEYWORD_SUGGESTION_COUNT,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string
     */
    public function getSearchPageSize($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_SEARCH_RESULT_PAGE_SIZE,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string
     */
    public function getErrorMessage($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_SEARCH_RESULT_ERROR_MESSAGE,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string
     */
    public function getAutosuggestTypingDelay($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_TYPING_DELAY,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return int
     */
    public function getShowPrice($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_COMMON_SHOW_PRICE,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return int
     */
    public function getShowProrductLabels($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_COMMON_SHOW_PRODUCT_LABELS,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string
     */
    public function getSkipCategoryList($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_SEARCH_RESULT_SKIP_CATEGORY,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @param null $store
     * @return string
     */
    public function getPopularSearchTerms($store = null)
    {
        $popularSearchTerms = $this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_AUTO_COMPLETE_POPULAR_SEARCH_TERMS,
            ScopeInterface::SCOPE_STORES,
            $store
        );

        return !empty($popularSearchTerms) ? trim($popularSearchTerms) : "";
    }

    /**
     * @return String
     */
    public function getAttributeLabelsMap($store = null)
    {
        return trim($this->scopeConfig->getValue(
            self::XML_PATH_UNBXD_SETUP_SEARCH_RESULT_ATTRIBUTE_LABELS_MAP,
            ScopeInterface::SCOPE_STORES,
            $store
        ));
    }

    /**
     * @return bool
     */
    public function isHoverEnabled()
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_HOVER_ENABLED, ScopeInterface::SCOPE_STORE);
    }

    /**
     * @return string
     */
    public function getHoverStyle()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_HOVER_STYLE, ScopeInterface::SCOPE_STORE);
    }

    /**
     * Get catalog frontend display mode from configuration
     *
     * @return string
     */
    public function getCatalogFrontendDisplayMode()
    {
        return $this->scopeConfig->getValue(
            'catalog/frontend/list_mode',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }
}
