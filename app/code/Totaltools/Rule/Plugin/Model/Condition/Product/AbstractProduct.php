<?php

namespace Totaltools\Rule\Plugin\Model\Condition\Product;

/**
 * Class AbstractProduct
 * @package Totaltools\Rule\Plugin\Model\Condition\Product
 */
class AbstractProduct
{
    /**
     * @var \Magento\Catalog\Model\ProductFactory
     */
    private $productFactory;

    /**
     * @var \Magento\Catalog\Model\ResourceModel\Product
     */
    private $productResource;

    /**
     * AbstractProduct constructor.
     * @param \Magento\Catalog\Model\ProductFactory $productFactory
     * @param \Magento\Catalog\Model\ResourceModel\Product $productResource
     */
    public function __construct(
        \Magento\Catalog\Model\ProductFactory $productFactory,
        \Magento\Catalog\Model\ResourceModel\Product $productResource
    ) {
        $this->productFactory = $productFactory;
        $this->productResource = $productResource;
    }

    /**
     * @param \Magento\Rule\Model\Condition\Product\AbstractProduct $subject
     * @param \Closure $proceed
     * @param $productId
     * @return bool
     */
    public function aroundValidateByEntityId(
        \Magento\Rule\Model\Condition\Product\AbstractProduct $subject,
        \Closure $proceed,
        $productId
    ) {
        if ('category_ids' == $subject->getAttribute()) {
            $result = $subject->validateAttribute($this->getAvailableInCategories($productId));
        } elseif ('attribute_set_id' == $subject->getAttribute()) {
            $result = $subject->validateAttribute($this->getAttributeSetId($productId));
        } else {
            $product = $this->productFactory->create()->load($productId);
            $result = $subject->validate($product);
            unset($product);
        }
        return $result;
    }

    /**
     * Retrieve category ids where product is available
     *
     * @param int $productId
     * @return array
     */
    private function getAvailableInCategories($productId)
    {
        return $this->productResource->getConnection()
            ->fetchCol(
                $this->productResource->getConnection()
                    ->select()
                    ->distinct()
                    ->from(
                        $this->productResource->getTable('catalog_category_product'),
                        ['category_id']
                    )->where(
                        'product_id = ?',
                        $productId
                    )
            );
    }

    /**
     * Get attribute set id for product
     *
     * @param int $productId
     * @return string
     */
    private function getAttributeSetId($productId)
    {
        return $this->productResource->getConnection()
            ->fetchOne(
                $this->productResource->getConnection()
                    ->select()
                    ->distinct()
                    ->from(
                        $this->productResource->getTable('catalog_product_entity'),
                        ['attribute_set_id']
                    )->where(
                        'entity_id = ?',
                        $productId
                    )
            );
    }
}
