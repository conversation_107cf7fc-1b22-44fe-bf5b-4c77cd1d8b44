<?php
/**
 * <AUTHOR> Internet (<EMAIL>)
 * @package Totaltoools_SendFriend
 */
namespace Totaltools\SendFriend\Model;

use Magento\Framework\Exception\LocalizedException as CoreException;

/**
 * SendFriend Log
 *
 * @method \Magento\SendFriend\Model\ResourceModel\SendFriend _getResource()
 * @method \Magento\SendFriend\Model\ResourceModel\SendFriend getResource()
 * @method int getIp()
 * @method \Magento\SendFriend\Model\SendFriend setIp(int $value)
 * @method int getTime()
 * @method \Magento\SendFriend\Model\SendFriend setTime(int $value)
 *
 * <AUTHOR> Core Team <<EMAIL>>
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class SendFriendRewrite extends \Magento\SendFriend\Model\SendFriend
{
    /**
     * @return $this
     * @throws CoreException
     */
    public function send()
    {
        if ($this->isExceedLimit()) {
            throw new \Magento\Framework\Exception\LocalizedException(
                __('You\'ve met your limit of %1 sends in an hour.', $this->getMaxSendsToFriend())
            );
        }

        $this->inlineTranslation->suspend();

        $message = nl2br($this->_escaper->escapeHtml($this->getSender()->getMessage()));
        $sender = [
            'name' => $this->_escaper->escapeHtml($this->getSender()->getName()),
            'email' => $this->_escaper->escapeHtml($this->getSender()->getEmail()),
        ];

        foreach ($this->getRecipients()->getEmails() as $k => $email) {
            $name = $this->getRecipients()->getNames($k);
            $product = $this->getProduct();
            $productImage = $this->_catalogImage->init($product, 'sendfriend_small_image');
            $this->_transportBuilder->setTemplateIdentifier(
                $this->_sendfriendData->getEmailTemplate()
            )->setTemplateOptions(
                [
                    'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                    'store' => $this->_storeManager->getStore()->getId(),
                ]
            )->setFrom(
                'general'
            )->setReplyTo(
                $sender['email'],
                $sender['name']
            )->setTemplateVars([
                'name' => $name,
                'email' => $email,
                'product_name' => $this->getProduct()->getName(),
                'product_url' => $this->getProduct()->getUrlInStore(),
                'message' => $message,
                'sender_name' => $sender['name'],
                'sender_email' => $sender['email'],
                'product_image' => $productImage->getType() !== null
                    ? $productImage->getUrl()
                    : $productImage->getDefaultPlaceholderUrl()
            ])->addTo(
                $email,
                $name
            );
            $transport = $this->_transportBuilder->getTransport();
            $transport->sendMessage();
        }

        $this->inlineTranslation->resume();

        $this->_incrementSentCount();

        return $this;
    }
}
