<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_ClickAndCollect
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\ClickAndCollect\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;
use Totaltools\ClickAndCollect\Helper\Reallocate;
use Magento\Framework\App\State;
use Magento\Framework\App\Area;

class ReallocateStoreOrderCommand extends Command
{

    const NAME = 'totaltools:order:reallocate';
    const ORDER_ID = 'order_id';
    const STORELOCATOR_ID = 'storelocator_id';
    const DESCRIPTION = 'Reallocate the store location of an order.';

    /**
     * @var storeLocationData
     */
    protected $storeLocationData;

    /**
     * @var State
     */
    private $state;

    /**
     * SetEnvironmentCommand constructor.
     *
     * @param Data $storeLocationData
     * @param State $state
     */
    public function __construct(
        Reallocate\Proxy $storeLocationData,
        State $state
    )
    {
        parent::__construct();
        $this->storeLocationData = $storeLocationData;
        $this->state = $state;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $options = [
            new InputOption(
                self::ORDER_ID,
                null,
                InputOption::VALUE_REQUIRED,
                'Order ID'
            ),
            new InputOption(
                self::STORELOCATOR_ID,
                null,
                InputOption::VALUE_REQUIRED,
                'Store Locator ID'
            )
        ];

        $this
            ->setName(static::NAME)
            ->setDescription(static::DESCRIPTION)
            ->setDefinition($options);

        parent::configure();
    }

    /**
     * {@inheritdoc}
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $this->state->setAreaCode(Area::AREA_ADMINHTML);
        $order_id = $input->getOption(self::ORDER_ID);
        $storelocator_id = $input->getOption(self::STORELOCATOR_ID);
        $result = $this->storeLocationData->updateOrderStoreLocation($order_id, $storelocator_id);
        if(count($result['error']) > 0) {
            foreach ($result['error'] as $message) {
                $output->writeln('<error>'.$message.'</error>');
            }
        } else {
            foreach ($result['success'] as $message) {
                $output->writeln('<info>'.$message.'</info>');
            }
        }
    }
}