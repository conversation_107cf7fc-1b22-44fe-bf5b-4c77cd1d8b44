<?php

namespace Totaltools\ClickAndCollect\Model;

use Totaltools\ClickAndCollect\Model\Api\Data\StatusUpdate;

/**
 * Class StatusUpdater
 * @package Totaltools\ClickAndCollect\Model
 */
class StatusUpdater implements \Totaltools\ClickAndCollect\Api\StatusUpdaterInterface
{
    private $state = [
        StatusUpdate::STATUS_ORDER_PLACED => 0,
        StatusUpdate::STATUS_DESPATCH_IN_PROGRESS => 1,
        StatusUpdate::STATUS_READY_FOR_PICKUP => 2,
        StatusUpdate::STATUS_UNTRACKABLE => 3,
    ];

    /**
     * @var \Shippit\Shipping\Model\ResourceModel\Sync\OrderFactory
     */
    private $shippitOrderFactory;

    /**
     * @var \Magento\Sales\Api\OrderRepositoryInterface
     */
    private $orderRepository;

    /**
     * @var \Magento\Framework\Serialize\Serializer\Json
     */
    private $jsonEncoder;

    /**
     * @var \Magento\Sales\Model\Order\Status\HistoryFactory
     */
    private $historyFactory;

    /**
     * @var \Magento\Sales\Api\OrderStatusHistoryRepositoryInterface
     */
    private $orderStatusHistoryRepository;

    /**
     * StatusUpdater constructor.
     * @param \Shippit\Shipping\Model\Sync\OrderFactory $shippitOrderFactory
     * @param \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
     * @param \Magento\Framework\Serialize\Serializer\Json $jsonEncoder
     * @param \Magento\Sales\Model\Order\Status\HistoryFactory $historyFactory
     * @param \Magento\Sales\Api\OrderStatusHistoryRepositoryInterface $orderStatusHistoryRepository
     */
    public function __construct(
        \Shippit\Shipping\Model\Sync\OrderFactory $shippitOrderFactory,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository,
        \Magento\Framework\Serialize\Serializer\Json $jsonEncoder,
        \Magento\Sales\Model\Order\Status\HistoryFactory $historyFactory,
        \Magento\Sales\Api\OrderStatusHistoryRepositoryInterface $orderStatusHistoryRepository
    ) {
        $this->shippitOrderFactory = $shippitOrderFactory;
        $this->orderRepository = $orderRepository;
        $this->jsonEncoder = $jsonEncoder;
        $this->historyFactory = $historyFactory;
        $this->orderStatusHistoryRepository = $orderStatusHistoryRepository;
    }

    /**
     * @param \Totaltools\ClickAndCollect\Api\Data\StatusUpdateInterface $statusUpdate
     * @return bool|false|mixed|string|\Totaltools\ClickAndCollect\Api\Data\StatusUpdateInterface
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function update(\Totaltools\ClickAndCollect\Api\Data\StatusUpdateInterface $statusUpdate)
    {
        $result = [];
        $ccCurrentState = $statusUpdate->getCurrentState();
        if (!$ccCurrentState || !array_key_exists($ccCurrentState, $this->state)) {
            $result = array_merge($result, [
                'error' => [
                    'message' => __('Current state of shipment not found or invalid. Please check or try again later.')
                ]
            ]);
        }
        if (array_key_exists($ccCurrentState, $this->state)) {
            $state = $this->state[$ccCurrentState];
            $trackingNumber = $statusUpdate->getTrackingNumber();
            $shippitSyncOrder = $this->shippitOrderFactory->create()->load($trackingNumber, 'tracking_number');
            if (!$shippitSyncOrder->getData()) {
                $result = array_merge($result, [
                    'error' => [
                        'message' => __('Unable to retrieve order by tracking number (%1). Please check and try again later.', $trackingNumber)
                    ]
                ]);
            }
            if ($shippitSyncOrder->getOrderId()) {
                $orderId = $shippitSyncOrder->getOrderId();
                $order = $this->orderRepository->get($orderId);
                $order->setCcCurrentState($state);
                $this->orderRepository->save($order);
                $orderState = $order->getState();
                $orderHistorys = $statusUpdate->getStatusHistory();
                foreach ($orderHistorys as $orderHistory) {
                    $message = __('Shippit C&C Order - Status: %status|Time: %time',
                        [
                            'status' => $orderHistory->getStatus(),
                            'time' => $orderHistory->getTime()
                        ]);
                    $this->addCommentToHistory($orderId, $orderState, $message);
                }
            }
        }

        return $this->jsonEncoder->serialize(array_merge(
            $result,
            [
                'current_state' => $ccCurrentState
            ]
        ));
    }

    /**
     * @param $orderId
     * @param $orderState
     * @param $comment
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    private function addCommentToHistory($orderId, $orderState, $comment)
    {
        /** @var \Magento\Sales\Model\Order\Status\History $history */
        $history = $this->historyFactory->create();
        $history->setParentId($orderId)
            ->setComment($comment)
            ->setEntityName('order')
            ->setStatus($orderState)
            ->save();

        $this->orderStatusHistoryRepository->save($history);
    }
}
