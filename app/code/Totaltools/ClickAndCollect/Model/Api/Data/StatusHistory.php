<?php

namespace Totaltools\ClickAndCollect\Model\Api\Data;

use Totaltools\ClickAndCollect\Api\Data\StatusHistoryInterface;

/**
 * Class StatusHistory
 * @package Totaltools\ClickAndCollect\Model\Api\Data
 */
class StatusHistory extends \Magento\Framework\Model\AbstractModel implements StatusHistoryInterface
{
    /**
     * Status.
     *
     * {@inheritdoc}
     */
    public function getStatus()
    {
        return $this->getData(StatusHistoryInterface::STATUS);
    }

    /**
     * Set status.
     *
     * {@inheritdoc}
     */
    public function setStatus($status)
    {
        return $this->setData(StatusHistoryInterface::STATUS, $status);
    }

    /**
     * Date time.
     *
     * {@inheritdoc}
     */
    public function getTime()
    {
        return $this->getData(StatusHistoryInterface::TIME);
    }

    /**
     * Set date time.
     *
     *{@inheritdoc}
     */
    public function setTime($time)
    {
        return $this->setData(StatusHistoryInterface::TIME, $time);
    }
}
