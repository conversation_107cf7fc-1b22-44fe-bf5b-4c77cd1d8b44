<?php

namespace Totaltools\ClickAndCollect\Model\Api\Data;

use Totaltools\ClickAndCollect\Api\Data\StatusUpdateInterface;

/**
 * Class StatusUpdate
 * @package Totaltools\ClickAndCollect\Model\Api\Data
 */
class StatusUpdate extends \Magento\Framework\Model\AbstractModel implements StatusUpdateInterface
{
    const STATUS_ORDER_PLACED = 'order_placed';
    const STATUS_DESPATCH_IN_PROGRESS = 'despatch_in_progress';
    const STATUS_READY_FOR_PICKUP = 'ready_for_pickup';
    const STATUS_UNTRACKABLE = 'untrackable';

    /**
     * {@inheritdoc}
     */
    public function getTrackingNumber()
    {
        return $this->getData(StatusUpdateInterface::SU_TRACKING_NUMBER);
    }

    /**
     * {@inheritdoc}
     */
    public function setTrackingNumber($trackingNumber)
    {
        return $this->setData(StatusUpdateInterface::SU_TRACKING_NUMBER, $trackingNumber);
    }

    /**
     * Expected delivery date.
     *
     * {@inheritdoc}
     */
    public function getExpectedDeliveryDate()
    {
        return $this->getData(StatusUpdateInterface::SU_EXPECTED_DELIVERY_DATE);
    }

    /**
     * Set exepected delivery date.
     *
     * {@inheritdoc}
     */
    public function setExpectedDeliveryDate($expectedDeliveryDate)
    {
        return $this->setData(StatusUpdateInterface::SU_EXPECTED_DELIVERY_DATE, $expectedDeliveryDate);
    }

    /**
     * Tracking url.
     *
     * {@inheritdoc}
     */
    public function getTrackingUrl()
    {
        return $this->getData(StatusUpdateInterface::SU_TRACKING_URL);
    }

    /**
     * Set tracking url.
     *
     * {@inheritdoc}
     */
    public function setTrackingUrl($trackingUrl)
    {
        return $this->setData(StatusUpdateInterface::SU_TRACKING_URL, $trackingUrl);
    }

    /**
     * Merchant url.
     *
     * {@inheritdoc}
     */
    public function getMerchantUrl()
    {
        return $this->getData(StatusUpdateInterface::SU_MERCHANT_URL);
    }

    /**
     * Set merchant url.
     *
     * {@inheritdoc}
     */
    public function setMerchantUrl($merchantUrl)
    {
        return $this->setData(StatusUpdateInterface::SU_MERCHANT_URL, $merchantUrl);
    }

    /**
     * Current state.
     *
     * {@inheritdoc}
     */
    public function getCurrentState()
    {
        return $this->getData(StatusUpdateInterface::SU_CURRENT_STATE);
    }

    /**
     * Set current state.
     *
     * {@inheritdoc}
     */
    public function setCurrentState($currentState)
    {
        return $this->setData(StatusUpdateInterface::SU_CURRENT_STATE, $currentState);
    }

    /**
     * Retailer order number.
     *
     * {@inheritdoc}
     */
    public function getRetailerOrderNumber()
    {
        return $this->getData(StatusUpdateInterface::SU_RETAILER_ORDER_NUMBER);
    }

    /**
     * Set retailer order number.
     *
     * {@inheritdoc}
     */
    public function setRetailerOrderNumber($retailerOrderNumber)
    {
        return $this->setData(StatusUpdateInterface::SU_RETAILER_ORDER_NUMBER, $retailerOrderNumber);
    }

    /**
     * Retailer reference.
     *
     * {@inheritdoc}
     */
    public function getRetailerReference()
    {
        return $this->getData(StatusUpdateInterface::SU_RETAILER_REFERENCE);
    }

    /**
     * Set retailer reference.
     *
     * {@inheritdoc}
     */
    public function setRetailerReference($retailerReference)
    {
        return $this->setData(StatusUpdateInterface::SU_RETAILER_REFERENCE, $retailerReference);
    }

    /**
     * Courier name.
     *
     * {@inheritdoc}
     */
    public function getCourierName()
    {
        return $this->getData(StatusUpdateInterface::SU_COURIER_NAME);
    }

    /**
     * Set courier name.
     *
     * {@inheritdoc}
     */
    public function setCourierName($courierName)
    {
        return $this->setData(StatusUpdateInterface::SU_COURIER_NAME, $courierName);
    }

    /**
     * Courier job id.
     *
     * {@inheritdoc}
     */
    public function getCourierJobId()
    {
        return $this->getData(StatusUpdateInterface::SU_COURIER_JOB_ID);
    }

    /**
     * Set courier job id.
     *
     * {@inheritdoc}
     */
    public function setCourierJobId($courierJobId)
    {
        return $this->setData(StatusUpdateInterface::SU_COURIER_JOB_ID, $courierJobId);
    }

    /**
     * Delivery address.
     *
     * {@inheritdoc}
     */
    public function getDeliveryAddress()
    {
        return $this->getData(StatusUpdateInterface::SU_DELIVERY_ADDRESS);
    }

    /**
     * Set delivery address.
     *
     * {@inheritdoc}
     */
    public function setDeliveryAddress($deliveryAddress)
    {
        return $this->setData(StatusUpdateInterface::SU_DELIVERY_ADDRESS, $deliveryAddress);
    }

    /**
     * Delivery suburb.
     *
     * {@inheritdoc}
     */
    public function getDeliverySuburb()
    {
        return $this->getData(StatusUpdateInterface::SU_DELIVERY_SUBURB);
    }

    /**
     * Set delivery suburb.
     *
     * {@inheritdoc}
     */
    public function setDeliverySuburb($deliverySuburb)
    {
        return $this->setData(StatusUpdateInterface::SU_DELIVERY_SUBURB, $deliverySuburb);
    }

    /**
     * Delivery postcode.
     *
     * {@inheritdoc}
     */
    public function getDeliveryPostcode()
    {
        return $this->getData(StatusUpdateInterface::SU_DELIVERY_POSTCODE);
    }

    /**
     * Set delivery postcode.
     *
     * {@inheritdoc}
     */
    public function setDeliveryPostcode($deliveryPostcode)
    {
        return $this->setData(StatusUpdateInterface::SU_DELIVERY_POSTCODE, $deliveryPostcode);
    }

    /**
     * Delivery state.
     *
     * {@inheritdoc}
     */
    public function getDeliveryState()
    {
        return $this->getData(StatusUpdateInterface::SU_DELIVERY_STATE);
    }

    /**
     * Set delivery state
     *
     * {@inheritdoc}
     */
    public function setDeliveryState($deliveryState)
    {
        return $this->setData(StatusUpdateInterface::SU_DELIVERY_STATE, $deliveryState);
    }

    /**
     * Delivery country code.
     *
     * {@inheritdoc}
     */
    public function getDeliveryCountryCode()
    {
        return $this->getData(StatusUpdateInterface::SU_DELIVERY_COUNTRY_CODE);
    }

    /**
     * Set delivery country code.
     *
     * {@inheritdoc}
     */
    public function setDeliveryCountryCode($deliveryCountryCode)
    {
        return $this->setData(StatusUpdateInterface::SU_DELIVERY_COUNTRY_CODE, $deliveryCountryCode);
    }

    /**
     * Delivery latitude.
     *
     * {@inheritdoc}
     */
    public function getDeliveryLatitude()
    {
        return $this->getData(StatusUpdateInterface::SU_DELIVERY_LATITUDE);
    }

    /**
     * Set delivery latitude.
     *
     * {@inheritdoc}
     */
    public function setDeliveryLatitude($deliveryLatitude)
    {
        return $this->setData(StatusUpdateInterface::SU_DELIVERY_LATITUDE, $deliveryLatitude);
    }

    /**
     * Delivery longitude.
     *
     * {@inheritdoc}
     */
    public function getDeliveryLongitude()
    {
        return $this->getData(StatusUpdateInterface::SU_DELIVERY_LONGITUDE);
    }

    /**
     * Set delivery longitude.
     *
     * {@inheritdoc}
     */
    public function setDeliveryLongitude($deliveryLongitude)
    {
        return $this->setData(StatusUpdateInterface::SU_DELIVERY_LONGITUDE, $deliveryLongitude);
    }

    /**
     * Status history.
     *
     * {@inheritdoc}
     */
    public function getStatusHistory()
    {
        return $this->getData(StatusUpdateInterface::SU_STATUS_HISTORY);
    }

    /**
     * Set status history.
     *
     * {@inheritdoc}
     */
    public function setStatusHistory(array $statusHistory = null)
    {
        return $this->setData(StatusUpdateInterface::SU_STATUS_HISTORY, $statusHistory);
    }

    /**
     * Products associated.
     *
     * {@inheritdoc}
     */
    public function getProducts()
    {
        return $this->getData(StatusUpdateInterface::SU_PRODUCTS);
    }

    /**
     * Set the products.
     *
     * {@inheritdoc}
     */
    public function setProducts(array $products = null)
    {
        return $this->setData(StatusUpdateInterface::SU_PRODUCTS, $products);
    }
}
