<?php

namespace Totaltools\ClickAndCollect\Setup;

use Magento\Framework\DB\Ddl\Table as Table;
use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

/**
 * Class UpgradeSchema
 * @package Totaltools\ClickAndCollect\Setup
 */
class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * @var string
     */
    private $setupVersion;

    /**
     * Installs the data.
     *
     * @param SchemaSetupInterface $setup Setup model.
     * @param ModuleContextInterface $context Context model.
     *
     * @return void
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $this->setupVersion = $context->getVersion();
        unset($context);

        $setup->startSetup();

        if (version_compare($this->setupVersion, '0.1.0', '<')) {
            $this->addCcCurrentStateColumn($setup);
            $this->addIsCcEmailSentColumn($setup);
        }

        if (version_compare($this->setupVersion, '0.1.1', '<')) {
            $this->addIndexIsCcEmailSentColumn($setup);
        }

        $setup->endSetup();
    }

    /**
     * Add cc_current_state column to sales_order table
     * @param SchemaSetupInterface $setup
     */
    private function addCcCurrentStateColumn(SchemaSetupInterface $setup)
    {
        $value = [
            'type' => Table::TYPE_INTEGER,
            'length' => 3,
            'nullable' => true,
            'comment' => 'Click & Collect Current State'
        ];
        if ($setup->getConnection()->tableColumnExists($setup->getTable('sales_order'), 'cc_current_state')) {
            $setup->getConnection()->modifyColumn(
                $setup->getTable('sales_order'),
                'cc_current_state',
                $value
            );
        } else {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'cc_current_state',
                $value
            );
        }
    }

    /**
     * Add is_ccemail_sent column to sales_order table
     * @param SchemaSetupInterface $setup
     */
    private function addIsCcEmailSentColumn(SchemaSetupInterface $setup)
    {
        $value = [
            'type' => Table::TYPE_BOOLEAN,
            'nullable' => false,
            'default' => false,
            'comment' => 'Is Email Sent (Click & Collect)'
        ];
        if ($setup->getConnection()->tableColumnExists($setup->getTable('sales_order'), 'is_ccemail_sent')) {
            $setup->getConnection()->modifyColumn(
                $setup->getTable('sales_order'),
                'is_ccemail_sent',
                $value
            );
        } else {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'is_ccemail_sent',
                $value
            );
        }
    }

    /**
     * Add index is_ccemail_sent column to sales_order table
     * @param SchemaSetupInterface $setup
     */
    private function addIndexIsCcEmailSentColumn(SchemaSetupInterface $setup)
    {
        if ($setup->getConnection()->tableColumnExists($setup->getTable('sales_order'), 'is_ccemail_sent')) {
            $setup->getConnection()->addIndex(
                $setup->getTable('sales_order'),
                $setup->getIdxName('sales_order', ['is_ccemail_sent']),
                ['is_ccemail_sent']
            );
        }
    }
}
