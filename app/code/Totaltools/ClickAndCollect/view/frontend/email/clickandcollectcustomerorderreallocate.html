<!--@subject  {{trans "Your Total Tools order #%order_number has been reallocated to %tt_store_name" order_number=$order.increment_id tt_store_name=$reallocatedStore.getStoreName()}} @-->
{{template config_path="design/email/header_template_new"}}
<tr>
    <td class="navbar">
        {{template config_path="design/email/email_nav_bar"}}
    </td>
</tr>

                <tr>
                    <td class="main-content main-content-custom">
                    <!-- Begin Content -->

<table>
    {{depend order.getCustomerId()}}
    <tr class="member-text">
        <td>

            {{trans "<span>Member</span>" |raw}}
                {{trans "%loyalty_id" loyalty_id=$customerLoyaltyData.customerLoyaltyLevelText}}
        </td>
    </tr>
  {{/depend}}
  <tr class="email-intro">
    <td>
  <div class="bg-grey pt-3 px-3 fs-16">
            <p>{{trans "<span class='greeting'>G’day %customer_name,</span>" customer_name=$order.getCustomerName() |raw}}</p>
            <p>
                {{trans "Thank you for shopping with %store_name. Please be advised that your order is now reallocated to a new store:" store_name=$store.getFrontendName()  |raw}}
            </p>
            <p>
                {{trans "<span class='greeting'>%reallocated_store_name.</span> Your shipping method is <span class='greeting'>Click & Collect.</span>" reallocated_store_name=$reallocatedStore.getStoreName() |raw}}
                {{trans "<br/>You will be emailed when your order is ready to collect." |raw}}
            </p>
          
            <p class="btn-email" align="center">
                {{trans '<a href="%account_url">' account_url=$this.getUrl($store,'storelocator') |raw}}
                    <span>{{trans "%assigned_store_name" assigned_store_name=$reallocatedStore.getStoreName()}}</span>
                 </a> 

                  {{trans "Please bring your drivers licence and credit card (if used for payment) as proof of ID."}}
            </p>
        </div>
    </td>
</tr>
<tr class="email-summary">
    <td>
        <h1>{{trans 'Order Number: <span class="no-link">#%increment_id</span>' increment_id=$order.increment_id |raw}}</h1>
        <h3>{{trans 'Order Date: <span class="no-link">%created_at</span>' created_at=$order.getCreatedAtFormatted(10) |raw}}</h3>
    </td>
</tr>
    <tr class="email-information">
        <td>
            {{depend order.getEmailCustomerNote()}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var order.getEmailCustomerNote()|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}
            
            {{layout handle="sales_email_order_items" order=$order}}
        </td>
    </tr>
    <tr>
        <td>
            <div class="bg-grey pt-3 px-3 pb-3 mt-15">
                {{block id="email_online_order_enquiries_3"}}
</div>
        </td>
    </tr>
</table>
</div>
{{template config_path="design/email/footer_template"}}