<!--@subject  {{trans "Email not sent to Total Tools order #%order_number ready for collection at %tt_store_name" order_number=$order.increment_id tt_store_name=$assignedStore.getStoreName()}} @-->
{{template config_path="design/email/header_template_new"}}
<!--[if mso]>
<style type="text/css">
body, table, td {font-family: Arial, sans-serif !important;}
.help-bar{padding:7px 0 !important;}
.help-bar a{font-size:18px !important;}
</style>
<![endif]-->
<tr>
    <td class="navbar">
        {{template config_path="design/email/email_nav_bar"}}
    </td>
</tr>

<tr>
    <td class="main-content main-content-custom">
    <!-- Begin Content -->
<div class="email-inner-content" style=" width:100%; margin:auto;  font-size: 16px; ">
<table style="width:100%;">
    <tr class="email-intro">
        <td style="padding:0 30px; background-color: #f2f2f2;">
            <p style="margin-top:15px;">
                {{trans "The following order is ready for collection. However, the email was not sent due to an invalid email address provided by the customer"}}
            </p>

            <p>{{trans "Order is ready to collect from:"}} </p>
            
<table style="width:100%">
<tr>
<td style="width:20px; vertical-align: middle;">
<img src="{{view url='Magento_Email/location-pin.png'}}" alt=""  style="margin-right: 5px;"/>
</td>
<td style="vertical-align: middle;">
<a href="{{var $this.getUrl($store,'storelocator/%assigned_store_name' assigned_store_name=$assignedStore.getStoreName())}}" style="color:#222;"> 
                     {{var store.getStoreName()}},  {{var assignedStore.getZipcode()}} 
                </a>
</td>
</tr>
</table>
            
        </td>
    </tr>
<tr>
  <td  style="padding:0 30px; background-color: #f2f2f2;">
                  <p>{{var formattedShippingAddress|raw}}</p>
<table style="width:100%;  margin-bottom:10px;">
<tr>
<td style="width:146px;  vertical-align: baseline;">Click for Directions:</td>
<td style="width:20px; vertical-align: middle;">
<img src="{{view url='Magento_Email/location-pin.png'}}" alt=""/>
</td>
<td style="vertical-align: baseline;">
<a href="{{var assignedStore.getStoreDirectionUrl()}}"> 
                    {{var store.getStoreName()}}
 </a>
</td>
</tr>
</table>
</tr>
<tr>
<td  style="padding:0 30px 20px; background-color: #f2f2f2;">
                
            {{if authToLeaveActive}}
                    <span style="float: right;">{{trans "Authority to leave"}}: <br /> <strong>
                        {{var authToLeaveText|raw}}
                    </strong></span>
            {{/if}}
</td>
</tr>
    <tr class="email-summary">
        <td>
            <table class="order-info">
                <tr>
                    <td>
            <h1 style="margin-top:15px;">{{trans 'Tax Invoice: <span class="no-link">#%increment_id</span>' increment_id=$order.increment_id |raw}}</h1>
            <h3>{{trans 'Order Date: <span class="no-link">%created_at</span>' created_at=$order.getCreatedAtFormatted(10) |raw}}</h3>
           </td>
  <td class="right">
<p class="abn" >{{trans "ABN :  "}}  {{config path="general/store_information/abn"}}</p>
        </td>
 </tr>
</table>
        </td>
    </tr>
    <tr class="email-information">
        <td>
            {{depend order.getEmailCustomerNote()}}
            <table class="message-info">
                <tr>
                    <td>
                        {{var order.getEmailCustomerNote()|escape|nl2br}}
                    </td>
                </tr>
            </table>
            {{/depend}}
            <table class="order-details">
                <tr>
                    <td class="address-details">
                        <h3 style="font-weight:bold">{{trans "Billing Info"}}</h3>
                        <p>{{var formattedBillingAddress|raw}}
                       <br/>E: {{trans "%email" email=$order.getCustomerEmail()}}
                      </p>
                    </td>
                    {{depend order.getIsNotVirtual()}}
                    <td class="address-details">
                        <h3 style="font-weight:bold">{{if is_store_pickup}}{{trans "Click & Collect Store Info"}}{{else}}{{trans "Shipping Info"}}{{/if}}</h3>
                        <p>{{var formattedShippingAddress|raw}}</p>
                    </td>
                    {{/depend}}
                    <td class="method-info">
                        <h3 style="font-weight:bold">{{trans "Payment Method"}}</h3>
                        {{var payment_html|raw}}
                    </td>
                </tr>
            </table>
            {{layout handle="sales_email_order_items" order=$order area="frontend"}}
        </td>
    </tr>
</table>
</div>
{{template config_path="design/email/footer_template"}}