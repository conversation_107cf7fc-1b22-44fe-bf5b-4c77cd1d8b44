<?php
$allocation_data = $block->getStoreData();
$currentStore = @$allocation_data['order_data']['storelocator_name'] ?: 'NULL';
?>
<?php $_order = $block->getOrder(); ?>
<?php $rewardsPointBalance = $_order->getRewardPointsBalance() ?>
<?php if ($rewardsPointBalance > 0) { ?>
    <?php $loyaltyId = $block->getLoyaltyId($_order->getCustomerEmail()); ?>
    <div data-role="messages" id="messages">
        <div class="messages">
            <div class="message-notify">
                <div data-ui-id="messages-message-error"><?php echo __("This order is attached to loyalty ID: {$loyaltyId}. Please adjust redemption points on Pronto."); ?>
                </div>
            </div>
        </div>
    </div>
<?php } ?>
<div class="entry-edit admin__scope-old">
    <form id="reallocate_request_edit" name="reallocate_request_edit" action="<?= $block->getUrl('*/*/save')?>" method="post"
          data-mage-init='{"validation":{"rules": {"storelocator_id": {"required":true}}}}'>
        <input name="form_key" type="hidden" value="<?= $block->getFormKey() ?>"/>
        <fieldset id="reallocate-fieldset" class="fieldset admin__fieldset">
            <?php if ($allocation_data['order_data']['status'] == 'reallocating') : ?>
                <div data-role="messages">
                    <div class="messages">
                        <div class="message-notify">
                            <div data-ui-id="messages-message-error"><?php echo __('Note: This order was reallocated earlier, Please Perform the manual check after reallocation');?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif;?>
            <legend class="legend">
                <span><?= __('Select Store to Reallocate to. Current Store is %1:', $currentStore) ?></span>
            </legend>
            <input type="hidden" name="order_id" value="<?= $allocation_data['order_data']['order_id'] ?>">
            <input type="hidden" name="previous_storelocator_id" value="<?= $allocation_data['order_data']['storelocator_id'] ?>">
            <div class="field-content">
                <select name="storelocator_id" id="storelocator_id" class="admin__control-select">
                    <option value=""><?php echo __('Select Store to Reallocate to ')?></option>
                <?php
                    foreach ($allocation_data['store_data'] as $store_id => $store_name) {
                        echo '<option value="'.$store_id.'">'.$store_name.'</option>';
                    }
                ?>
                </select>
                <button class="scalable save" type="submit" id="reallocate_button">
                    <span><?= __('Reallocate')?></span>
                </button>
                <button class="scalable back" type="button" id="back_button" onClick="window.history.back();">
                    <span><?= __('Go Back')?></span>
                </button>
            </div>
        </fieldset>
    </form>
</div>
<script>
    require([
        'jquery',
        'Magento_Ui/js/modal/confirm'
    ], function($, confirmation) {
        let orderId = '<?= $allocation_data['order_data']['increment_id'] ?>';
        $('#reallocate_button').on('click', function(event){
            event.preventDefault();
            let selectedStore = $( "#storelocator_id option:selected" ).text();
            let currentStore = '<?php echo $currentStore?>';
            confirmation({
                title: $.mage.__('Reallocate Confirmation'),
                content: $.mage.__(`Are you sure you want to reallocate order #${orderId} from ${currentStore} to ${selectedStore}`),
                actions: {
                    confirm: function(){
                       $('#reallocate_request_edit').submit();
                    },
                    cancel: function(){
                        event.preventDefault();
                        console.log('cancel');
                    },
                    always: function(){}
                }
            });
        });
    });
</script>
