<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Totaltools\ClickAndCollect\Api\StatusUpdaterInterface" type="Totaltools\ClickAndCollect\Model\StatusUpdater"/>
    <preference for="Totaltools\ClickAndCollect\Api\Data\StatusUpdateInterface" type="Totaltools\ClickAndCollect\Model\Api\Data\StatusUpdate"/>
    <preference for="Totaltools\ClickAndCollect\Api\Data\StatusHistoryInterface" type="Totaltools\ClickAndCollect\Model\Api\Data\StatusHistory"/>
    <type name="Magento\Sales\Api\OrderRepositoryInterface">
        <plugin name="totaltools_click_and_collect_add_order_extension_attribute"
                type="Totaltools\ClickAndCollect\Plugin\OrderRepository" />
    </type>
    <type name="Magento\Framework\Console\CommandListInterface">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="reallocateStoreOrder" xsi:type="object">Totaltools\ClickAndCollect\Console\Command\ReallocateStoreOrderCommand</item>
            </argument>
        </arguments>
    </type>
</config>
