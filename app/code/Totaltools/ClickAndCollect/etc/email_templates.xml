<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Email:etc/email_templates.xsd">
    <template id="click_and_collect_cc_current_state_update"
              label="Click And Collect Email"
              file="clickandcollect.html"
              type="html"
              module="Totaltools_ClickAndCollect"
              area="frontend"/>
    <template id="storelocator_send_mail_customer_order_reallocate"
              label="Click And Collect Reallocate Customer Email"
              file="clickandcollectcustomerorderreallocate.html"
              type="html"
              module="Totaltools_ClickAndCollect"
              area="frontend"/>
    <template id="storelocator_send_mail_original_store_reallocate"
              label="Click And Collect Original Store Reallocate Email"
              file="clickandcollectoriginalstore.html"
              type="html"
              module="Totaltools_ClickAndCollect"
              area="frontend"/>
    <template id="storelocator_send_mail_cc_notify_email_template"
              label="Click And Collect Issue Notify Email"
              file="clickandcollectissuenotify.html"
              type="html"
              module="Totaltools_ClickAndCollect"
              area="frontend"/>
</config>