<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/clickandcollect/updateStatus" method="POST">
        <service class="Totaltools\ClickAndCollect\Api\StatusUpdaterInterface" method="update"/>
        <resources>
            <resource ref="Totaltools_ClickAndCollect::updateStatuses"/>
        </resources>
    </route>
</routes>
