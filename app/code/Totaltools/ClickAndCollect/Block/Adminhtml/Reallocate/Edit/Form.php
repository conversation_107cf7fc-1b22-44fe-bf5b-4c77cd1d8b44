<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_ClickAndCollect
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\ClickAndCollect\Block\Adminhtml\Reallocate\Edit;

use Magento\Backend\Block\Template;
use Magento\Backend\Block\Template\Context;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Model\OrderRepository;
use Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory;
use Magestore\Storelocator\Model\Status;
use Totaltools\Pronto\Model\Request\Customer;

/**
 * Class Form
 * @package Totaltools\ClickAndCollect\Block\Adminhtml\Reallocate\Edit
 */
class Form extends Template
{
    /**
     * Order Repository
     *
     * @var OrderRepository
     */
    protected $orderRepository;

    /**
     * Store Collection Factory
     *
     * @var CollectionFactory
     */
    protected $storeCollectionFactory;

    /**
     * @var Customer
     */
    protected $prontoCustomer;

    /**
     * @var array|bool
     */
    protected $customerMember;

    /**
     * @param Context $context
     * @param OrderRepository $orderRepository
     * @param CollectionFactory $storeCollectionFactory
     * @param array $data
     */
    public function __construct(
        Context $context,
        OrderRepository $orderRepository,
        CollectionFactory $storeCollectionFactory,
        Customer $pronto,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->orderRepository = $orderRepository;
        $this->storeCollectionFactory = $storeCollectionFactory;
        $this->prontoCustomer = $pronto;
    }

    /**
     * Retrieve reallocation selection data
     *
     * @return array
     * @throws InputException
     * @throws NoSuchEntityException
     */
    public function getStoreData()
    {
        $id = $this->_request->getParam('order_id');
        $order = $this->getOrder();
        $order->getExtensionAttributes();

        $storeCollection = $this->storeCollectionFactory->create();
        $storeCollection->setOrder('store_name', 'ASC');
        $dataArray = array(
            'store_data' => array(),
            'order_data' => array(
                'order_id' => $id,
                'storelocator_id' => $order->getStorelocatorId(),
                'warehouse' => $order->getWarehouse(),
                'store' => $order->getStore(),
                'status' => $order->getStatus(),
                'increment_id' => $order->getIncrementId(),
            )
        );

        foreach ($storeCollection as $store) {
            if ($store->getStatus() == Status::STATUS_DISABLED || !$store->getAllowStoreCollection()) {
                continue;
            }
            if ($order->getStorelocatorId() !== $store->getStorelocatorId()) {
                $dataArray['store_data'][$store->getStorelocatorId()] = $store->getStoreName();
            } else {
                $dataArray['order_data']['storelocator_name'] = $store->getStoreName();
            }
        }

        return $dataArray;
    }

    
    public function getOrder()
    {
        $id = $this->_request->getParam('order_id');
        return $this->orderRepository->get($id);
    }

    /**
     * @param $customerEmail
     * @return bool|mixed
     * @throws LocalizedException
     */
    public function getLoyaltyId($customerEmail)
    {
        $customerData = $this->getProntoCustomer($customerEmail);
        if (!empty($customerData)) {
            return $customerData['LoyaltyCustomers']['LoyaltyCustomer']['LoyaltyID'];
        }
        return false;
    }

    /**
     * @param $email
     * @return array|bool
     * @throws LocalizedException
     */
    public function getProntoCustomer($email)
    {
        if (!$this->customerMember) {
            $this->customerMember = $this->prontoCustomer->getMember([
                'Parameters' => [
                    'EmailAddress' => $email,
                ]
            ]);
        }
        return $this->customerMember;
    }

}