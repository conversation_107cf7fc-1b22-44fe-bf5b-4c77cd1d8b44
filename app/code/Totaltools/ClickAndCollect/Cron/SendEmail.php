<?php

namespace Totaltools\ClickAndCollect\Cron;

use Magento\Framework\App\Area;
use Magento\Framework\DataObject;
use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Address;
use Magento\Customer\Model\Address\Config as AddressConfig;
use Psr\Log\LoggerInterface;
use Magento\Framework\Debug;

/**
 * Class SendEmail
 * @package Totaltools\ClickAndCollect\Cron
 */
class SendEmail
{
    const XML_PATH_CC_STATUS_UPDATE_EMAIL = 'sales_email/order_ready_for_pickup/template';
    const XML_PATH_CC_STATUS_UPDATE_EMAIL_GUEST = 'sales_email/order_ready_for_pickup/guest_template';
    const XML_PATH_CC_NOTIFY_EMAIL_TO = 'storelocator/send_mail/cc_notify_email_to';
    const XML_PATH_CC_NOTIFY_EMAIL_TEMPLATE = 'storelocator/send_mail/cc_notify_email_template';

    /**
     * Click and collect method name
     */
    const STORE_PICKUP_METHOD = 'shippitcc_shippitcc';
    /**
     * @var \Magento\Framework\Mail\Template\TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var \Magento\Sales\Model\OrderFactory
     */
    private $orderFactory;

    /**
     * @var Address
     */
    protected $address;

    /**
     * @var AddressConfig
     */
    protected $addressConfig;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var array
     */
    private $lists = [
        'entity_id',
        'state',
        'customer_id',
        'increment_id',
        'quote_id',
        'store_id',
        'customer_is_guest',
        'is_virtual',
        'billing_address_id',
        'shipping_method',
        'shipping_address_id',
        'customer_email',
        'is_ccemail_sent',
        'cc_current_state',
    ];

    /**
     * @var \Magento\Sales\Model\Order\Address\Renderer
     */
    private $addressRenderer;

    /**
     * @var \Magento\Payment\Helper\Data
     */
    private $paymentHelper;

    /**
     * @var \Magento\Framework\Event\ManagerInterface
     */
    private $eventManager;

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    private $config;

    /**
     * @var \Magento\Sales\Model\Order\Email\Container\Template
     */
    private $templateContainer;
    /**
     * @var  \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;
    /**
     * @var  \Magento\Sales\Api\OrderRepositoryInterface
     */
    private $orderRepository;
    /**
     * SendEmail constructor.
     * @param \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder
     * @param \Magento\Sales\Model\OrderFactory $orderFactory
     * @param \Magento\Sales\Model\Order\Address\Renderer $addressRenderer
     * @param \Magento\Payment\Helper\Data $paymentHelper
     * @param \Magento\Framework\Event\ManagerInterface $eventManager
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig
     * @param \Magento\Sales\Model\Order\Email\Container\Template $templateContainer
     * @param Address $address
     * @param AddressConfig $addressConfig
     */
    public function __construct(
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Magento\Sales\Model\OrderFactory $orderFactory,
        \Magento\Sales\Model\Order\Address\Renderer $addressRenderer,
        \Magento\Payment\Helper\Data $paymentHelper,
        \Magento\Framework\Event\ManagerInterface $eventManager,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        \Magento\Sales\Model\Order\Email\Container\Template $templateContainer,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository,
        Address $address,
        AddressConfig $addressConfig,
        LoggerInterface $logger
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->orderFactory = $orderFactory;
        $this->addressRenderer = $addressRenderer;
        $this->paymentHelper = $paymentHelper;
        $this->eventManager = $eventManager;
        $this->config = $totalToolsConfig;
        $this->templateContainer = $templateContainer;
        $this->storeRepository = $storeRepository;
        $this->orderRepository = $orderRepository;
        $this->address = $address;
        $this->addressConfig = $addressConfig;
        $this->logger = $logger;

    }

    /**
     * @return $this
     * @throws \Exception
     */
    public function execute()
    {
        $shippingMethod = $this->config->getDefaultShippingMethod() ? : 'shippitcc_shippitcc';
        $orders = $this->orderFactory->create()->getCollection()
            ->addFieldToSelect($this->lists)
            ->addFieldToFilter('is_ccemail_sent', 0)
            ->addFieldToFilter('status', 'ready_for_collection')
            ->addFieldToFilter('shipping_method', $shippingMethod);
        foreach ($orders as $order) {
            $this->sendEmail($order);
        }

        return $this;
    }

    /**
     * @param $order
     * @throws \Exception
     */
    private function sendEmail($order)
    {
        try {
            /** @var \Magento\Sales\Model\Order $order */
            $order = $this->orderRepository->get($order->getId());
            $recipient = $order->getCustomerEmail();
            $storeId = $order->getData('storelocator_id');
            $store = $this->storeRepository->getById($storeId);
            $guestCustomer = $order->getCustomerIsGuest();
            if(!$guestCustomer) {
                $template = $this->config->getConfigValue(self::XML_PATH_CC_STATUS_UPDATE_EMAIL);
            } else {
                $template = $this->config->getConfigValue(self::XML_PATH_CC_STATUS_UPDATE_EMAIL_GUEST);
            }
            
            //set shipping address
            $datashipping = array();
            $datashipping['firstname'] = $store->getData('store_name');
            $datashipping['lastname'] = '';
            $datashipping['street'][0] = $store->getData('address');
            $datashipping['city'] = $store->getCity();
            $datashipping['region'] = $store->getState();
            $datashipping['postcode'] = $store->getData('zipcode');
            $datashipping['country_id'] = $store->getData('country_id');
            $datashipping['company'] = '';
            if ($store->getFax()) {
                $datashipping['fax'] = $store->getFax();
            } else {
                unset($datashipping['fax']);
            }
            if ($store->getPhone()) {
                $datashipping['telephone'] = $store->getPhone();
            } else {
                unset($datashipping['telephone']);
            }
            $datashipping['save_in_address_book'] = 0;

            $this->address->addData($datashipping);

            $this->sendReadyForCollectionEmail($order, $recipient, $template);
            
        } catch (\Exception $exception) {
            /** @var \Magento\Sales\Model\Order $order */
            $order = $this->orderRepository->get($order->getId());
            $template = $this->config->getConfigValue(self::XML_PATH_CC_NOTIFY_EMAIL_TEMPLATE);
            $recipient = $this->config->getConfigValue(self::XML_PATH_CC_NOTIFY_EMAIL_TO);
            $this->sendReadyForCollectionEmail($order, $recipient, $template);
            $this->logger->critical(
                'Issue with sending CC email for the order #' . $order->getId().'-'. $exception->getMessage() ,
                ['backtrace' => Debug::backtrace(true, false)]
            );
        }
    }

    /**
     * @param Order $order
     * @return string
     */
    protected function getCustomerName(Order $order)
    {
        $orderCustomerName = $order->getCustomerName();

        if ($orderCustomerName == 'Guest') {
            $orderCustomerName = $order->getBillingAddress()->getFirstname() . ' ' . $order->getBillingAddress()->getLastname();
        }

        return $orderCustomerName;
    }

    /**
     * @param $order
     * @return |null
     */
    private function getFormattedShippingAddress($order)
    {
        $this->addressConfig->setStore($order->getStoreId());
        $formatType = $this->addressConfig->getFormatByCode('html');
        return $order->getIsVirtual()
            ? null
            : $formatType->getRenderer()->renderArray($this->address);;
    }

    /**
     * @param $order
     * @return mixed
     */
    private function getFormattedBillingAddress($order)
    {
        return $this->addressRenderer->format($order->getBillingAddress(), 'html');
    }

    /**
     * @param $order
     * @return string
     * @throws \Exception
     */
    private function getPaymentHtml($order)
    {
        return $this->paymentHelper->getInfoBlockHtml(
            $order->getPayment(),
            $order->getStore()->getStoreId()
        );
    }

    private function sendReadyForCollectionEmail($order, $recipient, $template)
    {
        $storeId = $order->getData('storelocator_id');
        $store = $this->storeRepository->getById($storeId);
        $shippingMethod = $order->getShippingMethod();
        $isStorePickup = (bool) ($shippingMethod == self::STORE_PICKUP_METHOD);

        $transport = [
            'order' => $order,
            'order_id' => $order->getId(),
            'billing' => $order->getBillingAddress(),
            'customer_name' => $this->getCustomerName($order),
            'created_at_formatted' => $order->getCreatedAtFormatted(10),
            'assignedStore' => $store,
            'payment_html' => $this->getPaymentHtml($order),
            'store' => $store,
            'formattedShippingAddress' => $this->getFormattedShippingAddress($order),
            'formattedBillingAddress' => $this->getFormattedBillingAddress($order),
            'is_store_pickup' => $isStorePickup,
            'order_data' => [
                'customer_name' => $order->getCustomerName(),
                'is_not_virtual' => $order->getIsNotVirtual(),
                'email_customer_note' => $order->getEmailCustomerNote(),
                'frontend_status_label' => $order->getFrontendStatusLabel()
            ]
        ];
       
        $mailer = $this->transportBuilder
            ->setTemplateIdentifier($template)
            ->setTemplateOptions([
                'area' => Area::AREA_FRONTEND,
                'store' => $order->getStoreId(),
            ])
            ->setTemplateVars($transport)
            ->setFrom('sales')
            ->addTo($recipient);
        $transport = $mailer->getTransport();
        $transport->sendMessage();
        $order->setIsCcemailSent(true);
        $order->save();
    }
}
