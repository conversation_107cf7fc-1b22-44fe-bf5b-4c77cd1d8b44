<?php

namespace Totaltools\ClickAndCollect\Api;

/**
 * Interface StatusUpdaterInterface.
 * @api
 */
interface StatusUpdaterInterface
{
    /**
     * Update status for order from Shippit data.
     * NOTE: DO NOT import the namespaces! This is WebAPI and WebAPI must use full-qualified namespace in params.
     *
     * @param Data\StatusUpdateInterface $statusUpdate
     *
     * @return Data\StatusUpdateInterface
     */
    public function update(\Totaltools\ClickAndCollect\Api\Data\StatusUpdateInterface $statusUpdate);
}
