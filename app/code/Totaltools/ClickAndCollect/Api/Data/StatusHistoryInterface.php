<?php
/**
 * Totaltools Click & Collect.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\ClickAndCollect\Api\Data;

/**
 * Interface StatusHistoryInterface.
 * @api
 */
interface StatusHistoryInterface
{
    /**#@+
     * Constants defined for keys of data array
     */
    const STATUS = 'status';
    const TIME = 'time';
    /**#@-*/

    /**
     * Status.
     *
     * @return string|null
     */
    public function getStatus();

    /**
     * Set status.
     *
     * @param string $status
     * @return $this
     */
    public function setStatus($status);

    /**
     * Date time.
     *
     * @return string|null
     */
    public function getTime();

    /**
     * Set date time.
     *
     * @param string $time
     * @return $this
     */
    public function setTime($time);
}