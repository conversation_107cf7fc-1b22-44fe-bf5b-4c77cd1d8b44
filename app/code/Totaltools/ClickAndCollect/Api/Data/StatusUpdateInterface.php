<?php
/**
 * Totaltools Click & Collect.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\ClickAndCollect\Api\Data;

/**
 * Interface StatusUpdateInterface.
 * @api
 */
interface StatusUpdateInterface
{
    /**#@+
     * Constants defined for keys of data array
     */
    const SU_TRACKING_NUMBER = 'tracking_number';
    const SU_EXPECTED_DELIVERY_DATE = 'expected_delivery_date';
    const SU_TRACKING_URL = 'tracking_url';
    const SU_MERCHANT_URL = 'merchant_url';
    const SU_CURRENT_STATE = 'current_state';
    const SU_RETAILER_ORDER_NUMBER = 'retailer_order_number';
    const SU_RETAILER_REFERENCE = 'retailer_reference';
    const SU_COURIER_NAME = 'courier_name';
    const SU_COURIER_JOB_ID = 'courier_job_id';
    const SU_DELIVERY_ADDRESS = 'delivery_address';
    const SU_DELIVERY_SUBURB = 'delivery_suburb';
    const SU_DELIVERY_POSTCODE = 'delivery_postcode';
    const SU_DELIVERY_STATE = 'delivery_state';
    const SU_DELIVERY_COUNTRY_CODE = 'delivery_country_code';
    const SU_DELIVERY_LATITUDE = 'delivery_latitude';
    const SU_DELIVERY_LONGITUDE = 'delivery_longitude';
    const SU_STATUS_HISTORY = 'status_history';
    const SU_PRODUCTS = 'products';
    /**#@-*/

    /**
     * Tracking number.
     *
     * @return string|null
     */
    public function getTrackingNumber();

    /**
     * Set tracking number.
     *
     * @param string $trackingNumber
     * @return $this
     */
    public function setTrackingNumber($trackingNumber);

    /**
     * Expected delivery date.
     *
     * @return string|null
     */
    public function getExpectedDeliveryDate();

    /**
     * Set exepected delivery date.
     *
     * @param $expectedDeliveryDate
     * @return $this
     */
    public function setExpectedDeliveryDate($expectedDeliveryDate);

    /**
     * Tracking url.
     *
     * @return string|null
     */
    public function getTrackingUrl();

    /**
     * Set tracking url.
     *
     * @param string $trackingUrl
     * @return $this
     */
    public function setTrackingUrl($trackingUrl);

    /**
     * Merchant url.
     *
     * @return string|null
     */
    public function getMerchantUrl();

    /**
     * Set merchant url.
     *
     * @param string $merchantUrl
     * @return $this
     */
    public function setMerchantUrl($merchantUrl);

    /**
     * Current state.
     *
     * @return string|null
     */
    public function getCurrentState();

    /**
     * Set current state.
     *
     * @param string $currentState
     * @return $this
     */
    public function setCurrentState($currentState);

    /**
     * Retailer order number.
     *
     * @return string|null
     */
    public function getRetailerOrderNumber();

    /**
     * Set retailer order number.
     *
     * @param string $retailerOrderNumber
     * @return $this
     */
    public function setRetailerOrderNumber($retailerOrderNumber);

    /**
     * Retailer reference.
     *
     * @return string|null
     */
    public function getRetailerReference();

    /**
     * Set retailer reference.
     *
     * @param string $retailerReference
     * @return $this
     */
    public function setRetailerReference($retailerReference);

    /**
     * Courier name.
     *
     * @return string|null
     */
    public function getCourierName();

    /**
     * Set courier name.
     *
     * @param string $courierName
     * @return $this
     */
    public function setCourierName($courierName);

    /**
     * Courier job id.
     *
     * @return string|null
     */
    public function getCourierJobId();

    /**
     * Set courier job id.
     *
     * @param string $courierJobId
     * @return $this
     */
    public function setCourierJobId($courierJobId);

    /**
     * Delivery address.
     *
     * @return string|null
     */
    public function getDeliveryAddress();

    /**
     * Set delivery address.
     *
     * @param string $deliveryAddress
     * @return $this
     */
    public function setDeliveryAddress($deliveryAddress);

    /**
     * Delivery suburb.
     *
     * @return string|null
     */
    public function getDeliverySuburb();

    /**
     * Set delivery suburb.
     *
     * @param string $deliverySuburb
     * @return $this
     */
    public function setDeliverySuburb($deliverySuburb);

    /**
     * Delivery postcode.
     *
     * @return string|null
     */
    public function getDeliveryPostcode();

    /**
     * Set delivery postcode.
     *
     * @param string $deliveryPostcode
     * @return $this
     */
    public function setDeliveryPostcode($deliveryPostcode);

    /**
     * Delivery state.
     *
     * @return string|null
     */
    public function getDeliveryState();

    /**
     * Set delivery state
     *
     * @param string $deliveryState
     * @return $this
     */
    public function setDeliveryState($deliveryState);

    /**
     * Delivery country code.
     *
     * @return string|null
     */
    public function getDeliveryCountryCode();

    /**
     * Set delivery country code.
     *
     * @param string $deliveryCountryCode
     * @return $this
     */
    public function setDeliveryCountryCode($deliveryCountryCode);

    /**
     * Delivery latitude.
     *
     * @return string|null
     */
    public function getDeliveryLatitude();

    /**
     * Set delivery latitude.
     *
     * @param string $deliveryLatitude
     * @return $this
     */
    public function setDeliveryLatitude($deliveryLatitude);

    /**
     * Delivery longitude.
     *
     * @return string|null
     */
    public function getDeliveryLongitude();

    /**
     * Set delivery longitude.
     *
     * @param string $deliveryLongitude
     * @return $this
     */
    public function setDeliveryLongitude($deliveryLongitude);

    /**
     * Status history.
     *
     * @return \Totaltools\ClickAndCollect\Api\Data\StatusHistoryInterface[]
     */
    public function getStatusHistory();

    /**
     * Set status history.
     *
     * @param \Totaltools\ClickAndCollect\Api\Data\StatusHistoryInterface[] $statusHistory|null
     * @return $this
     */
    public function setStatusHistory(array $statusHistory = null);

    /**
     * Products associated.
     * @return \Magento\Catalog\Api\Data\ProductInterface[]
     */
    public function getProducts();

    /**
     * Set the products.
     *
     * @param \Magento\Catalog\Api\Data\ProductInterface[] $products|null
     * @return $this
     */
    public function setProducts(array $products = null);
}
