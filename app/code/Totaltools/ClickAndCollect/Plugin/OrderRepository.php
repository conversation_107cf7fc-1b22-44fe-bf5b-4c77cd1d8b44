<?php

namespace Totaltools\ClickAndCollect\Plugin;

use Magento\Sales\Api\Data\OrderExtensionFactory;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\Data\OrderSearchResultInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

/**
 * Class OrderRepository
 * @package Totaltools\ClickAndCollect\Plugin
 */
class OrderRepository
{
    const CC_CURRENT_STATE = 'cc_current_state';
    const CC_EMAIL_SENT = 'is_ccemail_sent';


    /**
     * Order Extension Attributes Factory
     *
     * @var OrderExtensionFactory
     */
    private $extensionFactory;

    /**
     * OrderRepositoryPlugin constructor
     *
     * @param OrderExtensionFactory $extensionFactory
     */
    public function __construct(OrderExtensionFactory $extensionFactory)
    {
        $this->extensionFactory = $extensionFactory;
    }

    /**
     * @param OrderRepositoryInterface $subject
     * @param OrderInterface $order
     * @return OrderInterface
     */
    public function afterGet(OrderRepositoryInterface $subject, OrderInterface $order)
    {
        $state = $order->getData(self::CC_CURRENT_STATE);
        $isCcemailSent = $order->getData(self::CC_EMAIL_SENT);
        $extensionAttributes = $order->getExtensionAttributes();
        $extensionAttributes = $extensionAttributes ? $extensionAttributes : $this->extensionFactory->create();
        $extensionAttributes->setCcCurrentState($state);
        $extensionAttributes->setIsCcemailSent($isCcemailSent);

        $order->setExtensionAttributes($extensionAttributes);

        return $order;
    }

    /**
     * @param OrderRepositoryInterface $subject
     * @param OrderSearchResultInterface $searchResult
     * @return OrderSearchResultInterface
     */
    public function afterGetList(OrderRepositoryInterface $subject, OrderSearchResultInterface $searchResult)
    {
        $orders = $searchResult->getItems();

        foreach ($orders as &$order) {
            /*** @var \Magento\Sales\Model\Order $order */
            $state = $order->getData(self::CC_CURRENT_STATE);
            $isCcemailSent = $order->getData(self::CC_EMAIL_SENT);
            $extensionAttributes = $order->getExtensionAttributes();
            $extensionAttributes = $extensionAttributes ? $extensionAttributes : $this->extensionFactory->create();
            $extensionAttributes->setCcCurrentState($state);
            $extensionAttributes->setIsCcemailSent($isCcemailSent);
            $order->setExtensionAttributes($extensionAttributes);
        }

        return $searchResult;
    }
}
