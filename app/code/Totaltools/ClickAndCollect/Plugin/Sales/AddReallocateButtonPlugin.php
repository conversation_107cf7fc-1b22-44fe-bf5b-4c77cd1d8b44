<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_ClickAndCollect
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\ClickAndCollect\Plugin\Sales;

use \Magento\Framework\App\Action\Context;
use \Magento\Backend\Model\UrlInterface;
use \Magento\Sales\Model\OrderRepository;

class AddReallocateButtonPlugin
{
    /**
     * @var Context
     */
    protected $context;

    /**
     * @var UrlInterface
     */
    protected $url;

    /**
     * @var OrderRepository
     */
    protected $orderRepository;

    /**
     * @var \Totaltools\ClickAndCollect\Helper\Reallocate
     */
    protected $reallocateHelper;

    /**
     * @var \Magento\Framework\Registry
     */
    protected $registry;

    /**
     * @param Context $context
     * @param UrlInterface $url
     * @param OrderRepository $orderRepository
     */
    public function __construct(
        Context $context,
        UrlInterface $url,
        OrderRepository $orderRepository,
        \Totaltools\ClickAndCollect\Helper\Reallocate $reallocateHelper,
        \Magento\Framework\Registry $registry
    ) {
        $this->context = $context;
        $this->url = $url;
        $this->orderRepository = $orderRepository;
        $this->reallocateHelper = $reallocateHelper;
        $this->registry = $registry;
    }

    /**
     * Retrieve order model object
     *
     * @param \Magento\Backend\Block\Widget\Context $subject
     * @param UrlInterface $buttonList
     * @throws \Exception
     *
     * @return $buttonList
     */
    public function afterGetButtonList(
        \Magento\Backend\Block\Widget\Context $subject,
        $buttonList
    ) {
        $currOrder = $this->registry->registry('current_order');
        if(
            $currOrder &&
            $this->context->getRequest()->getFullActionName() === 'sales_order_view' &&
            $this->reallocateHelper->isOrderAvailableForReallocating($currOrder)
        ){
            $buttonList->add(
                'reallocate_order',
                [
                    'label' => __('Reallocate Order'),
                    'onclick' => 'setLocation(\'' . $this->getReallocateOrderSyncUrl() . '\')',
                    'class' => 'reallocate'
                ],
                100
            );
        }

        return $buttonList;
    }

    /**
     * get reallocation url
     *
     * @return string
     */
    private function getReallocateOrderSyncUrl()
    {
        $orderId = $this->context->getRequest()->getParam('order_id');

        return $this->url->getUrl(
            'sales/order/reallocate',
            [
                'order_id' => $orderId
            ]
        );
    }
}
