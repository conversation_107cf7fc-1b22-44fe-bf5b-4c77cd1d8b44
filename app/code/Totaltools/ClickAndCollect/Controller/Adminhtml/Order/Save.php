<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_ClickAndCollect
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\ClickAndCollect\Controller\Adminhtml\Order;

use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Backend\Model\View\Result\Redirect;
use Magento\Framework\App\Request\Http;
use Magento\Framework\Message\ManagerInterface;
use Totaltools\ClickAndCollect\Helper\Reallocate;

/**
 * Class Submit
 * @package Totaltools\ClickAndCollect\Controller
 */
class Save extends Action
{
    /**
     * @var Http
     */
    private $request;
    /**
     * @var Reallocate
     */
    private $reallocate;
    /**
     * @var ManagerInterface
     */
    private $_messageManager;

    /**
     * @param Context $context
     * @param Http $request
     * @param Reallocate $reallocate
     * @param ManagerInterface $messageManager
     */
    public function __construct (
        Context $context,
        Http $request,
        Reallocate $reallocate,
        ManagerInterface $messageManager
    ){
        parent::__construct($context);
        $this->request = $request;
        $this->reallocate = $reallocate;
        $this->_messageManager = $messageManager;
    }

    /**
     * Reallocate the selected Store form
     */
    public function execute()
    {
        $order_id = $this->request->getParam('order_id');
        $store_id = $this->request->getParam('storelocator_id');
        $result = $this->reallocate->updateOrderStoreLocation($order_id, $store_id);

        if(count($result['success']) > 0) {
            $func = 'addSuccessMessage';
            $arr = 'success';
        } else {
            $func = 'addErrorMessage';
            $arr = 'error';
        }

        foreach ($result[$arr] as $message) {
            $this->_messageManager->$func(__($message));
        }

        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        return $resultRedirect->setPath('sales/order/view', ['order_id' => $order_id]);
    }
}