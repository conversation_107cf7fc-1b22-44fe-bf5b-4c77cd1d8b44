<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_ClickAndCollect
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2019, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\ClickAndCollect\Controller\Adminhtml\Order;

use Magento\Backend\App\Action;
/**
 * Class Reallocate
 * @package Totaltools\ClickAndCollect\Controller
 */
class Reallocate extends Action
{
    /**
     * Reallocate the selected Store form
     */
    public function execute()
    {
        $this->_view->loadLayout();
        $this->_view->renderLayout();
    }

}