<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Totaltools\StoreAccount\Api\AccountRepositoryInterface" type="Totaltools\StoreAccount\Model\AccountRepository"/>
	<preference for="Totaltools\StoreAccount\Api\Data\AccountInterface" type="Totaltools\StoreAccount\Model\Account"/>
	<preference for="Totaltools\StoreAccount\Api\Data\AccountSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<virtualType name="Totaltools\StoreAccount\Model\ResourceModel\Account\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">totaltools_store_account</argument>
			<argument name="resourceModel" xsi:type="string">Totaltools\StoreAccount\Model\ResourceModel\Account\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="totaltools_storeaccount_account_listing_data_source" xsi:type="string">Totaltools\StoreAccount\Model\ResourceModel\Account\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
</config>
