<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="totaltools_store_account" resource="default" engine="innodb" comment="Totaltools Store Account Table">
		<column xsi:type="int" name="account_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Account Id"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="account_id"/>
		</constraint>
		<column name="customer_name" nullable="true" xsi:type="varchar" comment="Customer Name" length="255"/>
		<column name="email" nullable="true" xsi:type="varchar" comment="Email" length="255"/>
		<column name="phone" nullable="true" xsi:type="varchar" comment="Phone" length="255"/>
		<column name="abn" nullable="true" xsi:type="varchar" comment="ABN" length="255"/>
		<column name="company_name" nullable="true" xsi:type="varchar" comment="Company Name" length="255"/>
		<column name="estimated_monthly_spend" nullable="true" xsi:type="varchar" comment="Estimated Monthly Spend" length="255"/>
		<column name="selected_stores" nullable="true" xsi:type="varchar" comment="Selected Stores" length="255"/>
		<column name="postcode" nullable="true" xsi:type="varchar" comment="Post Code" length="255"/>
		<column name="submission_date" nullable="false" xsi:type="timestamp" on_update='false' default='CURRENT_TIMESTAMP' comment="Submission Date"/>
	</table>
</schema>
