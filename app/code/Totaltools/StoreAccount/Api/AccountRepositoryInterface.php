<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\StoreAccount\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface AccountRepositoryInterface
{

    /**
     * Save Account
     * @param \Totaltools\StoreAccount\Api\Data\AccountInterface $account
     * @return \Totaltools\StoreAccount\Api\Data\AccountInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Totaltools\StoreAccount\Api\Data\AccountInterface $account
    );

    /**
     * Retrieve Account
     * @param string $accountId
     * @return \Totaltools\StoreAccount\Api\Data\AccountInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($accountId);

    /**
     * Retrieve Account matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Totaltools\StoreAccount\Api\Data\AccountSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Account
     * @param \Totaltools\StoreAccount\Api\Data\AccountInterface $account
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Totaltools\StoreAccount\Api\Data\AccountInterface $account
    );

    /**
     * Delete Account by ID
     * @param string $accountId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($accountId);
}

