<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\StoreAccount\Api\Data;

interface AccountSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Account list.
     * @return \Totaltools\StoreAccount\Api\Data\AccountInterface[]
     */
    public function getItems();

    /**
     * Set customer_name list.
     * @param \Totaltools\StoreAccount\Api\Data\AccountInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

