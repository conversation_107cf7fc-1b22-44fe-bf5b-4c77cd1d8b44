<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\StoreAccount\Api\Data;

interface AccountInterface
{

    const SUBMISSION_DATE = 'submission_date';
    const EMAIL = 'email';
    const ESTIMATED_MONTHLY_SPEND = 'estimated_monthly_spend';
    const CUSTOMER_NAME = 'customer_name';
    const ABN = 'abn';
    const COMPANY_NAME = 'company_name';
    const ACCOUNT_ID = 'account_id';
    const PHONE = 'phone';
    const POSTCODE = 'postcode';
    const SELECTED_STORES = 'selected_stores';

    /**
     * Get account_id
     * @return string|null
     */
    public function getAccountId();

    /**
     * Set account_id
     * @param string $accountId
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setAccountId($accountId);

    /**
     * Get customer_name
     * @return string|null
     */
    public function getCustomerName();

    /**
     * Set customer_name
     * @param string $customerName
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setCustomerName($customerName);

    /**
     * Get email
     * @return string|null
     */
    public function getEmail();

    /**
     * Set email
     * @param string $email
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setEmail($email);

    /**
     * Get phone
     * @return string|null
     */
    public function getPhone();

    /**
     * Set phone
     * @param string $phone
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setPhone($phone);

    /**
     * Get abn
     * @return string|null
     */
    public function getAbn();

    /**
     * Set abn
     * @param string $abn
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setAbn($abn);

    /**
     * Get company_name
     * @return string|null
     */
    public function getCompanyName();

    /**
     * Set company_name
     * @param string $companyName
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setCompanyName($companyName);

    /**
     * Get estimated_monthly_spend
     * @return string|null
     */
    public function getEstimatedMonthlySpend();

    /**
     * Set estimated_monthly_spend
     * @param string $estimatedMonthlySpend
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setEstimatedMonthlySpend($estimatedMonthlySpend);

    /**
     * Get selected_stores
     * @return string|null
     */
    public function getSelectedStores();

    /**
     * Set selected_stores
     * @param string $selectedStores
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setSelectedStores($selectedStores);

    /**
     * Get postcode
     * @return string|null
     */
    public function getPostcode();

    /**
     * Set postcode
     * @param string $postcode
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setPostcode($postcode);

    /**
     * Get submission_date
     * @return string|null
     */
    public function getSubmissionDate();

    /**
     * Set submission_date
     * @param string $submissionDate
     * @return \Totaltools\StoreAccount\Account\Api\Data\AccountInterface
     */
    public function setSubmissionDate($submissionDate);
}

