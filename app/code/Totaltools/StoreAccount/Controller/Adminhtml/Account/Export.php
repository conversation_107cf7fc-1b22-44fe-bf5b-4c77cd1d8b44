<?php

namespace Totaltools\StoreAccount\Controller\Adminhtml\Account;

use Totaltools\StoreAccount\Model\AccountFactory as StoreAccountFactory;
use Magento\Framework\App\Filesystem\DirectoryList;

class Export extends \Magento\Backend\App\Action
{
    /** @var \Magento\Framework\View\Result\PageFactory */
    protected $resultPageFactory;
    
    /** @var StoreAccountFactory */
    protected $storeAccountFactory;

    /**
     * @var \Magento\Framework\App\Response\Http\FileFactory
     */
    protected $fileFactory;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvProcessor;

    /**
     * @var \Magento\Framework\App\Filesystem\DirectoryList
     */
    protected $directoryList;

    /**
     * Constructor
     *
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param StoreAccountFactory $storeAccountFactory
     * @param \Magento\Framework\App\Response\Http\FileFactory $fileFactory
     * @param \Magento\Framework\File\Csv                      $csvProcessor
     * @param \Magento\Framework\App\Filesystem\DirectoryList  $directoryList
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        StoreAccountFactory $storeAccountFactory,
        \Magento\Framework\App\Response\Http\FileFactory $fileFactory,
        \Magento\Framework\File\Csv $csvProcessor,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->storeAccountFactory = $storeAccountFactory;
        $this->fileFactory = $fileFactory;
        $this->csvProcessor = $csvProcessor;
        $this->directoryList = $directoryList;
        parent::__construct($context);
    }

    /**
     * Store Accounts Export To CSV
     *
     * @return \Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function execute()
    {
        $content[] = [
            'Customer Name',
            'Email',
            'Phone',
            'ABN',
            'Company Name',
            'Estimated Monthly Spend',
            'Selected Store',
            'Post Code',
            'Submission Date'
        ];
        $resultPage = $this->storeAccountFactory->create();
        $collection = $resultPage->getCollection();
        $fileName = 'store_accounts_export.csv';
        $filePath =  $this->directoryList->getPath(DirectoryList::MEDIA) . "/" . $fileName;
        while ($storeAccount = $collection->fetchItem()) {
            $content[] = [
                $storeAccount->getData('customer_name'),
                $storeAccount->getData('email'),
                $storeAccount->getData('phone'),
                $storeAccount->getData('abn'),
                $storeAccount->getData('company_name'),
                $storeAccount->getData('estimated_monthly_spend'),
                $storeAccount->getData('selected_stores'),
                $storeAccount->getData('postcode'),
                $storeAccount->getData('submission_date')
            ];
        }
        $this->csvProcessor->setEnclosure('"')->setDelimiter(',')->saveData($filePath, $content);
        return $this->fileFactory->create(
            $fileName,
            [
                'type'  => "filename",
                'value' => $fileName,
                'rm'    => true,
            ],
            DirectoryList::MEDIA,
            'text/csv',
            null
        );
    }
}
