<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\StoreAccount\Model\ResourceModel\Account;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'account_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Totaltools\StoreAccount\Model\Account::class,
            \Totaltools\StoreAccount\Model\ResourceModel\Account::class
        );
    }
}

