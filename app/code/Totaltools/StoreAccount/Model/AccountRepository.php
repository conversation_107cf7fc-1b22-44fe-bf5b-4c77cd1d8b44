<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\StoreAccount\Model;

use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\StoreAccount\Api\AccountRepositoryInterface;
use Totaltools\StoreAccount\Api\Data\AccountInterface;
use Totaltools\StoreAccount\Api\Data\AccountInterfaceFactory;
use Totaltools\StoreAccount\Api\Data\AccountSearchResultsInterfaceFactory;
use Totaltools\StoreAccount\Model\ResourceModel\Account as ResourceAccount;
use Totaltools\StoreAccount\Model\ResourceModel\Account\CollectionFactory as AccountCollectionFactory;

class AccountRepository implements AccountRepositoryInterface
{

    /**
     * @var ResourceAccount
     */
    protected $resource;

    /**
     * @var AccountCollectionFactory
     */
    protected $accountCollectionFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var AccountInterfaceFactory
     */
    protected $accountFactory;

    /**
     * @var Account
     */
    protected $searchResultsFactory;


    /**
     * @param ResourceAccount $resource
     * @param AccountInterfaceFactory $accountFactory
     * @param AccountCollectionFactory $accountCollectionFactory
     * @param AccountSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceAccount $resource,
        AccountInterfaceFactory $accountFactory,
        AccountCollectionFactory $accountCollectionFactory,
        AccountSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->accountFactory = $accountFactory;
        $this->accountCollectionFactory = $accountCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(AccountInterface $account)
    {
        try {
            $this->resource->save($account);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the account: %1',
                $exception->getMessage()
            ));
        }
        return $account;
    }

    /**
     * @inheritDoc
     */
    public function get($accountId)
    {
        $account = $this->accountFactory->create();
        $this->resource->load($account, $accountId);
        if (!$account->getId()) {
            throw new NoSuchEntityException(__('Account with id "%1" does not exist.', $accountId));
        }
        return $account;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->accountCollectionFactory->create();
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(AccountInterface $account)
    {
        try {
            $accountModel = $this->accountFactory->create();
            $this->resource->load($accountModel, $account->getAccountId());
            $this->resource->delete($accountModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Account: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($accountId)
    {
        return $this->delete($this->get($accountId));
    }
}

