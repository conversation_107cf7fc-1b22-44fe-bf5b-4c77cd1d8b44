<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\StoreAccount\Model;

use Magento\Framework\Model\AbstractModel;
use Totaltools\StoreAccount\Api\Data\AccountInterface;

class Account extends AbstractModel implements AccountInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Totaltools\StoreAccount\Model\ResourceModel\Account::class);
    }

    /**
     * @inheritDoc
     */
    public function getAccountId()
    {
        return $this->getData(self::ACCOUNT_ID);
    }

    /**
     * @inheritDoc
     */
    public function setAccountId($accountId)
    {
        return $this->setData(self::ACCOUNT_ID, $accountId);
    }

    /**
     * @inheritDoc
     */
    public function getCustomerName()
    {
        return $this->getData(self::CUSTOMER_NAME);
    }

    /**
     * @inheritDoc
     */
    public function setCustomerName($customerName)
    {
        return $this->setData(self::CUSTOMER_NAME, $customerName);
    }

    /**
     * @inheritDoc
     */
    public function getEmail()
    {
        return $this->getData(self::EMAIL);
    }

    /**
     * @inheritDoc
     */
    public function setEmail($email)
    {
        return $this->setData(self::EMAIL, $email);
    }

    /**
     * @inheritDoc
     */
    public function getPhone()
    {
        return $this->getData(self::PHONE);
    }

    /**
     * @inheritDoc
     */
    public function setPhone($phone)
    {
        return $this->setData(self::PHONE, $phone);
    }

    /**
     * @inheritDoc
     */
    public function getAbn()
    {
        return $this->getData(self::ABN);
    }

    /**
     * @inheritDoc
     */
    public function setAbn($abn)
    {
        return $this->setData(self::ABN, $abn);
    }

    /**
     * @inheritDoc
     */
    public function getCompanyName()
    {
        return $this->getData(self::COMPANY_NAME);
    }

    /**
     * @inheritDoc
     */
    public function setCompanyName($companyName)
    {
        return $this->setData(self::COMPANY_NAME, $companyName);
    }

    /**
     * @inheritDoc
     */
    public function getEstimatedMonthlySpend()
    {
        return $this->getData(self::ESTIMATED_MONTHLY_SPEND);
    }

    /**
     * @inheritDoc
     */
    public function setEstimatedMonthlySpend($estimatedMonthlySpend)
    {
        return $this->setData(self::ESTIMATED_MONTHLY_SPEND, $estimatedMonthlySpend);
    }

    /**
     * @inheritDoc
     */
    public function getSelectedStores()
    {
        return $this->getData(self::SELECTED_STORES);
    }

    /**
     * @inheritDoc
     */
    public function setSelectedStores($selectedStores)
    {
        return $this->setData(self::SELECTED_STORES, $selectedStores);
    }

    /**
     * @inheritDoc
     */
    public function getPostcode()
    {
        return $this->getData(self::POSTCODE);
    }

    /**
     * @inheritDoc
     */
    public function setPostcode($postcode)
    {
        return $this->setData(self::POSTCODE, $postcode);
    }

    /**
     * @inheritDoc
     */
    public function getSubmissionDate()
    {
        return $this->getData(self::SUBMISSION_DATE);
    }

    /**
     * @inheritDoc
     */
    public function setSubmissionDate($submissionDate)
    {
        return $this->setData(self::SUBMISSION_DATE, $submissionDate);
    }
}

