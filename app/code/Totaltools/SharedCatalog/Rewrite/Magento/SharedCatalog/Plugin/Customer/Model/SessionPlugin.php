<?php
/**
 * SessionPlugin
 *
 * @category  Totaltools
 * @package   Totaltools_SharedCatalog
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */


namespace Totaltools\SharedCatalog\Rewrite\Magento\SharedCatalog\Plugin\Customer\Model;

use Magento\Customer\Model\Session;

class SessionPlugin extends \Magento\SharedCatalog\Plugin\Customer\Model\SessionPlugin
{
    public function afterGetCustomerGroupId(Session $subject, $groupId)
    {
        
        if ( $subject->getCustomer()->getId() && $subject->getCustomerData()) {
            if ($groupId != $subject->getCustomerData()->getGroupId()) {
                $customerGroupId = $subject->getCustomerData()->getGroupId();
                $subject->setCustomerGroupId($customerGroupId);
                return $customerGroupId;
            }
        }
        return $groupId;
    }
}

