<?php

namespace Totaltools\Customer\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\Customer\Cron\MissingLoyaltyReport;

class GenerateMissingLoyaltyReport extends Command
{
    /**
     * @var MissingLoyaltyReport
     */
    private $missingLoyaltyReport;

    /**
     * @param MissingLoyaltyReport $missingLoyaltyReport
     */
    public function __construct(
        MissingLoyaltyReport $missingLoyaltyReport
    ) {
        parent::__construct();
        $this->missingLoyaltyReport = $missingLoyaltyReport;
    }

    /**
     * Configure the command
     */
    protected function configure()
    {
        $this->setName('totaltools:report:missing-loyalty')
            ->setDescription('Generate report of customers missing loyalty IDs');
    }

    /**
     * Execute the command
     *
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|null
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $output->writeln('<info>Generating Missing Loyalty ID Report...</info>');
            $this->missingLoyaltyReport->execute();
            $output->writeln('<info>Report generated successfully!</info>');
            return Command::SUCCESS;
        } catch (\Exception $e) {
            $output->writeln('<error>' . $e->getMessage() . '</error>');
            return Command::FAILURE;
        }
    }
}
