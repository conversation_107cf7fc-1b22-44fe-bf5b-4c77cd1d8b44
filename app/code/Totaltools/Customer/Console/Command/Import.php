<?php
/**
 * <AUTHOR> Nguyen (<EMAIL>)
 * @package Totaltools_Data
 */

namespace Totaltools\Customer\Console\Command;

use Magento\Framework\App\State as AppState;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Input\InputOption;
use Totaltools\Customer\Model\Import as ImportModel;

class Import extends Command
{
    const INPUT_KEY_EXTENDED = 'filename';

    /**
     * @var AppState
     */
    protected $appState;

    /**
     * @var AppState
     */
    protected $importModel;

    public function __construct(
        AppState $appState,
        ImportModel $importModel
    )
    {
        $this->appState = $appState;
        $this->importModel = $importModel;
        $this->appState->setAreaCode('admin');
        parent::__construct();
    }

    protected function configure()
    {
        $options = [
            new InputOption(
                self::INPUT_KEY_EXTENDED,
                null,
                InputOption::VALUE_OPTIONAL,
                'Get Filename info'
            ),
        ];
        $this->setName('customer:import:exec')
            ->setDescription('Import customer via csv provided')
            ->setDefinition($options);
        parent::configure();
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        if ($input->getOption(self::INPUT_KEY_EXTENDED)) {
            $fileName = $input->getOption(self::INPUT_KEY_EXTENDED);
            $this->importModel->execute($fileName, $output);
        } else {
            $output->writeln('<error>' . 'Please enter required option --filename' . '</error>');
        }
    }
}
