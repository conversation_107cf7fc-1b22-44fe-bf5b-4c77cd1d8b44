<?php
namespace Totaltools\Customer\Cron;

use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\Filesystem\Directory\WriteInterface;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Customer\Model\Email\Sender;
use Magento\Framework\Filesystem\Io\File;
use Magento\Framework\File\Csv;

class MissingLoyaltyReport
{
    private const XML_PATH_REPORT_DIR = 'totaltools_customer/loyalty_report/report_path';

    /**
     * @var CollectionFactory
     */
    private $customerCollectionFactory;

    /**
     * @var Filesystem
     */
    private $filesystem;

    /**
     * @var WriteInterface
     */
    private $directory;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var Sender
     */
    private $emailSender;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var File
     */
    private $ioFile;

    /**
     * @var Csv
     */
    private $csvProcessor;


    /**
     * @param CollectionFactory $customerCollectionFactory
     * @param Filesystem $filesystem
     * @param LoggerInterface $logger
     * @param StoreManagerInterface $storeManager
     * @param Sender $emailSender
     * @param ScopeConfigInterface $scopeConfig
     * @param File $ioFile
     * @param Csv $csvProcessor
     */
    public function __construct(
        CollectionFactory $customerCollectionFactory,
        Filesystem $filesystem,
        File $ioFile,
        LoggerInterface $logger,
        StoreManagerInterface $storeManager,
        Sender $emailSender,
        ScopeConfigInterface $scopeConfig,
        Csv $csvProcessor
    ) {
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->filesystem = $filesystem;
        $this->directory = $filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);
        $this->logger = $logger;
        $this->storeManager = $storeManager;
        $this->emailSender = $emailSender;
        $this->scopeConfig = $scopeConfig;
        $this->ioFile = $ioFile;
        $this->csvProcessor = $csvProcessor;
    }

    /**
     * Execute the cron job
     *
     * @return void|null
     */
    public function execute()
    {
        try {
            $this->logger->info('Starting Missing Loyalty ID Report generation');

            $collection = $this->customerCollectionFactory->create();
            $collection->addAttributeToSelect([
                'entity_id',
                'email',
                'firstname',
                'lastname',
                'created_at',
                'store_id',
                'loyalty_id'
            ])->addAttributeToFilter([
                ['attribute' => 'loyalty_id', 'null' => true],
                ['attribute' => 'loyalty_id', 'eq' => '']
            ], '', 'left');

            if ($collection->getSize() === 0) {
                $this->logger->info('No customers found without loyalty ID');
                return;
            }

            $csvData = [];
            $csvData[] = [
                'Customer ID',
                'Email',
                'First Name',
                'Last Name',
                'Store',
                'Registration Date'
            ];

            foreach ($collection as $customer) {
                try {
                    $storeName = $this->storeManager->getStore($customer->getStoreId())->getName();
                } catch (\Exception $e) {
                    $storeName = 'Unknown Store';
                }

                $csvData[] = [
                    $customer->getId(),
                    $customer->getEmail(),
                    $customer->getFirstname(),
                    $customer->getLastname(),
                    $storeName,
                    $customer->getCreatedAt()
                ];
            }

            
            $fullFilePath = $this->saveCsvReportToFile($csvData);

            
            $emailSent = $this->emailSender->sendReportEmail($fullFilePath, $collection->getSize());

            $this->logger->info(sprintf(
                'Missing Loyalty ID Report generated successfully. Found %d customers. File: %s. Email sent: %s',
                $collection->getSize(),
                $fullFilePath,
                $emailSent ? 'Yes' : 'No'
            ));

        } catch (\Exception $e) {
            $this->logger->error('Error generating Missing Loyalty ID Report: ' . $e->getMessage());
        }
    }

    /**
     * Save CSV report 
     *
     * @param array $csvData 
     * @return string The full file path of the saved report
     */
    public function saveCsvReportToFile(array $csvData): string
    {
        
        $reportDir = $this->scopeConfig->getValue(self::XML_PATH_REPORT_DIR);
        $reportDir = $reportDir ?: 'report/loyalty';
        
        
        $filename = sprintf(
            '%s_missing_loyalty_id_report.csv',
            date('d-m-y-h-i-s')
        );
        
        $filePath = $reportDir . DIRECTORY_SEPARATOR . $filename;
        
        
        if (!$this->ioFile->fileExists($reportDir, false)) {
            $this->ioFile->mkdir($reportDir);
        }
        
        
        if ($this->ioFile->fileExists($reportDir, false)) {
            $this->csvProcessor->saveData($filePath, $csvData);
        }
        
        
        return $filePath;
    }
}
