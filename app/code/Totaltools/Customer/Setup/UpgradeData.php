<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Customer\Setup;

use Magento\Cms\Model\BlockFactory;
use Magento\Customer\Model\Customer;
use Magento\Cms\Model\BlockRepository;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Setup\CustomerSetup;
use Magento\Framework\UrlFactory;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Exception\LocalizedException;
use Exception;

/**
 * Class UpgradeData
 * @package Totaltools\Customer\Setup
 */
class UpgradeData implements \Magento\Framework\Setup\UpgradeDataInterface
{
    /**
     * @var string $_setupVersion Current setup version.
     */
    private $_setupVersion;

    /**
     * @var \Magento\Customer\Setup\CustomerSetupFactory
     */
    private $_customerSetupFactory;

    /**
     * @var \Magento\Eav\Model\Entity\Attribute\SetFactory
     */
    private $_attributeSetFactory;

    /**
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var BlockFactory
     */
    private $blockFactory;

    /**
     * @var BlockRepository
     */
    private $blockRepository;
    /**
     * @var UrlFactory
     */
    private $urlFactory;

    /**
     * UpgradeData constructor.
     * @param \Magento\Customer\Setup\CustomerSetupFactory $customerSetupFactory
     * @param \Magento\Eav\Model\Entity\Attribute\SetFactory $setFactory
     * @param EavSetupFactory $eavSetupFactory
     * @param BlockFactory $blockFactory
     * @param BlockRepository $blockRepository
     */
    public function __construct(
        \Magento\Customer\Setup\CustomerSetupFactory $customerSetupFactory,
        \Magento\Eav\Model\Entity\Attribute\SetFactory $setFactory,
        EavSetupFactory $eavSetupFactory,
        BlockFactory $blockFactory,
        UrlFactory $urlFactory,
        BlockRepository $blockRepository
    ) {
        $this->_customerSetupFactory = $customerSetupFactory;
        $this->_attributeSetFactory = $setFactory;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->blockFactory = $blockFactory;
        $this->blockRepository = $blockRepository;
        $this->UrlModel = $urlFactory->create();
    }

    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $this->_setupVersion = $context->getVersion();

        $setup->startSetup();

        $this->_addCustomerAttributes($setup);
        $this->_updateCustomerAttributes($setup);

        if (version_compare($this->_setupVersion, '1.0.8') < 0) {
            $this->_addMobileNumberAttribute($setup);
            $this->_setVisibleDobAttribute($setup);
            $this->_updatePreferredStoreAttribute($setup);
            $this->_setDobRequired($setup);
            $this->_updateMobileNumberAttribute($setup);
            $this->_addPreferredStoreToRegistration($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.26') < 0) {
            $this->updateDobRequired($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.27') < 0) {
            $this->upgradeAddressStreetMultiline($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.29') < 0) {
            $this->updatePreferredStoreToNonRequired($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.30') < 0) {
            $this->addLoginStaticBlocks();
        }
        if (version_compare($this->_setupVersion, '1.0.31') < 0) {
            $this->addRegisterStaticBlocks();
            $this->addCustomerEditBottom();
            $this->addRegisterTermsAndCondition();
            $this->addSignUpTermsAndCondition();
            $this->addOnlineCustomerActivationDataModificationAgreement();
        }

        if (version_compare($this->_setupVersion, '1.0.32') < 0) {
            $this->addLoyaltyStatusAttribute($setup);
        }
        if (version_compare($this->_setupVersion, '1.0.33') < 0) {
            $this->addReferralCode($setup);
        }
        if (version_compare($this->_setupVersion, '1.0.34') < 0) {
            $this->_addCustomerAttributes($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.35') < 0) {
            $this->_updatePhoneNumberAttribute($setup);
        }
        if (version_compare($this->_setupVersion, '1.0.36') < 0) {
            $this->_addBusinessAccountAttribute($setup);
            $this->_addEstimatedMonthlySpendAttribute($setup);
            $this->_addCommercialAccountBlock($setup);
            
        }
        if (version_compare($this->_setupVersion, '1.0.37') < 0) {
            $this->addStoreAccountBlock($setup);
            $this->_addUnlockTextBlock($setup);
        }
        if (version_compare($this->_setupVersion, '1.0.38') < 0) {
            $this->addTradeRewardsTermsAndCondition();
        }
        $setup->endSetup();
    }

    /**
     * Remove
     *
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _removeLoyaltyPointsAttribute(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $customerEntity = $customerSetup->getEavConfig()->getEntityType(\Magento\Customer\Model\Customer::ENTITY);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->removeAttribute($entityTypeId, 'loyalty_points');
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _addPreferredStoreToRegistration(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $eavConfig = $customerSetup->getEavConfig();
        $customerEntity = $eavConfig->getEntityType(Customer::ENTITY);
        $attribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'preferred_store');
        $attribute->addData([
            'used_in_forms' => [
                'customer_account_edit',
                'customer_account_create',
                'adminhtml_customer'
            ],
        ])->save();

        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'preferred_store', 'is_required', '1');
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _updateMobileNumberAttribute(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $eavConfig = $customerSetup->getEavConfig();
        $customerEntity = $eavConfig->getEntityType(Customer::ENTITY);
        $attribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'mobile_number');
        $attribute->addData([
            'used_in_forms' => [
                'customer_account_edit',
                'customer_account_create',
                'adminhtml_customer'
            ],
        ])->save();

        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'mobile_number', 'is_required', '1');
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _setDobRequired(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $customerEntity = $customerSetup->getEavConfig()->getEntityType(Customer::ENTITY);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'dob', 'is_required', '1');
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function updateDobRequired(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $customerEntity = $customerSetup->getEavConfig()->getEntityType(Customer::ENTITY);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'dob', 'is_required', '0');
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _updatePreferredStoreAttribute(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $preferredStoreAttribute = $customerSetup->getEavConfig()->getAttribute(Customer::ENTITY, 'preferred_store');
        $preferredStoreAttribute->addData([
            'user_defined' => true,
            'used_in_forms' => [
                'customer_account_edit',
                'customer_account_create',
                'adminhtml_customer'
            ],
        ]);

        $preferredStoreAttribute->save();
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _setVisibleDobAttribute(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $customerEntity = $customerSetup->getEavConfig()->getEntityType(Customer::ENTITY);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'dob', 'is_visible', '1');
    }
    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _setDobNotRequired(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $customerEntity = $customerSetup->getEavConfig()->getEntityType(Customer::ENTITY);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'dob', 'is_required', '0');
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _addMobileNumberAttribute(ModuleDataSetupInterface $setup)
    {
        $this->_createCustomerAttribute(
            $setup,
            'mobile_number',
            Customer::ENTITY,
            [
                'label' => 'Mobile Number',
                'type' => 'varchar',
                'input' => 'text',
                'visible' => true,
                'required' => true,
                'system' => 0,
                'user_defined' => true,
                'position' => 1050
            ],
            [
                'customer_account_edit',
                'adminhtml_customer'
            ]
        );
    }

    /**
     * Create customer attribute.
     *
     * @param ModuleDataSetupInterface $setup
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _addCustomerAttributes(ModuleDataSetupInterface $setup)
    {
        if (version_compare($this->_setupVersion, '1.0.1') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'default_mailing_address',
                'customer_address',
                [
                    'label' => 'Use as my default mailing address',
                    'type' => 'int',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                    'input' => 'boolean',
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1040
                ],
                [
                    'customer_address_edit'
                ]
            );
        }

        if (version_compare($this->_setupVersion, '1.0.2') < 0) {
            $this->_removeLoyaltyPointsAttribute($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.3') < 0) {
            $customerAttributes = [
                'loyalty_id' => [
                    'label' => 'Loyalty ID',
                    'input' => 'text',
                    'type' => 'int',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'length' => 10,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1040,
                ],
                'is_subscribed_sms_promo' => [
                    'label' => 'Is Subscribed SMS Promo',
                    'input' => 'select',
                    'type' => 'int',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1050,
                ],
                'is_subscribed_direct_marketing' => [
                    'label' => 'Is Subscribed Direct Marketing',
                    'input' => 'select',
                    'type' => 'int',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1060,
                ],
                'pronto_position' => [
                    'label' => 'Position',
                    'input' => 'select',
                    'type' => 'int',
                    'source' => \Totaltools\Customer\Model\Attribute\Source\Position::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1070,
                ]
            ];

            foreach ($customerAttributes as $name => $params) {
                $this->_createCustomerAttribute(
                    $setup,
                    $name,
                    Customer::ENTITY,
                    $params,
                    ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
                );
            }
        }

        if (version_compare($this->_setupVersion, '1.0.4') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'pronto_position',
                Customer::ENTITY,
                [
                    'label' => 'Position',
                    'input' => 'select',
                    'type' => 'varchar',
                    'source' => \Totaltools\Customer\Model\Attribute\Source\Position::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1100
                ],
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );
        }

        if (version_compare($this->_setupVersion, '1.0.6') < 0) {
            // update loyalty_id
            $this->_createCustomerAttribute(
                $setup,
                'loyalty_id',
                Customer::ENTITY,
                [
                    'label' => 'Loyalty ID',
                    'input' => 'text',
                    'type' => 'varchar',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1090
                ],
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );
        }

        if (version_compare($this->_setupVersion, '1.0.7') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'account_code',
                Customer::ENTITY,
                [
                    'label' => 'Account Code',
                    'input' => 'text',
                    'type' => 'varchar',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1100,
                ],
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );
        }

        if (version_compare($this->_setupVersion, '1.0.10') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'pronto_position',
                Customer::ENTITY,
                [
                    'label' => 'Position',
                    'input' => 'select',
                    'type' => 'varchar',
                    'source' => \Totaltools\Customer\Model\Attribute\Source\Position::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1050
                ],
                ['adminhtml_customer']
            );
        }

        if (version_compare($this->_setupVersion, '1.0.11') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'pronto_position',
                Customer::ENTITY,
                [
                    'label' => 'My Trade',
                    'input' => 'select',
                    'type' => 'varchar',
                    'source' => \Totaltools\Customer\Model\Attribute\Source\Position::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => true,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1100
                ],
                ['adminhtml_customer', 'customer_account_create']
            );
        }

        if (version_compare($this->_setupVersion, '1.0.12') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'pronto_position',
                Customer::ENTITY,
                [
                    'label' => 'My Trade',
                    'input' => 'select',
                    'type' => 'varchar',
                    'source' => \Totaltools\Customer\Model\Attribute\Source\Position::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => true,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1100
                ],
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );

            $this->_createCustomerAttribute(
                $setup,
                'preferred_store',
                Customer::ENTITY,
                [
                    'label' => 'My Preferred Store',
                    'type' => 'int',
                    'source' => \Totaltools\Storelocator\Model\Config\Source\Store::class,
                    'input' => 'select',
                    'visible' => true,
                    'required' => true,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1120,
                ],
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );
        }

        if (version_compare($this->_setupVersion, '1.0.13') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'pronto_position',
                Customer::ENTITY,
                [
                    'label' => 'My Trade',
                    'input' => 'select',
                    'type' => 'varchar',
                    'source' => \Totaltools\Customer\Model\Attribute\Source\Position::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => true,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1100
                ],
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );
        }

        if (version_compare($this->_setupVersion, '1.0.19') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'customer_company',
                Customer::ENTITY,
                [
                    'label' => 'Company',
                    'input' => 'text',
                    'type' => 'varchar',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1055,
                ],
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );

            $this->_createCustomerAttribute(
                $setup,
                'role',
                Customer::ENTITY,
                [
                    'label' => 'My Role',
                    'input' => 'select',
                    'type' => 'int',
                    'source' => \Totaltools\Customer\Model\Attribute\Source\Role::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1070,
                ],
                [
                    'adminhtml_customer',
                ]
            );
        }

        if (version_compare($this->_setupVersion, '1.0.20') < 0) {
            $customerAttributes = [
                'unverified_loyalty_id' => [
                    'label' => 'Unverified Loyalty ID',
                    'input' => 'text',
                    'type' => 'varchar',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1110
                ],
                'email_verification_status' => [
                    'label' => 'Email Verification Status',
                    'input' => 'select',
                    'type' => 'varchar',
                    'source' => \Totaltools\Customer\Model\Attribute\Source\EmailVerification::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1120
                ],
                'email_verification_hash' => [
                    'label' => 'Email Verification Hash',
                    'input' => 'text',
                    'type' => 'varchar',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1130
                ],
                'email_verification_created_at' => [
                    'label' => 'Email Verification Created At',
                    'input' => 'date',
                    'type' => 'datetime',
                    'backend' => \Magento\Eav\Model\Entity\Attribute\Backend\Datetime::class,
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1140
                ],
            ];

            foreach ($customerAttributes as $name => $params) {
                $this->_createCustomerAttribute(
                    $setup,
                    $name,
                    Customer::ENTITY,
                    $params,
                    ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
                );
            }
        }

        if (version_compare($this->_setupVersion, '1.0.21') < 0) {

            $params = [
                'label' => 'Phone',
                'input' => 'select',
                'type' => 'varchar',
                'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                'backend' => \Magento\Customer\Model\Attribute\Backend\Data\Boolean::class,
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'visible' => true,
                'required' => false,
                'system' => 0,
                'user_defined' => true,
                'position' => 1070,
            ];

            $this->_createCustomerAttribute(
                $setup,
                'is_subscribed_phone_promo',
                Customer::ENTITY,
                $params,
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );
        }

        if (version_compare($this->_setupVersion, '1.0.22') < 0) {

            $customerAttributes = [
                'email_verification_hash' => [
                    'label' => 'Email Verification Hash',
                    'input' => 'text',
                    'type' => 'varchar',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1130
                ],
                'email_verification_created_at' => [
                    'label' => 'Email Verification Created At',
                    'input' => 'date',
                    'type' => 'datetime',
                    'backend' => \Magento\Eav\Model\Entity\Attribute\Backend\Datetime::class,
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => false,
                    'required' => false,
                    'system' => 0,
                    'user_defined' => true,
                    'position' => 1140
                ],
            ];

            foreach ($customerAttributes as $name => $params) {
                $this->_createCustomerAttribute(
                    $setup,
                    $name,
                    Customer::ENTITY,
                    $params,
                    []
                );
            }
        }

        if (version_compare($this->_setupVersion, '1.0.23') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'email_attempts_limit',
                Customer::ENTITY,
                [
                    'label'        => 'Email Verification Attempts Limit',
                    'input'        => 'text',
                    'type'         => 'int',
                    'source'       => \Magento\Eav\Model\Entity\Attribute\Source\Table::class,
                    'global'       => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'length'       => 1,
                    'visible'      => false,
                    'required'     => false,
                    'system'       => 0,
                    'default'      => 0,
                    'user_defined' => true,
                    'position'     => 1150
                ],
                []
            );
        }

        if (version_compare($this->_setupVersion, '1.0.34') < 0) {
            $this->_createCustomerAttribute(
                $setup,
                'is_subscribed',
                Customer::ENTITY,
                [
                    'label' => 'Email Subscribed',
                    'input' => 'select',
                    'type' => 'int',
                    'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                    'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                    'visible' => true,
                    'required' => false,
                    'system' => 0,
                    'default'      => 0,
                    'user_defined' => true,
                    'position' => 1160,
                ],
                ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
            );
        }
    }

    /**
     * Update customer attribute.
     *
     * @param ModuleDataSetupInterface $setup
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _updateCustomerAttributes(ModuleDataSetupInterface $setup)
    {
        if (version_compare($this->_setupVersion, '1.0.4') < 0) {
            /** @var CustomerSetup $customerSetup */
            $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
            $customerEntity = $customerSetup->getEavConfig()->getEntityType(Customer::ENTITY);
            $entityTypeId = $customerEntity->getEntityTypeId();

            $customerAttributes = [
                'loyalty_id',
                'is_subscribed_sms_promo',
                'is_subscribed_direct_marketing'
            ];
            foreach ($customerAttributes as $customerAttribute) {
                $customerSetup->updateAttribute($entityTypeId, $customerAttribute, 'is_visible', 0);
            }
        }

        if (version_compare($this->_setupVersion, '1.0.12') < 0) {
            $this->_setDobRequired($setup);
            $this->_updateMobileNumberAttribute($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.14') < 0) {
            $this->_setDobNotRequired($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.18') < 0) {
            $this->removeCustomerAttribute($setup, 'customer_preferences', Customer::ENTITY);
            $preferencesAttributes = [
                'is_subscribed_direct_marketing' =>
                    [
                        'label' => 'Mail',
                        'input' => 'select',
                        'type' => 'varchar',
                        'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                        'backend' => \Magento\Customer\Model\Attribute\Backend\Data\Boolean::class,
                        'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                        'visible' => true,
                        'required' => false,
                        'system' => 0,
                        'user_defined' => true,
                        'position' => 1060,
                    ],
                'is_subscribed_sms_promo' =>
                    [
                        'label' => 'SMS',
                        'input' => 'select',
                        'type' => 'varchar',
                        'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                        'backend' => \Magento\Customer\Model\Attribute\Backend\Data\Boolean::class,
                        'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                        'visible' => true,
                        'required' => false,
                        'system' => 0,
                        'user_defined' => true,
                        'position' => 1050,
                    ],
            ];

            foreach ($preferencesAttributes as $name => $params) {
                $this->_createCustomerAttribute(
                    $setup,
                    $name,
                    Customer::ENTITY,
                    $params,
                    ['adminhtml_customer', 'customer_account_create', 'customer_account_edit']
                );
            }
        }

        if (version_compare($this->_setupVersion, '1.0.17') < 0) {
            $this->_updateMobileNumberAttribute($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.25') < 0) {
            $this->updateEmailVerificationStatusAttribute($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.28') < 0) {
            $this->_addPreferredStoreToRegistration($setup);
        }
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function updateEmailVerificationStatusAttribute(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $customerEntity = $customerSetup->getEavConfig()->getEntityType(Customer::ENTITY);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'email_verification_status', 'default_value', EmailVerification::PENDING_SYNC);
        $customerSetup->updateAttribute($entityTypeId, 'email_verification_status', 'is_visible', 1);
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @param $attributeCode
     * @param $entityType
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function removeCustomerAttribute(ModuleDataSetupInterface $setup, $attributeCode, $entityType)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);

        $customerEntity = $customerSetup->getEavConfig()->getEntityType($entityType);
        $entityTypeId = $customerEntity->getEntityTypeId();

        $customerSetup->removeAttribute($entityTypeId, $attributeCode);
    }

    /**
     * Create customer address attribute by parameters.
     *
     * @param ModuleDataSetupInterface $setup
     * @param string $entityType
     * @param string $attributeCode
     * @param array $attributeParams
     * @param array $usedInForms
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _createCustomerAttribute($setup, $attributeCode, $entityType, $attributeParams, $usedInForms)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);

        $customerEntity = $customerSetup->getEavConfig()->getEntityType($entityType);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $attributeSetId = $customerEntity->getDefaultAttributeSetId();

        $attributeSet = $this->_attributeSetFactory->create();
        $attributeGroupId = $attributeSet->getDefaultGroupId($attributeSetId);

        $customerSetup->removeAttribute($entityTypeId, $attributeCode);
        $customerSetup->addAttribute(
            $entityTypeId,
            $attributeCode,
            $attributeParams
        );

        $magentoAttribute = $customerSetup->getEavConfig()->getAttribute($entityType, $attributeCode);
        $magentoAttribute->addData([
            'attribute_set_id'   => $attributeSetId,
            'attribute_group_id' => $attributeGroupId,
            'used_in_forms'      => $usedInForms,
        ]);

        $magentoAttribute->save();

        $isUsedForCustomerSegment = 1;
        if (array_key_exists('is_used_for_customer_segment', $attributeParams)) {
            $isUsedForCustomerSegment = $attributeParams['is_used_for_customer_segment'];
        }
        $customerSetup->updateAttribute($entityTypeId, $attributeCode, 'is_used_for_customer_segment', $isUsedForCustomerSegment);
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws LocalizedException
     */
    private function upgradeAddressStreetMultiline(ModuleDataSetupInterface $setup)
    {
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $row = $eavSetup->getAttribute('customer_address', 'street', 'multiline_count');

        if ($row === false || ! is_numeric($row)) {
            throw new LocalizedException(__('Could not find the "multiline_count" config of the "street" ' .
                'Customer address attribute.'));
        }

        if ($row !== 3) {
            $eavSetup->updateAttribute('customer_address', 'street', 'multiline_count', 3);
        }
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function updatePreferredStoreToNonRequired(ModuleDataSetupInterface $setup)
    {
        /** @var CustomerSetup $customerSetup */
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $customerEntity = $customerSetup->getEavConfig()->getEntityType(Customer::ENTITY);
        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'preferred_store', 'is_required', '0');
    }

    private function addLoginStaticBlocks()
    {
        try {
            $topBlock = [
                'title' => 'Customer Login Top Text',
                'identifier' => 'customer_login_top_text',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "If you're an Insider Reward Member use your email address and password to log in below."
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);

            $bottomBlock = [
                'title' => 'Customer Login Bottom Text',
                'identifier' => 'customer_login_bottom_text',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "Never logged in online before?
                Simply create an online account using here using your Insider Reward email address and mobile."
            ];
            $block = $this->blockFactory->create(['data' => $bottomBlock]);
            $this->blockRepository->save($block);
        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }

    private function addRegisterStaticBlocks()
    {
        try {
            $topBlock = [
                'title' => 'Customer Register Top Text',
                'identifier' => 'customer_register_top_text',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "Please enter the email address and/or the mobile number registered against your insider account."
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);
        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }

    private function addCustomerEditBottom()
    {
        try {
            $topBlock = [
                'title' => 'Customer Edit Bottom Privacy Policy Text',
                'identifier' => 'customer_edit_privacy_policy',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => 'To see how we manage your personal information please see our <a target="_blank" href="https://www.totaltools.com.au/media/product-attachments/Insider-Rewards-Privacy-Policy-November-2017-ver01.pdf"><strong>Insider Rewards Privacy Policy</strong></a>'
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);

        } catch(Exception $e) {
                echo $e->getMessage();
        }
    }

    /**
     *
     */
    private function addRegisterTermsAndCondition()
    {
        $termsAndConditions = $this->UrlModel->getUrl('terms-conditions');
        try {
            $topBlock = [
                'title' => 'Terms and conditions link label',
                'identifier' => 'terms_and_condition_link_label',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => 'By submitting this form, you agree to the Tool Tools
                <a href="'.$termsAndConditions.'" target="_blank">Terms and Conditions</a>,
                <a href="/media/product-attachments/TT-Insider-Program-terms-and-conditions-2019.pdf">Insider Rewards Terms and Conditions</a> and
                <a href="/media/product-attachments/Insider-Rewards-Privacy-Policy-November-2017-ver01.pdf" target="_blank">Privacy Policy.</a>'
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);
        } catch(Exception $e) {
                echo $e->getMessage();
        }
    }

    private function addSignUpTermsAndCondition()
    {
        $termsAndConditions = $this->UrlModel->getUrl('terms-conditions');
        try {
            $topBlock = [
                'title' => 'Terms and conditions signup label',
                'identifier' => 'terms_and_condition_signup_label',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => 'By clicking Register, you agree to Total Tools
                <a href="'.$termsAndConditions.'" target="_blank">Terms and Conditions</a>,
                <a href="/media/product-attachments/TT-Insider-Program-terms-and-conditions-2019.pdf">Insider Rewards Terms and Conditions</a> and
                <a href="/media/product-attachments/Insider-Rewards-Privacy-Policy-November-2017-ver01.pdf" target="_blank">Privacy Policy.</a>'
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);
        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }

    private function addTradeRewardsTermsAndCondition()
    {
        $termsAndConditions = $this->UrlModel->getUrl('terms-conditions');
        try {
            $topBlock = [
                'title' => 'Trade rewards terms and conditions',
                'identifier' => 'trade-rewards-terms-and-conditions',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => 'By clicking Continue, you agree to Total Tools
                <a href="'.$termsAndConditions.'" target="_blank">Terms and Conditions</a>,
                <a href="{{media url=\'product-attachments/TT-Trade%20Rewards%20Program-Terms-and-Conditions-Feb2024.pdf\'}}">Trade Rewards Terms and Conditions</a> and
                <a href="{{media url=\'product-attachments/Trade-Rewards-Privacy-Policy-Feb2024.pdf\'}}" target="_blank">Privacy Policy.</a>'
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);
        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }

    private function addOnlineCustomerActivationDataModificationAgreement()
    {

        $termsAndConditions = $this->UrlModel->getUrl('terms-conditions');
        try {
            $topBlock = [
                'title' => 'Customer data modification agreement',
                'identifier' => 'customer_data_modification_agreement',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => 'By clicking register now, you agree to Total Tools
                <a href="'.$termsAndConditions.'" target="_blank">Terms and Conditions</a>,
                <a href="/media/product-attachments/TT-Insider-Program-terms-and-conditions-2019.pdf">Insider Rewards Terms and Conditions</a> and
                <a href="/media/product-attachments/Insider-Rewards-Privacy-Policy-November-2017-ver01.pdf" target="_blank">Privacy Policy.</a>'
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);
        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws LocalizedException
     */
    private function addLoyaltyStatusAttribute(ModuleDataSetupInterface $setup)
    {
        $this->_createCustomerAttribute(
            $setup,
            'loyalty_status',
            Customer::ENTITY,
            [
                'label' => 'Loyalty Status',
                'type' => 'int',
                'source' => \Totaltools\Pronto\Model\Source\LoyaltyStatus::class,
                'input' => 'select',
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'length' => 2,
                'visible' => true,
                'required' => false,
                'system' => 0,
                'default'=> 0,
                'user_defined' => true,
                'position' => 1080,
            ],
            ['adminhtml_customer']
        );
    }

      /**
     * @param ModuleDataSetupInterface $setup
     * @throws LocalizedException
     */
    private function addReferralCode(ModuleDataSetupInterface $setup)
    {
        $this->_createCustomerAttribute(
            $setup,
            'referred_by',
            Customer::ENTITY,
            [
                'label' => 'Referred By',
                'input' => 'text',
                'type' => 'varchar',
                'source' => '',
                'required' => false,
                'position' => 1090,
                'visible' => true,
                'system' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'is_searchable_in_grid' => false,
                'backend' => ''
            ],
            ['customer_account_create']
        );

        $this->_createCustomerAttribute(
            $setup,
            'referral_code',
            Customer::ENTITY,
            [
                'label' => 'Referral Code',
                'input' => 'text',
                'type' => 'varchar',
                'source' => '',
                'required' => false,
                'position' => 1095,
                'visible' => true,
                'system' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'is_searchable_in_grid' => false,
                'backend' => ''
            ],
            ['adminhtml_customer']
        );
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws LocalizedException
     */
    protected function _updatePhoneNumberAttribute($setup) {
        $customerSetup = $this->_customerSetupFactory->create(['setup' => $setup]);
        $eavConfig = $customerSetup->getEavConfig();
        $customerEntity = $eavConfig->getEntityType('customer_address');
        $entityTypeId = $customerEntity->getEntityTypeId();
        $customerSetup->updateAttribute($entityTypeId, 'telephone', 'is_required', '0');
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws LocalizedException
     */
    protected function _addBusinessAccountAttribute($setup) {
        $this->_createCustomerAttribute(
            $setup,
            'business_account',
            Customer::ENTITY,
            [
                'label' => 'Business Account',
                'type' => 'int',
                'source' => \Magento\Eav\Model\Entity\Attribute\Source\Boolean::class,
                'input' => 'boolean',
                'required' => false,
                'position' => 1100,
                'visible' => true,
                'system' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'is_searchable_in_grid' => true,
                'backend' => ''
            ],
            ['customer_account_create', 'adminhtml_customer']
        );
        $this->_createCustomerAttribute(
            $setup,
            'abn',
            Customer::ENTITY,
            [
                'label' => 'ABN',
                'input' => 'text',
                'type' => 'varchar',
                'source' => '',
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'required' => false,
                'length' => 11,
                'position' => 1105,
                'visible' => true,
                'system' => false,
                'unique' => true,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'is_searchable_in_grid' => true,
                'backend' => ''
            ],
            ['customer_account_create', 'adminhtml_customer']
        );

        try {
            $rightBlock = [
                'title' => 'Customer Trade Rewards Conversion',
                'identifier' => 'customer_business_account_conversion_right',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "Please enter your ABN and Company Name to join Trade Rewards."
            ];
            $block = $this->blockFactory->create(['data' => $rightBlock]);
            $this->blockRepository->save($block);

            $topBlock = [
                'title' => 'Customer Trade Rewards Conversion Top ',
                'identifier' => 'customer_business_account_conversion_top',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "Please enter your ABN and Company Name to join Trade Rewards."
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);

            $businessTopBlock = [
                'title' => 'Customer CREATE Trade Rewards Content Top',
                'identifier' => 'customer_create_business_account_content_top',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "Create an account today, we'll save your details for easy checkout later and manage orders from your own dashboard."
            ];
            $block = $this->blockFactory->create(['data' => $businessTopBlock]);
            $this->blockRepository->save($block);

            $businessBenefitsBlock = [
                'title' => 'Customer create Trade Rewards Benefits',
                'identifier' => 'customer_create_business_account_benefits',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' =>'<h2>WHY BECOME A MEMBER?</h2>
                <p >Join Today and benefit from bulk pricing, credit options, digital tools, dedicated support and much more!</p>
                <ul>
                <li>Exclusive Member Deals and Events</li>
                <li>Earn and Redeem Insider Dollars</li>
                <li>Digitally Saved Receipts</li>
                <li>Shop anywhere, instore or online</li>
                </ul>'
            ];
            $block = $this->blockFactory->create(['data' => $businessBenefitsBlock]);
            $this->blockRepository->save($block);

            $registerPageTopBlock = [
                'title' => 'TRADE REWARDS JOINING TOP',
                'identifier' => 'trade_rewards_member_registration_top_text',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "Please enter your insider member email and/or the mobile number to join Trade Rewards."
            ];
            $block = $this->blockFactory->create(['data' => $registerPageTopBlock]);
            $this->blockRepository->save($block);

        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws LocalizedException
     */
    protected function _addCommercialAccountBlock($setup) {
        

        try {
           

            $topBlock = [
                'title' => 'Commercial Account Top Text',
                'identifier' => 'commercial_account_top_text',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "Please enter your details to apply for Commercial Account Rewards."
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);


            $commercialBenefitsBlock = [
                'title' => 'Commercial Account Benefits',
                'identifier' => 'commercial_account_benefits',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' =>'<div class="block-top">
                <div class="block-title"><strong>WHY BECOME A Commercial Account Holder?</strong></div>
                <div class="block-content">
                <p>National Account for Large business or government:</p>
                <ul>
                 <li>National Commercial Pricing.</li>
                 <li>Dedicated Account Management.</li>
                 <li>Collection and Delivery Options.</li>
                 <li>Instore and Account Manager Orders Nationally.</li>
                </ul>
                </div>
                </div>'
            ];
            $block = $this->blockFactory->create(['data' => $commercialBenefitsBlock]);
            $this->blockRepository->save($block);

        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }


    /**
     * @param ModuleDataSetupInterface $setup
     * @throws LocalizedException
     */
    protected function addStoreAccountBlock($setup) 
    {
        try {
            $storeBenefitsBlock = [
                'title' => 'Store Account Benefits',
                'identifier' => 'store_account_benefits',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' =>'<div class="block-top">
                <div class="block-title"><strong>WHY BECOME A Store Account Holder?</strong></div>
                <div class="block-content">
                    <ul>
                        <li>Access to 30 or 60-day credit</li>
                        <li>Shop at local Total Tools</li>
                        <li>No Online Shopping</li>
                        <li>Access to dedicate Account representative</li>
                        <li>Subject to local store Approval</li>
                    </ul>
                </div>
                </div>'
            ];
            $block = $this->blockFactory->create(['data' => $storeBenefitsBlock]);
            $this->blockRepository->save($block);
        } catch (Exception $e) {
            echo $e->getMessage();
        }
    }

    /**
     * @param ModuleDataSetupInterface $setup
     * @throws LocalizedException
     */
    protected function _addEstimatedMonthlySpendAttribute($setup) {
        $this->_createCustomerAttribute(
            $setup,
            'estimated_monthly_spend',
            Customer::ENTITY,
            [
                'label' => 'Estimated Monthly Spend',
                'input' => 'text',
                'type' => 'varchar',
                'source' => '',
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'required' => false,
                'position' => 1125,
                'visible' => true,
                'system' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'is_searchable_in_grid' => true,
                'backend' => ''
            ],
            ['customer_account_create', 'adminhtml_customer']
        );
    }

    protected function _addUnlockTextBlock($set) {
        try {
           

            $topBlock = [
                'title' => 'Unlock Trade Account creation',
                'identifier' => 'unlock_trade_account',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "Once you click on continue button, you will be transferred to the Unlocked site to continue your registration."
            ];
            $block = $this->blockFactory->create(['data' => $topBlock]);
            $this->blockRepository->save($block);

        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }
}
