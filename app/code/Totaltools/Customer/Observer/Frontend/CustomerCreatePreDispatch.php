<?php

namespace Totaltools\Customer\Observer\Frontend;

/**
 * @package     Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @since       1.0.31
 * @copyright   2023 (c) Totaltools. <https://totaltools.com.au>
 */

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\RequestInterface;

/**
 * @class CustomerCreatePreDispatch
 */
class CustomerCreatePreDispatch implements ObserverInterface
{
   

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * RequestInterface
     *
     * @var RequestInterface
     */
    protected $request;

    /**
     * @param CustomerSession $customerSession
     * @param RequestInterface $RequestInterface
     */
    public function __construct(
        CustomerSession $customerSession,
        RequestInterface $request
        
    ) {
        $this->customerSession = $customerSession;
        $this->request = $request;
    }

    /**
     * @inheritdoc
     */
    public function execute(Observer $observer)
    {
        $param = $this->request->getParam('trade_rewards');
        
        if ($param) {
            $this->customerSession->setTradeRedirect($param);
            $this->customerSession->setTradeRewardService($param);
        }
    }

}
