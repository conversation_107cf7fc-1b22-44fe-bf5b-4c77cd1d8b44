<?php

namespace Totaltools\Customer\Observer\Frontend;

/**
 * @package Totatools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 */

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\App\ResponseFactory;
use Magento\Framework\UrlInterface;
use Magento\Framework\Message\ManagerInterface;
use Totaltools\Customer\Model\ProntoSyncManager;
use Totaltools\Customer\Helper\AttributeData;
use Totaltools\Reward\Api\RewardValidatorInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Framework\Controller\Result\RedirectFactory;

class CartPagePreDispatch extends CustomerSectionPredispatch
{
    /**
     * @var ProntoSyncManager
     */
    private $prontoSyncManager;
    /**
     * @var RewardValidatorInterface
     */
    private $rewardValidator;
    /**
     * @var CustomerFactory
     */
    private $customerFactory;
    /**
     * @param CustomerSession $customerSession
     * @param ProntoSyncManager $prontoSyncManager
     * @param ResponseFactory $responseFactory
     * @param UrlInterface $url
     * @param ManagerInterface $messageManager
     * @param AttributeData $customerHelper
     * @param RewardValidatorInterface $rewardValidator
     * @param CustomerFactory $customerFactory
     */
    public function __construct(
        CustomerSession $customerSession,
        ProntoSyncManager $prontoSyncManager,
        ResponseFactory $responseFactory,
        UrlInterface $url,
        ManagerInterface $messageManager,
        AttributeData $customerHelper,
        RewardValidatorInterface $rewardValidator,
        CustomerFactory $customerFactory,
        RedirectFactory $resultRedirectFactory
    ) {
        parent::__construct($customerSession, $url, $responseFactory, $messageManager, $customerHelper, $resultRedirectFactory);
        $this->prontoSyncManager = $prontoSyncManager;
        $this->customerSession = $customerSession;
        $this->url = $url;
        $this->responseFactory = $responseFactory;
        $this->messageManager = $messageManager;
        $this->customerHelper = $customerHelper;
        $this->rewardValidator = $rewardValidator;
        $this->customerFactory = $customerFactory;
    }

    /**
     * @inheritdoc
     */
    public function execute(Observer $observer)
    {
        try {
            $customerId = $this->customerSession->getCustomer()->getId();
            if ($customerId) {
                $customer = $this->customerSession->getCustomerData();
                if ($customer instanceof CustomerInterface && !$this->isUnverified($customer)) {
                    $this->prontoSyncManager->updateLoyaltyData($customer);
                    $customerData = $this->customerFactory->create()->load($customer->getId());
                    $loyaltyId = $customerData->getData('loyalty_id');
                    if(isset($loyaltyId)) {
                        $this->rewardValidator->setCustomer($customerData)->validate($loyaltyId, 0);
                    }
                }
            }
        } catch (\Exception $exception) {
            // TODO: add logger to add exception
        }
    }
}
