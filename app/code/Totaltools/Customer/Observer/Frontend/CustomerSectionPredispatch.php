<?php

namespace Totaltools\Customer\Observer\Frontend;

/**
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @since       1.0.31
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\Session as CustomerSession;
use Magento\Framework\UrlInterface;
use Magento\Framework\App\ResponseFactory;
use Magento\Framework\Message\ManagerInterface;
use Totaltools\Customer\Helper\AttributeData;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use Magento\Framework\Controller\Result\RedirectFactory;

/**
 * @class CustomerSectionPredispatch
 */
class CustomerSectionPredispatch implements ObserverInterface
{
    /**
     * @var string
     */
    const CUSTOMER_HOME = 'loyalty/insider/info';

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var UrlInterface
     */
    protected $url;

    /**
     * @var ResponseFactory
     */
    protected $responseFactory;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var AttributeData
     */
    protected $customerHelper;

    /**
     * @var RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @param CustomerSession $customerSession
     * @param UrlInterface $url
     * @param ResponseFactory $responseFactory
     * @param ManagerInterface $messageManager,
     * @param AttributeData $customerHelper
     */
    public function __construct(
        CustomerSession $customerSession,
        UrlInterface $url,
        ResponseFactory $responseFactory,
        ManagerInterface $messageManager,
        AttributeData $customerHelper,
        RedirectFactory $resultRedirectFactory,
        
    ) {
        $this->customerSession = $customerSession;
        $this->url = $url;
        $this->responseFactory = $responseFactory;
        $this->messageManager = $messageManager;
        $this->customerHelper = $customerHelper;
        $this->resultRedirectFactory = $resultRedirectFactory;
    }

    /**
     * @inheritdoc
     */
    public function execute(Observer $observer)
    {
        $customerId = $this->customerSession->getCustomer()->getId();
        $redirectionUrl = $this->url->getUrl(self::CUSTOMER_HOME);
        
        if ($this->customerHelper->redirectToUnlock()) {
            
            if ($this->customerHelper->isTradeRewards()) {
                $this->customerHelper->showUnlockMessage();    
            } else {
                $resultRedirect = $this->resultRedirectFactory->create();
                $url = $this->customerHelper->getBusinessAccountUrl();       
                $resultRedirect->setUrl($url);
                return $resultRedirect;
            }   
        }
        if ($customerId) {
            $customer = $this->customerSession->getCustomerData();

            if ($customer instanceof CustomerInterface
                && $this->isUnverified($customer)
                && !$this->customerHelper->isB2bCustomer()
            ) {
                
                
                $this->messageManager->addNoticeMessage(__("Please verify your account to continue."));
                $this->responseFactory->create()->setRedirect($redirectionUrl)->sendResponse();
                return;
            }
        }
    }

    /**
     * @param CustomerInterface $customer
     * @return bool
     */
    protected function isUnverified($customer)
    {
        $emailVerificationStatus = $customer->getCustomAttribute('email_verification_status');
        $verificationStatus = $emailVerificationStatus ? $emailVerificationStatus->getValue() : '';

        return $verificationStatus == EmailVerification::PENDING_VERIFICATION
        || $verificationStatus == EmailVerification::MANUAL_VERIFICATION_REQUIRED;
    }
}
