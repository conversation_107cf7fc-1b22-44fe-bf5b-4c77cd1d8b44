<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Customer\Observer\Adminhtml;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Totalto<PERSON>\Customer\Model\Attribute\Source\EmailVerification;

/**
 * Class CustomerSaveAfter
 * @package Totaltools\Customer\Observer\Adminhtml
 */
class CustomerSaveAfter implements ObserverInterface
{
    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * CustomerSaveAfter constructor.
     * @param CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        CustomerRepositoryInterface $customerRepository
    )
    {
        $this->customerRepository = $customerRepository;
    }

    /**
     * @param Observer $observer
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\State\InputMismatchException
     */
    public function execute(Observer $observer)
    {
        /**@var \Magento\Customer\Api\Data\CustomerInterface $customer*/
        $customer = $observer->getData('customer');
        $unverifiedLoyaltyIdAttribute = $customer->getCustomAttribute('unverified_loyalty_id');
        $verificationStatusAttribute = $customer->getCustomAttribute('email_verification_status');

        if ($verificationStatusAttribute &&
            $verificationStatusAttribute->getValue() === EmailVerification::VERIFIED &&
            $unverifiedLoyaltyIdAttribute &&
            $unverifiedLoyaltyIdAttribute->getValue() !== null
        ) {
            $customer->setCustomAttribute('unverified_loyalty_id', null);
            $this->customerRepository->save($customer);
        }
    }
}