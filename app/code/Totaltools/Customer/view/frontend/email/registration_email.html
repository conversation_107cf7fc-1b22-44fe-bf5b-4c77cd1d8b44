<!--@subject {{trans "Customer Email template from %store_name" store_name=$store.getFrontendName()}} @-->

{{template config_path="design/email/header_template"}}

<p class="greeting">{{trans "%name," name=name}}</p>
<p>{{trans "Welcome to %store_name." store_name=store}}</p>
<p>
    {{trans
    'To sign in to our site, use these credentials during checkout or on the <a href="%customer_url">my account</a> page:'

    customer_url=$this.getUrl($store,'customer/account/',[_nosid:1])
    |raw}}
</p>
<ul class="credentials">
    <li><strong>{{trans "Email:"}}</strong> {{var email}}</li>
    <li><strong>{{trans "Password:"}}</strong> <em>{{var password}}</em></li>
</ul>
<div class="account-rules-box">
    <p class="account-rules-title">{{trans "When you sign in to your account, you will be able to:"}}</p>
    <ul class="account-rules">
        <li class="checkout_faster" style="background: url({{store url=''}}static/frontend/Totaltools/base/en_AU/images/email/icons/checkout_faster.png) no-repeat;">
            <div class="text">{{trans "Proceed through checkout faster"}}</div>
        </li>
        <li class="check_orders" style="background: url({{store url=''}}static/frontend/Totaltools/base/en_AU/images/email/icons/check_order.png) no-repeat;">
            <div class="text">{{trans "Check the status of orders"}}</div>
        </li>
        <li class="store_addresses" style="background: url({{store url=''}}static/frontend/Totaltools/base/en_AU/images/email/icons/store_addresses.png) no-repeat;">
            <div class="text">Store alternative addresses <p class="sub-text">For shipping to multiple family members and friends</p></div>
        </li>
        <li class="view_orders" style="background: url({{store url=''}}static/frontend/Totaltools/base/en_AU/images/email/icons/view_orders.png) no-repeat;">
            <div class="text">{{trans "View past orders"}}</div>
        </li>
    </ul>
</div>

{{template config_path="design/email/footer_template"}}