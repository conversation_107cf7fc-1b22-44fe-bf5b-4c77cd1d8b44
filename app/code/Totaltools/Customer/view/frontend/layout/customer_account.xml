<?xml version="1.0"?>
<!--
/**
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au> 
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" design_abstraction="custom">
    <body>
        <referenceContainer name="sidebar.main">
            <!-- Account Dashboard Nav Links-->
            <referenceBlock name="customer-account-navigation-account-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">my-account</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-address-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">addresses</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-account-edit-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">profile</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-my-profile-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">profile</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-address-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">addresses</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-requisition-list-link">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Totaltools_Customer::nav/link.phtml</argument>
                </action>
                <arguments>
                    <argument name="css_class" xsi:type="string">orders</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-orders-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">orders</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-instoreorder-invoices-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">invoices</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-product-reviews-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">reviews</argument>
                    <argument name="label" xsi:type="string" translate="true">Reviews</argument>
                    <argument name="sortOrder" xsi:type="number">150</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-my-credit-cards-link">
                <arguments>
                    <argument name="css_class" xsi:type="string">payment-methods</argument>
                    <argument name="label" xsi:type="string" translate="true">Stored Payment Details</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-wish-list-link">
                <arguments>
                    <argument name="path" xsi:type="string">wishlist/index</argument>
                    <argument name="css_class" xsi:type="string">wishlist</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-company-link">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Totaltools_Customer::nav/link.phtml</argument>
                </action>
                <arguments>
                    <argument name="css_class" xsi:type="string">company-structure</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-company-users-link">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Totaltools_Customer::nav/link.phtml</argument>
                </action>
                <arguments>
                    <argument name="css_class" xsi:type="string">company-users</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-company-profile-link">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Totaltools_Customer::nav/link.phtml</argument>
                </action>
                <arguments>
                    <argument name="css_class" xsi:type="string">company-profile</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-company-roles-link">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Totaltools_Customer::nav/link.phtml</argument>
                </action>
                <arguments>
                    <argument name="css_class" xsi:type="string">company-roles</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-instoreorder-list-link">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Totaltools_Customer::nav/link.phtml</argument>
                </action>
                <arguments>
                    <argument name="css_class" xsi:type="string">insider</argument>
                </arguments>
            </referenceBlock>

            <referenceBlock name="customer-account-navigation-delimiter-1" remove="true" />
            <referenceBlock name="customer-account-navigation-delimiter-2" remove="true" />
            <referenceBlock name="customer-account-navigation-delimiter-container-b2b" remove="true" />
            <referenceBlock name="customer-account-navigation-delimiter-b2b" remove="true" />
            <referenceBlock name="customer-account-navigation-newsletter-subscriptions-link" remove="true" />
            <referenceBlock name="customer-account-navigation-my-credit-cards-link" remove="true"/>
        </referenceContainer>
    </body>
</page>
