<?xml version="1.0" ?>
<page layout="1column"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
  
    <body>
        <attribute name="class" value="customer-account-register"/>
        <referenceBlock name="breadcrumbs">
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Home</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Home</item>
                    <item name="label" xsi:type="string">Home</item>
                    <item name="link" xsi:type="string">/</item>
                </argument>
            </action>
            <action method="addCrumb">
                <argument name="crumbName" xsi:type="string">Register</argument>
                <argument name="crumbInfo" xsi:type="array">
                    <item name="title" xsi:type="string">Register</item>
                    <item name="label" xsi:type="string">Register</item>
                </argument>
            </action>
        </referenceBlock>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument name="title" xsi:type="helper"
                                    helper="Totaltools\Customer\Helper\Data::getRewardsRegisterAccountTitle">
                </argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <container name="customer.register.container" label="Customer Registration Container" htmlTag="div"
                       htmlClass="register-container">
                <block class="Totaltools\Customer\Block\Account\Register" name="account.register" template="Totaltools_Customer::account/register.phtml" cacheable="false">
                    <container name="form.additional.info">
                        <block class="Magento\ReCaptchaUi\Block\ReCaptcha"
                            name="recaptcha"
                            after="-"
                            template="Magento_ReCaptchaFrontendUi::recaptcha.phtml"
                            ifconfig="recaptcha_frontend/type_for/customer_register">
                            <arguments>
                                <argument name="recaptcha_for" xsi:type="string">customer_register</argument>
                                <argument name="jsLayout" xsi:type="array">
                                    <item name="components" xsi:type="array">
                                        <item name="recaptcha" xsi:type="array">
                                            <item name="component" xsi:type="string">Magento_ReCaptchaFrontendUi/js/reCaptcha</item>
                                        </item>
                                    </item>
                                </argument>
                            </arguments>
                        </block>
                    </container>
                </block>
            </container>
        </referenceContainer>
    
    </body>
</page>