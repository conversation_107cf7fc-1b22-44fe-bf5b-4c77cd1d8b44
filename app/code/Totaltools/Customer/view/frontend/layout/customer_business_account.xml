<?xml version="1.0" encoding="UTF-8"?>
<page  layout="1column"  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <title>INSIDER REWARDS MEMBER SIGN UP TO TRADE REWARDS</title>
    </head>
    <body>
        <referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">INSIDER REWARDS MEMBER SIGN UP TO TRADE REWARDS</argument>
            </action>
        </referenceBlock>
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template" name="business.account" template="Totaltools_Customer::business/account.phtml" />
        </referenceContainer>
        <attribute name="class" value="customer-account-login"/>
        <attribute name="class" value="customer-business-account"/>
		<referenceContainer name="content">
		 	<container  label="Bussiness Account" htmlTag="div" htmlClass="login-container business-account">
				<block class="Totaltools\Customer\Block\Business\Account" name="business.account" 
					template="Totaltools_Customer::business/account.phtml" cacheable="false"/>
			</container>
		</referenceContainer>
    </body>
</page>