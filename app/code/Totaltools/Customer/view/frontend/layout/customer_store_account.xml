<?xml version="1.0" encoding="UTF-8"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
   <head>
        <title>ACCELERATE YOUR BUSINESS WITH A STORE ACCOUNT</title>
    </head>
    <body>
		<referenceBlock name="page.main.title">
            <action method="setPageTitle">
                <argument translate="true" name="title" xsi:type="string">ACCELERATE YOUR BUSINESS WITH A STORE ACCOUNT</argument>
            </action>
        </referenceBlock>
		 <referenceContainer name="content">
             <container name="customer.register.wrapper" htmlTag="div" htmlClass="customer-register-wrapper">
                <container name="customer.register.form.wrapper" htmlTag="div" htmlClass="customer-register-form-wrapper" before="-">
                    <block class="Totaltools\Customer\Block\Store\Account"  name="store.account.form" as="store_account" template="Totaltools_Customer::store/account.phtml" cacheable="false">
                        <container name="form.additional.info">
                            <block class="Magento\ReCaptchaUi\Block\ReCaptcha"
                                name="recaptcha"
                                after="-"
                                template="Magento_ReCaptchaFrontendUi::recaptcha.phtml"
                                ifconfig="recaptcha_frontend/type_for/customer_register">
                                <arguments>
                                    <argument name="recaptcha_for" xsi:type="string">customer_register</argument>
                                    <argument name="jsLayout" xsi:type="array">
                                        <item name="components" xsi:type="array">
                                            <item name="recaptcha" xsi:type="array">
                                                <item name="component" xsi:type="string">Magento_ReCaptchaFrontendUi/js/reCaptcha</item>
                                            </item>
                                        </item>
                                    </argument>
                                </arguments>
                            </block>
                        </container>
                    </block>
                </container>
                <container name="customer.register.benefits.wrapper" htmlTag="div" htmlClass="note customer-register-benefits-wrapper">
                    <container htmlTag="div" htmlClass="block block-register-benefits">
                        <container htmlTag="div" htmlClass="block-box-wrapper">
                            <block class="Magento\Cms\Block\Block" name="store_account_benefits">
                                <arguments>
                                    <argument name="block_id" xsi:type="string">store_account_benefits</argument>
                                </arguments>
                            </block>
                        </container>
                    </container>
                </container>
            </container>
        </referenceContainer>
	</body>
</page>

