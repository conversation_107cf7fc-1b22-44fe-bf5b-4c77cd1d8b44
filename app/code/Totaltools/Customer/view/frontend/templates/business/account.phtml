<?php

/** @var \Totaltools\Customer\Block\Business\Account $block */
?>
<?php
/**
 * Shippit Order Tracking Form
 *
 * @see \Totaltools\Customer\Block\Business\Account
 * @var $block \Totaltools\Customer\Block\Business\Account
 */
?>
<?php
    $storeAccount = $block->getStoreAccount();
    $hepler = $this->helper('Totaltools\Customer\Helper\AttributeData');
    $tradeRewardCustomer = $hepler->isTradeRewards();
?>
<div class="block block-customer-login">
    <div class="block-box-wrapper">
        <div class="block-title">
            <strong id="block-customer-login-heading" role="heading" aria-level="2"><?php /* @escapeNotVerified */ echo __('EXISTING INSIDER MEMBER SIGN UP TO TRADE REWARDS') ?></strong>
        </div>
        <div class="block-content" >
            <form class="form form-login"
                  action="<?= /* @escapeNotVerified */ $block->getPostActionUrl() ?>"
                  method="post"
                  id="business-account"
                  data-mage-init='{"validation":{}}'>
                <?= $block->getBlockHtml('formkey'); ?>
                <fieldset class="fieldset login" data-hasrequired="<?php /* @escapeNotVerified */ echo __('* Required Fields') ?>">
                    <div class="field note">
                        <?= $this->getLayout()
                            ->createBlock('Magento\Cms\Block\Block')
                            ->setBlockId('customer_business_account_conversion_top')
                            ->toHtml();
                        ?>
                    </div>
                    <input type="hidden" name="trade_reward_account"  <?php if ($tradeRewardCustomer) {?>
                                value="1"
                                <?php } else {?>
                                    value="2"
                                     <?php }?>>
                    <div class="field abn required">
                        <label class="label" for="abn"><span><?php /* @escapeNotVerified */ echo __('ABN') ?></span></label>
                        <div class="control">
                        <input type="text"
                                name="abn"
                                id="abn"
                                <?php if ($tradeRewardCustomer) {?>
                                value="<?= $hepler->getAbn(); ?>"
                                disabled="true"
                                <?php } else {?>
                                    value=""
                                     <?php }?>
                                minlength="11"
                                maxlength="11"
                                title="<?php /* @escapeNotVerified */ echo __('ABN') ?>"
                                class="input-text validate-number no-space"
                                data-validate="{required:true}"
                                placeholder="<?php echo __('Enter ABN'); ?>"/>
                        </div>
                    </div>
                    <div class="field text required">
                        <label class="label" for="company"><span><?php /* @escapeNotVerified */ echo __('Company Name') ?></span></label>
                        <div class="control">
                            <input name="company"
                            <?php if ($tradeRewardCustomer) {?>
                                value="<?= $hepler->getCompany(); ?>"
                                disabled="true"
                                <?php } else {?>
                                    value=""
                                     <?php }?>
                            autocomplete="off" id="company" type="text" class="input-text" title="<?php /* @escapeNotVerified */ echo __('Company Name') ?>"  data-validate="{required:true}"  placeholder="<?php echo __('Enter your Company name'); ?>">
                        </div>
                    </div>
                    <div class="field required text">
                        <label class="label" for="postcode"><span><?php /* @escapeNotVerified */ echo __('Postcode') ?></span></label>
                        <div class="control">
                            <input type="text" id="postcode" name="postcode" placeholder="Enter Postcode" minlength="4" maxlength="4" value="" class="input-text required-entry validate-digits validate-au-postcode" autocomplete="off" aria-required="true">
                        </div>
                    </div>
                    <?php if($storeAccount) { ?>
                        <div class="field text required">
                            <label class="label" for="estimated_monthly_spend"><span><?php /* @escapeNotVerified */ echo __('Estimated Monthly Spend') ?></span></label>
                            <div class="control">
                                <input name="estimated_monthly_spend" value="" autocomplete="off" id="estimated_monthly_spend" type="text" class="input-text" title="<?php /* @escapeNotVerified */ echo __('Company Name') ?>"  data-validate="{required:true}"  placeholder="<?php echo __('Enter your Company name'); ?>">
                            </div>
                        </div>
                    <?php } ?>
                    <div class="control business_account hide">
                            <input type="checkbox"
                                    name="business_account"
                                    title="Business Account"
                                    value="1"
                                    id="business_account"
                                    checked="checked"
                                    class="checkbox"
                                />
                        </div>
                        <div class="control">
                            <label class="label unlocked_text" for="unlocked_text">
                                <span>
                                    <?= __($block->getUnlockedText()); ?>
                                </span>
                            </label>
                        </div>
                </fieldset>
                <div class="actions-toolbar">
                    <div class="primary"><button type="submit" class="action button-primary-3 login" name="submit" id="submit"><span><?php /* @escapeNotVerified */ echo __('Continue to Apply for Credit') ?></span></button></div>
                </div>
                <p class="required-note"><?php echo __('* Required fields'); ?></p>
            </form>
        </div>
    </div>
</div>

<script>
    require([
        'jquery',
        'mage/mage'
    ], function($) {
        var dataForm = $('#business-account');

        dataForm.on('submit', function(e) {
            var form = $(this);
            var isValid = form.validation('isValid');

            if (isValid) {
                var formSubmitBtn = form.find('button[type="submit"]');

                formSubmitBtn.prop('disabled', true);
                $('body').trigger('processStart');

                window.dataLayer = window.dataLayer || [];
                window.dataLayer.push({
                    event: 'national_commercial',
                    form_id: form.attr('id'),
                });
            }
        });
    });
</script>
