<?php

/**
 * @var $block \Totaltools\Customer\Block\Commercial\Account
 */
?>
<?php

/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Totaltools\Customer\Block\Account\Register $block */
?>
<?php
/**
 * Customer register form template
 *
 * @see \Totaltools\Customer\Block\Account\Register
 * @var $block \Totaltools\Customer\Block\Account\Register
 */
?>

<div class="block block-customer-register">
    <div class="block-box-wrapper">
        <div class="block-title">
            <strong id="block-customer-register-heading" role="heading" aria-level="2"><?php /* @escapeNotVerified */ echo __('National Commercial Account Request Form') ?></strong>
        </div>
        <div class="block-content" aria-labelledby="block-customer-register-heading">
            <form class="form create account form-create-account" action="<?= $block->getPostUrl() ?>" method="post" id="member-signup" enctype="multipart/form-data" autocomplete="off" data-mage-init='{"validation":{}}'>
                <?php echo $block->getBlockHtml('formkey'); ?>
                <fieldset class="fieldset register" data-hasrequired="<?php /* @escapeNotVerified */ echo __('* Required Fields') ?>">
                    <div class="field note show">
                        <?php
                        echo $this->getLayout()
                            ->createBlock('Magento\Cms\Block\Block')
                            ->setBlockId('commercial_account_top_text')
                            ->toHtml();
                        ?>
                    </div>
                    <div class="field field-name-firstname required">
                        <label class="label" for="firstname"><span>First Name</span></label>
                        <div class="control">
                            <input type="text" id="firstname" name="firstname" value="" title="First Name" class="input-text required-entry" data-validate="{required:true}" placeholder="Enter your first name" autocomplete="off" aria-required="true">
                        </div>
                    </div>
                    <div class="field field-name-lastname required">
                        <label class="label" for="lastname"><span>Last Name</span></label>
                        <div class="control">
                            <input type="text" id="lastname" name="lastname" value="" title="Last Name" class="input-text required-entry" data-validate="{required:true}" placeholder="Enter your last name" autocomplete="off" aria-required="true">
                        </div>
                    </div>
                    <div class="field required">
                        <label for="email_address" class="label"><span>Email</span></label>
                        <div class="control">
                            <input type="email" name="email" id="email_address" value="" title="Email" class="input-text" data-validate="{required:true, 'validate-email':true}" placeholder="Enter your email address" aria-required="true">
                        </div>
                    </div>
                    <div class="field field-mobile_number0 required">
                        <label class="label" for="mobile_number"><span>Mobile Number</span></label>
                        <div class="control">
                            <input type="text" id="mobile_number" name="mobile_number" value="" class="input-text required-entry validate-digits validatePhoneAU"
                            data-validate="{required:true, validatePhoneAU:true}" autocomplete="off" aria-required="true">
                        </div>
                    </div>
                    <div class="field required abn">
                        <label for="abn" class="label"><span>ABN</span></label>
                        <div class="control ">
                            <input type="text" name="abn" id="abn" minlength="11" maxlength="11" value="" title="ABN" class="input-text required-entry validate-number no-space" data-validate="{required:true}" placeholder="Enter ABN" autocomplete="off" aria-required="true">
                        </div>
                    </div>
                    <div class="field required text">
                        <label class="label" for="customer_company"><span>Company Name</span></label>
                        <div class="control">
                            <input name="customer_company" value="" autocomplete="off" id="customer_company" type="text" class="input-text required-entry" title="Company" data-validate="{required:true}" placeholder="Enter your Company name" aria-required="true">
                        </div>
                    </div>
                    <div class="field required text">
                        <label class="label" for="estimated_spending"><span>Estimated monthly spend</span></label>
                        <div class="control">
                            <input type="text" id="estimated_spending" name="estimated_spending" value="" class="input-text required-entry validate-digits" autocomplete="off" aria-required="true">
                        </div>
                    </div>
                    <div class="field field-post-code required">
                        <label class="label" for="preferred_store">
                            <span>Post Code</span>
                        </label>
                        <div class="control">
                            <input type="text" id="postcode" name="postcode" value="" minlength="4" maxlength="4" class="input-text required-entry validate-digits no-space validate-au-postcode" autocomplete="off" aria-required="true">
                        </div>
                    </div>
                </fieldset>

                <div class="block-content">
                    <fieldset class="fieldset create account" data-hasrequired="* Required Fields">
                        <?php echo $this->getChildHtml('form.additional.info'); ?>
                    </fieldset>
                    <div class="actions-toolbar">
                        <div class="primary">
                            <button type="submit" class="action button-primary-3 register" name="send" id="register">
                                <span><?php /* @escapeNotVerified */ echo __('REGISTER YOUR INTEREST') ?></span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
<script>
    require([
        'jquery',
        'mage/mage'
    ], function($) {
        var dataForm = $('#member-signup');

        dataForm.on('submit', function(e) {
            var form = $(this);
            var isValid = form.validation('isValid');

            if (isValid) {
                var formSubmitBtn = form.find('button[type="submit"]');

                formSubmitBtn.prop('disabled', true);
                $('body').trigger('processStart');

                window.dataLayer = window.dataLayer || [];
                window.dataLayer.push({
                    event: 'traderewards_signup',
                    form_id: form.attr('id'),
                });
            }
        });
    });
</script>