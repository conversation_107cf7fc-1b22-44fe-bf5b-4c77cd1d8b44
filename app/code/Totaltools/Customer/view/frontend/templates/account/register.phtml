<?php

/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var \Totaltools\Customer\Block\Account\Register $block */
?>
<?php
/**
 * Customer register form template
 *
 * @see \Totaltools\Customer\Block\Account\Register
 * @var $block \Totaltools\Customer\Block\Account\Register
 */
?>
<?php
    $customerHelper = $this->helper('\Totaltools\Customer\Helper\Data');
    $isTradeRewards = $customerHelper->isTradeRewards();
    $formData = $block->getFormData();
    $cssClass = !$formData['verified'] || !$formData['intro'] || (!$formData['mismatch_mobile'] && $formData['mismatch'] ) || ($formData['mismatch_mobile'] && $formData['mismatch'] )  ? ' hidden' : ' show';   
?>
<div class="block block-customer-register">
    <div class="block-box-wrapper">
        <div class="block-title">
            <strong id="block-customer-register-heading" role="heading" aria-level="2"><?php /* @escapeNotVerified */ echo __($customerHelper->getRewardsRegisterAccountHeading()) ?></strong>
        </div>
        <?php
        
        ?>
        <div class="block-content" aria-labelledby="block-customer-register-heading">
            <form class="form form-register" name="form_register" action="<?php /* @escapeNotVerified */ echo $block->getPostActionUrl() ?>" method="post" id="form_register" data-mage-init='{"validation":{}}'>
                <?php echo $block->getBlockHtml('formkey'); ?>
                <fieldset class="fieldset register" data-hasrequired="<?php /* @escapeNotVerified */ echo __('* Required Fields') ?>">
                    <div class="field note<?= /* @escapeNotVerified */ $cssClass; ?>">
                        <?php
                        echo $this->getLayout()
                            ->createBlock('Magento\Cms\Block\Block')
                            ->setBlockId($customerHelper->getRewardsRegisterAccountTop())
                            ->toHtml();
                        ?>
                    </div>
                    <div class="field email required<?= /* @escapeNotVerified */ $cssClass; ?>">
                        <label class="label" for="email"><span><?php /* @escapeNotVerified */ echo __('Email') ?></span></label>
                        <div class="control">
                            <input name="email" value="<?= $formData['email']; ?>" autocomplete="off" id="email" type="email" class="input-text" title="<?php /* @escapeNotVerified */ echo __('Email') ?>" data-validate="{ 'insider-email-required':true, 'validate-email':true}" placeholder="<?php echo __('Enter your email address'); ?>">
                        </div>
                    </div>
                    <?php
                        if ($isTradeRewards) {
                            $businessAccount = 'trade_rewards';
                            $required = 'true';
                            $businessAccountValue = 1;
                        } else {
                            $businessAccount = 'hide';
                            $required = 'false';
                            $businessAccountValue = 0;
                        }
                        ?>
                        <div class="field required abn <?= $businessAccount?>   ">
                            <label for="abn" class="label"><span><?php /* @escapeNotVerified */ echo __('ABN') ?></span></label>
                            <div class="control ">
                                <input type="text" name="abn" id="abn" minlength="11" maxlength="11" value="<?= $formData['abn']; ?>" title="<?php /* @escapeNotVerified */ echo __('ABN') ?>" class="input-text no-space validate-number" data-validate="{required:<?= $required ?>}" placeholder="<?php echo __('Enter ABN'); ?>"/>
                            </div>
                        </div>
                        <div class="field required text <?= $businessAccount?>">
                            <label class="label" for="company"><span><?php /* @escapeNotVerified */ echo __('Company Name') ?></span></label>
                            <div class="control">
                                <input name="company" value="<?= $formData['company']; ?>" autocomplete="off" id="company" type="text" class="input-text" title="<?php /* @escapeNotVerified */ echo __('Company Name') ?>" data-validate="{required:<?= $required ?>}"  placeholder="<?php echo __('Enter your Company name'); ?>">
                            </div>
                        </div>
                        <div class="control business_account hide">
                            <input type="checkbox"
                                    name="business_account"
                                    title="Trade Account"
                                    value="<?= $businessAccountValue ?>" 
                                    id="business_account" 
                                    <?php if ($required) : ?> checked="checked"<?php endif; ?>
                                    class="checkbox"
                                />
                        </div>
                    <div class="field mobile_number <?= /* @escapeNotVerified */ $cssClass; ?>">
                        <label for="mobile_number" class="label"><span><?php /* @escapeNotVerified */ echo __('Mobile Number') ?></span></label>
                        <div class="control">
                            <input name="mobile_number" value="<?php echo $formData['mobile_number']; ?>" type="text" autocomplete="off" class="input-text validatePhoneAU" id="mobile_number" title="<?php /* @escapeNotVerified */ echo __('Mobile Number') ?>" data-validate="{validatePhoneAU:true}" placeholder="<?php echo __('Enter your mobile number'); ?>">
                        </div>
                    </div>
                    <div class="field required terms-and-condition  <?= /* @escapeNotVerified */ $cssClass; ?>">
                        <div class="control">
                            <div class="options">
                                <input type="checkbox" name="termsandcondition" title="Terms and condition" value="1" id="termsandcondition" class="checkbox required" />
                                <label for="termsandcondition" class="label "><span><?= __($block->getTermsAndConditionText()) ?></span></label>
                            </div>
                        </div>
                    </div>


                    <?php if (!$formData['intro']) : ?>
                        <div class="field note"><?php /* @escapeNotVerified */ echo __('We don\'t have your first and last name, please provide this info too.') ?></div>
                        <div class="field first_name required">
                            <label class="label" for="first_name"><span><?php /* @escapeNotVerified */ echo __('First Name') ?></span></label>
                            <div class="control">
                                <input name="first_name" value="<?php echo $formData['first_name']; ?>" type="text" id="first_name" class="input-text" title="<?php /* @escapeNotVerified */ echo __('First Name') ?>" data-validate="{required:true}" placeholder="<?php echo __('Enter your First Name'); ?>">
                            </div>
                        </div>
                        <div class="field last_name required">
                            <label for="last_name" class="label"><span><?php /* @escapeNotVerified */ echo __('Last Name') ?></span></label>
                            <div class="control">
                                <input name="last_name" value="<?php echo $formData['last_name']; ?>" type="text" autocomplete="off" class="input-text" id="last_name" title="<?php /* @escapeNotVerified */ echo __('Last Name') ?>" data-validate="{required:true}" placeholder="<?php echo __('Enter your Last Name'); ?>">
                            </div>
                        </div>
                    <?php endif; ?>
                    <?php if (!$formData['verified']) : ?>
                        <div class="field verification_code required">
                            <label class="label" for="verification_code"><span><?php /* @escapeNotVerified */ echo __('Verification Code') ?></span></label>
                            <div class="control">
                                <input name="verification_code" type="text" id="verification_code"  value="<?php echo $formData['verification_code']; ?>"  class="input-text" title="<?php /* @escapeNotVerified */ echo __('Verification') ?>" data-validate="{required:true}" placeholder="<?php echo __('Enter Verification Code'); ?>">
                                <div class="field note"><?php /* @escapeNotVerified */ echo __('Please enter verification code that was just sent to your mobile.') ?></div>
                            </div>
                        </div>

                    <?php endif; ?>
                    <?php if ($formData['mismatch']) : ?>
                        <div class="field required ">
                            <div class="control">
                                <div class="options">
                                    <input type="checkbox" name="mismatch" title="Update data" value="1" id="mismatch" class="checkbox required" />
                                    <label for="mismatch" class="label "><span><?= __($block->getAgreementText()) ?></span></label>
                                </div>
                            </div>
                        </div>
                    <?php  endif; ?>
                    <?php echo $this->getChildHtml('form.additional.info'); ?>
                </fieldset>
                <div class="actions-toolbar">
                    <div class="primary">
                        <button type="submit" class="action button-primary-3 register" name="send" id="register">
                            <span><?php /* @escapeNotVerified */ echo __('Join') ?></span>
                        </button>
                    </div>
                    <div class="secondary">
                        <a class="action cancel" href="<?php /* @escapeNotVerified */ echo $block->getClearActionUrl() ?>">
                            <span><?php /* @escapeNotVerified */ echo __('Cancel') ?></span>
                        </a>
                    </div>
                </div>
                <p class="required-note"><?php echo __('* Required fields'); ?></p>
            </form>
        </div>
    </div>
</div>
<script>
    require([
        'jquery',
        'mage/mage'
    ], function($) {
        var dataForm = $('#form_register');

        dataForm.on('submit', function(e) {
            var isValid = $(this).validation('isValid');
            if (isValid) {
                var formSubmitBtn = $(this).find('button[type="submit"]');
                formSubmitBtn.prop('disabled', true);
                $('body').trigger('processStart');
            }
        });
    });
</script>
