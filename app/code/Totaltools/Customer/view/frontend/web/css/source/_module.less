/**
 * @package     Totaltools_Customer
 * <AUTHOR> <PERSON><PERSON><PERSON> <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au> 
 */

//
//  Vars
//  _____________________________________________
@accout-nav__border-color: @border-color__base;

@accout-nav-item__my-account__icon: '\E842';
@accout-nav-item__profile__icon: '\E83F';
@accout-nav-item__orders__icon: '\E83C';
@accout-nav-item__invoices__icon: '\E839';
@accout-nav-item__reviews__icon: '\E840';
@accout-nav-item__addresses__icon: '\E83E';
@accout-nav-item__payment-methods__icon: '\E83B';
@accout-nav-item__newsletter__icon: '\E83D';
@accout-nav-item__wishlist__icon: '\E83A';
@accout-nav-item__insider__icon: '\E843';
@accout-nav-item__company-structure__icon: '\E846';
@accout-nav-item__company-users__icon: '\E848';
@accout-nav-item__company-profile__icon: '\E847';
@accout-nav-item__company-roles__icon: '\E849';

//
//  Common
//  _____________________________________________

& when (@media-common = true) {
    #account-nav {
        margin-bottom: @indent__m;

        .items {
            border: 1px solid @accout-nav__border-color;

            .item {
                padding: 0;
                margin: 0;
                border-bottom: 1px solid @accout-nav__border-color;
                .lib-css(transition, all 0.2s ease, 1);

                &:last-child {
                    border-bottom: 0 none;
                }

                &:hover {
                    .lib-css(box-shadow, 0 0 3px rgba(0, 0, 0, 0.25), 1);
                }

                a,
                strong {
                    color: #444;
                    padding: 8px;

                    .lib-icon-font(
                        @_icon-font-content: '',
                        @_icon-font-size: 22px,
                        @_icon-font-margin: -2px 5px 0 0
                    );

                    &.my-account {
                        .lib-icon-font-symbol(@accout-nav-item__my-account__icon);

                        &:before {
                            .lib-font-size(18);
                        }
                    }

                    &.profile {
                        .lib-icon-font-symbol(@accout-nav-item__profile__icon);

                        &:before {
                            .lib-font-size(20);
                        }
                    }

                    &.orders {
                        .lib-icon-font-symbol(@accout-nav-item__orders__icon);
                    }

                    &.invoices {
                        .lib-icon-font-symbol(@accout-nav-item__invoices__icon);
                    }

                    &.payment-methods {
                        .lib-icon-font-symbol(@accout-nav-item__payment-methods__icon);
                    }

                    &.addresses {
                        .lib-icon-font-symbol(@accout-nav-item__addresses__icon);
                    }

                    &.wishlist {
                        .lib-icon-font-symbol(@accout-nav-item__wishlist__icon);
                    }

                    &.reviews {
                        .lib-icon-font-symbol(@accout-nav-item__reviews__icon);
                    }

                    &.newsletter {
                        .lib-icon-font-symbol(@accout-nav-item__newsletter__icon);
                    }

                    &.insider {
                        .lib-icon-font-symbol(@accout-nav-item__insider__icon);
                    }

                    &.company-structure {
                        .lib-icon-font-symbol(@accout-nav-item__company-structure__icon);
                    }

                    &.company-profile {
                        .lib-icon-font-symbol(@accout-nav-item__company-profile__icon);
                    }

                    &.company-users {
                        .lib-icon-font-symbol(@accout-nav-item__company-users__icon);
                    }

                    &.company-roles {
                        .lib-icon-font-symbol(@accout-nav-item__company-roles__icon);
                    }

                    display: block;

                    &:hover {
                        color: @link__color;
                    }
                }

                &.current {
                    position: relative;
                    .lib-css(box-shadow, 0 0 3px rgba(0, 0, 0, 0.25), 1);

                    strong {
                        font-weight: normal;
                        color: @link__color;
                    }

                    &:before {
                        content: '';
                        display: block;
                        width: 3px;
                        height: ~"calc(100% + 2px)";
                        background-color: @link__color;
                        position: absolute;
                        top: -1px;
                        left: -3px;
                    }
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'min') and (@break = @screen__m) {
}

//
//  Mobile
//  _____________________________________________

.media-width(@extremum, @break) when (@extremum = 'max') and (@break = @screen__m) {
    #account-nav {
        .items {
            .item {
                background-color: white;
            }
        }
    }
}
.privacy_text {
    padding-top: 10px;
}
.terms-and-condition p {
    margin-bottom: 0px !important;
}