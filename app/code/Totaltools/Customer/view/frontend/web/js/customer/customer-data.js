/**
 * @package     Totaltools_Loyalty
 * <AUTHOR> <<EMAIL>>
 * @since       1.0.30
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

define([
    'uiComponent',
    'Magento_Customer/js/customer-data'
], function (Component, customerData) {
    'use strict';

    return Component.extend({
        /**
         * @return Observable
         */
        customer: customerData.get('customer'),
        isB2BCustomer: function() {
            var isB2BCustomer = false;
            var mageCacheStorage = localStorage['mage-cache-storage'];
            
            if (typeof mageCacheStorage != 'undefined') {
                let mageCacheObj = JSON.parse(mageCacheStorage);
                isB2BCustomer = mageCacheObj.company && mageCacheObj.company.has_customer_company;
            }

            return isB2BCustomer;
        }
    });
});
