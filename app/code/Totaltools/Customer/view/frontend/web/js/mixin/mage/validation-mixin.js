/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'jquery',
    'jquery-ui-modules/widget',
    'jquery/validate'
], function ($) {
    'use strict';

    return function(widget) {
        $.widget('mage.validation', $.mage.validation, {
            
            listenFormValidateHandler: function (event, validation) {
                if (window.location.href.indexOf('checkout') > -1) {
                    return this._super(event, validation);
                }

                var firstActive = $(validation.errorList[0].element || []),
                    lastActive = $(validation.findLastActive() ||
                    validation.errorList.length && validation.errorList[0].element || []),
                    parent, windowHeight, successList;

                if (lastActive.is(':hidden')) {
                    parent = lastActive.parent();
                    windowHeight = $(window).height();
                    $('html, body').animate({
                        scrollTop: parent.offset().top - windowHeight / 2
                    });
                }

                // ARIA (removing aria attributes if success)
                successList = validation.successList;

                if (successList.length) {
                    $.each(successList, function () {
                        $(this)
                            .removeAttr('aria-describedby')
                            .removeAttr('aria-invalid');
                    });
                }

                if (firstActive.length) {
                    var scrollTo    = firstActive.parent().siblings(".label").length ?
                    firstActive.parent().siblings(".label").offset().top - firstActive.parent().siblings(".label").height() :
                        firstActive.offset().top;
                    $('html, body').animate({
                        scrollTop: scrollTo
                    });
                    firstActive.focus();
                }
            }
        });

        return $.mage.validation;
    };
});
