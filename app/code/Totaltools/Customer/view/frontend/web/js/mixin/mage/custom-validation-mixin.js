define(['jquery'], function($) {
    'use strict';

    return function() {
        $.validator.addMethod(
            'validatePhoneAU',
            function(value, element) {
                return this.optional(element) ||/^\D*04(\D*\d){8}\D*$/.test(value);
            },
            $.mage.__('Mobile number must start with 04 and should be 10 digits')
        );
        $.validator.addMethod(
            'insider-email-required',
            function(value, element) {
                return value !== undefined && value !== null && value.length > 0;
            },
            $.mage.__('This is a required field. Please enter the email address attached to your Insider Account or please enter a new email address to update your insider account.')
        );
        $.validator.addMethod(
            'no-space',
            function(value, element) {
                return value.indexOf(" ") < 0 && value != ""; 
            },
            $.mage.__('No spaces please')
        );
        $.validator.addMethod(
            'validate-au-postcode',
            function(value, element) {
                let countryId = $(element).closest('form').find('[name="country_id"]').val();
                return this.optional(element) || (countryId === 'AU' ? /^\d{4}$/.test(value) : true);
            },
            $.mage.__('Please enter a valid Australian postcode (4 digits)')
        );
    }
});