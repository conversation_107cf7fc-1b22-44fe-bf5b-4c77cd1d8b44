<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Api/etc/extension_attributes.xsd">
    <extension_attributes for="Magento\Customer\Api\Data\CustomerInterface">
        <attribute code="loyalty_id" type="string" />
        <attribute code="is_subscribed_sms_promo" type="boolean" />
        <attribute code="is_subscribed_direct_marketing" type="boolean" />
        <attribute code="role" type="string" />
        <attribute code="pronto_position" type="string" />
        <attribute code="account_code" type="string" />
        <attribute code="is_subscribed_phone_promo" type="boolean" />
        <attribute code="preferred_store" type="string" />
        <attribute code="loyalty_status" type="int" />
        <attribute code="referral_code" type="string"/>
		<attribute code="referred_by" type="string"/>
    </extension_attributes>
</config>
