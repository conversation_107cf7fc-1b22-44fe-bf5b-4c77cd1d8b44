<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Customer\Block\Form\Edit"
                type="Totaltools\Customer\Block\Form\EditRewrite" />
    <preference for="Magento\Customer\Block\Account\Dashboard\Info"
                type="Totaltools\Customer\Block\Account\Dashboard\InfoRewrite" />
    <preference for="\Magento\Customer\Controller\Account\EditPost"
                type="Totaltools\Customer\Controller\Account\EditPostRewrite" />
    <preference for="\Magento\Customer\Controller\Account\CreatePost"
                type="Totaltools\Customer\Controller\Account\CreatePostRewrite" />
    <preference for="\Magento\Customer\Block\Account\SortLink"
                type="Totaltools\Customer\Preference\Block\Account\SortLink" />
    <preference for="\Magento\Customer\Controller\Account\ForgotPasswordPost"
                type="Totaltools\Customer\Controller\Account\ForgotPasswordPost" />
    
    <type name="Magento\Customer\Model\ResourceModel\AddressRepository">
        <plugin name="Totaltools\Customer\Plugin\Model\ResourceModel\AddressRepository\AddressRepositoryPlugin" type="Totaltools\Customer\Plugin\Model\ResourceModel\AddressRepository\AddressRepositoryPlugin"/>
    </type>

    <type name="Magento\Customer\Controller\Account\LoginPost">
        <plugin name="totaltools_customer_login_post_controller" 
                type="Totaltools\Customer\Plugin\Controller\LoginPostPlugin" />
    </type>

    <type name="Magento\Customer\Controller\Account\CreatePost">
        <plugin name="totaltools_customer_create_post_controller" 
                type="Totaltools\Customer\Plugin\Controller\CreatePostPlugin" />
    </type>

    <type name="Magento\Customer\Controller\Account\Edit">
        <plugin name="totaltools_customer_edit_controller" 
                type="Totaltools\Customer\Plugin\Controller\EditPlugin" />
    </type>

    <type name="Magento\CustomAttributeManagement\Block\Form\Renderer\Text">
        <plugin name="Totaltools_Customer::FormRendererTextPlugin"
                type="Totaltools\Customer\Plugin\Block\Form\Renderer\TextPlugin" />
    </type>

    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="customerimport" xsi:type="object">Totaltools\Customer\Console\Command\Import</item>
            </argument>
        </arguments>
    </type> 
</config>
