<?xml version="1.0"?>
<!--
/**
* @package Totatools_Customer
* <AUTHOR> Iqbal <<EMAIL>>
* @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="controller_action_predispatch_customer_account_index">
        <observer name="totaltools_customer_account_index_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_customer_account_edit">
        <observer name="totaltools_customer_account_edit_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_loyalty_insider_info">
        <observer name="totaltools_customer_loyalty_insider_info_predispatch"
                instance="Totaltools\Customer\Observer\Frontend\CustomerEditPredispatch" />
    </event>
     
    <event name="controller_action_predispatch_customer_address_index">
        <observer name="totaltools_customer_address_index_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_customer_address_new">
        <observer name="totaltools_customer_address_new_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_loyalty_insider_invoices">
        <observer name="totaltools_customer_loyalty_insider_invoices_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_requisition_list_requisition_index">
        <observer name="totaltools_customer_requisition_list_requisition_index_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_wishlist_index_index">
        <observer name="totaltools_customer_wishlist_index_index_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_review_customer_index">
        <observer name="totaltools_customer_review_customer_index_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_vault_cards_listaction">
        <observer name="totaltools_customer_vault_cards_listaction_predispatch" 
                instance="Totaltools\Customer\Observer\Frontend\CustomerSectionPredispatch" />
    </event>

    <event name="controller_action_predispatch_customer_account_registerpost">
        <observer name="totaltools_customer_recaptcha"
                  instance="Totaltools\Customer\Observer\Frontend\RegisterFormObserver"/>
    </event>
    <event name="controller_action_predispatch_checkout_cart_index">
        <observer name="totaltools_cartpage_index"
         instance="Totaltools\Customer\Observer\Frontend\CartPagePreDispatch"
         />
    </event>
    <event name="controller_action_predispatch_customer_account_create">
        <observer name="customer_account_create"
         instance="Totaltools\Customer\Observer\Frontend\CustomerCreatePreDispatch"
         />
    </event>
</config>
