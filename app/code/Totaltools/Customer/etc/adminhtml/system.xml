<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="totaltools_customer" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1"
                 showInStore="0">
            <label>Customer Registration</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Customer::config</resource>
            <group id="sendmail" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Email Setting</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[Your Comments]]></comment>
                </field>
                <field id="sender" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="3" canRestore="1">
                    <label>Customer Registration email sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
            <group id="sendsms" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Message Media SMS Setting</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[Your Comments]]></comment>
                </field>
                <field id="endpoint" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="3" canRestore="1">
                    <label>API Endpoint</label>
                    <comment>Message Media Api end point</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="message" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="3" canRestore="1">
                    <label>SMS Text Message</label>
                    <comment>Text will send in sms</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="api" translate="label" type="obscure" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="3" canRestore="1">
                    <label>API</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <comment>Message Media Api</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="secret" translate="label" type="obscure" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="3" canRestore="1">
                    <label>Secrete</label>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                    <comment>Message Media Secret</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="minutes_duration" translate="label" type="text" sortOrder="30" showInDefault="1"
                       showInWebsite="1"
                       showInStore="3" canRestore="1">
                    <label>Number of Minutes</label>
                    <comment>Number of Minutes after which a customer can resend verification sms</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="no_of_tries" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1"
                       showInStore="3" canRestore="1">
                    <label>Number of Tries</label>
                    <comment>Number of Tries for resending sms in a session</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
            <group id="registration" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Registration Setting</label>
                <field id="sms_verification" translate="label comment" type="select" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Verify Mobile</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[Verify Mobile number for normal magento accounts ]]></comment>
                </field>
                <field id="save_address" translate="label comment" type="select" sortOrder="20" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Save Customer address</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment><![CDATA[Create customer address if exists in pronto]]></comment>
                </field>
                <field id="agreement_text" translate="label comment" type="text" sortOrder="10" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Update Customer Date Checkbox Text</label>
                    <comment><![CDATA[Update Customer Date Checkbox Text]]></comment>
                </field>
                <field id="sync_to_pronto" translate="label" type="select" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Sync Customer Data to pronto</label>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
                <field id="trade_reward_url" translate="label comment" type="textarea" sortOrder="50" showInDefault="1"
                       showInWebsite="1" showInStore="1">
                    <label>Trade Reward Url</label>
                    <comment><![CDATA[Enter Trade Reward url]]></comment>
                </field>
            </group>
            <group id="loyalty_report" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Loyalty Report Settings</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Email Report</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="email_template" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="sender" translate="label" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="recipients" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Recipients</label>
                    <comment>Comma-separated list of email addresses</comment>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="report_path" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Report Directory Path</label>
                </field>
            </group>
        </section>
        <section id="customer">
            <group id="create_account" translate="label" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <field id="business_rewards_email_template" translate="label comment" type="select" sortOrder="72" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Business Rewards Welcome Email</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>
        </section>
         <section id="recaptcha_frontend">
            <group id="type_for">
                <field id="customer_register" translate="label" type="select" sortOrder="180" showInDefault="1"
                       showInWebsite="1" showInStore="0" canRestore="1">
                    <label>Enable for Customer Register</label>
                    <source_model>Magento\ReCaptchaAdminUi\Model\OptionSource\Type</source_model>
                </field>
               </group>
        </section>
    </system>
</config>
