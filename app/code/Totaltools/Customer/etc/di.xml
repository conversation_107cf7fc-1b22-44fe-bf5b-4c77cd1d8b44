<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Customer\Api\CustomerRepositoryInterface">
        <plugin name="totaltools_customer_customer_extension_attribute"
                type="Totaltools\Customer\Plugin\CustomerRepositoryPlugin" />
    </type>
    <type name="Magento\Customer\Model\Address">
        <plugin name="totaltools_customer_model_address"
                type="Totaltools\Customer\Plugin\Model\Address"  sortOrder="10"/>
    </type>
    <type name="Magento\Framework\Reflection\CustomAttributesProcessor">
        <plugin name="totaltools_customer_custom_attribute_processor"
                type="Totaltools\Customer\Plugin\Reflection\CustomAttributesProcessor" />
    </type>

    <type name="Magento\Company\Model\Email\Sender">
        <plugin name="totaltools_customer_company_email_sender"
                type="Totaltools\Customer\Plugin\Model\Email\Sender" />
    </type>

    <type name="Magento\Customer\Model\Delegation\AccountDelegation">
        <plugin name="totaltools_customer_delegation_account_delegation"
                type="Totaltools\Customer\Plugin\Model\Delegation\AccountDelegation" />
    </type>
    <type name="Magento\Customer\Model\EmailNotification">
		<plugin name="Totaltools_Customer_Plugin_Magento_Customer_Model_EmailNotification" type="Totaltools\Customer\Plugin\Magento\Customer\Model\EmailNotification" sortOrder="10" disabled="false"/>
	</type>
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="totaltools_missing_loyalty_report" xsi:type="object">Totaltools\Customer\Console\Command\GenerateMissingLoyaltyReport</item>
            </argument>
        </arguments>
    </type>
</config>