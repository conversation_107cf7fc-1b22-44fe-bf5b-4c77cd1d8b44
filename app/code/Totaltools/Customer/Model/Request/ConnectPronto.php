<?php
/**
 * ConnectPronto
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Model\Request;

use Magento\Framework\Exception\LocalizedException;
use Totaltools\Pronto\Model\Api\Connect;

/**
 * ConnectPronto
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
class ConnectPronto
{
    protected $connect;

    /**
     * UnsubscribeSyncRepository constructor.
     *
     * @param Connect $connect Connect
     */
    public function __construct(
        Connect $connect
    ) {
        $this->connect = $connect;
    }

    /**
     * Get pronto member in separate request
     *
     * @param $post
     *
     * @return bool|int
     * @throws LocalizedException
     */
    public function getMember($post)
    {
        $params = [
            'EmailAddress' => $post['email'],
        ];
        $response = $this->getData($params);
        if (is_array($response) && array_key_exists('LoyaltyCustomers', $response)) {
            if (!empty($response['LoyaltyCustomers']) && array_key_exists('LoyaltyCustomer', $response['LoyaltyCustomers'])) {
                $response['LoyaltyCustomers']['LoyaltyCustomer']['param'] = 'email';
                return $response['LoyaltyCustomers'];
            } elseif (isset($post['mobile_number'])) {
                $params = [
                    'Mobile' => $post['mobile_number'],
                ];
                $response = $this->getData($params);
                if (is_array($response) && array_key_exists('LoyaltyCustomers', $response)) {
                    if (!empty($response['LoyaltyCustomers']) && array_key_exists('LoyaltyCustomer', $response['LoyaltyCustomers'])) {
                        $response['LoyaltyCustomers']['LoyaltyCustomer']['param'] = 'mobile';
                        return $response['LoyaltyCustomers'];
                    } else {
                        return ['status' => 'notfound'];
                    }
                } else {
                    return ['status' => 'failed'];
                }
            } else {
                return ['status' => 'failed'];
            }

        } else {
            return ['status' => 'failed'];
        }

    }

    /**
     * Get pronto customer in one request
     *
     * @param $post
     *
     * @return array
     * @throws LocalizedException
     */
    public function getProntoMember($post)
    {
        $params = [
            'EmailAddress' => $post['email'],
            'Mobile' => $post['mobile_number'],
        ];
        $response = $this->getData($params);
        if (is_array($response) && array_key_exists('LoyaltyCustomers', $response)) {
            if (!empty($response['LoyaltyCustomers']) && array_key_exists('LoyaltyCustomer', $response['LoyaltyCustomers'])) {
                return $response['LoyaltyCustomers'];
            } else {
                return ['status' => 'notfound'];
            }
        } else {
            return ['status' => 'failed'];
        }

    }

    /**
     * Get Data
     *
     * @param $param
     *
     * @return array|object|\stdClass
     * @throws LocalizedException
     */
    protected function getData($param)
    {
        $params = [
            'Parameters' => $param
        ];
        $this->connect
            ->setPath('GetMember')
            ->setRootNode('GetMemberRequest')
            ->setBody($params);
        try {
            return $this->connect->call('array');
        } catch (LocalizedException $e) {
            throw new LocalizedException(
                __('Error occurs on calling GetMember "%1"', $e->getMessage())
            );
        }

        return ['status' => 'failed'];
    }

    /**
     * Update customer email and mobile on pronto
     *
     * @param $email
     * @param $mobileNumber
     * @param $loyaltyId
     *
     * @return array|object|\stdClass
     * @throws LocalizedException
     */
    public function updateCustomerEmail($email, $mobileNumber, $loyaltyId)
    {
        if ($loyaltyId) {
            $params = [
                'EmailAddress' => $email,
                'LoyaltyID' => $loyaltyId,
                'Mobile' => $mobileNumber,
            ];
            $this->connect
                ->setPath('UpdateMemberDetails')
                ->setRootNode('UpdateMemberDetailsRequest')
                ->setBody(
                    [
                    'Parameters' => $params
                    ]
                );
            try {
                return $this->connect->call('array');
            } catch (LocalizedException $e) {
                throw new LocalizedException(
                    __('Error occurs on calling UpdateMemberDetails "%1"', $e->getMessage())
                );
            }
        }
    }

     /**
      * @param array $params
      * @return array|object|\stdClass
      * @throws LocalizedException
      */
    public function updateCustomerData(array $params)
    {
        if ($params['LoyaltyID']) {
            $this->connect
                ->setPath('UpdateMemberDetails')
                ->setRootNode('UpdateMemberDetailsRequest')
                ->setBody([
                    'Parameters' => $params
                ]);
            try {
                return $this->connect->call('array');
            } catch (LocalizedException $e) {
                throw new LocalizedException(
                    __('Error occurs on calling UpdateMemberDetails "%1"', $e->getMessage())
                );
            }
        }
    }

    /**
     * Create account code
     *
     * @param  $email
     *
     * @return array|object|\stdClass
     * @throws \Exception
     */
    public function createAccountCode($email, $mobileNumber)
    {
        $params = [
            'EmailAddress' => $email,
            'Mobile' => $mobileNumber,
            'Type' => 'Both'
        ];
        /**
         * CreateCustomer api will response create an account if not exist,
         * if not it responses LoyaltyCustomerExitsAlready as True and CustomerAccountcode
         **/
        $this->connect
            ->setPath('CreateCustomer')
            ->setRootNode('CreateCustomerRequest')
            ->setBody(
                [
                'Parameters' => $params
                ]
            );
        $response = $this->connect->call('array');
        if (isset($response['CustomerCreated']['LoyaltyCustomer'])) {
            return $response;
        }
        return [];
    }

}
