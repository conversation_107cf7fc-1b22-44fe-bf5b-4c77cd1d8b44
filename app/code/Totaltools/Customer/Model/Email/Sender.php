<?php
namespace Totaltools\Customer\Model\Email;

use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\Store;
use Psr\Log\LoggerInterface;
use Totaltools\Storelocator\Model\Mail\TransportBuilder;
use Magento\Framework\App\State;

class Sender
{
    private const XML_PATH_EMAIL_ENABLED = 'totaltools_customer/loyalty_report/enabled';
    private const XML_PATH_EMAIL_TEMPLATE = 'totaltools_customer/loyalty_report/email_template';
    private const XML_PATH_EMAIL_SENDER = 'totaltools_customer/loyalty_report/sender';
    private const XML_PATH_EMAIL_RECIPIENTS = 'totaltools_customer/loyalty_report/recipients';

    /**
     * @var TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var StateInterface
     */
    private $inlineTranslation;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var State
     */
    private $appState;

    public function __construct(
        TransportBuilder $transportBuilder,
        StateInterface $inlineTranslation,
        ScopeConfigInterface $scopeConfig,
        LoggerInterface $logger,
        State $appState
    ) {
        $this->transportBuilder = $transportBuilder;
        $this->inlineTranslation = $inlineTranslation;
        $this->scopeConfig = $scopeConfig;
        $this->logger = $logger;
        $this->appState = $appState;
    }

    /**
     * Send report email
     *
     * @param string $filePath
     * @param int $customerCount
     * @return bool
     */
    public function sendReportEmail($filePath, $customerCount)
    {
        if (!$this->scopeConfig->getValue(self::XML_PATH_EMAIL_ENABLED)) {
            return false;
        }

        try {
            try {
                $this->appState->setAreaCode(Area::AREA_FRONTEND);
            } catch (\Exception $e) {
                // Area code already set
            }

            $this->inlineTranslation->suspend();

            $recipientsString = $this->scopeConfig->getValue(self::XML_PATH_EMAIL_RECIPIENTS);
            if (empty($recipientsString)) {
                $this->logger->error('No recipients configured for loyalty report email');
                return false;
            }

            $recipients = explode(',', $recipientsString);
            $templateVars = [
                'report_date' => date('F Y'),
                'customer_count' => $customerCount
            ];

            $transport = $this->transportBuilder
                ->setTemplateIdentifier($this->scopeConfig->getValue(self::XML_PATH_EMAIL_TEMPLATE))
                ->setTemplateOptions([
                    'area' => Area::AREA_FRONTEND,
                    'store' => Store::DEFAULT_STORE_ID
                ])
                ->setTemplateVars($templateVars)
                ->setFrom($this->scopeConfig->getValue(self::XML_PATH_EMAIL_SENDER));

            foreach ($recipients as $recipient) {
                $transport->addTo(trim($recipient));
            }

            if (file_exists($filePath)) {                
                $transport->addAttachment(
                    file_get_contents($filePath),
                    basename($filePath),
                    'csv'
                );
            }

            $transport->getTransport()->sendMessage();

            $this->inlineTranslation->resume();
            return true;

        } catch (\Exception $e) {
            $this->logger->error('Error sending loyalty report email: ' . $e->getMessage());
            return false;
        }
    }
}
