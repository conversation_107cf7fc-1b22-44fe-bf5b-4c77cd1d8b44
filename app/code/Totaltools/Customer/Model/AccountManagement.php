<?php

namespace Totaltools\Customer\Model;


use Magento\Customer\Model\AccountManagement as CoreAccountManagement;
use Magento\Framework\Exception\InputException;

class AccountManagement extends CoreAccountManagement
{

    const MIN_PASSWORD_LENGTH = 6;

    /**
     * Override password strength check for importing old passwords
     *
     * @param string $password
     * @return void
     * @throws InputException
     */
    protected function checkPasswordStrength ($password)
    {
        $length = $this->stringHelper->strlen($password);
        if ($length < self::MIN_PASSWORD_LENGTH) {
            throw new InputException(
                __(
                    'Please enter a password with at least %1 characters.',
                    self::MIN_PASSWORD_LENGTH
                )
            );
        }
    }

}