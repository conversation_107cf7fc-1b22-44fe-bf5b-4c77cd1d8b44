<?php

namespace Totaltools\Customer\Model;

/**
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @since       1.0.32
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

use Magento\Customer\Model\Session as CustomerSession;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Api\Data\CustomerExtensionFactory;
use Magento\Customer\Api\Data\AddressInterfaceFactory;
use Magento\Eav\Api\Data\AttributeInterface;
use Magento\Eav\Model\ResourceModel\Entity\Attribute;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Newsletter\Model\ResourceModel\Subscriber as SubscriberModel;
use Magento\Newsletter\Model\SubscriberFactory;
use Magento\Eav\Api\AttributeRepositoryInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\DataObject;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Psr\Log\LoggerInterface;
use Totaltools\Pronto\Model\Request\CustomerRequestManager;
use Totaltools\Customer\Helper\Data as CustomerHelper;
use Magento\Customer\Model\CustomerFactory;
use Totaltools\Pronto\Api\CustomerRequestManagerInterface;

class ProntoSyncManager
{
    /**
     * Eav table for customer attribute
     */
    const CUSTOMER_EAV_TABLE = 'customer_entity_';
    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var CustomerExtensionFactory
     */
    protected $customerExtensionFactory;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var CustomerHelper
     */
    protected $customerHelper;

    /**
     * @var CustomerRequestManager
     */
    protected $prontoManager;

    /**
     * @var AddressInterfaceFactory
     */
    protected $addressDataFactory;

    /**
     * @var AddressRepositoryInterface
     */
    protected $addressRepository;

    /**
     * @var SubscriberModel
     */
    protected $subscriberModel;

    /**
     * @var SubscriberFactory
     */
    protected $subscriberFactory;

    /**
     * @var AttributeRepositoryInterface
     */
    protected $eavRepository;

    /**
     * @var DateTime
     */
    protected $datetime;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;

    /**
     * @var Attribute
     */
    private $_eavAttribute;

     /**
     * @var CustomerFactory
     */
    private $customerFactory;

     /**
     * Pronto customer
     *
     * @var CustomerRequestManagerInterface
     */
    protected $prontoRequestManager;

    /**
     * @param CustomerSession $customerSession
     * @param CustomerExtensionFactory $customerExtensionFactory
     * @param CustomerRepositoryInterface $customerRepository
     * @param CustomerHelper $customerHelper
     * @param CustomerRequestManager $prontoManager
     * @param AddressInterfaceFactory $addressDataFactory
     * @param AddressRepositoryInterface $addressRepository
     * @param SubscriberModel $subscriberModel
     * @param SubscriberFactory $subscriberFactory
     * @param AttributeRepositoryInterface $eavRepository
     * @param DateTime $datetime
     * @param LoggerInterface $logger
     * @param StoreManagerInterface $storeManager
     * @param ResourceConnection $resourceConnection
     * @param Attribute $eavAttribute
     */
    public function __construct(
        CustomerSession $customerSession,
        CustomerExtensionFactory $customerExtensionFactory,
        CustomerRepositoryInterface $customerRepository,
        CustomerHelper $customerHelper,
        CustomerRequestManager $prontoManager,
        AddressInterfaceFactory $addressDataFactory,
        AddressRepositoryInterface $addressRepository,
        SubscriberModel $subscriberModel,
        SubscriberFactory $subscriberFactory,
        AttributeRepositoryInterface $eavRepository,
        DateTime $datetime,
        LoggerInterface $logger,
        StoreManagerInterface $storeManager,
        ResourceConnection $resourceConnection,
        Attribute $eavAttribute,
        CustomerRequestManagerInterface $prontoRequestManager,
        CustomerFactory $customerFactory
    ) {
        $this->customerSession = $customerSession;
        $this->customerExtensionFactory = $customerExtensionFactory;
        $this->customerRepository = $customerRepository;
        $this->customerHelper = $customerHelper;
        $this->prontoManager = $prontoManager;
        $this->addressDataFactory = $addressDataFactory;
        $this->addressRepository = $addressRepository;
        $this->subscriberModel = $subscriberModel;
        $this->subscriberFactory = $subscriberFactory;
        $this->eavRepository = $eavRepository;
        $this->datetime = $datetime;
        $this->logger = $logger;
        $this->storeManager = $storeManager;
        $this->resourceConnection = $resourceConnection;
        $this->_eavAttribute = $eavAttribute;
        $this->prontoRequestManager = $prontoRequestManager;
        $this->customerFactory = $customerFactory;
    }

    /**
     * @param CustomerInterface $customer
     * @return void
     */
    public function updateProfile($customer = null)
    {
        if (!$customer) {
            $customer = $this->customerSession->getCustomerData();
        }

        if ($customer instanceof CustomerInterface && $this->isSyncReady()) {
            $prontoData = $this->prontoManager->getProntoCustomer($customer);

            if (!count($prontoData) || (!$prontoData['emailExist'] && !$prontoData['mobileExist'])) {
                return;
            }
            $prontoMember = new DataObject($prontoData);
            try {
                $this->customerHelper->setSyncTimestamp(time());
                $this->syncProfessionalDetails($customer, $prontoMember);
                $this->syncPreferences($customer, $prontoMember);
                $this->syncLoyaltyData($customer);
            } catch (\Exception $e) {
                $message = $e->getMessage();
                $this->logger->error($message);
            }
        }
    }

    /**
     * @param CustomerInterface $customer
     * @param DataObject $prontoMember
     * @return CustomerInterface
     * @throws NoSuchEntityException
     */
    protected function syncProfessionalDetails(&$customer, $prontoMember)
    {
        $connection  = $this->resourceConnection->getConnection();
        $preferredStore = $prontoMember->getData('PreferredStore') ?: null;
        $position = $prontoMember->getData('Industry') ?: null;
        $role = $prontoMember->getData('Role') ?: null;
        $businessAccount = !empty($prontoMember->getData('BusinessAccount')) && $prontoMember->getData('BusinessAccount') =='Y' ? 1 : 0;
        $abn = $prontoMember->getData('ABN') ?: null;

        if ($position && $this->attrValueExists('pronto_position', $position)) {
            $prontoPositionAttr = $this->getAttribute('pronto_position');
            $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $prontoPositionAttr->getBackendType());
            $data = [
                'attribute_id' => $prontoPositionAttr->getAttributeId(),
                'entity_id' =>  $customer->getId(),
                'value' => $position
            ];
            $connection->insertOnDuplicate($table, $data, ['value']);
        }
        if ($preferredStore && $this->attrValueExists('preferred_store', $preferredStore)) {
            $preferredStoreAttr = $this->getAttribute('preferred_store');
            $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $preferredStoreAttr->getBackendType());
            $data = [
                'attribute_id' => $preferredStoreAttr->getAttributeId(),
                'entity_id' => $customer->getId(),
                'value' =>$preferredStore
            ];
            $connection->insertOnDuplicate($table, $data, ['value']);
        }
        if ($role && $this->attrValueExists('role', $role)) {
            $roleAttr = $this->getAttribute('role');
            $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $roleAttr->getBackendType());
            $data = [
                'attribute_id' => $roleAttr->getAttributeId(),
                'entity_id' =>  $customer->getId(),
                'value' => $role
            ];
            $connection->insertOnDuplicate($table, $data, ['value']);
        }
        if ($businessAccount && $this->attrValueExists('business_account', $businessAccount)) {
            
            $company = $prontoMember->getData('Company') ?: null;
            $businessAccountAtr = $this->getAttribute('business_account');
            $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $businessAccountAtr->getBackendType());
            $data = [
                'attribute_id' => $businessAccountAtr->getAttributeId(),
                'entity_id' =>  $customer->getId(),
                'value' => $businessAccount
            ];
            $connection->insertOnDuplicate($table, $data, ['value']);

            $abnAtr = $this->getAttribute('abn');
            $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $abnAtr->getBackendType());
            $data = [
                'attribute_id' => $abnAtr->getAttributeId(),
                'entity_id' =>  $customer->getId(),
                'value' => $abn
            ];
            $connection->insertOnDuplicate($table, $data, ['value']);

            $company = $this->getAttribute('customer_company');
            $companyValue = $prontoMember->getData('Company') ?: null;
            $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $company->getBackendType());
            $data = [
                'attribute_id' => $company->getAttributeId(),
                'entity_id' =>  $customer->getId(),
                'value' => $companyValue
            ];
            $connection->insertOnDuplicate($table, $data, ['value']);
        }
        if(!$businessAccount && !is_null($abn)) {
            $this->insertOnDuplicateCustomer($connection, 'business_account', $businessAccount, $customer->getId());
        }
        return $customer;
    }

    public function insertOnDuplicateCustomer($connection, $attributeCode, $value, $customerId)
    {
        $customerAtr = $this->getAttribute($attributeCode);
        $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $customerAtr->getBackendType());
        $data = [
            'attribute_id' => $customerAtr->getAttributeId(),
            'entity_id' =>  $customerId,
            'value' => $value
        ];
        $connection->insertOnDuplicate($table, $data, ['value']);
    }

    /**
     * @param CustomerInterface $customer
     * @param DataObject $prontoData
     * @return CustomerInterface
     * @throws NoSuchEntityException
     */
    protected function syncPreferences(&$customer, $prontoData)
    {
        $connection  = $this->resourceConnection->getConnection();
        $isSubscribedDirectMarketingId = $this->getAttribute('is_subscribed_direct_marketing');
        $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $isSubscribedDirectMarketingId->getBackendType());
        $data = [
            'attribute_id' => $isSubscribedDirectMarketingId->getAttributeId(),
            'entity_id' =>  $customer->getId(),
            'value' => $prontoData->getData('DMPromo') === 'Y' ? 1 : 0
        ];
        $connection->insertOnDuplicate($table, $data, ['value']);
        $isSubscribedSmsPromoId = $this->getAttribute('is_subscribed_sms_promo');
        $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $isSubscribedSmsPromoId->getBackendType());
        $data = [
            'attribute_id' => $isSubscribedSmsPromoId->getAttributeId(),
            'entity_id' =>  $customer->getId(),
            'value' => $prontoData->getData('SMSPromo') === 'Y' ? 1 : 0
        ];
        $connection->insertOnDuplicate($table, $data, ['value']);
        $isSubscribedPhonePromoId = $this->getAttribute('is_subscribed_phone_promo');
        $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $isSubscribedPhonePromoId->getBackendType());
        $data = [
            'attribute_id' => $isSubscribedPhonePromoId->getAttributeId(),
            'entity_id' =>  $customer->getId(),
            'value' => $prontoData->getData('PhonePromo') === 'Y' ? 1 : 0
        ];
        $connection->insertOnDuplicate($table, $data, ['value']);

        $isSubscribed = $this->getAttribute('is_subscribed');
        $table = $connection->getTableName(self::CUSTOMER_EAV_TABLE . $isSubscribed->getBackendType());
        $data = [
            'attribute_id' => $isSubscribed->getAttributeId(),
            'entity_id' =>  $customer->getId(),
            'value' => $prontoData->getData('EmailPromo') === 'Y' ? 1 : 0
        ];
        $connection->insertOnDuplicate($table, $data, ['value']);

        return $customer;
    }


    public function syncLoyaltyData($customer)
    {
        $customer = $this->customerFactory->create()->load($customer->getId());
        if ($customer && $loyaltyId = $customer->getData('loyalty_id')) {
            $this->prontoRequestManager->updateCustomerInfo($customer, true);
        }

    }

       /**

     * @param CustomerInterface $customer

     * @return void

     */

    public function updateLoyaltyData($customer = null)
    {
        if (!$customer) {
            $customer = $this->customerSession->getCustomerData();
        }
        if ($customer instanceof CustomerInterface && $this->isSyncReady()) {
            try {
                $this->customerHelper->setSyncTimestamp(time());
                $this->syncLoyaltyData($customer, true);
            } catch (\Exception $e) {
                $message = $e->getMessage();
                $this->logger->error($message);
            }
        }
    }

    /**
     * @return bool
     */
    public function isSyncReady()
    {
        return $this->customerHelper->isSyncReady();
    }

    /**
     * @param bool $subscribe
     * @param CustomerInterface $customer
     * @return bool
     */
    protected function handleNewsletterSubscription($subscribe, $customer)
    {
        try {
            if (!$this->subscriptionExists($customer)) {
                $this->addSubscription($customer, (int) $subscribe);
            } else {
                $this->updateSubscription($customer, (int) $subscribe);
            }
        } catch (\Exception $e) {
            $message = '[' . $customer->getEmail() . ']: ' . $e->getMessage();
            $this->logger->error($message);
        } finally {
            return $subscribe;
        }
    }

    /**
     * @param CustomerInterface $customer
     * @return boolean
     */
    protected function subscriptionExists($customer)
    {
        return count($this->subscriberModel->loadByCustomerData($customer));
    }

    /**
     * @param CustomerInterface $customer
     * @param int $subscribe
     * @return int
     * @throws NoSuchEntityException
     */
    protected function addSubscription($customer, $subscribe)
    {
        return $this->getConnection()->insert(
            $this->getConnection()->getTableName('newsletter_subscriber'),
            [
                'store_id' => $this->storeManager->getStore()->getId(),
                'customer_id' => $customer->getId(),
                'subscriber_email' => $customer->getEmail(),
                'subscriber_status' => $subscribe
            ]
        );
    }

    /**
     * @param CustomerInterface $customer
     * @param int $subscribe
     * @return int
     * @throws NoSuchEntityException
     */
    protected function updateSubscription($customer, $subscribe)
    {
        return $this->getConnection()->update(
            $this->getConnection()->getTableName('newsletter_subscriber'),
            [
                'subscriber_status' => $subscribe
            ],
            [
                'customer_id = ?' => $customer->getId(),
                'store_id = ?' => $this->storeManager->getStore()->getId()
            ]
        );
    }

    /**
     * @return AdapterInterface|false
     */
    private function getConnection()
    {
        return $this->subscriberModel->getConnection();
    }

    /**
     * @param string $attributeCode
     * @param string $attributeValue
     * @param string $entityType
     * @return bool
     * @throws NoSuchEntityException
     */
    public function attrValueExists($attributeCode, $attributeValue, $entityType = \Magento\Customer\Model\Customer::ENTITY)
    {
        $attribute =  $this->getAttribute($attributeCode);
        $allOptions =  $attribute->getSource()->getAllOptions();
        foreach ($allOptions as $option) {
            if ($option['value'] == $attributeValue) {
                return true;
            }
        }
        return false;
    }

    /**
     * @param $attributeCode
     * @param string $entityType
     * @return AttributeInterface
     * @throws NoSuchEntityException
     */
    public function getAttribute($attributeCode, $entityType = \Magento\Customer\Model\Customer::ENTITY)
    {
        return $this->eavRepository->get($entityType, $attributeCode);
    }
}
