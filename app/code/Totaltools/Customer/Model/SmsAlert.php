<?php
namespace Totaltools\Customer\Model;

class SmsAlert
{
    const REQUEST_TIMEOUT = 60;

    protected $verificationCode;

    protected $mobileNo;

    protected $basicAuthorization;

    protected $helper;

    /**
     * SmsAlert constructor.
     * @param \Totaltools\Customer\Helper\Data $data
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function __construct(
        \Totaltools\Customer\Helper\SmsHelper $data
    ) {
        $this->helper = $data;
        $this->setBasicAuthorization($this->helper->getMessageMediaApi(), $this->helper->getMessgaeMediaSecret());
    }

    /**
     * @return string
     */
    public function getVerificationCode() : string
    {
        return $this->verificationCode;
    }

    /**
     * @param string $verificationCode
     * @return SmsAlert
     */
    public function setVerificationCode(string $verificationCode) : SmsAlert
    {
        $this->verificationCode = $verificationCode;
        return $this;
    }

    /**
     * @return string
     */
    public function getMobileNo() : string
    {
        return $this->mobileNo;
    }

    /**
     * @param string $mobileNo
     * @return SmsAlert
     * @format: +61xxxxxxxxx
     */
    public function setMobileNo(string $mobileNo) : SmsAlert
    {
        $this->mobileNo = $mobileNo;
        return $this;
    }

    /**
     * @return string
     */
    public function getBasicAuthorization() : string
    {
        return $this->basicAuthorization;
    }

    /**
     * @param string $api
     * @param string $secrete
     * @return SmsAlert
     */
    public function setBasicAuthorization(string $api, string $secrete) : SmsAlert
    {
        $code = $api . ":" . $secrete;
        $this->basicAuthorization = base64_encode($code);
        return $this;
    }

    /**
     * @return bool|string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function sendSms()
    {
        if ($this->helper->isMessageMediaEnabled()) {

            $c = curl_init();

            $body["messages"][] = ["content" => $this->helper->getMessageMediaMessage() . ": " . $this->getVerificationCode(),
                "destination_number" => $this->getMobileNo(),
                "format" => "SMS"
            ];

            $bodyXml = json_encode($body);

            $headers = [
                'Authorization: Basic '.$this->getBasicAuthorization(),
                'Content-type: application/json',
                'Accept: application/json'
            ];

            $requestUrl = $this->helper->getMessageMediaApiEndpoint();

            curl_setopt_array($c, [
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_POST           => true,
                CURLOPT_URL            => $requestUrl,
                CURLOPT_TIMEOUT        => self::REQUEST_TIMEOUT,
                CURLOPT_HTTPHEADER     => $headers,
                CURLOPT_HEADER         => true
            ]);

            if ($bodyXml) {
                curl_setopt($c, CURLOPT_POSTFIELDS, $bodyXml);
            }

            $result = curl_exec($c);

            if (!$result) {
                $error = curl_error($c);
                // TODO: Log error response
                return $result;
            }

            $content_type = curl_getinfo($c);

            curl_close($c);

            return true;
        }

        return false;
    }
}
