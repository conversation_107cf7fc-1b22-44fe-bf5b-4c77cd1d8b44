<?php
/**
 * <AUTHOR> Nguyen (<EMAIL>)
 * @package Totaltools_Customer
 */
namespace Totaltools\Customer\Model;

use Magento\Customer\Api\Data\CustomerInterface as CustomerDataInterface;
use Magento\Customer\Api\Data\CustomerInterfaceFactory;
use Magento\Customer\Model\Customer\CollectionFactory as CustomerCollectionFactory;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\ResourceModel\Customer\CollectionFactory as CustomerResourceCollectionFactory;
use Magento\Framework\Stdlib\DateTime\DateTime as MagentoDateTime;
use Magento\Customer\Model\AddressFactory;
use Magento\Customer\Api\Data\AddressInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use Magento\Customer\Model\Address;
use Magento\Customer\Api\Data\RegionInterfaceFactory;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\Customer\Helper\Data as TotaltoolsCustomerHelper;
use Totaltools\Customer\Model\AccountManagement as AccountManagementOverride;

/**
 * Import customers
 */
class Import
{

    /**
     * Address Factory
     * @var AddressInterfaceFactory
     */
    protected $_addressFactory;

    /**
     * Date/Time library
     * @var MagentoDateTime
     */
    protected $_date;

    /**
     * CSV headers
     * @var string[]
     */
    protected $headers = [];

    /**
     * Input mapper
     * @var string[]
     */
    protected $_mapper = [
        'firstname'      => 'firstname',
        'lastname'       => 'lastname',
        'prefix'         => 'prefix',
        'email'          => 'email',
        'gender'         => 'gender',
        'dob'            => 'dob',
        'password'       => 'password',
        'is_loyal'       => 'is_loyal',
        'account_code'   => 'account_code',
        'loyalty_level'  => 'loyalty_level',
        'website'        => 'website',
        'group'          => 'group_id',
        'store'          => 'store',
        'company'        => 'company',
        'street'         => 'street',
        'city'           => 'city',
        'country_id'     => 'country_id',
        'region'         => 'region',
        'postcode'       => 'postcode',
        'telephone'      => 'telephone',
    ];

    /**
     * Address model
     * @var Address
     */
    protected $_addressModel;

    /**
     * Customer factory
     * @var CustomerFactory
     */
    protected $_customerFactory;

    /**
     * Account management
     * @var AccountManagementOverride
     */
    protected $_accountManagement;

    /**
     * Customer collection factory
     * @var CustomerCollectionFactory
     */
    protected $_customerCollectionFactory;

    /**
     * Customer helper
     * @var TotaltoolsCustomerHelper
     */
    protected $_helper;

    /**
     * Address factory
     * @var AddressInterfaceFactory
     */
    protected $_addressInterface;

    /**
     * Data object helper
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * Region factory
     * @var RegionInterfaceFactory
     */
    protected $regionDataFactory;

    /**
     * Customer factory
     * @var CustomerInterfaceFactory
     */
    protected $customerInterfaceFactory;

    /**
     * Import constructor
     *
     * @param CustomerFactory $customerFactory
     * @param CustomerResourceCollectionFactory $customerCollectionFactory ,
     * @param AddressFactory $addressFactory
     * @param MagentoDateTime $date
     * @param Address $addressModel
     * @param AccountManagementOverride $accountManagement
     * @param AddressInterfaceFactory $addressInterface
     * @param DataObjectHelper $dataObjectHelper
     * @param RegionInterfaceFactory $regionDataFactory
     * @param TotaltoolsCustomerHelper $helper
     * @param CustomerInterfaceFactory $customerInterfaceFactory
     */
    public function __construct(
        CustomerFactory $customerFactory,
        CustomerResourceCollectionFactory $customerCollectionFactory,
        AddressFactory $addressFactory,
        MagentoDateTime $date,
        Address $addressModel,
        AccountManagementOverride $accountManagement,
        AddressInterfaceFactory $addressInterface,
        DataObjectHelper $dataObjectHelper,
        RegionInterfaceFactory $regionDataFactory,
        TotaltoolsCustomerHelper $helper,
        CustomerInterfaceFactory $customerInterfaceFactory
    ) {
        $this->_customerFactory = $customerFactory;
        $this->_customerCollectionFactory = $customerCollectionFactory;
        $this->_addressFactory = $addressFactory;
        $this->_addressModel = $addressModel;
        $this->_date = $date;
        $this->_accountManagement = $accountManagement;
        $this->_addressInterface = $addressInterface;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->regionDataFactory = $regionDataFactory;
        $this->_helper = $helper;
        $this->customerInterfaceFactory = $customerInterfaceFactory;
    }

    /**
     * Execute function
     * @param string $filePath
     * @param OutputInterface $output
     * @return boolean
     */
    public function execute($filePath, $output)
    {
        if (!$filePath) {
            return false;
        }

        $customerImportFile = $this->_helper->getAbsolutePath($filePath);
        if (!file_exists($customerImportFile)) {
            $output->writeln('<error>File '. $customerImportFile .' is not exist</error>');
            return false;
        }

        $validator = new \Magento\Framework\Validator\EmailAddress();
        $i = 0;
        $rowSuccess = 0;
        if (($handle = fopen($customerImportFile, 'r')) !== false) {
            while (($data = fgetcsv($handle, 1000, ",")) !== false) {
                unset($address);
                unset($customer);
                unset($customerDataObject);
                unset($passwordTooWeak);

                if ($i==0) {
                    $this->setHeaders($data);
                } else {
                    $data = $this->parseCsv($data);

                    if (
                        !isset($data['email'])
                        || $data['email'] == ''
                        || !$validator->isValid($data['email'])
                    ) {
                        continue;
                    }

                    $customer = $this->_customerFactory->create();
                    $customer->loadByEmail($data['email']);

                    $websiteId  = $this->_helper->getDefaultWebsiteId();
                    if (isset($data['website'])) {
                        $websiteId = $this->_helper->getWebsiteByCode($data['website']);
                    }

                    if ($customer->getId()) {
                        $data['entity_id'] = $customer->getId();
                        $data['website_id'] = isset($data['website_id'])
                            ? $data['website_id'] : $customer->getWebsiteId();
                        $data['is_active'] = $customer->getIsActive();
                    } else if (!isset($data['website_id'])) {
                        $data['website_id'] = $websiteId;
                    }

                    // Preparing data for new customer
                    if (isset($data['dob'])) {
                        if ($data['dob'] != '') {
                            $timestamp = $this->_date->timestamp($data['dob']);
                            $data['dob'] = $this->_date->gmtDate('Y-m-d', $timestamp);
                        }
                    }

                    $passwordTooWeak = (
                        isset($data['password'])
                        && strlen($data['password']) < AccountManagementOverride::MIN_PASSWORD_LENGTH
                    );

                    //set password for new customer
                    if (!$customer->getId()) {
                        if (!isset($data['password']) || $passwordTooWeak) {
                            $length = 50;
                            $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
                            $count = mb_strlen($chars);
                            for ($i = 0, $result = ''; $i < $length; $i++) {
                                $index = rand(0, $count - 1);
                                $result .= mb_substr($chars, $index, 1);
                            }
                            $data['password'] = '$@'.$result.'@$';
                        }
                    }

                    if (isset($data['gender'])) {
                        $gender = $data['gender'];
                        if (strtolower($gender) == 'male') {
                            $data['gender'] = 1;
                        } else {
                            $data['gender'] = 2;
                        }
                    }

                    // Create Address
                    if (isset($data['postcode']) && isset($data['street'])) {
                        if ($data['street'] == '') {
                            $data['street'] = 'N/A';
                        }
                        if (!isset($data['city'])) {
                            $data['city'] = 'N/A';
                        }
                        if (!isset($data['country_id'])) {
                            $data['country_id'] = 'AU';
                        }

                        $address = $this->_addressInterface->create();

                        if (isset($data['firstname'])) {
                            $address->setFirstname($data['firstname']);
                        }
                        if (isset($data['lastname'])) {
                            $address->setLastname($data['lastname']);
                        }
                        if (isset($data['company'])) {
                            $address->setCompany($data['company']);
                        }
                        if (isset($data['street'])) {
                            $address->setStreet([$data['street']]);
                        }
                        if (isset($data['city'])) {
                            $address->setCity($data['city']);
                        }
                        if (isset($data['postcode'])) {
                            $address->setPostcode($data['postcode']);
                        }
                        if (isset($data['telephone'])) {
                            $address->setTelephone($data['telephone']);
                        }
                        if (isset($data['telephone'])) {
                            $address->setCountryId($data['country_id']);
                        }
                        $address->setIsDefaultBilling(true);
                        $address->setIsDefaultShipping(true);
                    }

                    try {
                        if ($customer->getId()) {
                            $customer->setData($data)->setId($customer->getId());
                            $customer->save();
                            $output->writeln('<info>Updated customer: ' . $data['email'] . '</info>');
                        } else {
                            $customerDataObject = $this->customerInterfaceFactory->create();
                            $this->dataObjectHelper->populateWithArray(
                                $customerDataObject,
                                $data,
                                CustomerDataInterface::class
                            );
                            $customerDataObject->setWebsiteId($websiteId);
                            if (isset($address)) {
                                $customerDataObject->setAddresses([$address]);
                            }
                            $this->_accountManagement->createAccount($customerDataObject, $data['password'], '');
                            $message = '<info>Created customer: ' . $data['email'] . '</info>';
                            if ($passwordTooWeak) {
                                $message .= ' <comment>(password was too weak, requires reset)</comment>';
                            }
                            $output->writeln($message);
                        }
                        $rowSuccess++;
                    } catch (\Exception $e) {
                        $output->writeln('<error>ERROR: ' . $data['email'] . ' ' . $e->getMessage() . '</error>');
                        // continue importing next record when getting error save customer
                        continue;
                    }
                }
                $i++;
            }
        } else {
            $output->writeln('<error>Please grant read permission for the file '. $customerImportFile .'</error>');
        }

        return true;
    }

    /**
     * Set header array csv
     *
     * @param array $data
     * @return void
     */
    private function setHeaders($data)
    {
        foreach ($data as $col) {
            $this->headers[] = str_replace(' ', '_', strtolower($col));
        }
    }

    /**
     * Parse csv row to array with column header as key
     *
     * @param array $data
     * @return string[]
     */
    private function parseCsv($data)
    {
        $storeData = [];
        $col = 0;
        $mapper = $this->_mapper;
        foreach ($data as $value) {
            if (isset($this->headers[$col])) {
                if (isset($mapper[strtolower($this->headers[$col])])) {
                    $storeData[$mapper[strtolower($this->headers[$col])]] = trim($value);
                } else {
                    if ($this->headers[$col] != '') {
                        $storeData[$this->headers[$col]] = trim($value);
                    }
                }
            }
            $col++;
        }
        return $storeData;
    }
}
