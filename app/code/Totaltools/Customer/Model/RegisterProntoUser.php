<?php
/**
 * RegisterProntoUser
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Model;

use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\AddressInterfaceFactory;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Api\Data\RegionInterfaceFactory;
use Magento\Customer\Model\CustomerExtractor;
use Magento\Customer\Model\Session;
use Magento\Directory\Model\CountryFactory;
use Magento\Directory\Model\RegionFactory;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\StateException;
use \Magento\Customer\Model\Customer;
use Magento\Framework\Math\Random;
use Magento\Framework\Stdlib\DateTime\DateTime;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Magento\Newsletter\Model\SubscriberFactory;
use Magento\Store\Model\StoreManagerInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use Totaltools\Customer\Helper\Data;
use Magento\Framework\Message\ManagerInterface;
use Totaltools\Geo\Helper\GeoLocateHelper;
use Totaltools\Pronto\Model\Request\Customer as Pronto;
use Totaltools\Customer\Model\Request\ConnectPronto;
use Totaltools\Storelocator\Model\StoreRepository;

/**
 * RegisterProntoUser
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Ahmed <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
class RegisterProntoUser
{
    /**
     * Customer
     *
     * @var Customer
     */
    protected $customer;

    /**
     * Pronto
     *
     * @var Pronto
     */
    protected $pronto;

    /**
     * Random
     *
     * @var Random
     */
    protected $mathRandom;

    /**
     * StoreManagerInterface
     *
     * @var StoreManagerInterface
     */
    protected $store;

    /**
     * CustomerRepositoryInterface
     *
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Data
     *
     * @var Data
     */
    protected $customerHelper;

    /**
     * ManagerInterface
     *
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * GeoLocateHelper
     *
     * @var GeoLocateHelper
     */
    protected $geoLocateHelper;

    /**
     * StoreRepository
     *
     * @var StoreRepository
     */
    protected $storeRepository;

    /**
     * CustomerExtractor
     *
     * @var CustomerExtractor
     */
    protected $customerExtractor;

    /**
     * AccountManagementInterface
     *
     * @var AccountManagementInterface
     */
    protected $accountManagement;

    /**
     * ConnectPronto
     *
     * @var ConnectPronto
     */
    protected $connectPronto;

    /**
     * LoggerInterface
     *
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * DateTime
     *
     * @var DateTime
     */
    protected $_date;

    /**
     * AddressInterfaceFactory
     *
     * @var AddressInterfaceFactory
     */
    protected $addressDataFactory;

    /**
     * AddressRepositoryInterface
     *
     * @var AddressRepositoryInterface
     */
    protected $addressRepository;

    /**
     * CountryFactory
     *
     * @var CountryFactory
     */
    protected $countryFactory;

    /**
     * RegionFactory
     *
     * @var RegionFactory
     */
    protected $regionFactory;

    /**
     * RegionInterfaceFactory
     *
     * @var RegionInterfaceFactory
     */
    protected $customerRegionFactory;

    /**
     * SubscriberFactory
     *
     * @var SubscriberFactory
     */
    protected $subscriberFactory;

    /**
     * ScopeConfigInterface
     *
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * RegisterProntoUser constructor.
     *
     * @param Customer                    $customer
     * @param StoreManagerInterface       $store
     * @param Validator|null              $formKeyValidator
     * @param UrlFactory                  $urlFactory
     * @param Session                     $customerSession
     * @param CustomerRepositoryInterface $customerRepository
     * @param Random                      $mathRandom
     * @param Data                        $customerHelper
     * @param ManagerInterface            $messageManager
     * @param ConnectPronto               $connectPronto
     * @param GeoLocateHelper             $geoLocateHelper
     * @param StoreRepository             $storeRepository
     * @param CustomerExtractor           $customerExtractor
     * @param AccountManagementInterface  $accountManagement
     * @param LoggerInterface             $logger
     * @param DateTime                    $date
     * @param AddressRepositoryInterface  $addressRepository
     * @param AddressInterfaceFactory     $addressDataFactory
     * @param CountryFactory              $countryFactory
     * @param RegionFactory               $regionFactory
     * @param RegionInterfaceFactory      $customerRegionFactory
     * @param SubscriberFactory           $subscriberFactory
     * @param ScopeConfigInterface        $scopeConfig
     * @param Pronto                      $pronto
     */
    public function __construct(
        Customer $customer,
        StoreManagerInterface $store,
        Validator $formKeyValidator = null,
        UrlFactory $urlFactory,
        Session $customerSession,
        CustomerRepositoryInterface $customerRepository,
        Random $mathRandom,
        Data $customerHelper,
        ManagerInterface $messageManager,
        ConnectPronto $connectPronto,
        GeoLocateHelper $geoLocateHelper,
        StoreRepository $storeRepository,
        CustomerExtractor $customerExtractor,
        AccountManagementInterface $accountManagement,
        LoggerInterface $logger,
        DateTime $date,
        AddressRepositoryInterface $addressRepository,
        AddressInterfaceFactory $addressDataFactory,
        CountryFactory $countryFactory,
        RegionFactory $regionFactory,
        RegionInterfaceFactory $customerRegionFactory,
        SubscriberFactory $subscriberFactory,
        ScopeConfigInterface $scopeConfig,
        Pronto $pronto
    ) {
        $this->customerHelper = $customerHelper;
        $this->customer = $customer;
        $this->pronto = $pronto;
        $this->mathRandom = $mathRandom;
        $this->customerRepository = $customerRepository;
        $this->urlModel = $urlFactory->create();
        $this->store = $store;
        $this->session = $customerSession;
        $this->messageManager = $messageManager;
        $this->geoLocateHelper = $geoLocateHelper;
        $this->storeRepository = $storeRepository;
        $this->customerExtractor = $customerExtractor;
        $this->accountManagement = $accountManagement;
        $this->connectPronto = $connectPronto;
        $this->logger = $logger;
        $this->_date = $date;
        $this->addressRepository = $addressRepository;
        $this->addressDataFactory = $addressDataFactory;
        $this->scopeConfig = $scopeConfig;
        $this->countryFactory = $countryFactory;
        $this->regionFactory = $regionFactory;
        $this->subscriberFactory= $subscriberFactory;
        $this->customerRegionFactory = $customerRegionFactory;
    }

    /**
     *  Create new customer from pronto
     *
     * @param RequestInterface $request
     *
     * @return bool|int|CustomerInterface
     * @throws StateException
     * @throws LocalizedException
     */
    public function createProntoUser(RequestInterface $request)
    {
        $post = $request->getParams();
        $customer = $this->createUser($request);
        if (is_array($customer) && $customer['status'] == 'missing-info' ) {
            if(isset($customer['missing-data']) && $customer['missing-data'] == 'intro') {
                $post['intro'] = false;
                $post['mismatch'] = true;
                $post['mismatch_mobile'] = false;
                $this->messageManager->addNoticeMessage(__('Sorry, We don\'t have your first and last name, please provide this info too'));
            } elseif(isset($customer['missing-data']) && $customer['missing-data'] == 'mobile') {
                $post['mismatch'] = true;
                $post['mismatch_mobile'] = true;
            }
            $this->session->setCustomData($customer);
            $this->session->setCustomerFormData($post);
        } elseif (is_array($customer) && $customer['param'] == 'mobile' && $customer['status'] == 'mobile-verification') {
            $post['verified'] = false;
            $post['mismatch'] = true;
            $post['mismatch_mobile'] = false;
            $this->customerHelper->sendverificationCode($customer['Mobile']);
            $this->session->setCustomData($customer);
            $this->session->setCustomerFormData($post);
        }
        return $customer;
    }

    /**
     * Create user
     *
     * @param RequestInterface $request
     * @param bool             $forgotPassword
     *
     * @return bool|int|CustomerInterface
     * @throws StateException
     * @throws LocalizedException
     */
    public function createUser(RequestInterface $request, $forgotPassword = false)
    {
        $post = $request->getParams();
        $email = $post['email'];
        $mobileNumber = $post['mobile_number'];
        $abn = isset($post['abn']) ? $post['abn'] : '';

        $createUrl = $this->urlModel->getUrl('*/*/create');
        $customerExists = $this->customerHelper->customerExists($email, $mobileNumber, $abn);
        if (count($customerExists) !== 0) {
            $url = $this->urlModel->getUrl('customer/account/forgotpassword');
            // @codingStandardsIgnoreStart
            $message = __(
                'There is already an account with this email address or mobile number. If you are sure that it is your email address, <a href="%1">click here</a> to get your password and access your account.',
                $url
            );
            throw  new StateException($message);
        }
        $customerMember = $this->connectPronto->getProntoMember($post);
        if (!empty($customerMember) && array_key_exists('LoyaltyCustomer', $customerMember)) {
            $customerMember = $customerMember['LoyaltyCustomer'];
            if (isset($customerMember[0])) {
                $customerMember = $this->customerHelper->filterProntoCustomer($customerMember, $post);
            }
            if ($customerMember['EmailAddress'] == $email) {
                $customerMember['param'] = 'email';
            } else {
                $customerMember['param'] = 'mobile';
            }
            // Check if email already exists in db
            // but not when if this function is called from forgot password controller
            if (!empty($customerMember['EmailAddress']) && !$forgotPassword) {
                $alreadyCustomer = $this->emailCustomerExists($customerMember['EmailAddress']);
                if ($alreadyCustomer) {
                    $url = $this->urlModel->getUrl('contact');
                    $message = __(
                        '<strong>Your provided mobile number is associated with another registered account</strong>, Please contact support for further help <a href="%1">click here</a> .',
                        $url
                    );
                    throw  new StateException($message);
                }
            }
            // Check if loyalty id of the customer from pronto already exists
            $loyaltyIdExists = $this->loyaltyCustomerExists($customerMember['LoyaltyID'], $forgotPassword);
            if ($loyaltyIdExists) {
                return false;
            }
            if ((!empty($customerMember['FirstName']) && !empty($customerMember['LastName']))) {
                if ($forgotPassword) {
                    return $this->createCustomer($customerMember, $request);
                }
                if (!empty($mobileNumber) &&
                    (empty($customerMember['Mobile']) || $mobileNumber != $customerMember['Mobile'])) {
                    $customerMember['status'] = 'missing-info';
                    $customerMember['missing-data'] = 'mobile';
                    return $customerMember;
                }
                if ($customerMember['param'] == 'mobile') {
                    $customerMember['status'] = "mobile-verification";
                    return $customerMember;
                }
                $newCustomer = $this->createCustomer($customerMember, $request);
            } else {
                $customerMember['status'] = 'missing-info';
                $customerMember['missing-data'] = 'intro';
                return $customerMember;
            }
        } elseif (!empty($customerMember) && array_key_exists('status', $customerMember)) {
            if ($forgotPassword) {
                return $customerMember;
            }
            if ($customerMember['status'] == 'notfound') {
                throw new StateException(__('We were unable to find any existing Insider Rewards Member with the specified email or mobile number. Please register as a new Insider Rewards Member, <a href="%1">click here</a>.', $createUrl));
            } else {
                throw new \Exception('We couldn\'t verify your email at our servers please try again later');
            }
        } else {
            throw new StateException(__('We were unable to find any existing Insider Rewards Member with the specified email or mobile number. Please register as a new Insider Rewards Member, <a href="%1">click here</a>.', $createUrl));
        }
        return $newCustomer;
    }

    /**
     * @param $prontoCustomer
     * @param $request
     * @return bool|CustomerInterface
     * @throws StateException
     */
    public function createCustomer($prontoCustomer, $request)
    {
        $post = $request->getParams();
        $email = $post['email'];
        $mobileNumber = $post['mobile_number'];
        
        try {
            $storeId = $this->store->getStore()->getId();
            $websiteId = $this->store->getWebsite()->getId();
            $dob = !empty($prontoCustomer['DOB']) ? $prontoCustomer['DOB'] : '';
            if ($dob != '') {
                $timestamp = $this->_date->timestamp($dob);
                $dob = $this->_date->gmtDate('Y-m-d', $timestamp);
            }
            $position = !empty($prontoCustomer['Industry']) ? $prontoCustomer['Industry'] : '999';
            if (!empty($prontoCustomer['PreferredStore'])) {
                $preferredStore = $prontoCustomer['PreferredStore'];
            } else {
                try {
                    $preferredStore = $this->getNearestStore();
                } catch (\Exception $exception) {
                    $preferredStore = '003';
                }
            }

            $emailPromo = (
                !empty($prontoCustomer['EmailPromo']) &&
                $prontoCustomer['EmailPromo'] =='Y'
            )  ? 1 : 0;

            // if email empty and customer found by mobile number
            if (
                empty($prontoCustomer['EmailAddress']) &&
                ($prontoCustomer['param'] && $prontoCustomer['param'] == 'mobile')
            ) {
                $emailPromo = 1;
            }

            $DMPromo = !empty($prontoCustomer['DMPromo']) && $prontoCustomer['DMPromo'] =='Y' ? 1 : 0;
            $SMSPromo = !empty($prontoCustomer['SMSPromo']) && $prontoCustomer['SMSPromo'] =='Y' ? 1 : 0;
            $phonePromo = !empty($prontoCustomer['PhonePromo']) && $prontoCustomer['PhonePromo'] =='Y' ? 1 : 0;
            $abn = '';
            if (isset($post['business_account']) && $post['business_account'] == "1") {
                $businessAccount = 1;
                $abn = $post['abn'];
                $company = $post['company'];
            } else {
                $businessAccount = !empty($prontoCustomer['BusinessAccount']) && $prontoCustomer['BusinessAccount'] =='Y' ? 1 : 0;
                if ($businessAccount) {
                    $abn = !empty($prontoCustomer['ABN']) ? $prontoCustomer['ABN'] : '';
                }
                $company =  !empty($prontoCustomer['Company']) ? $prontoCustomer['Company'] : '';
            }
           
            if (!empty($prontoCustomer['missing-data']) && $prontoCustomer['missing-data'] == "intro") {
                $firstName = !empty($post['first_name']) ? $post['first_name'] : $prontoCustomer['FirstName'];
                $lastName = !empty($post['last_name']) ? $post['last_name'] : $prontoCustomer['LastName'];
            } else {
                $firstName = !empty($prontoCustomer['FirstName']) ? $prontoCustomer['FirstName'] : $post['first_name'];
                $lastName = !empty($prontoCustomer['LastName']) ? $prontoCustomer['LastName'] : $post['last_name'];
            }
            if(empty($mobileNumber)) {
                $mobileNumber = !empty($prontoCustomer['Mobile']) ? $prontoCustomer['Mobile'] : '';
            }

            $customerData = [
                'email' => $email,
                'firstname' => $firstName,
                'lastname' => $lastName,
                'mobile_number' => $mobileNumber,
                'pronto_position' => $position,
                'is_subscribed' => $emailPromo,
                '_store' => $storeId,
                '_website' => $websiteId,
                'oci_customer' => '',
                'is_active' => 1,
                'is_subscribed_direct_marketing' => $DMPromo,
                'is_subscribed_sms_promo' => $SMSPromo,
            ];
            $request->setParams($customerData);
            $customer = $this->customerExtractor->extract('customer_account_create', $request);
            $customer->setCreatedAt($prontoCustomer['JoinedDate']);
            $customer->setDob($dob);
            $customer
                ->setCustomAttribute('preferred_store', $preferredStore)
                ->setCustomAttribute('is_subscribed', $emailPromo)
                ->setCustomAttribute('is_subscribed_phone_promo', $phonePromo)
                ->setCustomAttribute('role', $prontoCustomer['Role'])
                ->setCustomAttribute('status', 1)
                ->setCustomAttribute('loyalty_id', $prontoCustomer['LoyaltyID'])
                ->setCustomAttribute('loyalty_level', (int)$prontoCustomer['Tier'])
                ->setCustomAttribute('is_loyal', 1)
                ->setCustomAttribute('customer_company', $company )
                ->setCustomAttribute('email_verification_status', EmailVerification::VERIFIED)
                ->setCustomAttribute('unverified_loyalty_id', null)
                ->setCustomAttribute('business_account', $businessAccount)
                ->setCustomAttribute('abn', $abn)
                ->setCustomAttribute('email_attempts_limit', 0);
            if (isset($prontoCustomer['PointExpiry'])) {
                $pointExpiry = $prontoCustomer['PointExpiry'];
                if ($pointExpiry && strtotime($pointExpiry)) {
                    $dateExpiry = date('d/m/Y', strtotime($pointExpiry));
                    $customer->setCustomAttribute('point_expiry', $dateExpiry);
                }
            }
            $customer = $this->accountManagement
                ->createAccount($customer);
            if ($customer->getId()) {
                $accountCode = $this->getAccountCode($prontoCustomer['LoyaltyID'], $email, $mobileNumber);
                $customer->setCustomAttribute('account_code', $accountCode);
                $this->customerRepository->save($customer);
                
                $this->updateProntoData($prontoCustomer, $customerData);
                // removed newsletter subscription
                if ($this->scopeConfig->getValue('totaltools_customer/registration/save_address') && isset($prontoCustomer['PostCode']) && isset($prontoCustomer['Address1'])) {
                    $this->addCustomerAddress($customer, $prontoCustomer);
                }
            }
            return $customer;
            // Note: The save returns the saved customer object, else throws an exception.
        } catch (\Exception $e) {
            $message = __('New customer registration failed '. $e->getMessage());
            $this->logger->alert('Registration error: '.$e->getMessage());
            throw  new StateException($message);
        }
        return false;
    }

     /**
     * @param $prontoCustomer
     * @param $customerData
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function updateProntoData($prontoCustomer, $customerData)
    {
        $modification = false;
        $params['LoyaltyID'] = $prontoCustomer['LoyaltyID'];

        if (!empty($customerData['email']) &&
            (empty($prontoCustomer['EmailAddress']) || ($customerData['email'] != $prontoCustomer['EmailAddress']))) {
            $params['EmailAddress'] = $customerData['email'];
            $modification = true;
        }

        if (!empty($customerData['mobile_number']) &&
            (empty($prontoCustomer['Mobile']) || $customerData['mobile_number'] != $prontoCustomer['Mobile'])) {
            $params['Mobile'] = $customerData['mobile_number'];
            $modification = true;
        }
        if ($modification) {
            $this->connectPronto->updateCustomerData($params);
        }
    }

    /**
     * Check for customer with same loyaty id
     * if loyalty id exists it will throw exception
     * but if the function call is from forgot password controller, it will return true
     *
     * @param $loyaltyID
     * @param $forgotPassword
     *
     * @return bool
     * @throws StateException
     * @throws LocalizedException
     */
    public function loyaltyCustomerExists($loyaltyID, $forgotPassword = false)
    {
        $customerExists = $this->customerHelper->getCustomerByField($loyaltyID, 'loyalty_id');
        if (count($customerExists) > 0) {
            if ($forgotPassword) {
                return true;
            }
            $url = $this->urlModel->getUrl('contact');
            $message = __(
                'There is already an account with this email address or mobile number. If you are sure that it is your account,  Please contact support <a href="%1">click here</a> .',
                $url
            );
            throw  new StateException($message);
        }
        return false;
    }

    /**
     *
     * @param $email
     * @return bool
     */
    public function emailCustomerExists($email)
    {
        $customer = $this->customer;
        $customer->loadByEmail($email);
        if ($customer->getId()) {
            return true;
        }
        return false;
    }

    /**
     * Get nearest store
     *
     * @return string
     * @throws NoSuchEntityException
     */
    protected function getNearestStore()
    {
        $postCode = $this->geoLocateHelper->getPostCode();
        $store = $this->storeRepository->getByZipCode($postCode);
        return $store && $store->getId() ? $store->getErpId() : '003';
    }

    /**
     * Add customer address
     *
     * @param $customer
     * @param $prontoData
     *
     */
    protected function addCustomerAddress($customer, $prontoData) {
        $addrLen = is_string($prontoData['Address1']) ? strlen($prontoData['Address1']) : 0;
        if (empty($prontoData['Address1']) || $addrLen < 5) {
            return false;
        }
        try {
            $street[] = !empty($prontoData['Address1'])? $prontoData['Address1'] :'';
            $street[] = !empty($prontoData['Address2'])? $prontoData['Address2'] :'';
            $address = $this->addressDataFactory->create();
            $city = !empty($prontoData['City'])? $prontoData['City'] :'';
            $postCode = !empty($prontoData['PostCode'])? $prontoData['PostCode'] :'';
            $company = !empty($prontoData['Company'])? $prontoData['Company'] :'';
            $phone = !empty($prontoData['Phone'])? $prontoData['Phone'] :'';
            $state = !empty($prontoData['State'])? $prontoData['State'] :'';
            $address->setFirstname($customer->getFirstname())
                ->setLastname($customer->getLastname())
                ->setCountryId('AU')
                ->setRegion($this->getRegion($state))
                ->setCity($city)
                ->setPostcode($postCode)
                ->setCustomerId($customer->getId())
                ->setCompany($company)
                ->setStreet($street)
                ->setTelephone($phone);
            $address->setIsDefaultBilling(1);
            $address->setIsDefaultShipping(1);

            $this->addressRepository->save($address);
        } catch (\Exception $exception) {
            $this->logger->error($customer->getEmail().' address registration failed. Error: '.$exception->getMessage() );
        }
    }

    /**
     * Get customer account code
     *
     * @param $loyaltyID
     * @param $email
     *
     * @return mixed|string
     * @throws \Exception
     */
    protected function getAccountCode($loyaltyID, $email, $mobileNumber)
    {
        $accountCode =  $this->pronto->getCustomerAccountCode($loyaltyID);
        if ($accountCode) {
            return  $accountCode;
        }
        if (empty($accountCode)) {
            $this->logger->info('Pronto Customer Register Event:: Customer Email - ' . $email .
                'Account Code Did not find. and going to createMember');
            $prontoCustomer = $this->connectPronto->createAccountCode($email, $mobileNumber);
            if (!empty($prontoCustomer)) {
                return $prontoCustomer['CustomerCreated']['CustomerAccountcode'];
            }
        }
        return '';
    }

    /**
     * Get Magento Region model by passing region code
     *
     * @param string $regionCode
     * @return \Magento\Customer\Api\Data\RegionInterface
     */
    protected function getRegion($regionCode, $countryCode = 'AU')
    {
        $countryId = $this->countryFactory->create()->loadByCode($countryCode)->getCountryId();
        $regionObj = $this->regionFactory->create()->loadByCode($regionCode, $countryId);
        $region = $this->customerRegionFactory->create()
            ->setRegionId((int) $regionObj->getRegionId())
            ->setRegion($regionObj->getDefaultName())
            ->setRegionCode($regionCode);

        return $region;
    }
}
