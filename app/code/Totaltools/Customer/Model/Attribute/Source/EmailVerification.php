<?php

namespace Totaltools\Customer\Model\Attribute\Source;

class EmailVerification extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    public function __construct( ) {}
    /**
     * Email verification statuses.
     *
     * @const string
     */
    const PENDING_SYNC = 'pending_sync';
    const PENDING_VERIFICATION = 'pending_verification';
    const MANUAL_VERIFICATION_REQUIRED = 'manual_verification_required';
    const VERIFIED = 'verified';

    /**
     * Get options for Email Verification Status attribute.
     *
     * @param bool $withEmpty
     * @param bool $defaultValues
     *
     * @return array
     */
    public function getAllOptions($withEmpty = false, $defaultValues = false)
    {
        if ($this->_options === null) {
            $this->_options = [
                ['value' => self::PENDING_SYNC, 'label' => __('Pending Sync')],
                ['value' => self::PENDING_VERIFICATION, 'label' => __('Pending Verification')],
                ['value' => self::MANUAL_VERIFICATION_REQUIRED, 'label' => __('Manual Verification Required')],
                ['value' => self::VERIFIED, 'label' => __('Verified')],
            ];
        }

        if ($withEmpty) {
            $this->_options = $this->addEmptyOption($this->_options);
        }

        return $this->_options;
    }

    /**
     * Add an empty option to the array
     *
     * @param array $options
     *
     * @return array
     */
    private function addEmptyOption(array $options)
    {
        array_unshift($options, ['label' => __('Please select an Email Status'), 'value' => '']);

        return $options;
    }
}