<?php

namespace Totaltools\Customer\Model\Attribute\Source;

class Position extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    public function __construct( ) {}
    
    /**
     * Get options for Position attribute.
     *
     * @param bool $withEmpty
     * @param bool $defaultValues
     *
     * @return array
     */
    public function getAllOptions($withEmpty = true, $defaultValues = false)
    {
        if ($this->_options === null) {
            $this->_options = [
                ['value' => '001', 'label' => __('Transportation/Trucking')],
                ['value' => '002', 'label' => __('Bricklayer / Concreter')],
                ['value' => '003', 'label' => __('Carpenter / Builder')],
                ['value' => '004', 'label' => __('Electrician')],
                ['value' => '005', 'label' => __('Joiner - Cabinet Maker')],
                ['value' => '006', 'label' => __('Marine - Boat Building')],
                ['value' => '007', 'label' => __('Metal Fabricator')],
                ['value' => '008', 'label' => __('Plumber')],
                ['value' => '009', 'label' => __('Roof Tiler')],
                ['value' => '010', 'label' => __('Telecommunications Technician')],
                ['value' => '011', 'label' => __('Building Maintenance Contractor')],
                ['value' => '012', 'label' => __('Tiler')],
                ['value' => '013', 'label' => __('Landscape Gardener')],
                ['value' => '014', 'label' => __('Painter')],
                ['value' => '015', 'label' => __('Fencer')],
                ['value' => '016', 'label' => __('Farmer')],
                ['value' => '017', 'label' => __('Aviation')],
                ['value' => '018', 'label' => __('Plasterer / Renderer')],
                ['value' => '019', 'label' => __('Government Department')],
                ['value' => '020', 'label' => __('Aircon Mechanical / Plumber')],
                ['value' => '021', 'label' => __('Wood Machinist')],
                ['value' => '022', 'label' => __('Welder / Fitter-Welder / Gasfitter')],
                ['value' => '023', 'label' => __('Fitter / Fitter & Turner / Machinist')],
                ['value' => '024', 'label' => __('Panel Beater')],
                ['value' => '025', 'label' => __('Sheetmetal Worker')],
                ['value' => '026', 'label' => __('Stonemason')],
                ['value' => '027', 'label' => __('Construction')],
                ['value' => '028', 'label' => __('Engineer')],
                ['value' => '029', 'label' => __('Heating, Ventilation & Air Conditioning')],
                ['value' => '030', 'label' => __('Automotive Mechanic')],
                ['value' => '031', 'label' => __('DIY')],
                ['value' => '032', 'label' => __('Powerline Worker')]
            ];
        }

        $labels = [];

        foreach ($this->_options as $index => $option) {
            $labels[$index] = $option['label']->__toString();
        }

        array_multisort($labels, SORT_ASC, $this->_options);
        $this->_options = array_merge($this->_options, [['value' => '999', 'label' => __('Other')]]);
        if ($withEmpty) {
            $this->_options = $this->addEmptyOption($this->_options);
        }

        return $this->_options;
    }

    /**
     * Add an empty option to the array
     *
     * @param array $options
     *
     * @return array
     */
    private function addEmptyOption(array $options)
    {
        array_unshift($options, ['label' => __('Please select a trade'), 'value' => '']);

        return $options;
    }
}