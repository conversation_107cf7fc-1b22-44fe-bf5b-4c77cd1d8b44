<?php

namespace Totaltools\Customer\Model\Attribute\Source;

class Role extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    public function __construct( ) {}
    /**
     * Get options for Role attribute.
     *
     * @param bool $withEmpty
     * @param bool $defaultValues
     *
     * @return array
     */
    public function getAllOptions($withEmpty = true, $defaultValues = false)
    {
        if ($this->_options === null) {
            $this->_options = [
                ['value' => 1, 'label' => __('Qualified Tradesman')],
                ['value' => 2, 'label' => __('Apprentice')],
                ['value' => 3, 'label' => __('Teacher')],
                ['value' => 4, 'label' => __('Procurement/Purchasing Officer')],
                ['value' => 5, 'label' => __('DIY')],
                ['value' => 9, 'label' => __('Other')]
            ];
        }

        if ($withEmpty) {
            $this->_options = $this->addEmptyOption($this->_options);
        }

        return $this->_options;
    }

    /**
     * Add an empty option to the array
     *
     * @param array $options
     *
     * @return array
     */
    private function addEmptyOption(array $options)
    {
        array_unshift($options, ['label' => __('Please select a role'), 'value' => '']);

        return $options;
    }
}