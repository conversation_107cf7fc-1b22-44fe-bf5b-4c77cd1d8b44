<?php
/**
 * Copyright © 2016 Balance Internet. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Customer\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Api\Data\StoreInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\StoreManagerInterface;

class SmsHelper extends AbstractHelper
{
    const MESSAGE_MEDIA_SMS_ENABLED = "totaltools_customer/sendsms/enabled";

    const MESSAGE_MEDIA_SMS_API_ENDIPOINT = "totaltools_customer/sendsms/endpoint";

    const MESSAGE_MEDIA_SMS_API = "totaltools_customer/sendsms/api";

    const MESSAGE_MEDIA_SMS_SECRET = "totaltools_customer/sendsms/secret";

    const MESSAGE_MEDIA_MESSAGE = "totaltools_customer/sendsms/message";

    /**
     * EncryptorInterface
     *
     * @var EncryptorInterface
     */
    private $_encryptor;

    /**
     * StoreManagerInterface
     *
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * SmsHelper constructor.
     *
     * @param Context               $context      Context
     * @param StoreManagerInterface $storeManager StoreManagerInterface
     * @param EncryptorInterface    $encryptor    EncryptorInterface
     */
    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        EncryptorInterface $encryptor
    ) {
        $this->storeManager = $storeManager;
        $this->_encryptor = $encryptor;
        parent::__construct($context);
    }

    /**
     * Get Current store id
     *
     * @return int
     * @throws NoSuchEntityException
     */
    public function getStoreId()
    {
        return $this->storeManager->getStore()->getId();
    }

    /**
     * Get Current store Info
     *
     * @return StoreInterface
     * @throws NoSuchEntityException
     */
    public function getStore()
    {
        return $this->storeManager->getStore();
    }


    /**
     * Check if sms api enabled
     *
     * @return bool
     * @throws NoSuchEntityException
     */
    public function isMessageMediaEnabled() : bool
    {
        return (bool) $this->scopeConfig->getValue(
            self::MESSAGE_MEDIA_SMS_ENABLED,
            ScopeInterface::SCOPE_STORE,
            $this->getStoreId()
        );
    }

    /**
     * Get Sms api endpoint
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getMessageMediaApiEndpoint() :string
    {
        return $this->scopeConfig->getValue(
            self::MESSAGE_MEDIA_SMS_API_ENDIPOINT,
            ScopeInterface::SCOPE_STORE,
            $this->getStoreId()
        );
    }

    /**
     * Get sms api
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getMessageMediaApi() : string
    {
        $api = $this->scopeConfig->getValue(
            self::MESSAGE_MEDIA_SMS_API,
            ScopeInterface::SCOPE_STORE,
            $this->getStoreId()
        );

        return $this->_encryptor->decrypt($api);
    }

    /**
     * Get sms api secret key
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getMessgaeMediaSecret() : string
    {
        $secret = $this->scopeConfig->getValue(
            self::MESSAGE_MEDIA_SMS_SECRET,
            ScopeInterface::SCOPE_STORE,
            $this->getStoreId()
        );

        return $this->_encryptor->decrypt($secret);
    }

    /**
     * Get sms text
     *
     * @return string
     * @throws NoSuchEntityException
     */
    public function getMessageMediaMessage() : string
    {
        return $this->scopeConfig->getValue(
            self::MESSAGE_MEDIA_MESSAGE,
            ScopeInterface::SCOPE_STORE,
            $this->getStoreId()
        );
    }
}