<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Customer\Helper;

use Magento\Framework\App\Helper\Context;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Company\Model\ResourceModel\Customer;

/**
 * Class AttributeData
 * @package Totaltools\Customer\Helper
 */
class AttributeData extends \Magento\Framework\App\Helper\AbstractHelper
{
    const XML_PATH_TRADE_URL= 'totaltools_customer/registration/trade_reward_url';  
    const XML_PATh_REFERRAL_CODE= 'totaltools_loyalty/insider_rewards/show_referral'; 

    /**
     * \Magento\Customer\Api\Data\CustomerInterface
     */
    protected $customer;

    /**
     * @var \Magento\Eav\Api\AttributeRepositoryInterface
     */
    protected $eavRepository;

    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $customerSession;

    /**
     * @var \Magento\Newsletter\Model\Subscriber
     */
    protected $subsriber;

    /**
     * @var \Magento\Company\Model\CustomerFactory
     */
    private $companyCustomerFactory;

    /**
     * @var \Magento\Framework\Message\ManagerInterface
     */
    protected $messageManager;
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;

    /**
     * AttributeData constructor.
     *
     * @param Context $context
     * @param \Magento\Eav\Api\AttributeRepositoryInterface $eavRepository
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Company\Model\CustomerFactory $companyCustomerFactory
     * @param \Magento\Newsletter\Model\Subscriber $subscriber
     */
    public function __construct(
        Context $context,
        \Magento\Eav\Api\AttributeRepositoryInterface $eavRepository,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Company\Model\CustomerFactory $companyCustomerFactory,
        \Magento\Framework\Message\ManagerInterface $messageManager,
        \Magento\Newsletter\Model\Subscriber $subscriber,
        Customer  $companyCustomer
    ) {
        parent::__construct($context);
        $this->eavRepository = $eavRepository;
        $this->customerRepository = $customerRepository;
        $this->customerSession = $customerSession;
        $this->companyCustomerFactory = $companyCustomerFactory;
        $this->subsriber = $subscriber;
        $this->messageManager = $messageManager;
        $this->companyCustomer = $companyCustomer;
    }

    /**
     * Return the Customer given the customer Id stored in the session.
     *
     * @return \Magento\Customer\Api\Data\CustomerInterface
     */
    public function getCustomer()
    {
        if (!$this->customer && $customerId = $this->customerSession->getCustomerId()) {
            $this->customer = $this->customerRepository->getById($customerId);
        }

        return $this->customer;
    }

    /**
     * @param $attributeCode
     * @param string $entityType
     * @return array
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getAttributeOptions($attributeCode, $entityType = \Magento\Customer\Model\Customer::ENTITY, $customer = null)
    {
        $attribute =  $this->eavRepository->get($entityType, $attributeCode);
        $allOptions =  $attribute->getSource()->getAllOptions();
        $resultOptions = [];
        $selectedAttribute = false;
        if(!isset($customer)) {
            $customer = $this->getCustomer();
        }
        
        $customerAttribute = $customer ? $customer->getCustomAttribute($attributeCode) : false;

        if ($customerAttribute) {
            $selectedAttribute = $customerAttribute->getValue();
        }

        foreach ($allOptions as $index => $option) {
            if (!trim($option['label'])) {
                continue;
            }

            $resultOptions[] = [
                'value' => $option['value'],
                'label' => $option['label'],
                'is_selected' => $selectedAttribute == $option['value']
            ];
        }

        return $resultOptions;
    }

    /**
     * @return array
     */
    public function getPreferencesAttributes()
    {
        $result = [];
        $subsriptionData = [];
        $attributes = [
            'is_subscribed' => __('Email'),
            'is_subscribed_direct_marketing' => __('Mail'),
            'is_subscribed_sms_promo' => __('SMS'),
            'is_subscribed_phone_promo' => __('Phone')
        ];

        $customer = $this->getCustomer();

        foreach ($attributes as $attributeCode => $attributeLabel) {
            $attributeValue = false;
            $customerAttribute = $customer ? $customer->getCustomAttribute($attributeCode) : false;

            if ($customerAttribute) {
                $attributeValue = $customerAttribute->getValue();
            }

            $result[$attributeCode] = [
                'value' => (bool)$attributeValue,
                'label' => $attributeLabel,
            ];
        }

        return array_merge($subsriptionData, $result);
    }

    /**
     * @return bool
     */
    public function isCustomerSubscribed()
    {
        if (!$customer = $this->getCustomer()) {
            return false;
        }

        $customerSubscriber = $this->subsriber->loadByEmail($customer->getEmail());

        return $customerSubscriber->isSubscribed();
    }


    /**
     * @return bool
     */
    public function isB2bCustomer()
    {
        $isLoggdIn = $this->customerSession->isLoggedIn();
        if ($isLoggdIn) {
            $customerId = $this->getCustomer()->getId();
            /** @var \Magento\Company\Model\Customer $b2bCustomer */
            $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId($customerId);
            $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
            $b2bCustomer =  array_diff($currentCompanyIds, [0]);
            return empty($b2bCustomer) ? false : true;
        }
        return false;
    }

    /**
     * @return bool
     */
    public function isRedirectedFromCheckout()
    {
        if ($this->customerSession->getIsRedirectedFromCheckout()) {
            $this->customerSession->unsIsRedirectedFromCheckout();
            return true;
        }

        return false;
    }

    /**
     * @return bool
     */
    public function redirectToUnlock()
    {
        $unlockRequest = $this->customerSession->getUnlockRedirect();
        if ($unlockRequest) {
            return true;
        }
        return false;
    }

    /**
     * @return bool
     */
    public function tradeRedirect()
    {
        $tradeRequest = $this->customerSession->getTradeRedirect();
        if ($tradeRequest) {
            if($this->isTradeRewards()) {
                $this->customerSession->setTradeRedirect(null);
            }
            return true;
        }
        return false;
    }

    /**
     * @return bool
     */
    public function storeRedirect()
    {
        $tradeRequest = $this->customerSession->getStoreRedirect();
        if ($tradeRequest) {
            if($this->isTradeRewards()) {
                $this->customerSession->setStoreRedirect(null);
            }
            return true;
        }
        return false;
    }

    public function isTradeRewards() : bool {
        $customerInterface = $this->getCustomer();
        if ($customerInterface) {
            return $this->isTradeRewardsCustomer($customerInterface);   
        }
        return false;   
    }

    public function isTradeRewardsCustomer($customerInterface) : bool {
        $businessAccount = $customerInterface->getCustomAttribute('business_account');
        $businessAccountStatus = $businessAccount && $businessAccount->getValue() ?
                $businessAccount->getValue() : null;   
        if($businessAccountStatus) {            
            return true;          
        } 
        return false; 
    }
    

    public function getUnlockUrl()
    {
        $url = '';
        $customerInterface = $this->getCustomer();
        if($this->isTradeRewards()) {         
            $url = $this->getUnlockWebUrl($customerInterface);            
            $this->customerSession->setUnlockRedirect(null);
        } else {
            $url = $this->getBusinessAccountUrl();
        }
        return $url;
    }

    public function getUnlockWebUrl($customerInterface) 
    {
        $unlockUrlString = $this->getCustomerDataString($customerInterface);
        return $this->getTradeRedirectUrl().$unlockUrlString;     
    }

    public function getTradeRedirectUrl()
    {
        $service = $this->customerSession->getTradeRewardService();
        $tradeUrlString = $this->scopeConfig->getValue(self::XML_PATH_TRADE_URL)??'';
        $tradeRewardUrls = json_decode($tradeUrlString, true);
        return $tradeRewardUrls[$service] ?? '';

    }

    public function getBusinessAccountUrl()
    {
        return $this->_urlBuilder->getUrl('customer/business/account');
    }

    public function getLoginUrl()
    {
        return $this->_urlBuilder->getUrl('customer/account/login');
    }

    public function getStoreAccountUrl()
    {
        $this->messageManager->addNoticeMessage(__('Please enter your ABN and Company Name to join Trade Rewards.'));
        return $this->_urlBuilder->getUrl('customer/store/account');
    }
    public function showUnlockMessage()
    {
        $customerInterface = $this->getCustomer();    
        if ($customerInterface instanceof CustomerInterface
            && $this->isUnverified($customerInterface)
        ) {
            $this->messageManager->addNoticeMessage(__("Please verify your account to continue."));
            return;
        }
        
        $url = $this->_urlBuilder->getUrl('customer/business/unlock');
        $message = __(
            'Thank you for signing up to Trade Rewards. Please <a href="%1" target="_blank">Click Here</a> to finish your Unlock application.',
            $url
        );
        $this->messageManager->addNotice($message);
    }

    public function getCustomerDataString($customerInterface) : string {
        $firstName = $customerInterface->getFirstname();
        $surname = $customerInterface->getLastname();
        $company = $this->getCustomerCompany($customerInterface);
        $email = $customerInterface->getEmail();
        $abn = $this->getCustomerAbn($customerInterface);
        $mobileNumber = $this->getCustomerMobile($customerInterface);
        return "first_name=$firstName&surname=$surname&business_name=$company&abn=$abn&email=$email&mobile=$mobileNumber";
    }

    public function getAbn()
    {
        $customerInterface = $this->getCustomer();
        return  $this->getCustomerAbn($customerInterface);
    }

    public function getCustomerAbn($customerInterface)
    {
        $abn = $customerInterface->getCustomAttribute('abn');
        return $abn ? $abn->getValue() : '';
    }

    public function getCompany()
    {
        $customerInterface = $this->getCustomer();
        return  $this->getCustomerCompany($customerInterface);
    }

    public function getCustomerCompany($customerInterface)
    {
        $company = $customerInterface->getCustomAttribute('customer_company');
        return $company ? $company->getValue() : '';
    }

    public function getCustomerMobile($customerInterface)
    {
        $mobileNumber = $customerInterface->getCustomAttribute('mobile_number');
        $auMobileNumber = $mobileNumber ?  preg_replace('/^0/', '+61', $mobileNumber->getValue() ?? '') : '';
        $auMobileNumber =  wordwrap($auMobileNumber, 3, "-", true);
        return $auMobileNumber;
    }

    /**
     * @param CustomerInterface $customer
     * @return bool
     */
    protected function isUnverified($customer)
    {
        $emailVerificationStatus = $customer->getCustomAttribute('email_verification_status');
        $verificationStatus = $emailVerificationStatus ? $emailVerificationStatus->getValue() : '';

        return $verificationStatus == EmailVerification::PENDING_VERIFICATION
        || $verificationStatus == EmailVerification::MANUAL_VERIFICATION_REQUIRED;
    }

    public function isLoggedIn() : bool {
        if (!$this->customerSession->isLoggedIn()) {
            return false;
        }   
        return true;
    }

    public function showReferralCode() {
        return $this->scopeConfig->getValue(self::XML_PATh_REFERRAL_CODE);
    }
}
