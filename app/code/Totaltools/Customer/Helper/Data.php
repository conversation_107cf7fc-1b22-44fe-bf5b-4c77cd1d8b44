<?php

/**
 * Copyright © 2016 Balance Internet. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Customer\Helper;

use Exception;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Api\Data\RegionInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Encryption\EncryptorInterface;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Filesystem;
use Magento\Framework\Math\Random;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\WebsiteRepository;
use Psr\Log\LoggerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Totaltools\Customer\Model\Request\ConnectPronto;
use Totaltools\Customer\Model\SmsAlert;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilder;
use Magento\Directory\Model\CountryFactory;
use Magento\Directory\Model\RegionFactory;
use Magento\Customer\Api\Data\RegionInterfaceFactory;
use \Magento\Framework\App\RequestInterface;

/**
 * Helper class
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Ahmed <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
class Data extends AbstractHelper
{
    const MESSAGE_RESEND_DURATION = "totaltools_customer/sendsms/minutes_duration";

    const MESSAGE_RESEND_TRIES = "totaltools_customer/sendsms/no_of_tries";

    const CONFIG_PATH_SYNC = 'totaltools_customer/registration/sync_to_pronto';

    const CUSTOMER_CREATE_ACCOUNT_CONTENT_TOP = 'customer_create_account_content_top';

    const CUSTOMER_BUSINESS_CREATE_ACCOUNT_CONTENT_TOP = 'customer_create_business_account_content_top';

    const CUSTOMER_CREATE_ACCOUNT_BENEFITS = 'customer_create_account_benefits';
    const CUSTOMER_CREATE_TRADE_ACCOUNT_BENEFITS = 'customer_create_business_account_benefits';

    const INSIDER_REWARDS_MEMBER_REGISTRATION_TOP_TEXT = 'customer_register_top_text';
    const TRADE_REWARDS_MEMBER_REGISTRATION_TOP_TEXT = 'trade_rewards_member_registration_top_text';

    

    const CUSTOMER_CREATE_TRADE_ACCOUNT_TITLE = 'Accelerate your business with trade rewards';
    const CUSTOMER_CREATE_ACCOUNT_TITLE = 'Be in the know with Insider Rewards';   

    const TRADE_REWARDS_MEMBER_ONLINE_REGISTRATION = 'INSIDER REWARDS MEMBER SIGN UP TO TRADE REWARDS';
    const TRADE_REWARDS_MEMBER_ONLINE_REGISTRATION_HEADING = 'Existing INSIDER REWARDS MEMBER SIGN UP TO TRADE REWARDS';
    const INSIDER_REWARDS_MEMBER_ONLINE_REGISTRATION = 'INSIDER REWARDS MEMBER ONLINE REGISTRATION';

    

    /**
     * Debug enable/disable
     *
     * @var bool
     */
    const CONFIG_PATH_DEBUG = 'totaltools_customer/debugging/enable';

    /**
     * Timeout configuration for Pronto to Magento sync
     *
     * @var int
     */
    const PRONTO_SYNC_TIMEOUT = "totaltools_loyalty/sync/sync_timeout";
    
    /**
     * Session key name for sync timestamp
     *
     * @var int
     */
    const SYNC_TIMESTAMP = 'sync_timestamp';

    /**
     * Timeout configuration for Pronto to Magento INVOICES sync
     *
     * @var int
     */
    const PRONTO_INVOICE_SYNC_TIMEOUT = "totaltools_loyalty/sync/invoices_sync_timeout";

    /**
     * Session key name for invoice sync timestamp
     *
     * @var int
     */
    const INVOICE_SYNC_TIMESTAMP = 'invoices_sync_timeout';

    /**
     * Timeout configuration for Pronto to Magento Rewards sync
     *
     * @var int
     */
    const PRONTO_REWARDS_SYNC_TIMEOUT = "totaltools_loyalty/sync/rewards_sync_timeout";

    /**
     * Session key name for rewards sync timestamp
     *
     * @var int
     */
    const REWARDS_SYNC_TIMESTAMP = 'rewards_sync_timeout';

    /**
     * Filesystem
     *
     * @var Filesystem
     */
    protected $_fileSystem;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $_customerRepositoryInterface;

    /**
     * StateInterface
     *
     * @var StateInterface
     */
    protected $inlineTranslation;

    /**
     * StateInterface
     *
     * @var TransportBuilder
     */
    protected $transportBuilder;

    /**
     * LoggerInterface
     *
     * @var LoggerInterface
     */
    protected $logger;
    /**
     * WebsiteRepository
     *
     * @var WebsiteRepository
     */
    protected $_storeManagerRepository;

    /**
     * StoreManagerInterface
     *
     * @var StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * EncryptorInterface
     *
     * @var EncryptorInterface
     */
    protected $_encryptor;

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Random
     *
     * @var Random
     */
    protected $mathRandom;

    /**
     * SmsAlert
     *
     * @var SmsAlert
     */
    protected $smsAlert;

    /**
     * SearchCriteriaBuilder
     *
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    /**
     * FilterBuilder
     *
     * @var FilterBuilder
     */
    protected $filterBuilder;


    /**
     * FilterGroupBuilder
     *
     * @var FilterGroupBuilder
     */
    protected $filterGroupBuilder;

    /**
     * CountryFactory
     *
     * @var CountryFactory
     */
    protected $countryFactory;

    /**
     * RegionFactory
     *
     * @var RegionFactory
     */
    protected $regionFactory;

    /**
     * RegionInterfaceFactory
     *
     * @var RegionInterfaceFactory
     */
    protected $customerRegionFactory;

    /**
     * ConnectPronto
     *
     * @var ConnectPronto
     */
    protected $connectPronto;

    /**
     * ManagerInterface
     *
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * StoreManagerInterface
     *
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;

    /**
     * RequestInterface
     *
     * @var RequestInterface
     */
    protected $request;

    /**
     * Data constructor.
     *
     * @param Context                     $context
     * @param Filesystem                  $fileSystem
     * @param WebsiteRepository           $_storeManagerRepository
     * @param StoreManagerInterface       $_storeManager
     * @param TransportBuilder            $transportBuilder
     * @param StateInterface              $inlineTranslation
     * @param Session                     $customerSession
     * @param Random                      $mathRandom
     * @param LoggerInterface             $logger
     * @param SmsAlert                    $smsAlert
     * @param SearchCriteriaBuilder       $searchCriteriaBuilder
     * @param FilterBuilder               $filterBuilder
     * @param FilterGroupBuilder          $filterGroupBuilder
     * @param CustomerRepositoryInterface $customerRepository
     * @param ManagerInterface            $messageManager
     * @param EncryptorInterface          $encryptor
     * @param CountryFactory              $countryFactory
     * @param RegionFactory               $regionFactory
     * @param ConnectPronto               $connectPronto
     * @param UrlFactory                  $urlFactory
     * @param RegionInterfaceFactory      $customerRegionFactory
     */
    public function __construct(
        Context $context,
        Filesystem $fileSystem,
        WebsiteRepository $_storeManagerRepository,
        StoreManagerInterface $_storeManager,
        TransportBuilder $transportBuilder,
        StateInterface $inlineTranslation,
        Session $customerSession,
        Random $mathRandom,
        LoggerInterface $logger,
        SmsAlert $smsAlert,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        FilterGroupBuilder $filterGroupBuilder,
        CustomerRepositoryInterface $customerRepository,
        ManagerInterface $messageManager,
        EncryptorInterface $encryptor,
        CountryFactory $countryFactory,
        RegionFactory $regionFactory,
        ConnectPronto $connectPronto,
        UrlFactory $urlFactory,
        RegionInterfaceFactory $customerRegionFactory,
        RequestInterface $request
    ) {
        $this->_fileSystem = $fileSystem;
        $this->_storeManagerRepository = $_storeManagerRepository;
        $this->storeManager = $_storeManager;
        $this->transportBuilder = $transportBuilder;
        $this->inlineTranslation = $inlineTranslation;
        $this->logger = $logger;
        $this->session = $customerSession;
        $this->mathRandom = $mathRandom;
        $this->_encryptor = $encryptor;
        $this->smsAlert = $smsAlert;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
        $this->_customerRepositoryInterface = $customerRepository;
        $this->messageManager = $messageManager;
        $this->countryFactory = $countryFactory;
        $this->regionFactory = $regionFactory;
        $this->customerRegionFactory = $customerRegionFactory;
        $this->connectPronto = $connectPronto;
        $this->request = $request;
        $this->urlModel = $urlFactory->create();
        parent::__construct($context);
    }

    /**
     * Generate absolute path from relative path
     *
     * @param string $relativePath string
     *
     * @return string
     * */
    public function getAbsolutePath($relativePath)
    {
        $tmpDirectory = $this->_fileSystem->getDirectoryRead('base');
        return $tmpDirectory->getAbsolutePath($relativePath);
    }

    /**
     * Retrieve default website id
     *
     * @return string | int
     * */
    public function getDefaultWebsiteId()
    {
        return $this->_storeManager->getDefaultStoreView()->getData('website_id');
    }

    /**
     * Retrieve default website id by website code
     *
     * @param $websiteCode
     *
     * @return string | int
     */
    public function getWebsiteByCode($websiteCode)
    {
        $website = $this->_storeManager->get($websiteCode);
        return $website->getId();
    }

    /**
     * Send verification code
     *
     * @param $mobileNumber
     *
     * @return bool
     * @throws LocalizedException
     */
    public function sendVerificationCode($mobileNumber)
    {
        $diffTime = 0;
        $verificationCode = null;
        $smsGap = (int)$this->scopeConfig->getValue(self::MESSAGE_RESEND_DURATION);
        $noOfTries = (int)$this->scopeConfig->getValue(self::MESSAGE_RESEND_TRIES);
        $sentBeforeAt = $this->session->getLastVerificationSmsTime();
        $noTried = 0;
        if (!isset($sentBeforeAt)) {
            $this->session->setLastVerificationSmsTime(time());
        } else {
            $noTried = (int)$this->session->getSmsTried();
            if ($noTried >= $noOfTries && $this->session->isLoggedIn()) {
                $this->messageManager->addNoticeMessage("You have reached the limit to resend verification code, please re-login ");
                return true;
            }
            $diffTime = (time() - $sentBeforeAt) / 60;
            if ($diffTime < $smsGap) {
                $this->messageManager->addNoticeMessage("Code already sent to this number {$mobileNumber} ");
                return true;
            }
            $this->session->setLastVerificationSmsTime(time());
        }

        if ($diffTime) {
            $verificationCode = $this->session->getVerificationCode();
        }
        if (!$verificationCode) {
            $char = Random::CHARS_DIGITS;
            $verificationCode = $this->mathRandom->getRandomString(5, $char);
        }
        // TODO : verify au mobile regex
        $auMobileNumber = preg_replace('/^0/', '+61', $mobileNumber);
        $this->smsAlert->setMobileNo($auMobileNumber);
        $this->smsAlert->setVerificationCode($verificationCode);
        $this->session->setVerificationCode($verificationCode);
        $this->smsAlert->sendSms();
        $this->session->setSmsTried(++$noTried);
        $this->messageManager->addNoticeMessage("We just sent a text message to {$mobileNumber} with a code for you to enter here");
        $this->_logger->debug("We just sent a text message to " . $mobileNumber . " with a code, Mobile verification Code:" . $verificationCode);
        return true;
    }

    /**
     * Verify Code
     *
     * @param $code
     *
     * @return bool
     */
    public function verifyCode($code)
    {
        $verificationCode = $this->session->getVerificationCode();
        return $verificationCode === $code;
    }

    /**
     * Get Customer mobile
     *
     * @return mixed
     */
    public function getCustomerMobile()
    {
        $mobileNumber = $this->session->getCustomer()->getData('mobile_number');
        return $mobileNumber ? $mobileNumber : '';
    }

    /**
     * Check customer
     *
     * @param $emil
     * @param $mobileNumber
     *
     * @return array|null
     * @throws LocalizedException
     */
    public function customerExists($emil, $mobileNumber, $abn = '')
    {
        $customerRepository = $this->_customerRepositoryInterface;
        $emailFilter = $this->filterBuilder
            ->setField('email')
            ->setValue($emil)
            ->setConditionType('eq')
            ->create();

        $this->filterGroupBuilder
            ->addFilter($emailFilter);
        if (!empty($mobileNumber)) {
            $mobile = $this->filterBuilder
                ->setField('mobile_number')
                ->setValue($mobileNumber)
                ->setConditionType('eq')
                ->create();
           $this->filterGroupBuilder
                ->addFilter($mobile);
        }
        if (!empty($abn)) {
            $abn = $this->filterBuilder
                ->setField('abn')
                ->setValue($abn)
                ->setConditionType('eq')
                ->create();
            $filterGroup = $this->filterGroupBuilder
                ->addFilter($abn);
        }
        $filterGroup = $this->filterGroupBuilder
            ->create();
        

        $searchCriteria = $this->searchCriteriaBuilder
            ->setFilterGroups([$filterGroup])
            ->create();
        return $customerRepository->getList($searchCriteria)->getItems();
    }

    /**
     * Get customer by field
     *
     * @param $value
     * @param $field
     *
     * @return CustomerInterface[]
     * @throws LocalizedException
     */
    public function getCustomerByField($value, $field)
    {
        $customerRepository = $this->_customerRepositoryInterface;
        $filter[] = $this->filterBuilder
            ->setField($field)
            ->setValue($value)
            ->setConditionType('eq')
            ->create();
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilters($filter)
            ->create();
        return $customerRepository->getList($searchCriteria)->getItems();
    }

    public function uniqueAttrCheck($post, $customerId = null)
    {
        $this->uniqueMobileCheck($post, $customerId);
    }
    /**
     * @param $post
     * @param null|int $customerId
     * @throws InputException
     * @throws LocalizedException
     */
    public function uniqueMobileCheck($post, $customerId = null)
    {
        $mobileNumber = isset($post['mobile_number']) ? $post['mobile_number'] : '';
        $abn = isset($post['abn'])? $post['abn'] : '';
        $customer = [];
        if (!empty($mobileNumber)) {
                $customer = $this->getCustomerByField($mobileNumber, 'mobile_number');
        }
        // customerId will be null on create customer request
        if (!$customerId) {
            if (count($customer) !== 0) {
                $this->uniqueMobileFailed();
            } else {
                $customer = $this->getCustomerByField($abn, 'abn');
                if (count($customer) !== 0) {
                    $message = __('There is already an account with this abn number.');
                    throw new  InputException(__($message));
                }
            }
            if (!empty($mobileNumber)) {
                $this->uniqueCheckOnPronto($post);
            }
        } else {
            if (count($customer) === 1) {
                // we receive data in array of objects, that's why pick the first object
                $customer = array_shift($customer);
                if ($customerId != $customer->getId()) {
                    $this->uniqueMobileFailed(true);
                }
            } elseif (count($customer) > 1) {
                // result returned are more than one which shows the mobile number is already assigned to multiple accounts
                $this->uniqueMobileFailed(true);
            } elseif (count($customer) == 0) {
                $post = [
                    'email' => $this->session->getCustomerData()->getEmail(),
                    'mobile_number' => $mobileNumber
                ];
                if (!empty($mobileNumber)) {
                    $this->uniqueCheckOnPronto($post, true);
                }
            }
        }
        
    }

    /**
     * Moile already exists
     *
     * @param bool $update
     *
     * @throws InputException
     */
    public function uniqueMobileFailed($update = false)
    {
        $url = $this->urlModel->getUrl('customer/account/forgotpassword');
        if (!$update) {
            $message = __(
                'There is already an account with this mobile number. If you are sure that it is your account, <a href="%1">click here</a> to get your password and access your account.',
                $url
            );
        } else {
            $message = __(
                'There is already an account with this mobile number.'
            );
        }
        throw new  InputException(__($message));
    }


    /**
     * @param $post
     * @throws LocalizedException
     */
    public function uniqueCheckOnPronto($post, $update = false)
    {
        $customerMember = $this->getProntoMember($post);
        if (!empty($customerMember) && array_key_exists('LoyaltyCustomer', $customerMember)) {
            $customerMember = $customerMember['LoyaltyCustomer'];
            if (isset($customerMember[0])) {
                $this->filterProntoCustomer($customerMember, $post, $update);
            }
            if(!$update) {
                $customerByLoyaltyId = $this->getCustomerByField($customerMember['LoyaltyID'], 'loyalty_id');
                if (count($customerByLoyaltyId) > 0) {
                    $url = $this->urlModel->getUrl('contact');
                    $message = __(
                        'There is already an account with this email address or mobile number. If you are sure that it is your account,  Please contact support <a href="%1">click here</a> .',
                        $url
                    );
                    throw  new InputException($message);
                }
            }
        }
    }


    /**
     * Get Magento Region model by passing region code
     *
     * @param string $regionCode
     * @param string $countryCode
     *
     * @return RegionInterface
     */
    public function getRegion($regionCode, $countryCode = 'AU')
    {
        $countryId = $this->countryFactory->create()->loadByCode($countryCode)->getCountryId();
        $regionObj = $this->regionFactory->create()->loadByCode($regionCode, $countryId);
        return $this->customerRegionFactory->create()
            ->setRegionId((int)$regionObj->getRegionId())
            ->setRegion($regionObj->getDefaultName())
            ->setRegionCode($regionCode);
    }


    /**
     * Is Debug mode enabled
     *
     * @return boolean
     */
    public function isDebug()
    {
        return $this->scopeConfig->isSetFlag(self::CONFIG_PATH_DEBUG);
    }


    /**
     * Get Pronto member data
     *
     * @param $post
     *
     * @return bool|int
     * @throws LocalizedException
     */
    public function getProntoMember($post)
    {
        return $this->connectPronto->getProntoMember($post);
    }

    /**
     * Filter multiple records from pronto
     *
     * @param $customerMember
     * @param $post
     *
     * @param bool $update
     * @return array|mixed
     * @throws InputException
     */
    public function filterProntoCustomer($customerMember, $post, $update = false)
    {
        $email = $post['email'];
        $mobileNumber = $post['mobile_number'];


        $customerMember = array_filter($customerMember, function ($customerData) use ($email, $mobileNumber) {
            if ($customerData['Mobile'] == $mobileNumber && $customerData['EmailAddress'] == $email) {
                return true;
            }
            return false;
        });
        if (isset($customerMember[0])) {
            $customerMember = $customerMember[0];
        } else {
            $url = $this->urlModel->getUrl('contact');
            if (!$update) {
                $message = __(
                    '<strong>We are unable to create your account using provided email and mobile number</strong>, Please contact support for further help <a href="%1">click here</a> .',
                    $url
                );
            } else {
                $message = __(
                    '<strong>We are unable to update your account using provided email and mobile number</strong>, Please contact support for further help <a href="%1">click here</a> .',
                    $url
                );
            }
            throw  new InputException($message);
        }
        return $customerMember;
    }

    /**
     * Timeout in seconds for Pronto to Magento sync of customer data
     *
     * @return int
     */
    public function getSyncTimeout()
    {
        return ((int)$this->scopeConfig->getValue(self::PRONTO_SYNC_TIMEOUT)) * 60;
    }

    /**
     * @return bool
     */
    public function isSyncReady()
    {
        $isSyncReadyTimeout = time() - $this->getSyncTimestamp() > $this->getSyncTimeout();
        $forcedSync = $this->session->getForcedSyncLoyaltyData();
        $this->session->setForcedSyncLoyaltyData();
        $isSyncReady = $isSyncReadyTimeout || $forcedSync;
        return $isSyncReady ;
    }

    /**
     * @param int $timestamp
     * @return void
     */
    public function setSyncTimestamp($timestamp)
    {
        $this->session->setData(self::SYNC_TIMESTAMP, $timestamp);
    }

    /**
     * @return int|null
     */
    public function getSyncTimestamp()
    {
        return (int) $this->session->getData(self::SYNC_TIMESTAMP);
    }

    /**
     * @return bool
     */
    public function isInvoiceSyncReady()
    {
        return time() - $this->getInvoiceSyncTimestamp() > $this->getSyncTimeout();
    }

    /**
     * @param int $timestamp
     * @return void
     */
    public function setInvoiceSyncTimestamp($timestamp)
    {
        $this->session->setData(self::INVOICE_SYNC_TIMESTAMP, $timestamp);
    }

    /**
     * @return int|null
     */
    public function getInvoiceSyncTimestamp()
    {
        return (int) $this->session->getData(self::INVOICE_SYNC_TIMESTAMP);
    }

    /**
     * @return int|null
     */
    public function getInvoiceSyncTimeout()
    {
        return ((int)$this->scopeConfig->getValue(self::PRONTO_INVOICE_SYNC_TIMEOUT)) * 60;
    }

    /**
     * @return bool
     */
    public function isRewardsSyncReady()
    {
        return time() - $this->getRewardsSyncTimestamp() > $this->getRewardsSyncTimeout();
    }

    /**
     * @param int $timestamp
     * @return void
     */
    public function setRewardsSyncTimestamp($timestamp)
    {
        $this->session->setData(self::REWARDS_SYNC_TIMESTAMP, $timestamp);
    }

    /**
     * @return int|null
     */
    public function getRewardsSyncTimestamp()
    {
        return (int) $this->session->getData(self::REWARDS_SYNC_TIMESTAMP);
    }

     /**
     * Timeout in seconds for Pronto to Magento sync of customer data
     *
     * @return int
     */
    public function getRewardsSyncTimeout()
    {
        return ((int)$this->scopeConfig->getValue(self::PRONTO_REWARDS_SYNC_TIMEOUT)) * 60;
    }

    /**
     * Get Url Parameters
     *
     * @return int
     */
    public function getParameters() 
    {
        return $this->request->getParams();
    }

    public function isTradeRewards() 
    {
        $params = $this->getParameters();
        if (isset($params['trade_rewards']) ) {
            return true;
        } else {
            return false;
        }
    }

    public function isStoreAccount()
    {
        $params = $this->getParameters();
        if (isset($params['store_account']) && $params['store_account'] == true) {
            return true;
        } else {
            return false;
        }
    }
    

    public function getCustomerCreateAccountTopBlock()
    {
        if ($this->isTradeRewards()) {
            return self::CUSTOMER_BUSINESS_CREATE_ACCOUNT_CONTENT_TOP;
        } else {
            return self::CUSTOMER_CREATE_ACCOUNT_CONTENT_TOP;
        }
    }

    public function getCustomerCreateAccountBenefits()
    {
        if ($this->isTradeRewards()) {
            return self::CUSTOMER_CREATE_TRADE_ACCOUNT_BENEFITS;
        } else {
            return self::CUSTOMER_CREATE_ACCOUNT_BENEFITS;
        }
    }

    public function getCustomerCreateAccountTitle()
    {
        if ($this->isTradeRewards()) {
            return self::CUSTOMER_CREATE_TRADE_ACCOUNT_TITLE;
        } else {
            return self::CUSTOMER_CREATE_ACCOUNT_TITLE;
        }
    }

    public function getRewardsRegisterAccountTop()
    {
        if ($this->isTradeRewards()) {
            return self::TRADE_REWARDS_MEMBER_REGISTRATION_TOP_TEXT;
        } else {
            return self::INSIDER_REWARDS_MEMBER_REGISTRATION_TOP_TEXT ;
        }
    }

    public function getRewardsRegisterAccountTitle()
    {
        if ($this->isTradeRewards()) {
            return self::TRADE_REWARDS_MEMBER_ONLINE_REGISTRATION;
        } else {
            return self::INSIDER_REWARDS_MEMBER_ONLINE_REGISTRATION ;
        }
    }

    public function getRewardsRegisterAccountHeading()
    {
        if ($this->isTradeRewards()) {
            return self::TRADE_REWARDS_MEMBER_ONLINE_REGISTRATION_HEADING;
        } else {
            return self::INSIDER_REWARDS_MEMBER_ONLINE_REGISTRATION ;
        }
    }

    //   ValidateABN
    //     Checks ABN for validity using the published 
    //     ABN checksum algorithm.
    

    function ValidateABN($abn)
    {
        $weights = array(10, 1, 3, 5, 7, 9, 11, 13, 15, 17, 19);

        // strip anything other than digits
        $abn = preg_replace("/[^\d]/","",$abn);

        // check length is 11 digits
        if (strlen($abn)==11) {
            // apply ato check method 
            $sum = 0;
            foreach ($weights as $position=>$weight) {
                $digit = $abn[$position] - ($position ? 0 : 1);
                $sum += $weight * $digit;
            }
            return ($sum % 89)==0;
        } 
        return false;
    }
}
