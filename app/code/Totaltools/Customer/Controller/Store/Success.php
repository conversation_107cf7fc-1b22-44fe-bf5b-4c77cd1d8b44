<?php
namespace Totaltools\Customer\Controller\Store;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Theme\Block\Html\Breadcrumbs;

class Success extends Action implements HttpGetActionInterface
{
    /**
     * PageFactory
     *
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $_storeManager;

    
    /**
     * Register constructor.
     *
     * @param Context      $context
     * @param PageFactory  $resultPageFactory
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->_storeManager = $storeManager;
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|ResultInterface|Page
     */
    public function execute()
    {
        $resultPage = $this->resultPageFactory->create();
        $resultPage->getConfig()->getTitle()->set(__('STORE ACCOUNT REQUEST'));

        /** @var Breadcrumbs $breadcrumbsBlock */
        if ($breadcrumbsBlock = $resultPage->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb(
                'home',
                [
                    'label' => __('Home'),
                    'title' => __('Go to Home Page'),
                    'link' => $this->_storeManager->getStore()->getBaseUrl()
                ]
            );
   
            $breadcrumbsBlock->addCrumb(
                'storeaccount',
                [
                    'label' => __('Store account request'),
                    'title' => __('Store account request')
                ]
            );
        }
        
        return $resultPage;
    }
}