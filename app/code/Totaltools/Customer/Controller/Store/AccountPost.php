<?php
namespace Totaltools\Customer\Controller\Store;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\App\Action\HttpPostActionInterface;
use Totaltools\Storelocator\Helper\CommercialEmail;

class AccountPost extends Action implements HttpPostActionInterface
{
    /**
     * PageFactory
     *
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * CommercialEmail
     *
     * @var CommercialEmail
     */
    protected $commercialEmail;

    
    /**
     * Register constructor.
     *
     * @param Context      $context
     * @param PageFactory  $resultPageFactory
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory,
        CommercialEmail $commercialEmail
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->commercialEmail = $commercialEmail;
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|ResultInterface|Page
     */
    public function execute()
    {
        $post = $this->getRequest()->getParams();
        $postData = [
            'firstname' => $post['firstname'],
            'lastname'  =>  $post['lastname'],
            'email'  =>  $post['email'],
            'mobile_number'  =>  $post['mobile_number'],
            'abn'  =>  $post['abn'],
            'customer_company'  =>  $post['customer_company'],
            'estimated_spending'  =>  $post['estimated_spending'],
            'postcode'  =>  $post['postcode']
        ];
        $this->commercialEmail->sendStoreAccountFormEmail($postData);
        $defaultUrl = $this->_url->getUrl('*/*/success', ['_secure' => true]);
        $resultRedirect = $this->resultRedirectFactory->create();
        $resultRedirect->setUrl($this->_redirect->success($defaultUrl));
        return $resultRedirect;
    }
}
