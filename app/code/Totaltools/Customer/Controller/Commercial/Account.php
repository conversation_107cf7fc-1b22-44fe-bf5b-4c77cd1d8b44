<?php
namespace Totaltools\Customer\Controller\Commercial;


use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;
use Magento\Framework\App\Action\HttpGetActionInterface;

/**
 * Account
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Ahmed <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
class Account extends Action implements HttpGetActionInterface 
{
    /**
     * PageFactory
     *
     * @var PageFactory
     */
    protected $resultPageFactory;

    
    /**
     * Register constructor.
     *
     * @param Context      $context
     * @param PageFactory  $resultPageFactory
     */
    public function __construct(
        Context $context,
        PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|ResultInterface|Page
     */
    public function execute()
    {   
        
        return $this->resultPageFactory->create();
    }
}