<?php
/**
 * Total InfoTech
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Controller\Business;

use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Page;
use \Magento\Authorization\Model\UserContextInterface;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Totaltools\Customer\Helper\AttributeData;

/**
 * Unlock
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
class Unlock extends Action implements HttpGetActionInterface
{

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * @var AttributeData
     */
    protected $customerHelper;

    /**
     * Register constructor.
     *
     * @param Context      $context
     * @param Session      $customerSession
     * @param UserContextInterface  $userContext
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        AttributeData $customerHelper
    ) {
        $this->session = $customerSession;
        $this->customerHelper = $customerHelper;
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|ResultInterface|Page
     */
    public function execute()
    {
        /**
         * Redirect
         *
         * @var Redirect $resultRedirect
        */
        $resultRedirect = $this->resultRedirectFactory->create();
        $this->session->setUnlockRedirect('1');
        $this->session->setTradeReward('unlock');
        $url = $this->_url->getUrl('customer/account/create',['trade_rewards'=> 'unlock']);
        if (!$this->session->isLoggedIn()) {
            $this->_redirect($url);
            return;
        }
        if ($this->customerHelper->redirectToUnlock()) {
            $url = $this->customerHelper->getUnlockUrl();         
        }
        $resultRedirect->setUrl($url);
        return $resultRedirect;
    }

    
}