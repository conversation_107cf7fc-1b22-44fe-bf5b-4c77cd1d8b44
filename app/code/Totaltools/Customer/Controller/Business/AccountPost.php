<?php
/**
 * Total Info Tech
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
namespace Totaltools\Customer\Controller\Business;

use Exception;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Exception\InputException;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Result\PageFactory;
use Magento\Customer\Model\ResourceModel\CustomerRepository;
use Totaltools\Customer\Helper\Data;
use Totaltools\Customer\Helper\AttributeData;
use Totaltools\Storelocator\Helper\CommercialEmail;
use Totaltools\Storelocator\Model\StoreRepository;
use Totaltools\Storelocator\Model\Zone;
use Magento\Framework\App\ResourceConnection;

/**
 * AccountPost controller
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Ahmed <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
class AccountPost extends Action
{
    protected $resultPageFactory;

    /**
     * Validator
     *
     * @VAR Validator
     */
    protected $formKeyValidator;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * @var CustomerRepository
     */
    protected $customerRepository;

    /**
     * @var Data
     */
    protected $customerHelper; 

    /**
     * @var AttributeData
     */
    protected $attributeData;

    /**
     * @var CommercialEmail
     */
    protected $commercialEmail;

    /**
     * @var StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;

    /**
     * Register constructor.
     *
     * @param Context            $context            Context
     * @param Validator|null     $formKeyValidator   Validator
     * @param UrlFactory         $urlFactory         UrlFactory
     * @param Session            $customerSession    Session
     * @param PageFactory        $resultPageFactory  PageFactory
     */
    public function __construct(
        Context $context,
        Validator $formKeyValidator = null,
        UrlFactory $urlFactory,
        Session $customerSession,
        PageFactory $resultPageFactory,
        Data $helper,
        AttributeData $attributeData,
        CustomerRepository $customerRepository,
        StoreRepository $storeRepository,
        CommercialEmail $commercialEmail,
        ResourceConnection $resourceConnection
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->urlModel = $urlFactory->create();
        $this->session = $customerSession;
        $this->customerRepository = $customerRepository;
        $this->formKeyValidator = $formKeyValidator ?: ObjectManager::getInstance()->get(Validator::class);
        $this->customerHelper = $helper;
        $this->attributeData = $attributeData;
        $this->commercialEmail = $commercialEmail;
        $this->_storeRepository = $storeRepository;
        $this->resourceConnection = $resourceConnection;
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|Redirect|ResultInterface
     */
    public function execute()
    {
        /**
         * Redirect
         *
         * @var Redirect $resultRedirect
        */
        $resultRedirect = $this->resultRedirectFactory->create();
        if (!$this->session->isLoggedIn() ) {
            $resultRedirect->setPath('*/*/');
            return $resultRedirect;
        }
        $post = $this->getRequest()->getParams();
        $estimatedSpend = '';
        $tradeReward = '';
        if (isset($post['trade_reward_account']) && !empty($post['trade_reward_account']) ) {
            $tradeReward = $post['trade_reward_account'];
        }
        if (!$this->getRequest()->isPost() || !$this->formKeyValidator->validate($this->getRequest())) {
            $url = $this->urlModel->getUrl('*/*/account', ['_secure' => true]);
            $resultRedirect->setUrl($this->_redirect->error($url));
            return $resultRedirect;
        }
        if ($tradeReward == '2' && isset($post['business_account']) && !empty($post['business_account']) ) {
            if ((!isset($post['abn']) || empty($post['abn']) )) {
                throw new InputException(__('Abn Number is required'));
            }
            $post['abn'] = (int) $post['abn'];
            if( strlen($post['abn']) != 11 ) {
                throw new InputException(__('Abn Number must be of 11 digits'));
            }
        }
        
        
        try {
            if (isset($post['estimated_monthly_spend']) && !empty($post['estimated_monthly_spend']) ) {
                $estimatedSpend = $post['estimated_monthly_spend'];
            }
            $customerInterface = $this->customerRepository->getById($this->session->getCustomerId());
            $businessAccount = $customerInterface->getCustomAttribute('business_account');
            $businessAccountStatus = $businessAccount && $businessAccount->getValue() ?
                    $businessAccount->getValue() : null;   
            if($businessAccountStatus && !$estimatedSpend ) {
                $defaultUrl = $this->_url->getUrl('*/account/edit', ['_secure' => true]);
                $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
                $this->messageManager->addNoticeMessage(__('You are already a business reward member'));
                return $resultRedirect;
            } 
            if($tradeReward == '2') {
                $customer = $this->customerHelper->getCustomerByField($post['abn'], 'abn');
                if (count($customer) !== 0) {
                    $message = __('There is already an account with this abn number.');
                    throw new  InputException(__($message));
                }
                $customerInterface->setCustomAttribute('abn',$post['abn']);
                if (isset($post['company'])) {
                    $customerInterface->setCustomAttribute('customer_company', $post['company'] );
                } 
                if (isset($post['postcode']) && !empty($post['postcode']) ) {
                    $store = $this->_storeRepository->getByPostcodeInZone($post['postcode']);
                    if ($store && $store->getId() !== null) {
                        $storeData = $store->getData();
                        $post['preferred_store'] =  $storeData['erp_id'];
                    } else {
                        $store = $this->_storeRepository->getByPostcodeInZone(Zone::DEFAULT_ZONE_POSTCODE);
                        $storeData = $store->getData();
                        $post['preferred_store'] =  $storeData['erp_id'];
                    }      
                    $customerInterface->setCustomAttribute('preferred_store', $post['preferred_store'] );
                }
                
                $customerInterface->setCustomAttribute('business_account', $post['business_account'] );
                $customerInterface->setCustomAttribute('estimated_monthly_spend', $estimatedSpend );
            } else {
                $customerInterface->setCustomAttribute('estimated_monthly_spend', $estimatedSpend );
            }
            
            $this->customerRepository->save($customerInterface);

            if (isset($post['postcode']) && !empty($post['postcode'])) {
                $this->updateCustomerPreferredStore($this->session->getCustomerId(), $post['preferred_store']);
            }
            
            if (!$estimatedSpend && $this->attributeData->tradeRedirect()) {
            
                if ($this->attributeData->isTradeRewards()) {
                    $this->commercialEmail->processTradeRewardsEmail($customerInterface);  
                    $url = $this->attributeData->getUnlockUrl();         
                    $resultRedirect->setUrl($url);
                    return $resultRedirect;  
                }  
            } elseif (!$estimatedSpend && !$this->attributeData->tradeRedirect()) {
                $this->messageManager->addSuccessMessage(__("Thank you for signing up for 'Trade Rewards'"));  
            }
            if ($estimatedSpend) {
                $this->processStoreEmail($customerInterface);
                $this->messageManager->addSuccessMessage(__("Thank you for registering your interest for 'Store Account'. Your application has been received and sent to your selected store and a store representative will get in touch with you shortly")); 
            } 
            
        } catch(Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }        
        $defaultUrl = $this->urlModel->getUrl('*/account', ['_secure' => true]);
       
        $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
        return $resultRedirect;
    }

    public function processStoreEmail($customer) 
    {

        $firstName = $customer->getFirstname();
        $lastName = $customer->getLastname();
        $emailAddress = $customer->getEmail();
        $mobileNumber = !empty($customer->getCustomAttribute('mobile_number')) ? $customer->getCustomAttribute('mobile_number')->getValue() : "";
        $store = $this->_storeRepository->getByErpId((int)$customer->getCustomAttribute('preferred_store')->getValue());
        $storeName = $store->getData('store_name');
        $storeEmail = $store->getData('store_escalations_email');
        $preferredStore = $storeName;
        $compnayName = $customer->getCustomAttribute('customer_company')->getValue();
        $abn = $customer->getCustomAttribute('abn')->getValue();
        $estimatedMonthlySpend = $customer->getCustomAttribute('estimated_monthly_spend')->getValue();

        $postData = [
            'firstname' => $firstName,
            'lastname'  =>  $lastName,
            'email'  =>  $emailAddress,
            'mobile_number'  =>  $mobileNumber,
            'abn'  =>  $abn,
            'customer_company'  =>  $compnayName,
            'estimated_spending'  =>  $estimatedMonthlySpend,
            'preferred_store'  =>  $storeName,
            'store_email'  =>  $storeEmail,
        ];

        $this->commercialEmail->sendStoreAccountFormEmail($postData);        
    }

    public function updateCustomerPreferredStore($customerId, $preferredStore)
    {
        $customer = $this->customerRepository->getById($customerId);
        $preferredStoreValue = !empty($customer->getCustomAttribute('preferred_store')) ? $customer->getCustomAttribute('preferred_store')->getValue() : 0;
        
        if ($preferredStoreValue != $preferredStore) {
            $connection = $this->resourceConnection->getConnection();
            $tableName = $this->resourceConnection->getTableName('customer_entity_int');
            $eavAttribute = $connection->getTableName('eav_attribute');
            $attributeId = $connection->fetchOne(
                "SELECT attribute_id FROM {$eavAttribute} WHERE attribute_code = :attribute_code AND entity_type_id = 1",
                [':attribute_code' => 'preferred_store']
            );

            if ($attributeId) {
                $data = [
                    'value' => $preferredStore
                ];
                
                $where = [
                    'entity_id = ?' => $customerId,
                    'attribute_id = ?' => $attributeId
                ];
                
                $connection->update($tableName, $data, $where);
            }
        }
    }
}