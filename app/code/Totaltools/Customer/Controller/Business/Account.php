<?php
/**
 * Total InfoTech
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Controller\Business;

use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;
use \Magento\Authorization\Model\UserContextInterface;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Customer\Model\ResourceModel\CustomerRepository;
use Magento\Theme\Block\Html\Breadcrumbs;

/**
 * Account
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
class Account extends Action implements HttpGetActionInterface
{
    /**
     * PageFactory
     *
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * @var CustomerRepository
     */
    protected $customerRepository;

    /**
     * Register constructor.
     *
     * @param Context      $context
     * @param Session      $customerSession
     * @param PageFactory  $resultPageFactory
     * @param CustomerRepository  $customerRepository
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        PageFactory $resultPageFactory,
        CustomerRepository $customerRepository
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->session = $customerSession;
        $this->customerRepository = $customerRepository;
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|ResultInterface|Page
     */
    public function execute()
    {
        /**
         * Redirect
         *
         * @var Redirect $resultRedirect
        */
        $tradeReward = $this->session->getTradeRedirect();
            if ($tradeReward) {
                $this->session->setTradeRedirect($tradeReward);
                $this->session->setTradeRewardService($tradeReward);
            } else {
                $tradeReward = 'unlock';
                $this->session->setTradeRedirect($tradeReward);
                $this->session->setTradeRewardService($tradeReward);
            }
        if (!$this->session->isLoggedIn()) {
            if($this->_request->getParam('insider')==1) {
                $this->_redirect('customer/account/login');
                return;
            }
            $this->_redirect('customer/account/create',['trade_rewards'=> $tradeReward]);
            return;
        }    
        $resultRedirect = $this->resultRedirectFactory->create();
        $customerInterface = $this->customerRepository->getById($this->session->getCustomerId());
        $businessAccount = $customerInterface->getCustomAttribute('business_account');
        $businessAccountStatus = $businessAccount && $businessAccount->getValue() ?
                $businessAccount->getValue() : null;   
        if($businessAccountStatus) {
            $defaultUrl = $this->_url->getUrl('*/account/edit', ['_secure' => true]);
            $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
            $this->messageManager->addNoticeMessage(__('You are already a Trade Rewards member'));
            return $resultRedirect;
        }

        $result = $this->resultPageFactory->create();

        /** @var Breadcrumbs $breadcrumbsBlock */
        if ($breadcrumbsBlock = $result->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb(
                'my-account',
                [
                    'label' => __('My Account'),
                    'title' => __('My Account'),
                    'link' => '/loyalty/insider/info/'
                ]
            );

            $breadcrumbsBlock->addCrumb(
                'trade-rewards',
                [
                    'label' => __('Sign Up To Trade Rewards'),
                    'title' => __('Sign Up To Trade Rewards')
                ]
            );
        }
        $this->messageManager->addNoticeMessage(__('Please enter your ABN and Company Name to join Trade Rewards.'));
        return $result;
    }
}