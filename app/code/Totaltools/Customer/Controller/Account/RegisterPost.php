<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
namespace Totaltools\Customer\Controller\Account;

use Magento\Customer\Model\Registration;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Escaper;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\StateException;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Result\PageFactory;
use Totaltools\Customer\Helper\Data;
use Totaltools\Customer\Model\RegisterProntoUser;

/**
 * RegisterPost controller
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Ahmed <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
class RegisterPost extends Action
{
    protected $resultPageFactory;

    /**
     * RegisterProntoUser
     *
     * @var RegisterProntoUser
     */
    protected $registerProntoUser;

    /**
     * Validator
     *
     * @VAR Validator
     */
    protected $formKeyValidator;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Registration
     *
     * @var Registration
     */
    protected $registration;

    /**
     * Escaper
     *
     * @var Escaper
     */
    protected $escaper;

    /**
     * Data
     *
     * @var Data
     */
    protected $customerHelper;

    /**
     * Register constructor.
     *
     * @param Context            $context            Context
     * @param Validator|null     $formKeyValidator   Validator
     * @param UrlFactory         $urlFactory         UrlFactory
     * @param Session            $customerSession    Session
     * @param Registration       $registration       Registration
     * @param PageFactory        $resultPageFactory  PageFactory
     * @param RegisterProntoUser $registerProntoUser RegisterProntoUser
     * @param Escaper            $escaper            Escaper
     * @param Data               $customerHelper     Data
     */
    public function __construct(
        Context $context,
        Validator $formKeyValidator = null,
        UrlFactory $urlFactory,
        Session $customerSession,
        Registration $registration,
        PageFactory $resultPageFactory,
        RegisterProntoUser $registerProntoUser,
        Escaper $escaper,
        Data $customerHelper
    ) {
        $this->registerProntoUser = $registerProntoUser;
        $this->resultPageFactory = $resultPageFactory;
        $this->urlModel = $urlFactory->create();
        $this->session = $customerSession;
        $this->registration = $registration;
        $this->escaper = $escaper;
        $this->customerHelper = $customerHelper;
        $this->formKeyValidator = $formKeyValidator ?: ObjectManager::getInstance()->get(Validator::class);
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|Redirect|ResultInterface
     */
    public function execute()
    {
        /**
         * Redirect
         *
         * @var Redirect $resultRedirect
        */
        $resultRedirect = $this->resultRedirectFactory->create();
        if ($this->session->isLoggedIn() || !$this->registration->isAllowed()) {
            $resultRedirect->setPath('*/*/');
            return $resultRedirect;
        }
        $post = $this->getRequest()->getParams();
        $tradeRewards = false;
        $queryParams = ['_secure' => true];
        if (isset($postData['business_account']) && !empty($postData['business_account']) ) {
            $queryParams['trade_rewards'] = true;
            $tradeRewards = true;
            $this->_url->addQueryParams($queryParams);
        }
        $defaultUrl = $this->_url->getUrl('*/*/register');
        if (!$this->getRequest()->isPost() || !$this->formKeyValidator->validate($this->getRequest())) {
            $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
            return $resultRedirect;
        }

        $verified = false;
        $customerData = $this->session->getCustomData();
        if (isset($post['verification_code'])) {
            $verified = $this->customerHelper->verifyCode($post['verification_code']);
            if (!$verified) {
                $post['verified'] = false;
                $this->messageManager->addNoticeMessage(__('verification code match failed, please try again'));
                $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
                $this->session->setCustomerFormData($post);
                return $resultRedirect;
            }
        }

        try {
            $validator = new \Laminas\Validator\EmailAddress();
            $validEmpty = new \Laminas\Validator\NotEmpty();
            if (!$validator->isValid(trim($post['email']),\Magento\Framework\Validator\EmailAddress::class)) {
                throw new InputException(__('Invalid email address'));
            }

            if (!$validEmpty->isValid(trim($post['email']))) {
                throw new InputException(__('Email field is required'));
            }

            if ($tradeRewards) {
                if ((!isset($post['abn']) || empty($post['abn']) )) {
                    throw new InputException(__('Abn Number is required'));
                }
                $post['abn'] = (int) $post['abn'];
                if( strlen($post['abn']) != 11 ) {
                    throw new InputException(__('Abn Number must be of 11 digits'));
                }
            }
            if (empty($customerData)) {
                if (!isset($post['termsandcondition'])) {
                    throw new InputException(__('You must agree to the Terms and Condition'));
                }
                $customer = $this->registerProntoUser->createProntoUser($this->_request);
                if (is_array($customer)) {
                    $resultRedirect->setPath('*/*/register');
                    return $resultRedirect;
                }
            } elseif (isset($customerData['param'])) {
                if ($customerData['param'] == 'email') {
                    $redirectBack = false;
                    if (is_array($customerData) && $customerData['status'] == 'missing-info') {
                        if (isset($customerData['missing-data']) && $customerData['missing-data'] == 'intro') {
                            if (isset($post['first_name']) && isset($post['last_name']) && isset($post['mismatch'])) {
                                $customerData['FirstName'] = $post['first_name'];
                                $customerData['LastName'] = $post['last_name'];
                            } else {
                                $redirectBack = true;
                                $post['intro'] = false;
                                $post['mismatch'] = true;
                                $post['mismatch_mobile'] = false;
                                $this->messageManager->addNoticeMessage(__('All fields are is required'));
                            }
                        } elseif (isset($customerData['missing-data']) && $customerData['missing-data'] == 'mobile' && isset($post['mismatch'])) {

                        } else {
                            $post['intro'] = true;
                            $post['mismatch'] = true;
                            $post['mismatch_mobile'] = true;
                            $redirectBack = true;
                        }
                    }
                    if (!$redirectBack) {
                        $this->session->setCustomData();
                        $this->session->setCustomerFormData();
                        $customer = $this->registerProntoUser->createCustomer($customerData, $this->_request);
                    } else {
                        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
                        $this->session->setCustomData($customerData);
                        $this->session->setCustomerFormData($post);
                        $resultRedirect->setUrl($this->_redirect->getRefererUrl());
                        return $resultRedirect;
                    }
                } elseif ($customerData['param'] == 'mobile') {
                    if (isset($post['first_name']) && isset($post['last_name']) && isset($post['mismatch'])) {
                        $customerData['FirstName'] = $post['first_name'];
                        $customerData['LastName'] = $post['last_name'];
                        $post['intro'] = true;
                        $post['mismatch'] = false; //mismatch check checked
                        $post['verified'] = false;
                        $this->customerHelper->sendVerificationCode($customerData['Mobile']);
                        $this->session->setCustomData($customerData);
                        $this->session->setCustomerFormData($post);
                        $resultRedirect->setPath('*/*/register', ['params' => $post]);
                        return $resultRedirect;
                    } elseif (isset($post['verification_code']) && $verified) {
                        $sessionData = $this->session->getCustomerFormData() ?? [];
                        $formData['mismatch'] = array_key_exists('mismatch', $sessionData) ? $sessionData['mismatch'] : true;
                        if ($formData['mismatch'] && !isset($post['mismatch'])) {
                            $post['intro'] = true;
                            $post['mismatch'] = true;
                            $post['verified'] = false;
                            $this->session->setCustomData($customerData);
                            $this->session->setCustomerFormData($post);
                            $this->messageManager->addNoticeMessage(__('Please agree to update data terms'));
                            $resultRedirect->setPath('*/*/register', ['params' => $post]);
                            return $resultRedirect;
                        }
                        $customerData['status'] = 'verified';
                        $this->session->setCustomData();
                        $this->session->setCustomerFormData();
                        $customer = $this->registerProntoUser->createCustomer($customerData, $this->_request);
                    } else {
                        $post['verified'] = false;
                        $this->customerHelper->sendVerificationCode($customerData['Mobile']);
                        $this->session->setCustomData($customerData);
                        $this->session->setCustomerFormData($post);
                        $resultRedirect->setPath('*/*/register', ['params' => $post]);
                        return $resultRedirect;
                    }
                }
            }
            if (isset($customer) && !is_bool($customer) && $customer->getId()) {
                $defaultUrl = $this->urlModel->getUrl('*/*/login', ['_secure' => true]);
                $this->messageManager->addSuccessMessage(__('User registration successful. Please check your email inbox for login details.'));
            } else {
                $this->messageManager->addErrorMessage(__('Sorry new account creation failed'));
            }
        } catch (StateException $e) {

            $this->messageManager->addError($e->getMessage());
        } catch (InputException $e) {
            $this->messageManager->addError($e->getMessage());
            foreach ($e->getErrors() as $error) {
                $this->messageManager->addError($error->getMessage());
            }
        } catch (LocalizedException $e) {
            $this->messageManager->addError($e->getMessage());
        } catch (\Exception $e) {
            $this->messageManager->addExceptionMessage($e, __($e->getMessage()));
            $defaultUrl = $this->_url->getUrl('*/*/create', ['_secure' => true]);
        }
        $this->session->setCustomData();
        $this->session->setCustomerFormData();
        $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
        return $resultRedirect;
    }

}
