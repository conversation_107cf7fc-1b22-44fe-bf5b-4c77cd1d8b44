<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
namespace Totaltools\Customer\Controller\Account;

use Magento\Customer\Model\Registration;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;
use Totaltools\Customer\Helper\Data;

/**
 * RegisterPost controller
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
class Resend extends Action
{
    /**
     * PageFactory
     *
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Registration
     *
     * @var Registration
     */
    protected $registration;

    /**
     * Registration
     *
     * @var Data
     */
    protected $customerHelper;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;

    /**
     * Register constructor.
     *
     * @param Context      $context           Context
     * @param Session      $customerSession   Session
     * @param Registration $registration      Registration
     * @param UrlFactory   $urlFactory        UrlFactory
     * @param Data         $customerHelper    Data
     * @param PageFactory  $resultPageFactory PageFactory
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        Registration $registration,
        UrlFactory $urlFactory,
        Data $customerHelper,
        PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->session = $customerSession;
        $this->registration = $registration;
        $this->customerHelper = $customerHelper;
        $this->urlModel = $urlFactory->create();
        parent::__construct($context);
    }

    /**
     * Execute
     *
     * @return ResponseInterface|ResultInterface|Page
     * @throws LocalizedException
     */
    public function execute()
    {
        /**
         * Redirect
         *
         * @var Redirect $resultRedirect
         */
        $resultRedirect = $this->resultRedirectFactory->create();
        $number = $this->getRequest()->getParam('num');
        if (empty($number)) {
            $number = $this->customerHelper->getCustomerMobile();
        }
        $this->customerHelper->sendVerificationCode($number);
        $defaultUrl = $this->_redirect->getRefererUrl();
        $resultRedirect->setUrl($this->_redirect->success($defaultUrl));
        return $resultRedirect;
    }
}