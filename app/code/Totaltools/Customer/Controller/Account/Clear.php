<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
namespace Totaltools\Customer\Controller\Account;

use Magento\Customer\Model\Registration;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;

/**
 * Clear form values in current session
 */
class Clear extends Action
{
    /**
     * PageFactory
     *
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Registration
     *
     * @var Registration
     */
    protected $registration;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;

    /**
     * Register constructor.
     *
     * @param Context      $context           context
     * @param Session      $customerSession   customerSession
     * @param Registration $registration      registration
     * @param UrlFactory   $urlFactory        urlFactory
     * @param PageFactory  $resultPageFactory resultPageFactory
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        Registration $registration,
        UrlFactory $urlFactory,
        PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->session = $customerSession;
        $this->registration = $registration;
        $this->urlModel = $urlFactory->create();
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|ResultInterface|Page
     */
    public function execute()
    {
        /**
         * Result Redirect
         *
         * @var Redirect $resultRedirect
         */
        $resultRedirect = $this->resultRedirectFactory->create();
        if ($this->session->isLoggedIn() || !$this->registration->isAllowed()) {
            $resultRedirect->setPath('*/*/');
            return $resultRedirect;
        }
        $this->session->setCustomData();
        $this->session->setCustomerFormData();
        $this->session->setLastVerificationSmsTime(null);
        $this->messageManager->addNoticeMessage(__('Session data cleared'));
        $defaultUrl = $this->urlModel->getUrl('*/*/register', ['_secure' => true]);
        $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
        return $resultRedirect;
    }
}