<?php
/**
 *
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Customer\Controller\Account;

use Exception;
use Magento\Customer\Model\AuthenticationInterface;
use Magento\Customer\Model\EmailNotificationInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\InvalidEmailOrPasswordException;
use Magento\Framework\Exception\State\UserLockedException;
use Totaltools\Customer\Helper\Data;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;

/**
 * Class EditPost
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class EditPostRewrite extends \Magento\Customer\Controller\Account\EditPost
{
    /** @var EmailNotificationInterface */
    private $emailNotification;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var AuthenticationInterface
     */
    private $authentication;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    private $customerFactory;

    /**
     * @var \Magento\Customer\Api\Data\AddressInterfaceFactory
     */
    protected $addressDataFactory;

    /**
     * @var \Magento\Framework\Api\DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @var \Magento\Customer\Model\Metadata\FormFactory
     */
    protected $formFactory;

    /**
     * @var \Magento\Customer\Api\Data\RegionInterfaceFactory
     */
    protected $regionDataFactory;

    /**
     * @var \Magento\Newsletter\Model\SubscriberFactory
     */
    protected $subscriberFactory;

    private $customerMapper;
    /**
     * @var Data
     */
    private $customerHelper;

     /**
      * @var \Magento\Customer\Api\AddressRepositoryInterface
      */
    protected $addressRepository;


    /**
     * EditPostRewrite constructor.
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Customer\Api\AccountManagementInterface $customerAccountManagement
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Framework\Data\Form\FormKey\Validator $formKeyValidator
     * @param \Magento\Customer\Model\CustomerExtractor $customerExtractor
     * @param \Magento\Customer\Api\Data\AddressInterfaceFactory $addressDataFactory
     * @param \Magento\Framework\Api\DataObjectHelper $dataObjectHelper
     * @param \Magento\Customer\Model\Metadata\FormFactory $formFactory
     * @param \Magento\Customer\Api\Data\RegionInterfaceFactory $regionDataFactory
     * @param \Magento\Newsletter\Model\SubscriberFactory $subscriberFactory
     * @param Data $helper
     * @param \Magento\Customer\Model\Customer\Mapper $customerMapper
     * @param \Magento\Customer\Api\AddressRepositoryInterface $addressRepository
     */
    public function __construct(
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Framework\App\Action\Context $context,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Customer\Api\AccountManagementInterface $customerAccountManagement,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Framework\Data\Form\FormKey\Validator $formKeyValidator,
        \Magento\Customer\Model\CustomerExtractor $customerExtractor,
        \Magento\Customer\Api\Data\AddressInterfaceFactory $addressDataFactory,
        \Magento\Framework\Api\DataObjectHelper $dataObjectHelper,
        \Magento\Customer\Model\Metadata\FormFactory $formFactory,
        \Magento\Customer\Api\Data\RegionInterfaceFactory $regionDataFactory,
        \Magento\Newsletter\Model\SubscriberFactory $subscriberFactory,
        Data $helper,
        \Magento\Customer\Model\Customer\Mapper $customerMapper,
        \Magento\Customer\Api\AddressRepositoryInterface $addressRepository
    ) {
        parent::__construct($context, $customerSession, $customerAccountManagement, $customerRepository,
            $formKeyValidator, $customerExtractor);
        $this->customerFactory = $customerFactory;
        $this->addressDataFactory = $addressDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->formFactory = $formFactory;
        $this->regionDataFactory = $regionDataFactory;
        $this->subscriberFactory = $subscriberFactory;
        $this->customerHelper = $helper;
        $this->customerMapper = $customerMapper;
        $this->addressRepository = $addressRepository;
    }

    /**
     * Change customer email or password action
     *
     * @return \Magento\Framework\Controller\Result\Redirect
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\SessionException
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $validFormKey = $this->formKeyValidator->validate($this->getRequest());
        $isEmailChanged = false;
        if ($validFormKey && $this->getRequest()->isPost()) {
            $currentCustomerDataObject = $this->getCustomerDataObject($this->session->getCustomerId());
            $customerCandidateDataObject = $this->populateNewCustomerDataObject(
                $this->_request,
                $currentCustomerDataObject
            );

            try {
                $postData = [
                    'email' => $this->session->getCustomerData()->getEmail(),
                    'mobile_number' => $this->getRequest()->getParam('mobile_number')
                ];
                // Unique mobile check
                $this->customerHelper->uniqueMobileCheck($postData, $this->session->getCustomerId());
                $customerObject = $this->customerFactory->create()->load($this->session->getCustomerId());
                if ($customerObject->getPasswordHash() != '' && $this->getRequest()->getPost('current_password') &&
                    $this->getRequest()->getParam('change_email')) {
                    // whether a customer enabled change email option
                    $isEmailChanged = $this->processChangeEmailRequest($currentCustomerDataObject);
                }

                // whether a customer enabled change password option
                $isPasswordChanged = $this->changeCustomerPassword($currentCustomerDataObject->getEmail());
                $this->customerRepository->save($customerCandidateDataObject);
                $this->processSubscription($customerCandidateDataObject->getId());
                $this->getEmailNotification()->credentialsChanged(
                    $customerCandidateDataObject,
                    $currentCustomerDataObject->getEmail(),
                    $isPasswordChanged
                );
                $this->dispatchSuccessEvent($customerCandidateDataObject);
                $this->messageManager->addSuccessMessage(__('You saved the account information.'));
                // logout from current session if password or email changed.
                if ($isPasswordChanged || $isEmailChanged) {
                    $this->session->logout();
                    $this->session->start();
                    return $resultRedirect->setPath('customer/account/login');
                }
                return $resultRedirect->setPath('customer/account');
            } catch (InvalidEmailOrPasswordException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (UserLockedException $e) {
                $message = __(
                    'The account is locked. Please wait and try again or contact %1.',
                    $this->getScopeConfig()->getValue('contact/email/recipient_email')
                );
                $this->session->logout();
                $this->session->start();
                $this->messageManager->addError($message);
                return $resultRedirect->setPath('customer/account/login');
            } catch (InputException $e) {
                $this->messageManager->addError($e->getMessage());
                foreach ($e->getErrors() as $error) {
                    $this->messageManager->addError($error->getMessage());
                }
            } catch (\Magento\Framework\Exception\LocalizedException $e) {
                $this->messageManager->addErrorMessage($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addExceptionMessage($e, __('We can\'t save the customer.'));
            }

            $this->session->setCustomerFormData($this->getRequest()->getPostValue());
        }

        return $resultRedirect->setPath('*/*/edit');
    }

    /**
     * @param $customerId
     */
    protected function processSubscription($customerId)
    {
        $isSubscribed = (bool)$this->getRequest()->getParam('is_subscribed');
        /** @var \Magento\Newsletter\Model\Subscriber $subscriber */
        $subscriber = $this->subscriberFactory->create();

        if ($isSubscribed) {
            $subscriber->subscribeCustomerById($customerId);
        } else {
            $subscriber->unsubscribeCustomerById($customerId);
        }
    }

    /**
     * Process change email request
     *
     * @param \Magento\Customer\Api\Data\CustomerInterface $currentCustomerDataObject
     *
     * @return void
     * @throws InvalidEmailOrPasswordException
     * @throws UserLockedException
     */
    private function processChangeEmailRequest(\Magento\Customer\Api\Data\CustomerInterface $currentCustomerDataObject)
    {
        if ($this->getRequest()->getParam('change_email')) {

            // authenticate user for changing email
            try {
                $this->getAuthentication()->authenticate(
                    $currentCustomerDataObject->getId(),
                    $this->getRequest()->getPost('current_password')
                );
                return true;
            } catch (InvalidEmailOrPasswordException $e) {
                throw new InvalidEmailOrPasswordException(__('The password doesn\'t match this account.'));
            }
        }
        return false;
    }

    /**
     * Get authentication
     *
     * @return AuthenticationInterface
     */
    private function getAuthentication()
    {

        if (!($this->authentication instanceof AuthenticationInterface)) {
            return \Magento\Framework\App\ObjectManager::getInstance()->get(
                \Magento\Customer\Model\AuthenticationInterface::class
            );
        } else {
            return $this->authentication;
        }
    }

    /**
     * Get email notification
     *
     * @return EmailNotificationInterface
     * @deprecated
     */
    private function getEmailNotification()
    {
        if (!($this->emailNotification instanceof EmailNotificationInterface)) {
            return \Magento\Framework\App\ObjectManager::getInstance()->get(
                \Magento\Customer\Model\EmailNotificationInterface::class
            );
        } else {
            return $this->emailNotification;
        }
    }


    /**
     * Get scope config
     *
     * @return ScopeConfigInterface
     */
    private function getScopeConfig()
    {
        if (!($this->scopeConfig instanceof \Magento\Framework\App\Config\ScopeConfigInterface)) {
            return \Magento\Framework\App\ObjectManager::getInstance()->get(
                \Magento\Framework\App\Config\ScopeConfigInterface::class
            );
        } else {
            return $this->scopeConfig;
        }
    }

    /**
     * Account editing action completed successfully event
     *
     * @param \Magento\Customer\Api\Data\CustomerInterface $customerCandidateDataObject
     * @return void
     */
    private function dispatchSuccessEvent(\Magento\Customer\Api\Data\CustomerInterface $customerCandidateDataObject)
    {
        $this->_eventManager->dispatch(
            'customer_account_edited',
            ['email' => $customerCandidateDataObject->getEmail()]
        );
    }

    /**
     * Get customer data object
     *
     * @param int $customerId
     *
     * @return \Magento\Customer\Api\Data\CustomerInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function getCustomerDataObject($customerId)
    {
        return $this->customerRepository->getById($customerId);
    }

    /**
     * Create Data Transfer Object of customer candidate
     *
     * @param \Magento\Framework\App\RequestInterface $inputData
     * @param \Magento\Customer\Api\Data\CustomerInterface $currentCustomerData
     * @return \Magento\Customer\Api\Data\CustomerInterface
     */
    private function populateNewCustomerDataObject(
        \Magento\Framework\App\RequestInterface $inputData,
        \Magento\Customer\Api\Data\CustomerInterface $currentCustomerData
    ) {
        $attributeValues = $this->customerMapper->toFlatArray($currentCustomerData);
        $attributeValues['role'] = $this->getRequest()->getParam('role');
        $attributeValues['pronto_position'] = $this->getRequest()->getParam('pronto_position');
        $attributeValues['preferred_store'] = $this->getRequest()->getParam('preferred_store');
        $attributeValues['is_subscribed'] = $this->getRequest()->getParam('is_subscribed');
        $customerDto = $this->customerExtractor->extract(self::FORM_DATA_EXTRACTOR_CODE, $inputData, $attributeValues);

        $emailVerificationStatusAttribute = $currentCustomerData->getCustomAttribute('email_verification_status');

        $emailVerificationStatusAttributeValue = $emailVerificationStatusAttribute && $emailVerificationStatusAttribute->getValue() ?
                $emailVerificationStatusAttribute->getValue() : EmailVerification::PENDING_SYNC;

        $customerDto->setCustomAttribute('email_verification_status', $emailVerificationStatusAttributeValue);
        $customerDto->setId($currentCustomerData->getId());
        $addressFromRequest = $this->extractAddress();

        if (!$customerDto->getAddresses()) {
            $customerDto->setAddresses($currentCustomerData->getAddresses());
        }

        $isNewAddress = false;
        $currentAddress = null;
        $defaultAddresId = $currentCustomerData->getDefaultBilling();
        if ($addressFromRequest) {
            foreach ($customerDto->getAddresses() as $index => $address) {
                    if($defaultAddresId == $address->getId()) {
                        $currentAddress = $address;
                    }
            }
           
        }

        $customerAddresses = array_merge($customerDto->getAddresses(), [$addressFromRequest]);

        if (!$customerDto->getAddresses()) {
            $customerDto->setAddresses(array_filter($customerAddresses));
        } elseif($currentAddress) {
                    try {
                        $currentAddress->setStreet($addressFromRequest->getStreet());
                        $currentAddress->setTelephone($addressFromRequest->getTelephone());
                        $currentAddress->setPostcode($addressFromRequest->getPostcode());
                        $currentAddress->setCity($addressFromRequest->getCity());
                        $currentAddress->setRegionId($addressFromRequest->getRegionId());
                        $currentAddress->setRegion($addressFromRequest->getRegion());
                        $currentAddress->setCountryId($addressFromRequest->getCountryId());
                        $this->addressRepository->save($currentAddress);
                    } catch(Exception $e) {
                        $this->messageManager->addErrorMessage($e->getMessage());
                    }
        }
        
        if (!$inputData->getParam('change_email')) {
            $customerDto->setEmail($currentCustomerData->getEmail());
        }

        return $customerDto;
    }

    /**
     * @return \Magento\Customer\Api\Data\AddressInterface|null
     */
    protected function extractAddress()
    {
        $addressForm = $this->formFactory->create('customer_address', 'customer_register_address');
        $allowedAttributes = $addressForm->getAllowedAttributes();

        $addressData = [];

        $regionDataObject = $this->regionDataFactory->create();
        foreach ($allowedAttributes as $attribute) {
            $attributeCode = $attribute->getAttributeCode();
            $value = $this->getRequest()->getParam($attributeCode);
            if ($value === null) {
                continue;
            }
            switch ($attributeCode) {
                case 'region_id':
                    $regionDataObject->setRegionId($value);
                    break;
                case 'region':
                    $regionDataObject->setRegion($value);
                    break;
                default:
                    $addressData[$attributeCode] = $value;
            }
        }
        $addressDataObject = $this->addressDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $addressDataObject,
            $addressData,
            \Magento\Customer\Api\Data\AddressInterface::class
        );
        $addressDataObject->setRegion($regionDataObject);

        $addressDataObject->setIsDefaultBilling(
            $this->getRequest()->getParam('default_billing', false)
        );

        return $addressDataObject;
    }
}
