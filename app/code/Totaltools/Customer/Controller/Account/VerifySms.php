<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Controller\Account;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Api\Data\CustomerInterfaceFactory;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;
use Totaltools\Customer\Helper\Data;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;

/**
 * VerifySms
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Ahmed <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
class VerifySms extends Action
{
    /**
     * PageFactory
     *
     * @var PageFactory
     */
    protected $resultPageFactory;
    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Data
     *
     * @var Data
     */
    protected $customerHelper;

    /**
     * UrlInterface
     *
     * @var UrlInterface
     */
    protected $urlModel;

    /**
     * CustomerRepositoryInterface
     *
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * CustomerInterfaceFactory
     *
     * @var CustomerInterfaceFactory
     */
    protected $customerFactory;

    /**
     * CustomerInterface
     *
     * @var Magento\Customer\Api\Data\CustomerInterface
     */
    protected $customerInterface;

    /**
     * Register constructor.
     *
     * @param Context                     $context            Context
     * @param Session                     $customerSession    Session
     * @param UrlFactory                  $urlFactory         UrlFactory
     * @param Data                        $customerHelper     Data
     * @param CustomerInterfaceFactory    $customerFactory    CustomerInterfaceFactory
     * @param CustomerRepositoryInterface $customerRepository CustomerRepositoryInterface
     * @param CustomerInterface           $customerInterface  CustomerInterface
     * @param PageFactory                 $resultPageFactory  PageFactory
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        UrlFactory $urlFactory,
        Data $customerHelper,
        CustomerInterfaceFactory $customerFactory,
        CustomerRepositoryInterface $customerRepository,
        CustomerInterface $customerInterface,
        PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->session = $customerSession;
        $this->customerHelper = $customerHelper;
        $this->customerInterface = $customerInterface;
        $this->customerRepository = $customerRepository;
        $this->customerFactory = $customerFactory;
        $this->urlModel = $urlFactory->create();
        parent::__construct($context);
    }

    /**
     * Execute
     *
     * @return ResponseInterface|ResultInterface|Page
     * @throws LocalizedException
     */
    public function execute()
    {
        /**
         * Redirect
         *
         * @var Redirect $resultRedirect
        */
        $resultRedirect = $this->resultRedirectFactory->create();
        if (!$this->session->isLoggedIn()) {
            $resultRedirect->setPath('*/*/');
            return $resultRedirect;
        }
        $post  = $this->_request->getParams();
        if (!empty($post['verification_code'])) {
            $verified = $this->customerHelper->verifyCode($post['verification_code']);
            if ($verified) {
                $customer = $this->session->getCustomerData();
                $customer->setCustomAttribute('email_verification_status', EmailVerification::VERIFIED)
                    ->setCustomAttribute('unverified_loyalty_id', null);
                $this->customerRepository->save($customer);
                $this->messageManager->addNoticeMessage(__('Mobile Number verified'));
            } else {
                $this->messageManager->addNoticeMessage(__('Mobile Number verification failed'));
            }
        }
        $defaultUrl = $this->urlModel->getUrl('*/*/', ['_secure' => true]);
        $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
        return $resultRedirect;
    }
}