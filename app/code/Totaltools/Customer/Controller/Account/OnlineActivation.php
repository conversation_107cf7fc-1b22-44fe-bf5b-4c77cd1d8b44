<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * Copyright © Magento, Inc. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Controller\Account;

use Magento\Customer\Model\Registration;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Escaper;
use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\StateException;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Magento\Framework\View\Result\PageFactory;
use Totaltools\Customer\Helper\Data;
use Totaltools\Customer\Model\RegisterProntoUser;

class OnlineActivation extends Action
{
    /**
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var RegisterProntoUser
     */
    protected $registerProntoUser;

    /**
     * @var mixed
     */
    protected $formKeyValidator;

    /**
     * @var UrlInterface
     */
    protected $urlModel;

    /**
     * @var Session
     */
    protected $session;

    /**
     * @var Registration
     */
    protected $registration;

    /**
     * @var Escaper
     */
    protected $escaper;

    /**
     * @var Data
     */
    protected $customerHelper;

    /**
     * Register constructor.
     * @param Context $context
     * @param Validator|null $formKeyValidator
     * @param UrlFactory $urlFactory
     * @param Session $customerSession
     * @param Registration $registration
     * @param PageFactory $resultPageFactory
     * @param RegisterProntoUser $registerProntoUser
     * @param Escaper $escaper
     * @param Data $customerHelper
     */
    public function __construct(
        Context $context,
        Validator $formKeyValidator = null,
        UrlFactory $urlFactory,
        Session $customerSession,
        Registration $registration,
        PageFactory $resultPageFactory,
        RegisterProntoUser $registerProntoUser,
        Escaper $escaper,
        Data $customerHelper
    ) {
        $this->urlModel = $urlFactory->create();
        $this->registerProntoUser = $registerProntoUser;
        $this->resultPageFactory = $resultPageFactory;
        $this->session = $customerSession;
        $this->registration = $registration;
        $this->escaper = $escaper;
        $this->customerHelper = $customerHelper;
        $this->formKeyValidator = $formKeyValidator ?: ObjectManager::getInstance()->get(Validator::class);
        parent::__construct($context);
    }

    /**
     * @return ResponseInterface|Redirect|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        if ($this->session->isLoggedIn() || !$this->registration->isAllowed()) {
            $resultRedirect->setPath('*/*/');
            return $resultRedirect;
        }
        $post = $this->getRequest()->getParams();
        $email = $this->getRequest()->getParam('email') ?? '';
        $mobile = $this->getRequest()->getParam('mobile_number') ?? '';
        $notValid = false;
        $validator = new \Laminas\Validator\EmailAddress();
        $this->_request->setParams(['email' => $email, 'mobile_number' => $mobile]);
        $defaultUrl = $this->urlModel->getUrl('*/*/register', ['_secure' => true]);
        $validEmpty = new \Laminas\Validator\NotEmpty();
        try {
            if ( !$validEmpty->isValid(trim($email)) || !$validator->isValid($email)) {
                $this->messageManager->addNoticeMessage(__('Please provide a valid email address'));
                $notValid = true;
            }
            if ($notValid) {
                $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
                $this->session->setCustomerFormData($post);
                return $resultRedirect;
            }
            $customer = $this->registerProntoUser->createProntoUser($this->_request);
            if (is_array($customer)) {
                $defaultUrl = $this->urlModel->getUrl('*/*/register', ['_secure' => true]);
                $resultRedirect->setPath('*/*/register');
                return $resultRedirect;
            }
            if (isset($customer) && !is_bool($customer) && $customer->getId()) {
                $defaultUrl = $this->urlModel->getUrl('*/*/login', ['_secure' => true]);
                $this->messageManager->addSuccessMessage(__('User registration successful. Please check your email inbox for login details.'));
            } else {
                $this->messageManager->addErrorMessage(__('Sorry new account creation failed'));
            }
        } catch (StateException $e) {
            $defaultUrl = $this->urlModel->getUrl('*/*/register', ['_secure' => true]);
            $this->messageManager->addError($e->getMessage());
        } catch (InputException $e) {
            $this->messageManager->addErrorMessage($this->escaper->escapeHtml($e->getMessage()));
            foreach ($e->getErrors() as $error) {
                $this->messageManager->addErrorMessage($this->escaper->escapeHtml($error->getMessage()));
            }
            $defaultUrl = $this->urlModel->getUrl('*/*/create', ['_secure' => true]);
        } catch (LocalizedException $e) {
            $this->messageManager->addErrorMessage($this->escaper->escapeHtml($e->getMessage()));
        } catch (\Exception $e) {
            $this->messageManager->addExceptionMessage($e, __($e->getMessage()));
            $defaultUrl = $this->urlModel->getUrl('*/*/create', ['_secure' => true]);
        }
        $this->session->setCustomData();
        $this->session->setCustomerFormData();
        $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
        return $resultRedirect;
    }
}
