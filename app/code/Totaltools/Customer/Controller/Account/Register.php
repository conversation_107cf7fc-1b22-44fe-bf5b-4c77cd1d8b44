<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Controller\Account;

use Magento\Customer\Model\Registration;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\ResponseInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\Page;
use Magento\Framework\View\Result\PageFactory;

/**
 * Register form
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
class Register extends Action
{
    const INSIDER_REWARDS_MEMBER_TO_TRADE_REWARDS = 'INSIDER REWARDS MEMBER SIGN UP TO TRADE REWARDS';
    const INSIDER_REWARDS_MEMBER_ONLINE_REGISTRATION = 'INSIDER REWARDS MEMBER ONLINE REGISTRATION';   
    /**
     * PageFactory
     *
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Registration
     *
     * @var Registration
     */
    protected $registration;

    /**
     * resultPage
     *
     * @var PageFactory
     */
    protected $resultPage;

    /**
     * Register constructor.
     *
     * @param Context      $context           Context
     * @param Session      $customerSession   Session
     * @param Registration $registration      Registration
     * @param PageFactory  $resultPageFactory PageFactory
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        Registration $registration,
        PageFactory $resultPageFactory
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->session = $customerSession;
        $this->registration = $registration;
        parent::__construct($context);
    }

    /**
     * Controller Execute method
     *
     * @return ResponseInterface|ResultInterface|Page
     */
    public function execute()
    {
        /**
         * Redirect
         *
         * @var Redirect $resultRedirect
        */
        $resultRedirect = $this->resultRedirectFactory->create();
        if ($this->session->isLoggedIn() || !$this->registration->isAllowed()) {
            $resultRedirect->setPath('*/*/');
            return $resultRedirect;
        }
        $title = self::INSIDER_REWARDS_MEMBER_ONLINE_REGISTRATION ;
        if($this->getRequest()->getParam('trade_rewards') && $this->getRequest()->getParam('trade_rewards') == true) {
            $title = self::INSIDER_REWARDS_MEMBER_TO_TRADE_REWARDS ;
        }
        $this->resultPage = $this->resultPageFactory->create(); 
		$this->resultPage->getConfig()->getTitle()->set((__($title)));
        return $this->resultPage;
    }
}