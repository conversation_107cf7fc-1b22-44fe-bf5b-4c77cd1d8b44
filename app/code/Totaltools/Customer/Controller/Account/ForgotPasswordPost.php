<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * Copyright © Magento, Inc. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Controller\Account;

use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Model\AccountManagement;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Escaper;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\SecurityViolationException;
use Magento\Framework\Exception\StateException;
use Magento\Framework\Phrase;
use Magento\Framework\UrlFactory;
use Magento\Framework\UrlInterface;
use Totaltools\Customer\Model\RegisterProntoUser;
use Psr\Log\LoggerInterface as Logger;

/**
 * ForgotPasswordPost controller
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class ForgotPasswordPost extends \Magento\Customer\Controller\AbstractAccount
{
    /**
     * @var \Magento\Customer\Api\AccountManagementInterface
     */
    protected $customerAccountManagement;

    /**
     * @var Escaper
     */
    protected $escaper;

    /**
     * @var Session
     */
    protected $session;

    /**
     * @var RegisterProntoUser
     */
    protected $registerProntoUser;

    /**
     * @var UrlInterface
     */
    protected $urlModel;

    /**
    * @var Logger
    */
    protected $logger;

    /**
     * @param Context $context
     * @param Session $customerSession
     * @param AccountManagementInterface $customerAccountManagement
     * @param RegisterProntoUser $registerProntoUser
     * @param UrlFactory $urlFactory
     * @param Escaper $escaper
     * @param Logger $logger
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        AccountManagementInterface $customerAccountManagement,
        RegisterProntoUser $registerProntoUser,
        UrlFactory $urlFactory,
        Escaper $escaper,
        Logger $logger
    ) {
        $this->session = $customerSession;
        $this->customerAccountManagement = $customerAccountManagement;
        $this->escaper = $escaper;
        $this->logger = $logger;
        $this->urlModel = $urlFactory->create();
        parent::__construct($context);
        $this->registerProntoUser = $registerProntoUser;
    }

    /**
     * Forgot customer password action
     *
     * @return Redirect
     * @throws LocalizedException
     * @throws StateException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    public function execute()
    {
        /** @var Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        $email = (string)$this->getRequest()->getPost('email');
        $validator = new \Laminas\Validator\EmailAddress();
        if ($email) {
            if (!$validator->isValid($email, \Magento\Framework\Validator\EmailAddress::class)) {
                $this->session->setForgottenEmail($email);
                $this->messageManager->addErrorMessage(__('Please correct the email address.'));
                return $resultRedirect->setPath('*/*/forgotpassword');
            }
            try {
                $this->customerAccountManagement->initiatePasswordReset(
                    $email,
                    AccountManagement::EMAIL_RESET
                );
            } catch (NoSuchEntityException $exception) {
                $post = ['email' => $email, 'mobile_number' => ''];
                $this->_request->setParams($post);
                $customer = $this->registerProntoUser->createUser($this->_request, true);
                $defaultUrl = $this->urlModel->getUrl('*/*/login', ['_secure' => true]);
                $resultRedirect->setUrl($this->_redirect->error($defaultUrl));
                if (isset($customer) && !is_bool($customer) && !is_array($customer) && $customer->getId()) {
                    $this->messageManager->addSuccessMessage(__('User registration successful. Please check your email inbox for login details.'));
                } else {
                    $this->messageManager->addSuccessMessage($this->getSuccessMessage($email));
                }
                return $resultRedirect;
            } catch (SecurityViolationException $exception) {
                $this->messageManager->addErrorMessage($exception->getMessage());
                return $resultRedirect->setPath('*/*/forgotpassword');
            } catch (\Exception $exception) {
                $this->logger->error('Password reset error: ' . $exception->getMessage());
                $this->messageManager->addExceptionMessage(
                    $exception,
                    __('We\'re unable to send the password reset email.')
                );
                return $resultRedirect->setPath('*/*/forgotpassword');
            }
            $this->messageManager->addSuccessMessage($this->getSuccessMessage($email));
            return $resultRedirect->setPath('*/*/login');
        } else {
            $this->messageManager->addErrorMessage(__('Please enter your email.'));
            return $resultRedirect->setPath('*/*/forgotpassword');
        }
    }

    /**
     * Retrieve success message
     *
     * @param string $email
     * @return Phrase
     */
    protected function getSuccessMessage($email)
    {
        return __(
            'If there is an account associated with %1 you will receive an email with a link to reset your password.',
            $this->escaper->escapeHtml($email)
        );
    }
}
