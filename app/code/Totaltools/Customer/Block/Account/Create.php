<?php
/**
 * Author: <PERSON><PERSON> <<EMAIL>>
 *
 */

namespace Totaltools\Customer\Block\Account;

use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;

/**
 * Class Create
 *
 * @package Totaltools\Customer\Block\Account
 */
class Create extends \Magento\Framework\View\Element\Template
{
    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * RequestInterface
     *
     * @var RequestInterface
     */
    protected $request;

    /**
     * Constructor
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param Session $session
     * @param RequestInterface $request
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        Session $session,
        RequestInterface $request,
        array $data = []
    ) {
        $this->session = $session;
        $this->request = $request;
        parent::__construct($context, $data);
    }

    /**
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getSignUpTermsAndConditionText()
    {
        $text = '';
        try {
            if ($this->isCreditRequest()) {
                $text = $this->getLayout()
                    ->createBlock('Magento\Cms\Block\Block')
                    ->setBlockId('trade-rewards-terms-and-conditions')
                    ->toHtml();
            } else {
                $text = $this->getLayout()
                    ->createBlock('Magento\Cms\Block\Block')
                    ->setBlockId('terms_and_condition_signup_label')
                    ->toHtml();
            }
        } catch (\Exception $exception) {
            $this->_logger->error($exception->getMessage());
        }
        return $text;
    }

    public function isCreditRequest() 
    {
        $params = $this->getParams();
        if (isset($params['trade_rewards']) ) {
            return true;
        }
        return false;
    }
    
    /**
     * Get unlock form text
     *
     * @return mixed
     */
    public function getUnlockedText()
    {
        $text = '';
        try {
            $text = $this->getLayout()
                ->createBlock('Magento\Cms\Block\Block')
                ->setBlockId('unlock_trade_account')
                ->toHtml();
        } catch (\Exception $exception) {
            $this->_logger->error($exception->getMessage());
        }
        return $text;

    }

    public function getParams()
    {
        return $this->request->getParams();
    }
}
