<?php

namespace Totaltools\Customer\Block\Account;

use Magento\Customer\Model\Session;


class Template extends \Magento\Framework\View\Element\Template
{
    /**
     * @var Session
     */
    private $_customerSession;

    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        Session $session,
        array $data = []
    ) {
        \Magento\Framework\View\Element\Template::__construct($context, $data);
        $this->_customerSession = $session;
    }

    /**
     * @return bool
     */
    public function isCustomerLoggedIn()
    {
        return $this->_customerSession->isLoggedIn();
    }

}
