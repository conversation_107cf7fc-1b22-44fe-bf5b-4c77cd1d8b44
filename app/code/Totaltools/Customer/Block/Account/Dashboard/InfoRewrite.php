<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Customer\Block\Account\Dashboard;

use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Dashboard Customer Info
 */
class InfoRewrite extends \Magento\Customer\Block\Account\Dashboard\Info
{
    const XML_REFERRAL_CODE = 'referral_code';

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    protected $customerFactory;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;

    /**
     * Constructor
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Customer\Helper\Session\CurrentCustomer $currentCustomer
     * @param \Magento\Newsletter\Model\SubscriberFactory $subscriberFactory
     * @param \Magento\Customer\Helper\View $helperView
     * @param array $data
     */
    public function __construct(
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Customer\Helper\Session\CurrentCustomer $currentCustomer,
        \Magento\Newsletter\Model\SubscriberFactory $subscriberFactory,
        \Magento\Customer\Helper\View $helperView,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Customer\Model\Session $session,
        array $data = []
    ) {
        parent::__construct($context, $currentCustomer, $subscriberFactory, $helperView, $data);
        $this->customerFactory = $customerFactory;
        $this->_storeRepository = $storeRepository;
        $this->customerSession = $session;

    }

    protected function _toHtml()
    {
        $this->setModuleName($this->extractModuleName('Magento\Customer\Block\Account\Dashboard\Info'));
        return parent::_toHtml();
    }

    /**
     * Return whether the form should be opened in an expanded mode showing the change password fields
     *
     * @return bool
     *
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    private function getFullCustomerObject()
    {
        if ($this->getCustomer()) {
            return $this->customerFactory->create()->load($this->currentCustomer->getCustomerId());
        }
        return false;
    }

    /**
     * check if customer account has password or not
     *
     * @return bool
     *
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function isNoPassword()
    {
        if ($this->getFullCustomerObject()->getPasswordHash() == '') {
            return true;
        }
        return false;
    }

    /**
     * @return string
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getPreferredStoreCity()
    {
        $attribute = $this->getCustomer()->getCustomAttribute('preferred_store');
        $storeCity = '';

        if (!$attribute || !$attribute->getValue()) {
            return $storeCity;
        }

        try {
            $store = $this->_storeRepository->getByErpId((int)$attribute->getValue());
            $storeCity = $store->getData('city');
        } catch (NoSuchEntityException $e) {}

        return $storeCity;
    }

    public function getReferralCode()
    {
       return $this->customerSession->getReferralCode();
    }
}
