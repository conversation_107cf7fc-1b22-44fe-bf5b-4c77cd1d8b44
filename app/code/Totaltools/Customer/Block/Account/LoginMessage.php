<?php

namespace Totaltools\Customer\Block\Account;

use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\View\Element\Template\Context;

/**
 * Class Register
 *
 * @package Totaltools\Customer\Block\Account
 */
class LoginMessage extends \Magento\Framework\View\Element\Template
{
    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * Constructor
     *
     * @param Context $context
     * @param Session $session
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param array   $data
     */
    public function __construct(
        Context $context,
        Session $session,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        array $data = []
    ) {
        $this->session = $session;
        $this->scopeConfig = $scopeConfig;
        parent::__construct($context, $data);
    }

    /**
     * @return mixed
     */
    public function isRedirectedFromCheckout()
    {
        if ($this->session->getIsRedirectedFromCheckout()) {
            return true;
        }

        return false;
    }

    /**
     * @return mixed
     */
    public function showLoginPageTextMessage()
    {
        return $this->scopeConfig->getValue('checkout/guest_checkout_login/text_meesage', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    
}

