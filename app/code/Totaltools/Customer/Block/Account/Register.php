<?php
/**
 * Total-Info Tech
 * php version 7.1.32
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */
namespace Totaltools\Customer\Block\Account;

use Magento\Customer\Model\Session;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\View\Element\Template\Context;

/**
 * Class Register
 *
 * @package Totaltools\Customer\Block\Account
 */
class Register extends \Magento\Framework\View\Element\Template
{
    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * Constructor
     *
     * @param Context $context
     * @param Session $session
     * @param array   $data
     */
    public function __construct(
        Context $context,
        Session $session,
        array $data = []
    ) {
        $this->session = $session;
        parent::__construct($context, $data);
    }

    /**
     * Get register form post action url
     *
     * @return string
     */
    public function getPostActionUrl()
    {
        return $this->getUrl("customer/account/registerpost");
    }

    /**
     * Get clear session url
     *
     * @return string
     */
    public function getClearActionUrl()
    {
        return $this->getUrl("customer/account/clear");
    }

    /**
     * Get Agreement text
     *
     * @return mixed
     */
    public function getAgreementText()
    {
        $text = '';
        try {
            $text = $text  . ' ' . $this->getLayout()
                ->createBlock('Magento\Cms\Block\Block')
                ->setBlockId('customer_data_modification_agreement')
                ->toHtml();
        } catch (\Exception $exception) {
            $this->_logger->error($exception->getMessage());
        }
        return $text;

    }

    /**
     * Get insider rewards privacy policy
     *
     * @return mixed
     */
    public function getInsiderRewardPrivacyPolicyText()
    {
        $text = '';
        try {
            $text = $text  . ' ' . $this->getLayout()
                ->createBlock('Magento\Cms\Block\Block')
                ->setBlockId('customer_edit_privacy_policy')
                ->toHtml();
        } catch (\Exception $exception) {
            $this->_logger->error($exception->getMessage());
        }
        return $text;
    }

    /**
     * Get terms and condition
     *
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getTermsAndConditionText()
    {
        $text = '';
        try {
            $text = $this->getLayout()
                ->createBlock('Magento\Cms\Block\Block')
                ->setBlockId('terms_and_condition_link_label')
                ->toHtml();
        } catch (\Exception $exception) {
            $this->_logger->error($exception->getMessage());
        }
        return $text;
    }

    /**
     * Get form session data
     *
     * @return mixed
     */
    public function getFormData()
    {
        $sessionData = $this->session->getCustomerFormData()??[];
        $formData['intro'] = array_key_exists('intro', $sessionData) ? $sessionData['intro']: true;
        $formData['verified'] = array_key_exists('verified', $sessionData) ? $sessionData['verified']: true;
        $formData['email'] = array_key_exists('email', $sessionData) ? $sessionData['email']: '';
        $formData['mobile_number'] = array_key_exists('mobile_number', $sessionData) ? $sessionData['mobile_number']: '';
        $formData['first_name'] = array_key_exists('first_name', $sessionData) ? $sessionData['first_name']: '';
        $formData['last_name'] = array_key_exists('last_name', $sessionData) ? $sessionData['last_name']: '';
        $formData['verification_code'] = array_key_exists('verification_code', $sessionData) ? $sessionData['verification_code']: '';
        $formData['mismatch'] = array_key_exists('mismatch', $sessionData) ? $sessionData['mismatch']: false;
        $formData['mismatch_mobile'] = array_key_exists('mismatch_mobile', $sessionData) ? $sessionData['mismatch_mobile']: false;
        $formData['abn'] = array_key_exists('abn', $sessionData) ? $sessionData['abn']: false;
        $formData['company'] = array_key_exists('company', $sessionData) ? $sessionData['company']: false;
        return $formData;
    }
}

