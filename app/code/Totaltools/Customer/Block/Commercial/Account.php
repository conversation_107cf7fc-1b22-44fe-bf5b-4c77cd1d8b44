<?php
namespace Totaltools\Customer\Block\Commercial;

class Account extends \Magento\Framework\View\Element\Template
{
    /**
     * Block constructor.
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param array                                            $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }
	protected function _prepareLayout(){
		
		if ($breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb(
                'home',
                [
                    'label' => __('Home'),
                    'title' => __('Go to Home Page'),
                    'link' => $this->_storeManager->getStore()->getBaseUrl()
                ]
            );
   
			$breadcrumbsBlock->addCrumb(
                'storelocations',
                [
                    'label' => __('Store commercial account request'),
                    'title' => __('Store commercial account request'),
                    'link' => $this->getUrl('customer/commercial/account')
                ]
            );
   
            
        }

        return parent::_prepareLayout();
    }

    public function getPostUrl() 
    {
        return $this->getUrl('customer/commercial/accountpost');
    }
    
}