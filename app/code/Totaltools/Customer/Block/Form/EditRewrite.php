<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Customer\Block\Form;

use Magento\Customer\Api\AccountManagementInterface;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\AddressInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Customer\Model\Session;
use Magento\Directory\Block\Data;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\View\Element\Template\Context;
use Magento\Newsletter\Model\SubscriberFactory;
use Totaltools\Customer\Helper\AttributeData;

/**
 * Customer edit form block
 *
 * @SuppressWarnings(PHPMD.DepthOfInheritance)
 */
class EditRewrite extends \Magento\Customer\Block\Form\Edit
{
    /**
     * @var CustomerFactory
     */
    protected $customerFactory;

    /**
     * \Magento\Customer\Api\Data\CustomerInterface
     */
    protected $customer;

    /**
     * @var AddressInterface
     */
    protected $address;

    /**
     * @var Data
     */
    protected $dataBlock;

    /**
     * @var AttributeData
     */
    protected $attributeHelper;

    /**
     * EditRewrite constructor.
     * @param CustomerFactory $customerFactory
     * @param Context $context
     * @param Session $customerSession
     * @param SubscriberFactory $subscriberFactory
     * @param CustomerRepositoryInterface $customerRepository
     * @param AccountManagementInterface $customerAccountManagement
     * @param Data $dataBlock
     * @param AttributeData $attributeData
     * @param array $data
     */
    public function __construct(
        CustomerFactory $customerFactory,
        Context $context,
        Session $customerSession,
        SubscriberFactory $subscriberFactory,
        CustomerRepositoryInterface $customerRepository,
        AccountManagementInterface $customerAccountManagement,
        Data $dataBlock,
        AttributeData $attributeData,
        array $data = []
    ) {
        parent::__construct(
            $context,
            $customerSession,
            $subscriberFactory,
            $customerRepository,
            $customerAccountManagement,
            $data
        );
        $this->attributeHelper = $attributeData;
        $this->customerFactory = $customerFactory;
        $this->dataBlock = $dataBlock;
    }

    /**
     * Return the Customer given the customer Id stored in the session.
     *
     * @return CustomerInterface
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getCustomer()
    {
        if (!$this->customer) {
            $this->customer = $this->customerRepository->getById($this->customerSession->getCustomerId());
        }

        return $this->customer;
    }

    /**
     * @param null $defValue
     * @param string $name
     * @param string $id
     * @param string $title
     * @return string
     */
    public function getCountryHtmlSelect($defValue = null, $name = 'country_id', $id = 'country', $title = 'Country')
    {
        return $this->dataBlock->getCountryHtmlSelect($defValue, $name, $id, $title);
    }

    /**
     * Return whether the form should be opened in an expanded mode showing the change password fields
     *
     * @return bool
     *
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    private function getFullCustomerObject()
    {
        if ($this->getCustomer()) {
            return $this->customerFactory->create()->load($this->customerSession->getCustomerId());
        }
        return false;
    }

    /**
     * check if customer account has password or not
     *
     * @return bool
     *
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @SuppressWarnings(PHPMD.BooleanGetMethodName)
     */
    public function isNoPassword()
    {
        if ($this->getFullCustomerObject()->getPasswordHash() == '') {
            return true;
        }
        return false;
    }

    /**
     * @return AddressInterface
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getAddress()
    {
        if (!$this->address) {
            $customer = $this->getCustomer();
            $addresses = $customer->getAddresses();
            $defaultBillingAddress = $customer->getDefaultBilling();

            foreach ($addresses as $index => $address) {
                if ($address->getId() == $defaultBillingAddress) {
                    $this->address = $address;
                }
            }
        }

        return $this->address;
    }

    /**
     * @param $lineNumber
     * @return string
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getStreetLine($lineNumber)
    {
        $address = $this->getAddress();

        if (!$address) {
            return '';
        }

        $street = $address->getStreet();

        return isset($street[$lineNumber - 1]) ? $street[$lineNumber - 1] : '';
    }

    /**
     * @return int
     * @throws LocalizedException
     * @throws NoSuchEntityException
     */
    public function getRegionId()
    {
        $address = $this->getAddress();

        if (!$address) {
            return null;
        }

        $region = $address->getRegion();

        return $region === null ? null : $region->getRegionId();
    }

    /**
     * Return the name of the region for the address being edited.
     *
     * @return string|null
     */
    public function getRegion()
    {
        $address = $this->getAddress();
        if (!$address) {
            return null;
        }
        $region = $address->getRegion();
        return $region === null ? null : ($region->getRegionCode() ?? $region->getRegion());
    }

    /**
     * Return the country Id.
     *
     * @return int|null|string
     */
    public function getCountryId()
    {
        $address = $this->getAddress();
        if (!$address) {
            return null;
        }
        if ($countryId = $address->getCountryId()) {
            return $countryId;
        }
        return null;
    }

    /**
     * @return bool
     */
    public function getIsB2bCustomer()
    {
        return $this->attributeHelper->isB2bCustomer();
    }

    /**
     * Get config value.
     *
     * @param string $path
     * @return string|null
     */
    public function getConfig($path)
    {
        return $this->_scopeConfig->getValue($path, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }
}
