<?php
/**
 * Total InfoTech
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://total-infotech.com>
 * @link      http://total-infotech.com
 */

namespace Totaltools\Customer\Block\Business;

use Magento\Customer\Model\Session;

class Account extends \Magento\Framework\View\Element\Template
{

    /**
     * Constructor
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param Session $session
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    
    /**
     * Get form post action url
     *
     * @return string
     */
    public function getPostActionUrl()
    {
        return $this->getUrl("customer/business/accountpost");
    }

    /**
     * Get unlock form text
     *
     * @return mixed
     */
    public function getUnlockedText()
    {
        $text = '';
        try {
            $text = $this->getLayout()
                ->createBlock('Magento\Cms\Block\Block')
                ->setBlockId('unlock_trade_account')
                ->toHtml();
        } catch (\Exception $exception) {
            $this->_logger->error($exception->getMessage());
        }
        return $text;

    }
}
