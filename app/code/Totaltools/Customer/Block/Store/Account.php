<?php
namespace Totaltools\Customer\Block\Store;

class Account extends \Magento\Framework\View\Element\Template
{
    /**
     * Block constructor.
     *
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param array                                            $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    protected function _prepareLayout()
    {
        if ($breadcrumbsBlock = $this->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb(
                'home',
                [
                    'label' => __('Home'),
                    'title' => __('Go to Home Page'),
                    'link' => $this->_storeManager->getStore()->getBaseUrl()
                ]
            );
   
            $breadcrumbsBlock->addCrumb(
                'storeaccount',
                [
                    'label' => __('Store account request'),
                    'title' => __('Store account request'),
                    'link' => $this->getUrl('customer/store/account')
                ]
            );
        }

        return parent::_prepareLayout();
    }

    public function getPostUrl() 
    {
        return $this->getUrl('customer/store/accountpost');
    }
}
