<?php

namespace Totaltools\Customer\Preference\Block\Account;

/**
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au> 
 */

use Magento\Framework\App\DefaultPathInterface;
use Magento\Framework\View\Element\Template\Context;
use Totaltools\Customer\Helper\AttributeData;

class SortLink extends \Magento\Customer\Block\Account\SortLink
{
    /**
     * @var array
     */
    protected static $b2cOnlyPaths = [
        'loyalty/insider/invoices',
        'review/customer',
        'wishlist/index',
        'vault/cards/listaction'
    ];

    /**
     * @inheritdoc
     */
    protected $_template = 'Totaltools_Customer::nav/link.phtml';

    /**
     * @var AttributeData
     */
    protected $helper;

    /**
     * Constructor
     *
     * @param Context $context
     * @param DefaultPathInterface $defaultPath
     * @param array $data
     */
    public function __construct(
        Context $context,
        DefaultPathInterface $defaultPath,
        AttributeData $helper,
        array $data = []
    ) {
        $this->helper = $helper;
        parent::__construct($context, $defaultPath, $data);
    }

    /**
     * @inheritdoc
     */
    protected function _toHtml()
    {
        $path = $this->getPath();

        if (in_array($path, self::$b2cOnlyPaths) && $this->helper->isB2bCustomer()) {
            return '';
        }

        return parent::_toHtml();
    }
}
