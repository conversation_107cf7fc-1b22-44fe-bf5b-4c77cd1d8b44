<?php

namespace Totaltools\Customer\Plugin;

use <PERSON><PERSON><PERSON>\Customer\Api\CustomerRepositoryInterface as CustomerRepository;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Api\Data\CustomerExtensionFactory;
use Magento\Customer\Api\Data\CustomerExtensionInterface;
use Magento\Framework\Api\SearchResultsInterface as SearchResultsInterfaceAlias;
use Magento\Newsletter\Model\ResourceModel\SubscriberFactory;

/**
 * Class CustomerRepositoryPlugin
 *
 * @package Totaltools\Customer\Plugin
 */
class CustomerRepositoryPlugin
{
    /**
     * Order Extension Attributes Factory
     *
     * @var CustomerExtensionFactory
     */
    protected $_customerExtensionFactory;
    /**
     * @var SubscriberFactory
     */
    private $_subscriberResource;

    /**
     * Logging instance
     * @var \Totaltools\Pronto\Model\Logger
     */
    protected $logger;

    /**
     * OrderRepositoryPlugin constructor
     *
     * @param CustomerExtensionFactory $extensionFactory
     * @param SubscriberFactory        $subscriberResource
     */
    public function __construct(
        CustomerExtensionFactory $extensionFactory,
        SubscriberFactory $subscriberResource,
        \Totaltools\Pronto\Model\Logger $logger
    )
    {
        $this->_customerExtensionFactory = $extensionFactory;
        $this->_subscriberResource = $subscriberResource->create();
        $this->logger = $logger;
    }

    /**
     * Add customer extension attributes to customer data object to make it accessible in API data
     *
     * @param CustomerRepository $subject
     * @param CustomerInterface $customer
     *
     * @return CustomerInterface
     */
    public function afterGetById(CustomerRepository $subject, CustomerInterface $customer)
    {
        return $this->setCustomerExtensionAttributes($customer);
    }

    /**
     * Add customer extension attributes to customer data object to make it accessible in API data
     *
     * @param CustomerRepository $subject
     * @param CustomerInterface $customer
     *
     * @return CustomerInterface
     */
    public function afterGet(CustomerRepository $subject, CustomerInterface $customer)
    {
        return $this->setCustomerExtensionAttributes($customer);
    }

    /**
     * @param CustomerRepository $subject
     * @param CustomerInterface $customer
     * @return CustomerInterface
     */
    public function beforeSave(CustomerRepository $subject,  CustomerInterface $customer, $passwordHash = null)
    {
        $this->logger->info('Pronto Customer Log - Create Customer Magento:: Before Customer Email - '.$customer->getEmail());
        return array($customer, $passwordHash);
    }

    /**
     * @param CustomerRepository $subject
     * @param CustomerInterface $customer
     * @return CustomerInterface
     */
    public function afterSave(CustomerRepository $subject,  CustomerInterface $customer)
    {
        $this->logger->info('Pronto Customer Log - Create Customer Magento:: Finish Customer ID - '.$customer->getId());
        return $customer;
    }

    /**
     * Get customer attribute value.
     *
     * @param CustomerInterface $customer
     * @param                   $attributeCode
     *
     * @return string|null
     */
    protected function _getCustomerAttributeValue(CustomerInterface $customer, $attributeCode)
    {
        if ($customerAttribute = $customer->getCustomAttribute($attributeCode)) {
            return $customerAttribute->getValue();
        }

        return null;
    }

    /**
     * Add customer extension attributes to customer data object to make it accessible in API data
     *
     * @param CustomerRepository          $subject
     * @param SearchResultsInterfaceAlias $searchResult
     *
     * @return SearchResultsInterfaceAlias
     */
    public function afterGetList(CustomerRepository $subject, SearchResultsInterfaceAlias $searchResult)
    {
        $customers = $searchResult->getItems();

        foreach ($customers as &$customer) {
            /*** @var CustomerInterface $customer */
            $extensionAttributes = ($customer->getExtensionAttributes()) ?: $this->_customerExtensionFactory->create();

            $this->setCustomerExtensionAttributes($customer, $extensionAttributes);

            // add is_subscribed customer attribute
            if ($extensionAttributes->getIsSubscribed() === null) {
                $isSubscribed = $this->isSubscribed($customer);
                $extensionAttributes->setIsSubscribed($isSubscribed);
            }
        }

        return $searchResult;
    }

    /**
     * Add customer extension attributes to customer data object to make it accessible in API data
     *
     * @param CustomerInterface $customer
     * @param \Magento\Customer\Api\Data\CustomerExtensionInterface|null
     *
     * @return CustomerInterface
     */
    private function setCustomerExtensionAttributes(CustomerInterface $customer, $extensionAttributes = null)
    {
        if ($extensionAttributes instanceof \Magento\Customer\Api\Data\CustomerExtensionInterface === false) {
            $extensionAttributes = ($customer->getExtensionAttributes()) ?: $this->_customerExtensionFactory->create();
        }

        $attributes = [
            'loyalty_id',
            'is_subscribed_sms_promo',
            'is_subscribed_direct_marketing',
            'is_subscribed_phone_promo',
            'role',
            'pronto_position',
            'account_code',
            'is_subscribed',
            'preferred_store'
        ];
        foreach ($attributes as $attribute) {
            $method = 'set' . str_replace(' ', '', ucwords(str_replace('_', ' ', $attribute)));
            if (method_exists($extensionAttributes, $method)) {
                $extensionAttributes->$method($this->_getCustomerAttributeValue($customer, $attribute));
            }
        }

        $customer->setExtensionAttributes($extensionAttributes);

        $attributes = [
            'is_subscribed',
            'is_subscribed_sms_promo',
            'is_subscribed_direct_marketing',
            'is_subscribed_phone_promo'
        ];
        foreach ($attributes as $booleanAttribute) {
            $hasAttribute = $customer->getCustomAttribute($booleanAttribute);

            if ($hasAttribute) {
                $customer->setCustomAttribute($booleanAttribute, (bool)$hasAttribute->getValue());
            }
        }

        return $customer;
    }

    /**
     * This method returns newsletters subscription status for given customer.
     *
     * @param CustomerInterface $customer
     *
     * @return boolean
     */
    private function isSubscribed(CustomerInterface $customer)
    {
        $subscriber = $this->_subscriberResource->loadByCustomerData($customer);

        return isset($subscriber['subscriber_status']) && $subscriber['subscriber_status'] == 1;
    }
}
