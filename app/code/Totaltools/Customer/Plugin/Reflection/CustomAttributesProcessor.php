<?php

namespace Totaltools\Customer\Plugin\Reflection;

use Magento\Framework\Api\AttributeInterface;
use Magento\Framework\Api\AttributeValue;
use Magento\Framework\Api\CustomAttributesDataInterface;
use Magento\Framework\Api\AttributeTypeResolverInterface;
use Magento\Framework\Reflection\DataObjectProcessor;

/**
 * Class CustomAttributesProcessor
 * @package Totaltools\Customer\Plugin\Reflection
 */
class CustomAttributesProcessor
{
    /**
     * @var DataObjectProcessor
     */
    private $dataObjectProcessor;

    /**
     * @var AttributeTypeResolverInterface
     */
    private $attributeTypeResolver;

    /**
     * @param DataObjectProcessor $dataObjectProcessor
     * @param AttributeTypeResolverInterface $typeResolver
     */
    public function __construct(
        DataObjectProcessor $dataObjectProcessor,
        AttributeTypeResolverInterface $typeResolver
    ) {
        $this->dataObjectProcessor = $dataObjectProcessor;
        $this->attributeTypeResolver = $typeResolver;
    }

    public function aroundBuildOutputDataArray(
        \Magento\Framework\Reflection\CustomAttributesProcessor $subject,
        \Closure $proceed,
        CustomAttributesDataInterface $objectWithCustomAttributes,
        $dataObjectType
    ) {
        $customAttributes = $objectWithCustomAttributes->getCustomAttributes();
        $result = [];
        foreach ($customAttributes as $customAttribute) {
            if ($customAttribute) {
                $result[] = $this->convertCustomAttribute($customAttribute, $dataObjectType);
            }
        }

        return $result;
    }

    /**
     * Convert custom_attribute object to use flat array structure
     *
     * @param AttributeInterface $customAttribute
     * @param string $dataObjectType
     * @return array
     */
    private function convertCustomAttribute(AttributeInterface $customAttribute, $dataObjectType)
    {
        $data = [];
        $data[AttributeValue::ATTRIBUTE_CODE] = $customAttribute->getAttributeCode();
        $value = $customAttribute->getValue();
        if (is_object($value)) {
            $type = $this->attributeTypeResolver->resolveObjectType(
                $customAttribute->getAttributeCode(),
                $value,
                $dataObjectType
            );
            $value = $this->dataObjectProcessor->buildOutputDataArray($value, $type);
        } elseif (is_array($value)) {
            $valueResult = [];
            foreach ($value as $singleValue) {
                if (is_object($singleValue)) {
                    $type = $this->attributeTypeResolver->resolveObjectType(
                        $customAttribute->getAttributeCode(),
                        $singleValue,
                        $dataObjectType
                    );
                    $singleValue = $this->dataObjectProcessor->buildOutputDataArray($singleValue, $type);
                }
                // Cannot cast to a type because the type is unknown
                $valueResult[] = $singleValue;
            }
            $value = $valueResult;
        }
        $data[AttributeValue::VALUE] = $value;

        return $data;
    }
}
