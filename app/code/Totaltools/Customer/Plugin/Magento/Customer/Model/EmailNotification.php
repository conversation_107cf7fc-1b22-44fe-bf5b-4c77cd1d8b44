<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Customer\Plugin\Magento\Customer\Model;

use Magento\Customer\Model\EmailNotificationInterface;
use Totaltools\Pronto\Model\Request\CustomerRequestManager;

class EmailNotification
{
    /**
     * @var CustomerRequestManager
     */
    protected $prontoManager;

    /**
     * @param CustomerRequestManager $prontoManager
     */
    public function __construct(
        CustomerRequestManager $prontoManager
    ) {
        $this->prontoManager = $prontoManager;
    }

    public function aroundNewAccount(
        \Magento\Customer\Model\EmailNotification $subject,
        \Closure $proceed,
        $customer,
        $type = 'registered',
        $backUrl = '',
        $storeId = 0,
        $sendemailStoreId = null
    ) {
        if($type == EmailNotificationInterface::NEW_ACCOUNT_EMAIL_CONFIRMATION) {
            $prontoData = $this->prontoManager->getProntoCustomer($customer);
            if (!count($prontoData) || (!$prontoData['emailExist'] && !$prontoData['mobileExist'])) {
                $result = $proceed($customer, $type, $backUrl, $storeId, $sendemailStoreId);
            } else {
                $result = null;
            }
        } else {
            $result = $proceed($customer, $type, $backUrl, $storeId, $sendemailStoreId);
        }
        
        return $result;
    }
}

