<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2018 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Customer\Plugin\Model\Email;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Api\CustomAttributesDataInterface;

/**
 * Class Sender.
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2018 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class Sender extends \Magento\Company\Model\Email\Sender
{
    /**
     * Overridden method of sending emails when assign customer as super admin for company.
     * We remove sending functionality.
     *
     * @param CustomerInterface $customer
     * @param int               $companyId
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     *
     * @return \Magento\Company\Model\Email\Sender
     */
    public function aroundSendAssignSuperUserNotificationEmail(
        \Magento\Company\Model\Email\Sender $subject,
        \Closure $proceed,
        CustomerInterface $customer,
        $companyId
    ) {
        return $subject;
    }

    /**
     * Overridden method of sending emails to customer with status update message.
     *
     * @param CustomerInterface $customer
     * @param int $status
     * @return \Magento\Company\Model\Email\Sender
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function aroundSendUserStatusChangeNotificationEmail(
        \Magento\Company\Model\Email\Sender $subject,
        \Closure $proceed,
        CustomerInterface $customer,
        $status
    ) {
        return $subject;
    }
}