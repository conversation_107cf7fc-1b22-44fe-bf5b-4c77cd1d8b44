<?php

namespace Totaltools\Customer\Plugin\Model;

use Magento\Customer\Api\Data\AddressInterface;
use Magento\Customer\Model\Address as addressModel;
/**
 * Class Address
 * @package Totaltools\Customer\Plugin\Model
 */
class Address
{
    /**
     * @var \Magento\Framework\Reflection\DataObjectProcessor
     */
    private $dataProcessor;

    /**
     * Address constructor.
     * @param \Magento\Framework\Reflection\DataObjectProcessor $dataProcessor
     */
    public function __construct(
        \Magento\Framework\Reflection\DataObjectProcessor $dataProcessor
    ) {
        $this->dataProcessor = $dataProcessor;
    }
    public function afterUpdateData(addressModel $subject, \Magento\Customer\Model\Address $result, AddressInterface $address)
 {
        // Set all attributes
        $attributes = $this->dataProcessor
            ->buildOutputDataArray($address, \Magento\Customer\Api\Data\AddressInterface::class);

        foreach ($attributes as $attributeCode => $attributeData) {
            if (AddressInterface::REGION === $attributeCode) {
                $subject->setRegion($address->getRegion()->getRegion());
                $subject->setRegionCode($address->getRegion()->getRegionCode());
                $subject->setRegionId($address->getRegion()->getRegionId());
            } else {
                $subject->setDataUsingMethod($attributeCode, $attributeData);
            }
        }
        // Need to explicitly set this due to discrepancy in the keys between model and data object
        $subject->setIsDefaultBilling($address->isDefaultBilling());
        $subject->setIsDefaultShipping($address->isDefaultShipping());
        $customAttributes = $address->getCustomAttributes();
        if ($customAttributes !== null) {
            foreach ($customAttributes as $attribute) {
                if ($attribute) {
                    $subject->setData($attribute->getAttributeCode(), $attribute->getValue());
                }
            }
        }

        return $this;
    }
}
