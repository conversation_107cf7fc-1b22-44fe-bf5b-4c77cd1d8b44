<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Customer
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Customer\Plugin\Model\ResourceModel\AddressRepository;

use Magento\Customer\Model\ResourceModel\AddressRepository;
use Magento\Customer\Model\Data\Address;
/**
 * Class AddressRepositoryPlugin
 * @package Totaltools\Customer\Plugin\Model\ResourceModel\AddressRepository
 */
class AddressRepositoryPlugin
{
    /**
     * @var \Magento\Customer\Model\Session
     */
    protected $_customerSession;

    /**
     * AddressRepositoryPlugin constructor.
     * @param \Magento\Customer\Model\Session $customerSession
     */
    public function __construct(
        \Magento\Customer\Model\Session $customerSession
    )
    {
        $this->_customerSession = $customerSession;
    }

    /**
     * Unset default_mailing_address flag for customer adresses
     *
     * @param AddressRepository $subject
     * @param Address $address
     */
    public function afterSave(AddressRepository $subject, Address $address)
    {
        $defaultMailingAttribute = $address->getCustomAttribute('default_mailing_address');
        $needUpdateAdresses = !empty($defaultMailingAttribute) && !empty($defaultMailingAttribute->getValue());

        if ($needUpdateAdresses) {
            $customerAddresses = $this->_customerSession->getCustomer()->getAddressesCollection();
            foreach ($customerAddresses as $index => $customerAddress) {
                $attributeValue = 0;
                if ($customerAddress->getId() == $address->getId()) {
                    $attributeValue = 1;
                }

                $customerAddress->setData('default_mailing_address', $attributeValue)->save();
            }
        }
    }
}