<?php

namespace Totaltools\Customer\Plugin\Model\Delegation;

use Magento\Customer\Api\Data\CustomerInterface;

/**
 * Class AccountDelegation
 * @package Totaltools\Customer\Plugin\Model\Delegation
 */
class AccountDelegation
{
    /**
     * @param \Magento\Customer\Model\Delegation\AccountDelegation $subject
     * @param CustomerInterface $customer
     * @param array|null $mixedData
     */
    public function beforeCreateRedirectForNew(
        \Magento\Customer\Model\Delegation\AccountDelegation $subject,
        CustomerInterface $customer,
        array $mixedData = null
    ) {
        $customer->setData('is_delegate_create', 1);
    }
}
