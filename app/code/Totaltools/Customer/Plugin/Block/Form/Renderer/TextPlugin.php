<?php

namespace Totaltools\Customer\Plugin\Block\Form\Renderer;

/**
 * @package Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @since 1.0.30
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

use Totaltools\Customer\Helper\AttributeData as CustomerHelper;

/** @class TextPlugin */
class TextPlugin
{
    /**
     * @var CustomerHelper
     */
    protected $customerHelper;

    /**
     * @param CustomerHelper $customerHelper
     */
    public function __construct(CustomerHelper $customerHelper)
    {
        $this->customerHelper = $customerHelper;
    }

    /**
     * @param \Magento\CustomAttributeManagement\Block\Form\Renderer\Text $subject
     * @param \Magento\Framework\View\Element\BlockInterface $templateContext 
     * @return void
     */
    public function beforeSetTemplateContext($subject, $templateContext)
    {
        $subject->setIsB2bCustomer($this->customerHelper->isB2bCustomer());
    }
}
