<?php

namespace Totaltools\Customer\Plugin\Controller;

/**
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

use Psr\Log\LoggerInterface;
use Totaltools\Customer\Helper\AttributeData;

/** @class LoginPostPlugin */
class LoginPostPlugin
{
    /**
     * @var AttributeData
     */
    protected $helper;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    private \Magento\Checkout\Model\Session $checkoutSession;

    /**
     * @var LoggerInterface
     */
    protected $_logger;

    /**
     * @var \Magento\Framework\Controller\Result\RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @param AttributeData $helper
     * @param \Magento\Checkout\Model\Session $checkoutSession
    * @param \Magento\Framework\Controller\Result\RedirectFactory $resultRedirectFactory
     */
    public function __construct(
        AttributeData $helper,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Framework\Controller\Result\RedirectFactory $resultRedirectFactory,
        LoggerInterface $_logger
    ) {
        $this->helper = $helper;
        $this->checkoutSession = $checkoutSession;
        $this->_logger = $_logger;
        $this->resultRedirectFactory = $resultRedirectFactory;
    }

    /**
     * After submitting customer login form, if account belongs to a non-b2b
     * customer, redirect to the insider rewards section instead of account info
     *
     * @param \Magento\Customer\Controller\Account\LoginPost $subject
     * @param \Magento\Framework\Controller\Result\Redirect $result
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function afterExecute($subject, $result)
    {
        $this->_logger->info('Loginpost :'. get_class($result));
        if (!$this->helper->isB2bCustomer() && $result instanceof \Magento\Framework\Controller\Result\Redirect) {
            if ($this->helper->isRedirectedFromCheckout()) {
                $result->setPath('checkout');
            } else {
                $result->setPath('loyalty/insider/info');
            }
        } elseif (!$this->helper->isB2bCustomer() && $result instanceof \Magento\Framework\Controller\Result\Forward ) {
            $result->forward('index');
        }
        $resultRedirect = $this->resultRedirectFactory->create();
        if ($this->helper->tradeRedirect()) {
            if ($this->helper->isLoggedIn() && $this->helper->isTradeRewards()) {
                $url = $this->helper->getUnlockUrl();         
                $resultRedirect->setUrl($url);
                return $resultRedirect;     
            } elseif ($this->helper->isLoggedIn() && !$this->helper->isTradeRewards()) {       
                $url = $this->helper->getBusinessAccountUrl();       
                $resultRedirect->setUrl($url);
                return $resultRedirect;
            } else {
                $url = $this->helper->getLoginUrl();       
                $resultRedirect->setUrl($url);
                return $resultRedirect;
            }   
        } elseif($this->helper->storeRedirect()) {
            if ($this->helper->isLoggedIn() && !$this->helper->isTradeRewards()) {  
                $url = $this->helper->getStoreAccountUrl();       
                $resultRedirect->setUrl($url);
                return $resultRedirect;
            } 
        }

        // reset storepickup session and storelocator_store
        $this->checkoutSession->setData('storepickup_session', null);
        $this->checkoutSession->setStorelocatorStore(null);

        return $result;
    }
}
