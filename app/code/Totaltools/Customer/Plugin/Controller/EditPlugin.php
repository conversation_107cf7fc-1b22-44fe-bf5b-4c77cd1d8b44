<?php

namespace Totaltools\Customer\Plugin\Controller;

/**
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @since       1.0.30
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au> 
 */

 /** @class EditPlugin */
class EditPlugin
{
    /**
     * @param \Magento\Customer\Controller\Account\Edit $subject
     * @param \Magento\Framework\View\Result\Page $resultPage
     * @return \Magento\Framework\View\Result\Page
     */
    public function afterExecute($subject, $resultPage)
    {
        if ($resultPage instanceof \Magento\Framework\View\Result\Page) {
            $resultPage->getConfig()->getTitle()->set(__('Personal Details'));
        }

        return $resultPage;
    }
}
