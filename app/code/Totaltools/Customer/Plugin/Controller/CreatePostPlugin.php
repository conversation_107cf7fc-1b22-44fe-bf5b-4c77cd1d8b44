<?php

namespace Totaltools\Customer\Plugin\Controller;

/**
 * @category    Totaltools
 * @package     Totaltools_Customer
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 * @link        https://total-infotech.com
 */

use Magento\Customer\Model\Session;
use Totaltools\Customer\Helper\AttributeData;

/** @class LoginPostPlugin */
class CreatePostPlugin
{
    /**
     * @var AttributeData
     */
    protected $helper;

    /**
     * @var Session
     */
    protected $customerSession;

    /**
     * @var \Magento\Framework\Controller\Result\RedirectFactory
     */
    protected $resultRedirectFactory;

    /**
     * @param AttributeData $helper
     * @param Session $customerSession
     */
    public function __construct(
        AttributeData $helper,
        Session $customerSession,
        \Magento\Framework\Controller\Result\RedirectFactory $resultRedirectFactory
    ) {
        $this->helper = $helper;
        $this->customerSession = $customerSession;
        $this->resultRedirectFactory = $resultRedirectFactory;
    }

    /**
     * After submitting registration, if account belongs to a non-b2b
     * customer, redirect to the insider rewards section instead of account info
     *
     * @param \Magento\Customer\Controller\Account\CreatePost $subject
     * @param \Magento\Framework\Controller\Result\Redirect $result
     * @return \Magento\Framework\Controller\Result\Redirect
     */
    public function afterExecute($subject, $result)
    {
        if ($this->customerSession->isLoggedIn() && !$this->helper->isB2bCustomer()) {
            $result->setPath('loyalty/insider/info');
        }
        if ($this->helper->tradeRedirect()) {
            $resultRedirect = $this->resultRedirectFactory->create();
            if ($this->helper->isTradeRewards()) {
                $url = $this->helper->getUnlockUrl();         
                $resultRedirect->setUrl($url);
                return $resultRedirect;   
            } else {
                $url = $this->helper->getBusinessAccountUrl();       
                $resultRedirect->setUrl($url);
                return $resultRedirect;
            }   
        }
        
        return $result;
    }
}
