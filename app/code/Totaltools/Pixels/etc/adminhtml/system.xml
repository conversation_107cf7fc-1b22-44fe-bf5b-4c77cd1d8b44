<?xml version="1.0"?>
<!--
/**
* @package Totaltools_Pixels
* <AUTHOR> Dev Team
* @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
*/
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label">
            <label>Totaltools</label>
        </tab>
        <section id="totaltools_pixels" translate="label" sortOrder="250" showInDefault="1" showInWebsite="1" showInStore="1">
            <class>separator-top</class>
            <label>Marketing Tags and Pixels</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Pixels::pixels_config</resource>
            <group id="ca_wtb" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Channel Advisor - Where to buy</label>
                <field id="enabled" translate="label" type="select" sortOrder="5" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable Pixel</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                 <field id="url" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Pixel URL</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="pixel_tag" translate="label" type="text" sortOrder="15" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Pixel Tag</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="retailer_auth_code" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Retailer Authorization Code</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="categories" translate="label" type="multiselect" sortOrder="25" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Brand Categories</label>
                    <source_model>Totaltools\Pixels\Model\Config\Source\Categories</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>