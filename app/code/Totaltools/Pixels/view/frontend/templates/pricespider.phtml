<?php

/**
 * @category Totaltools
 * @package Totaltools_Search
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2021 (c) Totaltools. <https://totaltools.com.au>
 */
?>

<!-- PriceSpider Pixel script -->
<script defer src="//cdn.pricespider.com/pathinsights/ps-pi-ps.min.js"></script>
<script>
    var getUrlParam = function(name) {
        name = name.replace(/[\[]/, '\\[').replace(/[\]]/, '\\]');
        var regex = new RegExp('[\\?&]' + name + '=([^&#]*)'),
            results = regex.exec(location.search);

        return results === null ? '' : decodeURIComponent(results[1].replace(/\+/g, ' '));
    };

    var pSsetCookie = function(value) {
        pswtb && pswtb.purchaseTracking.setCookie(value);

        var date = new Date();
        date.setDate(date.getDate() + 1);

        document.cookie = 'pswtb.psrid=' + encodeURIComponent(value) +
            '; expires=' + date.toUTCString() +
            '; domain=.' + location.hostname.replace('www.', '') +
            '; path=/' +
            '; secure=1';
    }

    document.addEventListener('DOMContentLoaded', function(e) {
        var psrId = getUrlParam('psrid');

        if (psrId.length) {
            pSsetCookie(psrId);
        }
    });
</script>
<!-- PriceSpider Pixel script ends -->