<?php

namespace Totaltools\Pixels\Plugin\Controller\Product;

/**
 * @package Totaltools_Pixels
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

class View
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var \Magento\Framework\App\Request\Http
     */
    protected $_request;

    /**
     * @var \Totaltools\Pixels\Helper\CaWtb\Data
     */
    protected $_helper;

    /**
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Framework\App\Request\Http $request
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Framework\App\Request\Http $request,
        \Totaltools\Pixels\Helper\CaWtb\Data $helper
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->_request = $request;
        $this->_helper = $helper;
    }

    /**
     * @param \Magento\Catalog\Controller\Product\View $subject
     * @return void
     */
    public function beforeExecute(
        $subject
    ) {
        $_ref = $this->_request->getParam('_ref', false);
        $_refv = $this->_request->getParam('_refv', false);

        if ($_ref && $_refv && $_ref == $this->_helper->getPixelTag()) {
            try {
                $this->_checkoutSession->setData(\Totaltools\Pixels\Helper\CaWtb\Data::SESSION_TAG, $_refv);
            } catch (\Exception $e) { }
        }
    }
}
