<?php

namespace Totaltools\Pixels\Helper\CaWtb;

/**
 * @package Totaltools_Pixels
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * Session tag to store pixel ref value
     */
    const SESSION_TAG = 'ca_wtb_tracking';
    
    const XML_PATH_CAWTB_ENABLED = 'totaltools_pixels/ca_wtb/enabled';

    const XML_PATH_CAWTB_URL = 'totaltools_pixels/ca_wtb/url';

    const XML_PATH_CAWTB_TAG = 'totaltools_pixels/ca_wtb/pixel_tag';

    const XML_PATH_CAWTB_AUTH_CODE = 'totaltools_pixels/ca_wtb/retailer_auth_code';

    const XML_PATH_CAWTB_CATEGORIES = 'totaltools_pixels/ca_wtb/categories';

    /**
     * @return string
     */
    public function isPixelEnabled(){
        return $this->scopeConfig->getValue(self::XML_PATH_CAWTB_ENABLED);
    }

    /**
     * @return string
     */
    public function getUrl()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_CAWTB_URL);
    }

    /**
     * @return string
     */
    public function getPixelTag()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_CAWTB_TAG);
    }

    /**
     * @return string
     */
    public function getAuthCode(){
        return $this->scopeConfig->getValue(self::XML_PATH_CAWTB_AUTH_CODE);
    }

    /**
     * @return string
     */
    public function getCategories(){
        return $this->scopeConfig->getValue(self::XML_PATH_CAWTB_CATEGORIES);
    }
}