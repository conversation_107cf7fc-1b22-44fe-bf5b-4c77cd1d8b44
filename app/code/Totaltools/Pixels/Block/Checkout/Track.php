<?php

namespace Totaltools\Pixels\Block\Checkout;

/**
 * @package Totaltools_Pixels
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Magento\Sales\Model\Order;
use Magento\Sales\Api\Data\OrderItemInterface;

class Track extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $_checkoutSession;

    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    protected $_productRepository;

    /**
     * @var \Magento\Framework\Pricing\Helper\Data
     */
    protected $_priceHelper;

    /**
     * @var \Totaltools\Pixels\Helper\CA_WTB\Data
     */
    protected $_configHelper;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context,
     * @param \Magento\Checkout\Model\Session $checkoutSession,
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
     * @param \Magento\Framework\Pricing\Helper\Data $priceHelper
     * @param \Totaltools\Pixels\Helper\CaWtb\Data $configHelper
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Catalog\Api\ProductRepositoryInterface $productRepository,
        \Magento\Framework\Pricing\Helper\Data $priceHelper,
        \Totaltools\Pixels\Helper\CaWtb\Data $configHelper,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->_checkoutSession = $checkoutSession;
        $this->_productRepository = $productRepository;
        $this->_priceHelper = $priceHelper;
        $this->_configHelper = $configHelper;
    }

    /**
     * Build pixel image URL using helper methods.
     *
     * @return string|bool
     */
    public function getPixelUrl()
    {
        if ($this->_configHelper->isPixelEnabled() && $this->getAuthToken()) {
            $order = $this->_checkoutSession->getLastRealOrder();

            $trackingCode = $this->_checkoutSession->getData(\Totaltools\Pixels\Helper\CaWtb\Data::SESSION_TAG);

            $authCode = $this->_configHelper->getAuthCode();

            if ($trackingCode && $authCode) {
                $url = $this->_configHelper->getUrl() . "?trackingCode={$trackingCode}&retailerAuthorizationToken={$authCode}&sale=";

                $tax = $this->getOrderTax($order);

                $url .= $tax;

                if (($products = $this->getOrderItems($order)) !== FALSE) {
                    $url .= $products;
                } else {
                    return false;
                }

                $this->clearTrackingCode(); // Clear tracking code once valid url is generated & pixel fired.

                return $url;
            }
        }

        return false;
    }

    /**
     * @return string|bool
     */
    protected function getOrderTax(Order $order)
    {
        return $order->getTaxAmount() ? "tax:true;;" : "tax:false;;";
    }

    /**
     * @return string|bool
     */
    protected function getOrderItems(Order $order)
    {
        $orderItems = $order->getAllVisibleItems();
        $products = false;
        $orderTotal = 0;

        foreach ($orderItems as $item) {
            if ($this->itemInCategory($item)) {
                $rowTotal = $item->getRowTotalInclTax();
                $orderTotal += $rowTotal;
                $products .= 'product:' . $item->getSku() . ';';
                $products .= $this->_priceHelper->currency($rowTotal, false, false) . ';';
                $products .= \number_format($item->getQtyOrdered(), 0) . ';;';
            }
        }

        if ($products) {
            $products = 'total_price:' . $this->_priceHelper->currency($orderTotal, false, false) . ";;" . $products;
        }

        return $products;
    }

    /**
     * @return string|bool
     */
    protected function getTrackingCode()
    {
        $trackingCode = $this->_checkoutSession->getData(\Totaltools\Pixels\Helper\CaWtb\Data::SESSION_TAG);
        return $trackingCode ? "trackingCode=$trackingCode;;" : false;
    }

    /**
     * @return string|bool
     */
    protected function getAuthToken()
    {
        return $this->_configHelper->getAuthCode() ? "retailerAuthorizationToken=" . $this->_configHelper->getAuthCode() . ";;" : false;
    }

    /**
     * Check whether given item belongs to the brand category defined via system configration.
     * 
     * @param \Magento\Sales\Api\Data\OrderItemInterface $item
     * @return int
     */
    private function itemInCategory(OrderItemInterface $item)
    {
        $_product = $this->_productRepository->getById($item->getProductId());
        $_productCategories = $_product->getCategoryIds();
        $_brandCategories = explode(',', $this->_configHelper->getCategories());
        $matchedCategories = array_intersect($_brandCategories, $_productCategories);

        return count($matchedCategories);
    }

    /**
     * @return void
     */
    private function clearTrackingCode()
    {
        $this->_checkoutSession->setData(\Totaltools\Pixels\Helper\CaWtb\Data::SESSION_TAG, null);
    }
}
