<?php
/**
 * Helper class
 *
 * @category  Totaltools
 * @package   Totaltools_CustomerGraphQl
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\CustomerGraphQl\Helper;

use Exception;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\InputException;
use Totaltools\Customer\Model\RegisterProntoUser;
use Magento\Framework\App\RequestInterface;
use Totaltools\Customer\Helper\Data as CustomerHelper;
use Laminas\Validator\EmailAddress as Validator;


class Data extends AbstractHelper
{
    /**
     * Session
     *
     * @var Session
     */
    protected $session;

    /**
     * RegisterProntoUser
     *
     * @var RegisterProntoUser
     */
    protected $registerProntoUser;

    /**
     * RequestInterface
     *
     * @var RequestInterface
     */
    protected $_request;

     /**
     * CustomerHelper
     *
     * @var CustomerHelper
     */
    protected $customerHelper;
    
    /**
     * Validator
     *
     * @var Validator
     */
    protected $validator;
   

    /**
     * Data constructor.
     *
     * @param Context                     $context
     * @param Session                     $customerSession
     * @param RegisterProntoUser          $registerProntoUser
     * @param CustomerHelper          $customerHelper
     * @param Validator          $validator
     */
    public function __construct(
        Context $context,
        Session $customerSession,
        RegisterProntoUser $registerProntoUser,
        CustomerHelper $customerHelper,
        Validator $validator
    ) {
        $this->session = $customerSession;
        $this->registerProntoUser = $registerProntoUser;
        $this->customerHelper = $customerHelper;
        $this->validator = $validator;
        parent::__construct($context);
    }

        
    /**
     * simplifiedRegisteration
     *
     * @param  array $post
     * @return array
     */
    public function simplifiedRegisteration($post)
    {
        $this->_request->setParams($post);
        $verified = false;
        $customerData = $this->session->getCustomData();
        $result = [];
       
        if (isset($post['verification_code'])) {
            $verified = $this->customerHelper->verifyCode($post['verification_code']);
            if (!$verified) {
                $post['verified'] = false;
                $result['message'] = __('verification code match failed, please try again');
                $result['status']  = 'code_verification_failed';
                return $result;
            }
        }
        try {
            if (!$this->validator->isValid(trim($post['email']))) {
                $result['message'] = __('Valid Emailaddress is required');
                $result['status']  = 'email_validation_failed';
                return $result;
            } 
            if (empty($customerData)) {
                if (!isset($post['termsandcondition'])) {
                    $result['message'] = __('You must agree to the Terms and Condition');
                    $result['status']  = 'terms_and_condition_required';
                    return $result;
                }
                $customer = $this->registerProntoUser->createProntoUser($this->_request);
                if($customer instanceof \Magento\Customer\Api\Data\CustomerInterface) {
                    $result['message'] = __('User registration successful. Please check your email inbox for login details ');
                    $result['status']  = 'success';
                    return $result;
                } elseif (is_array($customer) && $customer['status'] == 'missing-info' ) {
                    if(isset($customer['missing-data']) && $customer['missing-data'] == 'intro') {
                        $post['intro'] = false;
                        $post['mismatch'] = true;
                        $post['mismatch_mobile'] = false;
                        $result['message'] = __('Sorry, We don\'t have your first and last name, please provide this info too');
                    } elseif(isset($customer['missing-data']) && $customer['missing-data'] == 'mobile') {
                        $post['mismatch'] = true;
                        $post['mismatch_mobile'] = true;
                    }
                    $this->session->setCustomData($customer);
                    $this->session->setCustomerFormData($post);
                } elseif (is_array($customer) && $customer['param'] == 'mobile' && $customer['status'] == 'mobile-verification') {
                    $post['verified'] = false;
                    $post['mismatch'] = true;
                    $post['mismatch_mobile'] = false;
                    $this->customerHelper->sendverificationCode($customer['Mobile']);
                    $this->session->setCustomData($customer);
                    $this->session->setCustomerFormData($post);
                } else {
                    
                    $result['message'] = __('Failed to create account');
                    $result['status']  = 'failed';
                    return $result;
                }
               
               
            }  elseif (isset($customerData['param'])) {
                if ($customerData['param'] == 'email') {
                    $redirectBack = false;
                    if (is_array($customerData) && $customerData['status'] == 'missing-info') {
                        if (isset($customerData['missing-data']) && $customerData['missing-data'] == 'intro') {
                            if (isset($post['first_name']) && isset($post['last_name']) && isset($post['mismatch'])) {
                                $customerData['FirstName'] = $post['first_name'];
                                $customerData['LastName'] = $post['last_name'];
                            } else {
                                $redirectBack = true;
                                $post['intro'] = false;
                                $post['mismatch'] = true;
                                $post['mismatch_mobile'] = false;
                                
                                $result['message'] = __('All fields are required');
                                $result['status']  = 'required_fields';
                            }
                        } elseif (isset($customerData['missing-data']) && $customerData['missing-data'] == 'mobile' && isset($post['mismatch'])) {
                            $result['message'] = __('All fields are required');
                            $result['status']  = 7;
                            return $result;
                        } else {
                            $post['intro'] = true;
                            $post['mismatch'] = true;
                            $post['mismatch_mobile'] = true;
                            $redirectBack = true;
                            $result['message'] = __('All fields are is required');
                            $result['status']  = 8;
                            return $result;
                        }
                    }
                    if (!$redirectBack) {
                        $this->session->setCustomData();
                        $this->session->setCustomerFormData();
                        $customer = $this->registerProntoUser->createCustomer($customerData, $this->_request);
                        $result['message'] = __('Customer Created successfully');
                        $result['status']  = 1;
                        return $result;
                    } else {
                        
                        $this->session->setCustomData($customerData);
                        $this->session->setCustomerFormData($post);
                        $result['message'] = __('All fields are is required');
                        $result['status']  = 6;
                        return $result;
                    }
                } elseif ($customerData['param'] == 'mobile') {
                    if (isset($post['first_name']) && isset($post['last_name']) && isset($post['mismatch'])) {
                        $customerData['FirstName'] = $post['first_name'];
                        $customerData['LastName'] = $post['last_name'];
                        $post['intro'] = true;
                        $post['mismatch'] = false; //mismatch check checked
                        $post['verified'] = false;
                        $this->customerHelper->sendVerificationCode($customerData['Mobile']);
                        $this->session->setCustomData($customerData);
                        $this->session->setCustomerFormData($post);
                        $result['message'] = __('All fields are is required');
                        $result['status']  = 2;
                        return $result;
                    } elseif (isset($post['verification_code']) && $verified) {
                        $sessionData = $this->session->getCustomerFormData() ?? [];
                        $formData['mismatch'] = array_key_exists('mismatch', $sessionData) ? $sessionData['mismatch'] : true;
                        if ($formData['mismatch'] && !isset($post['mismatch'])) {
                            $post['intro'] = true;
                            $post['mismatch'] = true;
                            $post['verified'] = false;
                            $this->session->setCustomData($customerData);
                            $this->session->setCustomerFormData($post);
                            $result['message'] = __('Please agree to update data terms');
                            $result['status']  = 2;
                            return $result;
                        }
                        $customerData['status'] = 'verified';
                        $this->session->setCustomData();
                        $this->session->setCustomerFormData();
                        $customer = $this->registerProntoUser->createCustomer($customerData, $this->_request);
                        $result['message'] = __('Customer Created successfully');
                        $result['status']  = 1;
                        return $result;
                    } else {
                        $post['verified'] = false;
                        $this->customerHelper->sendVerificationCode($customerData['Mobile']);
                        $this->session->setCustomData($customerData);
                        $this->session->setCustomerFormData($post);
                        $result['message'] = __('Please agree to update data terms');
                        $result['status']  = 2;
                        return $result;
                    }
                }
            }
        }catch(Exception $e) {
            $result['message'] = __($e->getMessage());
            $result['status']  = 2;
            return $result;
        }
        $result['message'] = __('Custom message');
        $result['status']  = 3;
        return $result;
    }
}
