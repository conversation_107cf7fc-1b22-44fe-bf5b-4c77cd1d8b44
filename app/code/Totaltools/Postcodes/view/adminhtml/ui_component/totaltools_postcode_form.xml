<?xml version="1.0" ?>
<!--
/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">totaltools_postcode_form.postcode_form_data_source</item>
		</item>
		<item name="label" translate="true" xsi:type="string">General Information</item>
		<item name="template" xsi:type="string">templates/form/collapsible</item>
	</argument>
	<settings>
		<buttons>
			<button class="Totaltools\Postcodes\Block\Adminhtml\PostCode\Edit\BackButton" name="back"/>
			<button class="Totaltools\Postcodes\Block\Adminhtml\PostCode\Edit\DeleteButton" name="delete"/>
			<button class="Totaltools\Postcodes\Block\Adminhtml\PostCode\Edit\SaveButton" name="save"/>
			<button class="Totaltools\Postcodes\Block\Adminhtml\PostCode\Edit\SaveAndContinueButton" name="save_and_continue"/>
		</buttons>
		<namespace>totaltools_postcode_form</namespace>
		<dataScope>data</dataScope>
		<deps>
			<dep>totaltools_postcode_form.postcode_form_data_source</dep>
		</deps>
	</settings>
	<dataSource name="postcode_form_data_source">
		<argument name="data" xsi:type="array">
			<item name="js_config" xsi:type="array">
				<item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
			</item>
		</argument>
		<settings>
			<submitUrl path="*/*/save"/>
		</settings>
		<dataProvider class="Totaltools\Postcodes\Model\PostCode\DataProvider" name="postcode_form_data_source">
			<settings>
				<requestFieldName>postcode_id</requestFieldName>
				<primaryFieldName>postcode_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<fieldset name="general">
		<settings>
			<label/>
		</settings>
		<field formElement="input" name="postcode" sortOrder="10">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">PostCode</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Post Code</label>
				<dataScope>postcode</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field formElement="input" name="locality" sortOrder="20">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">PostCode</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Locality</label>
				<dataScope>locality</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field formElement="input" name="state" sortOrder="30">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">PostCode</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">State</label>
				<dataScope>state</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field formElement="input" name="comments" sortOrder="40">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">PostCode</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Comments</label>
				<dataScope>comments</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field formElement="input" name="longitude" sortOrder="50">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">PostCode</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Longitude</label>
				<dataScope>longitude</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field formElement="input" name="latitude" sortOrder="60">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">PostCode</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Latitude</label>
				<dataScope>latitude</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field formElement="input" name="category" sortOrder="70">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">PostCode</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Category</label>
				<dataScope>category</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">false</rule>
				</validation>
			</settings>
		</field>
		<field formElement="input" name="batch_id" sortOrder="80">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">PostCode</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Batch ID</label>
				<dataScope>batch_id</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
	</fieldset>
</form>
