<?xml version="1.0" ?>
<!--
/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">totaltools_postcode_listing.totaltools_postcode_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>totaltools_postcode_columns</spinner>
		<deps>
			<dep>totaltools_postcode_listing.totaltools_postcode_listing_data_source</dep>
		</deps>
		<buttons>
			<button name="import">
				<url path="adminhtml/import"/>
				<class>primary</class>
				<label translate="true">Import Post Codes</label>
			</button>
			<button name="export">
				<url path="*/*/export"/>
				<class>primary</class>
				<label translate="true">Export Post Codes</label>
			</button>
			<button name="add">
				<url path="*/*/new"/>
				<class>secondary</class>
				<label translate="true">Add new Post Code</label>
			</button>
		</buttons>
	</settings>
	<dataSource component="Magento_Ui/js/grid/provider" name="totaltools_postcode_listing_data_source">
		<settings>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>Totaltools_Postcodes::PostCode</aclResource>
		<dataProvider class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider" name="totaltools_postcode_listing_data_source">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>postcode_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="totaltools_postcode_columns">
		<settings>
			<editorConfig>
				<param name="selectProvider" xsi:type="string">totaltools_postcode_listing.totaltools_postcode_listing.totaltools_postcode_columns.ids</param>
				<param name="enabled" xsi:type="boolean">true</param>
				<param name="indexField" xsi:type="string">postcode_id</param>
				<param name="clientConfig" xsi:type="array">
					<item name="saveUrl" path="totaltools_postcodes/PostCode/inlineEdit" xsi:type="url"/>
					<item name="validateBeforeSave" xsi:type="boolean">false</item>
				</param>
			</editorConfig>
			<childDefaults>
				<param name="fieldAction" xsi:type="array">
					<item name="provider" xsi:type="string">totaltools_postcode_listing.totaltools_postcode_listing.totaltools_postcode_columns_editor</item>
					<item name="target" xsi:type="string">startEdit</item>
					<item name="params" xsi:type="array">
						<item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
						<item name="1" xsi:type="boolean">true</item>
					</item>
				</param>
			</childDefaults>
		</settings>
		<selectionsColumn name="ids">
			<settings>
				<indexField>postcode_id</indexField>
			</settings>
		</selectionsColumn>
		<column name="postcode_id">
			<settings>
				<filter>text</filter>
				<sorting>asc</sorting>
				<label translate="true">ID</label>
			</settings>
		</column>
		<column name="postcode">
			<settings>
				<filter>text</filter>
				<label translate="true">Post Code</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">true</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="locality">
			<settings>
				<filter>text</filter>
				<label translate="true">Locality</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">true</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="state">
			<settings>
				<filter>text</filter>
				<label translate="true">State</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">true</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="category">
			<settings>
				<filter>text</filter>
				<label translate="true">Category</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="comments">
			<settings>
			<filter>text</filter>
				<label translate="true">Comments</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="batch_id">
			<settings>
				<filter>text</filter>
				<label translate="true">Batch ID</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">true</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="latitude">
			<settings>
				<filter>text</filter>
				<label translate="true">Latitude</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="longitude">
			<settings>
				<filter>text</filter>
				<label translate="true">Longitude</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">false</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<actionsColumn class="Totaltools\Postcodes\Ui\Component\Listing\Column\PostCodeActions" name="actions">
			<settings>
				<indexField>postcode_id</indexField>
				<resizeEnabled>false</resizeEnabled>
				<resizeDefaultWidth>107</resizeDefaultWidth>
			</settings>
		</actionsColumn>
	</columns>
</listing>
