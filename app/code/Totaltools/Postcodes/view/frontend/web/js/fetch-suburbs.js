/**
 * @description Ajax wrapper to call Postcodes API for searching suburbs by post code.
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

define([
    'jquery', 
    'ko', 
    'mage/url'
], function($, ko, url) {
    'use strict';

    return {
        xhr: null,

        apiUrl: url.build('/rest/V1/postcodes/search'),

        fetchSuburbsByPostcode: function(query, $postcode, suburbs) {
            var self = this;

            $.when(self.fetchByPostcode(query, 'AUS', false))
                .done(function(result) {
                    $postcode
                        .removeClass('avs-active')
                        .parent()
                        .removeClass('loading');

                    if (result && result.items && result.items.length) {
                        $.each(result.items, function(index, match) {
                            if (typeof match.locality !== 'undefined') {
                                suburbs.push({
                                    city: match.locality,
                                    cityHtml: match.locality,
                                    postcode: match.postcode,
                                    postcodeHtml: match.postcode,
                                    country_id: 'AU',
                                    region: match.state
                                });
                            }
                        });
                    }

                    $('.error_message_search_store').hide();
                })
                .fail(function(result) {
                    $('.error_message_search_store').show();
                    setTimeout(function() {
                        $postcode
                            .removeClass('avs-active')
                            .parent()
                            .removeClass('loading');
                        $('.error_message_search_store').hide();
                    }, 3000);
                });
        },

        fetchByPostcode: function(postcode, country, showLoader) {
            var _self = this;

            if (postcode.length < 3) {
                return;
            }

            if (_self.xhr && _self.xhr.readystate != 4) {
                _self.xhr.abort();
            }

            return (_self.xhr = $.ajax({
                showLoader: showLoader,
                url: _self.apiUrl,
                data: $.param({q : postcode}),
                type: 'GET',
                contentType: 'application/json; charset=utf-8',
                dataType: 'json',
                cache: true
            }));
        }
    };
});
