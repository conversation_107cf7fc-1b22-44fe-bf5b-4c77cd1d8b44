<?php

namespace Totaltools\Postcodes\Model\ResourceModel\PostCode;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    /**
     * Define resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            \Totaltools\Postcodes\Model\PostCode::class,
            \Totaltools\Postcodes\Model\ResourceModel\PostCode::class
        );
    }
}
