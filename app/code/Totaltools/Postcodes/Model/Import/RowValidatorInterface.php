<?php

namespace Totaltools\Postcodes\Model\Import;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

interface RowValidatorInterface extends \Magento\Framework\Validator\ValidatorInterface
{
    const ERROR_INVALID_TITLE = 'InvalidValueTITLE';
    const ERROR_POSTCODE_IS_EMPTY = 'EmptyPostcode';
    const ERROR_LOCALITY_IS_EMPTY = 'EmptyLocality';
    const ERROR_STATE_IS_EMPTY = 'EmptyState';

    /**
     * Initialize validator
     *
     * @return $this
     */
    public function init($context);
}
