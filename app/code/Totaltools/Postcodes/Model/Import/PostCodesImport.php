<?php

namespace Totaltools\Postcodes\Model\Import;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Totaltools\Postcodes\Model\Import\RowValidatorInterface as ValidatorInterface;
use Magento\ImportExport\Model\Import\ErrorProcessing\ProcessingErrorAggregatorInterface;
use Magento\Framework\App\ResourceConnection;

class PostCodesImport extends \Magento\ImportExport\Model\Import\Entity\AbstractEntity
{
    const ID = 'postcode_id';
    const POSTCODE = 'postcode';
    const LOCALITY = 'locality';
    const STATE = 'state';
    const CATEGORY = 'category';
    const LONGITUDE = 'longitude';
    const LATITUDE = 'latitude';
    const COMMENTS = 'comments';
    const BATCH_ID = 'batch_id';

    const TABLE_ENTITY = 'totaltools_postcode';

    /**
     * Validation failure message template definitions
     *
     * @var array
     */
    protected $_messageTemplates = [
        ValidatorInterface::ERROR_POSTCODE_IS_EMPTY => 'Post code is empty',
        ValidatorInterface::ERROR_LOCALITY_IS_EMPTY => 'Locality is empty',
        ValidatorInterface::ERROR_STATE_IS_EMPTY => 'State is empty'
    ];

    /**
     * @var array
     */
    protected $_permanentAttributes = [
        self::POSTCODE,
        self::LOCALITY,
        self::STATE
    ];

    /**
     * If we should check column names
     *
     * @var bool
     */
    protected $needColumnCheck = true;

    /**
     * @return \Magento\Customer\Model\GroupFactory
     */
    protected $groupFactory;

    /**
     * Valid column names
     *
     * @return array
     */
    protected $validColumnNames = [
        self::ID,
        self::POSTCODE,
        self::LOCALITY,
        self::STATE,
        self::CATEGORY,
        self::LONGITUDE,
        self::LATITUDE,
        self::COMMENTS,
        self::BATCH_ID
    ];

    /**
     * Batch ID to be inserted with current import job
     * 
     * @var int
     */
    private $_batchId;

    /**
     * Need to log in import history
     *
     * @var bool
     */
    protected $logInHistory = true;

    /**
     * @var array
     */
    protected $_validators = [];
    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */

    /**
     * @var \Magento\Framework\DB\Adapter\AdapterInterface
     */
    protected $_connection;

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    protected $_resource;

    /**
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper,
     * @param \Magento\ImportExport\Helper\Data $importExportData,
     * @param \Magento\ImportExport\Model\ResourceModel\Import\Data $importData,
     * @param \Magento\Framework\App\ResourceConnection $resource,
     * @param \Magento\ImportExport\Model\ResourceModel\Helper $resourceHelper,
     * @param \Magento\Framework\Stdlib\StringUtils $string,
     * @param ProcessingErrorAggregatorInterface $errorAggregator,
     * @param \Magento\Customer\Model\GroupFactory $groupFactory
     */
    public function __construct(
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\ImportExport\Helper\Data $importExportData,
        \Magento\ImportExport\Model\ResourceModel\Import\Data $importData,
        \Magento\Framework\App\ResourceConnection $resource,
        \Magento\ImportExport\Model\ResourceModel\Helper $resourceHelper,
        \Magento\Framework\Stdlib\StringUtils $string,
        ProcessingErrorAggregatorInterface $errorAggregator,
        \Magento\Customer\Model\GroupFactory $groupFactory
    ) {
        $this->jsonHelper = $jsonHelper;
        $this->_importExportData = $importExportData;
        $this->_resourceHelper = $resourceHelper;
        $this->_dataSourceModel = $importData;
        $this->_resource = $resource;
        $this->_connection = $resource->getConnection(\Magento\Framework\App\ResourceConnection::DEFAULT_CONNECTION);
        $this->errorAggregator = $errorAggregator;
        $this->groupFactory = $groupFactory;
    }

    /**
     * @return array
     */
    public function getValidColumnNames()
    {
        return $this->validColumnNames;
    }

    /**
     * Entity type code getter.
     *
     * @return string
     */
    public function getEntityTypeCode()
    {
        return self::TABLE_ENTITY;
    }

    /**
     * Row validation.
     *
     * @param array $rowData
     * @param int $rowNum
     * @return bool
     */
    public function validateRow(array $rowData, $rowNum)
    {
        $title = false;
        if (isset($this->_validatedRows[$rowNum])) {
            return !$this->getErrorAggregator()->isRowInvalid($rowNum);
        }

        $this->_validatedRows[$rowNum] = true;

        if (!isset($rowData[self::POSTCODE]) || empty($rowData[self::POSTCODE])) {
            $this->addRowError(ValidatorInterface::ERROR_POSTCODE_IS_EMPTY, $rowNum);
            return false;
        }

        if (!isset($rowData[self::LOCALITY]) || empty($rowData[self::LOCALITY])) {
            $this->addRowError(ValidatorInterface::ERROR_LOCALITY_IS_EMPTY, $rowNum);
            return false;
        }

        if (!isset($rowData[self::STATE]) || empty($rowData[self::STATE])) {
            $this->addRowError(ValidatorInterface::ERROR_STATE_IS_EMPTY, $rowNum);
            return false;
        }

        return !$this->getErrorAggregator()->isRowInvalid($rowNum);
    }

    /**
     * @throws \Exception
     * @return bool Result of operation.
     */
    protected function _importData()
    {
        $this->_batchId = $this->newBatchId();

        if (\Magento\ImportExport\Model\Import::BEHAVIOR_DELETE == $this->getBehavior()) {
            $this->deleteEntity();
        } elseif (\Magento\ImportExport\Model\Import::BEHAVIOR_REPLACE == $this->getBehavior()) {
            $this->replaceEntity();
        } elseif (\Magento\ImportExport\Model\Import::BEHAVIOR_APPEND == $this->getBehavior()) {
            $this->saveEntity();
        }

        return true;
    }

    /**
     * Save post code
     *
     * @return $this
     */
    public function saveEntity()
    {
        $this->saveAndReplaceEntity();
        return $this;
    }

    /**
     * Replace post code
     *
     * @return $this
     */
    public function replaceEntity()
    {
        $this->saveAndReplaceEntity();
        return $this;
    }

    /**
     * Deletes post code data from raw data.
     *
     * @return $this
     */
    public function deleteEntity()
    {
        $listTitle = [];
        while ($bunch = $this->_dataSourceModel->getNextBunch()) {
            foreach ($bunch as $rowNum => $rowData) {
                $this->validateRow($rowData, $rowNum);
                if (!$this->getErrorAggregator()->isRowInvalid($rowNum)) {
                    $rowTitle = $rowData[self::POSTCODE];
                    $listTitle[] = $rowTitle;
                }
                if ($this->getErrorAggregator()->hasToBeTerminated()) {
                    $this->getErrorAggregator()->addRowToSkip($rowNum);
                }
            }
        }
        if ($listTitle) {
            $this->deleteEntityFinish(array_unique($listTitle), self::TABLE_ENTITY);
        }
        return $this;
    }

    /**
     * Save and replace post code
     *
     * @return $this
     */
    protected function saveAndReplaceEntity()
    {
        $behavior = $this->getBehavior();
        $listTitle = [];

        while ($bunch = $this->_dataSourceModel->getNextBunch()) {
            $entityList = [];
            foreach ($bunch as $rowNum => $rowData) {
                if (!$this->validateRow($rowData, $rowNum)) {
                    continue;
                }
                if ($this->getErrorAggregator()->hasToBeTerminated()) {
                    $this->getErrorAggregator()->addRowToSkip($rowNum);
                    continue;
                }
                $rowTitle = $rowData[self::POSTCODE];
                $listTitle[] = $rowTitle;
                if( !empty($rowData[self::ID]) ) 
                {
                    // Update Existing Post Code
                    $entityList[$rowTitle][] = [
                        self::ID => $rowData[self::ID],
                        self::POSTCODE => $rowData[self::POSTCODE],
                        self::LOCALITY => $rowData[self::LOCALITY],
                        self::STATE => $rowData[self::STATE],
                        self::CATEGORY => $rowData[self::CATEGORY],
                        self::LONGITUDE => $rowData[self::LONGITUDE],
                        self::LATITUDE => $rowData[self::LATITUDE],
                        self::COMMENTS => $rowData[self::COMMENTS],
                        self::BATCH_ID => $this->_batchId
                    ];
                } else {
                    // Add New Post Code
                    $entityList[$rowTitle][] = [
                        self::POSTCODE => $rowData[self::POSTCODE],
                        self::LOCALITY => $rowData[self::LOCALITY],
                        self::STATE => $rowData[self::STATE],
                        self::CATEGORY => $rowData[self::CATEGORY],
                        self::LONGITUDE => $rowData[self::LONGITUDE],
                        self::LATITUDE => $rowData[self::LATITUDE],
                        self::COMMENTS => $rowData[self::COMMENTS],
                        self::BATCH_ID => $this->_batchId
                    ];
                }
            }
            if (\Magento\ImportExport\Model\Import::BEHAVIOR_REPLACE == $behavior) {
                if ($listTitle) {
                    if ($this->saveEntityFinish($entityList, self::TABLE_ENTITY)) {
                        $this->deleteEntityFinish(array_unique($listTitle), self::TABLE_ENTITY);
                    }
                }
            } elseif (\Magento\ImportExport\Model\Import::BEHAVIOR_APPEND == $behavior) {
                $this->saveEntityFinish($entityList, self::TABLE_ENTITY);
            }
        }
        return $this;
    }

    /**
     * Save post codes.
     *
     * @param array $priceData
     * @param string $table
     * @return $this
     */
    protected function saveEntityFinish(array $entityData, $table)
    {
        if ($entityData) {
            $tableName = $this->_connection->getTableName($table);
            $entityIn = [];
            foreach ($entityData as $id => $entityRows) {
                foreach ($entityRows as $row) {
                    $entityIn[] = $row;
                }
            }
            
            if ($entityIn) { 
                return $this->_connection->insertOnDuplicate($tableName, $entityIn, [
                    self::ID,
                    self::POSTCODE,
                    self::LOCALITY,
                    self::STATE,
                    self::CATEGORY,
                    self::LONGITUDE,
                    self::LATITUDE,
                    self::COMMENTS,
                    self::BATCH_ID
                ]);
            }
        }

        return false;
    }

    /**
     * @return bool
     */
    protected function deleteEntityFinish(array $listTitle, $table)
    {
        if ($table && $listTitle) {
            try {
                $this->countItemsDeleted += $this->_connection->delete(
                    $this->_connection->getTableName($table),
                    $this->_connection->quoteInto('batch_id NOT IN (?)', $this->_batchId)
                );
                return true;
            } catch (\Exception $e) {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * Find last batch_id used and return new incrementing by 1.
     * 
     * @return int
     */
    public function newBatchId()
    {
        $tableName = $this->_connection->getTableName(self::TABLE_ENTITY);
        $query = "SELECT `batch_id` FROM $tableName ORDER BY `batch_id` DESC";
        $result = $this->_connection->fetchOne($query) ?: 0;

        return (int) $result + 1;
    }
}
