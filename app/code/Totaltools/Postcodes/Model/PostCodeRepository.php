<?php

namespace Totaltools\Postcodes\Model;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Totaltools\Postcodes\Api\PostCodeRepositoryInterface;
use Totaltools\Postcodes\Api\Data\PostCodeInterfaceFactory;
use Magento\Framework\Api\ExtensibleDataObjectConverter;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Postcodes\Model\ResourceModel\PostCode as ResourcePostCode;
use Totaltools\Postcodes\Model\ResourceModel\PostCode\CollectionFactory as PostCodeCollectionFactory;
use Magento\Framework\Api\ExtensionAttribute\JoinProcessorInterface;
use Magento\Framework\Api\DataObjectHelper;
use Totaltools\Postcodes\Api\Data\PostCodeSearchResultsInterfaceFactory;
use Magento\Framework\Reflection\DataObjectProcessor;
use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Api\FilterBuilder;
use Magento\Framework\Api\Search\FilterGroupBuilder;

class PostCodeRepository implements PostCodeRepositoryInterface
{
    protected $dataPostCodeFactory;

    protected $searchResultsFactory;

    private $storeManager;

    protected $postCodeFactory;

    protected $dataObjectProcessor;

    protected $resource;

    private $collectionProcessor;

    protected $postCodeCollectionFactory;

    protected $dataObjectHelper;

    protected $extensibleDataObjectConverter;

    protected $extensionAttributesJoinProcessor;

    protected $searchCriteriaBuilder;

    protected $filterBuilder;

    protected $filterGroupBuilder;
    protected $cache;
    /**
     * Logging instance
     * @var \Totaltools\Postcodes\Model\Logger
     */
    protected $logger;

    /**
     * @param ResourcePostCode $resource
     * @param PostCodeFactory $postCodeFactory
     * @param PostCodeInterfaceFactory $dataPostCodeFactory
     * @param PostCodeCollectionFactory $postCodeCollectionFactory
     * @param PostCodeSearchResultsInterfaceFactory $searchResultsFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param DataObjectProcessor $dataObjectProcessor
     * @param StoreManagerInterface $storeManager
     * @param CollectionProcessorInterface $collectionProcessor
     * @param JoinProcessorInterface $extensionAttributesJoinProcessor
     * @param ExtensibleDataObjectConverter $extensibleDataObjectConverter
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param FilterBuilder $filterBuilder,
     * @param FilterGroupBuilder $filterGroupBuilder
     * @param \Totaltools\Postcodes\Model\Logger $logger
     * @param \Magento\Framework\App\CacheInterface $cache
     */
    public function __construct(
        ResourcePostCode $resource,
        PostCodeFactory $postCodeFactory,
        PostCodeInterfaceFactory $dataPostCodeFactory,
        PostCodeCollectionFactory $postCodeCollectionFactory,
        PostCodeSearchResultsInterfaceFactory $searchResultsFactory,
        DataObjectHelper $dataObjectHelper,
        DataObjectProcessor $dataObjectProcessor,
        StoreManagerInterface $storeManager,
        CollectionProcessorInterface $collectionProcessor,
        JoinProcessorInterface $extensionAttributesJoinProcessor,
        ExtensibleDataObjectConverter $extensibleDataObjectConverter,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        FilterBuilder $filterBuilder,
        FilterGroupBuilder $filterGroupBuilder,
        \Totaltools\Postcodes\Model\Logger $logger,
        \Magento\Framework\App\CacheInterface $cache
    ) {
        $this->resource = $resource;
        $this->postCodeFactory = $postCodeFactory;
        $this->postCodeCollectionFactory = $postCodeCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->dataPostCodeFactory = $dataPostCodeFactory;
        $this->dataObjectProcessor = $dataObjectProcessor;
        $this->storeManager = $storeManager;
        $this->collectionProcessor = $collectionProcessor;
        $this->extensionAttributesJoinProcessor = $extensionAttributesJoinProcessor;
        $this->extensibleDataObjectConverter = $extensibleDataObjectConverter;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->filterBuilder = $filterBuilder;
        $this->filterGroupBuilder = $filterGroupBuilder;
        $this->logger = $logger;
        $this->cache = $cache;
    }

    /**
     * {@inheritdoc}
     */
    public function getList(string $q)
    {
        $q = htmlspecialchars($q, ENT_QUOTES, 'UTF-8');
        $cacheKey  = Cache\Postcodes::TYPE_IDENTIFIER;
        //create cache key for each search postcode/text
        $dynamicCacheKey = $cacheKey.'_'.trim(str_replace(" ", "_", $q));
        //check postcode in cache
        $cacheData =json_decode($this->cache->load($dynamicCacheKey), true);
        $searchResults = $this->searchResultsFactory->create();
        //incase post code already cached
        if(isset($cacheData)) {
            //load from cache
            $searchResults->setItems($cacheData);
           $searchResults->setTotalCount(count($cacheData));
            $searchResults->setData('search_criteria', []);
            return $searchResults;
        } else {
            //query databse
            $collection = $this->postCodeCollectionFactory->create();
            $criteria = $this->buildSearchCriteria($q);
            $searchResults->setSearchCriteria($criteria);

            $this->extensionAttributesJoinProcessor->process(
                $collection,
                \Totaltools\Postcodes\Api\Data\PostCodeInterface::class
            );
            $this->collectionProcessor->process($criteria, $collection);
            $items = [];
            foreach ($collection as $model) {
                $items[] = $model->getDataPostCode();
            }
            $searchResults->setItems($items);
            $this->cache->save(
                json_encode($items),
                $dynamicCacheKey,
                array(Cache\Postcodes::CACHE_TAG)
            );
            $searchResults->setTotalCount($collection->getSize());
            $searchResults->setData('search_criteria', []);
            if($collection->getSize() < 1 && intval($q) > 0) {
                $this->logger->info('Postcode not found in Database: ' . $q);
            }
            return $searchResults;

        }
    }

    /**
     * @param string $postcode
     * @return \Magento\Framework\Api\SearchCriteria
     */
    protected function buildSearchCriteria($postcode)
    {
        $postcode = "%" . $postcode . "%";

        $postcodeFilter = $this->filterBuilder
            ->setField('postcode')
            ->setValue($postcode)
            ->setConditionType('like')
            ->create();

        $localityFilter = $this->filterBuilder
            ->setField('locality')
            ->setValue($postcode)
            ->setConditionType('like')
            ->create();

        $filterGroup = $this->filterGroupBuilder
            ->addFilter($postcodeFilter)
            ->addFilter($localityFilter)
            ->create();

        $searchCriteria = $this->searchCriteriaBuilder
            ->setFilterGroups([$filterGroup])
            ->create();

        return $searchCriteria;
    }

    public function getNearestPostcode(float $latitude, float $longitude, $limit = 1)
    {
        $collection = $this->postCodeCollectionFactory->create();
        
        $distanceExpression = "SQRT(
            POW(69.1 * (latitude - {$latitude}), 2) +
            POW(69.1 * ({$longitude} - longitude) * COS(latitude / 57.3), 2)
        )";
    
        $collection->getSelect()
            ->columns(['distance' => new \Magento\Framework\DB\Sql\Expression($distanceExpression)])
            ->having('distance >= 0')
            ->order('distance ASC')
            ->limit($limit);

        return $collection->getFirstItem();
    }
}