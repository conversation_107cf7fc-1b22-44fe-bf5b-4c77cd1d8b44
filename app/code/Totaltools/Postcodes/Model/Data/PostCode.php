<?php

namespace Totaltools\Postcodes\Model\Data;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Totaltools\Postcodes\Api\Data\PostCodeInterface;

class PostCode extends \Magento\Framework\Api\AbstractExtensibleObject implements PostCodeInterface
{
    /**
     * Get postcode_id
     * @return string|null
     */
    public function getPostcodeId()
    {
        return $this->_get(self::POSTCODE_ID);
    }

    /**
     * Set postcode_id
     * @param string $postcodeId
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setPostcodeId($postcodeId)
    {
        return $this->setData(self::POSTCODE_ID, $postcodeId);
    }

    /**
     * Get postcode
     * @return string|null
     */
    public function getPostcode()
    {
        return $this->_get(self::POSTCODE);
    }

    /**
     * Set postcode
     * @param string $postcode
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setPostcode($postcode)
    {
        return $this->setData(self::POSTCODE, $postcode);
    }

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Totaltools\Postcodes\Api\Data\PostCodeExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * Set an extension attributes object.
     * @param \Totaltools\Postcodes\Api\Data\PostCodeExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Totaltools\Postcodes\Api\Data\PostCodeExtensionInterface $extensionAttributes
    ) {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Get locality
     * @return string|null
     */
    public function getLocality()
    {
        return $this->_get(self::LOCALITY);
    }

    /**
     * Set locality
     * @param string $locality
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setLocality($locality)
    {
        return $this->setData(self::LOCALITY, $locality);
    }

    /**
     * Get state
     * @return string|null
     */
    public function getState()
    {
        return $this->_get(self::STATE);
    }

    /**
     * Set state
     * @param string $state
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setState($state)
    {
        return $this->setData(self::STATE, $state);
    }

    /**
     * Get comments
     * @return string|null
     */
    public function getComments()
    {
        return $this->_get(self::COMMENTS);
    }

    /**
     * Set comments
     * @param string $comments
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setComments($comments)
    {
        return $this->setData(self::COMMENTS, $comments);
    }

    /**
     * Get category
     * @return string|null
     */
    public function getCategory()
    {
        return $this->_get(self::CATEGORY);
    }

    /**
     * Set category
     * @param string $category
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setCategory($category)
    {
        return $this->setData(self::CATEGORY, $category);
    }

    /**
     * Get batch_id
     * @return string|null
     */
    public function getBatchId()
    {
        return $this->_get(self::BATCH_ID);
    }

    /**
     * Set batch_id
     * @param string $batchId
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setBatchId($batchId)
    {
        return $this->setData(self::BATCH_ID, $batchId);
    }

    /**
     * Get latitude
     * @return string|null
     */
    public function getLatitude()
    {
        return $this->_get(self::LATITUDE);
    }

    /**
     * Set latitude
     * @param string $latitude
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setLatitude($latitude)
    {
        return $this->setData(self::LATITUDE, $latitude);
    }

    /**
     * Get longitude
     * @return string|null
     */
    public function getLongitude()
    {
        return $this->_get(self::LONGITUDE);
    }

    /**
     * Set longitude
     * @param string $longitude
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setLongitude($longitude)
    {
        return $this->setData(self::LONGITUDE, $longitude);
    }
}
