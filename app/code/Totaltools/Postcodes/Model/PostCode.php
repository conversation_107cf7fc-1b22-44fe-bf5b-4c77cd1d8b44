<?php

namespace Totaltools\Postcodes\Model;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Totaltools\Postcodes\Api\Data\PostCodeInterfaceFactory;
use Magento\Framework\Api\DataObjectHelper;
use Totaltools\Postcodes\Api\Data\PostCodeInterface;

class PostCode extends \Magento\Framework\Model\AbstractModel
{
    /**
     * @var \Magento\Framework\Api\DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @var string
     */
    protected $_eventPrefix = 'totaltools_postcode';

    /**
     * Name of object id field
     *
     * @var string
     */
    protected $_idFieldName = 'postcode_id';

    /**
     * @var \Totaltools\Postcodes\Api\Data\PostCodeInterfaceFactory
     */
    protected $postcodeDataFactory;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param PostCodeInterfaceFactory $postcodeDataFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param \Totaltools\Postcodes\Model\ResourceModel\PostCode $resource
     * @param \Totaltools\Postcodes\Model\ResourceModel\PostCode\Collection $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        PostCodeInterfaceFactory $postcodeDataFactory,
        DataObjectHelper $dataObjectHelper,
        \Totaltools\Postcodes\Model\ResourceModel\PostCode $resource,
        \Totaltools\Postcodes\Model\ResourceModel\PostCode\Collection $resourceCollection,
        array $data = []
    ) {
        $this->postcodeDataFactory = $postcodeDataFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
    }

    /**
     * Retrieve postcode model with postcode data
     * @return PostCodeInterface
     */
    public function getDataModel()
    {
        $postcodeData = $this->getData();

        $postcodeDataObject = $this->postcodeDataFactory->create();
        $this->dataObjectHelper->populateWithArray(
            $postcodeDataObject,
            $postcodeData,
            PostCodeInterface::class
        );

        return $postcodeDataObject;
    }

    /**
    * Retrieve store based on post code
    * @return PostCodeInterface
    */
    public function getDataPostCode()
    {
        return $this->getData();
    }
}
