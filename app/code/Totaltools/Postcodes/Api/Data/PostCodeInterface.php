<?php

namespace Totaltools\Postcodes\Api\Data;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

interface PostCodeInterface extends \Magento\Framework\Api\ExtensibleDataInterface
{

    const POSTCODE_ID = 'postcode_id';
    const POSTCODE = 'postcode';
    const COMMENTS = 'comments';
    const CATEGORY = 'category';
    const LOCALITY = 'locality';
    const STATE = 'state';
    const BATCH_ID = 'batch_id';
    const LATITUDE = 'latitude';
    const LONGITUDE = 'longitude';

    /**
     * Get postcode_id
     * @return string|null
     */
    public function getPostcodeId();

    /**
     * Set postcode_id
     * @param string $postcodeId
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setPostcodeId($postcodeId);

    /**
     * Get postcode
     * @return string|null
     */
    public function getPostcode();

    /**
     * Set postcode
     * @param string $postcode
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setPostcode($postcode);

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Totaltools\Postcodes\Api\Data\PostCodeExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     * @param \Totaltools\Postcodes\Api\Data\PostCodeExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Totaltools\Postcodes\Api\Data\PostCodeExtensionInterface $extensionAttributes
    );

    /**
     * Get locality
     * @return string|null
     */
    public function getLocality();

    /**
     * Set locality
     * @param string $locality
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setLocality($locality);

    /**
     * Get state
     * @return string|null
     */
    public function getState();

    /**
     * Set state
     * @param string $state
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setState($state);

    /**
     * Get comments
     * @return string|null
     */
    public function getComments();

    /**
     * Set comments
     * @param string $comments
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setComments($comments);

    /**
     * Get category
     * @return string|null
     */
    public function getCategory();

    /**
     * Set category
     * @param string $category
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setCategory($category);

    /**
     * Get batch_id
     * @return string|null
     */
    public function getBatchId();

    /**
     * Set batch_id
     * @param string $batchId
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setBatchId($batchId);

    /**
     * Get latitude
     * @return string|null
     */
    public function getLatitude();

    /**
     * Set latitude
     * @param string $latitude
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setLatitude($latitude);

    /**
     * Get longitude
     * @return string|null
     */
    public function getLongitude();

    /**
     * Set longitude
     * @param string $longitude
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface
     */
    public function setLongitude($longitude);
}
