<?php

namespace Totaltools\Postcodes\Api\Data;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

interface PostCodeSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get PostCode list.
     * @return \Totaltools\Postcodes\Api\Data\PostCodeInterface[]
     */
    public function getItems();

    /**
     * Set postcode list.
     * @param \Totaltools\Postcodes\Api\Data\PostCodeInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
