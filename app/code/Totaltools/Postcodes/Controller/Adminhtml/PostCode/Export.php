<?php

namespace Totaltools\Postcodes\Controller\Adminhtml\PostCode;
use Totaltools\Postcodes\Model\PostCodeFactory;
use Magento\Framework\App\Filesystem\DirectoryList;

class Export extends \Magento\Backend\App\Action
{
    /** @var \Magento\Framework\View\Result\PageFactory */
    protected $resultPageFactory;
    
    /** @var PostCodeFactory */
    protected $postcodeFactory;

    /**
     * @var \Magento\Framework\App\Response\Http\FileFactory
     */
    protected $fileFactory;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvProcessor;

    /**
     * @var \Magento\Framework\App\Filesystem\DirectoryList
     */
    protected $directoryList;

    /**
     * Constructor
     *
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\View\Result\PageFactory $resultPageFactory
     * @param PostCodeFactory $postcodeFactory
     * @param \Magento\Framework\App\Response\Http\FileFactory $fileFactory
     * @param \Magento\Framework\File\Csv                      $csvProcessor
     * @param \Magento\Framework\App\Filesystem\DirectoryList  $directoryList
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\View\Result\PageFactory $resultPageFactory,
        PostCodeFactory $postcodeFactory,
        \Magento\Framework\App\Response\Http\FileFactory $fileFactory,
        \Magento\Framework\File\Csv $csvProcessor,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->postcodeFactory = $postcodeFactory;
        $this->fileFactory = $fileFactory;
        $this->csvProcessor = $csvProcessor;
        $this->directoryList = $directoryList;
        parent::__construct($context);
    }

    /**
     * Postcode Export To CSV
     *
     * @return \Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function execute()
    {
        $content[] = [
            'postcode_id',
            'postcode',
            'locality',
            'state',
            'comments',
            'category',
            'longitude',
            'latitude'
        ];
        $resultPage = $this->postcodeFactory->create();
        $collection = $resultPage->getCollection(); 
        $fileName = 'postcodes_export.csv';
        $filePath =  $this->directoryList->getPath(DirectoryList::MEDIA) . "/" . $fileName;
        while ($postcode = $collection->fetchItem()) {
            $content[] = [
                $postcode->getPostcodeId(),
                $postcode->getPostcode(),
                $postcode->getLocality(),
                $postcode->getState(),
                $postcode->getComments(),
                $postcode->getCategory(),
                $postcode->getLongitude(),
                $postcode->getLatitude()
            ];
        }
        $this->csvProcessor->setEnclosure('"')->setDelimiter(',')->saveData($filePath, $content);
        return $this->fileFactory->create(
            $fileName,
            [
                'type'  => "filename",
                'value' => $fileName,
                'rm'    => true, 
            ],
            DirectoryList::MEDIA,
            'text/csv',
            null
        );
    }
}
