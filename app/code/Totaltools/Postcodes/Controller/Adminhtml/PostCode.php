<?php

namespace Totaltools\Postcodes\Controller\Adminhtml;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

abstract class PostCode extends \Magento\Backend\App\Action
{
    /** @var \Magento\Framework\Registry */
    protected $_coreRegistry;

    const ADMIN_RESOURCE = 'Totaltools_Postcodes::PostCode';

    /**
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Framework\Registry $coreRegistry
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        \Magento\Framework\Registry $coreRegistry
    ) {
        $this->_coreRegistry = $coreRegistry;
        parent::__construct($context);
    }

    /**
     * Init page
     *
     * @param \Magento\Backend\Model\View\Result\Page $resultPage
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function initPage($resultPage)
    {
        $resultPage->setActiveMenu(self::ADMIN_RESOURCE)
            ->addBreadcrumb(__('Totaltools'), __('Totaltools'))
            ->addBreadcrumb(__('Postcode'), __('Postcode'));
        return $resultPage;
    }
}
