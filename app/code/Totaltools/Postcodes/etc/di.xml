<?xml version="1.0" ?>
<!--
/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">

	<preference for="Totaltools\Postcodes\Api\PostCodeRepositoryInterface" type="Totaltools\Postcodes\Model\PostCodeRepository"/>

	<preference for="Totaltools\Postcodes\Api\Data\PostCodeInterface" type="Totaltools\Postcodes\Model\Data\PostCode"/>

	<preference for="Totaltools\Postcodes\Api\Data\PostCodeSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>

	<virtualType name="Totaltools\Postcodes\Model\ResourceModel\PostCode\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">totaltools_postcode</argument>
			<argument name="resourceModel" xsi:type="string">Totaltools\Postcodes\Model\ResourceModel\PostCode\Collection</argument>
		</arguments>
	</virtualType>

	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="totaltools_postcode_listing_data_source" xsi:type="string">Totaltools\Postcodes\Model\ResourceModel\PostCode\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
	<type name="Totaltools\Postcodes\Model\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Totaltools\Postcodes\Model\Logger">
        <arguments>
            <argument name="name" xsi:type="string">postcodeLog</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">Totaltools\Postcodes\Model\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
	<type name="Magento\ImportExport\Model\Import\SampleFileProvider">
        <arguments>
            <argument name="samples" xsi:type="array">
                <item name="totaltools_postcode" xsi:type="string">Totaltools_Postcodes</item>
            </argument>
        </arguments>
    </type>
</config>
