<?xml version="1.0" ?>
<!--
/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Acl/etc/acl.xsd">
	<acl>
		<resources>
			<resource id="Magento_Backend::admin">
				<resource id="Totaltools_Postcodes::PostCode" sortOrder="10" title="PostCode">
					<resource id="Totaltools_Postcodes::PostCode_save" sortOrder="10" title="Save PostCode"/>
					<resource id="Totaltools_Postcodes::PostCode_delete" sortOrder="20" title="Delete PostCode"/>
					<resource id="Totaltools_Postcodes::PostCode_update" sortOrder="30" title="Update PostCode"/>
					<resource id="Totaltools_Postcodes::PostCode_view" sortOrder="40" title="View PostCode"/>
				</resource>
			</resource>
		</resources>
	</acl>
</config>
