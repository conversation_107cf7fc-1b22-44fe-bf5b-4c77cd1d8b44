<?xml version="1.0" ?>
<!--
/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
	<menu>
		<add id="Totaltools_Postcodes::PostCode" module="Totaltools_Postcodes" resource="Totaltools_Postcodes::PostCode" sortOrder="9999" title="Post Codes" parent="Magento_Backend::content"/>
		<add action="totaltools_postcodes/postcode/index" id="Totaltools_Postcodes::totaltools_postcode" module="Totaltools_Postcodes" parent="Totaltools_Postcodes::PostCode" resource="Magento_Backend::content" sortOrder="10" title="Post Codes"/>
		<add action="adminhtml/import" id="Totaltools_Postcodes::totaltools_postcode_import" module="Magento_ImportExport" parent="Totaltools_Postcodes::PostCode" resource="Magento_ImportExport::import" sortOrder="20" title="Import"/>
	</menu>
</config>
