<?php

namespace Totaltools\Postcodes\Setup;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\ModuleContextInterface;

class UpgradeSchema implements UpgradeSchemaInterface
{

    /**
     * {@inheritdoc}
     */
    public function upgrade(
        SchemaSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $installer = $setup;
        $installer->startSetup();

        if (version_compare($context->getVersion(), "1.0.1", "<")) {
            $tableName = $setup->getTable('totaltools_postcode');

            $setup->getConnection()->addIndex(
                $tableName,
                $installer->getIdxName($tableName, ['postcode']),
                'postcode'
            );

            $setup->getConnection()->addIndex(
                $tableName,
                $installer->getIdxName($tableName, ['locality']),
                'locality'
            );
        }

        if (version_compare($context->getVersion(), "1.0.2", "<")) {
            $tableName = $setup->getTable('totaltools_postcode');

            $connection = $setup->getConnection();

            $connection->addColumn(
                $tableName,
                'longitude',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length' => 255,
                    'nullable' => false,
                    'default' => '',
                    'comment' => 'Longitude'
                ]
            );

            $connection->addColumn(
                $tableName,
                'latitude',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length' => 255,
                    'nullable' => false,
                    'default' => '',
                    'comment' => 'Latitude'
                ]
            );
        }

        $installer->endSetup();
    }
}
