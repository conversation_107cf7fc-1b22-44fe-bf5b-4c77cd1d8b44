<?php

namespace Totaltools\Postcodes\Setup;

/**
 * @package Totaltools_Postcodes
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

class InstallSchema implements InstallSchemaInterface
{

    /**
     * {@inheritdoc}
     */
    public function install(
        SchemaSetupInterface $setup,
        ModuleContextInterface $context
    ) {

        $table_totaltools_postcode = $setup->getConnection()->newTable($setup->getTable('totaltools_postcode'));

        $table_totaltools_postcode->addColumn(
            'postcode_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'nullable' => false, 'primary' => true, 'unsigned' => true,],
            'Entity ID'
        );

        $table_totaltools_postcode->addColumn(
            'postcode',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => False],
            'Post Code'
        );

        $table_totaltools_postcode->addColumn(
            'locality',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => False, 'unsigned' => true],
            'Locality post code belongs to'
        );

        $table_totaltools_postcode->addColumn(
            'state',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => False],
            'State post code belongs to'
        );

        $table_totaltools_postcode->addColumn(
            'comments',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            [],
            'Comments about post code'
        );

        $table_totaltools_postcode->addColumn(
            'category',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            [],
            'Type of post code'
        );

        $table_totaltools_postcode->addColumn(
            'batch_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_SMALLINT,
            null,
            ['nullable' => False, 'unsigned' => true],
            'Current batch id'
        );

        $setup->getConnection()->createTable($table_totaltools_postcode);
    }
}
