<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
    <table name="totaltools_audit_cms_block_logs" resource="default" engine="innodb" comment="Total Tools Audit CMS Block Logs">
        <column xsi:type="datetime" name="timestamp" default="CURRENT_TIMESTAMP" nullable="false" comment="Timestamp"/>
        <column xsi:type="smallint" name="block_id" padding="6" nullable="false" comment="Block ID"/>
        <column xsi:type="bigint" name="user_id" padding="21" unsigned="true" nullable="false" default="0" comment="User ID"/>
        <column xsi:type="varchar" name="user" nullable="false" length="64" comment="User"/>
        <column xsi:type="mediumtext" name="old_content" nullable="true" comment="Old Content"/>
        <column xsi:type="mediumtext" name="new_content" nullable="true" comment="New Content"/>
    </table>
    <table name="totaltools_audit_cms_page_logs" resource="default" engine="innodb" comment="Total Tools Audit CMS Page Logs">
        <column xsi:type="datetime" name="timestamp" default="CURRENT_TIMESTAMP" nullable="false" comment="Timestamp"/>
        <column xsi:type="smallint" name="page_id" padding="6" nullable="false" comment="Page ID"/>
        <column xsi:type="bigint" name="user_id" padding="21" unsigned="true" nullable="false" default="0" comment="User ID"/>
        <column xsi:type="varchar" name="user" nullable="false" length="64" comment="User"/>
        <column xsi:type="mediumtext" name="old_content" nullable="true" comment="Old Content"/>
        <column xsi:type="mediumtext" name="new_content" nullable="true" comment="New Content"/>
    </table>
    <table name="totaltools_audit_core_config_data_logs" resource="default" engine="innodb" comment="Total Tools Audit Core Config Data Logs">
        <column xsi:type="datetime" name="timestamp" default="CURRENT_TIMESTAMP" nullable="false" comment="Timestamp"/>
        <column xsi:type="int" name="config_id" padding="10" nullable="false" comment="Config ID"/>
        <column xsi:type="bigint" name="user_id" padding="21" unsigned="true" nullable="false" default="0" comment="User ID"/>
        <column xsi:type="varchar" name="user" nullable="false" length="64" comment="User"/>
        <column xsi:type="text" name="old_value" nullable="true" comment="Old Content"/>
        <column xsi:type="text" name="new_value" nullable="true" comment="New Content"/>
    </table>
</schema>
