<?php
/**
 * <AUTHOR> <PERSON> (<EMAIL>)
 * @copyright Copyright (c) 2016 Balance Internet (http://www.balanceinternet.com.au)
 * @package Totaltools_Setup
 */

namespace Totaltools\Setup\Setup;

use Magento\Catalog\Model\Product\Exception;
use Magento\Cms\Model\Page;
use Magento\Cms\Model\Block;
use Magento\Cms\Model\PageFactory;
use Magento\Cms\Model\BlockFactory;
use Magento\Framework\Module\Setup\Migration;
use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

/**
 * @codeCoverageIgnore
 */
class InstallData implements InstallDataInterface
{
    /**
     * Page factory
     *
     * @var PageFactory
     */
    private $__pageFactory;

    /**
     * Block factory
     *
     * @var BlockFactory
     */
    private $__blockFactory;

    /**
     * static block model
     *
     * @var Block
     */
    protected $staticBlock;

    /**
     * Page model
     *
     * @var BlockFactory
     */
    protected $page;
    /**
     * Init
     *
     * @param PageFactory $pageFactory
     * @param BlockFactory $blockFactory
     */
    public function __construct(
        PageFactory $pageFactory,
        BlockFactory $blockFactory,
        \Totaltools\Setup\Setup\SetupData\StaticBlock $staticBlockData,
        \Totaltools\Setup\Setup\SetupData\Page $page
    )
    {
        $this->__pageFactory = $pageFactory;
        $this->__blockFactory = $blockFactory;
        $this->staticBlocks = $staticBlockData;
        $this->page = $page;
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function install(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        /* Upgrade */
        if ($context->getVersion()) {
            /* Check current version */
            if (version_compare($context->getVersion(), '1.0.1') < 0) {
                /* Upgrade to 1.0.1 */
            }
            return;
        }
        $blocks = $this->staticBlocks->getStaticBlocks();

        /**
         * Insert / update
         */
        foreach ($blocks as $_data) {
            $_block = $this->createBlock()->load($_data['identifier']);
            if ($_block->getId()) {
                try {
                    $_block->setContent($_data['content'])->save();
                } catch (Exception $e) {
                }
            } else {
                try {
                    $_block->setData($_data)->save();
                } catch (Exception $e) {
                }
            }
        }

        //$pageFolder = $staticFolder .DIRECTORY_SEPARATOR. 'page';
        $pages = $this->page->getPages();

        /**
         * Insert / update
         */
        foreach ($pages as $_data) {
            $_page = $this->createPage()->load($_data['identifier']);
            if ($_page->getId()) {
                try {
                    $_page->setContent($_data['content'])
                        ->setLayoutUpdateXml($_data['layout_update_xml'])
                        ->save();
                } catch (Exception $e) {
                }
            } else {
                try {
                    $_page->setData($_data)->save();
                } catch (Exception $e) {
                }
            }
        }

        $setup->endSetup();
    }

    /**
     * Create page
     *
     * @return Page
     */
    public function createPage()
    {
        return $this->__pageFactory->create();
    }

    /**
     * Create block
     *
     * @return Block
     */
    public function createBlock()
    {
        return $this->__blockFactory->create();
    }
}
