<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Setup\Setup;

use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Unirgy\SimpleLicense\Exception;
use Magento\Sales\Setup\SalesSetupFactory;

/**
 * Upgrade Data script
 * @codeCoverageIgnore
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * EAV setup factory
     *
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    protected $_urlRewriteCollection;

    protected $_storeCollectionFactory;
    protected $_state;

    /**
     * @var SalesSetupFactory
     */
    protected $salesSetupFactory;

    /**
     * @param \Magento\Eav\Setup\EavSetupFactory $eavSetupFactory
     * @param \Magento\UrlRewrite\Model\ResourceModel\UrlRewriteCollectionFactory $urlRewriteCollection
     * @param \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory
     * @param \Magento\Framework\App\State $state
     */
    public function __construct(
        \Magento\Eav\Setup\EavSetupFactory $eavSetupFactory,
        \Magento\UrlRewrite\Model\ResourceModel\UrlRewriteCollectionFactory $urlRewriteCollection,
        \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory $storeCollectionFactory,
        \Magento\Framework\App\State $state,
        SalesSetupFactory $salesSetupFactory
    )
    {
        $this->_state = $state;
        $this->_storeCollectionFactory = $storeCollectionFactory;
        $this->_urlRewriteCollection = $urlRewriteCollection;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->salesSetupFactory = $salesSetupFactory;
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        $version = $context->getVersion();

        if (version_compare($version, '1.1.0') < 0) {
            // add attributes
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
            $params = array(
                'type' => 'text',
                'label' => 'Short Description',
                'input' => 'textarea',
                'required' => false,
                'sort_order' => 4,
                'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                'wysiwyg_enabled' => true,
                'is_html_allowed_on_front' => true,
                'group' => 'General Information',
            );
            $eavSetup->removeAttribute(\Magento\Catalog\Model\Category::ENTITY, 'short_description');
            $eavSetup->addAttribute(\Magento\Catalog\Model\Category::ENTITY, 'short_description', $params);
        }

        if (version_compare($version, '1.1.1') < 0) {
            // add attributes
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
            $params = array(
                'type' => 'varchar',
                'label' => 'Image',
                'input' => 'image',
                'backend' => 'Magento\Catalog\Model\Category\Attribute\Backend\Image',
                'required' => false,
                'sort_order' => 5,
                'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                'group' => 'General Information',
            );
            $eavSetup->removeAttribute(\Magento\Catalog\Model\Category::ENTITY, 'secondary_image');
            $eavSetup->addAttribute(\Magento\Catalog\Model\Category::ENTITY, 'secondary_image', $params);
        }

        if (version_compare($version, '1.1.2') < 0) {
            // update attributes
            $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
            $eavSetup->updateAttribute('customer_address', 'is_business_address', 'is_required', '0');
            $eavSetup->updateAttribute('customer_address', 'authority_to_leave', 'is_required', '0');
        }

        if (version_compare($version, '1.1.3') < 0) {

            $urlRewriteCollection = $this->_urlRewriteCollection->create();
            $urlRewriteCollection->addFieldToFilter('request_path', array('like' => 'storelocator%'));
            $urlRewriteCollection->addFieldToFilter('target_path', array('like' => 'storelocator%'));
            foreach ($urlRewriteCollection as $urlRewrite) {
                $urlRewrite->delete();
            }

            $storeLocators = $this->_storeCollectionFactory->create();
            foreach ($storeLocators as $storeLocator) {
                $url = preg_replace('/\D/', '', $storeLocator->getData('rewrite_request_path'));
                if ($url != "") {
                    $newUrl = $storeLocator->getData('rewrite_request_path');
                    $newUrl = str_replace($url, '', $newUrl);
                    $storeLocator->setData('rewrite_request_path', $newUrl);
                }
                try {
                    $storeLocator->setData('generate_url', true)->save();
                } catch (Exception $e) {
                    echo $e->getMessage();
                }
            }
        }

        if (version_compare($version, '1.1.5') < 0) {

            /** @var \Magento\Sales\Setup\SalesSetup $salesInstaller */
            $salesInstaller = $this->salesSetupFactory->create(['resourceName' => 'sales_setup', 'setup' => $setup]);

            $salesInstaller->addAttribute(
                'order',
                'recaptcha_bypass',
                ['type' => 'int', 'visible' => false, 'default' => 0]
            );
        }

        $setup->endSetup();
    }

}
