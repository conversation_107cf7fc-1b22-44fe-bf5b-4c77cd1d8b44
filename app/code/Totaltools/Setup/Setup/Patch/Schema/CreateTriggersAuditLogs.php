<?php
namespace Totaltools\Setup\Setup\Patch\Schema;

use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Ddl\Trigger;
use Magento\Framework\DB\Ddl\TriggerFactory;
use Magento\Framework\Setup\Patch\SchemaPatchInterface;

class CreateTriggersAuditLogs implements SchemaPatchInterface
{
    const SANSEC_LOG_TABLES = [
        ['source_table' => 'cms_block', 'log_table' => 'totaltools_audit_cms_block_logs'],
        ['source_table' => 'cms_page', 'log_table' => 'totaltools_audit_cms_page_logs'],
        ['source_table' => 'core_config_data', 'log_table' => 'totaltools_audit_core_config_data_logs']
    ];
    const DB_EVENTS = [
        Trigger::EVENT_INSERT,
        Trigger::EVENT_UPDATE,
        Trigger::EVENT_DELETE
    ];

    private TriggerFactory $triggerFactory;
    private ResourceConnection $resource;

    /**
     * @param TriggerFactory $triggerFactory
     * @param ResourceConnection $resource
     */
    public function __construct(
        TriggerFactory $triggerFactory,
        ResourceConnection $resource
    ) {
        $this->triggerFactory = $triggerFactory;
        $this->resource = $resource;
    }

    /**
     * @inheritDoc
     */
    public function apply()
    {
        $connection = $this->resource->getConnection();
        foreach (self::SANSEC_LOG_TABLES as $sansec) {
            foreach (self::DB_EVENTS as $event) {
                $trigger = $this->triggerFactory->create()
                    ->setName($sansec['log_table'] . '_' . strtolower($event))
                    ->setTime(Trigger::TIME_AFTER)
                    ->setEvent($event)
                    ->setTable($this->resource->getTableName($sansec['source_table']));
                $trigger->addStatement($this->buildStatement($sansec['log_table'], $event));
                $connection->dropTrigger($trigger->getName());
                $connection->createTrigger($trigger);
            }
        }
    }

    /**
     * @param string $logTable
     * @param string $event
     * @return string
     */
    private function buildStatement(string $logTable, string $event): string
    {
        $trigger = 'INSERT IGNORE INTO %s SET %s;';
        $logTableName = $this->resource->getTableName($logTable);
        $connection = $this->resource->getConnection();
        $describe = $connection->describeTable($logTableName);
        $columns = [];
        $columnNames = array_column($describe, 'COLUMN_NAME');
        foreach ($columnNames as $columnName) {
            $value = match ($columnName) {
                'config_id' => $event == Trigger::EVENT_DELETE ? '' : 'NEW.config_id',
                'block_id' => $event == Trigger::EVENT_DELETE ? '' : 'NEW.block_id',
                'page_id' => $event == Trigger::EVENT_DELETE ? '' : 'NEW.page_id',
                'user_id' => 'connection_id()',
                'user' => 'user()',
                'old_content' => $event == Trigger::EVENT_INSERT ? '"totaltools_audit_insert"' : 'OLD.content',
                'new_content' => $event == Trigger::EVENT_DELETE ? '' : 'NEW.content',
                'old_value' => $event == Trigger::EVENT_INSERT ? '"totaltools_audit_insert"' : 'OLD.value',
                'new_value' => $event == Trigger::EVENT_DELETE ? '' : 'NEW.value',
                default => '',
            };
            if ($value) {
                $columns[] = sprintf(
                    $connection->quoteIdentifier($columnName) . ' = %s',
                    $value
                );
            }
        }

        return sprintf(
            $trigger,
            $connection->quoteIdentifier($logTableName),
            implode(', ', $columns)
        );
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }
}
