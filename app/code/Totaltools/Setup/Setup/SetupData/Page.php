<?php
/**
 * @copyright Copyright (c) 2016 Balance Internet (http://www.balanceinternet.com.au)
 * @package Totaltools_Setup
 */

namespace Totaltools\Setup\Setup\SetupData;

/**
 * @codeCoverageIgnore
 */
class Page
{
    /**
     * declare pages
     *
     * @return array()
     */
    public function getPages()
    {
        return [
            [
                'title' => 'franchise',
                'page_layout' => '2columns-left',
                'identifier' => 'franchise',
                'content' => '<div class="wrapper wrap-cms">
    <h1 class="title title-cms">APPRENTICE</h1>
    <div class="banner">{{block class="Magento\\Cms\\Block\\Block" block_id="franchise_banner"}}</div>
    <div class="content">
        <div class="content-main">{{block class="Magento\\Cms\\Block\\Block" block_id="franchise_content"}}</div>
        <div class="content-right">{{block class="Magento\\Cms\\Block\\Block" block_id="franchise_sidebar"}}</div>
    </div>
</div>',
                'layout_update_xml' => '<referenceContainer name="catalog.compare.sidebar" remove="true"/>
                                        <referenceContainer name="wishlist_sidebar" remove="true"/>
                                        <referenceContainer name="sidebar.main">
                                            <block class="Magento\Cms\Block\Block" name="cms_nav">
                                                <arguments>
                                                    <argument name="block_id" xsi:type="string">cms_nav</argument>
                                                </arguments>
                                            </block>
                                        </referenceContainer>',
                'is_active' => 1,
                'stores' => [0],
            ],
            [
                'title' => 'apprentice',
                'page_layout' => '2columns-left',
                'identifier' => 'apprentice',
                'content' => '<div class="wrapper wrap-cms">
    <h1 class="title title-cms">FRANCHISE OPPORTUNITIES</h1>
    <div class="banner">{{block class="Magento\\Cms\\Block\\Block" block_id="franchise_banner"}}</div>
    <div class="content">
        <div class="content-main">{{block class="Magento\\Cms\\Block\\Block" block_id="franchise_content"}}</div>
        <div class="content-right">{{block class="Magento\\Cms\\Block\\Block" block_id="franchise_sidebar"}}</div>
    </div>
</div>',
                'layout_update_xml' => '<referenceContainer name="catalog.compare.sidebar" remove="true"/>
                                        <referenceContainer name="wishlist_sidebar" remove="true"/>
                                        <referenceContainer name="sidebar.main">
                                            <block class="Magento\Cms\Block\Block" name="cms_nav">
                                                <arguments>
                                                    <argument name="block_id" xsi:type="string">cms_nav</argument>
                                                </arguments>
                                            </block>
                                        </referenceContainer>',
                'is_active' => 1,
                'store_id' => [0],
            ],
            [
                'title' => 'ZipPay',
                'page_layout' => '1column',
                'identifier' => 'zippay',
                'content' => '<div class="block-zipmoney">
<h2 class="title-top">ZIPPAY - ALWAYS INTEREST FREE!</h2>
<div class="block-image"><img src="{{view url="images/banner-zipmoney.jpg"}}" alt="" />
<div class="info">
<p class="p1">Buy Now, Pay Later</p>
<p class="p2">6 Months Interest Free.</p>
</div>
<p class="an"><a>APPLY NOW</a></p>
<img class="logozip" src="{{view url="images/logo-zipmoney1.png"}}" alt="" /></div>
<div class="block wiz">
<h2>What is ZipPay?</h2>
<p>zipPay is a safe, simple and convenient way to pay for your purchase over time.<br /> It provides you with the flexibility to buy today and pay later on an interest-free payment plan.</p>
</div>
<div class="block zipwork">
<h2>How ZipPay works?</h2>
<div class="item">
<p class="number">1</p>
<p class="text">Complete your application and get a decision in minutes</p>
</div>
<div class="item two">
<p class="number">2</p>
<p class="text">ZipPay pays Total Tools on your behalf</p>
</div>
<div class="item">
<p class="number">3</p>
<p class="text">You pay zipPay over time in easy monthly payments</p>
</div>
</div>
<div class="block faq">
<h2>Frequently Asked Questions</h2>
<div class="block-inner">
<div class="title-inner">Who can apply?</div>
<p>Must be 18 years of ageMust be an Australian citizen or a residentMust be employed in some capacity</p>
</div>
<div class="block-inner">
<div class="title-inner">How can I apply?</div>
<p>Click Apply Now and complete the easy 3 minute application. You#39;ll be surprised at how simple the process is.</p>
<p>Once approved and you accept the terms and conditions, zipPay will pay TeamMoto Motorcycles on your behalf.</p>
</div>
<div class="block-inner">
<div class="title-inner">What else should I know?</div>
<p>Although zipPay#39;s interest free plan allows you to shop today without paying anything upfront - it is a loan and must be paid back. Failure to do so could affect your future credit rating.</p>
<p>zipPay#39;s payment plans are interest free for up to 6 months, but please be aware that interest will start to accrue after the interest free period. You can see your interest free balance, how long it is estimated to take to repay the loan and change your payment schedule in your digital wallet.</p>
</div>
<div class="block-inner">
<div class="title-inner">What are the benefits?</div>
<p>You can buy the goods you want today and pay nothing upfront. By paying with your zipPay account, you can split the cost of your purchase and pay over time. zipPay even pays TeamMoto Motorcycles on your behalf no money ever changes hands.</p>
<p>You#39;re also provided with a virtual account so for your next purchase, all you need to do is sign-in to your digital wallet, confirm and pay. There#39;s no more need to use your credit card or seek a loan elsewhere.</p>
<p>It#39;s simple and easy to use.</p>
</div>
<div class="block-inner">
<div class="title-inner">What are the fees?</div>
<p>No deposit, so nothing to pay todayA one off establishment fee of $25 may apply. Don#39;t worry this is added to your interest free balance. Please see your application and contract for details.Minimum monthly payments apply. See your contract and application for details.An interest free period of 6 months, after which interest will accrue at the standard annual percentage rateA small monthly admin fee of $4.95 (only payable if a balance is owing) Please refer to your contract, terms and conditions for further information.</p>
</div>
<div class="block-inner">
<div class="title-inner">Where can I find out more?</div>
<div class="call-us">Call us on <span>(02) 8294 2345</span></div>
<div class="call-us">Email us at <span><EMAIL></span></div>
</div>
<div class="block-inner">
<div class="title-inner">Terms and Conditions</div>
<p>Available to approved applicants only. Minimum monthly repayments are required, see your contract for details. Paying only the minimum monthly repayment amount will not pay out the purchase within the interest free period. Any balance outstanding at the expiry of the interest free period will be charged interest at the standard annual percentage rate, currently 23.9%. A one off establishment fee applies, see your contract for details. A Monthly Account Service Fee of $4.95 (when balance owing) apply. Terms &amp; Conditions apply and are available on application. Credit provided by zipPay Payments Pty Limited (ABN 58 ***********, Australian Credit Licence Number 441878). Visit wwww.zipMoney.com.au to find out more.</p>
</div>
</div>
<div class="block-image last"><img src="{{view url="images/banner-zipmoney.jpg"}}" alt="" />
<div class="info">
<p class="p1">Buy Now, Pay Later</p>
<p class="p2">No deposit, no credit card, 6 Months Interest Free</p>
</div>
<p class="an"><a>CLICK TO APPLY</a></p>
<img class="logozip" src="{{view url="images/logo-zipmoney1.png"}}" alt="" /></div>
</div>',
                'layout_update_xml' => '',
                'is_active' => 1,
                'store_id' => [0],
            ]
        ];
    }


}
