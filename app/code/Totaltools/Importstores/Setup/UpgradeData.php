<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Importstores
 */

namespace Totaltools\Importstores\Setup;

use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magestore\Storelocator\Model\StoreFactory;
use Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory;
use Magestore\Storelocator\Model\ScheduleFactory;
use Totaltools\Importstores\Setup\ImportStore;
use Totaltools\Importstores\Helper\Data;

/**
 * Upgrade Data script
 * @codeCoverageIgnore
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * store locator factory
     *
     * @var \Magestore\Storelocator\Model\StoreFactory
     */
    private $storeLocatorFactory;

    /**
     * store collection
     *
     * @var \Magestore\Storelocator\Model\ResourceModel\Store\Collection
     */
    private $storeCollection;

    /**
     * schedule factory
     *
     * @var \Magestore\Storelocator\Model\ScheduleFactory
     */
    private $scheduleFactory;

    /**
     * importstore
     *
     * @var \Totaltools\Importstores\Setup\Importstore
     */
    protected $importStore;

    /**
     * @var \Totaltools\Importstores\Helper\Data
     */
    protected $helper;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Init
     *
     * @param
     * ProductAttribute $productAttribute
     * EavSetupFactory $eavSetupFactory
     * \Magento\Framework\Logger\Monolog $logger
     */
    public function __construct(
        Data $helper,
        ImportStore $importStore,
        StoreFactory $storeLocatorFactory,
        CollectionFactory $storeCollection,
        ScheduleFactory $scheduleFactory,
        \Magento\Framework\Logger\Monolog $logger
    )
    {
        $this->storeCollection = $storeCollection;
        $this->helper = $helper;
        $this->importStore = $importStore;
        $this->storeLocatorFactory = $storeLocatorFactory;
        $this->scheduleFactory = $scheduleFactory;
        $this->logger = $logger;
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        $version = $context->getVersion();
        if ($context->getVersion() && version_compare($version, '1.0.1') < 0) {
            //$this->updateProductAttribute($setup, $version);
            $stores = $this->importStore->initialStores('1.0.0');

            foreach ($stores as $storeInfo) {
                $storeName = $storeInfo['store_name'];
                $storeCollection = $this->storeCollection->create();
                $store = $storeCollection->addFieldToFilter('store_name', $storeName)->getFirstItem();

                // check schedule for store
                $scheduleInfo = array();
                if (isset($storeInfo['open_moday'])) {
                    $process = $this->helper->processOpeningHours($storeInfo['open_moday']);
                    $scheduleInfo['monday_status'] = $process['status'];
                    if ($process['status'] == 1 ) {
                        $scheduleInfo['monday_open'] = $process['open'];
                        $scheduleInfo['monday_close'] = $process['close'];
                    }
                    unset($storeInfo['open_moday']);
                }
                if (isset($storeInfo['open_tuesday'])) {
                    $process = $this->helper->processOpeningHours($storeInfo['open_tuesday']);
                    $scheduleInfo['tuesday_status'] = $process['status'];
                    if ($process['status'] == 1 ) {
                        $scheduleInfo['tuesday_open'] = $process['open'];
                        $scheduleInfo['tuesday_close'] = $process['close'];
                    }
                    unset($storeInfo['open_tuesday']);
                }
                if (isset($storeInfo['open_wednesday'])) {
                    $process = $this->helper->processOpeningHours($storeInfo['open_wednesday']);
                    $scheduleInfo['wednesday_status'] = $process['status'];
                    if ($process['status'] == 1 ) {
                        $scheduleInfo['wednesday_open'] = $process['open'];
                        $scheduleInfo['wednesday_close'] = $process['close'];
                    }
                    unset($storeInfo['open_wednesday']);
                }
                if (isset($storeInfo['open_thursday'])) {
                    $process = $this->helper->processOpeningHours($storeInfo['open_thursday']);
                    $scheduleInfo['thursday_status'] = $process['status'];
                    if ($process['status'] == 1 ) {
                        $scheduleInfo['thursday_open'] = $process['open'];
                        $scheduleInfo['thursday_close'] = $process['close'];
                    }
                    unset($storeInfo['open_thursday']);
                }
                if (isset($storeInfo['open_friday'])) {
                    $process = $this->helper->processOpeningHours($storeInfo['open_friday']);
                    $scheduleInfo['friday_status'] = $process['status'];
                    if ($process['status'] == 1 ) {
                        $scheduleInfo['friday_open'] = $process['open'];
                        $scheduleInfo['friday_close'] = $process['close'];
                    }
                    unset($storeInfo['open_friday']);
                }
                if (isset($storeInfo['open_saturday'])) {
                    $process = $this->helper->processOpeningHours($storeInfo['open_saturday']);
                    $scheduleInfo['saturday_status'] = $process['status'];
                    if ($process['status'] == 1 ) {
                        $scheduleInfo['saturday_open'] = $process['open'];
                        $scheduleInfo['saturday_close'] = $process['close'];
                    }
                    unset($storeInfo['open_saturday']);
                }
                if (isset($storeInfo['open_sunday'])) {
                    $process = $this->helper->processOpeningHours($storeInfo['open_sunday']);
                    $scheduleInfo['sunday_status'] = $process['status'];
                    if ($process['status'] == 1 ) {
                        $scheduleInfo['sunday_open'] = $process['open'];
                        $scheduleInfo['sunday_close'] = $process['close'];
                    }
                    unset($storeInfo['open_sunday']);
                }

                $storeInfo['country_id'] = 'AU';
                $storeInfo['zoom_level'] = '15';
                $storeInfo['rewrite_request_path'] = $this->helper->processUrlRewrite($storeInfo['store_name']);

                if ($store->getId()) {
                    $scheduleId = $store->getScheduleId();
                    if ($scheduleId) {
                        $schedule = $this->scheduleFactory->create()->load($scheduleId);
                        $scheduleInfo['schedule_id'] = $scheduleId;
                        $schedule->setData($scheduleInfo);
                        $schedule->save();
                    }
                    $storeInfo['schedule_id'] = $scheduleId;
                    $storeInfo['storelocator_id'] = $store->getId();
                    $store->setData($storeInfo);
                    $store->save();

                } else {
                    //creating schedule
                    $scheduleInfo['schedule_name'] = 'Schedule for store '.$storeInfo['store_name'];
                    $schedule = $this->scheduleFactory->create();
                    $schedule->setData($scheduleInfo);
                    $schedule->save();

                    // creating store locator
                    $scheduleLastId = $schedule->getCollection()->getLastItem()->getId();
                    $storeInfo['schedule_id'] = $scheduleLastId;
                    $store = $this->storeLocatorFactory->create();
                    $store->setData($storeInfo);
                    $store->save();
                }

            }
        }

        // ... more version in the case we need update product attribute by another csv

        $setup->endSetup();
    }

    public function updateProductAttribute($setup, $version) {
        // add attributes
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        $attributes = $this->productAttribute->initialProductAttributes($version);

        foreach ($attributes as $attributeCode => $params) {
            $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode);
            $eavSetup->addAttribute(\Magento\Catalog\Model\Product::ENTITY, $attributeCode, $params);
        }
    }

}
