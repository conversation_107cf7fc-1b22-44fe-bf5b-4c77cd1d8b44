<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Importstores
 */

namespace Totaltools\Importstores\Setup;

use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

/**
 * Upgrade the Catalog module DB scheme
 */
class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * {@inheritdoc}
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        if (version_compare($context->getVersion(), '1.0.1', '<')) {

            $setup->getConnection()->addColumn(
                $setup->getTable($setup->getTable('magestore_storelocator_store')),
                'facebook_link',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length' => 255,
                    'nullable' => true,
                    'default' => '',
                    'comment' => 'Facebook link'
                ]
            );

            $setup->getConnection()->addColumn(
                $setup->getTable($setup->getTable('magestore_storelocator_store')),
                'instagram_link',
                [
                    'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length' => 255,
                    'nullable' => true,
                    'default' => '',
                    'comment' => 'Instagram link'
                ]
            );

            $setup->getConnection()->dropColumn(
                $setup->getTable('magestore_storelocator_store'),
                'suburb'
                );
            $setup->getConnection()->dropColumn(
                $setup->getTable('magestore_storelocator_store'),
                'open_monday'
            );
            $setup->getConnection()->dropColumn(
                $setup->getTable('magestore_storelocator_store'),
                'open_tuesday'
            );
            $setup->getConnection()->dropColumn(
                $setup->getTable('magestore_storelocator_store'),
                'open_wednesday'
            );
            $setup->getConnection()->dropColumn(
                $setup->getTable('magestore_storelocator_store'),
                'open_thursday'
            );
            $setup->getConnection()->dropColumn(
                $setup->getTable('magestore_storelocator_store'),
                'open_friday'
            );
            $setup->getConnection()->dropColumn(
                $setup->getTable('magestore_storelocator_store'),
                'open_saturday'
            );
            $setup->getConnection()->dropColumn(
                $setup->getTable('magestore_storelocator_store'),
                'open_sunday'
            );
        }

        $setup->endSetup();
    }
}
