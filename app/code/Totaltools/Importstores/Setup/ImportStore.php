<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Importstores
 */

namespace Totaltools\Importstores\Setup;

class ImportStore
{
    /**
     * @var \Magento\Framework\Filesystem
     */
    protected $fileSystem;

    /**
     * @var \Magento\Framework\Filesystem\DirectoryList
     */
    protected $directoryList;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Init
     *
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        \Magento\Framework\Filesystem $fileSystem,
        \Magento\Framework\Logger\Monolog $logger,
        \Magento\Framework\Filesystem\DirectoryList $directoryList
    )
    {
        $this->fileSystem = $fileSystem;
        $this->logger = $logger;
        $this->directoryList = $directoryList;
    }

    public function initialStores($version)
    {
        $ds = DIRECTORY_SEPARATOR;
        $csvFile = 'app'.$ds.'code'.$ds.'Totaltools'.$ds.'Importstores'.$ds.'data'.$ds.'stores'.$version.'.csv';
        //$path = $this->directoryList->getDefaultConfig();
        $tmpDirectory = $this->fileSystem->getDirectoryRead('base');
        $path = $tmpDirectory->getRelativePath($csvFile);
        $stream = $tmpDirectory->openFile($path);

        $headers = $stream->readCsv();
        if ($headers === false) {
            $stream->close();
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Please use the correct file format for stores.csv.')
            );
        }
        $stores = array();
        try {
            $rowNumber = 1;
            while (false !== ($csvLine = $stream->readCsv())) {
                $rowNumber++;

                if (empty($csvLine)) {
                    continue;
                }

                $attributeInfo = array();
                foreach ($this->matchingAttributes() as $key=>$attribute) {
                    if (isset($csvLine[$key])) {
                        $attributeInfo[$attribute] = $csvLine[$key];
                    }
                }
                $stores[] = $attributeInfo;
            }
        } catch (\Magento\Framework\Exception\LocalizedException $e) {
            $stream->close();
            throw new \Magento\Framework\Exception\LocalizedException(__($e->getMessage()));
        } catch (\Exception $e) {
            $stream->close();
            $this->logger->critical($e);
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Something went wrong while importing csv.')
            );
        }
        return $stores;
    }

    public function matchingAttributes()
    {
        return array(
            'status',
            'store_name',
            'address',
            'city',
            'zipcode',
            'phone',
            'fax',
            'latitude',
            'longitude',
            'open_moday',
            'open_tuesday',
            'open_wednesday',
            'open_thursday',
            'open_friday',
            'open_saturday',
            'open_sunday'
        );
    }


}