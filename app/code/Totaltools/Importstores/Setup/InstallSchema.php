<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Importstores
 */

namespace Totaltools\Importstores\Setup;

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

/**
 * @codeCoverageIgnore
 */
class InstallSchema implements InstallSchemaInterface
{
    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $installer = $setup;

        $installer->startSetup();

        /**
         * add more columns to store locator entity
         */
        $setup->getConnection()->addColumn(
            $setup->getTable($installer->getTable('magestore_storelocator_store')),
            'suburb',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'default' => '',
                'comment' => 'Suburd city'
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable($installer->getTable('magestore_storelocator_store')),
            'open_monday',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'default' => '',
                'comment' => 'Opening hours monday'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable($installer->getTable('magestore_storelocator_store')),
            'open_tuesday',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'default' => '',
                'comment' => 'Opening hours tuesday'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable($installer->getTable('magestore_storelocator_store')),
            'open_wednesday',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'default' => '',
                'comment' => 'Opening hours wednesday'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable($installer->getTable('magestore_storelocator_store')),
            'open_thursday',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'default' => '',
                'comment' => 'Opening hours thursday'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable($installer->getTable('magestore_storelocator_store')),
            'open_friday',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'default' => '',
                'comment' => 'Opening hours friday'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable($installer->getTable('magestore_storelocator_store')),
            'open_saturday',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'default' => '',
                'comment' => 'Opening hours saturday'
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable($installer->getTable('magestore_storelocator_store')),
            'open_sunday',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'default' => '',
                'comment' => 'Opening hours sunday'
            ]
        );
        $installer->endSetup();

    }
}
