<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Importstores
 */

namespace Totaltools\Importstores\Helper;

/**
 * Importstore data helper
 * @SuppressWarnings(PHPMD.TooManyFields)
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * StoreUrlPathGenerator constructor.
     *
     * @param \Magento\Framework\Filter\FilterManager $filterManager
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\Filter\FilterManager $filterManager
    ) {
        $this->_filterManager = $filterManager;
        parent::__construct($context);
    }

    /*
     * process opening hour data
     *
     * @param $value string
     * @return array
     * */
    public function processOpeningHours($value)
    {
        $result = array();
        if ($value == 'Closed') {
            $result['status'] = 2;
        } else {
            $result['status'] = 1;
            $hours = explode('-', $value);
            if(isset($hours[0])) {
                $openHour = $hours[0];
                $openHour = trim($openHour);
                $openHour = str_replace('am','', $openHour);
                $result['open'] = trim($openHour);
            }

            if(isset($hours[1])) {
                $closeHour = $hours[1];
                $closeHour = trim($closeHour);
                //$closeHour = str_replace('pm','', $closeHour);
                $closeHour = date("H:i", strtotime($closeHour));
                $result['close'] = trim($closeHour);
            }
            $result['open']  = str_pad($result['open'], 5, '0', STR_PAD_LEFT);
            $result['close'] = str_pad($result['close'], 5, '0', STR_PAD_LEFT);
        }
        return $result;
    }

    public function processUrlRewrite($storeName)
    {
        $storeLocatorFrontName = 'storelocator';

        $urlkey = str_replace('Total Tools', '', $storeName);
        $urlkey = trim($urlkey);
        $urlkey = $this->_filterManager->translitUrl($urlkey);
        return $storeLocatorFrontName . '/' . $urlkey;
    }
}
