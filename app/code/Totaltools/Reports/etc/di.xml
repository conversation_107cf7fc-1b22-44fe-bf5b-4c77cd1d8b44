<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Sales\Block\Adminhtml\Report\Filter\Form\Order" type="Totaltools\Reports\Block\Adminhtml\Filter\Form"/>
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="productexport-related"
                      xsi:type="object">Totaltools\Reports\Console\Command\GetProductWithRelatedProducts</item>
            </argument>
        </arguments>
    </type>
</config>
