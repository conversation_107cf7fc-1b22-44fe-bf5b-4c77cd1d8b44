<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @package Totaltools_Reports
 */
namespace Totaltools\Reports\Block\Adminhtml\Filter;

class Form extends \Magento\Sales\Block\Adminhtml\Report\Filter\Form\Order
{

    /**
     * Preparing form
     *
     * @return $this
     * @SuppressWarnings(PHPMD.UnusedLocalVariable)
     */
    protected function _prepareForm()
    {
        parent::_prepareForm();
        $form = $this->getForm();
        $htmlIdPrefix = $form->getHtmlIdPrefix();
        /** @var \Magento\Framework\Data\Form\Element\Fieldset $fieldset */
        $fieldset = $this->getForm()->getElement('base_fieldset');

        $dateFormat = $this->_localeDate->getDateFormat(
            \IntlDateFormatter::SHORT
        );

        if (is_object($fieldset) && $fieldset instanceof \Magento\Framework\Data\Form\Element\Fieldset) {
            $fieldset->removeField('from');
            $fieldset->removeField('to');
            $fieldset->addField(
                'from',
                'date',
                [
                    'name' => 'from',
                    'date_format' => $dateFormat,
                    'label' => __('From'),
                    'title' => __('From'),
                    'required' => true,
                    'class' => 'admin__control-text'
                ],
                'period_type'

            );

            $fieldset->addField(
                'to',
                'date',
                [
                    'name' => 'to',
                    'date_format' => $dateFormat,
                    'label' => __('To'),
                    'title' => __('To'),
                    'required' => true,
                    'class' => 'admin__control-text'
                ],
                'from'
            );
        }

        return $this;
    }
}