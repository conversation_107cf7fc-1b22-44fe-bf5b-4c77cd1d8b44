<?php

namespace Totaltools\Reports\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Catalog\Model\ResourceModel\Product\CollectionFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\TargetRule\Model\Cache\Index as IndexCache;
use Magento\Framework\Serialize\SerializerInterface;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\App\{State, Area};

class GetProductWithRelatedProducts extends Command
{
    /**
     * @var State
     */
    protected $state;

    /**
     * @var \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory
     */
    protected $_productCollectionFactory;

    /**
     * @var IndexCache
     */
    private $cache;

    /**
     * TargetRule Index instance
     *
     * @var \Magento\TargetRule\Model\Index
     */
    protected $_currentIndex;

    /**
     * @var \Magento\TargetRule\Model\IndexFactory
     */
    protected $_indexFactory;

    /**
     * @var \Magento\Catalog\Model\ProductFactory
     */
    protected $productFactory;

    /**
     * GetProductWithRelatedProducts constructor.
     * @param CollectionFactory $_productCollectionFactory
     * @param IndexCache $cache
     * @param SerializerInterface|null $serializer
     * @param State $state
     * @param \Magento\TargetRule\Model\IndexFactory $indexFactory
     * @param \Magento\Catalog\Model\ProductFactory $productFactory
     * @param null $name
     */
    public function __construct(
        CollectionFactory $_productCollectionFactory,
        IndexCache $cache,
        SerializerInterface $serializer = null,
        State $state,
        \Magento\TargetRule\Model\IndexFactory $indexFactory,
        \Magento\Catalog\Model\ProductFactory $productFactory,
        $name = null
    ) {
        parent::__construct($name);
        $this->_productCollectionFactory = $_productCollectionFactory;
        $this->cache = $cache;
        $this->state = $state;
        $this->_indexFactory = $indexFactory;
        $this->productFactory = $productFactory;
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName("totaltools:productexport:related");
        $this->setDescription("Total tools product export with related product SKUs");
        parent::configure(); // TODO: Change the autogenerated stub
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void|null
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $dataArray[] = ['sku', 'entity_id', 'frequently_product_skus'];

        try {
            $collection = $this->getEnabledProducts();
            foreach ( $collection as $item) {
                $loadedProduct = $this->productFactory->create()->load($item->getId());
                $indexModel = $this->_getTargetRuleIndex()->setType(
                    1
                )->setLimit(
                    20
                )->setProduct(
                    $loadedProduct
                )->setExcludeProductIds(
                    []
                );

                $relatedSku= [];
                $relatedSkuString= '';
                if(!empty($indexModel->getProductIds())) {
                    foreach ($indexModel->getProductIds() as $productId => $val) {
                        $relatedSku[]  = $this->productFactory->create()->load($productId)->getSku();
                    }
                    $relatedSkuString = implode(",",$relatedSku);
                }

                $dataArrayPush= [(string)$loadedProduct->getSku(),(string)$loadedProduct->getId(),$relatedSkuString];
                $dataArray[] = $dataArrayPush;

            }

            // Open a file in write mode ('w')
            $fp = fopen('var/product-export.csv', 'w');

            // Loop through file pointer and a line
            foreach ($dataArray as $fields) {
                fputcsv($fp, $fields);
            }

            fclose($fp);
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
        }
    }

    /**
     * @return Operation|null
     */
    protected function getEnabledProducts()
    {
        $collection = $this->_productCollectionFactory->create()
                            ->addAttributeToSelect('*')
                           // ->addAttributeToFilter('entity_id', array('eq' => 26104))
                            ->addAttributeToFilter('status', array('eq' => 1));
        return $collection;
    }

    /**
     * Retrieve Target Rule Index instance
     *
     * @return \Magento\TargetRule\Model\Index
     */
    protected function _getTargetRuleIndex()
    {
        if ($this->_currentIndex === null) {
            $this->_currentIndex = $this->_indexFactory->create();
        }
        return $this->_currentIndex;
    }

}
