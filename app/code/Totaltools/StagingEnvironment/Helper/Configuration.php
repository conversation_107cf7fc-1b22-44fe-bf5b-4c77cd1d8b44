<?php

namespace Totaltools\StagingEnvironment\Helper;

use Magento\Framework\Filesystem\DirectoryList;
use Magento\Framework\Module\Dir\Reader;
use Symfony\Component\Yaml\Parser;

class Configuration
{

    const MODULE_NAME = 'Totaltools_StagingEnvironment';
    const CONFIG_YML_FILE = 'config.yml';
    const USERS_YML_FILE = 'users.yml';
    const INTEGRATION_YML_FILE = 'integration.yml';

    /**
     * @var Reader
     */
    protected $moduleDirReader;

    /**
     * @var DirectoryList
     */
    protected $magentoDirReader;

    /*
     * @var Parser
     */
    protected $parser;

    /**
     * Configuration constructor.
     *
     * @param Reader        $moduleDirReader
     * @param DirectoryList $magentoDirReader
     */
    public function __construct(
        Reader $moduleDirReader,
        DirectoryList $magentoDirReader
    ) {
        $this->moduleDirReader = $moduleDirReader;
        $this->magentoDirReader = $magentoDirReader;
    }

    /**
     * Get the configuration.
     *
     * @param string $environment
     *
     * @return array
     */
    public function getConfiguration($environment = null)
    {
        return $this->getMergedConfig($environment);
    }

    /**
     * @param null $environment
     *
     * @return array
     */
    public function getAdminUsers($environment = null)
    {
        return $this->getMergedConfig($environment, static::USERS_YML_FILE);
    }

    /**
     * @param null $environment
     *
     * @return array
     */
    public function getIntegrations($environment = null)
    {
        return $this->getMergedConfig($environment, static::INTEGRATION_YML_FILE);
    }

    /**
     * Get some merged config.
     *
     * @param null   $environment
     * @param string $file
     *
     * @return array
     */
    protected function getMergedConfig($environment = null, $file = self::CONFIG_YML_FILE)
    {
        $config = array_replace_recursive($this->getDefaultConfiguration($file), $this->getPrivateConfiguration($file));

        if (is_null($environment)) {
            return $config;
        }

        if (isset($config[$environment]) && is_array($config[$environment])) {
            return $config[$environment];
        }

        return [];
    }

    /**
     * Get the default configuration.
     *
     * @param string $file
     *
     * @return array
     */
    protected function getDefaultConfiguration($file = self::CONFIG_YML_FILE)
    {
        $filePath = $this->moduleDirReader->getModuleDir('etc', static::MODULE_NAME) . '/' . $file;

        return $this->parseFile($filePath);
    }

    /**
     * Get the private configuration.
     *
     * @param string $file
     *
     * @return array
     */
    protected function getPrivateConfiguration($file = self::CONFIG_YML_FILE)
    {
        $filePath = $this->magentoDirReader->getPath('var') . DIRECTORY_SEPARATOR . static::MODULE_NAME . DIRECTORY_SEPARATOR . $file;

        return $this->parseFile($filePath);
    }

    /**
     * Parse file or fail silently if file does not exist.
     *
     * @param string $filePath
     *
     * @return array
     */
    protected function parseFile($filePath)
    {
        if (!file_exists($filePath)) {
            return [];
        }

        return $this->getParser()->parse(file_get_contents($filePath));
    }

    /**
     * Magento is instantiating the parser incorrectly no don't use di/object manager here.
     */
    protected function getParser()
    {
        if (!$this->parser) {
            $this->parser = new Parser();
        }

        return $this->parser;
    }

}