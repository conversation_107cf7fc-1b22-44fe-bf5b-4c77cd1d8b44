<?php

namespace Totaltools\StagingEnvironment\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\Framework\Acl\AclResource\ProviderInterface;

class ShowAllApiResources extends Command
{

    const KEY_ENVIRONMENT = 'environment';
    const NAME = 'totaltools:show-all-api-resources';
    const DESCRIPTION = 'Show all api resources.';

    /**
     * @var ProviderInterface
     */
    protected $aclResourceProvider;


    /**
     * @param ProviderInterface $aclResourceProvider
     */
    public function __construct(
        ProviderInterface $aclResourceProvider
    )
    {
        $this->aclResourceProvider = $aclResourceProvider;
        parent::__construct();
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this
            ->setName(static::NAME)
            ->setDescription(static::DESCRIPTION);

        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output) : int
    {
        $exitCode = 0;
        try {
            $resources = $this->getAllAclResources();
            $this->outputResources($resources, $output);

        }
        catch (\Exception $e) {
            $output->writeln(sprintf(
                '<error>%s</error>',
                $e->getMessage()
            ));
            $exitCode = 1;
        }
        return $exitCode;
    }

    /**
     * @param array $resources
     * @param $output
     * @return void
     */
    private function outputResources(array $resources, $output, $level = 0) {
        foreach ($resources as $resource) {
            if(is_array($resource)) {
                if (isset($resource['children']) && is_array($resource['children']) && !empty($resource['children'])) {
                    $this->outputResources($resource['children'], $output, $level + 4);
                }
                else {
                    $str = str_pad('', $level, ' ', STR_PAD_LEFT);
                    $output->writeln($str . $this->getElementText($resource));
                }
            }
            else {
                $output->writeln($resource);
            }

        }
    }

    private function getElementText(array $resource) {
        $txt = $resource['id'] ?: '';
        if (isset($resource['title'])) {
            $txt .= ': ' . $resource['title'];
        }
        return $txt;
    }

    /**
     * @return array|mixed|string
     */
    private function getAllAclResources()
    {
        $resources = $this->aclResourceProvider->getAclResources();
        $configResource = array_filter(
            $resources,
            function ($node) {
                return isset($node['id'])
                    && $node['id'] == 'Magento_Backend::admin';
            }
        );
        $configResource = reset($configResource);
        return isset($configResource['children']) ? $configResource['children'] : [];
    }
}
