<?php

namespace Totaltools\StagingEnvironment\Console\Command;

use Magento\Framework\App\Config\ConfigResource\ConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Integration\Model\Oauth\ConsumerFactory;
use Magento\Integration\Model\Oauth\TokenFactory as TokenFactory;
use Magento\Integration\Model\OauthService;
use Magento\User\Model\UserFactory;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputArgument;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\StagingEnvironment\Helper\Configuration;
use Magento\Authorization\Model\Acl\Role\Group as RoleGroup;
use Magento\Authorization\Model\RoleFactory;
use Magento\Authorization\Model\RulesFactory;
use Magento\Authorization\Model\UserContextInterface;
use Magento\Integration\Api\IntegrationServiceInterface;
use \Magestore\Storelocator\Model\ResourceModel\Store\CollectionFactory;
use \Magento\Framework\App\Config\Storage\WriterInterface;
use \Magento\Framework\App\Config\ScopeConfigInterface;
use \Xtento\ProductExport\Model\Profile as ProductProfile;
use \Xtento\OrderExport\Model\Profile as OrderProfile;
use \Xtento\OrderExport\Model\Destination as OrderDestination;
use \Xtento\ProductExport\Model\Destination as ProductDestination;

class SetEnvironmentCommand extends Command
{

    const KEY_ENVIRONMENT = 'environment';
    const NAME = 'totaltools:set:environment';
    const DESCRIPTION_ENVIRONMENT = 'The environment you would like to run. For integration creation, it will look for "resource" node in the integration.yml file, if nothing is defined there, access ALL will be assigned. to see all resource tobe added to integration.yml, use totaltools:show-all-api-resources command';
    const DESCRIPTION = 'Update the configuration for Magento.';

    /**
     * @var Configuration
     */
    protected $environmentConfig;

    /**
     * @var ConfigInterface
     */
    protected $config;

    /**
     * @var ResourceConnection
     */
    protected $resourceConnection;

    /**
     * @var UserFactory
     */
    protected $userFactory;

    /**
     * @var RoleFactory
     */
    protected $roleFactory;

    /**
     * @var RulesFactory
     */
    protected $rulesFactory;

    /**
     * @var IntegrationService
     */
    protected $integrationService;

    protected $consumerFactory;

    protected $tokenFactory;

    protected $oauthService;

    protected $storeCollection;

    protected  $configWriter;

    protected $productProfile;

    protected $orderProfile;

    protected $productDestination;

    protected $orderDestination;


    /**
     * SetEnvironmentCommand constructor.
     *
     * @param ConfigInterface $config
     * @param Configuration $environmentConfig
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        ConfigInterface $config,
        Configuration $environmentConfig,
        ResourceConnection $resourceConnection,
        UserFactory $userFactory,
        RoleFactory $roleFactory,
        RulesFactory $rulesFactory,
        ConsumerFactory $consumerFactory,
        TokenFactory $tokenFactory,
        IntegrationServiceInterface $integrationService,
        OauthService $oauthService,
        CollectionFactory $storeCollectionFactory,
        WriterInterface $configWriter,
        ProductProfile $productProfile,
        OrderProfile  $orderProfile,
        ProductDestination $productDestination,
        OrderDestination $orderDestination
    )
    {
        $this->config = $config;
        $this->environmentConfig = $environmentConfig;
        $this->resourceConnection = $resourceConnection;
        $this->productDestination = $productDestination;
        $this->orderDestination = $orderDestination;

        parent::__construct();
        $this->userFactory = $userFactory;
        $this->roleFactory = $roleFactory;
        $this->rulesFactory = $rulesFactory;
        $this->integrationService = $integrationService;
        $this->consumerFactory = $consumerFactory;
        $this->oauthService = $oauthService;
        $this->storeCollection = $storeCollectionFactory;
        $this->configWriter = $configWriter;
        $this->productProfile = $productProfile;
        $this->orderProfile = $orderProfile;
    }

    /**
     * {@inheritdoc}
     */
    protected function configure()
    {
        $this
            ->setName(static::NAME)
            ->setDescription(static::DESCRIPTION)
            ->addArgument(
                static::KEY_ENVIRONMENT,
                InputArgument::REQUIRED,
                static::DESCRIPTION_ENVIRONMENT
            );

        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int
     */
    protected function execute(InputInterface $input, OutputInterface $output) : int
    {
        $exitCode = 0;
        $environment = $input->getArgument(static::KEY_ENVIRONMENT);
        try {
            foreach ($this->environmentConfig->getConfiguration($environment) as $storeScope => $values) {

                $storeScope = explode('-', $storeScope);
                $scope = $storeScope[0];
                $scopeId = 0;
                if (isset($storeScope[1])) {
                    $scopeId = $storeScope[1];
                }

                if (isset($storeScope[0]) && $storeScope[0] == '0') {
                    $scope = \Magento\Store\Model\ScopeInterface::SCOPE_STORES; //ScopeConfigInterface::SCOPE_TYPE_DEFAULT;
                    $scopeId = 0;
                }
                if (isset($storeScope[0]) && $storeScope[0] == '1') {
                    $scope = \Magento\Store\Model\ScopeInterface::SCOPE_STORES; //ScopeConfigInterface::SCOPE_TYPE_DEFAULT;
                    $scopeId = 1;
                }
                if (isset($storeScope[0]) && $storeScope[0] == '2') {
                    $scope = \Magento\Store\Model\ScopeInterface::SCOPE_STORES;  //ScopeConfigInterface::SCOPE_TYPE_DEFAULT;
                    $scopeId = 2;
                }
                if (isset($storeScope[0]) && $storeScope[0] == '3') {
                    $scope = \Magento\Store\Model\ScopeInterface::SCOPE_STORES; //ScopeConfigInterface::SCOPE_TYPE_DEFAULT;
                    $scopeId = 3;
                }
                if (isset($storeScope[0]) && $storeScope[0] == '4') {
                    $scope = \Magento\Store\Model\ScopeInterface::SCOPE_STORES; //ScopeConfigInterface::SCOPE_TYPE_DEFAULT;
                    $scopeId = 4;
                }


                foreach ($values as $path => $value) {
                    if (is_null($value)) {
                        $this->config->deleteConfig($path, $scope, $scopeId);
                        continue;
                    }
                    $this->configWriter->save($path, $value, $scope, $scopeId);
                }
            }

            //Disable Product Export Profiles
            $productProfiles = $this->productProfile->getCollection()->load();
            foreach ($productProfiles as $productProfile) {
                $productProfile->setEnabled(0)->setDestinationIds('1&')->save();
            }

            // Disable Order Export Profiles
            $orderProfiles = $this->orderProfile->getCollection()->load();
            foreach ($orderProfiles as $orderProfile) {
                $orderProfile->setEnabled(0)->setDestinationIds('2&')->save();
            }

            $this->stripProductDestinations();
            $this->stripOrderDestinations();

            $role = $this->roleFactory->create();
            $role = $role->load('Administrators', 'role_name');
            if (!$role->getId()) {
                $role->setName('Administrators')
                    ->setPid(0)
                    ->setRoleType(RoleGroup::ROLE_TYPE)
                    ->setUserType(UserContextInterface::USER_TYPE_ADMIN);

                $role->getResource()->save($role);
            }
            $resource = [
                'Magento_Backend::all'
            ];

            $this->rulesFactory->create()->setRoleId($role->getId())->setResources($resource)->saveRel();
            $userIds = array();


            foreach ($this->environmentConfig->getAdminUsers($environment) as $adminUser) {
                /** @var \Magento\User\Model\User $user */
                $user = $this->userFactory->create();
                $user->loadByUsername($adminUser['username']);
                if ($user->getId()) {
                    continue;
                }

                $user->setData($adminUser);
                $user->setIsActive(true);
                $user->setRoleId($role->getId());
                $user->getResource()->save($user);
                $userIds[] = $user->getId();
            }


            if ($environment == 'staging') {
                $this->setWarehouseDataForTestStores();
                $this->setStagingConfigurations();
            }

            $prefix = '';
            if ($environment == 'staging') {
                $prefix = 'STG';
            } elseif ($environment == 'staging2') {
                $prefix = 'STG2';
            } elseif ($environment == 'staging3') {
                $prefix = 'STG3';
            } elseif ($environment == 'integration1') {
                $prefix = 'INT1';
            } elseif ($environment == 'integration2') {
                $prefix = 'INT2';
            }

            if ($prefix !== '') {
                // change order prefix
                $connection = $this->resourceConnection->getConnection();
                $sql1 = "UPDATE sales_sequence_profile SET prefix='" . $prefix . "' WHERE profile_id IN (1,2,3,4,6,7,8,9)";
                $connection->query($sql1);

                $prefix2 = $prefix . '.2';
                $sql2 = "UPDATE sales_sequence_profile SET prefix='" . $prefix2 . "' WHERE profile_id IN (11,12,13,14)";
                $connection->query($sql2);

                $prefix3 = $prefix . '.3';
                $sql3 = "UPDATE sales_sequence_profile SET prefix='" . $prefix3 . "' WHERE profile_id IN (16,17,18,19)";
                $connection->query($sql3);

                $prefix4 = $prefix . '.4';
                $sql4 = "UPDATE sales_sequence_profile SET prefix='" . $prefix4 . "' WHERE profile_id IN (21,22,23,24)";
                $connection->query($sql4);

                $prefix5 = $prefix . '.5';
                $sql5 = "UPDATE sales_sequence_profile SET prefix='" . $prefix5 . "' WHERE profile_id IN (26,27,28,29)";
                $connection->query($sql5);

                $sql6 = "UPDATE core_config_data AS target JOIN (SELECT config_id FROM core_config_data 
                    WHERE value LIKE '%@totaltools.com%') AS source ON target.config_id = source.config_id 
                    SET target.value = '<EMAIL>'";
                $connection->query($sql6);

                if ($environment == 'staging') {
                    $sql7 = "DELETE FROM usimplelic_license";
                    $connection->query($sql7);

                    $sql8 = "INSERT INTO usimplelic_license (license_key,license_status,last_checked,last_status,last_error,retry_num,products,server_restriction,server_restriction1,server_restriction2,license_expire,upgrade_expire,signature,server_info,aux_checksum) VALUES
	 ('SRFL9-D67DR-18LNF-29TRK-MJV1S','active','2023-07-31 07:45:04','success',NULL,0,'Unirgy_RapidFlow
Unirgy_RapidFlowPro','*@127.0.0.1
*.totaltools.com.au
totaltools.com.au
*.totaltools.balancenet.com.au
totaltools.balancenet.com.au
*.dev-totaltools.balancenet.com.au
dev-totaltools.balancenet.com.au
*.dev02.totaltools.com.au
dev02.totaltools.com.au
*.dev03-totaltools.balancenet.com.au
dev03-totaltools.balancenet.com.au
 *.uat.totaltools.com.au
uat.totaltools.com.au
*.dev05-totaltools.balancenet.com.au
dev05-totaltools.balancenet.com.au',NULL,NULL,NULL,'2023-03-18 00:00:00','c3ac427ecf32ef0417b7aae69d702e23117cd16e','Domain: mcstaging-admin.totaltools.com.au
Server IP: none

Adapter: lo
IP: 127.0.0.1
MAC: {00:00:00:00:00:00}

Adapter: eth0
IP: *************
MAC: {02:a7:92:fb:61:0c}

',456694144);";
                    $connection->query($sql8);
                }
                $sql9 = "DELETE FROM magento_scheduled_operations";
                $connection->query($sql9);
            }


            foreach ($this->environmentConfig->getIntegrations($environment) as $integration) {
                $integrationData = array();
                $integrationData['name'] = $integration['name'];
                $integrationData['status'] = $integration['status'];
                if (!isset($integration['resource'])) {
                    $integrationData['all_resources'] = 1;
                }
                $integrationEntry = $this->integrationService->findByName($integration['name']);
                $cleanExistingToken = false;
                if (!$integrationEntry->getId()) {
                    $integrationEntry = $this->integrationService->create($integrationData);
                }
                else {
                    $cleanExistingToken = true;
                    $integrationData['integration_id'] = $integrationEntry->getId();
                    $this->integrationService->update($integrationData);
                }
                $successful = $this->oauthService->createAccessToken($integrationEntry->getConsumerId(), $cleanExistingToken);
                if ($successful) {
                    $token = $this->oauthService->getAccessToken($integrationEntry->getConsumerId());
                    $token->setSecret($integration['token_secret']);
                    $token->setToken($integration['token_token']);
                    // $token->setV($integrationData['token_secret']);
                    $token->save();
                }
                $consumer = $this->oauthService->loadConsumer($integrationEntry->getConsumerId());
                $consumer->setSecret($integration['consumer_secret']);
                $consumer->setKey($integration['consumer_key']);
                $consumer->save();
            }

            $this->stripStoreEmailAddress();

            $connection = $this->resourceConnection->getConnection();

            $connection->update(
                $connection->getTableName('cms_block'),
                ['content' => ''],
                '`identifier` = "google_tag_manager"'
            );
        }
        catch (\Exception $e) {
            $output->writeln(sprintf(
                '<error>%s</error>',
                $e->getMessage()
            ));
            $exitCode = 1;
        }
        return $exitCode;
    }

    /**
     * update store email addresses
     */
    protected function stripStoreEmailAddress()
    {
        $collection = $this->storeCollection->create();
        foreach ($collection as $item) {
            $formattedEmail = 'totaltoolsdev+'.$item->getErpId().'@gmail.com';
            $item->setEmail($formattedEmail)->setStoreAdminEmail($formattedEmail)
                ->setCommercialStoreAdminEmail($formattedEmail)->save();
        }
    }

    /**
     * update product des
     */
    protected function stripProductDestinations()
    {
        $collection = $this->productDestination->getCollection()->load();
        $keepIds = [1,3];
        foreach ($collection as $item) {
            if(!in_array($item->getId(), $keepIds)) {
                $item->delete();
            }
        }
    }

    /**
     * update order des
     */
    protected function stripOrderDestinations()
    {
        $collection = $this->orderDestination->getCollection()->load();
        $keepIds = [2];
        foreach ($collection as $item) {
            if(!in_array($item->getId(), $keepIds)) {
                $item->delete();
            }
        }
    }

    /**
     * Update warehouse data
     */
    protected function setWarehouseDataForTestStores()
    {
        $warehouseMapping = ['AUN'=>'064',
            'BEE'=>'089','BEN'=>'055','BNB'=>'085','BRE'=>'077','BRK'=>'086','BRO'=>'075','BUN'=>'051',
            'BUR'=>'058','CAI'=>'071','CAN'=>'068','CAS'=>'091','DAR'=>'052','EBR'=>'069','ELI'=>'061',
            'FYS'=>'038','GOS'=>'081','HOB'=>'079','JOO'=>'063','LAU'=>'084','MAL'=>'082','MAN'=>'070',
            'MDR'=>'049','MID'=>'060','MIT'=>'053','MOR'=>'083','MRY'=>'090','MTN'=>'039','MTW'=>'045',
            'NAR'=>'050','NOO'=>'074','NOR'=>'087','OCO'=>'066','PTM'=>'072','ROC'=>'059','SHP'=>'044',
            'SPR'=>'062','SUN'=>'054','TAM'=>'080','TAR'=>'067','TBL'=>'031','TBR'=>'004','TBS'=>'006',
            'TBW'=>'034','TCD'=>'030','TCR'=>'021','TDA'=>'014','TFG'=>'018','TGE'=>'029','THC'=>'027',
            'TKE'=>'057','TKS'=>'026','TLO'=>'016','TMD'=>'037','TMK'=>'046','TMN'=>'035','TMR'=>'012',
            'TPA'=>'013','TPE'=>'036','TPR'=>'017','TSM'=>'032','TSV'=>'040','TTA'=>'020','TTB'=>'033',
            'TTE'=>'003','TTP'=>'005','TTR'=>'015','TTU'=>'019','TTW'=>'023','TWA'=>'007','VIR'=>'073',
            'WAR'=>'065','WIN'=>'043','WLG'=>'048','SAN'=>'094','BAN'=>'097','MTB'=>'093','RIC'=>'095',
            'WNG'=>'098','MAR'=>'099','PRS'=>'104','RCK'=>'103','BLA'=>'100','HOR'=>'105','BND'=>'106',
            'BAT'=>'107','MIN'=>'109','MER'=>'110','CAP'=>'111','WON'=>'112','OSB'=>'113','LKH'=>'114'];

        $shippintKeyMapping = ['594'=>'2W6MKlUUcfh2Cb_ZfCcO1g','595'=>'f7NgZZr84hPtuj0Sgbrs-Q','596'=>'BzhAxcXT5bFksAAHGcWaXw','597'=>'SnxkUHc3OAuEcp64NHXVfw','598'=>'9zhzFleVL75y8YSJ0__ORA','599'=>'6l_lbQBJFZzvTT5E32CW4w','600'=>'FxIQvoz17uoUOS56TW47-A','601'=>'Q9SBS5XJfP67T8Roi7V5AQ','602'=>'XVtTyz9mCpGNMYibM5voMQ','603'=>'VDKXMEMGPUviOmWRfbtcCQ','604'=>'1enJ2aijlLDSanjeQr8dXA','605'=>'mzJzJWspPtpG40OWGvArTA','606'=>'qzS1fDqUX2JLxj8783c5MQ','607'=>'jKV8Y2QwUFtqWTi_Cc9VJg','608'=>'zq950YZasxQUu86dUKW3_w','609'=>'5-bk2OqswFYw1qI9VzHGFg','610'=>'cd5cG8AJVJ8JXz9iG2Fqpw','611'=>'ub4Ee76Q4yOJ77pUVt16KQ','612'=>'4-OCtbfn2sf41Ek_gdkHFg','613'=>'_x4LU0XVGhlnozqht58nKA','614'=>'_PwKF-VjVEChL_fAUU04lQ','615'=>'3NwgyrWVrSUDwK2yUF4MGA','616'=>'hgCcTdiRcgFg7ECORBjIfA','617'=>'dnQtEDu19R7u-XYF2wq-Cw','618'=>'KRyvpOzyN0Plgvs6f0n9Nw','619'=>'A6jQ6JWkae7yjhO2qEhO3Q','620'=>'TIrkm0jYmUC5bpKrQyIo3w','621'=>'Md0Mvzcay1M4z4tkmjmi7w','622'=>'PHHl7MpOS0SZ8tzJm6_u3A','623'=>'tpr3iRKsJO1wBtLjYpD0iQ','624'=>'wE4Fox6ONSRreieLGHP9Ig','625'=>'0ktZ8_OMXK1RGWFe6sN0RA','626'=>'ZyJa1psuddq3OU0gItqDUg','627'=>'CJmzbIzlCFY2hsKJJzo6Vg','628'=>'ruZvUH7f289GnGSfiWYHSQ','629'=>'oMm-IHKtxPDxHyW3qzJrrQ','630'=>'H8Bbwj6K7B1grOM1nKcY_Q','631'=>'nQv2gvuNaOJ-GhpuNNCRyw','632'=>'ChfYvi3SiWV-l19XEHII0w','633'=>'6SmjxcyiTkA6BHW5H5r2EQ','634'=>'8vCrIdvZ7rGJHxhbm_yV0Q','635'=>'7if6Bz41_NCvGFt0SSa21Q','636'=>'Ul-ntnyUJWP1r0gvL-79mA','637'=>'Asx-BACU_M3qM2V9GH8vsA','638'=>'Qem5WOfP3mSaDHgiQqTKYQ','639'=>'MX6e1eUhW6niNGDsqNcBzg','640'=>'MyRIL6-0OMPbCoLfZ9puIA','641'=>'K-pz2NUXkdAKHo5-SM4g1Q','642'=>'r0QbZluShW4UTf9iWSh_zw','643'=>'KzxVqlnNaH8TDyHF4S22og','644'=>'51eXcjrdUSEGPWD4jUc1EQ','645'=>'GFcoI79nTXmFcQ-Ei1YN9Q','646'=>'tHMVUhHOrwWsGkn1v_PCyQ','647'=>'QtmVtesn1u0-qtF2yZjESA','648'=>'vBFfqzgpnxXxlOauLoWjWg','649'=>'lV6Qsl58MEGrMZx_uohr1Q','650'=>'Qp4_Ug260eM3GQdAjj-EsQ','651'=>'k5Ky3KC8_OQ5n-dEGmNBRQ','652'=>'5vLki-Iq0YNQSKqgwgsFUw','653'=>'G-VWYZf0qdPsGKvgUD4Q4w','654'=>'aUY3NBKUrRYJgeMQqYWM-A','655'=>'n10jMqWMz3Zspy2iHJYGQQ','656'=>'yMrG07Yq6U1JT6uT_9xXvQ','657'=>'7OhJQCO88veZrUeu4IGAfw','658'=>'414DWaW3_6dbqV5TV0A1aA','659'=>'xyTnpTudFiQNB-kf41RsIQ','660'=>'eBq8yiHBLhwaQaQiaMI78w','661'=>'n5AeLv0bUt-1vzw4I6h_MA','662'=>'eW67vgmbdcaouteAXvlfEw','663'=>'b9zriJ5QX4nCiI-aCyufFg','664'=>'sbXznyUz7QtSeC-TE0EDZg','665'=>'TEAB-G-Mx4JUcDbywZ5BoQ','666'=>'nplNxobbnf6GFpfQZmB2DA','667'=>'EwQMumcmGpzKxmSqawBADA','668'=>'BZfzqARJxuOa9H3v-n-2aQ','669'=>'uE6gEKtQLM_GO2YAK1LbOA','670'=>'nDge754Kmyt3jF9uKghtQQ','671'=>'nBmdCr1c3YqLDhlJ9vO1UA','672'=>'jBgvrrHZEHKteYCYk8Gg0w','673'=>'eaORLxIc4bDBkUDZdEngBQ','676'=>'0xmWL4FCySR4m4qmqPiqBA','677'=>'dV1GAkW8cWfTGrBCiekP8w','678'=>'iSGb9WheaPJWAX9kUrtiXQ','679'=>'kSy1f00AY7uITIaCoexc3w','680'=>'z6r-RHAyx1QwnN3XmnxBdw','681'=>'pfqPOtaJzwFnNp7qmdVcMA','682'=>'nEXtBY3kZyf1R26IpkLJug','683'=>'y42_8xxu1fU8XMLlKIDuQw','684'=>'Cnf9nvuFm2wP9VPKivY8XQ','685'=>'oUitmh8yF9V6Cdu9i-I5_g','686'=>'DsQ-KRSAHro0nX6bl7JkjA','687'=>'MgYCH5mdqf0ati_Lz_Jlsg','688'=>'2nbrcfdjn00OUhQx3Llxpw','689'=>'8cdtP63MwqrzpckwXy2j6Q','692'=>'reYnCaWOWxTKMdLqFF14dA','695'=>'RbG7whGimFX3AvU8HTNrqQ','698'=>'lGSPL0Ctskx665mNnCQXAA','699'=>'cybYYS8KvEuPLzfELl1OFw','700'=>'AqCY5JV2ffhXlBSfOFkIDg','701'=>'N2Vl9sO0MGkYBMWc3UsrIQ','702'=>'SuCLzTeDRODTlY1xd5B1GA','703'=>'frTQ6Jhdfaz4ichFi2oxng','704'=>'NLbWwHnVuJOVRE4MPmgA3w'];

        $collection = $this->storeCollection->create();
        foreach ($collection as $item) {
            if(isset($warehouseMapping[$item->getErpCode()])) {
                $testStore = $warehouseMapping[$item->getErpCode()];
                $item->setErpCode($testStore)
                    ->setWarehouseCode($testStore)
                    ->save();
            }
            if(isset($shippintKeyMapping[$item->getStorelocatorId()])) {
                $testStoreApiKey = $shippintKeyMapping[$item->getStorelocatorId()];
                $warehouseCode = $item->getWarehouseCode();
                $fullSohApiEndpoint = 'https://totaltools-xi-test-03.prontohosted.com.au/pronto/rest/042.onlinesoh/api/fullsohv2/' . $warehouseCode;
                $deltaSohApiEndpoint = 'https://totaltools-xi-test-03.prontohosted.com.au/pronto/rest/042.onlinesoh/api/SOHChangedTodayv2/' . $warehouseCode;

                $item->setShippitApiKey($testStoreApiKey)
                    ->setShippitApiKeySecond($testStoreApiKey)
                    ->setFullSohApiEndpoint($fullSohApiEndpoint)
                    ->setDeltaSohApiEndpoint($deltaSohApiEndpoint)
                    ->save();
            }
        }
    }

    /**
     * Set Staging Configurations
     */
    protected function setStagingConfigurations()
    {
        $configurations = [
            'ec/general/license' => 'OjVOSGeuRXZvYl8BQ4K23CULmuLzmdKzHG87c/XoQBLpLQj8rbclO2OXDZGBlet12ztlWLxwKnSNMpXQSbvoZg==',
            'ec4/general/license' => 'xes6YzcsMGXjDTfhXc/3fnA7GJ4Rs1/leSpGgfsPS2dSSKV36bblAVWGLaFH36Crv6zPFy8vzjAXBKb+hySbqg==',
            'storelocator/trade_reward/store_account_form_path' => '/app/izd7dnu5hj7jm_stg/var/integrations/totaltools/trade_rewards/EOI-Store-Account-Trade-Rewards-Program.xlsx',
            'storelocator/trade_reward/commercial_account_form_path' => '/app/izd7dnu5hj7jm_stg/var/integrations/totaltools/trade_rewards/EOI-Commercial-Account-Trade-Rewards-Program.xlsx',
            'totaltools_loqate/settings/licence_key' => 'total_tools_test_user:nMDxlvemOViThPNSJcLEolKnWoFnZvPb'
        ];

        foreach ($configurations as $path => $value) {
            $this->configWriter->save($path, $value);
        }
    }
}
