<?php

namespace Totaltools\Pwa\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Store\Model\StoreManagerInterface;

class ModifyEmailUrl implements ObserverInterface
{
    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * Constructor
     *
     * @param RequestInterface $request
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        RequestInterface $request,
        StoreManagerInterface $storeManager
    ) {
        $this->request = $request;
        $this->storeManager = $storeManager;
    }

    /**
     * Execute method
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        // Check for a custom header or parameter to identify PWA requests
        $isPwaRequest = $this->request->getHeader('X-PWA-Request') || $this->request->getParam('is_pwa_request');

        if ($isPwaRequest) {
            // Modify the base URL for PWA requests
            $store = $this->storeManager->getStore();
            $baseUrl = 'https://pwa.example.com/';
            $store->setBaseUrl($baseUrl);
            $store->setSecureBaseUrl($baseUrl);

            // Update URLs in the transport object
            $transport = $observer->getEvent()->getTransport();
            $templateVars = $transport->getTemplateVars();
            $templateVars['store_url'] = $baseUrl;
            $templateVars['store'] = $store;
            $transport->setTemplateVars($templateVars);
        }
    }
}
