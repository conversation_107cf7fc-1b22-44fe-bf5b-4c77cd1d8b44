<?php
/**
 * Copyright © 2016 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Catalog\Setup;

use Exception;
use Magento\Catalog\Model\Product;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;
use Magento\Eav\Model\Entity\Attribute\Source\Boolean;
use Magento\Eav\Setup\EavSetup;
use Magento\Framework\Setup;
use Magento\Eav\Setup\EavSetupFactory;
use Totaltools\Catalog\Model\Attribute\Source\StockistsLevel;
use Magento\Cms\Model\BlockFactory;
use Magento\Cms\Model\BlockRepository;
use Magento\Catalog\Model\Attribute\Backend\Startdate;
use Magento\Eav\Model\Entity\Attribute\Backend\Datetime;
use Magento\Eav\Model\Config as EavConfig;
use Totaltools\Catalog\Setup\CategorySetupFactory;

/**
 * Class InstallData
 *
 * <AUTHOR> <<EMAIL>>
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class UpgradeData implements Setup\UpgradeDataInterface
{
    const SHOW_SAVED_LABEL = "show_saved_label";

    const DEFAULT_STORE = 0;

    /**
     * @var Setup\SampleData\Executor
     */
    protected $executor;

    /**
     * Category setup factory
     *
     * @var CategorySetupFactory
     */
    private $categorySetupFactory;

    /**
     * EAV setup factory
     *
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory
     */
    private $collectionFactory;

    /**
     * @var \Magento\Catalog\Model\Category
     */
    private $category;

    /**
     * Set featured brands
     * @var array
     */
    protected $featuredData = [
        'bosch' => [
            'is_featured' => 1,
            'featured_order' => 1,
            'brand_background_color' => '#0A385A'
        ],
        'dewalt' => [
            'is_featured' => 1,
            'featured_order' => 2,
            'brand_background_color' => '#FEBD24'
        ],
        'makita' => [
            'is_featured' => 1,
            'featured_order' => 3,
            'brand_background_color' => '#008c99'
        ],
        'milwaukee' => [
            'is_featured' => 1,
            'featured_order' => 4,
            'brand_background_color' => '#E31837'
        ],
        'paslode' => [
            'is_featured' => 1,
            'featured_order' => 5,
            'brand_background_color' => '#000000'
        ],
        'sidchrome' => [
            'is_featured' => 1,
            'featured_order' => 6,
            'brand_background_color' => '#C32033'
        ]
    ];

    /**
     * @var StockistsLevel
     */
    private StockistsLevel $stockistsLevel;

    /**
     * @var BlockFactory
     */
    private $blockFactory;

    /**
     * @var BlockRepository
     */
    private $blockRepository;

    /**
     * @var EavConfig
     */
    private $eavConfig;

    /**
     * InstallData constructor.
     *
     * @param Setup\SampleData\Executor $executor
     * @param CategorySetupFactory $categorySetupFactory
     * @param EavSetupFactory $eavSetupFactory
     * @param \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $collectionFactory
     * @param \Magento\Catalog\Model\CategoryFactory $category
     * @param StockistsLevel $stockistsLevel
     * @param blockFactory $blockFactory
     * @param BlockRepository $blockRepository
     * @param EavConfig $eavConfig
     */
    public function __construct(
        Setup\SampleData\Executor $executor,
        CategorySetupFactory $categorySetupFactory,
        EavSetupFactory $eavSetupFactory,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $collectionFactory,
        \Magento\Catalog\Model\CategoryFactory $category,
        StockistsLevel $stockistsLevel,
        BlockFactory $blockFactory,
        BlockRepository $blockRepository,
        EavConfig $eavConfig
    ) {
        $this->executor             = $executor;
        $this->eavSetupFactory      = $eavSetupFactory;
        $this->categorySetupFactory = $categorySetupFactory;
        $this->collectionFactory    = $collectionFactory;
        $this->category             = $category;
        $this->stockistsLevel = $stockistsLevel;
        $this->blockFactory = $blockFactory;
        $this->blockRepository = $blockRepository;
        $this->eavConfig = $eavConfig;
    }

    /**
     * {@inheritdoc}
     */
    public function upgrade(Setup\ModuleDataSetupInterface $setup, Setup\ModuleContextInterface $moduleContext)
    {
        $setup->startSetup();
        $categorySetup = $this->categorySetupFactory->create(['setup' => $setup]);

        /** @var EavSetup $eavSetup */
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

        if (version_compare($moduleContext->getVersion(), '1.0.1', '<')) {
            /** @var \Totaltools\Catalog\Setup\CategorySetup $categorySetup */
            $categorySetup->installEntities();
        }
        if (version_compare($moduleContext->getVersion(), '1.0.2') < 0) {
            $categorySetup->installEntities($this->getCatEntity());
        }

        if (version_compare($moduleContext->getVersion(), '1.0.3') < 0) {
            $eavSetup->removeAttribute(\Magento\Catalog\Model\Category::ENTITY, 'brand_image');
            $eavSetup->addAttribute(\Magento\Catalog\Model\Category::ENTITY, 'brand_image', [
                    'type' => 'varchar',
                    'label' => 'Brand Image',
                    'input' => 'image',
                    'backend' => 'Magento\Catalog\Model\Category\Attribute\Backend\Image',
                    'required' => false,
                    'sort_order' => 110,
                    'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                    'group' => 'General Information',
                    "default" => "",
                    "class"    => "",
                    "note"       => ""
                ]
            );

            $eavSetup->removeAttribute(\Magento\Catalog\Model\Category::ENTITY, 'is_featured');
            $eavSetup->addAttribute(\Magento\Catalog\Model\Category::ENTITY, 'is_featured', [
                    'type' => 'int',
                    'label' => 'Is Featured',
                    'input' => 'select',
                    'required' => false,
                    'source' => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                    'sort_order' => 120,
                    'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                    'group' => 'General Information',
                    "default" => 0,
                    "class"    => "",
                    "note"       => ""
                ]
            );

            $eavSetup->removeAttribute(\Magento\Catalog\Model\Category::ENTITY, 'featured_order');
            $eavSetup->addAttribute(\Magento\Catalog\Model\Category::ENTITY, 'featured_order', [
                    'type' => 'varchar',
                    'label' => 'Featured order',
                    'input' => 'text',
                    'required' => false,
                    'sort_order' => 130,
                    'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                    'group' => 'General Information',
                    "default" => "",
                    "class"    => "",
                    "note"       => ""
                ]
            );
        }

        if (version_compare($moduleContext->getVersion(), '1.0.4') < 0) {
            $this->addBrandImages();
        }

        if (version_compare($moduleContext->getVersion(), '1.0.5') < 0) {
            $this->addLabeledImage($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.6') < 0) {
            $this->addUsedForReward($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.7') < 0) {
            $this->addNoOfStockInStock($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.8') < 0) {
            $this->addStockistsLevel($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.9') < 0) {
            $this->addNotForSaleProduct($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.10') < 0) {
            $this->addNotForSaleMessage($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.11') < 0) {
            $this->addCanBeFreeDelivery($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.12') < 0) {
            $this->addShowSavedLabel($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.13') < 0) {
            $this->addCategoryBrandsBlock();
        }

        if (version_compare($moduleContext->getVersion(), '1.0.14') < 0) {
            $this->addNewFromTo($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.15') < 0) {
            $this->addNoOfStoresInStockOriginal($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.16') < 0) {
            $this->addBrandTxtAttribute($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.17') < 0) {
            $this->addSupplierOrderAttribute($eavSetup);
        }

        if (version_compare($moduleContext->getVersion(), '1.0.18') < 0) {
            $this->addKnifeComplianceAttribute($eavSetup);
        }

        $setup->endSetup();
    }

    public function getCatEntity(){
        return [
            'catalog_category' => [
                'entity_model' => 'Magento\Catalog\Model\ResourceModel\Category',
                'attribute_model' => 'Magento\Catalog\Model\ResourceModel\Eav\Attribute',
                'table' => 'catalog_category_entity',
                'additional_attribute_table' => 'catalog_eav_attribute',
                'entity_attribute_collection' => 'Magento\Catalog\Model\ResourceModel\Category\Attribute\Collection',
                'attributes' => [
                    'brand_text_color' => [
                        'type' => 'text',
                        'label' => 'Brand Text Color(Hex value eg: #0000df,#294d98)',
                        'input' => 'text',
                        'required' => false,
                        'sort_order' => 12,
                        'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                        'wysiwyg_enabled' => false,
                        'is_html_allowed_on_front' => true,
                        'group' => 'General Information',
                    ],
                ],
            ],
        ];
    }

    public function getCategoryUpdate(){
        return [
            'catalog_category' => [
                'entity_model' => 'Magento\Catalog\Model\ResourceModel\Category',
                'attribute_model' => 'Magento\Catalog\Model\ResourceModel\Eav\Attribute',
                'table' => 'catalog_category_entity',
                'additional_attribute_table' => 'catalog_eav_attribute',
                'entity_attribute_collection' => 'Magento\Catalog\Model\ResourceModel\Category\Attribute\Collection',
                'attributes' => [
                    'brand_text_color' => [
                        'type' => 'text',
                        'label' => 'Brand Text Color(Hex value eg: #0000df,#294d98)',
                        'input' => 'text',
                        'required' => false,
                        'sort_order' => 12,
                        'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                        'wysiwyg_enabled' => false,
                        'is_html_allowed_on_front' => true,
                        'group' => 'General Information',
                    ],
                ],
            ],
        ];
    }

    /**
     * Add brand images by this script
     *
     * @throws \Exception
     */
    public function addBrandImages()
    {
        $featuredArray =  $this->featuredData;

        $collection =  $this->collectionFactory
                        ->create()
                        ->addAttributeToFilter('name','Brands')
                        ->setPageSize(1);
        if ($collection->getSize()) {
            $brandCategory = $collection->getFirstItem();
            $subcats = $brandCategory->getChildrenCategories();

            foreach ($subcats as $subcat) {
                $subcategory =  $this->category->create()->setStoreId(0)->load($subcat->getId());
                $brandImageName = $subcategory->getUrlKey().'_137x60.png';
                if(isset($featuredArray[$subcategory->getUrlKey()])) {
                    $subcategory->setIsFeatured($featuredArray[$subcategory->getUrlKey()]['is_featured']);
                    $subcategory->setFeaturedOrder($featuredArray[$subcategory->getUrlKey()]['featured_order']);
                    $subcategory->setBrandBackgroundColor($featuredArray[$subcategory->getUrlKey()]['brand_background_color']);
                }

                $subcategory->setBrandImage($brandImageName);
                $subcategory->save();
            }
        }
    }

    public function addLabeledImage($eavSetup)
    {
        /** @var EavSetup $eavSetup */

        $eavSetup->addAttribute(
            \Magento\Catalog\Model\Product::ENTITY,
            'labeled_image',
            [
                'type' => 'varchar',
                'label' => 'Image with label',
                'input' => 'media_image',
                'frontend' => 'Magento\Catalog\Model\Product\Attribute\Frontend\Image',
                'global' => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                'filterable' => false,
                'visible_on_front' => false,
                'used_in_product_listing' => true,
                'sort_order' => 10,
                'required' => false,
            ]
        );
    }

    /**
     * @param $eavSetup
     */
    public function addUsedForReward($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->addAttribute(
            Product::ENTITY,
            'is_used_for_reward',
            [
                'type' => 'int',
                'label' => 'Reward Point Redeemable',
                'input' => 'boolean',
                'source' => Boolean::class,
                'default' => '0',
                'required' => false,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'used_in_product_listing' => true,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => true,
                'is_visible_on_front' => true,
            ]
        );

    }

    /**
     * @param $eavSetup
     */
    public function addNoOfStockInStock($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->addAttribute(
            Product::ENTITY,
            'no_of_stores_in_stock',
            [
                'type' => 'int',
                'label' => 'No of Stores In Stock',
                'input' => 'text',
                'default' => '0',
                'required' => false,
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'used_in_product_listing' => false,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => true,
                'is_visible_on_front' => false,
                'system' => 0,
                'is_user_defined' => 1,
                'searchable' => false,
                'filterable' => false,
                'comparable' => false,
            ]
        );

    }

    /**
     * @param $eavSetup
     */
    public function addNoOfStoresInStockOriginal($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->addAttribute(
            Product::ENTITY,
            'no_of_stores_in_stock_original',
            [
                'type' => 'int',
                'label' => 'No of Stores In Stock Original',
                'input' => 'text',
                'default' => '0',
                'required' => false,
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'used_in_product_listing' => false,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => true,
                'is_visible_on_front' => false,
                'system' => 0,
                'is_user_defined' => 1,
                'searchable' => false,
                'filterable' => false,
                'comparable' => false,
            ]
        );
    }

    /**
     * @param $eavSetup
     */
    public function addStockistsLevel($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->addAttribute(
            Product::ENTITY,
            'stockists_level',
            [
                'group' => 'General',
                'type' => 'int',
                'backend' => '',
                'frontend' => '',
                'label' => 'Stockists Level',
                'input' => 'select',
                'class' => '',
                'source' => 'Magento\Eav\Model\Entity\Attribute\Source\Table',
                'option' =>
                    array (
                        'values' =>
                            array (
                                0 => StockistsLevel::INSUFFICIENT_STOCKISTS,
                                1 => StockistsLevel::LOW_STOCKISTS,
                                2 => StockistsLevel::MEDIUM_STOCKISTS,
                                3 => StockistsLevel::HIGH_STOCKISTS
                            ),
                    ),
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'visible' => true,
                'default' => 0,
                'required' => false,
                'user_defined' => true,
                'is_user_defined' => true,
                'searchable' => false,
                'filterable' => true,
                'comparable' => false,
                'comparable' => false,
                'visible_on_front' => true,
                'used_in_product_listing' => true,
                'unique' => false
            ]
        );

    }

    /**
     * @param $eavSetup
     */
    public function addNotForSaleProduct($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, 'not_for_sale');
        $eavSetup->addAttribute(
            Product::ENTITY,
            'not_for_sale',
            [
                'type' => 'int',
                'label' => 'Not For Sale',
                'input' => 'boolean',
                'source' => Boolean::class,
                'default' => '0',
                'required' => false,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'used_in_product_listing' => true,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => true,
                'is_filterable_in_grid' => true,
                'is_visible_on_front' => true,
            ]
        );

    }

    /**
     * @param $eavSetup
     */
    public function addNotForSaleMessage($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, 'not_for_sale_message');
        $eavSetup->addAttribute(
            Product::ENTITY,
            'not_for_sale_message',
            [
                'type' => 'text',
                'label' => 'Not For Sale Message',
                'input' => 'text',
                'default' => 'Not for sale in your state',
                'required' => false,
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'used_in_product_listing' => true,
                'wysiwyg_enabled' => false,
                'is_html_allowed_on_front' => true,
            ]
        );

    }

    /**
     * @param $eavSetup
     */
    public function addCanBeFreeDelivery($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, 'can_be_free_delivery');
        $eavSetup->addAttribute(
            Product::ENTITY,
            'can_be_free_delivery',
            [
                'type' => 'int',
                'label' => 'Can Be Free Delivery',
                'input' => 'boolean',
                'source' => Boolean::class,
                'default' => '0',
                'required' => false,
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'used_in_product_listing' => true,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => true,
                'is_filterable_in_grid' => true,
                'is_visible_on_front' => true,
            ]
        );
    }

    /**
     * @param $eavSetup
     * @param $setup
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    public function addShowSavedLabel($eavSetup) {
        /** @var EavSetup $eavSetup */
        $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, self::SHOW_SAVED_LABEL);

        $eavSetup->addAttribute(
        Product::ENTITY,
            'show_saved_label',
            [
                'type' => 'varchar',
                'label' => 'Show Saved Label',
                'input' => 'select',
                'source' => \Totaltools\Catalog\Model\Product\Attribute\Source\ShowSavedLabel::class,
                'frontend' => '',
                'required' => false,
                'backend' => '',
                'sort_order' => '30',
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'default' => 'D',
                'visible' => true,
                'user_defined' => true,
                'searchable' => true,
                'filterable' => false,
                'comparable' => false,
                'visible_on_front' => true,
                'unique' => false,
                'apply_to' => '',
                'group' => 'General',
                'used_in_product_listing' => true,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false
            ]
        );
    }

     /**
     * @param $eavSetup
     * @param $setup
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Validator\ValidateException
     */
    public function addNewFromTo($eavSetup) {
        /** @var EavSetup $eavSetup */
        $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, 'product_new_from_date');
        $eavSetup->removeAttribute(\Magento\Catalog\Model\Product::ENTITY, 'product_new_to_date');
        $eavSetup->addAttribute(
        Product::ENTITY,
            'product_new_from_date',
            [
                'type' => 'datetime',
                'label' => 'Set Product as New from Date',
                'input' => 'date',
                'backend' => Startdate::class,
                'frontend' => '',
                'required' => false,
                'sort_order' => '30',
                'global' => ScopedAttributeInterface::SCOPE_STORE,
                'visible' => true,
                'user_defined' => true,
                'searchable' => true,
                'filterable' => false,
                'comparable' => false,
                'visible_on_front' => true,
                'unique' => false,
                'apply_to' => '',
                'group' => 'General',
                'used_in_product_listing' => true,
                'is_used_in_grid' => true,
                'is_visible_in_grid' => false
            ]
        );

        $eavSetup->addAttribute(
            Product::ENTITY,
                'product_new_to_date',
                [
                    'type' => 'datetime',
                    'label' => 'Set Product as New to Date',
                    'input' => 'date',
                    'backend' => Datetime::class,
                    'frontend' => '',
                    'required' => false,
                    'sort_order' => '30',
                    'global' => ScopedAttributeInterface::SCOPE_STORE,
                    'visible' => true,
                    'user_defined' => true,
                    'searchable' => true,
                    'filterable' => false,
                    'comparable' => false,
                    'visible_on_front' => true,
                    'unique' => false,
                    'apply_to' => '',
                    'group' => 'General',
                    'used_in_product_listing' => true,
                    'is_used_in_grid' => true,
                    'is_visible_in_grid' => false
                ]
            );
    }


    private function addCategoryBrandsBlock()
    {
        try {
            $categoryBlock = [
                'title' => 'Category Brands',
                'identifier' => 'category_brands',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => ""
            ];
            $block = $this->blockFactory->create(['data' => $categoryBlock]);
            $this->blockRepository->save($block);

        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }

    /**
     * @param $eavSetup
     */
    public function addBrandTxtAttribute($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->removeAttribute(Product::ENTITY, 'brand_text');
        $eavSetup->addAttribute(
            Product::ENTITY,
            'brand_text',
            [
                'type' => 'varchar',
                'label' => 'Brand Text',
                'input' => 'text',
                'default' => '',
                'required' => false,
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'group' => 'Specifications',
                'used_in_product_listing' => true,
                'sort_order' => 2
            ]
        );

        // Retrieve the attribute using EavConfig and set `is_used_for_promo_rules`
        $attribute = $this->eavConfig->getAttribute(Product::ENTITY, 'brand_text');
        if ($attribute && $attribute->getId()) {
            $attribute->setData('is_used_for_promo_rules', 1); // Enable promo rule condition
            $attribute->save();
        }

    }

    /**
     * Add supplier order attribute
     * 
     * Works in conjunction with the pre_order attribute to hide labels on Unbxd search results
     * 
     * @param $eavSetup
     * @return void
     */
    public function addSupplierOrderAttribute($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->removeAttribute(Product::ENTITY, 'supplier_order');

        $eavSetup->addAttribute(
            Product::ENTITY,
            'supplier_order',
            [
                'label' => 'Supplier Order',
                'type' => 'int',
                'input' => 'boolean',
                'source' => Boolean::class,
                'default' => 0,
                'required' => false,
                'sort_order' => 100,
                'global' => ScopedAttributeInterface::SCOPE_GLOBAL,
                'group' => 'General',
                'filterable' => '0',
                'include_in_unbxd_product_feed' => '1',
                'unbxd_field_type' => 'boolean',
                'is_used_for_promo_rules' => '1'
            ]
        );
    }

    /**
     * Add knife compliance attribute
     * 
     * @param EavSetup $eavSetup
     * @return void
     */
    public function addKnifeComplianceAttribute($eavSetup)
    {
        /** @var EavSetup $eavSetup */
        $eavSetup->removeAttribute(Product::ENTITY, 'knife_compliance');

        $eavSetup->addAttribute(
            Product::ENTITY,
            'knife_compliance',
            [
                'type' => 'int',
                'label' => 'Knife Compliance',
                'input' => 'boolean',
                'source' => Boolean::class,
                'default' => '0',
                'required' => false,
                'global' => ScopedAttributeInterface::SCOPE_WEBSITE,
                'used_in_product_listing' => false,
                'is_used_in_grid' => false,
                'is_visible_in_grid' => false,
                'is_filterable_in_grid' => false,
                'is_visible_on_front' => false,
                'is_used_for_promo_rules' => true,
                'group' => 'General',
                'sort_order' => 105,
                'note' => 'Indicates if the product needs to comply with knife regulations'
            ]
        );
    }    
}
