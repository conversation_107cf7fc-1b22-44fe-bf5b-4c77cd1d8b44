<?php
/**
 * Import data in no_of_stores_in_stock attribute.
 *
 * @package Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Totaltools. All rights reserved. <https://totaltools.com.au>
 */
namespace Totaltools\Catalog\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Totaltools\Catalog\Model\StoresInStockImporter;

/**
 * Class NoOfStoresInStock
 * @package Totaltools\Catalog\Console\Command
 */
class NoOfStoresInStock extends Command
{
    /**
     * @var StoresInStockImporter
     */
    private StoresInStockImporter $storesInStockImporter;

    /**
     * NoOfStoresInStock constructor.
     * @param StoresInStockImporter $storesInStockImporter
     */
    public function __construct(
        StoresInStockImporter $storesInStockImporter
    ) {
        $this->storesInStockImporter = $storesInStockImporter;
        parent::__construct();
    }

    protected function configure()
    {
        $this->setName('totaltools:instock:count');
        $this->setDescription("Import Product Available In Store Count");
        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln(Date("Y-m-d H:i:s") . " Product available in store count Import started");
        try {
            $response = $this->storesInStockImporter->execute();
            if(!$response) {
                $output->writeln(Date("Y-m-d H:i:s") . " Enable backend configuration ( CATALOG -> Catalog -> No of stores instock )");
            }
        } catch (\Exception $exception) {
            $output->writeln(Date("Y-m-d H:i:s") . " " . $exception->getMessage());
        }
        $output->writeln(Date("Y-m-d H:i:s") . " Product available in store count Import finished");
    }
}
