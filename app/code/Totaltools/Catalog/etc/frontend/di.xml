<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager\etc\config.xsd">
    <type name="Magento\Customer\CustomerData\SectionPoolInterface">
        <arguments>
            <argument name="sectionSourceMap" xsi:type="array">
                <item name="fulfilment-data" xsi:type="string">Totaltools\Catalog\CustomerData\Fulfilment</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Catalog\Model\Product">
        <plugin name="totaltools-catalog-product-model-plugin" type="Totaltools\Catalog\Plugin\Catalog\Model\Product" />
    </type>
    <type name="Magento\Checkout\CustomerData\Cart">
        <plugin name="Totaltools_Checkout::cartCustomerDataPlugin" type="Totaltools\Catalog\Plugin\CustomerData\Cart" />
    </type>
    <type name="Magento\Catalog\Model\Config">
        <plugin name="totaltools-catalog-model-config" type="Totaltools\Catalog\Plugin\Catalog\Model\Config"/>
    </type>
    <type name="Magento\Catalog\Helper\Image">
        <plugin name="totaltools-catalog-plugin-catalog-helper-image" type="Totaltools\Catalog\Plugin\Catalog\Helper\Image"/>
    </type>
    <type name="Amasty\ShopbyBase\Model\UrlBuilder\UrlModifier">
        <plugin name="totaltools-catalog-url-modifier-plugin" type="Totaltools\Catalog\Plugin\Model\UrlBuilder\UrlModifier"/>
    </type>
    <type name="Amasty\Shopby\Helper\Category">
        <plugin name="totaltools-catalog-ajax-viewall-displaymode-plugin" type="Totaltools\Catalog\Plugin\Catalog\Helper\ViewAllDisplayMode" />
    </type>
    <type name="Magento\Framework\App\RouterList">
        <arguments>
            <argument name="routerList" xsi:type="array">
                <item name="view-all" xsi:type="array">
                    <item name="class" xsi:type="string">Totaltools\Catalog\Controller\Router</item>
                    <item name="disable" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="string">30</item>
                </item>
            </argument>
        </arguments>
    </type>
</config>