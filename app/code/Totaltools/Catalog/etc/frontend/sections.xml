<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Customer:etc/sections.xsd">
    <action name="storepickup/checkout/changestore">
        <section name="fulfilment-data"/>
    </action>
    <action name="rest/V1/checkout/change-location">
        <section name="fulfilment-data"/>
    </action>
    <action name="customer/account/logout">
        <section name="fulfilment-data"/>
    </action>
    <action name="customer/account/loginPost">
        <section name="fulfilment-data"/>
    </action>
    <action name="customer/account/createPost">
        <section name="fulfilment-data"/>
    </action>
    <action name="customer/account/editPost">
        <section name="fulfilment-data"/>
    </action>
    <action name="customer/ajax/login">
        <section name="fulfilment-data"/>
    </action>
    <action name="totaltools_storelocator/checkout/stockcheck">
        <section name="fulfilment-data"/>
    </action>
    <action name="totaltools_storelocator/checkout/changestore">
        <section name="fulfilment-data"/>
    </action>
    <action name="totaltools/checkout/changelocation">
        <section name="fulfilment-data"/>
    </action>
    <action name="customer/address/formpost/id/*/">
        <section name="instant-purchase"/>
    </action>
</config>