<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Catalog\Model\ResourceModel\Product\Collection" type="Totaltools\Catalog\Model\ResourceModel\Product\RewriteCollection"/>
    <preference for="Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable\Product\Collection" type="Totaltools\Catalog\Model\ResourceModel\Product\Type\Configurable\Product\RewriteCollection"/>
    <preference for="Magento\Catalog\Block\Category\View" type="Totaltools\Catalog\Block\Category\View" />
    <type name="Magento\Catalog\Block\Product\View">
        <plugin name="add_catalog_breadcrumb_block" type="Totaltools\Catalog\Plugin\Catalog\Block\Product\View" />
    </type>
    <type name="Magento\ConfigurableProduct\Block\Product\View\Type\Configurable">
        <plugin disabled="false" name="Totaltools_Catalog_Plugin_ConfigurableProduct_Block_Product_View_Type_Configurable" sortOrder="10" type="Totaltools\Catalog\Plugin\ConfigurableProduct\Block\Product\View\Type\Configurable"/>
    </type>

    <type name="Magento\Catalog\Block\Product\ImageFactory">
        <plugin disabled="false" name="Totaltools_Catalog_Plugin_ConfigurableProduct_Block_Product_ImageFactory" sortOrder="15" type="Totaltools\Catalog\Plugin\Block\Product\ImageFactory"/>
    </type>

    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="stores_instock_count"
                      xsi:type="object">Totaltools\Catalog\Console\Command\NoOfStoresInStock</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\Catalog\Block\Product\Gallery">
		<plugin name="Totaltools_Catalog_Plugin_Block_Product_Gallery" type="Totaltools\Catalog\Plugin\Block\Product\Gallery" sortOrder="10" disabled="false"/>
	</type>
    <preference for="Magento\Catalog\Block\Product\View\Gallery" type="Totaltools\Catalog\Rewrite\Magento\Catalog\Block\Product\View\Gallery"/>

    <type name="Magento\ConfigurableProduct\Block\Product\View\Type\Configurable">
        <plugin name="Totaltools_Catalog::ConfigurablePlugin" type="Totaltools\Catalog\Plugin\Block\Product\ConfigurablePlugin" />
    </type>
    <type name="Magento\Catalog\Ui\DataProvider\Product\ProductRenderCollectorComposite">
        <arguments>
            <argument name="productProviders" xsi:type="array">
                <item name="show_saved_label" xsi:type="object">\Totaltools\Catalog\Ui\DataProvider\Product\Listing\Collector\Label</item>
                <item name="not_for_sale" xsi:type="object">\Totaltools\Catalog\Ui\DataProvider\Product\Listing\Collector\NotForSale</item>
            </argument>
        </arguments>
    </type>
    <preference for="Magento\Catalog\Pricing\Price\TierPrice" type="Totaltools\Catalog\Rewrite\Magento\Catalog\Pricing\Price\TierPrice"/>
</config>
