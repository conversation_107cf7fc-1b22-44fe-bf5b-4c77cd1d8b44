<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Catalog\Model\Category\Attribute\Source\Mode">
        <plugin name="Totaltools_Catalog_Additional_Category_Display_Options" type="Totaltools\Catalog\Plugin\Model\Category\Attribute\Source\Mode" sortOrder="1" />
    </type>
    <virtualType name="Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\Pool">
        <arguments>
            <argument name="modifiers" xsi:type="array">
                <item name="custom-attribute" xsi:type="array">
                    <item name="class" xsi:type="string">Totaltools\Catalog\Ui\DataProvider\Product\Form\Modifier\ShippingDangerous</item>
                    <item name="sortOrder" xsi:type="number">10</item>
                </item>
            </argument>
        </arguments>
    </virtualType>
</config>
