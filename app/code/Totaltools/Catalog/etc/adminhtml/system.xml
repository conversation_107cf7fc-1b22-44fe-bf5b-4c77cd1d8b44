<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="catalog">
            <group id="brands_setting" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Featured Brands</label>
                <field id="featured_brands" translate="label" type="multiselect" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Featured Brands</label>
                    <can_be_empty>1</can_be_empty>
                    <frontend_model>Totaltools\Catalog\Block\Adminhtml\Frontend\CategoryList\Multiselect</frontend_model>
                    <source_model>Totaltools\Catalog\Model\Config\Source\CategoryList</source_model>
                </field>
            </group>
            <group id="labeled_image" showInDefault="1" sortOrder="10" translate="label">
                <label>Product With Labels</label>
                <field id="enabled" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="1" translate="label" type="select">
                    <label>Enabled</label>
                    <comment>Enable Disable  Product With Labels Image</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="stores_instock" showInDefault="1" sortOrder="10" translate="label">
                <label>No of Stores In Stock</label>
                <field id="enabled" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="1" translate="label" type="select">
                    <label>Enabled</label>
                    <comment>Enable No of stores instock</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="email" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="1" translate="label" type="text">
                    <label>Email</label>
                    <comment>No of stores instock email</comment>
                </field>
                <field id="stores_instock_button" translate="label comment" type="button" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Import</label>
                    <frontend_model>Totaltools\Catalog\Block\Adminhtml\System\Config\Form\StoresInStockSyncButton</frontend_model>
                </field>
            </group>
            <group id="compare_combo_kit" translate="label" type="text" sortOrder="1000" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Compare Combo Kit</label>
                <field id="product_skus" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="1" translate="label" type="textarea">
                    <label>Product SKU</label>
                    <comment>Enter all product skus seperated by comma for combo kit comparison</comment>
                </field>
            </group>
            <group id="cart_notifications" translate="label" type="text" sortOrder="1100" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Add to Cart Notifications</label>
                <field id="enabled" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="1" translate="label" type="select">
                    <label>Enabled</label>
                    <comment>Enable Notifications</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="auto_hide" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="2" translate="label" type="select">
                    <label>Can Auto Hide</label>
                    <comment>Notification should auto hide after certain time</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="holdup" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="3" translate="label" type="text">
                    <label>Notification Hold up</label>
                    <comment>Time in seconds before notification dissappears</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="fade_time" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="4" translate="label" type="text">
                    <label>Fade Delay</label>
                    <comment>Time in seconds for fade in/out animations</comment>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="close_btn" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="5" translate="label" type="select">
                    <label>Show Close Button</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="position" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="6" translate="label" type="select">
                    <label>Notification Position</label>
                    <source_model>Totaltools\Catalog\Model\System\Config\Source\Position</source_model>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="title" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="7" translate="label" type="text">
                    <label>Notification Title</label>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="button_text" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="8" translate="label" type="text">
                    <label>Button Title</label>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
                <field id="promo_text" showInDefault="1" showInStore="0" showInWebsite="1" sortOrder="9" translate="label" type="text">
                    <label>Promotional Message</label>
                    <depends>
                        <field id="*/*/enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
        <section id="delivery" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Stock Availability Messages</label>
            <tab>catalog</tab>
            <resource>Totaltools_Catalog::config_delivery</resource>
            <group id="message" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                <comment>
                    <![CDATA[Please note that the classes info-success, info-error, and info-warning can be used in span tag to style messages. Use info-tc class for Term and condition apply.]]>
                </comment>
                <label>Delivery Availability Messages</label>
                <field id="standard" translate="label" type="textarea" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Standard Delivery Stock Availability Message</label>
                </field>
                <field id="express" translate="label" type="textarea" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Express Delivery Stock Availability Message</label>
                </field>
                <field id="uber" translate="label" type="textarea" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Uber Delivery Stock Availability Message</label>
                </field>
                <field id="instock" translate="label" type="textarea" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>In Stock Message</label>
                </field>
                <field id="preorder" translate="label" type="textarea" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Pre Order Message</label>
                </field>
                <field id="special_order_product_out_of_stock" translate="label" type="textarea" sortOrder="6" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Special Order Product Out of Stock Message</label>
                </field>
                <field id="backorderable_normal_product_out_of_stock" translate="label" type="textarea" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Backorderable Normal Products Out Of Stock Message</label>
                </field>
                <field id="backorderable_eb_product_out_of_stock" translate="label" type="textarea" sortOrder="8" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Backorderable EB Products Out Of Stock Message</label>
                </field>
                <field id="not_backorderable_product_out_of_stock" translate="label" type="textarea" sortOrder="9" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Not Backorderable Products Out Of Stock Message</label>
                </field>
            </group>
            <group id="title" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Delivery Methods Titles</label>
                <field id="standard" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Standard Delivery Title</label>
                </field>
                <field id="express" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Express Delivery Title</label>
                </field>
                <field id="uber" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Uber Delivery Title</label>
                </field>
            </group>
            <group id="cc_message" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                <comment>
                    <![CDATA[Please note that the classes info-success, info-error, and info-warning can be used in span tag to style messages. Use info-tc class for Term and condition apply.]]>
                </comment>
                <label>Click And Collect Messages</label>
                <field id="instock" translate="label" type="textarea" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>In Stock Message</label>
                </field>
                <field id="preorder" translate="label" type="textarea" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Pre Order Message</label>
                </field>
               <field id="special_order_product_out_of_stock" translate="label" type="textarea" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Special Order Product Out of Stock Message</label>
                </field>
                <field id="backorderable_normal_product_out_of_stock" translate="label" type="textarea" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Backorderable Normal Products Out Of Stock Message</label>
                </field>
                <field id="backorderable_eb_product_out_of_stock" translate="label" type="textarea" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Backorderable EB Products Out Of Stock Message</label>
                </field>
                <field id="not_backorderable_product_out_of_stock" translate="label" type="textarea" sortOrder="6" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Not Backorderable Products Out Of Stock Message</label>
                </field>
            </group>
        </section>
    </system>
</config>