<?php
/**
 * Import data in no_of_stores_in_stock attribute.
 *
 * @package Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Totaltools. All rights reserved. <https://totaltools.com.au>
 */
namespace Totaltools\Catalog\Model\Eav;

use Exception;
use Magento\Catalog\Model\ProductFactory;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Totaltools\Catalog\Model\Attribute\Source\StockistsLevel;

/**
 * Class StoresInStock
 * @package Totaltools\Catalog\Model\Eav
 */
class StoresInStock
{
    const DEFAULT_STORE_ID = 0;
    const NO_STORES_IN_STORE_DEFAULT = 0;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var AdapterInterface
     */
    private $connection;

    /**
     * @var int
     */
    protected $noOfStoresInStockId;

    /**
     * @var int
     */
    protected $stockistsLevelId;

    /**
     * @var int
     */
    protected $noOfStoresInStockOriginalId;

    /**
     * @var int
     */
    protected $catalogProductEntityInt;

    /**
     * @var array
     */
    protected $summary = [];
    private ProductFactory $productFactory;

    /**
     * CatalogProductEntityInt constructor.
     * @param ResourceConnection $resourceConnection
     * @param ProductFactory $productFactory
     */
    public function __construct(
        ResourceConnection $resourceConnection,
        ProductFactory $productFactory
    ) {
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
        $this->initialization();
        $this->productFactory = $productFactory;
    }

    protected function initialization()
    {
        // get catalog product entity int table name
        $this->catalogProductEntityInt = $this->connection->getTableName("catalog_product_entity_int");
        $this->summary['totalProductUpdated'] = 0;
        $this->summary['totalProductNotFound'] = 0;
    }

    /**
     * @param int $noOfStoresInStockId
     */
    public function setNoOfStoresInStockId(int $noOfStoresInStockId)
    {
        $this->noOfStoresInStockId = $noOfStoresInStockId;
    }

    /**
     * @return int
     */
    public function getNoOfStoresInStockId()
    {
        return $this->noOfStoresInStockId;
    }

    /**
     * @param int $stockistsLevelId
     */
    public function setStockistsLevelId(int $stockistsLevelId)
    {
        $this->stockistsLevelId = $stockistsLevelId;
    }

    /**
     * @return string
     */
    public function getStockistsLevelId()
    {
        return $this->stockistsLevelId;
    }

    /**
     * @param int $noOfStoresInStockOriginalId
     */
    public function setNoOfStoresInStockOriginalId(int $noOfStoresInStockOriginalId)
    {
        $this->noOfStoresInStockOriginalId = $noOfStoresInStockOriginalId;
    }

    /**
     * @return int
     */
    public function getNoOfStoresInStockOriginalId()
    {
        return $this->noOfStoresInStockOriginalId;
    }

    /**
     * @return bool
     */
    public function executeImporter() : bool
    {
        try {
            $noOfStoresInStockId = $this->getnoOfStoresInStockId();
            $stockistsLevelId = $this->getStockistsLevelId();
            $noOfStoresInStockOriginalId = $this->getNoOfStoresInStockOriginalId();
            
            if (!empty($noOfStoresInStockId) && !empty($stockistsLevelId) && !empty($noOfStoresInStockOriginalId)) {
                $this->connection->beginTransaction();
                $this->updateNoOfStoresInStockAttributeRecord($noOfStoresInStockId);
                $this->updateStockistsLevelAttributeRecord($stockistsLevelId);
                $this->updateNoOfStoresInStockOriginalAttributeRecord($noOfStoresInStockOriginalId);
                $totalStores = $this->getTotalStores();

                if (!empty($collection = $this->getNoOfStoresInStockCollection())) {
                    foreach ($collection as $product) {
                        $rowId = $product["row_id"];
                        $noOfStoresOutOfStock = ((int)$totalStores - (int)$product["erp_id_count"] < 0) ? 0 : (int)$totalStores - (int)$product["erp_id_count"];
                        $noOfStoresInStock = (int)$product["erp_id_count"];
                        $storeId = 0;
                        // insert data for no_of_stores_in_stock
                        $this->insertIntData($rowId, $noOfStoresOutOfStock, $storeId, $noOfStoresInStockId);
                        // insert data for no_of_stores_in_stock_original
                        $this->insertIntData($rowId, $noOfStoresInStock, $storeId, $noOfStoresInStockOriginalId);

                        $stockistsLevelValue = $this->calculationStokistLevel((int)$product["erp_id_count"], (int)$totalStores);
                        $this->insertIntData($rowId, $stockistsLevelValue, $storeId, $stockistsLevelId);
                        $this->summary['totalProductUpdated'] ++;
                    }
                }
                // save all changes.
                $this->connection->commit();
                $this->summary["successMessage"] = "Imported Successfully";
            } else {
                $this->summary["errorMessage"] = "No rows effect. Don't find attribute";
            }
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->summary["errorMessage"] = $e->getMessage();
            return false;
        }

        return true;
    }

    /**
     * @param int $inStockStores
     * @param int $totalActiveStores
     * @return string
     */
    protected function calculationStokistLevel(int $inStockStores, int $totalActiveStores): string {

        if ($totalActiveStores > 0) {
            $percentage_in_stock =  round(($inStockStores / $totalActiveStores) * 100);

            if($percentage_in_stock > 70) {
                return $this->getOptionIdByLabel($this->getStockistsLevelId(), StockistsLevel::HIGH_STOCKISTS);
            } else if ($percentage_in_stock > 30) {
                return $this->getOptionIdByLabel($this->getStockistsLevelId(), StockistsLevel::MEDIUM_STOCKISTS);
            } else if ($percentage_in_stock > 10) {
                return $this->getOptionIdByLabel($this->getStockistsLevelId(), StockistsLevel::LOW_STOCKISTS);
            } else {
                return $this->getOptionIdByLabel($this->getStockistsLevelId(), StockistsLevel::INSUFFICIENT_STOCKISTS);
            }
        } else {
            return $this->getOptionIdByLabel($this->getStockistsLevelId(), StockistsLevel::INSUFFICIENT_STOCKISTS);
        }
    }

    /* Get Option id by Option Label */
    public function getOptionIdByLabel($attributeCode,$optionLabel)
    {
        $product = $this->productFactory->create();
        $isAttributeExist = $product->getResource()->getAttribute($attributeCode);
        $optionId = '';
        if ($isAttributeExist && $isAttributeExist->usesSource()) {
            $optionId = $isAttributeExist->getSource()->getOptionId($optionLabel);
        }
        return $optionId;
    }

    /**
     * @param int $noOfStoresInStockId
     */
    protected function updateNoOfStoresInStockAttributeRecord(int $noOfStoresInStockId)
    {
        $totalStores = $this->getTotalStores();
        $this->connection->update(
            $this->catalogProductEntityInt,
            ['value' => (int)$totalStores],
            ["attribute_id = ?" => $noOfStoresInStockId]
        );
    }

    /**
     * @param int $noOfStoresInStockOriginalId
     */
    protected function updateNoOfStoresInStockOriginalAttributeRecord(int $noOfStoresInStockOriginalId)
    {
        $this->connection->update(
            $this->catalogProductEntityInt,
            ['value' => self::NO_STORES_IN_STORE_DEFAULT],
            ["attribute_id = ?" => $noOfStoresInStockOriginalId]
        );
    }

    /**
     * @param int $stockistsLevel
     */
    protected function updateStockistsLevelAttributeRecord(string $stockistsLevelId)
    {
        $stockistsOptionId = $this->getOptionIdByLabel($this->getStockistsLevelId(), StockistsLevel::INSUFFICIENT_STOCKISTS);
        $this->connection->update(
            $this->catalogProductEntityInt,
            ['value' => $stockistsOptionId],
            ["attribute_id = ?" => $stockistsLevelId]
        );
    }


    /**
     * @return array
     */
    public function getNoOfStoresInStockCollection()
    {
        $this->connection = $this->resource->getConnection();
        $query = "select product.row_id, inventory.sku, count(inventory.erp_id) erp_id_count from magestore_storelocator_store_inventory inventory
                  inner join catalog_product_entity product on (product.sku = inventory.sku)
                    where units > 0
                    group by entity_id, sku";
        return $this->connection->fetchAll($query);
    }

    /**
     * @return array
     */
    public function getTotalStores()
    {
        $this->connection = $this->resource->getConnection();
        $query = "select count(*) total_store from magestore_storelocator_store where status = 1";
        return $this->connection->fetchOne($query);
    }


    /**
     * @param int $rowId
     * @param int $value
     * @param int $storeId
     * @param int $attributeId
     */
    protected function insertIntData(
        int $rowId,
        int $value,
        int $storeId,
        int $attributeId
    ) {
        $bind = [
            "attribute_id" => $attributeId,
            "store_id" => $storeId,
            "row_id" => $rowId,
            "value" => $value
        ];

        $this->connection->insertOnDuplicate($this->catalogProductEntityInt, $bind);
    }

    /**
     * @return array
     */
    public function getSummary() : array
    {
        return $this->summary;
    }
}
