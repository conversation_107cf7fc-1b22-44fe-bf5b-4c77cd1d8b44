<?php
/**
 * ShowSavedLabel
 *
 * @category  Totaltools
 * @package   Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Catalog\Model\Product\Attribute\Source;

class ShowSavedLabel extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    const SHOW_PERCENTAGE = 'P';
    const SHOW_DOLLAR = 'D';
    const DONT_SHOW_DISCOUNT = 'N';


    /**
     * getAllOptions
     *
     * @return array
     */
    public function getAllOptions()
    {
        $this->_options = [
            ['value' => self::SHOW_PERCENTAGE, 'label' => __('Show Percentage Saved')],
            ['value' => self::SHOW_DOLLAR, 'label' => __('Show Dollar Amount Saved')],
            ['value' => self::DONT_SHOW_DISCOUNT, 'label' => __('Do Not Show Discount')],
            ['value' => '', 'label' => __('None')]
        ];
        return $this->_options;
    }

    /**
     * @return array
     */
    public function getFlatColumns()
    {
        $attributeCode = $this->getAttribute()->getAttributeCode();
        return [
        $attributeCode => [
        'unsigned' => false,
        'default' => null,
        'extra' => null,
        'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
        'length' => 255,
        'nullable' => true,
        'comment' => $attributeCode . ' column',
        ],
        ];
    }

    /**
     * @return array
     */
    public function getFlatIndexes()
    {
        $indexes = [];

        $index = 'IDX_' . strtoupper($this->getAttribute()->getAttributeCode());
        $indexes[$index] = ['type' => 'index', 'fields' => [$this->getAttribute()->getAttributeCode()]];

        return $indexes;
    }

    /**
     * @param int $store
     * @return \Magento\Framework\DB\Select|null
     */
    public function getFlatUpdateSelect($store)
    {
        return $this->eavAttrEntity->create()->getFlatUpdateSelect($this->getAttribute(), $store);
    }
}
