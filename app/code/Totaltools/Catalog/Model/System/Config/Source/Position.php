<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Catalog\Model\System\Config\Source;

class Position implements \Magento\Framework\Data\OptionSourceInterface
{
    /**
     * @inheritdoc
     */
    public function toOptionArray()
    {
        return [
            [
                'label' => __('Top Left'),
                'value' => 'top-left'
            ],
            [
                'label' => __('Top Right'),
                'value' => 'top-right'
            ],
            [
                'label' => __('Bottom Left'),
                'value' => 'bottom-left'
            ],
            [
                'label' => __('Bottom Right'),
                'value' => 'bottom-right'
            ],
        ];
    }
}
