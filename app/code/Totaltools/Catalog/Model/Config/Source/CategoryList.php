<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Catalog\Model\Config\Source;

use Magento\Framework\Option\ArrayInterface;

class CategoryList implements ArrayInterface
{
    /**
     * @var \Magento\Catalog\Model\CategoryFactory
     */
    protected $categoryFactory;

    /**
     * @var \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory
     */
    protected $categoryCollectionFactory;

    /**
     * Cache instance to store categories data
     *
     * @var array
     */
    private $categories;

    public function __construct(
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryCollectionFactory
    ) {
        $this->categoryFactory = $categoryFactory;
        $this->categoryCollectionFactory = $categoryCollectionFactory;
    }

    /**
     * @param bool $isActive
     * @param bool $level
     * @param bool $sortBy
     * @param bool $pageSize
     *
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection
     */
    public function getCategoryCollection($isActive = true, $level = false, $sortBy = false, $pageSize = false)
    {
        /** @var \Magento\Catalog\Model\ResourceModel\Category\Collection $collection */
        $collection = $this->categoryCollectionFactory->create();
        $collection->addAttributeToSelect('*');

        // select only active categories
        if ($isActive) {
            $collection->addIsActiveFilter();
        }

        // select categories of certain level
        if ($level) {
            $collection->addLevelFilter($level);
        }

        // sort categories by some value
        if ($sortBy) {
            $collection->addOrderField($sortBy);
        }

        // select certain number of categories
        if ($pageSize) {
            $collection->setPageSize($pageSize);
        }

        return $collection;
    }

    /**
     * {@inheritdoc}
     */
    public function toOptionArray()
    {
        $optionArray = [];

        foreach ($this->parseCollectionData() as $key => $value) {
            $optionArray[] = [
                'value' => $key,
                'label' => $value
            ];
        }

        return $optionArray;
    }

    /**
     * @return array
     */
    private function parseCollectionData()
    {
        $categories = $this->getCategoryCollection(true, false, false, false);
        $categoryList = [];
        foreach ($categories as $category) {
            $categoryList[$category->getId()] = $this->getParentName($category->getPath()) . $category->getName();
        }

        return $categoryList;
    }

    private function getCategories()
    {
        if ($this->categories === null) {
            $this->categories = [];
            /** @var \Magento\Catalog\Model\Category $category */
            $category = $this->categoryFactory->create();
            $storeCategories = $category->getCategories(1, 1, false, false, true);
            foreach ($storeCategories as $item) {
                $this->categories[$item->getId()] = $this->getParentName($item->getPath()) . $item->getName();
            }
        }

        return $this->categories;
    }

    /**
     * @param string $path
     *
     * @return string
     */
    private function getParentName($path = '')
    {
        $parentName = '';
        $rootCats = array(1, 2);

        $catTree = explode("/", $path);
        // Deleting category itself
        array_pop($catTree);

        if ($catTree && (count($catTree) > count($rootCats))) {
            foreach ($catTree as $catId) {
                if (!in_array($catId, $rootCats)) {
                    $category = $this->categoryFactory->create()->load($catId);
                    $categoryName = $category->getName();
                    $parentName .= $categoryName . ' > ';
                }
            }
        }

        return $parentName;
    }
}
