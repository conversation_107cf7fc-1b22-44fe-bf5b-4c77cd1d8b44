<?php
/**
 * Import data in no_of_stores_in_stock attribute.
 *
 * @package Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 Totaltools. All rights reserved. <https://totaltools.com.au>
 */
namespace Totaltools\Catalog\Model;

use Magento\Eav\Model\Entity\Attribute;
use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\Store;
use Totaltools\Catalog\Model\Eav\StoresInStock;

/**
 * Class StoresInStockImporter
 * @package Totaltools\Catalog\Model
 */
class StoresInStockImporter
{

    const STOCKISTS_LEVEL = "stockists_level";
    const NO_OF_STORE_INSTOCK_ID = "no_of_stores_in_stock";
    const NO_OF_STORE_INSTOCK_ORIGINAL_ID = "no_of_stores_in_stock_original";
    const XML_NO_OF_STORES_INSTOCK_EMAIL = "catalog/stores_instock/email";
    const XML_NO_OF_STORES_INSTOCK_ENABLE = "catalog/stores_instock/enabled";

    /**
     * @var Attribute
     */
    private Attribute $eavAttribute;

    /**
     * @var StoresInStock
     */
    private StoresInStock $storesInStock;

    /**
     * @var ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;

    /**
     * @var TransportBuilder
     */
    private TransportBuilder $transportBuilder;

    /**
     * @var State
     */
    private State $state;

    /**
     * StoresInStockImporter constructor.
     * @param Attribute $eavAttribute
     * @param StoresInStock $storesInStock
     * @param ScopeConfigInterface $scopeConfig
     * @param TransportBuilder $transportBuilder
     * @param State $state
     */
    public function __construct(
        Attribute $eavAttribute,
        StoresInStock $storesInStock,
        ScopeConfigInterface $scopeConfig,
        TransportBuilder $transportBuilder,
        State $state
    ) {
        $this->eavAttribute = $eavAttribute;
        $this->storesInStock = $storesInStock;
        $this->scopeConfig = $scopeConfig;
        $this->transportBuilder = $transportBuilder;
        $this->state = $state;
    }

    /**
     * @param string $productEav
     * @return int
     */
    public function getAttributeId(string $productEav)
    {
        return $this->eavAttribute->getIdByCode("catalog_product", $productEav);
    }

    /**
     * @return mixed
     */
    protected function getStoresInStocEnable() {
        return $this->scopeConfig->getValue(
            self::XML_NO_OF_STORES_INSTOCK_ENABLE,
            ScopeInterface::SCOPE_STORE);
    }

    /**
     * @return mixed
     */
    protected function getStoresInStockEmailReceiver() {
        return $this->scopeConfig->getValue(
            self::XML_NO_OF_STORES_INSTOCK_EMAIL,
            ScopeInterface::SCOPE_STORE);
    }

    public function execute()
    {
        try {
            if ($this->getStoresInStocEnable()) {
                $this->storesInStock->setNoOfStoresInStockId($this->getAttributeId(self::NO_OF_STORE_INSTOCK_ID));
                $this->storesInStock->setStockistsLevelId($this->getAttributeId(self::STOCKISTS_LEVEL));
                $this->storesInStock->setNoOfStoresInStockOriginalId($this->getAttributeId(self::NO_OF_STORE_INSTOCK_ORIGINAL_ID));
                $this->storesInStock->executeImporter();
                $summary = $this->storesInStock->getSummary();
                $this->sendNotification($this->getFormatedLogTrace($summary));
            } else {
                return false;
            }
        } catch (\Exception $e) {
            $this->sendNotification($e->getMessage());
        }
        return true;
    }

    /**
     * @param $logTrace
     * @return string
     */
    public function getFormatedLogTrace($logTrace)
    {
        $trace = '<table>';
        foreach ($logTrace as $key => $info) {
            if ($info !== '') {
                $trace .= "<tr><td>".$key . ': ' . $info . "</td></tr>";
            }
        }
        $trace .= '</table>';
        return $trace;
    }

    /**
     * @param $message
     * @throws \Exception
     */
    public function sendNotification($message)
    {
        $recipient = $this->getStoresInStockEmailReceiver();
        if ($recipient) {
            try {
                $dt = new \DateTime('NOW');
                $transport = [
                    'message' => $message,
                    'dateAndTime' => $dt->format('Y-m-d H:i:s'),
                    'subject' => 'No of stores instock count'
                ];

                $this->state->setAreaCode(Area::AREA_FRONTEND);
                $mailer = $this->transportBuilder
                        ->setTemplateIdentifier('products_no_of_stores_in_stock_email')
                        ->setTemplateOptions([
                            'area' => \Magento\Backend\App\Area\FrontNameResolver::AREA_CODE,
                            'store' => Store::DEFAULT_STORE_ID,
                        ])
                        ->setTemplateVars($transport)
                        ->setFrom('general')
                        ->addTo(trim($recipient));
                    $transport = $mailer->getTransport();
                    $transport->sendMessage();
            } catch (\Exception $exception) {
                throw $exception;
            }
        }
    }
}
