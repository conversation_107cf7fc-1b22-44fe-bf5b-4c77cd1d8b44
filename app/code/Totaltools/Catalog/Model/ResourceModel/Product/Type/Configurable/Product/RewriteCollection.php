<?php
/**
 * <AUTHOR> Internet (<EMAIL>)
 * @package Totaltools_Catalog
 */
namespace Totaltools\Catalog\Model\ResourceModel\Product\Type\Configurable\Product;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Model\Product\Attribute\Source\Status as ProductStatus;
use Magento\CatalogUrlRewrite\Model\ProductUrlRewriteGenerator;
use Magento\Customer\Api\GroupManagementInterface;
use Magento\Framework\DB\Select;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\EntityManager\MetadataPool;
use Magento\Store\Model\Store;
use Magento\Catalog\Model\Product\Gallery\ReadHandler as GalleryReadHandler;
use Magento\Catalog\Model\ResourceModel\Product\Gallery;

/**
 * Rewrite Class Collection
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class RewriteCollection extends
    \Magento\ConfigurableProduct\Model\ResourceModel\Product\Type\Configurable\Product\Collection
{

    /**
     * @var Gallery
     */
    private $mediaGalleryResource;

    /**
     * @var GalleryReadHandler
     */
    private $productGalleryReadHandler;

    /**
     * @var MetadataPool
     */
    private $metadataPool;

    /**
     * Add media gallery data to loaded items
     *
     * @return $this
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @SuppressWarnings(PHPMD.NPathComplexity)
     */
    public function addMediaGalleryData()
    {
        if ($this->getFlag('media_gallery_added')) {
            return $this;
        }

        if (!$this->count()) {
            return $this;
        }

        /** @var $attribute \Magento\Catalog\Model\ResourceModel\Eav\Attribute */
        $attribute = $this->getAttribute('media_gallery');
        $select = $this->getMediaGalleryResource()->createBatchBaseSelect(
            $this->getStoreId(),
            $attribute->getAttributeId()
        );

        $mediaGalleries = [];
        $linkField = $this->getMetadataPool()->getMetadata(ProductInterface::class)->getLinkField();
        $items = $this->getItems();

        $select->where('entity.' . $linkField . ' IN (SELECT row_id FROM catalog_product_entity WHERE entity_id IN (?))', array_map(function ($item) {
            return $item->getId();
        }, $items));

        foreach ($this->getConnection()->fetchAll($select) as $row) {
            $mediaGalleries[$row[$linkField]][] = $row;
        }

        foreach ($items as $item) {
            $mediaEntries = isset($mediaGalleries[$item->getRowId()]) ? $mediaGalleries[$item->getRowId()] : [];
            $this->getGalleryReadHandler()->addMediaDataToProduct($item, $mediaEntries);
        }

        $this->setFlag('media_gallery_added', true);
        return $this;
    }

    /**
     * Retrieve GalleryReadHandler
     *
     * @return GalleryReadHandler
     * @deprecated 101.0.1
     */
    private function getGalleryReadHandler()
    {
        if ($this->productGalleryReadHandler === null) {
            $this->productGalleryReadHandler = ObjectManager::getInstance()->get(GalleryReadHandler::class);
        }
        return $this->productGalleryReadHandler;
    }

    /**
     * @deprecated
     * @return \Magento\Catalog\Model\ResourceModel\Product\Gallery
     */
    private function getMediaGalleryResource()
    {
        if (null === $this->mediaGalleryResource) {
            $this->mediaGalleryResource = ObjectManager::getInstance()->get(Gallery::class);
        }
        return $this->mediaGalleryResource;
    }

    /**
     * Get MetadataPool instance
     * @return MetadataPool
     */
    private function getMetadataPool()
    {
        if (!$this->metadataPool) {
            $this->metadataPool = ObjectManager::getInstance()->get(MetadataPool::class);
        }
        return $this->metadataPool;
    }
}
