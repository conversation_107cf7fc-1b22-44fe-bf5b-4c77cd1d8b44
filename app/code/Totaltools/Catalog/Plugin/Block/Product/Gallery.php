<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Catalog\Plugin\Block\Product;

class Gallery
{    

    /**
     * @var  mixed $catalogHelper
     */
    private $catalogHelper;
    /**
     * __construct
     *
     * @param  mixed $catalogHelper
     * @return void
     */
    public function __construct( \Magento\Catalog\Helper\Data $catalogHelper)
    {
        $this->catalogHelper = $catalogHelper;
    }
    
    /**
     * afterGetProduct
     *
     * @return \Magento\Catalog\Model\Product|null
     */
    public function afterGetProduct(
        \Magento\Catalog\Block\Product\Gallery $subject,
        $result
    ) {
        if(is_null($result)) {
            return $this->catalogHelper->getProduct();
        } 
        return $result;
    }
}