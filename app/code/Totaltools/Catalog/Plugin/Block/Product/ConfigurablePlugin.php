<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Catalog
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Catalog\Plugin\Block\Product;

use Magento\ConfigurableProduct\Block\Product\View\Type\Configurable;
use Magento\Framework\Locale\Format;

class ConfigurablePlugin
{
    /**
     * @var Format
     */
    private $localeFormat;

    /**
     * @param Format $localeFormat
     */
    public function __construct(Format $localeFormat)
    {
        $this->localeFormat = $localeFormat;
    }

    /**
     * @param Configurable $subject
     * @param string $config
     * @return string
     */
    public function afterGetJsonConfig($subject, $config)
    {
        $savings = [];

        foreach ($subject->getAllowProducts() as $product) {
            $showSavedLabel = $product->getData('show_saved_label') ?? 'N';

            $savings[$product->getId()] = [
                'showSavedLabel' => $showSavedLabel,
                'savings' => $this->calculateSavings($product),
            ];
        }

        if (count($savings)) {
            $config = json_decode($config);
            $config->savingsConfig = (object) $savings;
            $config = json_encode($config);
        }

        return $config;
    }

    /**
     * @param $product
     * @return array
     */
    protected function calculateSavings($product)
    {
        $priceInfo = $product->getPriceInfo();
        $savings = [];

        $regularPrice = $priceInfo->getPrice('regular_price')->getAmount()->getValue();
        $finalPrice = $priceInfo->getPrice('final_price')->getAmount()->getValue();

        if ($regularPrice != $finalPrice) {
            $saving = $this->localeFormat->getNumber($regularPrice - $finalPrice);
            $percentage = floor(($saving * 100) / $regularPrice);

            $savings['amount'] = $saving;
            $savings['percentage'] = $percentage;
        }

        return $savings;
    }
}
