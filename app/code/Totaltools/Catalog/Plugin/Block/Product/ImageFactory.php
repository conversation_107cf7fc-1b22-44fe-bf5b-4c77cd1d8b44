<?php
/**
 * @category    Totaltools
 * @package     Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */
namespace Totaltools\Catalog\Plugin\Block\Product;

use Magento\Catalog\Model\Product;
use Magento\Framework\View\ConfigInterface;
use Magento\Catalog\Model\Product\Image\ParamsBuilder;
use Magento\Catalog\Helper\Image as ImageHelper;

class ImageFactory 
{    
    /**
     * @var ConfigInterface
     */
    protected $presentationConfig;
    /**
     * @var ParamsBuilder
     */
    protected $imageParamsBuilder;

    /**
     * __construct
     *
     * @return void
     */
    public function __construct(
        ConfigInterface $presentationConfig,
        ParamsBuilder $imageParamsBuilder
    ) {
        $this->presentationConfig = $presentationConfig;
        $this->imageParamsBuilder = $imageParamsBuilder;
    }

        
    /**
     * beforeCreate
     *
     * @return void
     */
    public function beforeCreate(
        \Magento\Catalog\Block\Product\ImageFactory $subject,
        Product $product, 
        string $imageId, 
        array $attributes = null
        ) {
            $viewImageConfig = $this->presentationConfig->getViewConfig()->getMediaAttributes(
                'Magento_Catalog',
                ImageHelper::MEDIA_TYPE_CONFIG_NODE,
                $imageId
            );
    
            $imageMiscParams = $this->imageParamsBuilder->build($viewImageConfig);
            $labeledImage = $product->getLabeledImage();
            if (isset($labeledImage) && $labeledImage != 'no_selection') {
                $product->setData($imageMiscParams['image_type'], $labeledImage);
            }
            $result = [$product, $imageId, $attributes];
            return $result;
    }
}