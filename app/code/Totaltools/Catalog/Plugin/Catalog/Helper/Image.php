<?php

namespace Totaltools\Catalog\Plugin\Catalog\Helper;

class Image
{
    /**
     * @param \Magento\Catalog\Helper\Image $subject
     * @param $product
     * @param $imageId
     * @param $attributes
     * @return array
     */
    public function beforeInit(
        \Magento\Catalog\Helper\Image $subject,
        $product,
        $imageId,
        $attributes = []
    ) {
        $labeledImage = $product->getLabeledImage();
        if ($imageId == 'recently_viewed_products_grid_content_widget' && isset($labeledImage) && $labeledImage != 'no_selection') {
            $imageId = 'recently_viewed_products_grid_labeled_image';
        }

        return [$product, $imageId, $attributes];
    }
}
