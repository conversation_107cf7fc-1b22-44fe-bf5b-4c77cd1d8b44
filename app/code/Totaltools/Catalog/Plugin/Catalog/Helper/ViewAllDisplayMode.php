<?php

namespace Totaltools\Catalog\Plugin\Catalog\Helper;

use Amasty\Shopby\Helper\Category;
use Magento\Framework\App\RequestInterface;

class ViewAllDisplayMode
{
    /**
     * @var RequestInterface
     */
    private $request;

    public function __construct(RequestInterface $request)
    {
        $this->request = $request;
    }

    /**
     * After plugin for getChildrenCategoriesBlockDisplayMode method
     *
     * @param Category $subject
     * @param int $result
     * @return int
     */
    public function afterGetChildrenCategoriesBlockDisplayMode(Category $subject, int $result): int
    {
        if ($this->request->getParam('view-all') || strpos($this->request->getRequestUri(), '/view-all') !== false) {
            return 0; // Return 0 means display mode Disabled
        }
        
        return $result; 
    }
}
