<?php
/**
 * <AUTHOR> Dev Team <<EMAIL>>
 * @package    Totaltools_Catalog
 * @copyright  Copyright (c) 2019, Totaltools  (http://www.totaltools.com.au/)
 */

namespace Totaltools\Catalog\Plugin\Catalog\Block\Product;

use Magento\Framework\Pricing\PriceCurrencyInterface;

class View
{
    /**
     * @var PriceCurrencyInterface
     */
    protected $priceCurrency;

    /**
     * @param PriceCurrencyInterface $priceCurrency
     */
    public function __construct(PriceCurrencyInterface $priceCurrency)
    {
        $this->priceCurrency = $priceCurrency;
    }

    /**
     * @param \Magento\Catalog\Block\Product\View $subject
     * @param string $result
     * @return string
     */
    public function afterGetJsonConfig($subject, $result)
    {
        $config = \json_decode($result);

        if (is_object($config) && property_exists($config, 'priceFormat')) {
            $config->priceFormat->groupSymbol = '';
            $config->currencyFormatCode = $this->priceCurrency->getCurrencySymbol();
        }

        return \json_encode($config);
    }

    /**
     * Add Breadcrumbs Block
     *
     * @param \Magento\Catalog\Block\Product\View $subject
     * @param $result
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function afterSetLayout(\Magento\Catalog\Block\Product\View $subject, $result)
    {
        $subject->getLayout()->createBlock(\Magento\Catalog\Block\Breadcrumbs::class);

        return $result;
    }
}
