<?php

namespace Totaltools\Catalog\Plugin\Catalog\Model;

class Config
{
    public function afterGetAttributeUsedForSortByArray(
        \Magento\Catalog\Model\Config $catalogConfig,
        $options
    ) {
        $options =[];
        $options['search_order'] = 'Best Selling';
        $options['position'] = 'Position Asc';
        $options['position-desc'] = 'Position Desc';
        $options['price'] = 'Price - Low to High';
        $options['price-desc'] = 'Price - High to Low';
        $options['no_of_stores_in_stock'] = 'Stock Availability - High to Low';
        $options['no_of_stores_in_stock-desc'] = 'Stock Availability - Low to High';
        return $options;
    }
}
