<?php

namespace Totaltools\Catalog\Plugin\CustomerData;

use Magento\Quote\Model\Quote\Item as QuoteItem;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Registry;
use Psr\Log\LoggerInterface;
use Totaltools\Catalog\Helper\Notifications\Data;

class Cart
{
    /**
     * @var string
     */
    public const PROMO_ITEMS_KEY = 'am_promo_items';

    /**
     * @var QuoteItem
     */
    protected $quoteItem;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var Registry
     */
    protected $registry;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * Cart plugin constructor
     *
     * @param QuoteItem $quoteItem,
     * @param RequestInterface $request
     * @param Registry $registry
     * @param Data $helper
     */
    public function __construct(
        QuoteItem $quoteItem,
        RequestInterface $request,
        Registry $registry,
        Data $helper,
        LoggerInterface $logger
    ) {
        $this->quoteItem = $quoteItem;
        $this->request = $request;
        $this->registry = $registry;
        $this->helper = $helper;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Checkout\CustomerData\Cart $subject
     * @param array $result
     */
    public function afterGetSectionData(\Magento\Checkout\CustomerData\Cart $subject, $result)
    {
        if ($this->helper->isEnabled()) {
            $this->markRecentlyAddedItems($result);
        }

        return $result;
    }

    /**
     * Scans through current cart items and checks recently added items per current
     * add to cart request.
     *
     * @param array $result
     * @return array
     */
    protected function markRecentlyAddedItems(&$result)
    {
        $productId = $this->request->getParam('product', false);

        if (empty($result['items']) || !$productId) {
            return $result;
        }

        try {
            $productIds = [$productId];
            $relatedProducts = $this->request->getParam('related_product', '');
            $promoItems = $this->registry->registry(self::PROMO_ITEMS_KEY) ?? [];

            if (!empty($relatedProducts)) {
                $relatedProducts = explode(',', $relatedProducts);
                $productIds = array_merge($productIds, $relatedProducts);
            }

            if (!empty($promoItems)) {
                $promoItemsIds = array_map(function ($promoItem) {
                    return $promoItem->getProductId();
                }, $promoItems);

                $productIds = array_merge($productIds, $promoItemsIds);
            }

            foreach ($result['items'] as &$item) {
                /** @var \Magento\Quote\Model\Quote\Item $quoteItem */
                $quoteItem = $this->quoteItem->load($item['item_id']);
                $productId = $quoteItem->getProductId();

                if (in_array($productId, $productIds)) {
                    $item['recently_added'] = true;
                }

                if (is_array($relatedProducts) && in_array($productId, $relatedProducts)) {
                    $item['related_product'] = true;
                }

                if (isset($promoItemsIds) && in_array($productId, $promoItemsIds)) {
                    $item['promo_item'] = true;
                }
            }
        } catch (\Exception $e) {
            $this->logger->critical($e->getMessage());
        }

        return $result;
    }
}
