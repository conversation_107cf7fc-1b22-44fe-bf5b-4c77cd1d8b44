<?php

namespace Totaltools\Catalog\Plugin\Model\UrlBuilder;

use Amasty\ShopbyBase\Model\UrlBuilder\UrlModifier as BaseUrlModifier;
use Magento\Catalog\Model\Layer\Resolver as LayerResolver;
use Magento\Framework\Registry;

class UrlModifier
{
    /**
     * @var Registry
     */
    private $registry;

    /**
     * @var LayerResolver
     */
    private $layerResolver;

    /**
     * Constructor
     *
     * @param Registry $registry
     * @param LayerResolver $layerResolver
     */
    public function __construct(
        Registry $registry,
        LayerResolver $layerResolver
    ) {
        $this->registry = $registry;
        $this->layerResolver = $layerResolver;
    }

    /**
     * After Plugin for execute method
     *
     * @param BaseUrlModifier $subject
     * @param string $result
     * @param string $url
     * @param int|null $categoryId
     * @param bool $skipModuleCheck
     * @return string
     */
    public function afterExecute(
        BaseUrlModifier $subject,
        string $result,
        string $url,
        ?int $categoryId = null,
        bool $skipModuleCheck = false
    ): string {
        if (strpos($result, 'view-all/1/') !== false) {
            // Attempt to retrieve current category from registry
            $currentCategory = $this->registry->registry('current_category');

            if (!$currentCategory) {
                // Fallback to retrieve current category from layer navigation
                $layer = $this->layerResolver->get();
                $currentCategory = $layer->getCurrentCategory();
            }

            if ($currentCategory) {
                $categoryId = $currentCategory->getId();
                $urlKey = $currentCategory->getUrlPath();
                $modifiedUrl = $this->getCategoryViewAllModifiedUrl($categoryId, $urlKey, $result);
                return $modifiedUrl;
            }
        }

        return $result;
    }

    /**
     * Modify the URL by replacing specific segments
     *
     * @param int $categoryId
     * @param string $urlKey
     * @param string $url
     * @return string
     */
    private function getCategoryViewAllModifiedUrl($categoryId, $urlKey, $url)
    {
        // Replace 'view-all/1' with 'view-all'
        $url = str_replace('view-all/1/', 'view-all/', $url);

        // Replace 'catalog/category/view/id/$categoryId' with '$urlKey'
        $pattern = sprintf('/catalog\/category\/view\/id\/%d/', $categoryId);
        $url = preg_replace($pattern, $urlKey, $url);

        return $url;
    }
}
