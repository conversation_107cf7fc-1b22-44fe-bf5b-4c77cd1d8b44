<?php
/**
 * <AUTHOR> Dev (https://www.totaltools.com.au)
 * @package Totaltools_Catalog
 */

namespace Totaltools\Catalog\Plugin\Model\Category\Attribute\Source;

/**
 * Class Mode
 * @package Totaltools\Catalog\Plugin\Model\Category\Attribute\Source
 */
class Mode
{
    /**
     * @return array
     */
    public function afterGetAllOptions(
        \Magento\Catalog\Model\Category\Attribute\Source\Mode $subject,
        $options
    ) {
        $options[] = ['value' => \Totaltools\Catalog\Block\Category\View::DM_SUBCATEGORIES_PRODUCTS, 'label' => __('Subcategories and products')];
        $options[] = ['value' => \Totaltools\Catalog\Block\Category\View::DM_STATIC_BLOCKS_SUBCATEGORIES, 'label' => __('Static blocks and subcategories')];
        $options[] = ['value' => \Totaltools\Catalog\Block\Category\View::DM_SUBCATEGORIES_MIXED, 'label' => __('Subcategories, static blocks and products')];
        
        $default = ['value' => \Totaltools\Catalog\Block\Category\View::DM_SUBCATEGORIES, 'label' => __('Subcategories only')];        
        array_unshift($options, $default);

        return $options;
    }
}
