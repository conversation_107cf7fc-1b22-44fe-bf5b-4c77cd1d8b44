<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Catalog
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2018, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Catalog\Controller\Index;

use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use Magento\Framework\Controller\Result\JsonFactory;

/**
 * Class Count
 * @package Totaltools\Catalog\Controller\Index
 */
class Count extends Action
{
    /**
     * @var JsonHelper
     */
    protected $jsonHelper;

    /**
     * @var JsonFactory
     */
    protected $jsonFactory;

    /**
     * Index constructor.
     *
     * @param Context $context
     * @param JsonHelper $jsonHelper
     * @param JsonFactory $jsonFactory
     */
    public function __construct(
        Context $context,
        JsonHelper $jsonHelper,
        JsonFactory $jsonFactory
    ) {
        $this->jsonHelper = $jsonHelper;
        $this->jsonFactory = $jsonFactory;
        parent::__construct($context);
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\Result\Json|\Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $httpBadRequestCode = 400;
        //Check isXmlHttpRequest
        if (!$this->getRequest()->isXmlHttpRequest()) {
            return $this->jsonFactory->create()->setHttpResponseCode($httpBadRequestCode);
        }

        $html = $this->_view->getLayout()->createBlock(
            'Totaltools\Catalog\Block\Product\Count'
        )->toHtml();
        /** @var \Magento\Framework\Controller\Result\Json $resultJson */
        $resultJson = $this->jsonFactory->create();
        return $resultJson->setData(['html' => $html]);
    }
}