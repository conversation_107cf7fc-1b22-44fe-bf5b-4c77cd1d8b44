<?php


namespace Totaltools\Catalog\Controller\Adminhtml\Instock;


use Magento\Backend\App\Action;
use Magento\Backend\App\Action\Context;
use Magento\Framework\Controller\Result\Json;
use Magento\Framework\Controller\Result\JsonFactory;
use Totaltools\Catalog\Model\StoresInStockImporter;

class Sync extends Action
{
    /**
     * @var StoresInStockImporter
     */
    private StoresInStockImporter $storesInStockImporter;

    /**
     * @var \Magento\Framework\Filesystem\DirectoryList
     */
    private \Magento\Framework\Filesystem\DirectoryList $dir;

    /**
     * @param Context $context
     * @param JsonFactory $resultJsonFactory
     * @param StoresInStockImporter $storesInStockImporter
     */
    public function __construct(
        Context $context,
        JsonFactory $resultJsonFactory,
        StoresInStockImporter $storesInStockImporter,
        \Magento\Framework\Filesystem\DirectoryList $dir
    ) {
        $this->resultJsonFactory = $resultJsonFactory;
        parent::__construct($context);
        $this->context = $context;
        $this->storesInStockImporter = $storesInStockImporter;
        $this->dir = $dir;
    }

    /**
     * @return Json
     */
    public function execute()
    {
        /** @var Json $result */
        $result = $this->resultJsonFactory->create();
        try {
            $message = 'done';
            $directory = $this->dir->getRoot();
            $output = shell_exec('cd ' . $directory. ' && php bin/magento totaltools:instock:count');
            return $result->setData(['success' => $message, "data" => $output]);
        } catch (\Exception $e) {
            $message = $e->getMessage();
            return $result->setData(['error' => $message]);
        }
    }
}
