<?php

declare(strict_types=1);

namespace Totaltools\Catalog\Controller;

use Magento\Framework\App\Action\Forward;
use Magento\Framework\App\ActionFactory;
use Magento\Framework\App\ActionInterface;
use Magento\Framework\App\Request\Http as HttpRequest;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\App\RouterInterface;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Store\Model\StoreManagerInterface;
use Magento\UrlRewrite\Model\UrlFinderInterface;
use Magento\UrlRewrite\Service\V1\Data\UrlRewrite;

/**
 * View-all Controller Router
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class Router implements RouterInterface
{
    /**
     * @var ActionFactory
     */
    protected $actionFactory;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var UrlFinderInterface
     */
    protected $urlFinder;

    /**
     * @param ActionFactory $actionFactory
     * @param StoreManagerInterface $storeManager
     * @param UrlFinderInterface $urlFinder
     */
    public function __construct(
        ActionFactory $actionFactory,
        StoreManagerInterface $storeManager,
        UrlFinderInterface $urlFinder
    ) {
        $this->actionFactory = $actionFactory;
        $this->storeManager = $storeManager;
        $this->urlFinder = $urlFinder;
    }

    /**
     * Match corresponding URL Rewrite and modify request.
     *
     * @param RequestInterface|HttpRequest $request
     * @return ActionInterface|null
     * @throws NoSuchEntityException
     */
    public function match(RequestInterface $request)
    {
        $pathInfo = rtrim($request->getPathInfo() ?? '', '/');
        if (str_contains($pathInfo, "/view-all")) {
            $pathInfo = str_replace("/view-all", "", $pathInfo);
            $rewrite = $this->getRewrite(
                $pathInfo,
                $this->storeManager->getStore()->getId()
            );
    
            if ($rewrite === null) {
                return null;
            }
    
            $rewriteTargetPath = $rewrite->getTargetPath() ?? '';
            if ($rewriteTargetPath) {
                $rewriteTargetPath = rtrim($rewriteTargetPath, '/') . '/view-all/1';
            }
    
            $request->setPathInfo('/' . $rewriteTargetPath);
            return $this->actionFactory->create(
                Forward::class
            );
        } 

        return null;
    }

    /**
     * Find rewrite based on request data
     *
     * @param string $requestPath
     * @param int $storeId
     * @return UrlRewrite|null
     */
    protected function getRewrite($requestPath, $storeId)
    {
        return $this->urlFinder->findOneByData(
            [
                UrlRewrite::REQUEST_PATH => ltrim($requestPath, '/'),
                UrlRewrite::STORE_ID => $storeId,
            ]
        );
    }
}
