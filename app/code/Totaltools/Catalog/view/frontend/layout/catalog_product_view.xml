<?xml version="1.0"?>

<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="breadcrumbs" template="Magento_Theme::html/breadcrumbs.phtml" />
        <referenceContainer name="content">
            <block class="Totaltools\Catalog\Block\Product\Content" name="totaltools.catalog.content" template="Totaltools_Catalog::product/content.phtml" before="-"/>
        </referenceContainer>
        <referenceContainer name="product.info.stock.sku">
            <block class="Magento\ConfigurableProduct\Block\Product\View\Type\Configurable" name="product.info.product.type.configurable.sku" template="Totaltools_Catalog::product/configurable_sku.phtml" after="product.info.sku">
                <arguments>
                    <argument name="at_call" xsi:type="string">getSku</argument>
                    <argument name="at_code" xsi:type="string">sku</argument>
                    <argument name="css_class" xsi:type="string">configurable-sku</argument>
                    <argument name="at_label" xsi:type="string">default</argument>
                    <argument name="add_attribute" xsi:type="string">itemprop="sku"</argument>
                </arguments>
            </block>
        </referenceContainer>
        <container name="product.info.details.wrapper.main" htmlTag="div">
            <referenceBlock name="product.info.blog.relatedposts.block" remove="true" />
        </container>
        <container name="list.combo.bundle.product" htmlTag="div">
            <referenceBlock name="right.column" remove="true" />
        </container>
        <referenceContainer name="before.body.end">
            <block class="Totaltools\Catalog\Block\CartNotifications" name="totaltools.cart.notifications" after="-"/>
        </referenceContainer>
    </body>
</page>
