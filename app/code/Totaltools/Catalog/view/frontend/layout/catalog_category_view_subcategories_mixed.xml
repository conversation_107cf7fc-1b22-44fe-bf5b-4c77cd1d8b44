<?xml version="1.0"?>
<!--
/**
 * Copyright (c) 2019, Totaltools. (http://www.totaltools.com.au/)
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="1column" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <block class="Totaltools\Catalog\Block\Product\SubCategories" name="subcategories" template="Totaltools_Catalog::product/subcategories.phtml" after="content.top.area"></block>
            <referenceBlock name="top.content" remove="false"/>
            <container name="wrapper.product" as="wrapper_product" label="wrapper product" htmlTag="div" htmlClass="full-wrapper-product">
                <container name="full.left" as="full_left" label="Sidebar left" htmlTag="div" htmlClass="full-left-side" />
                <container name="full.right" as="full_right" label="full right" htmlTag="div" htmlClass="full-right-side" />
            </container>
        </referenceContainer>
        <move element="category.products" destination="full.right" after="-"/>
        <move element="catalog.leftnav" destination="full.left" after="-"/>
        <move element="category.short.description" destination="wrapper.product" after="-"/>
    </body>
</page>