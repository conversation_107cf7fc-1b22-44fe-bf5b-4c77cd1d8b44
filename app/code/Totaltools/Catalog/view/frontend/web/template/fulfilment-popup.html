<div
    class="fulfillment-modal"
    data-bind="afterRender: handleModalRender"
    style="display: none;"
>
    <div class="fulfillment-modal-content">
        <div class="block-title">
            <h1 translate="'Check stock availability at nearby stores'"></h1>
        </div>
        <div class="store-form">
            <form>
                <input
                    type="text"
                    name="store-zipcode"
                    placeholder="Enter Postcode/Suburb for nearest store"
                    data-bind="value: storeTextInput, event: { keyup: estimateFetchSuburbs }"
                    autocomplete="off"
                />
                <div class="suburb-options" if="estimateSuburbs().length">
                    <ul class="suburb-list">
                        <!--ko foreach: { data: estimateSuburbs, as: 'suburb'} -->
                        <li class="item-suburb">
                            <a data-bind="click: function(data, event) {
                                $parent.fetchStores(city, postcode, event)
                            }">
                                <span data-bind="html: cityHtml + ' ' + postcodeHtml"></span>
                            </a>
                        </li>
                        <!-- /ko -->
                    </ul>
                </div>
                <button type="submit" data-bind="click: estimateFetchSuburbs">
                    <span data-bind="i18n: 'Find your nearest store'"></span>
                </button>
            </form>
            <div class="store-options" if="stores().length">
                <ul class="store-list">
                    <!--ko foreach: { data: stores, as: 'store'}-->
                    <li class="item-store">
                        <div class="store">
                            <a class="store-name" data-bind="click: function(data, event) {
                                $parent.selectStore(store, event)
                            }">
                                <span class="name" data-bind="text: store.store_name"></span
                            ></a>
                            <div class="delivery-type" if="store.stockMessage">
                                <span class="delivery-message" data-bind="css: {
                                    'available'      :  $data.stockMessage.code == '0',
                                    'low-stock'      :  $data.stockMessage.code == '1',
                                    'not-available'  :  $data.stockMessage.code >= '2'
                                    }, html: $data.stockMessage.message
                                ">
                                </span>
                            </div>
                            <span
                                class="city"
                                data-bind="text: $data.address + ' ' + $data.city + ', ' + $data.zipcode + ', ' + $data.state"
                                style="display: block; clear: both"
                            ></span>
                            <a
                                class="store-phone"
                                data-bind="text: $data.phone, attr: { href: 'tel:' + $data.phone }"
                                style="display: block; clear: both"
                            ></a>
                            <button
                                data-bind="click: function(data, event) {
                                    $parent.selectStore(store, event)
                                }, text: 'Set as my store'"
                                class="button button-primary"
                            ></button>
                        </div>
                    </li>
                    <!-- /ko -->
                </ul>
            </div>
        </div>
    </div>
</div>
