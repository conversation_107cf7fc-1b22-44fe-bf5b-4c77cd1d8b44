/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */
define(['jquery', 'underscore', 'addToCartNotification'], function ($, _) {
    'use strict';

    var cartNotifications = {
        options: {
            notificationsWrapper: '#add-cart-notifications',
        },

        /**
         * @pubilc
         */
        init: function () {
            if (arguments[0] && 'object' === typeof arguments[0]) {
                this.options = $.extend(this.options, arguments[0]);
            }

            this._bindEventListener();
        },

        /**
         * Adds event listener for ajax:addToCart event
         * fired each time product is added to cart.
         *
         * @private
         */
        _bindEventListener: function () {
            $(document).on(
                'ajax:addToCart',
                function (ev, data) {
                    var cartItems = data?.response?.cart?.cart?.items ?? [];

                    if (cartItems.length) {
                        this._createWrapper()
                            ._createProductsNotification(cartItems);
                    }
                }.bind(this)
            );
        },

        /**
         * @private
         * @param {Array} items
         */
        _createProductsNotification: function (items) {
            var opts = this.options;
            var filteredItems = items.filter(function (item) {
                return item?.recently_added === true;
            });

            _.each(filteredItems, function (item) {
                $('<div/>').addToCartNotification({
                    data: item,
                    ...opts,
                });
            });

            return this;
        },

        _createWrapper: function () {
            var wrapper = this.options.notificationsWrapper;

            if (!$(wrapper).length) {
                $('body').append(
                    '<div id="' + wrapper.replace('#', '') + '"/>'
                );
            }

            return this;
        },
    };

    return cartNotifications;
});
