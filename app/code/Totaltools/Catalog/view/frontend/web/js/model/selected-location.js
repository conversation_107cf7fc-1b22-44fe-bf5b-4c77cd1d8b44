define([
    'jquery',
    'underscore',
    'ko',
    'Magento_Customer/js/customer-data',
    'Totaltools_Geo/js/model/geolocation',
], function ($, _, ko, customerData, geoLocation) {
    'use strict';

    var suburb = ko.observable(null),
        fulfilmentData = customerData.get('fulfilment-data');

    geoLocation.location.subscribe(function (location) {
        if (!_.isEmpty(location) && !location?.host) {
            suburb(location);
        }
    });

    fulfilmentData.subscribe(function (data) {
        if (data['location'] && !_.isEmpty(data.location)) {
            suburb(data.location);
        }
    });

    return {
        /**
         * Explicitly subscribe to suburb
         * in dependant components to get any changes
         */
        suburb: suburb,

        getSuburb: function () {
            return this.suburb;
        },

        setSuburb: function (data) {
            return this.suburb(data);
        },
    };
});
