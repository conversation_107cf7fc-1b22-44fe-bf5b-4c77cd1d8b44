/**
 * @category    Totaltools
 * @package     Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */
define([
    'jquery',
    'mage/url',
    'Magento_Customer/js/customer-data'
], function ($, url, customerData) {
    'use strict';

    return function (widget) {
        $.widget(
            'totaltools.addToCartNotification',
            $.totaltools.addToCartNotification,
            {
                /**
                 * @inheritdoc
                 */
                _create: function () {
                    var customer = customerData.get('customer');

                    if (!customer().firstname) {
                        this.options.btnUrl = url.build('checkout');
                    }

                    this._super();
                },
            }
        );

        return $.totaltools.addToCartNotification;
    };
});
