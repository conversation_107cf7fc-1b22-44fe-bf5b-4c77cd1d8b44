/**
 * @category    Totaltools
 * @package     Totaltools_Catalog
 * <AUTHOR> Iqbal <<EMAIL>>
 * @description A custom jquery widget that applies equal height to items in product list.
 * @copyright   Copyright (c) 2021, Totaltools. <http://totaltools.com.au>
 */

define(['jquery', 'underscore', 'jquery-ui-modules/widget'], function ($, _) {
    'use strict';

    $.widget('totaltools.equalHeight', {
        options: {
            selectors: {
                detailsClass: '.product-item-details',
                nameClass: '.product-item-name',
                priceClass: '.price-box'
            },
            skipSelectors: [],
            updateOnResize: false,
            observerAdded: false,
        },

        /**
         * @inheritdoc
         */
        _create: function () {
            this._super();
            this._init();
            this._bindEventListeners();
        },

        /**
         * @private
         */
        _init: function () {
            var that = this,
                opts = this.options;

            _.each(opts.selectors, function (selector, key) {
                if (opts.skipSelectors.indexOf(selector) != -1) return;

                var $els = that.element.find(selector);

                if ($els.length) {
                    var height = that.getMaxHeight($els);

                    if (height > 0) {
                        $els.css({ minHeight: height + 'px' });
                    }

                    if (opts.updateOnResize && !opts.observerAdded) {
                        _.each($els, function(elem) {
                            that._bindResizeObserver.call(that, elem);
                        });
                    }
                }
            });

            opts.observerAdded = true;
        },

        /**
         * @param {HTMLElement} target
         * @returns {Object} this
         */
        _bindResizeObserver: function(elem) {
            let self = this;

            if (elem && window.ResizeObserver) {
                const observer = new ResizeObserver(self._init.bind(self));
                observer.observe(elem);
            }

            return this;
        },

        /**
         * @private
         */
        _bindEventListeners: function () {
            var that = this;

            $(window).on('resize', _.debounce(this._init.bind(that), 200));
            $(this.element).on('init', this._init.bind(that));
            $(this.element).on('reInit', this._init.bind(that));
        },

        /**
         * Returns maximum height for given selector all matching elements.
         * @public
         * @param {jQuery} $elems
         * @returns {Number}
         */
        getMaxHeight: function ($elems) {
            var maxHeight = 0;

            $elems.css({ height: 'auto', minHeight: 'auto' });
            $elems.each(function () {
                var $el = $(this),
                    elHeight = $el[0].getBoundingClientRect().height;

                if (elHeight > maxHeight) {
                    maxHeight = elHeight;
                }
            });

            return parseFloat(maxHeight);
        },
    });

    return $.totaltools.equalHeight;
});
