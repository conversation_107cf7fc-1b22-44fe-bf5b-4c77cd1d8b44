/**
 * @category    Totaltools
 * @package     Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2021, Totaltools. <http://totaltools.com.au>
 */

define(['jquery', 'equalHeight'], function ($) {
    'use strict';

    return function (Listing) {
        return Listing.extend({
            /**
             * Applies equalHeight widget to the list once it is rendered in viewport
             * FadeIn added to buy time before list being fully rendered
             *
             * @param {HTMLElement} list
             */
            listRenderCallback: function (list) {
                var self = this;

                if ($(list).height() > 50) {
                    $(list).equalHeight({ updateOnResize: true });
                } else {
                    setTimeout(self.listRenderCallback.bind(self, list), 500);
                }
            },
        });
    };
});
