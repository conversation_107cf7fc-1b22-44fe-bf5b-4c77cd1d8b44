/**
 * @category    Totaltools
 * @package     Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2023, Totaltools. <http://totaltools.com.au>
 */

define(['priceUtils'], function (priceUtils) {
    'use strict';

    return function (finalPrice) {
        return finalPrice.extend({
            /**
             * @inheritdoc
             */
            getMinValueUnsanitizedHtml: function (row) {
                return (
                    '<span class="price">' +
                    priceUtils.getPriceHtml(row['price_info']['minimal_price']) +
                    '</span>'
                );
            },
        });
    };
});
