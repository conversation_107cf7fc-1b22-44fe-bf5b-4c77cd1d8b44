/**
 * <AUTHOR> Dev Team
 * @package    Totaltools_Catalog
 * @description A custom jquery widget to add sticky behavior to elements in catalog view
 * @copyright  Copyright (c) 2019, Totaltools.  (http://www.totaltools.com.au/)
 */
define(['jquery', 'underscore', 'jquery-ui-modules/widget', 'totaltools/quantifier'], function(
    $,
    _,
    ui,
    quantifier
) {
    'use strict';

    $.widget('totaltools.sticky', {
        options: {
            mainContainer: '.product-addto-container',
            stickyClass: 'affixed',
            titleCol: '#product-addto-bar .title-col',
            addtoCol: '#product-addto-bar .addto-cart-col',
            qtyAll: 'input[name="qty"]',
            qtyMain: '#product_addtocart_form #qty',
            qtySticky: '#product-addto-bar #qty',
            submitMain: '#product_addtocart_form #product-addtocart-button',
            submitSticky: '#product-addto-bar #product-addtocart-button'
        },

        /**
         * @private
         */
        _create: function() {
            this._super();
            this._popuplate();
            this._bindEventListeners();
            this._stick();
        },

        /**
         * @private
         */
        _popuplate: function() {
            var opt = this.options;

            var $title = $(opt.titleCol);
            var titleData = $title.data('target');

            var $addto = $(opt.addtoCol);
            var addtoData = $addto.data('target');

            $('.' + titleData.title)
                .clone()
                .appendTo($title); // Add title to sticky bar

            $('.' + titleData.meta)
                .clone()
                .appendTo($title); // Add SKU and MPN to sticky bar

            $('.' + addtoData)
                .clone()
                .appendTo($addto); // Add, add to cart controls to sticky bar

            $(opt.qtyAll).quantifier(); // Apply Quantifier widget to all quantity inputs in the page.
        },

        /**
         * @private
         */
        _bindEventListeners: function() {
            var self = this;
            var opt = this.options;

            $(window).bind('scroll', null, _.debounce($.proxy(self._stick, self), 100));
            $(opt.submitSticky).bind('click', null, $.proxy(self._handleAddToCart, self));
            $(document).bind('ajaxComplete', null, $.proxy(self._handleAjaxComplete, self));
        },

        /**
         * @private
         */
        _stick: function() {
            var el = this.element;
            var opt = this.options;
            var $window = $(window);
            var isDesktop = $window.width() > 1024;

            if (!isDesktop) {
                el.removeClass(opt.stickyClass);
                return;
            }

            var $target = $(opt.mainContainer);

            if ($target.length) {
                var winOffset = $window.scrollTop();
                var targetOffset = $target.offset().top + $target.height();

                if (winOffset > targetOffset) {
                    el.addClass(opt.stickyClass);
                } else {
                    el.removeClass(opt.stickyClass);
                }
            }
        },

        /**
         * @private
         */
        _handleAddToCart: function() {
            var opt = this.options;

            var $qtySticky = $(opt.qtySticky);
            var qtyStickyVal = $qtySticky.val();
            var $qtyMain = $(opt.qtyMain);
            var qtyMainVal = $qtyMain.val();

            var $addToCartMain = $(opt.submitMain);
            var $addToCartSticky = $(opt.submitSticky);

            var deferred = $.Deferred();
            deferred
                .then(function() {
                    $qtyMain.val(qtyStickyVal);
                })
                .then(function() {
                    $addToCartMain.trigger('click'); // Trigger click event on main add to cart button
                    $addToCartSticky.attr('disabled', true); // Disable submit button on sticky bar.
                })
                .then(function() {
                    $qtyMain.val(qtyMainVal);
                });

            deferred.resolve();
        },

        /**
         * @private
         */
        _syncQty: function() {
            // var mainQty = $().
        },

        /**
         * @private
         */
        _handleAjaxComplete: function(e, x, s) {
            $(this.options.submitSticky).removeAttr('disabled');
        },

        /**
         * @private
         */
        _reset: function() {}
    });

    return $.totaltools.sticky;
});
