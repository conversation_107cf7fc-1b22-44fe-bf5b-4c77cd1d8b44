/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */
define(['jquery', 'mage/template', 'jquery-ui-modules/widget'], function ($, mageTemplate) {
    'use strict';

    $.widget('totaltools.addToCartNotification', {
        options: {
            templateId: '#cart-notifications-template',
            notificationsWrapper: null,
            canAutoHide: true,
            hasCloseBtn: true,
            position: 'top-right',
            holdup: 30000,
            fadeTime: 500,
            data: null,
            title: 'Great choice!',
            btnText: 'View Cart',
            btnUrl: window.checkout.checkoutUrl,
            promoText: 'Buy now and get it in 3 days',
        },
        notification: null,

        /**
         * @inheritdoc
         */
        _create: function () {
            this.init();
        },

        /**
         * @public
         */
        init: function () {
            this._createNotification()
                ._renderNotification();
        },

        /**
         * @private
         */
        _createNotification: function () {
            var self = this;
            var opts = this.options;

            if ('object' === typeof opts.data) {
                let notificationTemplate = mageTemplate(
                    $(opts.templateId).html()
                );

                this.notification = $(
                    notificationTemplate({
                        data: opts.data,
                        opts: opts,
                    })
                ).delegate('.close', 'click', function (ev) {
                    self._destroy();
                });
            }

            return this;
        },

        /**
         * @private
         */
        _renderNotification: function () {
            var opts = this.options;

            if (this.notification && $(opts?.notificationsWrapper).length) {
                $(opts.notificationsWrapper).addClass(opts.position);

                this.notification
                    .hide()
                    .appendTo(opts.notificationsWrapper)
                    .fadeIn(opts.fadeTime);

                this._autoHideNotification();
            }

            return this;
        },

        /**
         * @private
         */
        _autoHideNotification: function () {
            var opts = this.options;

            if (opts.canAutoHide) {
                setTimeout(this._destroy.bind(this), opts.holdup);
            }
        },

        /**
         * @private
         */
        _destroy: function () {
            if (this.notification) {
                this.notification.fadeOut(
                    this.options.fadeTime,
                    function () {
                        this.notification.remove();
                        this.notification = null;
                    }.bind(this)
                );
            }
        },
    });

    return $.totaltools.addToCartNotification;
});
