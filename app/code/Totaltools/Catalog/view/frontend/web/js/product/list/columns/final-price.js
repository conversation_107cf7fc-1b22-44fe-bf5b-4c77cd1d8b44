/**
 * @category    Totaltools
 * @package     Totaltools_Catalog
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2021, Totaltools. <http://totaltools.com.au>
 */

define(['priceUtils'], function (priceUtils) {
    'use strict';

    return function (finalPrice) {
        return finalPrice.extend({
            /**
             * @inheritdoc
             */
            getPrice: function (row) {
                return this.format(row['price_info']['final_price']);
            },

            /**
             * @inheritdoc
             */
            getRegularPrice: function (row) {
                if (this.hasSpecialPrice(row)) {
                    var showSaveLabel = row['extension_attributes']['show_saved_label'];
                    var savings =
                        row['price_info']['regular_price'] -
                        row['price_info']['max_price'];
                        if(showSaveLabel == 'D') {
                            return 'Save ' + priceUtils.getPriceHtml(savings);
                        } else if(showSaveLabel == 'P') {

                            var final_price =  row['price_info']['final_price'];
                            var regular_price =  row['price_info']['regular_price'];
                            var yousavepct =  Math.floor(100*((regular_price - final_price)/regular_price));
                            return 'Save '+yousavepct+'%';
                         }
                    return 'Save ' + priceUtils.getPriceHtml(savings);
                }

                return this.format(row['price_info']['regular_price']);
            },

            /**
             * @inheritdoc
             */
            getMaxPrice: function (row) {
                return this.format(row['price_info']['max_price']);
            },

            /**
             * @inheritdoc
             */
            getMaxRegularPrice: function (row) {
                if (this.hasSpecialPrice(row)) {
                    var showSaveLabel = row['extension_attributes']['show_saved_label'];

                    if(showSaveLabel == 'D') {
                        var savings =
                        row['price_info']['regular_price'] -
                        row['price_info']['max_price'];
                        return 'Save ' + priceUtils.getPriceHtml(savings);
                    } else if(showSaveLabel == 'P') {
                        var final_price =  row['price_info']['final_price'];
                        var regular_price =  row['price_info']['regular_price'];
                        var yousavepct =  Math.floor(100*((regular_price - final_price)/regular_price));
                        return 'Save '+yousavepct+'%';
                     }
                }

                return this.format(row['price_info']['max_regular_price']);
            },

            /**
             * @inheritdoc
             */
            getMinimalPrice: function (row) {
                return this.format(row['price_info']['minimal_price']);
            },

            /**
             * @inheritdoc
             */
            getMinRegularPrice: function (row) {
                if (this.hasSpecialPrice(row)) {
                    var savings =
                        row['price_info']['regular_price'] -
                        row['price_info']['min_price'];

                    return 'Save ' + priceUtils.getPriceHtml(savings);
                }

                return this.format(row['price_info']['min_regular_price']);
            },

            /**
             * @param {Number} price
             * @returns {String}
             */
            format: function (price) {
                return (
                    '<span class="price">' +
                    priceUtils.getPriceHtml(price) +
                    '</span>'
                );
            },

            /**
             * @param {Object} row
             * @return {Boolean}
             */
            hasSavings: function (row) {
                let showSaveLabel = row['extension_attributes']['show_saved_label'];

                return this.hasSpecialPrice(row) && showSaveLabel !== 'N';
            }
        });
    };
});
