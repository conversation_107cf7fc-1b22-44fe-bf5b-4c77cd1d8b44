/**
 * <AUTHOR> Dev Team
 * @package    Totaltools_Catalog
 * @description A custom jquery widget to add increment/decrement controls to the quantity input.
 * @copyright  Copyright (c) 2019, Totaltools.  (http://www.totaltools.com.au/)
 */
define(['jquery', 'jquery-ui-modules/widget'], function($) {
    'use strict';

    $.widget('totaltools.quantifier', {
        options: {
            parentClass: 'quantity-widget',
            inputClass: 'quantity-widget-input',
            controlsClass: 'handle',
            decrementBtn: '[data-action=decrement]',
            decrementClass: 'decrease',
            incrementBtn: '[data-action=increment]',
            incrementClass: 'increase',
            minQty: 1,
            maxQty: 1000,
            updateAll: true
        },

        _create: function() {
            this._super();
            this._buildControls();
            this._updateContraints();
        },

        _destory: function() {
            this._destroyControls();
        },

        _buildControls: function() {
            var el = this.element;
            var options = this.options;

            var incrementControl = $(
                '<span type="button" class="' + options.controlsClass + ' ' + options.incrementClass + '" data-action="increment"><span>-</span></span>'
            );
            var decrementControl = $(
                '<span type="button" class="' + options.controlsClass + ' ' + options.decrementClass + '" data-action="decrement"><span>+</span></span>'
            );

            el.addClass(options.inputClass)
                .parent()
                .addClass(options.parentClass);

            decrementControl.insertBefore(el);
            incrementControl.insertAfter(el);

            decrementControl.bind('click', $.proxy(this._handleDecrement, this));
            incrementControl.bind('click', $.proxy(this._handleIncrement, this));
            el.bind('blur', $.proxy(this._ensureQtyContraints, this));
            el.bind('change', $.proxy(this._syncQty, this));
        },

        _updateContraints: function() {
            var el = this.element;
            var options = this.options;
            var validation = el.data('validate');

            if (typeof validation === 'object' && validation.hasOwnProperty('validate-item-quantity')) {
                
                if (typeof validation['validate-item-quantity'].minAllowed !== "undefined") {
                    options.minQty = validation['validate-item-quantity'].minAllowed;
                }
                if (typeof validation['validate-item-quantity'].maxAllowed !== "undefined") {
                    options.maxQty = validation['validate-item-quantity'].maxAllowed;
                }
            }
        },

        _handleDecrement: function(e) {
            if (this.element.is(':disabled')) {
                return;
            }

            var val = this.element.val();

            if (val > this.options.minQty) {
                this.element.val(--val);
                this.element.trigger('change');
            } else {
                alert("You can't add less than " + this.options.minQty + ' item.');
            }
        },

        _handleIncrement: function(e) {
            if (this.element.is(':disabled')) {
                return;
            }

            var val = this.element.val();

            if (val < this.options.maxQty) {
                this.element.val(++val);
                this.element.trigger('change');
            } else {
                alert("You can't add more than " + this.options.maxQty + ' items.');
            }
        },

        _ensureQtyContraints: function() {
            var el = this.element;
            var val = el.val();

            if (val < this.options.minQty) {
                el.val(this.options.minQty);
            } else if (val > this.options.maxQty) {
                el.val(this.options.maxQty);
            }
        },

        _destroyControls: function() {
            var el = this.element;
            var options = this.options;

            el.removeClass(options.inputClass)
                .parent()
                .removeClass(options.parentClass)
                .css({ width: '' })
                .find('.' + options.controlsClass)
                .remove();
        },

        _syncQty: function() {
            if (this.options.updateAll) {
                var newVal = this.element.val();
                $('.' + this.options.inputClass).val(newVal);
            }
        }
    });

    return $.totaltools.quantifier;
});
