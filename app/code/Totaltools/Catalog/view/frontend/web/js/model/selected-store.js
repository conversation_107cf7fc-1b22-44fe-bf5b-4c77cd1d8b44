define([
    'jquery',
    'underscore',
    'ko',
    'Magento_Customer/js/customer-data',
    'Totaltools_Geo/js/model/geolocation',
], function ($, _, ko, customerData, geoLocation) {
    'use strict';

    var store = ko.observable(null),
        fulfilmentData = customerData.get('fulfilment-data');

    geoLocation.location.subscribe(function (location) {
        if (location['store'] && !_.isEmpty(location.store)) {
            store(location.store);
        }
    });

    fulfilmentData.subscribe(function (data) {
        if (data['store'] && !_.isEmpty(data.store)) {
            store(data.store);
        }
    });

    return {
        /**
         * Explicitly subscribe to store
         * in dependant components to get any changes
         */
        store: store,

        getStore: function () {
            return this.store;
        },

        setStore: function (data) {
            return this.store(data);
        },
    };
});
