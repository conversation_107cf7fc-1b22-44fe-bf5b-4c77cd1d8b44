define([
    'jquery',
    'mage/utils/wrapper',
    'Totaltools_Catalog/js/view/fulfilment-boxes',
    'Totaltools_Catalog/js/model/selected-store'
], function ($, wrapper, fulfilmentBoxes, selectedStore) {
    'use strict';
    return function(targetModule){
        var store = selectedStore.getStore()();
        var reloadPrice = targetModule.prototype._reloadPrice;
        var reloadPriceWrapper = wrapper.wrap(reloadPrice, function(original){
            var result = original();
            var simpleSku = this.options.spConfig.skus[this.simpleProduct];

            if(simpleSku != '') {
                $('.product-info-stock-sku .configurable-sku .value').html(simpleSku);
                if (store && store['zipcode']) {
                    fulfilmentBoxes()._updateClickAndCollectMessage(store.zipcode);
                }
            }
            return result;
        });


        targetModule.prototype._reloadPrice = reloadPriceWrapper;
        return targetModule;
    };
});