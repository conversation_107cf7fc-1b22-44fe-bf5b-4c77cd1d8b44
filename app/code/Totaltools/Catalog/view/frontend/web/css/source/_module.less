//
//  Vars
//  _____________________________________________
@notification_title__color: #E41B13;
@notification_cta__background-color: @notification_title__color;
@notification_cta__border-color: #c4291a;

@quantity-widget-handle__increase-icon: '\f067';
@quantity-widget-handle__decrease-icon: '\f068';
@quantity-widget-handle__background-color: #2e2d76;
@quantity-widget-handle__size: 18px;
@quantity-widget-handle__font-size: 12px;
@quantity-widget-handle__line-height: 18px;
@quantity-widget-handle__color: #FFF;
@color-gray96: #f1f1f1;
@color-blue5: #272664;
@color-blue: #2e2d76;
@color-black-25: rgba(0,0,0,0.25);
@color-red2: #e41b13;
//
//  Common
//  _____________________________________________
& when (@media-common = true) {
    // Cart notifications
    #add-cart-notifications {
        max-width: 300px;
        position: fixed;
        z-index: 999999;

        &.top-right {
            top: 15%;
            right: @indent__m;
        }

        &.top-left {
            top: 15%;
            left: @indent__m;
        }

        &.bottom-right {
            bottom: 10%;
            right: 15%;
        }

        &.bottom-left {
            bottom: 10%;
            left: 15%;
        }

        .notification {
            background-color: rgba(247, 247, 247, 0.98);
            position: relative;
            .lib-css(padding, @indent__m);
            .lib-css(margin-bottom, @indent__base);
            .lib-css(border-radius, 3px, 1);
            .lib-css(box-shadow, 0 0 5px rgba(0, 0, 0, 0.25), 1);
            .lib-css(transition, all 0.2s ease, 1);

            .close {
                display: block;
                position: absolute;
                text-align: center;
                color: #999;
                width: 20px;
                height: 20px;
                top: 15px;
                right: 10px;
                cursor: pointer;
                .lib-font-size(14);
                .lib-css(transition, color 0.2s ease, 1);

                .lib-icon-font(
                    @_icon-font-content: '\e906',
                    @_icon-font-text-hide: true
                );

                &:hover {
                    color: #666;
                }
            }

            &-title {
                text-transform: initial;
                letter-spacing: normal;
                text-align: center;
                margin: 0 0 @indent__m;
                .lib-line-height(18);
                .lib-css(font-family, @font-family__base);
                .lib-css(color, @notification_title__color);
            }

            &-message {
                width: 100%;
                text-align: center;
                display: none;
                .lib-css(font-weight, @font-weight__bold);

                body.test_36 & {
                    display: block;
                    .lib-css(margin-top, @indent__s);
                }
            }

            &.cart-reminder {
                .notification-message {
                    display: block;
                }
            }

            &-content {
                .lib-vendor-prefix-display();
                .lib-vendor-prefix-flex-wrap();

                .product-thumb {
                    width: 70px;
                    height: 70px;
                    border: 2px solid #e5e5e5;
                    background: white;
                    padding: 3px;
                    text-align: center;
                    .lib-css(box-sizing, border-box, 1);

                    img {
                        max-width: 100%;
                    }
                }

                .product-name {
                    width: ~"calc(100% - 70px)";
                    margin-left: auto;
                    .lib-css(padding-left, @indent__m);
                    .lib-css(box-sizing, border-box, 1);
                }

                .cart-link {
                    width: 100%;
                    .lib-css(margin-top, @indent__m);

                    a {
                        display: block;
                        width: 100%;
                        text-align: center;
                        .lib-css(font-family, @font-family__base);
                        .lib-css(border-color, @notification_cta__border-color);
                        .lib-css(background-color, @notification_cta__background-color);
                    }
                }
            }
        }
    }

    // Quantity widget
    .quantity-widget {
        .lib-vendor-prefix-display;
        .lib-vendor-prefix-flex-wrap(nowrap);
        .lib-vendor-box-align(center);

        .handle {
            text-align: center;
            cursor: pointer;
            position: relative;
            .lib-css(background, @quantity-widget-handle__background-color);
            .lib-css(width, @quantity-widget-handle__size);
            .lib-css(height, @quantity-widget-handle__size);
            .lib-css(border-radius, 50%, 1);
            .lib-font-size(@quantity-widget-handle__font-size);
            .lib-line-height(@quantity-widget-handle__line-height);
            .lib-css(color, @quantity-widget-handle__color);
            .lib-css(transition, all 0.2s ease, 1);
            .lib-icon-font('', @_icon-font-text-hide: true);

            &.increase {
                .lib-icon-font-symbol(@quantity-widget-handle__increase-icon);
            }

            &.decrease {
                .lib-icon-font-symbol(@quantity-widget-handle__decrease-icon);
            }

            &:hover {
                .lib-css(background-color, lighten(@quantity-widget-handle__background-color, 10%));
            }

            &:active {
                .lib-css(background-color, darken(@quantity-widget-handle__background-color, 10%));
            }
        }

        .qty {
            text-align: center;
            max-width: 60px;
            .lib-css(margin, 0 @indent__xs);
        }
    }

    @media screen and (max-width: 359px) {
        .quantity-widget {
            .lib-vendor-prefix-flex-direction(row-reverse);
            position: relative;

            .handle {
                position: absolute;
                height: 50%;
                left: 100%;

                &.increase {
                    margin: 0;
                    top: 0;
                    border-bottom: 1px solid rgba(255, 255, 255, 0.25);
                    .lib-css(border-radius, 0 3px 0 0, 1);
                }

                &.decrease {
                    bottom: 0;
                    margin: 0;
                    z-index: 1;
                    .lib-css(border-radius, 0 0 3px 0, 1);
                }

                &:before {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    .lib-css(transform, translate(-50%, -50%), 1);
                }
            }

            .qty {
                .lib-css(margin, 0);
                .lib-css(border-radius, 3px 0 0 3px, 1);
            }
        }
    }

    .fulfillment-popup {
        .store-form {
            max-width: 100%;
        }
    }

    .fulfillment-modal {

        .block-title {
            h1 {
                font-weight: 700;
                text-align: center;
            }
        }
        .store-form {
            max-width: 100%;

            form {
                margin-bottom: 7px;
                
                input {
                    margin-bottom: 17px;
                }

                .suburb-options {
                    margin-bottom: 17px;
                    
                    .suburb-list {
                        list-style: none;
                        padding: 0;
                        margin: 0;
                        border-radius: 0 0 3px 3px;

                        li {
                            border: 1px solid @color-gray80;
                            border-width: 0 1px;
                            margin-bottom: 0;

                            &:first-child {
                                border-top-width: 1px;
                            }

                            &:last-child {
                                border-bottom-width: 1px;
                            }
                            
                            a {
                                display: block;
                                text-align: left;
                                padding: 12px;
                                background-color: @color-white;
                                font-size: 16px;
                                color: #333333;
                                cursor: pointer;
                                text-transform: capitalize;
                                
                                &:hover {
                                    text-decoration: none;
                                    background-color: @color-gray96;
                                }
                            }
                        }
                    }
                }

                button {
                    margin: 0;
                    background: @color-white;
                    border: 2px solid @color-blue5;
                    color: @color-blue;
                    font-weight: 700;
                    cursor: pointer;
                    padding: 13px 18px;
                    font-size: 17px;
                    line-height: 18px;
                    box-sizing: border-box;
                    vertical-align: middle;
                    border-radius: 3px;
                    width: 100%;

                    &:hover {
                        background: @color-blue;
                        border-color: @color-blue5;
                        color: @color-white;
                    }
                }
            }
        }
    }

    .catalog-product-view {
        .modals-wrapper {
            .modal-slide {
                &.modal-popup {
                    z-index: 1001;
                }
            }
        }
    }

    // Loyalty Points Popup
    #loyalty-points-popup {
        height: auto;
        max-height: unset;
        min-height: unset !important;
        max-width: 300px;
        width: ~"calc(100% - 30px)";
        top: 15%;
        margin: 0;
        right: 15px;
        position: fixed;
        left: auto;    
        border-radius: 3px;    
        background-color: rgba(247,247,247,.98);
        box-shadow: 0 0 5px @color-black-25;
        padding: 18px 15px 15px;
        z-index: 50;
        transition: 0.6s all ease;
        
        .close {
            position: absolute;
            text-align: center;
            color: @color-gray-light6;
            width: 20px;
            height: 20px;
            top: 15px;
            right: 10px;
            cursor: pointer;
            font-size: 14px;
            transition: color .2s ease;
            display: inline-block;
            text-decoration: none;
            &:hover {
                color: @color-gray40;
            }
            &::before {
                font-size: inherit;
                line-height: inherit;
                color: inherit;
                content: '\e906';
                font-family: 'TT Icons';
                vertical-align: middle;
                display: inline-block;
                font-weight: 400;
                text-align: center;
            }
        }
        h2 {
            font-size: 16px;
            line-height: 18px;
            margin: 0px 0 8px;
            color: @color-red2;
            text-transform: capitalize;
        }
        p {
            font-size: 15px;
            line-height: 18px;
            margin-bottom: 0;
        }
    }

    .full-wrapper-product {
        margin-top: 20px;
    }

    ul:has(> .full-wrapper-product) {
        padding: 0;
        margin: 0;
    }
}

//
//  Mobile
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='max') and (@break = @screen__m) {
    .catalog-category-view {
        &.page-layout-1column { 
            .column {
                &.main { 
                    .full-right-side {
                        #amasty-shopby-product-list { 
                            .toolbar-products {
                                display: block;
                            }
                        }
                    }
                }
            }

            .full-wrapper-product {
                .toolbar-products { 
                    #shopby-mobile {
                        display: block;
                        margin-bottom: 5px;
                    }
                }
            }
        }
    }
}

 //
//  Desktop
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='min') and (@break = @screen__m) {
    // Cart notifications
    #add-cart-notifications {
        &.top-right {
            top: 10%;
            right: 15%;
        }

        &.top-left {
            top: 10%;
            left: 15%;
        }

        &.bottom-right {
            bottom: 10%;
            right: 15%;
        }

        &.bottom-left {
            bottom: 10%;
            left: 15%;
        }
    }

    .fulfillment-modal {
        .block-title {
            h1 {
                letter-spacing: -0.5px;
            }
        }
    }

    // Loyalty Points Popup
    #loyalty-points-popup {
        max-width: 300px;
        right: 15%;
        top: 10%;
    }

    .full-wrapper-product {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        .full-right-side {
            width: 80%;
            order: 2;
            padding-left: 1.6%;
        }

    }
}
.notification-message.add-to-compare{
    font-weight: 500 !important;
}
