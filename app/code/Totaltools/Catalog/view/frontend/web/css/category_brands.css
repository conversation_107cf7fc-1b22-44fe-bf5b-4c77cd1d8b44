.category-brands-container {
  width: 100%;
  display: flex;
  height: auto;
  align-items: center;
  flex-direction: column;
  justify-content: flex-start;
}

.category-brands-list {
  width: 100%;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.category-brands-item {
  flex: 0 0 auto;
  width: 16.7%;
  height: 176px;
  display: flex;
  align-items: center;
  border-color: #dddddd;
  border-style: solid;
  border-width: 1px;
  font-weight: 700;
  flex-direction: column;
  justify-content: center;
  margin: 0 -1px -1px 0;
}

.category-brands-item > a {
  font-size: 18px;
  border: 1px solid transparent;
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 1px;
  transition: border-color .2s ease;
}

.category-brands-item a:hover {
  text-decoration: none;
  border-color: #2E2D76;
}

.category-brands-item .subcategory-title {
  text-align: center;
}

.home-image {
  width: auto;
  height: 70px;
  object-fit: cover;
}

.uppercase {
  text-transform: uppercase;
}

h2.category_brands,
.category_brands h2 {
  font-size: 1.75rem;
}

.category_brands {
  display: block;
}

@media(max-width: 991px) {
  .category-brands-list {
    flex: 0;
    flex-wrap: wrap;
  }

  .category-brands-item {
    width: 33%;
  }

}

@media(max-width: 479px) {
  .category-brands-list {
    flex: 0;
    flex-wrap: wrap;
    justify-content: center;
  }

  .category-brands-item {
    width: 49.5%
  }

  .category-brands-item > a {
    font-size: 16px;
    line-height: 1.5;
  }

  .home-image {
    height: auto;
    max-width: 150px;
    max-height: 50px;
  }
}


@media(min-width: 769px) {
  .catalog-category-view.page-layout-2columns-left .category-brands-item > a {
    font-size: 16px;
    line-height: 1.5;
  }
}

@media(min-width: 992px) {
  .catalog-category-view.page-layout-2columns-left .category-brands-item > a {
    font-size: 15px;
    line-height: 1.25;
  }
}

@media(min-width: 1200px) {
  .catalog-category-view.page-layout-2columns-left .category-brands-item > a {
    font-size: 16px;
    line-height: 1.5;
  }
}
