/*
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */
var config = {
    map: {
        '*': {
            'utt-productcount': 'Totaltools_Catalog/js/productcount',
            'totaltools/quantifier': 'Totaltools_Catalog/js/widgets/quantifier',
            'totaltools/sticky': 'Totaltools_Catalog/js/widgets/sticky',
            'equalHeight': 'Totaltools_Catalog/js/widgets/equal-height',
            'cartNotifications': 'Totaltools_Catalog/js/cart-notifications',
            'addToCartNotification': 'Totaltools_Catalog/js/widgets/notification',
        },
    },

    config: {
        mixins: {
            'Magento_ConfigurableProduct/js/configurable': {
                'Totaltools_Catalog/js/model/skuswitch': true,
            },
            'Magento_Catalog/js/product/list/listing': {
                'Totaltools_Catalog/js/product/list/listing': true,
            },
            'Magento_Catalog/js/product/list/columns/final-price': {
                'Totaltools_Catalog/js/product/list/columns/final-price': true,
            },
            'Magento_GiftCard/js/product/list/columns/final-price': {
                'Totaltools_Catalog/js/product/giftcard/list/columns/final-price': true,
            },
            'Totaltools_Catalog/js/widgets/notification': {
                'Totaltools_Catalog/js/mixin/notification-mixin': true
            },
            'Magento_Banner/js/view/banner': {
                'Totaltools_Catalog/js/mixin/banner-mixin': true
            }
        },
    },
};
