<?php

/**
 * @var \Totaltools\Catalog\Block\CartNotifications $block
 */
?>

<?php if ($block->isEnabled()): ?>
<script type="text/x-magento-template" id="cart-notifications-template">
    <div class="notification">
        <% if (opts.hasCloseBtn) { %><span class="close"><span>x</span></span><% } %>
        <div class="notification-wrapper">
            <h4 class="notification-title"><%- opts.title %></h4>
            <div class="notification-content">
                <div class="product-thumb"><img src="<%- data.product_image.src %>" /></div>
                <div class="product-name">
                    <a href="<%- data.product_url %>"><%= data.product_name %></a> has been added to your cart.
                </div>
                <div class="notification-message"><%= opts.promoText %></div>
                <div class="cart-link"><a class="action primary" href="<%- opts.btnUrl %>"><%- opts.btnText %></a></div>
            </div>
        </div>
    </div>
</script>

<script>
    require(['cartNotifications'], function(notifications) {
        notifications.init(<?= /* @noEscape */ $block->getJsonConfig(); ?>);
    });

    window.showCustomAddToCartBehavior = true;
</script>
<?php endif; ?>