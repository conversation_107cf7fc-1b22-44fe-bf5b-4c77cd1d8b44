<?php
/**
 * @var $block \Totaltools\Catalog\Block\Product\CategoryList
 */

$_productCollection = $block->getLoadedProductCollection();
$_helper = $this->helper('Magento\Catalog\Helper\Output');

?>
<?php if (!sizeof($_productCollection)) : ?>
    <div class="message info empty"><div><?php echo __('We can\'t find products matching the selection.') ?></div></div>
    <?php return; ?>
<?php endif; ?>
<?php
$viewMode = 'grid';
$image = 'category_page_grid';
$showDescription = true;
$templateType = \Magento\Catalog\Block\Product\ReviewRendererInterface::SHORT_VIEW;
/**
 * Position for actions regarding image size changing in vde if needed
 */
$pos = $block->getPositioned();
$listId = 'products_list_' . rand(1000, 9999);
?>
<div class="products wrapper <?php /* @escapeNotVerified */ echo $viewMode; ?> products-<?php /* @escapeNotVerified */ echo $viewMode; ?>">
    <?php $iterator = 1; ?>
    <ol id="<?php echo $listId; ?>" class="products list items product-items" data-flickity="flkt_<?php echo $listId;?>">
        <?php /** @var $_product \Magento\Catalog\Model\Product */ ?>
        <?php foreach ($_productCollection as $_product): ?>
            <?php /* @escapeNotVerified */ echo($iterator++ == 1) ? '<li class="item product product-item">' : '</li><li class="item product product-item">' ?>
            <div class="product-item-info" data-container="product-grid">
                <?php
                $productImage = $block->getImage($_product, $image);
                if ($pos != null) {
                    $position = ' style="left:' . $productImage->getWidth() . 'px;'
                        . 'top:' . $productImage->getHeight() . 'px;"';
                }
                ?>
                <?php // Product Image ?>
                <a href="<?php /* @escapeNotVerified */ echo $_product->getProductUrl() ?>" class="product photo product-item-photo" tabindex="-1">
                    <?php echo $productImage->toHtml(); ?>
                </a>
                <div class="product details product-item-details">
                    <?php
                    $_productNameStripped = $block->stripTags($_product->getName(), null, true);
                    ?>

                    <?php if ($_product->getTypeId() == \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE) : ?>
                        <p class="various-text"><?php echo __('Available in various sizes');?></p>
                    <?php endif;?>

                    <?php /*if ($_product->getAttributeText('brand')): ?>
                        <div class="brand"><?php echo $_product->getAttributeText('brand');?></div>
                    <?php endif;*/ ?>

                    <strong class="product name product-item-name">
                        <a class="product-item-link"
                           href="<?php /* @escapeNotVerified */ echo $_product->getProductUrl() ?>">
                            <?php /* @escapeNotVerified */ echo $_helper->productAttribute($_product, $_product->getName(), 'name'); ?>
                        </a>
                    </strong>

                    <div class="sku"><?php echo __('SKU# %1', $_product->getSku());?></div>

                    <?php /* @escapeNotVerified */ echo $block->getProductPrice($_product) ?>
                    <?php echo $block->getReviewsSummaryHtml($_product, $templateType); ?>
                    <?php echo $block->getProductDetailsHtml($_product); ?>

                    <!-- For list mode-->
                    <?php if ($showDescription):?>
                        <div class="product description product-item-description">
                            <?php /* @escapeNotVerified */ echo $_helper->productAttribute($_product, $_product->getShortDescription(), 'short_description') ?>
                        </div>
                    <?php endif; ?>

                    <div class="product-item-inner">
                        <div class="product actions product-item-actions"<?php echo strpos($pos, $viewMode . '-actions') ? $position : ''; ?>>

                            <a href="<?php /* @escapeNotVerified */ echo $_product->getProductUrl() ?>" title="<?php /* @escapeNotVerified */ echo $_productNameStripped ?>"
                               class="button3"><?php /* @escapeNotVerified */ echo __('View Details') ?></a>

                            <div data-role="add-to-links" class="actions-secondary"<?php echo strpos($pos, $viewMode . '-secondary') ? $position : ''; ?>>
                                <?php if ($this->helper('Magento\Wishlist\Helper\Data')->isAllow()): ?>
                                    <a href="#"
                                       class="action towishlist"
                                       title="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       aria-label="<?php echo $block->escapeHtml(__('Add to Wish List')); ?>"
                                       data-post='<?php /* @escapeNotVerified */ echo $block->getAddToWishlistParams($_product); ?>'
                                       data-action="add-to-wishlist"
                                       role="button">
                                        <span><?php /* @escapeNotVerified */ echo __('Add to Wish List') ?></span>
                                    </a>
                                <?php endif; ?>
                                <?php
                                $compareHelper = $this->helper('Magento\Catalog\Helper\Product\Compare');
                                ?>
                                <a href="#"
                                   class="action tocompare"
                                   title="<?php echo $block->escapeHtml(__('Add to Compare')); ?>"
                                   aria-label="<?php echo $block->escapeHtml(__('Add to Compare')); ?>"
                                   data-post='<?php /* @escapeNotVerified */ echo $compareHelper->getPostDataParams($_product); ?>'
                                   role="button">
                                    <span><?php /* @escapeNotVerified */ echo __('Add to Compare') ?></span>
                                </a>
                            </div>
                        </div>

                    </div>
                </div>

                <div class="right-info">
                    <?php if ($_product->getIsSalable()): ?>
                        <div class="stock available"><span><?php  echo __('In stock') ?></span></div>
                    <?php else: ?>
                        <div class="stock unavailable"><span><?php  echo __('Out of stock') ?></span></div>
                    <?php endif; ?>

                    <div class="more-info-wrapper">
                        <div class="more-info">
                            <?php /* @escapeNotVerified */ echo $block->getProductPrice($_product) ?>

                            <a href="<?php /* @escapeNotVerified */ echo $_product->getProductUrl() ?>" title="<?php /* @escapeNotVerified */ echo $_productNameStripped ?>"
                               class="button3-2"><?php /* @escapeNotVerified */ echo __('View Details') ?></a>
                        </div>
                    </div>
                </div>

            </div>
            <?php echo($iterator == count($_productCollection)+1) ? '</li>' : '' ?>
        <?php endforeach; ?>
    </ol>
</div>
<?php if (!$block->isRedirectToCartEnabled()) : ?>
    <script type="text/x-magento-init">
    {
        "[data-role=tocart-form], .form.map.checkout": {
            "catalogAddToCart": {}
        }
    }
    </script>
<?php endif; ?>
<script>
    require([
        'jquery',
        'flickity'
    ], function ($, Flickity) {
        'use strict';

        $(document).ready(function () {

            var count_item  = <?php echo count($_productCollection);?>;
            var wrapAround  = false;

            if ($(window).width()> 767){
                if (count_item > 5){
                    wrapAround  = true;
                }
            }else{
                if (count_item > 2){
                    wrapAround  = true;
                }
            }
            var flkt_<?php echo $listId; ?> = new Flickity($('#<?php echo $listId; ?>').get(0), {
                contain: true,
                pageDots: false,
                cellAlign: 'left',
                wrapAround: wrapAround
            });

            $("#<?php echo $listId; ?>").on("udp_flickity", function(){
                flkt_<?php echo $listId; ?>.resize();

                updateHeightProductInfo2();
            });

            updateHeightProductInfo2();

        });

        function updateHeightProductInfo(){
            $(".product-item-details",'#<?php echo $listId; ?>').css("height","auto");
            var max_height  = 0;
            $(".product-item-details",'#<?php echo $listId; ?>').each(function(){
                if (max_height < $(this).height()){
                    max_height  = $(this).height();
                }
            });
            $(".product-item-details",'#<?php echo $listId; ?>').css("height",max_height);
        }

        function updateHeightProductInfo2(){
            $("#feature-products-block .products.list.items.product-items").each(function(){
                $(".product-item-details",$(this)).css("height","auto");
                var max_height2  = 0;
                $(".product-item-details",$(this)).each(function(){
                    if (max_height2 < $(this).height()){
                        max_height2  = $(this).height();
                    }
                });
                $(".product-item-details",$(this)).css("height",max_height2);
            })
        }

    });
</script>
