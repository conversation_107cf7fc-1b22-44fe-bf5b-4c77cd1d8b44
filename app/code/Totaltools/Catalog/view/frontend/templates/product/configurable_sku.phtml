<?php
/** @var $block \Magento\ConfigurableProduct\Block\Product\View\Type\Configurable*/
$_helper = $this->helper('Magento\Catalog\Helper\Output');
$_product = $block->getProduct();
$_attributeValue = 0;
$allowedProducts = [];
if ($_product->getTypeId() === 'configurable') :
    $allowedProducts = $block->getAllowProducts();
endif;
$_code = $block->getAtCode();
$_className = $block->getCssClass();
$_attributeLabel = $block->getAtLabel();
$_attributeType = $block->getAtType();
$_attributeAddAttribute = $block->getAddAttribute();
if (!empty($allowedProducts)):
    $_attributeValue = $allowedProducts[0]->getSku();
endif;
?>

<?php if ($_attributeValue) : ?>
    <div style="display: none" class="product attribute <?php /* @escapeNotVerified */ echo $_className ?>">
        <span class="value" <?php /* @escapeNotVerified */ echo $_attributeAddAttribute; ?>>
            <?php /* @escapeNotVerified */ echo $_attributeValue; ?>
        </span>
    </div>
<?php endif; ?>