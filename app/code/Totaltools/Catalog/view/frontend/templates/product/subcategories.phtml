<?php
/**
 * Sub categories block
 *
 * @var $block \Totaltools\Catalog\Block\SubCategories
 * @var $helper \Totaltools\Catalog\Helper\Data
 */
$helper = $this->helper('Totaltools\Catalog\Helper\Data');

$current_category_id = $block->getCurrentCategoryId();
$categoryObj = $this->categoryRepository->get($current_category_id);
$subcategories = $categoryObj->getChildrenCategories();
$viewAllProductsUrl = $categoryObj->getUrl() . "/view-all";
?>


<h2 class="category_brands uppercase">Shop by Product Type</h2>
<?php if(count($subcategories)): ?>
<div class="subcat wrapper">
    <ul class="subcategories">
        <?php foreach($subcategories as $subcat): ?>
        <?php
            $count = $block->getProductsCount($subcat->getId());
            if ($count < 1) continue;
        ?>
        <li class="subcategory">
            <a class="subcategory-link" href="<?php echo $subcat->getUrl(); ?>" title="<?php echo $subcat->getName(); ?>">
                <header>
                    <div class="subcategory-image">
                        <?php 
                            $imgPath =  $this->getImageUrl($subcat->getId());
                            if ($imgPath):
                        ?>
                        <img src="<?php echo $imgPath; ?>" alt="<?php echo $subcat->getName(); ?>" />
                        <?php else: ?>
                        <img src="<?php echo $helper->getDefaultImage(); ?>" alt="<?php echo $subcat->getName(); ?>"/>
                        <?php endif; ?>                        
                    </div>
                </header>
                <footer>
                    <div class="subcategory-title">
                        <?php echo $subcat->getName(); ?>
                    </div>
                    <div class="subcategory-count">
                    <?php
                        $suffix = ' Products';
                        if ($count == 1) $suffix = ' Product';
                    ?>
                    <?php echo $count . $suffix; ?>
                    </div>
                </footer>
            </a>
        </li>    
    <?php endforeach; ?>
    </ul>

    <div class="toolbar bottom">
        <?php echo $block->getPagerHtml(); ?>
    </div>
    <div class="view-all-products-container">
        <a href="<?=$viewAllProductsUrl?>" class="view-all-products">View All Products</a>
    </div>
</div>
<?php endif; ?>