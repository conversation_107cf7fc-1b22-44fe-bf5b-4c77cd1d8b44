<?php
/**
 * Sub categories block
 *
 * @var $block \Totaltools\Catalog\Block\SubCategories
 * @var $helper \Totaltools\Catalog\Helper\Data
 */
$helper = $this->helper('Totaltools\Catalog\Helper\Data');
$topcategories = $block->getCategoryCollection();
?>

<?php if(count($topcategories)): ?>
<div class="top-categories wrapper">
    <h2>Popular Categories</h2>
    <ul class="top-category-list">
    <?php foreach($topcategories as $topcat): ?>
        <li class="top-category-item">
            <a class="top-category-item-wrap" href="<?php echo $topcat->getUrl(); ?>" title="<?php echo $topcat->getName(); ?>">
                <div class="top-category-item-image">
                    <?php
                        $secondaryImg = $topcat->getSecondaryImage();
                        if (!strpos($secondaryImg, "catalog/category")) {
                            $secondaryImg = 'catalog/category/' . $topcat->getSecondaryImage();
                            $imgPath =  $helper->getMediaUrl($secondaryImg);
                        } else {
                            $imgPath =  $helper->getBaseUrl(ltrim($secondaryImg,"/"));
                        }
                        if ($imgPath):
                    ?>
                    <img src="<?php echo $imgPath; ?>" alt="<?php echo $topcat->getName(); ?>" />
                    <?php else: ?>
                    <img src="<?php echo $helper->getDefaultImage(); ?>" alt="<?php echo $topcat->getName(); ?>"/>
                    <?php endif; ?>
                </div>
            </a>
        </li>
    <?php endforeach; ?>
    </ul>
</div>
<?php endif; ?>
