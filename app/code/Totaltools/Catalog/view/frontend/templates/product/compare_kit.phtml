<?php $total = $block->getItems()->getSize() ?>
<?php $helper = $this->helper(Magento\Catalog\Helper\Output::class); ?>
<?php $currentSku = $block->getCurrentProductSku(); ?>
<?php if ($total && $block->showCompareKitBlock()) :?>
<div class="product-combo-comparison-kit">
<a href="#tab-label-specification-title" id="jump-to-description-specification">View Description & Specification</a>
<h1>Compare Kits</h1>
<div class="table-wrapper comparison">
	<ul class="product-comparison" id="product-comparison">
		<?php foreach ($block->getItems() as $item) :?>
			<?php $finalPriceAmount = $item->getPriceInfo()->getPrice('final_price')->getValue(); ?>
			<li class="product-item <?php $currentSku == $item->getSku() ? 'active' : '' ?>">
				<?php if ($currentSku == $item->getSku()) : ?>
					<div class="active-title">CURRENT PRODUCT</div>
				<?php endif;?>
				<div class="product-item-info">
					<span class="attr-title">Product Name</span>
					<strong class="product-item-name">
                        <a href="<?= $block->escapeUrl($block->getProductUrl($item)) ?>"
                        	title="<?= /* @noEscape */ $block->stripTags($item->getName(), null, true) ?>">
                            <?= /* @noEscape */ $helper->productAttribute($item, $item->getName(), 'name') ?>
                        </a>
                    </strong>

					<a class="product-item-photo"
                        href="<?= $block->escapeUrl($block->getProductUrl($item)) ?>"
                        title="<?= /* @noEscape */ $block->stripTags($item->getName(), null, true) ?>">
                        <?= $block->getImage($item, 'product_comparison_list')->toHtml() ?>
                    </a>
				</div>
				<?php if ($compareKitAttributes = $block->getCompareKitAttributeData($item)): ?>
					<div class="product-item-attr">
						<?php foreach ($compareKitAttributes as $compareKitAttribute): ?>
							<span class="attr-title"><?php echo $block->escapeHtml(__($compareKitAttribute['frontend_label'])) ?></span>
							<span class="attr-value"><?php /* @escapeNotVerified */ echo $helper->productAttribute($item, $compareKitAttribute['value'], $compareKitAttribute['code']) ?></span>
							<?php if ($compareKitAttribute['code'] == 'part_no'): ?>
								<span class="attr-title">Price</span>
								<span class="attr-value price"><?php echo $block->getFormattedPrice($finalPriceAmount); ?></span>
							<?php endif;?>
						<?php endforeach; ?>
					</div>
				<?php endif;?>
			</li>
		<?php endforeach; ?>
	</ul>
</div>
</div>
<script>
    require([
        'jquery',
        'slick'
    ], function($) {
        'use strict';

        $(function () {
			$('.product-comparison').slick({
				dots: false,
				infinite: false,
				slidesToShow: 5,
				slidesToScroll: 1,
				responsive: [
					{
						breakpoint: 1025,
						settings: {
							slidesToShow: 3,
						},
					},
					{
						breakpoint: 641,
						settings: {
							slidesToShow: 2,
						},
					},
					{
						breakpoint: 375,
						settings: {
							slidesToShow: 1,
						},
					},
				],
			});
    	});
    });
</script>

<?php endif; ?>