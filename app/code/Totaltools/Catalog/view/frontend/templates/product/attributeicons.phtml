<?php

/** @var $block \Totaltools\Catalog\Block\Product\AttributeIcons */

$icons = $block->getIcons();

if (!$icons) {
    return;
}

?>
<ul class="product-attribute-icons">
<?php if ( $this->brandInfo->getBrandImageUrl()): ?>
<li ><a href="<?php echo $this->brandInfo->getBrandUrl(); ?>"><img src="<?php echo  $this->brandInfo->getBrandImageUrl(); ?>" /></a></li>
<?php endif; ?>
    <?php foreach ($icons as $icon) : ?>
        <li>
            <?php $text = __(sprintf('%s: %s', $icon->getData('label'), $icon->getData('value'))); ?>
            <img
                src="<?php echo $icon->getData('image'); ?>"
                alt="<?php echo $text; ?>"
                title="<?php echo $text; ?>"
                class="attribute-icon attribute-icon-<?php echo $icon->getData('class'); ?>"
            />
        </li>
    <?php endforeach; ?>
</ul>
