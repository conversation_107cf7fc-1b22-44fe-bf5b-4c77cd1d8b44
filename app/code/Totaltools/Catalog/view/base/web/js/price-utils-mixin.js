/**
 * @category   Totaltools
 * @package    Totaltools_Catalog
 * <AUTHOR>  <<EMAIL>>
 * @copyright  Copyright (c) 2021, Totaltools. (http://totaltools.com.au/)
 */

 define(['underscore', 'mage/utils/wrapper'], function (_, wrapper) {
    'use strict';

    var priceFormat = {
            decimalSymbol: '.',
            groupLength: 3,
            groupSymbol: '',
            integerRequired: false,
            pattern: '$%s',
            precision: 2,
            requiredPrecision: 2,
        },
        extender = {
            /**
             * Adds the ability to add html to formatted price.
             *
             * @param {Number} price
             * @param {Object} customFormat
             * @returns {String}
             */
            getPriceHtml: function (price, customFormat) {
                var priceHtml,
                    format = _.extend(priceFormat, customFormat),
                    formattedPrice = this.formatPrice(price, format),
                    currency = formattedPrice[0],
                    amount = formattedPrice.replace(
                        /(^\$)(\d+(,\d+)*)(\.\d+)?$/g,
                        '$2'
                    ),
                    decimal = formattedPrice.split('.')[1];

                priceHtml =
                    '<span class="currency-symbol">' + currency + '</span>';
                priceHtml += amount;
                priceHtml +=
                    parseInt(decimal) > 0
                        ? '<span class="decimal-dot">.</span><span class="price-decimal">' + decimal + '</span>'
                        : '';

                return priceHtml;
            },
        };

    return function (target) {
        return wrapper.extend(target, extender);
    };
});
