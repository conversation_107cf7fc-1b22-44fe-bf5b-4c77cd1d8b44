/**
 * @category    Totaltools
 * @package     Totaltools_Catalog
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define(['jquery', 'jquery-ui-modules/widget'], function ($) {
    'use strict';

    return function (mageConfigurable) {
        $.widget('mage.configurable', mageConfigurable, {
            options: {
                saveLabelSelector: '.you-save-statement',
                saveAmountSelector: '.you-save-int',
                saveDecimalSelector: '.you-save-decimal',
            },

            /**
             * @inheritdoc
             */
            _create: function () {
                this._super();
                this._bind();

                return this;
            },

            /**
             * Events binding
             */
            _bind: function () {
                $(document).on(
                    'updateMsrpPriceBlock',
                    this._toggleSaveLabel.bind(this)
                );

                return this;
            },

            /**
             * Show/Hide savings label and old price depending on price configuration.
             *
             * @param {jQuery.Event} ev
             * @param {String} optionId
             * @param {Object} optionPrices
             */
            _toggleSaveLabel: function (ev, optionId, optionPrices) {
                var opts = this.options;

                if ($(opts.slyOldPriceSelector).is(':visible')) {
                    var savings =
                            optionPrices[optionId].oldPrice.amount -
                            optionPrices[optionId].finalPrice.amount,
                        saveAmount = parseInt(savings),
                        saveDecimal = (savings - saveAmount).toFixed(2) * 100,
                        oldPrice = $(opts.slyOldPriceSelector).find('.price');

                    $(opts.saveAmountSelector).text(saveAmount);

                    if (oldPrice.length) {
                        oldPrice.text(optionPrices[optionId].oldPrice.amount);
                    }

                    if (saveDecimal > 0) {
                        saveDecimal = parseInt(saveDecimal);
                        saveDecimal.toString().length == 1
                            ? $(opts.saveDecimalSelector).text(
                                  '0' + saveDecimal.toString()
                              )
                            : $(opts.saveDecimalSelector).text(saveDecimal);
                    } else {
                        $(opts.saveDecimalSelector).text('');
                    }

                    $(opts.saveLabelSelector).show();
                } else {
                    $(opts.saveLabelSelector).hide();
                }
            },
        });

        return $.mage.configurable;
    };
});
