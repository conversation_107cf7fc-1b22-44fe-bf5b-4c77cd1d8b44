<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="content">
        <field name="short_description">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="class" xsi:type="string">Magento\Catalog\Ui\Component\Category\Form\Element\Wysiwyg</item>
                    <item name="formElement" xsi:type="string">wysiwyg</item>
                    <item name="wysiwygConfigData" xsi:type="array">
                        <item name="settings" xsi:type="array">
                            <item name="theme_advanced_buttons1" xsi:type="string">bold,italic,|,justifyleft,justifycenter,justifyright,|,fontselect,fontsizeselect,|,forecolor,backcolor,|,link,unlink,image,|,bullist,numlist,|,code</item>
                            <item name="theme_advanced_buttons2" xsi:type="boolean">false</item>
                            <item name="theme_advanced_buttons3" xsi:type="boolean">false</item>
                            <item name="theme_advanced_buttons4" xsi:type="boolean">false</item>
                            <item name="theme_advanced_statusbar_location" xsi:type="boolean">false</item>
                        </item>
                        <item name="files_browser_window_url" xsi:type="boolean">false</item>
                        <item name="height" xsi:type="string">100px</item>
                        <item name="toggle_button" xsi:type="boolean">false</item>
                        <item name="add_variables" xsi:type="boolean">false</item>
                        <item name="add_widgets" xsi:type="boolean">false</item>
                        <item name="add_images" xsi:type="boolean">false</item>
                    </item>
                    <item name="label" xsi:type="string" translate="true">Short Description</item>
                    <item name="template" xsi:type="string">ui/form/field</item>
                    <item name="source" xsi:type="string">category</item>
                    <item name="wysiwyg" xsi:type="boolean">true</item>
                    <item name="dataScope" xsi:type="string">short_description</item>
                    <item name="sortOrder" xsi:type="number">51</item>
                    <item name="rows" xsi:type="number">8</item>
                </item>
            </argument>
        </field>
        <field name="secondary_image">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="source" xsi:type="string">category</item>
                    <item name="label" xsi:type="string" translate="true">Secondary Image</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="formElement" xsi:type="string">fileUploader</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
                    <item name="previewTmpl" xsi:type="string">Magento_Catalog/image-preview</item>
                    <item name="required" xsi:type="boolean">false</item>
                    <item name="sortOrder" xsi:type="number">45</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="catalog/category_image/upload"/>
                    </item>
                </item>
            </argument>
        </field>
        <field name="include_on_homepage">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">21</item>
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="source" xsi:type="string">category</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="false" xsi:type="number">0</item>
                        <item name="true" xsi:type="number">1</item>
                    </item>
                    <item name="validation" xsi:type="array">
                        <item name="required-entry" xsi:type="boolean">false</item>
                    </item>
                    <item name="default" xsi:type="number">0</item>
                    <item name="label" xsi:type="string" translate="true">Include On Homepage</item>
                </item>
            </argument>
        </field>
        <field name="brand_background_color">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">70</item>
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="label" xsi:type="string" translate="true">Brand Background Colour</item>
                </item>
            </argument>
        </field>
        <field name="brand_text_color">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">71</item>
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="label" xsi:type="string" translate="true">Brand Text Color</item>
                </item>
            </argument>
        </field>
        <field name="brand_image">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="source" xsi:type="string">category</item>
                    <item name="label" xsi:type="string" translate="true">Brand Image</item>
                    <item name="visible" xsi:type="boolean">true</item>
                    <item name="formElement" xsi:type="string">fileUploader</item>
                    <item name="elementTmpl" xsi:type="string">ui/form/element/uploader/uploader</item>
                    <item name="previewTmpl" xsi:type="string">Magento_Catalog/image-preview</item>
                    <item name="sortOrder" xsi:type="number">110</item>
                    <item name="uploaderConfig" xsi:type="array">
                        <item name="url" xsi:type="url" path="catalog/category_imageuploader_brand/brand"/>
                    </item>
                    <item name="notice" xsi:type="string" translate="true"></item>
                    <item name="additionalClasses" xsi:type="string"></item>

                </item>
            </argument>
        </field>
        <field name="is_featured">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">120</item>
                    <item name="dataType" xsi:type="string">boolean</item>
                    <item name="formElement" xsi:type="string">checkbox</item>
                    <item name="source" xsi:type="string">category</item>
                    <item name="prefer" xsi:type="string">toggle</item>
                    <item name="valueMap" xsi:type="array">
                        <item name="true" xsi:type="string">1</item>
                        <item name="false" xsi:type="string">0</item>
                    </item>
                    <item name="default" xsi:type="string">0</item>
                    <item name="label" xsi:type="string" translate="true">Is Featured</item>
                    <item name="notice" xsi:type="string" translate="true"></item>
                    <item name="additionalClasses" xsi:type="string"></item>

                </item>
            </argument>
        </field>
        <field name="featured_order">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="sortOrder" xsi:type="number">130</item>
                    <item name="dataType" xsi:type="string">string</item>
                    <item name="formElement" xsi:type="string">input</item>
                    <item name="label" xsi:type="string" translate="true">Featured order</item>
                    <item name="notice" xsi:type="string" translate="true"></item>
                    <item name="additionalClasses" xsi:type="string"></item>

                </item>
            </argument>
        </field>
    </fieldset>
</form>
