/*
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

/*! jQuery UI - v1.9.2 - 2012-11-23
* http://jqueryui.com
* Includes: jquery.ui.core.css, jquery.ui.accordion.css, jquery.ui.autocomplete.css, jquery.ui.button.css, jquery.ui.datepicker.css, jquery.ui.dialog.css, jquery.ui.menu.css, jquery.ui.progressbar.css, jquery.ui.resizable.css, jquery.ui.selectable.css, jquery.ui.slider.css, jquery.ui.spinner.css, jquery.ui.tabs.css, jquery.ui.tooltip.css, jquery.ui.theme.css
* Copyright 2012 jQuery Foundation and other contributors; Licensed MIT */

/* Layout helpers
----------------------------------*/
.uix-multiselect .ui-helper-hidden { display: none; }
.uix-multiselect .ui-helper-hidden-accessible { border: 0; clip: rect(0 0 0 0); height: 1px; margin: -1px; overflow: hidden; padding: 0; position: absolute; width: 1px; }
.uix-multiselect .ui-helper-reset { margin: 0; padding: 0; border: 0; outline: 0; line-height: 1.3; text-decoration: none; font-size: 100%; list-style: none; }
.uix-multiselect .ui-helper-clearfix:before, .ui-helper-clearfix:after { content: ""; display: table; }
.uix-multiselect .ui-helper-clearfix:after { clear: both; }
.uix-multiselect .ui-helper-clearfix { zoom: 1; }
.uix-multiselect .ui-helper-zfix { width: 100%; height: 100%; top: 0; left: 0; position: absolute; opacity: 0; filter:Alpha(Opacity=0); }


/* Interaction Cues
----------------------------------*/
.uix-multiselect .ui-state-disabled { cursor: default !important; }


/* Icons
----------------------------------*/

/* states and images */
.uix-multiselect .ui-icon { display: block; text-indent: -99999px; overflow: hidden; background-repeat: no-repeat; }


/* Misc visuals
----------------------------------*/

/* Overlays */
.uix-multiselect .ui-widget-overlay { position: absolute; top: 0; left: 0; width: 100%; height: 100%; }

.uix-multiselect .ui-accordion .ui-accordion-header { display: block; cursor: pointer; position: relative; margin-top: 2px; padding: .5em .5em .5em .7em; zoom: 1; }
.uix-multiselect .ui-accordion .ui-accordion-icons { padding-left: 2.2em; }
.uix-multiselect .ui-accordion .ui-accordion-noicons { padding-left: .7em; }
.uix-multiselect .ui-accordion .ui-accordion-icons .ui-accordion-icons { padding-left: 2.2em; }
.uix-multiselect .ui-accordion .ui-accordion-header .ui-accordion-header-icon { position: absolute; left: .5em; top: 50%; margin-top: -8px; }
.uix-multiselect .ui-accordion .ui-accordion-content { padding: 1em 2.2em; border-top: 0; overflow: auto; zoom: 1; }

.uix-multiselect .ui-autocomplete {
    position: absolute;
    top: 0;
    left: 0;
    cursor: default;
}

/* workarounds */
* html .ui-autocomplete { width:1px; } /* without this, the menu expands to 100% in IE6 */

.uix-multiselect .ui-button { display: inline-block; position: relative; padding: 0; margin-right: .1em; cursor: pointer; text-align: center; zoom: 1; overflow: visible; } /* the overflow property removes extra width in IE */
.uix-multiselect .ui-button, .uix-multiselect .ui-button:link, .uix-multiselect .ui-button:visited, .uix-multiselect .ui-button:hover, .uix-multiselect .ui-button:active { text-decoration: none; }
.uix-multiselect .ui-button-icon-only { width: 2.2em; } /* to make room for the icon, a width needs to be set here */
.uix-multiselect button.ui-button-icon-only { width: 2.4em; } /* button elements seem to need a little more width */
.uix-multiselect .ui-button-icons-only { width: 3.4em; }
.uix-multiselect button.ui-button-icons-only { width: 3.7em; }

/*button text element */
.uix-multiselect .ui-button .ui-button-text { display: block; line-height: 1.4;  }
.uix-multiselect .ui-button-text-only .ui-button-text { padding: .4em 1em; }
.uix-multiselect .ui-button-icon-only .ui-button-text, .uix-multiselect .ui-button-icons-only .ui-button-text { padding: .4em; text-indent: -9999999px; }
.uix-multiselect .ui-button-text-icon-primary .ui-button-text, .uix-multiselect .ui-button-text-icons .ui-button-text { padding: .4em 1em .4em 2.1em; }
.uix-multiselect .ui-button-text-icon-secondary .ui-button-text, .uix-multiselect .ui-button-text-icons .ui-button-text { padding: .4em 2.1em .4em 1em; }
.uix-multiselect .ui-button-text-icons .ui-button-text { padding-left: 2.1em; padding-right: 2.1em; }
/* no icon support for input elements, provide padding by default */
.uix-multiselect input.ui-button { padding: .4em 1em; }

/*button icon element(s) */
.uix-multiselect .ui-button-icon-only .ui-icon, .ui-button-text-icon-primary .ui-icon, .uix-multiselect .ui-button-text-icon-secondary .ui-icon, .uix-multiselect .ui-button-text-icons .ui-icon, .uix-multiselect .ui-button-icons-only .ui-icon { position: absolute; top: 50%; margin-top: -8px; }
.uix-multiselect .ui-button-icon-only .ui-icon { left: 50%; margin-left: -8px; }
.uix-multiselect .ui-button-text-icon-primary .ui-button-icon-primary, .uix-multiselect .ui-button-text-icons .ui-button-icon-primary, .uix-multiselect .ui-button-icons-only .ui-button-icon-primary { left: .5em; }
.uix-multiselect .ui-button-text-icon-secondary .ui-button-icon-secondary, .uix-multiselect .ui-button-text-icons .ui-button-icon-secondary, .uix-multiselect .ui-button-icons-only .ui-button-icon-secondary { right: .5em; }
.uix-multiselect .ui-button-text-icons .ui-button-icon-secondary, .uix-multiselect .ui-button-icons-only .ui-button-icon-secondary { right: .5em; }

/*button sets*/
.uix-multiselect .ui-buttonset { margin-right: 7px; }
.uix-multiselect .ui-buttonset .ui-button { margin-left: 0; margin-right: -.3em; }

/* workarounds */
.uix-multiselect button.ui-button::-moz-focus-inner { border: 0; padding: 0; } /* reset extra padding in Firefox */

.uix-multiselect .ui-progressbar { height:2em; text-align: left; overflow: hidden; }
.uix-multiselect .ui-progressbar .ui-progressbar-value {margin: -1px; height:100%; }
.uix-multiselect .ui-resizable { position: relative;}
.uix-multiselect .ui-resizable-handle { position: absolute;font-size: 0.1px; display: block; }
.uix-multiselect .ui-resizable-disabled .ui-resizable-handle, .uix-multiselect .ui-resizable-autohide .ui-resizable-handle { display: none; }
.uix-multiselect .ui-resizable-n { cursor: n-resize; height: 7px; width: 100%; top: -5px; left: 0; }
.uix-multiselect .ui-resizable-s { cursor: s-resize; height: 7px; width: 100%; bottom: -5px; left: 0; }
.uix-multiselect .ui-resizable-e { cursor: e-resize; width: 7px; right: -5px; top: 0; height: 100%; }
.uix-multiselect .ui-resizable-w { cursor: w-resize; width: 7px; left: -5px; top: 0; height: 100%; }
.uix-multiselect .ui-resizable-se { cursor: se-resize; width: 12px; height: 12px; right: 1px; bottom: 1px; }
.uix-multiselect .ui-resizable-sw { cursor: sw-resize; width: 9px; height: 9px; left: -5px; bottom: -5px; }
.uix-multiselect .ui-resizable-nw { cursor: nw-resize; width: 9px; height: 9px; left: -5px; top: -5px; }
.uix-multiselect .ui-resizable-ne { cursor: ne-resize; width: 9px; height: 9px; right: -5px; top: -5px;}
.uix-multiselect .ui-selectable-helper { position: absolute; z-index: 100; border:1px dotted black; }

.uix-multiselect .ui-tooltip {
    padding: 8px;
    position: absolute;
    z-index: 9999;
    max-width: 300px;
    -webkit-box-shadow: 0 0 5px #aaa;
    box-shadow: 0 0 5px #aaa;
}
/* Fades and background-images don't work well together in IE6, drop the image */
* html .ui-tooltip {
    background-image: none;
}
body .ui-tooltip { border-width: 2px; }

/* Component containers
----------------------------------*/
.uix-multiselect.ui-widget { font-family: Verdana,Arial,sans-serif/*{ffDefault}*/; font-size: 1.1em/*{fsDefault}*/; }
.uix-multiselect.ui-widget .ui-widget { font-size: 1em; }
.uix-multiselect.ui-widget input, .uix-multiselect.ui-widget select, .uix-multiselect.ui-widget textarea, .uix-multiselect.ui-widget button { font-family: Verdana,Arial,sans-serif/*{ffDefault}*/; font-size: 1em; }
.uix-multiselect .ui-widget-content { border: 1px solid #aaaaaa/*{borderColorContent}*/; background: #ffffff/*{bgColorContent}*/ url(images/ui-bg_flat_75_ffffff_40x100.png)/*{bgImgUrlContent}*/ 50%/*{bgContentXPos}*/ 50%/*{bgContentYPos}*/ repeat-x/*{bgContentRepeat}*/; color: #222222/*{fcContent}*/; }
.uix-multiselect .ui-widget-content a { color: #222222/*{fcContent}*/; }
.uix-multiselect .ui-widget-header { border: 1px solid #aaaaaa/*{borderColorHeader}*/; background: #cccccc/*{bgColorHeader}*/ url(images/ui-bg_highlight-soft_75_cccccc_1x100.png)/*{bgImgUrlHeader}*/ 50%/*{bgHeaderXPos}*/ 50%/*{bgHeaderYPos}*/ repeat-x/*{bgHeaderRepeat}*/; color: #222222/*{fcHeader}*/; font-weight: bold; }
.uix-multiselect .ui-widget-header a { color: #222222/*{fcHeader}*/; }

/* Interaction states
----------------------------------*/
.uix-multiselect .ui-state-default, .uix-multiselect .ui-widget-content .ui-state-default, .uix-multiselect .ui-widget-header .ui-state-default { border: 1px solid #d3d3d3/*{borderColorDefault}*/; background: #e6e6e6/*{bgColorDefault}*/ url(images/ui-bg_glass_75_e6e6e6_1x400.png)/*{bgImgUrlDefault}*/ 50%/*{bgDefaultXPos}*/ 50%/*{bgDefaultYPos}*/ repeat-x/*{bgDefaultRepeat}*/; font-weight: normal/*{fwDefault}*/; color: #555555/*{fcDefault}*/; }
.uix-multiselect .ui-state-default a, .uix-multiselect .ui-state-default a:link, .uix-multiselect .ui-state-default a:visited { color: #555555/*{fcDefault}*/; text-decoration: none; }
.uix-multiselect.ui-state-hover, .uix-multiselect .ui-widget-content .ui-state-hover, .uix-multiselect .ui-widget-header .ui-state-hover, .uix-multiselect .ui-state-focus, .uix-multiselect .ui-widget-content .ui-state-focus, .uix-multiselect .ui-widget-header .ui-state-focus { border: 1px solid #999999/*{borderColorHover}*/; background: #dadada/*{bgColorHover}*/ url(images/ui-bg_glass_75_dadada_1x400.png)/*{bgImgUrlHover}*/ 50%/*{bgHoverXPos}*/ 50%/*{bgHoverYPos}*/ repeat-x/*{bgHoverRepeat}*/; font-weight: normal/*{fwDefault}*/; color: #212121/*{fcHover}*/; }
.uix-multiselect .ui-state-hover a, .uix-multiselect .ui-state-hover a:hover, .uix-multiselect .ui-state-hover a:link, .uix-multiselect .ui-state-hover a:visited { color: #212121/*{fcHover}*/; text-decoration: none; }
.uix-multiselect .ui-state-active, .uix-multiselect .ui-widget-content .ui-state-active, .uix-multiselect .ui-widget-header .ui-state-active { border: 1px solid #aaaaaa/*{borderColorActive}*/; background: #ffffff/*{bgColorActive}*/ url(images/ui-bg_glass_65_ffffff_1x400.png)/*{bgImgUrlActive}*/ 50%/*{bgActiveXPos}*/ 50%/*{bgActiveYPos}*/ repeat-x/*{bgActiveRepeat}*/; font-weight: normal/*{fwDefault}*/; color: #212121/*{fcActive}*/; }
.uix-multiselect .ui-state-active a, .uix-multiselect .ui-state-active a:link, .uix-multiselect .ui-state-active a:visited { color: #212121/*{fcActive}*/; text-decoration: none; }

/* Interaction Cues
----------------------------------*/
.uix-multiselect .ui-state-highlight, .uix-multiselect .ui-widget-content .ui-state-highlight, .uix-multiselect .ui-widget-header .ui-state-highlight  {border: 1px solid #fcefa1/*{borderColorHighlight}*/; background: #fbf9ee/*{bgColorHighlight}*/ url(images/ui-bg_glass_55_fbf9ee_1x400.png)/*{bgImgUrlHighlight}*/ 50%/*{bgHighlightXPos}*/ 50%/*{bgHighlightYPos}*/ repeat-x/*{bgHighlightRepeat}*/; color: #363636/*{fcHighlight}*/; }
.uix-multiselect .ui-state-highlight a, .uix-multiselect .ui-widget-content .ui-state-highlight a, .uix-multiselect .ui-widget-header .ui-state-highlight a { color: #363636/*{fcHighlight}*/; }
.uix-multiselect .ui-state-error, .uix-multiselect .ui-widget-content .ui-state-error, .uix-multiselect .ui-widget-header .ui-state-error {border: 1px solid #cd0a0a/*{borderColorError}*/; background: #fef1ec/*{bgColorError}*/ url(images/ui-bg_glass_95_fef1ec_1x400.png)/*{bgImgUrlError}*/ 50%/*{bgErrorXPos}*/ 50%/*{bgErrorYPos}*/ repeat-x/*{bgErrorRepeat}*/; color: #cd0a0a/*{fcError}*/; }
.uix-multiselect .ui-state-error a, .uix-multiselect .ui-widget-content .ui-state-error a, .uix-multiselect .ui-widget-header .ui-state-error a { color: #cd0a0a/*{fcError}*/; }
.uix-multiselect .ui-state-error-text, .uix-multiselect .ui-widget-content .ui-state-error-text, .uix-multiselect .ui-widget-header .ui-state-error-text { color: #cd0a0a/*{fcError}*/; }
.uix-multiselect .ui-priority-primary, .uix-multiselect .ui-widget-content .ui-priority-primary, .uix-multiselect .ui-widget-header .ui-priority-primary { font-weight: bold; }
.uix-multiselect .ui-priority-secondary, .uix-multiselect .ui-widget-content .ui-priority-secondary,  .uix-multiselect .ui-widget-header .ui-priority-secondary { opacity: .7; filter:Alpha(Opacity=70); font-weight: normal; }
.uix-multiselect .ui-state-disabled, .uix-multiselect .ui-widget-content .ui-state-disabled, .uix-multiselect .ui-widget-header .ui-state-disabled { opacity: .35; filter:Alpha(Opacity=35); background-image: none; }
.uix-multiselect .ui-state-disabled .ui-icon { filter:Alpha(Opacity=35); } /* For IE8 - See #6059 */

/* Icons
----------------------------------*/

/* states and images */
.uix-multiselect .ui-icon { width: 16px; height: 16px; background-image: url(images/ui-icons_222222_256x240.png)/*{iconsContent}*/; }
.uix-multiselect .ui-widget-content .ui-icon {background-image: url(images/ui-icons_222222_256x240.png)/*{iconsContent}*/; }
.uix-multiselect .ui-widget-header .ui-icon {background-image: url(images/ui-icons_222222_256x240.png)/*{iconsHeader}*/; }
.uix-multiselect .ui-state-default .ui-icon { background-image: url(images/ui-icons_888888_256x240.png)/*{iconsDefault}*/; }
.uix-multiselect .ui-state-hover .ui-icon, .ui-state-focus .ui-icon {background-image: url(images/ui-icons_454545_256x240.png)/*{iconsHover}*/; }
.uix-multiselect .ui-state-active .ui-icon {background-image: url(images/ui-icons_454545_256x240.png)/*{iconsActive}*/; }
.uix-multiselect .ui-state-highlight .ui-icon {background-image: url(images/ui-icons_2e83ff_256x240.png)/*{iconsHighlight}*/; }
.uix-multiselect .ui-state-error .ui-icon, .uix-multiselect .ui-state-error-text .ui-icon {background-image: url(images/ui-icons_cd0a0a_256x240.png)/*{iconsError}*/; }

/* positioning */
.uix-multiselect .ui-icon-carat-1-n { background-position: 0 0; }
.uix-multiselect .ui-icon-carat-1-ne { background-position: -16px 0; }
.uix-multiselect .ui-icon-carat-1-e { background-position: -32px 0; }
.uix-multiselect .ui-icon-carat-1-se { background-position: -48px 0; }
.uix-multiselect .ui-icon-carat-1-s { background-position: -64px 0; }
.uix-multiselect .ui-icon-carat-1-sw { background-position: -80px 0; }
.uix-multiselect .ui-icon-carat-1-w { background-position: -96px 0; }
.uix-multiselect .ui-icon-carat-1-nw { background-position: -112px 0; }
.uix-multiselect .ui-icon-carat-2-n-s { background-position: -128px 0; }
.uix-multiselect .ui-icon-carat-2-e-w { background-position: -144px 0; }
.uix-multiselect .ui-icon-triangle-1-n { background-position: 0 -16px; }
.uix-multiselect .ui-icon-triangle-1-ne { background-position: -16px -16px; }
.uix-multiselect .ui-icon-triangle-1-e { background-position: -32px -16px; }
.uix-multiselect .ui-icon-triangle-1-se { background-position: -48px -16px; }
.uix-multiselect .ui-icon-triangle-1-s { background-position: -64px -16px; }
.uix-multiselect .ui-icon-triangle-1-sw { background-position: -80px -16px; }
.uix-multiselect .ui-icon-triangle-1-w { background-position: -96px -16px; }
.uix-multiselect .ui-icon-triangle-1-nw { background-position: -112px -16px; }
.uix-multiselect .ui-icon-triangle-2-n-s { background-position: -128px -16px; }
.uix-multiselect .ui-icon-triangle-2-e-w { background-position: -144px -16px; }
.uix-multiselect .ui-icon-arrow-1-n { background-position: 0 -32px; }
.uix-multiselect .ui-icon-arrow-1-ne { background-position: -16px -32px; }
.uix-multiselect .ui-icon-arrow-1-e { background-position: -32px -32px; }
.uix-multiselect .ui-icon-arrow-1-se { background-position: -48px -32px; }
.uix-multiselect .ui-icon-arrow-1-s { background-position: -64px -32px; }
.uix-multiselect .ui-icon-arrow-1-sw { background-position: -80px -32px; }
.uix-multiselect .ui-icon-arrow-1-w { background-position: -96px -32px; }
.uix-multiselect .ui-icon-arrow-1-nw { background-position: -112px -32px; }
.uix-multiselect .ui-icon-arrow-2-n-s { background-position: -128px -32px; }
.uix-multiselect .ui-icon-arrow-2-ne-sw { background-position: -144px -32px; }
.uix-multiselect .ui-icon-arrow-2-e-w { background-position: -160px -32px; }
.uix-multiselect .ui-icon-arrow-2-se-nw { background-position: -176px -32px; }
.uix-multiselect .ui-icon-arrowstop-1-n { background-position: -192px -32px; }
.uix-multiselect .ui-icon-arrowstop-1-e { background-position: -208px -32px; }
.uix-multiselect .ui-icon-arrowstop-1-s { background-position: -224px -32px; }
.uix-multiselect .ui-icon-arrowstop-1-w { background-position: -240px -32px; }
.uix-multiselect .ui-icon-arrowthick-1-n { background-position: 0 -48px; }
.uix-multiselect .ui-icon-arrowthick-1-ne { background-position: -16px -48px; }
.uix-multiselect .ui-icon-arrowthick-1-e { background-position: -32px -48px; }
.uix-multiselect .ui-icon-arrowthick-1-se { background-position: -48px -48px; }
.uix-multiselect .ui-icon-arrowthick-1-s { background-position: -64px -48px; }
.uix-multiselect .ui-icon-arrowthick-1-sw { background-position: -80px -48px; }
.uix-multiselect .ui-icon-arrowthick-1-w { background-position: -96px -48px; }
.uix-multiselect .ui-icon-arrowthick-1-nw { background-position: -112px -48px; }
.uix-multiselect .ui-icon-arrowthick-2-n-s { background-position: -128px -48px; }
.uix-multiselect .ui-icon-arrowthick-2-ne-sw { background-position: -144px -48px; }
.uix-multiselect .ui-icon-arrowthick-2-e-w { background-position: -160px -48px; }
.uix-multiselect .ui-icon-arrowthick-2-se-nw { background-position: -176px -48px; }
.uix-multiselect .ui-icon-arrowthickstop-1-n { background-position: -192px -48px; }
.uix-multiselect .ui-icon-arrowthickstop-1-e { background-position: -208px -48px; }
.uix-multiselect .ui-icon-arrowthickstop-1-s { background-position: -224px -48px; }
.uix-multiselect .ui-icon-arrowthickstop-1-w { background-position: -240px -48px; }
.uix-multiselect .ui-icon-arrowreturnthick-1-w { background-position: 0 -64px; }
.uix-multiselect .ui-icon-arrowreturnthick-1-n { background-position: -16px -64px; }
.uix-multiselect .ui-icon-arrowreturnthick-1-e { background-position: -32px -64px; }
.uix-multiselect .ui-icon-arrowreturnthick-1-s { background-position: -48px -64px; }
.uix-multiselect .ui-icon-arrowreturn-1-w { background-position: -64px -64px; }
.uix-multiselect .ui-icon-arrowreturn-1-n { background-position: -80px -64px; }
.uix-multiselect .ui-icon-arrowreturn-1-e { background-position: -96px -64px; }
.uix-multiselect .ui-icon-arrowreturn-1-s { background-position: -112px -64px; }
.uix-multiselect .ui-icon-arrowrefresh-1-w { background-position: -128px -64px; }
.uix-multiselect .ui-icon-arrowrefresh-1-n { background-position: -144px -64px; }
.uix-multiselect .ui-icon-arrowrefresh-1-e { background-position: -160px -64px; }
.uix-multiselect .ui-icon-arrowrefresh-1-s { background-position: -176px -64px; }
.uix-multiselect .ui-icon-arrow-4 { background-position: 0 -80px; }
.uix-multiselect .ui-icon-arrow-4-diag { background-position: -16px -80px; }
.uix-multiselect .ui-icon-extlink { background-position: -32px -80px; }
.uix-multiselect .ui-icon-newwin { background-position: -48px -80px; }
.uix-multiselect .ui-icon-refresh { background-position: -64px -80px; }
.uix-multiselect .ui-icon-shuffle { background-position: -80px -80px; }
.uix-multiselect .ui-icon-transfer-e-w { background-position: -96px -80px; }
.uix-multiselect .ui-icon-transferthick-e-w { background-position: -112px -80px; }
.uix-multiselect .ui-icon-folder-collapsed { background-position: 0 -96px; }
.uix-multiselect .ui-icon-folder-open { background-position: -16px -96px; }
.uix-multiselect .ui-icon-document { background-position: -32px -96px; }
.uix-multiselect .ui-icon-document-b { background-position: -48px -96px; }
.uix-multiselect .ui-icon-note { background-position: -64px -96px; }
.uix-multiselect .ui-icon-mail-closed { background-position: -80px -96px; }
.uix-multiselect .ui-icon-mail-open { background-position: -96px -96px; }
.uix-multiselect .ui-icon-suitcase { background-position: -112px -96px; }
.uix-multiselect .ui-icon-comment { background-position: -128px -96px; }
.uix-multiselect .ui-icon-person { background-position: -144px -96px; }
.uix-multiselect .ui-icon-print { background-position: -160px -96px; }
.uix-multiselect .ui-icon-trash { background-position: -176px -96px; }
.uix-multiselect .ui-icon-locked { background-position: -192px -96px; }
.uix-multiselect .ui-icon-unlocked { background-position: -208px -96px; }
.uix-multiselect .ui-icon-bookmark { background-position: -224px -96px; }
.uix-multiselect .ui-icon-tag { background-position: -240px -96px; }
.uix-multiselect .ui-icon-home { background-position: 0 -112px; }
.uix-multiselect .ui-icon-flag { background-position: -16px -112px; }
.uix-multiselect .ui-icon-calendar { background-position: -32px -112px; }
.uix-multiselect .ui-icon-cart { background-position: -48px -112px; }
.uix-multiselect .ui-icon-pencil { background-position: -64px -112px; }
.uix-multiselect .ui-icon-clock { background-position: -80px -112px; }
.uix-multiselect .ui-icon-disk { background-position: -96px -112px; }
.uix-multiselect .ui-icon-calculator { background-position: -112px -112px; }
.uix-multiselect .ui-icon-zoomin { background-position: -128px -112px; }
.uix-multiselect .ui-icon-zoomout { background-position: -144px -112px; }
.uix-multiselect .ui-icon-search { background-position: -160px -112px; }
.uix-multiselect .ui-icon-wrench { background-position: -176px -112px; }
.uix-multiselect .ui-icon-gear { background-position: -192px -112px; }
.uix-multiselect .ui-icon-heart { background-position: -208px -112px; }
.uix-multiselect .ui-icon-star { background-position: -224px -112px; }
.uix-multiselect .ui-icon-link { background-position: -240px -112px; }
.uix-multiselect .ui-icon-cancel { background-position: 0 -128px; }
.uix-multiselect .ui-icon-plus { background-position: -16px -128px; }
.uix-multiselect .ui-icon-plusthick { background-position: -32px -128px; }
.uix-multiselect .ui-icon-minus { background-position: -48px -128px; }
.uix-multiselect .ui-icon-minusthick { background-position: -64px -128px; }
.uix-multiselect .ui-icon-close { background-position: -80px -128px; }
.uix-multiselect .ui-icon-closethick { background-position: -96px -128px; }
.uix-multiselect .ui-icon-key { background-position: -112px -128px; }
.uix-multiselect .ui-icon-lightbulb { background-position: -128px -128px; }
.uix-multiselect .ui-icon-scissors { background-position: -144px -128px; }
.uix-multiselect .ui-icon-clipboard { background-position: -160px -128px; }
.uix-multiselect .ui-icon-copy { background-position: -176px -128px; }
.uix-multiselect .ui-icon-contact { background-position: -192px -128px; }
.uix-multiselect .ui-icon-image { background-position: -208px -128px; }
.uix-multiselect .ui-icon-video { background-position: -224px -128px; }
.uix-multiselect .ui-icon-script { background-position: -240px -128px; }
.uix-multiselect .ui-icon-alert { background-position: 0 -144px; }
.uix-multiselect .ui-icon-info { background-position: -16px -144px; }
.uix-multiselect .ui-icon-notice { background-position: -32px -144px; }
.uix-multiselect .ui-icon-help { background-position: -48px -144px; }
.uix-multiselect .ui-icon-check { background-position: -64px -144px; }
.uix-multiselect .ui-icon-bullet { background-position: -80px -144px; }
.uix-multiselect .ui-icon-radio-on { background-position: -96px -144px; }
.uix-multiselect .ui-icon-radio-off { background-position: -112px -144px; }
.uix-multiselect .ui-icon-pin-w { background-position: -128px -144px; }
.uix-multiselect .ui-icon-pin-s { background-position: -144px -144px; }
.uix-multiselect .ui-icon-play { background-position: 0 -160px; }
.uix-multiselect .ui-icon-pause { background-position: -16px -160px; }
.uix-multiselect .ui-icon-seek-next { background-position: -32px -160px; }
.uix-multiselect .ui-icon-seek-prev { background-position: -48px -160px; }
.uix-multiselect .ui-icon-seek-end { background-position: -64px -160px; }
.uix-multiselect .ui-icon-seek-start { background-position: -80px -160px; }
/* ui-icon-seek-first is deprecated, use ui-icon-seek-start instead */
.uix-multiselect .ui-icon-seek-first { background-position: -80px -160px; }
.uix-multiselect .ui-icon-stop { background-position: -96px -160px; }
.uix-multiselect .ui-icon-eject { background-position: -112px -160px; }
.uix-multiselect .ui-icon-volume-off { background-position: -128px -160px; }
.uix-multiselect .ui-icon-volume-on { background-position: -144px -160px; }
.uix-multiselect .ui-icon-power { background-position: 0 -176px; }
.uix-multiselect .ui-icon-signal-diag { background-position: -16px -176px; }
.uix-multiselect .ui-icon-signal { background-position: -32px -176px; }
.uix-multiselect .ui-icon-battery-0 { background-position: -48px -176px; }
.uix-multiselect .ui-icon-battery-1 { background-position: -64px -176px; }
.uix-multiselect .ui-icon-battery-2 { background-position: -80px -176px; }
.uix-multiselect .ui-icon-battery-3 { background-position: -96px -176px; }
.uix-multiselect .ui-icon-circle-plus { background-position: 0 -192px; }
.uix-multiselect .ui-icon-circle-minus { background-position: -16px -192px; }
.uix-multiselect .ui-icon-circle-close { background-position: -32px -192px; }
.uix-multiselect .ui-icon-circle-triangle-e { background-position: -48px -192px; }
.uix-multiselect .ui-icon-circle-triangle-s { background-position: -64px -192px; }
.uix-multiselect .ui-icon-circle-triangle-w { background-position: -80px -192px; }
.uix-multiselect .ui-icon-circle-triangle-n { background-position: -96px -192px; }
.uix-multiselect .ui-icon-circle-arrow-e { background-position: -112px -192px; }
.uix-multiselect .ui-icon-circle-arrow-s { background-position: -128px -192px; }
.uix-multiselect .ui-icon-circle-arrow-w { background-position: -144px -192px; }
.uix-multiselect .ui-icon-circle-arrow-n { background-position: -160px -192px; }
.uix-multiselect .ui-icon-circle-zoomin { background-position: -176px -192px; }
.uix-multiselect .ui-icon-circle-zoomout { background-position: -192px -192px; }
.uix-multiselect .ui-icon-circle-check { background-position: -208px -192px; }
.uix-multiselect .ui-icon-circlesmall-plus { background-position: 0 -208px; }
.uix-multiselect .ui-icon-circlesmall-minus { background-position: -16px -208px; }
.uix-multiselect .ui-icon-circlesmall-close { background-position: -32px -208px; }
.uix-multiselect .ui-icon-squaresmall-plus { background-position: -48px -208px; }
.uix-multiselect .ui-icon-squaresmall-minus { background-position: -64px -208px; }
.uix-multiselect .ui-icon-squaresmall-close { background-position: -80px -208px; }
.uix-multiselect .ui-icon-grip-dotted-vertical { background-position: 0 -224px; }
.uix-multiselect .ui-icon-grip-dotted-horizontal { background-position: -16px -224px; }
.uix-multiselect .ui-icon-grip-solid-vertical { background-position: -32px -224px; }
.uix-multiselect .ui-icon-grip-solid-horizontal { background-position: -48px -224px; }
.uix-multiselect .ui-icon-gripsmall-diagonal-se { background-position: -64px -224px; }
.uix-multiselect .ui-icon-grip-diagonal-se { background-position: -80px -224px; }


/* Misc visuals
----------------------------------*/

/* Corner radius */
.uix-multiselect .ui-corner-all, .uix-multiselect .ui-corner-top, .uix-multiselect .ui-corner-left, .uix-multiselect .ui-corner-tl { -moz-border-radius-topleft: 4px/*{cornerRadius}*/; -webkit-border-top-left-radius: 4px/*{cornerRadius}*/; -khtml-border-top-left-radius: 4px/*{cornerRadius}*/; border-top-left-radius: 4px/*{cornerRadius}*/; }
.uix-multiselect .ui-corner-all, .uix-multiselect .ui-corner-top, .uix-multiselect .ui-corner-right, .uix-multiselect .ui-corner-tr { -moz-border-radius-topright: 4px/*{cornerRadius}*/; -webkit-border-top-right-radius: 4px/*{cornerRadius}*/; -khtml-border-top-right-radius: 4px/*{cornerRadius}*/; border-top-right-radius: 4px/*{cornerRadius}*/; }
.uix-multiselect .ui-corner-all, .uix-multiselect .ui-corner-bottom, .uix-multiselect .ui-corner-left, .uix-multiselect .ui-corner-bl { -moz-border-radius-bottomleft: 4px/*{cornerRadius}*/; -webkit-border-bottom-left-radius: 4px/*{cornerRadius}*/; -khtml-border-bottom-left-radius: 4px/*{cornerRadius}*/; border-bottom-left-radius: 4px/*{cornerRadius}*/; }
.uix-multiselect .ui-corner-all, .uix-multiselect .ui-corner-bottom, .uix-multiselect .ui-corner-right, .uix-multiselect .ui-corner-br { -moz-border-radius-bottomright: 4px/*{cornerRadius}*/; -webkit-border-bottom-right-radius: 4px/*{cornerRadius}*/; -khtml-border-bottom-right-radius: 4px/*{cornerRadius}*/; border-bottom-right-radius: 4px/*{cornerRadius}*/; }

/* Overlays */
.uix-multiselect .ui-widget-overlay { background: #aaaaaa/*{bgColorOverlay}*/ url(images/ui-bg_flat_0_aaaaaa_40x100.png)/*{bgImgUrlOverlay}*/ 50%/*{bgOverlayXPos}*/ 50%/*{bgOverlayYPos}*/ repeat-x/*{bgOverlayRepeat}*/; opacity: .3;filter:Alpha(Opacity=30)/*{opacityOverlay}*/; }
.uix-multiselect .ui-widget-shadow { margin: -8px/*{offsetTopShadow}*/ 0 0 -8px/*{offsetLeftShadow}*/; padding: 8px/*{thicknessShadow}*/; background: #aaaaaa/*{bgColorShadow}*/ url(images/ui-bg_flat_0_aaaaaa_40x100.png)/*{bgImgUrlShadow}*/ 50%/*{bgShadowXPos}*/ 50%/*{bgShadowYPos}*/ repeat-x/*{bgShadowRepeat}*/; opacity: .3;filter:Alpha(Opacity=30)/*{opacityShadow}*/; -moz-border-radius: 8px/*{cornerRadiusShadow}*/; -khtml-border-radius: 8px/*{cornerRadiusShadow}*/; -webkit-border-radius: 8px/*{cornerRadiusShadow}*/; border-radius: 8px/*{cornerRadiusShadow}*/; }
