<?php

/** @var \Totaltools\Catalog\Block\Adminhtml\System\Config\Form\StoresInStockSyncButton $block */
$scope = $block->getScopeConfig();

?>
<script>
    require([
        'jquery',
        'prototype'
    ], function (jQuery) {

        var collectSpan = jQuery('#collect_span');

        jQuery('#totaltools_catalog_stores_instock_btn').click(function () {
            jQuery('#totaltools_catalog_stores_instock_btn').prop('disabled', true);
            var params = {};
            new Ajax.Request('<?php echo $block->getAjaxUrl() ?>', {
                parameters: params,
                asynchronous: true,
                onCreate: function () {
                    collectSpan.find('.success-icon').hide();
                    collectSpan.find('.error-icon').hide();
                    collectSpan.find('.processing-icon').show();
                    collectSpan.find('.loading-mask').show();
                    jQuery('#ajax_message_span').text('Processing');
                },
                onSuccess: function (response) {

                    var res = response.responseJSON;

                    collectSpan.find('.processing-icon').hide();
                    collectSpan.find('.loading-mask').hide();

                    var resultText = '';
                    // response.status > 200
                    if (res.success) {
                        resultText = res.success;
                        collectSpan.find('.success-icon').show();
                    } else {
                        resultText = res.error;
                        collectSpan.find('.error-icon').show();
                    }
                    jQuery('#ajax_message_span').text(resultText);
                    jQuery('#zendesk_customer_sync_customer_btn').prop('disabled', false);
                }
            });
        });
    });
</script>

<?php echo $block->getButtonHtml() ?>
<span class="collect-indicator" id="collect_span">
    <img class="processing-icon" hidden alt="Processing"
         src="<?php echo $block->getViewFileUrl('images/process_spinner.gif') ?>"/>
    <img class="success-icon" hidden alt="Success"
         src="<?php echo $block->getViewFileUrl('images/rule_component_apply.gif') ?>"/>
    <img class="error-icon" hidden alt="Error"
         src="<?php echo $block->getViewFileUrl('images/rule_component_remove.gif') ?>"/>
    <span id="ajax_message_span"></span>
</span>
