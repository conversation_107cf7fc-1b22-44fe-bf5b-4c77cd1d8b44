<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Catalog\Block\Adminhtml\Frontend\CategoryList;

use Magento\Framework\Data\Form\Element\AbstractElement;

/**
 * Class Multiselect
 *
 * @package Totaltools\Catalog\Block\Adminhtml\Frontend\CategoryList
 * <AUTHOR> <<EMAIL>>
 */
class Multiselect extends \Magento\Config\Block\System\Config\Form\Field
{
    /**
     * @param AbstractElement $element
     *
     * @return string
     */
    protected function _getElementHtml(AbstractElement $element)
    {
        $html = parent::_getElementHtml($element);

        $html
            .= <<<EOT
<script type="text/javascript">
require(["jquery", "jquery/ui", "ui-multiselect", "domReady!"], function($){
    function initMultiSelect() {
        var elementId = '#{$element->getHtmlId()}';
        
        $(elementId).css({width: '600px', height: '400px'});
        $(elementId).multiselect({
            sortable: true,
            splitRatio: 0.5,
            sortMethod: null,
            appendToEndOfSelected: true
        }).bind('multiselectChange', function(evt, ui) {
           var selectedCount = $("option:selected", this).length;
        
           $(this)
              .find('option:not(:selected)').prop('disabled', selectedCount === 6).end()
              .multiselect('refresh');
        });
    }
    
    initMultiSelect();
});
</script>
EOT;


        return $html;
    }
}
