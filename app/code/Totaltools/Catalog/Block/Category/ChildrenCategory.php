<?php
namespace Totaltools\Catalog\Block\Category;


use Magento\Framework\View\Element\Template;
use Magento\Framework\App\RequestInterface;

class ChildrenCategory extends Template
{
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    private $request;

    public function __construct(
        Template\Context $context,
        RequestInterface $request,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->request =  $request;
    }

    /**
     * @param Category $category
     * @param int $size
     *
     * @return bool|string|null
     */
    public function isAjaxCall()
    {
      return  $this->request->getParam('shopbyAjax');
    }


}
