<?php
/**
 * @package: Totaltools_Catalog
 * @author: Totaltools Dev <https://www.totaltools.com.au>
 */

namespace Totaltools\Catalog\Block\Category;

/**
 * Class View
 * @package Totaltools\Catalog\Block\Category
 */
class View extends \Magento\Catalog\Block\Category\View
{
    /**
     * Additional Category Display Modes
     */
    const DM_SUBCATEGORIES = 'SUBCATEGORIES_ONLY';

    const DM_SUBCATEGORIES_PRODUCTS = 'SUBCATEGORIES_AND_PRODUCTS';

    const DM_STATIC_BLOCKS_SUBCATEGORIES = 'STATIC_BLOCKS_AND_SUBCATEGORIES';

    const DM_SUBCATEGORIES_MIXED = 'SUBCATEGORIES_MIXED';
    const DM_STATIC_BLOCKS_PRODUCTS = 'PRODUCTS_AND_PAGE';

    /**
     * @return boolean
     */
    public function isSubCategoriesMode()
    {
        return $this->getCurrentCategory()->getDisplayMode() == self::DM_SUBCATEGORIES;
    }

    /**
     * @return boolean
     */
    public function isSubCategoriesProductsMode()
    {
        return $this->getCurrentCategory()->getDisplayMode() == self::DM_SUBCATEGORIES_PRODUCTS;
    }

    /**
     * @return boolean
     */
    public function isStaticBlocksSubCategoriesMode()
    {
        return $this->getCurrentCategory()->getDisplayMode() == self::DM_STATIC_BLOCKS_SUBCATEGORIES;
    }

    /**
     * @return boolean
     */
    public function isSubCategoriesMixedMode()
    {
        return $this->getCurrentCategory()->getDisplayMode() == self::DM_SUBCATEGORIES_MIXED;
    }
}
