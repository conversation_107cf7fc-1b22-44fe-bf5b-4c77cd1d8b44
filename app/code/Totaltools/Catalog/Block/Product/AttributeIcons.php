<?php

namespace Totaltools\Catalog\Block\Product;

use Amasty\Shopby\Helper\FilterSetting;
use Amasty\ShopbyBase\Helper\OptionSetting as OptionSettingHelper;
use Magento\Catalog\Api\ProductAttributeRepositoryInterface;
use Magento\Catalog\Block\Product\Context;
use Magento\Catalog\Block\Product\View\AbstractView as AbstractProductView;
use Magento\Framework\Data\Collection as DataCollection;
use Magento\Framework\Data\CollectionFactory as DataCollectionFactory;
use Magento\Framework\DataObject;
use Magento\Framework\Filter\FilterManager;
use Magento\Framework\Stdlib\ArrayUtils;
use Totaltools\Brand\Block\Info;

class AttributeIcons extends AbstractProductView
{

    /**
     * Collection of attribute icons
     *
     * @var DataCollection
     */
    protected $icons;

    /**
     * Product attribute repository
     *
     * @var ProductAttributeRepositoryInterface
     */
    protected $productAttributeRepository;

    /**
     * Amasty Shopby Option Setting helper
     *
     * @var OptionSettingHelper
     */
    protected $optionSettingHelper;

    /**
     * Filter helper
     *
     * @var FilterManager
     */
    protected $filter;

    /**
     * Data Collection factory
     *
     * @var DataCollectionFactory
     */
    protected $dataCollectionFactory;

    /**
     * Brand Info
     *
     * @var Info
     */
    public $brandInfo;

    /**
     * AttributeIcons constructor
     *
     * @param ProductAttributeRepositoryInterface $productAttributeRepositoryInterface
     * @param OptionSettingHelper $optionSettingHelper
     * @param DataCollectionFactory $dataCollectionFactory
     * @param Context $context
     * @param ArrayUtils $arrayUtils
     * @param Info $brandInfo
     * @param array $data
     */
    public function __construct(
        ProductAttributeRepositoryInterface $productAttributeRepository,
        OptionSettingHelper $optionSettingHelper,
        DataCollectionFactory $dataCollectionFactory,
        Context $context,
        ArrayUtils $arrayUtils,
        Info $brandInfo,
        array $data = []
    ) {
        parent::__construct($context, $arrayUtils, $data);

        $this->productAttributeRepository = $productAttributeRepository;
        $this->optionSettingHelper = $optionSettingHelper;
        $this->filter = $context->getFilterManager();
        $this->dataCollectionFactory = $dataCollectionFactory;
        $this->brandInfo = $brandInfo;
    }

    /**
     * Get array of attributes
     *
     * @return \Magento\Framework\Api\AttributeInterface[]
     */
    protected function getIconAttributes ()
    {
        return $this->getProduct()->getCustomAttributes();
    }

    /**
     * Get clean HTML class from attribute data
     *
     * @param string $label
     * @param string $value
     * @return string
     */
    protected function getHtmlClass ($label, $value)
    {
        return $this->filter->translitUrl($label . ' ' . $value);
    }

    /**
     * Get image URL by attribute code/value pair
     *
     * @param string $attributeCode
     * @param string $value
     * @return null|string
     */
    protected function getImageUrl ($attributeCode, $value)
    {
        $filterCode = FilterSetting::ATTR_PREFIX . $attributeCode;
        $setting = $this->optionSettingHelper->getSettingByValue($value, $filterCode, 1);
        if ($setting) {
            return $setting->getImageUrl();
        }
        return null;
    }

    /**
     * Get icon data for attribute
     *
     * @param \Magento\Framework\Api\AttributeInterface $attribute
     * @return null|string[]
     */
    protected function getIconData ($attribute)
    {
        $code  = $attribute->getAttributeCode();
        $value = $attribute->getValue();
        if (is_array($value)) {
            return null;
        }

        $imageUrl = $this->getImageUrl($code, $value);
        if (!$imageUrl) {
            return null;
        }

        $labelText = $this->productAttributeRepository->get($code)->getDefaultFrontendLabel();
        $valueText = $this->getProduct()->getAttributeText($code);
        $htmlClass = $this->getHtmlClass($labelText, $valueText);

        return [
            'label' => $labelText,
            'value' => $valueText,
            'image' => $imageUrl,
            'class' => $htmlClass,
        ];
    }

    /**
     * Get Collection of icons
     *
     * @return DataCollection
     */
    public function getIcons ()
    {
        if (!$this->icons) {
            $icons = $this->dataCollectionFactory->create();
            foreach ($this->getIconAttributes() as $attribute) {
                $iconData = $this->getIconData($attribute);
                if ($iconData) {
                    $icons->addItem(new DataObject($iconData));
                }
            }
            $this->icons = $icons;
        }
        return $this->icons;
    }

}