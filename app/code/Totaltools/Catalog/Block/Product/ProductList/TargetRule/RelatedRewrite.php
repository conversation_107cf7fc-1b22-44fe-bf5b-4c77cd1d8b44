<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

namespace Totaltools\Catalog\Block\Product\ProductList\TargetRule;


/**
 * Catalog product related items block
 *
 * @SuppressWarnings(PHPMD.LongVariable)
 */
class RelatedRewrite extends \Magento\TargetRule\Block\Catalog\Product\ProductList\Related
{
    /**
     * add class into catalog product view body
     */
    protected function _prepareLayout()
    {
        if (sizeof($this->getAllItems())) {
            $this->pageConfig->addBodyClass('has-column-right');
        }
        return parent::_prepareLayout();
    }
}
