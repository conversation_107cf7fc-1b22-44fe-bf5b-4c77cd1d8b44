<?php
/**
 * @category    Totaltools
 * @package     Totaltools_Optimization
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Catalog\Block\Product;

use Magento\Catalog\Model\ProductRepository;
use Magento\Catalog\Helper\Image;

class Product extends \Magento\Framework\View\Element\Template
{
    protected $_productRepository;
    protected $_imageHelper;
    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Catalog\Model\ProductRepository $productRepository
     * @param \Magento\Catalog\Helper\Image $imageHelper
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        ProductRepository $productRepository,
        Image $imageHelper,
        array $data = []
    ) {
        $this->_productRepository = $productRepository;
        $this->_imageHelper = $imageHelper;
        parent::__construct($context, $data);
    }

    /**
     * Get Product
     *
     * @return Magento\Catalog\Model\ProductRepository
     */
    public function getProductById($id)
    {
        return $this->_productRepository->getById($id);
    }
    /**
    * Get image helper
    *
    * @return \Magento\Catalog\Helper\Image
    */
    public function getImageHelper()
    {
        return $this->_imageHelper;
    }

}
