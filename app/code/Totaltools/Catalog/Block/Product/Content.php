<?php

namespace Totaltools\Catalog\Block\Product;

use Magento\Framework\View\Element\Template;

/**
 * Class Content
 * @package Totaltools\Catalog\Block\Product
 */
class Content extends Template
{
    private $config;

    /**
     * Content constructor.
     * @param Template\Context $context
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $config
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        \Totaltools\Checkout\Helper\TotalToolsConfig $config,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->config = $config;
    }

    /**
     * Get Config
     * @param $path
     * @return mixed
     */
    public function getConfig($path)
    {
        return $this->config->getTotalToolsConfig($path);
    }
}
