<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Catalog\Block\Product;

/**
 * Product view price and stock alerts
 */
class Stock extends \Magento\ProductAlert\Block\Product\View
{
    public $currentProduct = null;
    /**
     * @var \Magento\Framework\Registry
     */
    protected $_registry;

    /**
     * Helper instance
     *
     * @var \Magento\ProductAlert\Helper\Data
     */
    protected $_helper;

    /**
     * @var \Magento\Framework\Data\Helper\PostHelper
     */
    protected $coreHelper;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\ProductAlert\Helper\Data $helper
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Data\Helper\PostHelper $coreHelper
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\ProductAlert\Helper\Data $helper,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Data\Helper\PostHelper $coreHelper,
        array $data = []
    ) {
        parent::__construct($context, $helper, $registry, $coreHelper, $data);
        $this->_registry = $registry;
        $this->_helper = $helper;
        $this->coreHelper = $coreHelper;
    }

     public function setTemplate($template)
    {
        $product = $this->_registry->registry('current_product');
        // Check if product has stock_availability_code attribute
        if ($product->getCustomAttribute('stock_availability_code')) {
            $stockCode = $product->getAttributeText('stock_availability_code');
            $stockAlertAllowed = true; // $this->_helper->isStockAlertAllowed();
            $product = $this->getProduct();

             if (!$stockAlertAllowed || !$product->getId() || $stockCode != 'OOS') {
                $template = '';
            } else {
                $this->setSignupUrl($this->_helper->getSaveUrl('stock'));
            }
        }
       
        return parent::setTemplate($template);
    }

    /**
     * Retrieve currently edited product object
     *
     * @return \Magento\Catalog\Model\Product|boolean
     */
    protected function getCurrentProduct()
    {
        $product = $this->_registry->registry('current_product');
        if ($product && $product->getId()) {
            return $this->currentProduct = $product;
        }
        return false;
    }

    /**
     * Retrieve post action config
     *
     * @return string
     */
    public function getPostAction()
    {
        return $this->coreHelper->getPostData($this->getSignupUrl());
    }

    /**
     * Retrieve post action config
     *
     * @return string
     */
    public function getSubjectProduct()
    {
        $product = $this->_registry->registry('current_product');
        if ($product && $product->getId()) {
            return $product;
        }
        return false;
    }
}
