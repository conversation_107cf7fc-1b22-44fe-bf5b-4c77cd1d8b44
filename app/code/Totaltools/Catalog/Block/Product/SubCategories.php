<?php
/**
 * <AUTHOR> Dev Team
 * @package    Totaltools_Catalog
 * @copyright  Copyright (c) 2019, Totaltools.  (http://www.totaltools.com.au/)
 */

namespace Totaltools\Catalog\Block\Product;

use Magento\Framework\View\Element\Template;
use Magento\Catalog\Model\Layer\Resolver;
use Magento\Catalog\Model\CategoryRepository;
use Magento\Catalog\Model\CategoryFactory;
use Magento\Framework\View\Element\Template\Context;
use Magento\Catalog\Model\Category;
use Magento\Framework\Exception\LocalizedException;
use Magento\Catalog\Model\Product\Visibility;

class SubCategories extends Template
{
    /**
     * @var Resolver
     */
    private $_layerResolver;

    /**
     * @var CategoryRepository
     */
    public $categoryRepository;

    /**
     * @var CategoryFactory
     */
    public $categoryFactory;

    /**
     * @param Context $context
     * @param Resolver $_layerResolver
     * @param CategoryRepository $categoryRepository
     * @param CategoryFactory $categoryFactory
     * @param array $data
     */
    public function __construct(
        Context $context,
        Resolver $_layerResolver,
        CategoryRepository $categoryRepository,
        CategoryFactory $categoryFactory,
        array $data = []
    ) {
        parent::__construct($context, $data);

        $this->_layerResolver = $_layerResolver;
        $this->categoryRepository = $categoryRepository;
        $this->categoryFactory = $categoryFactory;
    }

    public function getCurrentCategory()
    {
        return $this->_layerResolver->get()->getCurrentCategory();
    }

    /**
     * @return integer
     */
    public function getCurrentCategoryId()
    {
        return $this->getCurrentCategory()->getId();
    }

    /**
     * @param integer
     *
     * @return Category
     */
    public function getCategoryData($id)
    {
        return $this->categoryFactory->create()->load($id);
    }

    /**
     * @param $id
     * @return bool|string
     * @throws LocalizedException
     */
    public function getImageUrl($id)
    {
        $category = $this->categoryFactory->create()->load($id);

        return $category->getImageUrl();
    }

    /**
     * This function will fetch the product count.
     *
     * @param $id
     * @return integer
     */
    public function getProductsCount($id)
    {
        $category = $this->categoryFactory->create()->load($id);
        $collection = $category->getProductCollection()
            ->addAttributeToSelect('*')
            ->addAttributeToFilter('visibility', array('in' => [
                Visibility::VISIBILITY_IN_CATALOG,
                Visibility::VISIBILITY_IN_SEARCH,
                Visibility::VISIBILITY_BOTH
            ]))
            ->addAttributeToFilter('status', array('eq' => 1));
        $collection->getSelect()->join('cataloginventory_stock_status', 'cataloginventory_stock_status.product_id = e.entity_id', array('stock_status'))
            ->where("`cataloginventory_stock_status`.`stock_status` = 1");
        return $collection->getSize();
    }

    /**
     * @return string
     */
    public function getPagerHtml()
    {
        return $this->getChildHtml('pager');
    }
}
