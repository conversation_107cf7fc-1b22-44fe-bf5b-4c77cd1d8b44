<?php

namespace Totaltools\Catalog\Block\Product;

class Fulfilment extends \Magento\Framework\View\Element\Template
{
    /**
     * @var GeoLocateModel
     */
    protected $_geoLocateModel;

    /**
     * @var array
     */
    protected $jsLayout;

    /**
     * @var Product
     */
    protected $_product = null;

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param GeoLocateModel $geoLocateModel
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Totaltools\Geo\Model\GeoLocateModel $geoLocateModel,
        \Magento\Framework\Registry $registry,
        array $data = []
    )
    {
        parent::__construct($context, $data);
        $this->_geoLocateModel = $geoLocateModel;
        $this->jsLayout = isset($data['jsLayout']) && is_array($data['jsLayout']) ? $data['jsLayout'] : [];
        $this->_coreRegistry = $registry;
    }

    /**
     * @return string
     */
    public function getJsLayout()
    {
        return \Laminas\Json\Json::encode($this->jsLayout);
    }

    /**
     * @return string
     */
    public function getGeoLocation()
    {
        return $this->_geoLocateModel->getLocation(false, true);
    }

    /**
     * @return Product
     */
    public function getProduct()
    {
        if (!$this->_product) {
            $this->_product = $this->_coreRegistry->registry('product');
        }
        return $this->_product;
    }

}
