<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Catalog
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2018, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Catalog\Block\Product;

use Magento\Framework\View\Element\Template;;

/**
 * Class Count
 * @package Totaltools\Catalog\Block\Product
 */
class Count extends Template
{
    protected $_template = 'Totaltools_Catalog::product/count.phtml';

    /**
     * Request instance
     *
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $_request;


    protected $categoryFactory;


    public function __construct(
        Template\Context $context,
        \Magento\Catalog\Model\CategoryFactory $categoryFactory,
        array $data = [])
    {
        $this->categoryFactory = $categoryFactory;
        parent::__construct($context, $data);
    }
    
    /**
     * Get Category ID
     *
     * @return mixed
     */
    public function getCategoryId()
    {
        return $this->_request->getParam('category_id');
    }

    /**
     * Get Category Text Color
     *
     * @return bool|mixed
     */
    public function getCategoryTextColor()
    {
        if($this->_request->getParam('text_color') != '') {
            return $this->_request->getParam('text_color');
        } else {
            return false;
        }
    }

    /**
     * Get Category URL
     *
     * @return mixed
     */
    public function getCategoryUrl()
    {
        return $this->_request->getParam('category_url');
    }

    /**
     * This function will fetch the product count.
     *
     * @param $categoryId
     * @return mixed
     */
    public function getProductCount($categoryId)
    {
        $category = $this->categoryFactory->create()->load($categoryId);
        $collection = $category->getProductCollection()
            ->addAttributeToSelect('*')
            ->addAttributeToFilter('visibility', array('in' => [
                \Magento\Catalog\Model\Product\Visibility::VISIBILITY_IN_CATALOG,
                \Magento\Catalog\Model\Product\Visibility::VISIBILITY_IN_SEARCH,
                \Magento\Catalog\Model\Product\Visibility::VISIBILITY_BOTH
            ]) )
            ->addAttributeToFilter('status', array('eq' => 1) );
        $collection->getSelect()->join('cataloginventory_stock_status', 'cataloginventory_stock_status.product_id = e.entity_id', array('stock_status'))
            ->where("`cataloginventory_stock_status`.`stock_status` = 1");
        return $collection->getSize();
    }
}