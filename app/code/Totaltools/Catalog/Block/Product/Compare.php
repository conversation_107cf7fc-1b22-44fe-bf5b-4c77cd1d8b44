<?php

namespace Totaltools\Catalog\Block\Product;

/**
 * Class Compare
 * @package Totaltools\Catalog\Block\Product
 */
class Compare extends \Magento\Catalog\Block\Product\AbstractProduct
{
    protected $scopeConfig;

    protected $productCollectionFactory;

    protected $registry;

    protected $priceCurrency;

    public function __construct(
        \Magento\Catalog\Block\Product\Context $context,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Catalog\Model\ResourceModel\Product\CollectionFactory $productCollectionFactory,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->scopeConfig = $scopeConfig;
        $this->productCollectionFactory = $productCollectionFactory;
        $this->registry = $registry;
        $this->priceCurrency = $priceCurrency;
    }

    public function getCompareKitProducts()
    {
        $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
        return $this->scopeConfig->getValue('catalog/compare_combo_kit/product_skus', $storeScope);
    }

    public function getItems()
    {
        $products = $this->getCompareKitProducts();
        $productsArray = explode(",", $products ?? '');
        $rearrangeProductsArray = $this->rearrangeProductsArray($productsArray);
        $sortedSkus = '';
        if ($rearrangeProductsArray) {
            $sortedSkus = implode("','", $rearrangeProductsArray);
            $sortedSkus = "'" . $sortedSkus . "'";
        }
        
        /** @var \Magento\Catalog\Model\ResourceModel\Product\Collection $collection */
        $collection = $this->productCollectionFactory->create();
        $collection->addAttributeToSelect('*');
        $collection->addAttributeToFilter('sku', ['in' => $rearrangeProductsArray]);
        if ($sortedSkus) {
            $collection->getSelect()->order(new \Zend_Db_Expr('FIELD(sku,' . $sortedSkus .')'));
        }

        return $collection;
    }

    public function getCurrentProductSku()
    {
        $currentProduct = $this->registry->registry('current_product');
        return $currentProduct->getSku();
    }

    public function showCompareKitBlock()
    {
        $products = $this->getCompareKitProducts();
        $productsArray = explode(",", $products ?? '');
        $currentSku = $this->getCurrentProductSku();
        return in_array($currentSku, $productsArray) ? true : false;
    }

    public function getCompareKitAttributeData($product)
    {
        $data = [];
        $includeAttr = ['brand', 'part_no', 'brushless_brushed', 'voltage_2', 'combo_kit', 'batteries_supplied'];
        $newLabel = "";
        $attributes = $product->getAttributes();
        foreach ($attributes as $attribute) {
            if ($attribute->getIsVisibleOnFront() && in_array($attribute->getAttributeCode(), $includeAttr)) {
                switch ($attribute->getAttributeCode()) {
                    case 'brand':
                        $newLabel = "Brand";
                        break;
                    case 'part_no':
                        $newLabel = "Part No";
                        break;
                    case 'brushless_brushed':
                        $newLabel = "Brushless/Brushed";
                        break;
                    case 'voltage_2':
                        $newLabel = "Battery Power Type";
                        break;
                    case 'combo_kit':
                        $newLabel = "Number of Tools (Excluding Bonus Tools)";
                        break;
                    case 'batteries_supplied':
                        $newLabel = "Number of Batteries Supplied";
                        break;
                }

                $value = $attribute->getFrontend()->getValue($product);

                if (!$product->hasData($attribute->getAttributeCode())) {
                    $value = __('N/A');
                } elseif ((string)$value == '') {
                    $value = __('No');
                } elseif ($attribute->getFrontendInput() == 'price' && is_string($value)) {
                    $value = $this->priceCurrency->convertAndFormat($value);
                }

                $data[$attribute->getAttributeCode()] = [
                    'label' => __($attribute->getStoreLabel()),
                    'frontend_label' => $newLabel,
                    'value' => $value,
                    'code' => $attribute->getAttributeCode(),
                ];
            }
        }

        $sortedData = [];

        foreach ($includeAttr as $k) {
            if (isset($data[$k])) {
                $sortedData[$k] = $data[$k];
            }
        }
        
        return $sortedData;
    }

    public function getFormattedPrice($price)
    {
        return $this->priceCurrency->convertAndFormat($price);
    }

    public function rearrangeProductsArray($productsArray)
    {
        $currentSku = $this->getCurrentProductSku();
        if ($productsArray) {
            $key = array_search($currentSku, $productsArray);
            unset($productsArray[$key]);
            array_unshift($productsArray, $currentSku);
        }

        return $productsArray;
    }
}
