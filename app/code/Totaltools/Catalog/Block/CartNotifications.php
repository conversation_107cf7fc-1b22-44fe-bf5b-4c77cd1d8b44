<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Catalog\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\Serialize\Serializer\Json;
use Totaltools\Catalog\Helper\Notifications\Data;

class CartNotifications extends Template
{
    /**
     * @inheritdoc
     */
    protected $_template = 'Totaltools_Catalog::cart_notifications.phtml';

    /**
     * @var Json
     */
    protected $serializer;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * CartNotifications constructor
     *
     * @param Template\Context $context
     * @param Json $serializer
     * @param Data $helper
     * @param array $data
     */
    public function __construct(
        Template\Context $context,
        Json $serializer,
        Data $helper,
        array $data = []
    ) {
        $this->serializer = $serializer;
        $this->helper = $helper;

        parent::__construct($context, $data);
    }

    /**
     * @return string
     */
    public function getJsonConfig()
    {
        $config = [
            'canAutoHide' => $this->helper->getFlag('auto_hide'),
            'holdup' => (int) ($this->helper->getConfig('holdup') * 1000),
            'fadeTime' => (int) ($this->helper->getConfig('fade_time') * 1000),
            'hasCloseBtn' => $this->helper->getFlag('close_btn'),
            'position' => $this->helper->getConfig('position'),
            'title' => $this->helper->getConfig('title'),
            'btnText' => $this->helper->getConfig('button_text'),
            'promoText' => $this->helper->getConfig('promo_text'),
        ];

        return $this->serializer->serialize($config);
    }

    /**
     * @return boolean
     */
    public function isEnabled()
    {
        return $this->helper->isEnabled();
    }
}
