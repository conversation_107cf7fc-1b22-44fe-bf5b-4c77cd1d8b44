<?php
/**
 * @category    Totaltools
 * @package     Totaltools_Optimization
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Catalog\Block\Currency;

use Magento\Directory\Model\Currency;

class Format extends \Magento\Framework\View\Element\Template
{
    protected $_currency;
    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param Magento\Directory\Model\Currency $currency
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        Currency $currency,
        array $data = []
    ) {
        $this->_currency = $currency;
        parent::__construct($context, $data);
    }

    /**
     * currency object
     *
     * @return Magento\Directory\Model\Currency
     */
    public function getCurrency()
    {
        return $this->_currency;
    }

}
