<?php

namespace Totaltools\Catalog\Observer;

use Magento\Framework\Registry;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Psr\Log\LoggerInterface as Logger;
use Totaltools\Catalog\Block\Category\View;
use Magento\Framework\Exception\NoSuchEntityException;

class CategoryDisplayLayout implements ObserverInterface
{
    const ACTION_NAME = 'catalog_category_view';

    /** @var Registry */
    private $registry;

    /**
    * @var Logger
    */
    private $logger;

    /**
     * @param Registry $registry
     * @param Logger $logger
     */
    public function __construct(
        Registry $registry,
        Logger $logger
    ) {
        $this->registry = $registry;
        $this->logger = $logger;
    }

    /**
    * @param Observer $observer
    */
    public function execute(Observer $observer)
    {
        if ($observer->getFullActionName() !== static::ACTION_NAME) {
            return;
        }

        try {
            /** @var \Magento\Catalog\Model\Category $category */
            $category = $this->registry->registry('current_category');

            if ($category) {
                $displayMode = $category->getDisplayMode();

                if (
                    $displayMode != View::DM_SUBCATEGORIES &&
                    $displayMode != View::DM_SUBCATEGORIES_PRODUCTS &&
                    $displayMode != View::DM_STATIC_BLOCKS_SUBCATEGORIES &&
                    $displayMode != View::DM_SUBCATEGORIES_MIXED &&
                    $displayMode != View:: DM_STATIC_BLOCKS_PRODUCTS
                ) {
                    return;
                }

                /** @var \Magento\Framework\View\Layout $layout */
                $layout = $observer->getLayout();
                $layout->getUpdate()->addHandle(static::ACTION_NAME . '_' . strtolower($displayMode));
            }
            
        } catch (\Exception $exception) {
            $this->logger->error($exception->getMessage());
        }
    }
}
