<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_Catalog
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2018, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\Catalog\Observer;

use Magento\Framework\Event\ObserverInterface;

class ViewConfigObserver implements ObserverInterface
{
    /**
     * @var \Magento\Framework\Pricing\PriceCurrencyInterface
     */
    private $priceCurrency;

    /**
     * @param \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency
     */
    public function __construct(
        \Magento\Framework\Pricing\PriceCurrencyInterface $priceCurrency
    ) {

        $this->priceCurrency = $priceCurrency;
    }

    /**
     * Append extra options to the price config in product view jsonConfig
     * called from \Magento\Catalog\Block\Product\View::getJsonConfig
     *
     * @param \Magento\Framework\Event\Observer $observer
     * @return $this
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $responseObject = $observer->getEvent()->getResponseObject();

        $data = $responseObject->getAdditionalOptions();
        $data['currencyFormatCode'] = $this->priceCurrency->getCurrencySymbol();

        $responseObject->setAdditionalOptions($data);
        return $this;
    }
}
