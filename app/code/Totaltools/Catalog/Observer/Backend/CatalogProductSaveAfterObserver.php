<?php
/**
 * CatalogProductSaveAfterObserver
 *
 * @category  Totaltools
 * @package   Totaltools_Catalog
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Catalog\Observer\Backend;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

/**
 * Class CatalogProductSaveAfterObserver
 * @package Totaltools\Catalog\Observer\Backend
 */
class CatalogProductSaveAfterObserver implements ObserverInterface
{
    /**
     * @param Observer $observer
     */
    public function execute(Observer $observer)
    {
        $product = $observer->getProduct();
        $productNewFromDate = $product->getProductNewFromDate();
        $productNewToDate = $product->getProductNewToDate();
        $product->setNewsFromDate($productNewFromDate);  
        $product->setNewsToDate($productNewToDate);
        $product->getResource()->saveAttribute($product, 'news_from_date');
        $product->getResource()->saveAttribute($product, 'news_to_date');
        
    }
}
