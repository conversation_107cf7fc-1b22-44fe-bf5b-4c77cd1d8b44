<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Catalog\Helper\Notifications;

use Magento\Store\Model\ScopeInterface;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @return string
     */
    const XML_CONFIG_PATH = 'catalog/cart_notifications/';

    /**
     * @return string
     */
    public function getConfig($key, $scope = ScopeInterface::SCOPE_WEBSITE)
    {
        return $this->scopeConfig->getValue(self::XML_CONFIG_PATH . $key, $scope);
    }

    /**
     * @return boolean
     */
    public function getFlag($key, $scope = ScopeInterface::SCOPE_WEBSITE)
    {
        return $this->scopeConfig->isSetFlag(self::XML_CONFIG_PATH . $key, $scope);
    }

    /**
     * @return boolean
     */
    public function isEnabled()
    {
        return $this->getFlag('enabled');
    }
}
