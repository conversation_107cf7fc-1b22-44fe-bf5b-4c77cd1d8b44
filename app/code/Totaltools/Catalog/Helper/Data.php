<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Catalog\Helper;

use Magento\Catalog\Model\Product;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Filesystem;
use Magento\Framework\UrlInterface;
use Magento\Framework\Registry;
use Magento\Framework\View\Asset\Repository;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Zip\ZipPayment\Model\Config as ZipConfig;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    const XML_PATH_FEATURED_CATEGORIES = 'catalog/brands_setting/featured_brands';

    const XML_DEFALUT_PLACEHOLDER_PATH = 'catalog/placeholder/image_placeholder';

    /**
     * @var \Magento\Framework\View\Asset\Repository
     */
    protected $assetRepository;

    /**
     * Filesystem instance
     *
     * @var Filesystem
     */
    protected $filesystem;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory
     */
    protected $categoryCollectionFactory;

    private Registry $coreRegistry;

    private ZipConfig $zipConfig;
    private PriceCurrencyInterface $priceCurrency;

    /**
     * Data constructor.
     *
     * @param Context $context
     * @param Filesystem $filesystem
     * @param Repository $assetRepository
     * @param StoreManagerInterface $storeManager
     * @param CollectionFactory $categoryFactory
     * @param Registry $coreRegistry
     * @param PriceCurrencyInterface $priceCurrency
     * @param ZipConfig $zipConfig
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\View\Asset\Repository $assetRepository,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Catalog\Model\ResourceModel\Category\CollectionFactory $categoryFactory,
        Registry $coreRegistry,
        PriceCurrencyInterface $priceCurrency,
        ZipConfig $zipConfig
    ) {
        parent::__construct($context);
        $this->filesystem = $filesystem;
        $this->assetRepository = $assetRepository;
        $this->storeManager = $storeManager;
        $this->categoryCollectionFactory = $categoryFactory;
        $this->coreRegistry = $coreRegistry;
        $this->zipConfig = $zipConfig;
        $this->priceCurrency = $priceCurrency;
    }

    /**
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getFeaturedCategories()
    {
        $categories = $this->getCollection()
            ->addAttributeToSelect('*')
            ->addAttributeToFilter('is_active', array('eq' => 1))
            ->addAttributeToFilter('is_featured', array('eq' => 1))
            ->setOrder('featured_order','ASC')
            ->setStore($this->storeManager->getStore());

        return $categories;
    }

    /**
     * @param \Magento\Catalog\Model\Category $category
     *
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getNonFeaturedCategories($category, $featureCategories = array())
    {
        //$categoryIds = explode(',', $this->getIdsFromConfiguration());

        $categories = $this->getCollection()
            ->addAttributeToSelect('*')
            ->addFieldToFilter('parent_id', $category->getId())
            //->addFieldToFilter('entity_id', ['nin' => $categoryIds])
            ->setStore($this->storeManager->getStore())
            ->addAttributeToFilter('is_active', array('eq' => 1));
        if(isset($featureCategories) && !empty($featureCategories)) {
            $categories->addAttributeToFilter('entity_id', array('nin' => $featureCategories));
        }
        $categories->addAttributeToSort('name', 'ASC');
        return $categories;
    }

    /**
     * @param string $path
     *
     * @return string
     */
    public function getMediaUrl($path)
    {
        return sprintf(
            '%s%s',
            $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA),
            $path
        );
    }

    /**
     * @param string $path
     *
     * @return string
     */
    public function getBaseUrl($path)
    {
        return sprintf(
            '%s%s',
            $this->storeManager->getStore()->getBaseUrl(),
            $path
        );
    }

    /**
     * @param \Magento\Catalog\Model\Category $category
     *
     * @return string|null
     */
    public function getBrandImagePath($category)
    {
        $imagePath = $category->getBrandImage();
        if($imagePath != '') {
            if (!strpos($imagePath, "catalog/category")) {
                $imagePath =  'catalog/category/'.$imagePath;
            } else {
                $imagePath = ltrim($imagePath, '/media/');
            }

            return $imagePath;
        }

        return '';
    }


    /**
     * @param \Magento\Catalog\Model\Category $category
     *
     * @return string|null
     */
    public function getBrandImageUrl($category)
    {
        $imagePath = $category->getBrandImage();
        if($imagePath != '') {
            if (!strpos($imagePath, "catalog/category")) {
                $brandImage = 'catalog/category/'.$imagePath;
                $imagePath = $this->getMediaUrl($brandImage);
            } else {
                $imagePath = $this->getBaseUrl(ltrim($imagePath, '/'));
            }
            return $imagePath;
        }

        return '';
    }

    /**
     * @param string $path
     *
     * @return bool
     */
    public function isBrandImageExists($path)
    {
        return $this->getMediaDirectory()->isExist($path);
    }

    /**
     * @return string|null
     */
    private function getIdsFromConfiguration()
    {
        return $this->scopeConfig->getValue(
            self::XML_PATH_FEATURED_CATEGORIES,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * @return Filesystem\Directory\ReadInterface
     */
    private function getMediaDirectory()
    {
        return $this->filesystem->getDirectoryRead(DirectoryList::MEDIA);
    }

    /**
     * @return Filesystem\Directory\ReadInterface
     */
    private function getStaticViewDirectory()
    {
        return $this->filesystem->getDirectoryRead(DirectoryList::STATIC_VIEW);
    }

    /**
     * @return \Magento\Catalog\Model\ResourceModel\Category\Collection
     */
    private function getCollection()
    {
        return $this->categoryCollectionFactory->create();
    }

    /**
     * This method will return the default placeholder image of product.
     *
     * @return mixed
     */
    public function getDefaultImage()
    {
        return $this->storeManager->getStore()->getBaseUrl(UrlInterface::URL_TYPE_MEDIA).
            'catalog/product/placeholder/'.$this->scopeConfig->getValue(
            self::XML_DEFALUT_PLACEHOLDER_PATH,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    public function getAjaxUrl()
    {
        return $this->storeManager->getStore()->getUrl('productcount/index/count');
    }

    /**
     * @return float
     */
    public function getCurrentProductFinalPrice()
    {
        $finalPrice = 0;
        if ($product = $this->getCurrentProduct()) {
            $finalPrice = $product->getPriceInfo()->getPrice('final_price')->getAmount()->getValue();
        }

        return (float) $finalPrice;
    }

    /**
     * @return Product|null
     */
    public function getCurrentProduct()
    {
        return $this->coreRegistry->registry('product') ?? null;
    }

    /**
     * @return bool
     */
    public function showCartZipWidget()
    {
        return $this->zipConfig->isMethodActive() &&
            $this->zipConfig->getConfigData(ZipConfig::ADVERTS_CART_IMAGE_ACTIVE);
    }

    /**
     * @return bool
     */
    public function showPDPZipWidget()
    {
        return $this->zipConfig->isMethodActive() &&
            $this->zipConfig->getConfigData(ZipConfig::ADVERTS_PRODUCT_IMAGE_ACTIVE);
    }

    /**
     * @return array
     */
    public function getZipPaymentConfig()
    {
        return [
            'publicKey' => $this->zipConfig->getMerchantPublicKey(),
            'minTotal' => $this->zipConfig->getOrderTotalMinimum(),
            'maxTotal' => $this->zipConfig->getOrderTotalMaximum()
        ];
    }

    /**
     * @param $price
     * @return string
     */
    public function convertAndFormatPrice($price)
    {
        $value = $this->priceCurrency->convertAndFormat($price, false);
        return preg_replace('~\.0+$~','', $value);
    }

    /**
     * @return string
     */
    public function getCurrencySymbol()
    {
        return $this->priceCurrency->getCurrencySymbol();
    }

     /**
     * $excludeAttr is optional array of attribute codes to
     * exclude them from additional data array
     *
     * @param Magento\Catalog\Model\Product $product
     * @param array $excludeAttr
     * @return array
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    public function getAdditionalData($product, array $excludeAttr = [])
    {
        $data = [];
        $attributes = $product->getAttributes();
        foreach ($attributes as $attribute) {
            if ($attribute->getIsVisibleOnFront() && !in_array($attribute->getAttributeCode(), $excludeAttr)) {
                $value = $attribute->getFrontend()->getValue($product);

                if (!$product->hasData($attribute->getAttributeCode())) {
                    $value = __('N/A');
                } elseif ((string)$value == '') {
                    $value = __('No');
                } elseif ($attribute->getFrontendInput() == 'price' && is_string($value)) {
                    $value = $this->priceCurrency->convertAndFormat($value);
                }

                if (is_string($value) && strlen($value)) {
                    $data[$attribute->getAttributeCode()] = [
                        'label' => __($attribute->getStoreLabel()),
                        'value' => $value,
                        'code' => $attribute->getAttributeCode(),
                    ];
                }
            }
        }
        return $data;
    }
}
