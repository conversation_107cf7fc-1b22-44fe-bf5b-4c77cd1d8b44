<?php


namespace Totaltools\Catalog\Rewrite\Magento\Catalog\Block\Product\View;

/**
 * Class Gallery
 *
 * @package Totaltools\Catalog\Rewrite\Magento\Catalog\Block\Product\View
 */
class Gallery extends \Magento\Catalog\Block\Product\View\Gallery
{
    const PRODUCT_LABELED_IMAGE = 'catalog/labeled_image/enabled';
    /**
     * Is product main image
     *
     * @param \Magento\Framework\DataObject $image
     * @return bool
     */
    public function isMainImage($image)
    {
        $product = $this->getProduct();
        if ($this->_scopeConfig->getValue(self::PRODUCT_LABELED_IMAGE))
        {
            $labeledImage = $product->getLabeledImage();
            if (isset($labeledImage) && $labeledImage != 'no_selection') {
                return $labeledImage == $image->getFile();
            }
        }
        return $product->getImage() == $image->getFile();
    }

    /**
     * Retrieve product images in JSON format
     *
     * @return string
     */
    public function getGalleryImagesJson()
    {
        $imagesItems = [];
        foreach ($this->getGalleryImages() as $image) {
            $imagesItems[] = [
                'thumb' => $image->getData('small_image_url'),
                'img' => $image->getData('medium_image_url'),
                'full' => $image->getData('large_image_url'),
                'caption' => ($image->getLabel() ?: $this->getProduct()->getName()),
                'position' => $image->getPosition(),
                'isMain' => $this->isMainImage($image),
                'type' => str_replace('external-', '', $image->getMediaType()),
                'videoUrl' => $image->getVideoUrl(),
            ];
        }

        if (empty($imagesItems)) {
            $imagesItems[] = [
                'thumb' => $this->_imageHelper->getDefaultPlaceholderUrl('thumbnail'),
                'img' => $this->_imageHelper->getDefaultPlaceholderUrl('image'),
                'full' => $this->_imageHelper->getDefaultPlaceholderUrl('image'),
                'caption' => '',
                'position' => '0',
                'isMain' => true,
                'type' => 'image',
                'videoUrl' => null,
            ];
        }
        /*Images sorting*/
        $mainImageIndex = array_search("1", array_column($imagesItems, 'isMain'));
        if ($mainImageIndex > 0) {
            $firstImage =$imagesItems[$mainImageIndex];
            unset($imagesItems[$mainImageIndex]);
            $imagesItems = array_merge([0 =>$firstImage], $imagesItems);
        }
        return json_encode($imagesItems);
    }
    /**
     * Return magnifier options
     *
     * @return string
     */
    public function getMagnifier()
    {
        return $this->jsonEncoder->encode($this->getVar('magnifier', 'Magento_Catalog'));
    }

    /**
     * Return breakpoints options
     *
     * @return string
     */
    public function getBreakpoints()
    {
        return $this->jsonEncoder->encode($this->getVar('breakpoints', 'Magento_Catalog'));
    }
}

