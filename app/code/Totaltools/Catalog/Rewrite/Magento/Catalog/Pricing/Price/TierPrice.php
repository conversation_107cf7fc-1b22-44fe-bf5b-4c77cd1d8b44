<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Catalog\Rewrite\Magento\Catalog\Pricing\Price;

use Magento\Framework\Pricing\Amount\AmountInterface;
use Magento\Catalog\Pricing\Price\FinalPrice;

class TierPrice extends \Magento\Catalog\Pricing\Price\TierPrice
{
    public function getSavePercent(AmountInterface $amount)
    {
        $productPriceAmount = $this->priceInfo->getPrice(FinalPrice::PRICE_CODE)
            ->getAmount();

        return floor(100 - ((100 / $productPriceAmount->getValue()) * $amount->getValue()));
    }
}
