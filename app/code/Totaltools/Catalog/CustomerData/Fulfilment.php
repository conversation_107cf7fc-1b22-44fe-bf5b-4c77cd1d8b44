<?php

namespace Totaltools\Catalog\CustomerData;

/**
 * Fulfilment is a section source class to provide private content for fulfilment-data.
 *
 * @package Totaltools_Catalog
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2019 Totaltools. All rights reserved. <https://totaltools.com.au>
 */

class Fulfilment extends \Magento\Framework\DataObject implements \Magento\Customer\CustomerData\SectionSourceInterface
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $checkoutSession;

    /**
     * @var \Magento\Customer\Helper\Session\CurrentCustomerAddress
     */
    protected $currentCustomerAddress;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    /**
     * @var \Totaltools\Geo\Model\GeoLocateModel
     */
    protected $geoLocateModel;

    /**
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Customer\Helper\Session\CurrentCustomerAddress $currentCustomerAddress
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Geo\Model\GeoLocateModel $geoLocateModel
     * @param array $data
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magento\Customer\Helper\Session\CurrentCustomerAddress $currentCustomerAddress,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Geo\Model\GeoLocateModel $geoLocateModel,
        array $data = []
    ) {
        parent::__construct($data);

        $this->checkoutSession = $checkoutSession;
        $this->storeRepository = $storeRepository;
        $this->geoLocateModel = $geoLocateModel;
        $this->currentCustomerAddress = $currentCustomerAddress;
    }

    /**
     * @inheritdoc
     */
    public function getSectionData()
    {
        return [
            'location' => $this->getLocation(),
            'store' => $this->getStore()
        ];
    }

    /**
     * Get user location from session, if not found try fetching from GeoLocateModel
     * either from cached location data or based on user IP
     * @return array|null
     */
    private function getLocation()
    {
        $location = $this->checkoutSession->getData('guest_location_session');

        if (!$location || $location['postcode'] == null) {
            $geoLocation = $this->geoLocateModel->getLocation(false, false);
            $location = isset($geoLocation['postcode']) ? $geoLocation : null;
        }

        if ($location && isset($location['store'])) {
            unset($location['store']);
        }

        return $location;
    }

    /**
     * Get user store from session, if not found try fetching from GeoLocateModel
     * either from cached location data or based on user IP
     * @return array|null
     */
    private function getStore()
    {
        $store = null;
        $storeData = [];
        $nearestStore = $this->checkoutSession->getData('neareststore_session');
        if ($nearestStore) {
            if ($this->checkoutSession->getData('dont_check_for_nearest_store') != true) {
                $this->checkoutSession->setStorepickupSession($nearestStore);
                $this->checkoutSession->unsNeareststoreSession();
                $this->checkoutSession->setNeareststoreCheckSession(true);
                $this->checkoutSession->setData('dont_check_for_nearest_store', true);
            } elseif ($this->checkoutSession->getBrowserLocationSession() === true) {
                $this->checkoutSession->setStorepickupSession($nearestStore);
                $this->checkoutSession->unsNeareststoreSession();
                $this->checkoutSession->unsBrowserLocationSession();
            }
        } 
        
        $storePickup = $this->checkoutSession->getData('storepickup_session');
        $storeId = $storePickup['store_id'] ?? null;

        if (!$storeId) {
            $geoLocation = $this->geoLocateModel->getLocation(false, false);
            $storeId = isset($geoLocation['store']['storelocator_id']) ? $geoLocation['store']['storelocator_id'] : $storeId;
        }

        if ($storeId && ($store = $this->storeRepository->getById($storeId)) !== null) {
            $storeData = $store->getData();
            $storeData['upcoming_special_day'] = current($store->getSpecialdaysData());
            $storeData['upcoming_holiday'] = current($store->getHolidaysData());
        }
        return $storeData;
    }
}
