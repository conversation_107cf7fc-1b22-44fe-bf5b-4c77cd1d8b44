<?php
namespace Totaltools\Catalog\Ui\DataProvider\Product\Listing\Collector;
 

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\Data\ProductRender\PriceInfoInterface;
use Magento\Catalog\Api\Data\ProductRender\PriceInfoInterfaceFactory;
use Magento\Catalog\Api\Data\ProductRenderInterface;
use Magento\Catalog\Model\ProductRender\FormattedPriceInfoBuilder;
use Magento\Catalog\Ui\DataProvider\Product\ProductRenderCollectorInterface;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Framework\Locale\Format;
use Magento\Catalog\Api\Data\ProductRenderExtensionFactory;
/**
 * Collect enough information for rendering reviews, ratings, etc...
 * Can be used on product page, on product listing page
 */
class Label implements ProductRenderCollectorInterface
{
    /** FInal Price key */
    const show_saved_label = "show_saved_label";

   
    /**
     * @var Format
     */
    private $localeFormat;
    /**
     * @var PriceCurrencyInterface
     */
    private $priceCurrency;
    /**
     * @var array
     */
    private $excludeAdjustments;

    /**
     * @var PriceInfoInterfaceFactory
     */
    private $priceInfoFactory;
        /**
     * @var FormattedPriceInfoBuilder
     */
    private $formattedPriceInfoBuilder;
    /**
     * @var ProductRenderExtensionFactory
     */
    protected $productRenderExtensionFactory;


    /**
     * @param PriceCurrencyInterface $priceCurrency
     * @param PriceInfoInterfaceFactory $priceInfoFactory
     * @param FormattedPriceInfoBuilder $formattedPriceInfoBuilder
     * @param ProductRenderExtensionFactory $productRenderExtensionFactory
     * @param array $excludeAdjustments
     */
    public function __construct(
        PriceCurrencyInterface $priceCurrency,
        PriceInfoInterfaceFactory $priceInfoFactory,
        FormattedPriceInfoBuilder $formattedPriceInfoBuilder,
        Format $localeFormat,
        ProductRenderExtensionFactory $productRenderExtensionFactory,
        array $excludeAdjustments = []
    ) {
        $this->localeFormat = $localeFormat;
        $this->priceCurrency = $priceCurrency;
        $this->excludeAdjustments = $excludeAdjustments;
        $this->priceInfoFactory = $priceInfoFactory;
        $this->formattedPriceInfoBuilder = $formattedPriceInfoBuilder;
        $this->productRenderExtensionFactory = $productRenderExtensionFactory;
    }

    /**
     * @inheritdoc
     */
    public function collect(ProductInterface $product, ProductRenderInterface $productRender)
    {
        $extensionAttributes = $productRender->getExtensionAttributes();

        if (!$extensionAttributes) {
            $extensionAttributes = $this->productRenderExtensionFactory->create();
        }
        $showSavedLabel = $product->getData('show_saved_label') ?? 'N';
        $extensionAttributes
            ->setShowSavedLabel($showSavedLabel);

        $productRender->setExtensionAttributes($extensionAttributes);
    }

}