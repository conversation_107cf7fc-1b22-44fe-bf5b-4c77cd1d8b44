<?php

namespace Totaltools\Catalog\Ui\DataProvider\Product\Listing\Collector;

use Magento\Catalog\Api\Data\ProductInterface;
use Magento\Catalog\Api\Data\ProductRenderInterface;
use Magento\Catalog\Ui\DataProvider\Product\ProductRenderCollectorInterface;
use Magento\Catalog\Api\Data\ProductRenderExtensionFactory;

class NotForSale implements ProductRenderCollectorInterface
{
    const NOT_FOR_SALE = "not_for_sale";
    const NOT_FOR_SALE_MESSAGE = "not_for_sale_message";

    /**
     * @var ProductRenderExtensionFactory
     */
    protected $productRenderExtensionFactory;

    /**
     * @param ProductRenderExtensionFactory $productRenderExtensionFactory
     */
    public function __construct(ProductRenderExtensionFactory $productRenderExtensionFactory)
    {
        $this->productRenderExtensionFactory = $productRenderExtensionFactory;
    }

    /**
     * @inheritdoc
     */
    public function collect(ProductInterface $product, ProductRenderInterface $productRender)
    {
        $extensionAttributes = $productRender->getExtensionAttributes();

        if (!$extensionAttributes) {
            $extensionAttributes = $this->productRenderExtensionFactory->create();
        }

        $notForSale = $product->getData(static::NOT_FOR_SALE) ?? false;
        $notForSaleMessage = $product->getData(static::NOT_FOR_SALE_MESSAGE) ?? '';

        $extensionAttributes
            ->setNotForSale($notForSale)
            ->setNotForSaleMessage($notForSaleMessage);

        $productRender->setExtensionAttributes($extensionAttributes);
    }

}