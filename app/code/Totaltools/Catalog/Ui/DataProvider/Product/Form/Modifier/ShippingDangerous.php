<?php
/**
 * Totaltools Catalog.
 *
 * @category   Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright  2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Catalog\Ui\DataProvider\Product\Form\Modifier;

use Magento\Catalog\Ui\DataProvider\Product\Form\Modifier\AbstractModifier;
use Magento\Framework\Stdlib\ArrayManager;

/**
 * Class ShippingDangerous
 * @package Totaltools\Catalog\Ui\DataProvider\Product\Form\Modifier
 */
class ShippingDangerous extends AbstractModifier
{
        /**
     * @var ArrayManager
     */
    private $arrayManager;

    /**
     * CustomAttribute constructor.
     *
     * @param ArrayManager $arrayManager
     */
    public function __construct(
        ArrayManager $arrayManager
    ) {
        $this->arrayManager = $arrayManager;
    }

    /**
     * {@inheritdoc}
     */
    public function modifyMeta(array $meta)
    {
        $meta = $this->customizeCustomAttrField($meta);

        return $meta;
    }

    /**
     * {@inheritdoc}
     */
    public function modifyData(array $data)
    {
        return $data;
    }

    /**
     * Customise Custom Attribute field
     *
     * @param array $meta
     *
     * @return array
     */
    protected function customizeCustomAttrField(array $meta)
    {
        $fieldCode = 'shipping_dangerous';
        $elementPath = $this->arrayManager->findPath($fieldCode, $meta, null, 'children');
        $containerPath = $this->arrayManager->findPath(static::CONTAINER_PREFIX . $fieldCode, $meta, null, 'children');

        if (!$elementPath) {
            return $meta;
        }

        $meta = $this->arrayManager->merge(
            $containerPath,
            $meta,
            [
                'arguments' => [
                    'data' => [
                        'config' => [
                            'label'         => __('Dangerous'),
                            'breakLine'     => false,
                            'formElement'   => 'container',
                            'componentType' => 'container',
                        ],
                    ],
                ],
                'children'  => [
                    $fieldCode => [
                        'arguments' => [
                            'data' => [
                                'config' => [
                                    'dataType' => 'number',
                                    'formElement' => 'checkbox',
                                    'visible' => '1',
                                    'required' => '0',
                                    'notice' => null,
                                    'default' => null,
                                    'label' => __('Dangerous'),
                                    'code' => $fieldCode,
                                    'source' => 'product-details',
                                    'scopeLabel' => '[WEBSITE]',
                                    'globalScope' => false,
                                    'sortOrder' => 100,
                                    'options' => [
                                        [
                                            'value' => '1',
                                            'label' => 'Yes'
                                        ],
                                        [
                                            'value' => '0',
                                            'label' => 'No'
                                        ]
                                    ],
                                    'componentType' => 'field',
                                    'prefer' => 'toggle',
                                    'valueMap' => [
                                        'true' => '1',
                                        'false' => '0'
                                    ]
                                ],
                            ],
                        ],
                    ]
                ]
            ]
        );

        return $meta;
    }
}
