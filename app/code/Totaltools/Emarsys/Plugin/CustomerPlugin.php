<?php

namespace Totaltools\Emarsys\Plugin;

/**
 * @category    Totaltools
 * @package     Totaltools_Emarsys
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2022 Totaltools (https://totaltools.com.au/)
 */

use Magento\Customer\Helper\Session\CurrentCustomer;
use Magento\Customer\CustomerData\Customer;

class CustomerPlugin
{
    /**
     * @var CurrentCustomer
     */
    private $currentCustomer;

    /**
     * @param CurrentCustomer $currentCustomer
     */
    public function __construct(CurrentCustomer $currentCustomer)
    {
        $this->currentCustomer = $currentCustomer;
    }

    /**
     * @param Customer $subject
     * @param array $result
     * @return array
     */
    public function afterGetSectionData($subject, $result)
    {
        if (!$this->currentCustomer->getCustomerId()) {
            return $result;
        }

        $customer = $this->currentCustomer->getCustomer();
        $extAttributes = $customer->getExtensionAttributes();

        if ($extAttributes->getLoyaltyId()) {
            $result['loyalty_id'] = $extAttributes->getLoyaltyId();
        }

        return $result;
    }
}
