<?php
/**
 * @category  Totaltools
 * @package   Totaltools_
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Emarsys\Model\Observer;

use Magento\Customer\Model\Session;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;

class EmarsysLogin implements ObserverInterface
{
    private Session $customerSession;

    /**
     * @param Session $customerSession
     */
    public function __construct(Session $customerSession)
    {
        $this->customerSession = $customerSession;
    }

    public function execute(Observer $observer)
    {
        $customer = $observer->getEvent()->getCustomer();
        if ($customer->getId()) {
            $this->customerSession->setEmarsysLogin(true);
        }
    }
}
