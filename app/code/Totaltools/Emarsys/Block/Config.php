<?php
/**
 * @category  Totaltools
 * @package   Totaltools_
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Emarsys\Block;

use Magento\Framework\View\Element\Template;
use Magento\Customer\Model\Session;
use Magento\ImportExport\Model\Import\ConfigInterface;
use Totaltools\Emarsys\Helper\Data;

class Config extends Template
{
    private Session $customerSession;

    private Data $helper;

    /**
     * @param Session $customerSession
     * @param Data $helper,
     * @param Template\Context $context
     * @param array $data
     */
    public function __construct(
        Session $customerSession,
        Data $helper,
        Template\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->customerSession = $customerSession;
        $this->helper = $helper;
    }

    /**
     * @return Data
     */
    public function getHelper() {
        return $this->helper;
    }

    /**
     * @return bool
     */
    public function isCustomerLoggedIn()
    {
        return $this->customerSession->getEmarsysLogin();
    }

    /**
     * @return bool
     */
    public function isCustomerLogout()
    {
        return  $this->customerSession->getEmarsysLogout();
    }

    /**
     * @return void
     */
    public function unsCustomerLoginState()
    {
        $this->customerSession->unsEmarsysLogout();
        $this->customerSession->unsEmarsysLogin();
    }

    /**
     * @return \Magento\Customer\Api\Data\CustomerInterface|null
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getCustomerData()
    {
        if ($this->customerSession->isLoggedIn()) {
            return $this->customerSession->getCustomerData();
        }

        return null;
    }

    /**
     * @inheritdoc
     */
    protected function _toHtml()
    {
        if (!$this->helper->isEnabled()) {
            return '';
        }

        return parent::_toHtml();
    }
}
