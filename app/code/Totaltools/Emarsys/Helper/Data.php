<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Emarsys
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2022 Totaltools (https://totaltools.com.au/)
 */

namespace Totaltools\Emarsys\Helper;

use Magento\Framework\App\Helper\AbstractHelper;

class Data extends AbstractHelper
{
    const XML_PATH_EMARSYS_ENABLE_WEBPUSH = 'totaltools_emarsys/webpush/enabled';
    const XML_PATH_EMARSYS_APPLICATION_CODE = 'totaltools_emarsys/webpush/app_code';
    const XML_PATH_EMARSYS_APPLICATION_SERVER_PUBLIC_KEY = 'totaltools_emarsys/webpush/public_key';
    const XML_PATH_EMARSYS_SAFARI_WEBPUSH_ID = 'totaltools_emarsys/webpush/safari_id';
    const XML_PATH_EMARSYS_DEFAULT_NOTIFICATION_TITLE = 'totaltools_emarsys/webpush/default_title';
    const XML_PATH_EMARSYS_DEFAULT_NOTIFICATION_ICON = 'totaltools_emarsys/webpush/default_icon';
    const XML_PATH_EMARSYS_CUSTOMER_FIELD_ID = 'totaltools_emarsys/webpush/customer_field_id';
    const XML_PATH_EMARSYS_AUTO_SUBSCRIBE = 'totaltools_emarsys/webpush/auto_subscribe';

    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_EMARSYS_ENABLE_WEBPUSH);
    }

    public function getAppCode():? string
    {
        return $this->scopeConfig->getValue(self::XML_PATH_EMARSYS_APPLICATION_CODE);
    }

    public function getAppPublicKey():? string
    {
        return $this->scopeConfig->getValue(self::XML_PATH_EMARSYS_APPLICATION_SERVER_PUBLIC_KEY);
    }

    public function getSafariWebId():? string
    {
        return $this->scopeConfig->getValue(self::XML_PATH_EMARSYS_SAFARI_WEBPUSH_ID);
    }

    public function getDefaultTitle():? string
    {
        return $this->scopeConfig->getValue(self::XML_PATH_EMARSYS_DEFAULT_NOTIFICATION_TITLE);
    }

    public function getDefaultIcon():? string
    {
        return $this->scopeConfig->getValue(self::XML_PATH_EMARSYS_DEFAULT_NOTIFICATION_ICON);
    }

    public function getCustomerFieldId():? int
    {
        return (int) $this->scopeConfig->getValue(self::XML_PATH_EMARSYS_CUSTOMER_FIELD_ID);
    }

    public function isAutoSubscribe(): string
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_EMARSYS_AUTO_SUBSCRIBE) ? 'true' : 'false';
    }
}
