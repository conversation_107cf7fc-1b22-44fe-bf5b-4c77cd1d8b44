<?xml version="1.0"?>
<!--
 /** 
 * @category    Totaltools
 * @package     Totaltools_Emarsys
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2022 Totaltools (https://totaltools.com.au/)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="totaltools_emarsys" translate="label" sortOrder="260" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Emarsys</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Emarsys::config_emarsys</resource>
            <group id="webpush" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Emarsys Webpush</label>
                <field id="enabled" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable Webpush</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="app_code" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Application Code</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="public_key" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Application Server Public Key</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="safari_id" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Safari Website Push ID</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="default_title" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Default Notification Title</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="default_icon" translate="label" type="text" sortOrder="60" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Default Notification Icon</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="customer_field_id" translate="label" type="text" sortOrder="65" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Loyalty Field ID</label>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
                <field id="auto_subscribe" translate="label" type="select" sortOrder="70" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Auto Subscribe</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
    </system>
</config>
