<?php

/**
 * @var \Totaltools\Emarsys\Block\Config $block
 */

$helper = $block->getHelper();
?>
<script type="text/javascript">
    var WebEmarsysSdk = WebEmarsysSdk || [];

    window.emarsysCustomerFieldId = <?= /** @noEscape */ $helper->getCustomerFieldId(); ?>;

    WebEmarsysSdk.push(['init', {
        applicationCode: '<?= /** @noEscape */ $helper->getAppCode(); ?>',
        safariWebsitePushID: '<?= /** @noEscape */ $helper->getSafariWebId(); ?>',
        defaultNotificationTitle: '<?= /** @noEscape */ $helper->getDefaultTitle(); ?>',
        defaultNotificationIcon: '<?= /** @noEscape */ $helper->getDefaultIcon(); ?>',
        autoSubscribe: <?= /** @noEscape */ $helper->isAutoSubscribe(); ?>,
        serviceWorker: {
            url: 'service-worker.js',
            applicationServerPublicKey: '<?= /** @noEscape */ $helper->getAppPublicKey(); ?>'
        }
    }]);

    require(['emarsysWebpushClient'], function(client) {
        WebEmarsysSdk.push(['onReady', function() {
            client.init.apply(null, [WebEmarsysSdk]);
        }]);
    });    
</script>
