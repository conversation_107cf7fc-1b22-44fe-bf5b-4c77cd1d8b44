/**
 * @category    Totaltools
 * @package     Totaltools_Emarsys
 * <AUTHOR> <<EMAIL>>
 * @copyright   Copyright (c) 2022 Totaltools (https://totaltools.com.au/)
 */

define([
    'jquery',
    'underscore',
    'Magento_Customer/js/customer-data',
    'jquery/jquery-storageapi',
], function ($, _, storage) {
    'use strict';

    var sdk = WebEmarsysSdk || [],
        idField = window.emarsysCustomerFieldId,
        cacheKey = 'emarsys-webpush',
        customerData = storage.get('customer'),

        /**
         * @returns {Object}
         */
        initData = function () {
            return {
                is_subscribed: false,
                logged_in: false,
            };
        },

        /**
         * @returns {*}
         */
        getData = function () {
            var data = storage.get(cacheKey)();

            if ($.isEmptyObject(data)) {
                data = $.initNamespaceStorage('mage-cache-storage').localStorage.get(cacheKey);

                if ($.isEmptyObject(data)) {
                    data = initData();
                    saveData(data);
                }
            }

            return data;
        },

        /**
         * @param {Object} data
         */
        saveData = function (data) {
            storage.set(cacheKey, data);
        },

        /**
         * If a new customer, subscribe him.
         */
        subscribeCustomer = function () {
            if (typeof sdk.isSubscribed === 'undefined') return;

            sdk.isSubscribed().then(function (isSubscribed) {
                !isSubscribed && sdk.subscribe();
            });
        },

        /**
         * Logs in a customer to webpush when customer logs in to magento account.
         *
         * @param {String} loyaltyId
         */
        loginCustomer = function (loyaltyId) {
            if (!loyaltyId || !idField) return;

            var data = getData();

            if (!data.logged_in) {
                let info = {
                    fieldId: idField,
                    fieldValue: loyaltyId.toString(),
                };

                sdk.login(info).then(function (res) {
                    data.logged_in = true;
                    saveData(data);
                });
            }
        },

        /**
         * Logs out a customers that has magento customer logged-out.
         */
        logoutCustomer = function () {
            let status = getData();
            if (!status.logged_in && !status.is_subscribed) return;

            sdk.getLoggedInContact().then(function (result) {
                if (result && result.fieldValue != null) {
                    let data = initData();
                    saveData(data);
                    sdk.logout();
                }
            });
        },

        /**
         * Post subscription callback.
         */
        subscriptionCallback = function () {
            let data = getData(),
                customer = customerData();

            data.is_subscribed = true;
            saveData(data);

            if (!data.logged_in && !_.isUndefined(customer.loyalty_id)) {
                loginCustomer(customer.loyalty_id);
            }

            if (data.logged_in && _.isUndefined(customer.loyalty_id)) {
                logoutCustomer();
            }

            document.removeEventListener('scroll', debounceSubscription);
        },

        /**
         * subscribeCustomer wrapped in debounce method to avoid too many calls.
         */
        debounceSubscription = _.debounce(subscribeCustomer, 200),

        /**
         * Safari and Firefox require an event to happen on the
         * page prior to requesting the user permission.
         */
        registerEventListeners = function () {
            document.addEventListener('scroll', debounceSubscription);
        },

        /**
         * Register knockoutjs subscribers callbacks.
         */
        registerSubscribers = function () {
            customerData.subscribe(function (customer) {
                if (!getData()['logged_in'] && !_.isUndefined(customer.loyalty_id)) {
                    loginCustomer(customer.loyalty_id);
                }
            });
        };

    return {
        init: function () {
            sdk = arguments[0] || sdk;

            if (!sdk) return;

            registerEventListeners();

            setTimeout(function() {
                sdk.push(['onSubscribe', subscriptionCallback]);
                sdk.push(['onPermissionGranted', subscriptionCallback]);
                sdk.push(['onUnsubscribe', logoutCustomer]);
                sdk.push(['onPermissionDenied', logoutCustomer]);

                registerSubscribers();
            }, 300);
        },
    };
});
