<?php
/**
 * <AUTHOR> Internet (<EMAIL>)
 * @package Totaltools_Wishlist
 */
namespace Totaltools\Wishlist\Controller\Index;
use Magento\Framework\Controller\ResultFactory;

class Share extends \Magento\Wishlist\Controller\Index\Share
{
    /**
     * Prepare wishlist for share
     *
     * @return void|\Magento\Framework\View\Result\Page
     */
    public function execute()
    {
        if ($this->customerSession->authenticate()) {
            $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
            return $resultRedirect->setPath('*/*/');

            /** @var \Magento\Framework\View\Result\Page $resultPage */
            //$resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
            //return $resultPage;
        }
    }
}
