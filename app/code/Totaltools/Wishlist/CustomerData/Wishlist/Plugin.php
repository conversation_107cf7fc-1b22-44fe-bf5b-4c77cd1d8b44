<?php
/**
 * <AUTHOR> Internet (<EMAIL>)
 * @package Totaltools_Wishlist
 */

namespace Totaltools\Wishlist\CustomerData\Wishlist;

/**
 * layout processor checkout page plugin
 */
class Plugin
{
    /**
     * @param array $result
     * @return array
     */
    public function afterGetSectionData(\Magento\Wishlist\CustomerData\Wishlist $wishlistData, $result) {
        if (isset($result['items'])) {
            $result['count'] = sizeof($result['items']);
        }
        return $result;
    }
}
