type Query {

    getBox (
        identifier: String! @doc(description: "Box identifier")
    ): Box
    @resolver(class:"Totaltools\\BoxGraphQl\\Model\\Resolver\\GetBox")
    @doc(description:"Load box by identifier")
    @cache(cacheIdentity: "Totaltools\\BoxGraphQl\\Model\\Resolver\\Box\\Identity")

    getBoxesByGroup (
        identifier: String! @doc(description: "Box Group identifier")
    ): [Box]
    @resolver(class:"Totaltools\\BoxGraphQl\\Model\\Resolver\\GetBoxByGroup")
    @doc(description:"Get boxes collection by group identifier")

}

type Box {
    identifier: String @doc(description: "Box Identifier")
    title: String @doc(description: "Box Title")
    group_id: Int @doc(description: "Box Group ID")
    desktop_image: String @doc(description: "Box Desktop Image") @resolver(class: "Totaltools\\BoxGraphQl\\Model\\Resolver\\GetDesktopImage")
    mobile_image: String @doc(description: "Box Mobile Image") @resolver(class: "Totaltools\\BoxGraphQl\\Model\\Resolver\\GetMobileImage")
    alt_text: String @doc(description: "Box Alt Text")
    heading: String @doc(description: "Box Heading")
    content: ComplexTextValue @doc(description: "Box Content")
    link: String @doc(description: "Box Link")
    button_text: String @doc(description: "Box Button Text")
    layout: String @doc(description: "Box Layout")
    from_date: String @doc(description: "Box Enabled From Date")
    to_date: String @doc(description: "Box Enabled To Date")
    position: Int @doc(description: "Box Position")
    is_active: Int @doc(description: "Is Box Active")
    creation_time: String @doc(description: "Creation Time")
    update_time: String @doc(description: "Update Time")
}