<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label" sortOrder="0">
            <label>Total Tools</label>
        </tab>
        <section id="totaltools_pronto" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Pronto</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Pronto::config_pronto</resource>
            <group id="general" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>General</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Enabled</label>
                    <comment>Enables or disables extension.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="sandbox" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Sandbox</label>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
                <field id="debug" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Debug</label>
                    <source_model>Magento\Config\Model\Config\Source\Enabledisable</source_model>
                </field>
                <field id="enable_api_soh" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable API SOH Import</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="giftcard_warehouse_code" translate="label" type="text" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Gift Card Warehouse Code</label>
                </field>
                <field id="giftcard_store_code" translate="label" type="text" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Gift Card Store Code</label>
                </field>
            </group>
            <group id="authentication" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Authentication</label>
                <group id="avenue" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Avenue</label>
                    <field id="request_timeout" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Request Timeout</label>
                    </field>
                    <group id="sandbox" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Sandbox</label>
                        <field id="url" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>URL</label>
                        </field>
                        <field id="username" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>Username</label>
                            <comment>(email address)</comment>
                        </field>
                        <field id="password" translate="label" type="obscure" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>Password</label>
                            <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                        </field>
                    </group>
                    <group id="production" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Production</label>
                        <field id="url" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>URL</label>
                        </field>
                        <field id="username" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>Username</label>
                            <comment>(email address)</comment>
                        </field>
                        <field id="password" translate="label" type="obscure" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>Password</label>
                            <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                        </field>
                    </group>
                </group>
                <group id="connect" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Connect</label>
                    <field id="request_timeout" translate="label comment" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Request Timeout</label>
                        <comment>Takes maximum 22 seconds to finish a request.</comment>
                    </field>
                    <group id="sandbox" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Sandbox</label>
                        <field id="url" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>URL</label>
                        </field>
                        <field id="username" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>Username</label>
                        </field>
                        <field id="password" translate="label" type="obscure" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>Password</label>
                            <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                        </field>
                    </group>
                    <group id="production" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Production</label>
                        <field id="url" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>URL</label>
                        </field>
                        <field id="username" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>Username</label>
                        </field>
                        <field id="password" translate="label" type="obscure" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                            <label>Password</label>
                            <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                        </field>
                    </group>
                </group>
            </group>
            <group id="token" translate="label" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Token</label>
                <field id="expired_date" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Expired Date</label>
                    <frontend_model>Totaltools\Pronto\Block\System\Config\Form\Field\Disable</frontend_model>
                </field>
                <field id="token_key" translate="label" type="obscure" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Token Key</label>
                    <frontend_model>Totaltools\Pronto\Block\System\Config\Form\Field\Disable</frontend_model>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                </field>
                <field id="token_button" translate="button_label comment" type="button" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                    <button_label>Generated New Token</button_label>
                    <button_url>
                        <![CDATA[pronto/token/generate]]>
                    </button_url>
                    <frontend_model>Totaltools\Pronto\Block\System\Button\Token</frontend_model>
                </field>
            </group>
        </section>
    </system>
</config>
