<?xml version="1.0"?>
<!--
  ~ Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
  ~ See COPYING.txt for license details.
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Totaltools\Pronto\Api\Data\LoyaltyTierInterface"
                type="Totaltools\Pronto\Model\LoyaltyTier"/>

    <preference for="Totaltools\Pronto\Api\LoyaltyTierRepositoryInterface"
                type="Totaltools\Pronto\Model\LoyaltyTierRepository"/>

    <preference for="Totaltools\Pronto\Api\CustomerSyncRepositoryInterface"
                type="Totaltools\Pronto\Model\CustomerSyncRepository"/>

    <preference for="Totaltools\Pronto\Api\Data\CustomerSyncInterface"
                type="Totaltools\Pronto\Model\CustomerSync"/>

    <preference for="Totaltools\Pronto\Api\Data\CustomerSyncSearchResultsInterface"
                type="Magento\Framework\Api\SearchResults"/>

    <preference for="Totaltools\Pronto\Api\CustomerRequestManagerInterface"
                type="Totaltools\Pronto\Model\Request\CustomerRequestManager"/>

    <virtualType name="Totaltools\Pronto\Model\ResourceModel\CustomerSync\Grid\Collection"
                 type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
        <arguments>
            <argument name="mainTable" xsi:type="string">totaltools_customer_sync</argument>
            <argument name="resourceModel"
                      xsi:type="string">Totaltools\Pronto\Model\ResourceModel\CustomerSync\Collection</argument>
        </arguments>
    </virtualType>

    <type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
        <arguments>
            <argument name="collections" xsi:type="array">
                <item name="totaltools_pronto_customersync_grid_data_source"
                      xsi:type="string">Totaltools\Pronto\Model\ResourceModel\CustomerSync\Grid\Collection</item>
            </argument>
        </arguments>
    </type>
    <type name="Totaltools\Pronto\Model\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Totaltools\Pronto\Model\Logger">
        <arguments>
            <argument name="name" xsi:type="string">prontoLog</argument>
            <argument name="handlers"  xsi:type="array">
                <item name="system" xsi:type="object">Totaltools\Pronto\Model\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
     <type name="Magento\Framework\View\Element\UiComponent\Argument\Interpreter\ConfigurableObject">
        <arguments>
            <argument name="classWhitelist" xsi:type="array">
                <item name="0" xsi:type="string">Magento\Framework\Data\OptionSourceInterface</item>
                <item name="1" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProviderInterface</item>
                <item name="2" xsi:type="string">Magento\Framework\View\Element\UiComponent\ContextInterface</item>
            </argument>
        </arguments>
    </type>
</config>
