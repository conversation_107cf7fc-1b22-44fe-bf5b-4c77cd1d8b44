<?xml version="1.0"?>
<!--
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="checkout_onepage_controller_success_action">
        <observer name="unset_pronto_session" instance="Totaltools\Pronto\Observer\UnsetProntoSession" />
    </event>
    <event name="totaltools_send_verification_email">
        <observer name="totaltools_pronto_email_verification"
                  instance="Totaltools\Pronto\Observer\Registration\EmailVerification" />
    </event>
    <event name="customer_register_success">
        <observer name="customer_pronto_identification" instance="Totaltools\Pronto\Observer\Registration\ProntoCustomer" />
    </event>
    <event name="customer_pronto_identification">
        <observer name="customer_pronto_creation" instance="Totaltools\Pronto\Observer\Registration\ProntoCustomer" />
    </event>
    <event name="totaltools_send_verification_sms">
        <observer name="totaltools_pronto_sms_verification" instance="Totaltools\Pronto\Observer\Registration\SmsVerification" />
    </event>
</config>
