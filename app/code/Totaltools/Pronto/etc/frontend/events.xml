<?xml version="1.0"?>
<!--
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="customer_login">
        <observer name="update_pronto_info_after_customer_logged_in" instance="Totaltools\Pronto\Observer\UpdateProntoAfterCustomerLoggedIn" />
    </event>
</config>
