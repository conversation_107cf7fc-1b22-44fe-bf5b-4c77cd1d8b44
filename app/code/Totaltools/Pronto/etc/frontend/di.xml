<?xml version="1.0"?>
<!--
/**
 * Created by <PERSON>
 * Copyright (c) 2017 Balance Internet (http://www.balanceinternet.com.au/)
 * Date: 03/11/2017
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Block\Onepage">
        <arguments>
            <argument name="layoutProcessors" xsi:type="array">
                <item name="pronto_checkout_processors" xsi:type="object">Totaltools\Pronto\Block\Checkout\LayoutProcessor</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\CustomAttributeManagement\Block\Form\Renderer\Select">
        <plugin name="Totaltools_Pronto::SelectBlockPlugin" type="Totaltools\Pronto\Plugin\Block\Form\Renderer\SelectPlugin" />
    </type>
</config>
