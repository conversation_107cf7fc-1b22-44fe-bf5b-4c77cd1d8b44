<?xml version="1.0"?>
<!--
  ~ Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
  ~ See COPYING.txt for license details.
  -->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Cron:etc/crontab.xsd">
    <group id="totaltools_pronto_crongroup_general">
        <!--Run every 35 minutes-->
        <job name="update_insider_tiers" instance="Totaltools\Pronto\Cron\JobUpdateInsiderTiers" method="execute">
            <schedule>35 * * * *</schedule>
        </job>

        <!--Run every 23 PM-->
        <job name="create_token" instance="Totaltools\Pronto\Cron\GenerateToken" method="execute">
            <schedule>0 23 * * *</schedule>
        </job>

        <!--Run every 15 minutes-->
        <job name="update_customers" instance="Totaltools\Pronto\Cron\CustomerSync" method="execute">
            <schedule>*/15 * * * *</schedule>
        </job>

        <!--Run every 22 PM-->
        <job name="update_customers_account_code" instance="Totaltools\Pronto\Cron\CustomerSyncAccountCode" method="execute">
            <schedule>0 22 * * *</schedule>
        </job>
    </group>
</config>
