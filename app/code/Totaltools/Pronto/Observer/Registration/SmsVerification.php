<?php
/**
 * Totaltools
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Total-Infotech  Pvt., Ltd. All rights reserved.
 * @link      http://total-infotech.com/
 */

namespace Totaltools\Pronto\Observer\Registration;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Math\Random;
use Psr\Log\LoggerInterface;
use Totaltools\Customer\Helper\Data;

/**
 * Class SmsVerification
 *
 * @package Totaltools\Pronto\Observer\Registration
 */
class SmsVerification implements ObserverInterface
{
    /**
     * @var Sender
     */
    protected $verificationSender;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var Random
     */
    protected $mathRandom;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var Data
     */
    private $customerHelper;

    /**
     * SmsVerification constructor.
     *
     * @param Data                        $customerHelper
     * @param CustomerRepositoryInterface $customerRepository
     * @param Random                      $mathRandom
     * @param LoggerInterface             $logger
     */
    public function __construct(
        Data $customerHelper,
        CustomerRepositoryInterface $customerRepository,
        Random $mathRandom,
        LoggerInterface $logger
    ) {
        $this->customerHelper = $customerHelper;
        $this->customerRepository = $customerRepository;
        $this->mathRandom = $mathRandom;
        $this->logger = $logger;
    }

    /**
     * @param Observer $observer
     *
     * @event totaltools_pronto_sms_verification
     */
    public function execute(Observer $observer)
    {
        /***
         * @var CustomerInterface $customer
         */
        $customer = $observer->getData('customer');
        try {
            if ($mobileNo = $customer->getCustomAttribute('mobile_number')) {
                $this->customerHelper->sendVerificationCode($mobileNo->getValue());
            }
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }
}
