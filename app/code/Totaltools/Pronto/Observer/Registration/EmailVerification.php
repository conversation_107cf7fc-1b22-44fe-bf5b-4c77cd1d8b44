<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Observer\Registration;

use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Math\Random;
use Psr\Log\LoggerInterface;
use Totaltools\Loyalty\Model\Email\Verification\Sender;

/**
 * Class EmailVerification
 * @package Totaltools\Pronto\Observer\Registration
 */
class EmailVerification implements ObserverInterface
{
    /**
     * @var Sender
     */
    protected $verificationSender;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var Random
     */
    protected $mathRandom;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * EmailVerification constructor.
     *
     * @param Sender                      $verificationSender
     * @param CustomerRepositoryInterface $customerRepository
     * @param Random                      $mathRandom
     * @param LoggerInterface             $logger
     */
    public function __construct(
        Sender $verificationSender,
        CustomerRepositoryInterface $customerRepository,
        Random $mathRandom,
        LoggerInterface $logger
    )
    {
        $this->verificationSender = $verificationSender;
        $this->customerRepository = $customerRepository;
        $this->mathRandom = $mathRandom;
        $this->logger = $logger;
    }

    /**
     * @param Observer $observer
     *
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\State\InputMismatchException
     *
     * @event totaltools_pronto_email_verification
     */
    public function execute(Observer $observer)
    {
        /*** @var \Magento\Customer\Api\Data\CustomerInterface $customer */
        $customer = $observer->getData('customer');

        $customer->setCustomAttribute('email_verification_hash', $this->mathRandom->getUniqueHash());

        try {
            $this->verificationSender->sendVerificationEmail($customer);

            $customer->setCustomAttribute(
                'email_verification_status',
                \Totaltools\Customer\Model\Attribute\Source\EmailVerification::PENDING_VERIFICATION
            );
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        $customer->setCustomAttribute('email_verification_created_at', time());

        $this->customerRepository->save($customer);
    }
}