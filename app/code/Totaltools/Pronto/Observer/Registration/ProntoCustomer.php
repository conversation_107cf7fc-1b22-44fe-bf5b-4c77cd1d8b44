<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Observer\Registration;

use Magento\Customer\Api\Data\CustomerInterface as CustomerInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;

/**
 * Class ProntoCustomer
 * @package Totaltools\Pronto\Observer\Registration
 */
class ProntoCustomer implements ObserverInterface
{
    /**
     * @var \Totaltools\Pronto\Api\CustomerRequestManagerInterface
     */
    protected $customerProntoManager;

    /**
     * @var \Totaltools\Pronto\Helper\Config
     */
    protected $helper;

    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var \Magento\Customer\Model\Customer
     */
    protected $customer;

    /**
     * @var \Magento\Framework\Event\ManagerInterface
     */
    protected $eventManager;

    /**
     * Logging instance
     * @var \Totaltools\Pronto\Model\Logger
     */
    protected $logger;

    /**
     * ProntoCustomer constructor.
     * @param \Totaltools\Pronto\Api\CustomerRequestManagerInterface $customerRequestManager
     * @param \Totaltools\Pronto\Helper\Config $helper
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     * @param \Magento\Customer\Model\Customer $customer
     * @param \Magento\Framework\Event\ManagerInterface $eventManager
     * @param \Totaltools\Pronto\Model\Logger $logger
     */
    public function __construct(
        \Totaltools\Pronto\Api\CustomerRequestManagerInterface $customerRequestManager,
        \Totaltools\Pronto\Helper\Config $helper,
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository,
        \Magento\Customer\Model\Customer $customer,
        \Magento\Framework\Event\ManagerInterface $eventManager,
        \Totaltools\Pronto\Model\Logger $logger
    )
    {
        $this->customerProntoManager = $customerRequestManager;
        $this->helper = $helper;
        $this->customerRepository = $customerRepository;
        $this->customer = $customer;
        $this->eventManager = $eventManager;
        $this->logger = $logger;
    }

    /**
     * @param Observer $observer
     * @throws LocalizedException
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\State\InputMismatchException
     */
    public function execute(Observer $observer)
    {
        /** @var CustomerInterface $customerInterface */
        $customerInterface = $observer->getEvent()->getData('customer');
        $customerInterface->setCustomAttribute('is_loyal', 1);
        $this->customerRepository->save($customerInterface);
        $businessAccount = $customerInterface->getCustomAttribute('business_account');
        $businessAccountValue = $businessAccount ? $businessAccount->getValue() : '';
        $prontoCustomer = $this->customerProntoManager->getProntoCustomer($customerInterface);
        $accountCode = null;

        $this->logger->info('Pronto Customer Register Event:: Start Customer ID - ' . $customerInterface->getId());

        if (!$this->_isCustomerExistByEmail($prontoCustomer) && !$this->_isCustomerExistByMobile($prontoCustomer)) {
            $this->logger->info('Pronto Customer Register Event:: Customer ID - ' . $customerInterface->getId() .
                'Both Email and Phone NOT Exist in Pronto');
            $customerModel = $this->customerProntoManager->getCustomerModel($customerInterface->getId());
            $prontoCustomer = $this->customerProntoManager->createMember($customerModel, 'Both');
            if (!empty($prontoCustomer)) {
                $accountCode = $prontoCustomer['CustomerCreated']['CustomerAccountcode'];
                $prontoCustomer = $prontoCustomer['CustomerCreated']['LoyaltyCustomer'];
                $customerInterface
                    ->setCustomAttribute('account_code', $accountCode)
                    ->setCustomAttribute('loyalty_id', $prontoCustomer['LoyaltyID'])
                    ->setCustomAttribute('unverified_loyalty_id', null)
                    ->setCustomAttribute('referral_code', $prontoCustomer['ReferralCode'])
                    ->setCustomAttribute(
                        'email_verification_status',
                        EmailVerification::VERIFIED
                    );
                $this->logger->info('Pronto Customer Register Event:: Customer ID - ' . $customerInterface->getId() .
                    'Pronto Customer Created Account ID' . $accountCode . 'loyalty id ' . $prontoCustomer['LoyaltyID']);
                $this->customerRepository->save($customerInterface);
                return $this;
            }
        }

        if (array_key_exists('LoyaltyID', $prontoCustomer)) {
            $this->logger->info('Pronto Customer Register Event:: Customer ID - ' . $customerInterface->getId() .
                'get Account Code by Loyalty Id ' . $prontoCustomer['LoyaltyID']);
            $customerInterface->setCustomAttribute('unverified_loyalty_id', $prontoCustomer['LoyaltyID']);
            $customerInterface->setCustomAttribute('loyalty_id', $prontoCustomer['LoyaltyID']);
            $customerInterface->setCustomAttribute('referral_code', $prontoCustomer['ReferralCode']);
            
            if(!$businessAccountValue) {
                $customerInterface->setCustomAttribute('abn', $prontoCustomer['ABN']);
                $customerInterface->setCustomAttribute('business_account', $prontoCustomer['BusinessAccount']=='Y' ? 1 : 0);
                $customerInterface->setCustomAttribute('customer_company', $prontoCustomer['Company'] ?: null);
            }
            $accountCode = $this->customerProntoManager->getCustomerAccountCode($prontoCustomer['LoyaltyID']);
            if ($accountCode) {
                $this->logger->info('Pronto Customer Register Event:: Customer ID - ' . $customerInterface->getId() .
                    'get Account ID by Loyalty Id ' . $prontoCustomer['LoyaltyID'] . 'Account Code found.' . $accountCode);
                $customerInterface->setCustomAttribute('account_code', $accountCode);
                if ($this->_isCustomerExistByEmail($prontoCustomer)) {
                    $customerInterface->setCustomAttribute(
                        'email_verification_status',
                        EmailVerification::PENDING_VERIFICATION
                    );
                    $this->eventManager->dispatch(
                        'totaltools_send_verification_email',
                        ['customer' => $customerInterface]
                    );
                } else {
                    $customerInterface->setCustomAttribute(
                        'email_verification_status',
                        EmailVerification::MANUAL_VERIFICATION_REQUIRED
                    );
                    $this->eventManager->dispatch(
                        'totaltools_send_verification_sms',
                        ['customer' => $customerInterface]
                    );
                }
                $customerInterface->setConfirmation(NULL);
                $this->customerRepository->save($customerInterface);
            }
        }
        if ($accountCode == null) {
            $this->logger->info('Pronto Customer Register Event:: Customer ID - ' . $customerInterface->getId() .
                'Account Code Did not find. and going to createMember');
            $customerModel = $this->customerProntoManager->getCustomerModel($customerInterface->getId());
            $prontoCustomer = $this->customerProntoManager->createMember($customerModel, 'Both');
            $getProntoCustomer = $this->customerProntoManager->getProntoCustomer($customerInterface);
            if (!empty($prontoCustomer)) {
                $accountCode = $prontoCustomer['CustomerCreated']['CustomerAccountcode'];
                $prontoCustomer = $prontoCustomer['CustomerCreated']['LoyaltyCustomer'];
                $customerInterface
                    ->setCustomAttribute('account_code', $accountCode)
                    ->setCustomAttribute('loyalty_id', $prontoCustomer['LoyaltyID'])
                    ->setCustomAttribute('referral_code', $prontoCustomer['ReferralCode'])
                    ->setCustomAttribute('unverified_loyalty_id', $prontoCustomer['LoyaltyID']);
                if(!$businessAccountValue) {
                    $customerInterface->setCustomAttribute('abn', $prontoCustomer['ABN'])
                    ->setCustomAttribute('business_account', $prontoCustomer['BusinessAccount'] =='Y' ? 1 : 0)
                    ->setCustomAttribute('customer_company', $prontoCustomer['Company'] ?: null);
                }
                $this->logger->info('Pronto Customer Register Event:: Customer ID - ' . $customerInterface->getId() .
                    'Pronto Customer Created Account ID' . $accountCode . 'loyalty id ' . $prontoCustomer['LoyaltyID']);
                if ($this->_isCustomerExistByEmail($getProntoCustomer)) {
                    $customerInterface->setCustomAttribute(
                        'email_verification_status',
                        EmailVerification::PENDING_VERIFICATION
                    );
                    $this->eventManager->dispatch(
                        'totaltools_send_verification_email',
                        ['customer' => $customerInterface]
                    );
                } else {
                    $customerInterface->setCustomAttribute(
                        'email_verification_status',
                        EmailVerification::MANUAL_VERIFICATION_REQUIRED
                    );
                    $this->eventManager->dispatch(
                        'totaltools_send_verification_sms',
                        ['customer' => $customerInterface]
                    );
                }
                $this->customerRepository->save($customerInterface);
                return $this;
            }
        }


        return $this;
    }

    /**
     * Check whether customer is found by email.
     *
     * @param array $data
     *
     * @return bool
     */
    private function _isCustomerExistByEmail(array $data)
    {
        if (isset($data['emailExist'])) {
            return $data['emailExist'];
        }
        return false;
    }

    /**
     * Check whether customer is found by mobile phone.
     *
     * @param array $data
     *
     * @return bool
     */
    private function _isCustomerExistByMobile(array $data)
    {
        if (isset($data['mobileExist'])) {
            return $data['mobileExist'];
        }
        return false;
    }
}