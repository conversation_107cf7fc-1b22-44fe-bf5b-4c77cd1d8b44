<?php
/**
 * <AUTHOR> Nguyen (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Pronto\Observer;

use Magento\Customer\Model\Session as CustomerSession;
use Totaltools\Customer\Model\ProntoSyncManager;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Totaltools\Pronto\Helper\Config;
use Totaltools\Customer\Helper\Data as CustomerHelper;

/**
 * Class UpdateProntoAfterPlaceOrder
 */
class UnsetProntoSession implements ObserverInterface
{

    /**
     * Customer session
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var Config
     */
    protected $helper;

    /**
     * Logger
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var ProntoSyncManager
     */
    private $prontoSyncManager;

    /**
     * UnsetProntoSession constructor
     *
     * @param CustomerSession $customerSession
     * @param Config $helper
     * @param ProntoSyncManager $prontoSyncManager
     */
    public function __construct(
        CustomerSession $customerSession,
        Config $helper,
        ProntoSyncManager $prontoSyncManager
    ) {
        $this->customerSession = $customerSession;
        $this->helper           = $helper;
        $this->prontoSyncManager = $prontoSyncManager;
    }

    /**
     * @param Observer $observer
     *
     * @return void
     * @event checkout_onepage_controller_success_action
     */
    public function execute(Observer $observer)
    {
        $status = $this->helper->isEnabled();
        if ($status) {
            $this->customerSession->unsCurrentIsLoyal();
            $this->customerSession->setForcedSyncLoyaltyData(1);
            $this->customerSession->setData(CustomerHelper::SYNC_TIMESTAMP, null);
            $this->customerSession->setSyncTimestamp();
            $this->customerSession->unsetData(CustomerHelper::SYNC_TIMESTAMP);
        }
    }

}
