<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Pronto\Observer;

use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Sales\Model\Order;

/**
 * Class UpdateProntoAfterPlaceOrder
 */
class UpdateProntoAfterPlaceOrder implements ObserverInterface
{

    /**
     * Set Pronto integration detail on new order
     *
     * @param Observer $observer
     * @return void
     */
    public function execute(Observer $observer)
    {
        /** @var Order $order */
        /*$order = $observer->getEvent()->getData('order');
        $order
            ->setData('pronto_order_number', '')
            ->setData('pronto_status', 'pending')
            ->setData('pronto_processing', '0')
            ->setData('pronto_account_code', '')
            ->save();*/
    }

}
