<?php
/**
 * <AUTHOR> Nguyen (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Pronto\Observer;

use Magento\Customer\Model\Customer;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Totaltools\Pronto\Api\CustomerRequestManagerInterface;
use Totaltools\Pronto\Helper\Config;

/**
 * Class UpdateProntoAfterCustomerLoggedIn
 */
class UpdateProntoAfterCustomerLoggedIn implements ObserverInterface
{

    /**
     * Pronto customer
     *
     * @var CustomerRequestManagerInterface
     */
    protected $prontoRequestManager;

    /**
     * @var Config
     */
    protected $helper;

    /**
     * UpdateProntoAfterCustomerLoggedIn constructor.
     * @param CustomerRequestManagerInterface $prontoRequestManager
     * @param Config $helper
     */
    public function __construct(
        CustomerRequestManagerInterface $prontoRequestManager,
        Config $helper
    ){
        $this->prontoRequestManager = $prontoRequestManager;
        $this->helper = $helper;
    }

    /**
     * {@inheritdoc}
     */
    public function execute(Observer $observer)
    {
        $status = $this->helper->isEnabled();
        if($status) {
            /** @var Customer $customer */
            $customer = $observer->getEvent()->getData('customer');
            $customer->load($customer->getId());
            $this->prontoRequestManager->updateCustomerInfo($customer, true);
        }
    }
}
