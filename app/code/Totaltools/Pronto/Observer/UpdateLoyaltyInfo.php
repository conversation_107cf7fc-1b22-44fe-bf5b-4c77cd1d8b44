<?php
/**
 * @category Totaltools_Pronto
 */
namespace Totaltools\Pronto\Observer;

use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Model\CustomerFactory;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Exception\LocalizedException;
use Totaltools\Pronto\Api\CustomerRequestManagerInterface;
use Totaltools\Pronto\Helper\Config;
use Psr\Log\LoggerInterface;

/**
 * Update loyalty information when a customer creates a new account
 */
class UpdateLoyaltyInfo implements ObserverInterface
{

    /**
     * Customer factory
     * @var CustomerFactory
     */
    private $customerFactory;

    /**
     * Pronto customer
     * @var CustomerRequestManagerInterface
     */
    private $prontoRequestManager;

    /**
     * @var Config
     */
    protected $helper;

    /**
     * Logger
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * UpdateLoyaltyInfo constructor.
     * @param CustomerFactory $customerFactory
     * @param CustomerRequestManagerInterface $prontoRequestManager
     * @param Config $helper
     * @param LoggerInterface $logger
     */
    public function __construct(
        CustomerFactory $customerFactory,
        CustomerRequestManagerInterface $prontoRequestManager,
        Config $helper,
        LoggerInterface $logger
    )
    {
        $this->customerFactory = $customerFactory;
        $this->prontoRequestManager = $prontoRequestManager;
        $this->helper = $helper;
        $this->logger = $logger;
    }

    /**
     * Update new customer's loyalty information
     *
     * @param Observer $observer
     *
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     * @event customer_register_success
     */
    public function execute(Observer $observer)
    {
        $status = $this->helper->isEnabled();
        if($status) {
            /** @var CustomerInterface $customerInterface */
            $customerInterface = $observer->getData('customer');
            if ($customerInterface && $customerInterface->getId()) {
                $customer = $this->customerFactory
                    ->create()
                    ->load($customerInterface->getId());
                try {
                    $this->prontoRequestManager->updateCustomerInfo($customer, true);
                } catch (LocalizedException $exception) {
                    $this->logger->error($exception->getLogMessage());
                }
            }
        }
    }

}