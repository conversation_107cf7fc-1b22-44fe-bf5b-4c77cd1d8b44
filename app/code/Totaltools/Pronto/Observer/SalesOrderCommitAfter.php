<?php
/**
 * Created by <PERSON>
 * Copyright (c) 2017 Balance Internet (http://www.balanceinternet.com.au/)
 * Date: 06/11/2017
 */

namespace Totaltools\Pronto\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Model\CustomerFactory;
use Totaltools\Pronto\Helper\Config;

class SalesOrderCommitAfter implements ObserverInterface
{


    /**
     * Checkout Session.
     *
     * @param \Magento\Checkout\Model\Session $checkoutSession
     *
     * @codeCoverageIgnore
     */
    protected $_checkoutSession;
    protected $_customer;

    /**
     * @var Config
     */
    protected $helper;

    /**
     * SalesOrderCommitAfter constructor.
     *
     * @param CheckoutSession $checkoutSession
     * @param CustomerFactory $customer
     */
    public function __construct(
        CheckoutSession $checkoutSession,
        CustomerFactory $customerFactory,
        Config $helper
    ){
        $this->_checkoutSession = $checkoutSession;
        $this->_customer        = $customerFactory->create();
        $this->helper           = $helper;

    }

    /**
     * Fix problem when save is_loyal customer attribute
     *
     * @event sales_order_save_commit_after
     * @param \Magento\Framework\Event\Observer $observer
     * @return $this
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $order = $observer->getEvent()->getOrder();
        $status = $this->helper->isEnabled();
        if($status) {
            if (!$order || !$order->getId() || !$order->getCustomerId())
                return $this;
            if ($this->_checkoutSession->getCurrentIsLoyal()) {
                $customer = $this->_customer->load($order->getCustomerId());
                $customer->setIsLoyal(1);
                $customer->getResource()->saveAttribute($customer, 'is_loyal');
            }
        }
        return $this;

    }
}