<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Controller\Adminhtml\CustomerSync;

use \Totaltools\Pronto\Api\Data\CustomerSyncInterface;
use \Totaltools\Pronto\Api\CustomerSyncRepositoryInterface;
use \Magento\Ui\Component\MassAction\Filter;
use \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory;
use Magento\Framework\Controller\ResultFactory;
use Totaltools\Pronto\Api\Data\CustomerSyncInterfaceFactory;

/**
 * Class Index
 * @package Totaltools\Pronto\Controller\Adminhtml
 */
class PushCustomers  extends \Magento\Backend\App\Action
{
    /**
     * Authorization level of a basic admin session
     *
     * @see _isAllowed()
     */
    const ADMIN_RESOURCE = 'Totaltools_Pronto::push';

    /**
     * @var CustomerSyncInterface
     */
    protected $customerInterface;

    /**
     * @var CustomerSyncRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var Filter
     */
    protected $filter;

    /**
     * @var CollectionFactory
     */
    protected $collectionFactory;

    /**
     * pushCustomers constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param CustomerSyncInterface $customerSyncInterface
     * @param CustomerSyncRepositoryInterface $customerRepository
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        CustomerSyncInterface $customerSyncInterface,
        CustomerSyncRepositoryInterface $customerRepository,
        Filter $filter,
        CollectionFactory $collectionFactory
    ) {
        $this->customerInterface  = $customerSyncInterface;
        $this->customerRepository = $customerRepository;
        $this->filter = $filter;
        $this->collectionFactory = $collectionFactory;
        parent::__construct($context);
    }

    /**
     * @return \Magento\Backend\Model\View\Result\Redirect|\Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        $collection = $this->filter->getCollection($this->collectionFactory->create());
        $customersPushed = 0;
        foreach ($collection->getAllIds() as $customerId) {
            $customer = $this->customerInterface;
            $customer->setEntityId(null);
            $customer->setCustomerId($customerId);
            $customer->setStatus('Pending');
            $customer->setMessage('Customer Ready to push');
            $this->customerRepository->save($this->customerInterface);
            $customersPushed++;
        }

        if ($customersPushed) {
            $this->messageManager->addSuccess(__('A total of %1 record(s) were added to pronto sync queue', $customersPushed));
        }
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setPath('customer/index');

        return $resultRedirect;
    }
}
