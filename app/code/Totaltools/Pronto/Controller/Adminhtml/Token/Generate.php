<?php

namespace Totaltools\Pronto\Controller\Adminhtml\Token;

use Magento\Framework\App\Config\ScopeConfigInterface;

/**
 * Class Generate
 * @package Totaltools\Pronto\Controller\Adminhtml\Token
 */
class Generate extends \Magento\Backend\App\Action
{
    /**
     * Authorization level of a basic admin session.
     *
     * @see _isAllowed()
     */
    const ADMIN_RESOURCE = 'Totaltools_Pronto::config_pronto';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var \Totaltools\Pronto\Model\Api\GenerateToken
     */
    private $generateToken;

    /**
     * Generate constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param ScopeConfigInterface $scopeConfig
     * @param \Totaltools\Pronto\Model\Api\GenerateToken $generateToken
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        ScopeConfigInterface $scopeConfig,
        \Totaltools\Pronto\Model\Api\GenerateToken $generateToken
    ) {
        parent::__construct($context);
        $this->scopeConfig = $scopeConfig;
        $this->generateToken = $generateToken;
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface|void|\Zend_Db_Statement_Interface
     */
    public function execute()
    {
        try {
            $this->generateToken->generate();
           $this->messageManager->addSuccessMessage('New token has been generated.');
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }
        $redirectPage = $this->resultRedirectFactory->create();
        $referUrl = $this->_redirect->getRefererUrl();

        return $redirectPage->setUrl($referUrl);
    }
}
