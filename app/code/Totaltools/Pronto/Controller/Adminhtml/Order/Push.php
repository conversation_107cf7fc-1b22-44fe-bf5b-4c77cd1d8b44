<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2021 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Controller\Adminhtml\Order;

use Exception;
use Magento\Framework\App\Area as AppArea;

class Push extends \Magento\Backend\App\Action
{

    const ADMIN_ACTION = 'Totaltools_Pronto::push';

    /**
     * @var \Magento\Store\Model\App\Emulation
     */
    protected $_appEmulation;
    /**
     * @var \Magento\Sales\Api\Data\OrderInterface
     */
    protected $_orderInterface;

    /**
     * Push constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param \Magento\Sales\Api\Data\OrderInterface $orderInterface
     * @param \Magento\Store\Model\App\Emulation $emulation
     */
    public function __construct (
        \Magento\Backend\App\Action\Context $context,
        \Magento\Sales\Api\Data\OrderInterface $orderInterface,
        \Magento\Store\Model\App\Emulation $emulation
    ) {
        $this->_orderInterface = $orderInterface;
        $this->_appEmulation = $emulation;

        parent::__construct($context);
    }

    /**
     * {@inheritdoc}
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed(self::ADMIN_ACTION);
    }

    /**
     * Delete action
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        $orderId = $this->getRequest()->getParam('order_id', null);

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();

        if (empty($orderId)) {
            $this->messageManager->addError(__('The order to be push could not be found.'));

            return $resultRedirect->setPath('sales/order/index/');
        }

        $order = $this->_orderInterface->load($orderId);

        if (!$order) {
            $this->messageManager->addError(__('The order to be push could not be found.'));

            return $resultRedirect->setPath('sales/order/view/', ['order_id' => $orderId]);
        }

        try {
            $this->_appEmulation->startEnvironmentEmulation(
                $order->getStoreId(),
                AppArea::AREA_ADMINHTML
            );
            $orderStatusOld = $order->getMailchimpFlag();
            $order->setMailchimpFlag(6)->save();
            $order->setMailchimpFlag($orderStatusOld)->save();

            $comment = __('Order timestamp updated to push to Pronto from Backend.');
            $order->addStatusHistoryComment($comment)
                ->setIsVisibleOnFront(false)
                ->save();
            $this->messageManager->addSuccessMessage('The order timestamp updated to push to Pronto.');
        }
        catch (Exception $e) {
            // display error message
            $this->messageManager->addError($e->getMessage());
        }
        finally {
            $this->_appEmulation->stopEnvironmentEmulation();
        }

        return $resultRedirect->setPath('sales/order/view/', ['order_id' => $orderId]);
    }
}
