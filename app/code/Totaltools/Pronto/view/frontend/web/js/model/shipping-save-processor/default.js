/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
/*global define,alert*/
define(
    [
        'jquery',
        'ko',
        'Magento_Checkout/js/model/quote',
        'Magento_Checkout/js/model/resource-url-manager',
        'mage/storage',
        'Magento_Checkout/js/model/payment-service',
        'Magento_Checkout/js/model/payment/method-converter',
        'Magento_Checkout/js/model/error-processor',
        'Magento_Checkout/js/model/full-screen-loader',
        'Magento_Checkout/js/action/select-billing-address'
    ],
    function ($,
              ko,
              quote,
              resourceUrlManager,
              storage,
              paymentService,
              methodConverter,
              errorProcessor,
              fullScreenLoader,
              selectBillingAddressAction) {
        'use strict';

        return {
            saveShippingInformation: function () {
                var payload;
                if (quote.isVirtual() === true) {
                    return;
                }
                if (!quote.billingAddress()) {
                    selectBillingAddressAction(quote.shippingAddress());
                }
                var storeId = 0;
                if (quote.extension_attributes && quote.extension_attributes.storelocator_id) {
                    storeId = quote.extension_attributes.storelocator_id;
                }
                payload = {
                    addressInformation: {
                        shipping_address: quote.shippingAddress(),
                        billing_address: quote.billingAddress(),
                        shipping_method_code: quote.shippingMethod() ? quote.shippingMethod().method_code : null,
                        shipping_carrier_code: quote.shippingMethod() ? quote.shippingMethod().carrier_code : null,
                        custom_attributes: quote.shippingAddress().customAttributes, //Temando specific code. Taken from Shipping save processor in Temando
                        extension_attributes: {
                            is_loyal: $('[name="is_loyal"]').is(':checked'),
                            shippit_authority_to_leave: ($('#shippit-options [name="shippit_authority_to_leave"]').is(':checked') ? 1 : 0),
                            shippit_delivery_instructions: $('#shippit-options [name="shippit_delivery_instructions"]').val(),
                            storelocator_id : storeId
                        }
                    }
                };

                fullScreenLoader.startLoader();

                return storage.post(
                    resourceUrlManager.getUrlForSetShippingInformation(quote),
                    JSON.stringify(payload)
                ).done(
                    function (response) {
                        quote.setTotals(response.totals);
                        paymentService.setPaymentMethods(methodConverter(response.payment_methods));
                        fullScreenLoader.stopLoader();
                    }
                ).fail(
                    function (response) {
                        errorProcessor.process(response);
                        fullScreenLoader.stopLoader();
                    }
                );
            }
        };
    }
);
