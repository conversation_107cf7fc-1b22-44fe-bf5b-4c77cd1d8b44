<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
/**
 * Create account form template
 */
/* @var $block \Magento\CustomAttributeManagement\Block\Form\Renderer\Select */
?>
<?php
$fieldCssClass = 'field field-' . $block->getHtmlId();
$fieldCssClass .= $block->isRequired() ? ' required' : '';
$attribute = $block->getAttributeObject();
$entity = $block->getEntity();
$value =  $block->getValue();
if ($block->getData('current_area') === 'customer_account_create') {
    $value = $block->getData($block->getFieldName());
}

if ($attribute->getAttributeCode() == 'is_loyal' && $block->getIsB2bCustomer()) {
    return;
}
?>
<?php if ($attribute->getAttributeCode() == 'is_loyal' && ($entity instanceof Magento\Customer\Model\Customer)) : ?>
<?php
    $checked = false;
    if ($entity->getIsLoyal()) {
        $checked = true;
    }
?>
<div class="<?php /* @escapeNotVerified */ echo $fieldCssClass; ?> choice" <?php if ($checked) { echo "style='display:none;'";}?>>
    <div class="control">

        <input type="hidden" name="<?php echo $block->getFieldName(); ?>" value="0">
        <input type="checkbox" name="<?php echo $block->getFieldName(); ?>" value="1"<?php if ($checked) { echo ' checked="checked"'; } ?> title="<?php echo $block->getLabel(); ?>" id="<?php echo $block->getHtmlId(); ?>" class="checkbox checkbox_for_select">
        <label class="label" for="<?php echo $block->getHtmlId()?>"><span><?php /* @escapeNotVerified */ echo $block->getLabel() ?></span></label>
        <?php if ($_message = $block->getAdditionalDescription()) : ?>
            <div class="note"><?php /* @escapeNotVerified */ echo $_message; ?></div>
        <?php endif; ?>
    </div>
</div>

<?php else : ?>

<div class="<?php /* @escapeNotVerified */ echo $fieldCssClass; ?>">
    <label class="label" for="<?php echo $block->getHtmlId()?>"><span><?php /* @escapeNotVerified */ echo $block->getLabel() ?></span></label>
    <div class="control">
        <select id="<?php echo $block->getHtmlId()?>" name="<?php /* @escapeNotVerified */ echo $block->getFieldName()?>"<?php if ($block->getHtmlClass()):?> class="select <?php echo $block->getHtmlClass();?>"<?php endif;?>>
        <?php foreach ($block->getOptions() as $option):?>
        <option value="<?php /* @escapeNotVerified */ echo $option['value']?>"<?php if ($option['value'] == $value):?> selected="selected"<?php endif;?>><?php echo $block->escapeHtml($option['label'])?></option>
        <?php endforeach;?>
        </select>
        <?php if ($_message = $block->getAdditionalDescription()) : ?>
        <div class="note"><?php /* @escapeNotVerified */ echo $_message; ?></div>
        <?php endif; ?>
    </div>
</div>

<?php endif; ?>
