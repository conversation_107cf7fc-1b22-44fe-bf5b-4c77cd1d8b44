<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="context" xsi:type="configurableObject">
		<argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\Context</argument>
		<argument name="namespace" xsi:type="string">totaltools_pronto_customersync_listing</argument>
	</argument>
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">totaltools_pronto_customersync_listing.totaltools_pronto_customersync_grid_data_source</item>
			<item name="deps" xsi:type="string">totaltools_pronto_customersync_listing.totaltools_pronto_customersync_grid_data_source</item>
		</item>
		<item name="spinner" xsi:type="string">totaltools_pronto_customersync_columns</item>
	</argument>
	<dataSource name="totaltools_pronto_customersync_grid_data_source">
		<argument name="dataProvider" xsi:type="configurableObject">
			<argument name="class" xsi:type="string">Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider</argument>
			<argument name="name" xsi:type="string">totaltools_pronto_customersync_grid_data_source</argument>
			<argument name="primaryFieldName" xsi:type="string">customersync_id</argument>
			<argument name="requestFieldName" xsi:type="string">id</argument>
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
					<item name="update_url" path="mui/index/render" xsi:type="url"/>
					<item name="storageConfig" xsi:type="array">
						<item name="indexField" xsi:type="string">entity_id</item>
					</item>
				</item>
			</argument>
		</argument>
	</dataSource>
	<listingToolbar name="listing_top">
		<argument name="data" xsi:type="array">
			<item name="config" xsi:type="array">
				<item name="sticky" xsi:type="boolean">false</item>
			</item>
		</argument>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="totaltools_pronto_customersync_columns">
		<argument name="data" xsi:type="array"/>
		<column name="entity_id">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="filter" xsi:type="string">text</item>
					<item name="label" translate="true" xsi:type="string">Entity Id</item>
				</item>
			</argument>
		</column>
		<column name="customer_id">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="filter" xsi:type="string">text</item>
					<item name="label" translate="true" xsi:type="string">Customer Id</item>
				</item>
			</argument>
		</column>
		<column name="status">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="filter" xsi:type="string">text</item>
					<item name="label" translate="true" xsi:type="string">Status</item>
				</item>
			</argument>
		</column>
		<column name="message">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="filter" xsi:type="string">text</item>
					<item name="label" translate="true" xsi:type="string">Message</item>
				</item>
			</argument>
		</column>
	</columns>
</listing>
