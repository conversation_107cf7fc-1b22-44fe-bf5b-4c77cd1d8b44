<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Setup;

use Magento\Framework\App\Config\ScopeConfigInterface as ScopeConfig;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Setup\CustomerSetupFactory;
use Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface;

/**
 * Upgrade Data script
 * @codeCoverageIgnore
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * EAV setup factory
     *
     * @var CustomerSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @var \Totaltools\Pronto\Api\CustomerRequestManagerInterface
     */
    protected $prontoRequestManager;

    /**
     * @var \Magento\Framework\App\Config\ConfigResource\ConfigInterface
     */
    private $_config;

    /**
     * @var string $_setupVersion Current setup version.
     */
    private $_setupVersion;

    /**
     * UpgradeData constructor.
     * @param CustomerSetupFactory $eavSetupFactory
     * @param \Magento\Framework\Logger\Monolog $logger
     * @param \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager
     * @param \Magento\Framework\App\Config\ConfigResource\ConfigInterface $config
     */
    public function __construct(
        CustomerSetupFactory $eavSetupFactory,
        \Magento\Framework\Logger\Monolog $logger,
        \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager,
        \Magento\Framework\App\Config\ConfigResource\ConfigInterface $config
    )
    {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->logger = $logger;
        $this->prontoRequestManager = $prontoRequestManager;
        $this->_config = $config;
    }

    /**
     * {@inheritdoc}
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        // Share current setup version with all class functionality.
        $this->_setupVersion = $context->getVersion();

        if (version_compare($this->_setupVersion, '0.1.1') < 0) {
            // add customer attributes
            $params = array(
                'type'                         => 'int',
                'label'                        => 'Insider',
                'input'                        => 'select',
                'source'                       => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                'default'                      => 0,
                'visible'                      => 1,
                'required'                     => 0,
                'system'                       => 0,
                'user_defined'                 => 1,
                'sort_order'                   => 120,
                'position'                     => 120,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'is_loyal', $params, [
                'adminhtml_customer',
                'adminhtml_checkout',
                'customer_account_create',
                'checkout_onepage_register',
            ], true);
        }

        if (version_compare($this->_setupVersion, '0.1.2') < 0) {
            // add customer attributes
            $params = array(
                'type'                         => 'varchar',
                'label'                        => 'Account Code',
                'input'                        => 'text',
                'source'                       => '',
                'default'                      => 0,
                'visible'                      => 1,
                'required'                     => 0,
                'system'                       => 0,
                'user_defined'                 => 1,
                'sort_order'                   => 121,
                'position'                     => 121,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'account_code', $params, ['adminhtml_customer'], true);

        }

        if (version_compare($this->_setupVersion, '0.1.4') < 0) {
            // add customer attributes
            $params = array(
                'type'                         => 'int',
                'label'                        => 'Insider Level',
                'input'                        => 'select',
                'source'                       => '\Totaltools\Pronto\Model\Source\InsiderLevel',
                'default'                      => '',
                'visible'                      => 1,
                'required'                     => 0,
                'system'                       => 0,
                'user_defined'                 => 1,
                'sort_order'                   => 125,
                'position'                     => 125,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'loyalty_level', $params, ['adminhtml_customer'], true);

            $params = array(
                'type'                         => 'varchar',
                'label'                        => 'Insider Points',
                'input'                        => 'text',
                'source'                       => '',
                'default'                      => '',
                'visible'                      => 1,
                'required'                     => 0,
                'system'                       => 0,
                'user_defined'                 => 1,
                'sort_order'                   => 130,
                'position'                     => 130,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'loyalty_points', $params, ['adminhtml_customer'], true);

            $params = array(
                'type'                         => 'varchar',
                'label'                        => 'Insider Points Last Updated',
                'input'                        => 'text',
                'source'                       => '',
                'default'                      => '',
                'visible'                      => 1,
                'required'                     => 0,
                'system'                       => 0,
                'user_defined'                 => 1,
                'sort_order'                   => 135,
                'position'                     => 135,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'loyalty_last_updated', $params, ['adminhtml_customer'], true);

        }

        if (version_compare($this->_setupVersion, '0.1.5') < 0) {
            $this->migrateAuthConfig($setup);
        }

        if (version_compare($this->_setupVersion, '0.1.6') < 0) {
            //$this->updateIsLoyalAttribute($setup);
            // add customer attributes
            $params = array(
                'type'                         => 'int',
                'label'                        => 'Become a Total Tools Insider',
                'input'                        => 'boolean',
                'source'                       => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                'global'                       => ScopedAttributeInterface::SCOPE_GLOBAL,
                'default'                      => 0,
                'visible'                      => true,
                'required'                     => false,
                'system'                       => false,
                'user_defined'                 => true,
                'sort_order'                   => 120,
                'position'                     => 120,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'is_loyal', $params, [
                'adminhtml_customer',
                'adminhtml_checkout',
                'customer_account_create',
                'checkout_onepage_register',
            ], true);
        }

        if (version_compare($this->_setupVersion, '0.1.9') < 0) {
            // add customer attributes
            $params = array(
                'type'                         => 'varchar',
                'label'                        => 'Point Expiry',
                'input'                        => 'text',
                'source'                       => '',
                'default'                      => '',
                'visible'                      => 1,
                'required'                     => 0,
                'system'                       => 0,
                'user_defined'                 => 1,
                'sort_order'                   => 140,
                'position'                     => 140,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'point_expiry', $params, ['adminhtml_customer'], true);

        }

        if (version_compare($this->_setupVersion, '0.1.10') < 0) {
            // add customer attributes
            $params = array(
                'type'                         => 'int',
                'label'                        => 'Become a Total Tools Insider',
                'input'                        => 'select',
                'source'                       => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                'default'                      => 0,
                'visible'                      => 1,
                'required'                     => 0,
                'system'                       => 0,
                'user_defined'                 => 1,
                'sort_order'                   => 120,
                'position'                     => 120,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'is_loyal', $params, [
                'adminhtml_customer',
                'adminhtml_checkout',
                'customer_account_create',
                'checkout_onepage_register',
            ], true);
        }

        if (version_compare($this->_setupVersion, '0.1.11') < 0) {
            // add customer attributes
            $params = array(
                'type'                         => 'int',
                'label'                        => 'Become a Total Tools Insider',
                'input'                        => 'select',
                'source'                       => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                'default'                      => 0,
                'visible'                      => 1,
                'required'                     => 0,
                'system'                       => 0,
                'user_defined'                 => 1,
                'sort_order'                   => 120,
                'position'                     => 120,
                'is_used_in_grid'              => 1,
                'is_visible_in_grid'           => 1,
                'is_filterable_in_grid'        => 1,
                'is_searchable_in_grid'        => 1,
                'is_used_for_customer_segment' => '1',
            );
            $this->addCustomerAttribute($setup, 'is_loyal', $params, [
                'adminhtml_customer',
                'adminhtml_checkout',
                'checkout_onepage_register',
            ], true);
        }
        // ... more version in the case we need update product attribute by another csv

        // Update Avenue sandbox url.
        if (version_compare($this->_setupVersion, '0.1.12') < 0) {
            $this->_config->saveConfig(
                'totaltools_pronto/authentication/avenue/sandbox/url',
                'https://totaltools-test-750.prontoavenue.biz',
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
        }

        $this->prontoRequestManager->getGuestAccountCode();
        $setup->endSetup();
    }

    /**
     * add attribute to customer - should not change anything
     * @param ModuleDataSetupInterface $setup
     * @param string $attributeCode
     * @param array $params
     * @param array $usedInForms
     * @param Boolean $usedForCustomerSegment
     * @return void
     */
    public function addCustomerAttribute($setup, $attributeCode, $params, $usedInForms, $usedForCustomerSegment)
    {
        /*@var Magento\Customer\Setup\CustomerSetup*/
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

        // remove and add attribute
        $eavSetup->removeAttribute(\Magento\Customer\Model\Customer::ENTITY, $attributeCode);
        $eavSetup->addAttribute(\Magento\Customer\Model\Customer::ENTITY, $attributeCode, $params);

        $defautlAttributeSetId = $eavSetup->getDefaultAttributeSetId(\Magento\Customer\Model\Customer::ENTITY);
        $defaultGroupId = $eavSetup->getDefaultAttributeGroupId(\Magento\Customer\Model\Customer::ENTITY,
            $defautlAttributeSetId);

        if ($usedForCustomerSegment) {
            $eavSetup->updateAttribute('customer', $attributeCode, 'is_used_for_customer_segment', '1');
        }

        $attribute = $eavSetup->getEavConfig()->getAttribute(\Magento\Customer\Model\Customer::ENTITY, $attributeCode);
        $attribute->addData([
            'attribute_set_id' => $defautlAttributeSetId,
            'attribute_group_id' => $defaultGroupId
        ]);
        $attribute->setData('used_in_forms', $usedInForms);
        $attribute->save();
    }

    /**
     * Migrate config paths to include different
     * values for sandbox & production
     */
    protected function migrateAuthConfig (ModuleDataSetupInterface $setup) {
        $configPaths = [
            ['old' => 'totaltools_pronto/authentication/avenue/url',       'new' => 'totaltools_pronto/authentication/avenue/sandbox/url'       ],
            ['old' => 'totaltools_pronto/authentication/avenue/username',  'new' => 'totaltools_pronto/authentication/avenue/sandbox/username'  ],
            ['old' => 'totaltools_pronto/authentication/avenue/password',  'new' => 'totaltools_pronto/authentication/avenue/sandbox/password'  ],
            ['old' => 'totaltools_pronto/authentication/connect/url',      'new' => 'totaltools_pronto/authentication/connect/sandbox/url'      ],
            ['old' => 'totaltools_pronto/authentication/connect/username', 'new' => 'totaltools_pronto/authentication/connect/sandbox/username' ],
            ['old' => 'totaltools_pronto/authentication/connect/password', 'new' => 'totaltools_pronto/authentication/connect/sandbox/password' ],
        ];

        $connection  = $setup->getConnection();
        $configTable = $setup->getTable('core_config_data');
        $query       = "SELECT * FROM `$configTable` WHERE `path` = :path";

        $newRows = [];
        foreach ($configPaths as $configPath) {
            $oldRows = $connection->fetchAssoc($query, [':path' => $configPath['old']]);
            $newRow = $connection->fetchAssoc($query, [':path' => $configPath['new']]);
            if (sizeof($newRow)==0) {
                foreach ($oldRows as $row) {
                    $newRows[] = [
                        'scope'    => $row['scope'],
                        'scope_id' => $row['scope_id'],
                        'path'     => $configPath['new'],
                        'value'    => $row['value'],
                    ];
                }
            }
        }
        if (sizeof($newRows)>0) {
            $connection->insertMultiple($configTable, $newRows);
        }
    }
}
