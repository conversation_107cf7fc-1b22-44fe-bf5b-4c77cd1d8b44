<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Setup;

use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magento\Framework\Setup\UpgradeSchemaInterface;

/**
 * Class UpgradeSchema
 *
 * @package Totaltools\Pronto\Setup
 * <AUTHOR> Nguy<PERSON> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * {@inheritdoc}
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        if (version_compare($context->getVersion(), '0.1.2', '<')) {
            //add column to order
            $this->addColumnToSalesOrder($setup, '0.1.2');
        }

        if (version_compare($context->getVersion(), '0.1.3', '<')) {
            //add some columns to order
            $this->addColumnToSalesOrder($setup, '0.1.3');
        }

        if (version_compare($context->getVersion(), '0.1.4', '<')) {
            /**
             * Create table 'totaltools_instore_order'
             */
            $this->createTableInstoreOrder($setup);

            /**
             * Create table 'totaltools_instore_order_item'
             */
            $this->createTableInstoreOrderItem($setup);
        }

        if (version_compare($context->getVersion(), '0.1.7', '<')) {
            /**
             * Create table 'totaltools_loyalty_tiers'
             */
            $this->createTableLoyaltyTiers($setup);
        }

        if (version_compare($context->getVersion(), '0.1.8', '<')) {
            $this->createCustomerSyncTable($setup);
        }

        if (version_compare($context->getVersion(), '0.1.13', '<')) {
            $this->addColumnToInstoreOrderTable($setup);
        }

        if (version_compare($context->getVersion(), '0.1.14', '<')) {
            $this->addColumnToCustomerSyncTable($setup);
        }


        $setup->endSetup();
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function addColumnToInstoreOrderTable(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'address_1',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Address 1',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'address_2',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Address 2',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'address_3',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Address 2',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'address_4',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Address 4',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'address_5',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Address 5',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'address_6',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Address 6',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'post_code',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                'length'   => '11',
                'nullable' => true,
                'comment'  => 'Post Code',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'phone',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Phone',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'email',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Email',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'abn',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'ABN',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'insider_id',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Insider Id',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'company',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Company',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'first_name',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'First Name',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'last_name',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Last Name',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'payment_methods',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Payment Methods',
            ]
        );
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_instore_order'),
            'online_order_id',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Online Order Id',
            ]
        );
    }

    /**
     * Add/Update columns to `sales_order` table based on version
     *
     * @param SchemaSetupInterface $setup
     * @param string|null          $version
     */
    private function addColumnToSalesOrder(SchemaSetupInterface $setup, $version = null)
    {
        if ($version === '0.1.2') {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'order_number',
                [
                    'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length'   => '255',
                    'nullable' => true,
                    'comment'  => 'Order Number on TTO system',
                ]
            );
        }

        if ($version === '0.1.3') {
            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'pronto_processing',
                [
                    'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_SMALLINT,
                    'nullable' => true,
                    'comment'  => 'Pronto Processing Status',
                ]
            );

            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'pronto_status',
                [
                    'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length'   => '50',
                    'nullable' => true,
                    'comment'  => 'Pronto Status',
                ]
            );

            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'pronto_uuid',
                [
                    'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length'   => '100',
                    'nullable' => true,
                    'comment'  => 'Pronto Transaction Id',
                ]
            );

            $setup->getConnection()->addColumn(
                $setup->getTable('sales_order'),
                'pronto_account_code',
                [
                    'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length'   => '50',
                    'nullable' => true,
                    'comment'  => 'Pronto Account Code',
                ]
            );

            $setup->getConnection()->changeColumn(
                $setup->getTable('sales_order'),
                'order_number',
                'pronto_number',
                [
                    'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                    'length'   => '255',
                    'nullable' => true,
                    'comment'  => 'Order Number on TTO system',
                ]
            );
        }
    }

    /**
     * Create `totaltools_instore_order` table
     *
     * @param SchemaSetupInterface $setup
     */
    private function createTableInstoreOrder(SchemaSetupInterface $setup)
    {
        $table = $setup->getConnection()->newTable(
            $setup->getTable('totaltools_instore_order')
        )->addColumn(
            'entity_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'Entity ID'
        )->addColumn(
            'customer_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['unsigned' => true, 'nullable' => false],
            'Magento Customer ID'
        )->addColumn(
            'seq_no',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Instore Order SeqNo'
        )->addColumn(
            'order_no',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Instore Order OrderNo'
        )->addColumn(
            'trans_date',
            \Magento\Framework\DB\Ddl\Table::TYPE_DATE,
            null,
            ['nullable' => true],
            'Instore Order Transaction Date'
        )->addColumn(
            'user_name',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Instore Order UserName'
        )->addColumn(
            'invoice_number',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Instore Order InvoiceNumber'
        )->addColumn(
            'store_name',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Instore Order StoreName'
        )->addForeignKey(
            'CUSTOMER_FOREIGN_KEY',
            'customer_id',
            $setup->getTable('customer_entity'),
            'entity_id',
            \Magento\Framework\DB\Ddl\Table::ACTION_CASCADE
        )->setComment(
            'Totaltools Order Tables'
        );

        $setup->getConnection()->createTable($table);
    }

    /**
     * Create `totaltools_instore_order_item`
     *
     * @param SchemaSetupInterface $setup
     */
    private function createTableInstoreOrderItem(SchemaSetupInterface $setup)
    {
        $table = $setup->getConnection()->newTable(
            $setup->getTable('totaltools_instore_order_item')
        )->addColumn(
            'entity_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'Entity ID'
        )->addColumn(
            'order_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['unsigned' => true, 'nullable' => false],
            'Instore Order ID'
        )->addColumn(
            'line_no',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Instore Order Item LineNo'
        )->addColumn(
            'stock_code',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Instore Order Item StockCode'
        )->addColumn(
            'stock_description',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Instore Order Item StockDescription'
        )->addColumn(
            'qty',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            null,
            ['nullable' => true],
            'Instore Order Item Qty'
        )->addColumn(
            'qty',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            [12, 4],
            ['nullable' => true],
            'Instore Order Item Qty'
        )->addColumn(
            'line_value',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            [12, 4],
            ['nullable' => true],
            'Instore Order Item LineValue'
        )->addColumn(
            'line_tax',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            [12, 4],
            ['nullable' => true],
            'Instore Order Item LineTax'
        )->addForeignKey(
            'INSTORE_ORDER_FOREIGN_KEY',
            'order_id',
            $setup->getTable('totaltools_instore_order'),
            'entity_id',
            \Magento\Framework\DB\Ddl\Table::ACTION_CASCADE
        )->setComment(
            'Totaltools Order Tables'
        );

        $setup->getConnection()->createTable($table);
    }

    /**
     * Create table `totaltools_loyalty_tiers`
     *
     * @param SchemaSetupInterface $setup
     */
    private function createTableLoyaltyTiers(SchemaSetupInterface $setup)
    {
        $table = $setup->getConnection()->newTable(
            $setup->getTable('totaltools_loyalty_tiers')
        )->addColumn(
            'entity_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'Entity ID'
        )->addColumn(
            'tier_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['unsigned' => true, 'nullable' => false],
            'Tier ID'
        )->addColumn(
            'description',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => true, 'default' => ''],
            'Insider Level'
        )->addColumn(
            'spend_required',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            [12, 4],
            ['nullable' => true, 'default' => 0],
            'Spend Required'
        )->addColumn(
            'rate',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            [12, 4],
            ['nullable' => true, 'default' => 0],
            'Rate'
        )->addColumn(
            'birthday_reward',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            [12, 4],
            ['nullable' => true, 'default' => 0],
            'Birthday Reward'
        )->addColumn(
            'upgrade_reward',
            \Magento\Framework\DB\Ddl\Table::TYPE_DECIMAL,
            [12, 4],
            ['nullable' => true, 'default' => 0],
            'Upgrade Reward'
        )->setComment(
            'Totaltools Loyalty Tiers Table'
        );

        $setup->getConnection()->createTable($table);
    }

    /**
     * create customer sync table
     * @param SchemaSetupInterface $setup
     * @throws \Zend_Db_Exception
     */
    protected function createCustomerSyncTable(SchemaSetupInterface $setup)
    {
        $table = $setup->getConnection()->newTable(
            $setup->getTable('totaltools_customer_sync')
        )->addColumn(
            'entity_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['identity' => true, 'unsigned' => true, 'nullable' => false, 'primary' => true],
            'Entity ID'
        )->addColumn(
            'customer_id',
            \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
            null,
            ['unsigned' => true, 'nullable' => false],
            'Magento Customer ID'
        )->addColumn(
            'status',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Customer Sync status'
        )->addColumn(
            'message',
            \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
            255,
            ['nullable' => false, 'default' => ''],
            'Customer Sync status message'
        )->setComment(
            'Totaltools Pronto Customer Sync Table'
        );

        $setup->getConnection()->createTable($table);
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function addColumnToCustomerSyncTable(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable('totaltools_customer_sync'),
            'change_email_verification',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Change Email Verification',
                'default'  => 0
            ]
        );
    }
}
