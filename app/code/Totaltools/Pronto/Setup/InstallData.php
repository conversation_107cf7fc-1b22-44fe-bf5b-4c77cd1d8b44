<?php
/**
 * <AUTHOR> Nguyen (<EMAIL>)
 * @package Totaltools_Pronto
 */


namespace Totaltools\Pronto\Setup;

use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Customer\Setup\CustomerSetupFactory;

/**
 * install data for Pronto
 */
class InstallData implements InstallDataInterface
{
    /**
     * EAV setup factory
     *
     * @var CustomerSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Init
     *
     * @param EavSetupFactory $eavSetupFactory
     * @param \Magento\Framework\Logger\Monolog $logger
     */
    public function __construct(
        CustomerSetupFactory $eavSetupFactory,
        \Magento\Framework\Logger\Monolog $logger
    )
    {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->logger = $logger;
    }

    /**
     * install
     *
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface $context
     */
    public function install(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $installer = $setup;

        $installer->startSetup();

        // add customer attributes
        /*@var Magento\Customer\Setup\CustomerSetup*/
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);

        $defautlAttributeSetId = $eavSetup->getDefaultAttributeSetId(\Magento\Customer\Model\Customer::ENTITY);
        $defaultGroupId        = $eavSetup->getDefaultAttributeGroupId(\Magento\Customer\Model\Customer::ENTITY, $defautlAttributeSetId);

        $code   = 'is_loyal';
        $params = array(
            'type'                         => 'int',
            'label'                        => 'Insider',
            'input'                        => 'select',
            'source'                       => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
            'default'                      => 0,
            'visible'                      => 1,
            'required'                     => 0,
            'system'                       => 0,
            'user_defined'                 => 1,
            'sort_order'                   => 120,
            'position'                     => 120,
            'attribute_set_id'             => $defautlAttributeSetId,
            'attribute_group_id'           => $defaultGroupId,
            'is_used_in_grid'              => 1,
            'is_visible_in_grid'           => 1,
            'is_filterable_in_grid'        => 1,
            'is_searchable_in_grid'        => 1,
        );
        $eavSetup->removeAttribute(\Magento\Customer\Model\Customer::ENTITY, $code);
        $eavSetup->addAttribute(\Magento\Customer\Model\Customer::ENTITY, $code, $params);

        $attribute = $eavSetup->getEavConfig()->getAttribute(\Magento\Customer\Model\Customer::ENTITY, $code);
        $attribute->setData('is_used_for_customer_segment', 1);
        $attribute->setData('used_in_forms', [
            'adminhtml_customer',
            'adminhtml_checkout',
            'customer_account_create',
            'checkout_onepage_register',
        ]);
        $attribute->save();

        $installer->endSetup();
    }
}
