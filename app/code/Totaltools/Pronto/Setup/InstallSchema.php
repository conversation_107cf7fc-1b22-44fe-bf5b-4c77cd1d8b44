<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Pronto
 */


namespace Totaltools\Pronto\Setup;

use Magento\Framework\Setup\InstallSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

/**
 * install schema for Pronto
 */
class InstallSchema implements InstallSchemaInterface
{
    /**
     * install schema
     *
     * @param SchemaSetupInterface $setup
     * @param ModuleContextInterface $context
     */
    public function install(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $installer = $setup;
        $installer->startSetup();

        //add column to order
        $installer->getConnection()->addColumn(
            $installer->getTable('sales_order'),
            'order_number',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Order Number on TTO system',
            ]
        );

        $installer->endSetup();
    }
}
