<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Cron;

use Totaltools\Pronto\Model\Executor\InsiderTier as ExecutorInsiderTier;
use Totaltools\Pronto\Helper\Config;

/**
 * Class JobUpdateInsiderTiers
 *
 * NOTICE: Logic inside \Totaltools\Pronto\Model\Executor\InsiderTier could be re-use in the future or for the locally
 * testing purpose (such as a TestCommand), that is why we should separate the business logic as much as possible
 * outside files like this, and obviously it's too easy to maintain if need (edit once, run everywhere)
 *
 * @package Totaltools\Pronto\Cron
 * <AUTHOR> <<EMAIL>>
 */
class JobUpdateInsiderTiers
{
    /**
     * @var ExecutorInsiderTier
     */
    private $executor;

    /**
     * @var Config
     */
    protected $helper;

    /**
     * Constructor.
     *
     * @param ExecutorInsiderTier $executor
     */
    public function __construct(
        ExecutorInsiderTier $executor,
        Config $helper
    ) {
        $this->executor = $executor;
        $this->helper   = $helper;
    }

    /**
     * @return void
     */
    public function execute()
    {
        $status = $this->helper->isEnabled();
        if($status) {
            $this->executor->update();
        }
    }
}
