<?php

namespace Totaltools\Pronto\Cron;

/**
 * Class GenerateToken
 * @package Totaltools\Pronto\Cron
 */
class GenerateToken
{
    /**
     * @var \Totaltools\Pronto\Model\Api\GenerateToken
     */
    private $generate;

    /**
     * GenerateToken constructor.
     * @param \Totaltools\Pronto\Model\Api\GenerateToken $generateToken
     */
    public function __construct(
        \Totaltools\Pronto\Model\Api\GenerateToken $generateToken
    ) {
        $this->generate = $generateToken;
    }

    /**
     * Push customers ot pronto
     */
    public function execute()
    {
        $this->generate->generate();
    }
}
