<?php
/**
 * <AUTHOR> Internet (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Pronto\Cron;

use Magento\Sales\Model\Order;
use Magento\Sales\Model\Order\Item as OrderItem;
use Magento\Sales\Model\Order\Tax as OrderTax;
use Magento\Sales\Model\Order\TaxFactory as OrderTaxFactory;
use Totaltools\Checkout\Helper\TotalToolsConfig as TotalToolsConfigAlias;
use Totaltools\Pronto\Model\Api\Avenue;
use Magento\Sales\Model\ResourceModel\Order\CollectionFactory as OrderCollectionFactory;
use Magento\Customer\Model\CustomerFactory;
use Totaltools\Pronto\Api\CustomerRequestManagerInterface;
use Psr\Log\LoggerInterface;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Pronto\Helper\Config as ConfigAlias;

class OrderIntegration
{
    const PAYMENT_METHOD_OTHER = 'CC'; // Pronto has no "other"
    const PAYMENT_METHOD_CREDIT = 'CC';
    const PAYMENT_METHOD_PAYPAL = 'PP';
    const PAYMENT_METHOD_ZIPMONEY = 'CC';
    const PAYMENT_METHOD_EBAY = 'PP'; // eBay orders use PayPal

    const PAYMENT_REFERENCE_OTHER = 'CC';
    const PAYMENT_REFERENCE_PAYPAL = 'PP';
    const PAYMENT_REFERENCE_ZIPMONEY = 'ZP';
    const PAYMENT_REFERENCE_CREDIT = 'CC';
    const PAYMENT_REFERENCE_GIFT_CARD = 'GC';
    const PAYMENT_REFERENCE_EBAY = 'PP';

    const CARRIER_CODE_UNKNOWN = 'OTH';
    const CARRIER_CODE_PICKUP = 'PIK';

    const PREFIX_STORE_CODE = 'STORE';
    const PREFIX_WAREHOUSE_CODE = 'WHSE';

    /**
     * PayPal payment methods
     * @var string[]
     */
    protected $paypalPaymentMethods = [
        'braintree_paypal',
        'braintree_paypal_vault',
        'paypal_direct',
        'paypal_express',
    ];

    /**
     * Credit card payment methods
     * @var string[]
     */
    protected $creditCardPaymentMethods = [
        'authorizenet_directpost',
        'braintree',
        'braintree_cc_vault',
        'cybersource',
        'eway',
        'payflowpro',
        'payflowpro_cc_vault'
    ];

    /**
     * Zipmoney payment methods
     * @var string[]
     */
    protected $zipmoneyPaymentMethods = [
        'zipmoneypayment',
        'au_zipmoneypayment'
    ];

    /**
     * eBay orders (imported via ChannelAdvisor)
     * @var string[]
     */
    protected $ebayPaymentMethods = [
        'channeladvisorapi',
    ];

    /**
     * Other payment methods
     * @var string[]
     */
    protected $otherPaymentMethods = [
        'banktransfer',
        'cashondelivery',
        'checkmo',
        'free',
        'purchaseorder',
    ];

    /**
     * @var array
     */
    private $fieldOrderCollections = [
        'entity_id',
        'customer_id',
        'increment_id',
        'quote_id',
        'store_id',
        'customer_is_guest',
        'is_virtual',
        'billing_address_id',
        'shipping_method',
        'shipping_address_id',
        'shipping_incl_tax',
        'shipping_description',
        'base_subtotal_incl_tax',
        'base_currency_code',
        'customer_email',
        'global_currency_code',
        'order_currency_code',
        'store_currency_code',
        'base_subtotal',
        'grand_total',
        'subtotal',
        'tax_amount',
        'total_qty_ordered',
        'store_name',
        'shippit_delivery_instructions',
        'pronto_uuid',
        'pronto_processing',
        'pronto_account_code',
        'pronto_status',
    ];

    /**
     * Avenue API model
     * @var Avenue
     */
    protected $avenue;

    /**
     * Order collection factory
     * @var OrderCollectionFactory
     */
    protected $orderCollectionFactory;

    /**
     * Customer factory
     * @var CustomerFactory
     */
    protected $customerFactory;

    /**
     * Pronto guest customer model
     * @var CustomerRequestManagerInterface
     */
    protected $prontoRequestManager;

    /**
     * Order Tax factory
     * @var OrderTaxFactory
     */
    protected $orderTaxFactory;

    /**
     * Logger interface
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var TotalToolsConfigAlias
     */
    private $totaltoolsConfig;

    /**
     * @var ConfigAlias
     */
    protected $helper;

    /**
     * OrderIntegration constructor.
     * @param Avenue $avenue
     * @param OrderCollectionFactory $orderCollectionFactory
     * @param CustomerFactory $customerFactory
     * @param CustomerRequestManagerInterface $prontoRequestManager
     * @param OrderTaxFactory $orderTaxFactory
     * @param LoggerInterface $logger
     * @param StoreManagerInterface $storeManager
     * @param TotalToolsConfigAlias $totalToolsConfig
     * @param ConfigAlias $configHelper
     */
    public function __construct(
        Avenue $avenue,
        OrderCollectionFactory $orderCollectionFactory,
        CustomerFactory $customerFactory,
        CustomerRequestManagerInterface $prontoRequestManager,
        OrderTaxFactory $orderTaxFactory,
        LoggerInterface $logger,
        StoreManagerInterface $storeManager,
        TotalToolsConfigAlias $totalToolsConfig,
        ConfigAlias $configHelper
    ) {
        $this->avenue = $avenue;
        $this->orderCollectionFactory = $orderCollectionFactory;
        $this->customerFactory = $customerFactory;
        $this->prontoRequestManager = $prontoRequestManager;
        $this->orderTaxFactory = $orderTaxFactory;
        $this->logger = $logger;
        $this->storeManager = $storeManager;
        $this->totaltoolsConfig = $totalToolsConfig;
        $this->helper = $configHelper;
    }

    /**
     * Add products to changes list with price which depends on date
     *
     * @return void
     */
    public function execute()
    {
        $status = $this->helper->isEnabled();
        if($status) {
            $orderCollection = $this->orderCollectionFactory->create()
                ->addFieldToSelect($this->fieldOrderCollections)
                ->addFieldToFilter('state', ['nin' => ['holded', 'canceled']])
                ->addFieldToFilter('pronto_status', ['nin' => ['complete', 'error']])
                ->addFieldToFilter('pronto_processing', '0');

            /** @var Order $order */
            foreach ($orderCollection as $order) {
                try {
                    if ($order->getCustomerId()) {
                        $this->storeManager->setCurrentStore($order->getStoreId());
                        $order->setData('pronto_processing', '1')->save();
                        $prontoStatus = $order->getData('pronto_status');
                        switch ($prontoStatus) {
                            case 'pending':
                                $this->createDebtor($order);
                                break;
                            case 'has_debtor':
                                $this->createProntoOrder($order);
                                break;
                            case 'order_pending':
                                $this->updateOrder($order);
                                break;
                            default:
                                break;
                        }
                        $order->setData('pronto_processing', '0')->save();
                    }

                } catch (\Exception $e) {
                    try {
                        $order->setData('pronto_processing', '0')->save();
                    } catch (\Exception $e1) {
                        $this->logger->error($e1->getMessage());
                        continue;
                    }
                    $this->logger->error('Error updating pronto order (' . $order->getId() . '): ' . $e->getMessage());
                    continue;
                }
            }
        }
    }

    /**
     * Create Debtor on Pronto Avenue
     *
     * @param Order $order
     *
     * @return void
     * @throws \Exception
     */
    public function createDebtor($order)
    {
        $customerId = $order->getCustomerId();

        if ($customerId) {
            $customer = $this->customerFactory->create();
            $customer->load($customerId);
            $accountCode = $customer->getData('account_code');
        } else {
            $accountCode = $this->prontoRequestManager->getGuestAccountCode();
        }

        if ($accountCode) {
            $order
                ->setData('pronto_account_code', $accountCode)
                ->setData('pronto_status', 'has_debtor')
                ->save();
        }
    }

    /**
     * Create Order on Pronto Avenue
     *
     * @param Order $order
     *
     * @return void
     * @throws \Exception
     */
    public function createProntoOrder($order)
    {
        // create order on Avenue - response number_order
        $orderParams = [];

        $orderParams['customer_reference'] = $order->getIncrementId();
        $orderParams['debtor'] = $order->getData('pronto_account_code');
        $orderParams['delivery_instructions'] =
            $order->getData('order_comment') ? $order->getData('order_comment') : '';

        $shippingAddress = $order->getShippingAddress();
        if ($shippingAddress) {
            $orderParams['delivery_address'] = [
                'address1' => $shippingAddress->getStreetLine(1),
                'address2' => $shippingAddress->getStreetLine(2),
                'address3' => $shippingAddress->getStreetLine(3),
                'address4' => $shippingAddress->getStreetLine(4),
                'address5' => $shippingAddress->getStreetLine(5),
                'address6' => $shippingAddress->getStreetLine(6),
                'address7' => $shippingAddress->getStreetLine(7),
                'postcode' => $shippingAddress->getPostcode(),
                'phone'    => $shippingAddress->getTelephone(),
            ];
        } else {
            $orderParams['delivery_address'] = [
                'address1' => 'N\A',
                'address2' => 'N\A',
                'address3' => 'N\A',
                'address4' => 'N\A',
                'address5' => 'N\A',
                'address6' => 'N\A',
                'address7' => 'N\A',
                'postcode' => 'N\A',
                'phone'    => 'N\A',
            ];
        }

        $paymentMethod = self::PAYMENT_METHOD_OTHER;
        $paymentReference = self::PAYMENT_REFERENCE_OTHER;
        $orderPaymentMethod = $order->getPayment()->getMethod();
        if (in_array($orderPaymentMethod, $this->paypalPaymentMethods)) {
            $paymentMethod = self::PAYMENT_METHOD_PAYPAL;
            $paymentReference = self::PAYMENT_REFERENCE_PAYPAL;
        } elseif (in_array($orderPaymentMethod, $this->creditCardPaymentMethods)) {
            $paymentMethod = self::PAYMENT_METHOD_CREDIT;
            $paymentReference = self::PAYMENT_REFERENCE_CREDIT;
        } elseif (in_array($orderPaymentMethod, $this->otherPaymentMethods)) {
            $paymentMethod = self::PAYMENT_METHOD_OTHER;
            $paymentReference = self::PAYMENT_REFERENCE_GIFT_CARD;
        } elseif (in_array($orderPaymentMethod, $this->zipmoneyPaymentMethods)) {
            $paymentMethod = self::PAYMENT_METHOD_ZIPMONEY;
            $paymentReference = self::PAYMENT_REFERENCE_ZIPMONEY;
        } elseif (in_array($orderPaymentMethod, $this->ebayPaymentMethods)) {
            $paymentMethod = self::PAYMENT_METHOD_EBAY;
            $paymentReference = self::PAYMENT_REFERENCE_EBAY . $order->getIncrementId();
        }

        $orderParams['payment'] = [
            'method'        => $paymentMethod,
            'reference'     => $paymentReference,
            'amount'        => (float) $order->getPayment()->getAmountOrdered(),
            'currency_code' => $order->getOrderCurrency()->getCode(),
            'bank_code'     => '',
            'tax_rate_code' => '',
        ];

        /** @var OrderItem $item */
        foreach ($order->getAllVisibleItems() as $item) {
            $carrierCode = self::CARRIER_CODE_UNKNOWN;
            $defaultCarrierCode = $this->totaltoolsConfig->getTotalToolsConfig('cc_carrier_code');
            if (strpos(strtolower($order->getShippingMethod()), $defaultCarrierCode) !== false) {
                $carrierCode = self::CARRIER_CODE_PICKUP;
            }

            $orderParams['lines'][] = [
                'type'          => 'SN',
                'item_code'     => $item->getSku(),
                'quantity'      => (float) $item->getQtyOrdered(),
                'uom'           => '',
                'price_ex_tax'  => (float) $item->getPrice(),
                'price_inc_tax' => (float) $item->getPriceInclTax(),
                'charge_type'   => '',
                'description'   => $item->getName(),
                'carrier_code'  => $carrierCode,
            ];
        }

        $shippingDescription = $order->getShippingDescription();

        if (!$shippingDescription) {
            $shippingDescription = 'N\A';
        }

        /** @var OrderTax $orderTax */
        $orderTax = $this->orderTaxFactory->create();
        $orderTax->load($order->getId(), 'order_id');

        $taxRate = (float) $orderTax->getPercent();
        $shippingExclTax = $order->getShippingInclTax() / (($taxRate / 100) + 1);
        $shippingExclTax = round($shippingExclTax, 4);

        $orderParams['lines'][] = [
            'type' => 'SC',
            'item_code' => 'CL1',
            'quantity' => 1,
            'price_ex_tax' => (float) $shippingExclTax,
            'price_inc_tax' => (float) $order->getShippingInclTax(),
            'description' => $shippingDescription,
        ];

        // Retrieve order order history comment with corresponding eBay fee.
        $filteredOrderHistory = array_filter($order->getAllStatusHistory(), function($orderHistory) {
            /** @var \Magento\Sales\Model\Order\Status\History $orderHistory */
            return strpos($orderHistory->getComment(), 'EBAY') === 0;
        });
        if (count($filteredOrderHistory) > 0) {
            /** @var \Magento\Sales\Model\Order\Status\History $orderHistory */
            $orderHistory = reset($filteredOrderHistory);
            $orderParams['lines'][] = [
                'type' => 'DN',
                'item_code' => 'N',
                'description' => $orderHistory->getComment(),
            ];
        }
        $avenueApi = $this->avenue
            ->setHttpMethod(Avenue::HTTP_METHOD_POST)
            ->setFormat(Avenue::FORMAT_JSON)
            ->setPath('order/v5')
            ->setPost($orderParams);
        $response = $avenueApi->call();
        //decode to get array
        $response = json_decode($response, true);
        if (isset($response['apitransactions'][0]['uuid'])) {
            $uuid = $response['apitransactions'][0]['uuid'];
            $order
                ->setData('pronto_status', 'order_pending')
                ->setData('pronto_uuid', $uuid)
                ->save();
        } else {
            $order->setData('pronto_status', 'error')->save();
        }
    }

    /**
     * Update pronto_status for order
     *
     * @param Order $order
     *
     * @return void
     * @throws \Exception
     */
    public function updateOrder($order)
    {
        if ($order->getData('pronto_uuid')) {
            $avenueApi = $this->avenue
                ->setHttpMethod(Avenue::HTTP_METHOD_GET)
                ->setFormat(Avenue::FORMAT_JSON)
                ->setPath('transaction/v4')
                ->setGetFilter('uuid', $order->getData('pronto_uuid'))
            ;
            $response = $avenueApi->call();
            $response = json_decode($response, true);

            if (isset($response['apitransactions'][0]['result_url'])) {
                $resultUrl = str_replace('/api/xml/order/v4?', '', $response['apitransactions'][0]['result_url']);
                parse_str($resultUrl, $params);

                if (($response['apitransactions'][0]['status'] == 'Complete') && isset($params['number'])) {
                    $order
                        ->setData('pronto_status', 'complete')
                        ->setData('pronto_order_number', $params['number'])
                        ->save();
                }
            }
        } else {
            $order->setData('pronto_status', 'error')->save();
        }
    }
}