<?php
/**
 * Copyright © 2015 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Pronto\Cron;

use Magento\Framework\Exception\LocalizedException;
use Totaltools\Pronto\Helper\Config;
use Totaltools\Pronto\Model\CustomerIntegrationRepository;

class LoyaltyIntegration
{
    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $_logger;

    /**
     * @var \Totaltools\Pronto\Model\Request\OrderHistory
     */
    protected $orderHistory;
    /**
     * @var \Totaltools\Pronto\Api\CustomerRequestManagerInterface
     */
    protected $prontoRequestManager;
    /**
     * @var \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory
     */
    protected $customerCollectionFactory;
    /**
     * @var Config
     */
    protected $helper;

    /**
     * @var CustomerIntegrationRepository
     */
    protected $integrationRepository;

    /**
     * LoyaltyIntegration constructor.
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager
     * @param \Totaltools\Pronto\Model\Request\OrderHistory $orderHistory
     * @param \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerCollectionFactory
     * @param Config $helper
     * @param CustomerIntegrationRepository $integrationRepository
     */
    public function __construct(
        \Psr\Log\LoggerInterface $logger,
        \Totaltools\Pronto\Api\CustomerRequestManagerInterface $prontoRequestManager,
        \Totaltools\Pronto\Model\Request\OrderHistory $orderHistory,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerCollectionFactory,
        Config $helper,
        CustomerIntegrationRepository $integrationRepository
    ) {
        $this->_logger = $logger;
        $this->prontoRequestManager = $prontoRequestManager;
        $this->orderHistory = $orderHistory;
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->helper   = $helper;
        $this->integrationRepository = $integrationRepository;
    }

    public function execute()
    {
        $status = $this->helper->isEnabled();
        if($status) {
            try {
                /** @var \Magento\Customer\Model\Customer $customer */
                foreach ($this->integrationRepository->getLoyalCustomers() as $customer) {
                    try {
                        $this->_logger->info($customer->getFirstname());

                        // update instore orders
                        $this->orderHistory->updateInstoreOrders($customer);

                        // update loyalty_last_updated
                        $now = date('Y-m-d H:i:s');
                        $customer->setLoyaltyLastUpdated($now);
                        $customer->setData('loyalty_last_updated', $now);
                        $customer->getResource()->saveAttribute($customer, 'loyalty_last_updated');
                    } catch (\Exception $e) {
                        $this->_logger->error('Error updating loyalty customer (' . $customer->getId() . ')');
                        $this->_logger->error($e->getMessage());
                    }
                }
            } catch (LocalizedException $exception) {
                $this->_logger->error($exception->getMessage());
            }
        }
    }
}