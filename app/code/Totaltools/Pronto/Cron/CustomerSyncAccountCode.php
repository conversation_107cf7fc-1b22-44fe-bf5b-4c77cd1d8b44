<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_CustomerSyncAccountCode
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2020 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Cron;

use \Totaltools\Pronto\Api\Data\CustomerSyncInterface;
use \Magento\Framework\Api\SearchCriteriaBuilder;
use \Magento\Customer\Api\CustomerRepositoryInterface;
use \Totaltools\Pronto\Api\CustomerSyncRepositoryInterface;

class CustomerSyncAccountCode
{
    /**
     * @var \Totaltools\Pronto\Model\PushToPronto
     */
    protected $pronto;

    /**
     * @var CustomerRepositoryInterface
     */
    protected $customerRepository;

    /**
     * @var CustomerSyncRepositoryInterface
     */
    protected $customerSyncRepository;

    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    /**
     * @var CustomerSyncInterface
     */
    protected $customerInterface;

    /**
     * CustomerSync constructor.
     * @param \Totaltools\Pronto\Model\PushToPronto $pronto
     */
    public function __construct(
        CustomerSyncInterface $customerSyncInterface,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        CustomerRepositoryInterface $customerRepository,
        CustomerSyncRepositoryInterface $customerSyncRepository
    )
    {
        $this->customerInterface       = $customerSyncInterface;
        $this->searchCriteriaBuilder   = $searchCriteriaBuilder;
        $this->customerRepository      = $customerRepository;
        $this->customerSyncRepository  = $customerSyncRepository;
    }

    /**
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function execute()
    {
        $searchCriteria = $this->searchCriteriaBuilder->addFilter(
            'account_code',
            true,
            'null'
        )->create();
        $collection = $this->customerRepository->getList($searchCriteria);

        foreach ($collection->getItems() as $customer) {
            $customerId = $customer->getId();
            $customerSync = $this->customerInterface;
            $customerSync->setEntityId(null);
            $customerSync->setCustomerId($customerId);
            $customerSync->setStatus('Pending');
            $customerSync->setMessage('Customer Ready to push');
            $customerSync->setChangeEmailVerification(1);
            $this->customerSyncRepository->save($this->customerInterface);
        }
    }
}