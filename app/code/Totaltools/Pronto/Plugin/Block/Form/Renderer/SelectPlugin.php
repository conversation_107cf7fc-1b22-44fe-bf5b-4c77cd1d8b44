<?php

namespace Totaltools\Pronto\Plugin\Block\Form\Renderer;

/**
 * @package     Totaltools_Pronto
 * <AUTHOR> Iqbal <<EMAIL>>
 * @since       0.1.14
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

use Totaltools\Customer\Helper\AttributeData as CustomerHelper;

/** @class SelectPlugin */
class SelectPlugin
{
    /**
     * @var CustomerHelper
     */
    protected $customerHelper;

    /**
     * @param CustomerHelper $customerHelper
     */
    public function __construct(CustomerHelper $customerHelper)
    {
        $this->customerHelper = $customerHelper;
    }

    /**
     * @param \Magento\CustomAttributeManagement\Block\Form\Renderer\Select $subject
     * @param \Magento\Framework\View\Element\BlockInterface $templateContext 
     * @return void
     */
    public function beforeSetTemplateContext($subject, $templateContext)
    {
        $subject->setIsB2bCustomer($this->customerHelper->isB2bCustomer());
    }
}
