<?php

namespace Totaltools\Pronto\Plugin\Checkout\Model;

/**
 * Class CustomerChangeShippingAddress
 * @package Totaltools\Pronto\Plugin\Checkout\Model
 */
class CustomerChangeShippingAddress
{
    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;

    /**
     * @var \Totaltools\Pronto\Helper\Config
     */
    private $config;

    /**
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Totaltools\Pronto\Helper\Config $config
     */
    public function __construct(
        \Magento\Customer\Model\Session $customerSession,
        \Totaltools\Pronto\Helper\Config $config
    ) {
        $this->customerSession = $customerSession;
        $this->config = $config;
    }

    /**
     * @param \Magento\Checkout\Model\ShippingInformationManagement $subject
     * @param $cartId
     * @param \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
     */
    public function beforeSaveAddressInformation(
        \Magento\Checkout\Model\ShippingInformationManagement $subject,
        $cartId,
        \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
    ) {
        $status = $this->config->isEnabled();
        if ($status) {
            if ($this->customerSession->isLoggedIn()) {
                $customer = $this->customerSession->getCustomer();
                if (!$customer->getData('is_loyal')) {
                    $exAttributes = $addressInformation->getExtensionAttributes();
                    if ($exAttributes->getIsLoyal()) {
                        $this->customerSession->setCurrentIsLoyal(1);
                    } else {
                        $this->customerSession->setCurrentIsLoyal(false);
                    }
                }
            }
        }
    }
}
