<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2021 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Plugin\Sales;

class AddPushToProntoPlugin
{
    /**
     * @var \Magento\Framework\App\Action\Context
     */
    private $context;

    /**
     * @var \Magento\Backend\Model\UrlInterface
     */
    private $url;

    /**
     * AddPushToProntoPlugin constructor.
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Backend\Model\UrlInterface $url
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Backend\Model\UrlInterface $url
    ) {
        $this->url = $url;
        $this->context = $context;
    }

    /**
     * @param \Magento\Backend\Block\Widget\Context $subject
     * @param $buttonList
     * @return mixed
     */
    public function afterGetButtonList(
        \Magento\Backend\Block\Widget\Context $subject,
        $buttonList
    ) {
        $request = $this->context->getRequest();

        if ($request->getFullActionName() == 'sales_order_view'){
            $message = __('Are you sure you want to update order timestamp to push to Pronto?');
            $buttonList->add(
                'pronto_order_push',
                [
                    'label' => __('Push to Pronto'),
                    'onclick' => "confirmSetLocation('{$message}', '{$this->getOrderPushUrl($request)}')",
                    'class' => 'push'
                ],
                100
            );
        }

        return $buttonList;
    }

    public function getOrderPushUrl($request)
    {
        $orderId = $request->getParam('order_id');

        return $this->url->getUrl(
            'pronto/order/push',
            [
                'order_id' => $orderId
            ]
        );
    }
}
