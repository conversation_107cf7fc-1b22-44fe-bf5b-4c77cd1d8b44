<?php
/**
 * Created by <PERSON>
 * Copyright (c) 2017 Balance Internet (http://www.balanceinternet.com.au/)
 * Date: 03/11/2017
 */

namespace Totaltools\Pronto\Block\Checkout;

use Magento\Customer\Model\Session as CustomerSession;

class LayoutProcessor implements \Magento\Checkout\Block\Checkout\LayoutProcessorInterface
{
    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * LayoutProcessor constructor.
     *
     * @param CustomerSession $customerSession
     */
    public function __construct(CustomerSession $customerSession)
    {
        $this->customerSession = $customerSession;
    }

    /**
     * Process js Layout of block
     *
     * @param array $jsLayout
     * @return array
     */
    public function process($jsLayout)
    {
        $isLoggedIn  = $this->customerSession->isLoggedIn();
        $targetField = 'shipping-address-fieldset';
        $isLoyal     = false;
        if ($isLoggedIn) {
            $customer = $this->customerSession->getCustomer();
            if (count($customer->getAddresses())) {
                $targetField = 'before-form';
            }
            $isLoyal = $customer->getData('is_loyal');
        }
        if (!$isLoyal || !$isLoggedIn) {
            $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']
            ['shippingAddress']['children'][$targetField]['children']['is_loyal'] = [
                'component'  => 'Magento_Ui/js/form/element/boolean',
                'config'     => [
                    'customScope' => 'shippingAddress',
                    'template'    => 'ui/form/field',
                    'elementTmpl' => 'ui/form/element/checkbox',
                    'id'          => 'is_loyal'
                ],
                'dataScope'  => 'shippingAddress.is_loyal',
                'label'      => __('Become a loyal customer'),
                'provider'   => 'checkoutProvider',
                'visible'    => false,
                'validation' => [],
                'sortOrder'  => 1000,
                'id'         => 'is_loyal'
            ];
        }

        return $jsLayout;
    }
}