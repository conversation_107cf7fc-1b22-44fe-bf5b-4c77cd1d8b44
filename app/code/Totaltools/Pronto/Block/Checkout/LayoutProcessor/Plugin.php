<?php
/**
 * <AUTHOR> (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Pronto\Block\Checkout\LayoutProcessor;

/**
 * layout processor checkout page plugin
 */
class Plugin
{
    protected $customerSession;
    protected $checkoutSession;

    public function __construct(
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Checkout\Model\Session $checkoutSession
    )
    {
        $this->customerSession = $customerSession;
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        array  $jsLayout
    ) {
        if ($this->customerSession->isLoggedIn()) {

            $customer = $this->customerSession->getCustomer();
            $isLoyal = $customer->getData('is_loyal');
            if (!$isLoyal) {
                /*$jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']
                ['shippingAddress']['children']['shipping-address-fieldset']['children']['is_loyal'] = [
                    'component' => 'Magento_Ui/js/form/element/boolean',
                    'config' => [
                        'customScope' => 'shippingAddress',
                        'template' => 'ui/form/field',
                        'elementTmpl' => 'ui/form/element/checkbox',
                        'id' => 'is_loyal'
                    ],
                    'dataScope' => 'shippingAddress.is_loyal',
                    'label' => 'Become a loyal customer',
                    'provider' => 'checkoutProvider',
                    'visible' => true,
                    'validation' => [],
                    'sortOrder' => 250,
                    'id' => 'is_loyal_field'
                ];*/
                $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']
                ['shippingAddress']['children']['before-fields']['children']['is_loyal'] = [
                    'component' => 'Magento_Ui/js/form/element/boolean',
                    'config' => [
                        'customScope' => 'shippingAddress',
                        'template' => 'ui/form/field',
                        'elementTmpl' => 'ui/form/element/checkbox',
                        'id' => 'is_loyal'
                    ],
                    'dataScope' => 'shippingAddress.is_loyal',
                    'label' => 'Become a loyal customer',
                    'provider' => 'checkoutProvider',
                    'visible' => false,
                    'validation' => [],
                    'sortOrder' => 250,
                    'id' => 'is_loyal'
                ];
            }
        }

        if ($this->checkoutSession->getQuote()->getIsVirtual()) {
            $jsLayout['components']['checkout']['children']['continueButton']['componentDisabled'] = true;
        }

        return $jsLayout;
    }
}
