<?php
namespace Totaltools\Pronto\Block\Adminhtml;

use Magento\Framework\View\Element\UiComponent\Control\ButtonProviderInterface;
use Magento\Customer\Block\Adminhtml\Edit\GenericButton;

use Magento\Customer\Controller\RegistryConstants;

class SyncInvoices  extends GenericButton implements ButtonProviderInterface
{
    /**
     * @var \Magento\Framework\App\Action\Context
     */
    private $context;

    /**
     * @var \Magento\Backend\Model\UrlInterface
     */
    private $url;
    /**
     * @var Registry
     */
    private $_coreRegistry;
    /**
     * AddSyncInvoicesPlugin constructor.
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Backend\Model\UrlInterface $url
     */
    public function __construct(
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Backend\Model\UrlInterface $url
    ) {
        $this->url = $url;
        $this->_coreRegistry = $registry;
        $this->context = $context;
    }

    /**
     * @return array
     */
    public function getButtonData()
    {
        $message = __("Are you sure you want to update customer's invoices from Pronto?");
        $customerId = $this->getCustomerId();
      
        $data = [
            'label' => __('Sync Invoices'),
            'class' => 'add',
            'id' => 'sync_customer_invoices',
            'on_click' => 'deleteConfirm("' . $message . '", "' . $this->getSyncInvoicesUrl($customerId) . '")',
            'sort_order' => 65,
            'aclResource' => 'Magento_Customer::invalidate_tokens',
        ];
        return $data;
    }

    public function getSyncInvoicesUrl($customerId)
    {
        return $this->url->getUrl(
            'pronto/customersync/invoices',
            [
                'id' => $customerId
            ]
        );
    }
    /**
     * Return the customer Id.
     *
     * @return int|null
     */
    public function getCustomerId()
    {
        $customerId = $this->_coreRegistry->registry(RegistryConstants::CURRENT_CUSTOMER_ID);
        return $customerId;
    }
}