<?php

namespace Totaltools\Pronto\Block\System\Button;

/**
 * Class Token
 * @package Totaltools\Pronto\Block\System\Button
 */
class Token extends \Magento\Config\Block\System\Config\Form\Field
{
    /**
     * @return $this|\Magento\Config\Block\System\Config\Form\Field
     */
    protected function _prepareLayout()
    {
        parent::_prepareLayout();
        $this->setTemplate('system/config/button.phtml');

        return $this;
    }

    /**
     * @param \Magento\Framework\Data\Form\Element\AbstractElement $element
     * @return string
     */
    public function render(\Magento\Framework\Data\Form\Element\AbstractElement $element)
    {
        $element->unsScope()->unsCanUseWebsiteValue()->unsCanUseDefaultValue();

        return parent::render($element);
    }

    /**
     * Get the button and scripts contents
     *
     * @param \Magento\Framework\Data\Form\Element\AbstractElement $element
     * @return string
     */
    protected function _getElementHtml(\Magento\Framework\Data\Form\Element\AbstractElement $element)
    {
        $originalData = $element->getOriginalData();
        $buttonLabel = !empty($originalData['button_label']) ? $originalData['button_label'] : 'Generated New Token';
        $this->addData(
            [
                'button_label' => __($buttonLabel),
                'button_url' => $this->getUrl($originalData['button_url']),
                'html_id' => $element->getHtmlId()
            ]
        );

        return $this->_toHtml();
    }
}
