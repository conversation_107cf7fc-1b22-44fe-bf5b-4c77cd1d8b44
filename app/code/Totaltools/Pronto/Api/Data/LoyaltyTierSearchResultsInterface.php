<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Api\Data;

/**
 * Class LoyaltyTierSearchResultsInterface
 *
 * @api
 * @package Totaltools\Pronto\Api\Data
 * <AUTHOR> <<EMAIL>>
 */
interface LoyaltyTierSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{
    /**
     * Get attributes list.
     *
     * @return \Totaltools\Pronto\Api\Data\LoyaltyTierInterface[]
     */
    public function getItems();

    /**
     * Set attributes list.
     *
     * @param \Totaltools\Pronto\Api\Data\LoyaltyTierInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}
