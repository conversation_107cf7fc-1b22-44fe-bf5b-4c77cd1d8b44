<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Api\Data;

/**
 * Interface LoyaltyTierInterface
 *
 * @api
 * @package Totaltools\Pronto\Api\Data
 * <AUTHOR> <<EMAIL>>
 */
interface LoyaltyTierInterface extends \Magento\Framework\Api\CustomAttributesDataInterface
{
    /**#@+
     * Constants defined for keys of  data array
     */
    const TIER_ID = 'tier_id';
    const DESCRIPTION = 'description';
    const SPEND_REQUIRED = 'spend_required';
    const RATE = 'rate';
    const BIRTHDAY_REWARD = 'birthday_reward';
    const UPGRADE_REWARD = 'upgrade_reward';
    /**#@-*/

    /**
     * Tier id
     *
     * @return int|null
     */
    public function getTierId();

    /**
     * Set tier id
     *
     * @param int $tierId
     *
     * @return $this
     */
    public function setTierId($tierId);

    /**
     * Tier description
     *
     * @return string|null
     */
    public function getDescription();

    /**
     * Set tier description
     *
     * @param string $description
     *
     * @return $this
     */
    public function setDescription($description);

    /**
     * Tier spend required
     *
     * @return float|null
     */
    public function getSpendRequired();

    /**
     * Set tier spend required
     *
     * @param float $amount
     *
     * @return $this
     */
    public function setSpendRequired($amount);

    /**
     * Tier rate
     *
     * @return float|null
     */
    public function getRate();

    /**
     * Set tier rate
     *
     * @param float $rate
     *
     * @return $this
     */
    public function setRate($rate);

    /**
     * Tier birthday reward
     *
     * @return float|null
     */
    public function getBirthdayReward();

    /**
     * Set tier birthday reward
     *
     * @param float $amount
     *
     * @return $this
     */
    public function setBirthdayReward($amount);

    /**
     * Tier upgrade reward
     *
     * @return float|null
     */
    public function getUpgradeReward();

    /**
     * Set tier upgrade reward
     *
     * @param float $amount
     *
     * @return $this
     */
    public function setUpgradeReward($amount);

    /**
     * Retrieve existing extension attributes object or create a new one.
     *
     * @return \Totaltools\Pronto\Api\Data\LoyaltyTierExtensionInterface|null
     */
    public function getExtensionAttributes();

    /**
     * Set an extension attributes object.
     *
     * @param \Totaltools\Pronto\Api\Data\LoyaltyTierExtensionInterface $extensionAttributes
     *
     * @return $this
     */
    public function setExtensionAttributes(
        \Totaltools\Pronto\Api\Data\LoyaltyTierExtensionInterface $extensionAttributes
    );
}
