<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Api\Data;

interface CustomerSyncInterface
{

    const ENTITY_ID           = 'entity_id';
    const CUSTOMER_ID         = 'customer_id';
    const STATUS              = 'status';
    const MESSAGE             = 'message';
    const EMAIL_VERIFICATION  = 'change_email_verification';


    /**
     * Get entity_id
     * @return string|null
     */
    public function getEntityId();

    /**
     * Set entity_id
     * @param string $entityId
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setEntityId($entityId);

    /**
     * Get customer_id
     * @return string|null
     */
    public function getCustomerId();

    /**
     * Set customer_id
     * @param string $customerId
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setCustomerId($customerId);

    /**
     * Get status
     * @return string|null
     */
    public function getStatus();

    /**
     * Set status
     * @param string $status
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setStatus($status);

    /**
     * Get message
     * @return string|null
     */
    public function getMessage();

    /**
     * Set message
     * @param string $message
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setMessage($message);

    /**
     * @return mixed
     */
    public function getChangeEmailVerification();

    /**
     * @param $emailVerification
     * @return mixed
     */
    public function setChangeEmailVerification($emailVerification);
}
