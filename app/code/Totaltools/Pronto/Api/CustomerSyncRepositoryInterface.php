<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface CustomerSyncRepositoryInterface
{

    /**
     * Save CustomerSync
     * @param \Totaltools\Pronto\Api\Data\CustomerSyncInterface $customerSync
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Totaltools\Pronto\Api\Data\CustomerSyncInterface $customerSync
    );

    /**
     * Retrieve CustomerSync
     * @param string $entityId
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getById($entityId);

    /**
     * Retrieve CustomerSync matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete CustomerSync
     * @param \Totaltools\Pronto\Api\Data\CustomerSyncInterface $customerSync
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Totaltools\Pronto\Api\Data\CustomerSyncInterface $customerSync
    );

    /**
     * Delete CustomerSync by ID
     * @param string $entityId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($entityId);
}
