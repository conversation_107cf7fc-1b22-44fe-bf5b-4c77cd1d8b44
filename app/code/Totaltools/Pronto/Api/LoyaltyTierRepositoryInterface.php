<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Api;

/**
 * Interface LoyaltyTierRepositoryInterface
 *
 * @api
 * @package Totaltools\Pronto\Api
 * <AUTHOR> <<EMAIL>>
 */
interface LoyaltyTierRepositoryInterface
{
    /**
     * Create tier
     *
     * @param \Totaltools\Pronto\Api\Data\LoyaltyTierInterface $tier
     * @return \Totaltools\Pronto\Api\Data\LoyaltyTierInterface
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\StateException
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    public function save(\Totaltools\Pronto\Api\Data\LoyaltyTierInterface $tier);

    /**
     * Get info about tier by tier description
     *
     * @param string $description
     * @return \Totaltools\Pronto\Api\Data\LoyaltyTierInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function get($description);

    /**
     * Get info about tier by tier id
     *
     * @param int $tierId
     * @return \Totaltools\Pronto\Api\Data\LoyaltyTierInterface
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function getById($tierId);

    /**
     * Delete tier
     *
     * @param \Totaltools\Pronto\Api\Data\LoyaltyTierInterface $tier
     * @return bool Will returned True if deleted
     * @throws \Magento\Framework\Exception\StateException
     */
    public function delete(\Totaltools\Pronto\Api\Data\LoyaltyTierInterface $tier);

    /**
     * @param int $tierId
     * @return bool Will returned True if deleted
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\StateException
     */
    public function deleteById($tierId);
}
