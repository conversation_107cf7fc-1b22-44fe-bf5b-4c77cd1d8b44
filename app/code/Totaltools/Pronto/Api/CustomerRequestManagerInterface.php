<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Pronto\Api;

/**
 * Interface CustomerRequestManagerInterface
 * @package Totaltools\Pronto\Api
 */
interface CustomerRequestManagerInterface
{
    /**
     * @param \Magento\Customer\Model\Customer $customer
     * @param string $type
     * @return array
     */
    public function createMember(\Magento\Customer\Model\Customer $customer, string $type);

    /**
     * Update customer on pronto
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @return bool
     */
    public function updateCustomerOnPronto(\Magento\Customer\Model\Customer $customer);

    /**
     * @param \Magento\Customer\Model\Customer $customer
     * @param bool $force
     * @return void
     */
    public function updateCustomerInfo(\Magento\Customer\Model\Customer $customer, $force = false);

    /**
     * @param \Magento\Customer\Api\Data\CustomerInterface $customerInterface
     * @return array
     */
    public function getProntoCustomer(\Magento\Customer\Api\Data\CustomerInterface $customerInterface);

    /**
     * @param string $loyaltyId
     * @return string
     */
    public function getCustomerAccountCode(string $loyaltyId);

    /**
     * @return string
     */
    public function getGuestAccountCode();

    /**
     * @param \Magento\Customer\Model\Customer $customer
     * @return array
     */
    public function getMemberPoints(\Magento\Customer\Model\Customer $customer);

    /**
     * @param \Magento\Customer\Model\Customer $customer
     * @return bool
     */
    public function isManualVerificationRequired(\Magento\Customer\Model\Customer $customer);

    /**
     * @param int $customerId
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomerModel(int $customerId);

    /**
     * @param $loyaltyId
     * @param $transactionId
     * @return bool
     */
    public function extendExpiryDate($loyaltyId, $transactionId);
}