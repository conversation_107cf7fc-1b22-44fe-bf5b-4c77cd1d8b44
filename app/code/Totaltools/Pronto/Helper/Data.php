<?php
namespace Totaltools\Pronto\Helper;

use Magento\Framework\App\Helper\Context;
use Totaltools\Pronto\Model\Source\LoyaltyStatus;
use Totaltools\Loyalty\Model\CustomerSession as CustomerSession;
use Magento\Customer\Model\Session;
use Magento\Customer\Api\CustomerRepositoryInterface;
use Magento\Framework\DataObjectFactory;
use Totaltools\Reward\Helper\RewardHelper;
use Magento\Reward\Model\Reward;
use Totaltools\Reward\Model\RewardInformationFactory;
use Magento\Store\Model\StoreManagerInterface;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    const XML_POINT = 'point';
    const XML_AMOUNT = 'amount';
    const XML_EXPIRY = 'expiry';
    const XML_LEVEL_TEXT = 'level_text';
    const XML_NEXT_AMOUNT = 'next_amount';
    const XML_Spend_To_Retain = 'spend_to_retain';
    const XML_NEXT_LEVEL_TEXT = 'next_level_text';
    const XML_LOYALTY_STATUS = 'loyalty_status';
    const XML_REFERRAL_CODE = 'referral_code';
    const XML_TIER_EXPIRY = 'tier_expiry';
    

    /**
     * @var LoyaltyStatus
     */
    private $loyaltyStatus;
    
    private $customerSession;

    private $session;

    private $loyaltyTierRepository;

    /**
     * @var CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * @var DataObjectFactory
     */
    private $objectFactory;

    /**
     * @var RewardInformationFactory
     */
    private $rewardInformationFactory;

    /**
     * @var \Totaltools\Reward\Helper\RewardHelper
     */
    private $totRewardHelper;

    /**
     * @var StoreManagerInterface
     */
    private $_storeManager;

    /**
     * Data constructor.
     * @param Context $context
     * @param CustomerSession $customerSession
     * @param LoyaltyTierRepository $context
     * @param LoyaltyStatus $loyaltyTierRepository
     * @param CustomerRepositoryInterface $customerRepository
     * @param DataObjectFactory  $objectFactory
     * @param RewardInformationFactory $rewardInformationFactory
     * @param RewardHelper $totRewardHelper
     */
    public function __construct(
        Context $context,
        CustomerSession $customerSession,
        Session $session,
        \Totaltools\Pronto\Model\LoyaltyTierRepository $loyaltyTierRepository,
        CustomerRepositoryInterface $customerRepository,
        DataObjectFactory $objectFactory,
        LoyaltyStatus $loyaltyStatus,
        RewardInformationFactory $rewardInformationFactory,
        RewardHelper $totRewardHelper,
        StoreManagerInterface $storeManager
    ) {
        $this->loyaltyStatus = $loyaltyStatus;
        $this->customerSession = $customerSession;
        $this->session = $session;
        $this->loyaltyTierRepository = $loyaltyTierRepository;
        $this->customerRepository = $customerRepository;
        $this->objectFactory = $objectFactory;
        $this->totRewardHelper = $totRewardHelper;
       
        $this->_storeManager = $storeManager;
        $this->rewardInformationFactory = $rewardInformationFactory;
        parent::__construct($context);
    }

    public static function arrayToXml ($array, &$xml) {
        foreach ($array as $key => $value) {
            if (is_array($value)) {
                if (is_numeric($key)) {
                    $key = 'i' . $key;
                }
                $subnode = $xml->addChild($key);
                self::arrayToXml($value, $subnode);
            } else {
                $xml->addChild("$key", htmlspecialchars("$value"));
            }
        }
    }

    /**
     * @param $status
     * @return int
     */
    public function getLoyaltyStatus($status)
    {
        if (is_array($status)) {
            return 0;
        }
        $customerStatus = [
            ''  => LoyaltyStatus::ACTIVE,
            'R' => LoyaltyStatus::ACTIVE,
            'F' => LoyaltyStatus::FROZEN,
            'I' => LoyaltyStatus::BLOCKED,
            'RI'=> LoyaltyStatus::BLOCKED,
            'D' => LoyaltyStatus::BLOCKED,
            'X' => LoyaltyStatus::BLOCKED,
        ];
        if (isset($customerStatus[$status])) {
            return $customerStatus[$status];
        }
        return 0;
    }

    /**
     * @param $status
     * @return false|string
     */
    public function getLoyaltyStatusLabel($status)
    {
        $status = $this->getLoyaltyStatus($status);
        return $this->loyaltyStatus->getOptionText($status);
    }

    /**
     * @param Array $prontoData
     * @return string | null
     *
     * */
    public function setLoyaltyData($prontoCustomer)
    {
        $this->session->setData(self::XML_LEVEL_TEXT, $prontoCustomer['TierDescription']);
        $nextLevelId = $prontoCustomer['NextTier'];
        if ($nextLevelId === 'MAX') {
            $nextLevelId = $prontoCustomer['Tier'];
        }
        $this->session->setData(self::XML_NEXT_AMOUNT, number_format($prontoCustomer['SpendToNextTier'], 2));
        $this->session->setData(self::XML_Spend_To_Retain, number_format($prontoCustomer['SpendToRetain'], 2));
        $nextLevelText = $this->loyaltyTierRepository->getById($nextLevelId);
        $this->session->setData(self::XML_NEXT_LEVEL_TEXT, $nextLevelText->getDescription());
        $points = $prontoCustomer['Points'];
        $this->session->setData(self::XML_POINT, $points);
        $this->session->setData(self::XML_AMOUNT, number_format($points / 100, 2));
        $pointExpiry = !empty($prontoCustomer['PointExpiry']) ? str_replace('-', ' ', $prontoCustomer['PointExpiry']) : '';
        $this->session->setData(self::XML_EXPIRY, $pointExpiry);
        $this->session->setData(self::XML_LOYALTY_STATUS, $this->getLoyaltyStatusLabel($prontoCustomer['Status']));
        $this->session->setData(self::XML_REFERRAL_CODE, $prontoCustomer['ReferralCode']);
        $this->session->setData(self::XML_TIER_EXPIRY, $prontoCustomer['MYEnd']);
        return $this;
    }

    public function getCustomerLoyaltyData($customerId)
    {
        $obj = $this->objectFactory->create();
        $customer = $this->customerRepository->getById($customerId);
        $loyalty = $customer->getCustomAttribute('loyalty_id');
        $loyaltyId = $loyalty ? $loyalty->getValue() : '';
        $loyaltyLevel = $customer->getCustomAttribute('loyalty_level');
        $loyaltyLevel = $loyaltyLevel ? $loyaltyLevel->getValue() : '';
        $levelText = $this->loyaltyTierRepository->getById($loyaltyLevel);
        if($loyaltyId && $loyaltyLevel) {
            $obj->setData('customerLoyaltyId', $loyaltyId);
            $obj->setData('customerLoyaltyLevel', $loyaltyLevel);
            $obj->setData('customerLoyaltyLevelText', $levelText->getDescription());
        }
        return $obj;
        
    }

      /**
     * @return bool
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function setZeroBalance($customer, $amount = 0)
    {
        /** @var \Totaltools\Reward\Model\RewardInformation $reward */
        $reward = $this->rewardInformationFactory->create();
        $reward->setBalance($amount);        
        $reward->getLoyaltyStatus(0);        
        /** @var \Magento\Framework\DataObject $data */
        $data = $this->objectFactory->create();
        $websiteId = $this->getWebsiteId();
        $data->setWebsiteId($websiteId)->setAction(Reward::REWARD_ACTION_ADMIN);
        $this->totRewardHelper->setCustomer($customer)->updateRewardBalance($data, $reward);
        
    }

    public function getStore() {
        return $this->_storeManager->getStore();
    }

    public function getWebsiteId() {
        return $this->getStore()->getWebsiteId();
    }  
}
