<?php
namespace Totaltools\Pronto\Helper;

class Config
    extends \Magento\Framework\App\Helper\AbstractHelper
{

    const CONFIG_PATH_STATUS             = 'totaltools_pronto/general/enabled';
    const CONFIG_PATH_SANDBOX            = 'totaltools_pronto/general/sandbox';
    const CONFIG_PATH_DEBUG              = 'totaltools_pronto/general/debug';
    const CONFIG_PATH_GUEST_ACCOUNT_CODE = 'totaltools_pronto/general/guest_account_code';

    const CONFIG_PATH_AUTH_AVENUE_SANDBOX_URL         = 'totaltools_pronto/authentication/avenue/sandbox/url';
    const CONFIG_PATH_AUTH_AVENUE_SANDBOX_USERNAME    = 'totaltools_pronto/authentication/avenue/sandbox/username';
    const CONFIG_PATH_AUTH_AVENUE_SANDBOX_PASSWORD    = 'totaltools_pronto/authentication/avenue/sandbox/password';
    const CONFIG_PATH_AUTH_AVENUE_PRODUCTION_URL      = 'totaltools_pronto/authentication/avenue/production/url';
    const CONFIG_PATH_AUTH_AVENUE_PRODUCTION_USERNAME = 'totaltools_pronto/authentication/avenue/production/username';
    const CONFIG_PATH_AUTH_AVENUE_PRODUCTION_PASSWORD = 'totaltools_pronto/authentication/avenue/production/password';
    const CONFIG_PATH_AUTH_AVENUE_REQUEST_TIMEOUT     = 'totaltools_pronto/authentication/avenue/request_timeout';

    const CONFIG_PATH_AUTH_CONNECT_SANDBOX_URL         = 'totaltools_pronto/authentication/connect/sandbox/url';
    const CONFIG_PATH_AUTH_CONNECT_SANDBOX_USERNAME    = 'totaltools_pronto/authentication/connect/sandbox/username';
    const CONFIG_PATH_AUTH_CONNECT_SANDBOX_PASSWORD    = 'totaltools_pronto/authentication/connect/sandbox/password';
    const CONFIG_PATH_AUTH_CONNECT_PRODUCTION_URL      = 'totaltools_pronto/authentication/connect/production/url';
    const CONFIG_PATH_AUTH_CONNECT_PRODUCTION_USERNAME = 'totaltools_pronto/authentication/connect/production/username';
    const CONFIG_PATH_AUTH_CONNECT_PRODUCTION_PASSWORD = 'totaltools_pronto/authentication/connect/production/password';
    const CONFIG_PATH_AUTH_CONNECT_REQUEST_TIMEOUT     = 'totaltools_pronto/authentication/connect/request_timeout';

    const CONFIG_PATH_TOKEN_EXPIRED_DATE =  'totaltools_pronto/token/expired_date';
    const CONFIG_PATH_TOKEN_KEY =  'totaltools_pronto/token/token_key';


    /**
     * @var boolean
     */
    protected $isSandbox;

    /**
     * @var boolean
     */
    protected $isDebug;

    /**
     * @var string
     */
    protected $avenueUrl;

    /**
     * @var string
     */
    protected $avenueUsername;

    /**
     * @var string
     */
    protected $avenuePassword;

    /**
     * @var string
     */
    protected $connectUrl;

    /**
     * @var string
     */
    protected $connectUsername;

    /**
     * @var string
     */
    protected $connectPassword;

    /**
     * @var string
     */
    protected $guestAccountCode;


    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    protected $encryptor;

    /**
     * @var boolean
     */
    protected $enabled;

    /**
     * @var \Magento\Config\Model\ResourceModel\Config
     */
    protected $resourceConfig;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\TimezoneInterface
     */
    private $timezone;

    /**
     * Config constructor.
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezone
     * @param \Magento\Config\Model\ResourceModel\Config $resourceConfig
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Magento\Framework\Stdlib\DateTime\TimezoneInterface $timezone,
        \Magento\Config\Model\ResourceModel\Config $resourceConfig
    ) {
        parent::__construct($context);
        $this->encryptor = $encryptor;
        $this->resourceConfig = $resourceConfig;
        $this->encryptor = $encryptor;
        $this->timezone = $timezone;
    }


    /**
     * @return boolean
     */
    public function isSandbox () {
        if (is_null($this->isSandbox)) {
            $this->isSandbox = $this->scopeConfig->isSetFlag(self::CONFIG_PATH_SANDBOX);
        }
        return $this->isSandbox;
    }


    public function isEnabled()
    {
        if (is_null($this->enabled)) {
            $this->enabled = $this->scopeConfig->isSetFlag(self::CONFIG_PATH_STATUS);
        }
        return $this->enabled;
    }

    /**
     * @return boolean
     */
    public function isDebug () {
        if (is_null($this->isDebug)) {
            $this->isDebug = $this->scopeConfig->isSetFlag(self::CONFIG_PATH_DEBUG);
        }
        return $this->isDebug;
    }


    /**
     * @return string
     */
    public function getAvenueUrl () {
        if (is_null($this->avenueUrl)) {
            $configPath = ($this->isSandbox()) ?
                self::CONFIG_PATH_AUTH_AVENUE_SANDBOX_URL :
                self::CONFIG_PATH_AUTH_AVENUE_PRODUCTION_URL;
            $this->avenueUrl = $this->scopeConfig->getValue($configPath);
        }
        return $this->avenueUrl;
    }


    /**
     * @return string
     */
    public function getAvenueUsername () {
        if (is_null($this->avenueUsername)) {
            $configPath = ($this->isSandbox()) ?
                self::CONFIG_PATH_AUTH_AVENUE_SANDBOX_USERNAME :
                self::CONFIG_PATH_AUTH_AVENUE_PRODUCTION_USERNAME;
            $this->avenueUsername = $this->scopeConfig->getValue($configPath);
        }
        return $this->avenueUsername;
    }


    /**
     * @return string
     */
    public function getAvenuePassword () {
        if (is_null($this->avenuePassword)) {
            $configPath = ($this->isSandbox()) ?
                self::CONFIG_PATH_AUTH_AVENUE_SANDBOX_PASSWORD :
                self::CONFIG_PATH_AUTH_AVENUE_PRODUCTION_PASSWORD;
            $this->avenuePassword = $this->encryptor->decrypt($this->scopeConfig->getValue($configPath));
        }
        return $this->avenuePassword;
    }


    /**
     * @return string
     */
    public function getConnectUrl () {
        if (is_null($this->connectUrl)) {
            $configPath = ($this->isSandbox()) ?
                self::CONFIG_PATH_AUTH_CONNECT_SANDBOX_URL :
                self::CONFIG_PATH_AUTH_CONNECT_PRODUCTION_URL;
            $this->connectUrl = $this->scopeConfig->getValue($configPath);
        }
        return $this->connectUrl;
    }


    /**
     * @return string
     */
    public function getConnectUsername () {
        if (is_null($this->connectUsername)) {
            $configPath = ($this->isSandbox()) ?
                self::CONFIG_PATH_AUTH_CONNECT_SANDBOX_USERNAME :
                self::CONFIG_PATH_AUTH_CONNECT_PRODUCTION_USERNAME;
            $this->connectUsername = $this->scopeConfig->getValue($configPath);
        }
        return $this->connectUsername;
    }


    /**
     * @return string
     */
    public function getConnectPassword () {
        if (is_null($this->connectPassword)) {
            $configPath = ($this->isSandbox()) ?
                self::CONFIG_PATH_AUTH_CONNECT_SANDBOX_PASSWORD :
                self::CONFIG_PATH_AUTH_CONNECT_PRODUCTION_PASSWORD;
            $this->connectPassword = $this->encryptor->decrypt($this->scopeConfig->getValue($configPath));
        }
        return $this->connectPassword;
    }


    /**
     * @return string
     */
    public function getGuestAccountCode () {
        if (is_null($this->guestAccountCode)) {
            $this->guestAccountCode = $this->scopeConfig->getValue(self::CONFIG_PATH_GUEST_ACCOUNT_CODE);
        }

        return $this->guestAccountCode;
    }

    /**
     * @param string $guestAccountCode
     */
    public function saveGuestAccountCode(string $guestAccountCode)
    {
        $this->resourceConfig->saveConfig(
            self::CONFIG_PATH_GUEST_ACCOUNT_CODE,
            $guestAccountCode,
            \Magento\Framework\App\Config\ScopeConfigInterface::SCOPE_TYPE_DEFAULT,
            0
        );
    }

    /**
     * @param $path
     * @return mixed
     */
    public function getRequestTimeout($path)
    {
        return $this->scopeConfig->getValue($path);
    }

    /**
     * @return mixed
     */
    public function getTokenExpiredDate()
    {
        return $this->scopeConfig->getValue(self::CONFIG_PATH_TOKEN_EXPIRED_DATE);
    }

    /**
     * @return mixed
     */
    public function getTokenKey()
    {
        $token = $this->scopeConfig->getValue(self::CONFIG_PATH_TOKEN_KEY);
        $expiredDate = $this->scopeConfig->getValue(
            self::CONFIG_PATH_TOKEN_EXPIRED_DATE);
        if ($expiredDate) {
            $expiredDateStr = strtotime($expiredDate);
            $now = strtotime($this->timezone->date()->format('d-m-Y H:i:s'));
            if ($now > $expiredDateStr) {
                return false;
            }
        }
        return $token;
    }
}
