<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Model;

use Totaltools\Pronto\Api\Data\CustomerSyncSearchResultsInterfaceFactory;
use Magento\Framework\Reflection\DataObjectProcessor;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Api\SortOrder;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Totaltools\Pronto\Api\Data\CustomerSyncInterfaceFactory;
use Totaltools\Pronto\Model\ResourceModel\CustomerSync as ResourceCustomerSync;
use Totaltools\Pronto\Api\CustomerSyncRepositoryInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\Api\DataObjectHelper;
use Totaltools\Pronto\Model\ResourceModel\CustomerSync\CollectionFactory as CustomerSyncCollectionFactory;

class CustomerSyncRepository implements CustomerSyncRepositoryInterface
{
    /**
     * @var DataObjectHelper
     */
    protected $dataObjectHelper;

    /**
     * @var DataObjectProcessor
     */
    protected $dataObjectProcessor;

    /**
     * @var CustomerSyncInterfaceFactory
     */
    protected $dataCustomerSyncFactory;

    /**
     * @var ResourceCustomerSync
     */
    protected $resource;

    /**
     * @var CustomerSyncFactory
     */
    protected $customerSyncFactory;

    /**
     * @var CustomerSyncSearchResultsInterfaceFactory
     */
    protected $searchResultsFactory;

    /**
     * @var CustomerSyncCollectionFactory
     */
    protected $customerSyncCollectionFactory;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;


    /**
     * @param ResourceCustomerSync $resource
     * @param CustomerSyncFactory $customerSyncFactory
     * @param CustomerSyncInterfaceFactory $dataCustomerSyncFactory
     * @param CustomerSyncCollectionFactory $customerSyncCollectionFactory
     * @param CustomerSyncSearchResultsInterfaceFactory $searchResultsFactory
     * @param DataObjectHelper $dataObjectHelper
     * @param DataObjectProcessor $dataObjectProcessor
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        ResourceCustomerSync $resource,
        CustomerSyncFactory $customerSyncFactory,
        CustomerSyncInterfaceFactory $dataCustomerSyncFactory,
        CustomerSyncCollectionFactory $customerSyncCollectionFactory,
        CustomerSyncSearchResultsInterfaceFactory $searchResultsFactory,
        DataObjectHelper $dataObjectHelper,
        DataObjectProcessor $dataObjectProcessor,
        StoreManagerInterface $storeManager
    ) {
        $this->resource = $resource;
        $this->customerSyncFactory = $customerSyncFactory;
        $this->customerSyncCollectionFactory = $customerSyncCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->dataObjectHelper = $dataObjectHelper;
        $this->dataCustomerSyncFactory = $dataCustomerSyncFactory;
        $this->dataObjectProcessor = $dataObjectProcessor;
        $this->storeManager = $storeManager;
    }

    /**
     * {@inheritdoc}
     */
    public function save(
        \Totaltools\Pronto\Api\Data\CustomerSyncInterface $customerSync
    ) {
        /* if (empty($customerSync->getStoreId())) {
            $storeId = $this->storeManager->getStore()->getId();
            $customerSync->setStoreId($storeId);
        } */
        try {
            $this->resource->save($customerSync);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the Customer Sync: %1',
                $exception->getMessage()
            ));
        }
        return $customerSync;
    }

    /**
     * {@inheritdoc}
     */
    public function getById($customerSyncId)
    {
        $customerSync = $this->customerSyncFactory->create();
        $this->resource->load($customerSync, $customerSyncId);
        if (!$customerSync->getId()) {
            throw new NoSuchEntityException(__('Customer Sync with id "%1" does not exist.', $customerSyncId));
        }
        return $customerSync;
    }

    /**
     * {@inheritdoc}
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->customerSyncCollectionFactory->create();
        foreach ($criteria->getFilterGroups() as $filterGroup) {
            $fields = [];
            $conditions = [];
            foreach ($filterGroup->getFilters() as $filter) {
                if ($filter->getField() === 'store_id') {
                    $collection->addStoreFilter($filter->getValue(), false);
                    continue;
                }
                $fields[] = $filter->getField();
                $condition = $filter->getConditionType() ?: 'eq';
                $conditions[] = [$condition => $filter->getValue()];
            }
            $collection->addFieldToFilter($fields, $conditions);
        }
        
        $sortOrders = $criteria->getSortOrders();
        if ($sortOrders) {
            /** @var SortOrder $sortOrder */
            foreach ($sortOrders as $sortOrder) {
                $collection->addOrder(
                    $sortOrder->getField(),
                    ($sortOrder->getDirection() == SortOrder::SORT_ASC) ? 'ASC' : 'DESC'
                );
            }
        }
        $collection->setCurPage($criteria->getCurrentPage());
        $collection->setPageSize($criteria->getPageSize());
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        $searchResults->setTotalCount($collection->getSize());
        $searchResults->setItems($collection->getItems());
        return $searchResults;
    }

    /**
     * {@inheritdoc}
     */
    public function delete(
        \Totaltools\Pronto\Api\Data\CustomerSyncInterface $customerSync
    ) {
        try {
            $this->resource->delete($customerSync);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Customer Sync: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function deleteById($customerSyncId)
    {
        return $this->delete($this->getById($customerSyncId));
    }
}
