<?php
/**
 * Totaltools Pronto.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
namespace Totaltools\Pronto\Model\Source;

use Magento\Eav\Model\Entity\Attribute\Source\AbstractSource;

/**
 * Class LoyaltyStatus
 *
 * @package Totaltools\Pronto\Model\Source
 * <AUTHOR> <<EMAIL>>
 */
class LoyaltyStatus extends AbstractSource
{
    const ACTIVE = 0;
    const FROZEN = 1;
    const BLOCKED = 2;

    /**
     * Retrieve all options array
     *
     * @return array
     */
    public function getAllOptions()
    {
        if ($this->_options === null) {
            $this->_options = [
                ['label' => 'Active', 'value' => self::ACTIVE],
                ['label' => 'Frozen', 'value' => self::FROZEN],
                ['label' => 'Blocked', 'value' => self::BLOCKED]
            ];
        }
        return $this->_options;
    }

    /**
     * Get a text for option value
     *
     * @param string|int $value
     *
     * @return string|false
     */
    public function getOptionText($value)
    {
        $options = $this->getAllOptions();
        foreach ($options as $option) {
            if ($option['value'] == $value) {
                return $option['label'];
            }
        }
        return 'Active';
    }
}
