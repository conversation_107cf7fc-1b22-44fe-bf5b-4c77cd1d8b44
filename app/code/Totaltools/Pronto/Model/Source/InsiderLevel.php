<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Model\Source;

/**
 * Class InsiderLevel
 *
 * @package Totaltools\Pronto\Model\Source
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 */
class InsiderLevel extends \Magento\Eav\Model\Entity\Attribute\Source\AbstractSource
{
    /**
     * @TODO: Need to fix the final hard-code in Block\Content, to fix it add new column to totaltools_loyalty_tiers
     */
    const INSIDER = 1;
    const INSIDER_PLUS = 2;
    const INSIDER_MAX = 3;

    /**
     * Tier collection factory
     *
     * @var \Totaltools\Pronto\Model\ResourceModel\LoyaltyTier\CollectionFactory
     */
    protected $tierCollectionFactory;

    /**
     * Construct
     *
     * @param \Totaltools\Pronto\Model\ResourceModel\LoyaltyTier\CollectionFactory $tierCollectionFactory
     */
    public function __construct(
        \Totaltools\Pronto\Model\ResourceModel\LoyaltyTier\CollectionFactory $tierCollectionFactory
    ) {
        $this->tierCollectionFactory = $tierCollectionFactory;
    }

    /**
     * Retrieve all options array
     *
     * @return array
     */
    public function getAllOptions()
    {
        if ($this->_options === null) {
            $this->_options = [];
            /** @var \Totaltools\Pronto\Model\LoyaltyTier $tier */
            foreach ($this->getTierCollection() as $tier) {
                $this->_options[] = [
                    'label' => $tier->getDescription(),
                    'value' => $tier->getTierId(),
                ];
            }
            array_unshift($this->_options, ['value' => '', 'label' => __('Please select an insider level.')]);
        }

        return $this->_options;
    }

    /**
     * Retrieve option label in array as key
     *
     * @return array
     */
    public function getOptionValue()
    {
        $_options = array();
        foreach ($this->getAllOptions() as $option) {
            $label                       = $option['label'];
            $_options[$label->getText()] = $option['value'];
        }

        return $_options;
    }

    /**
     * Retrieve option array
     *
     * @return array
     */
    public function getOptionArray()
    {
        $_options = [];
        foreach ($this->getAllOptions() as $option) {
            $_options[$option['value']] = $option['label'];
        }

        return $_options;
    }

    /**
     * Get a text for option value
     *
     * @param string|int $value
     *
     * @return string|false
     */
    public function getOptionText($value)
    {
        $options = $this->getAllOptions();
        foreach ($options as $option) {
            if ($option['value'] == $value) {
                return $option['label'];
            }
        }

        return false;
    }

    /**
     * @param $value
     * @return bool|mixed
     */
    public function getFullOptions($value)
    {
        /** @var \Totaltools\Pronto\Model\LoyaltyTier $option */
        foreach ($this->getTierCollection() as $option) {
            if ($option->getTierId() === $value) {
                return $option->getData();
            }
        }

        return false;
    }

    /**
     * @return \Totaltools\Pronto\Model\ResourceModel\LoyaltyTier\Collection
     */
    public function getTierCollection()
    {
        return $this->tierCollectionFactory->create();
    }
}
