<?php


namespace Totaltools\Pronto\Model\Data;

use Totaltools\Pronto\Api\Data\CustomerSyncInterface;

class CustomerSync extends \Magento\Framework\Api\AbstractExtensibleObject implements CustomerSyncInterface
{

    /**
     * Get entity_id
     * @return string|null
     */
    public function getEntityId()
    {
        return $this->_get(self::ENTITY_ID);
    }

    /**
     * Set entity_id
     * @param string $entityId
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setEntityId($entityId)
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * Retrieve existing extension attributes object or create a new one.
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        return $this->_getExtensionAttributes();
    }

    /**
     * Set an extension attributes object.
     * @param \Totaltools\Pronto\Api\Data\CustomerSyncExtensionInterface $extensionAttributes
     * @return $this
     */
    public function setExtensionAttributes(
        \Totaltools\Pronto\Api\Data\CustomerSyncExtensionInterface $extensionAttributes
    ) {
        return $this->_setExtensionAttributes($extensionAttributes);
    }

    /**
     * Get customer_id
     * @return string|null
     */
    public function getCustomerId()
    {
        return $this->_get(self::CUSTOMER_ID);
    }

    /**
     * Set customer_id
     * @param string $customerId
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setCustomerId($customerId)
    {
        return $this->setData(self::CUSTOMER_ID, $customerId);
    }

    /**
     * Get status
     * @return string|null
     */
    public function getStatus()
    {
        return $this->_get(self::STATUS);
    }

    /**
     * Set status
     * @param string $status
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get message
     * @return string|null
     */
    public function getMessage()
    {
        return $this->_get(self::MESSAGE);
    }

    /**
     * Set message
     * @param string $message
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setMessage($message)
    {
        return $this->setData(self::MESSAGE, $message);
    }

    /**
     * Get message
     * @return string|null
     */
    public function getChangeEmailVerification()
    {
        return $this->_get(self::EMAIL_VERIFICATION);
    }

    /**
     * Set message
     * @param string $message
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setChangeEmailVerification($emailverification)
    {
        return $this->setData(self::EMAIL_VERIFICATION, $emailverification);
    }

}
