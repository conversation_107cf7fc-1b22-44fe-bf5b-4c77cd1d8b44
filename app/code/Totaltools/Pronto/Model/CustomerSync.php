<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Model;

use Totaltools\Pronto\Api\Data\CustomerSyncInterface;

class CustomerSync extends \Magento\Framework\Model\AbstractModel implements CustomerSyncInterface
{

    /**
     * @var string
     */
    protected $_eventPrefix = 'totaltools_pronto_customersync';

    /**
     * @return void
     */
    protected function _construct()
    {
        $this->_init('Totaltools\Pronto\Model\ResourceModel\CustomerSync');
    }

    /**
     * Get entity_id
     * @return string
     */
    public function getEntityId()
    {
        return $this->getData(self::ENTITY_ID);
    }

    /**
     * Set entity_id
     * @param string $entityId
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setEntityId($entityId)
    {
        return $this->setData(self::ENTITY_ID, $entityId);
    }

    /**
     * Get customer_id
     * @return string
     */
    public function getCustomerId()
    {
        return $this->getData(self::CUSTOMER_ID);
    }

    /**
     * Set customer_id
     * @param string $customerId
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setCustomerId($customerId)
    {
        return $this->setData(self::CUSTOMER_ID, $customerId);
    }

    /**
     * Get status
     * @return string
     */
    public function getStatus()
    {
        return $this->getData(self::STATUS);
    }

    /**
     * Set status
     * @param string $status
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setStatus($status)
    {
        return $this->setData(self::STATUS, $status);
    }

    /**
     * Get message
     * @return string
     */
    public function getMessage()
    {
        return $this->getData(self::MESSAGE);
    }

    /**
     * Set message
     * @param string $message
     * @return \Totaltools\Pronto\Api\Data\CustomerSyncInterface
     */
    public function setMessage($message)
    {
        return $this->setData(self::MESSAGE, $message);
    }

    /**
     * @return mixed
     */
    public function getChangeEmailVerification()
    {
        return $this->getData(self::EMAIL_VERIFICATION);
    }

    /**
     * @param $emailVerification
     * @return mixed|CustomerSync
     */
    public function setChangeEmailVerification($emailVerification)
    {
        return $this->setData(self::EMAIL_VERIFICATION, $emailVerification);
    }
}
