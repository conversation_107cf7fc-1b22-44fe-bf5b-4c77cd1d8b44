<?php
/**
 * <AUTHOR> Nguyen (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Pronto\Model;

class InstoreOrder extends \Magento\Framework\Model\AbstractModel
{
    protected $orderItems;

    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    protected $customerSession;

    /**
     * @param \Totaltools\Pronto\Model\ResourceModel\InstoreOrderItem\CollectionFactory $collectionFactory
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Totaltools\Loyalty\Model\CustomerSession $customerSession
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb $resourceCollection
     * @param array $data
     */
    public function __construct(
        \Totaltools\Pronto\Model\ResourceModel\InstoreOrderItem\CollectionFactory $collectionFactory,
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Totaltools\Loyalty\Model\CustomerSession $customerSession,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = []
    ) {

        $this->customerSession = $customerSession;
        parent::__construct($context, $registry, $resource, $resourceCollection, $data);
        $this->orderItems = $collectionFactory;
    }

    /**
     * Initialize instore order model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('Totaltools\Pronto\Model\ResourceModel\InstoreOrder');
    }

    /**
     * load
     * @param string $orderNo
     * @return \Totaltools\Pronto\Model\InstoreOrder
     */
    public function loadByOrderNo($orderNo)
    {
        // We can have multiple orders with same order_no (placed at different Total Tools Stores by different customers)
        // So we have to check if the order actually belongs to the customer who is logged in.
        $customerId = $this->customerSession->getCustomerId();
        $order = $this->getCollection()
                        ->addFieldToFilter('customer_id', $customerId)
                        ->addFieldToFilter('order_no', $orderNo)
            ->getFirstItem();
        return $order;
    }

    /**
     * retrieve instore item collection
     *
     * @return \Totaltools\Pronto\Model\ResourceModel\InstoreOrderItem\CollectionFactory
     */
    public function getOrderItems()
    {
        return $this->orderItems->create()->addFieldToFilter('order_id', $this->getId());
    }

    /**
     * get order total, include shipping fee
     *
     * @return float
     */
    public function getOrderTotal()
    {
        $total = 0;
        $items = $this->getOrderItems();
        foreach ($items as $item) {
            $total += floatval($item->getLineValue());
        }
        return $total;
    }

    /**
     * get order total exclude shipping fee
     *
     * @return float
     */
    public function getSubtotal()
    {
        $subTotal = 0;
        $items = $this->getOrderItems()->addFieldToFilter('stock_code', ['neq' => 'Charge']);
        foreach ($items as $item) {
            $subTotal += floatval($item->getLineValue());
        }

        return $subTotal;
    }

    /**
     * get order tax
     *
     * @return float
     */
    public function getOrderTax()
    {
        $total = 0;
        $items = $this->getOrderItems();
        foreach ($items as $item) {
            $total += floatval($item->getLineTax());
        }
        return $total;
    }

    /**
     * @param int $customerId
     * @return mixed
     */
    public function getCustomerInvoiceLastTransDate(int $customerId)
    {
        $customerInStoreOrder = $this->getCollection()
            ->addFieldToFilter('customer_id', $customerId)
            ->setOrder('trans_date','DESC')
            ->getFirstItem();
        return $customerInStoreOrder;
    }

}
