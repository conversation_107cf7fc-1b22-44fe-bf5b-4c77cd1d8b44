<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Model;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Model\AbstractExtensibleModel;
use Totaltools\Pronto\Api\Data\LoyaltyTierInterface;

/**
 * Loyalty tier model
 * NOTICE: PLEASE DONT USE ME LIKE $model->load($id), USE REPOSITORY INSTEAD !!!!
 *
 * @package Totaltools\Pronto\Model
 * <AUTHOR> <<EMAIL>>
 */
class LoyaltyTier extends AbstractExtensibleModel implements LoyaltyTierInterface
{
    /**
     * Entity code.
     * Can be used as part of method name for entity processing
     */
    const ENTITY = 'totaltools_insider_tier';

    /**
     * Tier cache tag
     */
    const CACHE_TAG = 'totaltools_insider_tier';

    /**
     * @var string
     */
    protected $_cacheTag = self::CACHE_TAG;

    /**
     * @var string
     */
    protected $_eventPrefix = 'totaltools_insider_tier';

    /**
     * @var string
     */
    protected $_eventObject = 'tier';

    /**
     * Initialize resources
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('Totaltools\Pronto\Model\ResourceModel\LoyaltyTier');
    }

    /**
     * Identifier getter
     *
     * @return int
     */
    public function getId()
    {
        return $this->_getData('entity_id');
    }

    /**
     * Set tier Id
     *
     * @param int $value
     * @return $this
     */
    public function setId($value)
    {
        return $this->setData('entity_id', $value);
    }

    /**
     * {@inheritdoc}
     */
    public function getTierId()
    {
        return $this->_getData(self::TIER_ID);
    }

    /**
     * {@inheritdoc}
     */
    public function setTierId($tierId)
    {
        return $this->setData(self::TIER_ID, $tierId);
    }

    /**
     * {@inheritdoc}
     */
    public function getDescription()
    {
        return $this->_getData(self::DESCRIPTION);
    }

    /**
     * {@inheritdoc}
     */
    public function setDescription($description)
    {
        return $this->setData(self::DESCRIPTION, $description);
    }

    /**
     * {@inheritdoc}
     */
    public function getSpendRequired()
    {
        return $this->_getData(self::SPEND_REQUIRED);
    }

    /**
     * {@inheritdoc}
     */
    public function setSpendRequired($amount)
    {
        return $this->setData(self::SPEND_REQUIRED, $amount);
    }

    /**
     * {@inheritdoc}
     */
    public function getRate()
    {
        return $this->_getData(self::RATE);
    }

    /**
     * {@inheritdoc}
     */
    public function setRate($rate)
    {
        return $this->setData(self::RATE, $rate);
    }

    /**
     * {@inheritdoc}
     */
    public function getBirthdayReward()
    {
        return $this->_getData(self::BIRTHDAY_REWARD);
    }

    /**
     * {@inheritdoc}
     */
    public function setBirthdayReward($amount)
    {
        return $this->setData(self::BIRTHDAY_REWARD, $amount);
    }

    /**
     * {@inheritdoc}
     */
    public function getUpgradeReward()
    {
        return $this->_getData(self::UPGRADE_REWARD);
    }

    /**
     * {@inheritdoc}
     */
    public function setUpgradeReward($amount)
    {
        return $this->setData(self::UPGRADE_REWARD, $amount);
    }

    /**
     * {@inheritdoc}
     *
     * @codeCoverageIgnoreStart
     * @return \Totaltools\Pronto\Api\Data\LoyaltyTierExtensionInterface|null
     */
    public function getExtensionAttributes()
    {
        $extensionAttributes = $this->_getExtensionAttributes();
        if (!$extensionAttributes) {
            return $this->extensionAttributesFactory->create('Totaltools\Pronto\Api\Data\LoyaltyTierInterface');
        }

        return $extensionAttributes;
    }

    /**
     * {@inheritdoc}
     *
     * @param \Totaltools\Pronto\Api\Data\LoyaltyTierExtensionInterface $extensionAttributes
     *
     * @return $this
     */
    public function setExtensionAttributes(
        \Totaltools\Pronto\Api\Data\LoyaltyTierExtensionInterface $extensionAttributes
    ) {
        return $this->_setExtensionAttributes($extensionAttributes);
    }
    //@codeCoverageIgnoreEnd

    /**
     * {@inheritdoc}
     */
    public function beforeSave()
    {
        if ($this->getTierId() === null) {
            throw new LocalizedException(__('Tier ID must not be empty'));
        }
        return parent::beforeSave();
    }
}
