<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Model\Executor;

use Totaltools\Pronto\Api\Data\LoyaltyTierInterface;

/**
 * Class InsiderTier
 *
 * @package Totaltools\Pronto\Model\Executor
 * <AUTHOR> <<EMAIL>>
 */
class InsiderTier
{
    /**
     * @var \Totaltools\Pronto\Model\Api\Connect
     */
    private $apiClient;

    /**
     * @var \Totaltools\Pronto\Model\LoyaltyTierFactory
     */
    private $tierFactory;

    /**
     * @var \Totaltools\Pronto\Api\LoyaltyTierRepositoryInterface
     */
    private $tierRepository;

    public function __construct(
        \Totaltools\Pronto\Model\Api\Connect $apiClient,
        \Totaltools\Pronto\Model\LoyaltyTierFactory $tierFactory,
        \Totaltools\Pronto\Api\LoyaltyTierRepositoryInterface $tierRepository
    ) {
        $this->apiClient = $apiClient;
        $this->tierFactory = $tierFactory;
        $this->tierRepository = $tierRepository;
    }

    /**
     * @return void
     */
    public function update()
    {
        $response = $this->apiClient->setPath('GetTiers')->setRootNode('GetTiersRequest')->call('array');
        if (isset($response['Tiers']['Tier'])) {
            foreach ($response['Tiers']['Tier'] as $requestedTier) {
                /** @var \Totaltools\Pronto\Model\LoyaltyTier $tier */
                $tier = $this->tierFactory->create();
                $tier->setData([
                    LoyaltyTierInterface::TIER_ID         => (int)$requestedTier['ID'],
                    LoyaltyTierInterface::DESCRIPTION     => trim($requestedTier['Description']),
                    LoyaltyTierInterface::SPEND_REQUIRED  => (float)$requestedTier['SpendRequired'],
                    LoyaltyTierInterface::RATE            => (float)$requestedTier['Rate'],
                    LoyaltyTierInterface::BIRTHDAY_REWARD => (float)$requestedTier['BirthdayReward'],
                    LoyaltyTierInterface::UPGRADE_REWARD  => (float)$requestedTier['UpgradeReward']
                ]);
                $this->tierRepository->save($tier);
            }
        }
    }
}
