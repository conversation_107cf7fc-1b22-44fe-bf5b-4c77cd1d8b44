<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Model;

use \Magento\Framework\Api\SearchCriteriaBuilder;
use \Totaltools\Pronto\Api\CustomerSyncRepositoryInterface;
use \Magento\Customer\Model\CustomerFactory;
use \Totaltools\Pronto\Model\Customer\MassPush;

class PushToPronto
{
    /**
     * @var SearchCriteriaBuilder
     */
    protected $searchCriteriaBuilder;

    /**
     * @var CustomerSyncRepositoryInterface
     */
    protected $customerSyncRepository;

    /**
     * @var CustomerFactory
     */
    protected $customerFactory;

    /**
     * @var MassPush
     */
    protected $massPush;

    /**
     * @return void
     */
    public function __construct(
        SearchCriteriaBuilder $searchCriteriaBuilder,
        CustomerSyncRepositoryInterface $customerSyncRepository,
        CustomerFactory $customerFactory,
        MassPush $massPush
    )
    {
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->customerSyncRepository = $customerSyncRepository;
        $this->customerFactory = $customerFactory;
        $this->massPush = $massPush;
    }

    /**
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function pushCustomers()
    {
        $searchCriteria = $this->searchCriteriaBuilder->addFilter(
            'status',
            'Pending',
            'eq'
        )->setPageSize(50)->setCurrentPage(1)->create();
        $collection = $this->customerSyncRepository->getList($searchCriteria);
        foreach ($collection->getItems() as $item) {
            $customer = $this->customerFactory->create()->load($item->getCustomerId());
            $type = 'Both';
            $status = $this->massPush->getRegisteredAccountCode($customer, $type);
            if(!empty($status) && isset($status['status']) && $status['status'] === 'Success') {
                $item->setMessage($status['message'].' - '.$status['account_code']);
                $item->setStatus($status['status']);
                $this->customerSyncRepository->save($item);
                if($item->getChangeEmailVerification() == 1) {
                    $customer->setEmailVerificationStatus('manual_verification_required');
                    $customer->save();
                }
            } else if (!empty($status) && isset($status['status']) && $status['status'] === 'Error') {
                $item->setMessage($status['message']);
                $item->setStatus('Error');
                $this->customerSyncRepository->save($item);
            }
        }
    }

}