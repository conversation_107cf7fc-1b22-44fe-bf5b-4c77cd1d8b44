<?php
/**
 * Total Tools Pronto.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Pronto\Model\Request;

use Magento\Customer\Model\Customer;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Totaltools\Pronto\Model\Api\Connect as ConnectApi;
use Totaltools\Pronto\Model\InstoreOrder;
use Totaltools\Pronto\Model\InstoreOrderItemFactory;
use Totaltools\Pronto\Model\ResourceModel\InstoreOrder\CollectionFactory as InstoreOrderCollectionFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResource;
use Magento\Sales\Api\Data\OrderInterfaceFactory;

/**
 * Pronto Customer data updates
 */
class OrderHistory
{
    /**
     * Pronto Connect API
     *
     * @var ConnectApi
     */
    protected $connect;

    /**
     * Instore order collection factory
     *
     * @var InstoreOrderCollectionFactory
     */
    protected $instoreOrderCollectionFactory;

    /**
     * Instore order item factory
     *
     * @var InstoreOrderItemFactory
     */
    protected $instoreOrderItemFactory;

    /**
     * @var CustomerResource
     */
    protected $customerResource;

    /**
     * @var TimezoneInterface
     */
    private $timezone;

    /**
     * @var \Magento\Sales\Api\Data\OrderInterfaceFactory
     */
    private $orderFactory;

    /**
     * @var InstoreOrder
     */
    protected $inStoreOrderModel;

    /**
     * OrderHistory constructor.
     * @param ConnectApi $connect
     * @param InstoreOrderCollectionFactory $instoreOrderCollectionFactory
     * @param InstoreOrderItemFactory $instoreOrderItemFactory
     * @param CustomerResource $customerResource
     * @param TimezoneInterface $localeDate
     * @param OrderInterfaceFactory $orderFactory
     */
    public function __construct(
        ConnectApi $connect,
        InstoreOrderCollectionFactory $instoreOrderCollectionFactory,
        InstoreOrderItemFactory $instoreOrderItemFactory,
        CustomerResource $customerResource,
        TimezoneInterface $localeDate,
        OrderInterfaceFactory $orderFactory,
        InstoreOrder $instoreOrder
    )
    {
        $this->connect = $connect;
        $this->instoreOrderCollectionFactory = $instoreOrderCollectionFactory;
        $this->instoreOrderItemFactory = $instoreOrderItemFactory;
        $this->customerResource = $customerResource;
        $this->timezone = $localeDate;
        $this->orderFactory = $orderFactory;
        $this->inStoreOrderModel = $instoreOrder;
    }

    /**
     * Update customer's instore order information
     *
     * @param Customer $customer
     *
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function updateInstoreOrders(Customer $customer)
    {
        try {
            $instoreOrder = $this->inStoreOrderModel->getCustomerInvoiceLastTransDate($customer->getId());
            if ($transDate = $instoreOrder->getTransDate()) {
                $lastUpdated = $this->timezone
                    ->date(new \DateTime($transDate))
                    ->format('d/m/Y');
            } else {
                $lastUpdated = '';
            }
        } catch (\Exception $exception) {
            $lastUpdated = '';
        }
        $loyaltyId = $customer->getData('loyalty_id');
        $request = [
            'Parameters' => [
                'LoyaltyID' => $loyaltyId,
            ],
            'Filters' => [
                'Date' => [
                    'Range' => [
                        'From' => $lastUpdated,
                        'To' => $this->timezone->date()->format('d/m/Y')
                    ]
                ]
            ]
        ];

        $this->connect
            ->setPath('GetMemberTransactions')
            ->setRootNode('GetMemberTransactionRequest')
            ->setBody($request);
        try {
            $response = $this->connect->call('array');

            if (isset($response['LoyaltyTransactions']['LoyaltyTransaction'])) {
                $transactions = $response['LoyaltyTransactions']['LoyaltyTransaction'];
                $transactions = array_key_exists('TransactionHeader', $transactions)
                    ? [$transactions]
                    : $transactions;
                foreach ($transactions as $transaction) {
                    $orderNo = $transaction['TransactionHeader']['OrderNo'];
                    $instoreOrder = $this->instoreOrderCollectionFactory->create()
                        ->addFieldToFilter('order_no', $orderNo)
                        ->addFieldToFilter('customer_id', $customer->getId())
                        ->getFirstItem();
                    // save instore order in the case order is not exist.
                    if (!$instoreOrder->getId()) {
                        $storeName = $transaction['TransactionHeader']['StoreDetails']['StoreName'] ?? null;
                        $storeAddress1 = $transaction['TransactionHeader']['StoreDetails']['Address1'] ?? null;
                        $storeAddress2 = $transaction['TransactionHeader']['StoreDetails']['Address2'] ?? null;
                        $storeAddress3 = $transaction['TransactionHeader']['StoreDetails']['Address3'] ?? null;
                        $storeAddress4 = $transaction['TransactionHeader']['StoreDetails']['Address4'] ?? null;
                        $storeAddress5 = $transaction['TransactionHeader']['StoreDetails']['Address5'] ?? null;
                        $storeAddress6 = $transaction['TransactionHeader']['StoreDetails']['Address6'] ?? null;
                        $storePhone = $transaction['TransactionHeader']['StoreDetails']['Phone'] ?? null;
                        $storePostCode = $transaction['TransactionHeader']['StoreDetails']['PostCode'] ?? null;
                        $storeEmail = $transaction['TransactionHeader']['StoreDetails']['Email'] ?? null;
                        $storeAbn = $transaction['TransactionHeader']['StoreDetails']['ABN'] ?? null;
                        $company = $transaction['TransactionHeader']['Company'] ?? null;
                        $firstName = $transaction['TransactionHeader']['FirstName'] ?? null;
                        $lastName = $transaction['TransactionHeader']['LastName'] ?? null;
                        $onlineOrderId = $transaction['TransactionHeader']['OnlineOrderNo'] ?? '';
                        $payments = $transaction['TransactionHeader']['Payments']['Payment'] ?? [];
                        $paymentMethods = [];
                        if (isset($payments['Tender'])) {
                            $paymentMethods[] = $payments['Tender'];
                        } else {
                            foreach ($payments as $payment) {
                                $paymentMethods[] = $payment['Tender'];
                            }
                        }

                        if (empty($paymentMethods) && $onlineOrderId) {
                            /** @var \Magento\Sales\Model\Order @order */
                            $order = $this->orderFactory->create()->loadByIncrementId($onlineOrderId);
                            $orderPayment = $order->getPayment();
                            if ($orderPayment) {
                                $method = $orderPayment->getMethodInstance();
                                $paymentMethods[] = $method->getTitle();
                            }
                        }
                        $orderParams = [
                            'customer_id' => $customer->getId(),
                            'order_no' => $orderNo,
                            'seq_no' => $transaction['TransactionHeader']['SeqNo'] ?? '',
                            'trans_date' => $transaction['TransactionHeader']['TransDate'] ?? '',
                            'user_name' => $transaction['TransactionHeader']['UserName'] ?? '',
                            'invoice_number' => $transaction['TransactionHeader']['InvoiceNumber'] ? trim($transaction['TransactionHeader']['InvoiceNumber']) : '',
                            'store_name' => empty($storeName) ? '' : $storeName,
                            'address_1' => empty($storeAddress1) ? '' : $storeAddress1,
                            'address_2' => empty($storeAddress2) ? '' : $storeAddress2,
                            'address_3' => empty($storeAddress3) ? '' : $storeAddress3,
                            'address_4' => empty($storeAddress4) ? '' : $storeAddress4,
                            'address_5' => empty($storeAddress5) ? '' : $storeAddress5,
                            'address_6' => empty($storeAddress6) ? '' : $storeAddress6,
                            'phone' => empty($storePhone) ? '' : $storePhone,
                            'post_code' => empty($storePostCode) ? '' : $storePostCode,
                            'email' => empty($storeEmail) ? '' : $storeEmail,
                            'abn' => empty($storeAbn) ? '' : $storeAbn,
                            'insider_id' => $loyaltyId,
                            'company' => empty($company) ? '' : $company,
                            'first_name' => empty($firstName) ? '' : $firstName,
                            'last_name' => empty($lastName) ? '' : $lastName,
                            'payment_methods' => $paymentMethods ? implode(', ', $paymentMethods) : '',
                            'online_order_id' => $onlineOrderId,
                        ];

                        $instoreOrder->setData($orderParams);
                        $instoreOrder->save();

                        $fields = $this->matchingInstoreOrderItemFields();
                        $transactionLine = []; // for the case there is one item on transaction
                        foreach ($transaction['TransactionLine'] as $key => $line) {
                            $itemParams = [];
                            $itemParams['order_id'] = $instoreOrder->getId();
                            if (is_array($line)) {
                                foreach ($line as $field => $value) {
                                    if ($value === []) {
                                        $value = '';
                                    }
                                    if (isset($fields[$field])) {
                                        $itemParams[$fields[$field]] = $value;
                                    }
                                }
                                $instoreOrderItem = $this->instoreOrderItemFactory->create();
                                $instoreOrderItem->setData($itemParams);
                                try {
                                    $instoreOrderItem->save();
                                } catch (\Exception $e) {
                                    throw new \Magento\Framework\Exception\LocalizedException(
                                        __('Error occurs on saving instore order "%1"', $e->getMessage())
                                    );
                                }

                            } else {
                                if (isset($fields[$key])) {
                                    $transactionLine[$fields[$key]] = $line;
                                }
                            }
                        }

                        if (sizeof($transactionLine) > 0) {
                            $transactionLine['order_id'] = $instoreOrder->getId();
                            $instoreOrderItem = $this->instoreOrderItemFactory->create();
                            $instoreOrderItem->setData($transactionLine);
                            try {
                                $instoreOrderItem->save();
                            } catch (\Exception $e) {
                                throw new \Magento\Framework\Exception\LocalizedException(
                                    __('Error occurs on saving instore order item "%1"', $e->getMessage())
                                );
                            }
                        }
                    }
                }
            } else {
                // @todo: log message that no orders were found
            }

        } catch (\Exception $e) {
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Error occurs on calling GetMemberTransactions "%1"', $e->getMessage())
            );
        }
    }

    public function updateCustomerData(Customer $customer, $allInvoices = false)
    {
        $transactions = $this->getMemberTransactions($customer, $allInvoices);
        if (is_array($transactions)) {
            foreach ($transactions as $transaction) {
                $orderNo = $transaction['TransactionHeader']['OrderNo'];
                $instoreOrder = $this->instoreOrderCollectionFactory->create()
                    ->addFieldToFilter('order_no', $orderNo)
                    ->addFieldToFilter('customer_id', $customer->getId())
                    ->getFirstItem();
                // save instore order in the case order is not exist.
                if (!$instoreOrder->getId()) {
                    $orderParams = $this->prepareInstantOrder($transaction, $customer, $orderNo);
                    $instoreOrder->setData($orderParams);
                    $instoreOrder->save();
                    $fields = $this->matchingInstoreOrderItemFields();
                    $transactionLine = []; // for the case there is one item on transaction
                    foreach ($transaction['TransactionLine'] as $key => $line) {
                        $itemParams = [];
                        $itemParams['order_id'] = $instoreOrder->getId();
                        if (is_array($line)) {
                            foreach ($line as $field => $value) {
                                if ($value === []) {
                                    $value = '';
                                }
                                if (isset($fields[$field])) {
                                    $itemParams[$fields[$field]] = $value;
                                }
                            }
                            $instoreOrderItem = $this->instoreOrderItemFactory->create();
                            $instoreOrderItem->setData($itemParams);
                            try {
                                $instoreOrderItem->save();
                            } catch (\Exception $e) {
                                throw new \Magento\Framework\Exception\LocalizedException(
                                    __('Error occurs on saving instore order "%1"', $e->getMessage())
                                );
                            }

                        } else {
                            if (isset($fields[$key])) {
                                $transactionLine[$fields[$key]] = $line;
                            }
                        }
                    }
                    if (sizeof($transactionLine) > 0) {
                        $transactionLine['order_id'] = $instoreOrder->getId();
                        $instoreOrderItem = $this->instoreOrderItemFactory->create();
                        $instoreOrderItem->setData($transactionLine);
                        try {
                            $instoreOrderItem->save();
                        } catch (\Exception $e) {
                            throw new \Magento\Framework\Exception\LocalizedException(
                                __('Error occurs on saving instore order item "%1"', $e->getMessage())
                            );
                        }
                    }
                }
            }
        }

    }

    public function getMemberTransactions(Customer $customer, $allInvoices = false)
    {
        $lastUpdated = '';
        if(!$allInvoices) {
            try {
                $instoreOrder = $this->inStoreOrderModel->getCustomerInvoiceLastTransDate($customer->getId());
                if ($transDate = $instoreOrder->getTransDate()) {
                    $lastUpdated = $this->timezone
                        ->date(new \DateTime($transDate))
                        ->format('d/m/Y');
                }
            } catch (\Exception $exception) {
                $lastUpdated = '';
            }
        }
        $loyaltyId = $customer->getData('loyalty_id');
        $request = [
            'Parameters' => [
                'LoyaltyID' => $loyaltyId,
            ],
            'Filters' => [
                'Date' => [
                    'Range' => [
                        'From' => $lastUpdated,
                        'To' => $this->timezone->date()->format('d/m/Y')
                    ]
                ]
            ]
        ];

        $this->connect
            ->setPath('GetMemberTransactions')
            ->setRootNode('GetMemberTransactionRequest')
            ->setBody($request);
        try {
            $response = $this->connect->call('array');
            if (isset($response['LoyaltyTransactions']['LoyaltyTransaction'])) {
                $transactions = $response['LoyaltyTransactions']['LoyaltyTransaction'];
                $transactions = array_key_exists('TransactionHeader', $transactions)
                    ? [$transactions]
                    : $transactions;
                return $transactions;
            } else {
                return false;
                // @todo: log message that no orders were found
            }
        } catch (\Exception $e) {
            throw new \Magento\Framework\Exception\LocalizedException(
                __('Error occurs on calling GetMemberTransactions "%1"', $e->getMessage())
            );
        }
    }

    public function prepareInstantOrder($transaction, $customer)
    {
        $storeName = $transaction['TransactionHeader']['StoreDetails']['StoreName'] ?? null;
        $storeAddress1 = $transaction['TransactionHeader']['StoreDetails']['Address1'] ?? null;
        $storeAddress2 = $transaction['TransactionHeader']['StoreDetails']['Address2'] ?? null;
        $storeAddress3 = $transaction['TransactionHeader']['StoreDetails']['Address3'] ?? null;
        $storeAddress4 = $transaction['TransactionHeader']['StoreDetails']['Address4'] ?? null;
        $storeAddress5 = $transaction['TransactionHeader']['StoreDetails']['Address5'] ?? null;
        $storeAddress6 = $transaction['TransactionHeader']['StoreDetails']['Address6'] ?? null;
        $storePhone = $transaction['TransactionHeader']['StoreDetails']['Phone'] ?? null;
        $storePostCode = $transaction['TransactionHeader']['StoreDetails']['PostCode'] ?? null;
        $storeEmail = $transaction['TransactionHeader']['StoreDetails']['Email'] ?? null;
        $storeAbn = $transaction['TransactionHeader']['StoreDetails']['ABN'] ?? null;
        $company = $transaction['TransactionHeader']['Company'] ?? null;
        $firstName = $transaction['TransactionHeader']['FirstName'] ?? null;
        $lastName = $transaction['TransactionHeader']['LastName'] ?? null;
        $onlineOrderId = !empty($transaction['TransactionHeader']['OnlineOrderNo']) ? $transaction['TransactionHeader']['OnlineOrderNo'] : '';
        $payments = $transaction['TransactionHeader']['Payments']['Payment'] ?? [];
        $paymentMethods = [];
        if (isset($payments['Tender'])) {
            $paymentMethods[] = $payments['Tender'];
        } else {
            foreach ($payments as $payment) {
                $paymentMethods[] = $payment['Tender'];
            }
        }

        if (empty($paymentMethods) && $onlineOrderId) {
            /** @var \Magento\Sales\Model\Order @order */
            $order = $this->orderFactory->create()->loadByIncrementId($onlineOrderId);
            $orderPayment = $order->getPayment();
            if ($orderPayment) {
                $method = $orderPayment->getMethodInstance();
                $paymentMethods[] = $method->getTitle();
            }
        }
        $orderParams = [
            'customer_id' => $customer->getId(),
            'order_no' => $transaction['TransactionHeader']['OrderNo'],
            'seq_no' => !empty($transaction['TransactionHeader']['SeqNo']) ?$transaction['TransactionHeader']['SeqNo']: '',
            'trans_date' => !empty($transaction['TransactionHeader']['TransDate']) ? $transaction['TransactionHeader']['TransDate'] : '',
            'user_name' => !empty($transaction['TransactionHeader']['UserName']) ?$transaction['TransactionHeader']['UserName']: '',
            'invoice_number' => !empty($transaction['TransactionHeader']['InvoiceNumber']) ? trim($transaction['TransactionHeader']['InvoiceNumber']) : '',
            'store_name' => empty($storeName) ? '' : $storeName,
            'address_1' => empty($storeAddress1) ? '' : $storeAddress1,
            'address_2' => empty($storeAddress2) ? '' : $storeAddress2,
            'address_3' => empty($storeAddress3) ? '' : $storeAddress3,
            'address_4' => empty($storeAddress4) ? '' : $storeAddress4,
            'address_5' => empty($storeAddress5) ? '' : $storeAddress5,
            'address_6' => empty($storeAddress6) ? '' : $storeAddress6,
            'phone' => empty($storePhone) ? '' : $storePhone,
            'post_code' => empty($storePostCode) ? '' : $storePostCode,
            'email' => empty($storeEmail) ? '' : $storeEmail,
            'abn' => empty($storeAbn) ? '' : $storeAbn,
            'insider_id' => $customer->getData('loyalty_id'),
            'company' => empty($company) ? '' : $company,
            'first_name' => empty($firstName) ? '' : $firstName,
            'last_name' => empty($lastName) ? '' : $lastName,
            'payment_methods' => $paymentMethods ? implode(', ', $paymentMethods) : '',
            'online_order_id' => $onlineOrderId,
        ];

        return $orderParams;
    }

    public function getInstoreOrders(Customer $customer)
    {
        $transactions = $this->getMemberTransactions($customer);
        $transactions = array_key_exists('TransactionHeader', $transactions)
            ? [$transactions]
            : $transactions;
        $orderTransaction = '';
        foreach ($transactions as $transaction) {
            $orderTransaction[] = $this->prepareInstantOrder($transaction);
        }
    }

    /**
     * Get instore order item field mapping
     *
     * @return string[]
     */
    public function matchingInstoreOrderItemFields()
    {
        return [
            'LineNo' => 'line_no',
            'StockCode' => 'stock_code',
            'StockDescription' => 'stock_description',
            'LineValue' => 'line_value',
            'LineTax' => 'line_tax',
            'Qty' => 'qty'
        ];
    }

    /**
     * Get instore order field mapping
     *
     * @return string[]
     */
    public function matchingInstoreOrderField()
    {
        return [
            'SeqNo' => 'seq_no',
            'OrderNo' => 'order_no',
            'TransDate' => 'trans_date',
            'UserName' => 'user_name',
            'InvoiceNumber' => 'invoice_number',
            'StoreName' => 'store_name'
        ];
    }
}
