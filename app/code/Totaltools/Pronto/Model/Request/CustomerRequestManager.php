<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Model\Request;

use Magento\Framework\Exception\LocalizedException;
use Totaltools\Customer\Model\Attribute\Source\EmailVerification;

/**
 * Class CustomerRequestManager
 * @package Totaltools\Pronto\Model\Request
 */
class CustomerRequestManager implements \Totaltools\Pronto\Api\CustomerRequestManagerInterface
{
    /**
     * @var \Totaltools\Pronto\Model\Request\Customer
     */
    protected $customerRequest;

    /**
     * @var \Magento\Newsletter\Model\SubscriberFactory
     */
    protected $subscriberFactory;

    /**
     * @var \Magento\Customer\Model\ResourceModel\Customer
     */
    protected $customerResource;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    protected $customerFactory;

    /**
     * @var \Totaltools\Pronto\Helper\Config
     */
    protected $configHelper;

    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $request;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Logging instance
     * @var \Totaltools\Pronto\Model\Logger
     */
    protected $customLogger;

    /**
     * @var \Totaltools\Pronto\Helper\Data
     */
    protected $prontoHelper;

    /**
     * CustomerRequestManager constructor.
     * @param Customer $customerRequest
     * @param \Magento\Newsletter\Model\SubscriberFactory $subscriber
     * @param \Magento\Customer\Model\ResourceModel\Customer $customerResource
     * @param \Magento\Customer\Model\CustomerFactory $customerFactory
     * @param \Totaltools\Pronto\Helper\Config $configHelper
     * @param \Magento\Framework\App\RequestInterface $request
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Totaltools\Pronto\Model\Logger $customLogger
     * @param \Totaltools\Pronto\Helper\Data $prontoHelper
     */
    public function __construct(
        \Totaltools\Pronto\Model\Request\Customer $customerRequest,
        \Magento\Newsletter\Model\SubscriberFactory $subscriber,
        \Magento\Customer\Model\ResourceModel\Customer $customerResource,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Totaltools\Pronto\Helper\Config $configHelper,
        \Magento\Framework\App\RequestInterface $request,
        \Psr\Log\LoggerInterface $logger,
        \Totaltools\Pronto\Model\Logger $customLogger,
        \Totaltools\Pronto\Helper\Data $prontoHelper
    )
    {
        $this->customerRequest = $customerRequest;
        $this->subscriberFactory = $subscriber;
        $this->customerResource = $customerResource;
        $this->customerFactory = $customerFactory;
        $this->configHelper = $configHelper;
        $this->request = $request;
        $this->logger = $logger;
        $this->customLogger = $customLogger;
        $this->prontoHelper = $prontoHelper;
    }

    /**
     * @param int $customerId
     * @param string $type
     * @return array
     */
    public function createMember(\Magento\Customer\Model\Customer $customer, string $type)
    {
        $result = [];

        try {
            $this->logger->info('Pronto Customer Register Event:: Customer ID - '.$customer->getId().'Hit createMember');

            if ($this->isManualVerificationRequired($customer)) {
                $this->logger->info('Pronto Customer Register Event:: Customer ID - '.$customer->getId().'Manual Verification Required');
                return $result;
            }

            // removed is_subscribed_newsletter
            $result = $this->customerRequest->createMember($customer, $type);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return $result;
    }

    /**
     * @param int $customerId
     *
     * @return bool
     */
    public function updateCustomerOnPronto(\Magento\Customer\Model\Customer $customer)
    {
        try {
            if ($this->isManualVerificationRequired($customer)) {
                return false;
            }

            // remove is_subscribed_newsletter
            $this->customerRequest->updateCustomerOnPronto($customer);

            return true;
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return false;
    }

    /**
     * @param \Magento\Customer\Model\Customer $customer
     * @param bool $force
     * @return void
     */
    public function updateCustomerInfo(\Magento\Customer\Model\Customer $customer, $force = false)
    {

        if (!$customer->getData('is_loyal') || !$force) {
            return;
        }

        try {
            $prontoData = $this->customerRequest->getMember([
                'Parameters' => [
                    'EmailAddress' => $customer->getEmail(),
                ]
            ]);

            if (!$prontoData) {
                return;
            }

            $loyaltyCustomerInfo = $prontoData['LoyaltyCustomers']['LoyaltyCustomer'];
            $customer->setData('is_loyal', 1);

            if (array_key_exists('LoyaltyID', $loyaltyCustomerInfo)) {
                $customer->setData('loyalty_id', $loyaltyCustomerInfo['LoyaltyID']);
            }

            if (isset($loyaltyCustomerInfo['Tier'])) {
                $customer->setData('loyalty_level', (int) $loyaltyCustomerInfo['Tier']);
            }

            if (isset($loyaltyCustomerInfo['PointExpiry'])) {
                $pointExpiry = $loyaltyCustomerInfo['PointExpiry'];
                if($pointExpiry && strtotime($pointExpiry)){
                    $dateExpiry = date('d/m/Y', strtotime($pointExpiry));
                    $customer->setData('point_expiry', $dateExpiry);
                }
            }
            $this->customerResource->save($customer);
            if (isset($loyaltyCustomerInfo['InsiderDollars']) && $loyaltyCustomerInfo['InsiderDollars'] <= 0) {
                $this->prontoHelper->setZeroBalance($customer,  0);
            }
            $this->prontoHelper->setLoyaltyData($loyaltyCustomerInfo);
        } catch (LocalizedException $localizedException) {
            $this->logger->error($localizedException->getLogMessage());
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param \Magento\Customer\Api\Data\CustomerInterface $customerInterface
     * @return array
     */
    public function getProntoCustomer(\Magento\Customer\Api\Data\CustomerInterface $customerInterface)
    {
        $result = [];

        try {
            $result = $this->customerRequest->getProntoCustomer($customerInterface);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return $result;
    }

    /**
     * @param string $loyaltyId
     * @return string
     */
    public function getCustomerAccountCode(string $loyaltyId)
    {
        $accountCode = '';

        try {
            $accountCode = $this->customerRequest->getCustomerAccountCode($loyaltyId);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return $accountCode;
    }

    /**
     * @return string
     */
    public function getGuestAccountCode()
    {
        $guestAccountCode = $this->configHelper->getGuestAccountCode();

        if (!$guestAccountCode) {
            try {
                $guestAccountCode = $this->customerRequest->getGuestAccountCode();

                if ($guestAccountCode) {
                    $this->configHelper->saveGuestAccountCode($guestAccountCode);
                }
            } catch (\Exception $e) {
                $this->logger->error($e->getMessage());
            }
        }

        return $guestAccountCode;
    }

    /**
     * Return customer loyalty points.
     *
     * @param \Magento\Customer\Model\Customer $customer
     *
     * @return array
     */
    public function getMemberPoints(\Magento\Customer\Model\Customer $customer)
    {
        $result = [];

        try {
            $result = $this->customerRequest->getMemberPoints($customer->getEmail());
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return $result;
    }

    /**
     * @param \Magento\Customer\Model\Customer $customer
     * @return bool
     */
    public function isManualVerificationRequired(\Magento\Customer\Model\Customer $customer)
    {
        return $customer->getData('email_verification_status') == EmailVerification::MANUAL_VERIFICATION_REQUIRED;
    }

    /**
     * @param int $customerId
     * @return \Magento\Customer\Model\Customer
     */
    public function getCustomerModel(int $customerId)
    {
        $customer = $this->customerFactory->create();
        $this->customerResource->load($customer, $customerId);

        return $customer;
    }

    /**
     * @param string $email
     * @return string
     */
    private function getIsSubscribedNewsletter(string  $email)
    {
        $subscriber = $this->subscriberFactory->create();

        return ($this->request->getParam('is_subscribed') || $subscriber->loadByEmail($email)->isSubscribed()) ? 'Y' : 'N';
    }

     /**
     * @param array $data
     * @return bool
     */
    public function extendExpiryDate($loyaltyId, $transactionId)
    {
        $result = [];
        $data = ['loyalty_id' => $loyaltyId, 'transaction' => $transactionId];
        try {
            $this->logger->info('Pronto Extend Points Expiry:: Loyalty ID - '.$loyaltyId.'Hit expendExpiryDate');

            $result = $this->customerRequest->extendExpiryDate($data);
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return $result;
    }
}
