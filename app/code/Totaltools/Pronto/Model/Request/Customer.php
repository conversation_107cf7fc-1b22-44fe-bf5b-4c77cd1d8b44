<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
namespace Totaltools\Pronto\Model\Request;

use Magento\Framework\Exception\LocalizedException;

/**
 * Class Customer
 * @package Totaltools\Pronto\Model\Request
 */
class Customer
{
    /**
     * @var \Totaltools\Pronto\Model\Api\Connect
     */
    protected $connect;

    /**
     * Logging instance
     * @var \Totaltools\Pronto\Model\Logger
     */
    protected $logger;

    /**
     * Customer constructor.
     * @param \Totaltools\Pronto\Model\Api\Connect $connect
     */
    public function __construct(
        \Totaltools\Pronto\Model\Api\Connect  $connect,
        \Totaltools\Pronto\Model\Logger $logger
    ) {
        $this->connect = $connect;
        $this->logger = $logger;
    }

    /**
     * Create customer on Pronto
     *
     * @param $customer
     * @param $type
     * @return array
     * @throws \Exception
     */
    public function createMember($customer, $type)
    {
        $address = $this->extractCustomerAddress($customer);
        $params = [
            'Type' => $type,
            'FirstName' => $customer->getData('firstname'),
            'LastName' => $customer->getData('lastname'),
            'DOB' => $customer->getData('dob') ? $customer->getData('dob') : '',
            'EmailAddress' => $customer->getEmail(),
            'Mobile' => $customer->getData('mobile_number'),
            'ABN' => $customer->getData('abn'),
            'Company' => $customer->getData('customer_company'),
            'BusinessAccount' => $customer->getData('business_account')?'Y':'N',
            'PreferredStore' => $customer->getData('preferred_store') ? $customer->getData('preferred_store') : '',
            'Industry' => $customer->getData('pronto_position') ? $customer->getData('pronto_position') : '',
            'ReferralCode' => $customer->getData('referred_by'),
            'SMSPromo' => $customer->getData('is_subscribed_sms_promo')?'Y':'N',
            'PhonePromo' => $customer->getData('is_subscribed_phone_promo')?'Y':'N',
            'DMPromo' => $customer->getData('is_subscribed_direct_marketing')?'Y':'N',
            'EmailPromo' =>  $customer->getData('is_subscribed')?'Y':'N',
            'EmailPromo1' =>  $customer->getData('is_subscribed')?'Y':'N',
            'EmailPromo2' => '',
            'EmailPromo3' => '',
            'EmailPromo4' => '',
            'EmailPromo5' => '',
            'EmailPromo6' => '',
            'EmailPromo7' => '',
            'EmailPromo8' => '',
            'EmailPromo9' => '',
            'EmailPromo10' => '',
        ];

        if ($address) {
            $params['Phone'] = $address->getTelephone() ? $address->getTelephone() : '';
            $params['Address1'] = $address->getStreetLine(1) ? $address->getStreetLine(1) : '';
            $params['Address2'] = $address->getStreetLine(2) ? $address->getStreetLine(2) : '';
            $params['City'] = $address->getCity() ? $address->getCity() : '';
            $params['State'] = $address->getRegionCode() ?: '';
            $params['PostCode'] = $address->getPostcode() ? $address->getPostcode() : '';
        }

        $this->logger->info(print_r($params,true));

        /**
         * CreateCustomer api will response create an account if not exist,
         * if not it responses LoyaltyCustomerExitsAlready as Y and CustomerAccountcode
         * **/
        $this->connect
            ->setPath('CreateCustomer')
            ->setRootNode('CreateCustomerRequest')
            ->setBody([
                'Parameters' => $params
            ]);

        $response = $this->connect->call('array');

        $this->logger->info(print_r($response,true));
        if (isset($response['CustomerCreated']['LoyaltyCustomer'])) {
            return $response;
        }

        return [];
    }

    /**
     * Get customer from Pronto.
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @param array                            $params
     *
     * @return bool|array
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getMember(array $params)
    {
        $this->connect
            ->setPath('GetMember')
            ->setRootNode('GetMemberRequest')
            ->setBody($params);
        try {
            $response = $this->connect->call('array');
            if (!empty($response['LoyaltyCustomers']) && array_key_exists('LoyaltyCustomer', $response['LoyaltyCustomers'])) {
                return $response;
            }
        } catch (LocalizedException $e) {
            throw new LocalizedException(
                __('Error occurs on calling GetMember "%1"', $e->getMessage())
            );
        }

        return false;
    }

    /**
     * Return customer address
     * return the first address - billing address priority then shipping address
     *
     * @param \Magento\Customer\Model\Customer $customer
     * @return \Magento\Customer\Model\Address|null
     */
    private function extractCustomerAddress(\Magento\Customer\Model\Customer $customer)
    {
        $addresses = $customer->getPrimaryAddresses();

        return !empty($addresses[0]) ? $addresses[0] : null;
    }

    /**
     * Update customer in Pronto.
     *
     * @param $customer
     *
     * @return array|bool
     * @throws \Exception
     */
    public function updateCustomerOnPronto($customer)
    {
        $address = $this->extractCustomerAddress($customer);
        $params = [
            'FirstName' => $customer->getData('firstname'),
            'LastName' => $customer->getData('lastname'),
            'Industry' => $customer->getData('pronto_position') ? $customer->getData('pronto_position') : '',
            'Role' => $customer->getData('role') ? $customer->getData('role') : '',
            'DOB' => $customer->getData('dob') ? date("d-M-Y", strtotime($customer->getData('dob'))) : '',
            'Mobile' => $customer->getData('mobile_number'),
            'PreferredStore' => $customer->getData('preferred_store') ? $customer->getData('preferred_store') : '',
            'SMSPromo' => $customer->getData('is_subscribed_sms_promo')?'Y':'N',
            'PhonePromo' => $customer->getData('is_subscribed_phone_promo')?'Y':'N',
            'DMPromo' => $customer->getData('is_subscribed_direct_marketing')?'Y':'N',
            'EmailPromo' => $customer->getData('is_subscribed')?'Y':'N',
            'EmailPromo1' => $customer->getData('is_subscribed')?'Y':'N',
        ];

        $loyaltyId = $customer->getData('loyalty_id');

        if ($loyaltyId) {
            $params['LoyaltyID'] = $loyaltyId;
        } else {
            $params['Email'] = $customer->getEmail();
        }

        if ($address) {
            $params['Company'] = $address->getData('company') ? $address->getData('company') : '';
            $params['Phone'] = $address->getTelephone() ? $address->getTelephone() : '';
            $params['Address1'] = $address->getStreetLine(1) ? $address->getStreetLine(1) : '';
            $params['Address2'] = $address->getStreetLine(2) ? $address->getStreetLine(2) : '';
            $params['City'] = $address->getCity() ? $address->getCity() : '';
            $params['State'] = $address->getRegion() ? $address->getRegion() : '';
            $params['PostCode'] = $address->getPostcode() ? $address->getPostcode() : '';
        }

        $this->connect
            ->setPath('UpdateMemberDetails')
            ->setRootNode('UpdateMemberDetailsRequest')
            ->setBody([
                'Parameters' => $params
            ]);

        return $this->connect->call('array');
    }

    /**
     * Get guest account code in Pronto Connect system
     *
     * @return string
     * @throws \Exception
     */
    public function getGuestAccountCode()
    {
        $guestAccountCode = '';
        $guestInfo = [
            'Type' => 'Debtor',
            'Title' => '',
            'Firstname' => 'Guest',
            'Lastname' => 'Guest',
            'DOB' => '',
            'EmailAddress' => '<EMAIL>',
            'Phone' => '',
            'Mobile' => '',
            'Address1' => '',
            'Address2' => '',
            'City' => '',
            'State' => '',
            'PostCode' => '',
            'SMSPromo' => '',
            'DMPromo' => '',
            'EmailPromo1' => 'Y',
            'EmailPromo2' => '',
            'EmailPromo3' => '',
            'EmailPromo4' => '',
            'EmailPromo5' => '',
            'EmailPromo6' => '',
            'EmailPromo7' => '',
            'EmailPromo8' => '',
            'EmailPromo9' => '',
            'EmailPromo10' => '',
        ];

        $this->connect
            ->setPath('CreateCustomer')
            ->setRootNode('CreateCustomerRequest')
            ->setBody([
                'Parameters' => $guestInfo
            ]);
        $response = $this->connect->call('array');

        if (isset($response['CustomerCreated']['CustomerAccountcode'])) {
            $guestAccountCode = $response['CustomerCreated']['CustomerAccountcode'];
        }

        return $guestAccountCode;
    }

    /**
     * Get customer from Pronto.
     *
     * @param \Magento\Customer\Api\Data\CustomerInterface $customer
     * @return array
     * @throws LocalizedException
     */
    public function getProntoCustomer(\Magento\Customer\Api\Data\CustomerInterface $customer)
    {
        $result = [
            'emailExist' => false,
            'mobileExist' => false,
        ];

        $customerMember = $this->getMember([
            'Parameters' => [
                'EmailAddress' => $customer->getEmail(),
            ]
        ]);

        if ($customerMember) {
            $result['emailExist'] = true;

            $customerMember = $customerMember['LoyaltyCustomers']['LoyaltyCustomer'];
            if (!empty($customerMember) && array_key_exists('LoyaltyID', $customerMember)) {
                $result = array_merge($customerMember, $result);
            }
        } else {
            if (!$mobileNumber = $customer->getCustomAttribute('mobile_number')) {
                return $result;
            }

            $customerMember = $this->getMember([
                'Parameters' => [
                    'Mobile' => $mobileNumber->getValue()
                ]
            ]);
            if ($customerMember) {
                $result['mobileExist'] = true;

                if (count($customerMember) > 1) {
                    $customerMember = array_shift($customerMember);
                }

                $customerMember = $customerMember['LoyaltyCustomers']['LoyaltyCustomer'];

                if (!empty($customerMember) && array_key_exists('LoyaltyID', $customerMember)) {
                    $result = array_merge($customerMember, $result);
                }
            }
        }

        return $result;
    }

    /**
     * @param string $loyaltyId
     * @throws \Exception
     *
     * @return string
     */
    public function getCustomerAccountCode(string $loyaltyId)
    {
        $accountCode = '';
        $this->connect
            ->setPath('GetAccountCode')
            ->setRootNode('GetAccountCodeRequest')
            ->setBody([
                'Parameters' => [
                    'LoyaltyID' => $loyaltyId
                ]
            ]);

        $response = $this->connect->call('array');

        if (isset($response['AccountCodes']['AccountCode'])) {
            $accountCode =  $response['AccountCodes']['AccountCode'];
        }

        return $accountCode;
    }


    /**
     * @param string $customerEmail
     * @return array
     * @throws \Exception
     */
    public function getMemberPoints(string $customerEmail)
    {
        $this->connect
            ->setPath('GetMemberPoints')
            ->setRootNode('GetMemberPointsRequest')
            ->setBody([
                'Parameters' => [
                    'EmailAddress' => $customerEmail
                ]
            ]);

        $response = $this->connect->call('array');

        if (isset($response['LoyaltyPoints']) && isset($response['LoyaltyPoints']['LoyaltyPoint'])) {
            return $response['LoyaltyPoints']['LoyaltyPoint'];
        }

        return [];
    }


    /**
     * Extend Expiry Date in Pronto.
     *
     * @param $data
     *
     * @return array|bool
     * @throws \Exception
     */
    public function extendExpiryDate($data)
    {
        $params = [
            'LoyaltyID' => $data['loyalty_id'],
            'Transaction' => $data['transaction'],
            
        ];
        $this->connect
            ->setPath('ExtendPoints')
            ->setRootNode('ExtendPointsRequest')
            ->setBody([
                'Parameters' => $params
            ]);
        $response = $this->connect->call('array');

        $this->logger->info(print_r($response,true));
        if (isset($response)) {
            return $response;
        }

        return [];
    }
}
