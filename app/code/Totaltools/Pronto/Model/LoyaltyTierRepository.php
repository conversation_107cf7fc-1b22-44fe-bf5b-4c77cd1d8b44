<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Model;

use Magento\Framework\Api\ExtensibleDataObjectConverter;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\ValidatorException;
use Totaltools\Pronto\Api\Data\LoyaltyTierInterface;
use Totaltools\Pronto\Api\LoyaltyTierRepositoryInterface;

/**
 * Class LoyaltyTierRepository
 *
 * @package Totaltools\Pronto\Model
 * <AUTHOR> <<EMAIL>>
 */
class LoyaltyTierRepository implements LoyaltyTierRepositoryInterface
{
    /**
     * @var \Magento\Framework\Api\ExtensibleDataObjectConverter
     */
    private $extensibleDataObjectConverter;

    /**
     * @var ResourceModel\LoyaltyTier
     */
    private $tierResource;

    /**
     * @var LoyaltyTierFactory
     */
    private $tierFactory;

    /**
     * @var LoyaltyTier[]
     */
    private $instances = [];

    /**
     * @var LoyaltyTier[]
     */
    private $instancesById = [];

    /**
     * @param ExtensibleDataObjectConverter $extensibleDataObjectConverter
     * @param ResourceModel\LoyaltyTier     $tierResource
     * @param LoyaltyTierFactory            $tierFactory
     */
    public function __construct(
        \Magento\Framework\Api\ExtensibleDataObjectConverter $extensibleDataObjectConverter,
        \Totaltools\Pronto\Model\ResourceModel\LoyaltyTier $tierResource,
        \Totaltools\Pronto\Model\LoyaltyTierFactory $tierFactory
    ) {
        $this->extensibleDataObjectConverter = $extensibleDataObjectConverter;
        $this->tierResource = $tierResource;
        $this->tierFactory = $tierFactory;
    }

    /**
     * {@inheritdoc}
     */
    public function save(\Totaltools\Pronto\Api\Data\LoyaltyTierInterface $tier)
    {
        try {
            $existingTier = $this->getById($tier->getTierId());
        } catch (NoSuchEntityException $e) {
            $existingTier = null;
        }

        $tierDataArray = $this->extensibleDataObjectConverter
            ->toNestedArray($tier, [], 'Totaltools\Pronto\Api\Data\LoyaltyTierInterface');
        $tierDataArray = array_replace($tierDataArray, $tier->getData());
        $tier = $this->initialiseTierData($tierDataArray, empty($existingTier));

        try {
            unset($this->instances[$tier->getDescription()]);
            unset($this->instancesById[$tier->getTierId()]);
            $this->tierResource->save($tier);
        } catch (ValidatorException $e) {
            throw new CouldNotSaveException(__($e->getMessage()));
        } catch (\Exception $e) {
            throw new CouldNotSaveException(__('Unable to save tier %1', $tier->getTierId()));
        }

        unset($this->instances[$tier->getDescription()]);
        unset($this->instancesById[$tier->getTierId()]);
        return $this->getById($tier->getTierId());
    }

    /**
     * {@inheritdoc}
     */
    public function get($description)
    {
        $cacheKey = $this->getCacheKey([$description, uniqid()]);
        if (!isset($this->instances[$description][$cacheKey])) {
            /** @var \Totaltools\Pronto\Model\LoyaltyTier $tier */
            $tier = $this->tierFactory->create();

            $tierId = $this->tierResource->getIdByDescription($description);
            if (!$tierId) {
                throw new NoSuchEntityException(__('Requested tier doesn\'t exist'));
            }

            $tier->load($tierId);
            $this->instances[$description][$cacheKey] = $tier;
            $this->instancesById[$tier->getId()][$cacheKey] = $tier;
        }

        return $this->instances[$description][$cacheKey];
    }

    /**
     * {@inheritdoc}
     */
    public function getById($tierId)
    {
        $cacheKey = $this->getCacheKey([$tierId, uniqid()]);
        if (!isset($this->instancesById[$tierId][$cacheKey])) {
            /** @var \Totaltools\Pronto\Model\LoyaltyTier $tier */
            $tier = $this->tierFactory->create();

            $tierId = $this->tierResource->getIdByTierId($tierId);
            if (!$tierId) {
                throw new NoSuchEntityException(__('Requested tier doesn\'t exist'));
            }

            $tier->load($tierId);
            if (null === $tier->getTierId()) {
                throw new NoSuchEntityException(__('Tier with specified ID "%1" not found.', $tier->getTierId()));
            }
            $this->instancesById[$tierId][$cacheKey] = $tier;
            $this->instances[$tier->getDescription()][$cacheKey] = $tier;
        }

        return $this->instancesById[$tierId][$cacheKey];
    }

    /**
     * {@inheritdoc}
     */
    public function delete(\Totaltools\Pronto\Api\Data\LoyaltyTierInterface $tier)
    {
        $insiderLevel = $tier->getDescription();
        $tierId = $tier->getTierId();
        try {
            unset($this->instances[$tier->getDescription()]);
            unset($this->instancesById[$tier->getTierId()]);
            $this->tierResource->delete($tier);
        } catch (ValidatorException $e) {
            throw new CouldNotSaveException(__($e->getMessage()));
        } catch (\Exception $e) {
            throw new CouldNotDeleteException(__('Unable to remove tier %1', $tier->getTierId()));
        }
        unset($this->instances[$insiderLevel]);
        unset($this->instancesById[$tierId]);

        return true;
    }

    /**
     * {@inheritdoc}
     */
    public function deleteById($tierId)
    {
        $tier = $this->getById($tierId);
        return $this->delete($tier);
    }

    /**
     * Initialise tier data
     *
     * @param array $tierData
     * @param bool  $createNew
     *
     * @return \Totaltools\Pronto\Api\Data\LoyaltyTierInterface
     */
    private function initialiseTierData(array $tierData, $createNew)
    {
        if ($createNew) {
            $tier = $this->tierFactory->create();
        } else {
            unset($this->instances[$tierData[LoyaltyTierInterface::DESCRIPTION]]);
            $tier = $this->get($tierData[LoyaltyTierInterface::DESCRIPTION]);
        }

        foreach ($tierData as $key => $value) {
            $tier->setData($key, $value);
        }

        return $tier;
    }

    /**
     * Get key for cache
     *
     * @param array $data
     * @return string
     */
    private function getCacheKey($data)
    {
        $serializeData = [];
        foreach ($data as $key => $value) {
            if (is_object($value)) {
                $serializeData[$key] = $value->getId();
            } else {
                $serializeData[$key] = $value;
            }
        }

        return md5(serialize($serializeData));
    }
}
