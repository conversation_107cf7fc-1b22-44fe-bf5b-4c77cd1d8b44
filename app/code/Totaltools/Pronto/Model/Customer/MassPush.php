<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Pronto
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Pronto\Model\Customer;

use Magento\Config\Model\ResourceModel\Config as ConfigResource;
use Magento\Customer\Model\AddressFactory as CustomerAddressFactory;
use Magento\Customer\Model\ResourceModel\Customer as CustomerResource;
use Magento\Customer\Model\ResourceModel\CustomerFactory as CustomerResourceFactory;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Exception\LocalizedException;
use Totaltools\Pronto\Helper\Config as ConfigHelper;
use Totaltools\Pronto\Model\Api\Connect as ConnectApi;
use Magento\Config\Model\ConfigFactory;
use Magento\Newsletter\Model\SubscriberFactory;
use Psr\Log\LoggerInterface;


/**
 * Model for creating guest customer on TTO system
 *
 * Class is not used currently
 */
class MassPush
{
    /**
     * Pronto Connect API
     * @var ConnectApi
     */
    protected $connect;

    /**
     * Config helper
     * @var ConfigHelper
     */
    protected $configHelper;

    /**
     * Config resource
     * @var ConfigResource
     */
    protected $resourceConfig;

    /**
     * App request
     * @var RequestInterface
     */
    protected $request;

    /**
     * Customer address factory
     * @var CustomerAddressFactory
     */
    protected $customerAddressFactory;

    /**
     * Logger
     *
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * Customer resource factory
     * @var CustomerResourceFactory
     */
    protected $customerResourceFactory;

    /**
     * Subscriber factory
     * @var SubscriberFactory
     */
    protected $subscriberFactory;

    /**
     * @var CustomerResource
     */
    protected $customerResource;

    /**
     * Abstract Pronto constructor
     *
     * @param ConnectApi                 $connect
     * @param ConfigHelper               $configHelper
     * @param ConfigResource             $resourceConfig
     * @param RequestInterface           $request
     * @param CustomerAddressFactory     $customerAddressFactory
     * @param SubscriberFactory          $subscriberFactory
     * @param LoggerInterface            $logger
     * @param CustomerResource           $customerResource
     */
    public function __construct(
        ConnectApi $connect,
        ConfigHelper $configHelper,
        ConfigResource $resourceConfig,
        RequestInterface $request,
        CustomerAddressFactory $customerAddressFactory,
        SubscriberFactory $subscriberFactory,
        LoggerInterface $logger,
        CustomerResource $customerResource,
        CustomerResourceFactory $customerResourceFactory
    ) {
        $this->connect = $connect;
        $this->configHelper = $configHelper;
        $this->resourceConfig = $resourceConfig;
        $this->request = $request;
        $this->customerAddressFactory = $customerAddressFactory;
        $this->subscriberFactory = $subscriberFactory;
        $this->logger = $logger;
        $this->customerResource = $customerResource;
        $this->customerResourceFactory = $customerResourceFactory;
    }

    /**
     * Get customer's account code in Pronto Connect system. Create if not exist, update if exists
     *
     * @param Customer $customer
     * @param string $type
     * @throws LocalizedException
     * @return string
     */
    public function getRegisteredAccountCode($customer, $type)
    {
        if ($this->isManualVerificationRequired($customer)) {
            return false;
        }

        $address = null;
        $addresses = $customer->getPrimaryAddresses();
        $status  = array();

        if (isset($addresses[0])) {
            $address = $addresses[0];
        }

        $subscribed = (
            $this->request->getParam('is_subscribed')
            || $customer->getData('is_subscribed')
        ) ? 'Y' : 'N';

        $accountCodeMage = $customer->getData('account_code');

        $params = [
            'Type' => $type,
            'Title' => $customer->getData('prefix') ? $customer->getData('prefix') : '',
            'Firstname' => $customer->getData('firstname'),
            'Lastname' => $customer->getData('lastname'),
            'DOB' => $customer->getData('dob') ? $customer->getData('dob') : '',
            'EmailAddress' => $customer->getEmail(),
            'SMSPromo' => '',
            'DMPromo' => '',
            'EmailPromo1' => $subscribed,
            'EmailPromo2' => '',
            'EmailPromo3' => '',
            'EmailPromo4' => '',
            'EmailPromo5' => '',
            'EmailPromo6' => '',
            'EmailPromo7' => '',
            'EmailPromo8' => '',
            'EmailPromo9' => '',
            'EmailPromo10' => '',
        ];
        if ($accountCodeMage) {
            $params['Accountcode'] = $accountCodeMage;
        }
        if ($address) {
            $params['Phone'] = $address->getTelephone() ? $address->getTelephone() : '';
            $params['Mobile'] = $address->getTelephone() ? $address->getTelephone() : '';
            $params['Address1'] = $address->getStreetLine(1) ? $address->getStreetLine(1) : '';
            $params['Address2'] = $address->getStreetLine(2) ? $address->getStreetLine(2) : '';
            $params['City'] = $address->getCity() ? $address->getCity() : '';
            $params['State'] = $address->getRegion() ? $address->getRegion() : '';
            $params['PostCode'] = $address->getPostcode() ? $address->getPostcode() : '';
        }

        try {
            $this->connect
                ->setPath('CreateCustomer')
                ->setRootNode('CreateCustomerRequest')
                ->setBody([
                    'Parameters' => $params
                ]);

            $response = $this->connect->call('array');
            $loyaltyId = '';
            $accountCode = '';
            if (isset($response['CustomerCreated']['CustomerAccountcode'])) {
                $accountCode = $response['CustomerCreated']['CustomerAccountcode'];
            }
            if (isset($response['CustomerCreated']['LoyaltyCustomer']['LoyaltyID'])) {
                $loyaltyId = $response['CustomerCreated']['LoyaltyCustomer']['LoyaltyID'];
            }
            if($accountCodeMage) {
                $customerData = $customer->getDataModel();
                $customerData->setId($customer->getId());
                $customerData->setCustomAttribute('account_code', $accountCodeMage);
                if ($loyaltyId !== '') {
                    $customerData->setCustomAttribute('loyalty_id', $loyaltyId);
                }
                $customer->updateData($customerData);
                $customerResource = $this->customerResourceFactory->create();
                $customerResource->saveAttribute($customer, 'account_code');
                if ($loyaltyId !== '') {
                    $customerResource->saveAttribute($customer, 'loyalty_id');
                }
                $status['status'] = 'Success';
                $status['account_code'] = $accountCodeMage;
                $status['message'] = 'Customer Account has synced to Pronto Account Code';
            } else {
                if ($accountCode) {
                    $customerData = $customer->getDataModel();
                    $customerData->setId($customer->getId());
                    $customerData->setCustomAttribute('account_code', $accountCode);
                    if ($loyaltyId !== '') {
                        $customerData->setCustomAttribute('loyalty_id', $loyaltyId);
                    }
                    $customer->updateData($customerData);
                    $customerResource = $this->customerResourceFactory->create();
                    $customerResource->saveAttribute($customer, 'account_code');
                    if ($loyaltyId !== '') {
                        $customerResource->saveAttribute($customer, 'loyalty_id');
                    }
                    $status['status'] = 'Success';
                    $status['account_code'] = $accountCode;
                    $status['message'] = 'Customer Account has synced to Pronto Account Code';
                } else {
                    $customerData = $customer->getDataModel();
                    $customerData->setId($customer->getId());
                    $customer->updateData($customerData);
                    $status['status'] = 'Error';
                    $status['account_code'] = '';
                    $status['message'] = "Pronto does not return accountCode! Disabled Insider on customer id - " . $customer->getId();
                    $this->logger->error("Pronto does not return accountCode!Disabled Insider on customer id" . $customer->getId());
                }
            }
        } catch (\Exception $e) {
            $status['status'] = 'Error';
            $status['account_code'] = '';
            $status['message'] = $e->getMessage();
            $this->logger->error($e->getMessage());
        }
        return $status;
    }


    /**
     * @param \Magento\Customer\Model\Customer $customer
     * @return bool
     * @throws LocalizedException
     */
    protected function isManualVerificationRequired(\Magento\Customer\Model\Customer $customer)
    {
        $this->customerResource->loadByEmail($customer, $customer->getEmail());

        return $customer->getData('email_verification_status') == \Totaltools\Customer\Model\Attribute\Source\EmailVerification::MANUAL_VERIFICATION_REQUIRED;
    }
}
