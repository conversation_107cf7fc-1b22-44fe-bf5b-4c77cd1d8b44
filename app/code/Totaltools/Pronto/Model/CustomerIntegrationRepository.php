<?php

namespace Totaltools\Pronto\Model;


use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\DB\Query\Generator;
use Magento\Framework\DB\Select;
use Magento\Framework\Exception\LocalizedException;

class CustomerIntegrationRepository
{
    /**
     * @var AdapterInterface
     */
    private $connection;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $_logger;

    /**
     * @var Generator
     */
    private $batchQueryGenerator;

    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    /**
     * @var int
     */
    private $batchSize;

    /**
     * @var \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory
     */
    protected $customerCollectionFactory;

    /**
     * @var \Magento\Customer\Model\CustomerFactory
     */
    protected $customerFactory;

    public function __construct(
        Generator $generator,
        ResourceConnection $resourceConnection,
        \Magento\Customer\Model\ResourceModel\Customer\CollectionFactory $customerCollectionFactory,
        \Magento\Customer\Model\CustomerFactory $customerFactory,
        \Psr\Log\LoggerInterface $logger,
        $batchSize = 100
    )
    {
        $this->batchQueryGenerator = $generator;
        $this->resourceConnection = $resourceConnection;
        $this->connection = $this->resourceConnection->getConnection();
        $this->customerCollectionFactory = $customerCollectionFactory;
        $this->customerFactory = $customerFactory;
        $this->batchSize = $batchSize;
        $this->_logger = $logger;
    }

    /**
     * Return loyal customers.
     *
     * @return \Generator
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getLoyalCustomers(): \Generator
    {
        $batchSelectIterator = $this->batchQueryGenerator->generate(
            'entity_id',
            $this->getLoyalCustomersSelect(),
            $this->batchSize,
            \Magento\Framework\DB\Query\BatchIteratorInterface::NON_UNIQUE_FIELD_ITERATOR
        );

        foreach ($batchSelectIterator as $select) {
            foreach ($this->connection->fetchAssoc($select) as $key => $customer) {
                yield $key => $this->getCustomer($customer['entity_id']);
            }
        }
    }

    /**
     * Return Select to fetch all loyal customers
     *
     * @return Select
     */
    private function getLoyalCustomersSelect(): Select
    {
        /*** @var \Magento\Customer\Model\ResourceModel\Customer\Collection $customerCollection */
        $customerCollection = $this->customerCollectionFactory->create()
            ->addAttributeToFilter('is_loyal', 1)
            ->addAttributeToFilter('unverified_loyalty_id', ['null' => true], 'left');

        $customerCollection->getSelect()
            ->reset(\Zend_Db_Select::COLUMNS)
            ->columns(['entity_id']);

        return $customerCollection->getSelect();
    }

    /**
     * Get customer by ID.
     *
     * @param $entityId
     *
     * @return bool|\Magento\Customer\Api\Data\CustomerInterface
     */
    private function getCustomer($entityId)
    {
        try {
            $customer = $this->customerFactory->create();
            return $customer->load($entityId);
        } catch (LocalizedException $exception) {
            $this->_logger->error($exception->getMessage());
        }

        return false;
    }
}