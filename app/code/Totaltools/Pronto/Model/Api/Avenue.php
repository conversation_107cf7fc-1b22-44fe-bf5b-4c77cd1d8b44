<?php
/**
 * <AUTHOR> Internet (<EMAIL>)
 * @package Totaltools_Pronto
 */

namespace Totaltools\Pronto\Model\Api;

use Magento\Framework\Model\AbstractModel;
use Magento\Framework\Model\Context;
use Magento\Framework\Registry;
use Totaltools\Pronto\Helper\Config as ConfigHelper;

class Avenue extends AbstractModel
{

    const HTTP_METHOD_GET  = CURLOPT_HTTPGET;
    const HTTP_METHOD_POST = CURLOPT_POST;

    /**
     * List of available HTTP methods
     * @var int[]
     */
    protected $validHttpMethods = [
        self::HTTP_METHOD_GET,
        self::HTTP_METHOD_POST,
    ];

    const FORMAT_JSON = 'json';
    const FORMAT_XML  = 'xml';

    /**
     * List of valid request formats
     * @var string[]
     */
    protected $validFormats = [
        self::FORMAT_JSON,
        self::FORMAT_XML,
    ];

    /**
     * List of valid API paths
     * @var string[]
     */
    protected $validPaths = [
        'category/v4',
        'debtor/v4',
        'error_response/v4',
        'order/v4',
        'order/v4',
        'order/v5',
        'pricing/v4',
        'pricing/v5',
        'product/v4',
        'transaction/v4',
        'transaction/v5',
    ];

    const REQUEST_USERAGENT = 'Total Tools Magento 2 Application';
    const REQUEST_TIMEOUT   = 30;

    /**
     * HTTP request method (with default)
     * @var int
     */
    protected $httpMethod = self::HTTP_METHOD_GET;

    /**
     * Request format (with default)
     * @var string
     */
    protected $format = self::FORMAT_JSON;

    /**
     * API path
     * @var string
     */
    protected $path;

    /**
     * List of GET request filters
     * @var string[]
     */
    protected $getFilters = [];

    /**
     * Data for POST request
     * @var string[]
     */
    protected $post;

    /**
     * Config helper
     * @var ConfigHelper
     */
    protected $configHelper;


    /**
     * Avenue constructor
     *
     * @param ConfigHelper $configHelper
     * @param Context $context
     * @param Registry $registry
     */
    public function __construct(
        ConfigHelper $configHelper,
        Context $context,
        Registry $registry
    ) {
        parent::__construct($context, $registry);
        $this->configHelper = $configHelper;
    }

    /**
     * Build & return full request URL
     *
     * @return string
     */
    protected function getUrl ()
    {
        $url = trim(trim($this->configHelper->getAvenueUrl()), '/');
        $url .= '/api/';
        $url .= $this->format . '/';
        $url .= $this->path;

        if ($this->httpMethod === self::HTTP_METHOD_GET && $this->getFilters) {
            $url .= '?';
            foreach ($this->getFilters as $param => $value) {
                $url .= urlencode($param) . '=' . urlencode($value) . '&';
            }
            $url = rtrim($url, '&');
        }

        return $url;
    }

    /**
     * Validate before calling API
     *
     * @return void
     * @throws \Exception
     */
    protected function validate ()
    {
        if (!$this->path) {
            throw new \Exception('No path specified');
        }
        if ($this->httpMethod === self::HTTP_METHOD_POST) {
            if (
                !is_array($this->post)
                || !$this->post
            ) {
                throw new \Exception('No POST data specified');
            }
        }
    }

    /**
     * Set HTTP request method
     *
     * @param int $httpMethod cURL const HTTP method code
     * @return $this
     * @throws \Exception
     */
    public function setHttpMethod ($httpMethod)
    {
        if (!in_array($httpMethod, $this->validHttpMethods)) {
            throw new \Exception('Unknown HTTP method: ' . $httpMethod);
        }
        $this->httpMethod = $httpMethod;
        return $this;
    }

    /**
     * Set requested response format
     *
     * @param string $format Request format
     * @return $this
     * @throws \Exception
     */
    public function setFormat ($format)
    {
        if (!in_array($format, $this->validFormats)) {
            throw new \Exception('Unknown format: ' . $format);
        }
        $this->format = $format;
        return $this;
    }

    /**
     * Set API request path
     *
     * @param string $path API path
     * @return $this
     * @throws \Exception
     */
    public function setPath ($path)
    {
        $path = trim(trim($path), '/');
        if (!in_array($path, $this->validPaths)) {
            throw new \Exception('Unknown path: ' . $path);
        }
        $this->path = $path;
        return $this;
    }

    /**
     * Add GET filter to request
     *
     * @param string $param Filter param
     * @param string $value Filter value
     * @return $this
     */
    public function setGetFilter ($param, $value)
    {
        $this->getFilters[$param] = $value;
        return $this;
    }

    /**
     * Set data for POST request
     *
     * @param string[] $data POST data
     * @return $this
     */
    public function setPost ($data)
    {
        $this->post = $data;
        return $this;
    }

    /**
     * Call API
     *
     * @return mixed
     */
    public function call ()
    {
        $this->validate();

        $isDebug = $this->configHelper->isDebug();

        $url = $this->getUrl();
        if ($isDebug) {
            $this->_logger->info('Totaltools_Pronto Avenue calling URL: ' . $url);
        }

        $c = curl_init();
        $curlAuth = $this->configHelper->getAvenueUsername() . ':' . $this->configHelper->getAvenuePassword();
        curl_setopt_array($c, [
            $this->httpMethod      => true,
            CURLOPT_FAILONERROR    => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HTTPAUTH       => CURLAUTH_BASIC,
            CURLOPT_USERPWD        => $curlAuth,
            CURLOPT_URL            => $url,
            CURLOPT_USERAGENT      => self::REQUEST_USERAGENT,
            CURLOPT_TIMEOUT        => $this->configHelper->getRequestTimeout(ConfigHelper::CONFIG_PATH_AUTH_AVENUE_REQUEST_TIMEOUT)?: self::REQUEST_TIMEOUT,
        ]);

        if ($this->httpMethod === self::HTTP_METHOD_POST) {
            $postQuery = json_encode($this->post) ;
            curl_setopt($c, CURLOPT_FOLLOWLOCATION, true);
            curl_setopt($c, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($c, CURLOPT_POSTFIELDS, $postQuery);
            if ($isDebug) {
                $this->_logger->info('Totaltools_Pronto Avenue request POST data: ' . var_export($this->post, true));
            }
        }

        if ($isDebug) {
            curl_setopt($c, CURLINFO_HEADER_OUT, true);
        }

        $result = curl_exec($c);

        if ($isDebug) {
            $this->_logger->info('Totaltools_Pronto Avenue cURL info: ' . var_export(curl_getinfo($c), true));
            $this->_logger->info('Totaltools_Pronto Avenue cURL response data: ' . var_export($result, true));
            if (!$result) {
                $this->_logger->info('Totaltools_Pronto Connect cURL error: ' . curl_error($c));
            }
        }

        curl_close($c);

        return $result;
    }

}
