<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Model\Api;

class Connect extends \Magento\Framework\Model\AbstractModel
{
    const REQUEST_USERAGENT = 'Total Tools Magento 2 Application';
    const REQUEST_TIMEOUT = 10;
    const LOGIN_PATH = 'login';

    protected $validPaths
        = [
            self::LOGIN_PATH,
            'GetTiers',
            'GetMember',
            'GetMemberPoints',
            'GetMemberTransactions',
            'GetTransactionTypes',
            'CreateTransaction',
            'CreateCustomer',
            'UpdateMemberDetails',
            'GetAccountCode',
            'ConfirmPoints',
            'ExtendPoints'
        ];

    /**
     * @var string|mixed
     */
    protected $token;

    /**
     * @var string|mixed
     */
    protected $path;

    /**
     * @var object|mixed
     */
    protected $rootNode;

    /**
     * @var object|mixed
     */
    protected $body;

    /**
     * @var \Totaltools\Pronto\Helper\Config
     */
    protected $configHelper;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry      $registry
     * @param \Totaltools\Pronto\Helper\Config $configHelper
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Totaltools\Pronto\Helper\Config $configHelper
    ) {
        parent::__construct($context, $registry);
        $this->configHelper = $configHelper;
    }

    /**
     * Validate and start creating the request
     *
     * @param string $format
     *
     * @return array|object|\stdClass
     */
    public function call($format = 'object')
    {
        $this->validate();

        return $this->request($format);
    }

    /**
     * @return bool|mixed
     * @throws \Exception
     */
    public function getToken()
    {
        $tmpPath = $this->path;
        $this->setPath(self::LOGIN_PATH);
        $token = $this->configHelper->getTokenKey(\Totaltools\Pronto\Helper\Config::CONFIG_PATH_TOKEN_KEY);
        if (!$token) {
            $result = $this->request();
            if ($result) {
                $token = $result->token;
            }
        }
        $this->token = $token;

        $this->path = $tmpPath;

        return $token;
    }

    /**
     * @param string $path
     *
     * @return \Totaltools\Pronto\Model\Api\Connect
     * @throws \Exception
     */
    public function setPath($path)
    {
        $path = trim(trim($path), '/');
        if (!in_array($path, $this->validPaths)) {
            throw new \Exception('Unknown path: ' . $path);
        }
        $this->path = $path;

        return $this;
    }

    /**
     * @param string $rootNode
     *
     * @return \Totaltools\Pronto\Model\Api\Connect
     */
    public function setRootNode($rootNode)
    {
        $this->rootNode = $rootNode;

        return $this;
    }

    /**
     * @param array $body
     *
     * @return \Totaltools\Pronto\Model\Api\Connect
     */
    public function setBody(array $body)
    {
        $this->body = $body;

        return $this;
    }

    /**
     * Create a request to Pronto and retrieve the result
     * (result type based on $format parameter)
     *
     * @param string $format
     *
     * @return \stdClass|array|object
     * */
    protected function request($format = 'object')
    {
        $c = curl_init();

        $isDebug = $this->configHelper->isDebug();
        $bodyXml = $this->getBodyXml();

        $headers = [
            'Content-type: application/xml',
            'X-Pronto-Content-Type: application/xml',
        ];

        if ($this->path === self::LOGIN_PATH) {
            $headers[] = 'X-Pronto-Username: ' . $this->configHelper->getConnectUsername();
            $headers[] = 'X-Pronto-Password: ' . $this->configHelper->getConnectPassword();
        } else {
            $headers[] = 'X-Pronto-Token: ' . $this->token;
        }
        if ($bodyXml) {
            $headers[] = 'Content-Length: ' . strlen($bodyXml);
        }

        curl_setopt_array($c, [
            CURLOPT_FAILONERROR    => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_POST           => true,
            CURLOPT_URL            => $this->getUrl(),
            CURLOPT_USERAGENT      => self::REQUEST_USERAGENT,
            CURLOPT_TIMEOUT        => self::REQUEST_TIMEOUT,
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_FOLLOWLOCATION => false
        ]);

        if ($bodyXml) {
            curl_setopt($c, CURLOPT_POSTFIELDS, $bodyXml);
        }

        curl_setopt($c, CURLOPT_FOLLOWLOCATION, false);

        if ($isDebug) {
            curl_setopt($c, CURLINFO_HEADER_OUT, true);
        }
        $isSandbox = (bool) $this->configHelper->isSandbox();
        if ($isSandbox === true) {
            curl_setopt($c, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($c, CURLOPT_SSL_VERIFYPEER, FALSE);
        }

        $result = curl_exec($c);

        if ($isDebug) {
            $this->_logger->info('Totaltools_Pronto Connect cURL info: ' . var_export(curl_getinfo($c), true));
            $this->_logger->info('Totaltools_Pronto Connect cURL request POST data: ' . var_export($bodyXml, true));
            $this->_logger->info('Totaltools_Pronto Connect cURL response data: ' . var_export($result, true));
            if (!$result) {
                $this->_logger->info('Totaltools_Pronto Connect cURL error: ' . curl_error($c));
            }
        }

        curl_close($c);

        // Turn XML response into Object
        $result = simplexml_load_string($result);

        switch ($format) {
            case 'xml':
                break;
            case 'json':
                $result = json_encode($result);
                break;
            case 'array':
                $result = json_encode($result);
                $result = json_decode($result, true);
                break;
            case 'object':
                $result = json_encode($result);
                $result = json_decode($result);
        }

        return $result;
    }

    protected function getUrl()
    {
        $url = trim(trim($this->configHelper->getConnectUrl()), '/');
        if ($this->path !== self::LOGIN_PATH) {
            $url .= '/api';
        }
        $url .= '/' . $this->path;

        return $url;
    }

    protected function getBodyXml()
    {
        if (!is_array($this->body) || !$this->body) {
            return;
        }

        if (!$this->rootNode) {
            throw new \Exception('No root node set for request');
        }
        $xml = new \SimpleXMLElement('<' . $this->rootNode . '/>');

        \Totaltools\Pronto\Helper\Data::arrayToXml($this->body, $xml);

        return $xml->asXML();
    }

    /**
     * Validate before create request
     *
     * @throws \Exception
     */
    protected function validate()
    {
        if (!$this->path) {
            throw new \Exception('No path specified');
        }
        if (!$this->token) {
            $this->getToken();
        }
    }
}
