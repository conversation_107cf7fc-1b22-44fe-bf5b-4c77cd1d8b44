<?php

namespace Totaltools\Pronto\Model\Api;

use Totaltools\Pronto\Helper\Config;
use Magento\Framework\App\Config\ConfigResource\ConfigInterface;
use Magento\Framework\App\Config\ScopeConfigInterface as ScopeConfig;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\Encryption\EncryptorInterface;


/**
 * Class GenerateToken
 * @package Totaltools\Pronto\Model\Api
 */
class GenerateToken
{
    const REQUEST_USERAGENT = 'Total Tools Magento 2 Application';
    const REQUEST_TIMEOUT = 60;
    /**
     * @var Config
     */
    private $configHelper;

    /**
     * @var ConfigInterface
     */
    private $config;

    /**
     * @var TimezoneInterface
     */
    private $timezone;

    /**
     * @var Connect
     */
    private $connect;

    /**
     * @var EncryptorInterface
     */
    private $encryptor;

    /**
     * GenerateToken constructor.
     * @param Config $configHelper
     * @param ConfigInterface $configInterface
     * @param TimezoneInterface $timezone
     * @param Connect $connect
     * @param EncryptorInterface $encryptor
     */
    public function __construct(
        Config $configHelper,
        ConfigInterface $configInterface,
        TimezoneInterface $timezone,
        Connect $connect,
        EncryptorInterface $encryptor
    ) {
        $this->configHelper = $configHelper;
        $this->config = $configInterface;
        $this->timezone = $timezone;
        $this->connect = $connect;
        $this->encryptor = $encryptor;
    }

    /**
     * @return bool|mixed
     * @throws \Exception
     */
    public function generate()
    {
        $result = $this->connect->getToken();
        if ($result) {
            $now = $this->timezone->date()->modify('+1 day')->format('d-m-Y H:i:s');
            $value = $this->encryptor->encrypt($result);
            $this->config->saveConfig(Config::CONFIG_PATH_TOKEN_EXPIRED_DATE, $now);
            $this->config->saveConfig(Config::CONFIG_PATH_TOKEN_KEY, $value);
        }

        return $result;
    }
}
