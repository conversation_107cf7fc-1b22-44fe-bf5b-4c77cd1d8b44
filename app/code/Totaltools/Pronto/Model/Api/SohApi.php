<?php

namespace Totaltools\Pronto\Model\Api;

use Totaltools\Pronto\Model\Api\Connect;
use Totaltools\Storelocator\Parser\File;

class Soh<PERSON>pi extends \Magento\Framework\Model\AbstractModel
{
    const REQUEST_USERAGENT = 'Total Tools Magento 2 Application';
    const REQUEST_TIMEOUT = 20;

    /**
     * @var object|mixed
     */
    protected $rootNode;

    /**
     * @var object|mixed
     */
    protected $body;

    /**
     * @var \Totaltools\Pronto\Helper\Config
     */
    protected $configHelper;

    /**
     * @var Connect
     */
    protected $connect;

    /**
     * @var File
     */
    protected $fileParser;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry      $registry
     * @param \Totaltools\Pronto\Helper\Config $configHelper
     * @param Connect $connect
     * @param File $fileParser
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Totaltools\Pronto\Helper\Config $configHelper,
        Connect $connect,
        File $fileParser
    ) {
        parent::__construct($context, $registry);
        $this->configHelper = $configHelper;
        $this->connect = $connect;
        $this->fileParser = $fileParser;
    }

    /**
     * Create a request to Pronto and retrieve the result
     * (result type based on $format parameter)
     *
     * @param string $endpoint
     * @param string $erpId
     * @param string $apiRequestType
     * @param string $format
     *
     * @return \stdClass|array|object
     * */
    public function sendApiSohRequest($endpoint, $erpId, $apiRequestType, $format = 'array')
    {
        $c = curl_init();

        $isDebug = $this->configHelper->isDebug();
        $bodyXml = $this->getBodyXml();

        $headers = [
            'Content-type: application/xml',
            'X-Pronto-Content-Type: application/xml',
        ];

        $headers[] = 'X-Pronto-Token: ' . $this->connect->getToken();

        if ($bodyXml) {
            $headers[] = 'Content-Length: ' . strlen($bodyXml);
        }

        curl_setopt_array($c, [
            CURLOPT_FAILONERROR    => true,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_POST           => true,
            CURLOPT_URL            => $endpoint,
            CURLOPT_USERAGENT      => self::REQUEST_USERAGENT,
            CURLOPT_TIMEOUT        => self::REQUEST_TIMEOUT,
            CURLOPT_HTTPHEADER     => $headers,
            CURLOPT_FOLLOWLOCATION => false
        ]);

        if ($bodyXml) {
            curl_setopt($c, CURLOPT_POSTFIELDS, $bodyXml);
        }

        curl_setopt($c, CURLOPT_FOLLOWLOCATION, false);

        if ($isDebug) {
            curl_setopt($c, CURLINFO_HEADER_OUT, true);
        }
        $isSandbox = (bool) $this->configHelper->isSandbox();
        if ($isSandbox === true) {
            curl_setopt($c, CURLOPT_SSL_VERIFYHOST, FALSE);
            curl_setopt($c, CURLOPT_SSL_VERIFYPEER, FALSE);
        }

        $result = curl_exec($c);

        if ($isDebug) {
            $this->_logger->info('Totaltools_Pronto Connect cURL info: ' . var_export(curl_getinfo($c), true));
            $this->_logger->info('Totaltools_Pronto Connect cURL response data: ' . var_export($result, true));
            if (!$result) {
                $this->_logger->info('Totaltools_Pronto Connect cURL error: ' . curl_error($c));
            }
        }

        curl_close($c);

        if (!str_contains($result, '<?xml')) {
            $this->_logger->info('Totaltools_Pronto SOH API Endpoint having error: ' . $endpoint);
            $this->_logger->info('Totaltools_Pronto SOH API Error: ' . $result);
            $this->fileParser->createSohApiResponseLogs($erpId, $apiRequestType, $result);
            return [];
        }

        // Turn XML response into Object
        $result = simplexml_load_string($result);

        switch ($format) {
            case 'xml':
                break;
            case 'json':
                $result = json_encode($result);
                break;
            case 'array':
                $result = json_encode($result);
                $result = json_decode($result, true);
                break;
            case 'object':
                $result = json_encode($result);
                $result = json_decode($result);
        }
        $this->fileParser->createSohApiResponseLogs($erpId, $apiRequestType, $result);
        return $result;
    }

    protected function getBodyXml()
    {
        if (!is_array($this->body) || !$this->body) {
            return;
        }

        if (!$this->rootNode) {
            throw new \Exception('No root node set for request');
        }
        $xml = new \SimpleXMLElement('<' . $this->rootNode . '/>');

        \Totaltools\Pronto\Helper\Data::arrayToXml($this->body, $xml);

        return $xml->asXML();
    }
}
