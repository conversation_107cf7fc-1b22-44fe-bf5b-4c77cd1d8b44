<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Model\ResourceModel\LoyaltyTier;

/**
 * Class Collection
 *
 * @package Totaltools\Pronto\Model\ResourceModel\LoyaltyTier
 * <AUTHOR> <<EMAIL>>
 */
class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    /**
     * Initialize loyalty tier resource collection
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init(
            \Totaltools\Pronto\Model\LoyaltyTier::class,
            \Totaltools\Pronto\Model\ResourceModel\LoyaltyTier::class
        );
    }
}
