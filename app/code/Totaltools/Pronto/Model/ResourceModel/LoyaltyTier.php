<?php
/**
 * Copyright © 2017 Balance Internet Pty., Ltd. All rights reserved.
 * See COPYING.txt for license details.
 */

namespace Totaltools\Pronto\Model\ResourceModel;

/**
 * Class LoyaltyTier
 *
 * @package Totaltools\Pronto\Model\ResourceModel
 * <AUTHOR> <<EMAIL>>
 */
class LoyaltyTier extends \Magento\Framework\Model\ResourceModel\Db\AbstractDb
{
    /**
     * Initialize tier resource model
     *
     * @return void
     */
    protected function _construct()
    {
        $this->_init('totaltools_loyalty_tiers', 'entity_id');
    }

    /**
     * Get tier identifier by tier id
     *
     * @param int $tierId
     *
     * @return int|false
     */
    public function getIdByTierId($tierId)
    {
        $connection = $this->getConnection();
        $select = $connection->select()->from('totaltools_loyalty_tiers', 'entity_id');

        if ( $tierId == "MAX") {
            $bind = [];
            $select->order("tier_id desc");
        } else {
            $select->where('tier_id = :tier_id');
            $bind = [':tier_id' => (int)$tierId];
        }

        return $connection->fetchOne($select, $bind);
    }

    /**
     * Get tier identifier by description
     *
     * @param string $description
     *
     * @return int|false
     */
    public function getIdByDescription($description)
    {
        $connection = $this->getConnection();

        $select = $connection->select()->from('totaltools_loyalty_tiers', 'entity_id')
            ->where('description = :description');

        $bind = [':description' => (string)$description];

        return $connection->fetchOne($select, $bind);
    }
}
