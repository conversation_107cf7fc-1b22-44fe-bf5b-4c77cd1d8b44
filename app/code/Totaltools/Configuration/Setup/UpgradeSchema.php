<?php

namespace Totaltools\Configuration\Setup;

use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;
use Magestore\Storepickup\Setup\InstallSchema as StorepickupSchema;

/**
 * Class UpgradeSchema
 *
 * @package Totaltools\Configuration\Setup
 */
class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * @var \Magento\Framework\App\Config\ConfigResource\ConfigInterface
     */
    private $_resourceConfig;

    /**
     * UpgradeSchema constructor.
     *
     * @param \Magento\Framework\App\Config\ConfigResource\ConfigInterface $resourceConfig
     */
    public function __construct(
        \Magento\Framework\App\Config\ConfigResource\ConfigInterface $resourceConfig
    )
    {
        $this->_resourceConfig = $resourceConfig;
    }

    /**
     * {@inheritdoc}
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        $version = $context->getVersion();

        if ($context->getVersion() && version_compare($version, '1.2.0') < 0) {
            $this->_removeStorepickupAttributes($setup);
        }

        if ($context->getVersion() && version_compare($version, '1.2.3') < 0) {
            $this->_resourceConfig->deleteConfig('totaltools_channel_advisor_api/ebay_fee/power_tools_category_id');

            $connection = $this->_resourceConfig->getConnection();
            $connection->delete(
                $connection->getTableName('core_config_data'),
                [
                    $connection->quoteInto('path LIKE ?', "channeladvisorapi%")
                ]
            );

            foreach ($this->_getChannelAdvisorApiTables() as $channelAdvisorApiTable) {
                $connection->dropTable($channelAdvisorApiTable);
            }
        }

        if ($context->getVersion() && version_compare($version, '1.2.8') < 0) {
            $connection = $this->_resourceConfig->getConnection();
            $connection->delete(
                $connection->getTableName('core_config_data'),
                [
                    $connection->quoteInto('path LIKE ?', "sociallogin%")
                ]
            );

            foreach ($this->_getSocialloginTables() as $socialloginTable) {
                $connection->dropTable($socialloginTable);
            }
        }

        $setup->endSetup();
    }

    /**
     * @return array
     */
    private function _getSocialloginTables()
    {
        return [
            'twlogin_customer',
            'authorlogin_customer',
            'vklogin_customer',
        ];
    }

    /**
     * @return array
     */
    private function _getChannelAdvisorApiTables()
    {
        return [
            'totaltools_channeladvisorapi_queue',
            'wiserobot_channeladvisorapi_accountidmap',
            'wiserobot_channeladvisorapi_attribute_map',
            'wiserobot_channeladvisorapi_attributeset_map',
            'wiserobot_channeladvisorapi_attributeset_classification_map',
            'wiserobot_channeladvisorapi_classification_map',
            'wiserobot_channeladvisorapi_dccodemap',
            'wiserobot_channeladvisorapi_imageplacement',
            'wiserobot_channeladvisorapi_shipmethod',
            'wiserobot_channeladvisorapi_carriermap',
            'wiserobot_channeladvisorapi_paymentmap',
            'wiserobot_channeladvisorapi_salessource',
            'wiserobot_channeladvisorapi_error',
            'wiserobot_channeladvisorapi_history_quantity',
            'wiserobot_channeladvisorapi_order',
            'wiserobot_channeladvisorapi_product_image',
            'wiserobot_channeladvisorapi_export_queue',
            'wiserobot_channeladvisorapi_log',
            'wiserobot_channeladvisorapi_labelmap',
            'wiserobot_channeladvisorapi_productdata',
            'wiserobot_channeladvisorapi_catalogrule',
            'wiserobot_channeladvisorapi_ruleproductid',
        ];
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    private function _removeStorepickupAttributes(SchemaSetupInterface $setup)
    {
        $columnsToDrop = [
            'allow_store_collection',
            'erp_id',
            'erp_code',
            'warehouse_code',
            'account_mode',
            'account_sandbox',
            'account_username',
            'account_password',
            'account_clientid',
            'supporting_origins',
            'zone_id'
        ];

        foreach ($columnsToDrop as $column) {
            $setup->getConnection()->dropColumn(
                $setup->getTable(StorepickupSchema::SCHEMA_STORE),
                $column
            );
        }
        $setup->getConnection()->dropIndex(
            $setup->getTable(StorepickupSchema::SCHEMA_STORE),
            $setup->getIdxName(StorepickupSchema::SCHEMA_STORE, ['zone_id'])
        );
        $setup->getConnection()->dropForeignKey(
            $setup->getTable(StorepickupSchema::SCHEMA_STORE),
            $setup->getFkName(StorepickupSchema::SCHEMA_STORE, 'zone_id', 'magestore_storepickup_zone', 'zone_id')
        );

        // drop zone table
        $setup->getConnection()->dropTable(
            'magestore_storepickup_zone'
        );
        // drop inventory table
        $setup->getConnection()->dropTable(
            'magestore_storepickup_store_inventory'
        );
    }
}