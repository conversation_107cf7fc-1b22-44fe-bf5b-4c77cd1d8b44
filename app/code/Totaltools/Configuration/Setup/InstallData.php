<?php
namespace Totaltools\Configuration\Setup;

use Magento\Framework\Setup\InstallDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

/**
 * Class InstallData
 * @package Totaltools\Configuration\Setup
 */
class InstallData implements InstallDataInterface
{
    /**
     * Installs the data.
     *
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface   $context
     *
     * @return void
     */
    public function install(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();
        $connection = $setup->getConnection();
        $connection->delete(
            $connection->getTableName('core_config_data'),
            'path LIKE "%temando%"'
        );

        $setup->endSetup();
    }
}