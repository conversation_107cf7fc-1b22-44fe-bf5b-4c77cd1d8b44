<?php
/**
 * Magento
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/osl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * @category  Totaltools
 * @package   Totaltools_Configuration
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Configuration\Setup;

use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\App\Config\ScopeConfigInterface as ScopeConfig;

/**
 * Class UpgradeData.
 *
 * @category  Totaltools
 * @package   Totaltools_Configuration
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright 2019 Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var string $_setupVersion Current setup version.
     */
    private $_setupVersion;
    /**
     * @var \Magento\Framework\App\Config\ConfigResource\ConfigInterface
     */
    private $_config;
    /**
     * @var \Magento\Framework\Module\Status
     */
    private $_status;

    /**
     * UpgradeData constructor.
     *
     * @param \Magento\Framework\App\Config\ConfigResource\ConfigInterface $config
     */
    public function __construct(
        \Magento\Framework\App\Config\ConfigResource\ConfigInterface $config,
        \Magento\Framework\Module\Status $status
    )
    {
        $this->_config = $config;
        $this->_status = $status;
    }

    /**
     * Upgrades the data.
     *
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface   $context
     *
     * @return void
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        // Share current setup version with all class functionality.
        $this->_setupVersion = $context->getVersion();

        $setup->startSetup();

        $this->_changeB2bConfiguration();
        $this->_deleteAdvisorapiAttribute($setup);
        $this->_updateTemandoAttribute($setup);
        $this->_createInventoryImportConfiguration();
        $this->_disableVertexTaxModule();
        $this->_disableWyomindCronScheduler();
        $this->_disableCreationCompany();
        $this->_updateShippitSyncMode();

        $setup->endSetup();
    }

    /**
     * Change shippit sync mode to cron
     *
     * @return void
     */
    private function _updateShippitSyncMode()
    {
        if (version_compare($this->_setupVersion, '1.2.10') < 0) {
            $this->_config->saveConfig(
                'shippit/sync_order/mode',
                \Shippit\Shipping\Model\Config\Source\Shippit\Sync\Order\Mode::SCHEDULED,
                'default',
                0
            );
        }
    }

    /**
     * Disable Wyomind_CronScheduler module.
     *
     * @return void
     */
    private function _disableWyomindCronScheduler()
    {
        if (version_compare($this->_setupVersion, '1.2.7') < 0) {
            $this->_config->saveConfig('advanced/modules_disable_output/Wyomind_CronScheduler', true, 'default', 0);
            $this->_status->setIsEnabled(false, ['Wyomind_CronScheduler']);
        }
    }

    /**
     * Disable Vertex_Tax module.
     *
     * @return void
     */
    private function _disableVertexTaxModule()
    {
        if (version_compare($this->_setupVersion, '1.2.6') < 0) {
            $this->_config->saveConfig('advanced/modules_disable_output/Vertex_Tax', true, 'default', 0);
            $this->_status->setIsEnabled(false, ['Vertex_Tax']);
        }
    }

    /**
     * Save inventory import configuration
     *
     * @return void
     */
    private function _createInventoryImportConfiguration()
    {
        if (version_compare($this->_setupVersion, '1.2.5') < 0) {
            $this->_config->saveConfig(
                \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_DIRECTORY,
                '/mnt/web/sftp/Temando',
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
            $this->_config->saveConfig(
                \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_DAYS,
                7,
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
            $this->_config->saveConfig(
                \Totaltools\Storelocator\Model\Config\Source\Inventory::CONFIG_INVENTORY_LOG,
                1,
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
        }
    }

    /**
     * Change B2B settings
     */
    private function _changeB2bConfiguration()
    {
        if (version_compare($this->_setupVersion, '1.2.1') < 0) {
            $this->_config->saveConfig(
                'btob/website_configuration/company_active',
                1,
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
            $this->_config->saveConfig(
                'btob/website_configuration/quickorder_active',
                1,
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
            $this->_config->saveConfig(
                'btob/website_configuration/requisition_list_active',
                1,
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
            $this->_config->saveConfig(
                'btob/website_configuration/sharedcatalog_active',
                1,
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
        }
    }

    /**

     * Delete channeladvisorapi attribute group
     * Delete channeladvisorapi attributes
     *
     * @param ModuleDataSetupInterface $setup
     */
    private function _deleteAdvisorapiAttribute(ModuleDataSetupInterface $setup)
    {
        if (version_compare($this->_setupVersion, '1.2.4') < 0) {
            $setup->getConnection()->delete(
                $setup->getTable('eav_attribute_group'),
                'attribute_group_code = "channeladvisor"'
            );
            $setup->getConnection()->delete(
                $setup->getTable('eav_attribute'),
                'attribute_code = "ca_is_in_relationship" OR attribute_code = "ca_relationship_name" OR attribute_code = "ca_is_parent" OR attribute_code = "ca_parent_sku"'
            );
        }
    }

    /**
     * Update temando attribute group
     *
     * @param ModuleDataSetupInterface $setup
     */
    private function _updateTemandoAttribute(ModuleDataSetupInterface $setup)
    {
        if (version_compare($this->_setupVersion, '1.2.2') < 0) {
            $setup->getConnection()->update(
                $setup->getTable('eav_attribute_group'),
                [
                    'attribute_group_name' => 'Shipping',
                    'attribute_group_code' => 'totaltools_storelocator'
                ],
                'attribute_group_code = "temando"'

            );
        }
    }

    /**
     * Disable allowance registration company from front side.
     *
     * @param ModuleDataSetupInterface $setup
     */
    private function _disableCreationCompany()
    {
        if (version_compare($this->_setupVersion, '1.2.9') < 0) {
            $this->_config->saveConfig(
                'company/general/allow_company_registration',
                0,
                ScopeConfig::SCOPE_TYPE_DEFAULT,
                0
            );
        }
    }
}