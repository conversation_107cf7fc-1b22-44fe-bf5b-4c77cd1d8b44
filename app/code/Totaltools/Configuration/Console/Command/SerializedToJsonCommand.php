<?php
/**
 * Totaltools Configuration.
 *
 * @category   Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright  2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Configuration\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;

/**
 * Class SerializedToJsonCommand
 * @package Totaltools\Configuration\Console\Command
 */
class SerializedToJsonCommand extends Command
{
    /**
     * @var \Magento\Config\Model\ResourceModel\Config\Data\CollectionFactory
     */
    private $configFactory;

    /**
     * @var \Magento\Framework\DB\DataConverter\SerializedToJson
     */
    private $serializedToJson;

    /**
     * SerializedToJsonCommand constructor.
     * @param \Magento\Config\Model\ResourceModel\Config\Data\CollectionFactory $configFactory
     * @param \Magento\Framework\DB\DataConverter\SerializedToJson $serializedToJson
     * @param string|null $name
     */
    public function __construct(
        \Magento\Config\Model\ResourceModel\Config\Data\CollectionFactory $configFactory,
        \Magento\Framework\DB\DataConverter\SerializedToJson $serializedToJson,
        string $name = null
    ) {
        parent::__construct($name);
        $this->configFactory = $configFactory;
        $this->serializedToJson = $serializedToJson;
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName('totaltools:config:serialized-to-json');
        $this->setDescription('Serialized To Json');
    }

    /**
     * @inheritdoc
     */
    public function execute(InputInterface $input, OutputInterface $output)
    {
        $this->serializedToJson();
        $output->writeln('Fix serialized data progress is finished.');
    }

    /**
     * Change Serialized Data to Json.
     */
    private function serializedToJson()
    {
        /** @var \Magento\Config\Model\ResourceModel\Config\Data\Collection $collection */
        $collection = $this->configFactory->create();
        /** @var \Magento\Framework\App\Config\Value $item */
        foreach ($collection as $item) {
            $data = $item->getValue();
            if ($this->isSerialized($data)) {
                $item->setValue($this->serializedToJson->convert($item->getValue()));
                $item->save();
            }
        }
    }

    /**
     * check data is serialized
     *
     * @param $data
     * @param bool $strict
     * @return bool
     */
    private function isSerialized($data, $strict = true)
    {
        if (!is_string($data)) {
            return false;
        }
        $data = trim($data);
        if ('N;' === $data) {
            return true;
        }
        if (strlen($data) < 4) {
            return false;
        }
        if (':' !== $data[1]) {
            return false;
        }
        if ($strict) {
            $lastc = substr($data, -1);
            if (';' !== $lastc && '}' !== $lastc) {
                return false;
            }
        } else {
            $semicolon = strpos($data, ';');
            $brace = strpos($data, '}');
            if (false === $semicolon && false === $brace) {
                return false;
            }
            if (false !== $semicolon && $semicolon < 3) {
                return false;
            }
            if (false !== $brace && $brace < 4) {
                return false;
            }
        }
        $token = $data[0];
        switch ($token) {
            case 's':
                if ($strict) {
                    if ('"' !== substr($data, -2, 1)) {
                        return false;
                    }
                } elseif (false === strpos($data, '"')) {
                    return false;
                }
            case 'a':
            case 'O':
                return (bool)preg_match("/^{$token}:[0-9]+:/s", $data);
            case 'b':
            case 'i':
            case 'd':
                $end = $strict ? '$' : '';
                return (bool)preg_match("/^{$token}:[0-9.E-]+;$end/", $data);
        }
        return false;
    }
}
