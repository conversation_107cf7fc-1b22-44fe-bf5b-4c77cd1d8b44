<?xml version="1.0"?>
<!--
/**
 * Totaltools Configuration.
 *
 * @category   Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright  2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="serialized_to_json"
                      xsi:type="object">Totaltools\Configuration\Console\Command\SerializedToJsonCommand</item>
            </argument>
        </arguments>
    </type>
</config>
