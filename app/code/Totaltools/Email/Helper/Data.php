<?php
namespace Totaltools\Email\Helper;

class Data
    extends \Magento\Framework\App\Helper\AbstractHelper
{
    protected $storeMail;
    protected $storePhone;
    protected $storeAbn;

    const CONFIG_STORE_EMAIL    = 'trans_email/ident_sales/email';
    const CONFIG_STORE_PHONE    = 'general/store_information/phone';
    const CONFIG_STORE_ABN      = 'general/store_information/abn';

    /**
     * @return string
     */
    public function getStoreEmail () {
        return $this->scopeConfig->getValue(self::CONFIG_STORE_EMAIL);
    }


    /**
     * @return string
     */
    public function getStorePhone () {
        return $this->scopeConfig->getValue(self::CONFIG_STORE_PHONE);
    }

    /**
     * @return string
     */
    public function getStoreAbn() {
        return $this->scopeConfig->getValue(self::CONFIG_STORE_ABN);
    }
}