<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="connector_developer_settings">
            <group id="cron_schedules">
                <field id="abandoned_cart" translate="label" sortOrder="60" type="select" showInStore="0" showInWebsite="1"
                       showInDefault="1">
                    <label>Abandoned Cart</label>
                    <source_model>Totaltools\Email\Model\Config\Developer\AbandonedExpressions</source_model>
                </field>
            </group>
        </section>
        <section id="design">
            <group id="email" translate="label" sortOrder="60" type="select" showInStore="0" showInWebsite="1"
                   showInDefault="1">
                <label>Quote Email</label>
                <field id="header_template_b2b" translate="label" sortOrder="10" type="select" showInStore="0" showInWebsite="1"
                       showInDefault="1">
                    <label>Design Email Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="email_nav_bar" translate="label" sortOrder="10" type="select" showInStore="0" showInWebsite="1"
                       showInDefault="1">
                    <label>Design Email Navbar Template</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
