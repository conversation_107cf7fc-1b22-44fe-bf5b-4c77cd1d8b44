<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Theme\Model\Design\Config\MetadataProvider">
        <arguments>
            <argument name="metadata" xsi:type="array">
                <item name="email_logo" xsi:type="array">
                    <item name="path" xsi:type="string">design/email/logo</item>
                    <item name="fieldset" xsi:type="string">other_settings/email</item>
                    <item name="backend_model" xsi:type="string">Magento\Email\Model\Design\Backend\Logo</item>
                    <item name="base_url" xsi:type="array">
                        <item name="type" xsi:type="string">media</item>
                        <item name="scope_info" xsi:type="string">1</item>
                        <item name="value" xsi:type="string">email/logo</item>
                    </item>
                </item>
                <item name="email_logo_alt" xsi:type="array">
                    <item name="path" xsi:type="string">design/email/logo_alt</item>
                    <item name="fieldset" xsi:type="string">other_settings/email</item>
                </item>
                <item name="email_logo_width" xsi:type="array">
                    <item name="path" xsi:type="string">design/email/logo_width</item>
                    <item name="fieldset" xsi:type="string">other_settings/email</item>
                </item>
                <item name="email_logo_height" xsi:type="array">
                    <item name="path" xsi:type="string">design/email/logo_height</item>
                    <item name="fieldset" xsi:type="string">other_settings/email</item>
                </item>
                <item name="email_header_template_new" xsi:type="array">
                    <item name="path" xsi:type="string">design/email/header_template_new</item>
                    <item name="fieldset" xsi:type="string">other_settings/email</item>
                </item>
                <item name="email_footer_template_new" xsi:type="array">
                    <item name="path" xsi:type="string">design/email/footer_template_new</item>
                    <item name="fieldset" xsi:type="string">other_settings/email</item>
                </item>
            </argument>
        </arguments>
    </type>

   <type name="Magento\Variable\Model\Config\Structure\AvailableVariables">
        <arguments>
            <argument name="configPaths" xsi:type="array">
                <item name="general/store_information" xsi:type="array">
                    <item name="general/store_information/abn" xsi:type="string">1</item>
                </item>
            </argument>
        </arguments>
    </type>
</config>
