<?php
/**
 * @package     Totaltools_Sales
 * <AUTHOR> <<EMAIL>>
 * @copyright   2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Email\Setup;

use Magento\Cms\Model\BlockFactory;
use Magento\Cms\Model\BlockRepository;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;

/**
 * Data upgrade script
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var BlockFactory
     */
    private $blockFactory;
    /**
     * @var BlockRepository
     */
    private $blockRepository;

    public function __construct(
        BlockFactory $blockFactory,
        BlockRepository $blockRepository
    ) {
        $this->blockFactory = $blockFactory;
        $this->blockRepository = $blockRepository;
    }
    /**
     * {@inheritdoc}
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        if (version_compare($context->getVersion(), '0.1.1', '<')) {
            $this->addB2bEmailHeaderStaticBlock();
        }
    }

    /**
     * @throws \Magento\Framework\Exception\CouldNotSaveException
     */
    private function addB2bEmailHeaderStaticBlock()
    {
        try {
            $b2bHeaderBlock = [
                'title' => 'Email B2B Header Content ',
                'identifier' => 'email_b2b_header_content',
                'stores' => ['0','1'],
                'is_active' => 1,
                'content' => "<p><a class=\"logo logo-b2b\" href=\"{{store url=\"\"}}\"> <img src=\"{{view url='Magento_Email/commercial_email_logo.png'}}\" alt=\"\" /> </a></p>"
            ];
            $block = $this->blockFactory->create(['data' => $b2bHeaderBlock]);
            $this->blockRepository->save($block);
        } catch(Exception $e) {
            echo $e->getMessage();
        }
    }
}