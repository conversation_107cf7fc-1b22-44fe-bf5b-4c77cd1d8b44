<?php

namespace Totaltools\Email\Model\Config\Developer;

/**
 * Class AbandonedExpressions
 * @package Totaltools\Email\Model\Config\Developer
 */
class AbandonedExpressions implements \Magento\Framework\Data\OptionSourceInterface
{
    /**
     * Get options.
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            ['value' => '*/15 * * * *', 'label'  => __('Every 15 Minutes')],
            ['value' => '*/30 * * * *', 'label'  => __('Every 30 Minutes')],
            ['value' => '00 * * * *', 'label'    => __('Every 60 Minutes')],
            ['value' => '00 */2 * * *', 'label'  => __('Every 2 Hours')],
            ['value' => '00 */3 * * *', 'label'  => __('Every 3 Hours')],
            ['value' => '00 */4 * * *', 'label'  => __('Every 4 Hours')],
            ['value' => '00 */5 * * *', 'label'  => __('Every 5 Hours')],
            ['value' => '00 */6 * * *', 'label'  => __('Every 6 Hours')],
            ['value' => '00 */7 * * *', 'label'  => __('Every 7 Hours')],
            ['value' => '00 */8 * * *', 'label'  => __('Every 8 Hours')],
            ['value' => '00 */9 * * *', 'label'  => __('Every 9 Hours')],
            ['value' => '00 */10 * * *', 'label' => __('Every 10 Hours')],
            ['value' => '00 */11 * * *', 'label' => __('Every 11 Hours')],
            ['value' => '00 */12 * * *', 'label' => __('Every 12 Hours')],
            ['value' => '00 */13 * * *', 'label' => __('Every 13 Hours')],
            ['value' => '00 */14 * * *', 'label' => __('Every 14 Hours')],
            ['value' => '00 */15 * * *', 'label' => __('Every 15 Hours')],
            ['value' => '00 */16 * * *', 'label' => __('Every 16 Hours')],
            ['value' => '00 */17 * * *', 'label' => __('Every 17 Hours')],
            ['value' => '00 */18 * * *', 'label' => __('Every 18 Hours')],
            ['value' => '00 */19 * * *', 'label' => __('Every 19 Hours')],
            ['value' => '00 */20 * * *', 'label' => __('Every 20 Hours')],
            ['value' => '00 */21 * * *', 'label' => __('Every 21 Hours')],
            ['value' => '00 */22 * * *', 'label' => __('Every 22 Hours')],
            ['value' => '00 */23 * * *', 'label' => __('Every 23 Hours')],
            ['value' => '00 */24 * * *', 'label' => __('Every 24 Hours')]
        ];
    }
}
