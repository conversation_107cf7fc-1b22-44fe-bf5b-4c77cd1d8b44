<?php

namespace Totaltools\Email\Block\Order;

class Footer extends \Magento\Framework\View\Element\Template
{
    protected $dataHelper;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Totaltools\Email\Helper\Data $helper
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Totaltools\Email\Helper\Data $helper,
        array $data = [])
    {
        $this->dataHelper = $helper;
        parent::__construct($context, $data);
    }

    /**
     * get store email
     * @return string
     */
    public function getStoreEmail()
    {
        return $this->dataHelper->getStoreEmail();
    }

    /**
     * get store phone
     * @return string
     */
    public function getStorePhone()
    {
        return $this->dataHelper->getStorePhone();
    }

    /**
     * get store abn
     * @return string
     */
    public function getStoreAbn()
    {
        return $this->dataHelper->getStoreAbn();
    }

}
?>