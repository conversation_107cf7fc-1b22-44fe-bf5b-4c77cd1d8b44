<?php

namespace Totaltools\InventoryInStorePickup\Block\Checkout;

use Magento\InventoryInStorePickupFrontend\Block\Checkout\Onepage\Success\IsOrderStorePickup;

class SuccessPickup extends IsOrderStorePickup
{
    // Add your custom methods here for handling order data, store information, etc.
    
    public function getOrderData()
    {
        return 'getOrderData';
    }
    
    public function isClickAndCollect()
    {
        // Move click & collect logic here
    }
    
    public function getStoreData()
    {
        // Move store data logic here
    }
}