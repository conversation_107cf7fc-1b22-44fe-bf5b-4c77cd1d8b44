<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Quote\Model;


class Address extends \Magento\Quote\Model\Quote\Address
{

    /**
     * Retrieve all grouped shipping rates
     *
     * @return array
     */
    public function getGroupedAllShippingRates()
    {
        $_objectManager = \Magento\Framework\App\ObjectManager::getInstance();
        $carrierFactory = $_objectManager->get('Magento\Shipping\Model\CarrierFactoryInterface');
        $ratesCollection = $this->getShippingRatesCollection();
        $rates = [];
        $filterOut = [];
        $carriers = [];
        $customSort= ['shippitcc', 'freeshipping','Standard', 'Express', 'ondemand', 'Priority'] ;
        foreach ($ratesCollection as $rate) {
            if($rate->getCarrier() == 'freeshipping') {
                $filterOut = 'Standard';
                break;
            }
        }
        foreach ($ratesCollection as $rate) {
            if (!empty($filterOut) && $rate->getMethod() == $filterOut) {
                continue;
            }
            if (!$rate->isDeleted()) {
                $carrierCode = $rate->getCarrier();
                
                if (!isset($carriers[$carrierCode])) {
                    $carriers[$carrierCode] = $carrierFactory->get($carrierCode);
                }
                if ($carriers[$carrierCode]) {
                    if (!isset($rates[$carrierCode])) {
                        $rates[$carrierCode] = [];
                    }
                    $rates[$carrierCode][] = $rate;
                }
            }
        }
        $sortedShippingMethods = [];
        foreach ($customSort as $index) {
            foreach ($rates as $shippingMethod) {
                $code = $shippingMethod[0]->getMethod();
                if (str_contains($code , $index)) {
                    $sortedShippingMethods[] = $shippingMethod;
                }
            }
        }
        return $sortedShippingMethods;
    }

   
}
