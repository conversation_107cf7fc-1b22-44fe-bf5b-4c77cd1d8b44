<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Quote\Model;
use Magento\Customer\Api\Data\CustomerInterface;
use Magento\Customer\Api\Data\GroupInterface;
use Magento\Framework\Model\AbstractExtensibleModel;
use Magento\Quote\Api\Data\PaymentInterface;
use Magento\Quote\Model\Quote\Address;
use Magento\Sales\Model\ResourceModel;
use Magento\Sales\Model\Status;
use Magento\Framework\Api\AttributeValueFactory;
use Magento\Framework\Api\ExtensionAttribute\JoinProcessorInterface;

class Quote extends \Magento\Quote\Model\Quote
{
    /**
     * Merge quotes
     *
     * @param   Quote $quote
     * @return $this
     */
    public function merge(\Magento\Quote\Model\Quote $quote)
    {
        $this->_eventManager->dispatch(
            $this->_eventPrefix . '_merge_before',
            [$this->_eventObject => $this, 'source' => $quote]
        );

        // Check quote data
        $giftCurrentQuote = 0;
        $giftNewQuote = 0;
        foreach ($this->getAllVisibleItems() as $item) {
            if ($item->getProductType() == 'giftcard') {
                $giftCurrentQuote = 1;
                break;
            }
        }

        foreach ($quote->getAllVisibleItems() as $item) {
            if ($item->getProductType() == 'giftcard') {
                $giftNewQuote = 1;
                break;
            }
        }
        // End check quote

        // Start check add/remove
        foreach ($this->getAllVisibleItems() as $item) {
            if ($giftCurrentQuote != $giftNewQuote) {
                if ($giftCurrentQuote == 1) {
                    if ($item->getProductType() == 'giftcard') {
                        $item->delete();
                    }
                } else {
                    if ($item->getProductType() != 'giftcard') {
                        $item->delete();
                    }
                }
            }
        }

        foreach ($quote->getAllVisibleItems() as $item) {
            $found = false;
            foreach ($this->getAllItems() as $quoteItem) {
                if ($quoteItem->compare($item)) {
                    $quoteItem->setQty($quoteItem->getQty() + $item->getQty());
                    $found = true;
                    break;
                }
            }

            if (!$found) {
                $item->setExclude(false);
                if ($giftCurrentQuote != $giftNewQuote) {
                    if ($giftCurrentQuote == 1) {
                        if ($item->getProductType() == 'giftcard') {
                            $item->setExclude(true);
                        }
                    } else {
                        if ($item->getProductType() != 'giftcard') {
                            $item->setExclude(true);
                        }
                    }
                }
                if ($item->getExclude() == false) {
                    $newItem = clone $item;
                    $this->addItem($newItem);
                    if ($item->getHasChildren()) {
                        foreach ($item->getChildren() as $child) {
                            $newChild = clone $child;
                            $newChild->setParentItem($newItem);
                            $this->addItem($newChild);
                        }
                    }
                } else {
                    $item->delete();
                }
            }
        }


        // End start add/remove/update

        /**
         * Init shipping and billing address if quote is new
         */
        if (!$this->getId()) {
            $this->getShippingAddress();
            $this->getBillingAddress();
        }

        if ($quote->getCouponCode()) {
            $this->setCouponCode($quote->getCouponCode());
        }

        $this->_eventManager->dispatch(
            $this->_eventPrefix . '_merge_after',
            [$this->_eventObject => $this, 'source' => $quote]
        );

        return $this;
    }
}
