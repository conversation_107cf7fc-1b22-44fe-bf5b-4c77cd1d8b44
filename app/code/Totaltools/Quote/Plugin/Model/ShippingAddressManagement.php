<?php
/**
 * Totaltools Quote.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Quote\Plugin\Model;

use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\NoSuchEntityException;
/**
 * Class ShippingAddressManagement
 * @package Totaltools\Quote\Plugin\Model
 */
class ShippingAddressManagement
{
    /**
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private $quoteRepository;

    /**
     * @var \Magento\Quote\Model\QuoteAddressValidator
     */
    private $addressValidator;

    /**
     * @var \Magento\Customer\Api\AddressRepositoryInterface
     */
    private $addressRepository;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * ShippingAddressManagement constructor.
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\Quote\Model\QuoteAddressValidator $addressValidator
     * @param \Magento\Customer\Api\AddressRepositoryInterface $addressRepository
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\Quote\Model\QuoteAddressValidator $addressValidator,
        \Magento\Customer\Api\AddressRepositoryInterface $addressRepository,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->quoteRepository = $quoteRepository;
        $this->addressValidator = $addressValidator;
        $this->addressRepository = $addressRepository;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Quote\Model\ShippingAddressManagement $subject
     * @param \Closure $proceed
     * @param $cartId
     * @param \Magento\Quote\Api\Data\AddressInterface $address
     * @return int|null
     * @throws InputException
     * @throws NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function aroundAssign(
        \Magento\Quote\Model\ShippingAddressManagement $subject,
        \Closure $proceed,
        $cartId,
        \Magento\Quote\Api\Data\AddressInterface $address
    ) {
        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->getActive($cartId);
        if ($quote->isVirtual()) {
            throw new NoSuchEntityException(
                __('Cart contains virtual product(s) only. Shipping address is not applicable.')
            );
        }

        $saveInAddressBook = $address->getSaveInAddressBook() ? 1 : 0;
        $sameAsBilling = $address->getSameAsBilling() ? 1 : 0;
        $customerAddressId = $address->getCustomerAddressId();
        $this->addressValidator->validateForCart($quote, $address);
        $quote->setShippingAddress($address);
        $address = $quote->getShippingAddress();

        if ($customerAddressId === null) {
            $address->setCustomerAddressId(null);
        }

        if ($customerAddressId) {

            if (class_exists('\Zip\ZipPayment\Model\Config') &&  $quote->getPayment()->getMethod() !== \Zip\ZipPayment\Model\Config::METHOD_CODE
                && $quote->getShippingAddress()->getShippingMethod() !== 'shippitcc_shippitcc') {
                $addressData = $this->addressRepository->getById($customerAddressId);
                $address = $quote->getShippingAddress()->importCustomerAddressData($addressData);
            }
        } elseif ($quote->getCustomerId()) {
            $address->setEmail($quote->getCustomerEmail());
        } else {
            if (!$address->getEmail()) {
                $address->setEmail($quote->getCustomerEmail());
            }
        }

        $address->setSameAsBilling($sameAsBilling);
        $address->setSaveInAddressBook($saveInAddressBook);
        $address->setCollectShippingRates(true);

        try {
            $address->save();
        }
        catch (\Magento\Framework\Validator\Exception $validationException) {
            throw new InputException(__($validationException->getMessage()));
        }
        catch (\Exception $e) {
            $this->logger->critical($e);
            throw new InputException(__('Unable to save address. Please check input data.'));
        }

        return $quote->getShippingAddress()->getId();
    }
}
