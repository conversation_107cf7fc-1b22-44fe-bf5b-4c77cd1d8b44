<?php

namespace Totaltools\Quote\Plugin\Model;

/**
 * @category    Totaltools
 * @package     Totaltools_Quote
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2020 Totaltools. <https://totaltools.com.au>
 */

use Magento\Quote\Api\Data\ShippingMethodInterface;
use Magento\Quote\Api\ShipmentEstimationInterface;
use Magento\Quote\Api\ShippingMethodManagementInterface;
use Magento\Checkout\Model\Session as CheckoutSession;
use Totaltools\Checkout\Helper\TotalToolsConfig;

class DangerousGoodsMethodsFilter
{
    /**
     * @var string
     */
    const SHIPPING_DANGEROUS_KEY = 'shipping_dangerous';

    /**
     * @var CheckoutSession
     */
    protected $checkoutSession;

    /**
     * @var TotalToolsConfig
     */
    protected $totalToolsConfig;

    /**
     * @param CheckoutSession $checkoutSession
     * @param TotalToolsConfig $totalToolsConfig
     */
    public function __construct(
        CheckoutSession $checkoutSession,
        TotalToolsConfig $totalToolsConfig
    ) {
        $this->checkoutSession = $checkoutSession;
        $this->totalToolsConfig = $totalToolsConfig;
    }

    /**
     * @param ShipmentEstimationInterface $subject
     * @param ShippingMethodInterface[] $result
     * @return ShippingMethodInterface[]
     */
    public function afterEstimateByExtendedAddress($subject, $result)
    {
        return $this->processShippingMethods($result);
    }

    /**
     * @param ShippingMethodManagementInterface $subject
     * @param ShippingMethodInterface[] $result
     * @return ShippingMethodInterface[]
     */
    public function afterEstimateByAddressId($subject, $result)
    {
        return $this->processShippingMethods($result);
    }

    /**
     * @param ShippingMethodInterface[] $shippingMethods
     * @return ShippingMethodInterface[]
     */
    protected function processShippingMethods($shippingMethods)
    {
        if (count($shippingMethods) && $this->_hasDangerousItems()) {
            $shippingMethods = $this->_filterMethods($shippingMethods);
        }

        return $shippingMethods;
    }

    /**
     * @param ShippingMethodInterface[] $methods
     * @return ShippingMethodInterface[]
     */
    private function _filterMethods($methods)
    {
        $storePickupMethod = $this->totalToolsConfig->getDefaultShippingMethod();

        return array_values(
            array_filter($methods, function ($method) use ($storePickupMethod) {
                /** @var ShippingMethodInterface $method */
                $methodName = $method->getCarrierCode() . '_' . $method->getMethodCode();

                return $methodName == $storePickupMethod;
            })
        );
    }

    /**
     * @return boolean
     */
    private function _hasDangerousItems()
    {
        $items = $this->checkoutSession->getQuote()->getAllVisibleItems();
        $hasDangerous = false;

        if (count($items)) {
            foreach ($items as $item) {
                $_product = $item->getProduct();
                $_isDangerous = (bool) $_product->getData(self::SHIPPING_DANGEROUS_KEY);

                if ($_isDangerous) {
                    $hasDangerous = true;
                    break;
                }
            }
        }

        return $hasDangerous;
    }

    /**
     * @param \Magento\Quote\Model\ShippingAddressManagement $subject
     * @param string $cartId
     * @param string $addressId
     * @return array
     */
    public function beforeEstimateByAddressId($subject, $cartId, $addressId)
    {
        return [$cartId, (int) $addressId];
    }
}
