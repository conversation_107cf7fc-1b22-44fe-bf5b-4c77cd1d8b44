<?php

namespace Totaltools\Megamenu\Block;

use Magento\Catalog\Helper\Data;
use Magento\Framework\View\Element\Template\Context;
use Magento\Store\Model\Store;
use Magento\Framework\Registry;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Catalog\Api\CategoryRepositoryInterface;
use Magento\Catalog\Model\ResourceModel\Category\CollectionFactory as CategoryCollectionFactory;

class Crumbs extends \Magedelight\Megamenu\Block\Crumbs
{
    /**
     * Catalog data
     * @var Data
     */
    private $catalogData = null;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    protected $categoryRepository;
    protected $categoryCollectionFactory;

    /**
     * Crumbs constructor.
     * @param Context $context
     * @param Data $catalogData
     * @param Registry $registry
     * @param ScopeConfigInterface $scopeConfig
     * @param CategoryRepositoryInterface $categoryRepository
     * @param CategoryCollectionFactory $categoryCollectionFactory
     * @param array $data
     */
    public function __construct(
        Context $context,
        Data $catalogData,
        Registry $registry,
        ScopeConfigInterface $scopeConfig,
        CategoryRepositoryInterface $categoryRepository,
        CategoryCollectionFactory $categoryCollectionFactory,
        \Magento\Framework\App\Request\Http $request,
        array $data = []
    ) {
        $this->catalogData = $catalogData;
        $this->registry = $registry;
        $this->request = $request;
        $this->scopeConfig = $scopeConfig;
        $this->categoryRepository = $categoryRepository;
        $this->categoryCollectionFactory = $categoryCollectionFactory;
        parent::__construct($context, $catalogData, $registry,$data,$scopeConfig, $request);
    }

    public function getCrumbs()
    {
        $categoriesIds = $this->request->getParam('category');
        $evercrumbs = [];
        $refererUrl = $this->request->getServer('HTTP_REFERER');
        
        $evercrumbs[] = [
            'label' => 'Home',
            'title' => 'Go to Home Page',
            'link' => $this->_storeManager->getStore()->getBaseUrl()
        ];
        $product = $this->registry->registry('current_product');

        if ($refererUrl) {
            if (str_contains($refererUrl, 'catalogsearch/result')) {
                // Parse the URL and extract query parameters
                $parsedUrl = parse_url($refererUrl);
                if (isset($parsedUrl['query'])) {
                    parse_str($parsedUrl['query'], $queryParams);
                    $q = $queryParams['q'] ?? null;
                }
                if ($q) {
                    $searchString = sprintf("Search results for: '%s'", $q);
                    $evercrumbs[] = [
                        'label' => $searchString,
                        'title' => $searchString,
                        'link'  => $refererUrl
                    ];
                }

                $evercrumbs[] = [
                    'label' => $product->getName(),
                    'title' => $product->getName(),
                    'link' => ''
                ];
                
                return $evercrumbs;
            } else {
                $category = $this->getProductCategoryFromRefererUrl($refererUrl);
            }
        }
        
        $categoryIds = $this->getActiveCategories($product->getCategoryIds());
        if (empty($category) && !empty($categoryIds)) {
            $skipCategories = $this->scopeConfig->getValue(
                'catalog/breadcrumb/categories',
                \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE
            );
            if (!empty($skipCategories)) {
                $skipCategories = explode(",", $skipCategories);
                $categoryIds = array_diff($categoryIds, $skipCategories);
            }
            $categoryId = reset($categoryIds);
            $category = $this->categoryRepository->get($categoryId);
        }
        
        $userCategoryPathInUrl = $this->scopeConfig->getValue(
            'catalog/seo/product_use_categories',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );

        if (($userCategoryPathInUrl) && (!is_null($categoriesIds))) {
            $path = $this->catalogData->getBreadcrumbPath();
            foreach ($path as $k => $p) {
                $evercrumbs[] = [
                    'label' => $p['label'],
                    'title' => $p['label'],
                    'link' => isset($p['link']) ? $p['link'] : ''
                ];
            }
        } elseif (!empty($category)) {
            $path = $this->getProductBreadcrumbPath($product, $category);
            foreach ($path as $k => $p) {
                $evercrumbs[] = [
                    'label' => $p['label'],
                    'title' => $p['label'],
                    'link' => isset($p['link']) ? $p['link'] : ''
                ];
            }
        } else {
             $evercrumbs[] = [
                'label' => $product->getName(),
                'title' => $product->getName(),
                'link' => ''
             ];
        }
        return $evercrumbs;
    }

    public function getProductBreadcrumbPath($product, $category)
    {
        $path = [];
        if ($category) {
            $pathInStore = $category->getPathInStore();
            $pathIds = array_reverse(explode(',', $pathInStore));

            $categories = $category->getParentCategories();

            // add category path breadcrumb
            foreach ($pathIds as $categoryId) {
                if (isset($categories[$categoryId]) && $categories[$categoryId]->getName()) {
                    $path['category' . $categoryId] = [
                        'label' => $categories[$categoryId]->getName(),
                        'link' => $this->_isCategoryLink($categoryId, $category, $product) ? $categories[$categoryId]->getUrl() : ''
                    ];
                }
            }
        }

        if ($product) {
            $path['product'] = ['label' => $product->getName()];
        }

        return $path;
    }

    protected function _isCategoryLink($categoryId, $category, $product)
    {
        if ($product) {
            return true;
        }
        if ($categoryId != $category->getId()) {
            return true;
        }
        return false;
    }

    public function getProductCategoryFromRefererUrl($refererUrl)
    {
        $parsedUrl = parse_url($refererUrl);
        $path = $parsedUrl['path'] ?? '';
        $pathSegments = explode('/', trim($path, '/'));
        $urlKey = end($pathSegments);
        if (empty($urlKey)) {
            return null;
        }
        $categoryCollection = $this->categoryCollectionFactory->create();
        $categoryCollection->addAttributeToSelect('name');
        $categoryCollection->addAttributeToFilter('url_key', $urlKey);

        if ($categoryCollection->getSize()) {
            /** @var \Magento\Catalog\Model\Category $category */
            return $categoryCollection->getFirstItem();
        }

        return null;
    }

    protected function getActiveCategories($categoryIds)
    {
        $activeCategoryIds = [];
        if (!empty($categoryIds)) {
            $categories = $this->categoryCollectionFactory->create()
                ->addAttributeToSelect('*')
                ->addAttributeToFilter('is_active', 1)
                ->addAttributeToFilter('entity_id', ['in' => $categoryIds]);

            $activeCategoryIds = $categories->getAllIds();
        }
        return $activeCategoryIds;
    }
}
