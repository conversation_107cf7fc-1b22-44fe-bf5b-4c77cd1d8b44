<?php
/**
 * UpgradeSchema
 *
 * @category  Totaltools
 * @package   Totaltools_Megamenu
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Megamenu\Controller\Adminhtml\Menu;

class Save extends \Magedelight\Megamenu\Controller\Adminhtml\Menu\Save
{
    /**
     * Save action
     *
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        $data = $this->getRequest()->getPostValue();
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        if ($data) {
            $menuData = json_decode($data['menu_data_json'], true);
            if (is_array($menuData) && count($menuData) > 0) {
                $menuDataFinal = $menuData['menu_data'];
            }
            $data = $this->dataProcessor->filter($data);

            if (isset($data['is_active']) && !empty($data['is_active'])) {
                $data['is_active'] = 1;
            } else {
                $data['is_active'] = 0;
            }

            if (empty($data['menu_id'])) {
                $data['menu_id'] = null;
            }

            if (isset($data['store_id']) && (in_array("0", $data['store_id']))) {
                unset($data['store_id']);
                $data['store_id'] = [0];
            }

                /** @var \Magedelight\Megamenu\Model\Menu $model */
            $model = $this->menuModel;

            $id = $this->getRequest()->getParam('menu_id');
            if ($id) {
                $model->load($id);
            }

            if ($data['customer_groups']) {
                $data['customer_groups'] = implode(',', $data['customer_groups']);
            }

            $model->setData($data);
            $this->_eventManager->dispatch(
                'megamenu_menu_prepare_save',
                ['menu' => $model, 'request' => $this->getRequest()]
            );

            if (!$this->dataProcessor->validateRequireEntry($data)) {
                return $resultRedirect->setPath('*/*/edit', ['menu_id' => $model->getMenuId(),
                    '_current' => true]);
            }

            try {
                $form = $model->save();

                $menuId = $form->getMenuId();
                $deleteItems = $this->menuItemModel->deleteItems($menuId);

                if (is_array($menuData) && count($menuData) > 0) {
                    foreach ($menuDataFinal as $i => $menu_item_data) {
                        $menu_data = $menuDataFinal[$i];
                        if (isset($menu_data['item_name']) && isset($menu_data['item_type'])) {
                            $itemsData = [];
                            $itemsData['item_name'] = $menu_data['item_name'];
                            $itemsData['item_type'] = $menu_data['item_type'];
                            $itemsData['sort_order'] = $menu_data['sort_order'];
                            $itemsData['item_parent_id'] = $menu_data['item_parent_id'];
                            $itemsData['menu_id'] = $menuId;
                            $itemsData['object_id'] = $menu_data['object_id'];
                            $itemsData['item_link'] = $menu_data['item_link'];
                            $itemsData['item_font_icon'] = $menu_data['item_font_icon'];
                            $itemsData['item_class'] = $menu_data['item_class'];
                            $itemsData['animation_option'] = $menu_data['animation_option'];

                            if ($menu_data['item_all_cat']) {
                                $itemsData['category_display'] = $menu_data['item_all_cat'];
                            }
                            if ($menu_data['item_vertical_menu']) {
                                $itemsData['category_vertical_menu'] = $menu_data['item_vertical_menu'];
                            }

                            if ($menu_data['vertical_menu_bgcolor']) {
                                $itemsData['category_vertical_menu_bg'] = $menu_data['vertical_menu_bgcolor'];
                            }

                            if (!empty($menu_data['item_columns'])) {
                                $itemsData['item_columns'] = json_encode($menu_data['item_columns']);
                            }

                            if (!empty($menu_data['category_columns'])) {
                                $itemsData['category_columns'] = json_encode($menu_data['category_columns']);
                            }
                            if (!empty($menu_data['verticalcatexclude'])) {
                                $itemsData['vertical_cat_exclude'] = $menu_data['verticalcatexclude'];
                            }
                            if (!empty($menu_data['verticalcatfeatured'])) {
                                $itemsData['vertical_cat_featured'] = $menu_data['verticalcatfeatured'];
                            }
                            if (!empty($menu_data['show_child_cat'])) {
                                $itemsData['show_child_cat'] = $menu_data['show_child_cat'];
                            }
                            
                            if (!empty($menu_data['vertical_cat_sortby'])) {
                                $itemsData['vertical_cat_sortby'] = $menu_data['vertical_cat_sortby'];
                            }
                            if (!empty($menu_data['vertical_cat_sortorder'])) {
                                $itemsData['vertical_cat_sortorder'] = $menu_data['vertical_cat_sortorder'];
                            }
                            if (!empty($menu_data['vertical_cat_level'])) {
                                $itemsData['vertical_cat_level'] = $menu_data['vertical_cat_level'];
                            }
                            $currentItem = $this->menuItemModel->setData($itemsData)->save();
                            $itemId = $currentItem->getItemId();
                            foreach ($menuDataFinal as $key => $val) {
                                if (isset($val['item_parent_id']) && $val['item_parent_id'] == $i) {
                                    $menuDataFinal[$key]['item_parent_id'] = $itemId;
                                }
                            }
                        }
                    }
                }
                $this->messageManager->addSuccess(__('You saved the menu.'));
                $this->dataPersistor->clear('megamenu_menu');
                if ($this->getRequest()->getParam('back')) {
                    return $resultRedirect->setPath('*/*/edit', ['menu_id' => $model->getMenuId(),
                        '_current' => true]);
                }
                return $resultRedirect->setPath('*/*/');
            } catch (LocalizedException $e) {
                $this->messageManager->addError($e->getMessage());
            } catch (\Exception $e) {
                $this->messageManager->addException($e, $e->getMessage());
            }

            $this->dataPersistor->set('megamenu_menu', $data);
            return $resultRedirect->setPath(
                '*/*/edit',
                ['menu_id' => $this->getRequest()->getParam('menu_id')]
            );
        }
        return $resultRedirect->setPath('*/*/');
    }
}
