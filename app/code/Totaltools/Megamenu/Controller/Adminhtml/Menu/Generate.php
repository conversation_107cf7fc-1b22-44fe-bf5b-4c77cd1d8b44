<?php
/**
 * Generate
 *
 * @category  Totaltools
 * @package   Totaltools_Megamenu
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Megamenu\Controller\Adminhtml\Menu;

use Exception;
use Magento\Backend\App\Action;
use Totaltools\Megamenu\Helper\Data;

class Generate extends \Magento\Backend\App\Action
{
    /**
     * @var Data
     */
    protected $helper;

    /**
     * Generate constructor.
     * 
     * @param Action\Context $context
     * @param Data $helper
     */
    public function __construct(
        Action\Context $context,
        Data $helper
    ) {
        $this->helper = $helper;
        parent::__construct($context);
    }

    /**
     * Generate action
     *
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     * @return \Magento\Framework\Controller\ResultInterface
     */
    public function execute()
    {
        try {
            $this->helper->generateBlock();
            $this->messageManager->addSuccessMessage(__('Menu block generated'));
        } catch(Exception $e) {
            $this->messageManager->addErrorMessage(__($e->getMessage()));
        }
        
      
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultRedirectFactory->create();
        
        return $resultRedirect->setPath('*/*/');
    }
}