<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
		<section id="magedelight">
			<group id="general" sortOrder="10" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
				<label>general</label>
				<field id="use_static_block" type="select" sortOrder="5" showInWebsite="1" showInStore="1" showInDefault="1" translate="label">
					<label>Use Generated Static Block</label>
					 <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
					<comment>Use Generated Static Block in Menu</comment>
				</field>
				<field id="parent_mega_menu_block" translate="label" type="text" sortOrder="3" showInDefault="1" showInStore="1" showInWebsite="1">
                    <label>Parent Mega Menu Block</label>
                    <comment><![CDATA[Enter parent mega menu block here that renders all menus.]]></comment>
                </field>
			</group>
		</section>
		<section id="catalog">
			<group id="breadcrumb" translate="label" type="text" sortOrder="1500" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Breadcrumb Settings</label>
                <field id="categories" showInDefault="1" showInWebsite="1" showInStore="0" sortOrder="1" translate="label" type="textarea">
                    <label>Skip Categories In Breadcrumb</label>
                    <comment>Enter all category IDs separated by commas that you don't want to show in the product breadcrumb.</comment>
                </field>
            </group>
		</section>
	</system>
</config>