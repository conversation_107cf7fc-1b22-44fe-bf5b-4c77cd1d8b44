<?php
namespace Totaltools\Megamenu\Setup\Patch\Data;

use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;

class UpdateMenuLabelShapeAttribute implements DataPatchInterface
{
    private $moduleDataSetup;

    public function __construct(
        ModuleDataSetupInterface $moduleDataSetup
    ) {
        $this->moduleDataSetup = $moduleDataSetup;
    }

    public function apply()
    {
        $this->moduleDataSetup->getConnection()->startSetup();

        $this->moduleDataSetup->getConnection()->update(
            $this->moduleDataSetup->getTable('eav_attribute'),
            ['frontend_input' => 'select'],
            ['attribute_code = ?' => 'md_menu_label_shape']
        );

        $this->moduleDataSetup->getConnection()->endSetup();
    }

    public static function getDependencies()
    {
        return [
            \Magedelight\Megamenu\Setup\Patch\Data\AddProductAttribute::class
        ];
    }

    public function getAliases()
    {
        return [];
    }
}
