<?php
/**
 * UpgradeSchema
 *
 * @category  Totaltools
 * @package   Totaltools_Megamenu
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Megamenu\Setup;

use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

/**
 * Upgrade the Megamenu module DB scheme
 */
class UpgradeSchema implements UpgradeSchemaInterface
{

    /**
     * {@inheritdoc}
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        if (version_compare($context->getVersion(), '1.0.1', '<')) {
            $this->addFeaturedCategoriesColumn($setup);
        }

        if (version_compare($context->getVersion(), '1.0.2', '<')) {
            $this->addMenuIdentifierColumn($setup);
        }
    }

    protected function addFeaturedCategoriesColumn(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->changeColumn(
            $setup->getTable('megamenu_menu_items'),
            'vertical_cat_exclude',
            'vertical_cat_exclude',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'nullable' => true,
                'comment' => 'Vertical Exclude Child Category',
                'after' => 'item_class'
            ]
        );
        
        $setup->getConnection()->addColumn(
            $setup->getTable('megamenu_menu_items'),
            'vertical_cat_featured',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Featured Child Category',
                'after' => 'vertical_cat_exclude'
            ]
        );

        $setup->getConnection()->addColumn(
            $setup->getTable('megamenu_menu_items'),
            'show_child_cat',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'nullable' => true,
                'comment' => 'Show Child categories',
                'after' => 'vertical_cat_featured'
            ]
        );
    }

    protected function addMenuIdentifierColumn(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable('megamenu_menus'),
            'menu_identifier',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length' => 255,
                'nullable' => true,
                'comment' => 'Menu Identifier',
                'after' => 'menu_name'
            ]
        );
    }
}
