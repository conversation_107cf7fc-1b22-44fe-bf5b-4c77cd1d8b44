<?xml version="1.0"?>
<!--
/**
* Magedelight
* Copyright (C) 2017 Magedelight <<EMAIL>>
*
* @category Magedelight
* @package Magedelight_Megamenu
* @copyright Copyright (c) 2017 Mage Delight (http://www.magedelight.com/)
* @license http://opensource.org/licenses/gpl-3.0.html GNU General Public License,version 3 (GPL-3.0)
* <AUTHOR> <<EMAIL>>
*/
-->
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        
        <item name="buttons" xsi:type="array">
            <item name="generate_menu" xsi:type="array">
                <item name="name" xsi:type="string">generate_menu</item>
                <item name="label" xsi:type="string" translate="true">Generate Menu Block</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/generate</item>
            </item>
        </item>
    </argument>
    
</listing>
