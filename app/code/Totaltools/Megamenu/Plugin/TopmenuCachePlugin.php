<?php
namespace Totaltools\Megamenu\Plugin;

use Magedelight\Megamenu\Block\Topmenu;
use Psr\Log\LoggerInterface;

class TopmenuCachePlugin
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @param LoggerInterface $logger
     */
    public function __construct(
        LoggerInterface $logger
    ) {
        $this->logger = $logger;
    }

    /**
     * Replace the getCacheKeyInfo method to prevent excessive logging
     *
     * @param Topmenu $subject
     * @param array $result
     * @return array
     */
    public function afterGetCacheKeyInfo(Topmenu $subject, $result)
    {
        // exit('getCacheKeyInfo: afterGetCacheKeyInfo');
        $this->logger->info('getCacheKeyInfo: Topmenu - Using optimized cache key');
        // Create a simpler cache key that doesn't include the URL
        $optimizedResult = [
            'TOPMENU',
            $subject->getTemplate(),
            $subject->getNameInLayout()
        ];

        if (method_exists($subject, '_design') && $subject->_design) {
            $optimizedResult[] = $subject->_design->getDesignTheme()->getId();
        }
        $this->logger->info('getCacheKeyInfo: Topmenu - Cache key: ' . implode('', $optimizedResult));
        return $optimizedResult;
    }
}