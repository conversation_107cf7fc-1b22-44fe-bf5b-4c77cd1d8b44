<?php
/**
 * UpgradeSchema
 *
 * @category  Totaltools
 * @package   Totaltools_Megamenu
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Megamenu\Helper;

use \Magento\Framework\App\Helper\Context;
use \Magento\Framework\App\Helper\AbstractHelper;
use Magento\Cms\Model\BlockFactory;
use Magento\Framework\App\DeploymentConfig;
use Magento\Backend\App\Area\FrontNameResolver;
use Magento\Store\Model\Store;
use Magento\Backend\Setup\ConfigOptionsList;
use Magedelight\Megamenu\Model\Menu;
use Magedelight\Megamenu\Model\ResourceModel\Menu\CollectionFactory;

class Data extends AbstractHelper
{

    const PRIMARY_MEGAMENU = 'magedelight/general/primary_menu';
    const PARENT_MEGAMENU_BLOCK = 'magedelight/general/parent_mega_menu_block';

    /**
     * @var Context
     */
    protected $context;

    protected $helper;

    /**
     * @var \Magedelight\Megamenu\Model\Menu
     */
    protected $menuModel;

    protected $menuCollectionFactory;

    /**
     * @var \Magedelight\Megamenu\Model\MenuItems
     */
    protected $menuItemModel;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var \Magento\Framework\View\DesignInterface
     */
    private $designInterface;

    /**
     * @var \Magento\Backend\App\Config
     */
    private $config;

    /**
     * @var DeploymentConfig
     */
    private $deploymentConfig;

    /**
     * @var \Magento\Framework\App\ViewInterface
     */
    protected $_view;
     /**
     * @var  \Magento\Framework\App\State
     */
    protected $appState;
    
    /**
     * @var \Magento\Cms\Model\BlockFactory
     */
    private $blockFactory;

    protected $megamenuManagement;

    /**
     * Data constructor.
     * @param Context $context
     * @param GroupFactory $customerGroupFactory
     * @param \Magento\Framework\App\ViewInterface $view
     * @param \Magedelight\Megamenu\Model\MegamenuManagement $megamenuManagement,
     */
    public function __construct(
        Context $context,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\Framework\View\DesignInterface $designInterface,
        \Magento\Backend\App\Config $config,
        DeploymentConfig $deploymentConfig,
        \Magento\Framework\App\ViewInterface $view,
        \Magento\Framework\App\State $state,
        \Magedelight\Megamenu\Model\MegamenuManagement $megamenuManagement,
        CollectionFactory $menuCollectionFactory,
        BlockFactory $blockFactory
    ) {
        $this->storeManager = $storeManager;
        $this->designInterface = $designInterface;
        $this->config = $config;
        $this->deploymentConfig = $deploymentConfig;
        $this->blockFactory = $blockFactory;
        $this->_view = $view;
        $this->appState = $state;
        $this->megamenuManagement = $megamenuManagement;
        $this->menuCollectionFactory = $menuCollectionFactory;
        parent::__construct($context);
       
    }

   
    /**
     * @param $config_path
     * @return mixed
     */
    public function getConfig($config_path)
    {
        return $this->scopeConfig->getValue(
            $config_path,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * @param $config_path
     * @return mixed
     */
    public function getPrimaryMenuId()
    {
        return $this->getConfig(self::PRIMARY_MEGAMENU);
    }

    public function generateBlock() 
    {
        $this->appState->emulateAreaCode(
            \Magento\Framework\App\Area::AREA_FRONTEND,
            [$this, 'process'],
            []
        );
       
    }

    
    public function process() 
    {
        $menuHtml = '';
        $mobileTabs = '';
        // Load the collection
        $menuCollection = $this->menuCollectionFactory->create();
        $menuCollection->addFieldToFilter('is_active', ['eq' => 1]); 

        foreach ($menuCollection as $menu) {
            $menuHtml .= $this->processMenu($menu->getId());
            $mobileTabs .= '<button type="button" id="category_'.$menu->getId().'">'.$menu->getMenuName().'</button>';
        }
        $parentMenuBockId = $this->getParentMegaMenuBlock();
        $staticBlock = $this->blockFactory->create()->load($parentMenuBockId);
        $tabs = '<ul class="custom-tabs">
        '.$mobileTabs.'</ul><div class="shop-by-wrap">';
        $closeBtn =' </div><button type="button" class="close-navigation">x</button>';
        $htmlElement = '<div data-content-type="html" data-appearance="default" data-element="main">' . htmlentities($tabs).$menuHtml. htmlentities($closeBtn) .'
       
        </div>';
        
        $staticBlock->setData('content',  $htmlElement)->save();       

    }
    public function processMenu($menuId) 
    {
        $website = $this->getDefaultWebsiteId();
        $store = $this->storeManager->getStore($website);
        $theme = $this->getConfig('design/theme/theme_id', $store);
        $this->storeManager->setCurrentStore($store->getId());
        $this->designInterface->setArea('frontend')
            ->setDesignTheme($theme);
        $html = $this->_view->getLayout()->createBlock('Totaltools\Megamenu\Block\ShortcodeMenu')                        
                ->setTemplate('Magedelight_Megamenu::menu/shortcode.phtml')
                ->setData([
                            'menuid' => $menuId
                        ])
                ->toHtml();
        $html = htmlentities( $this->updateUrlsInHtml($html, $store));
        return $html;
    }

    /**
     * @return string|int
     */
    public function getDefaultWebsiteId()
    {
        return $this->storeManager->getDefaultStoreView()->getWebsiteId();
    }

    /**
     * @param string $html
     * @param \Magento\Store\Api\Data\StoreInterface $store
     * @return string
     */
    private function updateUrlsInHtml($html, $store)
    {
        $adminUrl = $this->getAdminBaseName();
        $baseUrl = (string)$this->config->getValue(Store::XML_PATH_SECURE_BASE_URL);

        if (strpos($html, $baseUrl) !== false) {
            $html = str_replace($baseUrl, $store->getBaseUrl(), $html);
        }

        if (strpos($html, $adminUrl) !== false) {
            $html = str_replace($adminUrl, $store->getBaseUrl(), $html);
        }

        return $html;
    }

    /**
     * @return mixed|null|string
     */
    private function getAdminBaseName()
    {
        $isCustomPathUsed = (bool)(string)$this->config->getValue(FrontNameResolver::XML_PATH_USE_CUSTOM_ADMIN_URL);
        if ($isCustomPathUsed) {
            return (string)$this->config->getValue(FrontNameResolver::XML_PATH_CUSTOM_ADMIN_URL);
        }
        $baseUrl = (string)$this->config->getValue(Store::XML_PATH_SECURE_BASE_URL);
        $defaultFrontName = $this->deploymentConfig->get(ConfigOptionsList::CONFIG_PATH_BACKEND_FRONTNAME);
        return $baseUrl . $defaultFrontName . "/";
    }

    public function getParentMegaMenuBlock()
    {
        return $this->config->getValue(
            self::PARENT_MEGAMENU_BLOCK,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
    }
    
}
