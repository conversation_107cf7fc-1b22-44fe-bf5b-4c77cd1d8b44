<?php
/**
 *  Megamenu.
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright totaltools (https://www.totaltools.au)
 */

 namespace Totaltools\Megamenu\Helper;

use Magento\Framework\App\Helper;
use Magedelight\Megamenu\Model\CacheType;

/**
 * Class Cache
 */
class Cache extends Helper\AbstractHelper
{
    const CACHE_LIFETIME = 86400;

    /**
     * @var \Magento\Framework\App\Cache
     */
    protected $cache;

    /**
     * @var \Magento\Framework\App\Cache\State
     */
    protected $cacheState;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var int
     */
    private $storeId;

    /**
     * Cache constructor.
     * @param Helper\Context $context
     * @param \Magento\Framework\App\Cache $cache
     * @param \Magento\Framework\App\Cache\State $cacheState
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function __construct(
        Helper\Context                             $context,
        \Magento\Framework\App\Cache               $cache,
        \Magento\Framework\App\Cache\State         $cacheState,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        $this->cache = $cache;
        $this->cacheState = $cacheState;
        $this->storeManager = $storeManager;
        $this->storeId = $storeManager->getStore()->getId();
        parent::__construct($context);
    }

    /**
     * @param $method
     * @param array $vars
     * @return string
     */
    public function getId($method, $vars = [])
    {
        return base64_encode($this->storeId . 'megamenu' . $method . implode('', $vars));
    }

    /**
     * @param $cacheId
     * @return bool|string
     */
    public function load($cacheId)
    {
        if ($this->cacheState->isEnabled('megamenu')) {
            return $this->cache->load($cacheId);
        }

        return false;
    }

    /**
     * @param $data
     * @param $cacheId
     * @param int $cacheLifetime
     * @return bool
     */
    public function save($data, $cacheId, $cacheLifetime = self::CACHE_LIFETIME)
    {
        if ($this->cacheState->isEnabled('megamenu')) {
            $this->cache->save($data, $cacheId, ['MEGAMENU'], $cacheLifetime);
            return true;
        }
        return false;
    }
}
