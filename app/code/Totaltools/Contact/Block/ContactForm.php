<?php
namespace Totaltools\Contact\Block;

use Magento\Contact\Block\ContactForm as BaseForm;
use Magento\Customer\Model\Session;
use Magento\Framework\Data\Form\FormKey;
use Magento\Framework\View\Element\Template;

class ContactForm extends BaseForm
{
    /**
     * @var Session
     */
    private $session;

    public function __construct(
        FormKey $formKey,
        Session $session,
        Template\Context $context,
        array $data = []
    )
    {
        parent::__construct( $context, $data);
        $this->formKey = $formKey;

        $this->session = $session;
    }

    /**
     * get form key
     *
     * @return string
     */
    public function getFormKey()
    {
        return $this->formKey->getFormKey();
    }

    public function getSubmitted()
    {
        $submitted = false;
        $this->session->setSubmitted($submitted);
        return $submitted;

    }
}