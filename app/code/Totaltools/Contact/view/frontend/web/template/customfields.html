<div class="field enquiry-type required">
    <label class="label" for="enquirytype"><span><!-- ko i18n: 'What is your enquiry about?' --><!-- /ko --></span></label>
    <div class="control">
        <select name="enquirytype" id="enquiry-type" class="input-sm" data-bind="value: selectedEnquiryType, options: enquiryTypes, optionsCaption: 'Select Enquiry Type', event: { change: enquiryTypeChanged}" data-validate="{required:true}"></select>
    </div>
</div>
<div class="field feedback-category required" data-bind="if: selectedEnquiryType() === 'Feedback'">
    <label class="label" for="feedbackcategory"><span><!-- ko i18n: 'Please select an option' --><!-- /ko --></span></label>
    <div class="control">
        <select name="feedbackcategory" id="feedback-category" class="input-sm" data-bind="value: selectedFeedbackCategory, options: feedbackCategories, optionsCaption: 'Select Option', event: { change: feedbackCategoryChanged}" data-validate={requiredifenquirytypeisfeedback:true}></select>
    </div>
</div>
<div class="field state required" data-bind="if: ((selectedEnquiryType() === 'In-store shopping') || (selectedEnquiryType() === 'Feedback' && selectedFeedbackCategory() === 'Store'))">
    <label class="label" for="state"><span><!-- ko i18n: 'Which state/territory are you located in?' --><!-- /ko --></span></label>
    <div class="control">
        <select name="state" id="state" class="input-sm" data-bind="value: selectedState, options: states, optionsCaption: 'Select State', event: { change: stateChanged}" data-validate="{requiredifenquirytypeisinstoreshopping:true}"></select>
    </div>
</div>
<!-- ko if: selectableStores() && selectableStores().length > 0-->
<div class="field store required" data-bind="if: ((selectedEnquiryType() === 'In-store shopping' && selectedState) || (selectedEnquiryType() === 'Feedback' && selectedFeedbackCategory() === 'Store'))">
    <label class="label" for="store"><span><!-- ko i18n: 'Which store have you shopped from, or intend to shop at?' --><!-- /ko --></span></label>
    <div class="control">
        <select name="store" id="store" class="input-sm" data-bind="value: selectedStore, options: selectableStores, optionsCaption: 'Select Store'" data-validate="{requiredifenquirytypeisinstoreshopping:true}"></select>
    </div>
</div>
<!-- /ko -->
<div class="field reason required" data-bind="if: selectedEnquiryType() === 'Online shopping'">
    <label class="label" for="reason"><span><!-- ko i18n: 'Please select a reason for contact' --><!-- /ko --></span></label>
    <div class="control">
        <select name="reason" id="reason" class="input-sm" data-bind="value: selectedReason, options: reasons, optionsCaption: 'Select Reason', event: { change: reasonChanged}" data-validate="{requiredifenquirytypeisonlineshopping:true}"></select>
        <!-- ko if: selectedReason() === 'Price Match Request' -->
        <p class="help-block medium">
            <!-- ko i18n: 'Please note - you can price match via the product page more efficiently.' --><!-- /ko -->
        </p>
        <!-- /ko -->
    </div>
</div>
<!-- ko if: selectableTypes().length > 0-->
<div class="field type" data-bind="if: selectedEnquiryType() === 'Online shopping' && selectedReason">
    <label class="label" for="type"><span><!-- ko i18n: 'Type of enquiry?' --><!-- /ko --></span></label>
    <div class="control">
        <select name="type" id="type" class="input-sm" data-bind="value: selectedType, options: selectableTypes, optionsCaption: 'Select Type'" data-validate="{requiredifreasonneedstype:true}"></select>
    </div>
</div>
<!-- /ko -->
<div class="field reward required" data-bind="if: selectedEnquiryType() === 'Insider Rewards'">
    <label class="label" for="reward"><span><!-- ko i18n: 'Please select an option' --><!-- /ko --></span></label>
    <div class="control">
        <select name="rewardreason" id="reward" class="input-sm" data-bind="value: selectedReward, options: rewards, optionsCaption: 'Select Option', event: { change: rewardChanged}" data-validate="{requiredifenquirytypeisinsiderrewards:true}"></select>
    </div>
</div>
<!-- ko if: selectedReason() === 'Price Match Request' -->
<!-- ko foreach: priceMatchItems -->
<div class="field price-match">
    <label class="label" data-bind="attr: {for: name}">
            <span data-bind="text: label"></span>
            <!-- ko if: validate -->
            <span class="field required"></span>
            <!-- /ko -->
    </label>
    <div class="control">
        <!-- ko if: validate -->
            <input type="text" data-bind="attr: {name: name, id: name, 'data-validate': validation}"  />
        <!-- /ko -->
        <!-- ko ifnot: validate -->
            <input type="text" data-bind="attr: {name: name, id: name}" />
        <!-- /ko -->
        <!-- ko if: name == 'productlink' -->
        <p class="help-block medium">
            <!-- ko i18n: 'Please contact your local Total Tools store for any price match enquiries if you are unable to find the product online.' --><!-- /ko -->
        </p>
        <!-- /ko -->

    </div>
</div>
<!-- /ko -->
<div class="field price-match">
    <label class="label" for="shipping"><span><!-- ko i18n: 'Type of shipping' --><!-- /ko --></span></label>
    <div class="control">
        <div class="control">
            <select name="shipping" id="shipping" class="input-sm" data-bind="value: selectedShipping, options: shipping, optionsCaption: 'Select Option', event: { change: shippingChanged}" data-validate="{required:true}"></select>
        </div>
    </div>
</div>
<!-- /ko -->
<!-- ko if: selectedEnquiryType() === 'Other' || ( selectedEnquiryType() === 'Online shopping' && selectedReason() === 'Product Information' || selectedReason() === 'Other') -->
<div class="field postCode required" >
    <label class="label" for="postCode"><span><!-- ko i18n: 'Your Post Code' --><!-- /ko --></span></label>
    <div class="control">
        <input type="text" data-bind="attr: {name: postCode, id: postCode}" data-validate="{postcodeAU:true}" />
    </div>
</div>
<!-- /ko -->
<div class="field comment required">
    <label class="label" for="comment"><span><!-- ko i18n: 'What are the details of your enquiry?' --><!-- /ko --></span></label>
    <div class="control">
        <textarea name="comment" id="comment" title="What are the details of your enquiry?'" class="input-text" cols="5" rows="7" data-validate="{required:true}"></textarea>
        <!-- ko if: (selectedEnquiryType() === 'Online shopping' && selectedReason() === 'Product Information') -->
            <p class="help-block medium">
                <!-- ko i18n: 'For example: Product SKU or any other details that could help us better understand your enquiry. Note: Product SKU is available on the product page; below the product title and above the price.' --><!-- /ko -->
            </p>
        <!-- /ko -->
        <!-- ko if: (selectedEnquiryType() === 'In-store shopping' || selectedEnquiryType() === 'Online shopping' || selectedEnquiryType() === 'Compliments and Complaints') -->
            <!-- ko if: (selectedReason() != 'Price Match Request' && selectedReason() != 'Product Information') -->
                <p class="help-block medium">
                    <!-- ko i18n: 'For example: Order number, invoice number or any other details that could help us better understand your enquiry.' --><!-- /ko -->
                </p>
            <!-- /ko -->
        <!-- /ko -->
        <!-- ko if: selectedReason() === 'Price Match Request' -->
            <p class="help-block medium">
                <!-- ko i18n: 'For Example: Product Name, Colour, Size, Model, Product Link on Total Tools website, Product link on competitors website.' --><!-- /ko -->
            </p>
        <!-- /ko -->
    </div>
</div>