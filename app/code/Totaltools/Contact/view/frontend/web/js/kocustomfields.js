define(['uiComponent', 'ko', 'jquery'], function (Component, ko, $) {

    return Component.extend({

        initialize: function () {

            this._super();

            var _self = this;

            this.enquiryTypes = ko.observableArray([
                "Media Enquiries",
                "Employment",
                "Corporate Sales",
                "Franchise Opportunities",
                "Sponsorship & Partnerships",
                "Wholesale / Product Supply",
            ]);

            this.selectedEnquiryType = ko.observable('');

            this.states = ko.observableArray([
                'Australian Capital Territory',
                'New South Wales',
                'Northern Territory',
                'Queensland',
                'South Australia',
                'Tasmania',
                'Victoria',
                'Western Australia'
            ]);

            this.selectedState = ko.observable('');

            this.stores = ko.observableArray([]);

            $.getJSON("/rest/V1/storelocator/list", function(data) {

                _self.stores = JSON.parse(data);
            });

            this.selectableStores = ko.observableArray([]);

            this.selectedStore = ko.observable('');

            this.reasons = ko.observableArray([
                "My Order",
                "Returns",
                "Delivery & Tracking",
                "Payments & Billing",
                "Price Match Request",
                "Gift Cards",
                "Product Information",
                "Product Warranty",
                "My Account",
                "Other",
            ]);

            this.selectedReason = ko.observable('');

            this.priceMatchItems = [
                { name: 'productname',              label: 'Product Name',                  validate: true, validation:'{required:true}'},
                { name: 'colour',                   label: 'Colour',                        validate: false},
                { name: 'size',                     label: 'Size',                          validate: false},
                { name: 'model',                    label: 'Model',                         validate: false},
                { name: 'productlink',              label: 'Total Tools Product Link',      validate: true, validation:'{required:true}' },
                { name: 'sellingprice',             label: 'Total Tools Product Price',     validate: true, validation:'{required:true,  number: true}'},
                { name: 'shippingcost',             label: 'Total Tools Shipping Cost',    validate: false},
                { name: 'Competitorproductlink',    label: 'Competitor\'s Product Link',       validate: true, validation:'{required:true}'},
                { name: 'Competitorsellingprice',   label: 'Competitor\'s Product Price',   validate: true, validation:'{required:true}'},
                { name: 'Competitorshippingcost',  label: 'Competitor\'s Shipping Cost',  validate: false},
                { name: 'postCode',                 label: 'Your Post Code',  validate: true, validation:'{postcodeAU:true}'},

            ];

            this.types = {

                "My Order": [
                    "Cancellation",
                    "Amendment",
                    "Change delivery address",
                    "Delivery update",
                    "Missing item",
                    "Wrong item sent",
                    "Damaged in transit",
                    "Other",
                ],

                "Returns": [
                    "Change of mind",
                    "Wrong item purchased",
                    "Faulty item",
                    "Wrong item sent",
                    "Damaged in transit",
                    "Refund processing update",
                    "Other",
                ],

                "Delivery & Tracking": [
                    "Priority dispatch",
                    "Authority to leave request",
                    "Delivery times",
                    "Order tracking update",
                    "Other",
                ],

                "Payments & Billing": [
                    "Checkout issues",
                    "Payment issues",
                    "Tax invoice request",
                    "Other"
                ],

                "My Account": [
                    "Lost my password",
                    "Reset my password",
                    "Unable to login",
                    "National or regional account request",
                    "Other"
                ],

                "Gift Cards": [],

                "Product Information": [],

                "Product Warranty": [],

                "Price Match Request": [],

                "Other": [],


            };

            this.selectableTypes = ko.observableArray([]);

            this.selectedType = ko.observable('');

            this.rewards = ko.observableArray([
                'Insiders app',
                'Update details',
                'Remove from mailing list',
                'Insider points balance',
                'Missing invoice',
                'Other',
            ]);

            this.selectedReward = ko.observable('');

            this.shipping = ko.observableArray([
                'Delivery',
                'Click & Collect',
            ]);
            this.selectedShipping = ko.observable('');

            this.postCode = ko.observable('postCode');

            this.feedbackCategories = ko.observableArray([
                'Online Store',
                'Store',
            ])

            this.selectedFeedbackCategory = ko.observable();
        },

        enquiryTypeChanged: function (obj, event) {

            if (this.selectedEnquiryType() === 'In-store shopping') {

                this.selectedReason('');

            } else if (this.selectedEnquiryType() === 'Online shopping') {

                this.selectedState('');
                this.selectedStore('');

            } else {

                this.selectedReason('');
                this.selectedState('');
                this.selectedStore('');
            }

        },

        stateChanged: function (obj, event) {

            this.selectableStores(this.stores[this.selectedState()]);

        },

        rewardChanged: function (obj, event) {

            // to do
        },
        shippingChanged: function (obj, event) {

            // to do
        },


        feedbackCategoryChanged: function (obj, event) {

            // to do
        },


        reasonChanged: function (obj, event) {

            this.selectedType('');
            this.selectableTypes(this.types[this.selectedReason()]);
        }

    });
});
