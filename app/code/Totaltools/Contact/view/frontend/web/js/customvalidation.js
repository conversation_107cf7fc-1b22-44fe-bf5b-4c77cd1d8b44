define([
    'jquery',
    'jquery/validate',
    'mage/translate'
], function ($) {
    'use strict';
    return function () {

        $.validator.addMethod(
            "requiredifenquirytypeisinstoreshopping",

            function (value, element) {

                let enquiryType = $("#enquiry-type").val();

                if (value == "" && (enquiryType == "In-store shopping" || enquiryType == "Feedback")) {

                    return false;
                } else {

                    return true;
                }

            },

            $.mage.__("This field is required if enquiry type is selected as 'In-store shopping'.")
        );

        $.validator.addMethod(
            "requiredifenquirytypeisonlineshopping",

            function (value, element) {

                let enquiryType = $("#enquiry-type").val();

                if (value == "" && enquiryType == "Online shopping") {

                    return false;
                } else {

                    return true;
                }

            },

            $.mage.__("This field is required if enquiry type is selected as 'Online shopping'.")
        );

        $.validator.addMethod(
            "requiredifenquirytypeisinsiderrewards",   

            function (value, element) {
                let enquiryType = $("#enquiry-type").val();

                if (value == "" && enquiryType == "Insider Rewards") {

                    return false;
                } else {

                    return true;
                }

            },

            $.mage.__("This field is required if enquiry type is selected as 'Insider Rewards'.")
        );

        $.validator.addMethod(
            "requiredifenquirytypeisfeedback",   

            function (value, element) {
                let enquiryType = $("#enquiry-type").val();

                if (value == "" && enquiryType == "Feedback") {

                    return false;
                } else {

                    return true;
                }

            },

            $.mage.__("This field is required if enquiry type is selected as 'Feedback'.")
        );

        $.validator.addMethod(
            "requiredifreasonneedstype",

            function (value, element) {

                let reason = $("#reason").val();

                if (value == "" && (reason == "My Order" || reason == "Returns" || reason == "Delivery & Tracking" || reason == "Payments & Billing" || reason == "My Account")) {

                    return false;
                } else {

                    return true;
                }

            },

            $.mage.__("This field is required.")
        );


        $.validator.addMethod( "postcodeAU", function( value, element ) {
            return  /^(\d{4})+$/.test( value );
        }, "The specified Post Code is invalid" );

    }
});