<?php
/**
 *
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Contact\Controller\Index;

use Magento\Backend\App\Area\FrontNameResolver as FrontNameResolverAlias;
use Magento\Contact\Controller\Index;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\Config\ScopeConfigInterface as ScopeConfigInterfaceAlias;
use Magento\Framework\App\Request\DataPersistorInterface;
use Magento\Contact\Model\ConfigInterface;
use Magento\Framework\Controller\Result\Redirect;
use Magento\Framework\Data\Form\FormKey\Validator;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Mail\Template\TransportBuilder as TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface as StateInterfaceAlias;
use Magento\Store\Model\ScopeInterface as ScopeInterfaceAlias;
use Magento\Store\Model\Store as StoreAlias;
use Magento\Framework\App\Area;
use Totaltools\Contact\Model\Mail;
use Unirgy\RapidFlow\Exception;
use Totaltools\Storelocator\Model\StoreRepository;

class Post extends Index
{
    /**
     * @var DataPersistorInterface
     */
    private $_dataPersistor;
    /**
     * @var StateInterfaceAlias
     */
    private $_inlineTranslation;
    /**
     * @var ScopeConfigInterfaceAlias
     */
    private $_scopeConfig;
    /**
     * @var TransportBuilder
     */
    private $_transportBuilder;
    /**
     * @var Validator
     */
    private $formKeyValidator;
    /**
     * @var Session
     */
    private $session;

    private $contactsConfig;

    private $mail;
    /**
     * @var StoreRepository
     */
    protected $storeRepository;

    /**
     * @param Context $context
     * @param ConfigInterface $contactsConfig
     * @param DataPersistorInterface $_dataPersistor
     * @param StateInterfaceAlias $inlineTranslation
     * @param ScopeConfigInterfaceAlias $scopeConfig
     * @param TransportBuilder $transportBuilder
     * @param Mail $mail
     * @param Validator $validator
     * @param Session $session
     * @param StoreRepository $storeRepository
     */
    public function __construct(
        Context $context,
        ConfigInterface $contactsConfig,
        DataPersistorInterface $_dataPersistor,
        StateInterfaceAlias $inlineTranslation,
        ScopeConfigInterfaceAlias $scopeConfig,
        TransportBuilder $transportBuilder,
        Mail $mail,
        Validator $validator,
        Session $session,
        StoreRepository $storeRepository
    ) {
        parent::__construct($context, $contactsConfig);

        $this->mail = $mail;
        $this->_dataPersistor = $_dataPersistor;
        $this->_inlineTranslation = $inlineTranslation;
        $this->_scopeConfig = $scopeConfig;
        $this->_transportBuilder = $transportBuilder;
        $this->contactsConfig = $contactsConfig;
        $this->formKeyValidator = $validator;
        $this->session = $session;
        $this->storeRepository = $storeRepository;
    }

    /**
     * Post user question
     *
     * @return void
     * @throws \Exception
     */
    public function execute()
    {
        if (!$this->formKeyValidator->validate($this->getRequest())) {
            $this->_redirect('corporate-contact');
            return;
        }
        $post = $this->getRequest()->getPostValue();

        if (!$post) {
            $this->_redirect('corporate-contact');
            return;
        }
        if (!$this->session->getSubmitted())
        {
            $this->session->setSubmitted(true);
        } else {
            $this->_redirect('corporate-contact');
            return;
        }
        $this->_inlineTranslation->suspend();
        try {
            $postObject = new \Magento\Framework\DataObject();

            $post["comment"] = nl2br($post["comment"]);

            $postObject->setData($post);

            $error = false;
            $validator = new \Laminas\Validator\EmailAddress();
            $validEmpty = new \Laminas\Validator\NotEmpty();
            if (!$validEmpty->isValid(trim($post['comment']))) {
                $error = true;
            }
            if (!$validEmpty->isValid(trim($post['name']))) {
                $error = true;
            }
            if (!$validator->isValid(trim($post['email']), \Magento\Framework\Validator\EmailAddress::class)){
                $error = true;
            }
            if ($validEmpty->isValid(trim($post['hideit']))) {
                $error = true;
            }
            if ($error) {
                throw new \Exception();
            }

            $priceMatchRequest = isset($post["reason"]) && $post["reason"] == "Price Match Request" ? true : false;
            $feedback = isset($post["enquirytype"]) && $post["enquirytype"] == "Feedback" ? true : false;
            $insiderRewards = isset($post["enquirytype"]) && $post["enquirytype"] == "Insider Rewards" ? true : false;
            $storeInfo = empty($post["state"]) ? false : true;
            $hasReason = empty($post["reason"]) ? false : true;
            $hasType = empty($post["type"]) ? false : true;

            $enquiryType = empty($post["enquirytype"]) ? "" : $post["enquirytype"];
            $feedbackcategory = empty($post["feedbackcategory"]) ? "" : $post["feedbackcategory"];
            $state = empty($post["state"]) ? "" : $post["state"];
            $store = empty($post["store"]) ? "" : $post["store"];

            $postCode = isset($post["postCode"]) && $post["postCode"] != '' ? $post["postCode"] : false;
            $nearestStoreName ='';
            if (isset($post['postCode']) && !empty($post['postCode'])) {
                try {
                    $nearestStore =  $this->storeRepository->getByZipCode($postCode) ;
                    $nearestStoreName =  $nearestStore->getData('store_name') ?? '';
                } catch (NoSuchEntityException $exception) {
                    $nearestStoreName ='Not Found';
                    $this->messageManager->addNoticeMessage(
                        __('We don\'t have store related to this "%1" zipcode.', $postCode)
                    );
                }
            }
            if($enquiryType == "In-store shopping") {
                $subject = "Contact Us Form - In-store shopping - {$state} - {$store}";
            } else if($enquiryType == "Online shopping") {
                $reason = empty($post["reason"]) ? "" : $post["reason"];
                $type = empty($post["type"]) ? "" : " - {$post["type"]}";
                $subject = "Contact Us Form - Online shopping - {$reason}{$type}";
            } else if($enquiryType == "Insider Rewards") {
                $rewardreason = empty($post["rewardreason"]) ? "" : $post["rewardreason"];
                $subject = "Contact Us Form - Insider Rewards - {$rewardreason}";
            } else if($enquiryType == "Feedback" && $feedbackcategory == "Online Store") {
                $subject = "Contact Us Form - Feedback - Online Store";
            } else if($enquiryType == "Feedback" && $feedbackcategory == "Store") {
                $subject = "Contact Us Form - Feedback - Store - {$state} - {$store}";
            } else {
                $subject = "Contact Us Form - {$enquiryType}";
            }

            $this->_inlineTranslation->suspend();
            try {
                $storeScope = ScopeInterfaceAlias::SCOPE_STORE;


                $variable = [
                    'data' => $postObject,
                    'priceMatchRequest' => $priceMatchRequest,
                    'postCode' => $postCode,
                    'closestStore' => $nearestStoreName,
                    'feedback' => $feedback,
                    'storeInfo' => $storeInfo,
                    'insiderRewards' => $insiderRewards,
                    'hasReason' => $hasReason,
                    'hasType' => $hasType,
                    'subject' =>  '=?UTF-8?B?'.base64_encode($subject).'?=',
                ];

                $this->mail->send($post, $variable,
                    $this->_scopeConfig->getValue(self::XML_PATH_EMAIL_TEMPLATE, $storeScope));
            } finally {
                $this->_inlineTranslation->resume();
            }

            $this->messageManager->addSuccessMessage(
                __('Thanks for contacting us with your comments and questions. We\'ll respond to you very soon.')
            );
            $this->_dataPersistor->clear('contact_us');
            $this->_redirect('corporate-contact');
            $resultRedirect = $this->resultRedirectFactory->create();
            $resultRedirect->setPath('corporate-contact');
            return $resultRedirect;
            return $this->resultRedirectFactory->create()->setPath('corporate-contact');
        } catch (\Exception $e) {
            $this->_inlineTranslation->resume();
            $this->messageManager->addErrorMessage(
                __('We can\'t process your request right now. Sorry, that\'s all we know. ')
            );
            $this->_dataPersistor->set('contact_us', $post);
            $resultRedirect = $this->resultRedirectFactory->create();
            $resultRedirect->setPath('corporate-contact');
            return $resultRedirect;
            return $this->resultRedirectFactory->create()->setPath('corporate-contact');
        }
    }
}
