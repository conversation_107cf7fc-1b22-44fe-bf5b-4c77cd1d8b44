<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * <AUTHOR> Dev
 * @package Totaltools_Signifyd
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="totaltools_signifyd" translate="label" type="text" sortOrder="500" showInDefault="1" showInWebsite="1" showInStore="1">
            <label>Signifyd</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Signifyd::config_signifyd</resource>

            <group id="general" translate="label" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>General Settings</label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Extension</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>

            <group id="action_nothing" translate="label" type="text" sortOrder="200" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>
                    <![CDATA["Nothing" Action Settings
                    <p style="font-size: 12px;font-weight: normal;">When <strong>Do nothing</strong> is selected in Signifyd "Declined Guarantees"</p>]]>
                </label>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Action</label>
                    <comment></comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="email_template" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Template (Customer)</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
            </group>

            <group id="action_cancellation" translate="label comment" type="text" sortOrder="200" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>
                    <![CDATA["Cancel/Void" Action Settings
                    <p style="font-size: 12px;font-weight: normal;">When <strong>Void payment and cancel order</strong> is selected in Signifyd "Declined Guarantees"</p>]]>
                </label>
               <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Enable Action</label>
                    <comment></comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="email_template" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Template (Customer)</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="email_template_store" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Email Template (Store)</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="customer_support_email" translate="label comment" type="text" sortOrder="40"  showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Customer Support Email</label>
                    <comment>comma seperated email address</comment>
                </field>
            </group>
        </section>
    </system>
</config>
