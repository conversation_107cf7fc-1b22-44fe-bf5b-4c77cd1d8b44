<?xml version="1.0"?>
<!--
/**
 * <AUTHOR> Dev
 * @package Totaltools_Signifyd
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Totaltools\Signifyd\Model\MailInterface" type="Totaltools\Signifyd\Model\Mail" />
    <type name="Totaltools\Shippit\Observer\SendOrderToShippitObserver">
        <plugin disabled="false"
                name="totaltools_shippit_push_observer_plugin"
                sortOrder="10"
                type="Totaltools\Signifyd\Plugin\Shippit\Observer\SendOrderToShippitObserver"/>
    </type>
    <type name="Totaltools\Signifyd\Model\Logger\Handler">
        <arguments>
            <argument name="filesystem" xsi:type="object">Magento\Framework\Filesystem\Driver\File</argument>
        </arguments>
    </type>
    <type name="Totaltools\Signifyd\Model\Logger">
        <arguments>
            <argument name="name" xsi:type="string">balanceSignifydLogger</argument>
            <argument name="handlers" xsi:type="array">
                <item name="system" xsi:type="object">Totaltools\Signifyd\Model\Logger\Handler</item>
            </argument>
        </arguments>
    </type>
    <preference for="Signifyd\Connect\Model\Api\Recipient" type="Totaltools\Signifyd\Model\Api\Recipient" />
    <type name="Signifyd\Connect\Model\UpdateOrder\Capture">
        <plugin name="totaltools_signifyd_plugin_model_capture" type="Totaltools\Signifyd\Plugin\Model\Capture" sortOrder="10" />
    </type>
    <type name="Signifyd\Connect\Model\UpdateOrder\Cancel">
        <plugin name="totaltools_signifyd_plugin_model_cancel" type="Totaltools\Signifyd\Plugin\Model\Cancel" sortOrder="20" />
    </type>
    <type name="Signifyd\Connect\Model\Casedata\UpdateCaseV2">
        <plugin name="totaltools_signifyd_plugin_model_updatecase" type="Totaltools\Signifyd\Plugin\Model\UpdateCaseV2" sortOrder="30" />
    </type>
</config>
