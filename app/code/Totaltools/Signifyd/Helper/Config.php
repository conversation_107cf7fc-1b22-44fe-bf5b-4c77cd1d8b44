<?php

namespace Totaltools\Signifyd\Helper;

/**
 * <AUTHOR> Dev
 * @package Totaltools_Signifyd
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Config extends AbstractHelper
{
    const SIGNIFYD_ENABLED = 'totaltools_signifyd/general/enabled';

    const SIGNIFYD_ACTION_NOTHING_ENABLED = 'totaltools_signifyd/action_nothing/enabled';

    const SIGNIFYD_ACTION_NOTHING_TEMPLATE = 'totaltools_signifyd/action_nothing/template';

    const SIGNIFYD_ACTION_CANCEL_ENABLED = 'totaltools_signifyd/action_cancel/enabled';

    const SIGNIFYD_ACTION_CANCEL_TEMPLATE = 'totaltools_signifyd/action_cancel/template';

    const SIGNIFYD_ACTION_CANCEL_TEMPLATE_STORE = 'totaltools_signifyd/action_cancel/template_store';

    const SIGNIFYD_ACTION_CANCEL_CUSTOMER_SUPPORT_EMAIL = 'totaltools_signifyd/action_cancellation/customer_support_email';

    /**
     * @return bool
     */
    public function isEnabled($scope = ScopeInterface::SCOPE_STORE)
    {
        return $this->scopeConfig->isSetFlag(self::SIGNIFYD_ENABLED, $scope);
    }

    /**
     * @return bool
     */
    public function getNothingActionEnabled($scope = ScopeInterface::SCOPE_STORE)
    {
        return $this->scopeConfig->isSetFlag(self::SIGNIFYD_ACTION_NOTHING_ENABLED, $scope);
    }

    /**
     * @return string
     */
    public function getNothingActionEmailTemplate($scope = ScopeInterface::SCOPE_STORE)
    {
        return $this->scopeConfig->getValue(self::SIGNIFYD_ACTION_NOTHING_TEMPLATE, $scope);
    }

    /**
     * @return bool
     */
    public function getCancelActionEnabled($scope = ScopeInterface::SCOPE_STORE)
    {
        return $this->scopeConfig->isSetFlag(self::SIGNIFYD_ACTION_CANCEL_ENABLED, $scope);
    }

    /**
     * @return string
     */
    public function getCancelActionEmailTemplate($scope = ScopeInterface::SCOPE_STORE)
    {
        return $this->scopeConfig->getValue(self::SIGNIFYD_ACTION_CANCEL_TEMPLATE, $scope);
    }

    /**
     * @return string
     */
    public function getCancelActionStoreEmailTemplate($scope = ScopeInterface::SCOPE_STORE)
    {
        return $this->scopeConfig->getValue(self::SIGNIFYD_ACTION_CANCEL_TEMPLATE_STORE, $scope);
    }

    /**
     * @return string
     */
    public function getCustomerSupportEmails($scope = ScopeInterface::SCOPE_STORE)
    {
        return $this->scopeConfig->getValue(self::SIGNIFYD_ACTION_CANCEL_CUSTOMER_SUPPORT_EMAIL, $scope);
    }
}
