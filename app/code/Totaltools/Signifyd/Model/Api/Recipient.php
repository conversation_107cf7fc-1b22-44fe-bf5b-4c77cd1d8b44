<?php

namespace Totaltools\Signifyd\Model\Api;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Serialize\Serializer\Json as JsonSerializer;
use Magento\Sales\Model\Order;
use Signifyd\Connect\Model\Api\AddressFactory;
use Signifyd\Connect\Model\Api\Recipient as OriginalRecipient;
use Totaltools\Storelocator\Model\Store;
use Totaltools\Storelocator\Model\StoreRepository;
use Magento\Quote\Model\Quote\AddressFactory as QuoteAddressFactory;
use Totaltools\Storelocator\Model\Logger;
use Totaltools\Storelocator\Helper\Checkout\Data;
use Totaltools\Storelocator\Model\ShippingZoneRepository;

class Recipient extends OriginalRecipient
{
    /**
     * Click and collect method name
     */
    const STORE_PICKUP_METHOD = 'shippitcc_shippitcc';

    /**
     * @var ScopeConfigInterface
     */
    public $scopeConfigInterface;

    /**
     * @var JsonSerializer
     */
    public $jsonSerializer;

    /**
     * @var AddressFactory
     */
    public $addressFactory;

    /**
     * @var StoreRepository
     */
    protected $storeRepository;

    /**
     * @var QuoteAddressFactory
     */
    protected $quoteAddressFactory;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var Data
     */
    protected $checkoutHelper;

    /**
     * @var ShippingZoneRepository
     */
    protected $shippingZoneRepository;

    /**
     * @param ScopeConfigInterface $scopeConfigInterface
     * @param JsonSerializer $jsonSerializer
     * @param AddressFactory $addressFactory
     * @param StoreRepository $storeRepository
     * @param QuoteAddressFactory $quoteAddressFactory
     * @param Logger $logger
     * @param Data $checkoutHelper
     * @param ShippingZoneRepository $shippingZoneRepository
     */
    public function __construct(
        ScopeConfigInterface $scopeConfigInterface,
        JsonSerializer $jsonSerializer,
        AddressFactory $addressFactory,
        StoreRepository $storeRepository,
        QuoteAddressFactory $quoteAddressFactory,
        Logger $logger,
        Data $checkoutHelper,
        ShippingZoneRepository $shippingZoneRepository
    ) {
        parent::__construct($scopeConfigInterface, $jsonSerializer, $addressFactory);
        $this->scopeConfigInterface = $scopeConfigInterface;
        $this->jsonSerializer = $jsonSerializer;
        $this->addressFactory = $addressFactory;
        $this->storeRepository = $storeRepository;
        $this->quoteAddressFactory = $quoteAddressFactory;
        $this->logger = $logger;
        $this->checkoutHelper = $checkoutHelper;
        $this->shippingZoneRepository = $shippingZoneRepository;
    }

    /**
     * @param Order $order
     * @return array
     */
    protected function makeRecipient(Order $order)
    {
        $shippingMethod = $order->getShippingMethod();
        if ($shippingMethod == self::STORE_PICKUP_METHOD) {
            $recipient = [];
            if ($order->getId() && $order->getIsNotVirtual()) {
                $storeId = (int) $order->getData('storelocator_id');
                if ($storeId > 0) {
                    $store = $this->storeRepository->getById($storeId);
                } else {
                    $shippingAddress = $order->getShippingAddress();
                    $storeId = 0;
                    $postcode = $shippingAddress ? $shippingAddress->getPostcode() : "";
                    $quoteShippingAddr  = $this->quoteAddressFactory->create()->load($shippingAddress->getQuoteAddressId());
                    if ((int) $quoteShippingAddr->getStorelocatorId() > 0) {
                        $storeId = (int) $quoteShippingAddr->getStorelocatorId();
                        $store = $this->storeRepository->getById($storeId);
                        $this->logger->info(__('makeRecipient: get click and collect store from shipping address '));
                    } elseif ($this->checkoutHelper->getSessionStoreId()) {
                        $storeId = $this->checkoutHelper->getSessionStoreId();
                        $store = $this->storeRepository->getById($storeId);
                        $this->logger->info(__('makeRecipient: store based on click and collect -> session. Order: '));
                    } else {
                        $this->logger->info(__('makeRecipient: Store based on default postcode owner for order: '));
                        $store = $this->_getStoreByPostcode($postcode);
                        if ($store) {
                            $storeId = $store->getId();
                        }
                    }
                }

                $address = $order->getShippingAddress();
                if ($storeId > 0 && $store->getId() > 0) {
                    if ($address !== null) {
                        $recipient['fullName'] = $address->getName();
                        $recipient['organization'] = $address->getCompany();
                        $recipient['address'] = $this->formatSignifydAddress($store);
                    }

                    if (empty($recipient['fullName'])) {
                        $recipient['fullName'] = $order->getCustomerName();
                    }
                }
            }

            return $recipient;
        }

        return parent::makeRecipient($order);
    }

    /**
      * Format Store Address
      *
      * @param Store $store
      *
      * @return array
      */
    public function formatSignifydAddress($store)
    {
        $address = [];

        $address['streetAddress'] = $store->getAddress();
        $address['unit'] = '';
        $address['city'] = $store->getCity();
        $address['provinceCode'] = $store->getState();
        $address['postalCode'] = $store->getZipcode();
        $address['countryCode'] = $store->getCountryId();

        return $address;
    }

    /**
     * Get store by postcode.
     *
     * @param $postcode
     *
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface
     */
    protected function _getStoreByPostcode($postcode)
    {
        $zone = $this->shippingZoneRepository ->getZoneByPostCode($postcode);

        if (!$zone || !$zone->getId()) {
            return false;
        }

        return $this->storeRepository->findByZone($zone->getId());
    }
}
