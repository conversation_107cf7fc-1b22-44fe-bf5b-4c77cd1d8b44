<?php

namespace Totaltools\Signifyd\Model;

/**
 * <AUTHOR> Dev
 * @package Totaltools_Signifyd
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Signifyd\Helper\Config;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\App\Area;
use Psr\Log\LoggerInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;

class Mail implements MailInterface
{
    /**
     * @var configHelper
     */
    private $configHelper;

    /**
     * @var TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var StateInterface
     */
    private $inlineTranslation;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * Scope config
     *
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * Initialize dependencies.
     *
     * @param Config $configHelper
     * @param TransportBuilder $transportBuilder
     * @param StateInterface $inlineTranslation
     * @param StoreManagerInterface|null $storeManager
     * @param LoggerInterface $logger
     */
    public function __construct(
        Config $configHelper,
        TransportBuilder $transportBuilder,
        StateInterface $inlineTranslation,
        StoreManagerInterface $storeManager = null,
        LoggerInterface $logger,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->configHelper = $configHelper;
        $this->transportBuilder = $transportBuilder;
        $this->inlineTranslation = $inlineTranslation;
        $this->storeManager = $storeManager ?: ObjectManager::getInstance()->get(StoreManagerInterface::class);
        $this->logger = $logger;
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Send email
     *
     * @param \Magento\Framework\DataObject $data
     * @return bool
     */
    public function send(\Magento\Framework\DataObject $data)
    {
        $result = false;
        $this->inlineTranslation->suspend();

        $store = $this->storeManager->getStore($data->getStoreId());
        $senderName = $this->scopeConfig->getValue(
            'trans_email/ident_support/name',
            ScopeInterface::SCOPE_STORE,
            $store
        );
        $senderEmail = $this->scopeConfig->getValue(
            'trans_email/ident_support/email',
            ScopeInterface::SCOPE_STORE,
            $store
        );

        $sender = [
            'name' => $senderName,
            'email' => $senderEmail,
        ];

        try {
            $transport = $this->transportBuilder
                ->setTemplateIdentifier($data->getTemplate())
                ->setTemplateOptions(
                    [
                        'area' => Area::AREA_FRONTEND,
                        'store' => $data->getStoreId()
                    ]
                )
                ->setTemplateVars($data->getTemplateVars())
                ->setFrom($sender)
                ->addTo($data->getEmail());
            if ($data->getCustomerSupportEmails()) {
                $cc = array_map('trim', explode(',', $data->getCustomerSupportEmails()));
                foreach ($cc as $ccEmail) {
                    if (!empty($ccEmail)) {
                        $transport->addCc($ccEmail);
                    }
                }
            }
            $transport = $transport->getTransport();
            $transport->sendMessage();
            $result = true;
        } catch (\Exception $e) {
            $result = false;
            $this->logger->critical($e->getMessage());
        } finally {
            // print_r($data->debug());
            $this->inlineTranslation->resume();
        }

        return $result;
    }
}
