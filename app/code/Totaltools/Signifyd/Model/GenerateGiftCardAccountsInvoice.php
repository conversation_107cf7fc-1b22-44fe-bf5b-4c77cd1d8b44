<?php

namespace Totaltools\Signifyd\Model;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Store\Model\ScopeInterface;
use Magento\GiftCard\Model\Giftcard;
use Magento\Sales\Model\Order\Item;
use Magento\GiftCard\Model\Catalog\Product\Type\Giftcard as ProductTypeGiftcard;
use Totaltools\Vii\Model\Api\Order as OrderApi;
use Magento\GiftCard\Model\Giftcard\Option as GiftcardOption;

/**
 * Class GenerateGiftCardAccountsInvoice
 * @package Totaltools\Vii\Plugin\Observer
 */
class GenerateGiftCardAccountsInvoice
{
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;


    /**
     * @var OrderApi
     */
    private $orderApi;


    /**
     * GenerateGiftCardAccountsInvoice constructor.
     * @param ScopeConfigInterface $scopeConfig
     * @param OrderApi $orderApi
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        OrderApi $orderApi
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->orderApi = $orderApi;
    }

    public function pushOrder($order)
    {
        $requiredStatus = (int) $this->scopeConfig->getValue(
            Giftcard::XML_PATH_ORDER_ITEM_STATUS,
            ScopeInterface::SCOPE_STORE,
            $order->getStore()
        );

        if ($requiredStatus !== Item::STATUS_INVOICED || ($order->getOrderNumberVii() !== 'pending' )) {
            return $this;
        }

        $giftCardItems = [];
        $giftCardTotal = 0;
        foreach ($order->getAllItems() as $item) {
            if (
                $item->getProductType() == ProductTypeGiftcard::TYPE_GIFTCARD
                && $item->getProduct()->getGiftcardType() == Giftcard::TYPE_VIRTUAL
            ) {
                $giftCardItems[] = $item;
                $giftCardTotal += $item->getPriceInclTax();
            }
        }

        if (sizeof($giftCardItems) > 0 && !$this->orderApi->isProcessing()) {
            // calling NewOrder api to Vii system
            $billingAddress = $order->getBillingAddress();
            $this->orderApi->start();
            $this->orderApi
                ->setOrderId($order->getIncrementId())
                ->setAmount($giftCardTotal)
                ->setCustomerFirstName($billingAddress->getFirstname())
                ->setCustomerLastName($billingAddress->getLastname())
                ->setCustomerEmail($billingAddress->getEmail())
            ;
            /**
             * @var \Magento\Sales\Model\Order\Item $item
             */
            foreach ($giftCardItems as $item) {
                $this->orderApi->addPackage(
                    $item->getProductOptionByCode(GiftcardOption::KEY_RECIPIENT_NAME),
                    $item->getProductOptionByCode(GiftcardOption::KEY_SENDER_NAME),
                    $item->getProductOptionByCode(GiftcardOption::KEY_MESSAGE),
                    $item->getProductOptionByCode(GiftcardOption::KEY_RECIPIENT_EMAIL),
                    $item->getPriceInclTax(),
                    (int)$item->getQtyOrdered()
                );
            }

            $this->orderApi->addToQueue((int) $order->getId());

            $order->setOrderNumberVii('pending')->setActivationPin('pending')->save();
        }

        return $this;
    }
}