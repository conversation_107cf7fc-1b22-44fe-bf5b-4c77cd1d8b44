<?php


namespace Totaltools\Signifyd\Plugin\Shippit\Observer;

use \Magento\Framework\Event\Observer;
use \Magento\Braintree\Model\Ui\ConfigProvider;
use \Zip\ZipPayment\Model\Ui\ConfigProvider as ZipMoneyConfigProvidor;

class SendOrderToShippitObserver
{

    /**
     * @var \Totaltools\Signifyd\Helper\Config
     */
    protected $configHelper;

    /**
     * SendOrderToShippitObserver constructor.
     * @param \Totaltools\Signifyd\Helper\Config $configHelper
     */
    public function __construct(
        \Totaltools\Signifyd\Helper\Config $configHelper
    )
    {
        $this->configHelper = $configHelper;
    }


    public function aroundExecute(
        \Totaltools\Shippit\Observer\SendOrderToShippitObserver $subject,
        \Closure $proceed,
        Observer $observer
    ) {
        // get the event parameters
        $order = $observer->getEvent()->getOrder();
        $code = $order->getPayment()->getMethodInstance()->getCode();
        if($code != ZipMoneyConfigProvidor::CODE) {
            // If our extension not enabled in config, return
            if ($this->configHelper->isEnabled()) {
                if ($code != \PayPal\Braintree\Model\Ui\ConfigProvider::CODE) {
                    return $proceed($observer);
                }
            } else {
                $result = $proceed($observer);
                return $result;
            }
        }
    }
}
