<?php

namespace Totaltools\Signifyd\Plugin\Model;


use Signifyd\Connect\Model\Casedata\UpdateCaseV2 as SignifydUpdateCaseV2;
use Magento\Sales\Model\OrderFactory;
use Signifyd\Connect\Model\Casedata;
use Signifyd\Connect\Model\ResourceModel\Order as SignifydOrderResourceModel;

class UpdateCaseV2
{
    /**
     * @var \Totaltools\Signifyd\Model\Logger
     */
    protected $logger;

    /**
     * @var OrderFactory
     */
    public $orderFactory;

    /**
     * @var SignifydOrderResourceModel
     */
    public $signifydOrderResourceModel;

    public function __construct(
        \Totaltools\Signifyd\Model\Logger $logger,
        OrderFactory $orderFactory,
        SignifydOrderResourceModel $signifydOrderResourceModel
    ) {
        $this->logger = $logger;
        $this->orderFactory = $orderFactory;
        $this->signifydOrderResourceModel = $signifydOrderResourceModel;
    }

    public function around__invoke(
        SignifydUpdateCaseV2 $subject,
        callable $proceed,
        $case,
        $response
    ) {
        $result = $proceed($case, $response);
        $this->logger->info('Signifyd invoking UpdateCaseV2::around__invoke');
        try {
            $order = $this->orderFactory->create();
            $this->signifydOrderResourceModel->load($order, $case->getData('order_id'));
            if (
                $case->getMagentoStatus() == Casedata::COMPLETED_STATUS &&
                @$response->guaranteeDisposition == 'APPROVED' &&
                $order->hasInvoices()
            ) {
                $this->logger->debug("Signifyd: Ignore 2nd update with the same Approved when the order was invoiced: " . $order->getIncrementId());
                return true;
            }
        }
        catch (\Exception $e) {
            $this->logger->debug($e->getMessage());
        }

        return $result;
    }
}
