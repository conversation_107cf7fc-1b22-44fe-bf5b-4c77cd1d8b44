<?php

namespace Totaltools\Signifyd\Plugin\Model;

/**
 * <AUTHOR> Dev
 * @package Totaltools_Signifyd
 * @copyright 2019 © Totaltools. <https://www.totaltools.com.au>
 */

use Magento\Sales\Model\Order;
use Magento\Framework\DataObject;
use Magento\Sales\Api\Data\InvoiceInterface;
use Magento\Sales\Api\Data\OrderItemInterface;
use Magento\Sales\Model\Order\Invoice;
use Magento\GiftCard\Model\Catalog\Product\Type\Giftcard as ProductGiftCard;
use Totaltools\Signifyd\Model\GenerateGiftCardAccountsInvoice;
use Magento\Framework\Exception\NotFoundException;
use Shippit\Shipping\Model\ResourceModel\Sync\Order\CollectionFactory;
use Signifyd\Connect\Model\Casedata as OrgCaseData;
use Signifyd\Connect\Logger\Logger as OrgLogger;
use Magento\Sales\Model\Order\Address\Renderer;
use Magento\Payment\Helper\Data as PaymentHelper;
use Totaltools\Vii\Model\RewriteGiftCardAccount as Giftcardaccount;
use Totaltools\Vii\Model\RewriteGiftCardAccountFactory;
use Magento\Framework\Serialize\Serializer\Json;
use Signifyd\Connect\Helper\OrderHelper;

class Casedata
{
    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @var \Totaltools\Signifyd\Helper\Config
     */
    protected $configHelper;

    /**
     * @var \Totaltools\Signifyd\Model\MailInterface
     */
    protected $mailer;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    /**
     * @var \Totaltools\Storelocator\Model\ShippingZoneRepository
     */
    protected $shippingZoneRepository;

    /**
     * @var \Magento\Quote\Model\Quote\AddressFactory
     */
    private $quoteAddress;

    /**
     * @var \Totaltools\Storelocator\Model\StoreShippitService
     */
    private $shippitService;

    /**
     * @var \Magento\Sales\Model\Order\Email\Sender\OrderSender
     */
    protected $orderSender;

    /**
     * @var GenerateGiftCardAccountsInvoice
     */
    protected $generateGiftCardVii;

    /**
     * @var \Magento\Sales\Model\OrderFactory
     */
    protected $orderFactory;

    /**
     * @var OrgLogger
     */
    protected $orgLogger;

    /**
     * @var \Signifyd\Connect\Model\ResourceModel\Casedata
     */
    protected $casedataResourceModel;

    /**
     * @var Renderer
     */
    private $addressRenderer;

    /**
     * @var \Magento\Payment\Helper\Data
     */
    private $paymentHelper;
    /**
     * @var Shippit\Shipping\Model\ResourceModel\Sync\Order\CollectionFactory
     */
    private $collectionFactory;
    /**
     * @var RewriteGiftCardAccountFactory
     */
    protected $giftCAFactory;

    /**
     * Instance of serializer.
     *
     * @var Json
     */
    private $serializer;

    /**
     * @var OrderHelper
     */
    protected $orderHelper;

    /**
     * Casedata constructor.
     * @param \Totaltools\Signifyd\Model\Logger $logger
     * @param \Totaltools\Signifyd\Helper\Config $configHelper
     * @param \Totaltools\Signifyd\Model\MailInterface $mailer
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Magento\Quote\Model\Quote\AddressFactory $quoteAddressFactory
     * @param \Totaltools\Storelocator\Model\ShippingZoneRepository $shippingZoneRepository
     * @param \Totaltools\Storelocator\Model\StoreShippitService $shippitService
     * @param Order\Email\Sender\OrderSender $orderSender
     * @param GenerateGiftCardAccountsInvoice $generateGiftCardVii
     * @param CollectionFactory $collectionFactory
     * @param \Magento\Sales\Model\OrderFactory $orderFactory
     * @param OrgLogger $orgLogger
     * @param Renderer $addressRenderer
     * @param PaymentHelper $paymentHelper
     * @param RewriteGiftCardAccountFactory $giftCAFactory
     * @param Json $serializer
     * @param OrderHelper $orderHelper
     */
    public function __construct(
        \Totaltools\Signifyd\Model\Logger $logger,
        \Totaltools\Signifyd\Helper\Config $configHelper,
        \Totaltools\Signifyd\Model\MailInterface $mailer,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Quote\Model\Quote\AddressFactory $quoteAddressFactory,
        \Totaltools\Storelocator\Model\ShippingZoneRepository $shippingZoneRepository,
        \Totaltools\Storelocator\Model\StoreShippitService $shippitService,
        \Magento\Sales\Model\Order\Email\Sender\OrderSender $orderSender,
        GenerateGiftCardAccountsInvoice $generateGiftCardVii,
        CollectionFactory $collectionFactory,
        \Magento\Sales\Model\OrderFactory $orderFactory,
        \Signifyd\Connect\Model\ResourceModel\Casedata $casedataResourceModel,
        OrgLogger $orgLogger,
        Renderer $addressRenderer,
        PaymentHelper $paymentHelper,
        RewriteGiftCardAccountFactory $giftCAFactory,
        Json $serializer,
        OrderHelper $orderHelper
    ) {
        $this->logger = $logger;
        $this->configHelper = $configHelper;
        $this->mailer = $mailer;
        $this->quoteAddress = $quoteAddressFactory;
        $this->storeRepository = $storeRepository;
        $this->shippingZoneRepository = $shippingZoneRepository;
        $this->shippitService = $shippitService;
        $this->orderSender = $orderSender;
        $this->generateGiftCardVii = $generateGiftCardVii;
        $this->collectionFactory  = $collectionFactory;
        $this->orderFactory = $orderFactory;
        $this->casedataResourceModel = $casedataResourceModel;
        $this->orgLogger = $orgLogger;
        $this->addressRenderer = $addressRenderer;
        $this->paymentHelper = $paymentHelper;
        $this->giftCAFactory = $giftCAFactory;
        $this->serializer = $serializer;
        $this->orderHelper = $orderHelper;
    }


    protected function getFormattedShippingAddress($order)
    {
        return $order->getIsVirtual()
            ? NULL
            : $this->addressRenderer->format($order->getShippingAddress(), 'html');
    }

    protected function getFormattedBillingAddress($order)
    {
        return $this->addressRenderer->format($order->getBillingAddress(), 'html');
    }

    /**
     * @param \Signifyd\Connect\Model\Casedata $subject
     * @param callable $proceed ,
     * @param array $caseData
     * @return bool
     */
    public function aroundUpdateOrder(
        \Signifyd\Connect\Model\Casedata $subject,
        callable $proceed
    ) {
        $case = $subject;
        $orderAction = $case->handleGuaranteeChange();

        if(isset($orderAction['action']) && $orderAction['action'] == 'capture') {
            $case->setMagentoStatus('processing_response_from_magento')
                ->setUpdated(Date('Y-m-d H:i:s', time()));
            $this->casedataResourceModel->save($case);
        }

        $result = false;
        try {
            $order = $case->getOrder(true);
            if(
            !($orderAction["action"] == 'capture' &&
                $order->hasInvoices()) ) {
                $result = $proceed();
            }

            $orderData = $this->orderFactory->create()->load($order->getId());
            $orderData->getExtensionAttributes();
            $this->logger->info(
                __('Warehouse data for order before update by signifyd balance plugin'. $orderData->getIncrementId().' '
                    .' warehouse-'.$orderData->getWarehouse().' store- '.$orderData->getData('store').'location id-'.$orderData->getData('storelocator_id')));

        } catch (\Exception $e) {
            $this->logger->info($e->__toString(), array('entity' => $case));
        }

        // If our extension not enabled in config, return
        if ($this->configHelper->isEnabled()) {
            $this->logger->info('Balance Signify Around Plugin hit ' );
            try {
                $order = $case->getOrder(true);
                if (
                    isset($orderAction["action"])
                    && $orderAction["action"] == 'capture'
                    && ( $order->getStatus() == Order::STATE_PROCESSING || $order->getStatus() == Order::STATE_COMPLETE )
                    && $result
                ) {

                    $firstInvoice = false;
                    $isVirtualOrder = false;
                    foreach ($order->getInvoiceCollection() as $invoice)
                    {
                        $firstInvoice = $invoice;
                        break;
                    }

                    /** @var Item $orderItem */
                    foreach ($order->getAllItems() as $orderItem) {
                        if ($orderItem->getProductType() === ProductGiftCard::TYPE_GIFTCARD) {
                            $isVirtualOrder = true;
                        }
                    }
                    if(!$isVirtualOrder) {
                        $cards = $order->getGiftCards() ? $this->serializer->unserialize($order->getGiftCards()) : [];
                        if(!empty($cards)) {
                            $this->redeemGiftCards($order, $cards);
                        }
                        $shippitCollection = $this->collectionFactory->create()
                            ->addFieldToFilter('order_id', $order->getId());
                        if($shippitCollection->getSize() < 1) {
                            $store = $this->getOrderStore($order);
                            $this->logger->info(
                                __('Warehouse data for order before push to shippit by bi plugin'. $orderData->getIncrementId().' '
                                    .' warehouse-'.$orderData->getWarehouse().' store- '.$orderData->getData('store').'location id-'.$orderData->getData('storelocator_id')));
                            $this->logger->info('push order to the Shippit' . $order->getIncrementId());
                            try {
                                $this->shippitService->sendOrder($order, $store);
                            } catch (NotFoundException $e) {
                                $this->logger->info('Shippit Error while pushing order from signifyd. Order Id -' . $order->getIncrementId());
                                $this->logger->info($e->getMessage());
                            }
                        }
                    } else {
                        $this->generateGiftCardVii->pushOrder($order);
                    }
                    $this->orderSender->send($order, true);
                }
                $this->logger->info("Balance Signify Around Plugin hit before check decline: " . print_r($orderAction, true), array('entity' => $case));

                $guaranteeDisposition = $case->getGuarantee();

                if (
                    isset($orderAction["action"])
                    && $orderAction["action"] == 'cancel'
                    && isset($guaranteeDisposition)
                    && $guaranteeDisposition == "DECLINED"
                ) {
                    $cards = $order->getGiftCards() ? $this->serializer->unserialize($order->getGiftCards()) : [];
                    if(!empty($cards)) {
                        $this->refundGiftCards($order, $cards);
                    }
                    $action = $case->getNegativeAction();
                    $this->logger->info("Balance Signify Around Plugin hit decline email function hit: " .$action);
                    switch ($action) {
                        case 'nothing':
                            $this->handleNothingAction($order);
                            break;
                        case 'cancel':
                            $this->handleCancellationAction($order);
                            break;
                        default:
                            break;
                    }
                }
            } catch (\Exception $e) {
                $this->logger->info($e->__toString(), array('entity' => $case));
                return false;
            }
        }

        return $result;
    }

    /**
     * @param OrgCaseData $subject
     * @param callable $proceed
     * @param $caseData
     * @return bool
     */
    public function aroundUpdateCase(
        OrgCaseData $subject,
        callable $proceed,
        $response
    ) {
        try {
            $case = $subject;
            $response = $response;
            $order = $case->getOrder(true);

            if (
                $case->getMagentoStatus() == OrgCaseData::COMPLETED_STATUS &&
                @$response->guaranteeDisposition == 'APPROVED' &&
                $order->hasInvoices()
            ) {
                $this->logger->debug("Signifyd: Ignore 2nd update with the same Approved when the order was invoiced: " . $order->getIncrementId());
                return true;
            }
        }
        catch (\Exception $e) {
            $this->logger->debug($e->getMessage());
        }
        return $proceed($response);
    }
    /**
     * @param Order $order
     * @return void
     */
    protected function handleNothingAction(Order $order)
    {
        $isEnabled = $this->configHelper->getNothingActionEnabled();
        $template = $this->configHelper->getNothingActionEmailTemplate();

        if (!$isEnabled || !$template) {
           return;
        }

        try {
            $customerMailData = $this->prepareTemplate($order, $template);
            $this->mailer->send($customerMailData);
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
        }
    }

    /**
     * @param Order $order
     * @return void
     */
    public function handleCancellationAction(Order $order)
    {
        $isEnabled = $this->configHelper->getCancelActionEnabled();
        $template = $this->configHelper->getCancelActionEmailTemplate();
        $storeTemplate = $this->configHelper->getCancelActionStoreEmailTemplate();

        if (!$isEnabled || !$template) {
            return;
        }

        try {
            $customerMailData = $this->prepareTemplate($order, $template);
            $this->mailer->send($customerMailData);
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
        }

        try {
            $store = $this->getOrderStore($order);
            if ($store && $storeTemplate) {
                $storeEmail = $store->getStoreAdminEmail();
                $storeMailData = $this->prepareTemplate($order, $storeTemplate, $storeEmail, $store);
                $this->mailer->send($storeMailData);
            }
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
        }

    }

        /**
     * @param $order
     * @return string
     * @throws \Exception
     */
    private function getPaymentHtml($order)
    {
        return $this->paymentHelper->getInfoBlockHtml(
            $order->getPayment(),
            $order->getStore()->getStoreId()
        );
    }


    /**
     * @return DataObject
     */
    protected function prepareTemplate(
        Order $order,
        $template,
        $email = null,
        $store = null
    ) {
        $customerSupportEmails = $this->configHelper->getCustomerSupportEmails();
        $transport = [
            'template_vars' => [
                'order' => $order,
                'order_id' => $order->getId(),
                'customer_name' => $this->getCustomerName($order),
                'created_at_formatted' => $order->getCreatedAtFormatted(10),
                'store' => $store,
                'mage_store' => $order->getStore(),
                'formattedShippingAddress' => $this->getFormattedShippingAddress($order),
                'formattedBillingAddress' => $this->getFormattedBillingAddress($order),
                'payment_html' => $this->getPaymentHtml($order),
                'order_data' => [
                    'customer_name' => $order->getCustomerName(),
                    'is_not_virtual' => $order->getIsNotVirtual()
                ]
            ],
            'store_id' => $order->getStore()->getId(),
            'template' => $template,
            'email' => $email ?: $order->getCustomerEmail(),
            'customer_support_emails' => $email && $store ? $customerSupportEmails : ''
        ];

        $transportObject = new DataObject($transport);
        return $transportObject;
    }

    /**
     * @param Order $order
     * @return \Totaltools\Storelocator\Model\Store
     */
    protected function getOrderStore(Order $order)
    {
        $storeId = (int)$order->getStorelocatorId();
        $store = $this->storeRepository->getById($storeId);
        return $store;
    }

    /**
     * @param Order $order
     * @return string
     */
    protected function getCustomerName(Order $order)
    {
        $orderCustomerName = $order->getCustomerName();

        if ($orderCustomerName == 'Guest') {
            $orderCustomerName = $order->getBillingAddress()->getFirstname() . ' ' . $order->getBillingAddress()->getLastname();
        }

        return $orderCustomerName;
    }

    /**
     * Get store by postcode.
     *
     * @param $postcode
     *
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface
     */
    protected function _getStoreByPostcode($postcode)
    {
        /**
         * @var \Totaltools\Storelocator\Api\Data\ZoneInterface $zone
         */
        $zone = $this->shippingZoneRepository->getZoneByPostCode($postcode);

        if (!$zone || !$zone->getId()) {
            return false;
        }

        return $this->storeRepository->findByZone($zone->getId());
    }

    /**
     * Returns order item invoiced quantity.
     *
     * @param InvoiceInterface $invoice
     * @param OrderItemInterface $orderItem
     * @return int
     */
    private function getInvoicedOrderItemQty(InvoiceInterface $invoice, OrderItemInterface $orderItem): int
    {
        $qty = 0;
        foreach ($invoice->getItems() as $invoiceItem) {
            // check, if this order item has been paid
            if ($invoiceItem->getOrderItemId()) {
                if ($invoiceItem->getOrderItemId() === $orderItem->getItemId()
                    && $invoice->getState() == Invoice::STATE_PAID
                ) {
                    $qty = (int)$invoiceItem->getQty();
                }
            } else {
                if ($invoiceItem->getProductId() === $orderItem->getProductId()
                    && $invoice->getState() == Invoice::STATE_PAID
                ) {
                    $qty = (int)$invoiceItem->getQty();
                }
            }
        }

        return $qty;
    }

    private function redeemGiftCards($order, $cards)
    {
        if (!empty($cards) && is_array($cards)) {
            foreach ($cards as &$card) {
                /** @var \Totaltools\Vii\Model\RewriteGiftCardAccount $giftCard */
                $giftCard = $this->giftCAFactory->create();
                $giftCard->loadByCode($card[Giftcardaccount::ID], $card[Giftcardaccount::PIN]);
                if ($giftCard->getId() && isset($card[Giftcardaccount::PRE_AUTH_CODE])) {
                    $giftCard->setExternalReference($order->getIncrementId());
                    $giftCard->charge($card[Giftcardaccount::BASE_AMOUNT], $card[Giftcardaccount::PRE_AUTH_CODE]);
                    unset($card[Giftcardaccount::PRE_AUTH_CODE]);
                }
            }
            $order->setGiftCards($this->serializer->serialize($cards))->save();
            $message = "Gift Cards Redeemed";
            $this->orderHelper->addCommentToStatusHistory($order, $message);
        }
    }

    private function refundGiftCards($order, $cards)
    {
        if (!empty($cards) && is_array($cards)) {
            foreach ($cards as &$card) {
                /** @var \Totaltools\Vii\Model\RewriteGiftCardAccount $giftCard */
                $giftCard = $this->giftCAFactory->create();
                $giftCard->loadByCode($card[Giftcardaccount::ID], $card[Giftcardaccount::PIN]);
                if ($giftCard->getId() && isset($card[Giftcardaccount::PRE_AUTH_CODE])) {
                    $giftCard->setExternalReference($order->getIncrementId());
                    $giftCard->refund($card[Giftcardaccount::PRE_AUTH_CODE]);
                    unset($card[Giftcardaccount::PRE_AUTH_CODE]);
                }
            }
            $order->setGiftCards($this->serializer->serialize($cards))->save();
            $message = "Gift Cards Refunded";
            $this->orderHelper->addCommentToStatusHistory($order, $message);
        }
    }
}
