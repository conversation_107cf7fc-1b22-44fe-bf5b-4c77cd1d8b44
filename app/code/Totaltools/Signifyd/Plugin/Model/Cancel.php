<?php

namespace Totaltools\Signifyd\Plugin\Model;

use Magento\Sales\Model\Order;
use Magento\Framework\DataObject;
use Signifyd\Connect\Model\UpdateOrder\Cancel as UpdateOrderCancel;
use Signifyd\Connect\Model\UpdateOrder\Action as UpdateOrderAction;
use Magento\Sales\Model\Order\Address\Renderer;
use Magento\Payment\Helper\Data as PaymentHelper;
use Totaltools\Vii\Model\RewriteGiftCardAccount as Giftcardaccount;
use Totaltools\Vii\Model\RewriteGiftCardAccountFactory;
use Magento\Framework\Serialize\Serializer\Json;
use Signifyd\Connect\Helper\OrderHelper;

class Cancel
{
    /**
     * @var \Totaltools\Signifyd\Model\Logger
     */
    protected $logger;

    /**
     * @var \Totaltools\Signifyd\Helper\Config
     */
    protected $configHelper;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    /**
     * @var UpdateOrderAction
     */
    protected $updateOrderAction;

    /**
     * @var Renderer
     */
    protected $addressRenderer;

    /**
     * @var \Totaltools\Signifyd\Model\MailInterface
     */
    protected $mailer;

    /**
     * @var PaymentHelper
     */
    private $paymentHelper;

    /**
     * @var RewriteGiftCardAccountFactory
     */
    protected $giftCAFactory;
    /**
     * Instance of serializer.
     *
     * @var Json
     */
    private $serializer;
    /**
     * @var OrderHelper
     */
    protected $orderHelper;

    public function __construct(
        \Totaltools\Signifyd\Model\Logger $logger,
        \Totaltools\Signifyd\Helper\Config $configHelper,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        UpdateOrderAction $updateOrderAction,
        \Totaltools\Signifyd\Model\MailInterface $mailer,
        Renderer $addressRenderer,
        PaymentHelper $paymentHelper,
        RewriteGiftCardAccountFactory $giftCAFactory,
        Json $serializer,
        OrderHelper $orderHelper
    ) {
        $this->logger = $logger;
        $this->configHelper = $configHelper;
        $this->storeRepository = $storeRepository;
        $this->updateOrderAction = $updateOrderAction;
        $this->mailer = $mailer;
        $this->addressRenderer = $addressRenderer;
        $this->paymentHelper = $paymentHelper;
        $this->giftCAFactory = $giftCAFactory;
        $this->serializer = $serializer;
        $this->orderHelper = $orderHelper;
    }

    public function around__invoke(
        UpdateOrderCancel $subject,
        callable $proceed,
        Order $order,
        $case,
        $orderAction,
        $completeCase
    ) {
        $result = $proceed($order, $case, $orderAction, $completeCase);
        $this->logger->info('Signifyd invoking Cancel::around__invoke order_id: ' . $order->getIncrementId());
        if ($this->configHelper->isEnabled()) {
            $this->logger->info("Signify Around Plugin hit before check decline: " . print_r($orderAction, true), ['entity' => $case]);
            $guaranteeDisposition = $case->getGuarantee();
            $this->logger->info("guaranteeDisposition: " . $guaranteeDisposition);
            if (
                isset($orderAction["action"])
                && $orderAction["action"] == 'cancel'
                && isset($guaranteeDisposition)
                && in_array($guaranteeDisposition, ["DECLINED", "REJECT"])
            ) {
                $cards = $order->getGiftCards() ? $this->serializer->unserialize($order->getGiftCards()) : [];
                if(!empty($cards)) {
                    $this->refundGiftCards($order, $cards);
                }
                $negativeAction = $this->updateOrderAction->getNegativeAction($case);
                $this->logger->info("Signify Around Plugin hit decline email function hit: " . $negativeAction);
                switch ($negativeAction) {
                    case 'nothing':
                        $this->handleNothingAction($order);
                        break;
                    case 'cancel':
                        $this->handleCancellationAction($order);
                        break;
                    default:
                        break;
                }
            }
        }

        return $result;
    }

    /**
     * @param Order $order
     * @return \Totaltools\Storelocator\Model\Store
     */
    protected function getOrderStore(Order $order)
    {
        $storeId = (int)$order->getStorelocatorId();
        $store = $this->storeRepository->getById($storeId);
        return $store;
    }

    /**
     * @param Order $order
     * @return void
     */
    protected function handleNothingAction(Order $order)
    {
        $isEnabled = $this->configHelper->getNothingActionEnabled();
        $template = $this->configHelper->getNothingActionEmailTemplate();

        if (!$isEnabled || !$template) {
           return;
        }

        try {
            $customerMailData = $this->prepareTemplate($order, $template);
            $this->mailer->send($customerMailData);
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
        }
    }

    /**
     * @param Order $order
     * @return void
     */
    public function handleCancellationAction(Order $order)
    {
        $isEnabled = $this->configHelper->getCancelActionEnabled();
        $template = $this->configHelper->getCancelActionEmailTemplate();
        $storeTemplate = $this->configHelper->getCancelActionStoreEmailTemplate();

        if (!$isEnabled || !$template) {
            return;
        }

        try {
            $customerMailData = $this->prepareTemplate($order, $template);
            $this->mailer->send($customerMailData);
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
        }

        try {
            $store = $this->getOrderStore($order);
            if ($store && $storeTemplate) {
                $storeEmail = $store->getStoreAdminEmail();
                $storeMailData = $this->prepareTemplate($order, $storeTemplate, $storeEmail, $store);
                $this->mailer->send($storeMailData);
            }
        } catch (\Exception $e) {
            $this->logger->info($e->getMessage());
        }

    }

        /**
     * @param $order
     * @return string
     * @throws \Exception
     */
    private function getPaymentHtml($order)
    {
        return $this->paymentHelper->getInfoBlockHtml(
            $order->getPayment(),
            $order->getStore()->getStoreId()
        );
    }


    /**
     * @return DataObject
     */
    protected function prepareTemplate(
        Order $order,
        $template,
        $email = null,
        $store = null
    ) {
        $customerSupportEmails = $this->configHelper->getCustomerSupportEmails();
        $transport = [
            'template_vars' => [
                'order' => $order,
                'order_id' => $order->getId(),
                'customer_name' => $this->getCustomerName($order),
                'created_at_formatted' => $order->getCreatedAtFormatted(10),
                'store' => $store,
                'mage_store' => $order->getStore(),
                'formattedShippingAddress' => $this->getFormattedShippingAddress($order),
                'formattedBillingAddress' => $this->getFormattedBillingAddress($order),
                'payment_html' => $this->getPaymentHtml($order),
                'order_data' => [
                    'customer_name' => $order->getCustomerName(),
                    'is_not_virtual' => $order->getIsNotVirtual()
                ]
            ],
            'store_id' => $order->getStore()->getId(),
            'template' => $template,
            'email' => $email ?: $order->getCustomerEmail(),
            'customer_support_emails' => $email && $store ? $customerSupportEmails : ''
        ];

        $transportObject = new DataObject($transport);
        return $transportObject;
    }

    /**
     * @param Order $order
     * @return string
     */
    protected function getCustomerName(Order $order)
    {
        $orderCustomerName = $order->getCustomerName();

        if ($orderCustomerName == 'Guest') {
            $orderCustomerName = $order->getBillingAddress()->getFirstname() . ' ' . $order->getBillingAddress()->getLastname();
        }

        return $orderCustomerName;
    }

    protected function getFormattedShippingAddress($order)
    {
        return $order->getIsVirtual()
            ? NULL
            : $this->addressRenderer->format($order->getShippingAddress(), 'html');
    }

    protected function getFormattedBillingAddress($order)
    {
        return $this->addressRenderer->format($order->getBillingAddress(), 'html');
    }

    private function refundGiftCards($order, $cards)
    {
        if (!empty($cards) && is_array($cards)) {
            foreach ($cards as &$card) {
                /** @var \Totaltools\Vii\Model\RewriteGiftCardAccount $giftCard */
                $giftCard = $this->giftCAFactory->create();
                $giftCard->loadByCode($card[Giftcardaccount::ID], $card[Giftcardaccount::PIN]);
                if ($giftCard->getId() && isset($card[Giftcardaccount::PRE_AUTH_CODE])) {
                    $giftCard->setExternalReference($order->getIncrementId());
                    $giftCard->refund($card[Giftcardaccount::PRE_AUTH_CODE]);
                    unset($card[Giftcardaccount::PRE_AUTH_CODE]);
                }
            }
            $order->setGiftCards($this->serializer->serialize($cards))->save();
            $message = "Gift Cards Refunded";
            $this->orderHelper->addCommentToStatusHistory($order, $message);
        }
    }
}
