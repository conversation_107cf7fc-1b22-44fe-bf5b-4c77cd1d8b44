<?php

namespace Totaltools\ScheduledImportExport\Plugin\Model\Scheduled\Operation;

/**
 * @package Totaltools_ScheduledImportExport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 */

/**
 * DataPlugin class to intercept \Magento\ScheduledImportExport\Model\Scheduled\Operation\Data
 */
class DataPlugin
{
    /**
     * Storage key for sFTP
     */
    const SFTP_STORAGE = 'sftp';

    /**
     * @param \Magento\ScheduledImportExport\Model\Scheduled\Operation\Data $subject
     * @param array $result
     */
    public function afterGetServerTypesOptionArray($subject, $result)
    {
        $result[self::SFTP_STORAGE] = __('Remote SFTP');
        return $result;
    }
}
