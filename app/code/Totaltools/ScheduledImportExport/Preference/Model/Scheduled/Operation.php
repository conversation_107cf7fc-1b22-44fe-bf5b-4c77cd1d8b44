<?php

namespace Totaltools\ScheduledImportExport\Preference\Model\Scheduled;

/**
 * @package Totaltools_ScheduledImportExport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 */

use Amasty\Label\Plugin\Catalog\Indexer\Product\Price;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\ScheduledImportExport\Model\Scheduled\Operation\Data;
use Magento\Framework\Serialize\Serializer\Json;
use Totaltools\PriceImport\Model\File;
use Totaltools\ScheduledImportExport\Plugin\Model\Scheduled\Operation\DataPlugin;
use Totaltools\PriceImport\Helper\Data as PriceImportHelperData;

class Operation extends \Magento\ScheduledImportExport\Model\Scheduled\Operation
{
    const FILE_EXTENSION = ".txt";

    /**
     * Date model
     *
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    protected $_dateModel;

    /**
     * Core store config
     *
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $_scopeConfig;

    /**
     * @var \Magento\Framework\App\Config\ValueFactory
     */
    protected $_configValueFactory;

    /**
     * @var \Magento\ScheduledImportExport\Model\Scheduled\Operation\DataFactory
     */
    protected $_operationFactory;

    /**
     * @var \Magento\ScheduledImportExport\Model\Scheduled\Operation\GenericFactory
     */
    protected $_schedOperFactory;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * @var \Magento\Framework\Stdlib\StringUtils
     */
    protected $string;

    /**
     * Filesystem instance
     *
     * @var \Magento\Framework\Filesystem
     */
    protected $filesystem;

    /**
     * @var \Magento\Framework\Mail\Template\TransportBuilder
     */
    protected $_transportBuilder;

    /**
     * @var \Magento\Framework\Filesystem\Io\Ftp
     */
    protected $ftpAdapter;

    /**
     * Serializer Instance
     *
     * @var Json
     */
    protected $serializer;

    /**
     * @var \Magento\Framework\Filesystem\Io\Sftp
     */
    protected $sftpAdapter;

    private $priceImporterHelper;

    /**
     * @var File
     */
    private File $file;

    /**
     * @param \Magento\Framework\Model\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     * @param \Magento\ScheduledImportExport\Model\Scheduled\Operation\GenericFactory $schedOperFactory
     * @param \Magento\ScheduledImportExport\Model\Scheduled\Operation\DataFactory $operationFactory
     * @param \Magento\Framework\App\Config\ValueFactory $configValueFactory
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $dateModel
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Framework\Stdlib\StringUtils $string
     * @param \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder
     * @param \Magento\Framework\Filesystem\Io\Ftp $ftpAdapter
     * @param \Magento\Framework\Filesystem\Io\Sftp $sftpAdapter
     * @param \Magento\Framework\Model\ResourceModel\AbstractResource|null $resource
     * @param \Magento\Framework\Data\Collection\AbstractDb|null $resourceCollection
     * @param PriceImportHelperData $priceImporterHelper
     * @param File $file
     * @param array $data
     * @param Json|null $serializer
     */
    public function __construct(
        \Magento\Framework\Model\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        \Magento\ScheduledImportExport\Model\Scheduled\Operation\GenericFactory $schedOperFactory,
        \Magento\ScheduledImportExport\Model\Scheduled\Operation\DataFactory $operationFactory,
        \Magento\Framework\App\Config\ValueFactory $configValueFactory,
        \Magento\Framework\Stdlib\DateTime\DateTime $dateModel,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Framework\Stdlib\StringUtils $string,
        \Magento\Framework\Mail\Template\TransportBuilder $transportBuilder,
        \Magento\Framework\Filesystem\Io\Ftp $ftpAdapter,
        \Magento\Framework\Filesystem\Io\Sftp $sftpAdapter,
        PriceImportHelperData $priceImporterHelper,
        File $file,
        \Magento\Framework\Model\ResourceModel\AbstractResource $resource = null,
        \Magento\Framework\Data\Collection\AbstractDb $resourceCollection = null,
        array $data = [],
        Json $serializer = null
    ) {
        $this->sftpAdapter = $sftpAdapter;
        $this->priceImporterHelper = $priceImporterHelper;

        parent::__construct(
            $context,
            $registry,
            $filesystem,
            $storeManager,
            $schedOperFactory,
            $operationFactory,
            $configValueFactory,
            $dateModel,
            $scopeConfig,
            $string,
            $transportBuilder,
            $ftpAdapter,
            $resource,
            $resourceCollection,
            $data,
            $serializer
        );
        $this->_init(\Magento\ScheduledImportExport\Model\ResourceModel\Scheduled\Operation::class);

        $this->priceImporterHelper = $priceImporterHelper;
        $this->file = $file;
    }

    /**
     * @inheritdoc
     */
    protected function writeData($filePath, $fileContent)
    {
        $this->validateAdapterType();
        $fileInfo = $this->getFileInfo();
        if (Data::FTP_STORAGE == $fileInfo['server_type']) {
            $this->ftpAdapter->open($this->_prepareIoConfiguration($fileInfo));
            $filePath = '/' . trim($filePath, '\\/');
            $result = $this->ftpAdapter->write($filePath, $fileContent);
        } elseif (DataPlugin::SFTP_STORAGE == $fileInfo['server_type']) {
            $fileInfo['username'] = $fileInfo['user'];
            $this->sftpAdapter->open($fileInfo);
            $filePath = '/' . trim($filePath, '\\/');
            $result = $this->sftpAdapter->write($filePath, $fileContent);
        } else {
            $rootDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::ROOT);
            $result = $rootDirectory->writeFile($filePath, $fileContent);
        }

        return $result;
    }

    /**
     * @inheritdoc
     */
    protected function readData($source, $destination)
    {
        $tmpDirectory = $this->filesystem->getDirectoryWrite(DirectoryList::VAR_DIR);

        $this->validateAdapterType();
        $fileInfo = $this->getFileInfo();
        if (Data::FTP_STORAGE == $fileInfo['server_type']) {
            $this->ftpAdapter->open($this->_prepareIoConfiguration($fileInfo));
            $source = '/' . trim($source, '\\/');
            $result = $this->ftpAdapter->read($source, $tmpDirectory->getAbsolutePath($destination));
        } elseif (DataPlugin::SFTP_STORAGE == $fileInfo['server_type']) {
            $fileInfo['username'] = $fileInfo['user'];
            $this->sftpAdapter->open($fileInfo);
            $source = '/' . trim($source, '\\/');
            $result = $this->sftpAdapter->read($source, $tmpDirectory->getAbsolutePath($destination));
            // remove sftp file if empty
            $this->removeEmptyFile($tmpDirectory, $source, $destination);
        } else {
            $rootDirectory = $this->filesystem->getDirectoryRead(DirectoryList::ROOT);
            if (!$rootDirectory->isExist($source)) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Import path %1 not exists', $source));
            }
            $contents = $rootDirectory->readFile($rootDirectory->getRelativePath($source));
            $result = $tmpDirectory->writeFile($destination, $contents);
        }
        if (!$result) {
            throw new \Magento\Framework\Exception\LocalizedException(__('We can\'t read the file.'));
        }

        return $tmpDirectory->getAbsolutePath($destination);
    }

    /**
     * @param $tmpDirectory
     * @param $source
     * @param $desination
     * @return void
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function removeEmptyFile($tmpDirectory, $source, $desination) {
        if (!$tmpDirectory->isExist($tmpDirectory->getAbsolutePath($desination))) {
            throw new \Magento\Framework\Exception\LocalizedException(__('Import path %1 not exists', $desination));
        }
        $content = $tmpDirectory->readFile($tmpDirectory->getAbsolutePath($desination));

        if (empty($content)) {
            $this->sftpAdapter->rm($source);
        }
    }

    /**
     * Get file based on "file_info" from server (ftp, local) and put to tmp directory
     *
     * @param \Magento\ScheduledImportExport\Model\Scheduled\Operation\OperationInterface $operation
     * @throws \Magento\Framework\Exception\LocalizedException
     * @return string full file path
     */
    public function getFileSource(
        \Magento\ScheduledImportExport\Model\Scheduled\Operation\OperationInterface $operation
    ) {
        $fileInfo = $this->getFileInfo();
        if (empty($fileInfo['file_name']) || empty($fileInfo['file_path'])) {
            throw new \Magento\Framework\Exception\LocalizedException(
                __('We can\'t read the file source because the file name is empty.')
            );
        }
        $operation->addLogComment(__('Connecting to server'));
        $operation->addLogComment(__('Reading import file'));

        $extension = pathinfo($fileInfo['file_name'], PATHINFO_EXTENSION);
        if (isset($fileInfo['file_name']) && strpos($fileInfo['file_name'], "%s") !== false ) {
            $fileName = $this->priceImporterHelper->setSftpFileNameFormat($fileInfo);
        } else {
            $fileName =$fileInfo['file_name'];
        }

        $operation->addLogComment(__('Final file name: ' . $fileName));

        $filePath = rtrim($fileInfo['file_path'], '\\/') . '/' . $fileName;
        $tmpFile = DirectoryList::TMP . '/' .uniqid() . '.' . $extension;

        try {
            $operation->addLogComment(__('Source file: ' . $filePath));
            $operation->addLogComment(__('Destination file: ' . $tmpFile));

            $tmpFilePath = $this->readData($filePath, $tmpFile);
        } catch (\Magento\Framework\Exception\FileSystemException $e) {
            throw new \Magento\Framework\Exception\LocalizedException(__('We can\'t read the import file.'));
        }
        $operation->addLogComment(__('Save history file content "%1"', $this->getHistoryFilePath()));
        $this->_saveOperationHistory($tmpFilePath);
        return $tmpFilePath;
    }

    /**
     * Send email notification
     *
     * @param array $vars
     * @return $this
     */
    public function sendEmailNotification($vars = [])
    {
        $fileInfo = $this->getFileInfo();
        if (DataPlugin::SFTP_STORAGE == $fileInfo['server_type']) {
            if (isset($fileInfo['file_name']) && strpos($fileInfo['file_name'], "%s") !== false ) {
                if (empty($this->file->getFileName())) {
                    return false;
                }
            }
        }

        $storeId = $this->_storeManager->getStore()->getId();
        $copyTo = explode(',', $this->getEmailCopy());
        $copyMethod = $this->getEmailCopyMethod();

        $receiverEmail = $this->_scopeConfig->getValue(
            self::CONFIG_PREFIX_EMAILS . $this->getEmailReceiver() . '/email',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
        $receiverName = $this->_scopeConfig->getValue(
            self::CONFIG_PREFIX_EMAILS . $this->getEmailReceiver() . '/name',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );

        // Set all required params and send emails
        $this->_transportBuilder->setTemplateIdentifier(
            $this->getEmailTemplate()
        )->setTemplateOptions(
            [
                'area' => \Magento\Backend\App\Area\FrontNameResolver::AREA_CODE,
                'store' => \Magento\Store\Model\Store::DEFAULT_STORE_ID,
            ]
        )->setTemplateVars(
            $vars
        )->setFrom(
            $this->getEmailSender()
        )->addTo(
            $receiverEmail,
            $receiverName
        );
        if ($copyTo && $copyMethod == 'bcc') {
            // Add bcc to customer email
            foreach ($copyTo as $email) {
                $this->_transportBuilder->addBcc($email);
            }
        }
        /** @var \Magento\Framework\Mail\TransportInterface $transport */
        $transport = $this->_transportBuilder->getTransport();
        $transport->sendMessage();

        // Email copies are sent as separated emails if their copy method is 'copy'
        if ($copyTo && $copyMethod == 'copy') {
            foreach ($copyTo as $email) {
                $this->_transportBuilder->setTemplateIdentifier(
                    $this->getEmailTemplate()
                )->setTemplateOptions(
                    [
                        'area' => \Magento\Backend\App\Area\FrontNameResolver::AREA_CODE,
                        'store' => \Magento\Store\Model\Store::DEFAULT_STORE_ID,
                    ]
                )->setTemplateVars(
                    $vars
                )->setFrom(
                    $this->getEmailSender()
                )->addTo(
                    $email
                )->getTransport()->sendMessage();
            }
        }

        return $this;
    }
}
