<?xml version="1.0"?>
<!--
/**
 * @package Totaltools_ScheduledImportExport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\ScheduledImportExport\Model\Scheduled\Operation\Data">
        <plugin name="Totaltools_ScheduledImportExport_Operation_Data" type="Totaltools\ScheduledImportExport\Plugin\Model\Scheduled\Operation\DataPlugin" />
    </type>

    <preference for="Magento\ScheduledImportExport\Model\Scheduled\Operation" type="Totaltools\ScheduledImportExport\Preference\Model\Scheduled\Operation" />
</config>
