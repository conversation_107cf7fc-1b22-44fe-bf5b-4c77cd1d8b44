<?xml version="1.0"?>
<!--
/**
 * @package Totaltools_ScheduledImportExport
 * <AUTHOR> I<PERSON>bal <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 */   
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <referenceBlock name="operation.form.after">
                <action method="setTemplate">
                    <argument name="template" xsi:type="string">Totaltools_ScheduledImportExport::scheduled/form/after.phtml</argument>
                </action>
            </referenceBlock>
        </referenceContainer>
    </body>
</page>
