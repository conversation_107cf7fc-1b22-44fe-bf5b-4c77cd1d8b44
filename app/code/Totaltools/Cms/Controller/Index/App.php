<?php
/**
 * @category  Totaltools
 * @package   Totaltools_
 * <AUTHOR> <<EMAIL>>
 * @copyright 2021 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Cms\Controller\Index;

use Magento\Framework\App\Action\Action;
use Magento\Cms\Helper\Page;
use Magento\Framework\App\Action\Context;
use Magento\Backend\Model\View\Result\ForwardFactory;
use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ObjectManager;

class App extends Action implements HttpGetActionInterface
{
    const XML_MOBILE_HOME_PAGE = "totaltools_cms/mobile/home_page";

    /**
     * @var Page|null
     */
    protected $page;

    /**
     * @var ForwardFactory
     */
    protected $resultForwardFactory;

    protected $config;

    /**
     * @param Context $context
     * @param ForwardFactory $forwardFactory
     * @param Page|null $page
     */
    public function __construct(
        Context $context,
        ForwardFactory $forwardFactory,
        ScopeConfigInterface $config,
        Page $page = null
    )
    {
        $this->page = $page ? : ObjectManager::getInstance()->get(Page::class);
        $this->resultForwardFactory = $forwardFactory;
        $this->config = $config ? : ObjectManager::getInstance()->get(ScopeConfigInterface::class);
        parent::__construct($context);
    }

    /**
     * @return bool|\Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface|\Magento\Framework\View\Result\Page|Forward
     */
    public function execute()
    {
        $pageId = $this->config->getValue(self::XML_MOBILE_HOME_PAGE);
        $resultPage = $this->page->prepareResultPage($this,$pageId);
        if (!$resultPage) {
            /** @var Forward $resultForward */
            $resultForward = $this->resultForwardFactory->create();
            $resultForward->forward('Index');
            return $resultForward;
        }
        return $resultPage;

        // TODO: Implement execute() method.
    }
}
