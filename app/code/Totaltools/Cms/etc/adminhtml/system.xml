<?xml version="1.0"  ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="totaltools_cms" translate="label" sortOrder="10" showInDefault="1" showInWebsite="1"
                 showInStore="0">
            <label>Mobile App Home</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Cms::config</resource>
            <group id="mobile" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1"
                   showInStore="1">
                <label>Setting</label>
                <field id="home_page" translate="label" type="select" sortOrder="11" showInDefault="1" showInStore="1" showInWebsite="1">
                    <label>Cms Page</label>
                    <source_model>Magento\Cms\Model\Config\Source\Page</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
