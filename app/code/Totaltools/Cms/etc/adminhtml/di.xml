<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Cms\Model\ResourceModel\Block\Grid\Collection" type="Totaltools\Cms\Model\Cms\ResourceModel\Block\Grid\Collection" />
    <type name="Totaltools\Cms\Model\Cms\ResourceModel\Block\Grid\Collection">
        <arguments>
            <argument name="mainTable" xsi:type="string">cms_block</argument>
            <argument name="eventPrefix" xsi:type="string">cms_block_grid_collection</argument>
            <argument name="eventObject" xsi:type="string">block_grid_collection</argument>
            <argument name="resourceModel" xsi:type="string">Magento\Cms\Model\ResourceModel\Block</argument>
        </arguments>
    </type>
</config>