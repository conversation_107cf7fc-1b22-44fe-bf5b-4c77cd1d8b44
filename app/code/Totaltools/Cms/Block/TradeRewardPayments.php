<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Cms
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Cms\Block;

use Magento\Framework\View\Element\Template;

class TradeRewardPayments extends Template
{

    /**
	 * Constructor 
	 * 
	 * @param Template\Context $context
	 */
    public function __construct(
        Template\Context $context,
        array $data = []
    ) {
        parent::__construct($context, $data);
    }

    public function getUnlockUrl() : string {
        return $this->getUrl('customer/business/unlock');;
    }
}