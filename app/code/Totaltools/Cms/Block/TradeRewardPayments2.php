<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Cms
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Cms\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Customer\Model\Session;

class TradeRewardPayments extends Template
{
    const UNLOCK_URL= 'totaltools_customer/registration/unlock_url'; 
    /**
	 * @var \Magento\Customer\Model\Session
	 */
	protected $customerSession;

    /**
	 * @var \Magento\Customer\Api\CustomerRepositoryInterface
	 */
	protected $customerRepositoryInterface;

    /**
     * @var Magento\Customer\Model\Customer
     */
    protected $customer = null;

    /**
	 * Constructor 
	 * 
	 * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface
	 */
    public function __construct(
        Template\Context $context,
        Session $customerSession, 
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface,
        array $data = []
    ) {
        /**
		 * Set customer session
		 * 
		 * @var \Magento\Customer\Model\Session $customerSession
		 */
		$this->customerSession = $customerSession;

        /**
		 * Set customer repository interface
		 * 
		 * @var \Magento\Customer\Api\CustomerRepositoryInterface $customerRepositoryInterface
		 */
		$this->customerRepositoryInterface = $customerRepositoryInterface;

       

        parent::__construct($context, $data);
    }

    public function getCustomerDataString() : string {
        $firstName = $this->customer->getFirstname();
        $surname = $this->customer->getLastname();
        $company = $this->customer->getCustomAttribute('customer_company');
        $company = $company ? $company->getValue() : '';
        $email = $this->customer->getEmail();
        $abn = $this->customer->getCustomAttribute('abn');
        $abn = $abn ? $abn->getValue() : '';
        $mobileNumber = $this->getCustomerMobile();
        return "first_name=$firstName&surname=$surname&business_name=$company&abn=$abn&email=$email&mobile=$mobileNumber";
    }

    public function getCustomerMobile()
    {
        $mobileNumber = $this->customer->getCustomAttribute('mobile_number');
        $auMobileNumber = $mobileNumber ?  preg_replace('/^0/', '+61', $mobileNumber->getValue() ?? '') : '';
        $auMobileNumber =  wordwrap($auMobileNumber, 3, "-", true);
        return $auMobileNumber;
    }

   

    public function isTradeRewardsAccount()
    {
        $this->getCustomer();
        $businessAccount = $this->customer->getCustomAttribute('business_account');
        $businessAccount = $businessAccount ? $businessAccount->getValue() : '';
        return  (bool)$businessAccount;
    }

    public function getUnlockUrl() : string {
        $unlockUrl = $this->getTradeRewardsUrl();
        
        if ($this->customerSession->isLoggedIn() && $this->isTradeRewardsAccount()) {
            $unlockUrlString = $this->getCustomerDataString();
            $unlockUrl = $this->_scopeConfig->getValue(self::UNLOCK_URL).$unlockUrlString;
        }
        return $unlockUrl;
    }

    public function getTradeRewardsUrl() : string {
        $tradeRewardsUrl = $this->getUrl('customer/account/create',['trade_rewards'=>true,'credit_request'=>true]);
        if ($this->customerSession->isLoggedIn() ) {
            $tradeRewardsUrl =  $this->getUrl('customer/business/account');
        }
        return $tradeRewardsUrl;
    }

    /**
	 * Get customer
	 */
	public function getCustomer()
	{ 
		$this->customer = null;
        if($this->customerSession->getCustomerId()) {
            $this->customer = $this->customerRepositoryInterface->getById($this->customerSession->getCustomerId());
        }
		
		return $this->customer;
	}
}