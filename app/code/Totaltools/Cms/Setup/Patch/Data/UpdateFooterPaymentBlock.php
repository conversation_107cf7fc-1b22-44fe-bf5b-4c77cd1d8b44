<?php

namespace Totaltools\Cms\Setup\Patch\Data;

use Magento\Cms\Api\BlockRepositoryInterface;
use Magento\Cms\Model\BlockFactory;
use Magento\Cms\Model\GetBlockByIdentifier;
use Magento\Cms\Model\ResourceModel\Block as ResourceBlock;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Module\Dir;
use Magento\Framework\Module\Dir\Reader as ModuleDirReader;
use Magento\Framework\Setup\Patch\DataPatchInterface;
use Magento\Framework\Setup\Patch\PatchHistory;
use Magento\Framework\UrlInterface;
use Magento\Store\Model\StoreManagerInterface;

class UpdateFooterPaymentBlock implements DataPatchInterface
{
    const MODULE_NAME = 'Totaltools_Cms';

    /**
     * @var BlockFactory
     */
    private $blockFactory;

    /**
     * @var BlockRepositoryInterface
     */
    private $blockRepository;

    /**
     * @var ResourceBlock
     */
    private $blockResourceModel;

    /**
     * @var ModuleDirReader
     */
    private $moduleReader;

    /**
     * @var PatchHistory
     */
    private $patchHistory;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var UrlInterface
     */
    private $baseUrl;

    /**
     * @var GetBlockByIdentifier
     */
    private $getBlockByIdentifier;

    /**
     * Constructor
     *
     * @param BlockFactory $blockFactory
     * @param BlockRepositoryInterface $blockRepository
     * @param ResourceBlock $blockResourceModel
     * @param ModuleDirReader $moduleReader
     * @param PatchHistory $patchHistory
     * @param StoreManagerInterface $storeManager
     * @param UrlInterface $baseUrl
     * @param GetBlockByIdentifier $getBlockByIdentifier
     */
    public function __construct(
        BlockFactory $blockFactory,
        BlockRepositoryInterface $blockRepository,
        ResourceBlock $blockResourceModel,
        ModuleDirReader $moduleReader,
        PatchHistory $patchHistory,
        StoreManagerInterface $storeManager,
        UrlInterface $baseUrl,
        GetBlockByIdentifier $getBlockByIdentifier
    ) {
        $this->blockFactory = $blockFactory;
        $this->blockRepository = $blockRepository;
        $this->blockResourceModel = $blockResourceModel;
        $this->moduleReader = $moduleReader;
        $this->patchHistory = $patchHistory;
        $this->storeManager = $storeManager;
        $this->baseUrl = $baseUrl;
        $this->getBlockByIdentifier = $getBlockByIdentifier;
    }

    /**
     * @inheritDoc
     */
    public function apply()
    {
        $this->createOrUpdateBlock('footer_payments', 'footer_payments');
    }

    /**
     * Update cms block
     * @param string $blockTitle
     * @param string $identifier
     * @param string $storeCode
     * @return void
     * @throws NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function createOrUpdateBlock(
        string $blockTitle,
        string $identifier
    )
    {
        $storeId = 0;

        try {
            /** @var \Magento\Cms\Model\Block $block */
            $block = $this->getBlockByIdentifier->execute($identifier, $storeId);
        } catch (NoSuchEntityException $e) {
            $block = $this->blockFactory->create();
        }

        $block->setTitle($blockTitle)
            ->setIdentifier($identifier)
            ->setContent(
                file_get_contents(
                    sprintf(
                        "%s/migration/blocks/%s.txt",
                        $this->moduleReader->getModuleDir(Dir::MODULE_ETC_DIR, static::MODULE_NAME),
                        $identifier
                    )
                )
            );

        $this->blockRepository->save($block);
    }

    /**
     * @inheritDoc
     */
    public static function getDependencies()
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    public function getAliases()
    {
        return [];
    }
}
