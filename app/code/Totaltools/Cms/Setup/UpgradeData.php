<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Cms\Setup;

use Magento\Cms\Model\Page;
use Magento\Cms\Model\PageFactory;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;

use Magento\Cms\Model\Block;
use Magento\Cms\Model\BlockFactory;
use Magento\Store\Model\Store;
use \Magento\Framework\File\Csv;

class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var PageFactory
     */
    private $pageFactory;

    /**
     * @var BlockFactory
     */
    private $blockFactory;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvReader;

    /**
     * @param PageFactory $pageFactory
     * @param BlockFactory $blockFactory
     * @param Csv $csvReader
     */
    public function __construct(PageFactory $pageFactory, BlockFactory $blockFactory, Csv $csvReader)
    {
        $this->pageFactory = $pageFactory;
        $this->blockFactory = $blockFactory;
        $this->csvReader = $csvReader;
    }

    /**
     * Create page
     *
     * @return Page
     */
    private function createPage()
    {
        return $this->pageFactory->create();
    }

    /**
     * Create block
     *
     * @return Block
     */
    private function createBlock()
    {
        return $this->blockFactory->create();
    }

    /**
     * Upgrades data for a module
     *
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface $context
     * @return void
     * @SuppressWarnings(PHPMD.ExcessiveMethodLength)
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        /* Directory separator */
        $ds = DIRECTORY_SEPARATOR;

        $fixtureFolder = dirname(dirname(__FILE__)) .$ds. 'fixtures'.$ds.'blocks';

        $versions = glob($fixtureFolder .$ds. "*", GLOB_ONLYDIR);
        if (count($versions)) {
            foreach ($versions as $version) {
                $_version = basename($version);
                if (is_numeric(str_replace('.', '', $_version))) {
                    if (version_compare($context->getVersion(), $_version, '<')) {
                        /* Insert / update blocks */
                        $rows = $this->csvReader->getData($fixtureFolder.$ds. 'blocks.csv');
                        $header = array_shift($rows);

                        foreach ($rows as $row) {
                            $data = [];
                            foreach ($row as $key => $value) {
                                $data[$header[$key]] = $value;
                            }
                            $row = $data;
                            $file = $version .$ds. $row['identifier'].'.html';
                            if(file_exists($file)){
                                $row['content'] = file_get_contents($file);
                                if (array_key_exists('identifier', $row)) {
                                    $this->blockFactory->create()->load($row['identifier'], 'identifier')
                                        ->addData($row)
                                        ->setStores([Store::DEFAULT_STORE_ID])
                                        ->save();
                                }
                            }
                        }
                    }
                }
            }
        }
        $setup->endSetup();
    }
}
