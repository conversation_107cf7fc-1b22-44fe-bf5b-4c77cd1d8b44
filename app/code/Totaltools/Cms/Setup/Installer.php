<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\Cms\Setup;

use Magento\Framework\Setup;

class Installer implements Setup\SampleData\InstallerInterface
{
    /**
     * @var \Totaltools\Cms\Model\Page
     */
    private $page;

    /**
     * @var \Totaltools\Cms\Model\Block
     */
    private $block;

    /**
     * @param \Totaltools\Cms\Model\Page  $page
     * @param \Totaltools\Cms\Model\Block $block
     */
    public function __construct(
        \Totaltools\Cms\Model\Page $page,
        \Totaltools\Cms\Model\Block $block
    ) {
        $this->page = $page;
        $this->block = $block;
    }

    /**
     * {@inheritdoc}
     */
    public function install()
    {
        $this->block->install(
            [
                'Totaltools_Cms::fixtures/blocks/blocks.csv'
            ]
        );
    }
}