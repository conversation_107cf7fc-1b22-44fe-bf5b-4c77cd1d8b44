<?php

namespace Totaltools\Brand\Block;

class Info extends \Magento\Catalog\Block\Product\View\Description
{
	protected $_objectManager = null;
	protected $_cachedCategory = null; // the category associated with brand

	/**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\App\ObjectManager $objectManager
     * @param array $data
     */
	public function __construct(
		\Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        array $data = []) {
		$this->_objectManager = $objectManager;
		parent::__construct($context, $registry, $data);
	}

    /**
     * add class into catalog product view body
     */
    protected function _prepareLayout()
    {
        if ($this->getBrandImageUrl()) {
            $this->pageConfig->addBodyClass('has-column-right');
        }
        return parent::_prepareLayout();
    }

	/**
	* Get brand url
	*/
	public function getBrandTitle() {
		$product = $this->getProduct();
		if (!$product) {
			return null;
		}

		return $product->getAttributeText('brand');
	}

	/**
	* Get brand url
	*/
	public function getBrandUrl() {
		$category = $this->_getCategory();
		
		if (!$category) {
			return null;
		}

		return $category->getUrl();
	}

	/**
	* Get brand image url
	*/
	public function getBrandImageUrl() {
		$category = $this->_getCategory();
		if (!$category) {
			return null;
		}

		return $category->getImageUrl();
	}

	/**
	 * Get associated category to the brand label
	 *
	 * @param string $brandLabel
	 */
	private function _getCategory() {
		// get brand label
		$brandLabel = $this->getBrandTitle();
		if (!$brandLabel) {
			return null;
		}

		$categoryModel = $this->_objectManager->create('Magento\Catalog\Model\Category');
		$urlKey = $categoryModel->formatUrlKey($brandLabel);

		if (!$this->_cachedCategory) {
			$categoryFactory = $this->_objectManager->create('Magento\Catalog\Model\ResourceModel\Category\CollectionFactory');
			$collection = $categoryFactory->create()
	            ->addAttributeToFilter('url_key', $urlKey)
	            ->addAttributeToSelect('*');
	    if ($collection->getSize() > 0) {
	    	$category = $collection->getFirstItem();
	    } else {
	    	$category = null;
	    }

	    $this->_cachedCategory = $category;
	  }

    return $this->_cachedCategory;
	}
}
?>