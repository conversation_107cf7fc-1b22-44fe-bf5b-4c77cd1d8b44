<?php

namespace Totaltools\SalesRule\Rewrite\Magento\SalesRule\Model\Rule\Condition\Product;


use Magento\Framework\App\Config\ScopeConfigInterface;

class Subselect extends \Magento\SalesRule\Model\Rule\Condition\Product\Subselect
{
    const USE_BUNDLE_CHILDREN_TOTAL = 'checkout/shoppingcartrule/use_bundle_children_total';
    const GRAND_PARENT_CLASS = 'Magento\SalesRule\Model\Rule\Condition\Product\Combine';
    const PARENT_CLASS = 'Magento\SalesRule\Model\Rule\Condition\Product\Subselect';
    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    public function __construct(
        \Magento\Rule\Model\Condition\Context $context,
        \Magento\SalesRule\Model\Rule\Condition\Product $ruleConditionProduct,
        ScopeConfigInterface $scopeConfig,
        array $data = []
    ) {
        parent::__construct($context, $ruleConditionProduct, $data);
        $this->scopeConfig = $scopeConfig;
    }

    /**
     * Validate
     *
     * @param \Magento\Framework\Model\AbstractModel $model
     * @return bool
     * @SuppressWarnings(PHPMD.CyclomaticComplexity)
     */
    public function validate(\Magento\Framework\Model\AbstractModel $model)
    {
        if (!$this->getConditions()) {
            return false;
        }
        $attr = $this->getAttribute();
        $total = 0;
        $useBundleChildrenTotal = $this->scopeConfig->getValue(self::USE_BUNDLE_CHILDREN_TOTAL);
        $grandParent = self::GRAND_PARENT_CLASS;
        if (!class_exists($grandParent)) {
            $grandParent = get_parent_class(get_parent_class($this));
            if ($grandParent == self::PARENT_CLASS) {
                $grandParent = get_parent_class($grandParent);
            }
        }
        foreach ($model->getQuote()->getAllVisibleItems() as $item) {
            $hasValidChild = false;
            $useChildrenTotal = $useBundleChildrenTotal ? ($item->getProductType() == \Magento\Catalog\Model\Product\Type::TYPE_BUNDLE) : false;
            $childrenAttrTotal = 0;
            $children = $item->getChildren();
            if (!empty($children)) {
                foreach ($children as $child) {
                    if ($grandParent::validate($child)) {
                        $hasValidChild = true;
                        if ($useChildrenTotal) {
                            $childrenAttrTotal += $child->getData($attr);
                        }
                    }
                }
            }
            if ($hasValidChild || $grandParent::validate($item)) {
                $total += ($hasValidChild && $useChildrenTotal && $childrenAttrTotal > 0)
                    ? $childrenAttrTotal * $item->getQty()
                    : $item->getData($attr);
            }
        }

        return $this->validateAttribute($total);
    }
}

