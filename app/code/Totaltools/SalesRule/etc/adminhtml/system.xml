<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
	<system>
		<section id="checkout" >
			<resource>Totaltools_SalesRule::config_totaltools_salesrule</resource>
			<group id="shoppingcartrule" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label">
				<label>Shopping Cart Rule Setting</label>
				<field id="use_bundle_children_total" showInDefault="1" showInStore="1" showInWebsite="1" sortOrder="10" translate="label" type="select">
					<label>Use children's total for shopping cart rule</label>
					<comment/>
					<source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
				</field>
			</group>
		</section>
	</system>
</config>
