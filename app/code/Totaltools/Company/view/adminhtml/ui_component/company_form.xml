<?xml version="1.0" encoding="UTF-8"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <fieldset name="information">
        <field name="account_code" sortOrder="30" formElement="input">
            <settings>
                <label translate="true">Account Code</label>
                <dataScope>account_code</dataScope>
            </settings>
        </field>
    </fieldset>
    <fieldset name="company_admin">
        <field name="pronto_position" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">pronto_position</item>
                </item>
            </argument>
            <settings>
                <validation>
                    <rule name="required-entry" xsi:type="boolean">true</rule>
                </validation>
                <dataType>text</dataType>
                <label translate="true">My Trade</label>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Totaltools\Company\Model\Company\Source\ProntoPosition"/>
                    </settings>
                </select>
            </formElements>
        </field>
        <field name="preferred_store" formElement="select">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">preferred_store</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">My Preferred Store</label>
            </settings>
            <formElements>
                <select>
                    <settings>
                        <options class="Totaltools\Company\Model\Company\Source\PreferredStore"/>
                    </settings>
                </select>
            </formElements>
        </field>
    </fieldset>
</form>