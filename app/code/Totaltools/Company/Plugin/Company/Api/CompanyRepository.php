<?php

namespace Totaltools\Company\Plugin\Company\Api;

use Magento\Company\Api\CompanyRepositoryInterface;
use Magento\Company\Api\Data\CompanyInterface;
use Magento\Framework\Api\SearchResultsInterface as SearchResultsInterfaceAlias;

class CompanyRepository
{
    /**
     * @var \Magento\Framework\App\RequestInterface
     */
    protected $_request;

    /**
     * CompanyRepository constructor.
     *
     * @param \Magento\Framework\App\RequestInterface $request
     */
    public function __construct(\Magento\Framework\App\RequestInterface $request)
    {
       $this->_request = $request;
    }

    /**
     * @const Account Code field.
     */
    const ACCOUNT_CODE_FIELD = 'account_code';

    /**
     * Add "account_code" extension attribute to company data object to make it accessible in API data
     *
     * @param CompanyRepositoryInterface $subject
     * @param CompanyInterface           $company
     *
     * @return \Magento\Company\Api\Data\CompanyInterface
     */
    public function afterGet(CompanyRepositoryInterface $subject, CompanyInterface $company)
    {
        $accountCode = $company->getData(self::ACCOUNT_CODE_FIELD);

        $extensionAttributes = $company->getExtensionAttributes();

        $extensionAttributes->setAccountCode($accountCode);

        $company->setExtensionAttributes($extensionAttributes);

        return $company;
    }

    /**
     * Add "account_code" extension attribute to company data object to make it accessible in API data
     *
     * @param CompanyRepositoryInterface    $subject
     * @param SearchResultsInterfaceAlias $searchResult
     *
     * @return SearchResultsInterfaceAlias
     */
    public function afterGetList(CompanyRepositoryInterface $subject, SearchResultsInterfaceAlias $searchResult)
    {
        $companies = $searchResult->getItems();

        foreach ($companies as &$company) {
            $accountCode = $company->getData(self::ACCOUNT_CODE_FIELD);

            $extensionAttributes = $company->getExtensionAttributes();

            $extensionAttributes->setAccountCode($accountCode);

            $company->setExtensionAttributes($extensionAttributes);
        }

        return $searchResult;
    }

    /**
     * Add "account_code" extension attribute to company data object to save it to the DataBase.
     *
     * @param CompanyRepositoryInterface $subject
     * @param CompanyInterface           $company
     *
     * @return void
     */
    public function beforeSave(CompanyRepositoryInterface $subject, CompanyInterface $company)
    {
        $information = $this->_request->getParam('information');
        if (!empty($information) && array_key_exists('account_code', $information)) {
            $company->setAccountCode($information['account_code']);
        }
    }
}