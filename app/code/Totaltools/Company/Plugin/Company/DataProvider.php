<?php

namespace Totaltools\Company\Plugin\Company;

use Magento\Company\Model\Company;
use Magento\Customer\Api\Data\CustomerInterface;

/**
 * Class DataProvider
 * @package Totaltools\Company\Plugin\Company
 */
class DataProvider
{
    const XML_PRONTO_POSITION = 'pronto_position';
    const XML_PREFERRED_STORE = 'preferred_store';

    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * DataProvider constructor.
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
    ) {
        $this->customerRepository = $customerRepository;
    }

    /**
     * Around get information data plugin.
     *
     * @param \Magento\Company\Model\Company\DataProvider $subject
     * @param \Closure $proceed
     * @param \Magento\Company\Api\Data\CompanyInterface $company
     *
     * @return array
     */
    public function aroundGetInformationData(
        \Magento\Company\Model\Company\DataProvider $subject,
        \Closure $proceed,
        \Magento\Company\Api\Data\CompanyInterface $company
    ) {
        $result = $proceed($company);

        if ($company) {
            $result['account_code'] = $company->getAccountCode();
        }

        return $result;
    }

    /**
     * @param Company\DataProvider $subject
     * @param \Closure $proceed
     * @param \Magento\Company\Api\Data\CompanyInterface $company
     * @return array
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function aroundGetCompanyAdminData(
        \Magento\Company\Model\Company\DataProvider $subject,
        \Closure $proceed,
        \Magento\Company\Api\Data\CompanyInterface $company
    ) {
        $customer = $this->customerRepository->getById($company->getSuperUserId());
        $preferredStore = $customer->getCustomAttribute('preferred_store');

        return [
            Company::JOB_TITLE => $customer->getExtensionAttributes()->getCompanyAttributes()->getJobTitle(),
            Company::PREFIX => $customer->getPrefix(),
            Company::FIRSTNAME => $customer->getFirstname(),
            Company::MIDDLENAME => $customer->getMiddlename(),
            Company::LASTNAME => $customer->getLastname(),
            Company::SUFFIX => $customer->getSuffix(),
            Company::EMAIL => $customer->getEmail(),
            Company::GENDER => $customer->getGender(),
            CustomerInterface::WEBSITE_ID => $customer->getWebsiteId(),
            self::XML_PRONTO_POSITION => $customer->getExtensionAttributes()->getProntoPosition(),
            self::XML_PREFERRED_STORE => $preferredStore ? $preferredStore->getValue() : null,
        ];
    }
}