<?php

namespace Totaltools\Company\Observer;

use Magento\Framework\Event\ObserverInterface;

/**
 * Class SaveObserver
 * @package Totaltools\Company\Observer
 */
class SaveObserver implements ObserverInterface
{
    /**
     * @var \Magento\Customer\Api\CustomerRepositoryInterface
     */
    private $customerRepository;

    /**
     * SaveObserver constructor.
     * @param \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
     */
    public function __construct(
        \Magento\Customer\Api\CustomerRepositoryInterface $customerRepository
    ) {
        $this->customerRepository = $customerRepository;
    }

    /**
     * @param \Magento\Framework\Event\Observer $observer
     * @return $this|void
     * @throws \Magento\Framework\Exception\InputException
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\State\InputMismatchException
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $company = $observer->getEvent()->getCompany();
        $request = $observer->getEvent()->getRequest();
        $superUserId = $company->getSuperUserId();
        if ($superUserId) {
            $companyAdmin = $request->getParam('company_admin');
            $customerInterface = $this->customerRepository->getById($superUserId);
            $customerInterface->setCustomAttribute('pronto_position',
                isset($companyAdmin['pronto_position']) ? $companyAdmin['pronto_position'] : '');
            $customerInterface->setCustomAttribute('preferred_store',
                    isset($companyAdmin['preferred_store']) ? $companyAdmin['preferred_store'] : '');
            $this->customerRepository->save($customerInterface);
        }

        return $this;
    }
}
