<?php


namespace Totaltools\Company\Setup;

use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * @var string
     */
    private $_setupVersion;

    /**
     * @param SchemaSetupInterface   $setup
     * @param ModuleContextInterface $context
     */
    public function upgrade(
        SchemaSetupInterface $setup,
        ModuleContextInterface $context
    )
    {
        $this->_setupVersion = $context->getVersion();
        unset($context);

        $setup->startSetup();

        if (version_compare($this->_setupVersion, '1.0.1', '<')) {
            $this->_addAccountCodeCompanyColumn($setup);
        }

        if (version_compare($this->_setupVersion, '1.0.2', '<')) {
            $this->upgradeAccountCodeCompanyColumn($setup);
        }

        $setup->endSetup();
    }

    /**
     * Add account_code column to company table.
     *
     * @param SchemaSetupInterface $setup
     */
    protected function _addAccountCodeCompanyColumn(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->addColumn(
            $setup->getTable('company'),
            'account_code',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER,
                'length'   => 10,
                'nullable' => true,
                'comment'  => 'Account Code'
            ]
        );
    }

    /**
     * @param SchemaSetupInterface $setup
     */
    protected function upgradeAccountCodeCompanyColumn(SchemaSetupInterface $setup)
    {
        $setup->getConnection()->changeColumn(
            $setup->getTable('company'),
            'account_code',
            'account_code',
            [
                'type'     => \Magento\Framework\DB\Ddl\Table::TYPE_TEXT,
                'length'   => '255',
                'nullable' => true,
                'comment'  => 'Account Code',
            ]
        );
    }
}