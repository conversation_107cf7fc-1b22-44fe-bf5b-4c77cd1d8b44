<?php

namespace Totaltools\Company\Model\Company\Source;

use Magento\Company\Model\Company\Source\Provider\CustomerAttributeOptions;
use Magento\Framework\Data\OptionSourceInterface;
use Magento\Eav\Model\Entity\AttributeFactory;

/**
 * Class PreferredStore
 * @package Totaltools\Company\Model\Company\Source
 */
class PreferredStore implements OptionSourceInterface
{
    const ENTITY_TYPE_CUSTOMER = 'customer';

    /**
     * @var AttributeFactory
     */
    private $attributeFactory;

    /**
     * @var CustomerAttributeOptions
     */
    private $provider;

    /**
     * @param AttributeFactory $attributeFactory
     * @param CustomerAttributeOptions $provider
     */
    public function __construct(
        AttributeFactory $attributeFactory,
        CustomerAttributeOptions $provider
    ) {
        $this->attributeFactory = $attributeFactory;
        $this->provider = $provider;
    }

    /**
     * @return array|array[]
     */
    public function toOptionArray()
    {
        return $this->provider->loadOptions('preferred_store');
    }
}
