<?php
/**
 * @package   Totaltools_CatalogSearchOrder
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\CatalogSearchOrder\Setup;

use Magento\Framework\Setup;
use Magento\Eav\Setup\EavSetupFactory;

class UpgradeData implements Setup\UpgradeDataInterface 
{
    /**
     * EAV setup factory
     *
     * @var EavSetupFactory
     */
    private $_eavSetupFactory;
    /**
     * @param EavSetupFactory  $eavSetupFactory
     */
    public function __construct(
        EavSetupFactory $eavSetupFactory
    ) {
        $this->_eavSetupFactory = $eavSetupFactory;
    }

    public function upgrade(
        Setup\ModuleDataSetupInterface $setup,
        Setup\ModuleContextInterface $moduleContext
    ) {
        $setup->startSetup();
        $eavSetup = $this->_eavSetupFactory->create(['setup' => $setup]);
        $eavSetup->updateAttribute(
            \Magento\Catalog\Model\Product::ENTITY,
            'search_order',
            'backend_type', 'int'
        );
        $setup->endSetup();
    }
}