<?php

/**
 * @category    Totaltools
 * @package     Totaltools_CatalogSearchOrder
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\CatalogSearchOrder\Model\Catalog;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Totaltools\CatalogSearchOrder\Helper\Data;
use Magento\Framework\App\ResourceConnection;
use Magento\Eav\Model\Entity\Attribute;
use Psr\Log\LoggerInterface;

/**
 * Class search
 * @package Totaltools\CatalogSearchOrder\Model\Catalog\Boostsearch
 */
class BoostSearch
{
    /**
     * Authorization level of a basic admin session.
     *
     * @see _isAllowed()
     */
    const ADMIN_RESOURCE = 'Totaltools_CatalogSearchOrder::Catalogsearch';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var Data
     */
    private $helper;
    /**
     * @var Search
     */
    private $searchModel;
    /**
     * @var ResourceConnection
     */
    private $resource;
    /**
     * @var AdapterInterface
     */
    private $connection;
    /**
     * @var brandName
     */
    private $brandName;
    /**
     * @var brandBoostValue
     */
    private $brandBoostValue;
    /**
     * @var ebCheck
     */
    private $ebCheck;
    /**
     * @var ebCheckBoostValue
     */
    private $ebCheckBoostValue;
    /**
     * @var backOrderCheck
     */
    private $backOrderCheck;
    /**
     * @var redemptionCheck
     */
    private $redemptionCheck;
    /**
     * @var redemptionCheckBoostValue
     */
    private $redemptionCheckBoostValue;
    /**
     * @var highestBoostValue
     */
    private $highestBoostValue;
    /**
     * @var backOrderCheckBoostValue
     */
    private $backOrderCheckBoostValue;
    /**
     * @var ebAttributeId
     */
    private $ebAttributeId;
    /**
     * @var brandAttributeId
     */
    private $brandAttributeId;
    /**
     * @var brandOptions
     */
    private $brandOptions;
    /**
     * @var isBackOrderAttributeId
     */
    private $isBackOrderAttributeId;
    /**
     * @var isRedumptionAttributeId
     */
    private $isRedumptionAttributeId;
    /**
     * @var unitSoldAttributeId
     */
    private $unitSoldAttributeId;
    /**
     * @var searchOrderAttributeId
     */
    private $searchOrderAttributeId;
    /**
     * @var productStatusAttributeId
     */
    private $productStatusAttributeId;
    /**
     * @var allActiveSkus
     */
    private $allActiveSkus = array();
    /**
     * @var defaultValue
     */
    private $defaultValue = 1;
    /**
     * @var arrBoostData
     */
    private $arrBoostData = array();
    /**
     * @var Attribute
     */
    protected $eavAttribute;
    /**
     * @var isNewCheck
     */
    private $isNewCheck;
    /**
     * @var isNewBoostValue
     */
    private $isNewBoostValue;
    /**
     * @var isNewAttributeId
     */
    private $isNewAttributeId;
    /**
     * @var onPromoCheck
     */
    private $onPromoCheck;
    /**
     * @var onPromoCheckBoostValue
     */
    private $onPromoCheckBoostValue;
    /**
     * @var onPromoAttributeId
     */
    private $onPromoAttributeId;
    /**
     * @var notForSaleCheck
     */
    private $notForSaleCheck;
    /**
     * @var notForSaleCheckBoostValue
     */
    private $notForSaleCheckBoostValue;
    /**
     * @var notForSaleAttributeId
     */
    private $notForSaleAttributeId;
    /**
     * @var limitedStockCheck
     */
    private $limitedStockCheck;
    /**
     * @var limitedStockCheckBoostValue
     */
    private $limitedStockCheckBoostValue;
    /**
     * @var limitedStockAttributeId
     */
    private $limitedStockAttributeId;

    /**
     * @var isHotPriceCheck
     */
    private $isHotPriceCheck;
    /**
     * @var isHotPriceCheckBoostValue
     */
    private $isHotPriceCheckBoostValue;
    /**
     * @var isHotPriceAttributeId
     */
    private $isHotPriceAttributeId;
    /**
     * @var Logger
     */
    private $logger;
    /**
     * @var highestRowId
     */
    private $highestRowId;
    /**
     * @var limit
     */
    private $limit = 5000;
    /**
     * @var totalChunks
     */
    private $totalChunks = 0;
    /**
     * Generate constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param ScopeConfigInterface $scopeConfig
     * @param Totaltools\CatalogSearchOrder\Helper\Data $helper
     */
    public function __construct(
        ScopeConfigInterface $scopeConfig,
        Data $helper,
        ResourceConnection $resourceConnection,
        Attribute $eavAttribute,
        LoggerInterface $logger
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->helper = $helper;
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
        $this->eavAttribute = $eavAttribute;
        $this->logger = $logger;
    }

    /**
     * set variable vales
     */
    public function processFormula()
    {
        $boostFormula = $this->helper->getConfig('boosting_rules');
        //proccess formula
        $processFormula = $this->helper->processFormula($boostFormula);
        foreach ($processFormula as $part) {
            //brand part
            $arrPart = explode('|', $part);
            if ($arrPart[0] == 'brand') {
                $this->brandName = strtolower($arrPart[1]);
                $this->brandBoostValue = $arrPart[2];
            }
            //eb product
            if ($arrPart[0] == 'eb_product') {
                $this->ebCheck = strtolower($arrPart[1]);
                $this->ebCheckBoostValue = $arrPart[2];
            }
            //backorderable
            if ($arrPart[0] == 'is_backorderable') {
                $this->backOrderCheck = strtolower($arrPart[1]);
                $this->backOrderCheckBoostValue = $arrPart[2];
            }
            //redumption
            if ($arrPart[0] == 'is_on_redemption') {
                $this->redemptionCheck = strtolower($arrPart[1]);
                $this->redemptionCheckBoostValue = $arrPart[2];
            }
            //is_new
            if ($arrPart[0] == 'is_new') {
                $this->isNewCheck = strtolower($arrPart[1]);
                $this->isNewBoostValue = $arrPart[2];
            }
            //is_on_promo
            if ($arrPart[0] == 'is_on_promo') {
                $this->onPromoCheck = strtolower($arrPart[1]);
                $this->onPromoCheckBoostValue = $arrPart[2];
            }
            //not_for_sale
            if ($arrPart[0] == 'not_for_sale') {
                $this->notForSaleCheck = strtolower($arrPart[1]);
                $this->notForSaleCheckBoostValue = $arrPart[2];
            }
            //is_limited_stock
            if ($arrPart[0] == 'is_limited_stock') {
                $this->limitedStockCheck = strtolower($arrPart[1]);
                $this->limitedStockCheckBoostValue = $arrPart[2];
            }
            //is_hot_price
            if ($arrPart[0] == 'is_hot_price') {
                $this->isHotPriceCheck = strtolower($arrPart[1]);
                $this->isHotPriceCheckBoostValue = $arrPart[2];
            }
        }
    }
    /**
     * set attribute ids
     */
    public function setAttributeIds()
    {

        //eb_product attribute Id
        $this->ebAttributeId = $this->getAttributeId(Data::EB_PRODUCT);
        //brand attribute Id
        $this->brandAttributeId = $this->getAttributeId(Data::BRAND);
        $this->brandOptions = $this->getBrandOptions((int) $this->brandAttributeId);
        //backorder attribute Id
        $this->isBackOrderAttributeId = $this->getAttributeId(Data::IS_BACKORDERABLE);
        //redumption attribute Id
        $this->isRedumptionAttributeId = $this->getAttributeId(Data::IS_ON_REDUMPTION);
        //unit_sold attribute Id
        $this->unitSoldAttributeId = $this->getAttributeId(Data::UNITS_SOLD);
        //search_order attribute Id
        $this->searchOrderAttributeId = $this->getAttributeId(Data::SEARCH_ORDER);
        //product status
        $this->productStatusAttributeId = $this->getAttributeId(Data::PRODUCT_STATUS);
        //is_new
        $this->isNewAttributeId = $this->getAttributeId(Data::IS_NEW);
        //is_on_promo
        $this->onPromoAttributeId = $this->getAttributeId(Data::IS_ON_PROMO);
        //not_for_sale
        $this->notForSaleAttributeId = $this->getAttributeId(Data::NOT_FOR_SALE);
        //is_limited_stock
        $this->limitedStockAttributeId = $this->getAttributeId(Data::IS_LIMITED_STOCK);
        //is_hot_price
        $this->isHotPriceAttributeId = $this->getAttributeId(Data::IS_HOT_PRICE);
        //fetch all active skus query
        $this->allActiveSkus = $this->getAllActiveSkus($this->productStatusAttributeId);
    }
    /**
     * process skus
     */
    public function processDryRun()
    {
        //set default values
        set_time_limit(600);
        $totalSkus = $this->getCountActiveSkus($this->productStatusAttributeId);
        $this->totalChunks = ceil($totalSkus / $this->limit);
        $this->logger->info('total skus:' . $totalSkus . ':total chunks:' . $this->totalChunks);
        for ($i = 0; $i < $this->totalChunks; $i++) {
            $offSet = $i * (int) $this->limit;
            $this->logger->info("limit:" . $this->limit . ":offset:" . $offSet);
            $allSkus = $this->getChunkActiveSkus($this->productStatusAttributeId, $this->limit, $offSet);
            $this->processSkus($allSkus, $i);
        }
        return $this->arrBoostData;
    }
    /**
     * update best selling attribute
     */
    public function updateBestSellingAttribute()
    {
        set_time_limit(600);
        $maxBoostedSku = $this->getSkuByRowId($this->highestRowId);
        $this->logger->info('Highest Boosted Value:' . ceil($this->highestBoostValue) . ":Sku:" . $maxBoostedSku);
        $this->connection->beginTransaction();
        for ($i = 0; $i < $this->totalChunks; $i++) {
            $this->updateAttributeByChunk($this->arrBoostData[$i]);
            $this->logger->info('Processing chunk:' . $i . ":of total:" . $this->totalChunks . ':chunk size:' . $this->limit);
        }
        $this->connection->commit();
    }

    /**
     * @return array
     */
    public function getAllSkus()
    {

        $select = $this->connection->select()->from($this->getTableCatProEntity(), ['entity_id', 'row_id']);
        return $this->connection->fetchAll($select);
    }

    /**
     * @return array
     */
    public function getAllActiveSkus($attributeId)
    {

        $select = $this->connection->select()->from($this->getTableCatProEntityInt(), ['row_id'])
            ->where(
                "attribute_id =?",
                $attributeId
            )
            ->where(
                "value =?",
                1
            )
            ->where(
                "store_id =?",
                0
            );
        return $this->connection->fetchAll($select);
    }

    /**
     * @param string $productEav
     * @return int
     */
    public function getAttributeId(string $productEav)
    {
        return $this->eavAttribute->getIdByCode("catalog_product", $productEav);
    }
    /**
     * @param string $rowId
     * @param  int $attributeId
     * @return int
     */
    public function getAttributeValue(string $rowId, int $attributeId)
    {
        $select = $this->connection->select()->from($this->getTableCatProEntityInt(), ['value'])
            ->where(
                "attribute_id =?",
                $attributeId
            )
            ->where(
                "row_id =?",
                $rowId
            );
        return $this->connection->fetchOne($select);
    }

    /**
     * 
     * @return connection object
     */
    public function getTableCatProEntity()
    {
        return  $this->connection->getTableName("catalog_product_entity");
    }
    /**
     * 
     * @return connection object
     */
    public function getTableCatProEntityInt()
    {
        return  $this->connection->getTableName("catalog_product_entity_int");
    }
    /**
     * 
     * @return connection object
     */
    public function getTableCatProEntityVarChar()
    {
        return  $this->connection->getTableName("catalog_product_entity_varchar");
    }
    /**
     * @param string $rowId
     * @param  int $attributeId
     * @return int
     */
    public function getAttributeValueVarChar(string $rowId, int $attributeId)
    {
        $select = $this->connection->select()->from($this->getTableCatProEntityVarChar(), ['value'])
            ->where(
                "attribute_id =?",
                $attributeId
            )
            ->where(
                "row_id =?",
                $rowId
            );
        return $this->connection->fetchOne($select);
    }
    /**
     * @param  int $attributeId
     * @return int
     */
    public function getBrandOptions(int $attributeId)
    {
        $storeId = 0;
        $select = $this->connection->select()
            ->from(
                ['c' => $this->connection->getTableName("eav_attribute_option")],
                ['c.option_id', 'ov.value']
            )
            ->joinLeft(
                ['ov' =>  $this->connection->getTableName("eav_attribute_option_value")],
                "c.option_id = ov.option_id"
            )
            ->where(
                "c.attribute_id =?",
                $attributeId
            );
        return $this->connection->fetchAll($select);
    }

    /**
     * @param int $rowId
     * @param  $attributeValue
     * @param int $attributeId
     * @return int
     */
    public function insertAttribute(int $rowId, $attributeValue, int $attributeId)
    {
        $storeId = 0;
        $bind = [
            "attribute_id" => $attributeId,
            "store_id" => $storeId,
            "row_id" => $rowId,
            "value" => $attributeValue
        ];
        return $this->connection->insertOnDuplicate($this->getTableCatProEntityInt(), $bind);
    }

    /**
     * @param int $rowId
     * @return int
     */
    public function getSkuByRowId($rowId)
    {
        $storeId = 0;
        $select = $this->connection->select()->from($this->connection->getTableName("catalog_product_entity"), ['sku'])
            ->where(
                "row_id =?",
                $rowId
            );
        return $this->connection->fetchOne($select);
    }

    /**
     * @return array
     */
    public function getCountActiveSkus($attributeId)
    {

        $select = $this->connection->select()->from($this->getTableCatProEntityInt(), 'COUNT(*)')
            ->where(
                "attribute_id =?",
                $attributeId
            )
            ->where(
                "value =?",
                1
            )
            ->where(
                "store_id =?",
                0
            );
        return $this->connection->fetchOne($select);
    }

    /**
     * @param $limit
     * @param $offSet
     * @param $attributeId
     * @return array
     */
    public function getChunkActiveSkus($attributeId, $limit, $offSet)
    {

        $select = $this->connection->select()->from($this->getTableCatProEntityInt(), ['row_id'])
            ->where(
                "attribute_id =?",
                $attributeId
            )
            ->where(
                "value =?",
                1
            )
            ->where(
                "store_id =?",
                0
            )
            ->limit($limit, $offSet);
        return $this->connection->fetchAll($select);
    }

    /**
     * @param $allSkus
     * @param $chunkNo
     * @return void
     */
    public function processSkus($allSkus, $chunkNo)
    {

        foreach ($allSkus as $sku) {
            //log
            $proSku = $this->getSkuByRowId($sku['row_id']);
            $this->logger->info('Started processing sku:' . $proSku);
            $skuBrandName = '';
            $newSearchOrder = [];
            //get attribute
            //unit_sold
            $unitSoldData = $this->getAttributeValue($sku['row_id'], (int) $this->unitSoldAttributeId);
            //boosted value eb_product
            if ($this->ebCheck) {
                $ebProductData = $this->getAttributeValue($sku['row_id'], (int) $this->ebAttributeId);
                $boostValueEb = (strtolower((string) $this->ebCheck)  ==  'yes' && $ebProductData == 1) ?  $this->ebCheckBoostValue : $this->defaultValue;
                $newSearchOrder['eb'] = $boostValueEb;
            }
            if ($this->brandName) {
                $brandData = $this->getAttributeValue($sku['row_id'], (int) $this->brandAttributeId);
                $key = array_search($brandData, array_column((array) $this->brandOptions, 'option_id'));
                $skuBrandName = $this->brandOptions[$key]['value'];
                $boostValueBrand =  (strtolower($skuBrandName) ==  strtolower((string) $this->brandName)) ?  $this->brandBoostValue : $this->defaultValue;
                $newSearchOrder['brand'] = $boostValueBrand;
            }
            //boosted value backorder
            if ($this->backOrderCheck) {
                $isBackOrderData = $this->getAttributeValue($sku['row_id'], (int) $this->isBackOrderAttributeId);
                $boostValueBo = (strtolower((string) $this->backOrderCheck)   ==  'no' && $isBackOrderData == 0 || $isBackOrderData == null) ?  $this->backOrderCheckBoostValue : $this->defaultValue;
                $newSearchOrder['backorder'] = $boostValueBo;
            }
            //boosted value redemptoin
            if ($this->redemptionCheck) {
                $isRedumptionData = $this->getAttributeValue($sku['row_id'], (int) $this->isRedumptionAttributeId);
                $boostValueRed = (strtolower((string) $this->redemptionCheck) ==  'yes' && $isRedumptionData == 1) ?  $this->redemptionCheckBoostValue  : $this->defaultValue;
                $newSearchOrder['redump'] = $boostValueRed;
            }
            //boosted value is_new
            if ($this->isNewCheck) {
                $isNewData =  $this->getAttributeValue($sku['row_id'], (int) $this->isNewAttributeId);
                $boostValueIsNew = (strtolower((string) $this->isNewCheck) ==  'yes' && $isNewData == 1) ?  $this->isNewBoostValue  : $this->defaultValue;
                $newSearchOrder['is_new'] = $boostValueIsNew;
            }
            //boosted value is_on_promo
            if ($this->onPromoCheck) {
                $onPromoData =  $this->getAttributeValue($sku['row_id'], (int) $this->onPromoAttributeId);
                $boostValueonPromo = (strtolower((string) $this->onPromoCheck) ==  'yes' && $onPromoData == 1) ?  $this->onPromoCheckBoostValue  : $this->defaultValue;
                $newSearchOrder['promo'] = $boostValueonPromo;
            }
            //boosted value not_for_sale
            if ($this->notForSaleCheck) {
                $notForSaleData =  $this->getAttributeValue($sku['row_id'], (int) $this->notForSaleAttributeId);
                $boostValueNotForSale = (strtolower((string) $this->notForSaleCheck) ==  'yes' && $notForSaleData == 1) ?  $this->notForSaleCheckBoostValue  : $this->defaultValue;
                $newSearchOrder['notforsale'] = $boostValueNotForSale;
            }
            //boosted value is_limited_stock
            if ($this->limitedStockCheck) {
                $limitedStockData =  $this->getAttributeValue($sku['row_id'], (int) $this->limitedStockAttributeId);
                $boostValuelimitedStock = (strtolower((string) $this->limitedStockCheck) ==  'yes' && $limitedStockData == 1) ?  $this->limitedStockCheckBoostValue  : $this->defaultValue;
                $newSearchOrder['limitedstock'] = $boostValuelimitedStock;
            }
            //boosted value is_hot_price
            if ($this->isHotPriceCheck) {
                $isHotPriceData =  $this->getAttributeValue($sku['row_id'], (int) $this->isHotPriceAttributeId);
                $boostValueIsHotPrice = (strtolower((string) $this->isHotPriceCheck) ==  'yes' && $isHotPriceData == 1) ?  $this->isHotPriceCheckBoostValue  : $this->defaultValue;
                $newSearchOrder['hotprice'] = $boostValueIsHotPrice;
            }
            //units sold
            $totalUnitSold =  ($unitSoldData == '' ||  $unitSoldData == 0) ? $this->defaultValue : $unitSoldData;
            //final calc
            $searchBoostValues = array_product($newSearchOrder);
            $searchOrder = $totalUnitSold *  $searchBoostValues;
            $this->arrBoostData[$chunkNo][] = array(
                'row_id' => $sku['row_id'],
                'value' => $searchOrder,
                'attribute_id' => (int) $this->searchOrderAttributeId
            );
            if ($searchOrder > $this->highestBoostValue) {
                $this->highestBoostValue = $searchOrder;
                $this->highestRowId = $sku['row_id'];
            }
            //log
            $this->logger->info('End processing sku:' . $proSku);
        }
    }
    /**
     * @param $chunk
     * @return void
     */
    public function updateAttributeByChunk($chunk)
    {
        foreach ($chunk as $data) {
            $boostOrder = ($this->highestBoostValue - $data['value']) + 1;
            $this->insertAttribute($data['row_id'], ceil($boostOrder), $data['attribute_id']);
            $proSku = $this->getSkuByRowId($data['row_id']);
            $this->logger->info('updated sku in Database:' . $proSku);
        }
    }
}
