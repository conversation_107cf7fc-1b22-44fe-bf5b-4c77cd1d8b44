<?php

/**
 * @category    Totaltools
 * @package     Totaltools_CatalogSearchOrder
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\CatalogSearchOrder\Controller\Adminhtml\Catalogsearch;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Totaltools\CatalogSearchOrder\Helper\Data;
use Magento\Framework\App\ResourceConnection;
use Totaltools\CatalogSearchOrder\Model\Catalog\BoostSearch;

/**
 * Class search
 * @package Totaltools\CatalogSearchOrder\Controller\Adminhtml\Catalogsearch
 */
class Index extends \Magento\Backend\App\Action
{
    /**
     * Authorization level of a basic admin session.
     *
     * @see _isAllowed()
     */
    const ADMIN_RESOURCE = 'Totaltools_CatalogSearchOrder::Catalogsearch';

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var Data
     */
    private $helper;
    /**
     * @var ResourceConnection
     */
    private $resource;
    /**
     * @var AdapterInterface
     */
    private $connection;
    /**
     * @var BoostSearch
     */
    private $boostSearchModel;
    /**
     * Generate constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param ScopeConfigInterface $scopeConfig
     * @param Totaltools\CatalogSearchOrder\Helper\Data $helper
     */
    public function __construct(
        \Magento\Backend\App\Action\Context $context,
        ScopeConfigInterface $scopeConfig,
        Data $helper,
        ResourceConnection $resourceConnection,
        BoostSearch $boostSearchModel
    ) {
        parent::__construct($context);
        $this->scopeConfig = $scopeConfig;
        $this->helper = $helper;
        $this->boostSearchModel =  $boostSearchModel;
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
    }

    /**
     * @return \Magento\Framework\App\ResponseInterface|\Magento\Framework\Controller\ResultInterface|void|\Zend_Db_Statement_Interface
     */
    public function execute()
    {
        try {
            //process fomula and set values
            $this->boostSearchModel->processFormula();
            //set requried attribute Ids
            $this->boostSearchModel->setAttributeIds();
            //process sku
            $this->boostSearchModel->processDryRun();
            //update best selling attribute
            $this->boostSearchModel->updateBestSellingAttribute();
            //send email notification
            $this->helper->sendEmail('Success');
            $this->messageManager->addSuccessMessage('Catalog Search Order has been Boosted.');
        } catch (\Exception $e) {
            //send failour email
            $this->helper->sendEmail('Fail');
            $this->messageManager->addErrorMessage($e->getMessage());
            $this->connection->rollBack();
        }
        $redirectPage = $this->resultRedirectFactory->create();
        $referUrl = $this->_redirect->getRefererUrl();
        return $redirectPage->setUrl($referUrl);
    }

    protected function _isAllowed()
    {
        return true;
    }

}