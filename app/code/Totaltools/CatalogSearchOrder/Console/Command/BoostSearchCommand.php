<?php

/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\CatalogSearchOrder\Console\Command;

use \Symfony\Component\Console\Command\Command;
use \Symfony\Component\Console\Input\InputInterface;
use \Symfony\Component\Console\Output\OutputInterface;
use Totaltools\CatalogSearchOrder\Model\Catalog\BoostSearch as CatalogBoostSearch;
use Totaltools\CatalogSearchOrder\Helper\Data;
use Magento\Framework\App\{State, Area};
use Magento\Framework\App\ResourceConnection;

class BoostSearchCommand extends Command
{
    const COMMAND_NAME = 'totaltools:boost-catalog-search-order';
    /**
     * @var State
     */
    protected $state;
    /**
     * @var Data
     */
    private $helper;
    /**
     * @var CatalogBoostSearch
     */
    private $boostSearchModel;
    /**
     * @var ResourceConnection
     */
    private $resource;
    /**
     * @var AdapterInterface
     */
    private $connection;

    public function __construct(
        State $state,
        Data $helper,
        CatalogBoostSearch $boostSearchModel,
        ResourceConnection $resourceConnection,
    ) {
        parent::__construct(self::COMMAND_NAME);
        $this->state = $state;
        $this->helper = $helper;
        $this->boostSearchModel = $boostSearchModel;
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();

    }

    protected function configure()
    {
        $this->setName('totaltools:boost-catalog-search-order')
             ->setDescription('Boost catalog product search order!');
        parent::configure();
    }
    protected function execute(InputInterface $input, OutputInterface $output):int
    {
        $this->state->setAreaCode(\Magento\Framework\App\Area::AREA_FRONTEND);
        
        //$this->catalogSearch->execute();
        try {
            $output->writeln('Catalog Search Order Boost Started');
            //process fomula and set values
            $this->boostSearchModel->processFormula();
            //set requried attribute Ids
            $this->boostSearchModel->setAttributeIds();
            //process sku
            $this->boostSearchModel->processDryRun();
            //update best selling attribute
            $this->boostSearchModel->updateBestSellingAttribute();
            //send email notification
            $this->helper->sendEmail('Success');
            $output->writeln('Catalog Search Order has been Boosted!');
        } catch (\Exception $e) {
            //send failour email
            $this->helper->sendEmail('Fail');
            $this->connection->rollBack();
            return \Magento\Framework\Console\Cli::RETURN_FAILURE;
        }
        $output->writeln('Successfully Finished!');
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }
}