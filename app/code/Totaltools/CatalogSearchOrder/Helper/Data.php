<?php
/**
 * @category    Totaltools
 * @package     Totaltools_CatalogSearchOrder
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\CatalogSearchOrder\Helper;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Mail\TransportInterfaceFactory;
use Magento\Framework\Mail\Message;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @return string
     */
    const XML_CONFIG_PATH = 'catalogsearchorder/general_settings/';
    const EB_PRODUCT = 'eb_product';
    const IS_BACKORDERABLE = 'is_backorderable';
    const IS_ON_REDUMPTION =  'is_on_redemption';
    const BRAND =  'brand';
    const UNITS_SOLD = 'units_sold';
    const SEARCH_ORDER = 'search_order';
    const PRODUCT_STATUS = 'status';
    const IS_NEW = 'is_new';
    const IS_ON_PROMO = 'is_on_promo';
    const NOT_FOR_SALE = 'not_for_sale';
    const IS_LIMITED_STOCK = 'is_limited_stock';
    const IS_HOT_PRICE = 'is_hot_price';

    const EMAIL_SUBJECT ='Catalog Search Order Boost';
    const EMAIL_BODY ='Catalog Search Order has been Boosted';
    const EMAIL_SUBJECT_FAILD ='Catalog Search Order Boost Faild';
    const EMAIL_BODY_FAILD ='Catalog Search Order Failed to Boosted';

    // $this->getAttributeId(CatalogProductEntityVarchar::IS_SPECIAL_ORDER)
    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var AdapterInterface
     */
    private $connection;
    protected $scopeConfig;
    protected $mailTransportFactory;

    public function __construct(
        ResourceConnection $resourceConnection,
        ScopeConfigInterface $scopeConfig,
        Context $context,
        TransportInterfaceFactory $mailTransportFactory
    ) {
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
        $this->scopeConfig = $scopeConfig;
        $this->mailTransportFactory = $mailTransportFactory;
        parent::__construct($context);
    }

    /**
     * @return string
     */
    public function getConfig($value)
    {
        return $this->scopeConfig->getValue(
            self::XML_CONFIG_PATH.$value,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
        );
    }
    /**
     * @return array
     */
    public function processFormula($formula)
    {
        //process first part
        $firstPart = explode(",", $formula);
        return $firstPart;
    }
    /**
     * @return exception
     */
    public function sendEmail($status)
    {
        $notificationEmail = $this->getConfig('boostnotification');
        $senderEmail = $this->scopeConfig->getValue('trans_email/ident_support/email',ScopeConfigInterface::SCOPE_TYPE_DEFAULT);

        if($notificationEmail != '' || $notificationEmail != null){
            $arrEmail = explode(',',$notificationEmail);
            foreach($arrEmail as $email){
            $message = new Message();
            $message->setFrom($senderEmail);
            $message->addTo($email);
            $message->setSubject(self::EMAIL_SUBJECT);
            $message->setBody(self::EMAIL_BODY);

            if(($status == 'Fail')){
                $message->setSubject(self::EMAIL_SUBJECT_FAILD);
                $message->setBody(self::EMAIL_BODY_FAILD);
            }

            $transport = $this->mailTransportFactory->create(['message' => $message]);
            try {
                $transport->sendMessage();
            } catch (\Exception $e) {
                echo $e->getMessage();
            }
        }
        }else{
            //dont do anything
        }

    }
}
