<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label" sortOrder="0">
            <label>Total Tools</label>
        </tab>
        <section id="catalogsearchorder" translate="label" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Catalog Search Order</label>
            <tab>totaltools</tab>
            <resource>Totaltools_CatalogSearchOrder::catalogsearch</resource>
            <group id="general_settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>General</label>
                <field id="boosting_rules" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Boosting Rules</label>
                </field>
                <field id="boostnotification" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Notification Email Address</label>
                    <comment>(email address comma seperated)</comment>
                </field>
                <field id="boosts_button" translate="button_label comment" type="button" sortOrder="30" showInDefault="1" showInWebsite="0" showInStore="0">
                    <button_label>Boost Search Order</button_label>
                    <button_url>
                        <![CDATA[catalogsearchorder/catalogsearch]]>
                    </button_url>
                    <frontend_model>Totaltools\CatalogSearchOrder\Block\System\Button\Catalogsearch</frontend_model>
                </field>
            </group>
            </section>
        </system>
    </config>