<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model\Entity;

class CatalogProductEntityTierPrice
{
    protected $rowId;
    protected $allGroups;
    protected $customerGroupId;
    protected $qty;
    protected $value;
    protected $percentageValue;
    protected $websiteId;
    protected $priceType;

    /**
     * @param int $rowId
     */
    public function setRowId(int $rowId)
    {
        $this->rowId = $rowId;
    }

    /**
     * @return int
     */
    public function getRowId() : int
    {
        return $this->rowId;
    }

    /**
     * @param int $allGroups
     */
    public function setAllGroups(int $allGroups)
    {
        $this->allGroups = $allGroups;
    }

    /**
     * @return int
     */
    public function getAllGroups() : int
    {
        return $this->allGroups;
    }

    /**
     * @param int $customerGroupId
     */
    public function setCustomerGroupId(int $customerGroupId)
    {
        $this->customerGroupId = $customerGroupId;
    }

    /**
     * @return int
     */
    public function getCustomerGroupId() : int
    {
        return $this->customerGroupId;
    }

    /**
     * @param int $qty
     */
    public function setQty(int $qty)
    {
        $this->qty = $qty;
    }

    /**
     * @return int
     */
    public function getQty() : int
    {
        return $this->qty;
    }

    /**
     * @param float $price
     */
    public function setValue(float $price)
    {
        $this->value = $price;
    }

    /**
     * @return float
     */
    public function getValue() : float
    {
        return (float) $this->value;
    }

    /**
     * @param int $websiteId
     */
    public function setWebsiteId(int $websiteId)
    {
        $this->websiteId = $websiteId;
    }

    /**
     * @return int
     */
    public function getWebsiteId() : int
    {
        return $this->websiteId;
    }

    /**
     * @param string $priceType
     */
    public function setPriceType(string $priceType)
    {
        $this->priceType = $priceType;
    }

    /**
     * @return string
     */
    public function getPriceType() : string
    {
        return $this->priceType;
    }
}
