<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model;

use Exception;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Totaltools\PriceImport\Model\Entity\CatalogProductEntityTierPrice;
use Totaltools\PriceImport\Helper\Data;

class TierPriceImporter extends ImporterAbstract
{
    const PRICE_TYPE = "fixed_total";
    const ALL_GROUPS = 0;
    const CUSTOMER_GROUP_ID = 1;
    const GUEST_GROUP_ID = 0;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var AdapterInterface
     */
    private $connection;

    /**
     * @var array
     */
    protected $websiteList;

    /**
     * @var string
     */
    protected $catalogProductEntityTierPrice;

    /**
     * @var string
     */
    protected $catalogProductEntity;

    /**
     * @var string
     */
    private $catalogProductEntityTierPriceTbl;

    /**
     * @var string
     */
    private $catalogProductEntityTbl;

    /**
     * @var WebsiteId
     */
    private WebsiteId $websiteId;

    /**
     * TierPriceImporter constructor.
     * @param ResourceConnection $resourceConnection
     * @param CatalogProductEntityTierPrice $catalogProductEntityTierPrice
     * @param Data $data
     */
    public function __construct(
        ResourceConnection $resourceConnection,
        CatalogProductEntityTierPrice $catalogProductEntityTierPrice,
        Data $data,
        WebsiteId $websiteId
    ) {
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
        $this->catalogProductEntityTierPrice = $catalogProductEntityTierPrice;

        // get catalog product entity tier price table name
        $this->catalogProductEntityTierPriceTbl = $this->connection->getTableName("catalog_product_entity_tier_price");
        $this->catalogProductEntityTbl = $this->connection->getTableName("catalog_product_entity");

        $this->setSummary('totalTierPrices', 0);
        $this->setSummary('totalTierUpdatePrices', 0);

        parent::__construct($data);
        $this->websiteId = $websiteId;
    }

    /**
     * @param array $data
     * @param array $config
     * @return bool
     */
    public function execute($data, $config) : bool
    {
        $this->config = $config;

        try {
            $this->connection->beginTransaction();
            // remove all tier prices
            $this->deleteAllTierPrices();
            $this->setSummary('totalTierPrices', count($data));

            // process base prices
            if (!empty($data)) {
                foreach ($data as $productTierPrice) {

                    // skip if we don't have price and qty value
                    if ($productTierPrice[3] == "" || $productTierPrice[4] == "") {
                        continue;
                    }

                    // TODO: load all sku's at once and code just get rowId
                    $rowId = $this->getCatalogProductRowId($productTierPrice[0]);

                    if (!empty($rowId)) {
                        $this->catalogProductEntityTierPrice->setRowId($rowId);
                        $this->catalogProductEntityTierPrice->setAllGroups(self::ALL_GROUPS);

                        $this->catalogProductEntityTierPrice->setCustomerGroupId(self::CUSTOMER_GROUP_ID);
                        if (is_numeric($productTierPrice[2])) {
                            $this->catalogProductEntityTierPrice->setCustomerGroupId($productTierPrice[2]);
                        }

                        $this->catalogProductEntityTierPrice->setQty($productTierPrice[3]);
                        $this->catalogProductEntityTierPrice->setValue((float)$productTierPrice[4]);
                        $this->catalogProductEntityTierPrice->setWebsiteId($productTierPrice[1] ? $this->websiteId->getWebsiteId($productTierPrice[1]) : 0);
                        $this->catalogProductEntityTierPrice->setPriceType(self::PRICE_TYPE);

                        // insert catalog product tier prices for loggedIn customer
                        $this->insertPrices();

                        if(!is_numeric($productTierPrice[2])) {
                            $this->catalogProductEntityTierPrice->setCustomerGroupId(self::GUEST_GROUP_ID);

                            // insert catalog product tier prices for guest users
                            $this->insertPrices();
                        }


                        $this->incrementSummary('totalTierUpdatePrices');
                    }
                }
            }

            // save all changes.
            $this->connection->commit();
            $this->sendNotification('Import succeeded', true);
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->sendNotification($e->getMessage(), false);
            return false;
        }

        return true;
    }

    /**
     * @param array $data
     * @param array $config
     * @return bool
     */
    public function executeDelta($data, $config) : bool
    {
        $this->config = $config;

        try {
            $this->connection->beginTransaction();
            $this->setSummary('totalTierPrices', count($data));

            // process base prices
            if (!empty($data)) {
                $rowIdList = [];
                foreach ($data as $productTierPrice) {
                    // skip if we don't have price and qty value
                    if ($productTierPrice[3] == "" || $productTierPrice[4] == "") {
                        continue;
                    }
                    // TODO: load all sku's at once and code just get rowId
                    $rowId = $this->getCatalogProductRowId($productTierPrice[0]);

                    if (!empty($rowId)) {
                        $this->catalogProductEntityTierPrice->setRowId($rowId);
                        $this->catalogProductEntityTierPrice->setAllGroups(self::ALL_GROUPS);

                        $this->catalogProductEntityTierPrice->setCustomerGroupId(self::CUSTOMER_GROUP_ID);
                        if (is_numeric($productTierPrice[2])) {
                            $this->catalogProductEntityTierPrice->setCustomerGroupId($productTierPrice[2]);
                        }

                        $this->catalogProductEntityTierPrice->setQty($productTierPrice[3]);
                        $this->catalogProductEntityTierPrice->setValue((float)$productTierPrice[4]);
                        $this->catalogProductEntityTierPrice->setWebsiteId($productTierPrice[1] ? $this->websiteId->getWebsiteId($productTierPrice[1]) : 0);
                        $this->catalogProductEntityTierPrice->setPriceType(self::PRICE_TYPE);

                        if (!in_array($rowId, $rowIdList)) {
                            array_push($rowIdList, $rowId);

                            // remove tier prices
                            $this->deleteDeltaTierPrices(
                                $rowId
                            );
                        }

                        // insert catalog product tier prices for logged in customer
                        $this->insertPrices();

                        if(!is_numeric($productTierPrice[2])) {
                            $this->catalogProductEntityTierPrice->setCustomerGroupId(self::GUEST_GROUP_ID);

                            // insert catalog product tier prices for guest
                            $this->insertPrices();
                        }

                        $this->incrementSummary('totalTierUpdatePrices');
                    }
                }
            }

            // save all changes.
            $this->connection->commit();
            $this->sendNotification('Import succeeded', true);
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->sendNotification($e->getMessage(), false);
            return false;
        }

        return true;
    }

    /**
     * @name: deleteAllTierPrices
     * @throws Exception
     * @return void
     */
    protected function deleteAllTierPrices()
    {
        $this->connection->delete(
            $this->catalogProductEntityTierPriceTbl
        );
    }

    /**
     * @param $rowId
     * @param $customerGroupId
     * @param $websiteId
     */
    protected function deleteDeltaTierPrices(
        $rowId
    ) {
        $this->connection->delete(
            $this->catalogProductEntityTierPriceTbl,
            [
                "row_id = ?" => $rowId
            ]
        );
    }

    /**
     * @param string $sku
     * @return int
     */
    protected function getCatalogProductRowId(string $sku)
    {
        $select = $this->connection->select()->from($this->catalogProductEntityTbl)->where('sku = :sku');
        $bind = [":sku" => (string)$sku];
        return $this->connection->fetchOne($select, $bind);
    }

    /**
     * @throws Exception
     * @return void
     */
    protected function insertPrices()
    {
        if (empty($this->catalogProductEntityTierPrice->getRowId())) {
            throw new Exception("Product missing.");
        }

        $bind = [
            "row_id" => $this->catalogProductEntityTierPrice->getRowId() ,
            "all_groups" => $this->catalogProductEntityTierPrice->getAllGroups(),
            "customer_group_id" => $this->catalogProductEntityTierPrice->getCustomerGroupId(),
            "qty" => $this->catalogProductEntityTierPrice->getQty(),
            "value" => $this->catalogProductEntityTierPrice->getValue(),
            "website_id" => $this->catalogProductEntityTierPrice->getWebsiteId(),
            "price_type" => $this->catalogProductEntityTierPrice->getPriceType()
        ];

        $this->connection->insertOnDuplicate($this->catalogProductEntityTierPriceTbl, $bind);
    }

    /**
     * @inheritdoc
     */
    protected function _prepareSummary()
    {
        return $this->getSummary();
    }
}
