<?php

namespace Totaltools\PriceImport\Model;

use Magento\Store\Model\StoreManagerInterface;
use Unbxd\ProductFeed\Helper\Data as HelperData;
use Unbxd\ProductFeed\Logger\LoggerInterface;
use Unbxd\ProductFeed\Logger\OptionsListConstants;
use Unbxd\ProductFeed\Model\CacheManager;
use Unbxd\ProductFeed\Model\CacheManagerFactory;
use Unbxd\ProductFeed\Model\Indexer\Product\Full\Action\Full as FullReindexAction;
use Unbxd\ProductFeed\Model\Indexer\Product\Full\Action\FullFactory as FullReindexActionFactory;
use Unbxd\ProductFeed\Model\Feed\Config as FeedConfig;
use Unbxd\ProductFeed\Model\Feed\Manager as FeedManager;
use Unbxd\ProductFeed\Model\Feed\ManagerFactory as FeedManagerFactory;

class UnbxdProductFeedIncrementalApi
{
    const MAX_PRODUCTS_UNBXD_FEED = 100;

    /**
     * @var FullReindexActionFactory
     */
    private $fullReindexActionFactory;

    /**
     * @var CacheManagerFactory
     */
    private $cacheManagerFactory;

    /**
     * @var null|CacheManager
     */
    private $cacheManager = null;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var HelperData
     */
    private $helperData;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var FeedManagerFactory
     */
    private $feedManagerFactory;

    /**
     * Flat to prevent duplicate cron jobs
     *
     * @var bool
     */
    private $lockProcess = false;


    /**
     * UnbxdProductFeedIncrementalApi constructor.
     * @param FullReindexActionFactory $fullReindexActionFactory
     * @param FeedManagerFactory $feedManagerFactory
     * @param CacheManagerFactory $cacheManagerFactory
     * @param LoggerInterface $logger
     * @param HelperData $helperData
     * @param StoreManagerInterface $storeManager
     */
    public function __construct(
        FullReindexActionFactory $fullReindexActionFactory,
        FeedManagerFactory $feedManagerFactory,
        CacheManagerFactory $cacheManagerFactory,
        LoggerInterface $logger,
        HelperData $helperData,
        StoreManagerInterface $storeManager
    ) {
        $this->fullReindexActionFactory = $fullReindexActionFactory;
        $this->feedManagerFactory = $feedManagerFactory;
        $this->cacheManagerFactory = $cacheManagerFactory;
        $this->logger = $logger->create(OptionsListConstants::LOGGER_TYPE_INDEXING);
        $this->helperData = $helperData;
        $this->storeManager = $storeManager;
    }

    /**
     * Check if related process is available for execution
     *
     * @return bool
     */
    private function isProcessAvailable($store = null)
    {
        // check authorization keys
        if (!$this->helperData->isAuthorizationCredentialsSetup($store)) {
            $this->logger->error('Please check authorization credentials to perform this operation.');
            return false;
        }

        // check if cron is configured
        if (!$this->helperData->isGeneralCronConfigured($store)) {
            $this->logger->error('General cron is not configured. Please configure it to perform this operation.');
            return false;
        }

        return true;
    }

    public function processIncrementalJobs($jobData)
    {
        if (empty($jobData)) {
            return;
        }

        $jobData = array_unique($jobData);

        $jobs = array_chunk($jobData, self::MAX_PRODUCTS_UNBXD_FEED);
        
        $this->flushCache();

        // prevent duplicate jobs
        if ($this->lockProcess) {
            $this->logger->info('Lock reindex by another process.');
            return false;
        }

        $stores = array_keys($this->storeManager->getStores());
        $defaultStore = $this->storeManager->getDefaultStoreView();
        $storeId = $defaultStore ? $defaultStore->getId() : reset($stores);

        //Check if api configured
        if (!$this->isProcessAvailable($storeId)) {
            return false;
        }

        $this->lockProcess = true;
        $this->logger->info('Run cron by schedule.');
        foreach ($jobs as $job) {
            $this->processIncrementalJobForStore($storeId, $job);
        }
        $this->lockProcess = false;
    }

    private function processIncrementalJobForStore($storeId, $jobData)
    {
        if (empty($jobData)) {
            $this->logger->info(sprintf('No incremental job for store with #%d', $storeId));
            return;
        }

        $isReindexSuccess = false;
        $error = false;
        $this->logger->info(sprintf('Start incremental reindex for store with #%d', $storeId))->startTimer();
        try {
            /** @var FullReindexAction $fullReindexAction */
            $fullReindexAction = $this->fullReindexActionFactory->create();
            $jobIndex = $fullReindexAction->rebuildProductStoreIndex($storeId, $jobData);
            $this->logger->info(sprintf('Start incremental reindex for store with #%d', $storeId))->logStats();
            $isReindexSuccess = true;
        } catch (\Exception $e) {
            $error = $e->getMessage();

            $this->logger->error(
                sprintf('Reindex failed for store with #%d. Error: %s. Trace: %s', $storeId, $error, $e->getTraceAsString())
            );
        }

        if (empty($jobIndex)) {
            $this->logger->error(sprintf('Can\'t execute feed for store #%d. Empty index.', $storeId));
            return;
        }

        // perform synchronization on reindex success with no empty index data
        if ($isReindexSuccess) {
            /** @var FeedManager $feedManager */
            $feedManager = $this->feedManagerFactory->create();
            $feedViewId = $feedManager->execute(
                $jobIndex,
                FeedConfig::FEED_TYPE_INCREMENTAL,
                $storeId,
                []
            );
            // set feed view ID, related to current reindex process
        }
    }

    /**
     * Clean cache.
     *
     * @return $this
     */
    private function flushCache()
    {
        try {
            $this->getCacheManager()->flushByTypes();
        } catch (\Exception $e) {
            $this->logger->error($e->getMessage());
        }

        return $this;
    }

    /**
     * Retrieve cache manager instance. Init if needed
     *
     * @return CacheManager|null
     */
    public function getCacheManager()
    {
        if (null === $this->cacheManager) {
            /** @var CacheManager */
            $this->cacheManager = $this->cacheManagerFactory->create();
        }

        return $this->cacheManager;
    }
}
