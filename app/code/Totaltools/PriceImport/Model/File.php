<?php
/**
 * @category  Totaltools
 * @package   Totaltools_
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model;

class File
{
    protected $fileName;

    protected $csvFilePath;

    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    private \Magento\Framework\Filesystem\Driver\File $fileDriver;

    public function __construct(
        \Magento\Framework\Filesystem\Driver\File $fileDriver
    ) {

        $this->fileDriver = $fileDriver;
    }

    /**
     * @return string
     */
    public function getFileName() : string
    {
        return $this->fileName ?? '';
    }

    /**
     * @param string $fileName
     * @return void
     */
    public function setFilename(string $fileName)
    {
        $this->fileName = $fileName;
    }

    /**
     * @return string
     */
    public function getCsvFile() : string
    {
        return $this->csvFilePath ?? '';
    }

    /**
     * @param string $filePath
     * @return void
     */
    public function setCsvFile(string $filePath) {
        $this->csvFilePath = $filePath;
    }

    /**
     * @return void
     * @throws \Magento\Framework\Exception\FileSystemException
     */
    public function deleteCsvFile() {
       if ($this->csvFilePath && $this->fileDriver->isExists($this->getCsvFile()))
       {
           $this->fileDriver->deleteFile($this->getCsvFile());
       }
    }
}
