<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model;

use Totaltools\PriceImport\Helper\Data;

abstract class ImporterAbstract
{
    /**
     * Helper class
     * @var Data
     */
    protected $helper;

    /**
     * Contains summary of the import records
     *
     * @var array
     */
    private $summary = [];

    /**
     * Contains job configuration passed from import model
     *
     * @var array
     */
    protected $config = [];

    /**
     * ImporterAbstract Constructor
     *
     * @param Data $helper
     */
    public function __construct(Data $helper)
    {
        $this->helper = $helper;
    }

    /**
     * Importer main execution method
     * @param $data
     * @param $config
     * @return void
     */
    abstract public function execute($data, $config);

    /**
     * Importer main execution method
     * @param $data
     * @param $config
     * @return void
     */
    abstract public function executeDelta($data, $config);

    /**
     * @return array
     */
    abstract protected function _prepareSummary();

    /**
     * @param string $message
     * @param false $success
     * @return void
     */
    protected function sendNotification($message = '', $success = false)
    {
        $trace = array_merge(
            $this->_prepareSummary(),
            ['Message' => $message]
        );

        $this->helper->sendEmailNotification(['trace' => $trace], $success, $this->config);
    }

    /**
     * @return array
     */
    protected function getSummary()
    {
        return $this->summary;
    }

    /**
     * @param $key
     * @param $value
     */
    protected function setSummary($key, $value)
    {
        $this->summary[$key] = $value;
    }

    /**
     * @param $key
     */
    protected function incrementSummary($key)
    {
        $this->summary[$key] ++;
    }
}
