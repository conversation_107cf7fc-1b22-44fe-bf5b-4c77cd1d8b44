<?php

namespace Totaltools\PriceImport\Model\Api;

use Psr\Log\LoggerInterface;
use Magento\MediaStorage\Model\File\UploaderFactory;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\Framework\Filesystem;
use Magento\Framework\App\Config\ScopeConfigInterface;

class Price
{
    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var UploaderFactory
     */
    protected $uploaderFactory;

    /**
     * @var Filesystem
     */
    protected $_filesystem;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * Upload Csv Directory config path
     */
    const XML_PATH_UPLOAD_CSV_DIR = 'catalog/price/price_csv_upload_path';

    /**
     * Allowed Extensions config path
     */
    const XML_PATH_ALLOWED_EXTENSIONS = 'catalog/price/price_csv_allowed_extensions';
  
    /**
     * Price constructor.
     *
     * @param LoggerInterface $logger
     * @param UploaderFactory $uploaderFactory
     * @param Filesystem $filesystem
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
      LoggerInterface $logger,
      UploaderFactory $uploaderFactory,
      Filesystem $filesystem,
      ScopeConfigInterface $scopeConfig
      )
    {
      $this->logger = $logger;
      $this->uploaderFactory = $uploaderFactory; 
      $this->_filesystem = $filesystem;
      $this->scopeConfig = $scopeConfig;
    }

    /**
     * @inheritdoc
     */
  
    public function uploadCsv()
    {
      $response = ['success' => false];
      $upload_path = $this->getUploadCsvDir();
      $mediaPath = $this->_filesystem->getDirectoryRead(DirectoryList::MEDIA)->getAbsolutePath();
    
      $uploader = $this->uploaderFactory->create(['fileId' => 'file']);
      $uploader->setFilesDispersion(false);
      $uploader->setFilenamesCaseSensitivity(false);
      $uploader->setAllowRenameFiles(false);
      $allowedExtensions = $this->getAllowedExtensions();
      $uploader->setAllowedExtensions($allowedExtensions);

      try { 
        $result = $uploader->save($mediaPath);
        $file_name = $uploader->getUploadedFileName();
        rename($mediaPath . $file_name, $upload_path . DIRECTORY_SEPARATOR . $file_name);
        $response = ['success' => true, 'message' => 'File uploaded successfully'];
      } catch (\Exception $e) {
            $response = ['success' => false, 'message' => $e->getMessage()];
            $this->logger->info($e->getMessage());
      }

      $returnArray = json_encode($response);
      return $returnArray;
    }

    /**
     * Get price csv upload directory path
     *
     * @return string
     */
    public function getUploadCsvDir() 
    {
      $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
      return $this->scopeConfig->getValue(self::XML_PATH_UPLOAD_CSV_DIR, $storeScope);
    }

    /**
     * Get Allowed Extensions for file upload
     *
     * @return array
     */
    public function getAllowedExtensions() 
    {
      $storeScope = \Magento\Store\Model\ScopeInterface::SCOPE_STORE;
      $allowedExtensions = $this->scopeConfig->getValue(self::XML_PATH_ALLOWED_EXTENSIONS, $storeScope);
      if($allowedExtensions)
      {
        return explode(",",$allowedExtensions);
      } else {
        return ['csv'];
      } 
    }
}