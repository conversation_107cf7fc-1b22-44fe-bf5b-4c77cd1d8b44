<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model\Eav;

use Exception;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;

class CatalogProductEntityVarchar
{
    /**
     * @var array
     */
    protected $data;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var AdapterInterface
     */
    private $connection;

    /**
     * @var array
     */
    protected $showSavedList;

    /**
     * @var array
     */
    protected $storeList;

    /**
     * @var int
     */
    protected $showSavedAttributeId;

    /**
     * @var string
     */
    protected $catalogProductEntity;

    /**
     * @var string
     */
    protected $catalogProductEntityVarchar;

    /**
     * @var array
     */
    protected $summary = [];

    /**
     * CatalogProductEntityVarchar constructor.
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        ResourceConnection $resourceConnection
    ) {
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
        $this->initialization();
    }

    protected function initialization()
    {
        // get catalog product entity table name
        $this->catalogProductEntity = $this->connection->getTableName("catalog_product_entity");

        // get catalog product entity varchar table name
        $this->catalogProductEntityVarchar = $this->connection->getTableName("catalog_product_entity_varchar");

        $this->summary['totalShowSavedRows'] = 0;
    }

    public function getCatalogProductEntityVarcharTable()
    {
        return $this->catalogProductEntityVarchar;
    }

    /**
     * @param int $showSavedAttributeId
     */
    public function setShowSavedAttributeId(int $showSavedAttributeId)
    {
        $this->showSavedAttributeId = $showSavedAttributeId;
    }

    /**
     * @param int $showSavedAttributeId
     * @return $this
     */
    public function withShowSavedAttributeId(int $showSavedAttributeId): CatalogProductEntityVarchar
    {
        $this->showSavedAttributeId = $showSavedAttributeId;
        return $this;
    }

    /**
     * @return int
     */
    public function getShowSavedAttributeId()
    {
        return $this->showSavedAttributeId;
    }

    /**
     * @param array $showSaveList
     */
    public function setShowSavedList(array $showSaveList)
    {
        $this->showSavedList = $showSaveList;
    }

    /**
     * @param array $showSaveList
     * @return $this
     */
    public function withShowSavedList(array $showSaveList): CatalogProductEntityVarchar
    {
        $this->showSavedList = $showSaveList;
        return $this;
    }

    /**
     * @return array
     */
    public function getShowSavedList()
    {
        return $this->showSavedList;
    }

    /**
     * @param array $storeList
     */
    public function setStoreList(array $storeList)
    {
        $this->storeList = $storeList;
    }

    /**
     * @return array
     */
    public function getStoreList()
    {
        return $this->storeList;
    }


    /**
     * @return bool
     */
    public function executeImporter(): bool
    {
        try {
            $this->connection->beginTransaction();
            if (empty($this->showSavedAttributeId)) {
                return false;
            }
            // remove all showSavedAttribute Record
            $this->deleteAttributeRecord($this->showSavedAttributeId);
            if (!empty($this->getShowSavedList())) {
                foreach ($this->getShowSavedList() as $product) {
                    $rowId = $this->getCatalogProductRowId($product[0]);
                    if (!empty($rowId)) {
                        $storeCode = $product[1];
                        if (empty($storeCode)) {
                            $storeId = 0;
                        } else {
                            $storeId = array_search($storeCode, $this->storeList);
                        }

                        // add show saved attribute data
                        $this->insertAttribute($rowId, strtoupper($product[4]), (int)$storeId, $this->showSavedAttributeId);
                        $this->summary['totalShowSavedRows'] ++;
                    }
                }
            }

            // save all changes.
            $this->connection->commit();
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->summary['error'] = $e->getMessage();
            return false;
        }

        return true;
    }


    /**
     * @return bool
     */
    public function executeDeltaImporter(): bool
    {
        try {
            $this->connection->beginTransaction();
            if (empty($this->showSavedAttributeId)) {
                return false;
            }

            if (!empty($this->getShowSavedList())) {
                foreach ($this->getShowSavedList() as $product) {
                    $rowId = $this->getCatalogProductRowId($product[0]);
                    if (!empty($rowId)) {
                        $storeCode = $product[1];
                        if (empty($storeCode)) {
                            $storeId = 0;
                        } else {
                            $storeId = array_search($storeCode, $this->storeList);
                        }

                        // delete default or store based save label
                        $this->deleteDeltaAttributeRecord($this->showSavedAttributeId, $rowId, $storeId);

                        // add show saved attribute data
                        $this->insertAttribute($rowId, strtoupper($product[4]), (int)$storeId, $this->showSavedAttributeId);
                        $this->summary['totalShowSavedRows'] ++;
                    }
                }
            }

            // save all changes.
            $this->connection->commit();
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->summary['error'] = $e->getMessage();
            return false;
        }

        return true;
    }

    /**
     * @param string $sku
     * @return string
     */
    protected function getCatalogProductRowId(string $sku)
    {
        $select = $this->connection->select()->from($this->catalogProductEntity)->where('sku = :sku');
        $bind = [":sku" => (string)$sku];
        return $this->connection->fetchOne($select, $bind);
    }

    /**
     * @param int $rowId
     * @param string $attributeValue
     * @param int $storeId
     * @param int $attributeId
     * @return int
     */
    protected function insertAttribute(int $rowId, string $attributeValue, int $storeId, int $attributeId)
    {
        $bind = [
            "attribute_id" => $attributeId,
            "store_id" => $storeId,
            "row_id" => $rowId,
            "value" => $attributeValue
        ];

        return $this->connection->insert($this->catalogProductEntityVarchar, $bind);
    }

    /**
     * @param int $attributeId
     */
    protected function deleteAttributeRecord(int $attributeId)
    {
        $this->connection->delete($this->catalogProductEntityVarchar, ["attribute_id = ?" => $attributeId]);
    }

    /**
     * @param int $attributeId
     * @param int $rowId
     * @param int $storeId
     */
    protected function deleteDeltaAttributeRecord(int $attributeId, int $rowId, int $storeId)
    {
        $this->connection->delete(
            $this->catalogProductEntityVarchar,
            ["row_id = ?" => $rowId, "attribute_id = ?" => $attributeId, "store_id = ?" => $storeId]
        );
    }

    /**
     * @return array
     */
    public function getSummary() : array
    {
        return $this->summary;
    }
}
