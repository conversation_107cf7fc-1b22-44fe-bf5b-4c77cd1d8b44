<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model\Eav;

use Exception;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Catalog\Model\Indexer\Product\Price\Processor as PriceIndexerProcessor;
use Totaltools\PriceImport\Model\UnbxdProductFeedIncrementalApi;

class CatalogProductEntityDecimal
{
    const DEFAULT_STORE_CODE = "default";

    const MIN_VALID_PRICE = 0.1;

    /**
     * @var array
     */
    protected $data;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var AdapterInterface
     */
    private $connection;

    /**
     * @var array
     */
    protected $basePriceList;

    /**
     * @var array
     */
    protected $statePriceList;

    /**
     * @var array
     */
    protected $specialPriceL<PERSON>;

    /**
     * @var array
     */
    protected $storeList;

    /**
     * @var int
     */
    protected $priceAttributeId;

    /**
     * @var int
     */
    protected $specialPriceAttributeId;

    /**
     * @var string
     */
    protected $catalogProductEntity;

    /**
     * @var string
     */
    protected $catalogProductEntityDecimal;

    /**
     * @var string
     */
    protected $catalogProductEntityDecimalTmp;

    /**
     * @var array
     */
    protected $summary = [];

    /**
     * @var CatalogProductEntityVarchar
     */
    private CatalogProductEntityVarchar $catalogProductEntityVarchar;

    /**
     * @var PriceIndexerProcessor
     */
    private PriceIndexerProcessor $priceIndexProcessor;

    /**
     * @var array
     */
    protected $priceIndexerDeltaProductIds = [];

    /**
     * @var UnbxdProductFeedIncrementalApi
     */
    private UnbxdProductFeedIncrementalApi $unbxdProductFeedIncrementalApi;

    /**
     * @var indexerRegistry
     */
    private $indexerRegistry;

    /**
     * @var productHelper
     */
    private $productHelper;

    /**
     * @param ResourceConnection $resourceConnection
     * @param CatalogProductEntityVarchar $catalogProductEntityVarchar
     * @param PriceIndexerProcessor $priceIndexProcessor
     * @param UnbxdProductFeedIncrementalApi $unbxdProductFeedIncrementalApi
     */
    public function __construct(
        ResourceConnection $resourceConnection,
        CatalogProductEntityVarchar $catalogProductEntityVarchar,
        PriceIndexerProcessor $priceIndexProcessor,
        UnbxdProductFeedIncrementalApi $unbxdProductFeedIncrementalApi
    ) {
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
        $this->initialization();
        $this->catalogProductEntityVarchar = $catalogProductEntityVarchar;
        $this->priceIndexProcessor = $priceIndexProcessor;
        $this->unbxdProductFeedIncrementalApi = $unbxdProductFeedIncrementalApi;
    }

    /**
     *
     */
    protected function initialization()
    {
        // get catalog product entity table name
        $this->catalogProductEntity = $this->connection->getTableName("catalog_product_entity");

        // get catalog product entity decimal table name
        $this->catalogProductEntityDecimal = $this->connection->getTableName("catalog_product_entity_decimal");

        //get catalog product entity decimal tmp table name
        $this->catalogProductEntityDecimalTmp = $this->connection->getTableName("catalog_product_entity_decimal_tmp");

        $this->summary['totalBasePrice'] = 0;
        $this->summary['totalBasePriceRows'] = 0;
        $this->summary['totalRowsNotFound'] = 0;
        $this->summary['totalStatePrice'] = 0;
        $this->summary['totalStatePriceRows'] = 0;
        $this->summary['totalSpecialPrice'] = 0;
        $this->summary['totalSpecialPriceRows'] = 0;
        $this->summary['totalZeroPriceRows'] = 0;
        $this->summary['totalZeroSpecialPriceRows'] = 0;
    }

    /**
     * @param int $priceAttributeId
     */
    public function setPriceAttributeId(int $priceAttributeId)
    {
        $this->priceAttributeId = $priceAttributeId;
    }

    /**
     * @return int
     */
    public function getPriceAttributeId()
    {
        return $this->priceAttributeId;
    }

    /**
     * @param int $specialPriceAttributeId
     */
    public function setSpecialPriceAttributeId(int $specialPriceAttributeId)
    {
        $this->specialPriceAttributeId = $specialPriceAttributeId;
    }

    /**
     * @return int
     */
    public function getSpecialPriceAttributeId()
    {
        return $this->specialPriceAttributeId;
    }

    /**
     * @param array $basePriceList
     */
    public function setBasePriceList(array $basePriceList)
    {
        $this->basePriceList = $basePriceList;
    }

    /**
     * @param array $basePriceList
     * @return CatalogProductEntityDecimal
     */
    public function withBasePriceList(array $basePriceList) : CatalogProductEntityDecimal
    {
        $this->basePriceList = $basePriceList;
        return $this;
    }

    /**
     * @return array
     */
    public function getBasePriceList() : array
    {
        return $this->basePriceList;
    }

    /**
     * @param array $statePriceList
     */
    public function setStatePriceList(array $statePriceList)
    {
        $this->statePriceList = $statePriceList;
    }

    /**
     * @param array $statePriceList
     * @return $this
     */
    public function withStatePriceList(array $statePriceList) : CatalogProductEntityDecimal
    {
        $this->statePriceList = $statePriceList;
        return $this;
    }

    /**
     * @return array
     */
    public function getStatePriceList() : array
    {
        return $this->statePriceList;
    }

    /**
     * @param array $specialPriceList
     */
    public function setSpecialPriceList(array $specialPriceList)
    {
        $this->specialPriceList = $specialPriceList;
    }

    /**
     * @param array $specialPriceList
     * @return $this
     */
    public function withSpecialPriceList(array $specialPriceList) : CatalogProductEntityDecimal
    {
        $this->specialPriceList = $specialPriceList;
        return $this;
    }

    public function getSpecialPriceList() : array
    {
        return $this->specialPriceList;
    }

    /**
     * @param array $storeList
     */
    public function setStoreList(array $storeList)
    {
        $this->storeList = $storeList;
    }

    /**
     * @return array
     */
    public function getStoreList()
    {
        return $this->storeList;
    }

    /**
     * @return bool
     */
    public function   executeImporter() : bool
    {
        try {
            $this->connection->beginTransaction();

            $basePriceList = $this->getBasePriceList();
            $bundleProducts = [];

            // remove state based prices
            $this->deleteStateBasedRecord($this->priceAttributeId);

            // remove all special prices
            $this->deleteSpecialPricesRecord($this->specialPriceAttributeId);

            // process base prices
            $this->summary['totalBasePrice'] = count($basePriceList);
            $this->summary['totalRowsNotFound'] = 0;
            $this->summary['totalBasePriceRows'] = 0;
            $this->summary['totalStatePriceRows'] = 0;
            $this->summary['totalSpecialPriceRows'] = 0;
            $this->summary['totalZeroPriceRows'] = 0;
            $this->summary['totalZeroSpecialPriceRows'] = 0;


            if (!empty($basePriceList)) {
                foreach ($basePriceList as $product) {
                    // TODO: load all sku's at once and code just get rowId
                    $row = $this->getCatalogProductRow($product[0]);
                    if (!empty($row)) {
                        $rowId = $row['row_id'];
                        $storeCode = $product[1];

                        // update base price
                        if (empty($storeCode) || strtolower($storeCode) == self::DEFAULT_STORE_CODE) {
                            $storeId = 0;
                            $price = $product[2];

                            if ($price < self::MIN_VALID_PRICE) {
                                $this->summary['totalZeroPriceRows'] ++;
                            } else {
                                $this->updatePrices($rowId, $price, $storeId);
                                $this->summary['totalBasePriceRows'] ++;
                            }
                        }
                    } else {
                        $this->summary['totalRowsNotFound'] ++;
                    }
                }
            }

            // process state based prices
            $this->summary['totalStatePrice'] = count($this->getStatePriceList());
            if (!empty($this->getStatePriceList())) {
                foreach ($this->getStatePriceList() as $product) {
                    // TODO: load all sku's at once and code just get rowId
                    $row = $this->getCatalogProductRow($product[0]);
                    if (!empty($row)) {
                        $rowId = $row['row_id'];
                        $storeCode = $product[1];
                        if (empty($storeCode)) {
                            $storeId = 0;
                        } else {
                            $storeId = array_search($storeCode, $this->storeList);
                        }

                        $price = $product[2];
                        if ($price < self::MIN_VALID_PRICE) {
                            $this->summary['totalZeroPriceRows'] ++;
                            continue;
                        }

                        // add state based prices.
                        $this->insertPrices($rowId, $price, (int)$storeId, $this->priceAttributeId);
                        $this->summary["totalStatePriceRows"] ++;
                    }
                }
            }

            $this->summary['totalSpecialPrice'] = count($this->getSpecialPriceList());
            if (!empty($this->getSpecialPriceList())) {
                foreach ($this->getSpecialPriceList() as $product) {
                    // TODO: load all sku's at once and code just get rowId
                    $row = $this->getCatalogProductRow($product[0]);
                    if (!empty($row)) {
                        $rowId = $row['row_id'];

                        $storeCode = $product[1];
                        if (empty($storeCode)) {
                            $storeId = 0;
                        } else {
                            $storeId = array_search($storeCode, $this->storeList);
                        }

                        $price = $product[3];
                        if ($price < self::MIN_VALID_PRICE) {
                            $this->summary['totalZeroSpecialPriceRows'] ++;
                            continue;
                        }

                        // get bundle product base price
                        if ($row['type_id'] == \Magento\Bundle\Model\Product\Type::TYPE_CODE ) {
                            $regularPrice = $product[2];
                            if ($regularPrice >= self::MIN_VALID_PRICE) {
                                $price = ($price / $regularPrice * 100);
                                if ($price < self::MIN_VALID_PRICE) {
                                    $this->summary['totalZeroSpecialPriceRows']++;
                                    continue;
                                }
                            } else {
                                $this->summary['totalZeroSpecialPriceRows']++;
                                continue;
                            }
                        }

                        // add special prices
                        $this->insertPrices($rowId, $price, (int)$storeId, $this->specialPriceAttributeId);
                        $this->summary["totalSpecialPriceRows"] ++;

                    }
                }
            }

            // save all changes.
            $this->connection->commit();
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->summary["totalSpecialPriceRows"] = $e->getMessage();
            return false;
        }

        return true;
    }

    /**
     * @param int $attributeId
     */
    protected function deleteStateBasedRecord(int $attributeId)
    {
        $this->connection->delete(
            $this->catalogProductEntityDecimal,
            ["attribute_id = ?" => $attributeId, "store_id > 0"]
        );
    }

    /**
     * @param int $attributeId
     */
    protected function deleteSpecialPricesRecord(int $attributeId)
    {
        $this->connection->delete($this->catalogProductEntityDecimal, ["attribute_id = ?" => $attributeId]);
    }

    /**
     * @param string $sku
     * @return string
     */
    protected function getCatalogProductRow(string $sku)
    {
        $select = $this->connection->select()->from($this->catalogProductEntity)->where('sku = :sku');
        $bind = [":sku" => (string)$sku];
        return $this->connection->fetchRow($select, $bind);
    }

    /**
     * @param int $rowId
     * @param float $price
     * @param int $storeId
     */
    protected function updatePrices(int $rowId, float $price, int $storeId)
    {
        $bind = ["value" => $price];
        $where = [
            "attribute_id = ?" => (int)$this->priceAttributeId,
            "store_id = ?" => $storeId,  "row_id = ?" => $rowId
        ];
        $this->connection->update($this->catalogProductEntityDecimal, $bind, $where);
    }

    /**
     * @param int $rowId
     * @param float $price
     * @param int $storeId
     * @param int $attributeId
     */
    protected function insertPrices(int $rowId, float $price, int $storeId, int $attributeId)
    {
        $bind = ["attribute_id" => $attributeId, "store_id" => $storeId,  "row_id" => $rowId, "value" => $price];
        $this->connection->insertOnDuplicate($this->catalogProductEntityDecimal, $bind);
    }

    /**
     * @return array
     */
    public function getSummary() : array
    {
        return $this->summary;
    }


    /**
     * @return bool
     */
    public function executeDeltaImporter() : bool
    {
        try {
            $this->connection->beginTransaction();

            // process base prices
            $this->summary['totalBasePrice'] = count($this->getBasePriceList());
            $this->summary['totalRowsNotFound'] = 0;
            $this->summary['totalBasePriceRows'] = 0;
            $this->summary['totalStatePriceRows'] = 0;
            $this->summary['totalSpecialPriceRows'] = 0;
            $this->summary['totalZeroPriceRows'] = 0;
            $this->summary['totalZeroSpecialPriceRows'] = 0;
            $bundleProducts = [];
            $productIds = [];
            $storeIds = [];

            if (!empty($this->getBasePriceList())) {
                $storeIds[0] = 0;
                foreach ($this->getBasePriceList() as $product) {
                    // TODO: load all sku's at once and code just get rowId
                    $row = $this->getCatalogProductRow($product[0]);
                    if (!empty($row)) {
                        $rowId = $row['row_id'];
                        $this->priceIndexerDeltaProductIds[] = $row['entity_id'];

                        $storeCode = $product[1];
                        // update base price
                        if (empty($storeCode) || strtolower($storeCode) == self::DEFAULT_STORE_CODE) {
                            $storeId = 0;

                            // remove special price attribute all values
                            $this->deleteDeltaAttributeRecords(
                                $this->catalogProductEntityDecimal,
                                $this->specialPriceAttributeId,
                                $rowId
                            );

                            // remove store based prices
                            $this->deleteProductStateBasedRecord(
                                $this->catalogProductEntityDecimal,
                                $this->priceAttributeId,
                                $rowId
                            );

                            // remove show saved all values
                            $this->deleteDeltaAttributeRecords(
                                $this->catalogProductEntityVarchar->getCatalogProductEntityVarcharTable(),
                                $this->catalogProductEntityVarchar->getShowSavedAttributeId(),
                                $rowId
                            );

                            $price = $product[2];
                            if ($price < self::MIN_VALID_PRICE) {
                                $this->summary['totalZeroPriceRows'] ++;
                                continue;
                            }

                            $productIds[] = $row['entity_id'];
                            $this->updatePrices($rowId, $price, $storeId);
                            $this->summary['totalBasePriceRows'] ++;

                        }
                    } else {
                        $this->summary['totalRowsNotFound'] ++;
                    }
                }
            }

            // process state based prices
            $this->summary['totalStatePrice'] = count($this->getStatePriceList());
            if (!empty($this->getStatePriceList())) {
                foreach ($this->getStatePriceList() as $product) {

                    // TODO: load all sku's at once and code just get rowId
                    $row = $this->getCatalogProductRow($product[0]);
                    if (!empty($row)) {
                        $rowId = $row['row_id'];
                        $storeCode = $product[1];
                        if (empty($storeCode)) {
                            $storeId = 0;
                        } else {
                            $storeId = array_search($storeCode, $this->storeList);
                        }

                        // remove special price attribute
                        $this->deleteDeltaAttributeRecord(
                            $this->catalogProductEntityDecimal,
                            $this->specialPriceAttributeId,
                            $rowId,
                            $storeId
                        );

                        // remove store based prices
                        $this->deleteDeltaAttributeRecord(
                            $this->catalogProductEntityDecimal,
                            $this->priceAttributeId,
                            $rowId,
                            $storeId
                        );

                        // remove show saved
                        $this->deleteDeltaAttributeRecord(
                            $this->catalogProductEntityVarchar->getCatalogProductEntityVarcharTable(),
                            $this->catalogProductEntityVarchar->getShowSavedAttributeId(),
                            $rowId,
                            $storeId
                        );

                        $storeIds[$storeId] = $storeId;

                        $price = $product[2];
                        if ($price < self::MIN_VALID_PRICE) {
                            $this->summary['totalZeroPriceRows'] ++;
                            continue;
                        }

                        $productIds[] = $row['entity_id'];
                        // insert store based price
                        $this->insertPrices($rowId, $price, (int)$storeId, $this->priceAttributeId);
                        $this->summary["totalStatePriceRows"] ++;
                    }
                }
            }

            $this->summary['totalSpecialPrice'] = count($this->getSpecialPriceList());
            if (!empty($this->getSpecialPriceList())) {
                foreach ($this->getSpecialPriceList() as $product) {
                    // TODO: load all sku's at once and code just get rowId
                    $row = $this->getCatalogProductRow($product[0]);
                    if (!empty($row)) {
                        $rowId = $row['row_id'];
                        $storeCode = $product[1];
                        if (empty($storeCode)) {
                            $storeId = 0;
                        } else {
                            $storeId = array_search($storeCode, $this->storeList);
                        }

                        $price = $product[3];
                        if ($price < self::MIN_VALID_PRICE) {
                            $this->summary['totalZeroSpecialPriceRows'] ++;
                            continue;
                        }

                        // get bundle product base price
                        if ($row['type_id'] == \Magento\Bundle\Model\Product\Type::TYPE_CODE ) {
                            $regularPrice = $product[2];
                            if ($regularPrice >= self::MIN_VALID_PRICE) {
                                $price = ($price / $regularPrice * 100);

                                if ($price < self::MIN_VALID_PRICE) {
                                    $this->summary['totalZeroSpecialPriceRows']++;
                                    continue;
                                }
                            }  else {
                                $this->summary['totalZeroSpecialPriceRows']++;
                                continue;
                            }
                        }

                        // add available default/store based special prices
                        $this->insertPrices($rowId, $price, (int)$storeId, $this->specialPriceAttributeId);
                        $this->summary["totalSpecialPriceRows"] ++;

                    }
                }
            }

            // save all changes.
            $this->connection->commit();
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->summary["totalSpecialPriceRows"] = $e->getMessage();
            return false;
        }

        // reindex delta products
        $this->reindexPrice($this->priceIndexerDeltaProductIds);
        $this->unbxdProductFeedIncrementalApi->processIncrementalJobs($productIds);
        return true;
    }

    /**
     * @param int $attributeId
     * @param int $rowId
     * @param int $storeId
     */
    protected function deleteDeltaStateBasedRecord(int $attributeId, int $rowId, int $storeId)
    {
        $this->connection->delete(
            $this->catalogProductEntityDecimal,
            ["row_id = ?" => $rowId, "attribute_id = ?" => $attributeId, "store_id = ?" => $storeId]
        );
    }

    /**
     * @param int $attributeId
     * @param int $rowId
     * @param int $storeId
     */
    protected function deleteDeltaAttributeRecord($table, int $attributeId, int $rowId, int $storeId)
    {
        $this->connection->delete(
            $table,
            ["row_id = ?" => $rowId, "attribute_id = ?" => $attributeId, "store_id = ?" => $storeId]);
    }

    /**
     * @param $table
     * @param int $attributeId
     * @param int $rowId
     * @return void
     */
    protected function deleteProductStateBasedRecord($table, int $attributeId, int $rowId)
    {
        $this->connection->delete(
            $table,
            ["row_id = ?" => $rowId, "attribute_id = ?" => $attributeId, "store_id > 0"]
        );
    }

    /**
     * @param int $attributeId
     * @param int $rowId
     */
    protected function deleteDeltaAttributeRecords($table, int $attributeId, int $rowId)
    {
        $this->connection->delete(
            $table,
            ["row_id = ?" => $rowId, "attribute_id = ?" => $attributeId]);
    }

    /**
     * reindex price
     *
     * @param array $productIds
     * @return void
     */
    public function reindexPrice(array $productIds)
    {
        return $this->priceIndexProcessor->reindexList($productIds);
    }
}
