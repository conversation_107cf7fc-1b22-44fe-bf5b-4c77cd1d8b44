<?php
/**
 * @category  Totaltools
 * @package   Totaltools_
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model;

use Exception;
use Psr\Log\LoggerInterface;
use Magento\Store\Api\WebsiteRepositoryInterface;

class WebsiteId
{

    const QD_CODE = 'qd';
    const QLD_CODE = 'qld';

/**
     * @var WebsiteRepositoryInterface
     */
    public $websiteRepository;

    /**
     * @var LoggerInterface
     */
    public $logger;

    public function __construct(
        LoggerInterface $logger,
        WebsiteRepositoryInterface $websiteRepository
    ) {
        $this->logger = $logger;
        $this->websiteRepository = $websiteRepository;
    }

    /**
     * get website id
     *
     * @param string $code
     * @return int|null
     */
    public function getWebsiteId(string $code): ?int
    {
        $websiteId = 0;
        try {
            $website = $this->websiteRepository->get($this->handleQldCode(strtolower($code)));
            $websiteId = (int)$website->getId();
        } catch (Exception $exception) {
            $this->logger->error($exception->getMessage());
        }
        return $websiteId;
    }

    /**
     * @param $code
     * @return mixed|string
     */
    public function handleQldCode($code) {
        if ($code == self::QD_CODE) {
            return self::QLD_CODE;
        }

        return $code;
    }
}
