<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model\Import\Behavior;

/**
 * Import behavior source model
 *
 * @api
 */
class Pricing extends \Magento\ImportExport\Model\Source\Import\AbstractBehavior
{
     /**
     * Import behaviors
     */
    const BEHAVIOR_RRP_SPECIAL_PRICING = 'rrp_special';
    const BEHAVIOR_TIER_PRICING = 'tier';
    const BEHAVIOR_DELTA_RRP_SPECIAL_PRICING = 'delta_rrp';
    const BEHAVIOR_DELTA_TIER_PRICING = 'delta_tier';

    /**
     * {@inheritdoc}
     */
    public function toArray()
    {
        return [
            self::BEHAVIOR_RRP_SPECIAL_PRICING => __('RRP/Special Price Import'),
            self::BEHAVIOR_DELTA_RRP_SPECIAL_PRICING => __('RRP/Special Delta Price Import'),
            self::BEHAVIOR_TIER_PRICING => __('Tier Price Import'),
            self::BEHAVIOR_DELTA_TIER_PRICING => __('Tier Delta Price Import')
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getCode()
    {
        return 'pricing';
    }
}
