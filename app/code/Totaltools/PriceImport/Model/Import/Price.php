<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model\Import;

use Magento\Framework\App\ObjectManager;
use Magento\Framework\Filesystem\Io\Sftp;
use Magento\Framework\Serialize\Serializer\Json;
use Totaltools\PriceImport\Helper\Data;
use Totaltools\PriceImport\Model\File;
use Totaltools\PriceImport\Model\Import\Price\RowValidatorInterface;
use Magento\ImportExport\Model\Import\ErrorProcessing\ProcessingErrorAggregatorInterface;
use Totaltools\PriceImport\Model\Import\Behavior\Pricing as Behavior;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\ImportExport\Model\Import;
use Magento\ScheduledImportExport\Model\Scheduled\Operation\Data as OperationData;
use Totaltools\ScheduledImportExport\Plugin\Model\Scheduled\Operation\DataPlugin;

class Price extends \Magento\ImportExport\Model\Import\Entity\AbstractEntity
{
    /**
     * Column product sku.
     */
    const COL_SKU = 'sku';

    /**
     * Column product store view code.
     */
    const COL_STORE = 'store_view_code';

    /**
     * Column product price.
     */
    const COL_PRICE = 'price';

    /**
     * Column product special price.
     */
    const COL_SPECIAL_PRICE = 'special_price';

    /**
     * Column show saved
     */
    const COL_SHOW_SAVED = 'show_saved';

    /**
     * Column product tier price
     */
    const COL_TIER_PRICE = 'tier_price';

    /**
     * Column product tier price quantity
     */
    const COL_TIER_PRICE_QTY = 'tier_price_qty';

    /**
     * Column product tier price website
     */
    const COL_TIER_PRICE_WEBSITE = 'tier_price_website';

    /**
     * Column product tier price customer group
     */
    const COL_TIER_PRICE_CUSTOMER_GROUP = 'tier_price_customer_group';

    /**
     * Column product tier price value type
     */
    const COL_TIER_PRICE_VALUE_TYPE = 'tier_price_value_type';

    /**
     * Entity type code for operation
     */
    const ENTITY_TYPE_CODE = 'pronto_pricing';

    /**
     * Processed directory name
     */
    const PROCESSED_DIR = 'processed/';

    const PROCESSING_DIR = 'processing/';

    /**
     * Validation failure message template definitions
     *
     * @var array
     */
    protected $_messageTemplates = [
        RowValidatorInterface::ERROR_EMPTY_SKU => 'SKU is empty',
        RowValidatorInterface::ERROR_INVALID_STORE => 'Invalid Store Id',
        RowValidatorInterface::ERROR_DUPLICATE_SKU => 'Duplicate SKU',
        RowValidatorInterface::ERROR_INVALID_PRICE_FORMAT => 'Invalid price format',
        RowValidatorInterface::ERROR_EMPTY_PRICE => 'Price is empty',
        RowValidatorInterface::ERROR_EMPTY_TIER_PRICE => 'Tier price is empty',
        RowValidatorInterface::ERROR_INVALID_TIER_PRICE => 'Invalid tier price format',
        RowValidatorInterface::ERROR_EMPTY_TIER_PRICE_QTY => 'Tier price quantity is empty'
    ];

    /**
     * @var array
     */
    protected $_permanentAttributes = [
        self::COL_SKU
    ];

    /**
     * If we should check column names
     *
     * @var bool
     */
    protected $needColumnCheck = true;

    /**
     * Valid column names
     *
     * @return array
     */
    protected $validColumnNames = [
        self::COL_STORE,
        self::COL_SKU,
        self::COL_PRICE,
        self::COL_SPECIAL_PRICE,
        self::COL_SHOW_SAVED,
        self::COL_TIER_PRICE,
        self::COL_TIER_PRICE_QTY,
        self::COL_TIER_PRICE_WEBSITE,
        self::COL_TIER_PRICE_CUSTOMER_GROUP,
        self::COL_TIER_PRICE_VALUE_TYPE
    ];

    /**
     * Need to log in import history
     *
     * @var bool
     */
    protected $logInHistory = true;

    /**
     * @var array
     */
    protected $_validators = [];

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    protected $_resource;

    /**
     * @var \Totaltools\PriceImport\Model\PriceImporter
     */
    protected $priceImporter;

    /**
     * @var \Totaltools\PriceImport\Model\TierPriceImporter
     */
    protected $tierPriceImporter;

    /**
     * Json Serializer Instance
     *
     * @var Json
     */
    private $serializer;

    /**
     * @var array
     */
    private $_data = [];

    /**
     * @var \Magento\Framework\Filesystem\Directory\WriteInterface
     */
    private $_rootDirectory;

    /**
     * @var Sftp
     */
    private $sftp;

    /**
     * @var Data
     */
    private $helper;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    protected $localeDate;
    private File $file;

    /**
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Magento\ImportExport\Helper\Data $importExportData
     * @param \Magento\ImportExport\Model\ResourceModel\Import\Data $importData
     * @param \Magento\Framework\App\ResourceConnection $resource
     * @param \Magento\ImportExport\Model\ResourceModel\Helper $resourceHelper
     * @param \Magento\Framework\Stdlib\StringUtils $string
     * @param ProcessingErrorAggregatorInterface $errorAggregator
     * @param \Totaltools\PriceImport\Model\PriceImporter $priceImporter
     * @param \Totaltools\PriceImport\Model\TierPriceImporter $tierPriceImporter
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $localeDate
     */
    public function __construct(
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\ImportExport\Helper\Data $importExportData,
        \Magento\ImportExport\Model\ResourceModel\Import\Data $importData,
        \Magento\Framework\App\ResourceConnection $resource,
        \Magento\ImportExport\Model\ResourceModel\Helper $resourceHelper,
        ProcessingErrorAggregatorInterface $errorAggregator,
        \Totaltools\PriceImport\Model\PriceImporter $priceImporter,
        \Totaltools\PriceImport\Model\TierPriceImporter $tierPriceImporter,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\Stdlib\DateTime\DateTime $localeDate,
        Sftp $sftp,
        Data $helper,
        File $file
    ) {
        $this->jsonHelper = $jsonHelper;
        $this->_importExportData = $importExportData;
        $this->_resourceHelper = $resourceHelper;
        $this->_dataSourceModel = $importData;
        $this->_resource = $resource;
        $this->_connection = $resource->getConnection(\Magento\Framework\App\ResourceConnection::DEFAULT_CONNECTION);
        $this->errorAggregator = $errorAggregator;
        $this->priceImporter = $priceImporter;
        $this->tierPriceImporter = $tierPriceImporter;
        $this->_rootDirectory = $filesystem->getDirectoryWrite(DirectoryList::ROOT);
        $this->localeDate = $localeDate;
        $this->sftp = $sftp;
        $this->helper = $helper;

        $this->_initErrorTemplates();
        $this->file = $file;
    }

    /**
     * Initialize Product error templates
     */
    protected function _initErrorTemplates()
    {
        foreach ($this->_messageTemplates as $errorCode => $template) {
            $this->addMessageTemplate($errorCode, $template);
        }
    }

    /**
     * @return array
     */
    public function getValidColumnNames()
    {
        return $this->validColumnNames;
    }

    /**
     * Row validation.
     *
     * @param array $rowData
     * @param int $rowNum
     * @return bool
     */
    public function validateRow(array $rowData, $rowNum)
    {
        $params = $this->getParameters();
        $behavior = $params['behavior'] ?? false;

        $sku = $rowData[self::COL_SKU] ?? '';

        if (!$sku) {
            $this->addRowError(RowValidatorInterface::ERROR_EMPTY_SKU, $rowNum);
            return false;
        }

        if ($behavior &&
            (
                Behavior::BEHAVIOR_RRP_SPECIAL_PRICING == $behavior
                || Behavior::BEHAVIOR_DELTA_RRP_SPECIAL_PRICING == $behavior
            )
        ) {
            $price = $rowData[self::COL_PRICE] ?? '';
            $specialPrice = $rowData[self::COL_SPECIAL_PRICE] ?? '';

            /*
             * removed 0 price check in validation
             * if (!$price) {
                $this->addRowError(RowValidatorInterface::ERROR_EMPTY_PRICE, $rowNum);
                return false;
            }*/

            if (!is_numeric($price)) {
                $this->addRowError(RowValidatorInterface::ERROR_INVALID_PRICE_FORMAT, $rowNum);
                return false;
            }

            if ($specialPrice && !is_numeric($specialPrice)) {
                $this->addRowError(RowValidatorInterface::ERROR_INVALID_PRICE_FORMAT, $rowNum);
                return false;
            }
        }

        if ($behavior &&
            (
                Behavior::BEHAVIOR_TIER_PRICING == $behavior ||
                Behavior::BEHAVIOR_DELTA_TIER_PRICING == $behavior
            )
        ) {
            $tierPrice = $rowData[self::COL_TIER_PRICE] ?? '';
            $tierPriceQty = $rowData[self::COL_TIER_PRICE_QTY] ?? '';

            if (!$tierPrice) {
                $this->addRowError(RowValidatorInterface::ERROR_EMPTY_TIER_PRICE, $rowNum);
                return false;
            }

            if (!is_numeric($tierPrice)) {
                $this->addRowError(RowValidatorInterface::ERROR_INVALID_PRICE_FORMAT, $rowNum);
                return false;
            }

            if (!$tierPriceQty) {
                $this->addRowError(RowValidatorInterface::ERROR_EMPTY_TIER_PRICE_QTY, $rowNum);
                return false;
            }
        }

        if (isset($this->_validatedRows[$rowNum])) {
            return !$this->getErrorAggregator()->isRowInvalid($rowNum);
        }

        $this->_validatedRows[$rowNum] = true;

        return !$this->getErrorAggregator()->isRowInvalid($rowNum);
    }

    /**
     * @inheritdoc
     */
    protected function _saveValidatedBunches()
    {
        $source = $this->_getSource();
        $currentDataSize = 0;
        $bunchRows = [];
        $startNewBunch = false;
        $nextRowBackup = [];
        $maxDataSize = $this->_resourceHelper->getMaxDataSize();
        $bunchSize = $this->_importExportData->getBunchSize();
        $skuSet = [];

        $source->rewind();

        while ($source->valid() || $bunchRows) {
            if ($startNewBunch || !$source->valid()) {
                $bunchRows = $nextRowBackup;
                $currentDataSize = strlen($this->getSerializer()->serialize($bunchRows));
                $startNewBunch = false;
                $nextRowBackup = [];
            }
            if ($source->valid()) {
                try {
                    $rowData = $source->current();
                    if (array_key_exists('sku', $rowData)) {
                        $skuSet[$rowData['sku']] = true;
                    }
                } catch (\InvalidArgumentException $e) {
                    $this->addRowError($e->getMessage(), $this->_processedRowsCount);
                    $this->_processedRowsCount++;
                    $source->next();
                    continue;
                }

                $this->_processedRowsCount++;

                if ($this->validateRow($rowData, $source->key())) {
                    // add row to bunch for save
                    $rowData = $this->_prepareRowForDb($rowData);
                    $rowSize = strlen($this->jsonHelper->jsonEncode($rowData));

                    $isBunchSizeExceeded = $bunchSize > 0 && count($bunchRows) >= $bunchSize;

                    if ($currentDataSize + $rowSize >= $maxDataSize || $isBunchSizeExceeded) {
                        $startNewBunch = true;
                        $nextRowBackup = [$source->key() => $rowData];
                    } else {
                        $bunchRows[$source->key()] = $rowData;
                        $currentDataSize += $rowSize;
                    }

                    array_push($this->_data, $rowData);
                }
                $source->next();
            }
        }
        $this->_processedEntitiesCount = (count($skuSet)) ?: $this->_processedRowsCount;

        if (!$this->getErrorAggregator()->getErrorsCount()) {
            $this->importSource($this->getData());
        }

        return $this;
    }

    /**
     * @inheritdoc
     */
    protected function _importData()
    {
        if (!$this->getErrorAggregator()->getErrorsCount()) {
            return true;
        }
        return false;
    }

    /**
     * Import data with relevant importer
     *
     * @param array $data
     * @return void
     */
    public function importSource(array $data)
    {
        $params = $this->getParameters();
        $behavior = $params['behavior'] ?? false;

        if ($behavior && $behavior == \Totaltools\PriceImport\Model\Import\Behavior\Pricing::BEHAVIOR_RRP_SPECIAL_PRICING) {
            $this->priceImporter->execute($data, $params);
        } elseif ($behavior && $behavior == \Totaltools\PriceImport\Model\Import\Behavior\Pricing::BEHAVIOR_TIER_PRICING) {
            $this->tierPriceImporter->execute($data, $params);
        } elseif ($behavior && $behavior == \Totaltools\PriceImport\Model\Import\Behavior\Pricing::BEHAVIOR_DELTA_RRP_SPECIAL_PRICING) {
            $this->priceImporter->executeDelta($data, $params);
        } elseif ($behavior && $behavior == \Totaltools\PriceImport\Model\Import\Behavior\Pricing::BEHAVIOR_DELTA_TIER_PRICING) {
            $this->tierPriceImporter->executeDelta($data, $params);
         }

        $this->_moveFile($params);
    }

    /**
     * @inheritdoc
     */
    public function getEntityTypeCode()
    {
        return self::ENTITY_TYPE_CODE;
    }

    /**
     * @inheritdoc
     */
    protected function _prepareRowForDb(array $rowData)
    {
        return array_values($rowData);
    }

    /**
     * @param array $params
     * @return void
     */
    public function _moveFile($params)
    {
        $fileName = $params['file_name'] ?? null;
        $fileDir = $params['file_path'] ?? null;

        if (isset($fileName) && strpos($fileName, "%s") !== false ) {
            $fileName = $this->helper->setSftpFileNameFormat($params);
        }

        if ($fileName && $fileDir && $params['server_type'] == OperationData::FILE_STORAGE) {
            $processedDir = $this->_rootDirectory->getRelativePath($fileDir . DIRECTORY_SEPARATOR . self::PROCESSED_DIR);
            $fileRelativePath = $this->_rootDirectory->getRelativePath($fileDir . DIRECTORY_SEPARATOR . $fileName);

            try {
                $copyName = $this->localeDate->date('Y-m-d-His') . '_' . $fileName;
                $copyFile = $processedDir . $copyName;

                $this->_rootDirectory->copyFile($fileRelativePath, $copyFile);
                $this->_rootDirectory->delete($fileRelativePath);
            } catch (\Magento\Framework\Exception\FileSystemException $e) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Source file copying failed'));
            }
        }

        if ($fileName && $fileDir && $params['server_type'] == DataPlugin::SFTP_STORAGE) {
            $processedDir = $fileDir . DIRECTORY_SEPARATOR . self::PROCESSED_DIR . str_replace(self::PROCESSING_DIR, '', $fileName);
            $fileRelativePath = $fileDir . DIRECTORY_SEPARATOR . $fileName;

            try {
                $this->file->deleteCsvFile();
                //open sftp connection to move file from processing to processed
                //in long files it automactilly closed
                $params['username'] = $params['user'];
                $this->sftp->open($params);
                $this->sftp->mv($fileRelativePath, $processedDir);
            } catch (\Magento\Framework\Exception\FileSystemException $e) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Source file copying failed'));
            }
        }
    }

    /**
     * Get Serializer instance
     *
     * Workaround. Only way to implement dependency and not to break inherited child classes
     *
     * @return Json
     */
    private function getSerializer()
    {
        if (null === $this->serializer) {
            $this->serializer = ObjectManager::getInstance()->get(Json::class);
        }
        return $this->serializer;
    }

    /**
     * @return array
     */
    public function getData()
    {
        return $this->_data;
    }
}
