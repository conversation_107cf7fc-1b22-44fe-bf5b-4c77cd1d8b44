<?php
/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model\Import\Price;

interface RowValidatorInterface extends \Magento\Framework\Validator\ValidatorInterface
{
    const ERROR_INVALID_STORE = 'invalidStore';

    const ERROR_EMPTY_SKU = 'emptySku';

    const ERROR_DUPLICATE_SKU = 'duplicateSku';

    const ERROR_EMPTY_PRICE = 'emptyPrice';

    const ERROR_INVALID_PRICE_FORMAT = 'invalidPrice';

    const ERROR_EMPTY_TIER_PRICE = 'emptyTierPrice';

    const ERROR_INVALID_TIER_PRICE = 'invalidTierPrice';

    const ERROR_EMPTY_TIER_PRICE_QTY = 'emptyTierPriceQty';

    /**
     * Initialize validator
     * 
     * @param \Totaltools\PriceImport\Model\Import\Price $context
     * @return $this
     */
    public function init($context);
}
