<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Model;

use Exception;
use Magento\Eav\Model\Entity\Attribute;
use Magento\Store\Model\StoreManager;
use Totaltools\PriceImport\Model\Eav\CatalogProductEntityDecimal;
use Totaltools\PriceImport\Model\Eav\CatalogProductEntityVarchar;
use Magento\Indexer\Model\IndexerFactory;
use Totaltools\PriceImport\Helper\Data;
use Unbxd\ProductFeed\Model\CronManager;
use Unbxd\ProductFeed\Model\CronManagerFactory;


class PriceImporter extends ImporterAbstract
{
    const PRODUCT_PRICE = "price";
    const PRODUCT_SPECIAL_PRICE = "special_price";
    const PRODUCT_SHOW_SAVED = "show_saved_label";
    const CATALOG_PRODUCT_PRICE_INDEXER = "catalog_product_price";
    const QUEENSLAND_STORE_CODE = "QLD";
    const PRONTO_QUEENSLAND_STORE_CODE = "QD";

    /**
     * @var CatalogProductEntityDecimal
     */
    protected $catalogProductEntityDecimal;

    /**
     * @var CatalogProductEntityVarchar
     */
    protected $catalogProductEntityVarchar;

    /**
     * @var Attribute
     */
    protected $eavAttribute;

    /**
     * @var StoreManager
     */
    protected $storeManager;

    /**
     * @var IndexerFactory
     */
    protected $indexerFactory;

    /**
     * @var CronManagerFactory
     */
    protected $cronManagerFactory;

    /**
     * PriceImporter constructor.
     * @param CatalogProductEntityDecimal $catalogProductEntityDecimal
     * @param CatalogProductEntityVarchar $catalogProductEntityVarchar
     * @param Attribute $eavAttribute
     * @param StoreManager $storeManager
     * @param IndexerFactory $indexerFactory
     * @param Data $helper
     */
    public function __construct(
        CatalogProductEntityDecimal $catalogProductEntityDecimal,
        CatalogProductEntityVarchar $catalogProductEntityVarchar,
        Attribute $eavAttribute,
        StoreManager $storeManager,
        IndexerFactory $indexerFactory,
        Data $helper,
        CronManagerFactory $cronManagerFactory
    ) {
        $this->catalogProductEntityDecimal = $catalogProductEntityDecimal;
        $this->catalogProductEntityVarchar = $catalogProductEntityVarchar;
        $this->eavAttribute = $eavAttribute;
        $this->storeManager = $storeManager;
        $this->indexerFactory = $indexerFactory;
        $this->helper = $helper;
        $this->cronManagerFactory = $cronManagerFactory;

        parent::__construct($helper);

        // initialized required data.
        $this->initialization();
    }

    public function initialization()
    {
        // get store id & code
        $storeList = $this->getStores();

        // set store List for catalog product entity decimal entity
        $this->catalogProductEntityDecimal->setStoreList($storeList);

        // set store List for catalog product entity varchar entity
        $this->catalogProductEntityVarchar->setStoreList($storeList);

        // set price attributeId
        $this->catalogProductEntityDecimal
            ->setPriceAttributeId($this->getAttributeId(self::PRODUCT_PRICE));

        // set special_price attributeId
        $this->catalogProductEntityDecimal
            ->setSpecialPriceAttributeId($this->getAttributeId(self::PRODUCT_SPECIAL_PRICE));

        // set show_saved attributeId
        $this->catalogProductEntityVarchar
            ->setShowSavedAttributeId($this->getAttributeId(self::PRODUCT_SHOW_SAVED));
    }

    /**
     * @param array $data
     * @param array $config
     * @return void
     */
    public function execute($data, $config)
    {
        $this->config = $config;

        try {
            $this->splitBaseAndStatePrices($data);
            unset($data);

            if ($this->catalogProductEntityDecimal->executeImporter()) {
                $this->sendNotification('Price imported successfully', true);

                if ($this->helper->isPriceIndexerEnabled()) {
                    $this->reindexCatalogProductPrice();
                }

                if ($this->catalogProductEntityVarchar->executeImporter()) {
                    $this->sendNotification('Attributes updated successfully', true);
                }

                $this->sendNotification('Import succeeded', true);

                // run unbxd cron
                /** @var CronManager $cronManager */
                $cronManager = $this->cronManagerFactory->create();
                $cronManager->generateFullFeedJobsForIndexingQueue();
                $this->sendNotification('Unbxd indexer queued succeeded', true);

            } else {
                $this->sendNotification('Import failed', false);
            }
        } catch (Exception $e) {
            $this->sendNotification($e->getMessage(), false);
        }
    }

    /**
     * @param array $data
     * @param array $config
     * @return void
     */
    public function executeDelta($data, $config)
    {
        $this->config = $config;

        try {
            $this->splitBaseAndStatePrices($data);
            unset($data);

            // execute delta and index delta products only
            if ($this->catalogProductEntityDecimal->executeDeltaImporter()) {
                $this->sendNotification('Delta Price imported successfully', true);
                if ($this->catalogProductEntityVarchar->executeDeltaImporter()) {
                    $this->sendNotification('Delta Attributes updated successfully', true);
                }
                $this->sendNotification('Delta Import succeeded', true);
            } else {
                $this->sendNotification('Deta Import failed', false);
            }
        } catch (Exception $e) {
            $this->sendNotification($e->getMessage(), false);
        }
    }

    /**
     * @param array $data
     */
    protected function splitBaseAndStatePrices(array $data)
    {
        $basePriceList = array_filter(
            $data,
            function ($product) {
                return empty($product[1]) === true;
            }
        );

        $this->catalogProductEntityDecimal->setBasePriceList($basePriceList);

        $specialPriceList = array_filter(
            $data,
            function ($product) {
                return empty($product[3]) === false;
            }
        );
        $this->catalogProductEntityDecimal->setSpecialPriceList($specialPriceList);

        $statePriceList = array_diff_key($data, $basePriceList);
        $this->catalogProductEntityDecimal->setStatePriceList($statePriceList);


        $showSavedList = array_filter(
            $data,
            function ($product) {
                return empty($product[4]) === false;
            }
        );
        $this->catalogProductEntityVarchar->setShowSavedList($showSavedList);
    }

    /**
     * @param string $productEav
     * @return int
     */
    public function getAttributeId(string $productEav)
    {
        return $this->eavAttribute->getIdByCode("catalog_product", $productEav);
    }

    /**
     * @return array
     */
    protected function getStores()
    {
        $storeList = [];
        $stores = $this->storeManager->getStores();
        foreach ($stores as $store) {
            $code = $store->getCode();
            if (strtoupper($code) == self::QUEENSLAND_STORE_CODE) {
                $code = self::PRONTO_QUEENSLAND_STORE_CODE;
            }

            $storeList[$store->getId()] = strtoupper($code);
        }
        return $storeList;
    }

    /**
     * @return void
     */
    protected function reindexCatalogProductPrice()
    {
        try {
            $indexer = $this->indexerFactory->create();
            $indexer->load(self::CATALOG_PRODUCT_PRICE_INDEXER);
            $indexer->reindexAll();
            $this->sendNotification('Price re-indexing process completed', true);
        } catch (Exception $e) {
            $this->sendNotification($e->getMessage(), false);
        }
    }

    /**
     * @inheritdoc
     */
    protected function _prepareSummary()
    {
        return array_merge(
            $this->catalogProductEntityDecimal->getSummary(),
            $this->catalogProductEntityVarchar->getSummary()
        );
    }
}
