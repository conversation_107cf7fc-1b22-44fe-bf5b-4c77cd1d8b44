<?xml version="1.0"?>
<!--
/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> Iqbal <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Totaltools\PriceImport\Api\PriceInterface" type="Totaltools\PriceImport\Model\Api\Price"/>
    <type name="Magento\ScheduledImportExport\Model\Scheduled\Operation">
        <plugin name="Totaltools_PriceImport::Scheduled_Operation"
            type="Totaltools\PriceImport\Plugin\Model\Scheduled\OperationPlugin" />
    </type>

    <type name="Magento\ImportExport\Model\Import">
        <plugin name="Totaltools_PriceImport::Import_Model"
            type="Totaltools\PriceImport\Plugin\Model\ImportPlugin" />
    </type>

    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="price-importer"
                      xsi:type="object">Totaltools\PriceImport\Console\Command\ProductPriceImporter</item>
                <item name="price-delta-importer"
                      xsi:type="object">Totaltools\PriceImport\Console\Command\ProductPriceDeltaImporter</item>
                <item name="tier-price-importer"
                      xsi:type="object">Totaltools\PriceImport\Console\Command\ProductTierPriceImporter</item>
                <item name="tier-delta-price-importer"
                      xsi:type="object">Totaltools\PriceImport\Console\Command\ProductTierPriceDeltaImporter</item>
            </argument>
        </arguments>
    </type>
</config>
