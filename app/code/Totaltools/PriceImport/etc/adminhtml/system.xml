<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="catalog">
            <group id="price">
                <field id="price_csv_upload_path" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Price CSV Upload Directory</label>
                    <comment>Path to the folder on the server where price csv to be uploaded.</comment>
                </field>
                <field id="price_csv_allowed_extensions" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Price Api Allowed Extensions</label>
                    <comment>Comma separated e.g csv,pdf.</comment>
                </field>
                <field id="price_indexer_enable" translate="label" type="select" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Price Indexer Enabled</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>
