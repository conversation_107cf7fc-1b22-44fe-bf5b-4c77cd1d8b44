<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Plugin\Model;

class ImportPlugin
{
    /**
     * @param \Magento\ImportExport\Model\Import $subject
     * @param \Closure $proceed
     * @return bool
     */
    public function aroundImportSource($subject, $proceed)
    {
        $entityType = $subject->getEntity();

        if ($entityType && \Totaltools\PriceImport\Model\Import\Price::ENTITY_TYPE_CODE == $entityType) {
            return true;
        }

        if ($entityType && $entityType == "label_attribute_import") {
            return true;
        }

        return $proceed();
    }
}
