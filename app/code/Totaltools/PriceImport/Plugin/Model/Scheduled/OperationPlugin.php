<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Plugin\Model\Scheduled;

use Totaltools\PriceImport\Model\File;
use Totaltools\PriceImport\Model\Import\Price;
use Totaltools\PriceImport\Model\Import\Behavior\Pricing as Behavior;

class OperationPlugin
{
    /**
     * Text file extension
     */
    const EXT_TXT = 'txt';

    /**
     * CSV file extension
     */
    const EXT_CSV = 'csv';

    /**
     * RRP/Special price header columns
     */
    const RRP_SPECIAL_PRICE_HEADER = [
        Price::COL_SKU,
        Price::COL_STORE,
        Price::COL_PRICE,
        Price::COL_SPECIAL_PRICE,
        Price::COL_SHOW_SAVED
    ];

    /**
     * Tier price header columns
     */
    const TIER_PRICE_HEADER = [
        Price::COL_SKU,
        Price::COL_TIER_PRICE_WEBSITE,
        Price::COL_TIER_PRICE_CUSTOMER_GROUP,
        Price::COL_TIER_PRICE_QTY,
        Price::COL_TIER_PRICE
    ];

    /**
     * @var string
     */
    private $_behavior;

    /**
     * @var \Magento\Framework\Filesystem
     */
    protected $filesystem;

    /**
     * @var \Magento\Framework\App\Filesystem\DirectoryList $directoryList
     */

    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    protected $fileDriver;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvProcessor;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @var File
     */
    private File $file;
    /**
     * @var directoryList
     */
    private $directoryList;

    /**
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Framework\App\Filesystem\DirectoryList $directoryList
     * @param \Magento\Framework\Filesystem\Driver\File $fileDriver
     * @param \Magento\Framework\File\Csv $csvProcessor
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList,
        \Magento\Framework\Filesystem\Driver\File $fileDriver,
        \Magento\Framework\File\Csv $csvProcessor,
        File $file,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->filesystem = $filesystem;
        $this->directoryList = $directoryList;
        $this->fileDriver = $fileDriver;
        $this->csvProcessor = $csvProcessor;
        $this->logger = $logger;
        $this->file = $file;
    }

    /**
     * @param \Magento\ScheduledImportExport\Model\Scheduled\Operation $subject
     * @param string $result
     * @param \Magento\ScheduledImportExport\Model\Scheduled\Operation\OperationInterface $operation
     * @return string
     */
    public function afterGetFileSource(
        $subject,
        $result,
        $operation
    ) {
        $entityType = $subject->getEntityType();

        if ($entityType && Price::ENTITY_TYPE_CODE == $entityType) {
            $this->_behavior = $subject->getBehavior();
            $fileInfo = pathinfo($result);

            if ($fileInfo && $fileInfo['extension'] === self::EXT_TXT) {
                $result = $this->convertTxtToCsv($result);
            }
        }

        /** set csv file path */
        $this->file->setCsvFile($result);

        return $result;
    }

    /**
     * @param string $source
     * @return string
     */
    protected function convertTxtToCsv($source)
    {
        if (!$this->fileDriver->isExists($source)) {
            return $source;
        }

        $csvFilePath = '';

        try {
            $fileContents = $this->fileDriver->fileGetContents($source);
            $rows = explode(PHP_EOL, trim($fileContents));
            $data = [];

            if (sizeof($rows)) {
                foreach ($rows as $key => $row) {
                    $line = explode('|', trim($row));
                    if (is_array($line) && !empty($line)) {
                        $data[] = $line;
                    }
                }

                $csvFilePath = $this->createCsv($source, $data);
            }
        } catch (\Exception $e) {
            $this->logger->error('[' . $source . '] ' . $e->getMessage());
        } finally {
            return $csvFilePath;
        }
    }

    /**
     * @param string $filePath
     * @param array $data
     * @return int|bool
     */
    protected function createCsv($filePath, $data)
    {
        $result = '';
        $fileInfo = pathinfo($filePath);
        $filePath = $fileInfo['dirname'] . $fileInfo['filename'] . '.' . self::EXT_CSV;
        array_unshift($data, $this->getHeaderCells());

        try {
            $this->csvProcessor
                ->setEnclosure('"')
                ->setDelimiter(',')
                ->saveData($filePath, $data);

            $result = $filePath;
        } catch (\Exception $e) {
            $this->logger->error('[' . $filePath . '] ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * @return array
     */
    protected function getHeaderCells()
    {
        switch ($this->_behavior) {
            case Behavior::BEHAVIOR_TIER_PRICING:
            case Behavior::BEHAVIOR_DELTA_TIER_PRICING:
                return self::TIER_PRICE_HEADER;
                break;
            case Behavior::BEHAVIOR_RRP_SPECIAL_PRICING:
            case Behavior::BEHAVIOR_DELTA_RRP_SPECIAL_PRICING:
                return self::RRP_SPECIAL_PRICE_HEADER;
                break;
            default:
                return self::RRP_SPECIAL_PRICE_HEADER;
        }
    }
}
