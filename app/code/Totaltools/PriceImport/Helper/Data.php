<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Helper;

use Magento\Framework\Filesystem\Io\Sftp;
use Magento\ScheduledImportExport\Model\Scheduled\Operation;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\DataObject;
use Magento\ScheduledImportExport\Model\Scheduled\OperationFactory;
use Psr\Log\LoggerInterface;
use Totaltools\PriceImport\Model\File;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{
    /** @var string processing file directory */
    const PROCESSING_DIR =  'processing';

    /**
     * Import success email template
     */
    const IMPORT_SUCCESS_TEMPLATE = 'totaltools_price_import_success';

    /**
     * Import failure email template
     */
    const IMPORT_FAILURE_TEMPLATE = 'magento_scheduledimportexport_import_failed';

    const XML_PRICE_INDEXER_ENABLE = 'catalog/price/price_indexer_enable';

    /**
     * @var StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var TransportBuilder
     */
    protected $transportBuilder;

    /**
     * @var OperationFactory
     */
    protected $operationFactory;

    /**
     * @var Sftp
     */
    protected $sftpAdapter;

    /**
     * @var LoggerInterface
     */
    private LoggerInterface $logger;

    /**
     * @var File
     */
    private File $file;

    /**
     * Data constructor.
     * @param Context $context
     * @param StoreManagerInterface $storeManager
     * @param ScopeConfigInterface $scopeConfig
     * @param TransportBuilder $transportBuilder
     * @param OperationFactory $operationFactory
     * @param Sftp $sftpAdapter
     */
    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        ScopeConfigInterface $scopeConfig,
        TransportBuilder $transportBuilder,
        OperationFactory $operationFactory,
        Sftp $sftpAdapter,
        LoggerInterface $logger,
        File $file
    ) {
        parent::__construct($context);
        $this->storeManager = $storeManager;
        $this->scopeConfig = $scopeConfig;
        $this->transportBuilder = $transportBuilder;
        $this->operationFactory = $operationFactory;
        $this->sftpAdapter = $sftpAdapter;
        $this->logger = $logger;
        $this->file = $file;
    }

    /**
     * Send email notification
     *
     * @param array $vars
     * @param DataObject|array $config
     * @param bool $success
     * @return void
     */
    public function sendEmailNotification(
        $vars = [],
        $success = false,
        $config = [],
        $emailSubject = 'Price Import'
    ) {
        $operationId = $config['scheduled_operation_id'] ?? null;
        $operation = $this->getOperationConfig($operationId);

        // don't send email if file not found on server
        $fileName = $this->file->getFileName();
        if (empty($fileName)) {
            return false;
        }

        if (!$config instanceof DataObject) {
            $config = new DataObject(
                array_merge(
                    ['email_receiver' => 'general', 'email_template' => self::IMPORT_FAILURE_TEMPLATE],
                    $config,
                    $operation->getData()
                )
            );
        }

        if ($operation->getData('id')) {
            $dt = new \DateTime('NOW');
            $vars['dateAndTime'] = $dt->format('Y-m-d H:i:s');
            $vars['operationName'] = $operation->getData('name');
            $fileName = $operation->getData('file_info')['file_name'];
            if (isset($fileName) && strpos($fileName, "%s") !== false ) {
                $vars['fileName'] = $this->setSftpFileNameFormat($operation->getData('file_info'));
            } else {
                $vars['fileName'] = $fileName;
            }

            $vars['entity'] = $operation->getData('entity_type');
        }

        if ($success) {
            $config->setEmailTemplate(self::IMPORT_SUCCESS_TEMPLATE);
        }

        $vars['subject'] = $success ? $emailSubject.' Success' : $emailSubject.' Failed';

        if ($vars['trace'] && is_array($vars['trace'])) {
            $vars['subject'] = $vars['trace']['Message'] ?? $vars['subject'];
            $formattedTrace = $this->getFormatedLogTrace($vars['trace']);
            $vars['trace'] = nl2br($formattedTrace);
        }

        $storeId = $this->storeManager->getStore()->getId();
        $copyTo = array_filter(explode(',', $config->getEmailCopy()));
        $copyMethod = $config->getEmailCopyMethod();

        $receiverEmail = $this->scopeConfig->getValue(
            Operation::CONFIG_PREFIX_EMAILS . $config->getEmailReceiver() . '/email',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
        $receiverName = $this->scopeConfig->getValue(
            Operation::CONFIG_PREFIX_EMAILS . $config->getEmailReceiver() . '/name',
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );

        // Set all required params and send emails
        $this->transportBuilder->setTemplateIdentifier(
            $config->getEmailTemplate()
        )->setTemplateOptions(
            [
                'area' => \Magento\Backend\App\Area\FrontNameResolver::AREA_CODE,
                'store' => \Magento\Store\Model\Store::DEFAULT_STORE_ID,
            ]
        )->setTemplateVars(
            $vars
        )->setFrom(
            $config->getEmailSender()
        )->addTo(
            $receiverEmail,
            $receiverName
        );
        if ($copyTo && $copyMethod == 'bcc') {
            // Add bcc to customer email
            foreach ($copyTo as $email) {
                $this->transportBuilder->addBcc($email);
            }
        }
        /** @var \Magento\Framework\Mail\TransportInterface $transport */
        $transport = $this->transportBuilder->getTransport();
        $transport->sendMessage();

        // Email copies are sent as separated emails if their copy method is 'copy'
        if ($copyTo && $copyMethod == 'copy') {
            foreach ($copyTo as $email) {
                $this->transportBuilder->setTemplateIdentifier(
                    $config->getEmailTemplate()
                )->setTemplateOptions(
                    [
                        'area' => \Magento\Backend\App\Area\FrontNameResolver::AREA_CODE,
                        'store' => \Magento\Store\Model\Store::DEFAULT_STORE_ID,
                    ]
                )->setTemplateVars(
                    $vars
                )->setFrom(
                    $config->getEmailSender()
                )->addTo(
                    $email
                )->getTransport()->sendMessage();
            }
        }
    }

    /**
     * Return human readable debug trace.
     *
     * @param array $logTrace
     * @return string
     */
    public function getFormatedLogTrace($logTrace)
    {
        $trace = '';
        foreach ($logTrace as $key => $info) {
            if ($info !== '') {
                $trace .= $key . ': ' . $info . "\n";
            }
        }
        return $trace;
    }

    /**
     * @param int $operationId
     * @return DataObject
     */
    protected function getOperationConfig($operationId)
    {
        if ($operationId == null) {
            return new DataObject();
        }

        return $this->operationFactory->create()->load($operationId);
    }

    /**
     * @param $fileInfo
     * @return string
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function setSftpFileNameFormat($fileInfo)
    {
        if (!empty($calculatedFileName = $this->file->getFileName())) {
            return $calculatedFileName;
        }

        $fileName = $fileInfo['file_name'];
        $extension = pathinfo($fileInfo['file_name'], PATHINFO_EXTENSION);

        try {
            $dt = new \DateTime('NOW');
            $dt->setTimezone(new \DateTimeZone('Australia/Melbourne'));
            $fileName = sprintf(
                $fileName,
                $dt->format('dmY')
            );

            $fileGrep = rtrim($fileName, "." . $extension);
            $fileInfo['username'] = $fileInfo['user'];
            $this->sftpAdapter->open($fileInfo);
            $this->sftpAdapter->cd($fileInfo['file_path']);
            $listAllFiles = $this->sftpAdapter->ls();

            $this->logger->debug(print_r($fileInfo,true));
            $this->logger->debug(print_r($listAllFiles,true));

            if (!empty($listAllFiles)) {
                $latestFileName = $this->filterPattern($listAllFiles, $fileGrep);
                $fileName = $latestFileName;
            }

            $calculatedFileName = $fileName ? self::PROCESSING_DIR . "/" . $fileName : '';

            if ($calculatedFileName) {
                $this->sftpAdapter->mv($fileName, $calculatedFileName);
            }

            $this->logger->debug("Price importer fileName: " . $fileName);

            $this->file->setFilename($calculatedFileName);

            return $calculatedFileName;
        } catch (Exception $e) {
            throw new \Magento\Framework\Exception\LocalizedException(__('We can\'t read the import file.'));
        }
    }

    /**
     * @param array $list
     * @param string $fileName
     * @return string
     */
    protected function filterPattern(array $list, string $fileName): string
    {
        $fileList = [];
        foreach($list as $file) {
            if (str_starts_with($file['text'], $fileName)) {
                $fileList[] = $file['text'];
            }
        }
        arsort($fileList);
        // return smaller date file
        return array_pop($fileList) ?? '';
    }

    /**
     * @return string
     */
    public function getFileName() {
        return $this->file->getFileName();
    }

    /**
     * @return mixed
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function isPriceIndexerEnabled() {
        $storeId = $this->storeManager->getStore()->getId();
        return $this->scopeConfig->getValue(
            self::XML_PRICE_INDEXER_ENABLE,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            $storeId
        );
    }
}
