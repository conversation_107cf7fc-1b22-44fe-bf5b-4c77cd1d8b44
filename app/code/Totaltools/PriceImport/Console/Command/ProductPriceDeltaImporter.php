<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\PriceImport\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\ScheduledImportExport\Model\Scheduled\{Operation, OperationFactory};
use Magento\ScheduledImportExport\Model\ResourceModel\Scheduled\Operation\CollectionFactory as OperationCollectionFactory;
use Magento\Framework\App\{State, Area};
use Magento\Framework\Exception\LocalizedException;
use Totaltools\PriceImport\Model\Import\Price as PriceImport;
use Totaltools\PriceImport\Model\Import\Behavior\Pricing as Behavior;
use Totaltools\PriceImport\Helper\Data;
use Totaltools\PriceImport\Model\PriceImporter;

class ProductPriceDeltaImporter extends Command
{
    /**
     * @var State
     */
    protected $state;

    /**
     * @var OperationCollectionFactory
     */
    protected $operationCollectionFactory;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * @param State $state
     * @param OperationCollectionFactory $operationCollectionFactory
     * @param Data $helper
     * @param null $name
     */
    public function __construct(
        State $state,
        OperationCollectionFactory $operationCollectionFactory,
        Data $helper,
        $name = null
    ) {
        parent::__construct($name);
        $this->state = $state;
        $this->operationCollectionFactory = $operationCollectionFactory;
        $this->helper = $helper;
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName("totaltools:product:price-delta-importer");
        $this->setDescription("Total tools customer price delta importer to update base, state based and special prices");
        parent::configure(); // TODO: Change the autogenerated stub
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void|null
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            echo "process started ...";
            $this->state->setAreaCode(Area::AREA_FRONTEND);

            $operation = $this->loadOperation();

            if ($operation instanceof Operation && $operation->getId()) {
                $operation->run();
            } else {
                throw new LocalizedException(__("No operation exists for given entity (" . PriceImport::ENTITY_TYPE_CODE .") and behavior (" . Behavior::BEHAVIOR_DELTA_RRP_SPECIAL_PRICING. ")."));
            }
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
            $this->sendNotification($operation, $e->getMessage());
            return \Magento\Framework\Console\Cli::RETURN_FAILURE;
        }

        echo "\n\r process completed ...";
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }

    /**
     * @return Operation|null
     */
    protected function loadOperation()
    {
        return $this->operationCollectionFactory->create()
            ->addFieldToFilter('entity_type', PriceImport::ENTITY_TYPE_CODE)
            ->addFieldToFilter('behavior', Behavior::BEHAVIOR_DELTA_RRP_SPECIAL_PRICING)
            ->getFirstItem();
    }

    /**
     * @param Operation $operation
     * @param string $message
     * @return void
     */
    protected function sendNotification(Operation $operation, $message)
    {
        if (!$operation->getId()) {
            return $this->helper->sendEmailNotification(['trace' => $message]);
        }

        return $operation->sendEmailNotification(['trace' => $message]);
    }
}
