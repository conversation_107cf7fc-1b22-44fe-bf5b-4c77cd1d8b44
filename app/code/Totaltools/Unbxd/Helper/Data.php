<?php

namespace Totaltools\Unbxd\Helper;

use Magento\Framework\Indexer\IndexerRegistry;
use Unbxd\ProductFeed\Model\Indexer\Product as UnbxdProductIndexer;
use Unbxd\ProductFeed\Model\IndexingQueue\Handler;
use Magento\Catalog\Model\Product\Action;
use Unbxd\ProductFeed\Helper\ProductHelper;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    protected $resourceConnection;
    
    /**
     * @var IndexerRegistry
     */
    private $indexerRegistry;

    /**
     * @var ProductHelper
     */
    private $productHelper;

    /**
     * Indexer instance
     *
     * @var object
     */
    private $indexer = null;

    /**
     * Data constructor.
     *
     * @param \Magento\Framework\App\Helper\Context     $context
     * @param \Magento\Framework\App\ResourceConnection $resourceConnection
     * @param IndexerRegistry $indexerRegistry
     * @param ProductHelper $productHelper
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\App\ResourceConnection $resourceConnection,
        IndexerRegistry $indexerRegistry,
        ProductHelper $productHelper
    ) {
        parent::__construct($context);
        $this->resourceConnection = $resourceConnection;
        $this->indexerRegistry = $indexerRegistry;
        if (!$this->indexer) {
            $this->indexer = $indexerRegistry->get(UnbxdProductIndexer::INDEXER_ID);
        }
        $this->productHelper = $productHelper;
    }

    /**
     * @param array $skus
     */
    public function AddSkusToUnbxdQueue($skus)
    {
        $connection = $this->resourceConnection->getConnection();
        $tableName  = $connection->getTableName('catalog_product_entity');
        $query      = $connection->select()->from($tableName, 'entity_id')->where('sku IN(?)', $skus); 
        $productIds  = $connection->fetchCol($query);
      
        if (!$this->indexer->isScheduled() && !empty($productIds)) {
            $productIds = array_unique($productIds);
            $validProductIds = [];
            foreach ($productIds as $id) {
                /** @var \Magento\Catalog\Model\Product $product */
                $product = $this->productHelper->getProduct($id);
                if ($product && $this->productHelper->isProductTypeSupported($product->getTypeId())) {
                    Handler::$additionalInformation[$id] = __('Product with ID %1 was updated', $id);
                    $validProductIds[] = $id;
                }
            }
            if (!empty($validProductIds)) {
                // if indexer is 'Update on save' mode we need to rebuild related index data
                $this->indexer->reindexList($validProductIds);
            }
            return count($validProductIds);
        } else {
            return false;
        }
    }

}
