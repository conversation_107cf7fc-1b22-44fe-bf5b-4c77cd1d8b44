<?php


namespace Totaltools\Unbxd\Controller\Adminhtml\Productsync;

use Totaltools\Unbxd\Helper\Data as HelperData;

class Sync extends \Magento\Backend\App\Action
{
	/**
     * @var HelperData
     */
    protected $helperData;

	/**
     * Sync constructor.
     * @param \Magento\Backend\App\Action\Context $context
     * @param HelperData $helperData
     */
    public function __construct(
		\Magento\Backend\App\Action\Context $context,
        HelperData $helperData
    ) {
		parent::__construct($context);
        $this->helperData = $helperData;
    }

    public function execute()
    {
		$resultRedirect = $this->resultRedirectFactory->create();

		$data = $this->getRequest()->getPostValue();
		$skus = $data['skus'];
		$skus_array = preg_split("/\r\n|\n|\r/", $skus);
		
		// remove duplicate and empty values from sku array
		$skus_array = array_filter(array_unique($skus_array)); 
		
		$product_count = $this->helperData->AddSkusToUnbxdQueue($skus_array); 

		if($product_count)
		{
			$this->messageManager->addSuccess(__('%1 Product(s) successfully added to Unbxd indexing queue.', $product_count));
		} else {
			$this->messageManager->addError(__('Given skus did not match with any product sku.'));
		}
		

		return $resultRedirect->setPath('*/*/manual');
    }
}
