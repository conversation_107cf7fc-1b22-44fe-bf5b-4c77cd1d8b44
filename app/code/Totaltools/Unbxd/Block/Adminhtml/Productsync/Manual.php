<?php

namespace Totaltools\Unbxd\Block\Adminhtml\Productsync;

class Manual extends \Magento\Backend\Block\Widget\Form\Container
{

    public function _construct()
    {
        parent::_construct();
        $this->_blockGroup = 'Totaltools_Unbxd';
        $this->_controller = 'adminhtml_productsync';
        $this->_mode = 'manual';
        $this->buttonList->remove('delete');
        $this->buttonList->remove('reset');
        $this->updateButton('save', 'label', __('Sync to Unbxd Indexing Queue'));
    }
}
