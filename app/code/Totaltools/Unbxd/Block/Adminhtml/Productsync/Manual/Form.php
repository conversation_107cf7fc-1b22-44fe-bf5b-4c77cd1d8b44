<?php



namespace Totaltools\Unbxd\Block\Adminhtml\Productsync\Manual;

use Magento\Backend\Block\Widget\Form\Generic;

/**
 * Class Form
 */
class Form extends Generic
{
    /**
     * @return $this
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    protected function _prepareForm()
    {

        /** @var \Magento\Framework\Data\Form $form */
        $form = $this->_formFactory->create(
            [
                'data' => [
                    'id'      => 'edit_form',
                    'action'  => $this->getUrl('*/*/sync'),
                    'method'  => 'post',
                    'enctype' => 'multipart/form-data',
                ],
            ]
        );

        $fieldset = $form->addFieldset('general_fieldset', ['legend' => __('Manual Product Sync')]);

        $fieldset->addField(
            'skus',
            'textarea',
            [
                'title'    => __('SKUS'),
                'label'    => __('SKUS'),
                'name'     => 'skus',
                'required' => true,
                'rows' => 20,
                'cols' => 40,
                'note'     => 'Add one sku per line.',
            ]
        );

        $form->setUseContainer(true);
        $this->setForm($form);

        return parent::_prepareForm();
    }
}
