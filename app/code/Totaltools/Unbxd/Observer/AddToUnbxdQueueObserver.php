<?php

namespace Totaltools\Unbxd\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Indexer\IndexerRegistry;
use Unbxd\ProductFeed\Model\Indexer\Product as UnbxdProductIndexer;
use Unbxd\ProductFeed\Model\IndexingQueue\Handler;
use Magento\Catalog\Model\Product\Action;
use Unbxd\ProductFeed\Helper\ProductHelper;

class AddToUnbxdQueueObserver implements ObserverInterface
{

    /**
     * @var IndexerRegistry
     */
    private $indexerRegistry;

    /**
     * @var ProductHelper
     */
    private $productHelper;

    /**
     * Indexer instance
     *
     * @var object
     */
    private $indexer = null;

    /**
     * AddToUnbxdQueueObserver constructor.
     * @param IndexerRegistry $indexerRegistry
     * @param ProductHelper $productHelper
     */
    public function __construct(
        IndexerRegistry $indexerRegistry,
        ProductHelper $productHelper
    ) {
        $this->indexerRegistry = $indexerRegistry;
        if (!$this->indexer) {
            $this->indexer = $indexerRegistry->get(UnbxdProductIndexer::INDEXER_ID);
        }
        $this->productHelper = $productHelper;
    }

    /**
     * Add Rapidflow product import to Unbxd Queue
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        $eventVars = $observer->getData('vars');
        $products = $eventVars['old_data'];
        $productIds = [];
        if($products)
        {
            foreach($products as $product)
            {
                if (isset($product[0]['product.entity_id'])) {
                    $productIds[] = $product[0]['product.entity_id'];
                }

                if (isset($product[0]['entity_id'])) {
                    $productIds[] = $product[0]['entity_id'];
                }
            }
        }

        if (!$this->indexer->isScheduled() && $productIds) {
            $productIds = array_unique($productIds);
            $validProductIds = [];
            foreach ($productIds as $id) {
                /** @var \Magento\Catalog\Model\Product $product */
                $product = $this->productHelper->getProduct($id);
                if ($product && $this->productHelper->isProductTypeSupported($product->getTypeId())) {
                    Handler::$additionalInformation[$id] = __('Product with ID %1 was updated', $id);
                    $validProductIds[] = $id;
                }
            }
            if (!empty($validProductIds)) {
                // if indexer is 'Update on save' mode we need to rebuild related index data
                $this->indexer->reindexList($validProductIds);
            }
        }

    }
}
