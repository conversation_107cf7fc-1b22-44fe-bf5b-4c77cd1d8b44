<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Model\ResourceModel\Keyword;

use Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection;

class Collection extends AbstractCollection
{

    /**
     * @inheritDoc
     */
    protected $_idFieldName = 'keyword_id';

    /**
     * @inheritDoc
     */
    protected function _construct()
    {
        $this->_init(
            \Totaltools\Longtail\Model\Keyword::class,
            \Totaltools\Longtail\Model\ResourceModel\Keyword::class
        );
    }
}

