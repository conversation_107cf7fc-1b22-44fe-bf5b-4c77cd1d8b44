<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Model;

use Magento\Framework\Model\AbstractModel;
use Totaltools\Longtail\Api\Data\KeywordInterface;

class Keyword extends AbstractModel implements KeywordInterface
{

    /**
     * @inheritDoc
     */
    public function _construct()
    {
        $this->_init(\Totaltools\Longtail\Model\ResourceModel\Keyword::class);
    }

    /**
     * @inheritDoc
     */
    public function getKeywordId()
    {
        return $this->getData(self::KEYWORD_ID);
    }

    /**
     * @inheritDoc
     */
    public function setKeywordId($keywordId)
    {
        return $this->setData(self::KEYWORD_ID, $keywordId);
    }

    /**
     * @inheritDoc
     */
    public function getKeywordTitle()
    {
        return $this->getData(self::KEYWORD_TITLE);
    }

    /**
     * @inheritDoc
     */
    public function setKeywordTitle($keywordTitle)
    {
        return $this->setData(self::KEYWORD_TITLE, $keywordTitle);
    }

    /**
     * @inheritDoc
     */
    public function getKeywordUrl()
    {
        return $this->getData(self::KEYWORD_URL);
    }

    /**
     * @inheritDoc
     */
    public function setKeywordUrl($keywordUrl)
    {
        return $this->setData(self::KEYWORD_URL, $keywordUrl);
    }

    /**
     * @inheritDoc
     */
    public function getMetaTitle()
    {
        return $this->getData(self::META_TITLE);
    }

    /**
     * @inheritDoc
     */
    public function setMetaTitle($metaTitle)
    {
        return $this->setData(self::META_TITLE, $metaTitle);
    }

    /**
     * @inheritDoc
     */
    public function getMetaDescription()
    {
        return $this->getData(self::META_DESCRIPTION);
    }

    /**
     * @inheritDoc
     */
    public function setMetaDescription($metaDescription)
    {
        return $this->setData(self::META_DESCRIPTION, $metaDescription);
    }

    /**
     * @inheritDoc
     */
    public function getDescription()
    {
        return $this->getData(self::DESCRIPTION);
    }

    /**
     * @inheritDoc
     */
    public function setDescription($description)
    {
        return $this->setData(self::DESCRIPTION, $description);
    }

    /**
     * @inheritDoc
     */
    public function getCreatedAt()
    {
        return $this->getData(self::CREATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setCreatedAt($createdAt)
    {
        return $this->setData(self::CREATED_AT, $createdAt);
    }

    /**
     * @inheritDoc
     */
    public function getUpdatedAt()
    {
        return $this->getData(self::UPDATED_AT);
    }

    /**
     * @inheritDoc
     */
    public function setUpdatedAt($updatedAt)
    {
        return $this->setData(self::UPDATED_AT, $updatedAt);
    }
}

