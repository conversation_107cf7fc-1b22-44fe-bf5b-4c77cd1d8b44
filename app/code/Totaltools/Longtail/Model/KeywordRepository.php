<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Model;

use Magento\Framework\Api\SearchCriteria\CollectionProcessorInterface;
use Magento\Framework\Exception\CouldNotDeleteException;
use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;
use Totaltools\Longtail\Api\Data\KeywordInterface;
use Totaltools\Longtail\Api\Data\KeywordInterfaceFactory;
use Totaltools\Longtail\Api\Data\KeywordSearchResultsInterfaceFactory;
use Totaltools\Longtail\Api\KeywordRepositoryInterface;
use Totaltools\Longtail\Model\ResourceModel\Keyword as ResourceKeyword;
use Totaltools\Longtail\Model\ResourceModel\Keyword\CollectionFactory as KeywordCollectionFactory;

class KeywordRepository implements KeywordRepositoryInterface
{

    /**
     * @var Keyword
     */
    protected $searchResultsFactory;

    /**
     * @var KeywordInterfaceFactory
     */
    protected $keywordFactory;

    /**
     * @var CollectionProcessorInterface
     */
    protected $collectionProcessor;

    /**
     * @var KeywordCollectionFactory
     */
    protected $keywordCollectionFactory;

    /**
     * @var ResourceKeyword
     */
    protected $resource;


    /**
     * @param ResourceKeyword $resource
     * @param KeywordInterfaceFactory $keywordFactory
     * @param KeywordCollectionFactory $keywordCollectionFactory
     * @param KeywordSearchResultsInterfaceFactory $searchResultsFactory
     * @param CollectionProcessorInterface $collectionProcessor
     */
    public function __construct(
        ResourceKeyword $resource,
        KeywordInterfaceFactory $keywordFactory,
        KeywordCollectionFactory $keywordCollectionFactory,
        KeywordSearchResultsInterfaceFactory $searchResultsFactory,
        CollectionProcessorInterface $collectionProcessor
    ) {
        $this->resource = $resource;
        $this->keywordFactory = $keywordFactory;
        $this->keywordCollectionFactory = $keywordCollectionFactory;
        $this->searchResultsFactory = $searchResultsFactory;
        $this->collectionProcessor = $collectionProcessor;
    }

    /**
     * @inheritDoc
     */
    public function save(KeywordInterface $keyword)
    {
        try {
            $this->resource->save($keyword);
        } catch (\Exception $exception) {
            throw new CouldNotSaveException(__(
                'Could not save the keyword: %1',
                $exception->getMessage()
            ));
        }
        return $keyword;
    }

    /**
     * @inheritDoc
     */
    public function get($keywordId)
    {
        $keyword = $this->keywordFactory->create();
        $this->resource->load($keyword, $keywordId);
        if (!$keyword->getId()) {
            throw new NoSuchEntityException(__('Keyword with id "%1" does not exist.', $keywordId));
        }
        return $keyword;
    }

    /**
     * @inheritDoc
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $criteria
    ) {
        $collection = $this->keywordCollectionFactory->create();
        
        $this->collectionProcessor->process($criteria, $collection);
        
        $searchResults = $this->searchResultsFactory->create();
        $searchResults->setSearchCriteria($criteria);
        
        $items = [];
        foreach ($collection as $model) {
            $items[] = $model;
        }
        
        $searchResults->setItems($items);
        $searchResults->setTotalCount($collection->getSize());
        return $searchResults;
    }

    /**
     * @inheritDoc
     */
    public function delete(KeywordInterface $keyword)
    {
        try {
            $keywordModel = $this->keywordFactory->create();
            $this->resource->load($keywordModel, $keyword->getKeywordId());
            $this->resource->delete($keywordModel);
        } catch (\Exception $exception) {
            throw new CouldNotDeleteException(__(
                'Could not delete the Keyword: %1',
                $exception->getMessage()
            ));
        }
        return true;
    }

    /**
     * @inheritDoc
     */
    public function deleteById($keywordId)
    {
        return $this->delete($this->get($keywordId));
    }
}

