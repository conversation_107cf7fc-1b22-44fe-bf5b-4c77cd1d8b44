<?php
$links = $block->getLongTailKeywordsCollection();
?>
<?php if ($block->showLongtailKeywordsOnSitemap() && $links->count() > 0): ?>
    <div class="xsitemap-pages">
        <h3><?php echo $this->escapeHtml($block->getLongtailKeywordSitemapTitle()) ?></h3>
            <ul class="pages">
                <?php foreach ($links as $link): ?>
                    <?php $title = !empty($link->getKeywordTitle()) ? $link->getKeywordTitle() : $link->getKeywordUrl(); ?>
                    <li class="page-url">
                        <a href="<?php echo $block->escapeUrl($link->getKeywordUrl()) ?>"><?php echo $this->escapeHtml($title) ?></a>
                    </li>
                <?php endforeach; ?>
            </ul>
    </div>
<?php endif; ?>