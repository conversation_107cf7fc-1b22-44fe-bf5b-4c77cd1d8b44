<?php 

$longTailKeywords = $this->getCurrentCategoryLongTailKeywords();

$categoryPageContent = $this->getLongtailLinksCategoryPageMessage();
$categoryPageContent = $categoryPageContent ?? '';
$content = $longTailKeywords ? '<div class="category-longtail-keywords">' . trim($categoryPageContent) . ' ' : '';
$content .= '<ul>';
if ($longTailKeywords) {
    $links = [];
    foreach ($longTailKeywords as $longTailKeyword) {
        $links[] = '<li><a href="/buy/'. $longTailKeyword['keyword_url'] .'">' . $longTailKeyword['keyword_title'] . '</a></li>';
    }
    $content .= implode(' ', $links);

    $content .= '</ul></div>';
}

?>

<?= $content ?>
