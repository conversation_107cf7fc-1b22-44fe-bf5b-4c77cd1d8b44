<?php
/** @var Totaltools\Longtail\Block\Keyword\Index $block */
?>

<?php

$longtailKeyword = $block->getLongTailKeyword();
$blockPrefix = $block->getBlockPrefix();
$description = "";
if ($longtailKeyword) {
    $longTailKeywordData = $block->getLongTailKeywordByUrl($longtailKeyword);
    $description = $longTailKeywordData && !empty($longTailKeywordData->getData('description')) ? $longTailKeywordData->getData('description') : "";
}

?>
<div class="category-short-description">
    <?php echo $description; ?>
</div>
<script>
    window.unbxdConfig = window.unbxdConfig || {};
    window.unbxdConfig.longtailSearch = true;
    window.unbxdConfig.longtailPrefix = '<?= /* @noEscape */ $blockPrefix; ?>';
</script>

<div id="instant-search" class="instant-search">
    <div class="instant-search-loader">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
    </div>
</div>
<script type="text/javascript" src="<?= /* @noEscape */ $block->getViewFileUrl('Totaltools_Search::js/instantsearch.min.js'); ?>" async></script>
<script type="text/javascript" src="<?= /* @noEscape */ $block->getViewFileUrl('Totaltools_Search::js/vendor.min.js'); ?>" async></script>

