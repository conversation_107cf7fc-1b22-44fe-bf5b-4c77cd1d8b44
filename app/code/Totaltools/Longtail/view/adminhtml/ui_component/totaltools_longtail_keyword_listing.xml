<?xml version="1.0" ?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">totaltools_longtail_keyword_listing.totaltools_longtail_keyword_listing_data_source</item>
		</item>
	</argument>
	<settings>
		<spinner>totaltools_longtail_keyword_columns</spinner>
		<deps>
			<dep>totaltools_longtail_keyword_listing.totaltools_longtail_keyword_listing_data_source</dep>
		</deps>
		<buttons>
			<button name="import">
				<url path="adminhtml/import"/>
				<class>primary</class>
				<label translate="true">Import Longtail Keywords</label>
			</button>
			<button name="export">
				<url path="*/*/export"/>
				<class>primary</class>
				<label translate="true">Export Longtail Keywords</label>
			</button>
			<button name="add">
				<url path="*/*/new"/>
				<class>secondary</class>
				<label translate="true">Add new Longtail Keyword</label>
			</button>
		</buttons>
	</settings>
	<dataSource name="totaltools_longtail_keyword_listing_data_source" component="Magento_Ui/js/grid/provider">
		<settings>
			<storageConfig>
				<param name="indexField" xsi:type="string">keyword_id</param>
			</storageConfig>
			<updateUrl path="mui/index/render"/>
		</settings>
		<aclResource>Totaltools_Longtail::keywords</aclResource>
		<dataProvider name="totaltools_longtail_keyword_listing_data_source" class="Magento\Framework\View\Element\UiComponent\DataProvider\DataProvider">
			<settings>
				<requestFieldName>id</requestFieldName>
				<primaryFieldName>keyword_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<listingToolbar name="listing_top">
		<settings>
			<sticky>true</sticky>
		</settings>
		<bookmark name="bookmarks"/>
		<columnsControls name="columns_controls"/>
		<filters name="listing_filters"/>
		<massaction name="listing_massaction">
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="totaltools_longtail/keyword/massDelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete longtail keywords</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you wan't to delete selected longtail keywords?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
		<paging name="listing_paging"/>
	</listingToolbar>
	<columns name="totaltools_longtail_keyword_columns">
		<settings>
			<editorConfig>
				<param name="selectProvider" xsi:type="string">totaltools_longtail_keyword_listing.totaltools_longtail_keyword_listing.totaltools_longtail_keyword_columns.ids</param>
				<param name="enabled" xsi:type="boolean">true</param>
				<param name="indexField" xsi:type="string">keyword_id</param>
				<param name="clientConfig" xsi:type="array">
					<item name="saveUrl" xsi:type="url" path="totaltools_longtail/Keyword/inlineEdit"/>
					<item name="validateBeforeSave" xsi:type="boolean">false</item>
				</param>
			</editorConfig>
			<childDefaults>
				<param name="fieldAction" xsi:type="array">
					<item name="provider" xsi:type="string">totaltools_longtail_keyword_listing.totaltools_longtail_keyword_listing.totaltools_longtail_keyword_columns_editor</item>
					<item name="target" xsi:type="string">startEdit</item>
					<item name="params" xsi:type="array">
						<item name="0" xsi:type="string">${ $.$data.rowIndex }</item>
						<item name="1" xsi:type="boolean">true</item>
					</item>
				</param>
			</childDefaults>
		</settings>
		<selectionsColumn name="ids">
			<settings>
				<indexField>keyword_id</indexField>
			</settings>
		</selectionsColumn>
		<column name="keyword_id">
			<settings>
				<filter>text</filter>
				<sorting>asc</sorting>
				<label translate="true">ID</label>
			</settings>
		</column>
		<column name="keyword_title">
			<settings>
				<filter>text</filter>
				<label translate="true">Keyword Title</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">true</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="meta_title">
			<settings>
				<filter>text</filter>
				<label translate="true">Meta Title</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">true</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<actionsColumn name="actions" class="Totaltools\Longtail\Ui\Component\Listing\Column\KeywordActions">
			<settings>
				<indexField>keyword_id</indexField>
				<resizeEnabled>false</resizeEnabled>
				<resizeDefaultWidth>107</resizeDefaultWidth>
			</settings>
		</actionsColumn>
		<column name="keyword_url">
			<settings>
				<filter>text</filter>
				<label translate="true">Keyword Url</label>
				<editor>
					<editorType>text</editorType>
					<validation>
						<rule name="required-entry" xsi:type="boolean">true</rule>
					</validation>
				</editor>
			</settings>
		</column>
		<column name="created_at">
			<settings>
				<filter>text</filter>
				<label translate="true">Created At</label>
			</settings>
		</column>
		<column name="updated_at">
			<settings>
				<filter>text</filter>
				<label translate="true">Updated At</label>
			</settings>
		</column>
	</columns>
</listing>
