<?xml version="1.0" ?>
<form xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
	<argument name="data" xsi:type="array">
		<item name="js_config" xsi:type="array">
			<item name="provider" xsi:type="string">totaltools_longtail_keyword_form.keyword_form_data_source</item>
		</item>
		<item name="label" xsi:type="string" translate="true">General Information</item>
		<item name="template" xsi:type="string">templates/form/collapsible</item>
	</argument>
	<settings>
		<buttons>
			<button name="back" class="Totaltools\Longtail\Block\Adminhtml\Keyword\Edit\BackButton"/>
			<button name="delete" class="Totaltools\Longtail\Block\Adminhtml\Keyword\Edit\DeleteButton"/>
			<button name="save" class="Totaltools\Longtail\Block\Adminhtml\Keyword\Edit\SaveButton"/>
			<button name="save_and_continue" class="Totaltools\Longtail\Block\Adminhtml\Keyword\Edit\SaveAndContinueButton"/>
		</buttons>
		<namespace>totaltools_longtail_keyword_form</namespace>
		<dataScope>data</dataScope>
		<deps>
			<dep>totaltools_longtail_keyword_form.keyword_form_data_source</dep>
		</deps>
	</settings>
	<dataSource name="keyword_form_data_source">
		<argument name="data" xsi:type="array">
			<item name="js_config" xsi:type="array">
				<item name="component" xsi:type="string">Magento_Ui/js/form/provider</item>
			</item>
		</argument>
		<settings>
			<submitUrl path="*/*/save"/>
		</settings>
		<dataProvider name="keyword_form_data_source" class="Totaltools\Longtail\Model\Keyword\DataProvider">
			<settings>
				<requestFieldName>keyword_id</requestFieldName>
				<primaryFieldName>keyword_id</primaryFieldName>
			</settings>
		</dataProvider>
	</dataSource>
	<fieldset name="general">
		<settings>
			<label>General</label>
		</settings>
		<field name="keyword_url" formElement="input" sortOrder="10">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Keyword</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Keyword Url</label>
				<dataScope>keyword_url</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="keyword_title" formElement="input" sortOrder="20">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Keyword</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Keyword Title</label>
				<dataScope>keyword_title</dataScope>
				<validation>
					<rule name="required-entry" xsi:type="boolean">true</rule>
				</validation>
			</settings>
		</field>
		<field name="description" formElement="textarea" sortOrder="30">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Keyword</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Description</label>
                <dataScope>description</dataScope>
            </settings>
        </field>
		<field name="meta_title" formElement="input" sortOrder="35">
			<argument name="data" xsi:type="array">
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Keyword</item>
				</item>
			</argument>
			<settings>
				<dataType>text</dataType>
				<label translate="true">Meta Title</label>
				<dataScope>meta_title</dataScope>
			</settings>
		</field>
		<field name="meta_description" formElement="textarea" sortOrder="40">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="source" xsi:type="string">Keyword</item>
                </item>
            </argument>
            <settings>
                <dataType>text</dataType>
                <label translate="true">Meta Description</label>
                <dataScope>meta_description</dataScope>
            </settings>
        </field>
		<field name="category_ids">
			<argument name="data" xsi:type="array">
				<item name="options" xsi:type="object">Magento\Catalog\Ui\Component\Product\Form\Categories\Options</item>
				<item name="config" xsi:type="array">
					<item name="source" xsi:type="string">Keyword</item>
					<item name="label" xsi:type="string" translate="true">Categories</item>
					<item name="componentType" xsi:type="string">field</item>
					<item name="formElement" xsi:type="string">multiselect</item>
					<item name="component" xsi:type="string">Magento_Catalog/js/components/new-category</item>
					<item name="elementTmpl" xsi:type="string">ui/grid/filters/elements/ui-select</item>
					<item name="dataScope" xsi:type="string">categories</item>
					<item name="filterOptions" xsi:type="boolean">true</item>
					<item name="showCheckbox" xsi:type="boolean">true</item>
					<item name="disableLabel" xsi:type="boolean">true</item>
					<item name="multiple" xsi:type="boolean">true</item>
					<item name="levelsVisibility" xsi:type="number">1</item>
					<item name="sortOrder" xsi:type="number">50</item>
					<item name="required" xsi:type="boolean">false</item>
					<item name="validation" xsi:type="array">
						<item name="required-entry" xsi:type="boolean">false</item>
					</item>
				</item>
				<item name="listens" xsi:type="array">
					<item name="${ $.namespace }.${ $.namespace }:responseData" xsi:type="string">setParsed</item>
				</item>
			</argument>
		</field>
	</fieldset>
</form>
