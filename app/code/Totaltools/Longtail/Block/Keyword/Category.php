<?php

namespace Totaltools\Longtail\Block\Keyword;

class Category extends \Magento\Framework\View\Element\Template
{
    const XML_PATH_CATEGORY_PAGE_LONGTAIL_LINKS_MESSAGE = 'totaltools_longtail_keywords/longtail/category_pages_message';

    /**
     * Core registry
     *
     * @var \Magento\Framework\Registry
     */
    protected $_coreRegistry = null;

    protected $connection;
    protected $resource;
    protected $scopeConfig;

    /**
     * @param \Magento\Framework\View\Element\Template\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Magento\Framework\App\ResourceConnection $resource
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\Registry $registry,
        \Magento\Framework\App\ResourceConnection $resource,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        array $data = []
    ) {
        $this->resource = $resource;
        $this->connection = $resource->getConnection();
        $this->_coreRegistry = $registry;
        $this->scopeConfig = $scopeConfig;
        parent::__construct($context, $data);
    }

    

    /**
     * Retrieve current category model object
     *
     * @return \Magento\Catalog\Model\Category
     */
    public function getCurrentCategory()
    {
        return $this->_coreRegistry->registry('current_category');
    }

    public function getCurrentCategoryLongTailKeywords()
    {
        $currentCategory = $this->getCurrentCategory();
        if ($currentCategory) {
            $categoryId = $currentCategory->getId();
            $tableName = $this->resource->getTableName('totaltools_longtail_keyword');
            $select = $this->connection->select()
                ->from($tableName)
                ->where('FIND_IN_SET(?, category_ids)', $categoryId);

            return $this->connection->fetchAll($select);
        }
    }

    public function getLongTailKeywordsByCategory($categoryId)
    {
        $tableName = $this->resource->getTableName('totaltools_longtail_keyword');
        $select = $this->connection->select()
            ->from($tableName)
            ->where('FIND_IN_SET(?, category_ids)', $categoryId);

        return $this->connection->fetchAll($select);
    }

    public function getLongtailLinksCategoryPageMessage()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_CATEGORY_PAGE_LONGTAIL_LINKS_MESSAGE, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }
}
