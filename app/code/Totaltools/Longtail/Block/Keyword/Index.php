<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Block\Keyword;

class Index extends \Magento\Framework\View\Element\Template
{
    const XML_PATH_LONGTAIL_KEYWORD_BLOCK_PREFIX = 'totaltools_longtail_keywords/longtail/block_prefix';
    const XML_PATH_BUY_PAGE_BLOCK_ID = 'totaltools_longtail_keywords/longtail/buy_page_block_id';


    private $request;

    private $scopeConfig;

    private $longTailRepository;
    
    private $searchCriteriaBuilder;

    private $blockRepository;

    /**
     * Constructor
     *
     * @param \Magento\Framework\View\Element\Template\Context  $context
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Totaltools\Longtail\Api\KeywordRepositoryInterface $longTailRepository
     * @param \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder
     * @param \Magento\Cms\Model\BlockRepository $blockRepository
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Totaltools\Longtail\Api\KeywordRepositoryInterface $longTailRepository,
        \Magento\Framework\Api\SearchCriteriaBuilder $searchCriteriaBuilder,
        \Magento\Cms\Model\BlockRepository $blockRepository,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->request = $context->getRequest();
        $this->scopeConfig = $scopeConfig;
        $this->longTailRepository = $longTailRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->blockRepository = $blockRepository;
    }

    public function getLongTailKeyword()
    {
        $longtailKeyword = $this->request->getParam('longtail_keyword') ?? '';
        return $longtailKeyword;
    }

    public function getBlockPrefix()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_LONGTAIL_KEYWORD_BLOCK_PREFIX, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }

    public function getBuyPageBlockIdentifier()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_BUY_PAGE_BLOCK_ID, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }

    public function getLongTailKeywordByUrl($url)
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('keyword_url', $url, 'eq')->create();
        $items = $this->longTailRepository->getList($searchCriteria)->getItems();

        if (!empty($items)) {
            return current($items);
        }

        return null;
    }

    public function getBuyPageBlockTitle()
    {
        try {
            $blockIdentifier = $this->getBuyPageBlockIdentifier();
            if ($blockIdentifier) {
                $block = $this->blockRepository->getById($blockIdentifier);
                return $block->getTitle();
            }
        } catch (Exception $e) {
            return '';
        }
    }
}
