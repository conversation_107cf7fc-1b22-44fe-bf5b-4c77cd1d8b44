<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Block\Sitemap;

use Totaltools\Longtail\Model\ResourceModel\Keyword\CollectionFactory as KeywordCollectionFactory;

class KeywordLinks extends \Magento\Framework\View\Element\Template
{
    const XML_PATH_SHOW_LONGTAIL_KEYWORD_URL_SITEMAP = 'totaltools_longtail_keywords/longtail/show_on_sitemap';
    const XML_PATH_LONGTAIL_KEYWORD_SITEMAP_TITLE = 'totaltools_longtail_keywords/longtail/title';


    /**
     * @var KeywordCollectionFactory
     */
    protected $keywordCollectionFactory;

    private $scopeConfig;

    /**
     * Constructor
     *
     * @param \Magento\Framework\View\Element\Template\Context  $context
     * @param KeywordCollectionFactory $keywordCollectionFactory
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param array $data
     */
    public function __construct(
        \Magento\Framework\View\Element\Template\Context $context,
        KeywordCollectionFactory $keywordCollectionFactory,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        array $data = []
    ) {
        parent::__construct($context, $data);
        $this->keywordCollectionFactory = $keywordCollectionFactory;
        $this->scopeConfig = $scopeConfig;
    }

    public function getLongTailKeywordsCollection()
    {
        $collection = $this->keywordCollectionFactory->create();
        return $collection;
    }

    public function showLongtailKeywordsOnSitemap()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_SHOW_LONGTAIL_KEYWORD_URL_SITEMAP, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }

    public function getLongtailKeywordSitemapTitle()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_LONGTAIL_KEYWORD_SITEMAP_TITLE, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }
}
