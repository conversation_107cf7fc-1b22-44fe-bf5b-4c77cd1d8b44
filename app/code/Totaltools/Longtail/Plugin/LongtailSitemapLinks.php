<?php

namespace Totaltools\Longtail\Plugin;

use Magento\Framework\ObjectManagerInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use MageWorx\XmlSitemap\Helper\Data as Helper;
use MageWorx\XmlSitemap\Model\Generator;
use MageWorx\XmlSitemap\Model\Generator\AdditionalLinks;
use MageWorx\XmlSitemap\Model\WriterInterface;
use Totaltools\Longtail\Model\ResourceModel\Keyword\CollectionFactory as KeywordCollectionFactory;

class LongtailSitemapLinks
{
    const XML_PATH_SHOW_LONGTAIL_KEYWORD_URL_SITEMAP = 'totaltools_longtail_keywords/longtail/show_on_sitemap';

    const XML_URL_PREFIX = 'buy';

    /**
     * @var Helper
     */
    protected $helper;

    /**
     * @var ObjectManagerInterface
     */
    protected $objectManager;

    /**
     * @var KeywordCollectionFactory
     */
    protected $keywordCollectionFactory;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var WriterInterface
     */
    protected $xmlWriter;

    /**
     * LongtailSitemapLinks constructor.
     *
     * @param Helper $helper
     * @param ObjectManagerInterface $objectManager
     * @param KeywordCollectionFactory $keywordCollectionFactory
     * @param ScopeConfigInterface $scopeConfig
     * @param WriterInterface $xmlWriter
     */
    public function __construct(
        Helper $helper,
        ObjectManagerInterface $objectManager,
        KeywordCollectionFactory $keywordCollectionFactory,
        ScopeConfigInterface $scopeConfig,
        WriterInterface $xmlWriter
    ) {
        $this->helper = $helper;
        $this->objectManager = $objectManager;
        $this->keywordCollectionFactory = $keywordCollectionFactory;
        $this->scopeConfig = $scopeConfig;
        $this->xmlWriter = $xmlWriter;

    }

    /**
     * Add longtail keyword links to the sitemap after the original generate method
     *
     * @param AdditionalLinks $subject
     * @param int $storeId
     * @param Generator $writer
     * @param bool|null $usePubInMediaUrls
     */
    public function afterGenerate(
        AdditionalLinks $subject,
        $storeId,
        $writer,
        $usePubInMediaUrls = null
    ) {
        if ($this->showLongtailKeywordsOnSitemap()) {
            $this->helper->init($storeId);
            $this->storeBaseUrl = $this->xmlWriter->storeBaseUrl;
            $collection         = $this->keywordCollectionFactory->create();
            $changefreq         = $this->helper->getAdditionalLinkChangefreq();
            $priority           = $this->helper->getAdditionalLinkPriority();

            if (count($collection)) {
                foreach ($collection as $item) {
                    $sitemapLink = self::XML_URL_PREFIX . "/" . $item->getKeywordUrl();
                    $url = $this->helper->trailingSlash($this->convertLongtailUrl($sitemapLink));
                    $lastmod = $this->helper->getCurrentDate();
                    $this->xmlWriter->write($url, $lastmod, $changefreq, $priority);
                }
            }
            unset($collection);
        }
    }

    /**
     * Show Long tail keywords on sitemap
     *
     * @return bool
     */
    public function showLongtailKeywordsOnSitemap()
    {
        return $this->scopeConfig->getValue(self::XML_PATH_SHOW_LONGTAIL_KEYWORD_URL_SITEMAP, \Magento\Store\Model\ScopeInterface::SCOPE_STORE);
    }

    /**
     * Retrieve URL
     *
     * @param string $url
     * @return string
     */
    protected function convertLongtailUrl($url)
    {
        if (strpos($url, '://') !== false) {
            return $url;
        }

        return $this->storeBaseUrl . ltrim($url, '/');
    }
}
