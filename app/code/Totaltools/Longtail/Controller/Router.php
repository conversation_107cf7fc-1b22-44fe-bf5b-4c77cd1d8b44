<?php

namespace Totaltools\Longtail\Controller;

use Magento\Framework\App\ActionFactory;
use Magento\Framework\App\ResponseInterface;

/**
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 *
 * Class Router
 * @package Totaltools\Longtail\Controller
 */
class Router implements \Magento\Framework\App\RouterInterface
{
    const FRONT_NAME = 'buy';

    /**
     * @var ActionFactory
     */
    protected $actionFactory;

    /**
     * Response
     *
     * @var ResponseInterface
     */
    protected $_response;

    /**
     * @param ActionFactory $actionFactory
     * @param ResponseInterface $response
     */
    public function __construct(
        ActionFactory $actionFactory,
        ResponseInterface $response
    ) {
        $this->actionFactory = $actionFactory;
        $this->_response = $response;
    }

    /**
     * @param \Magento\Framework\App\RequestInterface $request
     * @return \Magento\Framework\App\ActionInterface|null
     */
    public function match(\Magento\Framework\App\RequestInterface $request)
    {
        $pathInfo = $request->getPathInfo() ?? '';
        $identifier = trim($pathInfo, '/');
        if (strpos($identifier, static::FRONT_NAME . '/') !== false) {
            $pathParams = explode('/', $identifier);
            $longtailKeyword = (isset($pathParams[1])) ? $pathParams[1] : '';
            $request->setModuleName(static::FRONT_NAME)
                ->setControllerName('keyword')
                ->setActionName('index')
                ->setParam('longtail_keyword', $longtailKeyword);
        } else {
            return false;
        }

        return $this->actionFactory->create(
            'Magento\Framework\App\Action\Forward',
            ['request' => $request]
        );
    }
}
