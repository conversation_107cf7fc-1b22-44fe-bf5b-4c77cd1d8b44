<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Controller\Keyword;

use Magento\Framework\App\Action\HttpGetActionInterface;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\ResultInterface;
use Magento\Framework\View\Result\PageFactory;
use Totaltools\Longtail\Block\Keyword\Index as BlockIndex;
use Magento\Theme\Block\Html\Breadcrumbs;

class Index implements HttpGetActionInterface
{

    /**
     * @var PageFactory
     */
    protected $resultPageFactory;

    /**
     * @var BlockIndex
     */
    protected $blockIndex;

    /**
     * @var RequestInterface
     */
    protected $request;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $_storeManager;

    /**
     * Constructor
     *
     * @param PageFactory $resultPageFactory
     * @param RequestInterface $request
     * @param BlockIndex $blockIndex
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     */
    public function __construct(
        PageFactory $resultPageFactory,
        RequestInterface $request,
        BlockIndex $blockIndex,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        $this->resultPageFactory = $resultPageFactory;
        $this->request = $request;
        $this->blockIndex = $blockIndex;
        $this->_storeManager = $storeManager;
    }

    /**
     * Execute view action
     *
     * @return ResultInterface
     */
    public function execute()
    {
        $result = $this->resultPageFactory->create();

        $longtailKeyword = $this->request->getParam('longtail_keyword');
        $longtailKeywordTitle = str_replace('-', ' ', $longtailKeyword);
        $longTailKeywordData = $this->blockIndex->getLongTailKeywordByUrl($longtailKeyword);
        if ($longTailKeywordData && !empty($longTailKeywordData->getData())) {
            $metaDescription = !empty($longTailKeywordData->getData('meta_description')) ? trim($longTailKeywordData->getData('meta_description')) : "";
            
            if($metaDescription) {
                $result->getConfig()->setDescription($metaDescription);
            }
        }

        $pageTitle = $longTailKeywordData && !empty($longTailKeywordData->getData('keyword_title')) ? trim($longTailKeywordData->getData('keyword_title')) : $longtailKeywordTitle;
        $pageTitle = ucwords($pageTitle);

        $metaTitle = $longTailKeywordData && !empty($longTailKeywordData->getData('meta_title')) ? trim($longTailKeywordData->getData('meta_title')) : $pageTitle;

        $result->getConfig()->getTitle()->set($pageTitle);
        $result->getConfig()->setMetadata('title', $metaTitle);

        /** @var Breadcrumbs $breadcrumbsBlock */
        if ($breadcrumbsBlock = $result->getLayout()->getBlock('breadcrumbs')) {
            $breadcrumbsBlock->addCrumb(
                'home',
                [
                    'label' => __('Home'),
                    'title' => __('Go to Home Page'),
                    'link' => $this->_storeManager->getStore()->getBaseUrl()
                ]
            );

            $breadcrumbsBlock->addCrumb(
                'buy',
                [
                    'label' => __('Buy'),
                    'title' => __('Buy'),
                    'link' => '/buy'
                ]
            );

            $breadcrumbsBlock->addCrumb('keyword', ['label' => $pageTitle, 'title' => $pageTitle]);
        }

        return $result;
    }
}
