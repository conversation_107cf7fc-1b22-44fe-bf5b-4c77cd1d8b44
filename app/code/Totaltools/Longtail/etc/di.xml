<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Totaltools\Longtail\Api\KeywordRepositoryInterface" type="Totaltools\Longtail\Model\KeywordRepository"/>
	<preference for="Totaltools\Longtail\Api\Data\KeywordInterface" type="Totaltools\Longtail\Model\Keyword"/>
	<preference for="Totaltools\Longtail\Api\Data\KeywordSearchResultsInterface" type="Magento\Framework\Api\SearchResults"/>
	<virtualType name="Totaltools\Longtail\Model\ResourceModel\Keyword\Grid\Collection" type="Magento\Framework\View\Element\UiComponent\DataProvider\SearchResult">
		<arguments>
			<argument name="mainTable" xsi:type="string">totaltools_longtail_keyword</argument>
			<argument name="resourceModel" xsi:type="string">Totaltools\Longtail\Model\ResourceModel\Keyword\Collection</argument>
		</arguments>
	</virtualType>
	<type name="Magento\Framework\View\Element\UiComponent\DataProvider\CollectionFactory">
		<arguments>
			<argument name="collections" xsi:type="array">
				<item name="totaltools_longtail_keyword_listing_data_source" xsi:type="string">Totaltools\Longtail\Model\ResourceModel\Keyword\Grid\Collection</item>
			</argument>
		</arguments>
	</type>
	<type name="Magento\ImportExport\Model\Import\SampleFileProvider">
        <arguments>
            <argument name="samples" xsi:type="array">
                <item name="longtail_keywords" xsi:type="string">Totaltools_Longtail</item>
            </argument>
        </arguments>
    </type>
	<type name="MageWorx\XmlSitemap\Model\Generator\AdditionalLinks">
        <plugin name="totaltools-longtail-sitemap-links" type="Totaltools\Longtail\Plugin\LongtailSitemapLinks" sortOrder="10" disabled="false" />
    </type>
</config>
