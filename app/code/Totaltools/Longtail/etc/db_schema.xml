<?xml version="1.0" ?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
	<table name="totaltools_longtail_keyword" resource="default" engine="innodb" comment="Totaltools Longtail Keyword Table">
		<column xsi:type="int" name="keyword_id" padding="10" unsigned="true" nullable="false" identity="true" comment="Keyword ID"/>
		<constraint xsi:type="primary" referenceId="PRIMARY">
			<column name="keyword_id"/>
		</constraint>
		<column name="keyword_title" nullable="false" xsi:type="varchar" comment="Keyword Title" length="255"/>
		<column name="keyword_url" nullable="false" xsi:type="varchar" comment="Keyword URL" length="255"/>
		<column name="category_ids" nullable="true" xsi:type="varchar" comment="Category IDs" length="255"/>
		<column name="description" nullable="true" xsi:type="text" comment="Description"/>
		<column name="meta_title" nullable="true" xsi:type="varchar" comment="Meta Title" length="255"/>
		<column name="meta_description" nullable="true" xsi:type="text" comment="Meta Description"/>
		<column name="created_at" nullable="false" xsi:type='timestamp' on_update='false'  default='CURRENT_TIMESTAMP' comment='Created At'/>
		<column name="updated_at" nullable="false" xsi:type='timestamp' on_update='true'  default='CURRENT_TIMESTAMP' comment='Updated At'/>
	</table>
</schema>
