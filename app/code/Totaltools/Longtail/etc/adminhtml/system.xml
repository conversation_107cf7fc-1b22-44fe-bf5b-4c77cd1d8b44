<?xml version="1.0"?>
<!--
/**
 * @package   Totaltools_Longtail
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 © Totaltools. <https://www.totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <tab id="totaltools" translate="label" sortOrder="0">
            <label>Total Tools</label>
        </tab>
        <section id="totaltools_longtail_keywords" translate="label" sortOrder="900" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Longtail Keywords</label>
            <tab>totaltools</tab>
            <resource>Totaltools_Longtail::config</resource>
                <group id="longtail" translate="label" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="60">
                    <label>Longtail Keywords Configuration</label>
                    <field id="block_prefix" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Block Prefix</label>
                        <comment>Block Prefix For Longtail Keyword Pages</comment>
                    </field>
                    <field id="buy_page_block_id" translate="label comment" type="text" sortOrder="15" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Buy Page Block Identifier</label>
                        <comment>This Block will render on /buy</comment>
                    </field>
                    <field id="show_on_sitemap" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Show Longtail Keywords Url On Sitemap</label>
                        <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="title" translate="label comment" type="text" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Longtail Keywords Title On Sitemap</label>
                        <comment>Write longtail keywords title here for sitemap</comment>
                    </field>
                    <field id="category_pages_message" translate="label" type="textarea" sortOrder="40" showInDefault="1" showInWebsite="1" showInStore="0">
                        <label>Category Page Longtail Links Message</label>
                    </field>
                </group>
        </section>
    </system>
</config>
