<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Backend:etc/menu.xsd">
	<menu>
		<add id="Totaltools_Longtail::top_level" title="Longtail Keywords" module="Totaltools_Longtail" sortOrder="9999" resource="Totaltools_Longtail::keywords" parent="Magento_Backend::content"/>
		<add id="Totaltools_Longtail::totaltools_longtail_keyword" title="Manage Longtail Keywords" module="Totaltools_Longtail" sortOrder="20" resource="Totaltools_Longtail::manage" parent="Totaltools_Longtail::top_level" action="totaltools_longtail/keyword/index"/>
		<add id="Totaltools_Longtail::totaltools_longtail_keyword_import" title="Import" module="Magento_ImportExport" sortOrder="30" resource="Totaltools_Longtail::import" parent="Totaltools_Longtail::top_level" action="adminhtml/import"/>
	</menu>
</config>
