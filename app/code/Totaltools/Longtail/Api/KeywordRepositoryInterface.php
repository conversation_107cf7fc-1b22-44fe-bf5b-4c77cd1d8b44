<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Api;

use Magento\Framework\Api\SearchCriteriaInterface;

interface KeywordRepositoryInterface
{

    /**
     * Save Keyword
     * @param \Totaltools\Longtail\Api\Data\KeywordInterface $keyword
     * @return \Totaltools\Longtail\Api\Data\KeywordInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function save(
        \Totaltools\Longtail\Api\Data\KeywordInterface $keyword
    );

    /**
     * Retrieve Keyword
     * @param string $keywordId
     * @return \Totaltools\Longtail\Api\Data\KeywordInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function get($keywordId);

    /**
     * Retrieve Keyword matching the specified criteria.
     * @param \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
     * @return \Totaltools\Longtail\Api\Data\KeywordSearchResultsInterface
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function getList(
        \Magento\Framework\Api\SearchCriteriaInterface $searchCriteria
    );

    /**
     * Delete Keyword
     * @param \Totaltools\Longtail\Api\Data\KeywordInterface $keyword
     * @return bool true on success
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function delete(
        \Totaltools\Longtail\Api\Data\KeywordInterface $keyword
    );

    /**
     * Delete Keyword by ID
     * @param string $keywordId
     * @return bool true on success
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteById($keywordId);
}

