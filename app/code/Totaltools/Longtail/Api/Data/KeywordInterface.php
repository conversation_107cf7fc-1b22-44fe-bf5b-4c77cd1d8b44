<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Api\Data;

interface KeywordInterface
{

    const KEYWORD_TITLE = 'keyword_title';
    const CREATED_AT = 'created_at';
    const UPDATED_AT = 'updated_at';
    const KEYWORD_ID = 'keyword_id';
    const KEYWORD_URL = 'keyword_url';
    const META_TITLE = 'meta_title';
    const META_DESCRIPTION = 'meta_description';
    const DESCRIPTION = 'description';

    /**
     * Get keyword_id
     * @return string|null
     */
    public function getKeywordId();

    /**
     * Set keyword_id
     * @param string $keywordId
     * @return \Totaltools\Longtail\Keyword\Api\Data\KeywordInterface
     */
    public function setKeywordId($keywordId);

    /**
     * Get keyword_title
     * @return string|null
     */
    public function getKeywordTitle();

    /**
     * Set keyword_title
     * @param string $keywordTitle
     * @return \Totaltools\Longtail\Keyword\Api\Data\KeywordInterface
     */
    public function setKeywordTitle($keywordTitle);

    /**
     * Get keyword_url
     * @return string|null
     */
    public function getKeywordUrl();

    /**
     * Set keyword_url
     * @param string $keywordUrl
     * @return \Totaltools\Longtail\Keyword\Api\Data\KeywordInterface
     */
    public function setKeywordUrl($keywordUrl);

    /**
     * Get meta_title
     * @return string|null
     */
    public function getMetaTitle();

    /**
     * Set meta_title
     * @param string $metaTitle
     * @return \Totaltools\Longtail\Keyword\Api\Data\KeywordInterface
     */
    public function setMetaTitle($metaTitle);

    /**
     * Get meta_description
     * @return string|null
     */
    public function getMetaDescription();

    /**
     * Set meta_description
     * @param string $metaDescription
     * @return \Totaltools\Longtail\Keyword\Api\Data\KeywordInterface
     */
    public function setMetaDescription($metaDescription);

    /**
     * Get description
     * @return string|null
     */
    public function getDescription();

    /**
     * Set description
     * @param string $description
     * @return \Totaltools\Longtail\Keyword\Api\Data\KeywordInterface
     */
    public function setDescription($description);

    /**
     * Get created_at
     * @return string|null
     */
    public function getCreatedAt();

    /**
     * Set created_at
     * @param string $createdAt
     * @return \Totaltools\Longtail\Keyword\Api\Data\KeywordInterface
     */
    public function setCreatedAt($createdAt);

    /**
     * Get updated_at
     * @return string|null
     */
    public function getUpdatedAt();

    /**
     * Set updated_at
     * @param string $updatedAt
     * @return \Totaltools\Longtail\Keyword\Api\Data\KeywordInterface
     */
    public function setUpdatedAt($updatedAt);
}

