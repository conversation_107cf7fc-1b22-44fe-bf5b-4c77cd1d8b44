<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Longtail\Api\Data;

interface KeywordSearchResultsInterface extends \Magento\Framework\Api\SearchResultsInterface
{

    /**
     * Get Keyword list.
     * @return \Totaltools\Longtail\Api\Data\KeywordInterface[]
     */
    public function getItems();

    /**
     * Set keyword_title list.
     * @param \Totaltools\Longtail\Api\Data\KeywordInterface[] $items
     * @return $this
     */
    public function setItems(array $items);
}

