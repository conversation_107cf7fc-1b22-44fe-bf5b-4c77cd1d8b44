<?php

declare(strict_types=1);

namespace Totaltools\RequisitionList\ViewModel;

use Magento\Framework\View\Element\Block\ArgumentInterface;
use Magento\Framework\UrlInterface;
use Magento\RequisitionList\Api\Data\RequisitionListItemInterface;
use Magento\RequisitionList\Model\RequisitionListItemProduct;

/**
 * Item view model for requisition list item.
 */
class Item implements ArgumentInterface
{
    /**
     * @var RequisitionListItemProduct
     */
    private $requisitionListItemProduct;

    /**
     * @var UrlInterface
     */
    private $urlBuilder;

    /**
     * @var RequisitionListItemInterface
     */
    private $item;

    /**
     * @param RequisitionListItemProduct $requisitionListItemProduct
     * @param UrlInterface $urlBuilder
     */
    public function __construct(
        RequisitionListItemProduct $requisitionListItemProduct,
        UrlInterface $urlBuilder
    ) {
        $this->requisitionListItemProduct = $requisitionListItemProduct;
        $this->urlBuilder = $urlBuilder;
    }

    /**
     * Set requisition list item.
     *
     * @param RequisitionListItemInterface $item
     * @return $this
     */
    public function setItem(RequisitionListItemInterface $item)
    {
        $this->item = $item;
        return $this;
    }

    /**
     * Get requisition list item.
     *
     * @return RequisitionListItemInterface
     */
    public function getItem()
    {
        return $this->item;
    }

    public function isItemSaleable($item)
    {
        $product = $this->requisitionListItemProduct->getProduct($item);
        return $product->isSaleable();
    }
}
