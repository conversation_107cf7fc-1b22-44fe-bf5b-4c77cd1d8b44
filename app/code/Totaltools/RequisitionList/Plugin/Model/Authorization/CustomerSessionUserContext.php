<?php
/**
 * Total Tools RequisitionList.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\RequisitionList\Plugin\Model\Authorization;

/**
 * Class CustomerSessionUserContext
 *
 * @package Totaltools\RequisitionList\Plugin\Model\Authorization
 */
class CustomerSessionUserContext
{
    /**
     * @var \Totaltools\Loyalty\Model\CustomerSession
     */
    private $session;

    /**
     * CustomerSessionUserContext constructor.
     *
     * @param \Totaltools\Loyalty\Model\CustomerSession $session
     */
    public function __construct(
        \Totaltools\Loyalty\Model\CustomerSession $session
    ) {
        $this->session = $session;
    }

    /**
     * @param \Magento\Customer\Model\Authorization\CustomerSessionUserContext $subject
     * @param $result
     *
     * @return int|null
     */
    public function afterGetUserId(
        \Magento\Customer\Model\Authorization\CustomerSessionUserContext $subject,
        $result
    ) {
        if (!$result) {
            $result = $this->session->getCustomerId();
        }

        return $result;
    }
}
