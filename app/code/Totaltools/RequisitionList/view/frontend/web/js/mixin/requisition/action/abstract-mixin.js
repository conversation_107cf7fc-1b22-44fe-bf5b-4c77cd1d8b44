/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

define([
    'Magento_RequisitionList/js/requisition',
    'jquery',
    'underscore',
    'mage/dataPost'
], function (RequisitionComponent, $, _, dataPost) {
    'use strict';

    return function(target) {
        return target.extend({
            performListAction: function (list) {
                $(".action.requisition-list-button").attr("disabled","disabled");

                var dfd = $.Deferred(),
                    postData, files;

                if (!this._isActionValid()) {
                    return dfd.reject().promise();
                }

                postData = {
                    action: this.action,
                    data: this._getActionData(list)
                };
                files = this._getLoadedFiles();

                if (Object.keys(files).length) {
                    postData = _.extend(postData, {
                        'files': files
                    });
                }

                dataPost().postData(postData);

                return dfd.resolve().promise();
            }
        });
    };
});
