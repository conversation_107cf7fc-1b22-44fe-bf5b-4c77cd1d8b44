<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\Blog\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

class Data extends AbstractHelper
{

    const XML_PATH_BLOG_INDEX_PAGE_TITLE = 'mfblog/index_page/title';
    const XML_PATH_BLOG_INDEX_PAGE_DESCRIPTION = 'mfblog/index_page/meta_description';

    /**
     * @param \Magento\Framework\App\Helper\Context $context
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context
    ) {
        parent::__construct($context);
    }

    public function getIndexTitle()
    {
        return $this->getConfigValue(self::XML_PATH_BLOG_INDEX_PAGE_TITLE);
    }

    public function getMetaDescription()
    {
        return $this->getConfigValue(self::XML_PATH_BLOG_INDEX_PAGE_DESCRIPTION);
    }
   
    /**
     * Retrieve blog title
     * @return string
     */
    public function getConfigValue($param)
    {
        return $this->scopeConfig->getValue(
            $param,
            ScopeInterface::SCOPE_STORE
        );
    }
}

