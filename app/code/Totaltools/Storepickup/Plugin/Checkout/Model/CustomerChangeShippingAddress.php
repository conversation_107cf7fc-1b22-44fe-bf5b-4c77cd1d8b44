<?php
/**
 * @category  Totaltools
 * @package   Totaltools_Storepickup
 * <AUTHOR> Internet Pty. , Ltd <<EMAIL>>
 * @copyright Balance Internet Pty., Ltd. All rights reserved.
 * @license   http://opensource.org/licenses/osl-3.0.php Open Software License (OSL 3.0)
 * @link      http://balanceinternet.com.au/
 */

namespace Totaltools\Storepickup\Plugin\Checkout\Model;

use Magento\Framework\Exception\InputException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Framework\Exception\StateException;

/**
 * Class CustomerChangeShippingAddress
 * @package Totaltools_Storepickup
 */
class CustomerChangeShippingAddress
{
    /**
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @codeCoverageIgnore
     */
    private $checkoutSession;

    /**
     * @var \Magestore\Storepickup\Model\StoreFactory
     */
    private $storeCollection;

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    private $totaltoolsConfig;

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    private $connection;

    /**
     * @var \Shippit\Shipping\Helper\Checkout
     */
    private $helper;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeLocator;

    /**
     * @var \Totaltools\Checkout\Model\CheckoutApiModel
     */
    private $checkoutApiModel;

    /**
     * CustomerChangeShippingAddress constructor.
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magestore\Storepickup\Model\StoreFactory $storeCollection
     * @param \Magento\Quote\Api\PaymentMethodManagementInterface $paymentMethodManagement
     * @param \Magento\Checkout\Model\PaymentDetailsFactory $paymentDetailsFactory
     * @param \Magento\Quote\Api\CartTotalRepositoryInterface $cartTotalsRepository
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\Quote\Model\QuoteAddressValidator $addressValidator
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Customer\Api\AddressRepositoryInterface $addressRepository
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Magento\Quote\Model\Quote\TotalsCollector $totalsCollector
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig
     * @param \Magento\Framework\App\ResourceConnection $connection
     * @param \Shippit\Shipping\Helper\Checkout $helper
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Checkout\Model\CheckoutApiModel $checkoutApiModel
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession,
        \Magestore\Storepickup\Model\StoreFactory $storeCollection,
        \Magento\Quote\Api\PaymentMethodManagementInterface $paymentMethodManagement,
        \Magento\Checkout\Model\PaymentDetailsFactory $paymentDetailsFactory,
        \Magento\Quote\Api\CartTotalRepositoryInterface $cartTotalsRepository,
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\Quote\Model\QuoteAddressValidator $addressValidator,
        \Psr\Log\LoggerInterface $logger,
        \Magento\Customer\Api\AddressRepositoryInterface $addressRepository,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Magento\Quote\Model\Quote\TotalsCollector $totalsCollector,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        \Magento\Framework\App\ResourceConnection $connection,
        \Shippit\Shipping\Helper\Checkout $helper,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Checkout\Model\CheckoutApiModel $checkoutApiModel
    ) {
        $this->checkoutSession = $checkoutSession;
        $this->storeCollection = $storeCollection;
        $this->paymentMethodManagement = $paymentMethodManagement;
        $this->paymentDetailsFactory = $paymentDetailsFactory;
        $this->cartTotalsRepository = $cartTotalsRepository;
        $this->quoteRepository = $quoteRepository;
        $this->addressValidator = $addressValidator;
        $this->logger = $logger;
        $this->addressRepository = $addressRepository;
        $this->scopeConfig = $scopeConfig;
        $this->totalsCollector = $totalsCollector;
        $this->totaltoolsConfig = $totalToolsConfig;
        $this->connection = $connection;
        $this->helper = $helper;
        $this->storeLocator = $storeRepository;
        $this->checkoutApiModel = $checkoutApiModel;
    }

    public function aroundSaveAddressInformation(
        \Magento\Checkout\Model\ShippingInformationManagement $subject,
        \Closure $proceed,
        $cartId,
        \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
    ) {
        $address = $addressInformation->getShippingAddress();
        $carrierCode = $addressInformation->getShippingCarrierCode();
        $methodCode = $addressInformation->getShippingMethodCode();

        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->getActive($cartId);
        $this->validateQuote($quote);

        $this->updateAuthorityToLeave($addressInformation, $quote);
        $this->updateDeliveryInstructions($addressInformation, $quote);

        $saveInAddressBook = $address->getSaveInAddressBook() ? 1 : 0;
        $sameAsBilling = $address->getSameAsBilling() ? 1 : 0;
        $customerAddressId = $address->getCustomerAddressId();
        $this->addressValidator->validate($address);
        $quote->setShippingAddress($address);
        $address = $quote->getShippingAddress();

        if ($customerAddressId) {
            $addressData = $this->addressRepository->getById($customerAddressId);
            $address = $quote->getShippingAddress()->importCustomerAddressData($addressData);
        }
        $billingAddress = $addressInformation->getBillingAddress();
        if ($billingAddress) {
            $quote->setBillingAddress($billingAddress);
        }

        $address->setSaveInAddressBook($saveInAddressBook);
        $address->setSameAsBilling($sameAsBilling);
        $address->setCollectShippingRates(true);

        if (!$address->getCountryId()) {
            throw new StateException(__('Shipping address is not set'));
        }

        $address->setShippingMethod($carrierCode . '_' . $methodCode);

        try {
            $this->totalsCollector->collectAddressTotals($quote, $address);
        } catch (\Exception $e) {
            $this->logger->critical($e);
            throw new InputException(__('Unable to save address. Please, check input data.'));
        }

        if (!$quote->isVirtual() && !$address->getShippingRateByCode($address->getShippingMethod())) {
            throw new NoSuchEntityException(
                __('Carrier with such method not found: %1, %2', $carrierCode, $methodCode)
            );
        }

        if (!$quote->validateMinimumAmount($quote->getIsMultiShipping())) {
            throw new InputException($this->scopeConfig->getValue(
                'sales/minimum_order/error_message',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
                $quote->getStoreId()
            ));
        }

        try {
            $address->save();
            $this->quoteRepository->save($quote->collectTotals());
            if ($addressInformation->getShippingAddress()) {
                $this->updateQuoteAddress((int) $quote->getId(), $addressInformation);
            }
        } catch (\Exception $e) {
            $this->logger->critical($e);
            throw new InputException(__('Unable to save shipping information. Please, check input data.'));
        }

        /** @var \Magento\Checkout\Api\Data\PaymentDetailsInterface $paymentDetails */
        $paymentDetails = $this->paymentDetailsFactory->create();
        $paymentDetails->setPaymentMethods($this->paymentMethodManagement->getList($cartId));
        $paymentDetails->setTotals($this->cartTotalsRepository->get($cartId));
        return $paymentDetails;
    }

    /**
     * @param int $quoteId
     * @param \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
     * @throws NoSuchEntityException
     */
    private function updateQuoteAddress($quoteId, $addressInformation)
    {
        $defaultMethodCode = $this->totaltoolsConfig->getTotalToolsConfig('cc_method_code');
        $carrierCode = $addressInformation->getShippingCarrierCode();
        $methodCode = $addressInformation->getShippingMethodCode();
        $shippingMethod = $carrierCode . '_' . $methodCode;
        $storeId = 'NULL';
        if ($addressInformation->getShippingMethodCode() === $defaultMethodCode
            && $addressInformation->getExtensionAttributes()
            && $addressInformation->getExtensionAttributes()->getStorelocatorId()
            && $addressInformation->getExtensionAttributes()->getStorelocatorId() > 0
        ) {
            $storeId = $addressInformation->getExtensionAttributes()->getStorelocatorId();
        }

        /** @var \Magento\Quote\Model\QuoteRepository $quote */
        $quote = $this->quoteRepository->get($quoteId);
        $shippingId = $quote->getShippingAddress()->getId();
        try {
            $defaultOption = [
                'shipping_method' => $shippingMethod,
                'storelocator_id' => $storeId,
            ];
            $addressData = array_merge($defaultOption, $this->buildShippingAddressStorePickup($addressInformation));
            $this->connection->getConnection()->update(
                $this->connection->getConnection()->getTableName('quote_address'),
                $addressData,
                ['address_id = ?' => (int)$shippingId]
            );
            if ((int)$storeId <= 0) {
                $this->setDeliveryStoreId($addressInformation);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error updating quote address (' . $shippingId . ')');
            $this->logger->error($e->getMessage());
        }
    }

    /**
     * @param \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
     */
    private function setDeliveryStoreId($addressInformation)
    {
        $shippingAddress = $addressInformation->getShippingAddress();
        $this->checkoutApiModel
            ->changeLocation(
                $shippingAddress->getPostcode(),
                $shippingAddress->getCity(),
                $shippingAddress->getCountryId(),
                $shippingAddress->getRegion());
    }
    /**
     * @param \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
     * @return array
     * @throws NoSuchEntityException
     */
    private function buildShippingAddressStorePickup($addressInformation)
    {
        // Workaround for ticket: TOT0017-1293
        // Check shipping method code
        $defaultMethodCode = $this->totaltoolsConfig->getTotalToolsConfig('cc_method_code');
        $datashipping = [];

        return $datashipping;
    }

    /**
     * @param $addressInformation
     * @param $quote
     */
    private function updateAuthorityToLeave($addressInformation, $quote)
    {
        if (!$this->helper->isAuthorityToLeaveActive()) {
            return;
        }

        $extensionAttributes = $addressInformation->getExtensionAttributes();

        if (empty($extensionAttributes) || !$extensionAttributes->getShippitAuthorityToLeave()) {
            return;
        }

        $authorityToLeave = $extensionAttributes->getShippitAuthorityToLeave();

        $quote->setShippitAuthorityToLeave($authorityToLeave);
    }

    /**
     * @param $addressInformation
     * @param $quote
     */
    private function updateDeliveryInstructions($addressInformation, $quote)
    {
        if (!$this->helper->isDeliveryInstructionsActive()) {
            return;
        }

        $extensionAttributes = $addressInformation->getExtensionAttributes();

        if (empty($extensionAttributes) || !$extensionAttributes->getShippitDeliveryInstructions()) {
            return;
        }

        $deliveryInstructions = $extensionAttributes->getShippitDeliveryInstructions();

        $quote->setShippitDeliveryInstructions($deliveryInstructions);
    }

    /**
     * Validate quote
     *
     * @param \Magento\Quote\Model\Quote $quote
     * @throws InputException
     * @throws NoSuchEntityException
     * @return void
     */
    private function validateQuote(\Magento\Quote\Model\Quote $quote)
    {
        if (0 == $quote->getItemsCount()) {
            throw new InputException(__('Shipping method is not applicable for empty cart'));
        }
    }
}
