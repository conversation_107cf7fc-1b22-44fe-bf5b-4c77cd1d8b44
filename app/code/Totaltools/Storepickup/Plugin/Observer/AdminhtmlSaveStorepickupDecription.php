<?php

namespace Totaltools\Storepickup\Plugin\Observer;

class AdminhtmlSaveStorepickupDecription 
{
    
    public function aroundExecute(
        \Magestore\Storepickup\Observer\AdminhtmlSaveStorepickupDecription $subject,
        \Closure $proceed,
        \Magento\Framework\Event\Observer $observer
    ) {
        $order = $observer->getEvent()->getOrder();
        if (!$order->getIsVirtual()) {
            return $proceed($observer);
        }
    }
    
}
