<?php
/**
 * Data
 *
 * @category  Totaltools
 * @package   Totaltools_EmailCart
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\EmailCart\Helper;

use Magento\Checkout\Model\Session;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Store\Model\ScopeInterface;
use Magento\Framework\Translate\Inline\StateInterface;
use Magento\Framework\Escaper;
use Magento\Framework\Mail\Template\TransportBuilder;
use \Magento\Framework\DataObject;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Customer\Model\Session as CusotmerSession;
use Totaltools\Customer\Helper\AttributeData;

class Data extends AbstractHelper
{
    const QUOTE_EMAIL_TEMPLATE = 'quote/email/template';
  
    /**
     * @var Session
     */
    private $checkoutSession;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var StateInterface
     */
    private $inlineTranslation;

    /**
     * @var Escaper
     */
    private $escaper;

    /**
     * @var TransportBuilder
     */
    private $transportBuilder;

    /**
     * @var DataObject
     */
    private $postObject;

    /**
     * @var CusotmerSession
     */
    private $session;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * Data constructor.
     * @param Context $context
     * @param StateInterface $inlineTranslation
     * @param Escaper $escaper
     * @param Session $checkoutSession
     * @param DataObject $postObject
     * @param StoreManagerInterface $storeManager
     * @param TransportBuilder $transportBuilder
     * @param CusotmerSession $session
     */
    public function __construct(
        Context $context,
        StateInterface $inlineTranslation,
        Escaper $escaper,
        Session $checkoutSession,
        DataObject $postObject,
        StoreManagerInterface $storeManager,
        TransportBuilder $transportBuilder,
        CusotmerSession $session,
        AttributeData $customerHelper
        
    ) {
        parent::__construct($context);
        $this->inlineTranslation = $inlineTranslation;
        $this->escaper = $escaper;
        $this->checkoutSession = $checkoutSession;
        $this->transportBuilder = $transportBuilder;
        $this->postObject = $postObject;
        $this->logger = $context->getLogger();
        $this->storeManager = $storeManager;
        $this->_customerSession = $session;
        $this->customerHelper = $customerHelper;
    }

    /**
     * @param $postData
     * @return string
     */
    public function sendQuoteInEmail($postData)
    {
        $sender = $this->scopeConfig->getValue(
            'quote/email/sender',
            ScopeInterface::SCOPE_STORE,
            $this->getStoreId()
        );
        $sendToEmail = $postData['email'];
        $senderName = isset($postData['name'])?$postData['name'] : $this->getStore()->getName();
        $comment = isset($postData['message']) ? $postData['message'] : '';
        $quote = $this->checkoutSession->getQuote();
        $this->postObject->setData(['quote' => $quote]);
        $template = !empty($this->getCartEmailTemplate()) ? $this->getCartEmailTemplate() : 'quote_email_template' ;

        try {
            $this->inlineTranslation->suspend();
            $transport = $this->transportBuilder
                ->setTemplateIdentifier($template)
                ->setTemplateOptions(
                    [
                        'area' => \Magento\Framework\App\Area::AREA_FRONTEND,
                        'store' => \Magento\Store\Model\Store::DEFAULT_STORE_ID,
                    ]
                )
                ->setTemplateVars(
                    [
                        'quote' => $this->postObject,
                        'sendername' => $senderName,
                        'message' => $comment,
                    ]
                )
                ->setFrom($sender)
                ->addTo($sendToEmail)
                ->getTransport();
            $transport->sendMessage();
            $this->inlineTranslation->resume();
            $message = 'success';
        } catch (\Exception $e) {
            $message = 'failed';
            $this->logger->alert('Email Cart:' . $e->getMessage());
        }
        return $message;
    }

    /**
     * get Current store id
     */
    public function getStoreId()
    {
        return $this->storeManager->getStore()->getId();
    }

    /**
     * get Current store Info
     */
    public function getStore()
    {
        return $this->storeManager->getStore();
    }

    /**
     * @return mixed
     */
    public function getCartEmailTemplate()
    {
        return $this->scopeConfig->getValue(self::QUOTE_EMAIL_TEMPLATE);
    }

    /**
     * @return bool
     */
    public function isLoggedIn()
    {
        return $this->_customerSession->isLoggedIn();
    }
    
    /**
     * getLoggedinCustomerName
     *
     * @return void
     */
    public function getLoggedinCustomerName()
    {
        if ($this->isLoggedIn())
        {
            return $this->_customerSession->getCustomer()->getName();
        }
        return '';
    }

    /**
     * Check whether is B2B customer or not.
     * 
     * @return boolean
     */
    public function isB2BCustomer()
    {
        return $this->customerHelper->isB2bCustomer();
    }
}