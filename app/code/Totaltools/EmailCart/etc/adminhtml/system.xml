<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="quote">
            <group id="email" translate="label" sortOrder="60" type="select" showInStore="0" showInWebsite="1"
                   showInDefault="1">
                <label>Quote Email</label>
                <field id="template" translate="label" sortOrder="10" type="select" showInStore="0" showInWebsite="1"
                       showInDefault="1">
                    <label>Quote Email</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Template</source_model>
                </field>
                <field id="sender" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1"
                       showInStore="0" canRestore="1">
                    <label>Cart items sender</label>
                    <source_model>Magento\Config\Model\Config\Source\Email\Identity</source_model>
                    <depends>
                        <field id="enabled">1</field>
                    </depends>
                </field>
            </group>
        </section>
         <section id="recaptcha_frontend">
            <group id="type_for">
                <field id="email_cart" translate="label" type="select" sortOrder="180" showInDefault="1"
                       showInWebsite="1" showInStore="0" canRestore="1">
                    <label>Enable for Email Cart Items</label>
                    <source_model>Magento\ReCaptchaAdminUi\Model\OptionSource\Type</source_model>
                </field>
               </group>
        </section>
    </system>
</config>
