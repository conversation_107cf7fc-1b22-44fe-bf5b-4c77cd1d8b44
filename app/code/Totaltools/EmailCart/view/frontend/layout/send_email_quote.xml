<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd" label="Email Quote Items List" design_abstraction="custom">
    <update handle="send_email_quote"/>
    <update handle="emailcart_email_quote_renderers"/>
    <body>
        <block class="Totaltools\EmailCart\Block\Quote\Items" name="emailcart.email.quote.default" template="Totaltools_EmailCart::email/items.phtml" cacheable="false">
            <block class="Magento\Framework\View\Element\RendererList" name="emailcart.email.quote.renderers" as="renderer.list"/>
                        <block class="Totaltools\EmailCart\Block\Quote\Totals" name="quote_totals" template="Totaltools_EmailCart::email/totals.phtml">
                            <arguments>
                                <argument name="label_properties" xsi:type="string">colspan="2"</argument>
                            </arguments>
                            <block class="Magento\Tax\Block\Sales\Order\Tax" name="tax" template="Magento_Tax::order/tax.phtml">
                                <action method="setIsPlaneMode">
                                    <argument name="value" xsi:type="string">1</argument>
                                </action>
                            </block>
                        </block>
        </block>
        <block class="Magento\Framework\View\Element\Template" name="additional.product.info" template="Magento_Theme::template.phtml"/>
    </body>
</page>
