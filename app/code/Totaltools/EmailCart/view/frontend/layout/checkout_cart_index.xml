<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="cart.summary">
            <block class="Totaltools\EmailCart\Block\EmailCart" name="email.cart.items" after="-" template="Totaltools_EmailCart::emailcart.phtml">
                <container name="form.additional.info">
                    <block class="Magento\ReCaptchaUi\Block\ReCaptcha" name="recaptcha" after="-" template="Magento_ReCaptchaFrontendUi::recaptcha.phtml" ifconfig="recaptcha_frontend/type_for/email_cart">
                        <arguments>
                            <argument name="recaptcha_for" xsi:type="string">email_cart</argument>
                            <argument name="jsLayout" xsi:type="array">
                                <item name="components" xsi:type="array">
                                    <item name="recaptcha" xsi:type="array">
                                        <item name="component" xsi:type="string">Magento_ReCaptchaFrontendUi/js/reCaptcha</item>
                                    </item>
                                </item>
                            </argument>
                        </arguments>
                    </block>
                </container>
            </block>
        </referenceContainer>
    </body>
</page>
