<?php

/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

?>
<?php
/**
 * @var \Totaltools\EmailCart\Block\EmailCart $block
 */
?>
<?php
$helper = $this->helper(\Totaltools\EmailCart\Helper\Data::class);
$loggedIn = $helper->isLoggedIn();
$customerName = $helper->getLoggedinCustomerName();

if (!$block->getQuote()->isVirtual() && !$helper->isB2BCustomer()):
?>
    <div class="block quote" id="block-quote" role="tablist" data-collapsible="true" data-mage-init='{"collapsible":{"openedState": "active", "saveState": false}}'>
        <div class="title" data-role="title" role="tab" tabindex="0">
            <strong id="block-quote-heading" role="heading" aria-level="2"><?php /* @escapeNotVerified */ echo __('Share Quote') ?></strong>
        </div>
        <div class="content" data-role="content" aria-labelledby="block-quote-heading" style="display: none" role="tabpanel" aria-hidden="true">
            <form id="quote-coupon-form" action="<?php /* @escapeNotVerified */ echo $block->getEmailCartUrl() ?>" data-mage-init='{"validation": {}}' method="post">
                <div class="fieldset quote">
                    <div class="field">
                        <div class="control">
                            <input type="<?php if (!$loggedIn) {
                                                echo  'text';
                                            } else {
                                                echo 'hidden';
                                            } ?>" value="<?= $customerName ?>" class="input-text required" id="name" name="name" placeholder="Sender Name" />
                        </div>
                    </div>
                    <div class="field">
                        <div class="control">
                            <input type="email" class="input-text required" id="email" name="email" data-validate="{required:true, 'validate-email':true}" placeholder="Email Address" />
                        </div>
                    </div>
                    <div class="field">
                        <div class="control">
                            <textarea name="message" id="message" class="input-box" placeholder="Write your message here"></textarea>
                        </div>
                    </div>
                    <?php echo $this->getChildHtml('form.additional.info'); ?>
                    <div class="actions-toolbar">
                        <div class="primary">
                            <button class="action sendquote primary" id="sendquote" type="submit" value="<?php /* @escapeNotVerified */ echo __('Send Email') ?>">
                                <span><?php /* @escapeNotVerified */ echo __('send_email') ?></span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
<?php endif; ?>

<?php if ($block->getQuote()->isVirtual()): ?>
<style type="text/css">.rewards {display: none !important;}</style>
<?php endif; ?>
