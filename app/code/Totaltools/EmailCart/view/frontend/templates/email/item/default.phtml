<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/** @var $block \Totaltools\EmailCart\Block\Quote\Renderer\Renderer */

/** @var $_item \Magento\Quote\Model\Quote\Item */
$_item = $block->getItem();
$_quote = $_item->getQuote();
$_product = $_item->getProduct();
$productUrl=$_product->getUrlModel()->getUrl($_product);
?>
<tr>
    <td class="item-info<?php if ($block->getItemOptions()): ?> has-extra<?php endif; ?>">
        <p class="product-name"><a href ="<?= $productUrl ?>"><?= $block->escapeHtml($_item->getName()) ?></a></p>
        <p class="sku"><?= /* @escapeNotVerified */  __('SKU') ?>: <?= $block->escapeHtml($_item->getSku()) ?></p>
        <?php if ($_item->getChildren()): ?>
            <dl class="item-options">
                <?php foreach ($_item->getChildren() as $option): ?>
                    <dt><strong><em><?= /* @escapeNotVerified */  $option->getName() ?></em></strong></dt>
                    <dd>
                        <?= /* @escapeNotVerified */  nl2br($option['value']) ?>
                    </dd>
                <?php endforeach; ?>
            </dl>
        <?php endif; ?>
        <?php $addInfoBlock = $block->getProductAdditionalInformationBlock(); ?>
        <?php if ($addInfoBlock) :?>
            <?= $addInfoBlock->setItem($_item)->toHtml() ?>
        <?php endif; ?>
        <?= $block->escapeHtml($_item->getDescription()) ?>
    </td>
    <td class="item-qty"><?= /* @escapeNotVerified */  $_item->getQty() * 1 ?></td>
    <td class="item-price">
        <?= /* @escapeNotVerified */  $_item->getPrice() ?>
    </td>
</tr>
<?php if ($_item->getGiftMessageId() && $_giftMessage = $this->helper('Magento\GiftMessage\Helper\Message')->getGiftMessage($_item->getGiftMessageId())): ?>
    <tr>
        <td colspan="3" class="item-extra">
            <table class="message-gift">
                <tr>
                    <td>
                        <h3><?= /* @escapeNotVerified */  __('Gift Message') ?></h3>
                        <strong><?= /* @escapeNotVerified */  __('From:') ?></strong> <?= $block->escapeHtml($_giftMessage->getSender()) ?>
                        <br /><strong><?= /* @escapeNotVerified */  __('To:') ?></strong> <?= $block->escapeHtml($_giftMessage->getRecipient()) ?>
                        <br /><strong><?= /* @escapeNotVerified */  __('Message:') ?></strong>
                        <br /><?= $block->escapeHtml($_giftMessage->getMessage()) ?>
                    </td>
                </tr>
            </table>
        </td>
    </tr>
<?php endif; ?>
