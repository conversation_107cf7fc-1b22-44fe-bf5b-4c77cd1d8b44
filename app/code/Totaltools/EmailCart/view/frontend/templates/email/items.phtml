<?php
/**
 * @var $block \Totaltools\EmailCart\Block\Quote\Items
 */
?>
<?php
$quote = $block->getQuote();
?>
<?php if ($quote->getId()): ?>
    <?php $_items = $block->getItems(); ?>
    <table class="email-items">
        <thead>
        <tr>
            <th class="item-info">
                <?= /* @escapeNotVerified */  __('Items') ?>
            </th>
            <th class="item-qty">
                <?= /* @escapeNotVerified */  __('Qty') ?>
            </th>
            <th class="item-price">
                <?= /* @escapeNotVerified */  __('Price') ?>
            </th>
        </tr>
        </thead>
        <?php foreach ($_items as $_item): ?>
            <?php if (!$_item->getParentItem()) : ?>
                <tbody>
                <?= $block->getItemHtml($_item) ?>
                </tbody>
            <?php endif; ?>
        <?php endforeach; ?>
        <tfoot class="quote-totals">
        <?= $block->getChildHtml('quote_totals') ?>
        </tfoot>
    </table>
    <?php if ($this->helper('Magento\GiftMessage\Helper\Message')->isMessagesAllowed('quote', $quote, $quote->getStore()) && $quote->getGiftMessageId()): ?>
        <?php $_giftMessage = $this->helper('Magento\GiftMessage\Helper\Message')->getGiftMessage($quote->getGiftMessageId()); ?>
        <?php if ($_giftMessage): ?>
            <br />
            <table class="message-gift">
                <tr>
                    <td>
                        <h3><?= /* @escapeNotVerified */  __('Gift Message for this Quote') ?></h3>
                        <strong><?= /* @escapeNotVerified */  __('From:') ?></strong> <?= $block->escapeHtml($_giftMessage->getSender()) ?>
                        <br /><strong><?= /* @escapeNotVerified */  __('To:') ?></strong> <?= $block->escapeHtml($_giftMessage->getRecipient()) ?>
                        <br /><strong><?= /* @escapeNotVerified */  __('Message:') ?></strong>
                        <br /><?= $block->escapeHtml($_giftMessage->getMessage()) ?>
                    </td>
                </tr>
            </table>
        <?php endif; ?>
    <?php endif; ?>
<?php endif; ?>