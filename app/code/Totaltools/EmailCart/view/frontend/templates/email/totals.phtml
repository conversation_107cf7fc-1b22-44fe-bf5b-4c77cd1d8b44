<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */

// @codingStandardsIgnoreFile

/**
 * @var $block \Totaltools\EmailCart\Block\Quote\Totals
 * @see \Totaltools\EmailCart\Block\Quote\Totals
 */
?>
<?php foreach ($block->getTotals() as $_code => $_total): ?>
    <?php $skip = ['giftwrapping', 'shipping', 'customerbalance'];
    if (in_array($_code, $skip)) continue; ?>
    <?php if ($_total->getBlockName()): ?>
        <?= $block->getChildHtml($_total->getBlockName(), false) ?>
    <?php else:?>
    <tr class="<?= /* @escapeNotVerified */ $_code ?>">
        <th  style="<?= /* @escapeNotVerified */ $block->getLabelProperties() ?>" scope="row"></th>
        <th  style="<?= /* @escapeNotVerified */ $block->getLabelProperties() ?>" scope="row">
            <?php if ($_total->getStrong()):?>
            <strong><?= $block->escapeHtml($_total->getTitle()) ?></strong>
            <?php else:?>
            <?= $block->escapeHtml($_total->getTitle()) ?>
            <?php endif?>
        </th>
        <td <?= /* @escapeNotVerified */ $block->getValueProperties() ?> style="font-family: 'Open Sans', sans-serif; vertical-align: top;  padding: 10px; text-align: right; background-color: none; padding-top: 5px; padding-bottom: 5px;" data-th="<?= $block->escapeHtml($_total->getTitle()) ?>">
            <?php if ($_total->getStrong()):?>
            <strong><?= /* @escapeNotVerified */ $this->helper('Magento\Checkout\Helper\Data')->formatPrice($_total->_total) ?></strong>
            <?php else:?>
          <?= /* @escapeNotVerified */ $this->helper('Magento\Checkout\Helper\Data')->formatPrice($_total->getValue()) ?>
            <?php endif?>
        </td>
    </tr>
    <?php endif?>
<?php endforeach?>

