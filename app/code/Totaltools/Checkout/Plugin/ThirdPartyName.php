<?php


namespace Totaltools\Checkout\Plugin;


class ThirdPartyName
{
    protected $helper;

    public function __construct (
        \Totaltools\Checkout\Helper\Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        array  $jsLayout
    ) {
        if (!$this->helper->isThirdPartyPickupActive()) {
            return $jsLayout;
        }

        $jsLayout['components']
        ['checkout']
        ['children']
        ['steps']
        ['children']
        ['shipping-step']
        ['children']
        ['shippingAddress']
        ['children']
        ['shippingAdditional']
        ['children']
        ['pickup']
        ['children']
        ['pickup-options']
        ['children']
        ['third_party_name']= [
            'component' => 'Magento_Ui/js/form/element/abstract',
            'config' => [
                'customScope' => 'shippingAddress',
                'template' => 'ui/form/field',
                'elementTmpl' => 'ui/form/element/input',
                'id' => 'third-party-name',
                'additionalClasses' => 'pickup-options-name'
            ],
            'dataScope' => 'shippingAddress.third_party_name',
            'label' => 'Third party details',
            'provider' => 'checkoutProvider',
            'visible' => true,
            'validation' => ['validate-pickup' => true],
            'sortOrder' => 201,
            'id' => 'third-party-name'
        ];

        return $jsLayout;
    }
}
