<?php
/**
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2021 Totaltools (https://www.totaltools.com.au)
 * @package Totaltools_Checkout
 */

namespace Totaltools\Checkout\Plugin;

use Magento\Framework\UrlInterface;

class Link
{
    /**
     * @var \Totaltools\Checkout\Helper\Data
     */
    private \Totaltools\Checkout\Helper\Data $helper;

    /**
     * @var UrlInterface
     */
    private UrlInterface $urlBuilder;

    /**
     * Link constructor.
     * @param \Totaltools\Checkout\Helper\Data $helper
     * @param UrlInterface $urlBuilder
     */
    public function __construct(
        \Totaltools\Checkout\Helper\Data $helper,
        UrlInterface $urlBuilder
    ) {
        $this->helper = $helper;
        $this->urlBuilder = $urlBuilder;
    }

    /**
     * @param \Magento\Checkout\Block\Onepage\Link $subject
     * @param $result
     * @return mixed
     */
    public function afterGetCheckoutUrl(\Magento\Checkout\Block\Onepage\Link $subject, $result)
    {
        if (!$this->helper->isOnepageEnable()) {
            return $this->urlBuilder->getUrl('checkout/steps');
        } else {
            return $result;
        }
    }
}
