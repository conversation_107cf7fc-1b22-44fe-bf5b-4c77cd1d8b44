<?php

namespace Totaltools\Checkout\Plugin;

class ThirdPartyPickup
{
    /**
     * @var \Totaltools\Checkout\Helper\Data
     */
    protected $helper;

    public function __construct(
        \Totaltools\Checkout\Helper\Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        array  $jsLayout
    ) {
        if (!$this->helper->isThirdPartyPickupActive()) {
            return $jsLayout;
        }

        $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shippingAdditional']['children']['pickup']['children']['pickup-options']['children']['third_party_pickup'] = [
            'component' => 'Totaltools_Checkout/js/view/form/element/dependent',
            'config' => [
                'customScope' => 'shippingAddress',
                'template' => 'ui/form/field',
                'elementTmpl' => 'ui/form/element/checkbox',
                'id' => 'third-party-pickup',
                'dependent' => 'third_party_name'
            ],
            'dataScope' => 'shippingAddress.third_party_pickup',
            'label' => '',
            'description' => 'Yes - Allow someone to pickup my order',
            'provider' => 'checkoutProvider',
            'visible' => true,
            'validation' => [],
            'sortOrder' => 200,
            'id' => 'third-party-pickup'
        ];

        return $jsLayout;
    }
}
