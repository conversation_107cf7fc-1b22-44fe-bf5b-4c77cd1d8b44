<?php
/**
 * Sidebar
 *
 * @category  Totaltools
 * @package   Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright 2022 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */
namespace Totaltools\Checkout\Plugin\Cart;

use Totaltools\Checkout\Helper\TotalToolsConfig;

class Sidebar
{
    /**
     * @var TotalToolsConfig
     */
    private $totalToolsConfig;

    protected $quote;
    
    /**
     * __construct
     * 
     * @param TotalToolsConfig $totalToolsConfig
     *
     * @return void
     */
    public function __construct(TotalToolsConfig $totalToolsConfig) 
    {
            $this->totalToolsConfig = $totalToolsConfig;
    }

    /**
     * Modify checkout config data.
     *
     * @param \Magento\Checkout\Block\Cart\Sidebar $subject,
     * @param array $config
     *
     * @return array
     */
    public function afterGetConfig(\Magento\Checkout\Block\Cart\Sidebar $subject, $config)
    {
        $config['freeShippingLimit'] = $this->totalToolsConfig->getConfigValue('checkout/total_tools/free_delivery_starts_at');
        return $config;
    }
}
