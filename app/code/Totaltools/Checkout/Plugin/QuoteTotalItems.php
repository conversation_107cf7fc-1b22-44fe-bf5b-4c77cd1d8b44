<?php

namespace Totaltools\Checkout\Plugin;

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * @version     1.0.1
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   Copyright (c) 2020-21 Totaltools. <https://totaltools.com.au>
 */

use Magento\Quote\Api\Data\TotalsItemInterface;
use Magento\Catalog\Model\Product;
use Psr\Log\LoggerInterface;

class QuoteTotalItems
{
    /**
     * @var int - Weight threshold to check for overweight product.
     */
    const WEIGHT_THRESHOLD = 20;

    /**
     * @var \Magento\Quote\Api\Data\CartItemInterfaceFactory
     */
    private $cartItemExtensionFactory;

    /**
     * @var \Magento\Quote\Model\Quote\ItemFactory
     */
    private $itemFactory;

    /**
     * @var \Magento\Catalog\Model\ProductRepository
     */
    private $productRepository;

    /**
     * @var LoggerInterface
     */
    private $logger;

    /**
     * @var array - quote items cache to avoid hitting factory.
     */
    private $quoteItemsCache = [];

    /**
     * @var array - product cache to avoid hitting repository.
     */
    private $productCache = [];

    /**
     * @param \Magento\Quote\Api\Data\TotalsItemExtensionFactory $cartItemExtension
     * @param \Magento\Quote\Model\Quote\ItemFactory $itemFactory,
     * @param \Magento\Catalog\Model\ProductRepository $productRepository
     * @param LoggerInterface $logger
     */
    public function __construct(
        \Magento\Quote\Api\Data\TotalsItemExtensionFactory $cartItemExtension,
        \Magento\Quote\Model\Quote\ItemFactory $itemFactory,
        \Magento\Catalog\Model\ProductRepository $productRepository,
        LoggerInterface $logger
    ) {
        $this->cartItemExtensionFactory = $cartItemExtension;
        $this->itemFactory = $itemFactory;
        $this->productRepository = $productRepository;
        $this->logger = $logger;
    }

    /**
     * @param \Magento\Quote\Api\CartTotalRepositoryInterface $cartTotalRepository
     * @param \Magento\Quote\Model\Cart\Totals $quoteTotals
     * @param int $cartId
     * @return \Magento\Quote\Model\Cart\Totals
     */
    public function afterGet($cartTotalRepository, $quoteTotals, $cartId)
    {
        $items = $quoteTotals->getItems();

        if (count($items)) {
            $itemsExtended = [];

            foreach ($items as $index => $item) {
                $extAttributes = $item->getExtensionAttributes();

                if ($extAttributes == null) {
                    $extAttributes = $this->cartItemExtensionFactory->create();
                }

                $extAttributes->setShippingDangerous($this->getShippingDangerous($item));
                $extAttributes->setOverweight($this->getOverweight($item));
                $extAttributes->setProductId($this->getProductId($item));
                $extAttributes->setSku($this->getProductSku($item));
                $extAttributes->setKnifeCompliance($this->getKnifeCompliance($item));

                if (is_string($shippingLabel = $this->getShippingLabel($item))) {
                    $extAttributes->setShippingLabel($shippingLabel);
                }

                $item->setExtensionAttributes($extAttributes);

                $itemsExtended[$index] = $item;
            }

            $quoteTotals->setItems($itemsExtended);
        }

        return $quoteTotals;
    }

    /**
     * @param \Magento\Quote\Api\Data\TotalsItemInterface $item
     * @throws \Exception
     * @return string|null
     */
    private function getShippingLabel($item)
    {
        $shippingLabel = null;

        try {
            $product = $this->getProduct($item);

            if ($product instanceof \Magento\Catalog\Model\Product) {
                $shippingLabel = $product->getResource()
                    ->getAttribute('shipping_label')
                    ->getFrontend()
                    ->getValue($product);
            }
        } catch (\Exception $e) {
            return null;
        }

        return $shippingLabel;
    }

    /**
     * @param \Magento\Quote\Api\Data\TotalsItemInterface $totalsItem
     * @throws \Exception
     * @return boolean
     */
    private function getShippingDangerous($item)
    {
        $isDangerousItem = false;

        try {
            $product = $this->getProduct($item);

            if ($product instanceof \Magento\Catalog\Model\Product) {
                $isDangerousItem = $product->getResource()
                    ->getAttribute(\Totaltools\Storelocator\Model\Store::SHIPPING_DANGEROUS_KEY)
                    ->getFrontend()
                    ->getValue($product);
            }
        } catch (\Exception $e) {
            return false;
        }

        return $isDangerousItem == 'Yes';
    }

    /**
     * Get knife compliance value for the item
     * 
     * @param \Magento\Quote\Api\Data\TotalsItemInterface $item
     * @return boolean
     */
    private function getKnifeCompliance($item)
    {
        $isKnifeCompliance = __('No');

        try {
            $product = $this->getProduct($item);

            if ($product instanceof \Magento\Catalog\Model\Product) {
                $isKnifeCompliance = $product->getResource()
                    ->getAttribute(\Totaltools\Storelocator\Model\Store::KNIFE_COMPLIANCE_KEY)
                    ->getFrontend()
                    ->getValue($product);
            }
        } catch (\Exception $e) {
            $this->logger->error('Error getting knife compliance: ' . $e->getMessage(), [
                'sku' => $this->getProductSku($item),
                'exception' => $e
            ]);
            return false;
        }

        return $isKnifeCompliance->getText() == 'Yes';
    }

    /**
     * @param \Magento\Quote\Api\Data\TotalsItemInterface $totalsItem
     * @throws \Exception
     * @return boolean
     */
    private function getOverweight($item)
    {
        $isOverweight = false;

        try {
            $product = $this->getProduct($item);

            if ($product instanceof \Magento\Catalog\Model\Product) {
                $isOverweight = $product->getWeight() >= self::WEIGHT_THRESHOLD;
            }
        } catch (\Exception $e) {
            return false;
        }

        return $isOverweight;
    }

    /**
     * Gets the product_id for given quote item.
     *
     * @param \Magento\Quote\Api\Data\TotalsItemInterface $item
     * @return int - product id
     */
    private function getProductId($item)
    {
        $itemId = $item->getItemId();

        if (isset($this->productCache[$itemId])) {
            return $this->quoteItemsCache[$itemId]->getProductId();
        }

        $quoteItem = $this->itemFactory->create()->load($itemId);
        $this->quoteItemsCache[$itemId] = $quoteItem;

        return (int) $quoteItem->getProductId();
    }

    /**
     * Gets the product sku for given quote item.
     *
     * @param \Magento\Quote\Api\Data\TotalsItemInterface $item
     * @return string
     */
    private function getProductSku($item)
    {
        $itemId = $item->getItemId();

        if (isset($this->productCache[$itemId])) {
            return $this->quoteItemsCache[$itemId]->getSku();
        }

        $quoteItem = $this->itemFactory->create()->load($itemId);
        $this->quoteItemsCache[$itemId] = $quoteItem;

        return $quoteItem->getSku();
    }

    /**
     * Gets the Product instance for given quote item.
     *
     * @param \Magento\Quote\Api\Data\TotalsItemInterface $item
     * @return \Magento\Catalog\Model\Product
     */
    private function getProduct($item)
    {
        $productId = $this->getProductId($item);

        if (isset($this->productCache[$productId])) {
            return $this->productCache[$productId];
        }

        $product = $this->productRepository->getById($productId);
        $this->productCache[$productId] = $product;

        return $product;
    }
}
