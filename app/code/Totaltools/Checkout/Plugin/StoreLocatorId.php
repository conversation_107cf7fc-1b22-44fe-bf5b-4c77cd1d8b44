<?php

namespace Totaltools\Checkout\Plugin;

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

class StoreLocatorId
{
    /**
     * @var \Totaltools\Checkout\Helper\Data
     */
    protected $helper;

    /**
     * @param \Totaltools\Checkout\Helper\Data $helper
     */
    public function __construct(
        \Totaltools\Checkout\Helper\Data $helper
    ) {
        $this->helper = $helper;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        array  $jsLayout
    ) {
        $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shippingAdditional']['children']['store-location']['children']['store-fields']['children']['storelocator_id'] = [
            'component' => 'Magento_Ui/js/form/element/abstract',
            'config' => [
                'customScope' => 'shippingAddress',
                'template' => 'ui/form/field',
                'elementTmpl' => 'ui/form/element/input',
                'id' => 'storelocator-id'
            ],
            'dataScope' => 'shippingAddress.storelocator_id',
            'label' => 'Select your pickup store',
            'description' => '',
            'placeholder' => 'Enter Suburb or Postcode',
            'provider' => 'checkoutProvider',
            'visible' => true,
            'validation' => [],
            'sortOrder' => 10,
            'id' => 'storelocator-id'
        ];

        return $jsLayout;
    }
}
