<?php

namespace Totaltools\Checkout\Plugin;

use Magento\Checkout\Model\PaymentDetailsFactory;
use Magento\Payment\Api\Data\PaymentMethodInterfaceFactory;
use Magento\Quote\Api\CartTotalRepositoryInterface;
use Magento\Quote\Api\PaymentMethodManagementInterface;

class QuoteThirdPartyPickup
{
    /**
     * @var \Magento\Quote\Model\QuoteRepository
     */
    protected $quoteRepository;

    /**
     * @var \Totaltools\Checkout\Helper\Data
     */
    protected $helper;

    /**
     * @var PaymentDetailsFactory
     */
    private $paymentDetailsFactory;
    /**
     * @var PaymentMethodManagementInterface
     */
    private $paymentMethodManagement;
    /**
     * @var CartTotalRepositoryInterface
     */
    private $cartTotalRepository;

    /**
     * QuoteThirdPartyPickup constructor.
     * @param \Magento\Quote\Model\QuoteRepository $quoteRepository
     * @param \Totaltools\Checkout\Helper\Data $helper
     * @param PaymentDetailsFactory $paymentDetailsFactory
     * @param PaymentMethodManagementInterface $paymentMethodManagement
     * @param CartTotalRepositoryInterface $cartTotalRepository
     */
    public function __construct(
        \Magento\Quote\Model\QuoteRepository $quoteRepository,
        \Totaltools\Checkout\Helper\Data $helper,
        PaymentDetailsFactory $paymentDetailsFactory,
        PaymentMethodManagementInterface $paymentMethodManagement,
        CartTotalRepositoryInterface $cartTotalRepository
    ) {
        $this->quoteRepository = $quoteRepository;
        $this->helper = $helper;
        $this->paymentDetailsFactory = $paymentDetailsFactory;
        $this->paymentMethodManagement = $paymentMethodManagement;
        $this->cartTotalRepository = $cartTotalRepository;
    }

    /**
     * @param \Magento\Checkout\Model\ShippingInformationManagement $subject
     * @param $cartId
     * @param \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
     */
    public function beforeSaveAddressInformation(
        \Magento\Checkout\Model\ShippingInformationManagement $subject,
        $cartId,
        \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
    ) {
        if(!$this->helper->isThirdPartyPickupActive()) {
            return;
        }

        $extensionAttributes = $addressInformation->getExtensionAttributes();

        if (empty($extensionAttributes)) {
            return;
        }

        $thirdPartyPickup = $extensionAttributes->getThirdPartyPickup();
        $thirdPartyName = $extensionAttributes->getThirdPartyName();

        $quote = $this->quoteRepository->getActive($cartId);

        if(isset($thirdPartyPickup)) {
            $quote->setThirdPartyPickup($thirdPartyPickup);
        }

        if(isset($thirdPartyName)) {
            $quote->setThirdPartyName($thirdPartyName);
        }

       return [$cartId, $addressInformation];
    }
}
