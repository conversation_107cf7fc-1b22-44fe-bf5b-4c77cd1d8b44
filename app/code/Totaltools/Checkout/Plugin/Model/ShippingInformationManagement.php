<?php

namespace Totaltools\Checkout\Plugin\Model;

/**
 * @package Totaltools_Checkout
 * <AUTHOR> Dev Team
 * @copyright 2019 © Totaltools <https://www.totaltools.com.au>
 */

use Magento\Framework\Exception\StateException;
use Totaltools\Storelocator\Helper\Data as StoreLocatorHelper;
use Totaltools\Storelocator\Helper\Checkout\Data as CheckoutHelper;
use Totaltools\Storelocator\Model\StoreRepository;
use Magestore\Storelocator\Model\Status;
use Magento\Framework\Phrase;
use Totaltools\Storelocator\Model\Store;
use Magento\Quote\Model\QuoteRepository;

class ShippingInformationManagement
{
    /**
     * Shipping country Id
     */
    const COUNTRY_ID = 'AU';

    /**
     * Stock code for out of stock items
     */
    const OUT_OF_STOCK_CODE = 5;

    /**
     * Click and collect method
     */
    const CLICK_AND_COLLECT = 'shippitcc_shippitcc';

    /**
     * @var StoreLocatorHelper
     */
    protected $storeLocatorHelper;

    /**
     * @var StoreRepository
     */
    protected $storeRepository;

    /**
     * @var string
     */
    protected $stockStatusMessage = '';

    /**
     * @var CheckoutHelper
     */
    protected $checkoutHelper;

    /**
     * @var QuoteRepository
     */
    protected $quoteRepository;

    /**
     * @param StoreLocatorHelper $storeLocatorHelper
     * @param StoreRepository $storeRepository
     * @param CheckoutHelper $checkoutHelper
     * @param QuoteRepository $quoteRepository
     */
    public function __construct(
        StoreLocatorHelper $storeLocatorHelper,
        StoreRepository $storeRepository,
        CheckoutHelper $checkoutHelper,
        QuoteRepository $quoteRepository
    ) {
        $this->storeLocatorHelper = $storeLocatorHelper;
        $this->storeRepository = $storeRepository;
        $this->checkoutHelper = $checkoutHelper;
        $this->quoteRepository = $quoteRepository;
    }

    /**
     * @param \Magento\Checkout\Model\ShippingInformationManagement $subject,
     * @param callable $proceed,
     * @param int $cartId,
     * @param \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
     * @return callable
     * @throws \Magento\Framework\Exception\StateException
     */
    public function aroundSaveAddressInformation(
        \Magento\Checkout\Model\ShippingInformationManagement $subject,
        callable $proceed,
        $cartId,
        \Magento\Checkout\Api\Data\ShippingInformationInterface $addressInformation
    ) {
        $shippingAddress = $addressInformation->getShippingAddress();
        $streetAddress = $shippingAddress->getStreetFull();
        $compnayAddress = $shippingAddress->getCompany();
        $country = $shippingAddress->getCountryId();
        $shippingMethod = $addressInformation->getShippingCarrierCode() . '_' . $addressInformation->getShippingMethodCode();
        $storeLocatorId = $addressInformation->getExtensionAttributes()->getStoreLocatorId() ?: $this->checkoutHelper->getSessionStoreId();
        $postCode = $shippingAddress->getPostcode();

        // if ((!$this->validateAddress($streetAddress) || !$this->validateAddress($compnayAddress)) && $shippingMethod != self::CLICK_AND_COLLECT) {
        //     throw new StateException(__('We cannot ship to PO boxes or parcel lockers.'));
        // }

        if (!empty($storeLocatorId) && $shippingMethod == self::CLICK_AND_COLLECT && !$this->storeHasEnabledCollection($storeLocatorId)) {
            $this->checkoutHelper->setSessionStoreId(null);
            throw new StateException(__('Store collection is not allowed for this store. Please select another store.'));
        }

        if ($country != self::COUNTRY_ID && $shippingMethod != self::CLICK_AND_COLLECT) {
            throw new StateException(__('We cannot ship to an address outside Australia.'));
        }

        if ($this->hasKnifeComplianceItems($cartId)) {
            throw new StateException(__('Delivery is not available for cart containing knife compliance items.'));
        }

        if (strlen($streetAddress) >= 3 && $this->hasOutOfStockItems($storeLocatorId, $shippingMethod, $postCode) && !empty($this->stockStatusMessage)) {
            throw new StateException(__($this->stockStatusMessage));
        }        

        $result = $proceed($cartId, $addressInformation);

        return $result;
    }

    /**
     * @param string $address
     * @return boolean
     */
    private function validateAddress($address)
    {
        $valid = true;
        $patterns = [
            '/(?:G?)?(?:P(?:ost(?:al)?)?[\.\-\s]*(?:(?:O(?:ffice)?[\.\-\s]*)?B(?:ox|in|\b|\d)|o(?:ffice|\b)(?:[-\s]*\d)|code)|box[-\s\b]*\d)/i',
            '/\bP(ost|ostal)?([ \.]*O(ffice)?)?([ \.]*Box)?\b/i',
            '/(?:^|\W)parcel (locker|collect)(?:$|\W)/i',
            '/(?:^|\W)locked (bag|bags)(?:$|\W)/i',
            '/(?:^|\W)private (bag|bags|locker|lockers)(?:$|\W)/i'
        ];

        foreach ($patterns as $pattern) {
            if (preg_match($pattern, (string) $address)) {
                $valid = false;
                break;
            }
        }

        return $valid;
    }

    /**
     * @param string $storeLocatorId
     * @param string $shippingMethod
     * @param string $postCode
     * @return bool
     */
    protected function hasOutOfStockItems($storeLocatorId, $shippingMethod, $postCode)
    {
        $result = false;
        $stockAvailabilityMessage = $this->storeLocatorHelper->getStockAvailabilityMessage(
            $storeLocatorId,
            $shippingMethod,
            null,
            $postCode
        );

        foreach ($stockAvailabilityMessage as $stock) {
            if (isset($stock['code']) && $stock['code'] == self::OUT_OF_STOCK_CODE) {
                $result = true;
                $cartMessage = '';

                if (isset($stock['cart_message'])) {
                    $cartMessage = $stock['cart_message'] instanceof Phrase ? $stock['cart_message']->getText() : $stock['cart_message'];
                }

                $this->stockStatusMessage =  $cartMessage;
                break;
            }
        }

        return $result;
    }

    /**
     * @param string $storeLocatorId
     * @return bool
     */
    protected function storeHasEnabledCollection($storeLocatorId)
    {
        try {
            $store = $this->storeRepository->getById($storeLocatorId);

            return (bool) $store &&
                $store->getId() &&
                (int) $store->getStatus() === Status::STATUS_ENABLED &&
                (int) $store->getIsVisible() === 1 &&
                (int) $store->getAllowStoreCollection() === 1;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @param string $cartId
     * @return bool
     */
    protected function hasKnifeComplianceItems($cartId)
    {
        $quote = $this->quoteRepository->get($cartId);

        if ($quote->getIsVirtual()) {
            return false;
        }

        foreach ($quote->getAllVisibleItems() as $item) {
            $product = $item->getProduct();
            $knifeCompliance = (bool) $product->getData('knife_compliance');

            if ($knifeCompliance) {
                return true;
            }
        }

        return false;
    }

}
