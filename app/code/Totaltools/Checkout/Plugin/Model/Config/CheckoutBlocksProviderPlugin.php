<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Checkout\Plugin\Model\Config;

use Amasty\CheckoutCore\Api\CheckoutBlocksProviderInterface;

class CheckoutBlocksProviderPlugin
{
    /**
     * Adds additinal blocks title translations.
     */
    public function afterGetDefaultBlockTitles(CheckoutBlocksProviderInterface $subject, array $result): array
    {
        $result['email_address']    = __('Email Address');
        $result['billing_address']  = __('Billing Address');
        $result['street_address']   = __('Street Address');

        return $result;
    }
}
