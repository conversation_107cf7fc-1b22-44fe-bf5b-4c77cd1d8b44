<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2025 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Checkout\Plugin\Model;

use Magento\Framework\Exception\StateException;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Totaltools\Checkout\Helper\ShippingConfig;

class PlaceOrderPlugin
{
    /**
     * Click and collect method
     */
    const CLICK_AND_COLLECT = 'shippitcc_shippitcc';

    /**
     * Config path for strict shipping country check
     */
    const XML_PATH_STRICT_SHIPPING_COUNTRY_CHECK = 'checkout/place_order_checks/strict_shipping_country_check';

    /**
     * @var CartRepositoryInterface
     */
    private $quoteRepository;

    /**
     * @var ShippingConfig
     */
    private $shippingConfig;

    /**
     * @param CartRepositoryInterface $quoteRepository
     * @param ShippingConfig $shippingConfig
     */
    public function __construct(
        CartRepositoryInterface $quoteRepository,
        ShippingConfig $shippingConfig
    ) {
        $this->quoteRepository = $quoteRepository;
        $this->shippingConfig = $shippingConfig;
    }

    /**
     * Validate shipping country before placing order
     *
     * @param \Magento\Checkout\Model\PaymentInformationManagement $subject
     * @param int $cartId
     * @param \Magento\Quote\Api\Data\PaymentInterface $paymentMethod
     * @param \Magento\Quote\Api\Data\AddressInterface|null $billingAddress
     * @return void
     * @throws StateException
     */
    public function beforeSavePaymentInformationAndPlaceOrder(
        \Magento\Checkout\Model\PaymentInformationManagement $subject,
        $cartId,
        \Magento\Quote\Api\Data\PaymentInterface $paymentMethod,
        \Magento\Quote\Api\Data\AddressInterface $billingAddress = null
    ) {
        if (!$this->shippingConfig->isStrictShippingCountryCheckEnabled()) {
            return;
        }

        $quote = $this->quoteRepository->getActive($cartId);

        if ($quote->isVirtual()) {
            return;
        }

        $shippingAddress = $quote->getShippingAddress();
        $country = $shippingAddress->getCountryId();
        $shippingMethod = $shippingAddress->getShippingMethod();
        $allowedCountries = $this->shippingConfig->getAllowedCountries();

        if ($country && $shippingMethod && !in_array($country, $allowedCountries) && $shippingMethod != self::CLICK_AND_COLLECT) {
            $countries = implode(', ', $allowedCountries);
            throw new StateException(__('We cannot ship to an address outside %1.', $countries));
        }
    }
}
