<?php

namespace Totaltools\Checkout\Plugin\Model\Cart;

use Magento\Quote\Api\CartItemRepositoryInterface;
use Magento\Checkout\Model\Cart\ImageProvider;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Catalog\Helper\Image as ImageHelper;
use Magento\Catalog\Model\Product\Configuration\Item\ItemResolverInterface;

class ImageProviderPlugin
{
    const LABELED_IMAGE_ENABLED = 'catalog/labeled_image/enabled';

    /**
     * @var CartItemRepositoryInterface
     */
    protected $itemRepository;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var ImageHelper
     */
    private $imageHelper;

    /**
     * @var ItemResolverInterface
     */
    private $itemResolver;

    /**
     * ImageDataConfigProvider contstructor
     *
     * @param CartItemRepositoryInterface $itemRepository
     * @param CheckoutSession $checkoutSession
     * @param ScopeConfigInterface $scopeConfig
     * @param ImageHelper $imageHelper
     * @param ItemResolverInterface $itemResolver
     */
    public function __construct(
        CartItemRepositoryInterface $itemRepository,
        ScopeConfigInterface $scopeConfig,
        ImageHelper $imageHelper,
        ItemResolverInterface $itemResolver
    ) {
        $this->itemRepository = $itemRepository;
        $this->scopeConfig = $scopeConfig;
        $this->imageHelper = $imageHelper;
        $this->itemResolver = $itemResolver;
    }

    /**
     * @param ImageProvider $subject
     * @param array $ouput
     * @param int $cartId
     */
    public function afterGetImages(ImageProvider $subject, $output, $cartId)
    {
        if ($this->scopeConfig->isSetFlag(static::LABELED_IMAGE_ENABLED)) {
            $output = $this->getImageData($cartId);
        }

        return $output;
    }

    protected function getImageData($cartId): array
    {
        $itemData = [];
        $items = $this->itemRepository->getList($cartId);

        /** @var \Magento\Quote\Model\Quote\Item $cartItem */
        foreach ($items as $cartItem) {
            $itemData[$cartItem->getItemId()] = $this->getProductImageData($cartItem);
        }

        return $itemData;
    }

    private function getProductImageData($cartItem): array
    {
        $product = $this->itemResolver->getFinalProduct($cartItem);
        $labeledImg = $product->getLabeledImage();
        $imageHelper = $this->imageHelper->init($product, 'mini_cart_product_thumbnail');

        if (!empty($labeledImg) && $labeledImg != 'no_selection') {
            $imageHelper->setImageFile($labeledImg);
        }

        $imageData = [
            'src' => $imageHelper->getUrl(),
            'alt' => $imageHelper->getLabel(),
            'width' => $imageHelper->getWidth(),
            'height' => $imageHelper->getHeight(),
        ];

        return $imageData;
    }
}
