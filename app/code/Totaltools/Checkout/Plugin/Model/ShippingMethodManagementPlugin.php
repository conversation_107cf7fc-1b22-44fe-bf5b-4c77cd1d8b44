<?php
namespace Totaltools\Checkout\Plugin\Model;

use Magento\Framework\Exception\InputException;
use Magento\Framework\Stdlib\DateTime\TimezoneInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Quote\Api\CartRepositoryInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\ShippingMethodManagement;
use Magento\Catalog\Model\Product;
use Psr\Log\LoggerInterface;
use Totaltools\Checkout\Helper\Data;
use Totaltools\Storelocator\Model\Store;

class ShippingMethodManagementPlugin
{
    const ON_DEMAND = 'ondemand';

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var \Totaltools\Storelocator\Model\StoreInventoryService
     */
    protected $storeInventoryService;

    /**
     * @var CartRepositoryInterface
     */
    protected $quoteRepository;

    /**
     * @var \Totaltools\Storelocator\Helper\Checkout\Data
     */
    protected $storeLocatorCheckoutHelper;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $storeRepository;

    /**
     * @var LoggerInterface
     */
    protected $logger;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $checkoutSession;

    /**
     * @var \Totaltools\Geo\Model\GeoLocateModel
     */
    protected $geoLocateModel;

    /**
     * @var TimezoneInterface
     */
    protected $timezoneInterface;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * @param LoggerInterface $logger
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * @param \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService
     * @param CartRepositoryInterface $quoteRepository
     * @param \Totaltools\Storelocator\Helper\Checkout\Data $checkoutData
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Totaltools\Geo\Model\GeoLocateModel $geoLocateModel
     * @param Data $helper
     * @param TimezoneInterface $timezoneInterface
     * @param ManagerInterface $messageManager
     */
    public function __construct(
        LoggerInterface $logger,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService,
        CartRepositoryInterface $quoteRepository,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutData,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Checkout\Model\Session $checkoutSession,
        \Totaltools\Geo\Model\GeoLocateModel $geoLocateModel,
        Data $helper,
        TimezoneInterface $timezoneInterface,
        ManagerInterface $messageManager
    ) {
        $this->scopeConfig = $scopeConfig;
        $this->storeInventoryService = $storeInventoryService;
        $this->quoteRepository = $quoteRepository;
        $this->storeLocatorCheckoutHelper = $checkoutData;
        $this->storeRepository = $storeRepository;
        $this->logger = $logger;
        $this->checkoutSession = $checkoutSession;
        $this->geoLocateModel = $geoLocateModel;
        $this->helper = $helper;
        $this->timezoneInterface = $timezoneInterface;
        $this->messageManager = $messageManager;
    }

    /**
     * @param ShippingMethodManagement $subject
     * @param $result
     * @param $cartId
     * @return array
     */
    public function afterEstimateByExtendedAddress(ShippingMethodManagement $subject, $result, $cartId)
    {
        $result = $this->filterShippitAddtionalMethods($result, $cartId);
        return $result;
    }

    /**
     * @param ShippingMethodManagement $subject
     * @param \Closure $subject
     * @param string $cartId
     * @param string $addressId
     * @return array
     */
    public function aroundEstimateByAddressId(ShippingMethodManagement $subject, \Closure $proceed, $cartId, $addressId)
    {
        $result = [];

        try {
            $result = $proceed($cartId, $addressId);
        } catch (InputException $e) {
            $this->messageManager->addErrorMessage(__('Selected address no more exist. Please select a new address.'));
        }

        return $result;
    }

    /**
     * @param ShippingMethodManagement $subject
     * @param $result
     * @param $cartId
     * @return array
     */
    public function afterEstimateByAddressId(ShippingMethodManagement $subject, $result, $cartId)
    {
        $result = $this->filterShippitAddtionalMethods($result, $cartId);
        return $result;
    }

    /**
     * @param $result
     * @param $cartId
     * @return array
     */
    private function filterShippitAddtionalMethods($result, $cartId)
    {
        // First check if any knife-compliant products are in the cart
        try {
            $quote = $this->quoteRepository->getActive($cartId);
            $hasKnifeCompliantProduct = false;
            
            foreach ($quote->getAllVisibleItems() as $item) {
                $product = $item->getProduct();
                if ($this->isKnifeCompliance($product)) {
                    $hasKnifeCompliantProduct = true;
                    break;
                }
            }
            
            // If knife-compliant product exists, only allow click and collect
            if ($hasKnifeCompliantProduct) {
                $knifeCompliantMethods = [];
                foreach ($result as $shippingMethod) {
                    if ($shippingMethod->getCarrierCode() === 'shippitcc') {
                        $knifeCompliantMethods[] = $shippingMethod;
                    }
                }
                return $knifeCompliantMethods;
            }
        } catch (\Exception $e) {
            $this->logger->error('Error checking knife compliance: ' . $e->getMessage());
        }

        // If no knife-compliant products or an error occurred, continue with normal filtering
        $result = $this->getSortedShippingMethods($result);

        $mainShippingMethods = ['Standard', 'shippitcc', 'freeshipping'];

        if ($this->helper->isB2BCustomer()) {
            return $this->getMainShippingMethodsOnly($result, $mainShippingMethods);
        }

        if (!$this->isExpressCheckoutOptionEnabled()) {
            return $result;
        }

        $shippingMethods = [];
        $priorityExists = false;
        foreach ($result as $shippingMethod) {
            $shippingMethods[] = $shippingMethod->getMethodCode();
            if (str_contains($shippingMethod->getMethodCode(), 'Priority')) {
                $priorityExists = true;
            }
        }

        $shippitAdditionalMethodsExists = array_diff($shippingMethods, $mainShippingMethods);

        if (empty($shippitAdditionalMethodsExists)) {
            return $result;
        }

        $excludeShippitAdditonalDelivery = $this->checkExcludeShippitAdditonalDelivery($cartId);

        $uberExists = in_array(self::ON_DEMAND, $shippitAdditionalMethodsExists) ? true : false;

        if ($uberExists) {
            $excludeUberDelivery = $this->checkExcludeShippitAdditonalDelivery($cartId, true);
            if (!$excludeUberDelivery) {
                $shippitAdditionalMethodsExists = array_diff($shippitAdditionalMethodsExists, [self::ON_DEMAND]);
            } else {
                $result = $this->excludeUberOnDemand($result);
            }
        }

        if ($excludeShippitAdditonalDelivery || !in_array("Express", $shippitAdditionalMethodsExists)) {
            return $this->excludeShippitAdditonalDelivery($result, $shippitAdditionalMethodsExists);
        } elseif ($priorityExists) {
            return $this->validatePriorityShippingTimeSlots($result);
        } else {
            return $result;
        }
    }

    /**
     * @param $result
     * @return array
     */
    private function getSortedShippingMethods($result)
    {
        $methods = [];
        $methods['shippitcc'] = $this->getShippingMethodSortOrder('shippitcc');
        $methods['freeshipping'] = $this->getShippingMethodSortOrder('freeshipping');
        $methods['Standard'] = $this->getShippingMethodSortOrder('standard');
        $methods['Express'] = $this->getShippingMethodSortOrder('express');
        $methods['ondemand'] = $this->getShippingMethodSortOrder(self::ON_DEMAND);
        $methods['Priority'] = $this->getShippingMethodSortOrder('priority');
        asort($methods);
        $sortedShippingMethods = [];
        foreach ($methods as $k => $method) {
            foreach ($result as $shippingMethod) {
                if (str_contains($shippingMethod->getMethodCode(), $k)) {
                    $sortedShippingMethods[] = $shippingMethod;
                }
            }
        }
        return $sortedShippingMethods;
    }

    /**
     * @param $result
     * @param $shippitAdditionalMethodsExists
     * @return array
     */
    private function excludeShippitAdditonalDelivery($result, $shippitAdditionalMethodsExists)
    {
        $shippingMethodsToShow = [];
        foreach ($result as $shippingMethod) {
            if (!in_array($shippingMethod->getMethodCode(), $shippitAdditionalMethodsExists)) {
                $shippingMethodsToShow[] = $shippingMethod;
            }
        }
        return $shippingMethodsToShow;
    }

    /**
     * @param $result
     * @return array
     */
    private function validatePriorityShippingTimeSlots($result)
    {
        $priorityTimeSlotStartAt = (int)$this->getPriorityTimeSlotStartAt();
        $priorityTimeSlotEndAt = (int)$this->getPriorityTimeSlotEndAt();

        $shippingMethodsToShow = [];
        foreach ($result as $shippingMethod) {
            if (!str_contains($shippingMethod->getMethodCode(), 'Priority')) {
                $shippingMethodsToShow[] = $shippingMethod;
            } else {
                if (!$this->excludePriorityTimeSlot($shippingMethod->getMethodCode(), $priorityTimeSlotStartAt, $priorityTimeSlotEndAt)) {
                    $shippingMethodsToShow[] = $shippingMethod;
                }
            }
        }
        return $shippingMethodsToShow;
    }

    /**
     * @param $result
     * @return array
     */
    private function excludeUberOnDemand($result)
    {
        $shippingMethodsToShow = [];
        foreach ($result as $shippingMethod) {
            if ($shippingMethod->getMethodCode()  !=  self::ON_DEMAND) {
                $shippingMethodsToShow[] = $shippingMethod;
            }
        }
        return $shippingMethodsToShow;
    }

    /**
     * @param $methodCode
     * @param $priorityTimeSlotStartAt
     * @param $priorityTimeSlotEndAt
     * @return bool
     */
    private function excludePriorityTimeSlot($methodCode, $priorityTimeSlotStartAt, $priorityTimeSlotEndAt)
    {
        $priorityDays = $this->getPriorityAvailableDays();

        if (!in_array(date('w'), $priorityDays)) {
            return true;
        }

        $methodCode = strtolower($methodCode);
        $methodCodeArray = explode("_", $methodCode);
        $timeslot = end($methodCodeArray);
        $timeslotArray = explode("-", $timeslot);
        $startTime = isset($timeslotArray[0]) ? $timeslotArray[0] : '0:00';
        $endTime = isset($timeslotArray[1]) ? $timeslotArray[1] : '23:00';
        $startTimeHour = current(explode(":", $startTime));
        $endTimeHour = current(explode(":", $endTime));
        if ($startTimeHour >= $priorityTimeSlotStartAt && $endTimeHour <= $priorityTimeSlotEndAt) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $store
     * @return bool
     */
    private function excludeUberBasedOnCurrentHourAndDay($store)
    {
        $uberDays =  $this->getStoreUberAvailableDays($store->getUberAvailableDays());

        if (!in_array(date('w'), $uberDays)) {
            return true;
        }

        if (!is_numeric($store->getUberStartAt()) || !is_numeric($store->getUberEndAt())) {
            return true;
        }

        $currentDateTime = $this->timezoneInterface->date();
        $currentHour = (int)$currentDateTime->format('G');
        $uberTimeSlotStartAt = (int)$store->getUberStartAt();
        $uberTimeSlotEndAt = (int)$store->getUberEndAt();

        if ($currentHour >= $uberTimeSlotStartAt && $currentHour <= $uberTimeSlotEndAt) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * @param $cartId
     * @param $isOnDemand
     * @return bool
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    private function checkExcludeShippitAdditonalDelivery($cartId, $isOnDemand = false)
    {
        $excludeShippitAdditonalDelivery = false;

        /** @var Quote $quote */
        $quote = $this->quoteRepository->getActive($cartId);

        if ($isOnDemand && $this->orderValueIsGreaterThanMaxAllowed($quote)) {
            return true;
        }

        if ($isOnDemand && $this->orderValueIsLessThanMinAllowed($quote)) {
            return true;
        }

        $postcode = $quote->getShippingAddress()->getPostcode() ?? "";

        // Get store from post code
        $store = $this->storeRepository->getByPostcodeInZone($postcode);

        // Get store from session local storage
        if (!$store && !$isOnDemand) {
            $store = $this->getStore();
        }

        // If store not found then exclude express delivery
        if (!$store) {
            return true;
        }

        if ($isOnDemand && !$store->getAllowUberOrders()) {
            return true;
        }

        if ($isOnDemand && $this->excludeUberBasedOnCurrentHourAndDay($store)) {
            return true;
        }

        $items = $quote->getAllVisibleItems();
        $requiredProductQuantities = [];
        if ($isOnDemand) {
            $surplusQtyBuffer = $this->getUberSurplusQtyBuffer();
        } else {
            $surplusQtyBuffer = $this->getExpressSurplusQtyBuffer();
        }

        // We first need to find out the store which should fulfill the order.
        foreach ($items as $item) {
            $product = $item->getProduct();
            
            if (!$product || ($product->getAttributeText('stock_availability_code') != 'OL' && $product->getAttributeText('stock_availability_code') != 'OD')) { 
                return true;
            }

            if ($isOnDemand && $this->checkUberOnDemandNotAllowed($product)) {
                return true;
            }
            

            $sku = $item->getSku();
            if (empty($sku)) {
                continue;
            }
            $qty = 1;

            if ($item instanceof \Magento\Quote\Model\Quote\Item) {
                $qty = $item->getQty();
            } elseif ($item instanceof \Magento\Sales\Model\Order\Item) {
                $qty = $item->getQtyOrdered();
            }

            $requiredProductQuantities[$sku] = $qty + $surplusQtyBuffer;
        }

        if (!$isOnDemand) {
            $store = $this->storeInventoryService->getStoreWhichShouldFulfillTheOrder($store, $requiredProductQuantities);
        }
        
        // Find out the stock level for each item individually based on the allocated store.
        foreach ($items as $item) {
            $sku = $item->getSku();

            if (empty($sku)) {
                continue;
            }

            $qty = 1;

            if ($item instanceof \Magento\Quote\Model\Quote\Item) {
                $qty = $item->getQty();
            } elseif ($item instanceof \Magento\Sales\Model\Order\Item) {
                $qty = $item->getQtyOrdered();
            }

            $qty = $qty + $surplusQtyBuffer;
            $stockExist = $this->storeInventoryService->checkStoreInventoryForSku($store, $sku, $qty);

            if (!$stockExist) {
                return true;
            }
        }

        return $excludeShippitAdditonalDelivery;
    }

    /**
     * @return mixed
     */
    public function isExpressCheckoutOptionEnabled()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/enabled', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getExpressSurplusQtyBuffer()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/express_surplus_qty_buffer', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getUberSurplusQtyBuffer()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_surplus_qty_buffer', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @param $methodCode
     * @return int
     */
    public function getShippingMethodSortOrder($methodCode)
    {
        return (int) $this->scopeConfig->getValue('checkout/shipping_methods_sort_order/' . $methodCode);
    }

    /**
     * @return mixed
     */
    public function getPriorityTimeSlotStartAt()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/hour_of_day_priority_start_at', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getPriorityTimeSlotEndAt()
    {
        return $this->scopeConfig->getValue('checkout/express_checkout_option/hour_of_day_priority_end_at', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getUberTimeSlotStartAt()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/hour_of_day_uber_start_at', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return mixed
     */
    public function getUberTimeSlotEndAt()
    {
        return $this->scopeConfig->getValue('checkout/uber_ondemand_settings/hour_of_day_uber_end_at', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return int
     */
    public function getUberMaxAllowedWeight()
    {
        return (int) $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_ondemand_max_weight', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @return int
     */
    public function getUberMaxAllowedDimension()
    {
        return (int) $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_ondemand_max_dimension', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * Checks if Uber on-demand is not allowed for a given product.
     *
     * @param Product $product The product to check.
     * @return bool Returns true if Uber on-demand is not allowed, false otherwise.
     */
    public function checkUberOnDemandNotAllowed($product)
    {
        $uberNotAllowed = false;
        if ($product->getWeight() !== null && floatval($product->getWeight()) > $this->getUberMaxAllowedWeight()) {
            $uberNotAllowed = true;
        }
        if ($product->getData('shipping_height') !== null && floatval($product->getData('shipping_height')) > $this->getUberMaxAllowedDimension()) {
            $uberNotAllowed = true;
        }
        if ($product->getData('shipping_length') !== null && floatval($product->getData('shipping_length')) > $this->getUberMaxAllowedDimension()) {
            $uberNotAllowed = true;
        }
        if ($product->getData('shipping_width') !== null && floatval($product->getData('shipping_width')) > $this->getUberMaxAllowedDimension()) {
            $uberNotAllowed = true;
        }

        return $uberNotAllowed;
    }

    /**
     * @return array
     */
    public function getPriorityAvailableDays()
    {
        $priorityDays = [];
        $priorityConfigDays = $this->scopeConfig->getValue('checkout/express_checkout_option/priority_available_days', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        if (!empty($priorityConfigDays)) {
            $priorityDays = explode(",", $priorityConfigDays);
        }

        return $priorityDays;
    }

    /**
     * @return array
     */
    public function getUberAvailableDays()
    {
        $uberDays = [];
        $uberConfigDays = $this->scopeConfig->getValue('checkout/uber_ondemand_settings/uber_available_days', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);
        if (!empty($uberConfigDays)) {
            $uberDays = explode(",", $uberConfigDays);
        }

        return $uberDays;
    }

    /**
     * @param $uberStoreDays
     * @return array
     */
    public function getStoreUberAvailableDays($uberStoreDays)
    {
        $uberDays = [];
        if (!empty($uberStoreDays)) {
            $uberDays = explode(",", $uberStoreDays);
        }

        return $uberDays;
    }

    /**
     * Get user store from session, if not found try fetching from GeoLocateModel
     * either from cached location data or based on user IP
     * @return array|null
     */
    private function getStore()
    {
        $store = null;
        $storePickup = $this->checkoutSession->getData('storepickup_session');
        $storeId = $storePickup['store_id'] ?? null;

        if (!$storeId) {
            $geoLocation = $this->geoLocateModel->getLocation(false, false);
            $storeId = isset($geoLocation['store']['storelocator_id']) ? $geoLocation['store']['storelocator_id'] : $storeId;
        }

        if ($storeId) {
            $store = $this->storeRepository->getById($storeId);
        }
        return $store;
    }

    /**
     * @param $result
     * @param $mainShippingMethods
     * @return array
     */
    private function getMainShippingMethodsOnly($result, $mainShippingMethods)
    {
        $shippingMethodsToShow = [];
        foreach ($result as $shippingMethod) {
            if (in_array($shippingMethod->getMethodCode(), $mainShippingMethods)) {
                $shippingMethodsToShow[] = $shippingMethod;
            }
        }
        return $shippingMethodsToShow;
    }

    /**
     * @param $quote
     * @return bool
     */
    private function orderValueIsGreaterThanMaxAllowed($quote)
    {
        $maxAllowedUberOrderTotal = $this->scopeConfig->getValue('checkout/uber_ondemand_settings/max_order_value', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);

        $subTotal = (float)$quote->getSubtotal() + (float)$quote->getShippingAddress()->getTaxAmount();

        if ((float)$subTotal > (float)$maxAllowedUberOrderTotal) {
            return true;
        }

        return false;
    }

    /**
     * @param $quote
     * @return bool
     */
    private function orderValueIsLessThanMinAllowed($quote)
    {
        $minAllowedUberOrderTotal = $this->scopeConfig->getValue('checkout/uber_ondemand_settings/min_order_value', \Magento\Store\Model\ScopeInterface::SCOPE_WEBSITE);

        $subTotal = (float)$quote->getSubtotal() + (float)$quote->getShippingAddress()->getTaxAmount();

        if ((float)$subTotal < (float)$minAllowedUberOrderTotal) {
            return true;
        }

        return false;
    }

    /**
     * @param Product $product
     * @return bool
     */
    private function isKnifeCompliance($product)
    {
        $knifeCompliance = $product->getAttributeText(Store::KNIFE_COMPLIANCE_KEY);
        if ($knifeCompliance == 'Yes') {
            return true;
        }
        return false;
    }
}
