<?php
namespace Totaltools\Checkout\Plugin\Checkout\CustomerData;

use   Magento\Checkout\Model\Session;
class Cart
{
     /**
     * @var Session
     */
    protected $checkoutSession;

    /**
     * @var \Magento\Quote\Model\Quote
     */
    protected $quote;

    public function __construct(
        Session $checkoutSession
    ) {
        $this->checkoutSession = $checkoutSession;
    }
    
    /**
     * Get active quote
     *
     * @return \Magento\Quote\Model\Quote
     */
    protected function getQuote()
    {
        if (null === $this->quote) {
            $this->quote = $this->checkoutSession->getQuote();
        }
        return $this->quote;
    }

    public function afterGetSectionData(
        \Magento\Checkout\CustomerData\Cart $subject, 
        $result
        ) {
        $result['canBeFreeDelivery'] = $this->getAllowedFreeShipping();
        return $result;
    }

    protected function getAllowedFreeShipping()
    {
        $quote = $this->getQuote();
        $freeShipping = true;
        $quoteItems = $quote->getAllItems();
        foreach($quoteItems as $item) {
            $canBeFreeDelivery = $item->getProduct()->getCanBeFreeDelivery();
            if(empty($canBeFreeDelivery)) {
                $freeShipping = false;
            }
        }
        return $freeShipping;
    }
}