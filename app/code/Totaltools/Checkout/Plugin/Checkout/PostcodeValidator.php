<?php

namespace Totaltools\Checkout\Plugin\Checkout;

use Magento\Checkout\Block\Checkout\LayoutProcessor;

class PostcodeValidator
{
    /**
     * Add Australian postcode validation to postcode fields
     *
     * @param LayoutProcessor $subject
     * @param array $result
     * @return array
     */
    public function afterProcess(
        LayoutProcessor $subject,
        array $result
    ) {
        // Add validation to shipping address postcode
        if (isset($result['components']['checkout']['children']['steps']['children']
            ['shipping-step']['children']['shippingAddress']['children']
            ['shipping-address-fieldset']['children']['postcode'])) {
            
            $result['components']['checkout']['children']['steps']['children']
                ['shipping-step']['children']['shippingAddress']['children']
                ['shipping-address-fieldset']['children']['postcode']['validation']['validate-au-postcode'] = true;
        }

        return $result;
    }
} 