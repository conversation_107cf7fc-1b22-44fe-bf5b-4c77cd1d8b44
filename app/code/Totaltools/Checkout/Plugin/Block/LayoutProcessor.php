<?php

namespace Totaltools\Checkout\Plugin\Block;

use Magento\Customer\Model\AttributeMetadataDataProvider;
use Magento\Ui\Component\Form\AttributeMapper;
use Magento\Checkout\Block\Checkout\AttributeMerger;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Customer\Model\Session as CustomerSession;
use Totaltools\Checkout\Helper\Data;

class LayoutProcessor
{
    /**
     * @var AttributeMetadataDataProvider
     */
    public $attributeMetadataDataProvider;

    /**
     * @var AttributeMapper
     */
    public $attributeMapper;

    /**
     * @var AttributeMerger
     */
    public $merger;

    /**
     * @var CheckoutSession
     */
    public $checkoutSession;

    /**
     * @var null
     */
    public $quote = null;

    /**
     * @var CustomerSession
     */
    protected $customerSession;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * LayoutProcessor constructor.
     *
     * @param AttributeMetadataDataProvider $attributeMetadataDataProvider
     * @param AttributeMapper $attributeMapper
     * @param AttributeMerger $merger
     * @param CheckoutSession $checkoutSession
     * @param CustomerSession $customerSession
     * @param Data $helper
     */
    public function __construct(
        AttributeMetadataDataProvider $attributeMetadataDataProvider,
        AttributeMapper $attributeMapper,
        AttributeMerger $merger,
        CheckoutSession $checkoutSession,
        CustomerSession $customerSession,
        Data $helper
    ) {
        $this->attributeMetadataDataProvider = $attributeMetadataDataProvider;
        $this->attributeMapper = $attributeMapper;
        $this->merger = $merger;
        $this->checkoutSession = $checkoutSession;
        $this->customerSession = $customerSession;
        $this->helper = $helper;
    }

    /**
     * Get Quote
     *
     * @return \Magento\Quote\Model\Quote|null
     */
    public function getQuote()
    {
        if (null === $this->quote) {
            $this->quote = $this->checkoutSession->getQuote();
        }

        return $this->quote;
    }

    /**
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $subject
     * @param array $jsLayout
     * @return array
     */
    public function aroundProcess(
        \Magento\Checkout\Block\Checkout\LayoutProcessor $subject,
        \Closure $proceed,
        array $jsLayout
    ) {
        $jsLayoutResult = $proceed($jsLayout);

        if (isset($jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fieldset'])) {

            $shippingConfig = &$jsLayoutResult['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fieldset'];

            // Update Street Address fields placeholders
            $shippingConfig['children']['street']['label'] = __('Address');
            $shippingConfig['children']['street']['children'][0]['label'] = __('Address');
            $shippingConfig['children']['street']['children'][0]['placeholder'] = __('Start typing your address');
            $shippingConfig['children']['street']['children'][0]['autocomplete'] = 'nope';
            $shippingConfig['children']['street']['children'][0]['validation']['prevent-pobox-rule'] = false;
            $shippingConfig['children']['street']['children'][1]['label'] = false;
            $shippingConfig['children']['street']['children'][1]['placeholder'] = __('Street line 2');
            $shippingConfig['children']['street']['children'][1]['validation']['prevent-pobox-rule'] = false;
            $shippingConfig["children"]["is_loyal"]["visible"] = 0;
            $shippingConfig["children"]["newsletter_subscribe"]["visible"] = 0;
            $shippingConfig['children']['telephone']['label'] = __('Mobile Number');
            $shippingConfig['children']['telephone']['validation']['required-entry'] = true;
            $shippingConfig['children']['telephone']['validation']['validate-phone-custom'] = true;
            $shippingConfig['children']['company']['label'] = __('Company (Optional)');
            $shippingConfig['children']['company']['sortOrder'] = 100;
            $shippingConfig['children']['company']['validation']['prevent-pobox-rule'] = false;
            $shippingConfig['children']['country_id']['validation']['au-only-delivery'] = true;
            $shippingConfig['children']['postcode']['validation']['validate-number'] = true;

            $this->processAddressFields($jsLayoutResult, $shippingConfig);
            $this->processDiscountAndRedeem($jsLayoutResult);

            if (!$this->helper->isOnepageEnable()) {
                $elements = $this->getAddressAttributes();
                $customBillingForm = $this->getCustomBillingAddressComponent($elements);

                $customBillingForm['children']['form-fields']['children']['street']['label'] = __('Street Address');

                $customBillingForm['children']['form-fields']['children']['street']['children'][0]['placeholder'] = __('Start typing your address');

                $customBillingForm['children']['form-fields']['children']['street']['children'][1]['placeholder'] = __('Street line 2');

                if (isset($customBillingForm['children']['form-fields']['children']['street']['children'][2])) {
                    unset($customBillingForm['children']['form-fields']['children']['street']['children'][2]);
                }

                $jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['billing-address-form'] = $customBillingForm;
            }
        }

        if (isset($jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']['children']['form-fields']['children'])) {
            $this->processBillingFields($jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']['children']['form-fields']['children']);
        }

        if (!$this->helper->isOnepageEnable()) {
            unset($jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']);
            if (isset($jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['discount'])) {
                unset($jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['discount']);
            }
        }

        if ($this->checkoutSession->getQuote()->isVirtual()) {
            unset($jsLayoutResult['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['discount']);
        }

        return $jsLayoutResult;
    }

    /**
     * Checkout LayoutProcessor after process plugin.
     *
     * @param \Magento\Checkout\Block\Checkout\LayoutProcessor $processor
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess(\Magento\Checkout\Block\Checkout\LayoutProcessor $processor, $jsLayout)
    {
        if ($this->helper->isOnepageEnable()) {
            return $this->modifyShippitAtl($jsLayout);
        }

        $shippingConfiguration = &$jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fieldset']['children'];

        //Checks if shipping step available.
        if (isset($shippingConfiguration)) {
            $shippingConfiguration = $this->processAddress(
                $shippingConfiguration,
                'shippingAddress',
                [
                    'checkoutProvider',
                    'checkout.steps.shipping-step.shippingAddress.shipping-address-fieldset.company'
                ]
            );
        }

        return $jsLayout;
    }

    /**
     * Process provided address to contains checkbox and have trackable address fields.
     *
     * @param $addressFieldset - Address fieldset config.
     * @param $dataScope - data scope
     * @param $deps - list of dependencies
     * @return array
     */
    private function processAddress($addressFieldset, $dataScope, $deps)
    {
        //Creates checkbox.
        $addressFieldset['company_checkbox'] = [
            'component' => 'Totaltools_Checkout/js/single-checkbox',
            'config' => [
                'customScope' => $dataScope,
                'prefer' => 'checkbox',
                'elementTmpl' => 'ui/form/element/checkbox'
            ],
            'dataScope' => $dataScope . '.company_checkbox',
            'deps' => $deps,
            'label' => __("I'm delivering to a business"),
            'provider' => 'checkoutProvider',
            'visible' => true,
            'initialValue' => false,
            'sortOrder' => 4,
            'valueMap' => [
                'true' => true,
                'false' => false
            ]
        ];

        return $addressFieldset;
    }

    /**
     * Get all visible address attribute
     *
     * @return array
     */
    private function getAddressAttributes()
    {
        /** @var \Magento\Eav\Api\Data\AttributeInterface[] $attributes */
        $attributes = $this->attributeMetadataDataProvider->loadAttributesCollection(
            'customer_address',
            'customer_register_address'
        );

        $elements = [];
        foreach ($attributes as $attribute) {
            $code = $attribute->getAttributeCode();
            if ($attribute->getIsUserDefined()) {
                continue;
            }
            $elements[$code] = $this->attributeMapper->map($attribute);
            if (isset($elements[$code]['label'])) {
                $label = $elements[$code]['label'];
                $elements[$code]['label'] = __($label);
            }
        }
        return $elements;
    }

    /**
     * Prepare billing address field for shipping step for physical product
     *
     * @param $elements
     * @return array
     */
    public function getCustomBillingAddressComponent($elements)
    {
        $billingForm =  [
            'component' => 'Totaltools_Checkout/js/view/billing-address',
            'displayArea' => 'before_step',
            'sortOrder' => 5,
            'provider' => 'checkoutProvider',
            'deps' => ['checkoutProvider'],
            'dataScopePrefix' => 'billingAddress',
            'children' => [
                'form-fields' => [
                    'component' => 'uiComponent',
                    'displayArea' => 'additional-fieldsets',
                    'children' => $this->merger->merge(
                        $elements,
                        'checkoutProvider',
                        'billingAddress',
                        [
                            'firstname' => [
                                'sortOrder' => 1,
                            ],
                            'lastname' => [
                                'sortOrder' => 2,
                            ],
                            'telephone' => [
                                'sortOrder' => 3,
                                'config' => [
                                    'tooltip' => [
                                        'description' => __('For delivery questions.'),
                                    ],
                                ],
                                'validation' => [
                                    'required-entry' => true,
                                    'validate-phone-custom' => true
                                ],
                            ],
                            'company' => [
                                // 'sortOrder' => 5,
                                'validation' => [
                                    'min_text_length' => 0,
                                ],
                            ],
                            'postcode' => [
                                'component' => 'Magento_Ui/js/form/element/post-code',
                                'validation' => [
                                    'required-entry' => true,
                                ],
                            ],
                            'region' => [
                                'visible' => false,
                            ],
                            'region_id' => [
                                'component' => 'Magento_Ui/js/form/element/region',
                                'config' => [
                                    'template' => 'ui/form/field',
                                    'elementTmpl' => 'ui/form/element/select',
                                    'customEntry' => 'billingAddress.region',
                                ],
                                'validation' => [
                                    'required-entry' => true,
                                ],
                                'filterBy' => [
                                    'target' => '${ $.provider }:${ $.parentScope }.country_id',
                                    'field' => 'country_id',
                                ],
                            ],
                            'country_id' => [
                                'sortOrder' => 115,
                            ],
                            'fax' => [
                                'validation' => [
                                    'min_text_length' => 0,
                                ],
                            ],
                        ]
                    ),
                ],
                'address-list' => [
                    'component' => 'Totaltools_Checkout/js/view/billing-address/list',
                    'displayArea' => 'address-list',
                ],
            ],
            'config' => [
                'popUpForm' => [
                    'element' => '#opc-new-billing-address',
                    'options' => [
                        'type' => 'popup',
                        'responsive' => true,
                        'innerScroll' => true,
                        'title' => __('Billing Address'),
                        'trigger' => 'opc-new-billing-address',
                        'buttons' => [
                            'save' => [
                                'text' => 'Save Address',
                                'class' => 'action primary action-save-address',
                            ],
                            'cancel' => [
                                'text' => 'Cancel',
                                'class' => 'action secondary action-hide-popup',
                            ],
                        ],
                    ],
                ],
            ],
        ];

        if (!$this->customerSession->isLoggedIn() && !$this->getQuote()->isVirtual()) {

            $billingForm["children"]["form-fields"]["children"]["firstname"]["visible"] = 0;
            $billingForm["children"]["form-fields"]["children"]["lastname"]["visible"] = 0;
            $billingForm["children"]["form-fields"]["children"]["telephone"]["visible"] = 0;
            $billingForm["children"]["form-fields"]["children"]["company"]["visible"] = 0;
        }

        return $billingForm;
    }

    /**
     * shippingAddional node modification in shipping-step
     *
     * @param array $jsLayout
     * @return array
     */
    private function modifyShippitAtl($jsLayout)
    {
        $authorityToLeave = &$jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shippingAdditional']['children']['shippit']['children']['shippit-options']['children']['shippit_authority_to_leave'];

        $authorityToLeave['component'] = 'Totaltools_Checkout/js/view/form/element/dependent';
        $authorityToLeave['config']['dependent'] = 'shippit_delivery_instructions';

        return $jsLayout;
    }

    /**
     * @param array $jsLayout
     * @param array $shippingConfig
     * @return array
     */
    private function processAddressFields(&$jsLayout, &$shippingConfig)
    {
        $jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fields'] = [
            'component' => 'uiComponent',
            'template' => 'Totaltools_Checkout/shipping/address-fieldset/address-form',
            'config' => [
                'deps' => ['checkoutProvider'],
                'label' => __('Address')
            ],
            'displayArea' => 'address-fields'
        ];

        $addressFields = &$jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shipping-address-fields']['children'];
        $addressFields['street'] = $shippingConfig['children']['street'];
        $addressFields['street']['sortOrder'] = 10;
        $addressFields['street']['label'] = false;
        $addressFields['city'] = $shippingConfig['children']['city'];
        $addressFields['city']['sortOrder'] = 20;
        $addressFields['country_id'] = $shippingConfig['children']['country_id'];
        $addressFields['country_id']['sortOrder'] = 30;
        $addressFields['region_id'] = $shippingConfig['children']['region_id'];
        $addressFields['region_id']['sortOrder'] = 40;
        $addressFields['region'] = $shippingConfig['children']['region'];
        $addressFields['region']['sortOrder'] = 50;
        $addressFields['postcode'] = $shippingConfig['children']['postcode'];
        $addressFields['postcode']['sortOrder'] = 100;

        unset($addressFields['street']['children'][2]);
        unset($shippingConfig['children']['street']['children'][2]);

        if (!$this->customerSession->isLoggedIn()) {
            unset($shippingConfig['children']['street']);
            unset($shippingConfig['children']['region']);
            unset($shippingConfig['children']['region_id']);
            unset($shippingConfig['children']['postcode']);
            unset($shippingConfig['children']['city']);
            unset($shippingConfig['children']['country_id']);
        }

        return $addressFields;
    }

    /**
     * @param array $jsLayout
     * @return array
     */
    private function processDiscountAndRedeem(&$jsLayout)
    {
        $paymentConfig = &$jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children'];

        $shippingConfig = &$jsLayout['components']['checkout']['children']['steps']['children']['shipping-step']['children']['shippingAddress']['children']['shippingAdditional']['children'];

        if (isset($paymentConfig) && isset($shippingConfig)) {
            if (isset($paymentConfig['discount'])) {
                $shippingConfig['discount'] = $paymentConfig['discount'];
                $shippingConfig['discount']['sortOrder'] = 90;
                unset($paymentConfig['discount']);
            }

            if (isset($paymentConfig['giftCardAccount'])) {
                $shippingConfig['giftCardAccount'] = $paymentConfig['giftCardAccount'];
                $shippingConfig['giftCardAccount']['sortOrder'] = 100;
                unset($paymentConfig['giftCardAccount']);
            }

            if (isset($paymentConfig['afterMethods']['children']['reward'])) {
                $shippingConfig['reward'] = $paymentConfig['afterMethods']['children']['reward'];
                $shippingConfig['reward']['sortOrder'] = 110;
                unset($paymentConfig['afterMethods']['children']['reward']);
            }

            if (isset($paymentConfig['afterMethods']['children']['storeCredit'])) {
                $shippingConfig['storeCredit'] = $paymentConfig['afterMethods']['children']['storeCredit'];
                $shippingConfig['storeCredit']['sortOrder'] = 120;
                unset($paymentConfig['afterMethods']['children']['storeCredit']);
            }
        }

        return $jsLayout;
    }

    /**
     * Modify billing address form fields.
     *
     * @param array $billingConfig
     * @return array
     */
    private function processBillingFields(&$billingConfig)
    {
        if (isset($billingConfig['street']['children'])) {
            $billingConfig['street']['children'][0]['label'] = __('Address');
            $billingConfig['street']['children'][0]['placeholder'] = __('Start typing your address');
            $billingConfig['street']['children'][1]['label'] = false;
            $billingConfig['street']['children'][1]['placeholder'] = __('Street line 2');
        }

        return $billingConfig;
    }
}
