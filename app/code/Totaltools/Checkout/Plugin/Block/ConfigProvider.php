<?php

namespace Totaltools\Checkout\Plugin\Block;

/**
 * @category Totaltools
 * @package Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright (c) 2021 Totaltools. <https://totaltools.com.au>
 */

use Magento\Checkout\Block\Onepage;
use Magento\Checkout\Block\Cart\Shipping;
use Totaltools\Checkout\Helper\Data;
use Totaltools\Checkout\Helper\TotalToolsConfig;
use Totaltools\Customer\Helper\AttributeData;
use Magento\Framework\UrlInterface;
use Magento\Framework\App\Config\ScopeConfigInterface as Scope;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Plugin to provide additional configuration data for checkout
 */
class ConfigProvider
{
    /**
     * Configuration paths constants
     */
    const XML_PATH_STOCK_MESSAGES_GROUP = 'checkout/total_tools/';
    const XML_PATH_STOCK_MESSAGES_ACTIVE = 'stock_messages_active';
    const XML_PATH_AUTHORITY_LEAVE_ACTIVE = 'authority_leave_active';

    /**
     * @var Data
     */
    protected $helper;

    /**
     * @var TotalToolsConfig
     */
    protected $checkoutSettings;

    /**
     * @var AttributeData
     */
    protected $customerHelper;

    /**
     * @var UrlInterface
     */
    protected $urlBuilder;

    /**
     * ConfigProvider constructor
     *
     * @param Data $helper
     * @param TotalToolsConfig $checkoutSettings
     * @param AttributeData $customerHelper
     * @param UrlInterface $urlBuilder
     */
    public function __construct(
        Data $helper,
        TotalToolsConfig $checkoutSettings,
        AttributeData $customerHelper,
        UrlInterface $urlBuilder
    ) {
        $this->helper = $helper;
        $this->checkoutSettings = $checkoutSettings;
        $this->customerHelper = $customerHelper;
        $this->urlBuilder = $urlBuilder;
    }

    /**
     * Add stock messages configuration to checkout config array
     *
     * @param Onepage|Shipping $subject
     * @param array $result
     * @return array
     * @throws LocalizedException
     * @throws NoSuchEntityException
     * @SuppressWarnings(PHPMD.UnusedFormalParameter)
     */
    public function afterGetCheckoutConfig($subject, array $result)
    {
        $isActive = $this->helper->getValue(
            self::XML_PATH_STOCK_MESSAGES_ACTIVE,
            Scope::SCOPE_TYPE_DEFAULT,
            self::XML_PATH_STOCK_MESSAGES_GROUP
        );

        $isLeaveActive = $this->helper->getValue(
            self::XML_PATH_AUTHORITY_LEAVE_ACTIVE,
            Scope::SCOPE_TYPE_DEFAULT,
            self::XML_PATH_STOCK_MESSAGES_GROUP
        );

        $result['stockMessages'] = ['enabled' => (bool) $isActive == '1'];
        $result['authToLeave'] = ['enabled' => (bool) $isLeaveActive == '1'];
        $result['thirdPartyPickup'] = $this->helper->isThirdPartyPickupActive();

        if (!$this->helper->isOnepageEnable()) {
            $result['checkoutUrl'] = $this->urlBuilder->getUrl('checkout/steps');
        }

        $result['isB2BCustomer'] = (bool) $this->customerHelper->isB2bCustomer();

        $result['cc_carrier_code'] = $this->checkoutSettings->getTotalToolsConfig('cc_carrier_code');
        $result['cc_method_code'] = $this->checkoutSettings->getTotalToolsConfig('cc_method_code');
        $result['cc_method_name'] = $result['cc_carrier_code'] . '_' . $result['cc_method_code'];

        return $result;
    }
}
