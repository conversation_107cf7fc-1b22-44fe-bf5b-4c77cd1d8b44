<?php

/**
 * @package   Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright Copyright (c) 2025 Totaltools (https://totaltools.com.au)
 */

namespace Totaltools\Checkout\Observer;

use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Event\Observer;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\StateException;

class ValidatePostcode implements ObserverInterface
{
    /**
     * Validate Australian postcodes during checkout
     *
     * @param Observer $observer
     * @return void
     * @throws LocalizedException
     */
    public function execute(Observer $observer)
    {
        $order = $observer->getEvent()->getOrder();

        // Validate shipping address postcode
        $shippingAddress = $order->getShippingAddress();
        if ($shippingAddress && $shippingAddress->getCountryId() === 'AU') {
            $postcode = $shippingAddress->getPostcode();
            if (!$this->isValidAustralianPostcode($postcode)) {
                throw new StateException(__('Please enter a valid Australian postcode (4 digits) for shipping address.'));
            }
        }

        // Validate billing address postcode
        $billingAddress = $order->getBillingAddress();
        if ($billingAddress && $billingAddress->getCountryId() === 'AU') {
            $postcode = $billingAddress->getPostcode();
            if (!$this->isValidAustralianPostcode($postcode)) {
                throw new StateException(__('Please enter a valid Australian postcode (4 digits) for billing address.'));
            }
        }
    }

    /**
     * Check if postcode is valid for Australia
     *
     * @param string $postcode
     * @return bool
     */
    private function isValidAustralianPostcode($postcode)
    {
        return (bool) preg_match('/^\d{4}$/', $postcode);
    }
}
