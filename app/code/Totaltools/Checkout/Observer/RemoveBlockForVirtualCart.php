<?php

/**
 * RemoveBlockForVirtualCart
 *
 * @category  Totaltools
 * @package   Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      http://total-infotech.com
 */

namespace Totaltools\Checkout\Observer;

use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\View\layoutInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;


class RemoveBlockForVirtualCart implements ObserverInterface
{

    const Allow_Discounts_And_Redemptions_on_Cart = 'checkout/total_tools_cart/allow_discounts_and_redemptions_on_cart';
    /**
     * @var CheckoutSession
     */
    private $_checkoutSession;

    /**
     * @var ScopeConfigInterface
     */
    private $_scopeConfig;
   /**
     * @var ScopeConfigInterface
     */
    private $_request;
    /**
     * @var layoutInterface
     */
    private $_layout;

    /**
     * RemoveBlockForVirtualCart constructor.
     * @param CheckoutSession $checkoutSession
     * @param this->_layoutInterface $this->_layout
     * @param ScopeConfigInterface $_scopeConfig
     */
    public function __construct(
        CheckoutSession $checkoutSession,
        ScopeConfigInterface $scopeConfig,
        layoutInterface $layout
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->_scopeConfig = $scopeConfig;
        $this->_layout = $layout;
    }

    /**
     * @param Observer $observer
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(Observer $observer)
    {
        if ($this->_checkoutSession->getQuote()->isVirtual()) {
            if ($this->_layout->getBlock('checkout.cart.coupon')) {
                $this->_layout->unsetElement('checkout.cart.coupon');
            }
            if ($this->_layout->getBlock('checkout.cart.giftcardaccount')) {
                $this->_layout->unsetElement('checkout.cart.giftcardaccount');
            }
        }

        if ($this->_layout->getBlock('checkout.cart.coupon') && !$this->_scopeConfig->getValue(self::Allow_Discounts_And_Redemptions_on_Cart)) {
            $this->_layout->unsetElement('checkout.cart.coupon');
        }
        if ($this->_layout->getBlock('checkout.cart.giftcardaccount') && !$this->_scopeConfig->getValue(self::Allow_Discounts_And_Redemptions_on_Cart)) {
            $this->_layout->unsetElement('checkout.cart.giftcardaccount');
        }
        if ($this->_layout->getBlock('checkout.cart.points') && !$this->_scopeConfig->getValue(self::Allow_Discounts_And_Redemptions_on_Cart)) {
            $this->_layout->unsetElement('checkout.cart.points');
        }
    }
}
