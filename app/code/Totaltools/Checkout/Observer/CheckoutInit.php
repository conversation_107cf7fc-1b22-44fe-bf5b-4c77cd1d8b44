<?php
namespace Totaltools\Checkout\Observer;

use Magento\Checkout\Model\Session;
use Magento\Framework\App\ResponseFactory;
use Magento\Framework\Event\Observer;
use Magento\Framework\Event\ObserverInterface;
use Magento\Framework\Message\ManagerInterface;
use Magento\Framework\UrlInterface;
use Totaltools\Storelocator\Model\Registry;

class CheckoutInit implements ObserverInterface
{
    const OUT_OF_STOCK_CODE = 5;

    /**
     * @var ResponseFactory
     */
    protected $responseFactory;

    /**
     * @var UrlInterface
     */
    protected $url;

    /**
     * @var \Totaltools\Storelocator\Helper\Data
     */
    protected $storeLocatorHelper;

    /**
     * @var \Totaltools\Storelocator\Helper\Checkout\Data
     */
    protected $storeLocatorCheckoutHelper;

    /**
     * @var \Totaltools\Checkout\Helper\TotalToolsConfig
     */
    protected $totalToolsConfig;

    /**
     * @var Session
     */
    protected $checkoutSession;

    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var Registry
     */
    private Registry $registry;

    /**
     * @var \Magento\Customer\Model\Session
     */
    private \Magento\Customer\Model\Session $customerSession;

    /**
     * CheckoutInit constructor.
     * @param ResponseFactory $responseFactory
     * @param UrlInterface $url
     * @param Session $session
     * @param \Totaltools\Storelocator\Helper\Data $data
     * @param \Totaltools\Storelocator\Helper\Checkout\Data $checkoutData
     * @param \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig
     * @param ManagerInterface $manager
     * @param Registry $registry
     * @param \Magento\Customer\Model\Session $customerSession
     */
    public function __construct(
        ResponseFactory $responseFactory,
        UrlInterface $url,
        Session $session,
        \Totaltools\Storelocator\Helper\Data $data,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutData,
        \Totaltools\Checkout\Helper\TotalToolsConfig $totalToolsConfig,
        ManagerInterface $manager,
        Registry $registry,
        \Magento\Customer\Model\Session $customerSession
    ) {
        $this->responseFactory = $responseFactory;
        $this->url = $url;
        $this->storeLocatorHelper = $data;
        $this->totalToolsConfig = $totalToolsConfig;
        $this->storeLocatorCheckoutHelper = $checkoutData;
        $this->checkoutSession = $session;
        $this->messageManager = $manager;
        $this->registry = $registry;
        $this->customerSession = $customerSession;
    }


    /**
     * @param Observer $observer
     * @throws \Magento\Framework\Exception\LocalizedException
     * @throws \Magento\Framework\Exception\NoSuchEntityException
     */
    public function execute(Observer $observer)
    {
        $shippingMethod = $this->checkoutSession->getQuote()->getShippingAddress()->getShippingMethod()
            ?? $this->totalToolsConfig->getDefaultShippingMethod();
        $storeLocatorId = $this->storeLocatorCheckoutHelper->getSessionStoreId() ?? 0;
        $postCode = $this->checkoutSession->getQuote()->getShippingAddress()->getPostcode() ?? "";

        if (empty($postCode)) {
            if ($this->customerSession->isLoggedIn()) {
                $defaultShippingAddress = $this->customerSession->getCustomer()->getDefaultShippingAddress();
                if ($defaultShippingAddress) {
                    $postCode = $defaultShippingAddress->getPostcode();
                }
            } else {
                $storeData = $this->registry->getStoresData();
                $deliveryLocation = $storeData[Registry::GUEST_LOCATION] ?? "";
                if ($deliveryLocation) {
                    $postCode = $deliveryLocation['postcode'];
                }
            }
        }

        $stockAvailabilityMessage = $this->storeLocatorHelper->getStockAvailabilityMessage(
            $storeLocatorId, $shippingMethod, $sku = "", $postCode );

        foreach($stockAvailabilityMessage as $stock) {
            if ($stock['code'] === self::OUT_OF_STOCK_CODE) {
                $this->messageManager->addErrorMessage($stock['cart_message']->getText());
                $homeUrl = $this->url->getUrl("/");
                $this->responseFactory->create()->setRedirect($stock['url'] ?? $homeUrl)->sendResponse();
                return;
            }
        }
    }
}
