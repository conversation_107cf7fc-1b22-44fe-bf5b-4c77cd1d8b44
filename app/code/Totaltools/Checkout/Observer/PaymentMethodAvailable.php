<?php

namespace Totaltools\Checkout\Observer;

use Magento\Framework\Event\ObserverInterface;

use Magento\Checkout\Model\Session as CheckoutSession;


class PaymentMethodAvailable implements ObserverInterface
{
    const XML_PATH_DISABLE_PAYMENTMETHODS = 'checkout/total_payment/disable_payment_methods';
   /**
     * @param CheckoutSession
     */
    private $checkoutSession;

    /**
     * @param CheckoutSession $checkoutSession
     */
    public function __construct(
        CheckoutSession $checkoutSession
        ) {
        $this->checkoutSession = $checkoutSession;
        }

    /**
     * payment_method_is_active event handler.
     *
     * @param \Magento\Framework\Event\Observer $observer
     */
    public function execute(\Magento\Framework\Event\Observer $observer)
    {
        if ($this->checkoutSession->getQuote()->getIsVirtual()) {
            $paymentCode = $observer->getEvent()->getMethodInstance()->getCode();
            if($paymentCode !=='braintree'){
                $checkResult = $observer->getEvent()->getResult();
                $checkResult->setData('is_available', false); 
            }
        }
    }
}