<?php


namespace Totaltools\Checkout\Observer;


use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;
use Totaltools\Checkout\Helper\Data;

class AddHtmlToOrderShippingViewObserver implements ObserverInterface
{
    /**
     * @var Data
     */
    protected $helper;

    /**
     * @var \Magento\Framework\View\Element\TemplateFactory
     */
    protected $templateFactory;

    /**
     * AddHtmlToOrderShippingViewObserver constructor.
     * @param Data $helper
     * @param \Magento\Framework\View\Element\TemplateFactory $templateFactory
     */
    public function __construct(
        \Totaltools\Checkout\Helper\Data $helper,
        \Magento\Framework\View\Element\TemplateFactory $templateFactory
    ) {
        $this->helper = $helper;
        $this->templateFactory = $templateFactory;
    }

    public function execute(EventObserver $observer)
    {
        if ($observer->getElementName() == 'order_shipping_view'
            && ($this->helper->isThirdPartyPickupActive())
        ) {
            $orderShippingViewBlock = $observer->getLayout()
                ->getBlock($observer->getElementName());

            $order = $orderShippingViewBlock->getOrder();

            $pickupOrderOptions = $this->templateFactory->create();

            // Set the data for the block
            $pickupOrderOptions->setHelper($this->helper)
                ->setThirdPartyPickup($order->getThirdPartyPickup())
                ->setThirdPartyName($order->getThirdPartyName());

            $pickupOrderOptions->setTemplate('Totaltools_Checkout::order/view/pickup_options.phtml');

            // Append to the blocks output and set
            $html = $observer->getTransport()->getOutput() . $pickupOrderOptions->toHtml();
            $observer->getTransport()->setOutput($html);
        }
    }
}
