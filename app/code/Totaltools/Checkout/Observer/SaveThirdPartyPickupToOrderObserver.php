<?php


namespace Totaltools\Checkout\Observer;


use Magento\Framework\Event\Observer as EventObserver;
use Magento\Framework\Event\ObserverInterface;

class SaveThirdPartyPickupToOrderObserver implements ObserverInterface
{
    /**
     * @var string
     */
    const STORE_PICKUP = 'shippitcc_shippitcc';

    /**
     * @var \Totaltools\Checkout\Helper\Data
     */
    protected $helper;

    /**
     * @var \Magento\Quote\Model\QuoteRepository
     */
    protected $quoteRepository;

    /**
     * SaveThirdPartyNameToOrderObserver constructor.
     * @param \Totaltools\Checkout\Helper\Data $helper
     * @param \Magento\Quote\Model\QuoteRepository $quoteRepository
     */
    public function __construct(
        \Totaltools\Checkout\Helper\Data $helper,
        \Magento\Quote\Model\QuoteRepository $quoteRepository
    ) {
        $this->helper = $helper;
        $this->quoteRepository = $quoteRepository;
    }

    public function execute(EventObserver $observer)
    {
        if (!$this->helper->isThirdPartyPickupActive()) {
            return $this;
        }

        $order = $observer->getOrder();
        /** @var \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->get($order->getQuoteId());
        $shippingMethod = $quote->getShippingAddress()->getShippingMethod();

        if ($shippingMethod === self::STORE_PICKUP) {
            $order->setThirdPartyPickup($quote->getThirdPartyPickup());
            $order->setThirdPartyName($quote->getThirdPartyName());

            $order->setData('shippit_authority_to_leave', false);
            $order->setData('shippit_delivery_instructions', null);
        } else {
            $order->setThirdPartyPickup(false);
            $order->setThirdPartyName(null);
        }

        return $this;
    }
}
