<div class="selected-store">
    <!-- ko if: selectedStore() && selectedStore().zipcode && selectedStore().is_visible == '1'-->
    <p>Your order will be collected from:</p>
    <span data-bind="text: selectedStore().store_name + ', ' + selectedStore().zipcode"></span>
    <!-- /ko -->

    <!-- ko if: !selectedStore() || !selectedStore().zipcode || !selectedStore().is_visible == '1' -->
    <p data-bind="i18n: 'Please select your nearest store for click & collect.'"></p>
    <span>&nbsp;</span>
    <!-- /ko -->
</div>

<a href="#" data-bind="click: openModal">Change store</a>

<div id="change-store-modal" style="display:none;" data-bind="blockLoader: isLoading, afterRender: createModal">

    <h1>Find a Store</h1>
    <p data-bind="if: !stores().length">Please enter your postcode or suburb to see availability for your items and estimated delivery dates.</p>

    <div class="store-form">

        <div class="store-zipcode" data-bind="css: { 'searching': isSearching }">
            <input type="text" name="change-store-postcode" id="change-store-postcode-input" data-bind="event: {keyup: fetchSuburbs},
            attr: {placeholder: 'Postcode or Suburb', autocomplete: 'off'}" />
        </div>

        <!-- ko if: suburbs().length || stores().length -->
        <div class="store-options">

            <!-- ko if: stores().length -->
            <div class="nearest-store">
                <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                    <span data-bind="text: 'Find your nearest store'"></span>
                </button>
            </div>
            <!-- /ko -->
            
            <ul class="store-list">
                <!--ko foreach: { data: suburbs, as: 'suburb'} -->
                <li class="item-suburb">
                    <a data-bind="click: function(data, event) { $parent.fetchStores(postcode, event) }">
                        <span data-bind="html: cityHtml + ' ' + postcodeHtml"></span>
                    </a>
                </li>
                <!-- /ko -->

                <!--ko foreach: { data: stores, as: 'store'}-->
                <li class="item-store">
                    <div class="store">
                        <a class="store-name" data-bind="click: function(data, event) { $parent.selectStore(store, event) }"><span class="name" data-bind="text: $data.store_name"></span></a>
                        <div class="delivery-type" visible="window.checkoutConfig.stockMessages.enabled || stockMessage.code == 5">
                            <!-- stockMessage code is missing in the ajax response so using stock message to build css class -->
                            <span class="delivery-message" data-bind="css: {
                                  'available'      :  $data.stockMessage.code == 0,
                                  'low-stock'      :  $data.stockMessage.code == 1,
                                  'not-available'  :  $data.stockMessage.code >= 2
                                }, html: $data.stockMessage.cart_message">
                            </span>
                            <!--<span class="distance">7.7km away</span>-->
                        </div>
                        <span class="city" data-bind="text: $data.address + ' ' + $data.city + ', ' + $data.zipcode + ', ' + $data.state" style="display: block; clear: both;"></span>
                        <a class="store-phone" data-bind="text: $data.phone, attr: { href: 'tel:' + $data.phone} " style="display: block; clear: both;"></a>
                        <button data-bind="click: function(data, event) { $parent.selectStore(store, event) }, text: 'Set as my store'" class="button button-primary"></button>
                    </div>
                </li>
                <!-- /ko -->
            </ul>
        </div>
        <!-- /ko -->
        <div class="error_message_search_store" style="display: none" >
            <span data-bind="text: window.error_message_search_store"></span>
        </div>
        <!-- ko if: (showUseMyCurrentLocation())-->
        <div class="or">or</div>

        <div class="current-location">
            <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                <span>Use my current location</span>
            </button>
        </div>
        <!-- /ko -->

    </div>

</div>