<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
* @TODO: Replace 
* @TODO: Replace shipping address with billing address.
 */
-->

<!-- ko if: (isVisible()) -->
<div class="estimated-shipping" if="showClickAndCollectMessage || showDeliveryMessage">
    <!-- ko if: getShippingMethodCode() == getDefaultShippingMethodCode() -->
        <p visible="showClickAndCollectMessage">
            <span data-bind="text: clickAndCollectMessage()"></span>
            <span class="estimated-shipping-tooltip">
                <span class="tooltip-toggle"></span>
                <span class="tooltip-content" data-bind="text: clickAndCollectMessage()"></span>
            </span>
        </p>
    <!-- /ko -->
    <!-- ko ifnot: getShippingMethodCode() == getDefaultShippingMethodCode() -->
        <p visible="showDeliveryMessage">
            <span data-bind="text: deliveryMessage()"></span>
            <span class="estimated-shipping-tooltip">
                <span class="tooltip-toggle"></span>
                <span class="tooltip-content" data-bind="text: deliveryMessage()"></span>
            </span>
        </p>
    <!-- /ko -->
</div>
<div class="shipping-information">
    <div class="ship-via">
        
        <div class="shipping-information-title" id="shipping-method-title">
            <span data-bind="text: getShippingMethodTitle()"></span>
        </div>

        <div class="shipping-information-content">

            <!-- ko if: getShippingMethodCode() == getDefaultShippingMethodCode() && store() != null -->

                <p data-bind="i18n: 'Your selected click & collect address.'"></p>
                <div class="address-block">
                    <!-- ko text:store().store_name --><!-- /ko --><br />
                    <!-- ko text:store().address --><!-- /ko -->, <!-- ko text:store().city --><!-- /ko --><br />
                    <!-- ko text:store().zipcode --><!-- /ko --> <!-- ko text:store().state --><!-- /ko --><br />
                </div>
            <!-- /ko -->

             <!-- ko ifnot: getShippingMethodCode() == getDefaultShippingMethodCode() -->

                <p data-bind="i18n: 'Your delivery cost and ETA are based on your registered address.'"></p>
                <div class="address-block">           
                    <!-- ko foreach: getRegion('ship-to') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                    <!--/ko-->
                </div>
            <!-- /ko -->

            <div class="actions-toolbar">
                <a class="action action-edit" data-bind="click: back">
                    <span data-bind="i18n: 'Edit this address'"></span>
                </a>
            </div>
        </div>
    </div>
<!--/ko-->