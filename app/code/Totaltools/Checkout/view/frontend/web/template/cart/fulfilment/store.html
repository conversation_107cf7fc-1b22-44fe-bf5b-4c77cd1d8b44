<div class="fulfilment-store" data-bind="css: {active: isActive}">
    <header class="tab-header">
        <div class="selected-store">
            <!-- ko if: selectedStore() && selectedStore().zipcode && selectedStore().is_visible == '1'-->
            <p data-bind="i18n: 'Your order will be collected from:'"></p>
            <span data-bind="text: selectedStore().store_name + ', ' + selectedStore().zipcode"></span>
            <!-- /ko -->

            <!-- ko if: !selectedStore() || !selectedStore().zipcode || !selectedStore().is_visible == '1' -->
            <p data-bind="i18n: 'Please select your nearest store for click & collect.'"></p>
            <!-- /ko -->
        </div>

        <div class="change-store-trigger">
            <a href="#" data-bind="click: showModal, i18n: 'Change Store'"></a>
        </div>
    </header>

    <footer class="tab-footer">
        <p class="stock-message" visible="showStockMessage">
            <span class="estimated-time" data-bind="text: stockMessage"></span>
            <span class="fulfilment-tooltip">
                <span class="tooltip-toggle">
                    <span>?</span>
                </span>
                <span class="tooltip-content" data-bind="text: stockMessage"></span>
            </span>
        </p>
    </footer>
</div>

<div id="change-store-modal" class="change-store" data-bind="afterRender: createModal, blockLoader: isLoading" style="display: none;">
    <h1 data-bind="i18n: 'Find a Store'"></h1>
    <p data-bind="if: !stores().length">
        <span>Please enter your postcode or suburb to see availability for your items and estimated delivery dates.</span>
    </p>

    <div class="store-form">
        <div class="store-zipcode" data-bind="css: {'searching': isSearching()}">
            <input type="text" name="postcode" id="postcode" data-bind="event: {keyup: fetchSuburbs}, textInput: postcode" placeholder="Postcode or Suburb" autocomplete="new-password" />
        </div>
        
        <div class="store-options" data-bind="if: suburbs().length || stores().length">
            <div class="nearest-store" data-bind="if: stores().length">
                <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                    <span data-bind="i18n: 'Find your nearest store'"></span>
                </button>
            </div>

            <div class="suburbs-result" data-bind="if: suburbs().length">
                <ul class="store-list" data-bind="foreach: suburbs">
                    <li class="item-suburb">
                        <a data-bind="click: function(data, event){$parent.fetchStores($data, data, event)}">
                            <span data-bind="text: city + ' ' + postcode"></span>
                        </a>
                    </li>
                </ul>
            </div>
                        
            <div class="stores-result" data-bind="if: stores().length">
                <ul class="store-list" data-bind="foreach: stores">
                    <li class="item-store">
                        <div class="store">
                            <a class="store-name" data-bind="click: function(data, event) { $parent.selectStore($data, data, event) }">
                                <span class="name" data-bind="text: store_name"></span>
                            </a>
                            <div class="delivery-type" visible="window.checkoutConfig.stockMessages.enabled || stockMessage.code == 5">
                                <span class="delivery-message" data-bind="css: {
                                    'available'      :  stockMessage.code == 0,
                                    'low-stock'      :  stockMessage.code == 1,
                                    'not-available'  :  stockMessage.code >= 2
                                    }, text: stockMessage.cart_message">
                                </span>
                            </div>
                            <span class="city" data-bind="text: address + ' ' + city + ', ' + zipcode + ', ' + state" style="display: block; clear: both;"></span>
                            <a class="store-phone" data-bind="text: phone, attr: { href: 'tel:' + phone} " style="display: block; clear: both;"></a>
                            <button data-bind="click: function(data, event) { $parent.selectStore($data, data, event)}" class="button button-primary">
                                <span data-bind="i18n: 'Set as my store'"></span>
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        
        <div class="error_message_search_store" style="display: none">
            <span data-bind="text: window.error_message_search_store"></span>
        </div>
        
        <div class="current-location" data-bind="if: showUseMyCurrentLocation">
            <span class="or" data-bind="i18n: 'or'"></span>
            <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                <span data-bind="i18n: 'Use my current location'"></span>
            </button>
        </div>
    </div>
</div>
