<div class="emarsys-recommended homepage-block web-recommender-checkout" data-bind="blockLoader: isLoading, visible: isVisible, attr: { id: 'web-recommender-' + widgetId}">
    <div id="emarsys-recommended-container" class="block">
        <div class="block-title title">
            <strong id="block-upsell-heading" role="heading" translate="'You may also like'"></strong>
        </div>
        <div
            class="web-recommender block-content content"
            data-bind="afterRender: renderCallback"
        >
            <div class="products-grid grid" if="products().length" style="position: relative;">
                <ol
                    class="product-items widget-product-carousel emarsys-products-grid"
                    data-role="carousel"
                    data-bind="foreach: { data: products, as: 'product' }, afterRender: listRenderCallback, attr: { id: 'emarsys-list-' + widgetId }"
                >
                    <li
                        data-bind="attr: { 'data-scarabitem': product.id }"
                        class="scarab-item rec-item item product product-item"
                    >
                        <div class="product-item-info">
                            <a
                                data-bind="click: $parent.handleProductClick, attr: {
                                'href': product.link,
                                'data-id': product.id,
                                'data-name': product.title,
                                'data-price': product.price,
                                'data-brand': product.brand,
                                'data-store': window.AEC.storeName,
                                'data-position': $index() + 1,
                                'data-list': $parent.gtmList,
                                'data-category': $parent.gtmCategory
                            }"
                                data-quantity="1"
                                data-attributes="[]"
                                data-event="productClick"
                                class="product-item-photo"
                            >
                                <span class="product-image-container" style="width: 240px;">
                                    <span class="product-image-wrapper" style="padding-bottom: 100%;">
                                        <img
                                            class="product-image-photo"
                                            data-bind="attr: { 'src': product.image, 'alt': product.title }"
                                            width="240"
                                            height="240"
                                            loading="lazy"
                                        />
                                    </span>
                                </span>
                            </a>
                            <div class="product details product-item-details">
                                <strong
                                    class="product name product-item-name"
                                >
                                    <a
                                        data-bind="text: product.title, click: $parent.handleProductClick, attr: {
                                        'href': product.link,
                                        'data-id': product.id,
                                        'data-name': product.title,
                                        'data-price': product.price,
                                        'data-brand': product.brand,
                                        'data-store': window.AEC.storeName,
                                        'data-position': $index() + 1,
                                        'data-list': $parent.gtmList,
                                        'data-category': $parent.gtmCategory
                                    }"
                                        class="product-item-link"
                                        data-quantity="1"
                                        data-attributes="[]"
                                        data-event="productClick"
                                    ></a>
                                </strong>
                                    <div
                                        class="price-box price-final_price"
                                        data-role="priceBox"
                                        data-bind="attr: { 'data-product': product.id, 'data-price-box': 'product-id-' + product.id }"
                                    >
                                        <span class="price-container price-final_price tax">
                                            <span
                                                data-price-type="finalPrice"
                                                content=""
                                                class="price-wrapper"
                                                data-bind="attr: { id: 'product-price-' + product.id, 'data-price-amount': product.price }"
                                            >
                                                <span
                                                    class="price"
                                                    data-bind="html: $parent.formatPrice(product.price), visible:product.price > 0"
                                                ></span>
                                            </span>
                                        </span>
                                    </div>
                                <div class="product-item-actions" if="product.available">
                                    <div class="actions-primary">
                                        <ifnot args="$parent.addToCartEnabled">
                                            <button
                                                type="button"
                                                title="View Details"
                                                class="action tocart todetails primary"
                                                tabindex="0"
                                                data-bind="attr: {
                                                    'data-id': product.id,
                                                    'data-name': product.title,
                                                    'data-price': product.price,
                                                    'data-brand': product.brand,
                                                    'data-store': window.AEC.storeName,
                                                    'data-position': $index() + 1,
                                                    'data-list': $parent.gtmList,
                                                    'data-category': $parent.gtmCategory,
                                                    'data-link': encodeURI(product.link)
                                                }"
                                                data-quantity="1"
                                                data-attributes="[]"
                                                data-event="productClick"
                                            >
                                                <span translate="'View Details'"></span>
                                            </button>
                                        </ifnot>
                                        <if args="$parent.addToCartEnabled">
                                            <button
                                                type="submit"
                                                title="Add to Cart"
                                                class="action tocart primary"
                                                tabindex="0"
                                                data-bind="attr: {
                                                    'data-id': product.id,
                                                    'data-name': product.title,
                                                    'data-price': product.price,
                                                    'data-brand': product.brand,
                                                    'data-store': window.AEC.storeName,
                                                    'data-position': $index() + 1,
                                                    'data-list': $parent.gtmList,
                                                    'data-category': $parent.gtmCategory
                                                }"
                                                data-quantity="1"
                                                data-attributes="[]"
                                                data-event="addToCart"
                                            >
                                                <span translate="'Add to Cart'"></span>
                                            </button>
                                        </if>
                                    <div class="product attribute shipping-label large" if="product.c_is_free_delivery == 'TRUE'">
                                        <span class="value" translate="'Free Delivery'"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </li>
                </ol>
            </div>
        </div>
    </div>
</div>
<script id="web-recommender-template" type="text/html"></script>
