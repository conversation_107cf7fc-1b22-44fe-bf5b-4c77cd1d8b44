<div class="delivery" data-bind="if: notifications()[$parents[1].item_id]">
    <p data-bind="css: {
           'availability'   :  true,
           'available'      :  notifications()[$parents[1].item_id].code == 0,
           'low-stock'      :  notifications()[$parents[1].item_id].code == 1,
           'not-available'  :  notifications()[$parents[1].item_id].code >= 2
        }">
        <span class="delivery-tooltip">
            <span class="tooltip-toggle">
                <span>?</span>
            </span>
            <span class="tooltip-content" data-bind="text: notifications()[$parents[1].item_id].message"></span>
        </span>
        <span class="delivery-message" data-bind="text: notifications()[$parents[1].item_id].message"></span>
    </p>
</div>