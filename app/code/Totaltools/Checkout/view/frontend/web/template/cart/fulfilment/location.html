<div class="fulfilment-location" data-bind="css: {active: isActive}">
    <header class="tab-header">
        <div class="selected-suburb">
            <!-- ko if: !selectedAddress() || !selectedAddress().postcode -->
            <!-- ko if: selectedSuburb() && selectedSuburb().postcode -->
            <!-- ko if: !selectedSuburb().store || (selectedSuburb().store && selectedSuburb().store.is_visible == '1') -->
            <p data-bind="i18n: 'Your order will be delivered to:'"></p>
            <span data-bind="text: selectedSuburb().city + ', ' + selectedSuburb().postcode"></span>
            <!-- /ko -->
            <!-- /ko -->
            <!-- ko if: selectedSuburb() && !selectedSuburb().postcode -->
            <p data-bind="i18n: 'Please select your location to get delivery estimates.'"></p>
            <!-- /ko -->
            <!-- /ko -->

            <!-- ko if: selectedAddress() && selectedAddress().postcode -->
            <p data-bind="i18n: 'Your order will be delivered to:'"></p>
            <span data-bind="text: selectedAddress().city + ', ' + selectedAddress().postcode"></span>
            <!-- /ko -->
        </div>

        <div data-bind="if: selectedAddress() && selectedAddress().city && selectedAddress().postcode">
            <span data-bind="i18n: 'Based on your delivery address.'"></span>
        </div>

        <div class="change-location-trigger" if="selectedSuburb() && (!selectedAddress() || !selectedAddress().city || !selectedAddress().postcode)">
            <a href="#" data-bind="click: showModal, i18n: 'Change Location'"></a>
        </div>        
    </header>

    <footer class="tab-footer">
        <p class="stock-message" visible="showStockMessage()">
            <span data-bind="text: stockMessage"></span>
            <span class="fulfilment-tooltip">
                <span class="tooltip-toggle">
                    <span>?</span>
                </span>
                <span class="tooltip-content" data-bind="text: stockMessage"></span>
            </span>
        </p>
    </footer>
</div>

<div id="change-location-modal" class="change-location" data-bind="afterRender: createModal, blockLoader: isLoading" style="display: none;">
    <h1 data-bind="i18n: 'Select a Location'"></h1>
    <p>Please enter your postcode or suburb to see availability for your items and estimated delivery dates.</p>

    <div class="store-form">
        <div class="store-zipcode" data-bind="css: {'searching' : isSearching}">
            <input type="text" name="postcode" id="change-location-postcode" data-bind="event: {keyup: fetchSuburbs}, textInput: postcode" placeholder="Postcode or Suburb" autocomplete="off"
            />
        </div>
        
        <div class="store-options" data-bind="if: suburbs().length">
            <ul class="store-list" data-bind="foreach: suburbs">
                <li class="item-suburb">
                    <a data-bind="click: function(data, event) {$parent.selectLocation($data, data, event)}">
                        <span data-bind="html: city + ' ' + postcode"></span>
                    </a>
                </li>
            </ul>
        </div>
        
        <div class="error_message_search_store" style="display: none">
            <span data-bind="text: window.error_message_search_store"></span>
        </div>

        <div class="current-location" data-bind="if: showUseMyCurrentLocation()">
            <div class="or">or</div>
            <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                <span>Use my current location</span>
            </button>
        </div>
    </div>
</div>
