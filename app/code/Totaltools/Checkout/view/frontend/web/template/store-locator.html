<div class="store-locator-wrapper">
    <div class="store-selector">
        <div class="store-selector-wrapper">
            <div class="store-icon">
                <svg
                    enable-background="new 0 0 27 27"
                    height="28"
                    viewBox="0 0 27 27"
                    width="30px"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <g>
                        <path
                            class="st0"
                            d="M0.23,13.92c-0.05-0.21-0.04-0.44-0.03-0.66c0.01-0.29,0-0.58,0-0.86c0-0.63-0.01-1.25-0.01-1.88
                    c0-0.25-0.03-0.51,0.04-0.76c0.09-0.34,0.36-0.52,0.62-0.72c0.27-0.2,0.55-0.39,0.82-0.59C2.2,8.08,2.73,7.71,3.26,7.33
                    c1.05-0.75,2.11-1.5,3.16-2.25s2.11-1.5,3.16-2.25c0.35-0.25,0.7-0.5,1.05-0.75c0.18-0.12,0.35-0.25,0.53-0.37
                    c0.17-0.12,0.34-0.25,0.52-0.36c0.08-0.05,0.16-0.12,0.24-0.18c0.08-0.06,0.16-0.12,0.25-0.18c0.17-0.12,0.33-0.24,0.5-0.35
                    c0.19-0.13,0.39-0.25,0.6-0.37c0.08-0.04,0.16-0.09,0.26-0.09c0.07,0,0.14,0.03,0.21,0.07c0.36,0.18,0.68,0.42,1.01,0.65
                    c0.01,0,0.01,0.01,0.02,0.01c0.33,0.23,0.66,0.47,0.99,0.7c0.35,0.25,0.7,0.5,1.05,0.75s0.7,0.5,1.05,0.75
                    c0.35,0.25,0.7,0.5,1.05,0.75c0.35,0.25,0.7,0.5,1.05,0.75s0.7,0.5,1.05,0.75c0.35,0.25,0.7,0.5,1.05,0.75
                    c0.19,0.13,0.38,0.27,0.57,0.4c1.24,0.88,2.48,1.77,3.73,2.65c0.27,0.19,0.44,0.42,0.44,0.75c0.01,1.28,0.01,2.57,0,3.85
                    c0,0.32-0.15,0.59-0.45,0.74c-0.27,0.13-0.55,0.11-0.81-0.04c-0.1-0.06-0.2-0.13-0.3-0.21c-0.21-0.16-0.44-0.32-0.68-0.43
                    c0,0.16,0,0.28,0,0.4c0,3.8-0.01,7.59,0.01,11.39c0,0.62-0.42,1.21-1.21,1.2c-0.59-0.01-1.18,0-1.77,0c-0.09,0-0.18-0.01-0.31-0.02
                    c0-2.24,0-4.45,0-6.65c0-2.21,0-4.41,0-6.63c-5.22,0-10.38,0-15.59,0c0,0.16,0,0.3,0,0.45c0,4.14,0,8.29,0,12.43
                    c0,0.13-0.01,0.27-0.02,0.41c-0.78,0-1.53,0.02-2.27-0.01c-0.4-0.02-0.71-0.24-0.87-0.61c-0.08-0.18-0.11-0.4-0.11-0.6
                    c-0.01-3.79,0-7.58,0-11.36c0-0.12,0-0.23,0-0.4C2.1,14,1.8,14.26,1.47,14.46c-0.44,0.27-1,0.07-1.2-0.4
                    C0.26,14.02,0.24,13.97,0.23,13.92z"
                        ></path>
                        <path
                            class="st0"
                            d="M7.46,17.68v-2.24c0-0.12,0.1-0.22,0.22-0.22h11.67c0.12,0,0.22,0.1,0.22,0.22v2.24c0,0.12-0.1,0.22-0.22,0.22
                    H7.68C7.56,17.9,7.46,17.8,7.46,17.68z"
                        ></path>
                        <path
                            class="st0"
                            d="M7.46,22.12v-2.24c0-0.12,0.1-0.22,0.22-0.22h11.67c0.12,0,0.22,0.1,0.22,0.22v2.24c0,0.12-0.1,0.22-0.22,0.22
                    H7.68C7.56,22.34,7.46,22.24,7.46,22.12z"
                        ></path>
                        <path
                            class="st0"
                            d="M7.46,26.57v-2.24c0-0.12,0.1-0.22,0.22-0.22h11.67c0.12,0,0.22,0.1,0.22,0.22v2.24c0,0.12-0.1,0.22-0.22,0.22
                    H7.68C7.56,26.79,7.46,26.69,7.46,26.57z"
                        ></path>
                    </g>
                </svg>
            </div>
            <if args="store() && store().zipcode && store().is_visible == '1'">
                <div class="store-selected" data-toggle="dropdown" aria-haspopup="true" data-bind="mageInit: {'dropdown': { 'menu': '.store-dropdown' }}">
                    <span class="store-name" data-bind="text: store().store_name"></span>
                    <div class="store-details">
                            <div class="store-dropdown">
                                <span class="action toggle store-status">
                                    <span class="store-status-time-icon" data-bind="css: {'open': storeIsOpen()}">
                                        <svg version="1.1" id="Capa_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" width="16px" height="16px" viewBox="0 0 559.98 559.98" style="enable-background:new 0 0 559.98 559.98;"
                                                xml:space="preserve">
                                            <g>
                                                <g>
                                                    <path d="M279.99,0C125.601,0,0,125.601,0,279.99c0,154.39,125.601,279.99,279.99,279.99c154.39,0,279.99-125.601,279.99-279.99
                                                        C559.98,125.601,434.38,0,279.99,0z M279.99,498.78c-120.644,0-218.79-98.146-218.79-218.79
                                                        c0-120.638,98.146-218.79,218.79-218.79s218.79,98.152,218.79,218.79C498.78,400.634,400.634,498.78,279.99,498.78z"></path>
                                                    <path d="M304.226,280.326V162.976c0-13.103-10.618-23.721-23.716-23.721c-13.102,0-23.721,10.618-23.721,23.721v124.928
                                                        c0,0.373,0.092,0.723,0.11,1.096c-0.312,6.45,1.91,12.999,6.836,17.926l88.343,88.336c9.266,9.266,24.284,9.266,33.543,0
                                                        c9.26-9.266,9.266-24.284,0-33.544L304.226,280.326z"></path>
                                                </g>
                                            </g>
                                        </svg>
                                    </span>
                                    <span class="store-status-text" data-bind="if: storeIsOpen()">
                                        Open <span data-bind="html: '&mdash; Closes ' + storeMessage()"></span>
                                    </span>
                                    <span class="store-status-text" data-bind="ifnot: storeIsOpen()">
                                        Closed <span data-bind="html: storeMessage()"></span>
                                    </span>
                                </span>
                                <div class="store-dropdown-options" data-target="dropdown" data-bind="afterRender: postRender">
                                    <h3 class="store-title" data-bind="text: store().store_name"></h3>
                                    <div class="store-address" data-bind="if: store().address">
                                        <span class="store-street" data-bind="text: store().address"></span>
                                        <span class="store-city" data-bind="if: store().city">
                                            <span text="store().city"></span>, <span text="store().zipcode"></span>, <span text="store().state"></span><span translate="' Australia'"></span>
                                        </span>
                                        <span class="store-phone" data-bind="if: store().phone">
                                            <span>Phone:</span>
                                            <a data-bind="text: store().phone, attr: {href: 'tel:' + store().phone}, click: function() {return true;}, clickBubble: false"></a>
                                        </span>
                                        <span class="store-email" data-bind="if: store().email">
                                            <span>Email:</span>
                                            <a data-bind="text: store().email, attr: {href: 'mailto:' + store().email}, click: function() {return true;}, clickBubble: false"></a>
                                        </span>
                                    </div>
                                    <div class="store-directions">
                                        <button class="btn btn-directions" style="display: none;">
                                            <span translate="'Get Directions Here'"></span>
                                        </button>
                                    </div>
                                    <div class="store-hours">
                                        <h3 class="schedule-title" data-bind="i18n: 'Trading Hours'"></h3>
                                        <ul class="store-timings">
                                            <!-- Monday -->
                                            <li>
                                                <span class="day">Monday:</span>
                                                <span class="time" data-bind="if: store().monday_status == 1">
                                                    <span data-bind="text: store().monday_open"></span> - <span data-bind="text: store().monday_close"></span>
                                                </span>
                                                <span class="time" data-bind="if: store().monday_status == 2">
                                                    <span data-bind="text: 'Closed'"></span>
                                                </span>
                                            </li>
                                    
                                            <!-- Tuesday -->
                                            <li>
                                                <span class="day">Tuesday:</span>
                                                <span class="time" data-bind="if: store().tuesday_status == 1">
                                                    <span data-bind="text: store().tuesday_open"></span> - <span data-bind="text: store().tuesday_close"></span>
                                                </span>
                                                <span class="time" data-bind="if: store().tuesday_status == 2">
                                                    <span data-bind="text: 'Closed'"></span>
                                                </span>
                                            </li>
                                    
                                            <!-- Wednesday -->
                                            <li>
                                                <span class="day">Wednesday:</span>
                                                <span class="time" data-bind="if: store().wednesday_status == 1">
                                                    <span data-bind="text: store().wednesday_open"></span> - <span data-bind="text: store().wednesday_close"></span>
                                                </span>
                                                <span class="time" data-bind="if: store().wednesday_status == 2">
                                                    <span data-bind="text: 'Closed'"></span>
                                                </span>
                                            </li>
                                    
                                            <!-- Thursday -->
                                            <li>
                                                <span class="day">Thursday:</span>
                                                <span class="time" data-bind="if: store().thursday_status == 1">
                                                    <span data-bind="text: store().thursday_open"></span> - <span data-bind="text: store().thursday_close"></span>
                                                </span>
                                                <span class="time" data-bind="if: store().thursday_status == 2">
                                                    <span data-bind="text: 'Closed'"></span>
                                                </span>
                                            </li>
                                    
                                            <!-- Friday -->
                                            <li>
                                                <span class="day">Friday:</span>
                                                <span class="time" data-bind="if: store().friday_status == 1">
                                                    <span data-bind="text: store().friday_open"></span> - <span data-bind="text: store().friday_close"></span>
                                                </span>
                                                <span class="time" data-bind="if: store().friday_status == 2">
                                                    <span data-bind="text: 'Closed'"></span>
                                                </span>
                                            </li>
                                    
                                            <!-- Saturday -->
                                            <li>
                                                <span class="day">Saturday:</span>
                                                <span class="time" data-bind="if: store().saturday_status == 1">
                                                    <span data-bind="text: store().saturday_open"></span> - <span data-bind="text: store().saturday_close"></span>
                                                </span>
                                                <span class="time" data-bind="if: store().saturday_status == 2">
                                                    <span data-bind="text: 'Closed'"></span>
                                                </span>
                                            </li>
                                    
                                            <!-- Sunday -->
                                            <li>
                                                <span class="day">Sunday:</span>
                                                <span class="time" data-bind="if: store().sunday_status == 1">
                                                    <span data-bind="text: store().sunday_open"></span> - <span data-bind="text: store().sunday_close"></span>
                                                </span>
                                                <span class="time" data-bind="if: store().sunday_status == 2">
                                                    <span data-bind="text: 'Closed'"></span>
                                                </span>
                                            </li>
                                    
                                            <!-- Public Holidays -->
                                            <li>
                                                <span class="day">Public Holidays:</span>
                                                <span class="time">Please call store.</span>
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div  data-bind="if: storeHasHoliday()">
                                        <div class="store-hours store-holiday" >
                                            <h3 class="schedule-title" data-bind="i18n: 'Upcoming Holiday'"></h3>
                                            <div class="table-responsive">
                                                <table class="table">
                                                    <tbody>
                                                        <tr>
                                                            <td class="time-label" >
                                                                <div class="event-label" data-bind="text: store().upcoming_holiday.name"></div>
                                                                <ul  data-bind="foreach: store().upcoming_holiday.date">
                                                                    <li>
                                                                        <span class="event-date" data-bind="text: $parent.formateDate($data)"></span>
                                                                        <span class="status-label">Closed</span>
                                                                    </li>
                                                                </ul>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- special day -->
                                    <div   data-bind="if: storeHasSpecialDays()">
                                        <div class="store-hours store-spcecial-day">
                                            <h3 class="schedule-title" data-bind="i18n: 'Upcoming Special Day'"></h3>
                                            <div class="table-responsive">
                                                <table class="table">
                                                    <tbody>
                                                        <tr class="special-day">
                                                            <td class="event-start">
                                                                <div class="event-start-wrap" data-bind="if: specialDaysStart">
                                                                    <span class="event-start-month" data-bind="text:specialDaysStart[0]"></span>
                                                                    <span class="event-start-day" data-bind="text:specialDaysStart[1]"></span>
                                                                </div>
                                                            </td>
                                                            <td class="event-info">
                                                                <div class="event-label" data-bind="text: store().upcoming_special_day.name"></div>
                                                                <ul  data-bind="foreach: store().upcoming_special_day.date">
                                                                    <li>
                                                                        <span class="event-date" data-bind="text: $parent.formateDate($data)"></span>
                                                                        <!-- ko if: $parent.specialDayOpenTime() -->
                                                                            <span class="event-time" data-bind="text: $parent.store().upcoming_special_day.time_open+' — '+$parent.store().upcoming_special_day.time_close"></span>
                                                                        <!-- /ko -->
                                                                        <!-- ko ifnot: $parent.specialDayOpenTime() -->
                                                                            <span class="status-label open">Open</span>
                                                                        <!-- /ko -->
                                                                    </li>
                                                                </ul>

                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="store-actions">
                                        <button class="btn btn-details" data-bind="click: storeUrl">
                                            <span data-bind="i18n: 'View Store Details'"></span>
                                        </button>
                                        <button class="btn btn-change" data-bind="click: showModal">
                                            <span data-bind="i18n: 'Change Store'"></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                    </div>
                </div>
            </if>
            <div class="store-change" ifnot="store() && store().zipcode && store().is_visible == '1'">
                <span click="showModal" translate="'Select Your Store'"></span>
            </div>
        </div>
    </div>
</div>
<div id="change-store" class="change-store" data-bind="afterRender: createModal" style="display: none;">
    <h1 data-bind="i18n: 'Find a Store'"></h1>
    <p data-bind="if: !stores().length">
        <span>Please enter your postcode or suburb to see availability for your items and estimated delivery dates.</span>
    </p>

    <div class="store-form">
        <div class="store-zipcode" data-bind="css: {'searching': isSearching()}">
            <input type="text" name="postcode" id="postcode" data-bind="event: {keyup: fetchSuburbs}, textInput: postcode" placeholder="Postcode or Suburb" autocomplete="new-password" />
        </div>

        <div class="store-options" data-bind="if: suburbs().length || stores().length">
            <div class="nearest-store" data-bind="if: stores().length">
                <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                    <span data-bind="i18n: 'Find your nearest store'"></span>
                </button>
            </div>

            <div class="suburbs-result" data-bind="if: suburbs().length">
                <ul class="store-list" data-bind="foreach: suburbs">
                    <li class="item-suburb">
                        <a data-bind="click: function(data, event){$parent.fetchStores($data, data, event)}">
                            <span data-bind="text: city + ' ' + postcode"></span>
                        </a>
                    </li>
                </ul>
            </div>

            <div class="stores-result" data-bind="if: stores().length">
                <ul class="store-list" data-bind="foreach: stores">
                    <li class="item-store">
                        <div class="store">
                            <a class="store-name" data-bind="click: function(data, event) { $parent.selectStore($data, data, event) }">
                                <span class="name" data-bind="text: store_name"></span>
                            </a>
                            <span class="city" data-bind="text: address + ' ' + city + ', ' + zipcode + ', ' + state" style="display: block; clear: both;"></span>
                            <a class="store-phone" data-bind="text: phone, attr: { href: 'tel:' + phone} " style="display: block; clear: both;"></a>
                            <button data-bind="click: function(data, event) { $parent.selectStore($data, data, event)}" class="button button-primary">
                                <span data-bind="i18n: 'Set as my store'"></span>
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

        <div class="error_message_search_store" style="display: none">
            <span data-bind="text: window.error_message_search_store"></span>
        </div>

        <div class="current-location" data-bind="if: showUseMyCurrentLocation">
            <span class="or" data-bind="i18n: 'or'"></span>
            <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                <span data-bind="i18n: 'Use my current location'"></span>
            </button>
        </div>
    </div>
</div>
