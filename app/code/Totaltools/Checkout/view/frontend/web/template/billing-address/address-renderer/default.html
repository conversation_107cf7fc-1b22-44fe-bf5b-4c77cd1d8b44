<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="billing-address-item" data-bind="css: isSelected() ? 'selected-item' : 'not-selected-item'">
    <div class="address-wrapper">
        <address>
            <!-- ko text: address().prefix --><!-- /ko --> <!-- ko text: address().firstname --><!-- /ko -->
            <!-- ko text: address().lastname --><!-- /ko --> <!-- ko text: address().suffix --><!-- /ko --><br/>
            <!-- ko text: address().street --><!-- /ko -->, <!-- ko text: address().city --><!-- /ko --><br />
            <!-- ko text: address().region --><!-- /ko --> <!-- ko text: address().postcode --><!-- /ko -->, 
            <!-- ko text: getCountryName(address().countryId) --><!-- /ko --><br/>
            <!-- ko text: address().telephone --><!-- /ko --><br/>
            <!-- ko foreach: { data: address().customAttributes, as: 'element' } -->
                <!-- ko foreach: { data: Object.keys(element), as: 'attribute' } -->
                    <!-- ko if: element[attribute].value != null -->
                        <!-- ko text: element[attribute].value --><!-- /ko --><br/>
                    <!-- /ko -->
                    <!-- ko if: element[attribute].value == null -->
                        <!-- ko text: element[attribute] --><!-- /ko --><br/>
                    <!-- /ko -->
                <!-- /ko -->
            <!-- /ko -->
            <!-- ko if: (address().isEditable()) -->
            <button type="button"
                    class="action edit-address-link"
                    data-bind="click: editAddress, visible: address().isEditable()">
                <span data-bind="i18n: 'Edit'"></span>
            </button>
            <!-- /ko -->
        </address>
    
        <button type="button" data-bind="click: selectAddress" class="action action-select-billing-item">
            <span data-bind="i18n: 'Use this Address'"></span>
        </button>
    </div>
</div>
