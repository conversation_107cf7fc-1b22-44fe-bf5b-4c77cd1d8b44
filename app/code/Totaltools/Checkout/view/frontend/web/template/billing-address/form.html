<div class="form form-billing-address" id="co-billing-form">
    <!-- ko foreach: getRegion('before-fields') -->
    <!-- ko template: getTemplate() --><!-- /ko -->
    <!--/ko-->
    <fieldset id="billing-new-address-form" class="fieldset address">
        <!-- ko foreach: getRegion('additional-fieldsets') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <!-- ko if: (isCustomerLoggedIn) -->
        <div class="field save-address">
            <input type="checkbox" class="checkbox" id="billing-save-in-address-book" data-bind="checked: saveInAddressBook" />
            <label class="label" for="billing-save-in-address-book">
                <span data-bind="i18n: 'Save in address book'"></span>
            </label>
        </div>

        <div>
             <span class="help-block">Please note that you can save only one new address to your address book during checkout process. If you wish to add multiple addresses then please use My Account section.</span>
        </div>
        <!-- /ko -->
    </fieldset>
</div>
