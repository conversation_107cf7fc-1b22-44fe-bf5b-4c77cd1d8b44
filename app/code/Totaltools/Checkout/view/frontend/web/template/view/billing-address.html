<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="checkout-billing-address">
    <h3 class="step-title" translate="getNameBillingAddress()"></h3>
    <each args="getRegion('billing-address-before')" render=""></each>
    <div class="billing-address-same-as-shipping-block field choice" data-bind="visible: canUseShippingAddress()">
        <input type="checkbox" name="billing-address-same-as-shipping"
               data-bind="checked: isAddressSameAsShipping, click: useShippingAddress, attr: {id: 'billing-address-same-as-shipping-' + getCode($parent)}"/>
        <label data-bind="attr: {for: 'billing-address-same-as-shipping-' + getCode($parent)}"><span
                data-bind="i18n: 'My billing and shipping address are the same'"></span></label>
    </div>
    <render args="detailsTemplate"></render>
    <fieldset class="fieldset" data-bind="visible: !isAddressDetailsVisible()">
        <each args="getRegion('billing-address-list')" render=""></each>
        <div data-bind="fadeVisible: isAddressFormVisible">
            <render args="formTemplate"></render>
        </div>
        <render args="actionsTemplate"></render>
    </fieldset>
    <each args="getRegion('billing-address-after')" render=""></each>
</div>
