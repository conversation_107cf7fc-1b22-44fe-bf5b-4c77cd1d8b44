<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="product" data-bind="{css: {'product-special' : hasSpecialPrice($parent), 'product-free-shipping' : hasFreeShipping($parent)}}">
    <!-- ko foreach: getRegion('before_details') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
    <!-- /ko -->
    <div class="product-item-details">

        <div class="product-item-inner">
            <div class="product-item-name-block">
                <strong class="product-item-name" data-bind="text: $parent.name"></strong>
            </div>
        </div>

        <!-- ko if: (JSON.parse($parent.options).length > 0)-->
        <div class="product options product-options" data-bind="mageInit: {'collapsible':{'openedState': 'active'}}">
            <span data-role="title" class="toggle"><!-- ko i18n: 'View Details' --><!-- /ko --></span>
            <div data-role="content" class="content">
                <strong class="subtitle"><!-- ko i18n: 'Options Details' --><!-- /ko --></strong>
                <dl class="item-options">
                    <!--ko foreach: JSON.parse($parent.options)-->
                    <dt class="label" data-bind="text: label"></dt>
                        <!-- ko if: ($data.full_view)-->
                        <dd class="values" data-bind="html: full_view"></dd>
                        <!-- /ko -->
                        <!-- ko ifnot: ($data.full_view)-->
                        <dd class="values" data-bind="html: value"></dd>
                        <!-- /ko -->
                    <!-- /ko -->
                </dl>
            </div>
        </div>
        <!-- /ko -->        

        <div class="product-price">
            <!-- ko foreach: getRegion('after_details') -->
                <!-- ko template: getTemplate() --><!-- /ko -->
            <!-- /ko -->
        </div>

        <div class="product-qty">
            <span data-bind="click: function(data, event) { updateItemQty($parent.item_id, $parent.qty + 1) }" class="increment control"><span>+</span></span>
            <input class="value form-control" data-bind="value: $parent.qty, event: {change: function(data, event) { updateItemQty($parent.item_id, $parent.qty) }}" />
            <span data-bind="click: function(data, event) { updateItemQty($parent.item_id, $parent.qty - 1) }" class="decrement control"><span>-</span></span>
        </div>

        <div class="product-remove">
            <a data-bind="i18n: 'Remove', click: function(data, event) { removeItem($parent.item_id) }"></a>
        </div>
    </div>

</div>

<!-- ko if: (showNotifications())-->
    <!-- ko foreach: getRegion('notifications') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
    <!-- /ko -->
<!-- /ko -->

