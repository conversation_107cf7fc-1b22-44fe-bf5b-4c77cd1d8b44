<div class="store-selector">
    <div class="selected-store">
         <!-- ko if: selectedStore() && selectedStore().zipcode && selectedStore().is_visible == '1'-->
        <span class="store-name" data-bind="text: selectedStore().store_name + ', ' + selectedStore().zipcode"></span>
         <!-- /ko -->
        <a class="change-store" href="#"  data-bind="click: showFormPopUp, clickBubble: false">Change Store</a>
    </div>
    <a class="select-store" href="#" data-bind="i18n: 'Select Your Store'"></a>
</div>

<div id="store-select-modal" style="display:none;">

    <h1>Find a Store</h1>
    <p data-bind="if: !stores().length">Please enter your postcode or suburb to see availability for your items and estimated delivery dates.</p>

    <div class="store-form">

        <div class="store-zipcode" data-bind="css: { 'searching' : stores().length || suburbs().length }">
            <input type="text" name="change-store-postcode" id="store-select-postcode-input" data-bind="event: {keyup: fetchSuburbs},
            attr: {placeholder: 'Postcode or Suburb', autocomplete: 'new-password'}" />
        </div>

        <!-- ko if: suburbs().length || stores().length -->
        <div class="store-options">

            <!-- ko if: stores().length -->
            <div class="nearest-store">
                <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                    <span data-bind="text: 'Find your nearest store'"></span>
                </button>
            </div>
            <!-- /ko -->

            
            <ul class="store-list">
                <!--ko foreach: { data: suburbs, as: 'suburb'} -->
                <li class="item-suburb">
                    <a data-bind="click: function(data, event) { $parent.fetchStores(city, postcode, event) }">
                        <span data-bind="html: cityHtml + ' ' + postcodeHtml"></span>
                    </a>
                </li>
                <!-- /ko -->

                <!--ko foreach: { data: stores, as: 'store'}-->
                <li class="item-store">
                    <div class="store">
                        <a class="store-name" data-bind="click: function(data, event) { $parent.selectStore(store, event) }"><span class="name" data-bind="text: $data.store_name"></span></a>
                        
                        <span class="city" data-bind="text: $data.address + ' ' + $data.city + ', ' + $data.zipcode + ', ' + $data.state" style="display: block; clear: both;"></span>
                        <a class="store-phone" data-bind="text: $data.phone, attr: { href: 'tel:' + $data.phone} " style="display: block; clear: both;"></a>
                        <button data-bind="click: function(data, event) { $parent.selectStore(store, event) }, text: 'Set as my store'" class="button button-primary"></button>
                    </div>
                </li>
                <!-- /ko -->
            </ul>
        </div>
        <!-- /ko -->
        <div class="error_message_search_store" style="display: none" >
            <span data-bind="text: window.error_message_search_store"></span>
        </div>
        <!-- ko if: (showUseMyCurrentLocation())-->
        <div class="or">or</div>

        <div class="current-location">
            <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                <span>Use my current location</span>
            </button>
        </div>
        <!-- /ko -->

    </div>

</div>