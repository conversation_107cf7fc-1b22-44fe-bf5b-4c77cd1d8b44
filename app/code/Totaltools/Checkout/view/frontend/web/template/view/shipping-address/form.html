<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<form class="form form-shipping-address" id="co-shipping-form" data-bind="visible: isVisible, attr: {'data-hasrequired': $t('* Required Fields')}">
    <!-- ko foreach: getRegion('before-fields') -->
    <!-- ko template: getTemplate() --><!-- /ko -->
    <!--/ko-->
    <div id="shipping-new-address-form" class="fieldset address">
        <!-- ko foreach: getRegion('additional-fieldsets') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->
        <!-- ko if: (isCustomerLoggedIn) -->
        <div class="field choice" data-bind="visible: !isFormInline">
            <input type="checkbox" class="checkbox" id="shipping-save-in-address-book" data-bind="checked: saveInAddressBook" />
            <label class="label" for="shipping-save-in-address-book">
                <span data-bind="i18n: 'Save in address book'"></span>
            </label>
        </div>
        <!-- /ko -->
    </div>
</form>
