<div class="free-shipping-container" if="canBeFreeDelivery()" data-bind="class: freeShipping() ?  'success' : 'in-progress'">
    <p
        class="free-shipping-message"
        html="$t('Chuck more tools in the cart, <strong>Free shipping over $99</strong>')"
        ifnot="freeShipping()"
    ></p>
    <p
        class="free-shipping-message"
        translate="'Mat<PERSON>, you\'ve got free shipping!'"
        if="freeShipping()"
    ></p>
    <div class="free-shipping-progress">
        <span data-bind="style: { width: percentComplete() + '%' } "></span>
    </div>
</div>
<div class="checkout-toolbar">
    <div class="checkout-secure">
        <button class="action primary checkout" data-bind="click: function() {location.href = window.checkout.checkoutUrl}">
            <span translate="'Checkout Securely'"></span>
        </button>
    </div>
    <div class="store-features">
        <span class="fast-delivery" translate="'Fast Delivery'"></span>
        <span class="online-returns" translate="'30 Day Online Returns*'"></span>
    </div>
</div>
