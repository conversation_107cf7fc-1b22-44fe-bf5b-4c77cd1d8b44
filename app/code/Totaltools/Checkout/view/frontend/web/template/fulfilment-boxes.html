<!--
/**
* Copyright © 2018-19 TotalTools. All rights reserved.
* @author: Totaltools Dev Team
*/    
-->
<li id="fulfilment-boxes" class="fulfilment-boxes" data-bind="fadeVisible: isVisible">    
    <div class="tabs-container" id="fulfillment-tabs">
        <div class="content-tabs">
             <div class="tab-content fulfilment-store" id="collect-method" data-role="content"  data-bind="css: {'active': selectedMethod() && selectedMethod() == window.cc_method_name}, blockLoader: clickAndCollectLoading">
                <header class="tab-header">
                    <!-- ko foreach: getRegion('change-store') -->
                        <!-- ko template: getTemplate() --><!-- /ko -->
                    <!--/ko-->
                </header>
                <footer class="tab-footer">
                    <p class="estimate" visible="showClickAndCollectMessage">
                        <span class="estimated-time" data-bind="text: clickAndCollectMessage()"></span>
                        <span class="fulfilment-tooltip">
                            <span class="tooltip-toggle">
                                <span>?</span>
                            </span>
                            <span class="tooltip-content" data-bind="text: clickAndCollectMessage()"></span>
                        </span>
                    </p>
                </footer>
            </div>

            <div class="tab-content fulfilment-location" id="deliver-method" data-role="content" data-bind="css: {'active': selectedMethod() && selectedMethod() != window.cc_method_name}, blockLoader: deliveryLoading">
                <header class="tab-header">
                    <!-- ko foreach: getRegion('change-location') -->
                        <!-- ko template: getTemplate() --><!-- /ko -->
                    <!--/ko-->
                </header>
                <footer class="tab-footer">
                    <p class="estimate" visible="showDeliveryMessage">
                        <span class="estimated-time" data-bind="text: deliveryMessage()"></span>
                        <span class="fulfilment-tooltip">
                            <span class="tooltip-toggle">
                                <span>?</span>
                            </span>
                            <span class="tooltip-content" data-bind="text: deliveryMessage()"></span>
                        </span>
                    </p>
                </footer>
            </div>
        </div>
    </div>
</li>
