<div class="checkout-billing-address inline-billing-address">
    <div class="step-title" data-bind="i18n: 'Billing Address'"></div>
    <div class="billing-address-same-as-billing-block field choice" data-bind="visible: isCheckboxVisible()">
        <input type="checkbox" class="checkbox" name="billing-address-same-as-shipping"
               data-bind="checked: isAddressSameAsShipping, click: useShippingAddress, attr: {id: 'billing-address-same-as-shipping'}"/>
        <label data-bind="attr: {for: 'billing-address-same-as-shipping'}">
            <span data-bind="i18n: 'My billing and delivery address are the same'"></span>
        </label>
    </div>
    <div data-bind="visible: !isAddressSameAsShipping() || !isCheckboxVisible()">
        <fieldset class="fieldset">
            <!-- ko foreach: getRegion('address-list') -->
                <!-- ko template: getTemplate() --><!-- /ko -->
            <!--/ko-->
        </fieldset>

        <!-- ko if: (!isFormInline) -->
            <button type="button"
                    data-bind="click: showFormPopUp, visible: !isNewAddressAdded()"
                    class="action action-show-popup button button-primary__outline button-medium">
                <span data-bind="i18n: 'New Address'"></span>
            </button>

            <div id="opc-new-billing-address" data-bind="visible: isFormPopUpVisible()">
                <!-- ko template: 'Totaltools_Checkout/billing-address/form' --><!-- /ko -->
            </div>
        <!-- /ko -->

        <!-- ko foreach: getRegion('before-form') -->
            <!-- ko template: getTemplate() --><!-- /ko -->
        <!--/ko-->

        <!-- ko if: (isFormInline) -->
            <!-- ko template: 'Magento_Checkout/billing-address/form' --><!-- /ko -->
        <render if="quoteIsVirtual || manualAddressInput" args="actionsTemplate"></render>
        <!-- /ko -->
    </div>
</div>
