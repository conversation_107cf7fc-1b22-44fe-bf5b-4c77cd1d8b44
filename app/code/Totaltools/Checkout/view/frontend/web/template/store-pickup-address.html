<!--
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<section id="pickup" class="checkout-pickup" data-bind="fadeVisible: isVisible">
    <div
        id="checkout-step-shipping-pickup"
        class="checkout-pickup-content"
        data-role="content"
    >
        <div class="store-detail">
            <div class="store-detail-content">
                <div class="block-title _required">
                    <h2 translate="'Click & Collect Address'"></h2>
                </div>
                <div class="stock-status" if="hasOutOfStockItems">
                    <span translate="'Sorry mate, ' + outOfStockMessage() + ' out of stock at your selected store - try another below!'"></span>
                    <strong translate="'Or swap to fast delivery for assured availability.'"></strong>
                </div>
                <h5 class="selected-store-label" if="selectedStore() && selectedStore().zipcode" translate="'Selected store'"></h5>
                </div>
                <div
                    class="selected-store"
                    if="selectedStore() && selectedStore().zipcode"
                >
                    <div class="bordered-box">
                        <a
                            href="#"
                            class="remove-item"
                            data-bind="click: removeStore"
                        >
                            <span>X</span>
                        </a>
                        <div class="box-content">
                            <div class="store-name">
                                <span
                                    data-bind="text: selectedStore().store_name"
                                ></span>
                                <p ifnot="showStockMessage">
                                    <span text="selectedStore().address"></span>
                                    <span text="selectedStore().city"></span>
                                    <span text="selectedStore().zipcode"></span>
                                    <span text="selectedStore().state"></span>.
                                </p>
                            </div>
                            <div
                                visible="showStockMessage"
                                data-bind="css: {
                                   'store-info'   :  true,
                                   'availability'   :  true,
                                   'available'      :  clickAndCollectCode() == 0,
                                   'low-stock'      :  clickAndCollectCode() == 1,
                                   'not-available'  :  clickAndCollectCode() >= 2
                                }"
                            >
                                <span
                                    class="left"
                                    text="clickAndCollectMessage()"
                               ></span>
                            </div>
                        </div>
                    </div>
                    <p if="showStockMessage">
                        <span text="selectedStore().address"></span>
                        <span text="selectedStore().city"></span>
                        <span text="selectedStore().zipcode"></span>
                        <span text="selectedStore().state"></span>.
                    </p>
                    <p class="store-links">
                        <a
                            class="store-direction-url"
                            data-bind="i18n: 'Get Directions', attr: { href: selectedStore().store_direction_url }"
                            target="_blank"
                       ></a>
                        &nbsp;|&nbsp;
                        <span
                            class="store-location-edit"
                            data-bind="i18n: 'Change Click & Collect Location', click: removeStore"
                       ></span>
                    </p>
                    <h4 translate="'Phone'"></h4>
                    <a
                        class="store-phone"
                        data-bind="text: selectedStore().phone, attr: { href: 'tel:' + selectedStore().phone }"
                    ></a>
                    <h4 translate="'Trading Hours'"></h4>
                    <table class="store-schedule">
                        <tr>
                            <td class="row">Monday:</td>
                            <td>
                                <span
                                    data-bind="text: selectedStore().monday_open"
                                ></span>
                                -
                                <span
                                    data-bind="text: selectedStore().monday_close"
                                ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="row">Tuesday:</td>
                            <td>
                                <span
                                    data-bind="text: selectedStore().tuesday_open"
                                ></span>
                                -
                                <span
                                    data-bind="text: selectedStore().tuesday_close"
                                ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="row">Wednesday:</td>
                            <td>
                                <span
                                    data-bind="text: selectedStore().wednesday_open"
                                ></span>
                                -
                                <span
                                    data-bind="text: selectedStore().wednesday_close"
                                ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="row">Thursday:</td>
                            <td>
                                <span
                                    data-bind="text: selectedStore().thursday_open"
                                ></span>
                                -
                                <span
                                    data-bind="text: selectedStore().thursday_close"
                                ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="row">Friday:</td>
                            <td>
                                <span
                                    data-bind="text: selectedStore().friday_open"
                                ></span>
                                -
                                <span
                                    data-bind="text: selectedStore().friday_close"
                                ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="row">Saturday:</td>
                            <td>
                                <span
                                    data-bind="text: selectedStore().saturday_open"
                                ></span>
                                -
                                <span
                                    data-bind="text: selectedStore().saturday_close"
                                ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="row">Sunday:</td>
                            <td>
                                <span
                                    data-bind="text: selectedStore().sunday_open"
                                ></span>
                                -
                                <span
                                    data-bind="text: selectedStore().sunday_close"
                                ></span>
                            </td>
                        </tr>
                        <tr>
                            <td class="row">Public Holidays:</td>
                            <td>Please call store.</td>
                        </tr>
                    </table>
                </div>

                <div
                    class="postcode-or-suburb"
                    if="!selectedStore() || !selectedStore().zipcode"
                >
                    <div class="store-form">
                        <!-- ko foreach: getRegion('store-fields') -->
                        <!-- ko template: getTemplate() --><!-- /ko -->
                        <!-- /ko -->

                        <!-- ko if: suburbs().length || stores().length -->
                        <div id="change-store-dropdown">
                            <div class="store-options">
                                <ul
                                    class="suburbs-list store-list"
                                    if="suburbs().length"
                                >
                                    <!-- ko foreach: { data: suburbs, as: 'suburb'} -->
                                    <li class="item-suburb">
                                        <a
                                            data-bind="click: function(data, event) { $parent.fetchStores(city, postcode, event) }"
                                        >
                                            <span
                                                html="cityHtml + ' ' + postcodeHtml"
                                           ></span>
                                        </a>
                                    </li>
                                    <!-- /ko -->
                                </ul>

                                <ul
                                    class="suburbs-list store-list"
                                    if="stores().length"
                                >
                                    <!-- ko foreach: { data: stores, as: 'store'}-->
                                    <li
                                        class="item-store"
                                        data-bind="click: function(data, event) { $parent.selectStore(store, event) }"
                                    >
                                        <div class="item-store-content">
                                            <div class="store">
                                                <a
                                                    ><span
                                                        class="name"
                                                        data-bind="text: $data.store_name"
                                                    ></span
                                                ></a>
                                                <div
                                                    class="delivery-type"
                                                    visible="window.checkoutConfig.stockMessages.enabled"
                                                >
                                                    <span
                                                        data-bind="css: {
                                                          'delivery-message available'      :  $data.stockMessage.code == '0',
                                                          'delivery-message low-stock'      :  $data.stockMessage.code == '1',
                                                          'delivery-message not-available'  :  $data.stockMessage.code >= '2'
                                                        }, html: $data.stockMessage.cart_message"
                                                    >
                                                    </span>
                                                    <!--<span class="distance">7.7km away</span>-->
                                                </div>
                                                <span
                                                    class="city"
                                                    data-bind="text: $data.address + ' ' + $data.city + ', ' + $data.zipcode + ', ' + $data.state"
                                                    style="
                                                        display: block;
                                                        clear: both;
                                                    "
                                                ></span>
                                            </div>
                                        </div>
                                    </li>
                                    <!-- /ko -->
                                </ul>
                            </div>
                        </div>
                        <!-- /ko -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
