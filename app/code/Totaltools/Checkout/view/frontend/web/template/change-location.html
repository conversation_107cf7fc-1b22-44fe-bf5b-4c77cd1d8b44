<div class="selected-suburb">
    <!-- ko if: !selectedAddress() || !selectedAddress().postcode -->
        <!-- ko if: selectedSuburb() && selectedSuburb().postcode -->
            <!-- ko if: !selectedSuburb().store || (selectedSuburb().store && selectedSuburb().store.is_visible == '1') -->
                <p>Your order will be delivered to:</p>
                <span data-bind="text: selectedSuburb().city + ', ' + selectedSuburb().postcode"></span>
            <!-- /ko -->
        <!-- /ko -->
        <!-- ko if: selectedSuburb() && !selectedSuburb().postcode -->
        <p data-bind="i18n: 'Please select your location to get delivery estimates.'"></p>
        <span>&nbsp;</span>
        <!-- /ko -->
    <!-- /ko -->
    <!-- ko if: selectedAddress() && selectedAddress().postcode -->
    <p>Your order will be delivered to:</p>
    <span data-bind="text: selectedAddress().city + ', ' + selectedAddress().postcode"></span>
    <!-- /ko -->
</div>

<!-- ko if: selectedSuburb() && (!selectedAddress() || !selectedAddress().city || !selectedAddress().postcode) -->
<a href="#" data-bind="click: openModal" style="display: none;">Change location</a>
<!-- /ko -->
<!-- ko if: selectedAddress() && selectedAddress().city && selectedAddress().postcode -->
<span>Based on your delivery address.</span>
<!-- /ko -->

<div id="change-location-modal" style="display:none;" data-bind="blockLoader: isLoading, afterRender: createModal">

    <h1>Select a Location</h1>
    <p>Please enter your postcode or suburb to see availability for your items and estimated delivery dates.</p>

    <div class="store-form">

        <div class="store-zipcode" data-bind="css: { 'searching' : isSearching }">
            <input type="text" name="change-location-postcode" id="change-location-postcode-input" data-bind="event: {keyup: fetchSuburbs}, attr: {placeholder: 'Postcode or Suburb', autocomplete: 'off'}" />
        </div>

        <!-- ko if: suburbs().length -->
        <div class="store-options">

            <ul class="store-list">
                <!-- ko foreach: { data: suburbs, as: 'suburb'} -->
                <li class="item-suburb">
                    <a data-bind="click: function(data, event) { $parent.selectSuburb($data, event) }"><span data-bind="html: cityHtml + ' ' + postcodeHtml"></span></a>
                </li>
                <!-- /ko -->
            </ul>
        </div>
        <!-- /ko -->
        <div class="error_message_search_store" style="display: none" >
            <span data-bind="text: window.error_message_search_store"></span>
        </div>
        <!-- ko if: (showUseMyCurrentLocation())-->
        <div class="or">or</div>

        <div class="current-location">
            <button class="button button-primary__outline button-block" data-bind="click: useCurrentLocation">
                <span>Use my current location</span>
            </button>
        </div>
        <!-- /ko -->

    </div>

</div>