<!--
/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<div class="block block-cart-items items-in-cart active">
    <div class="title" data-role="title">
        <strong role="heading" class="heading">
            <!-- ko if: maxCartItemsToDisplay < getCartLineItemsCount() -->
                <!-- ko text: maxCartItemsToDisplay --><!-- /ko -->
                <!-- ko i18n: 'of' --><!-- /ko -->
            <!-- /ko -->     
            <!-- ko i18n: 'Your Shopping Cart' --><!-- /ko -->       
        </strong>
        <strong role="complementary" class="count">
            <span data-bind="text: getItemsQty()"></span>
            <!-- ko if: getItemsQty() <= 1 -->
                <!-- ko i18n: 'Item' --><!-- /ko -->
            <!-- /ko -->
            <!-- ko if: getItemsQty() > 1 -->
                <!-- ko i18n: 'Items' --><!-- /ko -->
            <!-- /ko -->
        </strong>
    </div>
    <div class="content minicart-items" data-role="content">
        <div class="minicart-items-wrapper overflowed">
            <ol class="minicart-items">
                <!-- ko foreach: items() -->
                <li class="product-item">
                    <!-- ko foreach: $parent.elems() -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                    <!-- /ko -->
                </li>
                <!-- /ko -->

                <!-- ko if: getCartLineItemsCount() < 1 -->
                    <li class="product-item">
                    <!-- ko i18n: 'No items found.' --><!-- /ko -->
                    </li>
                <!-- /ko -->
            </ol>
        </div>
    </div>
    <!-- ko if: maxCartItemsToDisplay < getCartLineItemsCount() -->
    <div class="actions-toolbar">
        <div class="secondary">
            <a class="action viewcart" data-bind="attr: {href: cartUrl}">
                <span data-bind="i18n: 'View and Edit Cart'"></span>
            </a>
        </div>
    </div>
    <!-- /ko -->
</div>
