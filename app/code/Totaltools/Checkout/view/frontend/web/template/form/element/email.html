<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<!-- ko ifnot: isCustomerLoggedIn() -->
<h3 class="step-title" data-role="title" translate="getNameEmailAddress()"></h3>

<!-- ko foreach: getRegion('before-login-form') -->
<!-- ko template: getTemplate() --><!-- /ko -->
<!-- /ko -->
<form class="form form-login form-login-shipping custom-form" data-role="email-with-possible-login" id="form-login"
      data-bind="submit:login"
      method="post">
    <fieldset id="customer-email-fieldset" class="fieldset" data-bind="blockLoader: isLoading" style="margin: 0;">
        <div class="field required">
            <label class="label label-email" for="customer-email">
                <span data-bind="i18n: 'Email Address'"></span>
                <span class="login-tooltip">
                    <span class="tooltip-toggle"><span>?</span></span>
                    <span class="tooltip-content" data-bind="i18n: 'We\'ll send order confirmation here.'"></span>
                </span>
            </label>
            <div class="control _with-tooltip">
                <input class="input-text"
                       type="email"
                       data-bind="
                            textInput: email,
                            hasFocus: emailFocused,
                            mageInit: {'mage/trim-input':{}}"
                       name="username"
                       data-validate="{required:true, 'validate-email':true}"
                       id="customer-email" />
                <!-- ko template: 'ui/form/element/helper/tooltip' --><!-- /ko -->
                <span class="note" data-bind="fadeVisible: isPasswordVisible() == false && continueAsGuestSelected() == false"><!-- ko i18n: "If you don't have an account you can create one after checkout."--><!-- /ko --></span>
            </div>
        </div>

        <!-- ko foreach: getRegion('additional-login-email-fields') -->
        <!-- ko template: getTemplate() --><!-- /ko -->
        <!-- /ko -->

        <!--Hidden fields -->
        <fieldset class="fieldset hidden-fields" data-bind="fadeVisible: continueAsGuestSelected() == false">
            <div class="password-fields" data-bind="fadeVisible: isPasswordVisible">
                <div class="field">
                    <label class="label" for="customer-password">
                        <span data-bind="i18n: 'Password'"></span>
                    </label>
                    <div class="control">
                        <input class="input-text"
                            data-bind="
                                attr: {
                                    placeholder: $t('Optional'),
                                }"
                            type="password"
                            name="password"
                            id="customer-password"
                            data-validate="{required:true}" autocomplete="off"/>
                        <span class="note" data-bind="i18n: 'You already have an account with us. Login or continue as guest.'"></span>
                    </div>
                </div>
                <div class="field forgot-password">
                    <a class="action remind" data-bind="attr: { href: forgotPasswordUrl }">
                        <span data-bind="i18n: 'I forgot my password'"></span>
                    </a>
                </div>

                <!-- ko foreach: getRegion('additional-login-form-fields') -->
                    <!-- ko template: getTemplate() --><!-- /ko -->
                <!-- /ko -->

            </div>

            <div class="actions-toolbar" id="email-input-actions">
                <input name="context" type="hidden" value="checkout" />

                <button type="submit" class="button button-primary button-block login" data-action="checkout-method-login" data-bind="fadeVisible: isPasswordVisible()"><span data-bind="i18n: 'Login'"></span></button>

                <div class="or" data-bind="i18n: '- Or -', fadeVisible: isPasswordVisible() && showContinueAsGuestButton()"></div>

                <button type="button" data-bind="click: continueAsGuest, fadeVisible: isPasswordVisible() && showContinueAsGuestButton()" class="button button-primary__outline button-block continue" data-action="checkout-method-login"><span data-bind="i18n: 'Continue as Guest'"></span></button>
            </div>
        </fieldset>
        <!--Hidden fields -->
    </fieldset>
</form>
