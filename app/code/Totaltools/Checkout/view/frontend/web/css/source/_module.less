/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2020 Totaltools. <https://totaltools.com.au>
 */

@font-proxima-nova: 'proxima-nova';
@font-proxima-nova-condensed: 'proxima-nova-condensed';
@font-proxima-nova-extra-condensed: 'proxima-nova-extra-condensed';
@font-oswald: 'oswald';
@tt-blue: #2E2D76;
@tt-grey-ash: #ddd;
@tt-btn-blue: @tt-blue;
@tt-btn-blue-hover: #173f91;
@tt-btn-red: #E41B13;
@tt-btn-red-border: #c4291a;

.btn-abstract(
    @background: white;
    @background-hover: @tt-btn-blue-hover;
    @border: transparent;
    @color: white;
    @font-size: 16;
    @font-family: @font-proxima-nova-condensed;
) {
    display: block;
    width: 100%;
    color: @color;
    background-color: @background;
    border: 2px solid @border;
    font-weight: bold;
    .lib-font-size(@font-size);
    .lib-css(font-family, @font-family);
    .lib-css(border-radius, 3px, 1);
    .lib-css(box-sizing, border-box, 1);
    .lib-css(transition, all 0.2s ease, 1);

    &:hover {
        background-color: @background-hover;
        color: white;
    }
}

@media-common: true;

& when (@media-common = true) {
    .store-locator-top {
        margin-left: auto;
        background-color: rgba(0, 0, 0, 0.15);
        .lib-css(align-self, stretch, 1);

        .store-locator-wrapper {
            .lib-vendor-prefix-display;
            .lib-vendor-prefix-flex-wrap(wrap);
            .lib-css(font-family, @font-proxima-nova);
            color: white;
            padding: 8px 15px 6px;
            position: relative;
            .lib-clearfix;
        }

        a {
            color: white;
        }

        .store-selector {
            .lib-css(box-sizing, border-box, 1);

            &-wrapper {
                .lib-vendor-prefix-display;
                .lib-vendor-prefix-flex-wrap;
                .lib-css(align-items, center, 1);

                .store-icon {
                    fill: #e41b13;
                    max-width: 30px;
                    margin-right: 7px;
                    .lib-css(box-sizing, border-box, 1);

                    svg {
                        max-width: 30px;
                    }
                }

                .store-selected {
                    &.active {
                        .store-dropdown-options {
                            display: block;
                            z-index: 1;
                        }

                        .store-status:before {
                            .lib-css(transform, rotate(180deg), 1);
                        }

                        &:before {
                            display: block;
                            width: 100%;
                            height: 100%;
                            content: '';
                            position: absolute;
                            z-index: -1;
                            top: 0;
                            left: 0;
                            background-color: rgba(0, 0, 0, 0.25);
                        }
                    }
                }

                .store-name {
                    display: block;
                    font-weight: 900;
                    line-height: 1;
                    white-space: nowrap;
                    cursor: pointer;
                }

                .store-change {
                    font-weight: 900;
                    text-transform: uppercase;
                    .lib-font-size(16);
                    cursor: pointer;

                    &:before {
                        display: block;
                        width: 100%;
                        height: 100%;
                        content: '';
                        position: absolute;
                        z-index: -1;
                        top: 0;
                        left: 0;
                        .lib-css(transition, all 0.2s ease-in-out);
                    }

                    &:hover {
                        text-decoration: underline;

                        &:before {
                            background-color: rgba(0, 0, 0, 0.25);
                        }
                    }
                }
            }
        }

        .store-details {
            text-align: left;
            padding-right: @indent__m;
            .lib-css(box-sizing, border-box, 1);

            .store {
                &-dropdown {
                    margin-top: -2px;
                }

                &-status {
                    cursor: pointer;
                    position: relative;
                    text-transform: capitalize;

                    .lib-icon-font(
                        @_icon-font-content: '\E800',
                        @_icon-font-display: inline-block,
                        @_icon-font-size: 18px
                    );

                    &:before {
                        position: absolute;
                        right: -12px;
                        .lib-css(transition, all 0.2s ease, 1);
                    }

                    &-text {
                        .lib-font-size(14);
                        font-weight: 700;
                    }

                    &-time-icon {
                        position: relative;
                        display: inline-block;
                        width: 16px;
                        height: 16px;
                        margin-right: 2px;
                        display: none;

                        svg {
                            position: absolute;
                            top: 3px;
                            left: 0;
                            fill: white;
                        }

                        &.open svg {
                            fill: yellowgreen;
                        }
                    }
                }

                &-dropdown-options {
                    position: absolute;
                    top: 100%;
                    right: 0;
                    z-index: 10000;
                    width: 360px;
                    max-width: 100%;
                    padding: @indent__base;
                    text-align: left;
                    display: none;
                    background-color: white;
                    border: 1px solid @tt-grey-ash;
                    color: #515151;
                    .lib-css(box-sizing, border-box, 1);
                    .lib-css(font-family, @font-proxima-nova-condensed);

                    a {
                        color: @tt-btn-blue;
                    }

                    &:before,
                    &:after {
                        bottom: 100%;
                        right: 7%;
                        border: solid transparent;
                        content: '';
                        height: 0;
                        width: 0;
                        position: absolute;
                        pointer-events: none;
                    }

                    &:after {
                        border-color: rgba(255, 255, 255, 0);
                        border-bottom-color: #fff;
                        border-width: 8px;
                        margin-left: -8px;
                    }

                    &:before {
                        border-color: rgba(221, 221, 221, 0);
                        border-bottom-color: @tt-grey-ash;
                        border-width: 9px;
                        margin-left: -9px;
                    }

                    h3 {
                        font-family: @font-proxima-nova-condensed;
                        .lib-font-size(20);
                        color: @tt-btn-blue;
                        margin: 0;
                        text-transform: capitalize;
                    }
                }

                &-address {
                    > span {
                        display: block;
                    }
                }

                &-directions {
                    margin: @indent__s 0;

                    .btn-directions {
                        .btn-abstract(
                            white,
                            @tt-btn-blue-hover,
                            @tt-btn-blue,
                            @tt-btn-blue,
                            16,
                            @font-oswald
                        );
                        text-transform: uppercase;
                        padding-bottom: 8px;

                        span {
                            .lib-icon-font(
                                @_icon-font-content: '\e815',
                                @_icon-font-margin: -4px 1px 0
                            );
                        }
                    }
                }

                &-hours {
                    border-bottom: 1px solid @tt-grey-ash;
                    margin-bottom: @indent__m;
                }

                &-timings {
                    margin: 0;
                    padding: 0;
                    list-style: none;

                    li {
                        .lib-vendor-prefix-display;
                        .lib-vendor-prefix-flex-wrap(wrap);
                        margin-bottom: 5px;

                        span:first-child {
                            width: 40%;
                        }

                        span.time {
                            span:first-child {
                                content: '-';
                            }
                        }
                    }
                }

                &-spcecial-day {
                    .event-start-wrap {
                        border: 1px solid #dddddd;
                        display: inline-block;
                        text-align: center;
                        text-transform: uppercase;
                        width: 44px;
                        .event-start-month {
                            display: block;
                            background-color: #dddddd;
                            color: #333333;
                            font-size: 1.2rem;
                            padding: 0 8px;
                        }
                        .event-start-day {
                            display: block;
                            font-size: 1.8rem;
                            font-family: 'proxima-nova';
                        }
                    }
                    .event-info {

                        ul{
                            padding: 0;
                            margin: 0;
                            list-style: none;
                            .status-label, .event-time  {
                                display: inline-block;
                                float: right;
                                padding: 2px 4px 1px;
                                font-size: 1.2rem;
                                line-height: 1;
                                border: 1px solid #ee2921;
                                -webkit-border-radius: 2px;
                                -moz-border-radius: 2px;
                                -ms-border-radius: 2px;
                                border-radius: 2px;
                                color: #24a40e;
                                border-color: #24a40e;
                            }
                        }
                    }
                }

                &-holiday {
                    .event-label{
                        width: 33%;
                        float: left;
                    }
                    ul{
                        padding: 0;
                        margin: 0;
                        list-style: none;
                        float: right;
                        width: 66%;
                        .status-label {
                            display: inline-block;
                            float: right;
                            padding: 2px 4px 1px;
                            font-size: 1.2rem;
                            line-height: 1;

                            border: 1px solid #ee2921;
                            -webkit-border-radius: 2px;
                            -moz-border-radius: 2px;
                            -ms-border-radius: 2px;
                            border-radius: 2px;

                        }
                    }
                    .status-label, .event-time {
                        display: inline-block;
                        float: right;
                        padding: 2px 4px 1px;
                        font-size: 1.2rem;
                        line-height: 1;
                        color: #ee2921;
                        border: 1px solid #ee2921;
                        -webkit-border-radius: 2px;
                        -moz-border-radius: 2px;
                        -ms-border-radius: 2px;
                        border-radius: 2px;
                    }

                }

                &-actions {
                    .btn {
                        margin: @indent__s 0;
                        padding: 9px 15px 7px;
                    }

                    .btn-details {
                        .btn-abstract(
                            @tt-btn-red,
                            lighten(@tt-btn-red, 0.85),
                            @tt-btn-red-border,
                            white,
                            18
                        );
                        font-weight: 800;
                    }

                    .btn-change {
                        .btn-abstract(
                            white,
                            @tt-blue,
                            @tt-btn-blue,
                            @tt-btn-blue,
                            18
                        );
                    }
                }
            }
        }
    }

    // Trust seals
    .trust-seals {
        width: 100%;
        max-width: 372px;
        margin: @indent__base 0 @indent__base auto;
        .lib-clearfix();

        &-items {
            list-style: none;
            margin: 0;
            padding: 0;
            .lib-vendor-prefix-display();
        }

        &-item {
            max-width: 40%;

            &.ssl-secure-seal {
                max-width: 22%;
            }

            img {
                max-height: 60px;
            }
        }
    }

    #free-shipping-cart {
        text-align: center;
        clear: both;
        width: 90%;
        margin: 0 auto;
        display: none;
        .lib-clearfix();

        body.test_19 & {
            display: block;
        }

        .free-shipping-container {
            .lib-css(margin, @indent__s 0 @indent__base);
        }

        .free-shipping-message {
            .lib-css(font-family, @font-family__base);
            .lib-css(color, @link__color);
            .lib-css(font-weight, @font-weight__regular);
            .lib-css(margin-bottom, @indent__s);
            .lib-font-size(16);
            .lib-line-height(16);
        }

        .success {
            .free-shipping-message {
                .lib-css(font-weight, @font-weight__bold);
            }
        }

        .free-shipping-progress {
            width: 100%;
            display: block;
            border: 1px solid @tt-blue;
            padding: 1px;
            .lib-css(border-radius, 10px, 1);
            .lib-css(transition, width 0.2s ease, 1);
            .lib-css(box-sizing, border-box, 1);

            span {
                display: block;
                width: 0;
                max-width: 100%;
                min-height: 10px;
                background-color: @tt-blue;
                .lib-css(border-radius, 10px, 1);
            }
        }

        .success {
            .free-shipping-progress {
                border-color: green;

                span {
                    background-color: green;
                }
            }
        }
    }

    .checkout-toolbar {
        display: none;
        clear: both;
        .lib-css(font-family, @font-family__base);
        .lib-clearfix();

        body.test_19 & {
            display: block;
        }

        .checkout-secure {
            display: block;
            width: 100%;

            button {
                display: block;
                width: 100%;
                text-transform: uppercase;
                .lib-css(font-family, @font-oswald);
                .lib-font-size(18);
                .lib-css(padding, @indent__s 0);

                span {
                    &:before {
                        font-family: 'TT Icons';
                        font-size: inherit;
                        line-height: inherit;
                        content: '\E902';
                        vertical-align: middle;
                        display: inline-block;
                        speak: none;
                        margin: -4px 2px 0 0;
                    }
                }
            }
        }

        .store-features {
            width: 100%;
            .lib-css(display, flex, 1);
            .lib-css(justify-content, space-around, 1);
            .lib-font-size(14);
            .lib-css(margin, @indent__m 0 5px);

            .fast-delivery {
                font-weight: normal;

                &:before {
                    font-family: 'TT Icons';
                    font-size: inherit;
                    line-height: inherit;
                    content: '\E84B';
                    vertical-align: middle;
                    display: inline-block;
                    speak: none;
                    margin: -4px 3px 0 0;
                    color: @tt-btn-red;
                }
            }

            .online-returns {
                font-weight: normal;

                &:before {
                    font-family: 'TT Icons';
                    font-size: inherit;
                    line-height: inherit;
                    display: inline-block;
                    content: '\E84D';
                    vertical-align: middle;
                    speak: none;
                    margin: -4px 3px 0 0;
                    color: @tt-btn-red;
                }
            }
        }
    }

    .grand.free-shipping {
        display: none;

        body.test_19 & {
            display: table-row;
        }

        td span {
            text-transform: uppercase;
            color: #29B711;
            .lib-css(font-family, @font-proxima-nova);
            .lib-css(font-weight, @font-weight__bold);
        }
    }
}

//
//  Mobile
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__m) {
    .store-locator-top {
        background-color: @tt-blue;
        width: 100%;

        .store-selector {
            width: 100%;

            &-wrapper {
                .store-icon,
                .store-icon svg {
                    max-width: 20px;
                }

                .store-selected {
                    width: ~"calc(100% - 27px)";
                    .lib-vendor-prefix-display();
                    .lib-vendor-prefix-flex-wrap(wrap);
                    .lib-css(align-items, center, 1);

                    .store-name {
                        .lib-css(font-family, @font-proxima-nova-extra-condensed);
                        .lib-font-size(16);
                    }

                    .store-details {
                        .lib-css(font-family, @font-proxima-nova-condensed);
                        margin-left: auto;
                    }
                }
            }
        }

        .store-details {
            .store {
                &-dropdown-options {
                    max-width: 100vw;
                    width: 100vw;
                }
            }
        }
    }

    #free-shipping-cart{
        .free-shipping-container {
            &.in-progress {
                .free-shipping-message {
                    .lib-line-height(20);

                    strong {
                        display: block;
                    }
                }
            }
        }
    }
}

//
//  Mobile XXS
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='max') and (@break =@screen__xxs) {
    .store-locator-top {
        .store-selector {
            &-wrapper {
                .store-details .store-status-text {
                    > span {
                        display: none;
                    }
                }
            }
        }
    }
}

//
//  Desktop
//  _____________________________________________
.media-width(@extremum, @break) when (@extremum ='min') and (@break =@screen__m) {
    .store-locator-top {
        .store-locator-wrapper {
            border: 1px solid rgba(255, 255, 255, 0.45);
            border-width: 0 1px;
        }
    }

    .am-checkout {
        > .checkout-header {
            float: left;

            h1 {
                margin-bottom: @indent__m;
            }
        }

        > .trust-seals {
            margin-top: @indent__l;
        }

        > .messages {
            .lib-clearfix();
        }
    }

    #free-shipping-cart {
        width: 100%;
        margin: 0;
        text-align: left;

        .free-shipping-progress {
            max-width: 300px;
        }
    }

    .checkout-toolbar {
        display: none !important;
    }
}

& when (@media-common = true) {
    //
    //  Large Desktop
    //  _____________________________________________
    @media screen and (min-width: 1242px) {
        .store-locator-top {
            min-width: 300px;
        }
    }
}