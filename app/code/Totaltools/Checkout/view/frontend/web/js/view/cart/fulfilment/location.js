/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'underscore',
    'ko',
    'uiComponent',
    'Magento_Ui/js/modal/modal',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Checkout/js/model/selected-location',
    'Totaltools_Geo/js/model/geolocation',
    'Totaltools_Checkout/js/model/shipping-rate-processor/geo-location',
    'Totaltools_Checkout/js/model/use-current-location',
    'Totaltools_Checkout/js/action/select-location',
    'Totaltools_Postcodes/js/fetch-suburbs',
    'Totaltools_Checkout/js/model/delivery-message',
], function (
    $,
    _,
    ko,
    Component,
    modal,
    quote,
    selectedLocation,
    geoLocation,
    geoLocationRates,
    useCurrentLocation,
    selectLocationAction,
    fetchSuburbsAction,
    deliveryMessage
) {
    'use strict';

    return Component.extend({
        modalWindow: null,

        selectedAddress: quote.shippingAddress,

        selectedSuburb: selectedLocation.getSuburb(),

        postcode: ko.observable(''),

        suburbs: ko.observableArray(),

        showUseMyCurrentLocation: useCurrentLocation.getValue(),

        isLoading: ko.observable(false),

        isSearching: ko.observable(false),

        stockMessage: deliveryMessage.getMessage(),

        showStockMessage: deliveryMessage.showMessage.bind(this),

        isActive: ko.observable(false),

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();
            this.registerSubscribers();

            return this;
        },

        /**
         * Register subscriptions
         */
        registerSubscribers: function () {
            var self = this;

            quote.shippingMethod.subscribe(function(method) {
                var newMethod = method.carrier_code + '_' + method.method_code;
                self.isActive(newMethod !== window.cc_method_name);
            });

            geoLocation.location.subscribe(function(location) {
                geoLocationRates.getRates();
            });
        },

        /**
         * Fetch suburbs for given postcode
         */
        fetchSuburbs: function () {
            var self = this;
            var postCode = self.postcode();

            if (postCode.length < 3) {
                return;
            }
            
            self.isSearching(true);

            fetchSuburbsAction
                .fetchByPostcode(postCode, 'AU', false)
                .done(function (res) {
                    let suburbs = self.convertPostcodesDataToSuburbs(
                        res.items || []
                    );
                    self.suburbs(suburbs);
                    self.isSearching(false);
                })
                .fail(function (err) {
                    console.warn(err);
                    self.isSearching(false);
                });
        },

        /**
         *
         * @param {Object} suburb
         */
        selectLocation: function (suburb) {
            var self = this;

            self.isLoading(true);
            selectedLocation.setSuburb(suburb);

            selectLocationAction(suburb)
                .done(function (res) {
                    geoLocationRates.getRates();
                    self.hideModal();
                    self.isLoading(false);
                })
                .fail(function (err) {
                    console.warn(err);
                    self.isLoading(false);
                });
        },

        /**
         * Fetch suburbs based on user's current location
         */
        useCurrentLocation: function () {
            //geoLocation.getLocation(true);
            geoLocation.setBrowserLocation();
            this.hideModal();
        },

        /**
         * Takes a postcode response and converts into suburb objects.
         *
         * @param {Array} postcodesData
         * @returns {Array}
         */
        convertPostcodesDataToSuburbs: function (postcodesData) {
            return postcodesData
                .filter(function (po) {
                    return typeof po.locality !== 'undefined';
                })
                .map(function (po) {
                    return {
                        country: 'AU',
                        city: po.locality,
                        region: po.state,
                        postcode: po.postcode,
                    };
                });
        },

        /**
         * Initialization of popup
         *
         * @param {HTMLElement} element
         */
        createModal: function (element) {
            var self = this,
                options = {
                    type: 'popup',
                    modalClass: 'fulfillment-popup popup-change-location',
                    focus: '[name=postcode]',
                    responsive: true,
                    innerScroll: true,
                    trigger: '',
                    buttons: [],
                    closed: function (ev) {
                        self.postcode('');
                        self.isLoading(false);
                        self.isSearching(false);
                        self.suburbs.removeAll();
                    },
                };

            this.modalWindow = element;
            modal(options, $(this.modalWindow));
        },

        /** Show the store popup */
        showModal: function () {
            if (this.modalWindow) {
                $(this.modalWindow).modal('openModal');
            }
        },

        /** hide the store popup */
        hideModal: function () {
            if (this.modalWindow) {
                $(this.modalWindow).modal('closeModal');
            }
        },
    });
});
