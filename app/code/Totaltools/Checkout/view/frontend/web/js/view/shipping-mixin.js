/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'ko',
    'mage/translate',
    'Magento_Checkout/js/model/quote',
    'Magento_Customer/js/model/customer',
    'Magento_Checkout/js/model/shipping-service',
    'Magento_Checkout/js/model/shipping-rate-service',
    'Totaltools_Checkout/js/model/continue-as-guest',
    'Totaltools_Checkout/js/action/set-quote-store',
    'Magento_Checkout/js/action/set-shipping-information',
    'Totaltools_Checkout/js/model/selected-store',
    'Totaltools_Checkout/js/model/selected-shipping-method',
    'rjsResolver',
    'Totaltools_Checkout/js/model/cart/stock-notifications',
    'Amasty_CheckoutCore/js/view/utils',
    'mage/collapsible'
], function (
    $,
    ko,
    $t,
    quote,
    customer,
    shippingService,
    shippingRateService,
    continueAsGuest,
    setQuoteStoreAction,
    setShippingInformationAction,
    selectedStore,
    shippingMethod,
    onLoad,
    stockNotifications,
    viewUtils
) {
    'use strict';

    var isPriority = false;

    return function (Shipping) {
        return Shipping.extend({
            defaults: {
                modules: {
                    isBusiness:
                        '${ $.name }.shipping-address-fieldset.is_business_address',
                    company: '${ $.name }.shipping-address-fieldset.company',
                    billing: '${ $.name }.billing-address-form',
                },
            },

            priorityPrefix: 'priority',
            priorityRates: ko.observableArray([]),

            /**
             * @inheritdoc
             */
            initialize: function () {
                this._super();

                selectedStore.store.subscribe(function() {
                    setQuoteStoreAction();
                    setShippingInformationAction();
                });

                quote.shippingMethod.subscribe(function (method) {
                    this.toggleFields(method);
                    shippingMethod.setMethod(method);
                    setQuoteStoreAction();
                }, this);

                onLoad(
                    function () {
                        this.refreshShippingRates();
                    }.bind(this)
                );

                return this;
            },

            /**
             * Post collapsible widget creation callback.
             * @param {jQuery.Event} ev
             * @param {Object} ui
             */
            afterWidgetCreated: function(ev, ui) {
                /*
                Widget made active in all cases for feature-TOT9999-284
                Customers were not interacting with the collapsed widget.
                $(ev.target).collapsible(isPriority ? 'active' : 'deactivate');
                */
                $(ev.target).collapsible('active');
            },

            /**
             * @inheritdoc
             */
            initObservable: function () {
                var self = this;

                this._super().observe({
                    isVisible: true,
                });

                onLoad(function() {
                    if (!quote.isVirtual() && customer.isLoggedIn() && !this.isFormInline) {
                        setShippingInformationAction();
                    }

                    $('[name="third_party_name"], [name="shippit_delivery_instructions"]').on('blur', function(ev) {
                        if ($(this).val()) {
                            setShippingInformationAction();
                        }
                    });

                    this.isSelected.subscribe(function(method) {
                        isPriority = method ? method.toLowerCase().includes(self.priorityPrefix) : false;
                    });
                }.bind(this));

                this.rates.subscribe(function(updatedRates) {
                    let prefix = this.priorityPrefix;
                    let rates = updatedRates
                            .filter((rate) =>
                                rate.method_code.toLowerCase().includes(prefix)
                            )
                            .map((pRate) => {
                                let date;
                                let time;
                                let method = pRate.method_code.split('_');

                                if (method.length > 1) {
                                    date = method[1].split('-').reverse().join('/');
                                    time = pRate.method_title.substring(pRate.method_title.lastIndexOf(' '));
                                }

                                return Object.assign(pRate, {
                                    delivery_date: date || '',
                                    delivery_time: time || '',
                                })
                            });

                    this.priorityRates(rates);
                }.bind(this));

                return this;
            },

            /**
             * Toggle billing and shipping form based on customer type.
             *
             * @param {Boolean} visible
             * @returns {void}
             */
            toggleForms: function (visible) {
                try {
                    this.isVisible(visible);
                    this.billing() && this.billing().isVisible(visible);
                    this.toggleFields(quote.shippingMethod());
                } catch (err) {
                    console.error(err);
                }
            },

            /**
             * Toggle shipping form fields based on shipping method.
             *
             * @param {Object} method
             * @returns {void}
             */
            toggleFields: function (method) {
                var storePickup = Boolean(
                    method &&
                        method['method_code'] ===
                            window.checkoutConfig.cc_method_code
                );

                if (!this.isVisible() || !method) {
                    return;
                }

                if (storePickup) {
                    this.isBusiness() && this.isBusiness().hide();
                    this.company() && this.company().hide();
                } else {
                    if (this.isBusiness()) {
                        this.isBusiness().show();
                        this.isBusiness().value() && this.company().show();
                    }
                }
            },

            /**
             * Refreshes shipping rates if number of stored methods are less than 2.
             */
            refreshShippingRates: function () {
                if (!quote.isVirtual() && !shippingService.isLoading()) {
                    let existingRates = shippingService.getShippingRates();

                    if (existingRates().length < 2) {
                        shippingRateService.updateRates(null, true);
                    }
                }

                return this;
            },

            showArriveByEstimateMessage: ko.computed(function () {
                let notifs = stockNotifications.notifications();

                if (!notifs) return notifs;

                return notifs['arrive_by_estimate'] || undefined;
            }),

            showExpressCheckoutMessage: ko.computed(function () {
                let notifs = stockNotifications.notifications();

                if (!notifs) return notifs;

                return notifs['checkout_express_message'] || undefined;
            }),

            /**
             * @returns {String}
             */
            getNameStreetAddress: function () {
                return viewUtils.getBlockTitle('street_address');
            },

            /**
             * @inheritdoc
             */
            focusInvalid: function () {
                return this;
            }
        });
    };
});
