define(['ko'], function (ko) {
    'use strict';

    var message = ko.observable("Select 'Click & Collect' to check stock level."),
        code = ko.observable(0);

    return {
        /**
         * Explicitly subscribe to message and code
         * in dependant components to get any changes
         */
        message: message,

        code: code,

        isLoading: ko.observable(false),

        getMessage: function () {
            return this.message;
        },

        setMessage: function (data) {
            return this.message(data);
        },

        getCode: function () {
            return this.code;
        },

        setCode: function (data) {
            return this.code(data);
        },

        showMessage: ko.computed(function () {
            return window.checkoutConfig.stockMessages.enabled || code() == 5;
        }),
    };
});
