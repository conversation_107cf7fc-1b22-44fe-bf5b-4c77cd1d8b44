/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
/*jshint browser:true jquery:true*/
/*global alert*/
define([
    'jquery',
    'uiComponent',
    'ko',
    'mage/url',
    'Totaltools_Checkout/js/model/selected-shipping-method',
    'Totaltools_Checkout/js/model/selected-store',
    'Totaltools_Checkout/js/model/selected-location',
    'Totaltools_Checkout/js/model/click-and-collect-message',
    'Totaltools_Checkout/js/model/delivery-message',
    'Magento_Checkout/js/model/totals',
    'Magento_Checkout/js/model/quote'
], function (
    $,
    Component,
    ko,
    url,
    selectedShippingMethod,
    selectedStore,
    selectedLocation,
    clickAndCollectMessage,
    deliveryMessage,
    totals,
    quote
) {
    'use strict';
    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/cart-step/item/notifications',
        },

        notifications: ko.observableArray([]),

        selectedMethodName: selectedShippingMethod.getMethod(),

        selectedStore: selectedStore.getStore(),

        itemId: 0,

        xhr: null,

        cartHasOPOXLines: window.cartHasOPOXLines,

        initialize: function () {
            this._super();

            var _self = this;

            selectedShippingMethod.getMethod().subscribe(function (method) {
                _self._updateStockAvailabilityMessages();
            });

            selectedStore.getStore().subscribe(function (store) {
                _self._updateStockAvailabilityMessages();
            });

            selectedLocation.getSuburb().subscribe(function () {
                _self._updateStockAvailabilityMessages();
            });

            _self._updateStockAvailabilityMessages();

            return this;
        },

        _updateStockAvailabilityMessages: function () {
            var _self = this;

            if (_self.cartHasOPOXLines) {
                $('.button-success').prop('disabled', true);
            }

            if (_self.xhr && _self.xhr.readystate != 4) {
                _self.xhr.abort();
            }

            var postcode = selectedLocation.suburb().postcode
                ? selectedLocation.suburb().postcode
                : '';

            var selectedAddress = quote.shippingAddress();

            if (selectedAddress && selectedAddress.postcode) {
                postcode = selectedAddress.postcode;
            }

            var defaultStore = window.locationConfig.store.storelocator_id
                ? window.locationConfig.store.storelocator_id
                : 0;
            
            if (this.selectedMethodName() &&  this.selectedMethodName() !== null) {
                _self.xhr = $.ajax({
                    url: url.build('totaltools_storelocator/product/stockavailabilitymessage'),
                    data: {
                        postcode: postcode,
                        storelocator_id: this.selectedStore()
                            ? this.selectedStore().storelocator_id
                            : defaultStore,
                        method: this.selectedMethodName(),
                    },
                    type: 'GET',
                    dataType: 'json',
                }).done(function (data) {
                    _self.notifications(data);

                    var maxCode = -1;

                    $.each(data, function (k, v) {
                        if (maxCode < v.code) {
                            if (_self.selectedMethodName() == window.cc_method_name) {
                                clickAndCollectMessage.setMessage(v.cart_message);
                                clickAndCollectMessage.setCode(v.code);
                            } else {
                                deliveryMessage.setMessage(v.cart_message);
                                deliveryMessage.setCode(v.code);

                                if (typeof v.dangerous_item !== 'undefined') {
                                    deliveryMessage.setShippingDangerous(v.dangerous_item);
                                }
                            }

                            maxCode = v.code;
                        }
                    });

                    if (maxCode === 5) {
                        $('.button-success').prop('disabled', true);
                    } else {
                        $('.button-success').prop('disabled', false);
                    }
                });
            }
        },
    });
});
