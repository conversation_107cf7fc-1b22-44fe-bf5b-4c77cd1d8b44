/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'ko',
    'Totaltools_Checkout/js/model/cart/stock-notification-service',
    'Totaltools_Checkout/js/model/cart/stock-notifications',
], function ($, ko, stockService, stockNotifications) {
    'use strict';

    return function (ItemDetails) {
        return ItemDetails.extend({
            /**
             * @param {Object} item
             * @returns {Function}
             */
            stockStatus: function (item) {
                return ko.computed(function () {
                    let notifs = stockNotifications.notifications();

                    if (!notifs) return notifs;

                    return notifs[item.item_id] || undefined;
                });
            },

            /**
             * @param {Object} item
             * @returns {Boolean}
             */
            isOverweight: function (item) {
                var extAttrs = item['extension_attributes'];
                return extAttrs['overweight'];
            },

            /**
             * @param {Object} item
             * @returns {Boolean}
             */
            isShippingDangerous: function (item) {
                var extAttrs = item['extension_attributes'];
                return extAttrs['shipping_dangerous'];
            },

            /**
             * @param {Object} item
             * @returns {Boolean}
             */
            hasFreeShipping: function (item) {
                var extAttrs = item['extension_attributes'];

                return (
                    extAttrs['shipping_label'] &&
                    extAttrs['shipping_label'] === 'free_shipping'
                );
            },

            /**
             * @inheritdoc
             */
            initOptions: function (item) {
                this._super();

                var containerSelector =
                        '[data-role="product-attributes"][data-item-id=' +
                        item.item_id +
                        ']',
                    container = $(containerSelector);

                container.find('input, select, textarea').change(function (ev) {
                    if (ev.currentTarget.classList.contains('qty')) {
                        item.isUpdated(false);
                    }
                });

                const itemConfig = this.getItemConfig(item);
                if (itemConfig.hasOwnProperty('configurableAttributes')) {
                    container.amcheckoutConfigurable({
                        spConfig: JSON.parse(itemConfig.configurableAttributes.spConfig),
                        superSelector: '[data-role="product-attributes"][data-item-id=' + item.item_id + ']' + ' .super-attribute-select'
                    })
                }
            },

            /**
             * @inheritdoc
             */
            updateItem: function (item) {
                this._super();

                setTimeout(function () {
                    stockService.fetch();
                }, 3000);
            },

            /**
             *
             * @param {Object} item
             * @param {Object} propertyName
             * @return {*}
             */
            getPropertyDataFromItem: function (item, propertyName) {
                var property = this._super();

                // Check if the property exists in extension_attributes
                if (!property && item.hasOwnProperty('extension_attributes') && item.extension_attributes.hasOwnProperty(propertyName)) {
                    property = item.extension_attributes[propertyName];
                }

                return property;
            },

        });
    };
});
