define([
    'jquery',
    'ko',
    'Magento_Customer/js/customer-data',
    'Totaltools_Geo/js/model/geolocation',
], function ($, ko, customerData, geoLocation) {
    'use strict';

    var currentStore = customerData.get('fulfilment-data')()['store'] || {},
        selectedStore =
            window.locationConfig && window.locationConfig.store
                ? window.locationConfig.store
                : currentStore;


    var store = ko.observable(selectedStore);

    geoLocation.location.subscribe(function (location) {
        store(location.store);
    });

    return {
        /**
         * Explicitly subscribe to store
         * in dependant components to get any changes
         */
        store: store,

        getStore: function () {
            return this.store;
        },

        setStore: function (data) {
            return this.store(data);
        },
    };
});
