/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
/*jshint browser:true jquery:true*/
/*global alert*/
define(
    [
        'jquery',
        'uiComponent',
        'Magento_Checkout/js/model/quote',
        'Magento_Checkout/js/model/step-navigator',
        'Magento_Checkout/js/model/sidebar',
		'Totaltools_Checkout/js/model/selected-store'
    ],
    function($, Component, quote, stepNavigator, sidebarModel, selectedStore) {
        'use strict';

        return Component.extend({
			defaults: {
				template: "Totaltools_Checkout/billing-information"
			},

			store: selectedStore.getStore(),

			isVisible: function() {
				return stepNavigator.isProcessed("shipping");
			},

			back: function() {
				sidebarModel.hide();
				stepNavigator.navigateTo("shipping");
			},

			backToShippingMethod: function() {
				sidebarModel.hide();
				stepNavigator.navigateTo("shipping", "opc-shipping_method");
			},

			additionalClasses: function() {
                return quote.isVirtual() ? 'billing-virtual' : '';
            }
		});
    }
);
