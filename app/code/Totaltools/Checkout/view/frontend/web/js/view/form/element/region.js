/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'Magento_Ui/js/form/element/region',
    'Magento_Customer/js/customer-data',
    'uiRegistry',
], function (Component, customerData, registry) {
    'use strict';

    var countryData = customerData.get('directory-data');

    return Component.extend({
        defaults: {
            modules: {
                country: '${ $.parentName }.country_id',
            },
        },

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();

            if (
                window.checkoutConfig.amdefault &&
                window.checkoutConfig.amdefault.region
            ) {
                registry.get(
                    this.parentName + '.' + 'region_id_input',
                    function (region) {
                        if (!region.value()) {
                            var country = registry.get(this.parentName + '.' + 'country_id');
                            if (country.value() == window.checkoutConfig.amdefault.country_id) {
                                region.value(window.checkoutConfig.amdefault.region);
                            }
                        }
                    }.bind(this)
                );
            }            

            this.value.subscribe(function (regionId) {
                registry.get(
                    this.parentName + '.' + 'region_id_input',
                    function(region) {
                        let countryId = this.country().value(),
                            state = this.getState(countryId, regionId);
        
                        if (state && state['code']) {
                            region.value(state.code);
                        } else {
                            region.value('');
                        }
                    }.bind(this)
                );
            }, this);

            return this;
        },

        /**
         * Gets a state/region data from directory data.
         * @param {String} countryId
         * @param {Number} stateId
         * @returns {Object}
         */
        getState: function (countryId, stateId) {
            let data = countryData();

            return stateId && data[countryId] && data[countryId]['regions']
                ? data[countryId]['regions'][stateId]
                : null;
        },
    });
});
