define([
    'jquery',
    'mage/url',
    'ko',
    'Totaltools_Checkout/js/model/shipping-method-state'
], function ($, url, ko, shippingMethodState) {
    'use strict';

    var method = ko.observable(null);

    return {
        method: method,

        getMethod: function () {
            return this.method;
        },

        /**
         * @param {*} data
         */
        setMethod: function (data) {
            this.method(data);
            shippingMethodState.setDirty(true);
            return this;
        },

        /**
         * @return {Boolean}
         */
        isStorePickup: function () {
            let method = this.method(),
                methodName = method
                    ? method['carrier_code'] + '_' + method['method_code']
                    : null;

            return (
                methodName &&
                methodName === window.checkoutConfig.cc_method_name
            );
        },
    };
});
