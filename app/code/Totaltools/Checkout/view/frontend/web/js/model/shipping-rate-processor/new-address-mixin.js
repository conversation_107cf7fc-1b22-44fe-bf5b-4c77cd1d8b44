/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'mage/utils/wrapper',
    'Magento_Checkout/js/model/quote',
    './geo-location',
], function (wrapper, quote, geoLocation) {
    'use strict';

    return function (newAddress) {
        newAddress.getRates = wrapper.wrapSuper(newAddress.getRates, function (address) {
            if (address && address.city && address.countryId && address.postcode) {
                this._super(address);
                return;
            }

            if (!quote.isVirtual()) {
                geoLocation.getRates();
            }
        });

        return newAddress;
    };
});
