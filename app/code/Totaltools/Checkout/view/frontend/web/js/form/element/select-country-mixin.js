/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2025 Totaltools. <https://totaltools.com.au>
 */
define([], function () {
    'use strict';
  
    return function (SelectComponent) {
      return SelectComponent.extend({
        initialize: function () {
          this._super();
          
          if (this.inputName === 'country_id' && this.customScope === 'shippingAddress' && window?.checkoutConfig?.allowed_shipping_countries) {
            const allowedCountries = window.checkoutConfig.allowed_shipping_countries;
  
            this.options(Array.isArray(this.options()) 
              ? this.options().filter(country => allowedCountries.includes(country.value))
              : this.options());
          }
  
          return this;
        }
      });
    };
  });
  