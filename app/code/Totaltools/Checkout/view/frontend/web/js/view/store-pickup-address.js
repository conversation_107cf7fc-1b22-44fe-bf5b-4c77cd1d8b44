define([
    'jquery',
    'underscore',
    'ko',
    'Magento_Ui/js/form/form',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Checkout/js/model/selected-store',
    'Totaltools_Geo/js/model/geolocation',
    'Totaltools_Checkout/js/model/click-and-collect-message',
    'Totaltools_Storelocator/js/checkout/stockcheck',
    'Totaltools_Checkout/js/model/cart/stock-notifications',
    'Totaltools_Postcodes/js/fetch-suburbs',
], function (
    $,
    _,
    ko,
    Component,
    quote,
    selectedStore,
    geoLocation,
    clickAndCollectMessage,
    stockcheck,
    stockNotifications,
    fetchSuburbs
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/store-pickup-address',
            listens: {
                '${ $.name }.store-fields.storelocator_id:value': 'handleInput',
            },
            modules: {
                storeInput: '${ $.name }.store-fields.storelocator_id',
            },
        },
        selectedStore: selectedStore.getStore(),
        clickAndCollectMessage: clickAndCollectMessage.getMessage(),
        clickAndCollectCode: clickAndCollectMessage.getCode(),
        country: 'AU',
        regex: new RegExp('^[a-zA-Z0-9\\-\\s]+$'),
        _selectors: {
            _popup: '#change-store-modal2',
            _postcode: '#change-store-postcode-input2',
        },
        showStockMessage: clickAndCollectMessage.showMessage.bind(this),
        hasOutOfStockItems: ko.computed(function () {
            let hasStore = !_.isEmpty(selectedStore.store());
            let stockCode = clickAndCollectMessage.code();

            return hasStore && stockCode === 5;
        }),
        outOfStockMessage: ko.computed(function () {
            let message = 'product is';
            let outOfStock = [];
            let stockNotifs = stockNotifications.notifications();
            let notifications = _.filter(stockNotifs, (n, k) => !isNaN(k));

            if (!notifications.length) {
                return '';
            }

            notifications.forEach(function (product) {
                if (product?.code === 5) {
                    outOfStock.push(product?.name);
                }
            });

            if (notifications.length > 1 && outOfStock.length === 1) {
                message = outOfStock.toString();
                message += ' is';
            } else if (outOfStock.length > 1) {
                message = outOfStock
                    .join(', ')
                    .replace(/, ([^,]*)$/, ' and $1');
                message += ' are';
            }

            return message;
        }),
        isVisible: ko.computed(function () {
            return (
                quote.shippingMethod() &&
                quote.shippingMethod().carrier_code ==
                    window.checkoutConfig.cc_carrier_code
            );
        }),

        /**
         * @return {exports.initObservable}
         */
        initObservable: function () {
            this._super().observe({
                isStorePickupConfirmed: true,
                suburbs: [],
                stores: [],
            });

            return this;
        },

        hideFormPopUp: function () {
            $(this._selectors._popup).modal('closeModal');
            this.resetDefaults(true);
        },

        useCurrentLocation: function () {
            //geoLocation.getLocation(true);
            geoLocation.setBrowserLocation();
            $(this._selectors._popup).modal('closeModal');
        },

        resetDefaults: function (resetInput) {
            this.suburbs.removeAll();
            this.stores.removeAll();

            if (resetInput) $(this._selectors._postcode).val('');
        },

        fetchStores: function (suburb, postcode, event) {
            var _self = this;
            _self.resetDefaults();

            stockcheck.fetchStores(_self.stores, postcode, true);
        },

        selectStore: function (store, event) {
            var self = this;

            $.when(stockcheck.changeStore(store)).done(function (data) {
                selectedStore.setStore(store);
                self.selectedStore(store);
                self.hideFormPopUp();
                self.storeInput && self.storeInput().value('');
            });
        },

        fetchSuburbs: function (data, event) {
            var _self = this;
            var $postcode = $(this._selectors._postcode);
            var query = $postcode.val();

            _self.resetDefaults();

            if (this.regex.test(query) && event.keyCode != 17) {
                $postcode.addClass('avs-active');
                fetchSuburbs.fetchSuburbsByPostcode(
                    query,
                    $postcode,
                    _self.suburbs
                );
            }
        },

        highlightMatch: function (input, pattern) {
            var regex = new RegExp(pattern, 'gi');
            return input.replace(
                regex,
                '<span class="highlighted">' + pattern + '</span>'
            );
        },

        removeStore: function () {
            selectedStore.setStore(null);
            this.selectedStore(null);
            this.storeInput && this.storeInput().value('');
        },

        /**
         * @param {String} query
         * @returns {void}
         */
        handleInput: function (query) {
            var self = this,
                $postcode = $('input[name="storelocator_id"]');

            self.resetDefaults();

            if (this.regex.test(query)) {
                $postcode.addClass('avs-active');
                fetchSuburbs.fetchSuburbsByPostcode(
                    query,
                    $postcode,
                    self.suburbs
                );
            }
        },
    });
});
