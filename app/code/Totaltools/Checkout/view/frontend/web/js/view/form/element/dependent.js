/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define(['Magento_Ui/js/form/element/boolean', 'rjsResolver'], function (<PERSON><PERSON><PERSON>, onLoad) {
    'use strict';

    return Boolean.extend({
        defaults: {
            modules: {
                dependent: '${ $.parentName }.${ $.dependent }',
            },
            deps: ['${ $.parentName }.${ $.dependent }'],
        },

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();

            onLoad(
                function () {
                    if (!this.value() && this.dependent()) {
                        this.dependent().hide();
                    }
                }.bind(this)
            );

            return this;
        },

        /**
         * Show/hide dependent element based on current element value.
         */
        toggleDependent: function () {
            if (!this.dependent()) {
                return;
            }

            if (this.value()) {
                this.dependent().show();
            } else {
                this.dependent().hide();
            }
        },

        /**
         * @inheritdoc
         */
        onUpdate: function (val) {
            this._super();
            this.toggleDependent();
        },
    });
});
