define([
	'jquery',
	'underscore',
	'Magento_Ui/js/form/form',
	'ko',
	'Magento_Customer/js/model/customer',
	'Magento_Customer/js/customer-data',
	'Totaltools_Checkout/js/model/continue-as-guest',
	'Magento_Checkout/js/model/quote',
	'Magento_Checkout/js/model/shipping-service',
	'Magento_Checkout/js/action/select-shipping-method',
	'Magento_Checkout/js/checkout-data',
	'Totaltools_Checkout/js/model/selected-shipping-method',
	'Totaltools_Checkout/js/model/click-and-collect-message',
	'Totaltools_Checkout/js/model/delivery-message',
	'Totaltools_Checkout/js/model/shipping-method-state',
	'Totaltools_Checkout/js/model/selected-location',
	'Totaltools_Checkout/js/model/selected-store',
	'Magento_Checkout/js/model/step-navigator',
	'Magento_Ui/js/modal/alert',
	'Totaltools_Checkout/js/model/shipping-rate-processor/geo-location',
	'Totaltools_Checkout/js/model/cart/stock-notification-service',
	'Amasty_Conditions/js/action/recollect-totals',
	'Amasty_Conditions/js/model/subscriber',
	'mage/translate'
], function(
	$,
	_,
	Component,
	ko,
	customer,
	customerData,
	continueAsGuest,
	quote,
	shippingService,
	selectShippingMethodAction,
	checkoutData,
	selectedShippingMethod,
	clickAndCollectMessage,
	deliveryMessage,
	shippingMethodState,
	selectedLocation,
	selectedStore,
	stepNavigator,
	uiAlert,
	geoLocationRates,
	stockNotificationService,
	recollectTotal,
	subscriber,
	$t
) {
	'use strict';

	return Component.extend({
		defaults: {
			template: 'Totaltools_Checkout/fulfilment-boxes'
		},

		clickAndCollectMethod: ko.observable(window.cc_method_name || null),

		deliveryMethod: ko.observable(null),

		clickAndCollectMessage: clickAndCollectMessage.getMessage(),

		deliveryMessage: deliveryMessage.getMessage(),

		shippingDangerous: deliveryMessage.getShippingDangerous(),

		selectedMethod: ko.observable(null),

		noDeliveryStockMessage: $t("We are unable to find a delivery option based on your location/address and the items in your cart."),

		noDeliveryStockMessageHelp: $t("Please try following options:<br/><br/>Click 'Change location' link in the 'Delivery' tab and make sure postcode and suburb name is correct.<br/>Select a valid delivery address on 'Details' step.<br/>Remove any products classed as too bulky or dangerous."),

		fulfilmentData: customerData.get('fulfilment-data'),

		isVisible: ko.computed(function() {
			if (quote.isVirtual()) {
				return false;
			}

			let visible = false;
			let hashString = window.location.hash.replace('#', '');
			let activeStep = stepNavigator.getActiveItemIndex();

			if (
                activeStep == 0 &&
                hashString == 'shipping'
            ) {
                visible = true;
            }

			return visible;
		}),

		showInformationPopup: true,

		shippingRates: shippingService.getShippingRates(),

		shippingAddressCacheKey: '',

		showDeliveryMessage: deliveryMessage.showMessage.bind(this),

		showClickAndCollectMessage: clickAndCollectMessage.showMessage.bind(this),

		deliveryLoading: deliveryMessage.isLoading,

		clickAndCollectLoading: clickAndCollectMessage.isLoading,

		initialize: function() {
			this._super();

			this.shippingAddressCacheKey = quote.shippingAddress() ? quote.shippingAddress().getCacheKey() : '';
			this.populateLocationData();
			this.registerSubscriptions();

			return this;
		},

		/**
		 * Declare all subscriptions the component is dependent upon.
		 */
		registerSubscriptions: function() {
			var self = this,
				activeStep = stepNavigator.getActiveItemIndex();

			shippingService.getShippingRates().subscribe(function(rates) {
				if (activeStep === 0) {
					self.setupShippingMehods(rates);
				}
			});

			deliveryMessage.getShippingDangerous().subscribe(function (value) {
                if (value === 1){
					subscriber.isLoading(false);
					recollectTotal('shipping-dangerous');
                }
			});

			quote.shippingMethod.subscribe(function(method) {
				self.selectShippingMethod(method);
			});
		},

		/**
         * Populate component store & location properties with local storage data if available.
         * @return {void}
         */
        populateLocationData: function() {
            var data = this.fulfilmentData();

            if (!data.store && !data.location) {
                customerData.invalidate(['fulfilment-data']);
                customerData.reload(['fulfilment-data'], true);
                return;
            }

            if (data.store && "object" === typeof data.store) {
                selectedStore.setStore(data.store);
            }

            if (data.location && "object" === typeof data.location) {
                selectedLocation.setSuburb(data.location);
            }
        },

		/**
		 * @param {Object} shippingMethod
		 * @return {Boolean}
		 */
		selectShippingMethod: function(method) {
			var selectedMethod = method
                ? method.carrier_code + '_' + method.method_code
				: null;

			if (!selectedMethod) {
				return false;
			}

            checkoutData.setSelectedShippingRate(selectedMethod);
            this.selectedMethod(selectedMethod);
            selectedShippingMethod.setMethod(method);
            shippingMethodState.setDirty(true);

            return true;
		},

		setupShippingMehods: function(methods) {
			var _self = this,
				noShippingMethodAvailable = false;

			deliveryMessage.setShippingDangerous(null);

			if (
                ((_self.selectedMethod() &&
                    _self.selectedMethod() !== window.cc_method_name) ||
                    !_self.selectedMethod()) &&
                methods.length === 1 &&
                methods[0].method_code === window.cc_carrier_code
            ) {
                noShippingMethodAvailable = true;
            }

			this.deliveryMethod(null);
			this.clickAndCollectMethod(null);

			ko.utils.arrayForEach(methods, function(method) {
				if (method.method_code === window.cc_carrier_code) {
					_self.clickAndCollectMethod(method);
				} else if (!_self.deliveryMethod() || method.amount < _self.deliveryMethod().amount) {
					_self.deliveryMethod(method);

					if(deliveryMessage.getMessage()() == _self.noDeliveryStockMessage) {
						deliveryMessage.setMessage("");
					}
				}
			});

			if (noShippingMethodAvailable && _self.shouldDisplayInformationPopup()) {
				_self.showInformationPopup = false;
				deliveryMessage.setMessage(this.noDeliveryStockMessage);
				geoLocationRates.getRates();
				_self.selectShippingMethod(_self.clickAndCollectMethod());
			}

			_self.shippingAddressCacheKey = quote.shippingAddress() ? quote.shippingAddress().getCacheKey() : '';
		},

		shouldDisplayInformationPopup: function () {
			var self = this;

			if (self.showInformationPopup) {
				return true;
			} else {
				return self.shippingAddressCacheKey != (quote.shippingAddress() ? quote.shippingAddress().getCacheKey() : '')
			}
		}
	});
});
