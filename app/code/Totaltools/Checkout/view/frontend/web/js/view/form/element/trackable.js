/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define(['Magento_Ui/js/form/element/abstract'], function (Abstract) {
    'use strict';

    return Abstract.extend({
        defaults: {
            listens: {
                'checkout.steps.shipping-step.shippingAddress.shipping-address-fieldset.${ $.index }:value':
                    'updateValue',
            },
        },

        /**
         * @param {String|Number} val
         */
        updateValue: function (val) {
            this.value(val);
        },
    });
});
