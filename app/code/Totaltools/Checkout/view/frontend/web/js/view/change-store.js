define([
    'jquery',
    'underscore',
    'uiComponent',
    'ko',
    'Magento_Ui/js/modal/modal',
    'Totaltools_Checkout/js/model/selected-store',
    'Totaltools_Geo/js/model/geolocation',
    'Totaltools_Checkout/js/model/use-current-location',
    'Totaltools_Storelocator/js/checkout/stockcheck',
    'Totaltools_Postcodes/js/fetch-suburbs',
], function (
    $,
    _,
    Component,
    ko,
    modal,
    selectedStore,
    geolocation,
    useCurrentLocation,
    stockcheck,
    fetchSuburbs
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/change-store',
        },
        selectors: {
            modal: '#change-store-modal',
            postcodeInput: '#change-store-postcode-input',
        },
        modalWindow: null,
        selectedStore: selectedStore.getStore(),
        showUseMyCurrentLocation: useCurrentLocation.getValue(),
        suburbs: ko.observableArray(),
        stores: ko.observableArray(),
        isLoading: ko.observable(false),
        isSearching: ko.observable(false),

        /**
         * Initialization of popup
         *
         * @param {HTMLElement} element
         */
        createModal: function (element) {
            var self = this,
                options = {
                    type: 'popup',
                    modalClass: 'fulfillment-popup change-store-popup',
                    focus: 'input[name="change-store-postcode"]',
                    responsive: true,
                    innerScroll: true,
                    title: '',
                    buttons: [],
                    closed: function (ev) {
                        self.stores.removeAll();
                        self.suburbs.removeAll();
                        self.isLoading(false);
                        self.isSearching(false);
                        $(self.selectors.postcodeInput).val('');
                    },
                };

            this.modalWindow = element;
            modal(options, $(this.modalWindow));
        },

        openModal: function() {
            if (this.modalWindow) {
                $(this.modalWindow).modal('openModal');
            }
        },

        hideModal: function() {
            if (this.modalWindow) {
                $(this.modalWindow).modal('closeModal');
            }
        },

        useCurrentLocation: function () {
            //geolocation.getLocation(true);
            geolocation.setBrowserLocation();
            this.hideModal();
        },

        /**
         * @param {String} postcode
         * @param {jQuery.Event} ev
         */
        fetchStores: function (postcode, ev) {
            var self = this;

            self.isLoading(true);
            self.suburbs.removeAll();

            stockcheck
                .fetchAvailableStores(postcode, false)
                .done(function (res) {
                    if (res.stores) {
                        _.forEach(res.stores, function (store) {
                            self.stores.push(store);
                        });
                    }
                })
                .fail(function (err) {
                    console.warn(err);
                })
                .always(function (res) {
                    self.isLoading(false);
                });
        },

        /**
         * @param {Object} store
         * @param {jQuery.Event} ev
         */
        selectStore: function (store, ev) {
            var self = this;

            self.isLoading(true);
            self.stores.removeAll();

            $.when(stockcheck.changeStore(store))
                .done(function (data) {
                    selectedStore.setStore(store);
                    self.hideModal();
                })
                .always(function () {
                    self.isLoading(false);
                });
        },

        /**
         * @param {UiClass} obj
         * @param {jQuery.Event} ev
         */
        fetchSuburbs: function (obj, ev) {
            var self = this,
                validation = new RegExp('^[a-zA-Z0-9\\-\\s]+$'),
                $postcode = $(ev.target),
                query = $postcode.val();

            self.suburbs.removeAll();
            self.stores.removeAll();

            if (validation.test(query) && ev.keyCode !== 17) {
                self.isSearching(true);

                fetchSuburbs.fetchSuburbsByPostcode(
                    query,
                    $postcode,
                    self.suburbs
                );

                self.isSearching(false);
            }
        },
    });
});
