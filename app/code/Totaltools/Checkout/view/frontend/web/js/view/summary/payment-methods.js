/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */
define([
    'jquery',
    'ko',
    'underscore',
    'uiComponent',
    'Magento_Checkout/js/model/quote'
], function ($, ko, _, Component, quote) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/summary/payment-methods',
        },
        logosMap: {
            braintree: 'icons',
            braintree_paypal: 'paymentAcceptanceMarkSrc',
            zippayment: 'paymentAcceptanceMarkSrc',
            braintree_applepay: 'paymentMarkSrc',
            braintree_googlepay: 'paymentMarkSrc',
            humm_gateway: 'logo',
            afterpay: 'paymentMarkSrc',
        },
        methods: window.checkoutConfig.payment || {},
        logos: ko.observableArray([]),
        isVirtual: quote.isVirtual(),

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();
            this.getPaymentMethodLogos();

            return this;
        },

        getPaymentMethodLogos: function () {
            let self = this;
            let methods = self.methods;

            _.each(self.logosMap, function (field, method) {
                if (methods[method] && !_.isEmpty(methods[method][field])) {
                    let val = methods[method][field];

                    'object' === typeof val
                        ? self.parseBraintree(val, 'availableCardTypes')
                        : self.logos.push({name: method, url: val});
                }
            });
        },

        /**
         * @param {Object} opts
         * @param {String} filter
         */
        parseBraintree: function (opts, filter) {
            let self = this;
            let braintree = self.methods.braintree;

            _.each(opts, function (logo, method) {
                if (braintree[filter].includes(method)) {
                    self.logos.push({name: method, url: logo.url});
                }
            });
        },
    });
});
