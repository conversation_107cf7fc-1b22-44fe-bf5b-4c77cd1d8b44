/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2020 Totaltools. <https://totaltools.com.au>
 */

/*jshint browser:true*/
/*global define*/

define([
    'jquery',
    'underscore',
    'ko',
    'uiComponent',
    'mage/url',
    'Magento_Customer/js/customer-data',
    'Totaltools_Postcodes/js/fetch-suburbs',
    'Totaltools_Storelocator/js/checkout/stockcheck',
    'Totaltools_Checkout/js/model/use-current-location',
    'Totaltools_Geo/js/model/geolocation',
    'Magento_Checkout/js/model/full-screen-loader',
    'Totaltools_Catalog/js/view/fulfilment-boxes',
    'Totaltools_Catalog/js/model/selected-store',
    'Magento_Ui/js/modal/modal',
], function (
    $,
    _,
    ko,
    Component,
    urlBuilder,
    customerData,
    fetchSuburbsAction,
    stockCheckAction,
    useCurrentLocation,
    geoLocation,
    fullScreenLoader,
    fulfilmentBoxes,
    selectedStore,
    modal
) {
    'use strict';

    var fulfillmentData = customerData.get('fulfilment-data'),
        currentStore = fulfillmentData()['store'] ? fulfillmentData()['store'] : null;

    if (!currentStore) {
        customerData.reload(['fulfilment-data']);
    }

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/store-locator',
        },
        postcode: ko.observable(''),
        store: ko.observable(currentStore),
        suburbs: ko.observableArray(),
        stores: ko.observableArray(),
        isSearching: ko.observable(false),
        showUseMyCurrentLocation: useCurrentLocation.getValue(),
        storeMessage: ko.observable(''),
        specialDaysStart:ko.observable(''),
        specialDayOpenTime:ko.observable(''),

        /**
         * @inheritdoc
         */
        initObservable: function () {
            this._super();
            var self = this;

            this.storeIsOpen = ko.computed(function () {
                return this.getStoreStatus();
            }, self);
            this.storeHasSpecialDays = ko.computed(function () {
                return this.getStoreHasSpecialDay();
            }, self);


            this.storeHasHoliday = ko.computed(function () {
                return this.getStoreHasHoliday();
            }, self);
            if (this.store() && this.store().upcoming_special_day) {
                this.specialDays = this.store().upcoming_special_day;
                let date = new Date(this.store().upcoming_special_day.date[0]);
                let startMonth = date.toLocaleString('en-us', { month: 'short' });
                this.specialDaysStart = [startMonth, date.getDate()];
                this.specialDayOpenTime = ko.computed(function () {
                    return this.getStoreSpecialDayTiming(this.specialDays);
                }, self);
            };


            fulfillmentData.subscribe(function (data) {
                if (data.hasOwnProperty('store')) {
                    self.store(data.store);
                }
            });

            geoLocation.location.subscribe(function (data) {
                if (data.hasOwnProperty('store')) {
                    self.store(data.store);
                }
            });
            $(document).on('openChangeStoreModal', function () {
                self.showModal();
            });

            return this;
        },

        postRender: function(el) {
            $(el).on('click.toggleDropdown', function(ev) {
                if ($(this).parent().attr('aria-hidden') == 'false' && ev.currentTarget == el) {
                    ev.preventDefault && ev.preventDefault();
                    ev.stopImmediatePropagation && ev.stopImmediatePropagation();
                }
            });
        },

        /**
         * Fetch suburbs for given postcode
         */
        fetchSuburbs: function () {
            var self = this;
            var postCode = self.postcode();

            if (postCode.length < 3) {
                return;
            }

            self.suburbs.removeAll();

            fullScreenLoader.startLoader();
            self.isSearching(true);

            fetchSuburbsAction
                .fetchByPostcode(postCode, 'AU', false)
                .done(function (res) {
                    let suburbs = self.convertPostcodesDataToSuburbs(
                        res.items || []
                    );
                    self.suburbs(suburbs);
                })
                .fail(function (err) {
                    fullScreenLoader.stopLoader();
                    self.isSearching(false);
                })
                .always(function (res) {
                    fullScreenLoader.stopLoader();
                    self.isSearching(false);
                });
        },

        /**
         * @param {Object} suburb
         */
        fetchStores: function (suburb) {
            var self = this;

            fullScreenLoader.startLoader();
            self.isSearching(true);
            self.stores.removeAll();
            self.suburbs.removeAll();

            stockCheckAction
                .fetchAvailableStores(suburb.postcode, false)
                .done(function (res) {
                    if (res.stores) {
                        _.forEach(res.stores, function (store) {
                            self.stores.push(store);
                        });
                    }
                    fullScreenLoader.stopLoader();
                    self.isSearching(false);
                })
                .fail(function (err) {
                    console.warn(err);
                    fullScreenLoader.stopLoader();
                    self.isSearching(false);
                });
        },

        /**
         * @param {Object} store
         */
        selectStore: function (store) {
            var self = this;

            fullScreenLoader.startLoader();
            self.stores.removeAll();

            stockCheckAction
                .changeStore(store)
                .done(function (res) {
                    self.store(store);
                    localStorage.setItem('fulfilmentStoreBox', 1);
                })
                .fail(function (err) {
                    console.warn(err);
                })
                .always(function (res) {
                    fullScreenLoader.stopLoader();
                    self.hideModal();
                });
        },

        /**
         * Fetch suburbs based on user's current location
         */
        useCurrentLocation: function () {
           //geoLocation.getLocation(true);
            geoLocation.setBrowserLocation();
            this.hideModal();
        },

        /**
         * Takes a postcode response and converts into suburb objects.
         *
         * @param {Array} postcodesData
         * @returns {Array}
         */
        convertPostcodesDataToSuburbs: function (postcodesData) {
            return postcodesData
                .filter(function (po) {
                    return typeof po.locality !== 'undefined';
                })
                .map(function (po) {
                    return {
                        country: 'AU',
                        city: po.locality,
                        region: po.state,
                        postcode: po.postcode,
                    };
                });
        },

        /**
         * Initialization of popup
         *
         * @param {HTMLElement} element
         */
        createModal: function (element) {
            var self = this,
                options = {
                    type: 'popup',
                    modalClass: 'fulfillment-popup change-store-default',
                    focus: '[name=postcode]',
                    responsive: true,
                    innerScroll: true,
                    trigger: '',
                    buttons: [],
                    closed: function () {
                        self.postcode('');
                        self.suburbs.removeAll();
                        self.stores.removeAll();
                    },
                };

            this.modalWindow = element;
            modal(options, $(this.modalWindow));
        },

        /** Shows the store popup */
        showModal: function () {
            if (this.modalWindow) {
                $(this.modalWindow).modal('openModal');
            }
        },

        /** Hides the store popup */
        hideModal: function () {
            if (this.modalWindow) {
                $(this.modalWindow).modal('closeModal');
            }
        },

        /**
         * Get current status of the selected store based on store timings.
         *
         * @returns {Boolean}
         */
        getStoreStatus: function () {
            var result = false,
                storeTimings = this.getStoreTimings(),
                msg = '';
            if ('undefined' !== typeof storeTimings) {
                result =
                    storeTimings.today > storeTimings.open &&
                    storeTimings.today < storeTimings.close;

                if (result) {
                    msg = storeTimings.closes;
                } else {
                    msg = '';
                }
                this.storeMessage(msg);
            }

            return result;
        },

        getStoreHasSpecialDay: function(){
         var store = this.store();
            if (store && store.upcoming_special_day) {
                var specialDays = store.upcoming_special_day;
                if (specialDays) {
                    return true;
                }
            }
            return false;
        },

        getStoreSpecialDayTiming: function(specialDays) {
            return specialDays.time_open && specialDays.time_open != specialDays.time_close;
        },


        getStoreHasHoliday: function() {
            var store = this.store();
            if (store && store.upcoming_holiday) {
                var holiday = store.upcoming_holiday;
                if (holiday) {
                    return true;
                }
            }

            return false;
        },

        /**
         * Get currently selected store timings in comparable format.
         *
         * @returns {Object}
         */
        getStoreTimings: function () {
            var result, isHoliday,
                isSpecialDay = false,
                days = [
                    'sunday',
                    'monday',
                    'tuesday',
                    'wednesday',
                    'thursday',
                    'friday',
                    'saturday',
                ],
                store = this.store(),
                today = new Date(),
                day = days[today.getDay()],
                opening = new Date(),
                closing = new Date();
            if (store && store.zipcode && store.is_visible == '1' && day) {
                if (store.upcoming_special_day) {
                    var specialDaysDate = store.upcoming_special_day.date;
                    specialDaysDate.forEach(function(item, index){
                       var specialDay = new Date(item).toDateString();
                       if(specialDay == today.toDateString()) {
                        let open = store.upcoming_special_day.time_open.split(':'),
                            close = store.upcoming_special_day.time_close.split(':');
                            isSpecialDay = true;
                            opening.setHours(parseInt(open[0]), parseInt(open[1]), 0);
                            closing.setHours(parseInt(close[0]), parseInt(close[1]), 0);

                        result = {
                            today: today.getTime(),
                            open: opening.getTime(),
                            closes: store.upcoming_special_day.time_close,
                            close: closing.getTime(),
                        };
                        return result;
                       }
                    });

                }
                if (store.upcoming_holiday) {
                    var holidayDate = store.upcoming_holiday.date;
                    if (holidayDate) {
                        holidayDate.forEach(function(item, index){
                            var holiday = new Date(item).toDateString();
                            if(holiday == today.toDateString()) {
                                isHoliday = true;
                                return false;
                            }
                         });
                    }
                }
                if(!isSpecialDay && !isHoliday) {
                    let open = store[day + '_open'].split(':'),
                    close = store[day + '_close'].split(':');
                    opening.setHours(parseInt(open[0]), parseInt(open[1]), 0);
                    closing.setHours(parseInt(close[0]), parseInt(close[1]), 0);

                    result = {
                        today: today.getTime(),
                        open: opening.getTime(),
                        closes: store[day + '_close'],
                        close: closing.getTime(),
                    };
                }

            }

            return result;
        },

        /**
         * Get store path and redirect to store
         */
        storeUrl: function () {
            let path = this.store() ? this.store()['rewrite_request_path'] : '';
            window.location = urlBuilder.build(path);
        },

        formateDate: function(date) {
            return new Date(date).toDateString();
        }

    });
});
