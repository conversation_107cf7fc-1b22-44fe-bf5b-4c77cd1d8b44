/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 * @description A work-around to fix braintree recaptcha preventing free methods orders
 */

define([
    'jquery',
    'Magento_ReCaptchaWebapiUi/js/webapiReCaptchaRegistry'
], function ($, recaptchaRegistry) {
    'use strict';

    return function (FreeMethod) {
        return FreeMethod.extend({
            /**
             * @inheritdoc
             */
            placeOrder: function() {
                if (this.isAvailable()) {
                    this.resetRecaptchaTriggers();
                }

                this._super();
            },

            /**
             * Reset captcha triggers to skip captcha validation.
             */
            resetRecaptchaTriggers: function() {
                recaptchaRegistry.triggers = {};
            }
        });
    };
});
