define(
    [
        'ko'
    ],
    function (
        ko
    ) {

        'use strict';

        var message = ko.observable(null);

        return {

            /**
             * Explicitly subscribe to message and code
             * in dependant components to get any changes
             */
            message: message,

            getMessage: function () {

                return this.message;
            },

            setMessage: function (data) {

                return this.message(data);
            },

        };
    }
);
