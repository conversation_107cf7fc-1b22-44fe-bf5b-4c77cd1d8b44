/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'underscore',
    'Totaltools_Checkout/js/model/address-patterns',
    'Totaltools_Checkout/js/model/selected-shipping-method',
    'Totaltools_Checkout/js/model/selected-store',
    'Magento_Checkout/js/model/shipping-address/form-popup-state',
    'mage/utils/wrapper'
], function ($, _, patterns, shippingMethod, selectedStore, formPopup, wrapper) {
    'use strict';

    return function (validator) {
        validator.addRule(
            'validate-phone-custom',
            function (value, params, additionalParams) {
                return (
                    $.mage.isEmptyNoTrim(value) ||
                    /^\+?([0-9]{1,3})\)?[- ]?([0-9]{3,4})[- ]?([0-9]{4,5})$/.test(
                        value
                    )
                );
            },
            $.mage.__('Please enter a valid phone number.')
        );

        validator.addRule(
            'prevent-pobox-rule',
            function (value, params) {
                var validate = true;

                patterns.forEach(function (pattern) {
                    if (pattern.test(value)) {
                        validate = false;
                    }
                });

                return validate;
            },
            $.mage.__('We cannot ship to PO boxes or parcel lockers.')
        );

        validator.addRule(
            'au-only-delivery',
            function (value, params, additionalParams) {
                if (!shippingMethod.isStorePickup() && value !== 'AU') {
                    return false;
                }

                return true;
            },
            $.mage.__('We cannot ship to an address outside Australia.')
        );

        validator.addRule(
            'validate-pickup-store',
            function (value, params) {
                if (shippingMethod.isStorePickup()) {
                    let store = selectedStore.store();
                    return Boolean(_.isObject(store) && !_.isEmpty(store));
                }

                return true;
            },
            $.mage.__('Please select a store for Click & Collect.')
        );

        validator.addRule(
            'validate-atl',
            function (value, params) {
                let isDelivery = !shippingMethod.isStorePickup(),
                    atlChecked = $('input[name*="shippit_authority_to_leave"]:checked');

                if (
                    atlChecked.length &&
                    isDelivery &&
                    $.mage.isEmptyNoTrim(value) &&
                    formPopup.isVisible() == false
                ) {
                    return false;
                }

                return true;
            },
            $.mage.__('Please provide delivery instructions.')
        );

        validator.addRule(
            'validate-pickup',
            function (value, params) {
                let isPickup = shippingMethod.isStorePickup(),
                    thirdPartyChecked = $('input[name*="third_party_pickup"]:checked');

                if (
                    isPickup &&
                    thirdPartyChecked.length &&
                    $.mage.isEmptyNoTrim(value) &&
                    formPopup.isVisible() == false
                ) {
                    return false;
                }

                return true;
            },
            $.mage.__('Please nominate a person for third party pickup.')
        );

        validator.addRule(
            'validate-store',
            function(value, params) {
                let store = selectedStore.store(),
                    isPickup = shippingMethod.isStorePickup();

                    if (
                        $.mage.isEmptyNoTrim(value) &&
                        isPickup &&
                        (!_.isObject(store) || _.isEmpty(store))
                    ) {
                        return false;
                    }

                return true;
            },
            $.mage.__('A store must be selected with Click and Collect method.')
        );

        validator.addRule(
            'validate-au-postcode',
            function (value) {
                return /^\d{4}$/.test(value);
            },
            $.mage.__('Please enter a valid Australian postcode (4 digits)')
        );

        return validator;
    };
});
