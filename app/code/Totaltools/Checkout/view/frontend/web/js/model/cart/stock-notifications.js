/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define(['ko'], function (ko) {
    'use strict';

    var notifications = ko.observable(null);

    return {
        /**
         * Explicitly subscribe to notifications
         * in dependant components to get any changes
         */
        notifications: notifications,

        /**
         * Notifications Getter
         *
         * @returns {Observable}
         */
        getNotifications: function () {
            return this.notifications;
        },

        /**
         * Notifications Setter
         *
         * @param {Object} notifications
         */
        setNotifications: function (notifications) {
            this.notifications(notifications);
        },
    };
});
