define(['ko', 'mage/translate'], function (ko, $t) {
    'use strict';

    var message = ko.observable(''),
        code = ko.observable(0),
        shippingDangerous = ko.observable(false);

    return {
        /**
         * Explicitly subscribe to message and code
         * in dependant components to get any changes
         */
        message: message,

        code: code,

        shippingDangerous: shippingDangerous,

        shippingDangerousMessage: $t(
            'At least one item in your cart is not available for delivery as it is classified as dangerous good or is too bulky'
        ),

        isLoading: ko.observable(false),

        getMessage: function () {
            return this.message;
        },

        setMessage: function (data) {
            return this.message(data);
        },

        getCode: function () {
            return this.code;
        },

        setCode: function (data) {
            return this.code(data);
        },

        getShippingDangerous: function () {
            return this.shippingDangerous;
        },

        setShippingDangerous: function (data) {
            return this.shippingDangerous(data);
        },

        showMessage: ko.computed(function () {
            return window.checkoutConfig.stockMessages.enabled || code() == 5;
        }),
    };
});
