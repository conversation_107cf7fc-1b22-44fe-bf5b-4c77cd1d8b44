define([
	'Magento_Ui/js/form/form',
	'Totaltools_Checkout/js/model/selected-shipping-method',
	'ko',
	'Magento_Checkout/js/model/quote'
], function (
	Component,
	selectedShippingMethod,
	ko,
	quote
) {
		'use strict';

		return Component.extend({
			initialize: function () {
				this._super();
				// component initialization logic
				return this;
			},

			isVisible: ko.computed(function () {
				var method = selectedShippingMethod.getMethod();

				// return method() !== window.cc_method_name && !quote.isVirtual();
				return !quote.isVirtual();
			})
		});
	});
