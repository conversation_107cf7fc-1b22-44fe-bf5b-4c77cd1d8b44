/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'underscore',
    'ko',
    'uiComponent',
    'Magento_Customer/js/customer-data',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Checkout/js/model/cart/stock-notifications',
    'Totaltools_Checkout/js/model/cart/stock-notification-service',
    'Totaltools_Checkout/js/model/selected-location',
    'Totaltools_Checkout/js/model/selected-store',
], function (
    $,
    _,
    ko,
    Component,
    customerData,
    quote,
    stockNotifications,
    stockNotificationsAction,
    selectedLocation,
    selectedStore
) {
    'use strict';

    var fulfilmentData = customerData.get('fulfilment-data');

    return Component.extend({
        cache: fulfilmentData,

        notifications: stockNotifications.getNotifications(),

        isVisible: !quote.isVirtual(),

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();
            this.getStockNotifications();
            this._populateLocationData();
            return this;
        },
        fulfilmentData: customerData.get('fulfilment-data'),
        /**
         * Get Stock notifications
         */
        getStockNotifications: function () {
            stockNotificationsAction.fetch();
        },
        /**
         * Populate component store & location properties with local storage data if available.
         * @return {void}
         */
        _populateLocationData: function() {
            var data = this.fulfilmentData();

            if (!data.store && !data.location) {
                customerData.invalidate(['fulfilment-data']);
                customerData.reload(['fulfilment-data'], true);
                return;
            }

            if (data.store && "object" === typeof data.store) {
                selectedStore.setStore(data.store);
            }

            if (data.location && "object" === typeof data.location) {
                selectedLocation.setSuburb(data.location);
            }
        },
    });
});
