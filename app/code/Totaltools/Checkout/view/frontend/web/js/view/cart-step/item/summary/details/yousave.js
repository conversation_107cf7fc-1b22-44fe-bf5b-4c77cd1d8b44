/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2019-21 Totaltools. <https://totaltools.com.au>
 */

define([
    'uiComponent',
    'Magento_Checkout/js/model/quote',
    'Magento_Catalog/js/price-utils',
], function (Component, quote, priceUtils) {
    'use strict';

    const savings = window.checkoutConfig.specialPrices || {};

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/summary/item/details/yousave',
        },
        displayArea: 'after_details',

        /**
         * @param {Object} item
         * @returns {String|void}
         */
        getYouSavePrice: function (item) {
            if (!this.hasSpecialPrice(item)) {
                return;
            }

            var result,
                dicountItem = savings[item.item_id];

            switch (dicountItem.showSavingsLabel) {
                case 'P':
                    result =
                        'Save ' +
                        this.percentageFormatter(dicountItem.savingsPercentage);
                    break;

                case 'D':
                    result =
                        'Save ' +
                        this.decimalFormatter(
                            priceUtils.formatPrice(
                                savings[item.item_id]['savings'] * item.qty,
                                quote.getPriceFormat()
                            )
                        );
                    break;

                default:
                    break;
            }

            return result;
        },

        /**
         *
         * @param {Object} item
         * @returns {Boolean}
         */
        hasSpecialPrice: function (item) {
            return (
                savings[item.item_id] &&
                savings[item.item_id]['savings'] > 0 &&
                savings[item.item_id].showSavingsLabel != 'N'
            );
        },

        /**
         * @param {Number} price
         * @returns {String}
         */
        decimalFormatter: function (price) {
            if (price.indexOf('.') >= 0) {
                var formattedPrice = '',
                    currency = price[0],
                    decimalIndex = price.indexOf('.'),
                    priceStr = price.substring(
                        price.indexOf(currency) + 1,
                        decimalIndex
                    ),
                    decimalString = price.substring(decimalIndex + 1);

                var currencyHtml =
                        '<span class="savings-currency">' +
                        currency +
                        '</span>',
                    decimalHtml =
                        '<span class="decimal-dot">.</span><span class="savings-decimal">' +
                        decimalString +
                        '</span>';

                if (parseInt(decimalString) < 1) {
                    formattedPrice = currencyHtml + priceStr;
                } else {
                    formattedPrice = currencyHtml + priceStr + decimalHtml;
                }

                return formattedPrice;
            }

            return price;
        },

        /**
         * @param {Number} percentage
         * @returns {String}
         */
        percentageFormatter: function (percentage) {
            return percentage + '<span class="savings-decimal">%</span>';
        },
    });
});
