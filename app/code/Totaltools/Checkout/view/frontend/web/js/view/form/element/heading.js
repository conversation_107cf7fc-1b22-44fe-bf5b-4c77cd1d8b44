/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define(['uiComponent', 'ko'], function (Component, ko) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/form/element/heading',
        },
        heading: '',
        additionalClasses: {},

        /**
         * @inheritdoc
         */
        initObservable: function () {
            this._super()
                .observe(['heading']);
            
            return this;
        },
    });
});
