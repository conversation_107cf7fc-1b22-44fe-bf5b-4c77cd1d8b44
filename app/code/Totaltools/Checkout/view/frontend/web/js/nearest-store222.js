define([
    'jquery', 
    'Magento_Customer/js/customer-data',
    'Totaltools_Catalog/js/view/fulfilment-boxes',
    'Totaltools_Catalog/js/model/selected-store',
    'Totaltools_Catalog/js/model/selected-location',
    'Totaltools_Geo/js/model/geolocation'
], function ($, customerData, fulfilmentBoxes, selectedStore, selectedLocation, geolocation) {
    'use strict';
    var cacheData = localStorage.getItem('mage-cache-storage');
    navigator.geolocation.getCurrentPosition(showPosition);
    if (cacheData) {
        var parsedData = JSON.parse(cacheData);
        if (!parsedData['fulfilment-data'] || !parsedData['fulfilment-data'].store) {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(showPosition);
             }
        }
    } else {
        if (navigator.geolocation) {
            navigator.geolocation.getCurrentPosition(showPosition);
        }
    }  

    function showPosition(position) {
        const latitude = position.coords.latitude;
        const longitude = position.coords.longitude;
        $.ajax({
            url: '/totaltools/location/nearest',
            data: {
                latitude: latitude,
                longitude: longitude,
            },
            type: 'GET',
            dataType: 'json',
        }).done(function (data) {
           
            if (Object.keys(data.store).length > 0) {
                selectedStore.setStore(data.store);
                geolocation.setLocation(data.location);
                selectedLocation.setSuburb(data.location);
                localStorage.setItem('fulfilmentLocationBox', 1);
                localStorage.setItem('fulfilmentStoreBox', 1);
                fulfilmentBoxes().selectedStoreUpdate(true);
                
                fulfilmentBoxes()._updateClickAndCollectMessage(data.store.zipcode);
                $(document).trigger('ajax:nearest', { store: data });
            }
            
            
        });
    }
});
