/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'underscore',
    'mage/translate',
    'Magento_Checkout/js/model/quote',
    'Magento_Customer/js/model/customer',
    'Magento_Customer/js/customer-data',
    'Magento_Checkout/js/model/error-processor',
], function (_, $t, quote, customer, customerData, errorProcessor) {
    'use strict';

    var countryData = customerData.get('directory-data');

    return {
        validate: function () {
            var addressType = [],
                regionValidationResult = true;

            if (
                !quote.isVirtual() &&
                customer.isLoggedIn() &&
                !_.isEmpty(quote.shippingAddress()) &&
                !_.isEmpty(quote.billingAddress())
            ) {
                if (!_.isUndefined(countryData()[quote.shippingAddress()['countryId']]['regions']) && _.isEmpty(quote.shippingAddress()['regionId'])) {
                    addressType.push('shipping');
                    regionValidationResult = false;
                }

                if (
                    !_.isUndefined(countryData()[quote.billingAddress()['countryId']]['regions']) &&
                    _.isEmpty(quote.billingAddress()['regionId'])
                ) {
                    addressType.push('billing');
                    regionValidationResult = false;
                }
            }

            if (!regionValidationResult) {
                let type = addressType.join(' & '),
                    message = JSON.stringify({
                        message: $t(
                            'Your ' +
                                type +
                                ' address is missing state/region. Go to My Account > Order Addresses, edit this address and select a state/region.'
                        ),
                    }),
                    error = {
                        responseText: message,
                    };

                errorProcessor.process(error);
            }

            return regionValidationResult;
        },
    };
});
