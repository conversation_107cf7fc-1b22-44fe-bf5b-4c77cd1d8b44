/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */
define([
    'jquery',
    'uiComponent',
    'Magento_Checkout/js/model/quote'
], function ($, Component, quote) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/summary/discount-giftcard',
            isVisible: !quote.isVirtual(),
        },
        baseClass: '.payment-option',
        discountClass: '.discount-code',
        giftCardClass: '.giftcardaccount',

        /**
         * @param {UiClass} obj
         * @param {jQuery.Event} ev
         */
        handleClick: function (obj, ev) {
            ev.preventDefault();

            let $discount = $(this.baseClass + this.discountClass);
            let $giftCard = $(this.baseClass + this.giftCardClass);

            if ($discount.length && $giftCard.length) {
                !$discount.hasClass('_active') &&
                    $discount.find('.action-toggle').trigger('click');

                !$giftCard.hasClass('_active') &&
                    $giftCard.find('.action-toggle').trigger('click');

                $('html, body').animate(
                    { scrollTop: $discount.offset().top },
                    1000,
                    'swing',
                    function () {
                        $discount.find('#discount-code').focus();
                    }
                );
            }
        },
    });
});
