/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'uiRegistry',
    'Magento_Customer/js/model/customer',
    'Magento_Checkout/js/model/quote',
], function ($, registry, customer, quote) {
    'use strict';

    return {
        validate: function () {
            let result = true;

            if (!quote.isVirtual() && customer.isLoggedIn()) {
                registry.get('checkoutProvider', function (provider) {
                    provider.trigger('shippingAddress.data.validate');

                    if (provider.get('shippingAddress.custom_attributes')) {
                        provider.trigger('shippingAddress.custom_attributes.data.validate');
                    }

                    result = !provider.get('params.invalid') || !$('div.field:visible').hasClass('_error');
                });
            }

            return result;
        },
    };
});
