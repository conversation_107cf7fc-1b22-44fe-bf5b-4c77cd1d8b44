define(['jquery'], function ($) {
    'use strict';

    return function (Reward) {
        return Reward.extend({
            scrollToRewards: function (el, ev) {
                ev.preventDefault();

                let $rewards = $('#reward_placer');
                let topOffset = $rewards.offset().top - 60;

                if (!$rewards.hasClass('_active')) {
                    $rewards.find('[data-role="title"]').trigger('click');
                }

                $('body, html').animate(
                    { scrollTop: topOffset },
                    1000,
                    'swing',
                    () => {
                        $rewards.find('#reward-points-amount').focus();
                    }
                );
            },
        });
    };
});
