/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'mage/utils/wrapper',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Checkout/js/model/selected-shipping-method'
], function ($, wrapper, quote, selectedShippingMethod) {
    'use strict';

    return function (processor) {
        return wrapper.wrap(processor, function (proceed, payload) {
            payload = proceed(payload);

            var storeId = 0;
            if (quote.extension_attributes && quote.extension_attributes.storelocator_id) {
                storeId = quote.extension_attributes.storelocator_id;
            }

            var extentionAttributes = {
                third_party_pickup: $('#pickup-options [name="third_party_pickup"]').is(':checked') ? 1 : 0,
                third_party_name: $('#pickup-options [name="third_party_name"]').val(),
                is_loyal: $('[name="is_loyal"]').is(':checked'),
                storelocator_id: storeId
            };

            if (!selectedShippingMethod.isStorePickup()) {
                delete extentionAttributes.third_party_pickup;
                delete extentionAttributes.third_party_name;
            } else {
                delete payload.addressInformation.extension_attributes.shippit_authority_to_leave;
                delete payload.addressInformation.extension_attributes.shippit_delivery_instructions;
            }

            payload.addressInformation.extension_attributes = _.extend(
                payload.addressInformation.extension_attributes,
                extentionAttributes
            );

            payload.addressInformation.custom_attributes = quote.shippingAddress().customAttributes;

            return payload;
        });
    };
});
