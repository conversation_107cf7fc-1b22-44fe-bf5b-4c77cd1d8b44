/*jshint browser:true*/
/*global define*/
define([
    'ko',
    'underscore',
    'jquery',
    'Magento_Ui/js/form/form',
    'Magento_Customer/js/model/customer',
    'Totaltools_Checkout/js/model/billing-address/billing-address-list',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/model/address-converter',
    'Totaltools_Checkout/js/action/create-billing-address',
    'Magento_Checkout/js/action/select-billing-address',
    'Magento_Checkout/js/checkout-data',
    'Magento_Checkout/js/model/checkout-data-resolver',
    'Magento_Customer/js/customer-data',
    'Magento_Checkout/js/action/set-billing-address',
    'Totaltools_Checkout/js/model/billing-address/form-popup-state',
    'Magento_Ui/js/modal/modal',
    'Magento_Ui/js/model/messageList',
    'mage/translate',
    'Magento_Ui/js/modal/alert',
], function (
    ko,
    _,
    $,
    Component,
    customer,
    addressList,
    quote,
    addressConverter,
    createBillingAddress,
    selectBillingAddress,
    checkoutData,
    checkoutDataResolver,
    customerData,
    setBillingAddressAction,
    formPopUpState,
    modal,
    globalMessageList,
    $t,
    uiAlert
) {
    'use strict';

    var popUp = null,
        lastSelectedBillingAddress = null,
        countryData = customerData.get('directory-data'),
        manualAddressInput = ko.observable(false);

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/billing-address',
            actionsTemplate: 'Magento_Checkout/billing-address/actions',
        },
        visible: ko.observable(!quote.isVirtual()), //
        errorValidationMessage: ko.observable(false), //
        isCustomerLoggedIn: customer.isLoggedIn(),
        isFormPopUpVisible: formPopUpState.isVisible,
        isFormInline: !customer.isLoggedIn(),

        currentBillingAddress: quote.billingAddress,
        customerHasAddresses: addressList().length > 0,

        isNewAddressAdded: ko.observable(false),
        isGuestBillingAdded: ko.observable(false),
        saveInAddressBook: 1,
        quoteIsVirtual: quote.isVirtual(),

        manualAddressInput: manualAddressInput,

        /**
         * Init component
         */
        initialize: function () {
            var self = this,
                hasNewAddress;

            this._super();

            checkoutDataResolver.resolveBillingAddress();

            hasNewAddress = addressList().some(function (address) {
                return address.getType() == 'new-customer-billing-address';
            });

            this.isNewAddressAdded(hasNewAddress);

            this.isFormPopUpVisible.subscribe(function (value) {
                if (value) {
                    self.getPopUp().openModal();
                }
            });

            this.registerEventListerners();
        },

        /**
         * @return {exports.initObservable}
         */
        initObservable: function () {
            this._super().observe({
                addressFormVisible: false,
                isAddressSameAsShipping: true,
                saveInAddressBook: 1,
                selectedAddress: null,
            });

            this.isAddressFormVisible = ko.computed(function () {
                var visible = this.addressFormVisible();
                if (
                    (quote.shippingMethod() &&
                        quote.shippingMethod().carrier_code ==
                            window.cc_carrier_code) ||
                    quote.isVirtual()
                ) {
                    visible = true;
                }
                return visible;
            }, this);

            this.isCheckboxVisible = ko.computed(function () {
                var visible = false;
                if (
                    !quote.isVirtual() &&
                    quote.shippingAddress() &&
                    quote.shippingAddress().canUseForBilling()
                ) {
                    visible = true;
                }
                if (
                    quote.shippingMethod() &&
                    quote.shippingMethod().carrier_code ==
                        window.cc_carrier_code
                ) {
                    visible = false;
                }
                return visible;
            }, this);

            return this;
        },

        /**
         * Register event listeners related to this component
         */
        registerEventListerners: function () {
            var self = this,
                LOQATE_BILLING_KEY = 'ZB49-MG77-EB26-TY95';

            if (typeof pca !== 'undefined') {
                pca.on('data', function (source, key) {
                    if (key === LOQATE_BILLING_KEY) {
                        self.updateAddress();
                    }
                });

                pca.on('load', function (type, key, control) {
                    control.listen('manual', function (res, attrs) {
                        self.manualAddressInput(key == LOQATE_BILLING_KEY);
                    });
                });
            }
        },

        canUseShippingAddress: ko.computed(function () {
            return (
                !quote.isVirtual() &&
                quote.shippingAddress() &&
                quote.shippingAddress().canUseForBilling()
            );
        }),

        /**
         * @return {Boolean}
         */
        useShippingAddress: function () {
            if (this.isAddressSameAsShipping()) {
                selectBillingAddress(quote.shippingAddress());
                this.addressFormVisible(false);
            } else {
                lastSelectedBillingAddress = quote.billingAddress();

                if (this.isGuestBillingAdded()) {
                    this.updateAddress();
                    return true;
                }

                quote.billingAddress(null);
                this.addressFormVisible(false);

                if (addressList().length == 0) {
                    this.addressFormVisible(true);
                }
            }
            checkoutData.setSelectedBillingAddress(null);
            return true;
        },

        /**
         * Edit address action
         */
        editAddress: function () {
            lastSelectedBillingAddress = quote.billingAddress();
            quote.billingAddress(null);
            this.addressFormVisible(false);
        },

        /**
         * Cancel address edit action
         */
        cancelAddressEdit: function () {
            this.restoreBillingAddress();

            if (quote.billingAddress()) {
                // restore 'Same As Shipping' checkbox state
                this.isAddressSameAsShipping(
                    quote.billingAddress() != null &&
                        quote.billingAddress().getCacheKey() ==
                            quote.shippingAddress().getCacheKey() && //eslint-disable-line
                        !quote.isVirtual()
                );
                this.addressFormVisible(true);
            }
        },

        /**
         * Manage cancel button visibility
         */
        canUseCancelBillingAddress: ko.computed(function () {
            return quote.billingAddress() || lastSelectedBillingAddress;
        }),

        /**
         * Restore billing address
         */
        restoreBillingAddress: function () {
            if (lastSelectedBillingAddress != null) {
                selectBillingAddress(lastSelectedBillingAddress);
            }
        },

        /**
         * @param {int} countryId
         * @return {*}
         */
        getCountryName: function (countryId) {
            return countryData()[countryId] != undefined
                ? countryData()[countryId].name
                : '';
        },

        /**
         * Get code
         * @param {Object} parent
         * @returns {String}
         */
        getCode: function (parent) {
            return _.isFunction(parent.getCode) ? parent.getCode() : 'shared';
        },

        /**
         * @return {*}
         */
        getPopUp: function () {
            var self = this,
                buttons;

            if (!popUp) {
                buttons = this.popUpForm.options.buttons;
                this.popUpForm.options.buttons = [
                    {
                        text: buttons.save.text
                            ? buttons.save.text
                            : $t('Save Address'),
                        class: buttons.save.class
                            ? buttons.save.class
                            : 'action primary action-save-address',
                        click: self.saveNewAddress.bind(self),
                    },
                    {
                        text: buttons.cancel.text
                            ? buttons.cancel.text
                            : $t('Cancel'),
                        class: buttons.cancel.class
                            ? buttons.cancel.class
                            : 'action secondary action-hide-popup',

                        /** @inheritdoc */
                        click: this.onClosePopUp.bind(this),
                    },
                ];
                this.popUpForm.options.closed = function () {
                    self.isFormPopUpVisible(false);
                };

                this.popUpForm.options.modalClass = 'billing-form-modal';

                this.popUpForm.options.modalCloseBtnHandler = this.onClosePopUp.bind(
                    this
                );
                this.popUpForm.options.keyEventHandlers = {
                    escapeKey: this.onClosePopUp.bind(this),
                };

                /** @inheritdoc */
                this.popUpForm.options.opened = function () {
                    // Store temporary address for revert action in case when user click cancel action
                    self.temporaryAddress = $.extend(
                        true,
                        {},
                        checkoutData.getBillingAddressFromData()
                    );
                };
                popUp = modal(
                    this.popUpForm.options,
                    $(this.popUpForm.element)
                );
            }

            return popUp;
        },

        /**
         * Revert address and close modal.
         */
        onClosePopUp: function () {
            checkoutData.setBillingAddressFromData(
                $.extend(true, {}, this.temporaryAddress)
            );
            this.getPopUp().closeModal();
        },

        /**
         * Show address form popup
         */
        showFormPopUp: function () {
            this.isFormPopUpVisible(true);
        },

        /**
         * Save new billing address
         */
        saveNewAddress: function () {
            var addressData, newBillingAddress;

            this.source.set('params.invalid', false);
            this.source.trigger('billingAddress.data.validate');

            if (!this.source.get('params.invalid')) {
                addressData = this.source.get('billingAddress');
                // if user clicked the checkbox, its value is true or false. Need to convert.
                addressData.save_in_address_book = this.saveInAddressBook ? 1 : 0;

                // New address must be selected as a billing address
                newBillingAddress = createBillingAddress(addressData);
                selectBillingAddress(newBillingAddress);
                checkoutData.setSelectedBillingAddress(newBillingAddress.getKey());
                checkoutData.setNewCustomerBillingAddress($.extend(true, {}, addressData));
                this.getPopUp().closeModal();
                this.isNewAddressAdded(true);
            } else {
                this.validateHiddenFields(this.source.get('billingAddress'));
                this.onErrorFocus('.billing-form-modal .modal-content:visible');
            }
        },

        /**
         * Update address action
         */
        updateAddress: function () {
            var addressData, newBillingAddress;

            if (this.selectedAddress() && !this.isAddressFormVisible()) {
                selectBillingAddress(this.selectedAddress());
                checkoutData.setSelectedBillingAddress(
                    this.selectedAddress().getKey()
                );
            } else {
                this.source.set('params.invalid', false);
                this.source.trigger(this.dataScopePrefix + '.data.validate');

                if (this.source.get(this.dataScopePrefix + '.custom_attributes')) {
                    this.source.trigger(this.dataScopePrefix + '.custom_attributes.data.validate');
                }

                if (!this.source.get('params.invalid')) {
                    addressData = this.source.get(this.dataScopePrefix);

                    if (!quote.isVirtual()) {
                        var shippingAddress = addressConverter.quoteAddressToFormAddressData(
                            quote.shippingAddress()
                        );

                        for (var field in addressData) {
                            if (
                                addressData.hasOwnProperty(field) &&
                                shippingAddress.hasOwnProperty(field) &&
                                _.isEmpty(addressData[field])
                            ) {
                                addressData[field] = shippingAddress[field];
                            }
                        }
                    }

                    if (customer.isLoggedIn() && !this.customerHasAddresses) {
                        //eslint-disable-line max-depth
                        this.saveInAddressBook(1);
                    }
                    addressData[
                        'save_in_address_book'
                    ] = this.saveInAddressBook() ? 1 : 0;
                    newBillingAddress = createBillingAddress(addressData);
                    // New address must be selected as a billing address
                    selectBillingAddress(newBillingAddress);
                    checkoutData.setSelectedBillingAddress(
                        newBillingAddress.getKey()
                    );
                    checkoutData.setNewCustomerBillingAddress(addressData);
                    this.isGuestBillingAdded(!customer.isLoggedIn());
                } else {
                    this.validateHiddenFields(this.source.get('billingAddress'));
                    this.onErrorFocus();
                    
                    return false;
                }
            }
            this.updateAddresses();
        },

        /**
         * Trigger action to update shipping and billing addresses
         */
        updateAddresses: function () {
            if (
                window.checkoutConfig.reloadOnBillingAddress ||
                !window.checkoutConfig.displayBillingOnPaymentMethod
            ) {
                setBillingAddressAction(globalMessageList);
            }
        },

        /**
         * @return {Boolean}
         */
        validateBillingInformation: function () {
            var billingAddress,
                addressData,
                loginFormSelector = 'form[data-role=email-with-possible-login]',
                emailValidationResult = customer.isLoggedIn();

            if (!quote.billingMethod()) {
                this.errorValidationMessage('Please specify a billing method.');
                return false;
            }

            if (!customer.isLoggedIn()) {
                $(loginFormSelector).validation();
                emailValidationResult = Boolean(
                    $(loginFormSelector + ' input[name=username]').valid()
                );
            }

            if (this.isFormInline) {
                this.source.set('params.invalid', false);
                this.source.trigger('billingAddress.data.validate');

                if (this.source.get('billingAddress.custom_attributes')) {
                    this.source.trigger(
                        'billingAddress.custom_attributes.data.validate'
                    );
                }

                if (
                    this.source.get('params.invalid') ||
                    !quote.billingMethod().method_code ||
                    !quote.billingMethod().carrier_code ||
                    !emailValidationResult
                ) {
                    return false;
                }

                var billing = quote.billingAddress();
                addressData = addressConverter.formAddressDataToQuoteAddress(
                    this.source.get('billingAddress')
                );

                // Copy form data to quote billing address object
                for (var field in addressData) {
                    if (
                        addressData.hasOwnProperty(field) &&
                        billingAddress.hasOwnProperty(field) &&
                        typeof addressData[field] != 'function' &&
                        _.isEqual(billingAddress[field], addressData[field])
                    ) {
                        billingAddress[field] = addressData[field];
                    } else if (
                        typeof addressData[field] != 'function' &&
                        !_.isEqual(billingAddress[field], addressData[field])
                    ) {
                        billingAddress = addressData;
                        break;
                    }
                }

                if (customer.isLoggedIn()) {
                    billingAddress.save_in_address_book = 1;
                }
                selectBillingAddress(billingAddress);
            }

            if (!emailValidationResult) {
                $(loginFormSelector + ' input[name=username]').focus();

                return false;
            }

            return true;
        },

        /**
         * Validates fields hidden by loqate fetchAdddress widget.
         *
         * @param {Object} addressData
         * @returns {Boolean}
         */
        validateHiddenFields: function (addressData) {
            if (
                addressData.street[0] &&
                (!addressData.country_id ||
                    !addressData.city ||
                    !addressData.postcode ||
                    (!addressData.region_id && $('div[name^="billingAddress.region_id"]').hasClass('_required'))
                )
            ) {
                pca &&
                    pca.capturePlus &&
                    pca.capturePlus.controls.length > 1 &&
                    pca.capturePlus.controls[1].fire('manual');
                
                return false;
            }

            return true;
        },

        /**
         * @param {string} target
         */
        onErrorFocus: function (target) {
            var errorElement = $("div[class*='_error']:visible").first();

                if (!errorElement) {
                    errorElement = $("div[class*='mage-error']:visible").first();
                }
                
                if (!errorElement) {
                    errorElement = $("p[class*='error-message']:visible").first();
                }

            if (errorElement && typeof errorElement.offset() !== 'undefined') {
                var windowHeight = target ? $(target).height() : $(window).height(),
                    offsetTop = target ? errorElement.position().top : errorElement.offset().top;

                if (!target) {
                    target = 'html, body';
                }

                $(target).animate({
                    scrollTop: offsetTop - windowHeight / 3,
                });

                errorElement.focus();
            }
        },
    });
});
