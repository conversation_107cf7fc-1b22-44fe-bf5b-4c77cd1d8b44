/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'underscore',
    'mage/translate',
    'Magento_Checkout/js/model/error-processor',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Checkout/js/model/address-patterns',
], function ($, _, $t, errorProcessor, quote, patterns) {
    'use strict';

    return {
        validate: function() {
            var poBoxValidationResult = true;
            var shippingAddress = quote.shippingAddress();

            if (!quote.isVirtual() && !_.isEmpty(shippingAddress)) {
                _.each(shippingAddress, function(val) {
                    patterns.forEach(function (pattern) {
                        if (typeof val !== 'function' && pattern.test(val)) {
                            poBoxValidationResult = false;
                        }
                    });
                })
            }

            if (!poBoxValidationResult) {
                let message = JSON.stringify({
                    message: $t('We cannot ship to PO boxes or parcel lockers.'),
                }),
                    error = {
                        responseText: message,
                    };

                errorProcessor.process(error);
            }

            return poBoxValidationResult;
        },
    };
});
