/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'mage/url',
    'Totaltools_Checkout/js/model/selected-store',
    'Totaltools_Checkout/js/model/selected-location',
    'Totaltools_Checkout/js/model/selected-shipping-method',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/model/payment-service',
    './stock-notifications',
    '../delivery-message',
    '../click-and-collect-message',
], function (
    $,
    url,
    selectedStore,
    selectedLocation,
    selectedShippingMethod,
    quote,
    paymentService,
    stockNotifications,
    deliveryMessage,
    clickAndCollectMessage
) {
    'use strict';

    var serviceUrl = url.build('totaltools_storelocator/product/stockavailabilitymessage'),
        coreCheckoutDisabled = !!window.core_checkout_disabled,
        /**
         * Add subscribers that would trigger stock message request with each update.
         * Executed as soon as script file is loaded
         */
        registerSubscribers = function () {
            selectedLocation.suburb.subscribe(stockMessagesRequest);
            selectedStore.store.subscribe(stockMessagesRequest);
            quote.totals.subscribe(stockMessagesRequest);
            quote.shippingMethod.subscribe(function (method) {
                selectedShippingMethod.setMethod(method);
                stockMessagesRequest();
            });
        },
        /**
         * @returns {Object}
         */
        buildParams = function () {
            var postCode = selectedLocation.suburb() ? selectedLocation.suburb().postcode : '',
                selectedAddress = quote.shippingAddress(),
                quoteShippingMethod = quote.shippingMethod(),
                shippingMethod = quoteShippingMethod
                    ? quoteShippingMethod['carrier_code'] +
                      '_' +
                      quoteShippingMethod['method_code']
                    : selectedShippingMethod.getMethod()(),
                storeId = selectedStore.store() ? selectedStore.store().storelocator_id : 0;

            if (selectedAddress && selectedAddress.postcode) {
                postCode = selectedAddress.postcode;
            }

            return {
                postcode: postCode,
                storelocator_id: storeId,
                method: shippingMethod,
            };
        },
        /**
         * Validates params being passed to stock message endpoint.
         *
         * @param {Object} payload
         * @returns {Boolean}
         */
        validateParams = function (payload) {
            return (
                payload.postcode &&
                typeof payload.storelocator_id !== 'undefined' &&
                payload.method
            );
        },
        /**
         * @param {Object} options
         * @param {$.Deferred} deferred
         * @returns {Void}
         */
        stockMessagesRequest = function (options) {
            var params = buildParams(),
                settings = $.extend(
                    {
                        url: serviceUrl,
                        type: 'GET',
                        data: params,
                        dataType: 'json',
                    },
                    options
                );

            if (!validateParams(params)) {
                return false;
            }

            if (params.method == window.checkoutConfig.cc_method_name) {
                clickAndCollectMessage.isLoading(true);
            } else {
                deliveryMessage.isLoading(true);
            }

            // paymentService.isLoading(true);

            return $.ajax(settings)
                .done(function (res) {
                    handleStockMessages(res);
                })
                .fail(function (err) {
                    console.warn(err);
                })
                .always(function () {
                    // paymentService.isLoading(false);
                    deliveryMessage.isLoading(false);
                    clickAndCollectMessage.isLoading(false);
                });
        },
        /**
         * Handles stock message response by assigning messages to relaed observable vars
         *
         * @param {Object} messages
         */
        handleStockMessages = function (messages) {
            if (JSON.stringify(messages) == '{}') {
                return;
            }

            /** Set incoming notification to related model */
            stockNotifications.setNotifications(messages);

            var maxCode = -1;

            /** Parse icoming notifications for deliver & store pickup messages */
            $.each(messages, function (key, msg) {
                if (maxCode < msg.code) {
                    var selectedMethod = quote.shippingMethod()
                        ? quote.shippingMethod()['carrier_code'] +
                          '_' +
                          quote.shippingMethod()['method_code']
                        : '';

                    if (selectedMethod == window.checkoutConfig.cc_method_name) {
                        clickAndCollectMessage.setMessage(msg.cart_message);
                        clickAndCollectMessage.setCode(msg.code);
                    } else {
                        deliveryMessage.setMessage(msg.cart_message);
                        deliveryMessage.setCode(msg.code);                        
                    }
                    
                    maxCode = msg.code;
                }

                if (typeof msg.dangerous_item !== 'undefined') {
                    deliveryMessage.setShippingDangerous(
                        msg.dangerous_item
                    );
                }                
            });

            var checkoutBtn = $('[data-role=proceed-to-checkout], [data-role=opc-continue]');
            if (maxCode == 5) {
                checkoutBtn.attr('disabled', true);
            } else {
                checkoutBtn.attr('disabled', false);
            }
        };

    /** Register subscription callbacks on init */
    registerSubscribers();

    return {
        fetch: stockMessagesRequest,
    };
});
