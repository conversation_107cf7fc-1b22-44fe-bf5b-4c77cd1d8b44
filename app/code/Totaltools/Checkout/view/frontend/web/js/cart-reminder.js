/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'Magento_Customer/js/customer-data',
    'mage/translate',
    'mage/storage',
    'addToCartNotification',
    'jquery/jquery-storageapi',
], function ($, customerData, $t) {
    'use strict';

    var cartReminder,
        showNotificationIfIdle,
        cartData = customerData.get('cart'),
        customer = customerData.get('customer'),
        reminderSent = false,
        options = {
            idleTimeout: 24 * 60 * 60 * 1000,
            notification: {
                templateId: '#cart-reminder-template',
                notificationsWrapper: '#add-cart-notifications',
            },
        };

    /**
     * Shows a notification if a user is idle for given time.
     */
    showNotificationIfIdle = function () {
        var timestamp = Date.now(),
            lastActive = cartReminder.get() || timestamp,
            timeDiff = timestamp - lastActive,
            customerName = customer()?.firstname ?? false;

        if (!reminderSent && timeDiff >= options.idleTimeout) {
            $('<div/>').addToCartNotification({
                data: { name: customerName },
                ...options.notification,
            });

            reminderSent = true;
        }
    };

    /**
     * Cart data subscription & callback.
     */
    cartData.subscribe(function (cart) {
        cartReminder.init();
        cartReminder.remove(!cart.items.length);
    });

    cartReminder = {
        /**
         * Cart reminder initialization
         */
        init: function () {
            if (options.testMode && !$('body').hasClass(options.testClass)) {
                return;
            }

            if (cartData()?.items?.length) {
                cartReminder.createWrapper();
                showNotificationIfIdle();
                cartReminder.set(Date.now());
            }
        },

        /**
         * Creates wrapper for notification.
         */
        createWrapper: function () {
            let wrapper = options.notification.notificationsWrapper;

            if (!$(wrapper).length) {
                $('body').append(
                    '<div id="' + wrapper.replace('#', '') + '"/>'
                );
            }
        },

        /**
         * Get localStorage value
         * @returns {Number}
         */
        get: function () {
            return $.localStorage.get('tt-last-active');
        },

        /**
         * Set localStroage value
         * @param {Number} timestamp
         */
        set: function (timestamp) {
            $.localStorage.set('tt-last-active', Date.now(timestamp));
        },

        /**
         * Remove localStorage value
         * @param {Boolean} condition
         */
        remove: function (condition) {
            if (condition) {
                $.localStorage.remove('tt-last-active');
            }
        },

        /**
         * @param {Object} settings
         * @constructor
         */
        'Totaltools_Checkout/js/cart-reminder': function (settings) {
            $.extend(true, options, settings);
            cartReminder.init();
        },
    };

    return cartReminder;
});
