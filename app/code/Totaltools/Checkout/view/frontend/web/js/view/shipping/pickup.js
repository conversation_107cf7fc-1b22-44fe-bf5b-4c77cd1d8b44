/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'ko',
    'uiComponent',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/model/totals'
], function ($, ko, Component, quote, totals) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/shipping/pickup',
            listens: {
                '${ $.name }.pickup-options.third_party_pickup:value': 'toggleHelpText'
            }
        },
        canShowHelpText: ko.observable(false),
        isVisible: ko.observable(window.checkoutConfig.thirdPartyPickup),

        initObservable: function () {
            this._super();

            this.canShowPickupOptions = ko.computed(function () {
                // Retrieve the currently selected shipping method
                var method = quote.shippingMethod();

                // Combine the carrier_code + method code for the selected method code name
                var selectedMethodName =
                    method != null
                        ? method.carrier_code + '_' + method.method_code
                        : null;

                return selectedMethodName == window.checkoutConfig.cc_method_name;
            }, this);

            const isKnifeCompliant = this.getHasKnifeCompliantItems(quote.getItems());
            this.isVisible(!isKnifeCompliant);

            totals.getItems().subscribe(function(items) {
                const isKnifeCompliant = this.getHasKnifeCompliantItems(items);
                this.isVisible(!isKnifeCompliant);
            }, this);

            return this;
        },

        /**
         *  Toggles help text visibility for pickup options
         * @param {Boolean} val
         */
        toggleHelpText: function(val) {
            this.canShowHelpText(val);
        },

        /**
         * Checks if the quote has any knife compliant items
         * @param {Array} items
         * @returns {Boolean}
         */
        getHasKnifeCompliantItems: function(items) {
            return items.some((item) => item?.product?.knife_compliance == '1' || item?.extension_attributes?.knife_compliance);
        }
    });
});
