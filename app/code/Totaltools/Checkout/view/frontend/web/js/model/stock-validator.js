/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'mage/translate',
    'Magento_Checkout/js/model/error-processor',
    'Totaltools_Checkout/js/model/cart/stock-notification-service',
    'Totaltools_Checkout/js/model/cart/stock-notifications',
], function (
    $,
    $t,
    errorProcessor,
    stockService,
    stockNotifications
) {
    'use strict';

    return {
        /**
         * Validates stock availability for quote items.
         */
        validate: function () {
            var notifications = stockNotifications.getNotifications()();

            return this.validateStock(notifications || {});
        },

        /**
         * Validate stock for each items post ajax call.
         *
         * @param {Object} msgs
         * @returns {Boolean}
         */
        validateStock: function (msgs) {
            let isValid = true,
                error;

            if (!Object.keys(msgs).length) return isValid;

            for (const msg in msgs) {
                if (
                    msgs[msg].hasOwnProperty('code') &&
                    msgs[msg]['code'] == 5
                ) {
                    isValid = false;
                    error = msgs[msg].cart_message || '';
                }
            }

            error && this.handleError(error);

            return isValid;
        },

        /**
         * Passes error to error processor to be shown on page top.
         *
         * @param {String} msg
         */
        handleError: function (msg) {
            msg.length &&
                errorProcessor.process({
                    responseText: JSON.stringify({ message: $t(msg) }),
                });
        },
    };
});
