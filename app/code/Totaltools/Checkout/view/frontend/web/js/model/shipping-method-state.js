define(
    [
        'jquery',
        'mage/url',
        'ko'
    ],
    function (
        $,
        url,
        ko
    ) {

        'use strict';

        var dirty = ko.observable(false);

        return {

            dirty: dirty,

            getDirty: function () {

                return this.dirty;
            },

            setDirty: function (data) {

                return this.dirty(data);
            },

        };
    }
);
