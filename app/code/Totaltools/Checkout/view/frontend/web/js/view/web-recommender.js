/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'ko',
    'jquery',
    'emarsysComponent',
    'Magento_Checkout/js/model/quote',
    'Amasty_CheckoutCore/js/action/recollect-shipping-rates',
    'Amasty_Conditions/js/action/recollect-totals',
    'matchMedia',
], function (
    ko,
    $,
    Component,
    quote,
    recollectShippingRates,
    recollectTotals,
    mediaCheck
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/view/web-recommender',
            slickConfig: {
                slidesToShow: 2,
                responsive: [
                    {
                        breakpoint: 321,
                        settings: {
                            slidesToShow: 1,
                        },
                    },
                ],
            },
            isVisible: !quote.isVirtual(),
        },

        /**
         * @inheritdoc
         */
        renderCallback: function(elem) {
            if (!this.isVisible) {
                this.isLoading(false);
                return;
            }

            this._super();
        },

        /**
         * @inheritdoc
         */
        handleAddToCart: function (form) {
            let ajaxRequest = this._super();

            ajaxRequest.success(function (res) {
                recollectShippingRates();
                recollectTotals(true);
            });

            return ajaxRequest;
        },

        /**
         * @inheritdoc
         */
        listRenderCallback: function(productList) {
            this._super();

            var $selector = $('#web-recommender-' + this.widgetId);
            mediaCheck({
                media: '(min-width: 1023px)',
                entry: function() {
                    $selector.appendTo('.amcheckout-summary');
                },
                exit: function() {
                    $selector.appendTo('#co-payment-form > .fieldset');
                },
            });
        }
    });
});
