/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'mage/utils/wrapper',
    'uiRegistry',
    'Magento_Customer/js/model/customer',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Checkout/js/model/continue-as-guest',
], function (wrapper, registry, customer, quote, continueAsGuest) {
    'use strict';

    return function (customerEmailValidator) {
        customerEmailValidator.validate = wrapper.wrapSuper(
            customerEmailValidator.validate,
            function () {
                var result = this._super();

                if (quote.isVirtual() || customer.isLoggedIn() || !result) {
                    return result;
                }

                let checkoutProvider = registry.get('checkoutProvider'),
                    customerEmail = registry.get('index = customer-email');

                checkoutProvider.trigger('shippingAddress.data.validate');

                if (checkoutProvider.get('params.invalid')) {
                    customerEmail.continueAsGuestSelected(true);
                    continueAsGuest.setValue(true);

                    result = false;
                }

                return result;
            }
        );

        return customerEmailValidator;
    };
});
