/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2020 Totaltools. <https://totaltools.com.au>
 */

define([
    "jquery", 
    "ko", 
    "uiComponent",
    'Magento_Ui/js/modal/modal',
    'Totaltools_Checkout/js/model/selected-store',
    'Totaltools_Geo/js/model/geolocation',
    'Totaltools_Checkout/js/model/use-current-location',
    'Totaltools_Storelocator/js/checkout/stockcheck',
    'Totaltools_Postcodes/js/fetch-suburbs'
], function (
    $, 
    ko, 
    Component,
    modal,
    selectedStore,
    geolocation,
    useCurrentLocation,
    stockcheck,
    fetchSuburbs
    ) {
    "use strict";

    return Component.extend({
        defaults: {
            template: "Totaltools_Checkout/store-locator/store-selector"
        },

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();
        },
 

       
        selectedStore: selectedStore.getStore(),

        showUseMyCurrentLocation: useCurrentLocation.getValue(),

        country: 'AU',

        suburbs: ko.observableArray(),

        stores: ko.observableArray(),

        regex: new RegExp("^[a-zA-Z0-9\\-\\s]+$"),

        _selectors: {
            _popup: '#store-select-modal',
            _postcode: '#store-select-postcode-input'
        },

        

        showFormPopUp: function() {

            var options = {
                type: 'popup',
                modalClass: 'fulfillment-popup store-select-popup',
                focus: 'input[name="store-select-postcode"]',
                responsive: true,
                innerScroll: true,
                title: '',
                buttons: []
            };

            var popup = modal(options, $(this._selectors._popup));

            $(this._selectors._popup).modal('openModal');
        },

        useCurrentLocation: function() {
            //geolocation.getLocation(true);
            geolocation.setBrowserLocation();
            $(this._selectors._popup).modal('closeModal');
        },

        hideFormPopUp: function() {
            $(this._selectors._popup).modal('closeModal');
            this.resetDefaults(true);
        },

        resetDefaults: function(resetInput) {
            this.suburbs.removeAll();
            this.stores.removeAll();

            if (resetInput)
                $(this._selectors._postcode).val("");
        },

        fetchStores: function(suburb, postcode, event) {
            var _self = this;
            this.suburbs.removeAll();
            stockcheck.fetchStores(_self.stores, postcode, true);
        },

        selectStore: function(store, event) {
            var _self = this;
            $.when(stockcheck.changeStore(store)).done(function (data) {
                selectedStore.setStore(store);
                _self.hideFormPopUp();
            });
        },

        fetchSuburbs: function(data, event) {
            var _self = this;
            var $postcode = $(this._selectors._postcode);
            var query = $postcode.val();

            _self.resetDefaults();

            if (this.regex.test(query) && event.keyCode !== 17) {
                $postcode
                    .addClass("avs-active")
                    .parent()
                    .addClass("loading");
                fetchSuburbs.fetchSuburbsByPostcode(query, $postcode, _self.suburbs);
            }
        },

        highlightMatch: function(input, pattern) {
            var regex = new RegExp(pattern, "gi");
            return input.replace(regex, '<span class="highlighted">' + pattern + '</span>');
        }
    });
    
});
