define([
    'underscore',
    'mage/utils/wrapper',
    'Magento_Checkout/js/checkout-data',
    'Magento_Checkout/js/action/select-shipping-method'
], function (_, wrapper, checkoutData, selectShippingMethodAction) {
    'use strict';

    return function (checkoutDataResolver) {
        var resolveShippingRates = wrapper.wrap(
            checkoutDataResolver.resolveShippingRates,
            function (originalResolveShippingRates, ratesData) {
                var selectedShippingRate = checkoutData.getSelectedShippingRate(),
                    config = window.checkoutConfig,
                    clickAndCollect = _.find(ratesData, function (rate) {
                        return (
                            rate.carrier_code === config.cc_carrier_code &&
                            rate.method_code === config.cc_method_code
                        );
                    });

                if (!selectedShippingRate && clickAndCollect) {
                    selectShippingMethodAction(clickAndCollect);
                }

                return originalResolveShippingRates(ratesData);
            }
        );

        return _.extend(checkoutDataResolver, {
            resolveShippingRates: resolveShippingRates,
        });
    };
});
