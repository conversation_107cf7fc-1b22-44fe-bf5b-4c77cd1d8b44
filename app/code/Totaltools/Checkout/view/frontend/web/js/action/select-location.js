/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2020 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery', 
    'mage/url', 
    'mage/storage',
], function (
    $,
    urlBuilder,
    storage
) {
    'use strict';

    var serviceUrl = urlBuilder.build('totaltools/checkout/changelocation');

    /**
     * @param {Object} payload
     * @returns {$.Ajax}
     */
    return function (payload) {
        if (typeof payload.postcode == 'undefined') {
            console.warn('Valid payload not provided.', payload);
            return;
        }

        return storage.post(serviceUrl, JSON.stringify(payload));
    };
});
