/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'underscore',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Checkout/js/model/selected-shipping-method',
    'Totaltools_Checkout/js/model/selected-store',
], function (_, quote, shippingMethod, selectedStore) {
    'use strict';

    /**
     * Saves quote with new storelocator_id
     *
     * @param {String} storeId
     * @returns {void}
     */
    var saveStoreIdToQuote = function (storeId) {
        var extensionAttributes = quote.extension_attributes || {},
            updatedAttributes = _.extend(extensionAttributes, {
                storelocator_id: storeId,
            });

        quote.extension_attributes = updatedAttributes;
    };

    /**
     * @returns {void}
     */
    return function () {
        var storeId = '0',
            store = selectedStore.getStore()(),
            isStorePickup = shippingMethod.isStorePickup();

        if (isStorePickup && _.isObject(store)) {
            storeId = store['storelocator_id'] || storeId;
        }

        saveStoreIdToQuote(storeId);
    };
});
