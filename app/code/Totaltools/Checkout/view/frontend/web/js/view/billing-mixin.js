/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
'Amasty_CheckoutCore/js/view/utils',
'Magento_Checkout/js/model/quote'
], function (viewUtils,quote) {
    'use strict';
    return function (Billing) {
        return Billing.extend({
            defaults: {
                template: 'Totaltools_Checkout/view/billing-address',
            },

            /**
             * @returns {String}
             */
            getNameBillingAddress: function() {
                return viewUtils.getBlockTitle('billing_address');
            },
            getActionLabel: function() {
                return quote.isVirtual() ? 'Confirm Your Address' : 'Update';
            }
        });
    };
});
