define([
    'jquery',
    'underscore',
    'uiComponent',
    'ko',
    'mage/url',
    'Magento_Checkout/js/model/quote',
    'Magento_Ui/js/modal/modal',
    'Totaltools_Geo/js/model/geolocation',
    'Totaltools_Checkout/js/model/selected-location',
    'Totaltools_Checkout/js/model/shipping-rate-processor/geo-location',
    'Totaltools_Checkout/js/model/use-current-location',
    'Totaltools_Postcodes/js/fetch-suburbs',
], function (
    $,
    _,
    Component,
    ko,
    url,
    quote,
    modal,
    geolocation,
    selectedLocation,
    geoLocationRates,
    useCurrentLocation,
    fetchSuburbs
) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/change-location',
        },
        modalWindow: null,
        selectors: {
            modal: '#change-location-modal',
            postcodeInput: '#change-location-postcode-input',
        },
        selectedSuburb: selectedLocation.getSuburb(),
        selectedAddress: quote.shippingAddress,
        showUseMyCurrentLocation: useCurrentLocation.getValue(),
        suburbs: ko.observableArray(),
        isLoading: ko.observable(false),
        isSearching: ko.observable(false),

        /**
         * Initialization of popup
         *
         * @param {HTMLElement} element
         */
         createModal: function (element) {
            var self = this,
                options = {
                    type: 'popup',
                    modalClass: 'fulfillment-popup change-location-popup',
                    focus: 'input[name="change-location-postcode"]',
                    responsive: true,
                    innerScroll: true,
                    title: '',
                    buttons: [],
                    closed: function (ev) {
                        self.suburbs.removeAll();
                        self.isLoading(false);
                        self.isSearching(false);
                        $(self.selectors.postcodeInput).val('');
                    },
                };
                
            this.modalWindow = element;
            modal(options, $(this.modalWindow));
        },

        openModal: function() {
            if (this.modalWindow) {
                $(this.modalWindow).modal('openModal');
            }
        },

        hideModal: function() {
            if (this.modalWindow) {
                $(this.modalWindow).modal('closeModal');
            }
        },

        useCurrentLocation: function () {
            //geolocation.getLocation(true);
            geolocation.setBrowserLocation();
            this.hideModal();
        },

        /**
         * @param {Object} suburb
         * @param {jQuery.Event} ev
         */
        selectSuburb: function (suburb, ev) {
            var self = this;

            self.isLoading(true);
            self.suburbs.removeAll();

            if (suburb['postcode']) {
                var payload = {
                    postcode: suburb.postcode,
                    city: suburb.city,
                    country_id: suburb.country_id,
                    region: suburb.region,
                };

                selectedLocation.setSuburb(payload);

                $.ajax({
                    url: url.build('totaltools/checkout/changelocation'),
                    type: 'POST',
                    dataType: 'json',
                    data: payload,
                })
                    .done(function () {
                        geoLocationRates.getRates();
                    })
                    .always(function (res) {
                        self.isLoading(false);
                    });
            }

            self.hideModal();
        },

        /**
         * @param {UiClass} obj
         * @param {jQuery.Event} ev
         */
        fetchSuburbs: function (obj, ev) {
            var self = this,
                validation = new RegExp('^[a-zA-Z0-9\\-\\s]+$'),
                $postcode = $(ev.target),
                query = $postcode.val();

            self.suburbs.removeAll();

            if (validation.test(query) && ev.keyCode != 17) {
                self.isSearching(true);

                fetchSuburbs.fetchSuburbsByPostcode(
                    query,
                    $postcode,
                    self.suburbs
                );

                self.isSearching(false);
            }
        },
    });
});
