/**
 * @package Totaltools_Checkout
 * <AUTHOR> Dev Team
 * @copyright Copyright © 2019 Totaltools Pty Ltd. <https://www.totaltools.com.au>
 */

define([], function() {
    return [
        /(?:G?)?(?:P(?:ost(?:al)?)?[\.\-\s]*(?:(?:O(?:ffice)?[\.\-\s]*)?B(?:ox|in|\b|\d)|o(?:ffice|\b)(?:[-\s]*\d)|code)|box[-\s\b]*\d)/i,
        /\bP(ost|ostal)?([ \.]*O(ffice)?)?([ \.]*Box)?\b/i,
        /(?:^|\W)parcel (locker|collect)(?:$|\W)/i,
        /(?:^|\W)locked (bag|bags)(?:$|\W)/i,
        /(?:^|\W)private (bag|bags|locker|lockers)(?:$|\W)/i
    ];
});