/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'uiComponent',
    'ko',
    'mage/translate',
    'Magento_Customer/js/customer-data',
], function (Component, ko, $t, customerData) {
    'use strict';

    var cartData = customerData.get('cart');

    return Component.extend({
        freeShippingLevel: Number(window.checkout.freeShippingLimit),
        canBeFreeDelivery: ko.observable(true),
        percentComplete: ko.observable(0),
        freeShipping: ko.observable(false),
        cartData: cartData,

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();

            this.cartData.subscribe(this.calcProgress.bind(this));

            if ('object' === typeof this.cartData()) {
                this.calcProgress(this.cartData());
            }

            return this;
        },

        /**
         * Used for calculating class observable values, also as cart subscription callback.
         *
         * @param {Object} cart
         */
        calcProgress: function (cart) {
            let subtotal = cart.subtotalAmount || 0;
            let targetPercentage = (subtotal * 100) / this.freeShippingLevel;

            this.canBeFreeDelivery(cart.canBeFreeDelivery);
            this.percentComplete(targetPercentage);
            this.freeShipping(subtotal >= this.freeShippingLevel);
        },
    });
});
