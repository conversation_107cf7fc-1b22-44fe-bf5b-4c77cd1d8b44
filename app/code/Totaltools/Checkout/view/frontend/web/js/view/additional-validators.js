/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'uiComponent',
    'Magento_Checkout/js/model/payment/additional-validators',
    '../model/store-validator',
    '../model/stock-validator',
    '../model/custom-attributes-validator',
    '../model/region-validator'
], function (
    Component,
    additionalValidators,
    storeValidator,
    stockValidator,
    customAttributesValidator,
    regionValidator
) {
    'use strict';

    additionalValidators.registerValidator(storeValidator);
    additionalValidators.registerValidator(stockValidator);
    additionalValidators.registerValidator(customAttributesValidator);
    additionalValidators.registerValidator(regionValidator);

    return Component.extend({});
});
