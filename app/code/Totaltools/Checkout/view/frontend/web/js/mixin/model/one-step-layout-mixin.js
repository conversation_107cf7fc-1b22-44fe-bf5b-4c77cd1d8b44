/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'ko',
    'mage/utils/wrapper',
    'Amasty_CheckoutCore/js/view/utils'
], function (ko, wrapper, viewUtils) {
    'use strict';

    const MAPPING_BLOCK_NAME = {
        email_address: 'checkout.steps.shipping-step.shippingAddress.customer-email',
        street_address: 'checkout.steps.shipping-step.shippingAddress',
        billing_address: 'index=billing-address-form',
    };

    return function (oneStepLayout) {
        oneStepLayout.getCheckoutBlock = wrapper.wrapSuper(
            oneStepLayout.getCheckoutBlock,
            function (blockName) {
                var requestComponent = this._super(blockName);

                if (typeof requestComponent() !== 'object') {
                    requestComponent =
                        this.checkoutBlocks[blockName] ||
                        this.requestComponent(MAPPING_BLOCK_NAME[blockName]);

                    switch (blockName) {
                        case 'street_address':
                            if (requestComponent()) {
                                requestComponent().template =
                                    'Totaltools_Checkout/shipping/address-fieldset';
                            }

                            break;

                        default:
                            break;
                    }
                }

                return requestComponent;
            }
        );

        oneStepLayout.getBlockClassNames = wrapper.wrapSuper(
            oneStepLayout.getBlockClassNames,
            function (blockName) {
                var classNames = this._super(blockName);

                if (blockName) {
                    classNames += ' amcheckout-' + blockName;
                }

                if (window.isCustomerLoggedIn) {
                    classNames += ' logged-in';
                }

                return classNames;
            }
        );

        oneStepLayout.getVirtualLayout = wrapper.wrapSuper(
            oneStepLayout.getVirtualLayout,
            function () {
                return [
                    [viewUtils.getBlockLayoutConfig('summary')],
                    [viewUtils.getBlockLayoutConfig('payment_method')]
                ];
            }
        );

        return oneStepLayout;
    };
});
