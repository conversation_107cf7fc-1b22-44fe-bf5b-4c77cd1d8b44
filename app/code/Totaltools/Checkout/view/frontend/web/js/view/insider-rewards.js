/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'ko',
    'uiComponent',
    'uiRegistry',
    'Magento_Customer/js/model/customer',
], function(ko, Component, registry, customer) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/view/insider-rewards',
            isGuest: !customer.isLoggedIn(),
            isGuestWithAccount: false,
            isCustomerLoggedIn: customer.isLoggedIn(),
            listens: {
                '${ $.provider }:isPasswordVisible': 'onPasswordVisiblity',
            },
        },

        /**
        * @inheritdoc
        */
        initObservable: function () {
            this._super()
                .observe(['isGuest', 'isGuestWithAccount']);

            registry.async(this.provider)(function(cusotmerEmail) {
                this.onPasswordVisiblity(cusotmerEmail.isPasswordVisible());
            }.bind(this));

            return this;
        },

        /**
         *
         * @param {Boolean} visible
         */
        onPasswordVisiblity: function(visible) {
            this.isGuest(!visible);
            this.isGuestWithAccount(visible);
        }
    });
});
