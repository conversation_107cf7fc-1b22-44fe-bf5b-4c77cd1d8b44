/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
/*jshint browser:true jquery:true*/
/*global alert*/
define(
    [
        'jquery',
        'uiComponent',
        'Magento_Checkout/js/model/quote',
        'Magento_Checkout/js/model/step-navigator',
        'Magento_Checkout/js/model/sidebar',
        'Totaltools_Checkout/js/model/click-and-collect-message',
        'Totaltools_Checkout/js/model/delivery-message',
        'Totaltools_Checkout/js/model/selected-store'
    ],
    function($, Component, quote, stepNavigator, sidebarModel, clickAndCollectMessage, deliveryMessage, selectedStore) {
        'use strict';

        return Component.extend({
            defaults: {
                template: 'Totaltools_Checkout/shipping-information'
            },

            store: selectedStore.getStore(),

            deliveryMessage: deliveryMessage.getMessage(),

            clickAndCollectMessage: clickAndCollectMessage.getMessage(),

            showDeliveryMessage: deliveryMessage.showMessage.bind(this),

		    showClickAndCollectMessage: clickAndCollectMessage.showMessage.bind(this),

            isVisible: function() {
                return !quote.isVirtual() && stepNavigator.isProcessed('shipping');
            },

            getShippingMethodCode: function() {
                var shippingMethod = quote.shippingMethod();
                return shippingMethod ? shippingMethod.carrier_code : '';
            },

            getShippingMethodTitle: function() {
                var shippingMethod = quote.shippingMethod();
                if (shippingMethod){
                    return shippingMethod.carrier_code == window.cc_carrier_code ? 'Click & Collect' : 'Delivery Address';
                }
            },

            getDefaultShippingMethodCode: function() {
                return window.cc_carrier_code ? window.cc_carrier_code : '';
            },

            back: function() {
                sidebarModel.hide();
                stepNavigator.navigateTo('shipping');
            },

            backToShippingMethod: function() {
                sidebarModel.hide();
                stepNavigator.navigateTo('shipping', 'opc-shipping_method');
            }
        });
    }
);
