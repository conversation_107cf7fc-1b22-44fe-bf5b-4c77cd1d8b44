/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'Magento_Checkout/js/model/quote',
], function (quote) {
    'use strict';

    return function(Paypal) {
        return Paypal.extend({
            /**
             * @inheritdoc
             */
            initObservable: function() {
                quote.paymentMethod.subscribe(function (value) {
                    if (!value) return;
                    
                    var methodCode = value.method || value;
    
                    if (methodCode === 'braintree_paypal' || methodCode === 'braintree_paypal_vault') {
                        this.reInitPayPal();
                    }
                }, this);

                quote.totals.subscribe(function () {
                    if (this.grandTotalAmount !== quote.totals()['base_grand_total']) {
                        var paymentMethod = quote.paymentMethod();
    
                        if (typeof paymentMethod === 'object' && paymentMethod.method && (paymentMethod.method === 'braintree_paypal' || paymentMethod.method === 'braintree_paypal_vault')) {
                            this.grandTotalAmount = quote.totals()['base_grand_total'];
                            this.reInitPayPal();
                        }
                    }
                }, this);

                this._super();

                return this;
            }
        });
    }
});
