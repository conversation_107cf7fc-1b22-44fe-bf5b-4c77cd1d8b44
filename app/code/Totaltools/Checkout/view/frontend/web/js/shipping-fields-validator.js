/**
 * <AUTHOR> Internet
 * @package    Totaltools_Checkout
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2018, Balance Internet  (http://www.balanceinternet.com.au/)
 */

/*global define*/
define(
    [
        'jquery',
        'mage/translate'
    ],
    function (
        $,
        $t
    ) {
        'use strict';

        let patternListStreet = [
                /\bP(ost|ostal)?([ \.]*O(ffice)?)?([ \.]*Box)?\b/i,
                /(?:^|\W)parcel locker(?:$|\W)/i,
                /(?:^|\W)locked bag(?:$|\W)/i
            ];

        return {
            validateAddressTimeout: 0,
            validateDelay: 2000,

            /**
             *
             * @param input
             * @returns {boolean}
             */
            validateStreetElement: function (input) {
                let result = true;
                patternListStreet.forEach(function (pattern) {
                    if (pattern.test(input) ) {
                        result = false;
                    }
                });
                return result;
            },

            /**
             * validate street for special patterns
             * @param input
             * @returns {boolean}
             */
            validateStreet: function (input) {
                let self = this,
                    streetLineOne = true,
                    streetLineTwo = true;

                if (typeof input[0] !== 'undefined') {
                    streetLineOne = self.validateStreetElement(input[0]);
                }

                if (typeof input[1] !== 'undefined') {
                    streetLineTwo = self.validateStreetElement(input[1]);
                }
                return streetLineOne && streetLineTwo;
            }
        };
    }
);
