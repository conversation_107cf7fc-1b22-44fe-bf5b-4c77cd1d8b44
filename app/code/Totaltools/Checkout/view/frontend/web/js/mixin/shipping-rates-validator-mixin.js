/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define(['mage/utils/wrapper'], function (wrapper) {
    'use strict';

    return function (shippingRatesValidator) {
        shippingRatesValidator.validateAddressData = wrapper.wrapSuper(
            shippingRatesValidator.validateAddressData,
            function (address) {
                if (document.activeElement.name == 'street[0]') {
                    return false;
                }

                return this._super(address);
            }
        );

        return shippingRatesValidator;
    };
});
