/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'ko',
    'uiComponent',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/model/totals',
    'Magento_Checkout/js/model/shipping-service',
], function (ko, Component, quote, totals, shippingService) {
    'use strict';

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/shipping-information/note',
        },
        isVisible: ko.computed(function () {
            var visible = false,
                quoteItems = totals.getItems()(),
                hasDangerousItems = false,
                hasSingleShippingMethod =
                    shippingService.getShippingRates()().length == 1;

            if (quote.isVirtual()) {
                return false;
            }

            hasDangerousItems = quoteItems.some(function (item) {
                return (
                    item['extension_attributes']['shipping_dangerous'] &&
                    item['extension_attributes']['shipping_dangerous'] == true
                );
            });

            if (hasSingleShippingMethod && !hasDangerousItems) {
                visible = true;
            }

            return visible;
        }),
    });
});
