/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define(['ko', 'Totaltools_Checkout/js/model/delivery-message'], function (
    ko,
    deliveryMessage
) {
    'use strict';

    return function (ShippingRates) {
        return ShippingRates.extend({
            shippingDangerous: ko.observable(false),
            shippingDangerousMessage: deliveryMessage.shippingDangerousMessage,

            /**
             * @inheritdoc
             */
            initialize: function () {
                this._super();

                var self = this;

                deliveryMessage
                    .getShippingDangerous()
                    .subscribe(function (val) {
                        if (val == 1) {
                            self.shippingDangerous(true);
                        }
                    });
            },
        });
    };
});
