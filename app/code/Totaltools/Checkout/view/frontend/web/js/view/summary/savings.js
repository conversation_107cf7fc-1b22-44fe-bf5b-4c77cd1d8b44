/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */
define([
    'jquery',
    'ko',
    'uiComponent',
    'priceUtils',
    'Magento_Checkout/js/model/totals'
], function ($, ko, Component, priceUtils, totals) {
    'use strict';

    var savingsConfig = window.checkoutConfig.specialPrices;

    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/summary/savings',
            exports: {
                "savings": "checkout.sidebar.summary.totals.grand-total:savings",
            }
        },

        savings: ko.observable(0),
        savingsFormatted: ko.observable(''),

        /**
         * @inheritdoc
         */
        initialize: function() {
            this._super();

            totals.getItems().subscribe(function(totalItems) {
                this.calculateSavings(totalItems);
            }, this);

            return this;
        },

        /**
         * Calculates savings for available quote items.
         * @param {Array} items
         */
        calculateSavings: function(items) {
            var totalSavings = 0;

            totalSavings = items
                .filter(function (item) {
                    return savingsConfig[item.item_id] && savingsConfig[item.item_id]['showSavingsLabel'] != 'N';
                })
                .reduce(function (acc, item) {
                    return acc + (savingsConfig[item.item_id].savings * item.qty);
                }, 0);

            this.savings(totalSavings);
            this.savingsFormatted(priceUtils.formatPrice(totalSavings));
        },

        /**
         * @returns {Boolean}
         */
        isDisplayed: function() {
            return this.savings() > 0;
        },

        /**
         * @returns {ko.Observable}
         */
        getSavings: function() {
            return this.savingsFormatted;
        }
    });
});
