define([
    'Magento_Ui/js/form/element/single-checkbox',
    'mage/translate'
], function (AbstractField, $t) {
    'use strict';

    return AbstractField.extend({
        defaults: {
            modules: {
                company: '${ $.parentName }.company'
            }
        },
        initialize: function () {
            this._super();

            if (!this.value()) {
                this.company().hide();
            }

            return this;
        },
        toggleCompany: function () {
            if (this.value()) {
                this.company().show();
            } else {
                this.company().hide();
            }
        },
        onCheckedChanged: function () {
            this._super();
            this.toggleCompany();
        }
    });
});