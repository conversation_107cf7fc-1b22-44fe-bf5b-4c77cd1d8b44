define(
    [
        'jquery',
        'ko'
    ],
    function (
        $,
        ko
    ) {

        'use strict';

        var value = ko.observable(false);

        return {

            /**
             * Explicitly subscribe to suburb
             * in dependant components to get any changes
             */
            value: value,

            getValue: function () {

                return this.value;
            },

            setValue: function (data) {

                return this.value(data);
            },

        };
    }
);
