/**
 * Copyright © 2013-2017 Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
/*jshint browser:true jquery:true*/
/*global alert*/
define(
    [
        'jquery',
        'uiComponent',
        'ko',
        'mage/url',
        'Magento_Checkout/js/model/quote',
        'Magento_Checkout/js/action/get-payment-information',
        'Magento_Checkout/js/model/full-screen-loader',
        'Magento_Ui/js/modal/confirm',
        'Magento_Ui/js/modal/alert',
        'Magento_Checkout/js/model/shipping-rate-registry'
    ],
    function ($,
              Component,
              ko,
              url,
              quote,
              getPaymentInformationAction,
              fullScreenLoader,
              confirm,
              uiAlert,
              rateRegistry
    ) {
        "use strict";

        var priceData = window.checkoutConfig.special_prices;

        return Component.extend({
            defaults: {
                template: 'Totaltools_Checkout/cart-step/item/details'
            },

            showNotifications: ko.observable(!quote.isVirtual()),

            priceData: priceData,

            initialize: function () {

                this._super();

                return this;
            },

            getValue: function (quoteItem) {
                return quoteItem.name;
            },

            hasFreeShipping: function(item) {
                return item.extension_attributes && item.extension_attributes.shipping_label && item.extension_attributes.shipping_label.search(/free/i) !== -1;
            },

            hasSpecialPrice: function(item) {
                return this.priceData.hasOwnProperty(item.item_id) && this.priceData[item.item_id]["yousave"] > 0;
            },

            /**
             *
             * @param itemId
             * @param qty
             */
            updateItemQty: function(itemId, qty) {

                if (qty > 0) {
                    fullScreenLoader.startLoader();

                    this._ajax(window.checkout.updateItemQtyUrl, {
                        item_id: itemId,
                        item_qty: qty
                    }, this._updateItemQtyAfter);
                }
            },

            _updateItemQtyAfter: function() {
                var deferred = $.Deferred();

                getPaymentInformationAction(deferred);
                $.when(deferred).done(function() {
                    fullScreenLoader.stopLoader();
                });
            },

            removeItem: function(itemId) {
                var _self = this;
                confirm({
                    title: '',
                    content: $.mage.__('Are you sure you would like to remove this item from the shopping cart?'),
                    actions: {
                        confirm: function() {
                            fullScreenLoader.startLoader();

                            _self._ajax(window.checkout.removeItemUrl, {
                                item_id: itemId
                            }, _self._removeItemAfter);
                        }
                    }
                });
            },

            /**
             *
             * @private
             */
            _removeItemAfter: function() {

                var deferred = $.Deferred();

                getPaymentInformationAction(deferred);
                
                $.when(deferred).done(function() {
                    fullScreenLoader.stopLoader();
                });
            },


            /**
             * @param {String} url - ajax url
             * @param {Object} data - post data for ajax call
             * @param {Object} elem - element that initiated the event
             * @param {Function} callback - callback method to execute after AJAX success
             */
            _ajax: function(url, data, callback) {
                $.extend(data, {
                    'form_key': $.mage.cookies.get('form_key')
                });

                $.ajax({
                        url: url,
                        data: data,
                        type: 'post',
                        dataType: 'json',
                        context: this,
                    })
                    .done(function(response) {

                        if (response.success) {
                            callback.call(this, response);
                            this.refreshShippingMethod();
                        } else {
                            var msg = response.error_message;

                            if (msg) {
                                uiAlert({
                                    content: msg + " If the issue persists, then please try clearing browser cache."
                                });
                            }
                        }
                    })
                    .fail(function(error) {
                        fullScreenLoader.stopLoader();
                    });
            },

            refreshShippingMethod: function() {
                var address = quote.shippingAddress();
                address.trigger_reload = new Date().getTime();
                rateRegistry.set(address.getKey(), null);
                rateRegistry.set(address.getCacheKey(), null);
                quote.shippingAddress(address);
            },
        });
    }
);