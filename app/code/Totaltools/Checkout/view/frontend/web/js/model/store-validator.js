/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'mageUtils',
    'mage/translate',
    'Magento_Checkout/js/model/error-processor',
    'Magento_Checkout/js/model/quote',
    './selected-store',
], function ($, utils, $t, errorProcessor, quote, selectedStore) {
    'use strict';

    return {
        validate: function () {
            var storeValidationResult = true,
                errorMessage = '',
                shippingMethod = quote.shippingMethod(),
                store = selectedStore.store(),
                storeInput = $('[name="shippingAddress.storelocator_id"]');

            storeInput.length && storeInput.removeClass('_error');

            if (
                !utils.isEmpty(shippingMethod) &&
                shippingMethod['carrier_code'] +
                    '_' +
                    shippingMethod['method_code'] ==
                    window.checkoutConfig.cc_method_name
            ) {
                storeValidationResult = !utils.isEmpty(store);

                if (!storeValidationResult) {
                    errorMessage = $t('A store must be selected with Click & Click method. Select a store from Click & Collect Address section.');
                }
            }

            if (storeValidationResult && store?.status === '0' && store?.allow_store_collection === '0') {
                errorMessage = $t('Your selected store does not support Click & Collect. Select another store from Click & Collect Address section.');
                storeValidationResult = false;
            }

            if (!storeValidationResult) {
                let message = JSON.stringify({
                        message: $t(errorMessage),
                    }),
                    error = {
                        responseText: message,
                    };

                storeInput.length && storeInput.addClass('_error');
                errorProcessor.process(error);
            }

            return storeValidationResult;
        },
    };
});
