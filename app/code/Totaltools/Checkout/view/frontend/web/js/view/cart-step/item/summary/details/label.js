/**
 * Copyright © 2018 Totaltools, Inc. All rights reserved.
 * <AUTHOR> Dev
 */
define([
    'uiComponent'
], function (Component) {
    "use strict";
    var labelData = window.checkoutConfig.amasty_labels;
    return Component.extend({
        defaults: {
            template: 'Totaltools_Checkout/summary/item/details/label'
        },
        displayArea: 'before_details',
        labelData: labelData,
        getLabelItem: function(item) {
            if (this.labelData[item.item_id]) {
                return this.labelData[item.item_id];
            }
            return [];
        },
        getLabel: function(item) {
            if (this.labelData[item.item_id]) {
                return this.labelData[item.item_id];
            }
            return null;
        }
    });
});