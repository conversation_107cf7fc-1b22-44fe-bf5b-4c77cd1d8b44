/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'Magento_Checkout/js/model/payment/additional-validators',
], function ($, additionalValidators) {
    'use strict';

    return function (OpenPay) {
        return OpenPay.extend({
            /**
             * @inheritdoc
             */
            prepareForTokenization: function (context, event) {
                var _super = this._super.bind(this);

                if (additionalValidators.validate()) {
                    _super(context, event);
                }
            },
        });
    };
});
