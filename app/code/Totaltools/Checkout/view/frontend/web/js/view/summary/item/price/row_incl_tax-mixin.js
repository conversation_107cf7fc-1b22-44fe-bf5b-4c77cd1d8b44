/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery', 
    'priceUtils', 
    'Magento_Checkout/js/model/quote'
], function (
    $,
    priceUtils,
    quote
) {
    'use strict';

    return function (rowInclTax) {
        return rowInclTax.extend({
            defaults: {},

            /**
             * @param {Number} price
             * @returns {String}
             */
            getFormattedPrice: function (price) {
                return priceUtils.getPriceHtml(price, quote.getPriceFormat());
            },
        });
    };
});
