define([
    'jquery',
    'underscore',
    'ko',
    'Totaltools_Checkout/js/model/selected-location',
    'Totaltools_Checkout/js/model/selected-store',
    'mage/translate',
], function(
    $,
    _,
    ko,
    selectedLocation,
    selectedStore,
    $t
) {
    'use strict';

    return function (address, method) {
        var delivery = selectedLocation.getSuburb()(),
            storePickup = selectedStore.getStore()();

        if ((method === 'shippit' || method === 'freeshipping') && !address.postcode) {
            $.extend(address, {
                postcode: delivery.postcode ? delivery.postcode : '3207',
                city: delivery.city ? delivery.city : 'Port Melbourne',
                region : delivery.region ? delivery.region : 'VIC',
            });
        }

        if (method === 'shippitcc' && !_.isEmpty(storePickup) && !address.postcode) {
            $.extend(address, {
                postcode: storePickup.postcode ? storePickup.postcode : null,
                city: storePickup.city ? storePickup.city : null,
                region : storePickup.region ? storePickup.region : 'VIC',
            });
        }

        return address;
    };
});
