/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'underscore',
    'ko',
    'uiComponent',
    'Magento_Ui/js/modal/modal',
    'Magento_Checkout/js/model/quote',
    'Totaltools_Postcodes/js/fetch-suburbs',
    'Totaltools_Storelocator/js/checkout/stockcheck',
    'Totaltools_Checkout/js/model/selected-store',
    'Totaltools_Checkout/js/model/use-current-location',
    'Totaltools_Geo/js/model/geolocation',
    'Totaltools_Checkout/js/model/click-and-collect-message',
], function (
    $,
    _,
    ko,
    Component,
    modal,
    quote,
    fetchSuburbsAction,
    stockCheckAction,
    selectedStore,
    useCurrentLocation,
    geoLocation,
    clickAndCollectMessage
) {
    'use strict';

    return Component.extend({
        modalWindow: null,

        showUseMyCurrentLocation: useCurrentLocation.getValue(),

        isLoading: ko.observable(false),

        isSearching: ko.observable(false),

        postcode: ko.observable(''),

        suburbs: ko.observableArray(),

        stores: ko.observableArray(),

        selectedStore: selectedStore.getStore(),

        stockMessage: clickAndCollectMessage.getMessage(),

        isActive: ko.observable(false),

        showStockMessage: clickAndCollectMessage.showMessage.bind(this),

        /**
         * @inheritdoc
         */
        initialize: function () {
            this._super();
            this.registerSubscribers();

            return this;
        },

        /**
         * Register subscriptions
         */
        registerSubscribers: function () {
            var self = this;

            quote.shippingMethod.subscribe(function (method) {
                var newMethod = method.carrier_code + '_' + method.method_code;
                self.isActive(newMethod == window.cc_method_name);
            });
        },

        /**
         * Fetch suburbs for given postcode
         */
        fetchSuburbs: function () {
            var self = this;
            var postCode = self.postcode();

            if (postCode.length < 3) {
                return;
            }
            
            self.isSearching(true);
            self.stores.removeAll();

            fetchSuburbsAction
                .fetchByPostcode(postCode, 'AU', false)
                .done(function (res) {
                    let suburbs = self.convertPostcodesDataToSuburbs(
                        res.items || []
                    );
                    self.suburbs(suburbs);
                })
                .fail(function (err) {
                    console.warn(err);
                })
                .always(function (res) {
                    self.isSearching(false);
                });
        },

        /**
         * @param {Object} suburb
         */
        fetchStores: function (suburb) {
            var self = this;

            self.isLoading(true);
            self.suburbs.removeAll();

            stockCheckAction
                .fetchAvailableStores(suburb.postcode, false)
                .done(function (res) {
                    if (res.stores) {
                        _.forEach(res.stores, function (store) {
                            self.stores.push(store);
                        });
                    }
                })
                .fail(function (err) {
                    console.warn(err);
                })
                .always(function (res) {
                    self.isLoading(false);
                });
        },

        /**
         * @param {Object} store
         */
        selectStore: function (store) {
            var self = this;

            self.isLoading(true);

            stockCheckAction
                .changeStore(store)
                .done(function (res) {
                    selectedStore.setStore(store);
                    self.stores.removeAll();
                })
                .fail(function (err) {
                    console.warn(err);
                })
                .always(function (res) {
                    self.isLoading(false);
                    self.hideModal();
                });
        },

        /**
         * Fetch suburbs based on user's current location
         */
        useCurrentLocation: function () {
            //geoLocation.getLocation(true);
            geoLocation.setBrowserLocation();
            this.hideModal();
        },

        /**
         * Takes a postcode response and converts into suburb objects.
         *
         * @param {Array} postcodesData
         * @returns {Array}
         */
        convertPostcodesDataToSuburbs: function (postcodesData) {
            return postcodesData
                .filter(function (po) {
                    return typeof po.locality !== 'undefined';
                })
                .map(function (po) {
                    return {
                        country: 'AU',
                        city: po.locality,
                        region: po.state,
                        postcode: po.postcode,
                    };
                });
        },

        /**
         * Initialization of popup
         *
         * @param {HTMLElement} element
         */
        createModal: function (element) {
            var self = this,
                options = {
                    type: 'popup',
                    modalClass: 'fulfillment-popup change-store-popup',
                    focus: '[name=postcode]',
                    responsive: true,
                    innerScroll: true,
                    trigger: '',
                    buttons: [],
                    closed: function (ev) {
                        self.postcode('');
                        self.isLoading(false);
                        self.isSearching(false);
                        self.suburbs.removeAll();
                        self.stores.removeAll();
                    },
                };

            this.modalWindow = element;
            modal(options, $(this.modalWindow));
        },

        /** Shows the store popup */
        showModal: function () {
            if (this.modalWindow) {
                $(this.modalWindow).modal('openModal');
            }
        },

        /** Hides the store popup */
        hideModal: function () {
            if (this.modalWindow) {
                $(this.modalWindow).modal('closeModal');
            }
        },
    });
});
