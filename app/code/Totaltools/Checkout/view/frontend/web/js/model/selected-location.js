define([
    'jquery',
    'mage/url',
    'ko',
    'Magento_Customer/js/customer-data',
    'Totaltools_Geo/js/model/geolocation',
], function ($, url, ko, customerData, geoLocation) {
    'use strict';

    var currentLocation =
        customerData.get('fulfilment-data')()['location'] || {};
    var suburb = ko.observable(window.locationConfig || currentLocation);

    geoLocation.location.subscribe(function (location) {
        suburb(location);
    });

    return {
        /**
         * Explicitly subscribe to suburb
         * in dependant components to get any changes
         */
        suburb: suburb,

        getSuburb: function () {
            return this.suburb;
        },

        setSuburb: function (data) {
            return this.suburb(data);
        },
    };
});
