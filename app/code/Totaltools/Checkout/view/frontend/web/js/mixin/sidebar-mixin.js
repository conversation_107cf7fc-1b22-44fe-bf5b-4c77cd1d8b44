/**
 * @description This mixin modifies _removeItemAfter method to redirect from checkout
 *              to cart if all items in the cart have been removed.
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal<<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

define([
    'jquery',
    'Magento_Customer/js/customer-data',
    'mage/url'
], function ($, customerData, url) {
    return function (widget) {
        'use strict';

        $.widget('mage.sidebar', $.mage.sidebar, {
            checkoutUrl: window.checkout.checkoutUrl,

            /**
             * @inheritdoc
             */
            _create: function() {
                var customer = customerData.get('customer'),
                    events = {};

                events['click ' + this.options.button.checkout] = function (ev) {
                    if (!customer().firstname) {
                        location.href = this.checkoutUrl;
                        ev.stopImmediatePropagation();
                    }
                };

                this._on(this.element, events);
                this._super();
            },

            /**
             * @inheritdoc
             */
            _removeItemAfter: function (elem, response) {
                this._super(elem, response);

                if (
                    response &&
                    response.summaryQty < 1 &&
                    window.location.href.indexOf(this.checkoutUrl) === 0
                ) {
                    window.location = this.shoppingCartUrl;
                }
            },
        });

        return $.mage.sidebar;
    };
});
