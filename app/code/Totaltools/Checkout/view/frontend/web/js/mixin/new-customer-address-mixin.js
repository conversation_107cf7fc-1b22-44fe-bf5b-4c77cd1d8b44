/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'underscore',
    'mage/utils/wrapper',
    'Totaltools_Checkout/js/model/selected-location',
], function (_, wrapper, location) {
    'use strict';
    
    const defaultAddress = {
        city: 'Port Melbourne',
        region: {
            region_id: '518',
            region_code: 'VIC',
            region: 'Victoria',
        },
        postcode: '3207',
        country_id: 'AU',
        save_in_address_book: 0,
    };

    return function (newAddressFunction) {
        return wrapper.wrap(
            newAddressFunction,
            function (original, addressData) {
                let suburb = location.suburb(),
                    skipKeys = ['store', 'state'];

                if (!_.isEmpty(suburb) && !addressData['city'] && !addressData['postcode']) {
                    _.each(suburb, function (value, name) {
                        if (suburb[name] && !skipKeys.includes(name)) {
                            addressD<PERSON>[name] = value;
                        }
                    });

                    if (!addressData['region']['region_id']) {
                        addressData['region'] = suburb.state;
                    }
                }

                if (!addressData['city'] && !addressData['postcode']) {
                    addressData = _.extend({}, addressData, defaultAddress);
                }

                return original(addressData);
            }
        );
    };
});
