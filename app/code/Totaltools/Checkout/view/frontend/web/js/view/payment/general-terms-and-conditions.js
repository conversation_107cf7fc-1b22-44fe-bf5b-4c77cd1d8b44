define(
    [
        'jquery',
        'underscore',
        'ko',
        'uiComponent',
        'Magento_Ui/js/modal/modal',
        'mage/translate'
    ],
    function(
        $,
        _,
        ko,
        Component,
        modal,
        $t
    ) {
        'use strict';

        return Component.extend({

            defaults: {
                template: 'Totaltools_Checkout/payment/general-terms-and-conditions'
            },

            initialize: function() {

                this._super();

                return this;
            },

            initModal: function() {

                var options = {
                    'type': 'popup',
                    'modalClass': 'agreements-modal',
                    'responsive': true,
                    'innerScroll': true,
                    'trigger': '.show-modal',
                    'buttons': [
                        {
                            text: $t('Close'),
                            class: 'action secondary action-hide-popup',
                            click: function() {
                                this.closeModal();
                            }
                        }
                    ]
                };
                modal(options, $("#terms-and-conditions-modal"));
            },

            showModal: function () {

                $("#terms-and-conditions-modal").modal('openModal');
            },

        });
    }
);