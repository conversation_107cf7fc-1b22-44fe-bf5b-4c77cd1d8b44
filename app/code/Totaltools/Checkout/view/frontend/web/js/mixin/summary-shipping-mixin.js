define([
    'jquery',
    'ko',
    'Magento_Checkout/js/model/quote',
    'Magento_Catalog/js/price-utils',
], function ($, ko, quote, priceUtils) {
    'user strict';

    var isB2bCustomer = Boolean(window.checkoutConfig.isB2BCustomer);

    return function (Shipping) {
        return Shipping.extend({
            quoteShippingAmount: ko.observable(0),
            quoteShippingAmountFormatted: ko.observable('$0.00'),
            isB2bCustomer: isB2bCustomer,
            isClickandCollect: ko.observable(false),

            /**
             * @inheritdoc
             */
            initialize: function () {
                this._super();

                var self = this;

                quote.shippingMethod.subscribe(function (method) {
                    if (!method) return;

                    let methodName = method
                        ? method.carrier_code + '_' + method.method_code
                        : null;

                    if (method.price_incl_tax > 0) {
                        self.quoteShippingAmount(method.price_incl_tax);
                        self.quoteShippingAmountFormatted(
                            priceUtils.formatPrice(
                                method.price_incl_tax,
                                quote.getPriceFormat()
                            )
                        );
                    } else {
                        self.quoteShippingAmount(0);
                        self.quoteShippingAmountFormatted(
                            priceUtils.formatPrice(0, quote.getPriceFormat())
                        );
                        self.isClickandCollect(
                            methodName &&
                                methodName ===
                                    window.checkoutConfig.cc_method_name
                        );
                    }
                });
            },
        });
    };
});
