define(
    [
        'ko'
    ],
    function (
        ko
    ) {

        'use strict';

        var value = ko.observable(true);

        return {

            /**
             * Explicitly subscribe to message and code
             * in dependant components to get any changes
             */
            value: value,

            getValue: function () {

                return this.value;
            },

            setValue: function (data) {

                return this.value(data);
            },

        };
    }
);
