/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'ko',
    'Totaltools_Checkout/js/model/continue-as-guest',
    'Amasty_CheckoutCore/js/view/utils'
], function (ko, continueAsGuest, viewUtils) {
    'use strict';

    return function(Email) {
        return Email.extend({
            continueAsGuestSelected: ko.observable(false),
            showContinueAsGuestButton: ko.observable(false),

            /**
             * @inheritdoc
             */
            initialize: function() {
                this._super();
                this.template = 'Totaltools_Checkout/form/element/email';
                return this;
            },

            /**
             * @inheritdoc
             */
            initObservable: function() {
                this._super();

                if (this.email()) {
                    this.showContinueAsGuestButton(true);
                }

                this.email.subscribe(function(email) {
                    this.showContinueAsGuestButton(email !== '');
                }, this);

                this.isPasswordVisible.subscribe(function(val) {
                    this.showContinueAsGuestButton(val);
                    continueAsGuest.setValue(!val);
                }, this);

                return this;
            },

            /**
             * @returns {Boolean}
             */
            continueAsGuest: function () {
                continueAsGuest.setValue(true);
                this.continueAsGuestSelected(true);

                return true;
            },

            /**
             * @returns {String}
             */
            getNameEmailAddress: function() {
                return viewUtils.getBlockTitle('email_address');
            },
        });
    }
});
