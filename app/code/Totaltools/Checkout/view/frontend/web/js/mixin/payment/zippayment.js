/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

define([
    'underscore',
    'uiRegistry',
    'Magento_Customer/js/model/customer',
    'Magento_Customer/js/model/address-list',
    'Magento_Checkout/js/model/quote',
    'Magento_Checkout/js/action/set-shipping-information',
], function (_, registry, customer, addressList, quote, setShippingInformationAction) {
    'use strict';

    return function (Zippayment) {
        return Zippayment.extend({
            /**
             * @inheritdoc
             */
            continueToZipMoney: function () {
                let provider = registry.get('checkoutProvider'),
                    paramsInvalid = provider.get('params.invalid');

                if (customer.isLoggedIn() && addressList().length) {
                    paramsInvalid = _.isEmpty(quote.shippingAddress().getKey());
                }

                if (!quote.isVirtual() && !_.isEmpty(quote.shippingMethod()) && paramsInvalid === false) {
                    setShippingInformationAction();
                }
                
                this._super();
            },
        });
    };
});
