define([
    'Magento_Checkout/js/model/resource-url-manager',
    'Magento_Checkout/js/model/quote',
    'mage/storage',
    'Magento_Checkout/js/model/shipping-service',
    'Magento_Checkout/js/model/shipping-rate-registry',
    'Magento_Checkout/js/model/error-processor',
    'Totaltools_Checkout/js/model/selected-location',
], function (
    resourceUrlManager,
    quote,
    storage,
    shippingService,
    rateRegistry,
    errorProcessor,
    selectedLocation
) {
    'use strict';

    return {
        /**
         * Get rates based on Geo Location of customer.
         */
        getRates: function () {
            var serviceUrl = resourceUrlManager.getUrlForEstimationShippingMethodsForNewAddress(quote),
                geoLocation = selectedLocation.suburb(),
                defaultAddress = {
                    city: 'Port Melbourne',
                    region: 'VIC',
                    postcode: '3207',
                    country_id: 'AU',
                    save_in_address_book: 0,
                };

            /**
             * Default to 3207 to make sure if no location is detected then delivery address is still available initially.
             */
            var payload = JSON.stringify({
                address: {
                    city: geoLocation.city || defaultAddress.city,
                    region: geoLocation.region || defaultAddress.region,
                    postcode: geoLocation.postcode || defaultAddress.postcode,
                    country_id: geoLocation.country_id || defaultAddress.country_id,
                },
            });

            if (geoLocation && geoLocation.country_id !== 'AU') {
                payload = JSON.stringify({
                    address: defaultAddress,
                });
            }

            storage
                .post(serviceUrl, payload, false)
                .done(function (result) {
                    if (result.length) {
                        shippingService.setShippingRates(result);
                    }
                })
                .fail(function (response) {
                    shippingService.setShippingRates([]);
                    errorProcessor.process(response);
                });
        },
    };
});
