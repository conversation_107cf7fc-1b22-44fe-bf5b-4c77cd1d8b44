define([
	"ko",
	"jquery",
	"uiComponent",
	"underscore",
	"Magento_Checkout/js/model/step-navigator",
	"Magento_Checkout/js/model/quote"
], function(ko, $, Component, _, stepNavigator, quote) {
	"use strict";

	$(document).ready(function() {

		calcSideBarWidth();

		function calcSideBarWidth() {
			window.sidebarWidth = $(".sidebar-wrapper").width();
		}

		function affixSidebar() {
			const AFFIX_CLASS = 'affixed';

			var screen = $(window);
			var screenWidth = screen.width();
			var screenPosition = screen.scrollTop();

			var paymentStep = $('.opc-progress-bar .opc-progress-bar-item:last-child');
			var sidebar = $(".sidebar-container");

			if (
				quote.isVirtual() 
				|| screenWidth < 1024 
				|| paymentStep.hasClass('_active')) 
			{
				sidebar.removeClass(AFFIX_CLASS).removeAttr('style');
				return;
			}			

			if (!window.sidebarWidth) {
				calcSideBarWidth();
			}
			
			var sidebarHeight = sidebar.outerHeight();

			var opc = $('.opc-wrapper');
			var opcHeight = opc.outerHeight() - 30;
			var opcOffset = opc.offset().top;

			var maxY = (opcHeight + opcOffset) - sidebarHeight;
			var maxTop = opcHeight - sidebarHeight;

			if (screenPosition > opcOffset) {
				if (screenPosition < maxY) {
					sidebar.addClass(AFFIX_CLASS).removeAttr('style').width(sidebarWidth);
				} else {
					sidebar.removeClass(AFFIX_CLASS).css({
						position : 'absolute',
						top: maxTop + 'px',
						right: 0,
						maxWidth: sidebarWidth + 'px'
					});
				}
			} else {
				sidebar.removeClass(AFFIX_CLASS).removeAttr('style');
			}
		}

		$(window)
			.on("scroll", affixSidebar)
			.on("resize", calcSideBarWidth);
	});

	return Component.extend({
		defaults: {
			template: "Totaltools_Checkout/cart-step"
		},

		isVisible: ko.observable(true), //!quote.isVirtual()

		initialize: function() {
			this._super();

			stepNavigator.registerStep(
				"cart",
				null,
				"Cart",
				this.isVisible,
				_.bind(this.navigate, this),
				1
			);

			return this;
		},

		navigate: function() {
			//this.isVisible(true);
		},

		navigateToNextStep: function() {
			stepNavigator.next();
		}
	});
});
