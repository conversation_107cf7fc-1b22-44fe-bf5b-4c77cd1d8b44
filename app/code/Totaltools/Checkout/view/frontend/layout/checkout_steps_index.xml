<?xml version="1.0"?>
<!--
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" layout="checkout" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <update handle="checkout_index_index"/>
    <body>
        <referenceBlock name="checkout.root">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="checkout" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="steps" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="shipping-step" xsi:type="array">
                                            <item name="children" xsi:type="array">
                                                <item name="shippingAddress" xsi:type="array">
                                                    <item name="sortOrder" xsi:type="string">10</item>
                                                    <item name="children" xsi:type="array">
                                                        <item name="shipping-address-fieldset" xsi:type="array">
                                                            <item name="children" xsi:type="array">
                                                                <item name="telephone" xsi:type="array">
                                                                    <item name="validation" xsi:type="array">
                                                                        <item name="required-entry" xsi:type="string">true</item>
                                                                        <item name="validate-phone-custom" xsi:type="string">true</item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>

                                                        <!-- Store pickup details -->
                                                        <item name="store-pickup-details" xsi:type="array">
                                                            <item name="sortOrder" xsi:type="string">4</item>
                                                            <item name="component"  xsi:type="string">Totaltools_Checkout/js/view/store-pickup-address</item>
                                                            <item name="displayArea" xsi:type="string">after-form</item>
                                                        </item>

                                                        <!-- Fulfilment Boxes -->
                                                        <item name="fulfilment-boxes" xsi:type="array">
                                                            <item name="component" xsi:type="string">Totaltools_Checkout/js/view/fulfilment-boxes</item>
                                                            <item name="displayArea" xsi:type="string">shipping_methods_after</item>
                                                            <item name="provider" xsi:type="string">checkoutProvider</item>
                                                            <item name="config" xsi:type="array">
                                                                <item name="0" xsi:type="string">checkout.steps</item>
                                                            </item>
                                                            <item name="sortOrder" xsi:type="string">5</item>
                                                            <item name="children" xsi:type="array">
                                                                <item name="change-store" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/change-store</item>
                                                                    <item name="displayArea" xsi:type="string">change-store</item>
                                                                </item>
                                                                <item name="change-location" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/change-location</item>
                                                                    <item name="displayArea" xsi:type="string">change-location</item>
                                                                </item>
                                                            </item>
                                                        </item>

                                                        <item name="shippingAdditional" xsi:type="array">
                                                            <item name="component" xsi:type="string">uiComponent</item>
                                                            <item name="displayArea" xsi:type="string">shippingAdditional</item>
                                                            <item name="children" xsi:type="array">
                                                                <item name="pickup" xsi:type="array">
                                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/shipping/pickup</item>
                                                                    <item name="children" xsi:type="array">
                                                                        <item name="pickup-options" xsi:type="array">
                                                                            <item name="component" xsi:type="string">uiComponent</item>
                                                                            <item name="displayArea" xsi:type="string">pickup-options</item>
                                                                            <item name="children" xsi:type="array" />
                                                                        </item>
                                                                    </item>
                                                                </item>
                                                            </item>
                                                        </item>
                                                    </item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                                <!-- Remove Cart from Order Summary (Sidebar) -->
                                <item name="sidebar" xsi:type="array">
                                    <item name="children" xsi:type="array">
                                        <item name="summary" xsi:type="array"></item>
                                        <item name="shipping-information" xsi:type="array">
                                            <item name="component" xsi:type="string">Totaltools_Checkout/js/view/shipping-information</item>
                                            <item name="config" xsi:type="array">
                                                <item name="deps" xsi:type="string">checkout.steps.shipping-step.shippingAddress</item>
                                            </item>
                                            <item name="displayArea" xsi:type="string">shipping-information</item>
                                            <item name="children" xsi:type="array">
                                                <item name="ship-to" xsi:type="array">
                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/shipping-information/list</item>
                                                    <item name="displayArea" xsi:type="string">ship-to</item>
                                                </item>
                                            </item>
                                        </item>
                                        <item name="billing-information" xsi:type="array">
                                            <item name="component" xsi:type="string">Totaltools_Checkout/js/view/billing-information</item>
                                            <item name="config" xsi:type="array">
                                                <item name="deps" xsi:type="string">checkout.steps.shipping-step.shippingAddress</item>
                                            </item>
                                            <item name="displayArea" xsi:type="string">billing-information</item>
                                            <item name="children" xsi:type="array">
                                                <item name="ship-to" xsi:type="array">
                                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/billing-information/list</item>
                                                    <item name="displayArea" xsi:type="string">ship-to</item>
                                                </item>
                                            </item>
                                        </item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>

        <referenceContainer name="header.container">
            <referenceBlock name="store.locator" remove="true" />
        </referenceContainer>
    </body>
</page>
