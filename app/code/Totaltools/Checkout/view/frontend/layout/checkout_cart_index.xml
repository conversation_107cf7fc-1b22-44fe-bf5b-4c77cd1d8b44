<?xml version="1.0"?>
<!--
/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceBlock name="checkout.cart.form">
            <block class="Magento\Checkout\Block\Cart" name="checkout.cart.free.shipping"
                template="Totaltools_Checkout::cart/free_shipping.phtml" before="-" />
        </referenceBlock>

        <referenceBlock name="checkout.cart.totals">
            <arguments>
                <argument name="jsLayout" xsi:type="array">
                    <item name="components" xsi:type="array">
                        <item name="block-totals" xsi:type="array">
                            <item name="children" xsi:type="array">
                                <item name="block-free-shipping" xsi:type="array">
                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/cart/free-shipping</item>
                                    <item name="sortOrder" xsi:type="string">1000</item>
                                    <item name="config" xsi:type="array">
                                        <item name="template" xsi:type="string">Totaltools_Checkout/cart/summary/free-shipping</item>
                                    </item>
                                </item>
                            </item>
                        </item>
                    </item>
                </argument>
            </arguments>
        </referenceBlock>

        <referenceBlock name="cart.reminder" remove="true" />
    </body>
</page>