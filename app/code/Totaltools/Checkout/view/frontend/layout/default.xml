<?xml version="1.0"?>
<!--
/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2020 Totaltools. <https://totaltools.com.au>
 */
-->
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="page.top">
            <referenceContainer name="nav.bar.container">
                <block class="Magento\Framework\View\Element\Template" name="nav.bar.store.locator" after="-" template="Totaltools_Checkout::storelocator.phtml">
                    <arguments>
                        <argument name="title" translate="true" xsi:type="string">Store Locator</argument>
                        <argument name="use_force" xsi:type="boolean">true</argument>
                        <argument name="text" xsi:type="string"><![CDATA[<!-- Store Locator -->]]></argument>
                        <argument name="jsLayout" xsi:type="array">
                            <item name="components" xsi:type="array">
                                <item name="storelocator" xsi:type="array">
                                    <item name="component" xsi:type="string">Totaltools_Checkout/js/view/store-locator</item>
                                    <item name="provider" xsi:type="string">checkoutProvider</item>
                                    <item name="config" xsi:type="array">
                                        <item name="template" xsi:type="string">Totaltools_Checkout/store-locator</item>
                                    </item>
                                </item>
                            </item>
                        </argument>
                    </arguments>
                </block>
            </referenceContainer>
        </referenceContainer>
        <referenceContainer name="content">
            <block class="Magento\Framework\View\Element\Template" name="cart.reminder" template="Totaltools_Checkout::cart_reminder.phtml"/>
        </referenceContainer>
        <referenceContainer name="footer">
            <block class="Totaltools\Checkout\Block\NearestStore" name="nav.bar.nearest.store" template="Totaltools_Checkout::nearest-store.phtml" after="-"/>
        </referenceContainer>
    </body>
</page>