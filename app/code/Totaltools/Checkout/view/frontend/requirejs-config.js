var config = {
    config: {
        mixins: {
            "Magento_Checkout/js/view/shipping": {
                "Totaltools_Checkout/js/view/shipping-mixin": true,
            },
            "Magento_Checkout/js/view/payment": {
                "Totaltools_Checkout/js/mixin/payment-mixin": true,
            },
            "Magento_Tax/js/view/checkout/summary/shipping": {
                "Totaltools_Checkout/js/mixin/summary-shipping-mixin": true,
            },
            "Magento_Checkout/js/model/checkout-data-resolver": {
                "Totaltools_Checkout/js/mixin/data-resolver-shipping-rates": true,
            },
            "Magento_Ui/js/lib/validation/validator": {
                "Totaltools_Checkout/js/mixin/validator-mixin": true,
            },
            "Magento_Checkout/js/model/shipping-save-processor/payload-extender":
                {
                    "Totaltools_Checkout/js/model/shipping-save-processor/payload-extender": true,
                },
            "Magento_Checkout/js/sidebar": {
                "Totaltools_Checkout/js/mixin/sidebar-mixin": true,
            },
            "Magento_Checkout/js/view/cart/shipping-rates": {
                "Totaltools_Checkout/js/mixin/shipping-rates-mixin": true,
            },
            "Amasty_CheckoutCore/js/view/checkout/summary/item/details": {
                "Totaltools_Checkout/js/view/summary/item/details-mixin": true,
            },
            "Magento_Weee/js/view/checkout/summary/item/price/row_incl_tax": {
                "Totaltools_Checkout/js/view/summary/item/price/row_incl_tax-mixin": true,
            },
            "Magento_Checkout/js/view/billing-address": {
                "Totaltools_Checkout/js/view/billing-mixin": true,
            },
            "PayPal_Braintree/js/view/payment/method-renderer/paypal": {
                "Totaltools_Checkout/js/mixin/payment/paypal": true,
            },
            "Magento_Checkout/js/model/shipping-rates-validator": {
                "Totaltools_Checkout/js/mixin/shipping-rates-validator-mixin": true,
            },
            "Magento_Ui/js/view/messages": {
                "Totaltools_Checkout/js/mixin/messages-mixin": true,
            },
            "Magento_Checkout/js/model/customer-email-validator": {
                "Totaltools_Checkout/js/mixin/payment/customer-email-validator-mixin": true,
            },
            "Zip_ZipPayment/js/view/payment/method-renderer/zip-zippayment": {
                "Totaltools_Checkout/js/mixin/payment/zippayment": true,
            },
            "Magento_Checkout/js/model/shipping-rate-processor/new-address": {
                "Totaltools_Checkout/js/model/shipping-rate-processor/new-address-mixin": true,
            },
            "Amasty_CheckoutCore/js/view/form/element/email": {
                "Totaltools_Checkout/js/view/form/element/email-mixin": true,
            },
            "Amasty_CheckoutCore/js/model/one-step-layout": {
                "Totaltools_Checkout/js/mixin/model/one-step-layout-mixin": true,
            },
            "Magento_Reward/js/view/payment/reward": {
                "Totaltools_Checkout/js/view/payment/reward-mixin": true,
            },
            "Magento_Payment/js/view/payment/method-renderer/free-method": {
                "Totaltools_Checkout/js/view/payment/method-renderer/free-method-mixin": true
            },
            "Magento_Ui/js/form/element/select": {
                "Totaltools_Checkout/js/form/element/select-country-mixin": true
            }
        },
    },
    map: {
        "Amasty_CheckoutCore/js/form/element/region":
            "Totaltools_Checkout/js/view/form/element/region",
        headingComponent: "Totaltools_Checkout/js/view/form/element/heading",
    },
};
