<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> I<PERSON>bal <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

/** @var \Totaltools\Checkout\Block\Cart\Fulfilment $block */

$checkoutSettings = $block->getCheckoutSettings();
?>

<div id="fulfilment" data-bind="scope: 'fulfilmentboxes'" class="cart-container">
    <!-- ko template: getTemplate() --><!-- /ko -->
    <script type="text/x-magento-init">
        {
            "#fulfilment": {
                "Magento_Ui/js/core/app": <?= /* @noEscape */ $block->getJsLayout() ?>
            }
        }
    </script>
</div>

<script>
    window.locationConfig = <?= /* @noEscape */ $block->getGeoLocation(); ?>;
    window.cartHasOPOXLines = "<?= /* @noEscape */ $block->cartHasOPOXLines(); ?>";
    window.cc_carrier_code = "<?= /* @noEscape */ $checkoutSettings->getTotalToolsConfig('cc_carrier_code'); ?>";
    window.cc_method_code = "<?= /* @noEscape */ $checkoutSettings->getTotalToolsConfig('cc_method_code'); ?>";
    window.cc_method_name = window.cc_carrier_code + "_" + window.cc_method_code;
    window.error_message_search_store = "<?= /* @noEscape */ __('No matches found for this suburb, postcode.'); ?>";
</script>

<script>
    require(['jquery', 'sticky'], function($, sticky) {
        $('.cart-right-container').sticky();
    });
</script>