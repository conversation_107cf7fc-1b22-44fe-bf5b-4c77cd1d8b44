<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

/** @var \Totaltools\Checkout\Helper\CartReminder $helper */
$helper = $this->helper(\Totaltools\Checkout\Helper\CartReminder::class);

if ($helper->isEnabled()) {
?>
<script type="text/x-magento-init">
    {
        "*": {
            "Totaltools_Checkout/js/cart-reminder": {
                "idleTimeout": <?= /** @noEscape */ $helper->getConfig('idle_timeout') * 60 * 60 * 1000; ?>,
                "testMode": <?= /** @noEscape */ $helper->getConfig('operation') == 2 ? 'true' : 'false'; ?>,
                "testClass": "<?= /** @noEscape */ $helper->getConfig('test_class'); ?>",
                "notification": <?= /** @noEscape */ $helper->getNotificationConfig(); ?>
            }
        }
    }
</script>
<script type="text/x-magento-template" id="cart-reminder-template">
    <div class="notification cart-reminder">
        <% if (opts.hasCloseBtn) { %><span class="close"><span>x</span></span><% } %>
        <div class="notification-wrapper">
            <h4 class="notification-title"><%- opts.title %><% if (data.name) { %> <%= data.name %><% } %>!</h4>
            <div class="notification-content">
                <div class="notification-message">
                    <%= opts.message %>
                </div>
                <div class="cart-link"><a class="action primary" href="<%- window.checkout.shoppingCartUrl %>"><%- opts.btnText %></a></div>
            </div>
        </div>
    </div>
</script>
<?php } ?>
