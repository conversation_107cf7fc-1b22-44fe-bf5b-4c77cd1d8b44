<?php

namespace Totaltools\Checkout\Helper;

use Magento\Company\Model\CustomerFactory;
use Magento\Customer\Model\Session;
use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Framework\App\Helper\Context;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Stdlib\CookieManagerInterface;
use Magento\GiftCard\Model\Catalog\Product\Type\Giftcard;
use Magento\Store\Model\ScopeInterface;
use Humm\HummPaymentGateway\Gateway\Config\Config as HummConfig;
use Afterpay\Afterpay\Model\Config as AfterpayConfig;
use Magento\Framework\App\ResourceConnection;
use Magento\Company\Model\ResourceModel\Customer;

class Data extends AbstractHelper
{
    const XML_PATH_SETTINGS = 'checkout/total_checkout/';
    const XML_PATH_HUMM_MAX_AMOUNT = 'payment/humm_gateway/humm_conf/max_order_total';
    const XML_PATH_HUMM_MAX_AMOUNT_5X = 'payment/humm_gateway/humm_conf/max_order_total_5x';
    const XML_PATH_PAY_IN_FOUR_MIN_AMOUNT = 'totaltools_paypalpayinfour/payinfour/min_order_total';
    const XML_PATH_PAY_IN_FOUR_MAX_AMOUNT = 'totaltools_paypalpayinfour/payinfour/max_order_total';

    /**
     * @var Session
     */
    protected $_customerSession;

    /**
     * @var Customer
     */
    protected $_customerCompanyFactory;

    /**
     * @var CookieManagerInterface
     */
    private CookieManagerInterface $cookieManager;

    private HummConfig $hummConfig;

    private AfterpayConfig $afterpayConfig;

    private Context $context;

    private CheckoutSession $checkoutSession;

    /**
     * @var ResourceConnection
     */
    private $resourceConnection;
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;

    /**
     * Data constructor.
     * @param Context $context
     * @param Session $session
     * @param CustomerFactory $customerFactory
     * @param CookieManagerInterface $cookieManager
     * @param HummConfig $hummConfig
     * @param AfterpayConfig $afterpayConfig
     * @param CheckoutSession $checkoutSession
     */
    public function __construct(
        Context $context,
        Session $session,
        CustomerFactory $customerFactory,
        CookieManagerInterface $cookieManager,
        HummConfig $hummConfig,
        AfterpayConfig $afterpayConfig,
        CheckoutSession $checkoutSession,
        ResourceConnection $resourceConnection,
        Customer  $companyCustomer
    ) {
        parent::__construct($context);
        $this->_customerSession = $session;
        $this->_customerCompanyFactory = $customerFactory;
        $this->cookieManager = $cookieManager;
        $this->hummConfig = $hummConfig;
        $this->afterpayConfig = $afterpayConfig;
        $this->checkoutSession = $checkoutSession;
        $this->context = $context;
        $this->resourceConnection = $resourceConnection;
        $this->companyCustomer = $companyCustomer;
    }

    /**
     * @return bool
     */
    public function isB2BCustomer(): bool
    {
        $customer = $this->_customerSession->isLoggedIn() ? $this->_customerSession->getCustomer() : null;
        if ($customer) {
            $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId($customer->getId());
            $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
            $b2bCustomer =  array_diff($currentCompanyIds, [0]);
            if (!empty($b2bCustomer)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Return store config value for key
     *
     * @param string $key
     * @param string $scope
     * @param string $group
     * @return string
     */
    public function getValue($key, $scope = ScopeInterface::SCOPE_STORES, $group = self::XML_PATH_SETTINGS)
    {
        return $this->scopeConfig->getValue($group . $key, $scope);
    }

    /**
     * @return bool
     */
    public function isThirdPartyPickupActive()
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_SETTINGS . 'third_party_pickup_active');
    }

    /**
     * @return bool
     */
    public function isOnepageEnable()
    {
        return true;
        // return $this->cookieManager->getCookie('is_onepage_enable') === "true";
    }

    /**
     * @return float
     */
    public function getAfterpayMinAmount()
    {
        return (float) $this->afterpayConfig->getMinOrderTotal();
    }

    /**
     * @return float
     */
    public function getAfterpayMaxAmount()
    {
        return (float) $this->afterpayConfig->getMaxOrderTotal();
    }

    /**
     * @return float
     */
    public function getHummMinAmount()
    {
        return (float) $this->hummConfig->getMinTotal();
    }

    /**
     * @param $website
     * @return float
     */
    public function getHummMaxAmount($website = null)
    {
        return (float) $this->scopeConfig->getValue(self::XML_PATH_HUMM_MAX_AMOUNT, ScopeInterface::SCOPE_WEBSITE, $website);
    }

    /**
     * @param $website
     * @return float
     */
    public function getHummMaxAmount5x($website = null)
    {
        return (float) $this->scopeConfig->getValue(self::XML_PATH_HUMM_MAX_AMOUNT_5X, ScopeInterface::SCOPE_WEBSITE, $website);
    }

    /**
     * @param $website
     * @return float
     */
    public function getPaypalPayInFourMinAmount($website = null)
    {
        return (float) $this->scopeConfig->getValue(self::XML_PATH_PAY_IN_FOUR_MIN_AMOUNT, ScopeInterface::SCOPE_WEBSITE, $website);
    }

    /**
     * @param $website
     * @return float
     */
    public function getPaypalPayInFourMaxAmount($website = null)
    {
        return (float) $this->scopeConfig->getValue(self::XML_PATH_PAY_IN_FOUR_MAX_AMOUNT, ScopeInterface::SCOPE_WEBSITE, $website);
    }

    /**
     * @return float|int
     */
    public function getQuoteGrandTotal()
    {
        try {
            if ($quote = $this->checkoutSession->getQuote()) {
                return (float) $quote->getGrandTotal();
            }
        } catch (LocalizedException $e) {
            // skip
        }

        return 0;
    }

    /**
     * @return bool
     */
    public function isGiftCardQuote()
    {
        try {
            if ($quote = $this->checkoutSession->getQuote()) {
                foreach ($quote->getAllItems() as $item) {
                    /** @var \Magento\Catalog\Model\Product $product */
                    if ($item->getProductType() != Giftcard::TYPE_GIFTCARD) {
                        return false;
                    }
                }
            }
        } catch (LocalizedException $e) {
            // skip
        }

        return true;
    }
    
    /**
     * @return bool
     */
    public function isVirtualQuote() {
        try {
            if ($quote = $this->checkoutSession->getQuote()) {
                return $quote->isVirtual();
            }
        } catch (LocalizedException $e) {
            // skip
        }

        return false;
    }

    public function getNearestStore($latitudeFrom, $longitudeFrom)
    {

        $connection = $this->resourceConnection->getConnection();
        $tableName = $this->resourceConnection->getTableName('magestore_storelocator_store');

        $select = $connection->select()
                    ->from($tableName, ['storelocator_id', 'latitude','longitude'])
                    ->where('status = ?', 1)
                    ->where('allow_store_collection = ?', 1)
                    ->where('is_visible = ?', 1);

        $stores = $connection->fetchAll($select);

        $distance = [];
        foreach ($stores as $store) {
            $store_id = $store['storelocator_id'];
            $latitudeTo = $store['latitude'];
            $longitudeTo = $store['longitude'];
            $distance[$store_id] = $this->haversineGreatCircleDistance($latitudeFrom, $longitudeFrom, $latitudeTo, $longitudeTo);
        }

        
        $storeId = array_search(min($distance), $distance);

        if ($storeId) {
            $neareststoreSession['store_id'] = $storeId;
            $this->checkoutSession->setNeareststoreSession($neareststoreSession);
        }
        
        return $storeId;
    }

    /**
     * Calculates the great-circle distance between two points, with
     * the Haversine formula.
     * @param float $latitudeFrom Latitude of start point in [deg decimal]
     * @param float $longitudeFrom Longitude of start point in [deg decimal]
     * @param float $latitudeTo Latitude of target point in [deg decimal]
     * @param float $longitudeTo Longitude of target point in [deg decimal]
     * @param float $earthRadius Mean earth radius in [m]
     * @return float Distance between points in [km] (same as earthRadius)
     */
    public function haversineGreatCircleDistance($latitudeFrom, $longitudeFrom, $latitudeTo, $longitudeTo, $earthRadius = 6371)
    {
        // convert from degrees to radians
        $latFrom = deg2rad($latitudeFrom);
        $lonFrom = deg2rad($longitudeFrom);
        $latTo = deg2rad($latitudeTo);
        $lonTo = deg2rad($longitudeTo);
    
        $latDelta = $latTo - $latFrom;
        $lonDelta = $lonTo - $lonFrom;
    
        $angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) +
        cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));
        return $angle * $earthRadius;
    }
}
