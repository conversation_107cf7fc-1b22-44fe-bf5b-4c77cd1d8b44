<?php

namespace Totaltools\Checkout\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

/**
 * Class TotalToolsConfig
 */
class TotalToolsConfig extends AbstractHelper
{
    /**
     * Prefix path config
     */
    const XML_PATH_TT_CONFIG = 'checkout/total_tools/';

    /**
     * @param $field
     * @param null $storeId
     * @return mixed
     */
    public function getConfigValue($field, $storeId = null)
    {
        return trim($this->scopeConfig->getValue($field, ScopeInterface::SCOPE_STORE, $storeId));
    }

    /**
     * @param $code
     * @param null $storeId
     * @return mixed
     */
    public function getTotalToolsConfig($code, $storeId = null)
    {
        return trim($this->getConfigValue(self::XML_PATH_TT_CONFIG . $code, $storeId));
    }

    /**
     * @param null $storeId
     * @return string
     */
    public function getDefaultShippingMethod($storeId = null)
    {
        $carrierCode = trim($this->getConfigValue(self::XML_PATH_TT_CONFIG . 'cc_carrier_code', $storeId));
        $methodCode = trim($this->getConfigValue(self::XML_PATH_TT_CONFIG . 'cc_method_code', $storeId));
        $defaultShippingMethod = $carrierCode . '_' . $methodCode;

        return $defaultShippingMethod;
    }
}
