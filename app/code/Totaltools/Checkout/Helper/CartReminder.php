<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Checkout\Helper;

use Magento\Store\Model\ScopeInterface;

/**
 * Class CartReminder
 */
class CartReminder extends \Magento\Framework\App\Helper\AbstractHelper
{
    /**
     * @var string
     */
    const XML_PATH_GROUP = 'checkout/cart_reminder/';

    /**
     * @return bool
     */
    public function isEnabled(): bool
    {
        return $this->scopeConfig->isSetFlag(self::XML_PATH_GROUP . 'active', ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * @param string $key
     * @return string
     */
    public function getConfig($key): ?string
    {
        return $this->scopeConfig->getValue(self::XML_PATH_GROUP . $key, ScopeInterface::SCOPE_WEBSITE);
    }

    /**
     * Get notification popup configuration for cart reminder.
     * @return string
     */
    public function getNotificationConfig(): string
    {
        $config = [];

        if ($this->getConfig('title')) {
            $config['title'] = $this->getConfig('title');
        }

        if ($this->getConfig('message')) {
            $config['message'] = $this->getConfig('message');
        }

        if ($this->getConfig('button_text')) {
            $config['btnText'] = $this->getConfig('button_text');
        }

        return json_encode((object) $config);
    }
}
