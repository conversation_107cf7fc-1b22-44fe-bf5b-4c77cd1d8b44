<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2025 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Checkout\Helper;

use Magento\Framework\App\Helper\AbstractHelper;
use Magento\Store\Model\ScopeInterface;

/**
 * Helper for shipping configuration
 */
class ShippingConfig extends AbstractHelper
{
    const XML_PATH_SHIPPING_ALLOW_COUNTRIES = 'checkout/shipping_address/allow_countries';
    const XML_PATH_SHIPPING_SPECIFIC_COUNTRIES = 'checkout/shipping_address/specific_countries';
    const XML_PATH_STRICT_SHIPPING_COUNTRY_CHECK = 'checkout/place_order_checks/strict_shipping_country_check';

    /**
     * Get allowed countries for shipping address
     *
     * @return array
     */
    public function getAllowedCountries()
    {
        $allowCountries = $this->scopeConfig->getValue(
            self::XML_PATH_SHIPPING_ALLOW_COUNTRIES,
            ScopeInterface::SCOPE_STORE
        );

        if ($allowCountries == 1) {
            $specificCountries = $this->scopeConfig->getValue(
                self::XML_PATH_SHIPPING_SPECIFIC_COUNTRIES,
                ScopeInterface::SCOPE_STORE
            );

            return explode(',', $specificCountries);
        }
        
        return ['AU'];
    }

    /**
     * Check if country is allowed for shipping
     *
     * @param string $countryId
     * @return bool
     */
    public function isCountryAllowedForShipping($countryId)
    {
        $allowedCountries = $this->getAllowedCountries();

        if (empty($allowedCountries)) {
            return true; // All countries allowed
        }

        return in_array($countryId, $allowedCountries);
    }

    public function isStrictShippingCountryCheckEnabled()
    {
        return $this->scopeConfig->isSetFlag(
            self::XML_PATH_STRICT_SHIPPING_COUNTRY_CHECK,
            ScopeInterface::SCOPE_STORE
        );
    }
}
