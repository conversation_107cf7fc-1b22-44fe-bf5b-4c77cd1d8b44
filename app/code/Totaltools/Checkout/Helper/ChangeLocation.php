<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2024 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Checkout\Helper;

use Totaltools\Storelocator\Model\Registry;


/**
 * Class ChangeLocation
 */
class ChangeLocation extends \Magento\Framework\App\Helper\AbstractHelper
{

    /**
     * @var \Totaltools\Storelocator\Model\Registry
     */
    private $storeRegistry;

    /**
     * @var \Magento\Directory\Model\RegionFactory
     */
    private $regionFactory;
     /**
     * ChangeLocation constructor.
     *
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Totaltools\Storelocator\Model\Registry $storeRegistry
     * @param \Magento\Directory\Model\RegionFactory $regionFactory
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Totaltools\Storelocator\Model\Registry $storeRegistry,
        \Magento\Directory\Model\RegionFactory $regionFactory
    ) {
        parent::__construct($context);
        $this->storeRegistry = $storeRegistry;
        $this->regionFactory = $regionFactory;
    }

    public function setNearestLocation($requestParams)
    {
        $postcode = $requestParams['postcode'];
        $city = $requestParams['city'];
        $countryId = $requestParams['country_id'];
        $region = $requestParams['region'];
        $storeId = $requestParams['store_id'];
        $shippitKey = $requestParams['shippit_key'];
        
        $location = [];
            
        $location = [
            'postcode' => $postcode,
            'city' => $city,
            "region" => $region,
            'region_data' => $this->getRegion($region, $countryId),
            'state' => $region,
            'country_id' => $countryId,
        ];

        $this->storeRegistry->addStoreData([
            Registry::GUEST_LOCATION => $location,
            Registry::DELIVERY_LOCATION => $location,
            Registry::STORELOCATOR_STORE_ID => $storeId,
            Registry::STORELOCATOR_API_KEY => $shippitKey
        ]);
        return true;
    }

    /**
     * Get region data if available from Directory data.
     *
     * @param string $regionCode
     * @param string $countryId
     * @return mixed
     */
    private function getRegion($regionCode, $countryId)
    {
        $region = $this->regionFactory->create()
            ->loadByCode($regionCode, $countryId);

        if ($region) {
            return [
                'region_id'     => (string) $region->getId(),
                'region_code'   => $regionCode,
                'region'        => $region->getName(),
            ];
        }

        return (object) [];
    }
}
