<?php

/**
 * <AUTHOR> <<EMAIL>>
 * @copyright Copyright (c) 2021 Totaltools (https://www.totaltools.com.au)
 * @package Totaltools_Checkout
 */

namespace Totaltools\Checkout\Amasty\Plugin\Block;

use Magento\Checkout\Model\Session as CheckoutSession;

/**
 * Checkout Layout Processor
 * Set Default values, field positions
 * @since 3.0.0 refactored for being cached
 */
class LayoutProcessor
{
    /**
     * @var CheckoutSession
     */
    private $checkoutSession;

    /**
     * LayoutProcessor constructor.
     * @param CheckoutSession $checkoutSession
     */
    public function __construct(CheckoutSession $checkoutSession)
    {
        $this->checkoutSession = $checkoutSession;
    }

    /**
     * @param \Magento\CustomerCustomAttributes\Block\Checkout\LayoutProcessor $block
     * @param array $jsLayout
     * @return array
     */
    public function afterProcess($block, $jsLayout)
    {
        $this->modifyBillingForm($jsLayout);
        $this->modifySharedBillingForm($jsLayout);

        return $jsLayout;
    }

    /**
     * Billing address modifications, only applicable to Amasty OSC.
     *
     * @param array $jsLayout
     * @return array
     */
    private function modifyBillingForm(&$jsLayout)
    {
        $isVirtual = $this->checkoutSession->getQuote()->isVirtual();

        if (!isset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']['children']['form-fields']['children'])) {
            return $jsLayout;
        }

        $billingConfiguration = &$jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']['children']['form-fields']['children'];

        $billingConfiguration['firstname']['component'] = 'Totaltools_Checkout/js/view/form/element/trackable';
        $billingConfiguration['firstname']['visible'] = $isVirtual;
        $billingConfiguration['lastname']['component'] = 'Totaltools_Checkout/js/view/form/element/trackable';
        $billingConfiguration['lastname']['visible'] = $isVirtual;
        $billingConfiguration['street']['children'][0]['validation']['prevent-pobox-rule'] = false;
        $billingConfiguration['street']['children'][0]['config']['placeholder'] = __('Start typing your address');
        $billingConfiguration['street']['children'][1]['validation']['prevent-pobox-rule'] = false;

        if (isset($billingConfiguration['is_business_address'])) {
            unset($billingConfiguration['is_business_address']);
        }

        if (isset($billingConfiguration['company'])) {
            unset($billingConfiguration['company']);
        }

        if (isset($billingConfiguration['street']['children'][2])) {
            unset($billingConfiguration['street']['children'][2]);
        }

        return $jsLayout;
    }

    /**
     * @param array $jsLayout
     * @return array
     */
    private function modifySharedBillingForm(&$jsLayout)
    {
        if (!$this->checkoutSession->getQuote()->isVirtual() || !isset($jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']['children']['form-fields']['children'])) {
            return $jsLayout;
        }

        $billingConfiguration = &$jsLayout['components']['checkout']['children']['steps']['children']['billing-step']['children']['payment']['children']['afterMethods']['children']['billing-address-form']['children']['form-fields']['children'];

        $billingConfiguration['telephone']['validation']['validate-phone-custom'] = true;
        $billingConfiguration['street']['children'][0]['validation']['prevent-pobox-rule'] = false;
        $billingConfiguration['street']['children'][0]['config']['placeholder'] = __('Start typing your address');
        $billingConfiguration['street']['children'][1]['validation']['prevent-pobox-rule'] = false;

        if (isset($billingConfiguration['is_business_address'])) {
            unset($billingConfiguration['is_business_address']);
        }

        if (isset($billingConfiguration['company'])) {
            unset($billingConfiguration['company']);
        }

        if (isset($billingConfiguration['street']['children'][2])) {
            unset($billingConfiguration['street']['children'][2]);
        }

        return $jsLayout;
    }
}
