<?php

namespace Totaltools\Checkout\Model\Api\Data;

/**
 * Class Address
 * @package Totaltools\Checkout\Model\Api\Data
 */
class Address extends \Magento\Framework\Model\AbstractModel  implements \Totaltools\Checkout\Api\Data\AddressInterface
{
    /**
     * @inheritdoc
     */
    public function getCity()
    {
        return $this->getData(self::KEY_CITY);
    }

    /**
     * @inheritdoc
     */
    public function setCity($city)
    {
        return $this->setData(self::KEY_CITY, $city);
    }

    /**
     * @inheritdoc
     */
    public function getCountryId()
    {
        return $this->getData(self::KEY_COUNTRY_ID);
    }

    /**
     * @inheritdoc
     */
    public function setCountryId($countryId)
    {
        return $this->setData(self::KEY_COUNTRY_ID, $countryId);
    }

    /**
     * @inheritdoc
     */
    public function getPostcode()
    {
        return $this->getData(self::KEY_POSTCODE);
    }

    /**
     * @inheritdoc
     */
    public function setPostcode($postcode)
    {
        return $this->setData(self::KEY_POSTCODE, $postcode);
    }

    /**
     * @inheritdoc
     */
    public function getRegion()
    {
        return $this->getData(self::KEY_REGION);
    }

    /**
     * @inheritdoc
     */
    public function setRegion($region)
    {
        return $this->setData(self::KEY_REGION, $region);
    }
}