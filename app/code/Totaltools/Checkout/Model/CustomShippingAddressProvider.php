<?php

/**
 * @package   Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright Copyright (c) 2025 Totaltools (https://totaltools.com.au)
 */

namespace Totaltools\Checkout\Model;

use Magento\Checkout\Model\ConfigProviderInterface;
use Magento\Directory\Model\ResourceModel\Country\CollectionFactory;
use Magento\Store\Model\StoreManagerInterface;
use Totaltools\Checkout\Helper\ShippingConfig;
use Magento\Directory\Helper\Data as DirectoryHelper;

class CustomShippingAddressProvider implements ConfigProviderInterface
{
    /**
     * @var CollectionFactory
     */
    private $countryCollectionFactory;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var ShippingConfig
     */
    private $shippingConfig;

    /**
     * @var DirectoryHelper
     */
    private $directoryHelper;

    /**
     * @param CollectionFactory $countryCollectionFactory
     * @param StoreManagerInterface $storeManager
     * @param ShippingConfig $shippingConfig
     * @param DirectoryHelper $directoryHelper
     */
    public function __construct(
        CollectionFactory $countryCollectionFactory,
        StoreManagerInterface $storeManager,
        ShippingConfig $shippingConfig,
        DirectoryHelper $directoryHelper
    ) {
        $this->countryCollectionFactory = $countryCollectionFactory;
        $this->storeManager = $storeManager;
        $this->shippingConfig = $shippingConfig;
        $this->directoryHelper = $directoryHelper;
    }

    /**
     * {@inheritdoc}
     */
    public function getConfig()
    {
        $config = [];

        $config['allowed_shipping_countries'] = $this->shippingConfig->getAllowedCountries();
        
        return $config;
    }

    /**
     * Get shipping country options with all required attributes
     *
     * @return array
     */
    private function getShippingCountryOptions()
    {
        // Get allowed countries from config
        $allowedCountries = $this->shippingConfig->getAllowedCountries();

        // Load countries from store and filter by allowed countries
        $countryCollection = $this->countryCollectionFactory->create()
            ->loadByStore($this->storeManager->getStore()->getId())
            ->addFieldToFilter('country_id', ['in' => $allowedCountries]);

        // Convert to option array
        $countryOptions = $countryCollection->toOptionArray();
        
        // Order the country options
        return $this->orderCountryOptions($countryOptions);
    }

    /**
     * Order country options with top countries first
     *
     * @param array $countryOptions
     * @return array
     */
    private function orderCountryOptions(array $countryOptions)
    {
        $topCountryCodes = $this->directoryHelper->getTopCountryCodes();
        if (empty($topCountryCodes)) {
            return $countryOptions;
        }

        $headOptions = [];
        $tailOptions = [[
            'value' => 'delimiter',
            'label' => '',
            'disabled' => true,
        ]];
        
        foreach ($countryOptions as $countryOption) {
            if (empty($countryOption['value']) || in_array($countryOption['value'], $topCountryCodes)) {
                $headOptions[] = $countryOption;
            } else {
                $tailOptions[] = $countryOption;
            }
        }
        
        return array_merge($headOptions, $tailOptions);
    }
}
