<?php

namespace Totaltools\Checkout\Model;

use Magento\Framework\Exception\CouldNotSaveException;
use Magento\Framework\Exception\NoSuchEntityException;

/**
 * Class CouponManagement
 * @package Totaltools\Checkout\Model
 */
class CouponManagement  implements \Totaltools\Checkout\Api\CouponManagementInterface
{
    /**
     * Quote repository.
     *
     * @var \Magento\Quote\Api\CartRepositoryInterface
     */
    private $quoteRepository;

    /**
     * @var \Magento\Quote\Model\Quote\TotalsCollector
     */
    private $totalsCollector;

    /**
     * CouponManagement constructor.
     * @param \Magento\Quote\Api\CartRepositoryInterface $quoteRepository
     * @param \Magento\Quote\Model\Quote\TotalsCollector $totalsCollector
     */
    public function __construct(
        \Magento\Quote\Api\CartRepositoryInterface $quoteRepository,
        \Magento\Quote\Model\Quote\TotalsCollector $totalsCollector
    ) {
        $this->quoteRepository = $quoteRepository;
        $this->totalsCollector = $totalsCollector;
    }

    /**
     * @param int $cartId
     * @param string $couponCode
     * @param \Totaltools\Checkout\Api\Data\AddressInterface $address
     * @param string $method
     * @return bool
     * @throws CouldNotSaveException
     * @throws NoSuchEntityException
     */
    public function set($cartId, $couponCode, \Totaltools\Checkout\Api\Data\AddressInterface $address, $method)
    {
        $couponCode = trim($couponCode);
        /** @var  \Magento\Quote\Model\Quote $quote */
        $quote = $this->quoteRepository->getActive($cartId);
        if (!$quote->getItemsCount()) {
            throw new NoSuchEntityException(__('Cart %1 doesn\'t contain products', $cartId));
        }
        if (!$quote->getStoreId()) {
            throw new NoSuchEntityException(__('Cart isn\'t assigned to correct store'));
        }
        $region = isset($address['region']) ? $address['region'] : '';
        $regionId = isset($address['region_id']) ? $address['region_id'] : '';
        $postcode = isset($address['postcode']) ? $address['postcode'] : '';
        $city = isset($address['city']) ? $address['city'] : '';
        $countryId = isset($address['country_id']) ? $address['country_id'] : 'AU';
        $shippingAddress = $quote->getShippingAddress();
        $shippingAddress->setRegion($region);
        $shippingAddress->setRegionId($regionId);
        $shippingAddress->setPostcode($postcode);
        $shippingAddress->setCity($city);
        $shippingAddress->setCountryId($countryId);
        $shippingAddress->setShippingMethod($method);
        $shippingAddress->setCollectShippingRates(true);
        $quote->setShippingAddress($shippingAddress);
        $quote->getShippingAddress()->setCollectShippingRates(true);

        try {
            $this->totalsCollector->collectAddressTotals($quote, $shippingAddress);
            $quote->setCouponCode($couponCode);
            $this->quoteRepository->save($quote->collectTotals());
        } catch (\Exception $e) {
            throw new CouldNotSaveException(__('Could not apply coupon code'));
        }
        if ($quote->getCouponCode() != $couponCode) {
            throw new NoSuchEntityException(__('Coupon code is not valid'));
        }
        return true;
    }
}
