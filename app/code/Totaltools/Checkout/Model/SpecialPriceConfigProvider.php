<?php

namespace Totaltools\Checkout\Model;

use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Catalog\Api\ProductRepositoryInterface;

class SpecialPriceConfigProvider implements \Magento\Checkout\Model\ConfigProviderInterface
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    private $_checkoutSession;

    /**
     * @var \Magento\Catalog\Api\ProductRepositoryInterface
     */
    private $_productRepository;

    /**
     * SpecialPriceConfigProvider contstructor
     *
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param \Magento\Catalog\Api\ProductRepositoryInterface $productRepository
     */
    public function __construct(
        CheckoutSession $checkoutSession,
        ProductRepositoryInterface $productRepository
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->_productRepository = $productRepository;
    }

    /**
     * @inheritdoc
     */
    public function getConfig(): array
    {
        $output['specialPrices'] = $this->_fetchSpecialPrices();
        return $output;
    }

    /**
     * Get all the top level quote items for this session.
     *
     * @return \Magento\Quote\Model\Quote\Item[]
     */
    private function _fetchQuoteItems(): array
    {
        return $this->_checkoutSession->getQuote()->getAllVisibleItems();
    }

    /**
     * @return array
     */
    private function _fetchSpecialPrices(): array
    {
        $quoteVirtual = $this->_checkoutSession->getQuote()->isVirtual();
        $items = $this->_fetchQuoteItems();
        $prices = [];

        if (!$quoteVirtual && count($items)) {
            foreach ($items as $item) {

                /** @var \Magento\Catalog\Model\Product $product */
                $product = $this->_productRepository->get($item->getSku());
                $regularPrice = (float) $product->getPrice();
                $finalPrice = $product->getFinalPrice();
                $specialPrice = $product->getSpecialPrice();
                $showSavedLabel = $product->getCustomAttribute('show_saved_label') ? $product->getCustomAttribute('show_saved_label')->getValue() : 'N';

                if ($finalPrice == $regularPrice || $this->isBonusItem($item)) {
                    continue;
                }

                $savings = (float) $regularPrice - $finalPrice;
                $savingsFloor = floor($savings);
                $savingsDecimal = number_format(($savings - $savingsFloor), 2) * 100;
                $savingsPercentage = (100 * $savings) / $regularPrice;

                if ($product->getTypeId() === \Magento\Catalog\Model\Product\Type::TYPE_BUNDLE) {
                    $savingsPercentage = 100 - $specialPrice;
                }

                if ($savings) {
                    $prices[$item->getItemId()] = [
                        "regular" => $regularPrice,
                        "special" => $finalPrice,
                        "savings" => $savings,
                        "savingsFloor" => $savingsFloor,
                        "savingsDecimal" => $savingsDecimal,
                        "savingsPercentage" => floor($savingsPercentage),
                        "showSavingsLabel" => $showSavedLabel
                    ];
                }
            }
        }

        return $prices;
    }

    /**
     * @param \Magento\Quote\Model\Quote\Item $item
     * @return bool
     */
    private function isBonusItem($item): bool
    {
        return !empty($item->getAppliedRuleIds()) && $item->getPrice() <= 0;
    }
}
