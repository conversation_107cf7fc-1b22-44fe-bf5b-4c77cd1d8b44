<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Checkout\Model\Config\Source;

class Operation implements \Magento\Framework\Data\OptionSourceInterface
{
    /**
     * @inheritdoc
     */
    public function toOptionArray()
    {
        return [
            [
                'value' => 1,
                'label' => __('Permanent')
            ],
            [
                'value' => 2,
                'label' => __('Test')
            ],
        ];
    }
}
