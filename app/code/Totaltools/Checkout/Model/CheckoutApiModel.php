<?php

namespace Totaltools\Checkout\Model;

use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Checkout\Model\Session;
use Totaltools\Checkout\Api\CheckoutApiInterface;
use Magento\Framework\Json\Encoder;
use \Totaltools\Storelocator\Model\Store;
use Magento\Framework\Exception\NoSuchEntityException;
use \Totaltools\Storelocator\Helper\Data as Helper;
use Magento\Company\Model\ResourceModel\Customer;

/**
 * Class CheckoutApiModel
 * @package Totaltools\Checkout\Model
 */
class CheckoutApiModel implements CheckoutApiInterface
{
    /**
     * @var Session
     */
    protected $_checkoutSession;

    /**
     * @var Encoder
     */
    protected $_jsonEncoder;

    /**
     * @var \Totaltools\Storelocator\Helper\Data
     */
    protected $_storelocatorHepler;

    /**
     * @var \Totaltools\Storelocator\Model\StoreInventoryService
     */
    protected $_storeInventoryService;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    protected $_storeRepository;

    /**
     * @var \Totaltools\Storelocator\Helper\Checkout\Data
     */
    protected $_checkoutHelper;

    /**
     * @var bool
     */
    protected static $_shippingAllowed = true;

    /**
     * @var array
     */
    protected static $_requiredProducts = [];

    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;

    /**
     * @var \Magento\Company\Model\CustomerFactory
     */
    private $companyCustomerFactory;

    /**
     * @var array
     */
    private $_items = [];
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;

    /**
     * CheckoutApiModel constructor.
     *
     * @param Session $checkoutSession
     * @param Encoder $encoder
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Company\Model\CustomerFactory $companyCustomerFactory
     * @param \Totaltools\Storelocator\Helper\Data $helper
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService
     * @param \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper
     */
    public function __construct(
        Session $checkoutSession,
        Encoder $encoder,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Company\Model\CustomerFactory $companyCustomerFactory,
        \Totaltools\Storelocator\Helper\Data $helper,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutHelper,
        Customer  $companyCustomer
    ) {
        $this->_checkoutSession = $checkoutSession;
        $this->customerSession = $customerSession;
        $this->companyCustomerFactory = $companyCustomerFactory;
        $this->_jsonEncoder = $encoder;
        $this->_storelocatorHepler = $helper;
        $this->_storeRepository = $storeRepository;
        $this->_storeInventoryService = $storeInventoryService;
        $this->_checkoutHelper = $checkoutHelper;
        $this->companyCustomer = $companyCustomer;
    }

    /**
     * @param $items
     *
     * @return $this
     */
    public function setItems($items)
    {
        $this->_items = $items;

        return $this;
    }

    protected function _getItems()
    {
        return $this->_items;
    }

    /**
     * @param $postcode
     * @return bool|\Magento\Framework\Api\ExtensibleDataInterface|Store
     */
    public function getAllocatedStore($postcode) {

        $resultStore = null;
        $firstStore = $b2bStore = $this->_storeRepository->getByPostcodeInZone($postcode);
        $this->filterProductByStockStatus();
        if ($firstStore) {
            /** @var \Totaltools\Storelocator\Model\Store $firstStore */
            $resultStore = $this->_checkStoreAvailability($firstStore);
        } else {
            $resultStore = $b2bStore = $this->_storeRepository->getByPostcodeInZone(\Totaltools\Storelocator\Model\Zone::DEFAULT_ZONE_POSTCODE);
        }

        $customer = $this->customerSession->isLoggedIn() ? $this->customerSession->getCustomer() : null;
        if ($customer) {
            $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId($customer->getId());
            $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
            $b2bCustomer =  array_diff($currentCompanyIds, [0]);
            if (!empty($b2bCustomer)) {
                $resultStore = $b2bStore;
            }
        }

        return $resultStore;

    }

    /**
     * @param string $postcode
     * @param string $city
     * @param string $countryId
     * @param string $region
     *
     * @api
     * @return string json containing information about location
     */
    public function changeLocation($postcode, $city, $countryId, $region)
    {
        $resultStore = $this->getAllocatedStore($postcode);
        $resultStoreId = 0;
        $location = [];

        if ($resultStore) {

            $resultStoreId = $resultStore->getId();

            $location = [
                'postcode' => $resultStore->getZipcode(),
                'city' => $resultStore->getCity(),
                'country_id' => $resultStore->getCountryId(),
            ];
            $locationGuest = [
                'postcode' => $postcode,
                'city' => $city,
                'country_id' => $countryId,
            ];
            $this->_checkoutSession->setData('delivery_location_session', $location);
            $this->_checkoutSession->setData('guest_location_session', $locationGuest);
            /** Set storelocator store to checkout session */
            $this->_checkoutHelper->setSessionStoreId($resultStore->getId());
            $this->_checkoutHelper->setStorelocatorApiKey($resultStore->getShippitApiKey());
        }

        return $this->_jsonEncoder->encode(
            [
                'location' => $location,
                'store_id' => $resultStoreId
            ]
        );
    }

    /**
     * Check product types action
     *
     * @return $this
     */
    public function filterProductByStockStatus()
    {

        $this->_checkoutHelper->setSessionOdItemExisted(0);
        $this->_checkoutHelper->setSessionOxItemExisted(0);
        $this->_checkoutHelper->setSessionOeItemExisted(0);

        $shippingMethod = $this->_checkoutSession->getQuote()->getShippingAddress()->getShippingMethod();

        if ($shippingMethod == Helper::SHIPPIT_ONDEMAND) {
            $surplusQtyBuffer = $this->_storelocatorHepler->getUberSurplusQtyBuffer();
        } else {
            $surplusQtyBuffer = $this->_storelocatorHepler->getExpressSurplusQtyBuffer();
        }

        $items = $this->_getItems();
        if (empty($items)) {
            $items = $this->_checkoutSession->getQuote()->getItems();
        }

        if (empty($items)) {
            return $this;
        }

        try {
            $stockAvailabilityValues = $this->_storelocatorHepler->getStockAvailabilityAttributeValues();

        } catch (NoSuchEntityException $e) {}

        foreach ($items as $item) {
            /**
             * @var \Magento\Quote\Model\Quote\Item|\Magento\Sales\Model\Order\Item $item
             */
            $product = $item->getProduct();
            $isDangerous = $product->getData(Store::SHIPPING_DANGEROUS_KEY);
            $isBackorderable = (bool) $product->getData(Store::IS_BACKORDERABLE);

            if ($isDangerous) {
                self::$_shippingAllowed = false;
                break;
            }

            $stockAvailabilityCodeId = $product->getData(Store::STOCK_AVAILABILITY_KEY);
            $stockAvailabilityCode = isset($stockAvailabilityValues[$stockAvailabilityCodeId]) ? $stockAvailabilityValues[$stockAvailabilityCodeId] : '';

            if (in_array($stockAvailabilityCode, Store::UNSHIPPABLE_PRODUCT_TYPES)) {
                self::$_shippingAllowed = false;
                break;
            }

            if ($stockAvailabilityCode == Store::STOCK_ON_DEMAND) {
                $this->_checkoutHelper->setSessionOdItemExisted(1);
            } else if (!$isBackorderable) {
                $this->_checkoutHelper->setSessionOxItemExisted(1);
            } else if ($stockAvailabilityCode == Store::TYPE_OE) {
                $this->_checkoutHelper->setSessionOeItemExisted(1);
            }

            self::$_requiredProducts[$product->getSku()] = ($shippingMethod == Helper::SHIPPIT_EXPRESS || $shippingMethod == Helper::SHIPPIT_ONDEMAND || str_contains($shippingMethod, 'Priority')) ? $item->getQty() + (int)$surplusQtyBuffer  : $item->getQty();
        }

        return $this;
    }

    /**
     * @param Store $store
     * @return bool|Store
     */
    protected function _checkStoreAvailability(\Totaltools\Storelocator\Model\Store $store)
    {
        $allowedDelivery = $store->getAllowDeliveryOrders();
        if ($allowedDelivery == 1 && (!self::$_shippingAllowed || !self::$_requiredProducts) ) {
            return $store;
        }

        $hasInventory = $this->_storeInventoryService->checkStoreInventory($store, self::$_requiredProducts);
        
        if ($hasInventory && $allowedDelivery == 1) {
            return $store;
        }

        $fallbackStoresCollection = $this->_storeRepository->getFallbackStores($store->getId());

        foreach ($fallbackStoresCollection->getItems() as $fallbackStore) {
            /** @var \Totaltools\Storelocator\Model\Store $fallbackStore */
            $hasInventory = $this->_storeInventoryService->checkStoreInventory($fallbackStore, self::$_requiredProducts);

            if ($hasInventory) {
                return $fallbackStore;
            }
        }

        /**
         * Reached here - store does not have stock & fallback stores also do not have stock
         */
        if($this->_checkoutHelper->getSessionOxItemExisted() == 1) {

            // If an OX item existed then return Support Office
            return $this->_storeRepository->getByPostcodeInZone(\Totaltools\Storelocator\Model\Zone::DEFAULT_ZONE_POSTCODE);
        } else {

            if ($superStoreId = $store->getSuperStoreId()) {
                if ( $superStoreId != $store->getId()) {
                    $superStore = $this->_storeRepository->getById($superStoreId);
                    if ($superStore) {
                        return $superStore;
                    }
                }
            }

            // Return the first store if non of the fallback stores have stock
            return $store;
        }

    }
}