<?php

namespace Totaltools\Checkout\Model;


use Magento\Checkout\Model\ConfigProviderInterface;
use Totaltools\Geo\Helper\GeoLocateHelper;

class LocationConfigProvider implements ConfigProviderInterface
{

    /**
     * @var GeoLocateHelper
     */
    protected $_geoLocateHelper;

    public function __construct(GeoLocateHelper $geoLocateHelper)
    {
        $this->_geoLocateHelper = $geoLocateHelper;
    }

    /**
     * Retrieve assoc array of checkout configuration
     *
     * @return array
     */
    public function getConfig()
    {
        return [
            "test_config" => "TEST CONFIG"
        ];
    }
}