<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <!-- <event name="controller_action_predispatch_checkout_onepage_index">
        <observer name="totaltools_checkout_observer_checkoutinit" instance="Totaltools\Checkout\Observer\Checkoutinit" />
    </event>
    <event name="controller_action_predispatch_checkout_index_index">
        <observer name="totaltools_checkout_observer_checkoutinit" instance="Totaltools\Checkout\Observer\CheckoutInit" />
    </event> -->
</config>
