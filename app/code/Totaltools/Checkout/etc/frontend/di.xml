<?xml version="1.0"?>

<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Checkout\Block\Onepage\Link">
        <plugin name="checkout-url-customization" type="Totaltools\Checkout\Plugin\Link" sortOrder="10" />
    </type>

    <type name="Magento\Checkout\Model\CompositeConfigProvider">
        <arguments>
            <argument name="configProviders" xsi:type="array">
                <item name="totaltools_special_price_provider" xsi:type="object">Totaltools\Checkout\Model\SpecialPriceConfigProvider</item>
                <item name="custom_shipping_countries_provider" xsi:type="object">Totaltools\Checkout\Model\CustomShippingAddressProvider</item>
            </argument>
        </arguments>
    </type>

    <type name="Magento\Checkout\Model\Cart\ImageProvider">
        <plugin name="totaltools_image_provider_plugin" type="Totaltools\Checkout\Plugin\Model\Cart\ImageProviderPlugin" />
    </type>

    <type name="Magento\Checkout\Block\Checkout\LayoutProcessor">
        <plugin name="add-third-party-pickup" type="Totaltools\Checkout\Plugin\ThirdPartyPickup" sortOrder="100"/>
        <plugin name="add-third-party-name" type="Totaltools\Checkout\Plugin\ThirdPartyName" sortOrder="110"/>
        <plugin name="add-storelocator-id" type="Totaltools\Checkout\Plugin\StoreLocatorId" sortOrder="120"/>
        <plugin name="add-au-postcode-validator" type="Totaltools\Checkout\Plugin\Checkout\PostcodeValidator" sortOrder="130"/>
    </type>

    <type name="Magento\CustomerCustomAttributes\Block\Checkout\LayoutProcessor">
        <plugin name="amasty-checkout-modifications" type="Totaltools\Checkout\Amasty\Plugin\Block\LayoutProcessor" sortOrder="100" />
    </type>

    <type name="Magento\Checkout\Block\Onepage">
        <plugin name="totaltools_checkout_onepage_config_provider" type="Totaltools\Checkout\Plugin\Block\ConfigProvider" />
    </type>

    <type name="Magento\Checkout\Block\Cart\Shipping">
        <plugin name="totaltools_checkout_cart_config_provider" type="Totaltools\Checkout\Plugin\Block\ConfigProvider" />
    </type>    
</config>
