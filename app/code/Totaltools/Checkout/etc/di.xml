<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Magento\Checkout\Model\Sidebar" type="Totaltools\Checkout\Model\RewriteSidebar"/>
    <preference for="Totaltools\Checkout\Api\CheckoutApiInterface" type="Totaltools\Checkout\Model\CheckoutApiModel"/>
    <preference for="Totaltools\Checkout\Api\Data\AddressInterface" type="Totaltools\Checkout\Model\Api\Data\Address"/>
    <preference for="Totaltools\Checkout\Api\CouponManagementInterface" type="Totaltools\Checkout\Model\CouponManagement"/>
    <preference for="Totaltools\Checkout\Api\GuestCouponManagementInterface" type="Totaltools\Checkout\Model\GuestCouponManagement"/>
    <preference for="Magento\Checkout\Block\Onepage" type="Totaltools\Checkout\Block\Onepage"/>
    <preference for="Magento\Checkout\Block\Success" type="Totaltools\Checkout\Block\Success"/>
    <type name="Magento\Checkout\Block\Checkout\LayoutProcessor">
        <plugin name="totaltools_checkout_layout_processor" type="Totaltools\Checkout\Plugin\Block\LayoutProcessor" sortOrder="1"/>
    </type>
    <type name="Magento\Checkout\Model\PaymentInformationManagement">
        <plugin name="totaltools_checkout_payment_information_management" type="Totaltools\Checkout\Plugin\Model\PaymentInformationManagement" sortOrder="1"/>
    </type>
    <type name="Magento\Checkout\Model\ShippingInformationManagement">
        <plugin name="totaltools_checkout_shipping_information_management" type="Totaltools\Checkout\Plugin\Model\ShippingInformationManagement" sortOrder="1"/>
    </type>
    <!-- Custom log file for Totaltools Checkout -->
    <virtualType name="Totaltools\Checkout\Model\Logger\VirtualDebug" type="Magento\Framework\Logger\Handler\Base">
        <arguments>
            <argument name="fileName" xsi:type="string">/var/log/totaltools/shippit_shipping.log</argument>
        </arguments>
    </virtualType>
    <virtualType name="Totaltools\Checkout\Model\Logger\VirtualLogger" type="Magento\Framework\Logger\Monolog">
        <arguments>
            <argument name="name" xsi:type="string">TOT</argument>
            <argument name="handlers" xsi:type="array">
                <item name="debug" xsi:type="object">Totaltools\Checkout\Model\Logger\VirtualDebug</item>
            </argument>
        </arguments>
    </virtualType>
    <type name="Totaltools\Checkout\Controller\Checkout\Changelocation">
        <arguments>
            <argument name="logger" xsi:type="object">Totaltools\Checkout\Model\Logger\VirtualLogger</argument>
        </arguments>
    </type>
    <type name="Magento\Quote\Api\CartTotalRepositoryInterface">
        <plugin name="totaltools_checkout_quote_total_repo" type="Totaltools\Checkout\Plugin\QuoteTotalItems" sortOrder="50" />
    </type>
    <preference for="Magento\Checkout\CustomerData\DefaultItem" type="Totaltools\Checkout\Rewrite\Magento\Checkout\CustomerData\DefaultItem"/>
    <type name="Magento\Checkout\Api\PaymentInformationManagementInterface">
        <plugin name="validate-agreements" disabled="true" />
    </type>
    <type name="Magento\Checkout\Block\Cart\Sidebar">
        <plugin name="totaltools_checkout_cart_sidebar" type="Totaltools\Checkout\Plugin\Cart\Sidebar" sortOrder="50" />
    </type>
     <type name="Magento\Checkout\CustomerData\Cart">
        <plugin name="totaltools_checkout_cart_customerdata" type="Totaltools\Checkout\Plugin\Checkout\CustomerData\Cart" />
    </type>
    <type name="Amasty\CheckoutCore\Api\CheckoutBlocksProviderInterface">
        <plugin name="totaltools_checkout_blocks_provider" sortOrder="100" type="Totaltools\Checkout\Plugin\Model\Config\CheckoutBlocksProviderPlugin" />
    </type>
    <type name="Magento\Quote\Model\ShippingMethodManagement">
        <plugin disabled="false" name="totaltools_checkout_model_express_shipping" sortOrder="10" type="Totaltools\Checkout\Plugin\Model\ShippingMethodManagementPlugin"/>
    </type>
    <type name="Magento\Checkout\Model\PaymentInformationManagement">
        <plugin name="totaltools_checkout_place_order_validator" type="Totaltools\Checkout\Plugin\Model\PlaceOrderPlugin" sortOrder="10" />
    </type>
</config>
