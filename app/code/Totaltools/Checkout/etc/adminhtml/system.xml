<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="checkout">
            <group id="options">
                <field id="display_billing_address_on" translate="label" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1" canRestore="1">
                    <source_model>Amasty\CheckoutStyleSwitcher\Model\Config\Source\BillingAddressDisplayOptions</source_model>
                </field>
            </group>
            <group id="total_tools" translate="label" type="text" sortOrder="999" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Total Tools Custom Configurations</label>
                <field id="stock_messages_active" translate="label" type="select" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable stock messsages</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="authority_leave_active" translate="label" type="select" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable authority to leave</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="cc_carrier_code" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Click And Collect Carrier Code</label>
                </field>
                <field id="cc_method_code" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Click And Collect Method Code</label>
                </field>
                <field id="loqate_shipping_address_key" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Loqate Shipping Address Key</label>
                </field>
                <field id="loqate_billing_address_key" translate="label" type="text" sortOrder="4" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Loqate Billing Address Key</label>
                </field>
                <field id="loqate_fetch_suburb_api_url" translate="label" type="text" sortOrder="5" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Loqate Fetch Suburb API URL</label>
                </field>
                <field id="loqate_fetch_suburb_api_key" translate="label" type="text" sortOrder="6" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Loqate Fetch Suburb API Key</label>
                </field>
                <field id="free_delivery_starts_at" translate="label" type="text" sortOrder="7" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Free Delivery Starts At</label>
                </field>

            </group>
            <group id="total_checkout" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Checkout Options</label>

                <field id="third_party_pickup_active" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Display Third Party Pickup</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>

                <field id="hide_checkout_options_shipping_methods" type="multiselect" translate="label" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Hide Checkout Options for Shipping Methods</label>
                    <source_model>Shippit\Shipping\Model\Config\Source\Shipping\Methods</source_model>
                    <comment>Select the Shipping Methods that should hide the "Third Party Pickup" and "Third Party Name" options during checkout.</comment>
                    <can_be_empty>1</can_be_empty>
                </field>
            </group>
            <group id="total_tools_cart" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Cart Page Options</label>

                <field id="allow_discounts_and_redemptions_on_cart" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Allow Discounts And Redemptions on Cart</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
            <group id="total_arrive_estimate" translate="label" type="text" sortOrder="900" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Arrive By Estimate</label>
                <field id="enabled" translate="label" type="select" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Enable Arrive By Estimate</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="arrive_by_days" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Arrive By Days</label>
                </field>
                <field id="minimum_qty_buffer" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Minimum QTY Buffer</label>
                </field>
            </group>
            <group id="express_checkout_option" translate="label" type="text" sortOrder="1000" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Express Checkout Option</label>
                <field id="enabled" translate="label" type="select" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Enable Express Checkout Option</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="express_surplus_qty_buffer" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Express and Priority Surplus Qty Buffer</label>
                </field>
                <field id="hour_of_day_priority_start_at" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Priority Time Slot Start At</label>
                    <comment>Hour of the day. 0 to 23</comment>
                </field>
                <field id="hour_of_day_priority_end_at" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Priority Time Slot End At</label>
                    <comment>Hour of the day. 0 to 23</comment>
                </field>
                <field id="priority_available_days" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Priority Available on Days of the Week</label>
                    <comment>Comma-separated numbers representing days of the week, starting from 0 (Sunday) to 6 (Saturday).</comment>
                </field>
                <field id="hour_of_day" translate="label" type="text" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Hour of Day for Same Day Express Delivery</label>
                    <comment>Hour of the day. 0 to 23</comment>
                </field>
                <field id="arrive_by_today_message" translate="label" type="textarea" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Arrive By Today Message</label>
                </field>
                <field id="arrive_by_tomorrow_message" translate="label" type="textarea" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Arrive By Tomorrow Message</label>
                </field>
            </group>
            <group id="shipping_methods_sort_order" translate="label" type="text" sortOrder="1200" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Shipping Methods Sort Order</label>
                <field id="shippitcc" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Click And Collect</label>
                </field>
                <field id="freeshipping" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Free Delivery</label>
                </field>
                <field id="standard" translate="label" type="text" sortOrder="3" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Standard Delivery</label>
                </field>
                <field id="express" translate="label" type="text" sortOrder="4" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Express</label>
                </field>
                <field id="ondemand" translate="label" type="text" sortOrder="5" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>On Demand</label>
                </field>
                <field id="priority" translate="label" type="text" sortOrder="6" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Priority</label>
                </field>
            </group>
            <group id="uber_ondemand_settings" translate="label" type="text" sortOrder="1250" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Uber On Demand Checkout Setting</label>
                <field id="uber_surplus_qty_buffer" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Uber On Demand Surplus Qty Buffer</label>
                </field>
                <field id="title" translate="label" type="text" sortOrder="2" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Uber On Demand Checkout Title</label>
                    <comment>Uber on demand method text on checkout page.</comment>
                </field>
                <field id="uber_ondemand_custom_quote_price" translate="label comment" type="text" sortOrder="3" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Uber On Demand Custom Quote Price</label>
                    <comment>Set 0 for free uber delivery, set any number for fixed rates or set 999 for uber quote from shippit.</comment>
                    <validate>required-entry validate-number validate-zero-or-greater</validate>
                </field>
                <field id="uber_ondemand_max_weight" translate="label comment" type="text" sortOrder="4" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Maximum allowed weight for Uber</label>
                    <comment>In KG.</comment>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                </field>
                <field id="uber_ondemand_max_dimension" translate="label comment" type="text" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Maximum allowed dimension for Uber</label>
                    <comment>In Centimetres.</comment>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                </field>
                <field id="hour_of_day_uber_start_at" translate="label" type="text" sortOrder="6" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Uber Time Slot Start At</label>
                    <comment>Hour of the day. 0 to 23</comment>
                </field>
                <field id="hour_of_day_uber_end_at" translate="label" type="text" sortOrder="7" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Uber Time Slot End At</label>
                    <comment>Hour of the day. 0 to 23</comment>
                </field>
                <field id="uber_available_days" translate="label" type="text" sortOrder="8" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Uber Available on Days of the Week</label>
                    <comment>Comma-separated numbers representing days of the week, starting from 0 (Sunday) to 6 (Saturday).</comment>
                </field>
                <field id="max_order_value" translate="label comment" type="text" sortOrder="9" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Maximum Order Value for Uber</label>
                    <comment>Set limit for Uber orders by the Order Total.</comment>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                </field>
                <field id="min_order_value" translate="label comment" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Minimum Order Value for Uber</label>
                    <comment>If the order value is less than this amount, Uber will not be allowed.</comment>
                    <validate>required-entry validate-number validate-greater-than-zero</validate>
                </field>
            </group>
            <group id="cart_reminder" translate="label" type="text" sortOrder="1300"
                showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Cart Reminder Popup Configuration</label>
                <field id="active" translate="label" type="select" showInDefault="1"
                    showInWebsite="1" showInStore="0" sortOrder="10">
                    <label>Enable Cart Reminder</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="idle_timeout" translate="label" type="text" showInDefault="1"
                    showInWebsite="1" showInStore="0" sortOrder="30">
                    <label>Idle Timeout</label>
                    <comment>Time in hours for user to to be considered idle.</comment>
                    <depends>
                        <field id="*/*/active">1</field>
                    </depends>
                </field>
                <field id="operation" translate="label" type="select" showInDefault="1"
                    showInWebsite="1" showInStore="0" sortOrder="20">
                    <label>Operation Mode</label>
                    <source_model>Totaltools\Checkout\Model\Config\Source\Operation</source_model>
                    <depends>
                        <field id="*/*/active">1</field>
                    </depends>
                </field>
                <field id="test_class" translate="label" type="text" showInDefault="1"
                    showInWebsite="1" showInStore="0" sortOrder="30">
                    <label>Test Class</label>
                    <depends>
                        <field id="*/*/active">1</field>
                        <field id="*/*/operation">2</field>
                    </depends>
                </field>
                <field id="title" showInDefault="1" showInStore="0" showInWebsite="1"
                    translate="label" type="text" sortOrder="40">
                    <label>Notification Title</label>
                    <depends>
                        <field id="*/*/active">1</field>
                    </depends>
                </field>
                <field id="message" showInDefault="1" showInStore="0" showInWebsite="1"
                    sortOrder="50" translate="label" type="text">
                    <label>Notification Message</label>
                    <depends>
                        <field id="*/*/active">1</field>
                    </depends>
                </field>
                <field id="button_text" showInDefault="1" showInStore="0" showInWebsite="1"
                    sortOrder="60" translate="label" type="text">
                    <label>Notification Button Title</label>
                    <depends>
                        <field id="*/*/active">1</field>
                    </depends>
                </field>
            </group>
            <group id="guest_checkout_login" translate="label" type="text" sortOrder="1500" showInDefault="1" showInWebsite="0" showInStore="0">
                <label>Guest Checkout Login Page Messages</label>
                <field id="text_meesage" translate="label" type="text" sortOrder="1" showInDefault="1" showInWebsite="0" showInStore="0">
                    <label>Guest Checkout Login Page Text Message</label>
                </field>
            </group>
            <group id="shipping_address" translate="label" type="text" sortOrder="105" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Shipping Address Settings</label>
                <field id="allow_countries" translate="label" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Shipping Address Applicable Countries</label>
                    <source_model>Totaltools\Checkout\Model\Config\Source\Country</source_model>
                </field>
                <field id="specific_countries" translate="label" type="multiselect" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Shipping Address Specific Countries</label>
                    <source_model>Magento\Directory\Model\Config\Source\Country</source_model>
                    <depends>
                        <field id="allow_countries">1</field>
                    </depends>
                </field>
            </group>
            <group id="place_order_checks" translate="label" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                <label>Place Order Checks</label>
                <field id="strict_shipping_country_check" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Strict Shipping Country Check</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled, orders will be rejected for shipping addresses outside Australia (except for Click and Collect).</comment>
                </field>
            </group>
        </section>
        <section id="payment">
            <group id="humm_gateway">
                <group id="humm_conf">
                    <field id="max_order_total" translate="label comment" type="text" sortOrder="145" showInDefault="1"
                           showInWebsite="1" showInStore="0">
                        <label>Maximum Order Amount</label>
                        <comment>This is for 10x payments and must be greater than 5x and min</comment>
                        <validate>validate-number</validate>
                    </field>
                    <field id="max_order_total_5x" translate="label comment" type="text" sortOrder="146" showInDefault="1"
                           showInWebsite="1" showInStore="0">
                        <label>Maximum Amount for 5x payments</label>
                        <comment>This must be less than max and greater than min</comment>
                        <validate>validate-number</validate>
                    </field>
                </group>
            </group>
        </section>
    </system>
</config>
