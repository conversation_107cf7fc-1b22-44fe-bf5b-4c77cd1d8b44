<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Event/etc/events.xsd">
    <event name="sales_model_service_quote_submit_before">
        <observer name="save_third_party_to_order" instance="Totaltools\Checkout\Observer\SaveThirdPartyPickupToOrderObserver"/>
    </event>
     <event name="payment_method_is_active">
        <observer name="payment_method_status" instance="Totaltools\Checkout\Observer\PaymentMethodAvailable" />
    </event>
    <event name="layout_render_before_checkout_cart_index">
        <observer name="remove_block_for_virtual_products" instance="Totaltools\Checkout\Observer\RemoveBlockForVirtualCart" />
    </event>
</config>
