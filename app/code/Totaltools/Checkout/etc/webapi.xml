<?xml version="1.0"?>
<routes xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Webapi:etc/webapi.xsd">
    <route url="/V1/checkout/change-location" method="POST">
        <service class="Totaltools\Checkout\Api\CheckoutApiInterface" method="changeLocation"/>
        <resources>
            <resource ref="anonymous"/>
        </resources>
    </route>
    <route url="/V1/carts/mine/customcoupons/:couponCode" method="PUT">
        <service class="Totaltools\Checkout\Api\CouponManagementInterface" method="set"/>
        <resources>
            <resource ref="self" />
        </resources>
        <data>
            <parameter name="cartId" force="true">%cart_id%</parameter>
        </data>
    </route>
    <route url="/V1/guest-carts/:cartId/customcoupons/:couponCode" method="PUT">
        <service class="Totaltools\Checkout\Api\GuestCouponManagementInterface" method="set"/>
        <resources>
            <resource ref="anonymous" />
        </resources>
    </route>
</routes>
