<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Store:etc/config.xsd">
    <default>
        <checkout>
            <total_tools>
                <cc_carrier_code>shippitcc</cc_carrier_code>
                <cc_method_code>shippitcc</cc_method_code>
                <loqate_shipping_address_key>UT39-UU94-MH11-UB61</loqate_shipping_address_key>
                <loqate_billing_address_key>ZB49-MG77-EB26-TY95</loqate_billing_address_key>
                <loqate_fetch_suburb_api_url>https://api.addressy.com/Cleansing/International/Batch/v1.00/json4.ws</loqate_fetch_suburb_api_url>
                <loqate_fetch_suburb_api_key>HX99-PT51-XC74-KN45</loqate_fetch_suburb_api_key>
                <free_delivery_starts_at>99</free_delivery_starts_at>
            </total_tools>
            <total_arrive_estimate>
                <enabled>0</enabled>
                <arrive_by_days>2</arrive_by_days>
                <minimum_qty_buffer>5</minimum_qty_buffer>
            </total_arrive_estimate>
            <uber_ondemand_settings>
                <max_order_value>1000</max_order_value>
                <min_order_value>99</min_order_value>
            </uber_ondemand_settings>
            <cart_reminder>
                <active>1</active>
                <idle_timeout>24</idle_timeout>
                <operation>2</operation>
                <test_class>test_36</test_class>
                <title>Welcome Back</title>
                <message>We Saved Your Cart For You</message>
                <button_text>View Cart</button_text>
            </cart_reminder>
            <place_order_checks>
                <strict_shipping_country_check>1</strict_shipping_country_check>
            </place_order_checks>
            <shipping_address>
                <allow_countries>0</allow_countries>
                <specific_countries>AU</specific_countries>
            </shipping_address>
        </checkout>
        <payment>
            <humm_gateway>
                <humm_conf>
                    <max_order_total>1499</max_order_total>
                    <max_order_total_5x>1000</max_order_total_5x>
                </humm_conf>
            </humm_gateway>
        </payment>
    </default>
</config>
