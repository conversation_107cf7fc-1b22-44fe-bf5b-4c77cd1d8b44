<?php

namespace Totaltools\Checkout\Api;

/**
 * Interface GuestCouponManagementInterface
 * @package Totaltools\Checkout\Api
 */
interface GuestCouponManagementInterface
{
    /**
     * Add a coupon by code to a specified cart.
     *
     * @param string $cartId The cart ID.
     * @param string $couponCode The coupon code data.
     * @param \Totaltools\Checkout\Api\Data\AddressInterface $address
     * @param string $method
     * @return bool
     * @throws \Magento\Framework\Exception\NoSuchEntityException The specified cart does not exist.
     * @throws \Magento\Framework\Exception\CouldNotSaveException The specified coupon could not be added.
     */
    public function set($cartId, $couponCode, \Totaltools\Checkout\Api\Data\AddressInterface $address, $method);
}