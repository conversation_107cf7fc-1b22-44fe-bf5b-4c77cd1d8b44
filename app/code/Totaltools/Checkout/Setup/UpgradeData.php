<?php

namespace Totaltools\Checkout\Setup;

use Magento\Eav\Setup\EavSetupFactory;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;
use Magento\Quote\Setup\QuoteSetupFactory;
use Totaltools\Checkout\Setup\Operation\LayoutBuilderUpgrade;

/**
 * Class UpgradeData
 * @package Totaltools\Checkout\Setup
 */
class UpgradeData implements UpgradeDataInterface
{
    /**
     * @var \Magento\Quote\Setup\QuoteSetupFactory
     */
    private $quoteSetupFactory;

    /**
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var LayoutBuilderUpgrade
     */
    private $layoutBuilderUpgrade;

    /**
     * UpgradeData constructor.
     *
     * @param EavSetupFactory $eavSetupFactory
     */
    public function __construct(
        QuoteSetupFactory $quoteSetupFactory,
        EavSetupFactory $eavSetupFactory,
        LayoutBuilderUpgrade $layoutBuilderUpgrade
    ) {
        $this->quoteSetupFactory = $quoteSetupFactory;
        $this->eavSetupFactory = $eavSetupFactory;
        $this->layoutBuilderUpgrade = $layoutBuilderUpgrade;
    }

    /**
     * Run module upgrades.
     *
     * @param ModuleDataSetupInterface $setup
     * @param ModuleContextInterface $context
     */
    public function upgrade(ModuleDataSetupInterface $setup, ModuleContextInterface $context)
    {
        $setup->startSetup();

        if (version_compare($context->getVersion(), '1.0.1', '<')) {
            $this->addRetailerIdAttributes($setup);
        }

        if (version_compare($context->getVersion(), '1.0.3', '<')) {
            $this->layoutBuilderUpgrade->execute($setup);
        }

        $setup->endSetup();
    }

    /**
     *
     * @param ModuleDataSetupInterface $setup
     */
    private function addRetailerIdAttributes(ModuleDataSetupInterface $setup)
    {
        /** @var \Magento\Quote\Setup\QuoteSetup $quoteSetup */
        $quoteSetup = $this->quoteSetupFactory->create(['resourceName' => 'quote_setup', 'setup' => $setup]);

        $quoteSetup->addAttribute(
            'quote_address',
            'storelocator_id',
            ['type' => \Magento\Framework\DB\Ddl\Table::TYPE_INTEGER, 'visible' => false]
        );
    }
}
