<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2022 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Checkout\Setup\Operation;

use Amasty\Base\Model\Serializer;
use Amasty\CheckoutLayoutBuilder\Model\ConfigProvider as Config;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\App\Cache\Manager;
use Magento\Framework\App\Cache\Type\Config as CacheTypeConfig;

class LayoutBuilderUpgrade
{
    private string $layoutBuilderConfig;

    private string $frontendLayoutConfig;

    private Serializer $serializer;

    private Manager $cacheManager;

    public function __construct(
        Serializer $serializer,
        Manager $cacheManager
    ) {
        $this->serializer = $serializer;
        $this->cacheManager = $cacheManager;

        $this->layoutBuilderConfig = Config::PATH_PREFIX
            . Config::LAYOUT_BUILDER_BLOCK
            . Config::FIELD_LAYOUT_BUILDER_CONFIG;
        $this->frontendLayoutConfig = Config::PATH_PREFIX
            . Config::LAYOUT_BUILDER_BLOCK
            . Config::FIELD_FRONTEND_LAYOUT_CONFIG;
    }

    public function execute(ModuleDataSetupInterface $setup): void
    {
        $connection = $setup->getConnection();
        $configTable = $setup->getTable('core_config_data');

        $existingConfig = $this->getExistingConfig(
            $connection,
            $configTable
        );

        foreach ($existingConfig as $config) {
            $updatedConfig = $this->updateConfig($config);

            $connection->update(
                $configTable,
                ['value' => $this->serializer->serialize($updatedConfig)],
                ['config_id = ?' => $config['config_id']]
            );
        }

        $this->cacheManager->clean([CacheTypeConfig::TYPE_IDENTIFIER]);
    }

    private function updateConfig(array $config): array
    {
        $result = [];
        $path = $config['path'];
        $config = $this->serializer->unserialize($config['value']);

        if ($path === $this->layoutBuilderConfig) {
            $preset = $this->getUpdatedPreset();
            $mergedPresets = array_replace_recursive($config, $preset);
            $result = $mergedPresets;
        } elseif ($path === $this->frontendLayoutConfig) {
            $newBlocks = $this->filterDuplicates($config);
            $updatedBlocks[] = array_merge($config[0], $newBlocks);
            array_splice($config, 0, 1, $updatedBlocks);
            $result = $config;
        }

        return $result;
    }

    private function getExistingConfig(AdapterInterface $connection, string $configTableName): array
    {
        $select = $connection->select()
            ->from($configTableName)
            ->where('path IN(?)', [
                $this->layoutBuilderConfig,
                $this->frontendLayoutConfig
            ]);

        $configRows = $connection->fetchAll($select);

        return $configRows;
    }

    private function getUpdatedPreset(): array
    {
        return [
            'classic' => [
                '2columns' => [
                    'layout' => [
                        5 => [
                            'i' => 'email_address',
                            'x' => 0,
                            'y' => 1,
                            'w' => 1,
                            'h' => 1,
                        ],
                        6 => [
                            'i' => 'street_address',
                            'x' => 0,
                            'y' => 2,
                            'w' => 1,
                            'h' => 1,
                        ],
                        7 => [
                            'i' => 'billing_address',
                            'x' => 0,
                            'y' => 3,
                            'w' => 1,
                            'h' => 1,
                        ],
                    ],
                ],
                '3columns' => [
                    'layout' => [
                        5 => [
                            'i' => 'email_address',
                            'x' => 0,
                            'y' => 1,
                            'w' => 1,
                            'h' => 1,
                        ],
                        6 => [
                            'i' => 'street_address',
                            'x' => 0,
                            'y' => 2,
                            'w' => 1,
                            'h' => 1,
                        ],
                        7 => [
                            'i' => 'billing_address',
                            'x' => 0,
                            'y' => 3,
                            'w' => 1,
                            'h' => 1,
                        ],
                    ],
                ],
            ],
            'modern' => [
                '1column' => [
                    'layout' => [
                        5 => [
                            'i' => 'email_address',
                            'x' => 0,
                            'y' => 1,
                            'w' => 1,
                            'h' => 1,
                        ],
                        6 => [
                            'i' => 'street_address',
                            'x' => 0,
                            'y' => 2,
                            'w' => 1,
                            'h' => 1,
                        ],
                        7 => [
                            'i' => 'billing_address',
                            'x' => 0,
                            'y' => 3,
                            'w' => 1,
                            'h' => 1,
                        ],
                    ],
                ],
                '2columns' => [
                    'layout' => [
                        5 => [
                            'i' => 'email_address',
                            'x' => 0,
                            'y' => 1,
                            'w' => 1,
                            'h' => 1,
                        ],
                        6 => [
                            'i' => 'street_address',
                            'x' => 0,
                            'y' => 2,
                            'w' => 1,
                            'h' => 1,
                        ],
                        7 => [
                            'i' => 'billing_address',
                            'x' => 0,
                            'y' => 3,
                            'w' => 1,
                            'h' => 1,
                        ],
                    ],
                ],
                '3columns' => [
                    'layout' => [
                        5 => [
                            'i' => 'email_address',
                            'x' => 0,
                            'y' => 1,
                            'w' => 1,
                            'h' => 1,
                        ],
                        6 => [
                            'i' => 'street_address',
                            'x' => 0,
                            'y' => 2,
                            'w' => 1,
                            'h' => 1,
                        ],
                        7 => [
                            'i' => 'billing_address',
                            'x' => 0,
                            'y' => 3,
                            'w' => 1,
                            'h' => 1,
                        ],
                    ],
                ],
            ],
        ];
    }

    private function getUpdatedBlocks(): array
    {
        return [
            ['name' => 'email_address', 'title' => 'Email Address'],
            ['name' => 'street_address', 'title' => 'Street Address'],
            ['name' => 'billing_address', 'title' => 'Billing Address'],
        ];
    }

    private function filterDuplicates(array $blocksData): array
    {
        $newBlocks =  $this->getUpdatedBlocks();
        $searchOptions = ['haystack' => $blocksData, 'recursive' => true];
        $result = [];
        $validator = new \Laminas\Validator\InArray([$searchOptions]);
        foreach ($newBlocks as $block) {
            if (!$validator->isValid($block['name'])) {
                $result[] = $block;
            } 
        }
        $unique = array_map('unserialize', array_unique(array_map('serialize', $result)));

        return $unique;
    }
}
