<?php
/**
 * @category  Totaltools
 * @package   Totaltools_Checkout
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 © Totaltools. <https://www.totaltools.com.au>
 */

namespace Totaltools\Checkout\Setup;

use Magento\Framework\Setup\UpgradeSchemaInterface;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\SchemaSetupInterface;

class UpgradeSchema implements UpgradeSchemaInterface
{
    /**
     * Upgrade DB schema for the Shippit Shipping Module
     *
     * @param SchemaSetupInterface $setup
     * @param ModuleContextInterface $context
     * @return void
     */
    public function upgrade(SchemaSetupInterface $setup, ModuleContextInterface $context)
    {
        $installer = $setup;
        $installer->startSetup();

        if (version_compare($context->getVersion(), '1.0.2', '<')) {
            $this->addThirdPartyAttributes($setup);
        }

        $installer->endSetup();
    }

    /**
     * @param $installer
     */
    public function addThirdPartyAttributes($installer)
    {
        $installer->startSetup();

        $installer->getConnection()->addColumn(
            $installer->getTable('quote'),
            'third_party_pickup',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
                'nullable' => false,
                'default' => '0',
                'comment' => 'Third party pickup',
            ]
        );

        $installer->getConnection()->addColumn(
            $installer->getTable('quote'),
            'third_party_name',
            [
                'type' => 'text',
                'nullable' => true,
                'default' => null,
                'comment' => 'Third party name'
            ]
        );

        $installer->getConnection()->addColumn(
            $installer->getTable('sales_order'),
            'third_party_pickup',
            [
                'type' => \Magento\Framework\DB\Ddl\Table::TYPE_BOOLEAN,
                'nullable' => false,
                'default' => '0',
                'comment' => 'Third party pickup',
            ]
        );

        $installer->getConnection()->addColumn(
            $installer->getTable('sales_order'),
            'third_party_name',
            [
                'type' => 'text',
                'nullable' => true,
                'default' => null,
                'comment' => 'Third party name'
            ]
        );

        $installer->endSetup();
    }
}
