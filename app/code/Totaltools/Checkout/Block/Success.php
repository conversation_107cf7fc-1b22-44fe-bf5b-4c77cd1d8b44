<?php
namespace Totaltools\Checkout\Block;

use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Checkout\Block\Success as CheckoutSuccess;

class Success extends CheckoutSuccess
{
    const CONFIRMATION_PAGE_GIFTCARD = 'confirmation_page_giftcard';
    const CONFIRMATION_PAGE_DELIVERY_ORDER = 'confirmation_page_delivery_order';
    const CONFIRMATION_PAGE_CLICK_AND_COLLECT = 'confirmation_page_click_and_collect';
    
    
    protected $checkoutSession;

    public function __construct(
        Context $context,
        \Magento\Sales\Model\OrderFactory $orderFactory,
        CheckoutSession $checkoutSession,
        array $data = []
    ) {
        $this->checkoutSession = $checkoutSession;
        $this->_orderFactory = $orderFactory;
        parent::__construct($context, $orderFactory,$data);
    }

     public function getRealOrderId()
    {
        /** @var \Magento\Sales\Model\Order $order */
        $order = $this->_orderFactory->create()->load($this->getLastOrderId());
        return $order->getIncrementId();
    }

    public function getOrder()
    {
        return $this->checkoutSession->getLastRealOrder();
    }

    public function getOrderData()
    {
        return 'getOrderDatafunction';
    }

    public function getSuccessPageBlock()
    {
        $order = $this->getOrder();
        $orderId = $order->getId()??'';
        if ($order->getIsVirtual()) {
            return self::CONFIRMATION_PAGE_GIFTCARD;
        } else {
            $shippingMethod = $order->getShippingMethod();
            if ($shippingMethod) {
                if (strpos($shippingMethod, 'shippitcc_shippitcc') !== false) {
                    return self::CONFIRMATION_PAGE_CLICK_AND_COLLECT;
                } else {
                    return self::CONFIRMATION_PAGE_DELIVERY_ORDER;
                }
            }
        }
        return '';
    }
}
