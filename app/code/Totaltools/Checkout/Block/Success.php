<?php
namespace Totaltools\Checkout\Block;

use Magento\Checkout\Model\Session as CheckoutSession;
use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\Checkout\Block\Success as CheckoutSuccess;

class Success extends CheckoutSuccess
{
    const CONFIRMATION_PAGE_GIFTCARD = 'confirmation_page_giftcard';
    const CONFIRMATION_PAGE_DELIVERY_ORDER = 'confirmation_page_delivery_order';
    const CONFIRMATION_PAGE_CLICK_AND_COLLECT = 'confirmation_page_click_and_collect';


    protected $checkoutSession;
    protected $storeRepository;

    public function __construct(
        Context $context,
        \Magento\Sales\Model\OrderFactory $orderFactory,
        CheckoutSession $checkoutSession,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        array $data = []
    ) {
        $this->checkoutSession = $checkoutSession;
        $this->_orderFactory = $orderFactory;
        $this->storeRepository = $storeRepository;
        parent::__construct($context, $orderFactory,$data);
    }

     public function getRealOrderId()
    {
        /** @var \Magento\Sales\Model\Order $order */
        $order = $this->_orderFactory->create()->load($this->getLastOrderId());
        return $order->getIncrementId();
    }

    public function getOrder()
    {
        return $this->checkoutSession->getLastRealOrder();
    }

    public function getOrderData()
    {
        return 'getOrderDatafunction';
    }

    public function getSuccessPageBlock()
    {
        $order = $this->getOrder();
        $orderId = $order->getId()??'';
        if ($order->getIsVirtual()) {
            return self::CONFIRMATION_PAGE_GIFTCARD;
        } else {
            $shippingMethod = $order->getShippingMethod();
            if ($shippingMethod) {
                if (strpos($shippingMethod, 'shippitcc_shippitcc') !== false) {
                    return self::CONFIRMATION_PAGE_CLICK_AND_COLLECT;
                } else {
                    return self::CONFIRMATION_PAGE_DELIVERY_ORDER;
                }
            }
        }
        return '';
    }

    /**
     * Check if this is a click & collect order
     *
     * @return bool
     */
    public function isClickAndCollect()
    {
        $order = $this->getOrder();
        if ($order && $order->getId()) {
            $shippingMethod = $order->getShippingMethod();
            $storelocatorId = $order->getData('storelocator_id');

            return (strpos($shippingMethod, 'shippitcc_shippitcc') !== false || $storelocatorId);
        }
        return false;
    }

    /**
     * Check if this is a delivery order
     *
     * @return bool
     */
    public function isDelivery()
    {
        return !$this->isClickAndCollect();
    }

    /**
     * Get store data for click & collect orders
     *
     * @return \Totaltools\Storelocator\Model\Store|null
     */
    public function getStoreData()
    {
        if (!$this->isClickAndCollect()) {
            return null;
        }

        $order = $this->getOrder();
        $storelocatorId = $order->getData('storelocator_id');

        if ($storelocatorId) {
            try {
                $store = $this->storeRepository->getById($storelocatorId);
                if ($store && $store->getId()) {
                    return $store;
                }
            } catch (\Exception $e) {
                // Store not found, return null
            }
        }

        return null;
    }

    /**
     * Get customer shipping address for delivery orders
     *
     * @return \Magento\Sales\Model\Order\Address|null
     */
    public function getCustomerAddress()
    {
        if (!$this->isDelivery()) {
            return null;
        }

        $order = $this->getOrder();
        return $order ? $order->getShippingAddress() : null;
    }
}
