<?php

namespace Totaltools\Checkout\Block;

use Magento\Framework\View\Element\Template\Context;

class NearestStore extends \Magento\Framework\View\Element\Template
{
    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $checkoutSession;

    /**
     * NearestStore constructor.
     *
     * @param Context $context
     * @param \Magento\Checkout\Model\Session $checkoutSession
     * @param array $data
     */
    public function __construct(
        Context $context,
        \Magento\Checkout\Model\Session $checkoutSession,
        array $data = []
    ) {
        $this->checkoutSession = $checkoutSession;

        parent::__construct($context, $data);
    }

    /**
     * @return bool
     */
    public function checkForNearestStore()
    {
        $nearestStore = $this->checkoutSession->getData('neareststore_session');
        $nearestStoreCheck = $this->checkoutSession->getData('neareststore_check_session');
        return (!$nearestStore && !$nearestStoreCheck) ? true : false;
    }
}
