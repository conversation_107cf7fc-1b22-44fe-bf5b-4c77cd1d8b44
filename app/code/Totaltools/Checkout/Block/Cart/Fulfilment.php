<?php

namespace Totaltools\Checkout\Block\Cart;

/**
 * @category    Totaltools
 * @package     Totaltools_Checkout
 * <AUTHOR> Iqbal <<EMAIL>>
 * @copyright   (c) 2021 Totaltools. <https://totaltools.com.au>
 */

use Magento\Framework\View\Element\Template\Context;
use Totaltools\Geo\Model\GeoLocateModel;
use Totaltools\Storelocator\Helper\Data;
use Totaltools\Checkout\Helper\TotalToolsConfig;

class Fulfilment extends \Magento\Framework\View\Element\Template
{
    /**
     * @var GeoLocateModel
     */
    protected $geoLocateModel;

    /**
     * @var Data
     */
    protected $storeHelper;

    /**
     * @var TotalToolsConfig
     */
    protected $totalToolsConfig;

    /**
     * Fulfilment constructor.
     *
     * @param Context $context
     * @param GeoLocateModel $geoLocateModel
     * @param TotalToolsConfig $totalToolsConfig
     * @param Data $storeHelper
     * @param array $data
     */
    public function __construct(
        Context $context,
        GeoLocateModel $geoLocateModel,
        Data $storeHelper,
        TotalToolsConfig $totalToolsConfig,
        array $data = []
    ) {
        $this->geoLocateModel = $geoLocateModel;
        $this->storeHelper = $storeHelper;
        $this->totalToolsConfig = $totalToolsConfig;

        parent::__construct($context, $data);
    }

    /**
     * @return string
     */
    public function getGeoLocation()
    {
        return $this->geoLocateModel->getLocation(false, true);
    }

    /**
     * @return bool
     */
    public function cartHasOPOXLines()
    {
        return (bool) $this->storeHelper->cartHasOPOXLines();
    }

    /**
     * @return TotalToolsConfig
     */
    public function getCheckoutSettings()
    {
        return $this->totalToolsConfig;
    }
}
