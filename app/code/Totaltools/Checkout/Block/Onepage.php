<?php

namespace Totaltools\Checkout\Block;

use Magento\Framework\View\Element\Template\Context;
use Magento\Framework\Data\Form\FormKey;
use Magento\Checkout\Model\CompositeConfigProvider;
use Totaltools\Geo\Model\GeoLocateModel;
use Totaltools\Storelocator\Helper\Data;

/**
 * Onepage checkout block
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class Onepage extends \Magento\Checkout\Block\Onepage
{

    /**
     * @var GeoLocateModel
     */
    protected $_geoLocateModel;

    /**
     * @var Data
     */
    protected $_storeHelper;


    /**
     * Onepage constructor.
     * @param Context $context
     * @param FormKey $formKey
     * @param CompositeConfigProvider $configProvider
     * @param GeoLocateModel $geoLocateModel
     * @param Data $storeHelper
     * @param array $layoutProcessors
     * @param array $data
     */
    public function __construct(
        Context $context,
        FormKey $formKey,
        CompositeConfigProvider $configProvider,
        GeoLocateModel $geoLocateModel,
        Data $storeHelper,
        array $layoutProcessors = [],
        array $data = [])
    {
        $this->_geoLocateModel = $geoLocateModel;
        $this->_storeHelper = $storeHelper;
        parent::__construct($context, $formKey, $configProvider, $layoutProcessors, $data);
    }

    /**
     * @return string
     */
    public function getGeoLocation()
    {

        return $this->_geoLocateModel->getLocation(false, true);
    }

    /**
     * @return bool
     */
    public function cartHasOPOXLines()
    {
        return $this->_storeHelper->cartHasOPOXLines();
    }
}
