<?php

namespace Totaltools\Checkout\Controller\Location;

use Exception;
use Magento\Framework\App\Action\Action;
use Magento\Checkout\Model\Session;
use Magento\Framework\App\ResourceConnection;
use Totaltools\Postcodes\Model\PostCodeRepository;
use Totaltools\Checkout\Helper\ChangeLocation as ChangeLocationHelper;

class Nearest extends Action
{
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    private $resultJsonFactory;

    /**
     * @var \Magento\Checkout\Model\Session
     */
    protected $checkoutSession;

    /**
     * @var ResourceConnection
     */
    private $resourceConnection;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    private $_storeManager;

    /**
     * @var PostCodeRepository
     */
    private $_postCodeRepository;

    /**
     * @var ChangeLocationHelper
     */
    private $changeLocationHelper;


    /**
     * ChangeLocation constructor.
     *
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory
     * @param Session $checkoutSession
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param ResourceConnection $resourceConnection
     * @param PostCodeRepository $_postCodeRepository
     * @param ChangeLocationHelper $changeLocationHelper
     */
    public function __construct(
        \Psr\Log\LoggerInterface $logger,
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory,
        Session $checkoutSession,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Magento\Store\Model\StoreManagerInterface $storeManager,
        ResourceConnection $resourceConnection,
        PostCodeRepository $_postCodeRepository,
        ChangeLocationHelper $changeLocationHelper
    ) {
        parent::__construct($context);
        $this->logger = $logger;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->checkoutSession = $checkoutSession;
        $this->resourceConnection = $resourceConnection;
        $this->storeRepository = $storeRepository;
        $this->_storeManager = $storeManager;
        $this->_postCodeRepository = $_postCodeRepository;
        $this->changeLocationHelper = $changeLocationHelper;
    }

    /**
     * Execute controller
     *
     * @see \Magento\Framework\App\ActionInterface::execute()
     */
    public function execute()
    {
        $resultJson = $this->resultJsonFactory->create();
        $resultData = [];
        $nearestStore = '';
        $currentLatitude = $this->getRequest()->getParam('latitude');
        $currentLongitude = $this->getRequest()->getParam('longitude');
        $useCurrentLocation = $this->getRequest()->getParam('use_current_location');
        if (!$currentLatitude || !$currentLongitude) {
            return $resultJson->setData(['error' => 'Invalid latitude or longitude']);
        }
    
        $nearestPostLocation = $this->_postCodeRepository->getNearestPostcode($currentLatitude, $currentLongitude );
        $nearestPostCode = $nearestPostLocation->getPostcode();
        $resultData['nearestPostCode'] = $nearestPostCode;
      
        if($nearestPostCode) {
            try {
                $nearestStore = $this->storeRepository->getClickAndCollectStoreInZone($nearestPostCode);
            } catch (Exception $e) {
                $this->logger->debug($e->getMessage());
            }
        }
        if(!$nearestStore|| !$nearestStore->getStatus()  || !$nearestStore->getIsVisible()) {
            return $resultJson->setData($resultData);
        }

        if (null !== $nearestStore->getId()) {
            $storeId = $nearestStore->getId();
            $selectedStore = $this->storeRepository->getById($storeId);
            $resultData['store_name'] = $selectedStore->getStoreName();
            $resultData['store_url'] = $this->_storeManager->getStore()->getBaseUrl().$selectedStore->getRewriteRequestPath();
            $resultData['store_id'] = $storeId;
            $resultData['store'] = $selectedStore->getData();
            
            $locationParam = [
                'postcode' => $nearestPostLocation->getPostcode(),
                'city' => $nearestPostLocation->getLocality(),
                'country_id' => $nearestStore->getCountryId(),
                'region' => $nearestPostLocation->getState(),
                'store_id' => $nearestStore->getId(),
                'shippit_key' => $nearestStore->getShippitApiKey()
            ];
            $this->changeLocationHelper->setNearestLocation($locationParam);
            $resultData['location'] = $locationParam;
            $neareststoreSession['store_id'] = $storeId;
            $this->checkoutSession->setNeareststoreSession($neareststoreSession);

            $this->checkoutSession->setStorelocatorStore($storeId);
            $storepickup_session['store_id'] = $storeId;
            $this->checkoutSession->setData('storepickup_session', $storepickup_session);
            
            if ($useCurrentLocation) {
                $this->checkoutSession->setBrowserLocationSession(true);
            }
        }

        if ($storeId && in_array($storeId, [208, 407, 316, 501])) {
            /** @var \Psr\Log\LoggerInterface $logger */
            $logger = $this->_objectManager->create(\Psr\Log\LoggerInterface::class);
            $logger->critical('Erp ID found in Nearest Store Request: ' . $storeId, ['referer' => $this->_redirect->getRefererUrl()]);
        }

        return $resultJson->setData($resultData);
    }
}
