<?php

/**
 * Totaltools Checkout.
 *
 * @category  Mage
 *
 * <AUTHOR> <<EMAIL>>
 * @copyright 2019 Balance Internet Pty Ltd (https://www.balanceinternet.com.au)
 */

namespace Totaltools\Checkout\Controller\Checkout;

use Magento\Framework\App\Action\Action;
use Magento\Framework\Exception\NotFoundException;
use Totaltools\Storelocator\Model\Store;
use Totaltools\Storelocator\Model\Registry;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Company\Model\ResourceModel\Customer;

class Changelocation extends Action
{
    /**
     * @var \Magento\Framework\Controller\Result\JsonFactory
     */
    private $resultJsonFactory;

    /**
     * @var \Totaltools\Storelocator\Model\Registry
     */
    private $storeRegistry;

    /**
     * @var \Totaltools\Storelocator\Helper\Data
     */
    private $storelocatorHepler;

    /**
     * @var \Totaltools\Storelocator\Model\StoreInventoryService
     */
    private $storeInventoryService;

    /**
     * @var \Totaltools\Storelocator\Model\StoreRepository
     */
    private $storeRepository;

    /**
     * @var \Magento\Customer\Model\Session
     */
    private $customerSession;

    /**
     * @var \Magento\Company\Model\CustomerFactory
     */
    private $companyCustomerFactory;

    /**
     * @var \Magento\Directory\Model\RegionFactory
     */
    private $regionFactory;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    private $logger;

    /**
     * @var bool
     */
    private static $_shippingAllowed = true;

    /**
     * @var array
     */
    private static $_requiredProducts = [];

    /**
     * @var array
     */
    private $_items = [];
    /**
     * @var Magento\Company\Model\ResourceModel\Customer;
     */
    protected $companyCustomer;
    /**
     * ChangeLocation constructor.
     *
     * @param \Psr\Log\LoggerInterface $logger
     * @param \Magento\Framework\App\Action\Context $context
     * @param \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory
     * @param \Magento\Customer\Model\Session $customerSession
     * @param \Magento\Company\Model\CustomerFactory $companyCustomerFactory
     * @param \Totaltools\Storelocator\Model\Registry $storeRegistry
     * @param \Totaltools\Storelocator\Helper\Data $helper
     * @param \Totaltools\Storelocator\Model\StoreRepository $storeRepository
     * @param \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService
     * @param \Magento\Directory\Model\RegionFactory $regionFactory
     */
    public function __construct(
        \Psr\Log\LoggerInterface $logger,
        \Magento\Framework\App\Action\Context $context,
        \Magento\Framework\Controller\Result\JsonFactory $resultJsonFactory,
        \Magento\Customer\Model\Session $customerSession,
        \Magento\Company\Model\CustomerFactory $companyCustomerFactory,
        \Totaltools\Storelocator\Model\Registry $storeRegistry,
        \Totaltools\Storelocator\Helper\Data $helper,
        \Totaltools\Storelocator\Model\StoreRepository $storeRepository,
        \Totaltools\Storelocator\Model\StoreInventoryService $storeInventoryService,
        \Magento\Directory\Model\RegionFactory $regionFactory,
        Customer  $companyCustomer

    ) {
        parent::__construct($context);
        $this->logger = $logger;
        $this->resultJsonFactory = $resultJsonFactory;
        $this->customerSession = $customerSession;
        $this->companyCustomerFactory = $companyCustomerFactory;
        $this->storeRegistry = $storeRegistry;
        $this->storelocatorHepler = $helper;
        $this->storeRepository = $storeRepository;
        $this->storeInventoryService = $storeInventoryService;
        $this->regionFactory = $regionFactory;
        $this->companyCustomer = $companyCustomer;
    }

    /**
     * Execute controller
     *
     * @see \Magento\Framework\App\ActionInterface::execute()
     */
    public function execute()
    {
        $resultJson = $this->resultJsonFactory->create();

        try {
            $this->validateRequest();
        } catch (NotFoundException $notFoundException) {
            return $resultJson->setData($notFoundException->getMessage());
        }

        $content = json_decode($this->getRequest()->getContent());

        $postcode = $content->postcode ?? $this->getRequest()->getParam('postcode');
        $city = $content->city ?? $this->getRequest()->getParam('city');
        $countryId = $content->country_id ?? $this->getRequest()->getParam('country_id');
        $region = $content->region ?? $this->getRequest()->getParam('region');

        $fallbackStore = null;
        $isNotCompanyProcess = true;
        $firstStore = $this->storeRepository->getByPostcodeInZone($postcode);
        $this->filterProductByStockStatus();

        $customer = $this->customerSession->isLoggedIn() ? $this->customerSession->getCustomer() : null;
        if ($customer) {
            $b2bCustomer = $this->companyCustomer->getCompanyAttributesByCustomerId( $customer->getId());
            $currentCompanyIds = array_map('intval', array_keys($b2bCustomer));
            $b2bCustomer =  array_diff($currentCompanyIds, [0]);
            if (!empty($b2bCustomer)) {
                $isNotCompanyProcess = false;
            }
        }

        if ($firstStore && $isNotCompanyProcess === true) {
            /** @var \Totaltools\Storelocator\Model\Store $firstStore */
            $resultStore = $this->_checkStoreAvailability($firstStore);
        } elseif ($firstStore && $isNotCompanyProcess === false) {
            $resultStore = $firstStore;
        } else {
            $resultStore = $this->storeRepository->getByPostcodeInZone(
                \Totaltools\Storelocator\Model\Zone::DEFAULT_ZONE_POSTCODE
            );
        }

        $location = [];
        if ($resultStore) {
            $location = [
                'postcode' => $resultStore->getZipcode(),
                'city' => $resultStore->getCity(),
                'state' => $resultStore->getState(),
                'country_id' => $resultStore->getCountryId(),
            ];

            $locationGuest = [
                'postcode' => $postcode,
                'city' => $city,
                "region" => $region,
                'region_data' => $this->getRegion($region, $countryId),
                'state' => $region,
                'country_id' => $countryId,
            ];

            $this->storeRegistry->addStoreData([
                Registry::GUEST_LOCATION => $locationGuest,
                Registry::DELIVERY_LOCATION => $location,
                Registry::STORELOCATOR_STORE_ID => $resultStore->getId(),
                Registry::STORELOCATOR_API_KEY => $resultStore->getShippitApiKey()
            ]);
        }

        return $resultJson->setData([
            'location' => $location,
            'store_id' => $resultStore->getId()
        ]);
    }

    /**
     * @param $items
     *
     * @return $this
     */
    public function setItems($items)
    {
        $this->_items = $items;

        return $this;
    }

    /**
     * @return array
     */
    public function getItems()
    {
        return $this->_items;
    }

    /**
     * Check product types action
     *
     * @return $this
     */
    public function filterProductByStockStatus()
    {
        $this->storeRegistry->addStoreData([
            Registry::OD_ITEM_EXISTED => 0,
            Registry::OX_ITEM_EXISTED => 0,
            Registry::OE_ITEM_EXISTED => 0,
        ]);

        $items = $this->getItems();
        if (empty($items)) {
            $items = $this->storeRegistry->getQuote()->getItems();
        }

        if (empty($items)) {
            return $this;
        }

        try {
            $stockAvailabilityValues = $this->storelocatorHepler->getStockAvailabilityAttributeValues();
        } catch (NoSuchEntityException $e) {
        }

        /** @var \Magento\Quote\Model\Quote\Item $item */
        foreach ($items as $item) {
            $product = $item->getProduct();
            $isDangerous = $product->getData(Store::SHIPPING_DANGEROUS_KEY);
            $isBackorderable = (bool) $product->getData(Store::IS_BACKORDERABLE);

            if ($isDangerous) {
                self::$_shippingAllowed = false;
                break;
            }

            $stockAvailabilityCodeId = $product->getData(Store::STOCK_AVAILABILITY_KEY);
            $stockAvailabilityCode = isset($stockAvailabilityValues[$stockAvailabilityCodeId]) ? $stockAvailabilityValues[$stockAvailabilityCodeId] : '';

            if (in_array($stockAvailabilityCode, Store::UNSHIPPABLE_PRODUCT_TYPES)) {
                self::$_shippingAllowed = false;
                break;
            }

            if ($stockAvailabilityCode == Store::STOCK_ON_DEMAND) {
                $this->storeRegistry->addStoreData([
                    Registry::OD_ITEM_EXISTED => 1,
                ]);
            } else if (!$isBackorderable) {
                $this->storeRegistry->addStoreData([
                    Registry::OX_ITEM_EXISTED => 1,
                ]);
            } else if ($stockAvailabilityCode == Store::TYPE_OE) {
                $this->storeRegistry->addStoreData([
                    Registry::OE_ITEM_EXISTED => 1,
                ]);
            }

            self::$_requiredProducts[$product->getSku()] = $item->getQty();
        }

        return $this;
    }

    /**
     * @param Store $store
     * @return bool|Store
     */
    protected function _checkStoreAvailability(\Totaltools\Storelocator\Model\Store $store)
    {
        if (!self::$_shippingAllowed || !self::$_requiredProducts) {
            return $store;
        }

        $hasInventory = $this->storeInventoryService->checkStoreInventory($store, self::$_requiredProducts);

        if ($hasInventory) {
            return $store;
        }

        $fallbackStoresCollection = $this->storeRepository->getFallbackStores($store->getId());

        foreach ($fallbackStoresCollection->getItems() as $fallbackStore) {
            /** @var \Totaltools\Storelocator\Model\Store $fallbackStore */
            $hasInventory = $this->storeInventoryService->checkStoreInventory($fallbackStore, self::$_requiredProducts);

            if ($hasInventory) {
                return $fallbackStore;
            }
        }

        /**
         * Reached here - store does not have stock & fallback stores also do not have stock
         */
        $storeDataInSession = $this->storeRegistry->getStoresData();
        if (array_key_exists(Registry::OX_ITEM_EXISTED, $storeDataInSession) && $storeDataInSession[Registry::OX_ITEM_EXISTED] === 1) {
            // If an OX item existed then return Support Office
            return $this->storeRepository->getByPostcodeInZone(\Totaltools\Storelocator\Model\Zone::DEFAULT_ZONE_POSTCODE);
        } else {
            // Return the first store if non of the fallback stores have stock
            return $store;
        }
    }

    /**
     * Validates request.
     *
     * @return void
     *
     * @throws NotFoundException
     */
    private function validateRequest()
    {
        if (
            !$this->getRequest()->isAjax()
            || !$this->getRequest()->isPost()
        ) {
            throw new NotFoundException(__('Request type is incorrect'));
        }
    }

    /**
     * Get region data if available from Directory data.
     *
     * @param string $regionCode
     * @param string $countryId
     * @return mixed
     */
    private function getRegion($regionCode, $countryId)
    {
        $region = $this->regionFactory->create()
            ->loadByCode($regionCode, $countryId);

        if ($region) {
            return [
                'region_id'     => (string) $region->getId(),
                'region_code'   => $regionCode,
                'region'        => $region->getName(),
            ];
        }

        return (object) [];
    }
}
