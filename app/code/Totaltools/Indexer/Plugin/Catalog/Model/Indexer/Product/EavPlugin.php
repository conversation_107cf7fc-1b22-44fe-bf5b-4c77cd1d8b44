<?php
namespace Totaltools\Indexer\Plugin\Catalog\Model\Indexer\Product;
use Psr\Log\LoggerInterface;
class EavPlugin
{
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * EavPlugin constructor.
     *
     * @param LoggerInterface $logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }
    /**
     * Log the $ids array before executing the method.
     *
     * @param \Magento\Catalog\Model\Indexer\Product\Eav $subject
     * @param array $ids
     * @return array
     */
    public function beforeExecute(
        \Magento\Catalog\Model\Indexer\Product\Eav $subject,
        $ids
    ) {
        // Log the product IDs before execution
        $this->logger->info('Executing EAV Indexer with IDs:', ['ids' => $ids]);
        return [$ids]; // Always return the parameters as an array
    }
}