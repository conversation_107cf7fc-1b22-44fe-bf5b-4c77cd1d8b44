<?php
namespace Totaltools\Indexer\Plugin\Catalog\Model\Indexer;
use Psr\Log\LoggerInterface;
class AbstractIndexerPlugin
{
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * AbstractIndexerPlugin constructor.
     *
     * @param LoggerInterface $logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }
    /**
     * Log the $ids array before executing the method.
     *
     * @param \Magento\CatalogRule\Model\Indexer\AbstractIndexer $subject
     * @param array $ids
     * @return array
     */
    public function beforeExecute(
        \Magento\CatalogRule\Model\Indexer\AbstractIndexer $subject,
        $ids
    ) {
        // Log the IDs before execution
        $this->logger->info('Executing CatalogRule Indexer with IDs:', ['ids' => $ids]);
        return [$ids]; // Always return the parameters as an array
    }
}