<?php
namespace Totaltools\Indexer\Plugin\Catalog\Model\Indexer\Product;
use Psr\Log\LoggerInterface;
class PricePlugin
{
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * PricePlugin constructor.
     *
     * @param LoggerInterface $logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }
    /**
     * This method will run before the execute method of the original class.
     *
     * @param \Magento\Catalog\Model\Indexer\Product\Price $subject
     * @param array $ids
     * @return array
     */
    public function beforeExecute(
        \Magento\Catalog\Model\Indexer\Product\Price $subject,
        $ids
    ) {
        $this->logger->info('Executing Product Price Indexer with IDs:', ['ids' => $ids]);
        return [$ids]; 
    }
}