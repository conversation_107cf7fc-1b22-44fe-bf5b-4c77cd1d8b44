<?php
namespace Totaltools\Indexer\Plugin\Catalog\Model\Indexer\Category;
use Psr\Log\LoggerInterface;
class ProductPlugin
{
   /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * ProductPlugin constructor.
     *
     * @param LoggerInterface $logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }
    public function beforeExecute(
        \Magento\Catalog\Model\Indexer\Category\Product $subject,
        $ids
    ) {
       // Log the entity IDs before execution
       $this->logger->info('Executing Category/Product Indexer with IDs:', ['ids' => $ids]);
       return [$ids]; // Always return the parameters as an array
    }
  
}