<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\Catalog\Model\Indexer\Category\Product\Action\Full">
        <arguments>
            <argument name="batchRowsCount" xsi:type="number">30000</argument>
        </arguments>
    </type>
    <type name="Magento\Catalog\Model\Indexer\Category\Product">
        <plugin name="totaltools_catalog_indexer_category_product_plugin" type="Totaltools\Indexer\Plugin\Catalog\Model\Indexer\Category\ProductPlugin"/>
    </type>
    <type name="Magento\Catalog\Model\Indexer\Product\Price">
        <plugin name="totaltools_catalog_indexer_product_price_plugin" type="Totaltools\Indexer\Plugin\Catalog\Model\Indexer\Product\PricePlugin"/>
    </type>
    <type name="Magento\CatalogRule\Model\Indexer\AbstractIndexer">
        <plugin name="totaltools_catalogrule_abstractindexer_plugin" type="Totaltools\Indexer\Plugin\Catalog\Model\Indexer\AbstractIndexerPlugin"/>
    </type>
    <type name="Magento\Catalog\Model\Indexer\Product\Eav">
        <plugin name="totaltools_catalog_indexer_product_eav_plugin" type="Totaltools\Indexer\Plugin\Catalog\Model\Indexer\Product\EavPlugin"/>
    </type>
</config>
