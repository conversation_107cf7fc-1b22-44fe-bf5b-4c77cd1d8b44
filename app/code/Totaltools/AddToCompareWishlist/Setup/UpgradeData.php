<?php
namespace Totaltools\AddToCompareWishlist\Setup;

use Exception;
use Magento\Eav\Setup\EavSetupFactory;
use Magento\Catalog\Model\Product;
use Magento\Catalog\Setup\CategorySetupFactory;
use Magento\Eav\Model\Entity\Attribute\SetFactory;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Setup\ModuleContextInterface;
use Magento\Framework\Setup\ModuleDataSetupInterface;
use Magento\Framework\Setup\UpgradeDataInterface;
use Psr\Log\LoggerInterface;

class UpgradeData implements UpgradeDataInterface
{
    /**
     * EAV setup factory
     *
     * @var EavSetupFactory
     */
    private $eavSetupFactory;

    /**
     * @var Logger
     */
    private $logger;

    /**
     * Constructor
     *
     * @param EavSetupFactory $eavSetupFactory
     * @param CategorySetupFactory $categorySetupFactory
     * @param SetFactory $attributeSetFactory
     * @param ProductAttributes $attributeConfig
     * @param FBEHelper $helper
     * @param Logger $logger
     */
    public function __construct(
        EavSetupFactory $eavSetupFactory,
        CategorySetupFactory $categorySetupFactory,
        LoggerInterface $logger
    ) {
        $this->eavSetupFactory = $eavSetupFactory;
        $this->categorySetupFactory = $categorySetupFactory;
        $this->logger = $logger;
    }

   
    /**
     * {@inheritdoc}
     * @throws LocalizedException|\Zend_Validate_Exception
     */
    public function upgrade(
        ModuleDataSetupInterface $setup,
        ModuleContextInterface $context
    ) {
        $setup->startSetup();
        $eavSetup = $this->eavSetupFactory->create(['setup' => $setup]);
        // user can config if they want to sync a category or not
        if (version_compare($context->getVersion(), '1.0.0') < 0) {
            $attrCode = "cat_add_to_compare";
            $eavSetup->removeAttribute(Product::ENTITY, $attrCode);
            if (!$eavSetup->getAttributeId(Product::ENTITY, $attrCode)) {
                    $eavSetup->addAttribute(
                        \Magento\Catalog\Model\Category::ENTITY,
                        $attrCode,
                        [
                            'type'     => 'int',
                            'input'    => 'boolean',
                            'source'   => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                            'visible'  => true,
                            'default'  => "0",
                            'required' => false,
                            'global'   => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                            'group'    => 'Display Settings',
                        ]
                    );
            }
                //wishlist
                $attrCode = "cat_add_to_wishlist";
                $eavSetup->removeAttribute(Product::ENTITY, $attrCode);
                if (!$eavSetup->getAttributeId(Product::ENTITY, $attrCode)) {
                    $eavSetup->addAttribute(
                        \Magento\Catalog\Model\Category::ENTITY,
                        $attrCode,
                        [
                            'type'     => 'int',
                            'input'    => 'boolean',
                            'source'   => 'Magento\Eav\Model\Entity\Attribute\Source\Boolean',
                            'visible'  => true,
                            'default'  => "0",
                            'required' => false,
                            'global'   => \Magento\Eav\Model\Entity\Attribute\ScopedAttributeInterface::SCOPE_STORE,
                            'group'    => 'Display Settings',
                        ]
                    );
                }
        }

        $setup->endSetup();
    }
}