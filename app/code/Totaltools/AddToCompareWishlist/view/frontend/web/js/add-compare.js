/**
 * @category    Totaltools
 * @copyright   (c) 2023Totaltools. <https://totaltools.com.au>
 */
define(["jquery", "addToCartNotification"], function ($) {
    //get data
    $('body').on("change",".cat_add_to_comp",function(){
        var productId = $(this).attr("id");
        if ($(this).is(":checked")) {
            //add to compare list
            addToCompare(productId, "add");
        } else {
            //remove from compare list
            addToCompare(productId, "remove");
        }
      });
    /**
     * ajax add to compare
     * once checkbox checked add to compare.
     * @public
     */
    function addToCompare(productId, flg) {
        var url = "/addtocompare/index/index";
        var data = {
            productId: productId,
            flag: flg,
        };
        var posting = $.post(url, data);
        // Put the results in a div
        posting.done(function (data) {
            var objData = jQuery.parseJSON(data);
            var isType = objData.flg;
            var prodName = objData.product_name;
            var checkBoxId = objData.product_id;
            var mesgTxt='';
            if (isType == "remove") {
                mesgTxt = "You have Removed product " + prodName +' from Comparison List';
            }
            if (isType == "add") {
                mesgTxt = "You have added product " + prodName +' to Comparison List';
            }
            if (isType == "max_limit_exceeded") {
                mesgTxt = 'Maximum 4 products can be compared at one time';
                $('#'+checkBoxId).prop('checked', false);
            }
            options = {
                idleTimeout: 24 * 60 * 60 * 1000,
                notification: {
                    templateId: "#compare-reminder-template",
                    notificationsWrapper: "#add-cart-notifications",
                    btnText: "Comparison List",
                    title: "Comparison List",
                    btnUrl: "/catalog/product_compare/",
                    message:mesgTxt,
                },
            };
            $("<div/>").addToCartNotification({
                data: { name: " " },
                ...options.notification,
            });
        });
    }
});
