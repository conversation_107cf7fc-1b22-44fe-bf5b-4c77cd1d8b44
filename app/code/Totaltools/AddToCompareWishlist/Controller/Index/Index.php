<?php

/**
 * Copyright © 2023 tootaltools, Inc. All rights reserved.
 */

namespace Totaltools\AddToCompareWishlist\Controller\Index;

use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use Magento\Framework\Controller\Result\JsonFactory;
use Magento\Catalog\Model\Product\Compare\ListCompare;
use Magento\Catalog\Model\ProductFactory;
use Magento\Framework\Message\ManagerInterface;
use Magento\Catalog\Helper\Product\Compare;

class Index extends Action
{

    /**
     * @var ProductFactory
     */
    protected  $productloader;
    /**
     * @var ListCompare
     */
    protected $catalogProductCompareList;
    /**
     * @var ManagerInterface
     */
    protected $messageManager;

    /**
     * @var ProductFactory
     */
    protected  $helperCompare;

    public function __construct(
        Context $context,
        JsonHelper $jsonHelper,
        JsonFactory $jsonFactory,
        ListCompare $catalogProductCompareList,
        ProductFactory $productloader,
        ManagerInterface $messageManager,
        Compare $helperCompare

    ) {
        $this->jsonHelper = $jsonHelper;
        $this->jsonFactory = $jsonFactory;
        $this->productloader = $productloader;
        $this->catalogProductCompareList = $catalogProductCompareList;
        $this->messageManager = $messageManager;
        $this->helperCompare = $helperCompare;
        parent::__construct($context);
    }

    /**
     * Ajax call to add/remoe compare
     *
     * @return json
     * @throws \Exception
     */

    public function execute()
    {

        $resultPage = $this->resultFactory->create(ResultFactory::TYPE_PAGE);
        $response = $this->resultFactory->create(ResultFactory::TYPE_RAW);
        $productId = (int)$this->getRequest()->getParam('productId');
        $product = $this->productloader->create()->load($productId);
        $flg = $this->getRequest()->getParam('flag');
        if ($flg == 'add') {
            //check items in compare list
            $totalItem = count($this->helperCompare->getItemCollection());
            //only add 4 items at a time to compare list
            if ($totalItem < 4) {
                $this->catalogProductCompareList->addProduct($product);
            }else{
                $flg = 'max_limit_exceeded';
            }

        } else {
            $flg = 'remove';
            $this->catalogProductCompareList->removeProduct($product);
        }
        $response->setHeader('Content-type', 'text/plain');
        $productName = $product->getName();
        $productId =  $product->getId();
        $response->setContents(
            $this->jsonHelper->jsonEncode(
                [
                    'product_name' => $productName,
                    'product_id' => $productId,
                    'flg' => $flg
                ]
            )
        );
        return $response;
    }
}