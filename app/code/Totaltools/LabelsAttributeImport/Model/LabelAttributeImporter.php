<?php
/**
 * Import data in Label attributes.
 *
 * @package Totaltools_LabelsAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 (c) Totaltools. <https://totaltools.com.au>
 */
namespace Totaltools\LabelsAttributeImport\Model;

use Magento\Eav\Model\Entity\Attribute;
use Magento\Framework\App\Area;
use Magento\Framework\App\Config\ScopeConfigInterface;
use Magento\Framework\App\State;
use Magento\Framework\Mail\Template\TransportBuilder;
use Magento\Store\Model\ScopeInterface;
use Magento\Store\Model\Store;
use Totaltools\LabelsAttributeImport\Model\Eav\ProductLabelAttribute;

/**
 * Class LabelAttributeImporter
 * @package Totaltools\LabelsAttributeImport\Model
 */
class LabelAttributeImporter
{
    const XML_ALLOWED_LABEL_ATTRIBUTES = "labels_attribute_import/settings/allowed_attributes";
    const XML_LABEL_ATTRIBUTES_EMAIL_RECIPIENT = "labels_attribute_import/settings/email_recipient";

    /**
     * @var Attribute
     */
    private Attribute $eavAttribute;

    /**
     * @var ProductLabelAttribute
     */
    private ProductLabelAttribute $productLabelAttribute;

    /**
     * @var ScopeConfigInterface
     */
    private ScopeConfigInterface $scopeConfig;

    /**
     * @var TransportBuilder
     */
    private TransportBuilder $transportBuilder;

    /**
     * @var State
     */
    private State $state;

    /**
     * LabelAttributeImporter constructor.
     * @param Attribute $eavAttribute
     * @param ProductLabelAttribute $productLabelAttribute
     * @param ScopeConfigInterface $scopeConfig
     * @param TransportBuilder $transportBuilder
     * @param State $state
     */
    public function __construct(
        Attribute $eavAttribute,
        ProductLabelAttribute $productLabelAttribute,
        ScopeConfigInterface $scopeConfig,
        TransportBuilder $transportBuilder,
        State $state
    ) {
        $this->eavAttribute = $eavAttribute;
        $this->productLabelAttribute = $productLabelAttribute;
        $this->scopeConfig = $scopeConfig;
        $this->transportBuilder = $transportBuilder;
        $this->state = $state;
    }

    /**
     * @param string $productEav
     * @return int
     */
    public function getAttributeId(string $productEav)
    {
        return $this->eavAttribute->getIdByCode("catalog_product", $productEav);
    }

    /**
     * @return mixed
     */
    protected function getLabelAttributesImportEmailReceiver()
    {
        return $this->scopeConfig->getValue(
            self::XML_LABEL_ATTRIBUTES_EMAIL_RECIPIENT,
            ScopeInterface::SCOPE_STORE
        );
    }

    /**
     * @return array
     */
    public function getAllowedLabelImportAttributes()
    {
        $allowedAttributesArray = [];
        $allowedAttributes = $this->scopeConfig->getValue(
            self::XML_ALLOWED_LABEL_ATTRIBUTES,
            ScopeInterface::SCOPE_STORE
        );
        if ($allowedAttributes) {
            $allowedAttributesArray = explode("|", $allowedAttributes);
        }
        return $allowedAttributesArray;
    }

    public function import($importData)
    {
        try {
            $labelAttributes = [];
            $attributeIds = [];
            $allowedLabelAttributes = $this->getAllowedLabelImportAttributes();
            foreach ($allowedLabelAttributes as $allowedLabelAttribute) {
                $attributeId = $this->getAttributeId($allowedLabelAttribute);
                if ($attributeId) {
                    $attributeIds[] = $attributeId;
                    $labelAttributes[$allowedLabelAttribute] = $attributeId;
                }
            }

            if ($attributeIds && $importData) {
                $this->productLabelAttribute->executeImporter($attributeIds, $labelAttributes, $importData);
                $summary = $this->productLabelAttribute->getSummary();
                $this->sendNotification($this->getFormatedLogTrace($summary));
            }
        } catch (\Exception $e) {
            $this->sendNotification($e->getMessage());
        }
        return true;
    }

    public function validLabelAttributes()
    {
        $validLabelAttributes = [];
        $allowedLabelAttributes = $this->getAllowedLabelImportAttributes();
        foreach ($allowedLabelAttributes as $allowedLabelAttribute) {
            $attributeId = $this->getAttributeId($allowedLabelAttribute);
            if ($attributeId) {
                $validLabelAttributes[] = $allowedLabelAttribute;
            }
        }

        return $validLabelAttributes;
    }

    /**
     * @param $logTrace
     * @return string
     */
    public function getFormatedLogTrace($logTrace)
    {
        $trace = '<table>';
        foreach ($logTrace as $key => $info) {
            if ($info !== '') {
                $trace .= "<tr><td>" . $this->formatString($key) . ': ' . $info . "</td></tr>";
            }
        }
        $trace .= '</table>';
        return $trace;
    }

    /**
     * @param $message
     * @throws \Exception
     */
    public function sendNotification($message)
    {
        $recipient = $this->getLabelAttributesImportEmailReceiver();
        if ($recipient) {
            try {
                $dt = new \DateTime('NOW');
                $transport = [
                    'message' => $message,
                    'dateAndTime' => $dt->format('Y-m-d H:i:s'),
                    'subject' => 'Labels Attribute Import Report'
                ];

                $mailer = $this->transportBuilder
                        ->setTemplateIdentifier('label_attributes_import_email')
                        ->setTemplateOptions([
                            'area' => \Magento\Backend\App\Area\FrontNameResolver::AREA_CODE,
                            'store' => Store::DEFAULT_STORE_ID,
                        ])
                        ->setTemplateVars($transport)
                        ->setFrom('general')
                        ->addTo(trim($recipient));
                $transport = $mailer->getTransport();
                $transport->sendMessage();
            } catch (\Exception $exception) {
                throw $exception;
            }
        }
    }

    public function formatString(string $str): string
    {
        return ucwords(str_replace('_', ' ', $str));
    }
}
