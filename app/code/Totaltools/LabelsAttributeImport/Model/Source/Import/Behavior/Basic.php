<?php

namespace Totaltools\LabelsAttributeImport\Model\Source\Import\Behavior;

use Magento\ImportExport\Model\Import;

class Basic extends \Magento\ImportExport\Model\Source\Import\AbstractBehavior
{
    /**
     * @inheritdoc
     */
    public function toArray()
    {
        return [
            Import::BEHAVIOR_APPEND => __('Add/Update')
        ];
    }

    /**
     * @inheritdoc
     */
    public function getCode()
    {
        return 'labelbasic';
    }
}
