<?php
/**
 * Import data in Label attributes.
 *
 * @package Totaltools_LabelsAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 (c) Totaltools. <https://totaltools.com.au>
 */
namespace Totaltools\LabelsAttributeImport\Model\Eav;

use Exception;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\App\Config\ScopeConfigInterface;

/**
 * Class ProductLabelAttribute
 * @package Totaltools\LabelsAttributeImport\Model\Eav
 */
class ProductLabelAttribute
{
    const LABEL_DEFAULT_VALUE = 0;
    const XML_ALLOWED_LABEL_ATTRIBUTES = "labels_attribute_import/settings/allowed_attributes";
   
    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var AdapterInterface
     */
    private $connection;

    /**
     * @var int
     */
    protected $catalogProductEntityInt;

    /**
     * @var ScopeConfigInterface
     */
    private $scopeConfig;

    /**
     * @var array
     */
    protected $summary = [];

    /**
     * CatalogProductEntityInt constructor.
     * @param ResourceConnection $resourceConnection
     * @param ScopeConfigInterface $scopeConfig
     */
    public function __construct(
        ResourceConnection $resourceConnection,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
        $this->scopeConfig = $scopeConfig;
        $this->initialization();
    }

    protected function initialization()
    {
        $this->catalogProductEntityInt = $this->connection->getTableName("catalog_product_entity_int");
        $this->summary['total_product_updated'] = 0;
        $allowedLabelAttributes = $this->getAllowedLabelImportAttributes();
        foreach ($allowedLabelAttributes as $allowedLabelAttribute) {
            $this->summary[$allowedLabelAttribute] = 0;
        }
    }

    /**
     * @return bool
     */
    public function executeImporter($attributeIds, $labelAttributes, $importData) : bool
    {
        try {
            if (!empty($attributeIds) && !empty($labelAttributes) && !empty($importData)) {
                $skus = array_keys($importData);
                $this->connection->beginTransaction();
                $this->setDefaultValuesForLabelAttributes($attributeIds);
                $products = $this->getProductDataBySkus($skus);
        
                foreach ($importData as $sku => $data) {
                    $rowId = !empty($products[$sku]) ? $products[$sku] : 0;
                    if (!$rowId) {
                        continue;
                    }
                    $storeId = 0;
                    $value = 1;
                    foreach ($data as $attribute) {
                        $label = current($attribute);
                        $attributeId = $labelAttributes[$label];
                        // insert data for label attribute
                        $this->insertIntData($rowId, $value, $storeId, $attributeId);
                        $this->summary[$label] ++;
                    }
                    $this->summary['total_product_updated'] ++;
                }
                
                // save all changes.
                $this->connection->commit();
                $this->summary["success_message"] = "Labels Attribute Imported Successfully";
            } else {
                $this->summary["error_message"] = "No row effected. No attribute found";
            }
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->summary["error_message"] = $e->getMessage();
            return false;
        }

        return true;
    }

    /**
     * @param array $attributeIds
     */
    protected function setDefaultValuesForLabelAttributes(array $attributeIds)
    {
        $this->connection->update(
            $this->catalogProductEntityInt,
            ['value' => self::LABEL_DEFAULT_VALUE],
            ["attribute_id IN (?)" => $attributeIds]
        );
    }

    public function getProductDataBySkus(array $skus)
    {
        $products = [];
        $tableName = $this->resource->getTableName('catalog_product_entity');
        $this->connection = $this->resource->getConnection();
        $query = $this->connection->select()
            ->from($tableName, ['row_id', 'sku'])
            ->where('sku IN (?)', $skus);

        $records = $this->connection->fetchAll($query);

        foreach ($records as $record) {
            $sku = $record['sku'];
            $rowId = $record['row_id'];
            $products[$sku] = $rowId;
        }

        return $products;
    }
    
    /**
     * @param int $rowId
     * @param int $value
     * @param int $storeId
     * @param int $attributeId
     */
    protected function insertIntData(
        int $rowId,
        int $value,
        int $storeId,
        int $attributeId
    ) {
        $bind = [
            "attribute_id" => $attributeId,
            "store_id" => $storeId,
            "row_id" => $rowId,
            "value" => $value
        ];

        $this->connection->insertOnDuplicate($this->catalogProductEntityInt, $bind);
    }

    /**
     * @return array
     */
    public function getSummary() : array
    {
        return $this->summary;
    }

    /**
     * @return array
     */
    public function getAllowedLabelImportAttributes()
    {
        $allowedAttributesArray = [];
        $allowedAttributes = $this->scopeConfig->getValue(
            self::XML_ALLOWED_LABEL_ATTRIBUTES,
            \Magento\Store\Model\ScopeInterface::SCOPE_STORE
        );
        if ($allowedAttributes) {
            $allowedAttributesArray = explode("|", $allowedAttributes);
        }
        return $allowedAttributesArray;
    }
}
