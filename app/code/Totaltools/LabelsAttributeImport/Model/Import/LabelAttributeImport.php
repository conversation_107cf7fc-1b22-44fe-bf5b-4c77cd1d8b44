<?php

namespace Totaltools\LabelsAttributeImport\Model\Import;

/**
 * @package   Totaltools_LabelsAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 © Totaltools. <https://www.totaltools.com.au>
 */

use Exception;
use Magento\Framework\App\ObjectManager;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\Serialize\Serializer\Json;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Magento\Framework\Json\Helper\Data as JsonHelper;
use Magento\ImportExport\Helper\Data as ImportHelper;
use Magento\ImportExport\Model\Import;
use Magento\ImportExport\Model\Import\Entity\AbstractEntity;
use Magento\ImportExport\Model\Import\ErrorProcessing\ProcessingErrorAggregatorInterface;
use Magento\ImportExport\Model\ResourceModel\Helper;
use Magento\ImportExport\Model\ResourceModel\Import\Data;
use Totaltools\LabelsAttributeImport\Model\LabelAttributeImporter;
use Totaltools\PriceImport\Model\Import\Price;

/**
 * Class LabelAttributeImport
 */
class LabelAttributeImport extends AbstractEntity
{
    const COL_SKU = 'sku';
    const COL_LABEL_ATTRIBUTE = 'label_attribute';
    

    /**
     * If we should check column names
     */
    protected $needColumnCheck = false;

    /**
     * Need to log in import history
     */
    protected $logInHistory = true;

    /**
     * Permanent entity columns.
     */
    protected $_permanentAttributes = [self::COL_SKU];

    /**
     * @var AdapterInterface
     */
    protected $connection;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var LabelAttributeImporter
     */
    protected $labelAttributeImporter;

    /**
     * @var Price
     */
    protected $priceImportModel;

    /**
     * Json Serializer Instance
     *
     * @var Json
     */
    private $serializer;

    /**
     * @var array
     */
    private $_data = [];

    /**
     * Courses constructor.
     *
     * @param JsonHelper $jsonHelper
     * @param ImportHelper $importExportData
     * @param Data $importData
     * @param ResourceConnection $resource
     * @param Helper $resourceHelper
     * @param LabelAttributeImporter $labelAttributeImporter
     * @param Price $priceImportModel
     * @param ProcessingErrorAggregatorInterface $errorAggregator
     */
    public function __construct(
        JsonHelper $jsonHelper,
        ImportHelper $importExportData,
        Data $importData,
        ResourceConnection $resource,
        Helper $resourceHelper,
        LabelAttributeImporter $labelAttributeImporter,
        Price $priceImportModel,
        ProcessingErrorAggregatorInterface $errorAggregator
    ) {
        $this->jsonHelper = $jsonHelper;
        $this->_importExportData = $importExportData;
        $this->_resourceHelper = $resourceHelper;
        $this->_dataSourceModel = $importData;
        $this->resource = $resource;
        $this->connection = $resource->getConnection(ResourceConnection::DEFAULT_CONNECTION);
        $this->labelAttributeImporter = $labelAttributeImporter;
        $this->priceImportModel = $priceImportModel;
        $this->errorAggregator = $errorAggregator;
        $this->initMessageTemplates();
    }

    /**
     * Entity type code getter.
     *
     * @return string
     */
    public function getEntityTypeCode()
    {
        return 'label_attribute_import';
    }

    /**
     * Row validation
     *
     * @param array $rowData
     * @param int $rowNum
     *
     * @return bool
     */
    public function validateRow(array $rowData, $rowNum): bool
    {
        $sku = $rowData[self::COL_SKU] ?? '';
        $label_attribute = $rowData[self::COL_LABEL_ATTRIBUTE] ?? '';

        if (!$sku) {
            $this->addRowError('SkuIsRequired', $rowNum);
        }

        if (!$label_attribute) {
            $this->addRowError('LabelAttributeIsRequired', $rowNum);
        }

        if (isset($this->_validatedRows[$rowNum])) {
            return !$this->getErrorAggregator()->isRowInvalid($rowNum);
        }

        $this->_validatedRows[$rowNum] = true;

        return !$this->getErrorAggregator()->isRowInvalid($rowNum);
    }

    /**
     * Save Label entities
     * @param array $data
     * @return void
     */
    private function saveLabelEntity(array $data)
    {
        $behavior = $this->getBehavior();
        $validLabelAttributes = $this->labelAttributeImporter->validLabelAttributes();
        $entityList = [];

        foreach ($data as $rowNum => $row) {
            $rowId = $row[0];
            $rowLabel = $row[1];
            if (!in_array($rowLabel, $validLabelAttributes)) {
                continue;
            }
            $columnValues = [];
            $columnValues[] = $row[1];
            $entityList[$rowId][] = $columnValues;
        }

        if (Import::BEHAVIOR_APPEND === $behavior) {
            $this->labelAttributeImporter->import($entityList);
        }
    }

    /**
     * Get available columns
     *
     * @return array
     */
    private function getAvailableColumns(): array
    {
        return [self::COL_LABEL_ATTRIBUTE];
    }

    /**
     * Init Error Messages
     */
    private function initMessageTemplates()
    {
        $this->addMessageTemplate(
            'SkuIsRequired',
            __('SKU cannot be empty.')
        );

        $this->addMessageTemplate(
            'LabelAttributeIsRequired',
            __('Label Attribute cannot be empty.')
        );
    }

    /**
     * @inheritdoc
     */
    protected function _saveValidatedBunches()
    {
        $source = $this->_getSource();
        $currentDataSize = 0;
        $bunchRows = [];
        $startNewBunch = false;
        $nextRowBackup = [];
        $maxDataSize = $this->_resourceHelper->getMaxDataSize();
        $bunchSize = $this->_importExportData->getBunchSize();
        $skuSet = [];

        $source->rewind();

        while ($source->valid() || $bunchRows) {
            if ($startNewBunch || !$source->valid()) {
                $bunchRows = $nextRowBackup;
                $currentDataSize = strlen($this->getSerializer()->serialize($bunchRows));
                $startNewBunch = false;
                $nextRowBackup = [];
            }
            if ($source->valid()) {
                try {
                    $rowData = $source->current();
                    if (array_key_exists('sku', $rowData)) {
                        $skuSet[$rowData['sku']] = true;
                    }
                } catch (\InvalidArgumentException $e) {
                    $this->addRowError($e->getMessage(), $this->_processedRowsCount);
                    $this->_processedRowsCount++;
                    $source->next();
                    continue;
                }

                $this->_processedRowsCount++;

                if ($this->validateRow($rowData, $source->key())) {
                    // add row to bunch for save
                    $rowData = $this->_prepareRowForDb($rowData);
                    $rowSize = strlen($this->jsonHelper->jsonEncode($rowData));

                    $isBunchSizeExceeded = $bunchSize > 0 && count($bunchRows) >= $bunchSize;

                    if ($currentDataSize + $rowSize >= $maxDataSize || $isBunchSizeExceeded) {
                        $startNewBunch = true;
                        $nextRowBackup = [$source->key() => $rowData];
                    } else {
                        $bunchRows[$source->key()] = $rowData;
                        $currentDataSize += $rowSize;
                    }

                    array_push($this->_data, $rowData);
                }
                $source->next();
            }
        }
        $this->_processedEntitiesCount = (count($skuSet)) ?: $this->_processedRowsCount;

        if (!$this->getErrorAggregator()->getErrorsCount()) {
            $this->importSource($this->getData());
        }

        return $this;
    }

    /**
     * @inheritdoc
     */
    protected function _importData()
    {
        if (!$this->getErrorAggregator()->getErrorsCount()) {
            return true;
        }
        return false;
    }

    /**
     * Import data with relevant importer
     *
     * @param array $data
     * @return void
     */
    public function importSource(array $data)
    {
        $params = $this->getParameters();
        $behavior = $params['behavior'] ?? false;

        if (Import::BEHAVIOR_APPEND === $behavior) {
            $this->saveLabelEntity($data);
        }

        $this->priceImportModel->_moveFile($params);
    }

    /**
     * @return array
     */
    public function getData()
    {
        return $this->_data;
    }


    /**
     * Get Serializer instance
     *
     * Workaround. Only way to implement dependency and not to break inherited child classes
     *
     * @return Json
     */
    private function getSerializer()
    {
        if (null === $this->serializer) {
            $this->serializer = ObjectManager::getInstance()->get(Json::class);
        }
        return $this->serializer;
    }

    /**
     * @inheritdoc
     */
    protected function _prepareRowForDb(array $rowData)
    {
        return array_values($rowData);
    }
}
