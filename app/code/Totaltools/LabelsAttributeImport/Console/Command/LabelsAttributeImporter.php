<?php

/**
 * @package   Totaltools_LabelsAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2024 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\LabelsAttributeImport\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\ScheduledImportExport\Model\Scheduled\{Operation, OperationFactory};
use Magento\ScheduledImportExport\Model\ResourceModel\Scheduled\Operation\CollectionFactory as OperationCollectionFactory;
use Magento\Framework\App\{State, Area};
use Magento\Framework\Exception\LocalizedException;
use Magento\ImportExport\Model\Import;
use Totaltools\LabelsAttributeImport\Model\LabelAttributeImporter as Importer;

class LabelsAttributeImporter extends Command
{
    /**
     * @var State
     */
    protected $state;

    /**
     * @var OperationCollectionFactory
     */
    protected $operationCollectionFactory;

    /**
     * @var Importer
     */
    protected $importer;

    /**
     * LabelsAttributeImporter constructor.
     *
     * @param State $state
     * @param OperationCollectionFactory $operationCollectionFactory
     * @param Importer $importer
     * @param null $name
     */
    public function __construct(
        State $state,
        OperationCollectionFactory $operationCollectionFactory,
        Importer $importer,
        $name = null
    ) {
        parent::__construct($name);
        $this->state = $state;
        $this->operationCollectionFactory = $operationCollectionFactory;
        $this->importer = $importer;
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName("totaltools:product:labels-attribute-importer");
        $this->setDescription("Total tools labels attribute importer to update product labels");
        parent::configure();
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void|null
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        $output->writeln("Labels Attribute Import started\n");
        try {
            $this->state->setAreaCode(Area::AREA_ADMINHTML);

            $operation = $this->loadOperation();
            if ($operation instanceof Operation && $operation->getId()) {
                $operation->run();
            } else {
                throw new LocalizedException(__("No operation exists for given entity (" . 'label_attribute_import' .") and behavior (" . Import::BEHAVIOR_APPEND . ")."));
            }
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
            $this->sendNotification($operation, $e->getMessage());
            return \Magento\Framework\Console\Cli::RETURN_FAILURE;
        }
        $output->writeln("Labels Attribute Import finished");
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }

    /**
     * @return Operation|null
     */
    protected function loadOperation()
    {
        return $this->operationCollectionFactory->create()
            ->addFieldToFilter('entity_type', 'label_attribute_import')
            ->addFieldToFilter('behavior', Import::BEHAVIOR_APPEND)
            ->getFirstItem();
    }

    /**
     * @param Operation $operation
     * @param string $message
     * @return void
     */
    protected function sendNotification(Operation $operation, $message)
    {
        if (!$operation->getId()) {
            return $this->importer->sendNotification($message);
        }

        return $operation->sendEmailNotification(['trace' => $message]);
    }
}
