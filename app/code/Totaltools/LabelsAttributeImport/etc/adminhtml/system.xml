<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="labels_attribute_import" translate="label" sortOrder="120" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Labels Attribute Import</label>
            <tab>catalog</tab>
            <resource>Totaltools_LabelsAttributeImport::config_attributeimport</resource>
            <group id="settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Labels Attribute Import Settings</label>
                <field id="email_recipient" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Label Attributes Report Email Recipient</label>
                </field>
                <field id="allowed_attributes" translate="label comment" type="text" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="0">
                    <label>Allowed Product Attributes</label>
                    <comment>Separate each By |</comment>
                </field>
            </group>
        </section>
    </system>
</config>