<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\InstantPurchase\Rewrite\Magento\InstantPurchase\CustomerData;

use Magento\Customer\Model\Session;
use Magento\InstantPurchase\Model\InstantPurchaseInterface as InstantPurchaseModel;
use Magento\InstantPurchase\Model\Ui\CustomerAddressesFormatter;
use Magento\InstantPurchase\Model\Ui\PaymentTokenFormatter;
use Magento\InstantPurchase\Model\Ui\ShippingMethodFormatter;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Customer\Model\Address\Config ;


/**
 * Instant Purchase private customer data source.
 *
 * Contains all required data to perform instance purchase:
 *  - payment method
 *  - shipping address
 *  - billing address
 *  - shipping method
 */

class InstantPurchase extends \Magento\InstantPurchase\CustomerData\InstantPurchase
{

    /**
     * @var Session
     */
    private $customerSession;

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var InstantPurchaseModel
     */
    private $instantPurchase;

    /**
     * @var PaymentTokenFormatter
     */
    private $paymentTokenFormatter;

    /**
     * @var CustomerAddressesFormatter
     */
    private $customerAddressesFormatter;

    /**
     * @var ShippingMethodFormatter
     */
    private $shippingMethodFormatter;
    /**
     * @var Config
     */
    private $addressConfig;

    /**
     * InstantPurchase constructor.
     * @param Session $customerSession
     * @param StoreManagerInterface $storeManager
     * @param InstantPurchaseModel $instantPurchase
     * @param PaymentTokenFormatter $paymentTokenFormatter
     * @param CustomerAddressesFormatter $customerAddressesFormatter
     * @param ShippingMethodFormatter $shippingMethodFormatter
     */
    public function __construct(
        Session $customerSession,
        StoreManagerInterface $storeManager,
        InstantPurchaseModel $instantPurchase,
        PaymentTokenFormatter $paymentTokenFormatter,
        CustomerAddressesFormatter $customerAddressesFormatter,
        ShippingMethodFormatter $shippingMethodFormatter,
        Config $addressConfig
    ) {
        $this->customerSession = $customerSession;
        $this->storeManager = $storeManager;
        $this->instantPurchase = $instantPurchase;
        $this->paymentTokenFormatter = $paymentTokenFormatter;
        $this->customerAddressesFormatter = $customerAddressesFormatter;
        $this->shippingMethodFormatter = $shippingMethodFormatter;
        $this->addressConfig = $addressConfig;
    }

    /**
     * @inheritdoc
     */
    public function getSectionData(): array
    {
        if (!$this->customerSession->isLoggedIn()) {
            return ['available' => false];
        }

        $store = $this->storeManager->getStore();
        $customer = $this->customerSession->getCustomer();
        $instantPurchaseOption = $this->instantPurchase->getOption($store, $customer);
        $data = [
            'available' => $instantPurchaseOption->isAvailable()
        ];
        if (!$instantPurchaseOption->isAvailable()) {
            return $data;
        }

        $renderer = $this->addressConfig->getFormatByCode('html')->getRenderer();
        $paymentToken = $instantPurchaseOption->getPaymentToken();
        $shippingAddress = $instantPurchaseOption->getShippingAddress();
        $billingAddress = $instantPurchaseOption->getBillingAddress();
        $shippingMethod = $instantPurchaseOption->getShippingMethod();

        $data += [
           
            'paymentToken' => [
                'publicHash' => $paymentToken->getPublicHash(),
                'summary' => $this->paymentTokenFormatter->format($paymentToken),
            ],
            'shippingAddress' => [
                'id' => $shippingAddress->getId(),
                'summary' => $renderer->renderArray($shippingAddress),
            ],
            'billingAddress' => [
                'id' => $billingAddress->getId(),
                'summary' => $renderer->renderArray($billingAddress),
            ],
            'shippingMethod' => [
                'carrier' => $shippingMethod->getCarrierCode(),
                'method' => $shippingMethod->getMethodCode(),
                'summary' => $this->shippingMethodFormatter->format($shippingMethod),
            ]
        ];

        return $data;
    }
}

