<?xml version="1.0" ?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
	<preference for="Magento\InstantPurchase\CustomerData\InstantPurchase" type="Totaltools\InstantPurchase\Rewrite\Magento\InstantPurchase\CustomerData\InstantPurchase"/>
	<preference for="Magento\InstantPurchase\Model\ShippingMethodChoose\ShippingMethodChooserInterface" type="Totaltools\InstantPurchase\Rewrite\Magento\InstantPurchase\Model\ShippingMethodChoose\ShippingMethodChooser"/>
	<type name="Magento\InstantPurchase\Model\PlaceOrder">
        <plugin name="totaltools_instantPurchase_model_placeOrder"
                        type="Totaltools\InstantPurchase\Plugin\Model\PlaceOrder" />
    </type>
    <preference for="Totaltools\InstantPurchase\Api\AvailableDeliveryMethodManagementInterface" type="Totaltools\InstantPurchase\Model\AvailableDeliveryMethodManagement"/>
    <preference for="Magento\InstantPurchase\Controller\Button\PlaceOrder" type="Totaltools\InstantPurchase\Controller\Button\PlaceOrder"/>
    <preference for="Magento\InstantPurchase\Block\Button" type="Totaltools\InstantPurchase\Block\Button"/>

    <type name="Magento\InstantPurchase\Model\QuoteManagement\ShippingConfiguration">
        <plugin name="totaltools_instantPurchase_model_placeOrder"
                        type="Totaltools\InstantPurchase\Plugin\Model\QuoteManagement\ShippingConfiguration" />
    </type>

    <type name="Magento\InstantPurchase\Model\InstantPurchaseOptionLoadingFactory">
        <plugin name="totaltools_instantPurchase_model_placeOrder"
                        type="Totaltools\InstantPurchase\Plugin\Magento\InstantPurchase\Model\InstantPurchaseOptionLoadingFactory" />
    </type>
</config>
