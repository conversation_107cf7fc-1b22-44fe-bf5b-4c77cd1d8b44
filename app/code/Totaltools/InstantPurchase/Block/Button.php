<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\InstantPurchase\Block;

use Magento\Framework\View\Element\Template;
use Magento\Framework\View\Element\Template\Context;
use Magento\InstantPurchase\Model\Config;

/**
 * Configuration for JavaScript instant purchase button component.
 *
 * @api
 * @since 100.2.0
 */
class Button extends \Magento\InstantPurchase\Block\Button
{
     /**
     * @var \Magento\ReCaptchaUi\Model\IsCaptchaEnabledInterface
     */
    private $captchaUiConfigResolver;

     /**
     * @var \Magento\ReCaptchaUi\Model\UiConfigResolverInterface
     */
    private $isCaptchaEnabled;

    /**
     * @var \Magento\Framework\Module\Manager
     */
    private $moduleManager;


    /**
     * Button constructor.
     * @param Context $context
     * @param Config $instantPurchaseConfig
     * @param array $data
     */
    public function __construct(
        Context $context,
        Config $instantPurchaseConfig,
        \Magento\Framework\Module\Manager $moduleManager,
        array $data = []
    ) {
        parent::__construct($context,$instantPurchaseConfig, $data);
        $this->moduleManager = $moduleManager;
    }

 

    /**
     * @inheritdoc
     * @since 100.2.0
     */
    public function getJsLayout(): string
    {
        if ($this->moduleManager->isEnabled('Magento_ReCaptchaUi')) {
            $objectManager = \Magento\Framework\App\ObjectManager::getInstance();
            $this->isCaptchaEnabled = $objectManager->create(
                'Magento\ReCaptchaUi\Model\IsCaptchaEnabledInterface'
            );
            $this->captchaUiConfigResolver = $objectManager->create(
                'Magento\ReCaptchaUi\Model\UiConfigResolverInterface'
            );
        } else {
            return parent::getJsLayout();
        }

        $key = 'braintree';

        if ($this->isCaptchaEnabled->isCaptchaEnabledFor($key)) {
            $this->jsLayout['components']['instant-purchase']['children']['braintree-recaptcha']['children']
            ['recaptcha_braintree']['settings']= $this->captchaUiConfigResolver->get($key);
        } else {
            if (isset( $this->jsLayout['components']['instant-purchase']['children']['braintree-recaptcha']['children']
            ['recaptcha_braintree'])) {
                unset( $this->jsLayout['components']['instant-purchase']['children']['braintree-recaptcha']['children']
                ['recaptcha_braintree']);
            }
        }
        return parent::getJsLayout();
    }
}
