<?php
/**
 * Copyright ©  All rights reserved.
 * See COPYING.txt for license details.
 */
declare(strict_types=1);

namespace Totaltools\InstantPurchase\Api;

use Magento\Quote\Api\Data\AddressInterface;

interface AvailableDeliveryMethodManagementInterface
{

    /**
     * GET for availableDeliveryMethod api
     * @param string $productId
     * @param string $idType
     * @param string $addressId
     * @param string $storeLocatorId
     * @return \Magento\Quote\Api\Data\ShippingMethodInterface[] An array of shipping methods
     */
    public function getAvailableDeliveryMethod($productId, $idType, $addressId, $storeLocatorId);
}
