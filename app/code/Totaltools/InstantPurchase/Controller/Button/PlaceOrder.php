<?php
namespace Totaltools\InstantPurchase\Controller\Button;

use Exception;
use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Customer\Model\Session;
use Magento\Framework\App\Action\Action;
use Magento\Framework\App\Action\Context;
use Magento\Framework\App\RequestInterface;
use Magento\Framework\Controller\Result\Json as JsonResult;
use Magento\Framework\Controller\ResultFactory;
use Magento\Framework\Data\Form\FormKey\Validator as FormKeyValidator;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\InstantPurchase\Model\InstantPurchaseOptionLoadingFactory;
use Magento\InstantPurchase\Model\PlaceOrder as PlaceOrderModel;
use Magento\Sales\Api\OrderRepositoryInterface;
use Magento\Store\Model\StoreManagerInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Psr\Log\LoggerInterface;
use Totaltools\Storelocator\Helper\Data;

class PlaceOrder extends \Magento\InstantPurchase\Controller\Button\PlaceOrder
{
    const OUT_OF_STOCK_CODE = 5;
    /**
     * List of request params that handled by the controller.
     *
     * @var array
     */
    private static $knownRequestParams = [
        'form_key',
        'product',
        'instant_purchase_payment_token',
        'instant_purchase_shipping_address',
        'instant_purchase_billing_address',
    ];

    /**
     * @var StoreManagerInterface
     */
    private $storeManager;

    /**
     * @var Session
     */
    private $customerSession;

    /**
     * @var FormKeyValidator
     */
    private $formKeyValidator;

    /**
     * @var InstantPurchaseOptionLoadingFactory
     */
    private $instantPurchaseOptionLoadingFactory;

    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var PlaceOrderModel
     */
    private $placeOrder;

     /**
     * @var AddressRepositoryInterface
     */
    private $addressRepository;

    /**
     * @var OrderRepositoryInterface
     */
    private $orderRepository;

    /**
     * @param Context $context
     * @param StoreManagerInterface $storeManager
     * @param Session $customerSession
     * @param FormKeyValidator $formKeyValidator
     * @param InstantPurchaseOptionLoadingFactory $instantPurchaseOptionLoadingFactory
     * @param ProductRepositoryInterface $productRepository
     * @param PlaceOrderModel $placeOrder
     * @param OrderRepositoryInterface $orderRepository
     */
    public function __construct(
        Context $context,
        StoreManagerInterface $storeManager,
        Session $customerSession,
        FormKeyValidator $formKeyValidator,
        InstantPurchaseOptionLoadingFactory $instantPurchaseOptionLoadingFactory,
        ProductRepositoryInterface $productRepository,
        PlaceOrderModel $placeOrder,
        Data $data,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutData,
        AddressRepositoryInterface $addressRepository,
        OrderRepositoryInterface $orderRepository,
        LoggerInterface $logger
    ) {
        parent::__construct(
            $context, 
            $storeManager,
            $customerSession, 
            $formKeyValidator,
            $instantPurchaseOptionLoadingFactory,
            $productRepository,
            $placeOrder,
            $orderRepository
        );

        $this->storeManager = $storeManager;
        $this->customerSession = $customerSession;
        $this->formKeyValidator = $formKeyValidator;
        $this->instantPurchaseOptionLoadingFactory = $instantPurchaseOptionLoadingFactory;
        $this->productRepository = $productRepository;
        $this->placeOrder = $placeOrder;
        $this->orderRepository = $orderRepository;
        $this->storeLocatorHelper = $data;
        $this->logger = $logger;
        $this->addressRepository = $addressRepository;
        $this->storeLocatorCheckoutHelper = $checkoutData;
    }

    /**
     * Place an order for a customer.
     *
     * @return JsonResult
     */
    public function execute()
    {
        $request = $this->getRequest();
         $shippingAddressId = (int)$request->getParam('instant_purchase_shipping_address');
         $carrierCode = (string)$request->getParam('instant_purchase_carrier');
         $shippingMethodCode = (string)$request->getParam('instant_purchase_shipping');
         $productId = (int)$request->getParam('product');
         $product = $this->productRepository->getById($productId);
         $sku = $product->getSku();
         if($product->getTypeId() == \Magento\ConfigurableProduct\Model\Product\Type\Configurable::TYPE_CODE) {
            $selectedConfigurableOption = (int)$request->getParam('selected_configurable_option');
            $product = $this->productRepository->getById($selectedConfigurableOption);
            $sku = $product->getSku();
         }
         $address = $this->addressRepository->getById($shippingAddressId);
         $shippingMethod = (string)$carrierCode.'_'.(string)$shippingMethodCode;
         $storeLocatorId = (int)$request->getParam('storelocator_id');
         if ($shippingMethod === 'shippitcc_shippitcc') {
            $this->customerSession->setInstantPuchaseStoreLocatorId($storeLocatorId);
         } else {
            $this->customerSession->setInstantPuchaseStoreLocatorId(0);
          }
         
         $postCode = $address->getPostcode();
         $this->logger->debug('InstantPurchase: shippingAddressId= '.$shippingAddressId.' : carrierCode = '.$carrierCode.' : shippingMethodCode= '.$shippingMethodCode
         .' : productId ='. $productId.' : sku=  '.$sku.' : storeLocatorId= '.$storeLocatorId.' : postCode= '.$postCode);
         $stockAvailabilityMessage = $this->storeLocatorHelper->getStockAvailabilityMessage(
             $storeLocatorId, $shippingMethod, $sku , $postCode );
             $this->logger->debug('InstantPurchase: '.print_r($stockAvailabilityMessage, true));
         foreach($stockAvailabilityMessage as $stock) {
             if ($stock['code'] === self::OUT_OF_STOCK_CODE) {
                 
                 return $this->createResponse($stock['message']->getText(), false);
             }
         }
         

        return parent::execute();
    }

    /**
     * Creates error message without exposing error details.
     *
     * @return string
     */
    private function createGenericErrorMessage(): string
    {
        return (string)__('Something went wrong while processing your order. Please try again later.');
    }

    /**
     * Creates response with a operation status message.
     *
     * @param string $message
     * @param bool $successMessage
     * @return JsonResult
     */
    private function createResponse(string $message, bool $successMessage): JsonResult
    {
        /** @var JsonResult $result */
        $result = $this->resultFactory->create(ResultFactory::TYPE_JSON);
        $result->setData([
            'response' => $message,
            'status' => 'failed'
        ]);
        if ($successMessage) {
            $this->messageManager->addSuccessMessage($message);
        } else {
            $this->messageManager->addErrorMessage($message);
        }

        return $result;
    }
}