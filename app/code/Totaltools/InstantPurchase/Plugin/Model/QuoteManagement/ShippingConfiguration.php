<?php
/**
 * Copyright © Magento, Inc. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\InstantPurchase\Plugin\Model\QuoteManagement;

use Magento\Customer\Model\Session;
use Magento\Framework\Exception\LocalizedException;
use Magento\InstantPurchase\Model\ShippingMethodChoose\DeferredShippingMethodChooserInterface;
use Magento\Quote\Api\Data\ShippingMethodInterface;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Address;
use Magento\Checkout\Model\Session as CheckoutSession;

/**
 * Configure shipping method for instant purchase
 *
 * @api May be used for pluginization.
 * @since 100.2.0
 */
class ShippingConfiguration
{
    /**
     * @var Session
     */
    private $customerSession;

    /**
     * @var CheckoutSession
     */
    protected $_checkoutSession;

    /**
     * ShippingConfiguration constructor.
     * @param Session $customerSession
     * @param CheckoutSession $_checkoutSession
     */
    public function __construct(
        Session $customerSession,
        CheckoutSession $_checkoutSession
    ) {
        $this->customerSession = $customerSession;
        $this->_checkoutSession = $_checkoutSession;
    }

    /**
     * Sets shipping information to quote.
     *
     * @param Quote $quote
     * @param ShippingMethodInterface $shippingMethod
     * @return Quote
     * @throws LocalizedException if shipping can not be configured for a quote.
     * @since 100.2.0
     */
    public function afterConfigureShippingMethod(
        \Magento\InstantPurchase\Model\QuoteManagement\ShippingConfiguration $subject,
        Quote $result
    ): Quote {
        if ($result->isVirtual()) {
            return $result;
        }

        $shippingAddress = $result->getShippingAddress();
        $storeLocatorId = $this->customerSession->getInstantPuchaseStoreLocatorId();
        $shippingAddress->setStorelocatorId($storeLocatorId);
        $this->_checkoutSession->replaceQuote($result);
        return $result;
    }

   
}
