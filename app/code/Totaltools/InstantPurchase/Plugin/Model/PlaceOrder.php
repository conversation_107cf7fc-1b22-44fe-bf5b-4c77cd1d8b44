<?php
namespace Totaltools\InstantPurchase\Plugin\Model;

class PlaceOrder 
{
    /**
     * @var \Magento\Sales\Api\OrderRepositoryInterface
     */
    protected $orderRepository;

    /**
     * @var \Totaltools\Storelocator\Plugin\Model\Service\OrderService
     */
    protected $orderService;

    /**
     * Constructor
     *
     * @param \Magento\Sales\Api\OrderRepositoryInterface $orderRepository
     * @param \Totaltools\Storelocator\Plugin\Model\Service\OrderService $orderService
     */
     
    public function __construct(
        \Magento\Sales\Api\OrderRepositoryInterface $orderRepository,
        \Totaltools\Storelocator\Plugin\Model\Service\OrderService $orderService,
        \Magento\Checkout\Model\Session $checkoutSession
    ) {
        $this->orderRepository = $orderRepository;
        $this->orderService = $orderService;
        $this->checkoutSession = $checkoutSession;
    }
    public function afterPlaceOrder(\Magento\InstantPurchase\Model\PlaceOrder $subject, $result)
    {
        $orderId = $result;
        $order = $this->orderRepository->get($orderId);
        $order->setOrderSource('instant_purchase');
        $this->orderRepository->save($order);
        if ($order) {
            $this->checkoutSession->setLastOrderId($order->getId())
                ->setLastRealOrderId($order->getIncrementId())
                ->setLastOrderStatus($order->getStatus());
        }
        return $orderId;
    }
}