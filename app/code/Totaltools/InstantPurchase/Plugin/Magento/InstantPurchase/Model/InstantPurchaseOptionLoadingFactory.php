<?php
namespace Totaltools\InstantPurchase\Plugin\Magento\InstantPurchase\Model;

class InstantPurchaseOptionLoadingFactory {
    const CLICK_AND_COLLECT_CARRIER = 'shippitcc';

    public function beforeCreate(
        \Magento\InstantPurchase\Model\InstantPurchaseOptionLoadingFactory $subject,
        int $customerId,
        string $paymentTokenPublicHash,
        int $shippingAddressId,
        int $billingAddressId,
        string $carrierCode,
        string $shippingMethodCode
    ) {
        if ($carrierCode == self::CLICK_AND_COLLECT_CARRIER) {
            $shippingMethodCode = self::CLICK_AND_COLLECT_CARRIER;
        }
        $return = [$customerId, $paymentTokenPublicHash, $shippingAddressId,  $billingAddressId, $carrierCode, $shippingMethodCode];
        return $return;
    }

}