<?php
/**
 * AvailableDeliveryMethodManagement
 *
 * @category  Totaltools
 * @package   Totaltools_InstantPurchase
 * <AUTHOR> <<EMAIL>>
 * @copyright 2020 (c) Totaltools Pty Ltd. All rights reserved.
 * @link      https://www.totaltools.com.au
 */

namespace Totaltools\InstantPurchase\Model;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Customer\Api\AddressRepositoryInterface;
use Magento\Quote\Model\Quote\TotalsCollector;
use Magento\Framework\App\ObjectManager;
use Magento\Quote\Model\Cart\ShippingMethodConverter;
use Magento\Quote\Model\Quote;
use Magento\Quote\Model\Quote\Item;
use Totaltools\Checkout\Model\CheckoutApiModel;
use Totaltools\Storelocator\Helper\Data;


class AvailableDeliveryMethodManagement implements \Totaltools\InstantPurchase\Api\AvailableDeliveryMethodManagementInterface
{
    /**
     * @var string
     */
    const SHIPPING_DANGEROUS_KEY = 'shipping_dangerous';
    const CURRENCY_CODE = 'AUD';
    const OUT_OF_STOCK_CODE = 5;
    const PRODUCT_NOT_AVAILABLE = 2;
    const CLICK_AND_COLLECT_CODE = 'shippitcc';
    
    /**
     * @var \Magento\Quote\Model\Quote
     */
    protected $quote;

    protected $item;

    protected $dataProcessor;

    private $_isDangerous;
    private $product;
  

    /**
     * @var ProductRepositoryInterface
     */
    private $productRepository;

    /**
     * @var AddressRepositoryInterface
     */
    private $addressRepository;

    /**
     * @var CheckoutApiModel
     */
    protected $checkoutApiModel;

    /**
     * @var Data
     */
    protected $storeLocatorHelper;

    /**
     * @var \Magento\Framework\App\Config\ScopeConfigInterface
     */
    protected $scopeConfig;
    
    /**
     * __construct
     * 
     * @param ProductRepositoryInterface $productRepository,
     * @param AddressRepositoryInterface $addressRepository,
     * @param TotalsCollector $totalsCollector,
     * @param ShippingMethodConverter $converter,
     * @param Quote $quote,
     * @param Item $item
     * @param CheckoutApiModel $checkoutApiModel
     * @param Data $storeLocatorHelper
     * @param \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig
     * 
     * @return void
     */
    public function __construct(
        ProductRepositoryInterface $productRepository,
        AddressRepositoryInterface $addressRepository,
        TotalsCollector $totalsCollector,
        ShippingMethodConverter $converter,
        Quote $quote,
        Item $item,
        CheckoutApiModel $checkoutApiModel,
        Data $storeLocatorHelper,
        \Magento\Framework\App\Config\ScopeConfigInterface $scopeConfig,
        \Totaltools\Storelocator\Helper\Checkout\Data $checkoutData
    ) {
        $this->productRepository = $productRepository;
        $this->addressRepository = $addressRepository;
        $this->totalsCollector = $totalsCollector;
        $this->converter = $converter;
        $this->quote = $quote;
        $this->item = $item;
        $this->checkoutApiModel = $checkoutApiModel;
        $this->storeLocatorHelper = $storeLocatorHelper;
        $this->storeLocatorCheckoutHelper = $checkoutData;
        $this->scopeConfig = $scopeConfig;
    }
    
    /**
     * {@inheritdoc}
     */
    public function getAvailableDeliveryMethod($productId, $idType, $addressId, $storeLocatorId)
    {   
        if($idType == 'sku') {
            $this->product = $this->productRepository->get($productId);
        } else {
            $this->product = $this->productRepository->getById($productId);
        }        
        $this->_isDangerous = (bool) $this->product->getData(self::SHIPPING_DANGEROUS_KEY);
        $this->item->setProduct($this->product);
        $this->item->setQty(1);
        $address = $this->addressRepository->getById($addressId);
        $rates = $this->getShippingMethods($address, $storeLocatorId);
        
        return $rates;
    }


          
    /**
     * Get Shipping Methods
     *
     * @param  $address
     * @param  $storeLocatorId
     * @return void
     */
    private function getShippingMethods($address, $storeLocatorId)
    {
        $output=[];

        $this->quote->addItem($this->item);
        $sku = $this->item->getSku();
        $this->checkoutApiModel->setItems([$this->item]);
        $shippingAddress = $this->quote->getShippingAddress();
        $shippingAddress->addData($this->extractAddressData($address));
        if(!$storeLocatorId) {
            $this->checkoutApiModel
            ->changeLocation(
            $shippingAddress->getPostcode(),
            $shippingAddress->getCity(),
            $shippingAddress->getCountryId(),
            $shippingAddress->getRegion());
            $storeLocatorId = $this->storeLocatorCheckoutHelper->getSessionStoreId() ?? 0;
        }
        $shippingAddress->setCollectShippingRates(true);
        $this->totalsCollector->collectAddressTotals($this->quote, $shippingAddress);
        $shippingRates = $shippingAddress->getGroupedAllShippingRates();
        foreach ($shippingRates as $carrierRates) {
            foreach ($carrierRates as $rate) {
                $stockAvailability = $this->storeLocatorHelper->getStockAvailabilityMessage(
                    $storeLocatorId, $rate->getCode(), $sku , $shippingAddress->getPostcode()
                );
                $stockStatus = current($stockAvailability);
                
                if (
                    isset($stockStatus['code']) && 
                    ($stockStatus['code'] === self::OUT_OF_STOCK_CODE) || ($stockStatus['code'] === self::PRODUCT_NOT_AVAILABLE) 
                    ) {
                        continue;
                }
                if($this->_isDangerous && $rate->getCarrier()!= self::CLICK_AND_COLLECT_CODE) {
                    continue;
                }
                if (str_contains($rate->getMethod(), 'Priority')) {
                    continue;
                }
                $output[$rate->getMethod()] = $this->converter->modelToDataObject($rate, $this->quote->getQuoteCurrencyCode());
            }
        }
        return $this->getSortedShippingMethods($output); 
    }

    

     /**
     * Get transform address interface into Array
     *
     * @param \Magento\Framework\Api\ExtensibleDataInterface  $address
     * @return array
     */
    protected function extractAddressData($address)
    {
        $className = \Magento\Customer\Api\Data\AddressInterface::class;
        if ($address instanceof \Magento\Quote\Api\Data\AddressInterface) {
            $className = \Magento\Quote\Api\Data\AddressInterface::class;
        } elseif ($address instanceof \Magento\Quote\Api\Data\EstimateAddressInterface) {
            $className = \Magento\Quote\Api\Data\EstimateAddressInterface::class;
        }
        return $this->getDataObjectProcessor()->buildOutputDataArray(
            $address,
            $className
        );
    }

   
    /**
     * Gets the data object processor
     *
     * @return \Magento\Framework\Reflection\DataObjectProcessor
     */
    protected function getDataObjectProcessor()
    {
        if ($this->dataProcessor === null) {
            $this->dataProcessor = ObjectManager::getInstance()->get(\Magento\Framework\Reflection\DataObjectProcessor::class);
        }
        return $this->dataProcessor;
    }

    /**
     * @param $result
     * @return array
     */
    private function getSortedShippingMethods($result)
    {
        $methods = [];
        $methods['shippitcc'] = $this->getShippingMethodSortOrder('shippitcc');
        $methods['freeshipping'] = $this->getShippingMethodSortOrder('freeshipping');
        $methods['Standard'] = $this->getShippingMethodSortOrder('standard');
        $methods['Express'] = $this->getShippingMethodSortOrder('express');
        asort($methods);
        $sortedShippingMethods = [];
        foreach ($methods as $k => $method) {
            foreach ($result as $shippingMethod) {
                if (str_contains($shippingMethod->getMethodCode(), $k)) {
                    $sortedShippingMethods[] = $shippingMethod;
                }
            }
        }
        return $sortedShippingMethods;
    }

    /**
     * @param $methodCode
     * @return int
     */
    public function getShippingMethodSortOrder($methodCode)
    {
        return (int) $this->scopeConfig->getValue('checkout/shipping_methods_sort_order/' . $methodCode);
    }

}
