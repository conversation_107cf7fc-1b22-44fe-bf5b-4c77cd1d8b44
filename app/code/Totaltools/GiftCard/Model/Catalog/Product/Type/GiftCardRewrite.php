<?php
/**
 * Copyright © 2016 Magento. All rights reserved.
 * See COPYING.txt for license details.
 */
namespace Totaltools\GiftCard\Model\Catalog\Product\Type;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Framework\Pricing\PriceCurrencyInterface;
use Magento\Store\Model\Store;

/**
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class GiftCardRewrite extends \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard
{
    /**
     * Validate Gift Card product, determine and return its amount - add private function
     *
     * @param \Magento\Framework\DataObject $buyRequest
     * @param \Magento\Catalog\Model\Product $product
     * @param bool $processMode
     * @return mixed
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    private function _validate(\Magento\Framework\DataObject $buyRequest, $product, $processMode)
    {
        $isStrictProcessMode = $this->_isStrictProcessMode($processMode);
        $allowedAmounts = $this->_getAllowedAmounts($product);
        $allowOpen = $product->getAllowOpenAmount();
        $selectedAmount = $buyRequest->getGiftcardAmount();
        $customAmount = $this->_getCustomGiftcardAmount($buyRequest);
        $this->_checkFields($buyRequest, $product, $isStrictProcessMode);

        $amount = null;
        if (($selectedAmount == 'custom' || !$selectedAmount) && $allowOpen) {
            if ($customAmount <= 0 && $isStrictProcessMode) {
                throw new \Magento\Framework\Exception\LocalizedException(__('Please specify a gift card amount.'));
            }
            $amount = $this->_getAmountWithinConstraints($product, $customAmount, $isStrictProcessMode);
        } elseif (is_numeric($selectedAmount)) {
            if (in_array($selectedAmount, $allowedAmounts)) {
                $amount = $selectedAmount;
            }
        }

        $amount = $this->_getAmountFromAllowed($amount, $allowedAmounts);

        if ($isStrictProcessMode) {
            $this->_checkGiftcardFields($buyRequest, $this->isTypePhysical($product), $amount);
        }
        return $amount;
    }

    /**
     * Get allowed giftcard amounts
     *
     * @param \Magento\Catalog\Model\Product $product
     * @return array
     */
    protected function _getAllowedAmounts($product)
    {
        $allowedAmounts = [];
        foreach ($product->getGiftcardAmounts() as $value) {
            $allowedAmounts[] = $this->priceCurrency->round($value['website_value']);
        }
        $this->_giftcardAmounts = $allowedAmounts;
        return $this->_giftcardAmounts;
    }
}