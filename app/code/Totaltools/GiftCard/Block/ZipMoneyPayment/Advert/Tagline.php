<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_GiftCard
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2018, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\GiftCard\Block\ZipMoneyPayment\Advert;

class Tagline extends \Zip\ZipPayment\Block\Advert\Tagline
{

    /**
     * Render the block if needed
     *
     * @return string
     */
    protected function _toHtml()
    {
        if ($this->_configShow(static::WIDGET_TYPE, $this->getPageType()) && $this->validateProductType()) {
            return parent::_toHtml();
        }
        return '';
    }

    /**
     * @return bool
     */
    public function validateProductType()
    {
        $valid = true;
        $product = $this->_registry->registry('product');
        if ($product) {
            if ($product->getTypeId() == \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD
                && $product->getGiftcardType() === (string) \Magento\GiftCard\Model\Giftcard::TYPE_VIRTUAL)
            {
                return false;
            }
        }

        return $valid;
    }
}
