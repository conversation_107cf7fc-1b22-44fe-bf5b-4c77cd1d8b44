<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_GiftCard
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2018, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\GiftCard\Plugin\Payment\Model;

/**
 * @category Magestore
 * @package  Magestore_Storelocator
 * @module   Storelocator
 * <AUTHOR> Developer
 */
class MethodListPlugin
{

    /**
     * @var \Magento\Checkout\Model\Session
     */
    private $checkoutSession;


    /**
     * MethodListPlugin constructor.
     * @param \Magento\Checkout\Model\Session $checkoutSession
     */
    public function __construct(
        \Magento\Checkout\Model\Session $checkoutSession
    )
    {
        $this->checkoutSession = $checkoutSession;
    }


    /**
     * @param \Magento\Payment\Model\MethodList $subject
     * @param array $result
     * @return array
     */
    public function afterGetAvailableMethods(
        \Magento\Payment\Model\MethodList $subject,
        $result = []
    ) {
        if ($result) {
            foreach ($result as $key => $paymentMethod) {
                if ($paymentMethod->getCode() == \Zip\ZipPayment\Model\Config::METHOD_CODE
                    || $paymentMethod->getCode() == \Openpay\Payment\Model\CustomConfigProvider::METHOD_CODE ) {
                    if ($this->hasDigitalGiftCards()) {
                        // ** Quote has a digital (virtual) gift card
                        // ** Zipmoney payment method should not be shown
                        unset($result[$key]);
                    }
                }
            }
        }
        return $result;
    }

    /**
     * @return \Magento\Quote\Model\Quote
     */
    private function getQuote()
    {
        return $this->checkoutSession->getQuote();
    }

    /**
     * @return bool
     */
    private function hasDigitalGiftCards()
    {
        $result = false;

        if ($this->getQuote()->getItems()) {
            foreach ($this->getQuote()->getItems() as $item) {

                /** @var \Magento\Quote\Model\ResourceModel\Quote\Item $item */
                if ($product = $item->getProduct()) {

                    /** @var \Magento\Catalog\Model\Product $product */
                    if ($item->getProductType() == \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD
                        && $product->getGiftcardType() === (string) \Magento\GiftCard\Model\Giftcard::TYPE_VIRTUAL)
                    {
                        return true;
                    }
                }
            }
        }

        return $result;
    }
}
