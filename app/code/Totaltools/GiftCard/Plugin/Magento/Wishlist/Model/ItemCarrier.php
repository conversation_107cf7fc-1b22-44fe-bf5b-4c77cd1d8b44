<?php
/**
 * <AUTHOR> Internet
 * @package    Totaltools_GiftCard
 * <AUTHOR> Internet Team  <<EMAIL>>
 * @copyright  Copyright (c) 2018, Balance Internet  (http://www.balanceinternet.com.au/)
 */

namespace Totaltools\GiftCard\Plugin\Magento\Wishlist\Model;

/**
 * Class ItemCarrier
 * @package Totaltools\GiftCard\Plugin\Magento\Wishlist\Model
 */
class ItemCarrier extends \Magento\Wishlist\Model\ItemCarrier
{

    /**
     * Around plugin to validate wishlist addto cart function
     *
     * @param \Magento\Wishlist\Model\ItemCarrier $subject
     * @param \Closure $proceed
     * @param $wishlist
     * @param $qtys
     * @return mixed|string
     */
    public function aroundMoveAllToCart(
        \Magento\Wishlist\Model\ItemCarrier $subject,
        \Closure $proceed,
        $wishlist,
        $qtys
    ) {
        $collection = $wishlist->getItemCollection()->setVisibilityFilter();
        $virtual  = false;
        $physical = false;
        foreach ($collection as $item) {
            $productId = $item->getProduct()->getId();
            $product = $item->getProduct()->load($productId);
            if($this->validateVirtualProductType($product)) {
                $virtual = $this->validateVirtualProductType($product);
            }
            if($this->validatePhysicalProductType($product)) {
                $physical = $this->validatePhysicalProductType($product);
            }
        }
        $quoteItems = $subject->cart->getQuote()->getAllVisibleItems();
        foreach ($quoteItems as $item) {
            $product = $item->getProduct();
            if($this->validateVirtualProductType($product)) {
                $virtual = $this->validateVirtualProductType($product);
            }
            if($this->validatePhysicalProductType($product)) {
                $physical = $this->validatePhysicalProductType($product);
            }
        }

        if($virtual && $physical) {
            $isOwner = $wishlist->isOwner($subject->customerSession->getCustomerId());
            $messages[] = __('Sorry, you are unable to purchase a gift card with other items in your cart. Please place 2 separate orders for your gift card and other items.');
            if ($isOwner) {
                $indexUrl = $subject->helper->getListUrl($wishlist->getId());
            } else {
                $indexUrl = $subject->urlBuilder->getUrl('wishlist/shared', ['code' => $wishlist->getSharingCode()]);
            }
            if ($subject->cartHelper->getShouldRedirectToCart()) {
                $redirectUrl = $subject->cartHelper->getCartUrl();
            } elseif ($this->redirector->getRefererUrl()) {
                $redirectUrl = $subject->redirector->getRefererUrl();
            } else {
                $redirectUrl = $indexUrl;
            }

            if ($messages) {
                foreach ($messages as $message) {
                    $subject->messageManager->addWarning($message);
                }
                $redirectUrl = $indexUrl;
            }
            return $redirectUrl;
        } else {
            return $proceed($wishlist, $qtys);
        }
    }

    /**
     * Validate virtual items
     *
     * @param $product
     * @return bool
     */
    public function validateVirtualProductType($product)
    {
        $valid = false;
        if ($product) {
            if ($product->getTypeId() === \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD
                && $product->getGiftcardType() === (string) \Magento\GiftCard\Model\Giftcard::TYPE_VIRTUAL)
            {
                return true;
            }
        }

        return $valid;
    }

    /**
     * Validate physical items
     *
     * @param $product
     * @return bool
     */
    public function validatePhysicalProductType($product)
    {
        $valid = false;
        if ($product) {
            if ($product->getTypeId() == \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD
                && $product->getGiftcardType() !== (string) \Magento\GiftCard\Model\Giftcard::TYPE_VIRTUAL)
            {
                return true;
            } elseif ($product->getTypeId() != \Magento\GiftCard\Model\Catalog\Product\Type\Giftcard::TYPE_GIFTCARD){
                return true;
            }
        }

        return $valid;
    }
}

