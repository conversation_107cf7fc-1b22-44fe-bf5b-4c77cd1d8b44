<?php

/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\ProntoAttributeImport\Console\Command;

use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Magento\ScheduledImportExport\Model\Scheduled\{Operation, OperationFactory};
use Magento\ScheduledImportExport\Model\ResourceModel\Scheduled\Operation\CollectionFactory as OperationCollectionFactory;
use Magento\Framework\App\{State, Area};
use Magento\Framework\Exception\LocalizedException;
use Totaltools\ProntoAttributeImport\Model\Import\Attribute as AttributeImport;
use Totaltools\ProntoAttributeImport\Model\Import\Behavior\Attribute as Behavior;
use Totaltools\PriceImport\Helper\Data;

class AttributeImporter extends Command
{
    /**
     * @var State
     */
    protected $state;

    /**
     * @var OperationCollectionFactory
     */
    protected $operationCollectionFactory;

    /**
     * @var Data
     */
    protected $helper;

    /**
     * ProductPriceImporter constructor.
     * 
     * @param State $state
     * @param OperationCollectionFactory $operationCollectionFactory
     * @param Data $helper
     * @param null $name
     */
    public function __construct(
        State $state,
        OperationCollectionFactory $operationCollectionFactory,
        Data $helper,
        $name = null
    ) {
        parent::__construct($name);
        $this->state = $state;
        $this->operationCollectionFactory = $operationCollectionFactory;
        $this->helper = $helper;
    }

    /**
     * @inheritdoc
     */
    protected function configure()
    {
        $this->setName("totaltools:product:attribute-importer");
        $this->setDescription("Total tools pronto attribute importer to update is saleable,is special ,backorderable, adds MOQ and sets manage stock to yes/no");
        parent::configure(); // TODO: Change the autogenerated stub
    }

    /**
     * @param InputInterface $input
     * @param OutputInterface $output
     * @return int|void|null
     */
    protected function execute(InputInterface $input, OutputInterface $output)
    {
        try {
            $this->state->setAreaCode(Area::AREA_FRONTEND);

            $operation = $this->loadOperation();
            if ($operation instanceof Operation && $operation->getId()) {
                $operation->run();
            } else {
                throw new LocalizedException(__("No operation exists for given entity (" . AttributeImport::ENTITY_TYPE_CODE .") and behavior (" . Behavior::BEHAVIOR_IMPORT_ATTRIBUTES . ")."));
            }
        } catch (\Exception $e) {
            $output->writeln($e->getMessage());
            $this->sendNotification($operation, $e->getMessage());
            return \Magento\Framework\Console\Cli::RETURN_FAILURE;
        }
        return \Magento\Framework\Console\Cli::RETURN_SUCCESS;
    }

    /**
     * @return Operation|null
     */
    protected function loadOperation()
    {
        return $this->operationCollectionFactory->create()
            ->addFieldToFilter('entity_type', AttributeImport::ENTITY_TYPE_CODE)
            ->addFieldToFilter('behavior', Behavior::BEHAVIOR_IMPORT_ATTRIBUTES)
            ->getFirstItem();
    }

    /**
     * @param Operation $operation
     * @param string $message
     * @return void
     */
    protected function sendNotification(Operation $operation, $message)
    {
        if (!$operation->getId()) {
            return $this->helper->sendEmailNotification(['trace' => $message]);
        }

        return $operation->sendEmailNotification(['trace' => $message]);
    }
}
