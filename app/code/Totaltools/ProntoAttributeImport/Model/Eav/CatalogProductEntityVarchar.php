<?php

/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\ProntoAttributeImport\Model\Eav;

use Exception;
use Magento\Framework\App\ResourceConnection;
use Magento\Framework\DB\Adapter\AdapterInterface;
use Totaltools\PriceImport\Helper\Data;
use Magento\Framework\App\Config\ScopeConfigInterface;

class CatalogProductEntityVarchar
{
    /**
     * @var array
     */
    protected $data;

    /**
     * @var ResourceConnection
     */
    private $resource;

    /**
     * @var AdapterInterface
     */
    private $connection;

    /**
     * @var array
     */
    protected $prontoAttribute;

    /**
     * @var string
     */
    protected $catalogProductEntity;

    /**
     * @var string
     */
    protected $catalogProductEntityVarchar;

    /**
     * @var string
     */
    protected $catalogInventoryStock;
    /**
     * @var string
     */
    protected $eavAttributeOptions;

    /**
     * @var string
     */
    protected $eavAttributeOptionValues;
    /**
     * @var string
     */
    protected $catalogProductEntityInt;

    /**
     * @var array
     */
    protected $summary = [];
    /**
     * @var array
     */
    protected $storeList;
    /**
     * @var int
     */
    protected $backorderAttributeId;

    /**
     * @var int
     */
    protected $specialAttributeId;
    /**
     * @var int
     */
    protected $notforsaleAttributeId;
    /**
     * @var int
     */
    protected $minsaleqtyAttributeId;
    /**
     * @var int
     */
    protected $ebproductAttributeId;

    /**
     * @var int
     */
    protected $stockavalibilityId;

    /**
     * @var array
     */
    protected $showSavedList;
    /**
     * @var int
     */
    protected $showSavedAttributeId;
    /**
     * @var Totaltools\PriceImport\Helper\Data
     */
    protected $helper;

    /**
     * @var array
     */
    protected $avalibleStockOptions;
    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;
    /**
     * @var int
     */
    protected $productStatusAttributeId;

    /**
     * Import attribute 
     */
    const IS_BACKORDER_ABLE = 'is_backorderable';
    const IS_SPECIAL_ORDER = 'is_a_special_order';
    const NOT_FOR_SALE = 'not_for_sale';
    const STOCK_AVALIBALITY_CODE = 'stock_availability_code';
    const MIN_SALE_QTY = 'min_sale_qty';
    const MANAGE_STOCK = 'manage_stock';
    const QUEENSLAND_STORE_CODE = "QLD";
    const PRONTO_QUEENSLAND_STORE_CODE = "QD";
    const EB_PRODUCT = "eb_product";
    const STORE_ID_WA = 4;
    const IS_N3 = 'N3';
    const PRODUCT_STATUS = "status";

    /**
     * CatalogProductEntityVarchar constructor.
     * @param ResourceConnection $resourceConnection
     */
    public function __construct(
        ResourceConnection $resourceConnection,
        Data $helper,
        ScopeConfigInterface $scopeConfig
    ) {
        $this->resource = $resourceConnection;
        $this->connection = $this->resource->getConnection();
        $this->helper = $helper;
        $this->scopeConfig = $scopeConfig;
        $this->initialization();
    }

    protected function initialization()
    {
        // get catalog product entity table name
        $this->catalogProductEntity = $this->connection->getTableName("catalog_product_entity");
        // get catalog product entity varchar table name
        $this->catalogProductEntityInt = $this->connection->getTableName("catalog_product_entity_int");
        //stock table
        $this->catalogInventoryStock = $this->connection->getTableName("cataloginventory_stock_item");
        //eav_attribute_option table
        $this->eavAttributeOptions = $this->connection->getTableName("eav_attribute_option");
        //eav_attribute_option_value table
        $this->eavAttributeOptionValues = $this->connection->getTableName("eav_attribute_option_value");


        $this->summary['totalShowSavedRows'] = 0;
    }

    /**
     * @param array $csvData
     */
    public function setAttribute(array $csvData)
    {
        $this->prontoAttribute = $csvData;
    }
    /**
     * @param array 
     */
    public function getAttribute()
    {
        return $this->prontoAttribute;
    }
    /**
     * @return array
     */
    public function getShowSavedList()
    {
        return $this->showSavedList;
    }

    /**
     * @param array $storeList
     */
    public function setStoreList(array $storeList)
    {
        $this->storeList = $storeList;
    }
    /**
     * @param int $showSavedAttributeId
     */
    public function setShowSavedAttributeId(int $showSavedAttributeId)
    {
        $this->showSavedAttributeId = $showSavedAttributeId;
    }
    /**
     * @return array
     */
    public function getStoreList()
    {
        return $this->storeList;
    }
    /**
     * @param int $backorderAttributeId
     */
    public function setBackorderableAttributeId(int $backorderAttributeId)
    {
        $this->backorderAttributeId = $backorderAttributeId;
    }

    /**
     * @return int
     */
    public function getBackorderableAttributeId()
    {
        return $this->backorderAttributeId;
    }

    /**
     * @param int $specialAttributeId
     */
    public function setSpecialAttributeId(int $specialAttributeId)
    {
        $this->specialAttributeId = $specialAttributeId;
    }

    /**
     * @return int
     */
    public function getSpecialAttributeId()
    {
        return $this->specialAttributeId;
    }

    /**
     * @param int $notforsaleAttributeId
     */
    public function setNotforsaleAttributeId(int $notforsaleAttributeId)
    {
        $this->notforsaleAttributeId = $notforsaleAttributeId;
    }

    /**
     * @return int
     */
    public function getNotforsaleAttributeId()
    {
        return $this->notforsaleAttributeId;
    }

    /**
     * @param int $minsaleqtyAttributeId
     */
    public function setMinSaleQtyAttributeId(int $minsaleqtyAttributeId)
    {
        $this->minsaleqtyAttributeId = $minsaleqtyAttributeId;
    }
    /**
     * @param int $ebproductAttributeId
     */
    public function setEbProductAttributeId(int $ebproductAttributeId)
    {
        $this->ebproductAttributeId = $ebproductAttributeId;
    }
    /**
     * @param int $stockavalibilityId
     */
    public function setStockAvailabiltyAttributeId(int $stockavalibilityId)
    {
        $this->stockavalibilityId = $stockavalibilityId;
    }

    /**
     * @param int $stockavalibilityId
     */
    public function getStockAvailabiltyAttributeId()
    {
        return $this->stockavalibilityId;
    }
    /**
     * @param int 
     */
    public function getEbProductAttributeId()
    {
        return $this->ebproductAttributeId;
    }
    /**
     * @return int
     */
    public function getMinSaleQtyAttributeId()
    {
        return $this->minsaleqtyAttributeId;
    }

    /**
     * @param int $productStatusAttributeId
     */
    public function setProductStatusAttributeId(int $productStatusId)
    {
        $this->productStatusAttributeId = $productStatusId;
    }

    /**
     * @return bool
     */
    public function executeImporter(): bool
    {
        try {

            $this->connection->beginTransaction();
            //get all options of stockcode
            $stockAvlId = $this->stockavalibilityId;
            $avalibleStockOptions = $this->getStockAvalibilityOptions($stockAvlId);

            $disableNotForSaleProduct = $this->scopeConfig->getValue(
                'pronto_attribute_import/settings/enabled',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            );
            $deleteNotForSaleStoreBasedValues = $this->scopeConfig->getValue(
                'pronto_attribute_import/settings/delete',
                \Magento\Store\Model\ScopeInterface::SCOPE_STORE,
            );
            //remove store based not_for_sale values
            if ($deleteNotForSaleStoreBasedValues) {
                $this->deleteStoreBasedNotForSaleAttribute($this->getNotforsaleAttributeId());
            }
            if (!empty($this->getAttribute())) {
                foreach ($this->getAttribute() as $product) {
                    $productSku = $product[0];
                    $salesType = $product[2];
                    $stockCode = $product[3];
                    $stockGroup = $product[4];
                    $conditionCodeLength = strlen($product[1]);
                    $conditionCodeValue = $product[1];
                    $rowId = $this->getCatalogProductRowId($productSku);
                    //import logic
                    if (!empty($rowId)) {
                        //get stock avalibility code value
                        if ($stockCode != '') {
                            $key = array_search($stockCode, array_column((array) $avalibleStockOptions, 'value'));
                            $stockValue = $avalibleStockOptions[$key]['option_id'];
                            $this->insertAttribute($rowId, $stockValue, $this->stockavalibilityId);
                        }
                        if ($stockGroup == 'TT') {
                            $this->insertAttribute($rowId, 1, $this->ebproductAttributeId);
                        } else {
                            $this->insertAttribute($rowId, 0, $this->ebproductAttributeId);
                        }
                        //condition code
                        if ($conditionCodeValue == 'N' || $conditionCodeValue == 'P') {
                            //set is saleable to no
                            $this->insertAttribute($rowId, 1, $this->notforsaleAttributeId);
                            if ($disableNotForSaleProduct) {
                                //disable product
                                $this->insertAttribute($rowId, 2, $this->productStatusAttributeId);
                            }
                            //set is backorderable (NA/NO)
                            $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                            //set is is_a_special_order (NA/NO)
                            $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                        } elseif ($stockCode == '' || $stockCode == 'CM' || $stockCode == 'TO') {
                            //set is saleable to no 
                            $this->insertAttribute($rowId, 1, $this->notforsaleAttributeId);
                            if ($disableNotForSaleProduct) {
                                //disable product
                                $this->insertAttribute($rowId, 2, $this->productStatusAttributeId);
                            }
                            //set is backorderable (NA/NO) 
                            $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                            //set is is_a_special_order (NA/NO) 
                            $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                        } elseif ($stockCode == 'OL') {
                            //set is saleable to no
                            if ($salesType == '' && $conditionCodeLength == 0) {
                                //set is saleable to no 
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is backorderable (NA/NO) 
                                $this->insertAttribute($rowId, 1, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO) 
                                $this->insertAttribute($rowId, 1, $this->specialAttributeId);
                            } elseif ((($salesType != '' && $conditionCodeLength > 0) && ($salesType != self::IS_N3)) || (($salesType == '' && $conditionCodeLength > 0) && ($salesType != self::IS_N3))) {
                                //set is saleable to no 
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is backorderable (NA/NO) 
                                $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO) 
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            } elseif (($salesType == 'P') && $conditionCodeLength == 0) {
                                //set is saleable to no 
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is backorderable (NA/NO) 
                                $this->insertAttribute($rowId, 1, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO) 
                                $this->insertAttribute($rowId, 1, $this->specialAttributeId);
                            } elseif (($salesType == 'B') && $conditionCodeLength == 0) {
                                //set is saleable to no 
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is backorderable (NA/NO) 
                                $this->insertAttribute($rowId, 1, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO) 
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            } elseif (($salesType == self::IS_N3) && $conditionCodeLength == 0) {
                                //set is saleable to no
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is saleable to no in WA store
                                $this->insertAttribute($rowId, 1, $this->notforsaleAttributeId, self::STORE_ID_WA);
                                if ($disableNotForSaleProduct) {
                                    //disable product
                                    $this->insertAttribute($rowId, 2, $this->productStatusAttributeId, self::STORE_ID_WA);
                                }
                                //set is backorderable (Yes)
                                $this->insertAttribute($rowId, 1, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            } elseif (($salesType == self::IS_N3) && $conditionCodeLength > 0) {
                                //set is saleable to no
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is saleable to yes in WA store
                                $this->insertAttribute($rowId, 1, $this->notforsaleAttributeId, self::STORE_ID_WA);
                                if ($disableNotForSaleProduct) {
                                    //disable product
                                    $this->insertAttribute($rowId, 2, $this->productStatusAttributeId, self::STORE_ID_WA);
                                }
                                //set is backorderable (No)
                                $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            } elseif (($conditionCodeLength == 0)
                                && ($salesType == 'A'
                                    || $salesType == 'A1'
                                    || $salesType == 'A2'
                                    || $salesType == 'A3'
                                    || $salesType == 'A4'
                                    || $salesType == 'A7'
                                    || $salesType == 'A8'
                                    || $salesType == 'B1'
                                    || $salesType == 'B2'
                                    || $salesType == 'B3'
                                    || $salesType == 'B4'
                                    || $salesType == 'E')

                            ) {
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is backorderable (NA/NO)
                                $this->insertAttribute($rowId, 1, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            }
                        } elseif ($stockCode == 'OP') {
                            if (
                                $salesType == ''
                                || $salesType == 'A'
                                || $salesType == 'B'
                                || $salesType == 'E'
                                || $salesType == 'P'
                                || $salesType == 'A1'
                                || $salesType == 'A2'
                                || $salesType == 'A3'
                                || $salesType == 'A4'
                                || $salesType == 'A7'
                                || $salesType == 'A8'
                                || $salesType == 'B1'
                                || $salesType == 'B2'
                                || $salesType == 'B3'
                                || $salesType == 'B4'
                                || $salesType == 'D'
                                || $salesType == 'DT'
                            ) {
                                //set is saleable to no
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is backorderable (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            } elseif ($salesType == self::IS_N3) {
                                //set is saleable to no
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is saleable to no in WA store
                                $this->insertAttribute($rowId, 1, $this->notforsaleAttributeId, self::STORE_ID_WA);
                                if ($disableNotForSaleProduct) {
                                    //disable product
                                    $this->insertAttribute($rowId, 2, $this->productStatusAttributeId, self::STORE_ID_WA);
                                }
                                //set is backorderable (No)
                                $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            }
                        } elseif ($stockCode == 'OX' && $salesType == 'D') {
                            //set is saleable to no
                            $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                            //set is backorderable (NA/NO)
                            $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                            //set is is_a_special_order (NA/NO)
                            $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                        } elseif ($stockCode == 'OC') {
                            //special order case
                            if (
                                ($salesType == ''
                                    || $salesType == 'A'
                                    || $salesType == 'B'
                                    || $salesType == 'E'
                                    || $salesType == 'P'
                                    || $salesType == 'A1'
                                    || $salesType == 'A2'
                                    || $salesType == 'A3'
                                    || $salesType == 'A4'
                                    || $salesType == 'A7'
                                    || $salesType == 'A8'
                                    || $salesType == 'B1'
                                    || $salesType == 'B2'
                                    || $salesType == 'B3'
                                    || $salesType == 'B4')
                            ) {
                                //set is saleable to no 
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is backorderable (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            } elseif ($salesType == self::IS_N3) {
                                //set is saleable to yes
                                $this->insertAttribute($rowId, 0, $this->notforsaleAttributeId);
                                //set is saleable to no in WA store
                                $this->insertAttribute($rowId, 1, $this->notforsaleAttributeId, self::STORE_ID_WA);
                                if ($disableNotForSaleProduct) {
                                    //disable product
                                    $this->insertAttribute($rowId, 2, $this->productStatusAttributeId, self::STORE_ID_WA);
                                }
                                //set is backorderable (No)
                                $this->insertAttribute($rowId, 0, $this->backorderAttributeId);
                                //set is is_a_special_order (NA/NO)
                                $this->insertAttribute($rowId, 0, $this->specialAttributeId);
                            }
                        }
                        $this->summary['totalShowSavedRows']++;
                    }
                }
            }
            // save all changes.
            $this->connection->commit();
        } catch (Exception $e) {
            $this->connection->rollBack();
            $this->summary['error'] = $e->getMessage();
            return false;
        }

        return true;
    }

    /**
     * @param string $sku
     * @return string
     */
    protected function getCatalogProductId(string $sku)
    {
        $select = $this->connection->select()->from($this->catalogProductEntity, 'entity_id')->where('sku = :sku');
        $bind = [":sku" => (string)$sku];
        return $this->connection->fetchOne($select, $bind);
    }
    /**
     * @param string $sku
     * @return string
     */
    protected function getCatalogProductRowId(string $sku)
    {
        $select = $this->connection->select()->from($this->catalogProductEntity)->where('sku = :sku');
        $bind = [":sku" => (string)$sku];
        return $this->connection->fetchOne($select, $bind);
    }

    /**
     * @param int $rowId
     * @param int $attributeId
     * @param int $attributeValue
     */
    protected function updateAttribute(int $rowId, int $attributeId, $attributeValue)
    {
        $storeId = 0;
        $bind = ["value" => $attributeValue];
        $where = [
            "attribute_id = ?" => $attributeId,
            "store_id = ?" => $storeId,
            "row_id = ?" => $rowId
        ];
        $this->connection->update($this->catalogProductEntityInt, $bind, $where);
    }
    /**
     * @param int $productId
     * @param int $attributeValue
     */
    protected function updateStockMinSaleQtyAttribute(int $productId, $attributeValue)
    {
        //$storeId = 0;
        $bind = ["min_sale_qty" => $attributeValue, 'use_config_min_sale_qty' => 0];
        $where = [
            "product_id = ?" => $productId
        ];
        $this->connection->update($this->catalogInventoryStock, $bind, $where);
    }
    /**
     * @param int $attributeId
     * @param int $rowId
     */
    protected function deleteAttributeRecord(int $attributeId, int $rowId)
    {
        $storeId = 0;
        $this->connection->delete(
            $this->catalogProductEntityInt,
            ["row_id = ?" => $rowId, "attribute_id = ?" => $attributeId, "store_id = ?" => $storeId]
        );
    }

    /**
     * @param int $rowId
     * @param string $attributeValue
     * @param int $attributeId
     * @param int $storeId
     * @return int
     */
    protected function insertAttribute(int $rowId, int $attributeValue, int $attributeId, $storeId = 0)
    {
        $bind = [
            "attribute_id" => $attributeId,
            "store_id" => $storeId,
            "row_id" => $rowId,
            "value" => $attributeValue
        ];
        return $this->connection->insertOnDuplicate($this->catalogProductEntityInt, $bind);
    }
    /**
     * @param int $rowId
     * @param string $attributeValue
     * @param int $storeId
     * @param int $attributeId
     * @return int
     */
    protected function CheckAttributeValueIfExist(int $rowId, int $attributeId)
    {
        $select = $this->connection->select()
            ->from(
                ['c' => $this->catalogProductEntityInt],
                ['row_id']
            )
            ->where(
                "c.row_Id = :$rowId"
            )->where(
                "c.attribute_id = :$attributeId"
            )->where(
                "c.store_id = :0"
            );
        $bind = ['row_Id' => $rowId, 'attribute_id' => $attributeId, 'store_id' => 0];
        return $this->connection->fetchAll($select, $bind);
    }

    /**
     * @return array
     */
    public function getSummary(): array
    {
        return $this->summary;
    }

    /**
     * @param int $attributeId
     * @return array
     */

    protected function getStockAvalibilityOptions(int $attributeId)
    {
        $select = $this->connection->select()
            ->from(
                ['c' => $this->eavAttributeOptions],
                ['c.option_id', 'ov.value']
            )
            ->joinLeft(
                ['ov' => $this->eavAttributeOptionValues],
                "c.option_id = ov.option_id"
            )
            ->where(
                "c.attribute_id =?",
                $attributeId
            );
        return $this->connection->fetchAll($select);
    }
    /**
     * @param int $attributeId
     */
    protected function deleteStoreBasedNotForSaleAttribute(int $attributeId)
    {
        $this->connection->delete(
            $this->catalogProductEntityInt,
            ["attribute_id = ?" => $attributeId, "store_id > 0"]
        );
    }
}
