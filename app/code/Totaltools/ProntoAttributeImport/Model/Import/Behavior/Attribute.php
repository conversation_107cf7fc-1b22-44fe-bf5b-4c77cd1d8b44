<?php
/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\ProntoAttributeImport\Model\Import\Behavior;

/**
 * Import behavior source model
 *
 * @api
 */
class Attribute extends \Magento\ImportExport\Model\Source\Import\AbstractBehavior
{
     /**
     * Import behaviors
     */
    const BEHAVIOR_IMPORT_ATTRIBUTES = 'pronto_import';

    /**
     * {@inheritdoc}
     */
    public function toArray()
    {
        return [
            self::BEHAVIOR_IMPORT_ATTRIBUTES => __('Update')
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function getCode()
    {
        return 'prontoattributeimport';
    }
}
