<?php
/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */
namespace Totaltools\ProntoAttributeImport\Model\Import\Attribute;

interface RowValidatorInterface extends \Magento\Framework\Validator\ValidatorInterface
{
    const ERROR_SALES_TYPE_CODE = 'invalidSalesTypeCode';
    const ERROR_EMPTY_STOCK_USER_CODE = 'emptyStockUserCode';
    const ERROR_CONDITION_CODE = 'conditionCode';
    const ERROR_STOCK_CODE = 'emptyStockCode';

    /**
     * Initialize validator
     * 
     * @param \Totaltools\ProntoAttributeImport\Model\Import\Attribute $context
     * @return $this
     */
    public function init($context);
}
