<?php

/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\ProntoAttributeImport\Model\Import;

use Magento\Framework\App\ObjectManager;
use Magento\Framework\Filesystem\Io\Sftp;
use Magento\Framework\Serialize\Serializer\Json;
use Totaltools\PriceImport\Helper\Data;
use Totaltools\ProntoAttributeImport\Model\File;
use Totaltools\ProntoAttributeImport\Model\Import\Validate\RowValidatorInterface;
use Magento\ImportExport\Model\Import\ErrorProcessing\ProcessingErrorAggregatorInterface;
use Totaltools\ProntoAttributeImport\Model\Import\Behavior\Attribute as Behavior;
use Magento\Framework\App\Filesystem\DirectoryList;
use Magento\ImportExport\Model\Import;
use Magento\ScheduledImportExport\Model\Scheduled\Operation\Data as OperationData;
use Totaltools\ScheduledImportExport\Plugin\Model\Scheduled\Operation\DataPlugin;
use Totaltools\PriceImport\Model\Import\Price;

class Attribute extends \Magento\ImportExport\Model\Import\Entity\AbstractEntity
{
    /**
     * Column Stock code.
     */
    const COL_STOCK_CODE = 'stock_code';
    /**
     * Column condition code.
     */
    const COL_CONDITION_CODE = 'condition_code';
    /**
     * Column sales type code.
     */
    const COL_SALESTYPE_CODE = 'salestype_code';
    /**
     * Column Stock user code.
     */
    const COL_STOCKUSER_CODE = 'stockuser_code';
    /**
     * Column Stock user code.
     */
    const COL_STOCKUSER_GROUP = 'stockuser_group';
     /**
     * Column MOQ.
     */
    const COL_MOQ = 'moq';

    /**
     * Entity type code for operation
     */
    const ENTITY_TYPE_CODE = 'pronto_import';
    /**
     * Processed directory name
     */
    const PROCESSED_DIR = 'processed/';
    const PROCESSING_DIR = 'processing/';

    /**
     * Validation failure message template definitions
     *
     * @var array
     */
    protected $_messageTemplates = [
        RowValidatorInterface::ERROR_SALES_TYPE_CODE => 'Sales Type Is Empty',
        RowValidatorInterface::ERROR_EMPTY_STOCK_USER_CODE => 'Empty Stock user Code',
        RowValidatorInterface::ERROR_CONDITION_CODE => 'Empty Condition Code',
        RowValidatorInterface::ERROR_STOCK_CODE => 'Empty Stock Code',
    ];

    /**
     * @var array
     */
    protected $_permanentAttributes = [
        self::COL_STOCK_CODE
    ];

    /**
     * If we should check column names
     *
     * @var bool
     */
    protected $needColumnCheck = true;

    /**
     * Valid column names
     *
     * @return array
     */
    protected $validColumnNames = [
        self::COL_STOCK_CODE,
        self::COL_CONDITION_CODE,
        self::COL_SALESTYPE_CODE,
        self::COL_STOCKUSER_CODE,
        self::COL_STOCKUSER_GROUP
    ];

    /**
     * Need to log in import history
     *
     * @var bool
     */
    protected $logInHistory = true;

    /**
     * @var array
     */
    protected $_validators = [];

    /**
     * @var \Magento\Framework\App\ResourceConnection
     */
    protected $_resource;

    /**
     * @var \Totaltools\ProntoAttributeImport\Model\AttributeImporter
     */
    protected $attributeImporter;


    /**
     * Json Serializer Instance
     *
     * @var Json
     */
    private $serializer;

    /**
     * @var array
     */
    private $_data = [];

    /**
     * @var \Magento\Framework\Filesystem\Directory\WriteInterface
     */
    private $_rootDirectory;

    /**
     * @var Sftp
     */
    private $sftp;

    /**
     * @var Data
     */
    private $helper;

    /**
     * @var \Magento\Framework\Stdlib\DateTime\DateTime
     */
    protected $localeDate;
    private File $file;
    /**
     * @var Price
     */
    protected $priceImportModel;

    /**
     * @param \Magento\Framework\Json\Helper\Data $jsonHelper
     * @param \Magento\ImportExport\Helper\Data $importExportData
     * @param \Magento\ImportExport\Model\ResourceModel\Import\Data $importData
     * @param \Magento\Framework\App\ResourceConnection $resource
     * @param \Magento\ImportExport\Model\ResourceModel\Helper $resourceHelper
     * @param \Magento\Framework\Stdlib\StringUtils $string
     * @param ProcessingErrorAggregatorInterface $errorAggregator
     * @param \Totaltools\ProntoAttributeImport\Model\AttributeImporter $attributeImporter
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Framework\Stdlib\DateTime\DateTime $localeDate
     */
    public function __construct(
        \Magento\Framework\Json\Helper\Data $jsonHelper,
        \Magento\ImportExport\Helper\Data $importExportData,
        \Magento\ImportExport\Model\ResourceModel\Import\Data $importData,
        \Magento\Framework\App\ResourceConnection $resource,
        \Magento\ImportExport\Model\ResourceModel\Helper $resourceHelper,
        ProcessingErrorAggregatorInterface $errorAggregator,
        \Totaltools\ProntoAttributeImport\Model\AttributeImporter $attributeImporter,
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\Stdlib\DateTime\DateTime $localeDate,
        Sftp $sftp,
        Data $helper,
        File $file,
        Price $priceImportModel,
    ) {
        $this->jsonHelper = $jsonHelper;
        $this->_importExportData = $importExportData;
        $this->_resourceHelper = $resourceHelper;
        $this->_dataSourceModel = $importData;
        $this->_resource = $resource;
        $this->_connection = $resource->getConnection(\Magento\Framework\App\ResourceConnection::DEFAULT_CONNECTION);
        $this->errorAggregator = $errorAggregator;
        $this->attributeImporter = $attributeImporter;
        $this->_rootDirectory = $filesystem->getDirectoryWrite(DirectoryList::ROOT);
        $this->localeDate = $localeDate;
        $this->sftp = $sftp;
        $this->helper = $helper;
        $this->priceImportModel = $priceImportModel;
        $this->_initErrorTemplates();
        $this->file = $file;
    }

    /**
     * Initialize Product error templates
     */
    protected function _initErrorTemplates()
    {
        foreach ($this->_messageTemplates as $errorCode => $template) {
            $this->addMessageTemplate($errorCode, $template);
        }
    }

    /**
     * @return array
     */
    public function getValidColumnNames()
    {
        return $this->validColumnNames;
    }

    /**
     * Row validation.
     *
     * @param array $rowData
     * @param int $rowNum
     * @return bool
     */
    public function validateRow(array $rowData, $rowNum)
    {
        $params = $this->getParameters();
        $behavior = $params['behavior'] ?? false;
        if (
            $behavior && (Behavior::BEHAVIOR_IMPORT_ATTRIBUTES == $behavior)
        ) {
            $stockCode = $rowData[self::COL_STOCK_CODE] ?? '';
            if ($stockCode == '') {
                $this->addRowError(RowValidatorInterface::ERROR_STOCK_CODE, $rowNum);
                return false;
            }
        }
        if (isset($this->_validatedRows[$rowNum])) {
            return !$this->getErrorAggregator()->isRowInvalid($rowNum);
        }
        $this->_validatedRows[$rowNum] = true;

        return !$this->getErrorAggregator()->isRowInvalid($rowNum);
    }

    /**
     * @inheritdoc
     */
    protected function _saveValidatedBunches()
    {
        $source = $this->_getSource();
        $currentDataSize = 0;
        $bunchRows = [];
        $startNewBunch = false;
        $nextRowBackup = [];
        $maxDataSize = $this->_resourceHelper->getMaxDataSize();
        $bunchSize = $this->_importExportData->getBunchSize();
        $skuSet = [];

        $source->rewind();

        while ($source->valid() || $bunchRows) {
            if ($startNewBunch || !$source->valid()) {
                $bunchRows = $nextRowBackup;
                $currentDataSize = strlen($this->getSerializer()->serialize($bunchRows));
                $startNewBunch = false;
                $nextRowBackup = [];
            }
            if ($source->valid()) {
                try {
                    $rowData = $source->current();
                    if (array_key_exists('sku', $rowData)) {
                        $skuSet[$rowData['sku']] = true;
                    }
                } catch (\InvalidArgumentException $e) {
                    $this->addRowError($e->getMessage(), $this->_processedRowsCount);
                    $this->_processedRowsCount++;
                    $source->next();
                    continue;
                }

                $this->_processedRowsCount++;

                if ($this->validateRow($rowData, $source->key())) {
                    // add row to bunch for save
                    $rowData = $this->_prepareRowForDb($rowData);
                    $rowSize = strlen($this->jsonHelper->jsonEncode($rowData));

                    $isBunchSizeExceeded = $bunchSize > 0 && count($bunchRows) >= $bunchSize;

                    if ($currentDataSize + $rowSize >= $maxDataSize || $isBunchSizeExceeded) {
                        $startNewBunch = true;
                        $nextRowBackup = [$source->key() => $rowData];
                    } else {
                        $bunchRows[$source->key()] = $rowData;
                        $currentDataSize += $rowSize;
                    }

                    array_push($this->_data, $rowData);
                }
                $source->next();
            }
        }
        $this->_processedEntitiesCount = (count($skuSet)) ?: $this->_processedRowsCount;

        if (!$this->getErrorAggregator()->getErrorsCount()) {
            $this->importSource($this->getData());
        }

        return $this;
    }

    /**
     * @inheritdoc
     */
    protected function _importData()
    {
        if (!$this->getErrorAggregator()->getErrorsCount()) {
            return true;
        }
        return false;
    }

    /**
     * Import data with relevant importer
     *
     * @param array $data
     * @return void
     */
    public function importSource(array $data)
    {
        $params = $this->getParameters();
        $behavior = $params['behavior'] ?? false;

        if ($behavior && $behavior == \Totaltools\ProntoAttributeImport\Model\Import\Behavior\Attribute::BEHAVIOR_IMPORT_ATTRIBUTES) {
            $this->attributeImporter->execute($data, $params);
        }
        $this->priceImportModel->_moveFile($params);

    }

    /**
     * @inheritdoc
     */
    public function getEntityTypeCode()
    {
        return self::ENTITY_TYPE_CODE;
    }

    /**
     * @inheritdoc
     */
    protected function _prepareRowForDb(array $rowData)
    {
        return array_values($rowData);
    }

    /**
     * Get Serializer instance
     *
     * Workaround. Only way to implement dependency and not to break inherited child classes
     *
     * @return Json
     */
    private function getSerializer()
    {
        if (null === $this->serializer) {
            $this->serializer = ObjectManager::getInstance()->get(Json::class);
        }
        return $this->serializer;
    }

    /**
     * @return array
     */
    public function getData()
    {
        return $this->_data;
    }
}