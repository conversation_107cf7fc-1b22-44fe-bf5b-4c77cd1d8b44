<?php
/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */
namespace Totaltools\ProntoAttributeImport\Model;

use Totaltools\PriceImport\Helper\Data;

abstract class ImporterAbstract
{
    /**
     * Helper class
     * @var Data
     */
    protected $helper;

    /**
     * Contains summary of the import records
     *
     * @var array
     */
    private $summary = [];

    /**
     * Contains job configuration passed from import model
     *
     * @var array
     */
    protected $config = [];

    /**
     * ImporterAbstract Constructor
     *
     * @param Data $helper
     */
    public function __construct(Data $helper)
    {
        $this->helper = $helper;
    }

    /**
     * Importer main execution method
     * @param $data
     * @param $config
     * @return void
     */
    abstract public function execute($data, $config);
    /**
     * @return array
     */
    abstract protected function _prepareSummary();

    /**
     * @param string $message
     * @param false $success
     * @return void
     */
    protected function sendNotification($message = '', $success = false,$emailSubject = 'Attribute Import')
    {
        $trace = array_merge(
            $this->_prepareSummary(),
            ['Message' => $message]
        );

        $this->helper->sendEmailNotification(['trace' => $trace], $success, $this->config, $emailSubject);
    }

    /**
     * @return array
     */
    protected function getSummary()
    {
        return $this->summary;
    }

    /**
     * @param $key
     * @param $value
     */
    protected function setSummary($key, $value)
    {
        $this->summary[$key] = $value;
    }

    /**
     * @param $key
     */
    protected function incrementSummary($key)
    {
        $this->summary[$key] ++;
    }
}
