<?php

/**
 * @package   Totaltools_PriceImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\ProntoAttributeImport\Model;

use Exception;
use Totaltools\ProntoAttributeImport\Model\Eav\CatalogProductEntityVarchar;
use Totaltools\PriceImport\Helper\Data;
use Magento\Store\Model\StoreManager;
use Magento\Eav\Model\Entity\Attribute;


class AttributeImporter extends ImporterAbstract
{
    /**
     * @var CatalogProductEntityVarchar
     */
    protected $catalogProductEntityVarchar;

    /**
     * @var StoreManager
     */
    protected $storeManager;
    /**
     * @var Attribute
     */
    protected $eavAttribute;
    /**
     * AttributeImporter constructor.
     * @param CatalogProductEntityVarchar $catalogProductEntityVarchar
     * @param Data $helper
     */
    public function __construct(
        CatalogProductEntityVarchar $catalogProductEntityVarchar,
        StoreManager $storeManager,
        Data $helper,
        Attribute $eavAttribute,
    ) {
        $this->catalogProductEntityVarchar = $catalogProductEntityVarchar;
        $this->helper = $helper;
        $this->storeManager = $storeManager;
        $this->eavAttribute = $eavAttribute;
        parent::__construct($helper);
        // initialized required data.
        $this->initialization();
    }

    public function initialization()
    {
        // get store id & code
        $storeList = $this->getStores();
        // set store List for catalog product entity varchar entity
        $this->catalogProductEntityVarchar->setStoreList($storeList);
        // set price attributeId
        $this->catalogProductEntityVarchar
            ->setBackorderableAttributeId($this->getAttributeId(CatalogProductEntityVarchar::IS_BACKORDER_ABLE));
        $this->catalogProductEntityVarchar
            ->setSpecialAttributeId($this->getAttributeId(CatalogProductEntityVarchar::IS_SPECIAL_ORDER));
        $this->catalogProductEntityVarchar
            ->setNotforsaleAttributeId($this->getAttributeId(CatalogProductEntityVarchar::NOT_FOR_SALE));
        $this->catalogProductEntityVarchar
            ->setMinSaleQtyAttributeId($this->getAttributeId(CatalogProductEntityVarchar::MIN_SALE_QTY));
            $this->catalogProductEntityVarchar
            ->setEbProductAttributeId($this->getAttributeId(CatalogProductEntityVarchar::EB_PRODUCT));
            $this->catalogProductEntityVarchar
            ->setStockAvailabiltyAttributeId($this->getAttributeId(CatalogProductEntityVarchar::STOCK_AVALIBALITY_CODE));
            $this->catalogProductEntityVarchar
            ->setProductStatusAttributeId($this->getAttributeId(CatalogProductEntityVarchar::PRODUCT_STATUS));
    }
    /**
     * @param array $data
     * @param array $config
     * @return void
     */
    public function execute($data, $config)
    {
        $this->config = $config;

        try {
            $this->setProntoAttribute($data);
            unset($data);

            if ($this->catalogProductEntityVarchar->executeImporter()) {
                $this->sendNotification('Attributes updated successfully', true);
                $this->sendNotification('Import succeeded', true);
            } else {
                $this->sendNotification('Import failed', false);
            }
        } catch (Exception $e) {
            $this->sendNotification($e->getMessage(), false);
        }
    }


    /**
     * @param array $data
     */
    protected function setProntoAttribute(array $data)
    {

        $this->catalogProductEntityVarchar->setAttribute($data);
    }
    /**
     * @return array
     */
    protected function getStores()
    {
        $storeList = [];
        $stores = $this->storeManager->getStores();
        foreach ($stores as $store) {
            $code = $store->getCode();
            if (strtoupper($code) == CatalogProductEntityVarchar::QUEENSLAND_STORE_CODE) {
                $code = CatalogProductEntityVarchar::PRONTO_QUEENSLAND_STORE_CODE;
            }

            $storeList[$store->getId()] = strtoupper($code);
        }
        return $storeList;
    }
    /**
     * @param string $productEav
     * @return int
     */
    public function getAttributeId(string $productEav)
    {
        return $this->eavAttribute->getIdByCode("catalog_product", $productEav);
    }
    /**
     * @inheritdoc
     */
    protected function _prepareSummary()
    {
        return $this->catalogProductEntityVarchar->getSummary();
    }
}
