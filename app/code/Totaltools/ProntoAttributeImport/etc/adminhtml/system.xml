<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="pronto_attribute_import" translate="label" sortOrder="120" showInDefault="1" showInWebsite="1" showInStore="0">
            <label>Pronto Attribute Import</label>
            <tab>catalog</tab>
            <resource>Totaltools_ProntoAttributeImport::config_attributeimport</resource>
            <group id="settings" translate="label" type="text" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="0">
                <label>Pronto Attribute Import Settings</label>
                    <field id="enabled" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="1" translate="label" type="select">
                    <label>Disable Not for Sale Products</label>
                    <comment>Enable Disable  Not for Sale Products</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    </field>
                    <field id="delete" showInDefault="1" showInStore="0" showInWebsite="0" sortOrder="1" translate="label" type="select">
                    <label>Delete Store based values</label>
                    <comment>Delete Not for Sale store based values</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
            </group>
        </section>
    </system>
</config>