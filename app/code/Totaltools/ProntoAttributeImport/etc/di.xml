<?xml version="1.0"?>
<!--
/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */
-->
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\ScheduledImportExport\Model\Scheduled\Operation">
        <plugin name="Totaltools_ProntoAttributeImport::Scheduled_Operation"
            type="Totaltools\ProntoAttributeImport\Plugin\Model\Scheduled\OperationPlugin" />
    </type>
    <type name="Magento\ImportExport\Model\Import">
        <plugin name="Totaltools_ProntoAttributeImport::Import_Model"
            type="Totaltools\ProntoAttributeImport\Plugin\Model\ImportPlugin" />
    </type>
    <type name="Magento\Framework\Console\CommandList">
        <arguments>
            <argument name="commands" xsi:type="array">
                <item name="attribute-importer" xsi:type="object">Totaltools\ProntoAttributeImport\Console\Command\AttributeImporter</item>
            </argument>
        </arguments>
    </type>
    <type name="Magento\ImportExport\Model\Import\SampleFileProvider">
        <arguments>
            <argument name="samples" xsi:type="array">
                <item name="pronto_import" xsi:type="string">Totaltools_ProntoAttributeImport</item>
            </argument>
        </arguments>
    </type>
</config>
