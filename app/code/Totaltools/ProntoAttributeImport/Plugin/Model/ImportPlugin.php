<?php
/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\ProntoAttributeImport\Plugin\Model;

class ImportPlugin
{
    /**
     * @param \Magento\ImportExport\Model\Import $subject
     * @param \Closure $proceed
     * @return bool
     */
    public function aroundImportSource($subject, $proceed)
    {
        $entityType = $subject->getEntity();

        if ($entityType && \Totaltools\ProntoAttributeImport\Model\Import\Attribute::ENTITY_TYPE_CODE == $entityType) {
            return true;
        }

        return $proceed();
    }
}
