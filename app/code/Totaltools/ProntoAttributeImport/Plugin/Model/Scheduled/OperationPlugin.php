<?php
/**
 * @package   Totaltools_ProntoAttributeImport
 * <AUTHOR> <<EMAIL>>
 * @copyright 2023 (c) Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\ProntoAttributeImport\Plugin\Model\Scheduled;

use Gt\Dom\Attr;
use Totaltools\ProntoAttributeImport\Model\File;
use Totaltools\ProntoAttributeImport\Model\Import\Attribute;
use Totaltools\ProntoAttributeImport\Model\Import\Behavior\Attribute as Behavior;

class OperationPlugin
{
    /**
     * Text file extension
     */
    const EXT_TXT = 'txt';

    /**
     * CSV file extension
     */
    const EXT_CSV = 'csv';

    /**
     * Pronto attribute header columns
     */
    const PRONTO_ATTRIBUTE_HEADER = [
        Attribute::COL_STOCK_CODE,
        Attribute::COL_CONDITION_CODE,
        Attribute::COL_SALESTYPE_CODE,
        Attribute::COL_STOCKUSER_CODE,
        Attribute::COL_STOCKUSER_GROUP
    ];



    /**
     * @var string
     */
    private $_behavior;

    /**
     * @var \Magento\Framework\Filesystem
     */
    protected $filesystem;

    /**
     * @var \Magento\Framework\App\Filesystem\DirectoryList $directoryList
     */
    protected $directoryList;
    /**
     * @var \Magento\Framework\Filesystem\Driver\File
     */
    protected $fileDriver;

    /**
     * @var \Magento\Framework\File\Csv
     */
    protected $csvProcessor;

    /**
     * @var \Psr\Log\LoggerInterface
     */
    protected $logger;

    /**
     * @var File
     */
    private File $file;

    /**
     * @param \Magento\Framework\Filesystem $filesystem
     * @param \Magento\Framework\App\Filesystem\DirectoryList $directoryList
     * @param \Magento\Framework\Filesystem\Driver\File $fileDriver
     * @param \Magento\Framework\File\Csv $csvProcessor
     * @param \Psr\Log\LoggerInterface $logger
     */
    public function __construct(
        \Magento\Framework\Filesystem $filesystem,
        \Magento\Framework\App\Filesystem\DirectoryList $directoryList,
        \Magento\Framework\Filesystem\Driver\File $fileDriver,
        \Magento\Framework\File\Csv $csvProcessor,
        File $file,
        \Psr\Log\LoggerInterface $logger
    ) {
        $this->filesystem = $filesystem;
        $this->directoryList = $directoryList;
        $this->fileDriver = $fileDriver;
        $this->csvProcessor = $csvProcessor;
        $this->logger = $logger;
        $this->file = $file;
    }

    /**
     * @param \Magento\ScheduledImportExport\Model\Scheduled\Operation $subject
     * @param string $result
     * @param \Magento\ScheduledImportExport\Model\Scheduled\Operation\OperationInterface $operation
     * @return string
     */
    public function afterGetFileSource(
        $subject,
        $result,
        $operation
    ) {
        $entityType = $subject->getEntityType();

        if ($entityType && Attribute::ENTITY_TYPE_CODE == $entityType) {
            $this->_behavior = $subject->getBehavior();
            $fileInfo = pathinfo($result);

            if ($fileInfo && $fileInfo['extension'] === self::EXT_TXT) {
                $result = $this->convertTxtToCsv($result);
            }
        }

        /** set csv file path */
        $this->file->setCsvFile($result);

        return $result;
    }

    /**
     * @param string $source
     * @return string
     */
    protected function convertTxtToCsv($source)
    {
        if (!$this->fileDriver->isExists($source)) {
            return $source;
        }

        $csvFilePath = '';

        try {
            $fileContents = $this->fileDriver->fileGetContents($source);
            $rows = explode(PHP_EOL, trim($fileContents));
            $data = [];

            if (sizeof($rows)) {
                foreach ($rows as $key => $row) {
                    $line = explode('|', trim($row));
                    if (is_array($line) && !empty($line)) {
                        $data[] = $line;
                    }
                }

                $csvFilePath = $this->createCsv($source, $data);
            }
        } catch (\Exception $e) {
            $this->logger->error('[' . $source . '] ' . $e->getMessage());
        } finally {
            return $csvFilePath;
        }
    }

    /**
     * @param string $filePath
     * @param array $data
     * @return int|bool
     */
    protected function createCsv($filePath, $data)
    {
        $result = '';
        $fileInfo = pathinfo($filePath);
        $filePath = $fileInfo['dirname'] . $fileInfo['filename'] . '.' . self::EXT_CSV;
        array_unshift($data, $this->getHeaderCells());

        try {
            $this->csvProcessor
                ->setEnclosure('"')
                ->setDelimiter(',')
                ->saveData($filePath, $data);

            $result = $filePath;
        } catch (\Exception $e) {
            $this->logger->error('[' . $filePath . '] ' . $e->getMessage());
        }

        return $result;
    }

    /**
     * @return array
     */
    protected function getHeaderCells()
    {
        switch ($this->_behavior) {
            case Behavior::BEHAVIOR_IMPORT_ATTRIBUTES:
                return self::PRONTO_ATTRIBUTE_HEADER;
                break;
            default:
                return self::PRONTO_ATTRIBUTE_HEADER;
        }
    }
}
