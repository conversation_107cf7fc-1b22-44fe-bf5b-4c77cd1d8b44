<?php
namespace Totaltools\Afterpay\Plugin;
use Psr\Log\LoggerInterface;
class CheckoutDataBuilderPlugin
{
    /**
     * @var LoggerInterface
     */
    private $logger;
    /**
     * PricePlugin constructor.
     *
     * @param LoggerInterface $logger
     */
    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
    }
    /**
     * This method will run before the execute method of the original class.
     *
     * @param \Afterpay\Afterpay\Gateway\Request\Checkout\CheckoutDataBuilde $subject
     * @param array $result
     * @return array
     */
    public function afterBuild(
        \Afterpay\Afterpay\Gateway\Request\Checkout\CheckoutDataBuilder $subject,
        $result
    ) {
        $this->logger->info('Afterpay request Data:', ['Data' => $result ]);
        return $result; 
    }
}