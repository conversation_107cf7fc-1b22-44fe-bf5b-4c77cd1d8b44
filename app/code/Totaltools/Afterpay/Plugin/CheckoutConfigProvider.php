<?php

/**
 * @category    Totaltools
 * @package     Totaltools_Afterpay
 * <AUTHOR> <<EMAIL>>
 * @copyright   (c) 2023 Totaltools. <https://totaltools.com.au>
 */

namespace Totaltools\Afterpay\Plugin;

class CheckoutConfigProvider
{
    const AFTERPAY_LOGO = 'https://static.afterpay.com/integration/logo-afterpay-colour-221x46.png';

    /**
     * @param \Afterpay\Afterpay\Model\CheckoutConfigProvider $subject
     * @param array $result
     * @return array
     */
    public function afterGetConfig($subject, $config): array
    {
        if (isset($config['payment']['afterpay'])) {
            $config['payment']['afterpay']['paymentMarkSrc'] = self::AFTERPAY_LOGO;
        }

        return $config;
    }
}
