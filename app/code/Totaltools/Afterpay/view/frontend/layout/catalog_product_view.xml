<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <head>
        <css src="Afterpay_Afterpay::css/afterpay-express-checkout.css"/>
    </head>
    <body>
        <move element="afterpay.product.cta" destination="product.info.price"  after="-" />
        <referenceBlock name="afterpay.product.cta">
            <arguments>
                <argument name="ctn-class" xsi:type="string">afterpay-ctn</argument>
                <argument name="sort-order" xsi:type="string">30</argument>
            </arguments>
        </referenceBlock>
        <referenceBlock name="afterpay.product.express.checkout" remove="true" />
    </body>
</page>
