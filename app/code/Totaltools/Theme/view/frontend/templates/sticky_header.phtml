<?php
/** @var \Magento\Framework\View\Element\Template $block */
?>

<script async>
    require(['jquery', 'underscore'], function($, _) {
        'use strict';

        /**
         * Sticky header in mobile screens.
         */
        $(function() {
            var $pageHeader = $('.page-header');
            var $pageWrapper = $('.page-wrapper');
            var headerOffset = $pageHeader.offset()?.top ?? 0;

            $(window).on('scroll', _.debounce(function() {
                if ($(this).width() <= 768 && (window.scrollY || window.pageYOffset) > headerOffset) {
                    $pageHeader.addClass('affixed');
                    $pageWrapper.css({
                        paddingTop: $pageHeader.innerHeight()
                    });
                } else {
                    $pageHeader.removeClass('affixed');
                    $pageWrapper.css({
                        paddingTop: 0
                    })
                }
            }, 20));
        });
    });
</script>
