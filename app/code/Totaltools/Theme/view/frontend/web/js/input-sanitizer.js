define([], function () {
    "use strict";

    return function (input, escape) {
        function escapeJs(str) {
            return str
                .replace(/\\/g, "\\\\") // Escape backslashes
                .replace(/"/g, '\\"') // Escape double quotes
                .replace(/'/g, "\\'") // Escape single quotes
                .replace(/\n/g, "\\n") // Escape newlines
                .replace(/\r/g, "\\r"); // Escape carriage returns
        }

        // Strip HTML tags
        var stripped = input.replace(/<[^>]*>/g, "");

        // Escape for JavaScript
        return escape ? escapeJs(stripped) : stripped;
    };
});
