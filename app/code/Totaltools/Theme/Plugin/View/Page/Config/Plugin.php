<?php
/**
 * <AUTHOR> Internet Team
 * @copyright Copyright (c) 2016 Balance Internet (https://www.balanceinternet.com.au)
 * @package Totaltools_Theme
 */
namespace Totaltools\Theme\Plugin\View\Page\Config;
/**
 * Plugin to remove keywords from all pages
 *
 * @SuppressWarnings(PHPMD.CouplingBetweenObjects)
 */
class Plugin
{

    public function afterGetKeywords($subject, $return)
    {
        return (string)null;
    }
}