<?php
/**
 * <AUTHOR> Internet Team
 * @copyright Copyright (c) 2016 Balance Internet (https://www.balanceinternet.com.au)
 * @package Amasty_Shopby
 */
namespace Totaltools\Theme\Helper;

use Totaltools\Customer\Helper\AttributeData;

class Data extends \Magento\Framework\App\Helper\AbstractHelper
{

    protected $customerSession;
    /**
     * @var AttributeData
     */
    protected $attributeData;

    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        AttributeData $attributeData,
        \Magento\Customer\Model\Session $customerSession
    ) {
        $this->customerSession = $customerSession;
        parent::__construct($context);
        $this->attributeData = $attributeData;
    }

    public function isLoggedIn()
    {
        return $this->customerSession->isLoggedIn();
    }

    public function getFooterPaymentOptionsBlock()
    {
        $isB2bCustomer = $this->attributeData->isB2bCustomer();
        if ($isB2bCustomer) {
            return '';
        }
        return 'footer_payments';
    }
}
