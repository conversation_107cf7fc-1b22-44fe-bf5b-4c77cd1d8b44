<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="after.body.start">
            <block class="Anowave\Ec\Block\Track" name="ec_multishipping_addresses" template="checkout_multishipping.phtml">
            	<arguments>
                    <argument name="step" xsi:type="string">register</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>