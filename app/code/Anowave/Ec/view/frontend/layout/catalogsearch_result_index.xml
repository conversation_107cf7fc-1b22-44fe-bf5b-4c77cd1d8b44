<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="content">
            <block class="Anowave\Ec\Block\Track" name="ec_search_collection" template="search_collection.phtml">
            	<arguments>
                    <argument name="product_list" xsi:type="object">Anowave\Ec\ViewModel\ProductList</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>