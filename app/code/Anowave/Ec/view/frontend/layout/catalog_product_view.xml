<?xml version="1.0"?>
<page xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:View/Layout/etc/page_configuration.xsd">
    <body>
        <referenceContainer name="before.body.end">
            <block class="Anowave\Ec\Block\Track" name="ec_detail_product" template="detail_collection.phtml">
                <arguments>
                    <argument name="product_detail" xsi:type="object">Anowave\Ec\ViewModel\Product</argument>
                </arguments>
            </block>
        </referenceContainer>
    </body>
</page>