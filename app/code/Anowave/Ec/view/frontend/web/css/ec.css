@CHARSET "UTF-8";

.ec-gtm-cookie-directive 
{ 
	position: fixed; 
	bottom:20px; 
	left: 0; 
	right: 0; 
	text-align: center; 
	z-index:9999; 
	-ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=95)";
	filter: alpha(opacity=95);
	-moz-opacity: 0.95;
	-khtml-opacity: 0.95;
	opacity: 0.95;
}

.ec-gtm-cookie-directive > div 
{ 
	display: inline-block; 
	text-align: left;  
	background:#fff;
	padding:20px; 
	color:#000; 
	max-width:300px;
	-webkit-box-shadow: 0px 0px 5px 1px rgba(0,0,0,0.19);
	-moz-box-shadow: 0px 0px 5px 1px rgba(0,0,0,0.19);
	box-shadow: 0px 0px 5px 1px rgba(0,0,0,0.19);
}

.ec-gtm-cookie-directive > div > div { float:left; min-width:300px; }
.ec-gtm-cookie-directive > div > div > div:before,
.ec-gtm-cookie-directive > div > div > div:after { content: " ";display: table; }
.ec-gtm-cookie-directive > div > div > div:after { clear: both; }
.ec-gtm-cookie-directive > div > div > div p { display:block; padding:5px; margin:0px; }
.ec-gtm-cookie-directive > div > div > div a { cursor:pointer; color:#8e8e8e; }
.ec-gtm-cookie-directive > div > div > div a.action { display:block; text-align:left; padding:5px; float:left; }
.ec-gtm-cookie-directive > div > div > div a.action.accept { color:#8bc53f; font-weight:bold; }
.ec-gtm-cookie-directive > div > div > div a.action.accept-all { color:#8bc53f; font-weight:bold; }
.ec-gtm-cookie-directive > div > div > div a.action.refuse {  }

.ec-gtm-cookie-directive-segments { background:#f7f7f7; margin:10px -20px 10px -20px; padding:10px 25px 10px 25px; }
.ec-gtm-cookie-directive-segments > div { padding:5px 0px 5px 0px; }

.ec-gtm-cookie-directive-segments > div input[type="checkbox"] { display: none; }
.ec-gtm-cookie-directive-segments > div input[type="checkbox"] + label { display: block; position: relative; padding-left: 35px; cursor: pointer; -webkit-user-select: none; -moz-user-select: none; -ms-user-select: none; }
.ec-gtm-cookie-directive-segments > div input[type="checkbox"] + label:before { content: ''; display: block; width: 20px; height: 20px; border: 1px solid #000; position: absolute; left: 0; top: 0; opacity: 1; -webkit-transition: all .12s, border-color .08s; transition: all .12s, border-color .08s; }
.ec-gtm-cookie-directive-segments > div input[type="checkbox"]:checked + label:before { width: 10px;top: -5px;left: 5px;border-radius: 0;opacity: 1;border-top-color: transparent;border-left-color: transparent; -webkit-transform: rotate(45deg); transform: rotate(45deg);}
	


.ec-gtm-cookie-directive small { display:block; padding:10px 20px 10px 35px; }

.ec-gtm-cookie-directive > div > div > div .ec-gtm-cookie-directive-note-toggle { display:block; margin:5px 0px 5px 35px; color:#8bc53f; font-size:12px; cursor:pointer; }
.ec-gtm-cookie-directive > div > div > div .ec-gtm-cookie-directive-note { display:none; }

main .ec-gtm-cookie-directive { position:relative; }
main .ec-gtm-cookie-directive > div { -webkit-box-shadow:none; -moz-box-shadow:none; box-shadow: none; max-width:100%; padding:0px; }
main .ec-gtm-cookie-directive-segments { margin:10px 0px 20px 0px; background:none; padding:0px; }


@media (min-width: 1200px) 
{
	.ec-gtm-cookie-directive > div  { max-width:600px; }
	.ec-gtm-cookie-directive > div > div { min-width:500px; }
	
	main .ec-gtm-cookie-directive { max-width:100%;}
}

@media (max-width: 1200px) 
{
	.ec-gtm-cookie-directive > div > div { float:left; min-width:200px; }
}


