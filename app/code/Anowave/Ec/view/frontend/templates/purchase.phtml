<?php echo $block->getChildHtml('ec_datalayer') ?>
<?php if ($block->getOrderIds() && $block->getHelper()->isCustomerReviewsActive()): ?>
	<script data-ommit="true" src="https://apis.google.com/js/platform.js?onload=renderOptIn" async defer></script>
<?php endif ?>
<script data-ommit="true">

	var EC = [];

	/* Dynamic remarketing */
	window.google_tag_params = window.google_tag_params || {};

	/* Default pagetype */
	window.google_tag_params.ecomm_pagetype = '<?php echo $block->getPageType() ?>';

	/* Grouped products collection */
	window.G = [];

	/**
	 * Global revenue 
	 */
	window.revenue = 0;

	/**
	 * DoubleClick
	 */
	window.DoubleClick = 
	{
		DoubleClickRevenue:	 	0,
		DoubleClickTransaction: 0,
		DoubleClickQuantity: 	0
	};
	
	<?php
	/**
	 * Push visitor data
	 */
	?>

	AEC.Cookie.visitor(<?php echo $block->getVisitorPush() ?>).push(dataLayer, false);

	<?php
	/**
	 * Add pagetype
	 */
	?>
	
	dataLayer.push({ pageType: <?php echo $block->getHelper()->getPageType() ?>});
	
</script>
<?php echo $block->getChildHtml('ec_impressions') ?>
<?php echo $block->getChildHtml('ec_search') ?>
<?php echo $block->getChildHtml('ec_detail') ?>
<?php echo $block->getChildHtml('ec_cart') ?>
<?php echo $block->getChildHtml('ec_widgets') ?>

<?php 
/**
 * Google Tag Manager Snippet
 */
?>
<?php echo $block->getHeadSnippet() ?>

<?php
/**
 * Transaction tracking
 */
?>
<script data-ommit="true">

    <?php if ($block->getOrderIds()):?>
    
        ((collection) => 
        {
        	collection.forEach(data => 
        	{
        		<?php
        		/**
        		 * Push transaction
        		 */
        		?>

        		AEC.Cookie.purchase(data).push(dataLayer, false);
        
        		<?php 
        		/**
        		 * Push data to custom transport layer
        		 */
        		?>
        		
        		dataLayerTransport.push(data);
        
        		<?php if ($block->getHelper()->facebook()): ?>
        
            		var content_ids = [], content_length = data.ecommerce.purchase.products.length;
            		
            		for (i = 0, l = data.ecommerce.purchase.products.length; i < l; i++)
            		{
            			content_ids.push(data.ecommerce.purchase.products[i].id);
            		}
            
            		window.content_ids = content_ids;
            
            		<?php $key = $block->getHelper()->getFacebookValueKey() ?>
            
            		(function(callback)
            		{
            			if (AEC.Const.COOKIE_DIRECTIVE)
            			{
            				AEC.CookieConsent.queue(callback);
            			}
            			else 
            			{
            				callback.apply(window,[]);
            			}
            		})
            		(
            			(function(content_ids, content_length, data)
            			{
            				return function()
            				{
            					AEC.EventDispatcher.on('ec.facebook.loaded', () => 
                				{
                					fbq("track", "Purchase", 
    	        					{
    	        						content_type: 	'product',
    	        						content_ids:	content_ids,
    	        						num_items:		content_length,
    	        						value: 			data.facebook['<?php echo $key ?>'],
    	        						currency: 		'<?php echo $block->getHelper()->getCurrency() ?>'
    	        					}, 
    	        					{ eventID: AEC.UUID.generate({ event: 'Purchase'}) });
                				});
            				}
            			})(content_ids, content_length, data)
            		);
        			
        		<?php endif ?>
        	});
        	
        })(<?php echo $block->getHelper()->getPurchasePayloadCollection($block) ?>);
        
        <?php
        /**
         * AdWords Dynamic Remarketing
         */
        ?>
        <?php $google_tag_params = $block->getPurchaseGoogleTagParams($block) ?>
        
        /**
         * AdWords Dynamic Remarketing page type
         */
        window.google_tag_params.ecomm_pagetype		= <?php echo $block->getHelper()->getJsonHelper()->encode('purchase') ?>;
        window.google_tag_params.ecomm_prodid 		= <?php echo $block->getHelper()->getJsonHelper()->encode($google_tag_params->ecomm_prodid) ?>;
        window.google_tag_params.ecomm_pvalue 		= <?php echo $block->getHelper()->getJsonHelper()->encode($google_tag_params->ecomm_pvalue) ?>;
        window.google_tag_params.ecomm_pname 		= <?php echo $block->getHelper()->getJsonHelper()->encode($google_tag_params->ecomm_pname) ?>;
        window.google_tag_params.ecomm_totalvalue  	= <?php echo $google_tag_params->ecomm_totalvalue ?>;
        window.google_tag_params.returnCustomer 	= <?php echo $block->getHelper()->getIsReturnCustomer() ?>;
        
        <?php if ($block->getHelper()->supportDynx()): ?>
        
            window.google_tag_params.dynx_pagetype 		= 'conversion';
            window.google_tag_params.dynx_itemid 		= window.google_tag_params.ecomm_prodid;
            window.google_tag_params.dynx_totalvalue 	= window.google_tag_params.ecomm_totalvalue
        
        <?php endif ?>

        <?php 
        /**
         * AdWords Conversion Tracking
         */
        ?>
        
        <?php if ($block->getHelper()->isAdwordsConversionTrackingActive(true) && $block->getAdwords()->getGoogleConversionId() && !$block->getHelper()->useAdwordsConversionTrackingGtag()) : ?>
        
        	window.google_conversion_id 		= '<?php echo $block->getAdwords()->getGoogleConversionId() ?>';
        	window.google_conversion_value 		=  <?php echo $block->getAdwordsRevenue() ?>;
        	window.google_conversion_language 	= '<?php echo $block->getAdwords()->getGoogleConversionLanguage() ?>';
        	window.google_conversion_format 	= '<?php echo $block->getAdwords()->getGoogleConversionFormat() ?>';
        	window.google_conversion_label 		= '<?php echo $block->getAdwords()->getGoogleConversionLabel() ?>';
        	window.google_conversion_color		= '<?php echo $block->getAdwords()->getGoogleConversionColor() ?>';
        	window.google_conversion_currency 	= '<?php echo $block->getAdwords()->getGoogleConversionCurrency() ?>';
        
        	<?php
        	/**
        	 * Add an order ID to conversion tracking tag to help avoid counting duplicate conversions.
        	 */
        	?>
        	<?php foreach ($block->getOrders() as $order): ?>
        	
        	window.google_conversion_order_id = <?php echo $block->getHelper()->getJsonHelper()->encode($order->getIncrementId()) ?>;
        
        	<?php endforeach ?>
        		
        <?php endif ?>

        <?php
        /**
         * Google Customer Reviews
         */
        ?>
        <?php if ($block->getHelper()->isCustomerReviewsActive()): ?>
        
            window.renderOptIn = function() 
            {
            	window.gapi.load('surveyoptin', function() 
            	{
            		window.gapi.surveyoptin.render
            		(
            			<?php echo $block->getHelper()->getCustomerReviewsPayload($block) ?>
            		);
            	});
            }
        
        <?php endif ?>
        
    <?php endif ?>	
</script>
<?php 
/**
 * Apply initial bindings
 */
?>
<script data-ommit="true">AEC.Bind.apply(<?php echo $block->getHelper()->getInitialBinding() ?>)</script>
<?php
/**
 * AdWords Conversion Tracking
 */
?>
<?php if ($block->getOrders() && $block->getHelper()->supportEnhancedConversions()): ?>
	<script data-ommit="true">
	
    	<?php foreach ($block->getOrders() as $order): ?>
    	
        	var enhanced_conversion_data = <?php echo $block->getHelper()->getEnhancedConversionVariable($order) ?>;
        
        	dataLayer.push({ event: 'enhanced_conversion_data', eventVariable: enhanced_conversion_data });
        	
    	<?php endforeach ?>
    	
	</script>
<?php endif ?>
<?php if ($block->getHelper()->isAdwordsConversionTrackingActive(true)):?>

	<?php if (!$block->getHelper()->useAdwordsConversionTrackingGtag()): ?>
		<?php
		/**
		 * Standard AdWords Conversion Tracking Implementation
		 */
		?>
		<?php if ($block->getOrders() && $block->getAdwords()->getGoogleConversionId()) : ?>
			<script data-ommit="true" src="//www.googleadservices.com/pagead/conversion.js"></script>
		    <?php foreach ($block->getOrders() as $order): ?>
		    		<?php $img_conversion = "//www.googleadservices.com/pagead/conversion/{$block->getAdwords()->getGoogleConversionId()}/?value={$block->getRevenue()}&label={$block->getAdwords()->getGoogleConversionLabel()}&oid={$order->getIncrementId()}&script=0" ?>
		            <noscript><img height=1 width=1 border=0 src="<?php echo $img_conversion ?>"></noscript>
		    <?php endforeach ?>
		<?php endif ?>
	<?php else: ?>
		<?php
		/**
		 * Gtag AdWords Conversion Tracking Implementation
		 */
		?>
		<?php echo $block->getHelper()->getAdwordsConversionTrackingGtagSiteTag() ?>
		<?php if ($block->getOrders()) : ?>
			
			<?php foreach ($block->getOrders() as $order): ?>
				<script data-ommit="true">

    				((conversion) => 
    				{
    					document.addEventListener("DOMContentLoaded", () => 
    				    {
    				    	if ('undefined' !== typeof gtag)
    						{
    							gtag('event', 'conversion',  conversion);
    						}
    				    });
    				})(<?php echo $block->getHelper()->getAdwordsConversionTrackingGtagConvesionEvent($order)?>);
				</script>
			<?php endforeach ?>
		<?php endif ?>
	<?php endif ?>
<?php endif ?>
<?php echo $block->getChildHtml('ec_cookie') ?>