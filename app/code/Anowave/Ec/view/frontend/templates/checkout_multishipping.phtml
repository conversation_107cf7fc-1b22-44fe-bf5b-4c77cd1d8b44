<script data-ommit="true">

	var info = <?php echo $block->getMultiCheckoutPush($block->getStep()) ?>, data = info.push;

	(function(google_tag_params)
	{
		window.google_tag_params.ecomm_pagetype 	= 'cart';
		window.google_tag_params.ecomm_prodid 		= google_tag_params.ecomm_prodid;
		window.google_tag_params.ecomm_pvalue		= google_tag_params.ecomm_pvalue;
		window.google_tag_params.ecomm_pname		= google_tag_params.ecomm_pname;
		window.google_tag_params.ecomm_totalvalue 	= google_tag_params.ecomm_totalvalue;
		window.google_tag_params.returnCustomer 	= <?php echo $block->getHelper()->getIsReturnCustomer() ?>;

		<?php if ($block->getHelper()->supportDynx()): ?>

			window.google_tag_params.dynx_pagetype 		= 'conversionintent';
			window.google_tag_params.dynx_itemid 		= window.google_tag_params.ecomm_prodid;
			window.google_tag_params.dynx_totalvalue 	= window.google_tag_params.ecomm_totalvalue
		
		<?php endif ?>
		
	})(info.google_tag_params);

	<?php
	/**
	 * Set default checkout data
	 */
	?>
	
	AEC.Checkout.data = data;

	<?php
	/**
	 * Initial checkout step
	 */
	?>

	AEC.Cookie.checkout(data).push(dataLayer);

	<?php
	/**
	 * Track Facebook Pixel Initiate Checkout
	 */
	?>
	
	<?php if ($block->getHelper()->facebook()):?>
	
    	var content_ids = [], content_length = data.ecommerce.checkout.products.length;
    
    	for (var i = 0, l = data.ecommerce.checkout.products.length; i < l; i++)
    	{
    		content_ids.push(data.ecommerce.checkout.products[i].id);
    	}
    
    	(function(callback)
    	{
    		if (AEC.Const.COOKIE_DIRECTIVE)
    		{
    			AEC.CookieConsent.queue(callback).process();
    		}
    		else 
    		{
    			callback.apply(window,[]);
    		}
    	})
    	(
    		(function(info, content_ids, content_length)
    		{
    			return function()
    			{
    				AEC.EventDispatcher.on('ec.facebook.loaded', () => 
    				{
    					fbq("track", "InitiateCheckout", 
        				{
        					value:			info.total,
        					content_type:	'product',
        					content_name: 	'checkout',
        					content_ids:	content_ids,
        					num_items:		content_length,
        					currency: 		'<?php echo $block->getHelper()->getCurrency() ?>'
        				}, 
        				{ eventID: AEC.UUID.generate({ event: 'InitiateCheckout' }) });
    				});
    			}
    		})(info,content_ids,content_length)
    	);
	
	<?php endif ?>
			
	
</script>