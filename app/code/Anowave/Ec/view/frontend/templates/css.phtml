<style>
	.ec-gtm-cookie-directive > div { background: <?php echo $block->getHelper()->getCookieDirectiveBackgroundColor() ?>; color: <?php echo $block->getHelper()->getCookieDirectiveTextColor() ?>  }
	.ec-gtm-cookie-directive > div > div > div a.action.accept { color: <?php echo $block->getHelper()->getCookieDirectiveTextAcceptColor() ?> }
	.ec-gtm-cookie-directive > div > div > div .ec-gtm-cookie-directive-note-toggle { color: <?php echo $block->getHelper()->getCookieDirectiveTextAcceptColor() ?> }
    .ec-gtm-cookie-directive-segments > div input[type="checkbox"] + label:before { border:1px solid <?php echo $block->getHelper()->getCookieDirectiveCheckboxColor() ?> }  
</style>