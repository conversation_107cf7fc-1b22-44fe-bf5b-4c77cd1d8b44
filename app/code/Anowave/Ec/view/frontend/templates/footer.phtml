<script data-ommit="true">

	if (typeof dataLayer !== "undefined")
	{
		AEC.Request.get(AEC.Const.URL + 'datalayer/index/events', {}, response => 
		{
			Object.entries(response.events).forEach(([key, event]) => 
			{
				dataLayer.push(event);
			});
		});
		
		AEC.EventDispatcher.on('ec.facebook.loaded', () => 
		{
			<?php foreach ($block->getHelper()->getFacebookEvents() as $event => $parameters):?>
				if (AEC.facebook) {
					fbq('track', <?php echo json_encode($event) ?>, <?php echo $parameters ?>, { eventID: AEC.UUID.generate( { event: <?php echo json_encode($event) ?> }) });
				}
    
    		<?php endforeach ?>
		});
		
		for (a = 0, b = EC.length; a < b; a++)
		{
			EC[a].apply(this,[dataLayer]);
			EC[a].apply(this,[dataLayerTransport]);
		}

		<?php
	    /**
	     * AdWords Dynamic Remarketing
	     */
		?>
		
		if (window.google_tag_params)
		{
			(function(callback)
			{
				if (AEC.Const.COOKIE_DIRECTIVE)
				{
					if (!AEC.Const.COOKIE_DIRECTIVE_CONSENT_GRANTED)
					{
						AEC.CookieConsent.queue(callback);
					}
					else
					{
						callback.apply(window,[]);
					}
				}
				else 
				{
					callback.apply(window,[]);
				}
			})
			(
				(function(dataLayer)
				{
					return () => 
					{
						AEC.Cookie.remarketing({ event:'fireRemarketingTag', google_tag_params: window.google_tag_params }).push(dataLayer);
					}
				})(dataLayer)
			);
		}

		<?php
		/**
		 * Ad-Blocker fallback
		 */
		?>

		<?php if ($block->getHelper()->usePrivateFallback()): ?>
		
    		window.addEventListener('load', (event) => 
    		{
    			if (!AEC.gtm())
				{
					AEC.Request.post(AEC.Const.URL + 'datalayer/index/datalayer', { data: dataLayerTransport.serialize() }, response => 
					{
						console.log('Google Tag Manager may be blocked by Ad-Blocker or not included in page');
					});
				}
    		});
    		
		<?php endif ?>
	}
	else
	{
		console.log('dataLayer[] is not intialized. Check if GTM is installed properly');
	}
	
</script>