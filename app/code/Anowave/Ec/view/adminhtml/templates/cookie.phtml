<p>EUROPA websites must follow the Commission's guidelines on <a href="http://ec.europa.eu/ipg/basics/legal/data_protection/index_en.htm" target="_blank">privacy and data protection</a> and inform users that cookies are not being used to gather information unnecessarily.</p>
<p>The <a href="http://eur-lex.europa.eu/LexUriServ/LexUriServ.do?uri=CELEX:32002L0058:EN:HTML" target="_blank">ePrivacy directive</a> – more specifically Article 5(3) – requires prior informed consent for storage or for access to information stored on a user's terminal equipment. In other words, you must ask users if they agree to most cookies and similar technologies (e.g. web beacons, Flash cookies, etc.) before the site starts to use them.</p>
<p>For consent to be valid, it must be informed, specific, freely given and must constitute a real indication of the individual's wishes.</p>
<strong>HOW TO CONFIGURE</strong>
<div>
	<a href="javascript:void(0)" data-gdpr="true">User Guide</a>
</div>
<div data-gdpr-guide="true" style="display:none">
	<p>If <PERSON><PERSON> is enabled, there are changes that you need to do in your GTM account</p>
	<div style="padding:10px 0px 10px 0px">
		<strong>Universal Analytics tag</strong>
	</div>
	<ol>
		<li>Go to GTM -> Triggers and create a new trigger called <strong>Event Equals Cookie Consent Granted</strong>, it must be of type <strong>custom event</strong> and for event value put: <strong>cookieConsentGranted</strong></li>
		<li>Go to GTM -> Tags end edit your Universal Analytics tag, under firing options remove "All pages" and add "Event Equals Cookie Consent Granted"</li>
		<li>Save configuration and publish new container version</li>
	</ol>
	<p>With this configuration, the UA tag will fire whenever <strong>cookieConsentGranted</strong> event get pushed into dataLayer. This is done when customer accepts cookies or automatically on every other page (presuming consent was granted)</p>
	<br /><br />
	<strong style="color:#c70000">IMPORTANT:</strong> If Cookie Consent is deactivated from configuration your MUST revert the changes described above, otherwise UA tag will no longer fire and tracking will not work anymore.
</div>
<script>
	require(['jquery','Magento_Ui/js/modal/modal'],function($,modal)
	{
		$('a[data-gdpr]').on('click', function()
		{
			var content = $('[data-gdpr-guide]').html();

			$('<div />').html(content).modal(
			{
	            title: 'Cookie Consent User Guide',
	            autoOpen: true,
	            closed: function () {},
	            buttons: 
		        [
			        {
		                text: 'I understand',
		                attr: 
			            {
		                    'data-action': 'confirm'
		                },
		                'class': 'action-primary'
		            }
		         ]
	         });

			return false;
		});
	});
</script>