# Changelog

All notable changes to this project will be documented in this file.

## [2.0.4.21081] - 04/07/2022

### Fixed

- Added fixed domains for broader testing purposes

## [2.0.3] - 10/06/2022

### Fixed

- Fixed an issue with multi-store license 

## [2.0.2] - 08/06/2022

### Added

- Added extended support for wildcards

## [2.0.1]

### Fixed

- PHP 8.1 compatibility issues

## [2.0.0]

### Added

- Added support for PHP 8.1

## [1.0.9]

### Added

- Added extended support for widlcards

## [1.0.8]

### Added

- Added custom wildcard license support

## [1.0.7]

### Added

- Extended range of wildcard domains as follows

*.local
*.cloud
*.test
*.magento

## [1.0.6]

### Added

- Added limited support for wildcard domains.

## [1.0.5]

### Added

- Added PHP 7.4 support

## [1.0.4]

### Fixed

- Change afterGetComment() parameter declaration from (compatibility issue)

\Magento\Config\Model\Config\Structure\Element\Field\Interceptor

to \Magento\Config\Model\Config\Structure\Element\Field

## [1.0.3]

### Fixed

- Removed Undefined offset 1 on eval() error.

## [1.0.0]

- Initial version