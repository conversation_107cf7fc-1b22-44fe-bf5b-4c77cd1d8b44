<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <preference for="Anowave\Ec\Model\Api" type="Anowave\Ec4\Model\Api" />
	<type name="Anowave\Ec\Observer\Refund">
		<plugin name="ec4" type="Anowave\Ec4\Plugin\Refund" sortOrder="1" />
	</type>
	<type name="Anowave\Ec\Model\Api\Measurement\Protocol">
		<plugin name="ec4" type="Anowave\Ec4\Plugin\Api\Measurement\Protocol" sortOrder="1" />
	</type>
	<type name="Anowave\Ec\Block\Comment">
		<plugin name="ec4" type="Anowave\Ec4\Plugin\Comment" sortOrder="1" />
	</type>
</config>