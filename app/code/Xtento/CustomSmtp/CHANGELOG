Xtento_CustomSmtp

-------------
  CHANGELOG
-------------

===== 1.0.0 =====
 * Initial stable M2 release

===== 1.0.1 =====
 * Updated Xtento_XtCore to version 2.1

===== 1.0.2 =====
 * Fixed "From header set twice" error

===== 1.0.3 =====
 * Updated Xtento_XtCore to version 2.2.0

===== 1.0.4 =====
 * Fixed an issue where configuration is empty and emails are supposed to be logged only

===== 1.0.5 =====
 * Updated Xtento_XtCore to version 2.3.0
 * Confirmed compatibility with Magento CE 2.2.5 / EE 2.2.5

===== 1.0.6 =====
 + Added support for UTF-8 encoded email subjects

===== 1.0.7 =====
 * Removed (not working) full text search in grid

===== 1.0.8 =====
 * Fixed issue when trying to sort grid by from/to columns

===== 1.0.9 =====
 * Established compatibility with Magento 2.3 (ZF1 Mail -> ZF2 Mail)
 * Removed "return path" as not supported by Magento 2.3 anymore

===== 1.1.0 =====
 * SMTP password is now stored encrypted in the database

===== 1.1.1 =====
 * Fixed "Send test email" functionality when the configuration has been saved already and the password is thus encrypted

===== 1.1.2 =====
 * Fixed issue when using "reply to" feature

===== 1.1.3 =====
 * Fixed error 'Call to undefined method Magento\Framework\Mail\Message\Interceptor::getFrom()'

===== 1.1.4 =====
 * Compatibility release for Magento 2.2.8

===== 1.1.5 =====
 * Fixed default settings for SendGrid

===== 1.1.6 =====
 * Updated Xtento_XtCore to version 2.5.0

===== 1.1.7 =====
 * Fixed error "Please correct the email address"

===== 1.1.8 =====
 * Improved "Test email configuration" functionality by providing more descriptive error messages for certain situations

===== 1.1.9 =====
 * Updated / fixed built-in settings for Mailgun

===== 1.2.0 =====
 * Module code updated to comply with Magento Coding Standard as of August 2019
 * Removed silenced errors ("@function") entirely. Improved logging in such cases so those errors are logged/reported.
 * Logging improvements

===== 1.2.1 =====
 * Updated Xtento_XtCore to version 2.7.0

===== 1.2.2 =====
 * Established compatibility with Magento 2.2.10 / 2.3.3
 * Established compatibility with PHP 7.3
 * Updated Xtento_XtCore to version 2.8.0
 * Fixed email errors after Magento update

===== 1.2.3 =====
 * Fixed "Test E-Mail" functionality for Magento >=2.3.3

===== 1.2.4 =====
 * Established compatibility with Magento patch "EmailMessageInterface backward compatibility issue patch for Magento 2.3.3"

===== 1.2.5 =====
 * Established compatibility with Magento 2.3.4 (Error: Invalid header value detected ...)
 * Fixed email text being not readable/displayable in Custom SMTP log (=0A=0A=0A=0A=...)

===== 1.2.6 =====
 * Established compatibility with Amazon SES and other SMTP servers for Magento installations using version 2.3.3 or newer (Error: "Transaction failed: Expected disposition, got =")

===== 1.2.7 =====
 * Updated Xtento_XtCore to version 2.9.0

===== 1.2.8 =====
 + Added SMTP configuration presets for Planisys DMDS
 * Established compatibility with Magento 2.4.0 and 2.3.5-p2
 * Established compatibility with PHP 7.4, dropped support for PHP 5.x
 * Updated Xtento_XtCore to version 2.10.0

===== 1.2.9 =====
 + Added ability to set "From Name" when using "Override From" (only for versions newer than Magento 2.3.3)

===== 1.3.0 =====
 * Fixed an issue where extension related images/JS cannot be loaded in the Magento backend
 * Updated Xtento_XtCore to version 2.11.0 ("Added Content Security Policy (CSP) support. XTENTO resources are whitelisted.")

===== 1.3.1 =====
 * Updated Xtento_XtCore to version 2.12.0 ("Removed phpinfo from debug information section due to possibly sensitive information being disclosed")

===== 1.3.2 =====
 * Confirmed compatibility with Magento 2.3.7-p1 / 2.4.2-p2 / 2.4.3

===== 1.3.3 =====
 * Established compatibility with Magento 2.3.7-p3 / 2.4.3-p2 / 2.4.4
 * Established compatibility with PHP 8.1
 * Updated Xtento_XtCore to version 2.13.0 ("PHP 8.1 compatibility")

===== 1.3.4 =====
 * Updated Xtento_XtCore to version 2.14.0 ("Further PHP 8.1 fixes")

===== 1.3.5 =====
 * Converted database install scripts to db_schema.xml - please check this upgrade carefully and take a DB backup before upgrading
 * Updated Xtento_XtCore to version 2.16.0
 * Dropped support for Magento 2.2.x. Earliest supported version is 2.3.x now.

===== 1.3.6 =====
 * Established compatibility with Magento 2.4.5, 2.4.4-p1, 2.4.3-p3, 2.3.7-p4
 * Updated Xtento_XtCore to version 2.17.0 ("Fixed error "Area code not set" during installation/upgrade")

===== 1.3.7 =====
 * Established compatibility with Magento 2.4.6 / 2.4.5-p2 / 2.4.4-p3
 * Established compatibility with PHP 8.2
 * Updated Xtento_XtCore to version 2.18.0 ("Magento 2.4.6 and PHP 8.2 compatibility")
 * Dropped support for Magento 2.2.x. Earliest supported version is 2.3.x now.