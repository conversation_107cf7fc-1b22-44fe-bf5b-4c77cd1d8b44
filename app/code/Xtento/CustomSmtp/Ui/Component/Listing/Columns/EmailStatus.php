<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Ui/Component/Listing/Columns/EmailStatus.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Ui\Component\Listing\Columns;

/**
 * @api
 */
class EmailStatus extends \Magento\Ui\Component\Listing\Columns\Column
{
    const NAME = 'status';

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     *
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as & $item) {
                $class = '';
                $text = '';
                switch ($item['status']) {
                    case 0:
                        $class = 'grid-severity-critical';
                        $text = __('Failed');
                        break;
                    case 1:
                        $class = 'grid-severity-notice';
                        $text = __('Sent');
                        break;
                    case 2:
                        $class = 'grid-severity-minor';
                        $text = __('Logged only');
                        break;
                }
                $item['status'] = '<span class="' . $class . '"><span>' . $text . '</span></span>';
            }
        }

        return $dataSource;
    }
}
