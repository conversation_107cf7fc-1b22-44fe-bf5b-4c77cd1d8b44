<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2019-01-09T14:22:23+00:00
 * File:          app/code/Xtento/CustomSmtp/Ui/Component/Listing/Columns/ViewAction.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Ui\Component\Listing\Columns;

use Magento\Framework\UrlInterface;
use Magento\Framework\View\Element\UiComponent\ContextInterface;
use Magento\Framework\View\Element\UiComponentFactory;
use Magento\Ui\Component\Listing\Columns\Column;

/**
 * Class ViewAction
 */
class ViewAction extends Column
{
    /**
     * @var UrlInterface
     */
    protected $urlBuilder;

    /**
     * Constructor
     *
     * @param ContextInterface $context
     * @param UiComponentFactory $uiComponentFactory
     * @param UrlInterface $urlBuilder
     * @param array $components
     * @param array $data
     */
    public function __construct(
        ContextInterface $context,
        UiComponentFactory $uiComponentFactory,
        UrlInterface $urlBuilder,
        array $components = [],
        array $data = []
    ) {
        $this->urlBuilder = $urlBuilder;
        parent::__construct($context, $uiComponentFactory, $components, $data);
    }

    /**
     * Prepare Data Source
     *
     * @param array $dataSource
     *
     * @return array
     */
    public function prepareDataSource(array $dataSource)
    {
        if (isset($dataSource['data']['items'])) {
            foreach ($dataSource['data']['items'] as & $item) {
                if (isset($item['id'])) {
                    $hideButtonJs = "<script>
                                    jQuery('.modal-footer .action-secondary.action-dismiss').hide();
                                    setTimeout(function(){ 
                                        jQuery('.modal-popup').scrollTop(0); 
                                        jQuery('.modal-popup .modal-inner-wrap').width('90%');
                                        }, 250);
                                    </script>";
                    $emailBody = $this->removeStylingFromBody($item['body']);
                    $item[$this->getData('name')] = [
                        'view' => [
                            'href' => '#',
                            'label' => __('Preview'),
                            'confirm' => [
                                'title' => sprintf('%s: %s', __('E-Mail ID'), $item['id']),
                                'message' => $hideButtonJs . sprintf('<strong>%s:</strong> %s<br/><strong>%s:</strong> %s<br/><strong>%s:</strong> %s<br/><strong>%s:</strong> %s<br/><br/><br/>%s', __('Date'), $item['created_at'], __('From'), $item['from'], __('To'), $item['to'], __('Subject'), $item['subject'], $emailBody)
                            ],

                        ]
                    ];
                }
            }
        }

        return $dataSource;
    }

    protected function removeStylingFromBody($body)
    {
        $body = preg_replace('/(<(script|style)\b[^>]*>).*?(<\/\2>)/s', "", $body);
        $body = preg_replace('/(<body\b[^>]*>).*?(<\/\2>)/is', "$2", $body);
        return $body;
    }
}
