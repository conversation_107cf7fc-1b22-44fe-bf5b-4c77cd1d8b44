<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Controller/Adminhtml/Emaillog/Index.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Controller\Adminhtml\Emaillog;

use Magento\Backend\App\Action;

class Index extends \Magento\Backend\App\Action
{
    /**
     * @var \Xtento\CustomSmtp\Helper\Module
     */
    protected $moduleHelper;

    /**
     * Index constructor.
     *
     * @param Action\Context $context
     * @param \Xtento\CustomSmtp\Helper\Module $moduleHelper
     */
    public function __construct(
        Action\Context $context,
        \Xtento\CustomSmtp\Helper\Module $moduleHelper
    ) {
        parent::__construct($context);
        $this->moduleHelper = $moduleHelper;
    }

    /**
     * @param $resultPage \Magento\Backend\Model\View\Result\Page
     */
    protected function updateMenu($resultPage)
    {
        $resultPage->setActiveMenu('Xtento_CustomSmtp::emaillog');
        $resultPage->addBreadcrumb(__('Reports'), __('Reports'));
        $resultPage->addBreadcrumb(__('E-Mail Log'), __('E-Mail Log'));
        $resultPage->getConfig()->getTitle()->prepend(__('E-Mail Log'));
    }

    /**
     * @return \Magento\Backend\Model\View\Result\Page
     */
    public function execute()
    {
        if (!$this->moduleHelper->isModuleEnabled() || !$this->moduleHelper->confirmEnabled(true)) {
            /** @var \Magento\Framework\Controller\Result\Raw $resultPage */
            $resultPage = $this->resultFactory->create(\Magento\Framework\Controller\ResultFactory::TYPE_RAW);
            $resultPage->setContents(__("Module is disabled. Go to System > XTENTO Extensions > Custom SMTP to enable it."));
            return $resultPage;
        }
        if ($this->moduleHelper->isWrongEdition()) {
            $this->messageManager->addErrorMessage(
                __(
                    'Attention: The installed extension version is not compatible with the Enterprise Edition of Magento. The compatibility of the currently installed extension version has only been confirmed with the Community Edition of Magento. Please go to www.xtento.com to purchase or download the Enterprise Edition of this extension in our store if you\'ve already purchased it.'
                )
            );
        }

        /** @var \Magento\Backend\Model\View\Result\Page $resultPage */
        $resultPage = $this->resultFactory->create(\Magento\Framework\Controller\ResultFactory::TYPE_PAGE);
        $this->updateMenu($resultPage);
        return $resultPage;
    }

    /**
     * Check if user has enough privileges
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Xtento_CustomSmtp::emaillog');
    }
}
