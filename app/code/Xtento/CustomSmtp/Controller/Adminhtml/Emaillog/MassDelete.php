<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Controller/Adminhtml/Emaillog/MassDelete.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Controller\Adminhtml\Emaillog;

use Magento\Backend\App\Action;
use Magento\Ui\Component\MassAction\Filter;
use Xtento\CustomSmtp\Model\ResourceModel\EmailLog\CollectionFactory;

class MassDelete extends \Magento\Backend\App\Action
{
    /**
     * @var Filter
     */
    protected $filter;

    /**
     * @var CollectionFactory
     */
    protected $emailLogCollectionFactory;

    /**
     * MassDelete constructor.
     *
     * @param Action\Context $context
     * @param Filter $filter
     * @param CollectionFactory $emailLogCollectionFactory
     */
    public function __construct(
        Action\Context $context,
        Filter $filter,
        CollectionFactory $emailLogCollectionFactory
    ) {
        parent::__construct($context);
        $this->filter = $filter;
        $this->emailLogCollectionFactory = $emailLogCollectionFactory;
    }

    /**
     * Mass delete action
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(\Magento\Framework\Controller\ResultFactory::TYPE_REDIRECT);

        $collection = $this->filter->getCollection($this->emailLogCollectionFactory->create());
        $collectionSize = $collection->getSize();

        if ($collectionSize < 1) {
            $this->messageManager->addErrorMessage(__('Please select log entries to delete.'));
            $resultRedirect->setPath('*/*/');
            return $resultRedirect;
        }
        try {
            foreach ($collection as $item) {
                $item->delete();
            }
            $this->messageManager->addSuccessMessage(
                __('Total of %1 log entries were successfully deleted.', $collectionSize)
            );
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        $resultRedirect->setPath('*/*/');
        return $resultRedirect;
    }

    /**
     * Check if user has enough privileges
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Xtento_CustomSmtp::emaillog');
    }
}
