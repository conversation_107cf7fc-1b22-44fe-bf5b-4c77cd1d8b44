<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Controller/Adminhtml/Emaillog/ClearLog.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Controller\Adminhtml\Emaillog;

use Magento\Backend\App\Action;

class ClearLog extends \Magento\Backend\App\Action
{
    /**
     * @var \Xtento\CustomSmtp\Model\ResourceModel\EmailLog
     */
    protected $emailLog;

    /**
     * ClearLog constructor.
     *
     * @param Action\Context $context
     * @param \Xtento\CustomSmtp\Model\ResourceModel\EmailLog $emailLog
     */
    public function __construct(
        Action\Context $context,
        \Xtento\CustomSmtp\Model\ResourceModel\EmailLog $emailLog
    ) {
        parent::__construct($context);
        $this->emailLog = $emailLog;
    }

    /**
     * Mass delete action
     *
     * @return \Magento\Backend\Model\View\Result\Redirect
     */
    public function execute()
    {
        try {
            $this->emailLog->deleteAllEntries();
            $this->messageManager->addSuccessMessage(__('All log entries were successfully deleted.'));
        } catch (\Exception $e) {
            $this->messageManager->addErrorMessage($e->getMessage());
        }

        /** @var \Magento\Backend\Model\View\Result\Redirect $resultRedirect */
        $resultRedirect = $this->resultFactory->create(\Magento\Framework\Controller\ResultFactory::TYPE_REDIRECT);
        $resultRedirect->setPath('*/*/');
        return $resultRedirect;
    }

    /**
     * Check if user has enough privileges
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Xtento_CustomSmtp::emaillog');
    }
}
