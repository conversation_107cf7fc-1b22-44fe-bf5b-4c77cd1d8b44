<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2019-10-14T12:44:43+00:00
 * File:          app/code/Xtento/CustomSmtp/Controller/Adminhtml/Configuration/Test.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Controller\Adminhtml\Configuration;

use Magento\Backend\App\Action;
use Magento\Framework\Controller\ResultFactory;
use Xtento\XtCore\Helper\Utils;

class Test extends \Magento\Backend\App\Action
{
    /**
     * @var Utils
     */
    protected $utilsHelper;

    /**
     * @var \Magento\Framework\Encryption\EncryptorInterface
     */
    protected $encryptor;

    /**
     * @var \Xtento\CustomSmtp\Helper\Module
     */
    protected $moduleHelper;

    /**
     * Test constructor.
     *
     * @param Action\Context $context
     * @param Utils $utilsHelper
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param \Xtento\CustomSmtp\Helper\Module $moduleHelper
     */
    public function __construct(
        Action\Context $context,
        Utils $utilsHelper,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        \Xtento\CustomSmtp\Helper\Module $moduleHelper
    ) {
        parent::__construct($context);
        $this->utilsHelper = $utilsHelper;
        $this->encryptor = $encryptor;
        $this->moduleHelper = $moduleHelper;
    }

    /**
     * @return \Magento\Framework\Controller\Result\Raw
     */
    public function execute()
    {
        /** @var \Magento\Framework\Controller\Result\Raw $resultRaw */
        $resultRaw = $this->resultFactory->create(ResultFactory::TYPE_RAW);

        // Check if "Use Custom SMTP" is enabled
        if (!$this->moduleHelper->useCustomSmtpFunctionality($this->moduleHelper->getCurrentStoreId())) {
            $resultRaw->setContents(__('Connection test failed!<br/><br/>Reason: You forgot to enable the module or to enable your custom SMTP settings.<br/><br/>Solution: Here, on this page, in the configuration, make sure "Module Enabled" is set to "Yes" and "Use Custom SMTP Settings" is set to "Yes" as well. Then - <b>important</b> - click "Save Configuration". Then, try using the "Send Test E-Mail" button again.'));
            return $resultRaw;
        }

        // E-Mail to send from/to
        $fromEmail = $this->getRequest()->getPost('from_email');
        $toEmail = $this->getRequest()->getPost('to_email');
        // E-Mail Configuration
        $host = $this->getRequest()->getPost('address');
        $password = trim($this->getRequest()->getPost('password'));
        if ($password === '******') {
            // Already encrypted (saved before), get from DB
            $emailConfig = $this->moduleHelper->getEmailConfig($this->moduleHelper->getCurrentStoreId());
            $password = trim($this->encryptor->decrypt($emailConfig['server_password']));
        }

        if (version_compare($this->utilsHelper->getMagentoVersion(), '2.2.8', '>=')) {
            // >=M2.2.8
            if (empty($this->getRequest()->getPost('auth_method'))) {
                // No authentication required
                $parameters = [
                    'host' => $host,
                    'port' => intval($this->getRequest()->getPost('port')),
                    'override_configuration' => true
                ];
            } else {
                $parameters = [
                    'host' => $host,
                    'port' => intval($this->getRequest()->getPost('port')),
                    'connection_class' => strtolower($this->getRequest()->getPost('auth_method')),
                    'connection_config' => [
                        'username' => trim($this->getRequest()->getPost('username')),
                        'password' => $password,
                        'ssl' => strtolower($this->getRequest()->getPost('connection_security')),
                    ],
                    'override_configuration' => true
                ];
            }

            if (version_compare($this->utilsHelper->getMagentoVersion(), '2.3.3', '>=')) {
                $parameters = array_merge($parameters, ['from' => $fromEmail, 'to' => $toEmail]);
                $data = ['message' => false, 'parameters' => $parameters];
            } else {
                $data = ['parameters' => $parameters];
            }
        } else {
            // <=M2.2
            $parameters = [
                'port' => intval($this->getRequest()->getPost('port')),
                'username' => trim($this->getRequest()->getPost('username')),
                'password' => $password,
                'auth' => strtolower($this->getRequest()->getPost('auth_method')),
                'ssl' => strtolower($this->getRequest()->getPost('connection_security')),
                'override_configuration' => true
            ];
            $data = ['host' => $host, 'parameters' => $parameters];
        }

        $customTransport = $this->_objectManager->create('\Magento\Framework\Mail\TransportInterfaceFactory')->create($data);
        $testResult = '';
        try {
            $testResult = $customTransport->testConfiguration($fromEmail, $toEmail);
        } catch (\Exception $e) {
            $resultRaw->setContents(__('Connection test failed!<br/><br/>%1<br/><br/>Results:<br/><br/>%2<br/><br/>Stack Trace:<br/><br/>%3', $testResult, $e->getMessage(), nl2br($e->getTraceAsString())));
            return $resultRaw;
        }
        $resultRaw->setContents(__('Connection test finished. Do not forget to save the configuration to apply your settings!<br/><br/>Log: %1', $testResult));
        return $resultRaw;
    }

    /**
     * Check if user has enough privileges
     * @return bool
     */
    protected function _isAllowed()
    {
        return $this->_authorization->isAllowed('Xtento_CustomSmtp::configuration');
    }
}
