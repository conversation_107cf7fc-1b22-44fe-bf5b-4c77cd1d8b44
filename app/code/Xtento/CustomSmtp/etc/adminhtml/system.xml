<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Config:etc/system_file.xsd">
    <system>
        <section id="customsmtp" translate="label" sortOrder="125" showInDefault="1" showInStore="1" showInWebsite="1">
            <tab>xtento</tab>
            <label>Custom SMTP</label>
            <resource>Xtento_CustomSmtp::configuration</resource>
            <group id="general" translate="label" showInDefault="1" showInWebsite="0" showInStore="0" sortOrder="10">
                <label>Extension Configuration</label>
                <attribute type="expanded">1</attribute>
                <frontend_model>Xtento\XtCore\Block\System\Config\Form\Xtento\Module</frontend_model>
                <fieldset_css>Xtento_CustomSmtp|Xtento\CustomSmtp\Model\System\Config\Backend\Server</fieldset_css>
                <comment>
                    <![CDATA[
                    <script>
                    requirejs(['jquery', 'prototype'], function() {
                        function setCommentHtml(message) {
                            if (Prototype.Browser.IE) {
                                $('customsmtp_general_serial').next('p').outerHTML = message;
                            } else {
                                $('customsmtp_general_serial').next('p').innerHTML = message;
                            }
                        }

                        function initSystemConfiguration() {
                            if ($('customsmtp_general_server_name')) {
                                $('customsmtp_general_server_name').style.backgroundColor='#f0f0f0';
                                $('customsmtp_general_server_name').disable();
                                if ($('row_customsmtp_general_server')) {
                                    $('row_customsmtp_general_server').hide();
                                }
                            }

                            if ($('customsmtp_general_serial') && $('customsmtp_general_serial').value !== '' && $('customsmtp_general_server') && $('customsmtp_general_server').value !== '') {
                                if ($('customsmtp_general_serial').value.length !== 40) {
                                    setCommentHtml('<a href="http://www.xtento.com" target="_blank"><img src="//www.xtento.com/license/check/?d='+$('customsmtp_general_server').value+'" border="0" style="margin-top: 4px;"/></a>');
                                } else {
                                    if (typeof $('customsmtp_general_serial').nextSibling !== 'undefined' && typeof $('customsmtp_general_serial').nextSibling.nextSibling !== 'undefined') {
                                        setCommentHtml('<a href="http://www.xtento.com" target="_blank"><img src="//www.xtento.com/license/check/?d='+$('customsmtp_general_server').value+'" border="0" style="margin-top: 4px;"/></a>');
                                    }
                                }
                            } else {
                                if ($('customsmtp_general_server').value !== '') {
                                    setCommentHtml('<a href="http://www.xtento.com" target="_blank"><img src="//www.xtento.com/license/check/?d='+$('customsmtp_general_server').value+'" border="0" style="margin-top: 4px;"/></a>');
                                }
                            }
                        }
                        jQuery(document).ready(function () {
                            initSystemConfiguration();
                        });

                        // Module specific functionality
                        jQuery('#customsmtp_email_config_load_configuration').change(function() {
                            firstValue = jQuery('#customsmtp_email_config_load_configuration option:first').val();
                            selectedValue = jQuery(this).val();
                            smtpConfiguration = selectedValue.split('|');
                            jQuery('#customsmtp_email_config_server_address').val(smtpConfiguration[1]);
                            jQuery('#customsmtp_email_config_server_port').val(smtpConfiguration[2]);
                            jQuery('#customsmtp_email_config_server_auth_method').val(smtpConfiguration[3]);
                            jQuery('#customsmtp_email_config_server_connection_security').val(smtpConfiguration[4]);
                            // Reset dropdown value
                            if (selectedValue != firstValue) {
                                jQuery('#customsmtp_email_config_load_configuration').val(firstValue);
                            }
                        });
                    });
                    </script>
                    ]]>
                </comment>
                <field id="heading" translate="label" sortOrder="10" showInDefault="1">
                    <label>General Configuration</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field\Heading</frontend_model>
                </field>
                <field id="server_name" translate="label" sortOrder="20" showInDefault="1">
                    <label>Server Name</label>
                    <frontend_model>Xtento\XtCore\Block\System\Config\Form\Xtento\Servername</frontend_model>
                </field>
                <field id="serial" translate="label" sortOrder="30" showInDefault="1">
                    <label>License Key</label>
                    <comment>Please enter your license key here.</comment>
                    <backend_model>Xtento\XtCore\Model\System\Config\Backend\License</backend_model>
                </field>
                <field id="enabled" translate="label comment" type="select" sortOrder="40" showInDefault="1">
                    <label>Module Enabled</label>
                    <comment>A valid license key is required in order to enable the module.</comment>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                </field>
                <field id="server" translate="label" type="hidden" sortOrder="45" showInDefault="1">
                    <label/>
                    <backend_model>Xtento\CustomSmtp\Model\System\Config\Backend\Server</backend_model>
                </field>
            </group>
            <group id="email_config" translate="label" showInDefault="1" showInWebsite="1" showInStore="1" sortOrder="50">
                <label>E-Mail/SMTP Configuration</label>
                <attribute type="expanded">1</attribute>
                <field id="general_settings" translate="label" sortOrder="5" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>General SMTP Settings</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field\Heading</frontend_model>
                </field>
                <field id="enabled" translate="label comment" type="select" sortOrder="10" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Use custom SMTP settings</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled, below custom SMTP settings will be used</comment>
                </field>
                <field id="log_emails" translate="label comment" type="select" sortOrder="20" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Log all E-Mails sent</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>Log all emails sent by Magento. Useful for debugging purposes.</comment>
                </field>
                <field id="disable_emails" translate="label comment" type="select" sortOrder="30" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Disable E-Mail sending</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled, no emails will be sent. They will only be logged, if logging is enabled.</comment>
                </field>
                <field id="clear_log_after" translate="label comment" type="text" sortOrder="40" showInDefault="1">
                    <label>Clear Log After X Days</label>
                    <comment>E-Mails older than X days will automatically be deleted. Recommended to keep database tables small. If you do not want to clear logs automatically, set this to 0.</comment>
                </field>
                <field id="smtp_settings" translate="label" sortOrder="50" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>SMTP Server Settings</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field\Heading</frontend_model>
                </field>
                <field id="load_configuration" translate="label comment" type="select" sortOrder="60" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Load Configuration</label>
                    <comment>Automatically load settings for pre-defined providers</comment>
                    <source_model>Xtento\CustomSmtp\Model\System\Config\Source\AutoConfig</source_model>
                </field>
                <field id="server_address" translate="label comment" type="text" sortOrder="70" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>IP / Hostname</label>
                    <comment>IP address or hostname of SMTP server</comment>
                </field>
                <field id="server_port" translate="label comment" type="text" sortOrder="80" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Port</label>
                    <comment>Common ports: 25, 465 (SSL), 587 (TLS). Check your hosting providers setup instructions.</comment>
                </field>
                <field id="server_connection_security" translate="label comment" type="select" sortOrder="85" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Connection Security</label>
                    <source_model>Xtento\CustomSmtp\Model\System\Config\Source\ConnectionSecurity</source_model>
                    <comment>Encryption Settings for E-Mail sending</comment>
                </field>
                <field id="server_auth_method" translate="label comment" type="select" sortOrder="90" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Authentication Method</label>
                    <source_model>Xtento\CustomSmtp\Model\System\Config\Source\AuthMethod</source_model>
                    <comment>Authentication method for login</comment>
                </field>
                <field id="server_username" translate="label comment" type="text" sortOrder="100" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Username</label>
                    <comment>Username for SMTP Server</comment>
                </field>
                <field id="server_password" translate="label comment" type="obscure" sortOrder="110" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Password</label>
                    <comment>Password for SMTP Server</comment>
                    <backend_model>Magento\Config\Model\Config\Backend\Encrypted</backend_model>
                </field>
                <field id="email_header_settings" translate="label" sortOrder="115" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>E-Mail Headers</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field\Heading</frontend_model>
                </field>
                <field id="override_from" translate="label comment" type="select" sortOrder="117" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Override "From" header</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesno</source_model>
                    <comment>If enabled, emails sent from Magento will be sent "from" the specified email address.</comment>
                </field>
                <field id="override_from_email" translate="label comment" type="text" sortOrder="118" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>From E-Mail</label>
                    <depends>
                        <field id="override_from">1</field>
                    </depends>
                    <validate>validate-email</validate>
                    <comment>If "Override From" is enabled, this is the "From E-Mail Address" used when sending emails.</comment>
                </field>
                <field id="override_from_name" translate="label comment" type="text" sortOrder="119" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>From Name</label>
                    <depends>
                        <field id="override_from">1</field>
                    </depends>
                    <comment>If "Override From" is enabled, this is the "From Name" used when sending emails.</comment>
                </field>
                <field id="reply_to_header" translate="label comment" type="select" sortOrder="120" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Set Reply-To Header</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesnocustom</source_model>
                    <comment>If enabled, the "Reply-To" header will be set, specifying an email address where replies should be sent to.</comment>
                </field>
                <field id="reply_to_email" translate="label comment" type="text" sortOrder="125" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Reply-To E-Mail</label>
                    <depends>
                        <field id="reply_to_header">2</field>
                    </depends>
                    <validate>validate-email</validate>
                    <!--<backend_model>Magento\Config\Model\Config\Backend\Email\Address</backend_model>-->
                </field>
                <!--<field id="return_path_header" translate="label comment" type="select" sortOrder="130" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Set Return-Path Header</label>
                    <source_model>Magento\Config\Model\Config\Source\Yesnocustom</source_model>
                    <comment>If enabled, the "Return-Path" header will be set, specifying an email address where emails will be returned if they can't be delivered.</comment>
                </field>
                <field id="return_path_email" translate="label comment" type="text" sortOrder="140" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Return-Path Email</label>
                    <depends>
                        <field id="return_path_header">2</field>
                    </depends>
                    <validate>validate-email</validate>
                </field>-->
                <field id="debug_settings" translate="label" sortOrder="150" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>Test Configuration: Send Test E-Mail</label>
                    <frontend_model>Magento\Config\Block\System\Config\Form\Field\Heading</frontend_model>
                </field>
                <field id="from_email" translate="label comment" type="text" sortOrder="160" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>From E-Mail Address</label>
                    <frontend_class>validate-email</frontend_class>
                </field>
                <field id="to_email" translate="label comment" type="text" sortOrder="170" showInDefault="1" showInWebsite="1" showInStore="1">
                    <label>To E-Mail Address</label>
                    <frontend_class>validate-email</frontend_class>
                </field>
                <field id="test_button" translate="label comment" sortOrder="180" showInDefault="1" showInWebsite="1" showInStore="1">
                    <frontend_model>Xtento\CustomSmtp\Block\System\Config\TestButton</frontend_model>
                    <comment>After entering all settings above, enter from/to email and click this button. Debugging results from sending the test mail will be shown. NOTE: E-Mail Headers supplied above will not be used for test, only SMTP settings are checked.</comment>
                </field>
            </group>
        </section>
    </system>
</config>
<!--
/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2020-08-29T10:31:04+00:00
 * File:          app/code/Xtento/CustomSmtp/etc/adminhtml/system.xml
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */
-->