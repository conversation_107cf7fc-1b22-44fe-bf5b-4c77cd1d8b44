<?xml version="1.0"?>
<schema xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:framework:Setup/Declaration/Schema/etc/schema.xsd">
  <table name="xtento_customsmtp_emaillog" resource="default" engine="innodb" comment="Xtento_CustomSmtp Mail Log">
    <column xsi:type="int" name="id" padding="10" unsigned="true" nullable="false" identity="true" comment="ID"/>
    <column xsi:type="text" name="subject" nullable="true" comment="subject"/>
    <column xsi:type="text" name="from" nullable="true" comment="from"/>
    <column xsi:type="text" name="to" nullable="true" comment="to"/>
    <column xsi:type="text" name="body" nullable="true" comment="body"/>
    <column xsi:type="text" name="debug_messages" nullable="true" comment="debug_messages"/>
    <column xsi:type="timestamp" name="created_at" on_update="true" nullable="true" default="CURRENT_TIMESTAMP" comment="created_at"/>
    <column xsi:type="int" name="status" padding="11" unsigned="false" nullable="false" identity="false" comment="status"/>
    <constraint xsi:type="primary" referenceId="PRIMARY">
      <column name="id"/>
    </constraint>
    <index referenceId="CUSTOMSMTP_EMAILLOG_FROM_TO_SUBJECT_BODY_DEBUG_MESSAGES" indexType="fulltext">
      <column name="from"/>
      <column name="to"/>
      <column name="subject"/>
      <column name="body"/>
      <column name="debug_messages"/>
    </index>
  </table>
</schema>

<!--
/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2022-06-23T21:40:47+00:00
 * File:          app/code/Xtento/CustomSmtp/etc/db_schema.xml
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */
-->