<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2019-01-09T14:22:23+00:00
 * File:          app/code/Xtento/CustomSmtp/Model/ResourceModel/EmailLog/Collection.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Model\ResourceModel\EmailLog;

class Collection extends \Magento\Framework\Model\ResourceModel\Db\Collection\AbstractCollection
{
    protected $_idFieldName = 'id';

    /**
     * Initialize resource collection
     *
     * @return void
     */
    public function _construct()
    {
        $this->_init('Xtento\CustomSmtp\Model\EmailLog', 'Xtento\CustomSmtp\Model\ResourceModel\EmailLog');
    }

    public function addOrder($field, $direction = self::SORT_ORDER_DESC)
    {
        if ($field == 'from') {
            $field = '`from`';
        }
        if ($field == 'to') {
            $field = '`to`';
        }
        return parent::addOrder($field, $direction);
    }
}
