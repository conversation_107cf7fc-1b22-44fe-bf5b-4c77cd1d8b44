<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Model/ResourceModel/EmailLog.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Model\ResourceModel;

class EmailLog extends \Magento\Framework\Model\ResourceModel\Db\AbstractDb
{
    /**
     * Initialize resource
     *
     * @return void
     */
    public function _construct()
    {
        $this->_init('xtento_customsmtp_emaillog', 'id');
    }

    /**
     * @param $days
     *
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteEntriesOlderThan($days)
    {
        $this->getConnection()->delete($this->getMainTable(), ['DATEDIFF(NOW(), created_at) > ?' => intval($days)]);
    }

    /**
     * @throws \Magento\Framework\Exception\LocalizedException
     */
    public function deleteAllEntries()
    {
        $this->getConnection()->truncateTable($this->getMainTable());
    }
}
