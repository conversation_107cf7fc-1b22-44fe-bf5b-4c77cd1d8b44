<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Model/Block/Source/EmailStatus.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Model\Block\Source;

use Magento\Framework\Data\OptionSourceInterface;

class EmailStatus implements OptionSourceInterface
{
    /**
     * Get options
     *
     * @return array
     */
    public function toOptionArray()
    {
        return [
            [
                'label' => __('Sent'),
                'value' => 1,
            ],
            [
                'label' => __('Failed'),
                'value' => 0,
            ],
            [
                'label' => __('Not sent, logged only'),
                'value' => 2,
            ]
        ];
    }
}
