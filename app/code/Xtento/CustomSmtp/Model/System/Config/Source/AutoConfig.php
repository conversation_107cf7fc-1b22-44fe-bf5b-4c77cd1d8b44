<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Model/System/Config/Source/AutoConfig.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Model\System\Config\Source;

use Magento\Framework\Option\ArrayInterface;

/**
 * @codeCoverageIgnore
 */
class AutoConfig implements ArrayInterface
{
    /**
     * @var \Xtento\CustomSmtp\Helper\AutoConfig
     */
    protected $autoConfig;

    /**
     * AuthMethod constructor.
     *
     * @param \Xtento\CustomSmtp\Helper\AutoConfig $autoConfig
     */
    public function __construct(\Xtento\CustomSmtp\Helper\AutoConfig $autoConfig)
    {
        $this->autoConfig = $autoConfig;
    }

    /**
     * {@inheritdoc}
     */
    public function toOptionArray()
    {
        return $this->autoConfig->getProviders();
    }
}
