<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Model/System/Config/Source/ConnectionSecurity.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Model\System\Config\Source;

use Magento\Framework\Option\ArrayInterface;

/**
 * @codeCoverageIgnore
 */
class ConnectionSecurity implements ArrayInterface
{
    /**
     * {@inheritdoc}
     */
    public function toOptionArray()
    {
        $values = [
            '' => __('No encryption'),
            'TLS' => __('TLS'),
            'SSL' => __('SSL'),
        ];
        return $values;
    }
}
