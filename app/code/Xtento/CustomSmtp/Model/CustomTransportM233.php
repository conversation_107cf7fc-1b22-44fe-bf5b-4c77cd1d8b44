<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2022-03-07T20:22:19+00:00
 * File:          app/code/Xtento/CustomSmtp/Model/CustomTransportM233.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Model;

use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Mail\MessageInterface;
use Zend\Mail\Transport\Smtp;
use Zend\Mail\Transport\SmtpOptions;
use Zend\Mail\Message as ZendMessage;

/**
 * Magento >=2.3.3
 *
 * Class CustomTransportM233
 * @package Xtento\CustomSmtp\Model
 */
class CustomTransportM233 extends \Magento\Framework\Mail\Transport implements \Magento\Framework\Mail\TransportInterface
{
    const STATUS_FAILED = 0;
    const STATUS_SUCCESS = 1;
    const STATUS_LOGGEDONLY = 2;

    /**
     * @var Smtp
     */
    protected $zendTransport;

    /**
     * @var MessageInterface
     */
    protected $message;

    /**
     * @var \Zend\Mail\Message
     */
    protected $zendMessage;

    /**
     * @var \Xtento\CustomSmtp\Helper\Module
     */
    protected $moduleHelper;

    /**
     * @var EmailLogFactory
     */
    protected $emailLogFactory;

    /**
     * @var EmailLog
     */
    protected $emailLog = false;

    /**
     * CustomTransportM233 constructor.
     *
     * @param $message
     * @param \Xtento\CustomSmtp\Helper\Module $moduleHelper
     * @param EmailLogFactory $emailLogFactory
     * @param \Magento\Framework\ObjectManagerInterface $objectManager
     * @param \Magento\Framework\Encryption\EncryptorInterface $encryptor
     * @param array $parameters
     *
     * @throws \ReflectionException
     */
    public function __construct(
        $message,
        \Xtento\CustomSmtp\Helper\Module $moduleHelper,
        \Xtento\CustomSmtp\Model\EmailLogFactory $emailLogFactory,
        \Magento\Framework\ObjectManagerInterface $objectManager,
        \Magento\Framework\Encryption\EncryptorInterface $encryptor,
        $parameters = []
    ) {
        if (!is_object($message)) {
            $messageData['body'] = $objectManager->create('Magento\Framework\Mail\MimeMessageInterfaceFactory')->create(['parts' => []]);
            if (isset($parameters['from']) && isset($parameters['to'])) {
                $messageData['from'] = [$objectManager->create('Magento\Framework\Mail\AddressFactory')->create(['email' => $parameters['from'], 'name' => $parameters['from']])];
                $messageData['to'][] = $objectManager->create('Magento\Framework\Mail\AddressFactory')->create(['email' => $parameters['to'], 'name' => $parameters['to']]);
            } else {
                $messageData['from'] = [$objectManager->create('Magento\Framework\Mail\AddressFactory')->create(['email' => '<EMAIL>', 'name' => '<EMAIL>'])];
                $messageData['to'][] = $objectManager->create('Magento\Framework\Mail\AddressFactory')->create(['email' => '<EMAIL>', 'name' => '<EMAIL>']);
            }
            $messageData['subject'] = '';
            $message = $objectManager->create('Magento\Framework\Mail\EmailMessageInterfaceFactory')->create($messageData);
        }
        unset($parameters['from']);
        unset($parameters['to']);
        // Prepare message so we can read more details. Check if implements InterceptorInterface (when class is plugin'ed)
        $messageClass = isset(class_implements($message)['Magento\Framework\Interception\InterceptorInterface']) ? get_parent_class($message) : get_class($message);
        if (property_exists($message, 'message')) { // 2.3.3 pre-patch where they changed it back to zendMessage (how it was in <2.3.3)
            $messageProperty = new \ReflectionProperty($messageClass, 'message');
        } else {
            $messageProperty = new \ReflectionProperty($messageClass, 'zendMessage');
        }
        $messageProperty->setAccessible(true);
        /** @var \Zend\Mail\Message $zendMessage */
        $zendMessage = $messageProperty->getValue($message);

        $this->moduleHelper = $moduleHelper;
        $currentStoreId = $this->moduleHelper->getCurrentStoreId();
        if ($this->moduleHelper->isModuleEnabled() && $this->moduleHelper->useCustomSmtpFunctionality($currentStoreId)) {
            $emailConfig = $this->moduleHelper->getEmailConfig($currentStoreId);

            // E-Mail Headers
            if (isset($emailConfig['reply_to_header'])) {
                switch ($emailConfig['reply_to_header']) {
                    case 1: // Yes
                        $currentFrom = $zendMessage->getFrom()->current();
                        if ($currentFrom !== false) {
                            $zendMessage->setReplyTo($currentFrom->getEmail());
                        }
                        break;
                    case 2: // Specified
                        $zendMessage->setReplyTo($emailConfig['reply_to_email']);
                        break;
                }
            }
            if (isset($emailConfig['override_from']) && $emailConfig['override_from'] > 0 && !empty($emailConfig['override_from_email'])) {
                $fromName = (isset($emailConfig['override_from_name']) && !empty($emailConfig['override_from_name'])) ? $emailConfig['override_from_name'] : null;
                $zendMessage->setFrom($emailConfig['override_from_email'], $fromName);
            }

            // SMTP Configuration
            if (!isset($parameters['override_configuration']) && !empty($emailConfig['server_address']) && intval($emailConfig['server_port']) > 0) {
                if (empty($emailConfig['server_auth_method'])) {
                    // No authentication required
                    $parameters = [
                        'host' => $emailConfig['server_address'],
                        'port' => intval($emailConfig['server_port'])
                    ];
                } else {
                    $parameters = [
                        'host' => $emailConfig['server_address'],
                        'port' => intval($emailConfig['server_port']),
                        'connection_class' => strtolower($emailConfig['server_auth_method']),
                        'connection_config' => [
                            'username' => trim($emailConfig['server_username']),
                            'password' => trim($encryptor->decrypt($emailConfig['server_password'])),
                            'ssl' => strtolower($emailConfig['server_connection_security']),
                        ]
                    ];
                }
            }
        }
        if (isset($parameters['ssl']) && empty($parameters['ssl'])) {
            unset($parameters['ssl']);
        }
        if (isset($parameters['auth']) && empty($parameters['auth'])) {
            unset($parameters['auth']);
        }
        if (isset($parameters['override_configuration'])) {
            unset($parameters['override_configuration']);
        }
        if ($parameters === null) {
            $parameters = [];
        }

        $smtpOptions = new SmtpOptions($parameters);
        $this->zendTransport = new Smtp($smtpOptions);
        $this->message = $message;
        $this->zendMessage = $zendMessage;
        $this->emailLogFactory = $emailLogFactory;
    }

    /**
     * Send a mail using this transport
     *
     * @return void
     * @throws \Magento\Framework\Exception\MailException
     */
    public function sendMessage()
    {
        $debugMessages = [];
        try {
            $debugMessages[] = __('Ready to send E-Mail');
            $currentStoreId = $this->moduleHelper->getCurrentStoreId();
            $logEmailsSent = $this->moduleHelper->logAllEmailsSent($currentStoreId);
            if ($logEmailsSent) {
                $subject = $this->message->getSubject();
                if (function_exists('imap_mime_header_decode')) {
                    $decodedSubject = imap_mime_header_decode($subject);
                    if (isset($decodedSubject[0])) {
                        $subject = $decodedSubject[0]->text;
                    }
                } else {
                    if (strpos($subject, '=?utf-8?B?') !== false) {
                        $subject = base64_decode(str_replace('=?utf-8?B?', '', substr($subject, 0, -2)));
                    }
                    if (strpos($subject, '=?UTF-8?B?') !== false) {
                        $subject = base64_decode(str_replace('=?UTF-8?B?', '', substr($subject, 0, -2)));
                    }
                }

                $toEmails = [];
                $currentTo = $this->zendMessage->getTo()->current();
                if ($currentTo !== false) {
                    $toEmails[] = $currentTo->getEmail();
                }
                while (($address = $this->zendMessage->getTo()->next()) !== false) {
                    $toEmails[] = $address->getEmail();
                }
                $fromEmail = '';
                $currentFrom = $this->zendMessage->getFrom()->current();
                if ($currentFrom !== false) {
                    $fromEmail = $currentFrom->getEmail();
                }

                $bodyText = '';
                $body = $this->zendMessage->getBody();
                if (is_object($body) && $body instanceof \Zend\Mime\Message) {
                    foreach ($body->getParts() as $bodyPart) {
                        if ($bodyPart->getType() === 'text/html') {
                            $bodyText = $bodyPart->getContent();
                            break;
                        }
                    }
                    if (empty($bodyText)) {
                        foreach ($body->getParts() as $bodyPart) {
                            if ($bodyPart->getType() === 'text/plain') {
                                $bodyText = $bodyPart->getContent();
                                break;
                            }
                        }
                    }
                }
                if (empty($bodyText)) {
                    $bodyText = $this->zendMessage->getBodyText();
                }

                $bodyText = quoted_printable_decode($bodyText);

                $this->emailLog = $this->emailLogFactory->create();
                $this->emailLog->setData(
                    [
                        'from' => $fromEmail,
                        'to' => implode(",", $toEmails),
                        'subject' => $subject,
                        'body' => $bodyText
                    ]
                );
                $this->emailLog->save();
            }

            if ($this->moduleHelper->disableEmailSending($currentStoreId)) {
                $status = self::STATUS_LOGGEDONLY;
                $debugMessages[] = __('E-Mail would have been sent, but E-Mail sending is disabled in configuration.');
            } else {
                $this->zendMessage->getHeaders()->removeHeader('Content-Disposition');
                $this->zendTransport->send(
                    ZendMessage::fromString($this->message->getRawMessage())->setEncoding('utf-8')
                );
                $status = self::STATUS_SUCCESS;
                $debugMessages[] = __('E-Mail sent successfully.');
            }
            if ($this->emailLog) {
                $this->emailLog->setStatus($status)->setDebugMessages(implode("... ", $debugMessages))->save();
            }
        } catch (\Exception $e) {
            $debugMessages[] = __('Error while sending E-Mail: %1 ... %2', $e->getMessage(), $e->getTraceAsString());
            if ($this->emailLog) {
                $this->emailLog->setStatus(self::STATUS_FAILED)->setDebugMessages(implode("... ", $debugMessages))->save();
            }
            throw new \Magento\Framework\Exception\MailException(new \Magento\Framework\Phrase($e->getMessage()), $e);
        }
    }

    public function testConfiguration($fromEmail, $toEmail)
    {
        if (empty($fromEmail) || empty($toEmail)) {
            throw new LocalizedException(__('"From" or "To" E-Mail is empty. Specify so test can be executed.'));
        }
        $this->zendMessage->setFrom($fromEmail)
            ->addTo($toEmail)
            ->setSubject(__('XTENTO Custom SMTP Test E-Mail'))
            ->setBody((string)__('This is a test message. If this E-Mail arrives, your SMTP settings are correct.'));
        $this->sendMessage();
        if ($this->emailLog) {
            return $this->emailLog->getDebugMessages();
        } else {
            return false;
        }
    }

    public function getMessage()
    {
        return $this->message;
    }
}