<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2017-08-16T08:48:13+00:00
 * File:          app/code/Xtento/CustomSmtp/Cron/CleanLogs.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Cron;

use Magento\Framework\App\Config\ScopeConfigInterface;
use Xtento\CustomSmtp\Logger\Logger;

class CleanLogs
{
    /**
     * @var \Xtento\CustomSmtp\Helper\Module
     */
    protected $moduleHelper;

    /**
     * @var ScopeConfigInterface
     */
    protected $scopeConfig;

    /**
     * @var Logger
     */
    protected $logger;

    /**
     * @var \Xtento\CustomSmtp\Model\ResourceModel\EmailLog
     */
    protected $emailLog;

    /**
     * CleanLogs constructor.
     *
     * @param \Xtento\CustomSmtp\Helper\Module $moduleHelper
     * @param ScopeConfigInterface $scopeConfig
     * @param Logger $logger
     * @param \Xtento\CustomSmtp\Model\ResourceModel\EmailLog $emailLog
     */
    public function __construct(
        \Xtento\CustomSmtp\Helper\Module $moduleHelper,
        ScopeConfigInterface $scopeConfig,
        Logger $logger,
        \Xtento\CustomSmtp\Model\ResourceModel\EmailLog $emailLog
    ) {
        $this->moduleHelper = $moduleHelper;
        $this->scopeConfig = $scopeConfig;
        $this->logger = $logger;
        $this->emailLog = $emailLog;
    }

    /**
     * Clear logs
     *
     * @param $schedule
     */
    public function execute($schedule)
    {
        try {
            if (!$this->moduleHelper->isModuleEnabled()) {
                return;
            }

            $clearAfterDays = $this->scopeConfig->getValue('customsmtp/email_config/clear_log_after');
            if ($clearAfterDays > 0) {
                $this->logger->notice(__('Starting log cleaning process, logs older than %1 days will be deleted.', $clearAfterDays));
                $this->emailLog->deleteEntriesOlderThan($clearAfterDays);
                $this->logger->notice(__('Cleaning process finished.'));
            }
        } catch (\Exception $e) {
            $this->logger->critical(__('Cronjob exception: %1'), $e->getMessage());
        }
    }
}
