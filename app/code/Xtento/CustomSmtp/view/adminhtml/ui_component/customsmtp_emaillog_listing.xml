<?xml version="1.0" encoding="UTF-8"?>
<listing xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="urn:magento:module:Magento_Ui:etc/ui_configuration.xsd">
    <argument name="data" xsi:type="array">
        <item name="js_config" xsi:type="array">
            <item name="provider" xsi:type="string">customsmtp_emaillog_listing.customsmtp_emaillog_listing_data_source</item>
            <item name="deps" xsi:type="string">customsmtp_emaillog_listing.customsmtp_emaillog_listing_data_source</item>
        </item>
        <item name="spinner" xsi:type="string">customsmtp_emaillog_listing_columns</item>
        <item name="buttons" xsi:type="array">
            <item name="clear_log" xsi:type="array">
                <item name="name" xsi:type="string">clear_log</item>
                <item name="label" xsi:type="string" translate="true">Clear Log</item>
                <item name="class" xsi:type="string">primary</item>
                <item name="url" xsi:type="string">*/*/clearLog</item>
            </item>
        </item>
        <item name="acl" xsi:type="string">Xtento_CustomSmtp::emaillog</item>
    </argument>
    <dataSource name="customsmtp_emaillog_listing_data_source">
        <argument name="dataProvider" xsi:type="configurableObject">
            <argument name="name" xsi:type="string">customsmtp_emaillog_listing_data_source</argument>
            <argument name="class" xsi:type="string">Xtento\CustomSmtp\Ui\DataProvider\EmailLogDataProvider</argument>
            <argument name="primaryFieldName" xsi:type="string">id</argument>
            <argument name="requestFieldName" xsi:type="string">id</argument>
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="update_url" xsi:type="url" path="mui/index/render"/>
                    <item name="storageConfig" xsi:type="array">
                        <item name="indexField" xsi:type="string">id</item>
                    </item>
                </item>
            </argument>
        </argument>
        <argument name="data" xsi:type="array">
            <item name="js_config" xsi:type="array">
                <item name="component" xsi:type="string">Magento_Ui/js/grid/provider</item>
            </item>
        </argument>
    </dataSource>
    <listingToolbar name="listing_top">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="sticky" xsi:type="boolean">true</item>
            </item>
        </argument>
        <bookmark name="bookmarks"/>
        <columnsControls name="columns_controls"/>
        <filters name="listing_filters">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="templates" xsi:type="array">
                        <item name="filters" xsi:type="array">
                            <item name="select" xsi:type="array">
                                <item name="component" xsi:type="string">Magento_Ui/js/form/element/ui-select</item>
                                <item name="template" xsi:type="string">ui/grid/filters/elements/ui-select</item>
                            </item>
                        </item>
                    </item>
                </item>
            </argument>
        </filters>
        <massaction name="listing_massaction">
            <action name="delete">
                <argument name="data" xsi:type="array">
                    <item name="config" xsi:type="array">
                        <item name="type" xsi:type="string">delete</item>
                        <item name="label" xsi:type="string" translate="true">Delete</item>
                        <item name="url" xsi:type="url" path="xtento_customsmtp/emaillog/massDelete"/>
                        <item name="confirm" xsi:type="array">
                            <item name="title" xsi:type="string" translate="true">Delete items</item>
                            <item name="message" xsi:type="string" translate="true">Are you sure you want to delete selected items?</item>
                        </item>
                    </item>
                </argument>
            </action>
        </massaction>
        <paging name="listing_paging"/>
    </listingToolbar>
    <columns name="customsmtp_emaillog_listing_columns">
        <argument name="data" xsi:type="array">
            <item name="config" xsi:type="array">
                <item name="storageConfig" xsi:type="array">
                    <item name="provider" xsi:type="string">customsmtp_emaillog_listing.customsmtp_emaillog_listing.listing_top.bookmarks</item>
                    <item name="namespace" xsi:type="string">current</item>
                </item>
            </item>
        </argument>
        <selectionsColumn name="ids">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="resizeEnabled" xsi:type="boolean">false</item>
                    <item name="resizeDefaultWidth" xsi:type="string">55</item>
                    <item name="indexField" xsi:type="string">id</item>
                </item>
            </argument>
        </selectionsColumn>
        <column name="id">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">number</item>
                    <item name="align" xsi:type="string">left</item>
                    <item name="label" xsi:type="string" translate="true">ID</item>
                    <item name="sorting" xsi:type="string">desc</item>
                </item>
            </argument>
        </column>
        <column name="from">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="align" xsi:type="string">left</item>
                    <item name="label" xsi:type="string" translate="true">From</item>
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
        </column>
        <column name="to">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="align" xsi:type="string">left</item>
                    <item name="label" xsi:type="string" translate="true">To</item>
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
        </column>
        <column name="subject">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="align" xsi:type="string">left</item>
                    <item name="label" xsi:type="string" translate="true">Subject</item>
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
        </column>
        <column name="debug_messages">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="dataType" xsi:type="string">text</item>
                    <item name="align" xsi:type="string">left</item>
                    <item name="label" xsi:type="string" translate="true">Debug Log</item>
                    <item name="filter" xsi:type="string">text</item>
                </item>
            </argument>
        </column>
        <column name="created_at" class="Magento\Ui\Component\Listing\Columns\Date">
            <argument name="data" xsi:type="array">
                <item name="config" xsi:type="array">
                    <item name="filter" xsi:type="string">dateRange</item>
                    <item name="component" xsi:type="string">Magento_Ui/js/grid/columns/date</item>
                    <item name="dataType" xsi:type="string">date</item>
                    <item name="label" xsi:type="string" translate="true">Date</item>
                </item>
            </argument>
        </column>
        <column name="status" class="Xtento\CustomSmtp\Ui\Component\Listing\Columns\EmailStatus" >
            <argument name="data" xsi:type="array">
                <item name="options" xsi:type="object">Xtento\CustomSmtp\Model\Block\Source\EmailStatus</item>
                <item name="config" xsi:type="array">
                    <item name="bodyTmpl" xsi:type="string">ui/grid/cells/html</item>
                    <item name="filter" xsi:type="string">select</item>
                    <item name="dataType" xsi:type="string">select</item>
                    <item name="label" xsi:type="string" translate="true">Status</item>
                </item>
            </argument>
        </column>
        <actionsColumn name="actions" class="Xtento\CustomSmtp\Ui\Component\Listing\Columns\ViewAction">
        </actionsColumn>
    </columns>
</listing>
<!--
/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2019-01-09T14:22:23+00:00
 * File:          app/code/Xtento/CustomSmtp/view/adminhtml/ui_component/customsmtp_emaillog_listing.xml
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */
-->