<?php echo $block->getButtonHtml() ?>

<script>
    require([
        'jquery',
        'Magento_Ui/js/modal/alert'
    ], function ($, alert) {
        window.testCustomSmtpConfiguration = function () {
            $.ajaxSetup({
                showLoader: true,
                timeout: 30000
            });
            $.post('<?php echo $block->getTestUrl() ?>', {
                address: $('#customsmtp_email_config_server_address').val(),
                port: $('#customsmtp_email_config_server_port').val(),
                username: $('#customsmtp_email_config_server_username').val(),
                password: $('#customsmtp_email_config_server_password').val(),
                auth_method: $('#customsmtp_email_config_server_auth_method').val(),
                connection_security: $('#customsmtp_email_config_server_connection_security').val(),
                from_email: $('#customsmtp_email_config_from_email').val(),
                to_email: $('#customsmtp_email_config_to_email').val(),
                form_key: FORM_KEY
            }, null, 'html').done(function (data) {
                alert({
                    title: 'Test Configuration: Send Test E-Mail',
                    content: data
                });
            }).fail(function (xhr, status, error) {
                alert({
                    title: 'AJAX request failed',
                    content: error + "<br/><br/>" + xhr.responseText
                });
            });
        }
    });
</script>