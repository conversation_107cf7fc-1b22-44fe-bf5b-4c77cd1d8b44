<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2023-03-18T18:00:51+00:00
 * File:          app/code/Xtento/CustomSmtp/Plugin/TransportInterfaceFactory.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Plugin;

/**
 * Copied from factory class for \Magento\Framework\Mail\TransportInterface
 */
class TransportInterfaceFactory
{
    /**
     * Object Manager instance
     *
     * @var \Magento\Framework\ObjectManagerInterface
     */
    protected $_objectManager = null;

    /**
     * @var \Xtento\CustomSmtp\Helper\Module
     */
    protected $moduleHelper;

    /**
     * Instance name to create
     *
     * @var string
     */
    protected $_instanceName = null;

    /**
     * Factory constructor
     *
     * @param \Magento\Framework\ObjectManagerInterface $objectManager
     * @param string $instanceName
     */
    public function __construct(
        \Magento\Framework\ObjectManagerInterface $objectManager,
        \Xtento\CustomSmtp\Helper\Module $moduleHelper,
        $instanceName = 'Xtento\CustomSmtp\Model\CustomTransportM233'
    ) {
        $this->_objectManager = $objectManager;
        $this->_instanceName = $instanceName;
        $this->moduleHelper = $moduleHelper;
    }

    /**
     * @param \Magento\Framework\Mail\TransportInterfaceFactory $subject
     * @param \Closure $proceed
     * @param array $data
     *
     * @return mixed
     */
    public function aroundCreate(
        \Magento\Framework\Mail\TransportInterfaceFactory $subject,
        \Closure $proceed,
        array $data = []
    ) {
        if (!$this->moduleHelper->isModuleEnabled()) {
            return $proceed($data);
        }
        if (!$this->moduleHelper->useCustomSmtpFunctionality($this->moduleHelper->getCurrentStoreId())) {
            return $proceed($data);
        }

        if (version_compare($this->moduleHelper->getUtilsHelper()->getMagentoVersion(), '2.4.6', '>=')) {
            return $this->_objectManager->create('Xtento\CustomSmtp\Model\CustomTransportM246', $data);
        } elseif (version_compare($this->moduleHelper->getUtilsHelper()->getMagentoVersion(), '2.3.3', '>=')) {
            return $this->_objectManager->create('Xtento\CustomSmtp\Model\CustomTransportM233', $data);
        } elseif (version_compare($this->moduleHelper->getUtilsHelper()->getMagentoVersion(), '2.2.8', '>=')) {
            return $this->_objectManager->create('Xtento\CustomSmtp\Model\CustomTransportZF2', $data);
        }
    }
}
