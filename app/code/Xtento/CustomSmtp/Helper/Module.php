<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2019-01-09T14:22:23+00:00
 * File:          app/code/Xtento/CustomSmtp/Helper/Module.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Helper;

use Magento\Store\Model\ScopeInterface;

class Module extends \Xtento\XtCore\Helper\AbstractModule
{
    protected $edition = 'EE';
    protected $module = 'Xtento_CustomSmtp';
    protected $extId = 'MTWOXtento_CustomSmtp853921';
    protected $configPath = 'customsmtp/general/';

    /**
     * @var \Magento\Store\Model\StoreManagerInterface
     */
    protected $storeManager;

    /**
     * @var \Xtento\XtCore\Helper\Utils
     */
    protected $utilsHelper;

    /**
     * Module constructor.
     *
     * @param \Magento\Framework\App\Helper\Context $context
     * @param \Magento\Framework\Registry $registry
     * @param \Xtento\XtCore\Helper\Server $serverHelper
     * @param \Xtento\XtCore\Helper\Utils $utilsHelper
     * @param \Magento\Store\Model\StoreManagerInterface $storeManager
     */
    public function __construct(
        \Magento\Framework\App\Helper\Context $context,
        \Magento\Framework\Registry $registry,
        \Xtento\XtCore\Helper\Server $serverHelper,
        \Xtento\XtCore\Helper\Utils $utilsHelper,
        \Magento\Store\Model\StoreManagerInterface $storeManager
    ) {
        parent::__construct($context, $registry, $serverHelper, $utilsHelper);
        $this->storeManager = $storeManager;
        $this->utilsHelper = $utilsHelper;
    }

    /**
     * @return bool
     */
    public function isModuleEnabled()
    {
        return parent::isModuleEnabled();
    }

    // Module specific functionality below
    public function getCurrentStoreId()
    {
        /** @var \Magento\Sales\Model\Order $currentOrder */
        $currentOrder = $this->registry->registry('current_order');
        if ($currentOrder) {
            return $currentOrder->getStoreId();
        }
        $storeManagerId = $this->storeManager->getStore()->getId();
        if ($storeManagerId > 0) {
            return $storeManagerId;
        }
        return 0;
    }

    public function useCustomSmtpFunctionality($storeId)
    {
        return $this->scopeConfig->isSetFlag('customsmtp/email_config/enabled', ScopeInterface::SCOPE_STORE, $storeId);
    }

    public function logAllEmailsSent($storeId)
    {
        return $this->scopeConfig->isSetFlag('customsmtp/email_config/log_emails', ScopeInterface::SCOPE_STORE, $storeId);
    }

    public function disableEmailSending($storeId)
    {
        return $this->scopeConfig->isSetFlag('customsmtp/email_config/disable_emails', ScopeInterface::SCOPE_STORE, $storeId);
    }

    /**
     * @param $storeId
     *
     * @return array
     */
    public function getEmailConfig($storeId)
    {
        return $this->scopeConfig->getValue('customsmtp/email_config', ScopeInterface::SCOPE_STORE, $storeId);
    }

    /**
     * @return \Xtento\XtCore\Helper\Utils
     */
    public function getUtilsHelper()
    {
        return $this->utilsHelper;
    }
}
