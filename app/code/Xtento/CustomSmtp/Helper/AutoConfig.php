<?php

/**
 * Product:       Xtento_CustomSmtp
 * ID:            sxPQW7pZcFhjD/gI62Lrk5IEGminj4ObAzziB4F4M5E=
 * Last Modified: 2020-08-06T15:08:54+00:00
 * File:          app/code/Xtento/CustomSmtp/Helper/AutoConfig.php
 * Copyright:     Copyright (c) XTENTO GmbH & Co. KG <<EMAIL>> / All rights reserved.
 */

namespace Xtento\CustomSmtp\Helper;

class AutoConfig extends \Magento\Framework\App\Helper\AbstractHelper
{
    protected $providers = [
        ['name' => '--- Select ---'],
        ['name' => 'Empty Configuration', 'server' => '', 'port' => '', 'auth_method' => '', 'connection_security' => ''],
        ['name' => 'Gmail / Google Apps', 'server' => 'smtp.gmail.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'SSL'],
        ['name' => 'Hotmail / Live', 'server' => 'smtp.live.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'SSL'],
        ['name' => 'Office365', 'server' => 'smtp.office365.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Outlook', 'server' => 'smtp-mail.outlook.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Yahoo Mail', 'server' => 'smtp.mail.yahoo.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'SSL'],
        ['name' => 'Mail.com', 'server' => 'smtp.mail.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'SSL'],
        ['name' => 'Comcast', 'server' => 'smtp.comcast.net', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => ''],
        ['name' => 'GMX', 'server' => 'mail.gmx.net', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'AOL', 'server' => 'smtp.aol.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'AOL (DE)', 'server' => 'smtp.de.aol.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Orange', 'server' => 'smtp.orange.net', 'port' => '25', 'auth_method' => 'LOGIN', 'connection_security' => ''],
        ['name' => 'O2 Mail Ireland', 'server' => 'smtp.o2.ie', 'port' => '25', 'auth_method' => 'LOGIN', 'connection_security' => ''],
        ['name' => 'Yahoo Mail Plus', 'server' => 'plus.smtp.mail.yahoo.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'SSL'],
        ['name' => 'Yahoo Mail AU', 'server' => 'smtp.mail.yahoo.com.au', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'SSL'],
        ['name' => 'Amazon SES (US East, N. Virginia)', 'server' => 'email-smtp.us-east-1.amazonaws.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Amazon SES (US West, Oregon)', 'server' => 'email-smtp.us-west-2.amazonaws.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Amazon SES (EU, Ireland)', 'server' => 'email-smtp.eu-west-1.amazonaws.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Postmark', 'server' => 'smtp.postmarkapp.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Mailgun', 'server' => 'smtp.mailgun.org', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Mailjet', 'server' => 'in-v3.mailjet.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Sparkpost', 'server' => 'smtp.sparkpostmail.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Elastic Email', 'server' => 'smtp.elasticemail.com', 'port' => '2525', 'auth_method' => 'LOGIN', 'connection_security' => ''],
        ['name' => 'Mandrill', 'server' => 'smtp.mandrillapp.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'SendinBlue', 'server' => 'smtp-relay.sendinblue.com', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'SendGrid', 'server' => 'smtp.sendgrid.net', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
        ['name' => 'Zoho', 'server' => 'smtp.zoho.com', 'port' => '465', 'auth_method' => 'LOGIN', 'connection_security' => 'SSL'],
        ['name' => 'Planisys DMDS', 'server' => 'smtp-dmds.planisys.net', 'port' => '587', 'auth_method' => 'LOGIN', 'connection_security' => 'TLS'],
    ];

    public function getProviders()
    {
        $providerArray = [];
        foreach ($this->providers as $provider) {
            $providerArray[implode("|", array_values($provider))] = __($provider['name']);
        }
        return $providerArray;
    }
}
