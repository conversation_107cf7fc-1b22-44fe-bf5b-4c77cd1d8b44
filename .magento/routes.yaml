# The routes of the project.
#
# Each route describes how an incoming URL is going to be processed.

"http://{default}/":
    type: redirect
    to: https://{default}/

"http://{all}/":
    type: upstream
    upstream: "mymagento:http"

"https://{default}/":
    type: upstream
    upstream: "mymagento:http"

"https://{all}/":
    type: upstream
    upstream: "mymagento:http"

"https://*.{default}/":
    type: upstream
    upstream: "mymagento:http"
